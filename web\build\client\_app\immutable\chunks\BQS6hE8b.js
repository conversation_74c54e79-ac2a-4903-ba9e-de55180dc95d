import{c,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as p,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(e,s){const r=p(s,["children","$$slots","$$events","$$legacy"]),t=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z"}]];f(e,d({name:"message-circle"},()=>r,{get iconNode(){return t},children:(a,$)=>{var o=c(),n=l(o);m(n,s,"default",{},null),i(a,o)},$$slots:{default:!0}}))}export{y as M};
