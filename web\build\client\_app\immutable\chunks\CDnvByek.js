import{c as n,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(t,r){const e=l(r,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2"}]];f(t,d({name:"briefcase"},()=>e,{get iconNode(){return s},children:(a,$)=>{var o=n(),i=p(o);m(i,r,"default",{},null),c(a,o)},$$slots:{default:!0}}))}export{y as B};
