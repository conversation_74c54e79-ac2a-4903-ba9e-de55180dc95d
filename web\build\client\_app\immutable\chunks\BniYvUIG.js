import{ao as m,aA as w,aB as M}from"./CGmarHxI.js";const b=[];function C(t,r=!1){return i(t,new Map,"",b)}function i(t,r,e,o,s=null){if(typeof t=="object"&&t!==null){var _=r.get(t);if(_!==void 0)return _;if(t instanceof Map)return new Map(t);if(t instanceof Set)return new Set(t);if(m(t)){var n=Array(t.length);r.set(t,n),s!==null&&r.set(s,n);for(var f=0;f<t.length;f+=1){var g=t[f];f in t&&(n[f]=i(g,r,e,o))}return n}if(w(t)===M){n={},r.set(t,n),s!==null&&r.set(s,n);for(var S in t)n[S]=i(t[S],r,e,o);return n}if(t instanceof Date)return structuredClone(t);if(typeof t.toJSON=="function")return i(t.toJSON(),r,e,o,t)}if(t instanceof EventTarget)return t;try{return structuredClone(t)}catch{return t}}export{C as s};
