var Zt=Object.defineProperty;var Ct=u=>{throw TypeError(u)};var $t=(u,e,t)=>e in u?Zt(u,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):u[e]=t;var v=(u,e,t)=>$t(u,typeof e!="symbol"?e+"":e,t),It=(u,e,t)=>e.has(u)||Ct("Cannot "+t);var d=(u,e,t)=>(It(u,e,"read from private field"),t?t.call(u):e.get(u)),b=(u,e,t)=>e.has(u)?Ct("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(u):e.set(u,t);var w=(u,e,t)=>(It(u,e,"access private method"),t);import{c as O,a as k,f as dt}from"./BasJTneF.js";import{o as at,x as g,g as f,d as x,k as yt,p as pt,f as C,a as ft,e as St,c as mt,au as _t,r as bt,s as te,t as ee}from"./CGmarHxI.js";import{e as re}from"./C3w0v0gR.js";import{c as Pt}from"./BvdI7LR8.js";import{e as wt,c as se,a as ie,s as ne}from"./B-Xjo-Yt.js";import{p as A,r as vt,s as oe}from"./Btcx8l8F.js";import{s as Y,c as xt}from"./ncUU1dSD.js";import{i as Tt}from"./u21ee2wt.js";import{e as Et,w as Vt,u as Rt,b as P,m as kt}from"./BfX7a-t9.js";import{o as I}from"./CmxjS0TN.js";import{o as Yt}from"./Ntteq2n_.js";import{C as ae}from"./DuoUhxYL.js";import{b as zt,n as Xt,g as gt,h as At}from"./Bd3zs5C6.js";import{c as Ft}from"./Bpi49Nrf.js";import{i as ue}from"./D2egQzE8.js";import{b as Lt,a as Ut,A as Bt,c as Wt,H as Ht,E as Kt}from"./CIOgxH3l.js";import{u as Mt}from"./CnMg5bH0.js";import{n as Ot}from"./DX6rZLP_.js";function le(u,e,t){const r={position:"absolute"};return u==="lr"?(r.left=`${e}%`,r.right=`${t}%`):u==="rl"?(r.right=`${e}%`,r.left=`${t}%`):u==="bt"?(r.bottom=`${e}%`,r.top=`${t}%`):(r.top=`${e}%`,r.bottom=`${t}%`),r}function qt(u,e){const t={position:"absolute"};return u==="lr"?(t.left=`${e}%`,t.translate="-50% 0"):u==="rl"?(t.right=`${e}%`,t.translate="50% 0"):u==="bt"?(t.bottom=`${e}%`,t.translate="0 50%"):(t.top=`${e}%`,t.translate="0 -50%"),t}function Gt(u,e,t){const r={position:"absolute"};return u==="lr"?(r.left=`${e}%`,r.translate=`${t}% 0`):u==="rl"?(r.right=`${e}%`,r.translate=`${-t}% 0`):u==="bt"?(r.bottom=`${e}%`,r.translate=`0 ${-t}%`):(r.top=`${e}%`,r.translate=`0 ${t}%`),r}function E(u,e,t,r){const n=(u-(Number.isNaN(e)?0:e))%r;let i=Math.abs(n)*2>=r?u+Math.sign(n)*(r-Math.abs(n)):u-n;Number.isNaN(e)?!Number.isNaN(t)&&i>t&&(i=Math.floor(t/r)*r):i<e?i=e:!Number.isNaN(t)&&i>t&&(i=e+Math.floor((t-e)/r)*r);const s=r.toString(),l=s.indexOf("."),a=l>=0?s.length-l:0;if(a>0){const o=10**a;i=Math.round(i*o)/o}return i}function Nt(u,e,t=!0){const[r,n]=u,[i,s]=e,l=(s-i)/(n-r);return a=>{const o=i+l*(a-r);return t?o>Math.max(i,s)?Math.max(i,s):o<Math.min(i,s)?Math.min(i,s):o:o}}const ce="data-slider-root",ut="data-slider-thumb",he="data-slider-range",jt="data-slider-tick";var F,L,lt,U;class Jt{constructor(e){v(this,"opts");b(this,F,yt(!1));b(this,L,g(()=>this.opts.orientation.current==="horizontal"?this.opts.dir.current==="rtl"?"rl":"lr":this.opts.dir.current==="rtl"?"tb":"bt"));b(this,lt,g(()=>{if(!this.opts.disabled.current)return this.opts.orientation.current==="horizontal"?"pan-y":"pan-x"}));v(this,"getAllThumbs",()=>{const e=this.opts.ref.current;return e?Array.from(e.querySelectorAll(`[${ut}]`)):[]});v(this,"getThumbScale",()=>{var a,o;if(this.opts.thumbPositioning.current==="exact")return[0,100];const e=this.opts.orientation.current==="vertical",t=this.getAllThumbs()[0],r=e?t==null?void 0:t.offsetHeight:t==null?void 0:t.offsetWidth;if(r===void 0||Number.isNaN(r)||r===0)return[0,100];const n=e?(a=this.opts.ref.current)==null?void 0:a.offsetHeight:(o=this.opts.ref.current)==null?void 0:o.offsetWidth;if(n===void 0||Number.isNaN(n)||n===0)return[0,100];const i=r/2/n*100,s=i,l=100-i;return[s,l]});v(this,"getPositionFromValue",e=>{const t=this.getThumbScale();return Nt([this.opts.min.current,this.opts.max.current],t)(e)});b(this,U,g(()=>({id:this.opts.id.current,"data-orientation":gt(this.opts.orientation.current),"data-disabled":At(this.opts.disabled.current),style:{touchAction:f(d(this,lt))},[ce]:""})));this.opts=e,Rt(e)}get isActive(){return f(d(this,F))}set isActive(e){x(d(this,F),e,!0)}get direction(){return f(d(this,L))}set direction(e){x(d(this,L),e)}isThumbActive(e){return this.isActive}get props(){return f(d(this,U))}set props(e){x(d(this,U),e)}}F=new WeakMap,L=new WeakMap,lt=new WeakMap,U=new WeakMap;var B,W,H,K,q;class de extends Jt{constructor(t){super(t);v(this,"opts");v(this,"isMulti",!1);v(this,"updateValue",t=>{this.opts.value.current=E(t,this.opts.min.current,this.opts.max.current,this.opts.step.current)});v(this,"handlePointerMove",t=>{if(!this.isActive||this.opts.disabled.current)return;t.preventDefault(),t.stopPropagation();const r=this.opts.ref.current,n=this.getAllThumbs()[0];if(!r||!n)return;n.focus();const{left:i,right:s,top:l,bottom:a}=r.getBoundingClientRect();this.direction==="lr"?this.applyPosition({clientXY:t.clientX,start:i,end:s}):this.direction==="rl"?this.applyPosition({clientXY:t.clientX,start:s,end:i}):this.direction==="bt"?this.applyPosition({clientXY:t.clientY,start:a,end:l}):this.direction==="tb"&&this.applyPosition({clientXY:t.clientY,start:l,end:a})});v(this,"handlePointerDown",t=>{if(t.button!==0||this.opts.disabled.current)return;const r=this.opts.ref.current,n=this.getAllThumbs()[0];if(!n||!r)return;const i=t.target;!Ft(i)||!r.contains(i)||(t.preventDefault(),n.focus(),this.isActive=!0,this.handlePointerMove(t))});v(this,"handlePointerUp",()=>{this.opts.disabled.current||(this.isActive&&this.opts.onValueCommit.current(at(()=>this.opts.value.current)),this.isActive=!1)});b(this,B,g(()=>{const t=this.opts.value.current;return Array.from({length:1},()=>{const r=t,n=this.getPositionFromValue(r),i=qt(this.direction,n);return{role:"slider","aria-valuemin":this.opts.min.current,"aria-valuemax":this.opts.max.current,"aria-valuenow":r,"aria-disabled":Xt(this.opts.disabled.current),"aria-orientation":zt(this.opts.orientation.current),"data-value":r,tabindex:this.opts.disabled.current?-1:0,style:i,[ut]:""}})}));b(this,W,g(()=>this.thumbsPropsArr.map((t,r)=>r)));b(this,H,g(()=>{const t=this.opts.max.current,r=this.opts.min.current,n=this.opts.step.current,i=t-r;let s=Math.ceil(i/n);i%n==0&&s++;const l=this.opts.value.current;return Array.from({length:s},(a,o)=>{const h=o*n,p=Nt([0,(s-1)*n],this.getThumbScale()),c=o===0,m=o===s-1,y=c?0:m?-100:-50,R=Gt(this.direction,p(h),y),_=r+o*n,M=_<=l;return{"data-disabled":At(this.opts.disabled.current),"data-orientation":gt(this.opts.orientation.current),"data-bounded":M?"":void 0,"data-value":_,style:R,[jt]:""}})}));b(this,K,g(()=>this.ticksPropsArr.map((t,r)=>r)));b(this,q,g(()=>({ticks:this.ticksRenderArr,thumbs:this.thumbsRenderArr})));this.opts=t,Yt(()=>Et(I(document,"pointerdown",this.handlePointerDown),I(document,"pointerup",this.handlePointerUp),I(document,"pointermove",this.handlePointerMove),I(document,"pointerleave",this.handlePointerUp))),Vt([()=>this.opts.step.current,()=>this.opts.min.current,()=>this.opts.max.current,()=>this.opts.value.current],([r,n,i,s])=>{const l=o=>E(o,n,i,r)===o,a=o=>E(o,n,i,r);l(s)||(this.opts.value.current=a(s))})}applyPosition({clientXY:t,start:r,end:n}){const i=this.opts.min.current,s=this.opts.max.current,a=(t-r)/(n-r)*(s-i)+i;if(a<i)this.updateValue(i);else if(a>s)this.updateValue(s);else{const o=this.opts.step.current,h=Math.floor((a-i)/o),p=i+h*o+o/2,c=i+(h+1)*o+o/2,m=a>=p&&a<c?(h+1)*o+i:h*o+i;m<=s&&this.updateValue(m)}}get thumbsPropsArr(){return f(d(this,B))}set thumbsPropsArr(t){x(d(this,B),t)}get thumbsRenderArr(){return f(d(this,W))}set thumbsRenderArr(t){x(d(this,W),t)}get ticksPropsArr(){return f(d(this,H))}set ticksPropsArr(t){x(d(this,H),t)}get ticksRenderArr(){return f(d(this,K))}set ticksRenderArr(t){x(d(this,K),t)}get snippetProps(){return f(d(this,q))}set snippetProps(t){x(d(this,q),t)}}B=new WeakMap,W=new WeakMap,H=new WeakMap,K=new WeakMap,q=new WeakMap;var G,j,ct,J,Q,Z,$,tt;class pe extends Jt{constructor(t){super(t);v(this,"opts");v(this,"isMulti",!0);b(this,G,yt(null));b(this,j,yt(0));b(this,ct,t=>{const r=this.getAllThumbs();if(!r.length)return;for(const l of r)l.blur();const n=r.map(l=>{if(this.opts.orientation.current==="horizontal"){const{left:a,right:o}=l.getBoundingClientRect();return Math.abs(t.clientX-(a+o)/2)}else{const{top:a,bottom:o}=l.getBoundingClientRect();return Math.abs(t.clientY-(a+o)/2)}}),i=r[n.indexOf(Math.min(...n))],s=r.indexOf(i);return{node:i,idx:s}});v(this,"handlePointerMove",t=>{if(!this.isActive||this.opts.disabled.current)return;t.preventDefault(),t.stopPropagation();const r=this.opts.ref.current,n=this.activeThumb;if(!r||!n)return;n.node.focus();const{left:i,right:s,top:l,bottom:a}=r.getBoundingClientRect(),o=this.direction;o==="lr"?this.applyPosition({clientXY:t.clientX,activeThumbIdx:n.idx,start:i,end:s}):o==="rl"?this.applyPosition({clientXY:t.clientX,activeThumbIdx:n.idx,start:s,end:i}):o==="bt"?this.applyPosition({clientXY:t.clientY,activeThumbIdx:n.idx,start:a,end:l}):o==="tb"&&this.applyPosition({clientXY:t.clientY,activeThumbIdx:n.idx,start:l,end:a})});v(this,"handlePointerDown",t=>{if(t.button!==0||this.opts.disabled.current)return;const r=this.opts.ref.current,n=d(this,ct).call(this,t);if(!n||!r)return;const i=t.target;!Ft(i)||!r.contains(i)||(t.preventDefault(),this.activeThumb=n,n.node.focus(),this.isActive=!0,this.handlePointerMove(t))});v(this,"handlePointerUp",()=>{this.opts.disabled.current||(this.isActive&&this.opts.onValueCommit.current(at(()=>this.opts.value.current)),this.isActive=!1)});v(this,"getAllThumbs",()=>{const t=this.opts.ref.current;return t?Array.from(t.querySelectorAll(`[${ut}]`)):[]});v(this,"updateValue",(t,r)=>{const n=this.opts.value.current;if(!n.length){this.opts.value.current.push(t);return}if(n[r]===t)return;const s=[...n];if(!ue(r,s))return;const l=s[r]>t?-1:1,a=()=>{var y;const c=r+l;s[r]=s[c],s[c]=t;const m=this.getAllThumbs();m.length&&((y=m[c])==null||y.focus(),this.activeThumb={node:m[c],idx:c})};if(this.opts.autoSort.current&&(l===-1&&t<s[r-1]||l===1&&t>s[r+1])){a(),this.opts.value.current=s;return}const o=this.opts.min.current,h=this.opts.max.current,p=this.opts.step.current;s[r]=E(t,o,h,p),this.opts.value.current=s});b(this,J,g(()=>{const t=this.opts.value.current;return Array.from({length:t.length||1},(r,n)=>{const i=at(()=>this.currentThumbIdx);i<t.length&&at(()=>{this.currentThumbIdx=i+1});const s=t[n],l=this.getPositionFromValue(s??0),a=qt(this.direction,l);return{role:"slider","aria-valuemin":this.opts.min.current,"aria-valuemax":this.opts.max.current,"aria-valuenow":s,"aria-disabled":Xt(this.opts.disabled.current),"aria-orientation":zt(this.opts.orientation.current),"data-value":s,tabindex:this.opts.disabled.current?-1:0,style:a,[ut]:""}})}));b(this,Q,g(()=>this.thumbsPropsArr.map((t,r)=>r)));b(this,Z,g(()=>{const t=this.opts.max.current,r=this.opts.min.current,n=this.opts.step.current,i=t-r;let s=Math.ceil(i/n);i%n==0&&s++;const l=this.opts.value.current;return Array.from({length:s},(a,o)=>{const h=o*n,p=Nt([0,(s-1)*n],this.getThumbScale()),c=o===0,m=o===s-1,y=c?0:m?-100:-50,R=Gt(this.direction,p(h),y),_=r+o*n,M=l.length===1?_<=l[0]:l[0]<=_&&_<=l[l.length-1];return{"data-disabled":At(this.opts.disabled.current),"data-orientation":gt(this.opts.orientation.current),"data-bounded":M?"":void 0,"data-value":_,style:R,[jt]:""}})}));b(this,$,g(()=>this.ticksPropsArr.map((t,r)=>r)));b(this,tt,g(()=>({ticks:this.ticksRenderArr,thumbs:this.thumbsRenderArr})));this.opts=t,Yt(()=>Et(I(document,"pointerdown",this.handlePointerDown),I(document,"pointerup",this.handlePointerUp),I(document,"pointermove",this.handlePointerMove),I(document,"pointerleave",this.handlePointerUp))),Vt([()=>this.opts.step.current,()=>this.opts.min.current,()=>this.opts.max.current,()=>this.opts.value.current],([r,n,i,s])=>{const l=o=>E(o,n,i,r)===o,a=o=>E(o,n,i,r);s.some(o=>!l(o))&&(this.opts.value.current=s.map(a))})}get activeThumb(){return f(d(this,G))}set activeThumb(t){x(d(this,G),t,!0)}get currentThumbIdx(){return f(d(this,j))}set currentThumbIdx(t){x(d(this,j),t,!0)}isThumbActive(t){var r;return this.isActive&&((r=this.activeThumb)==null?void 0:r.idx)===t}applyPosition({clientXY:t,activeThumbIdx:r,start:n,end:i}){const s=this.opts.min.current,l=this.opts.max.current,o=(t-n)/(i-n)*(l-s)+s;if(o<s)this.updateValue(s,r);else if(o>l)this.updateValue(l,r);else{const h=this.opts.step.current,p=Math.floor((o-s)/h),c=s+p*h+h/2,m=s+(p+1)*h+h/2,y=o>=c&&o<m?(p+1)*h+s:p*h+s;y<=l&&this.updateValue(y,r)}}get thumbsPropsArr(){return f(d(this,J))}set thumbsPropsArr(t){x(d(this,J),t)}get thumbsRenderArr(){return f(d(this,Q))}set thumbsRenderArr(t){x(d(this,Q),t)}get ticksPropsArr(){return f(d(this,Z))}set ticksPropsArr(t){x(d(this,Z),t)}get ticksRenderArr(){return f(d(this,$))}set ticksRenderArr(t){x(d(this,$),t)}get snippetProps(){return f(d(this,tt))}set snippetProps(t){x(d(this,tt),t)}}G=new WeakMap,j=new WeakMap,ct=new WeakMap,J=new WeakMap,Q=new WeakMap,Z=new WeakMap,$=new WeakMap,tt=new WeakMap;const fe=[Lt,Ut,Bt,Wt,Ht,Kt];var et,rt;class me{constructor(e,t){v(this,"opts");v(this,"root");b(this,et,g(()=>{const e=Array.isArray(this.root.opts.value.current)&&this.root.opts.value.current.length>1?this.root.getPositionFromValue(Math.min(...this.root.opts.value.current)??0):0,t=Array.isArray(this.root.opts.value.current)?100-this.root.getPositionFromValue(Math.max(...this.root.opts.value.current)??0):100-this.root.getPositionFromValue(this.root.opts.value.current);return{position:"absolute",...le(this.root.direction,e,t)}}));b(this,rt,g(()=>({id:this.opts.id.current,"data-orientation":gt(this.root.opts.orientation.current),"data-disabled":At(this.root.opts.disabled.current),style:this.rangeStyles,[he]:""})));this.opts=e,this.root=t,Rt(e)}get rangeStyles(){return f(d(this,et))}set rangeStyles(e){x(d(this,et),e)}get props(){return f(d(this,rt))}set props(e){x(d(this,rt),e)}}et=new WeakMap,rt=new WeakMap;var ht,S,T,st;class be{constructor(e,t){b(this,S);v(this,"opts");v(this,"root");b(this,ht,g(()=>this.root.opts.disabled.current||this.opts.disabled.current));b(this,st,g(()=>({...this.root.thumbsPropsArr[this.opts.index.current],id:this.opts.id.current,onkeydown:this.onkeydown,"data-active":this.root.isThumbActive(this.opts.index.current)?"":void 0})));this.opts=e,this.root=t,Rt(e),this.onkeydown=this.onkeydown.bind(this)}onkeydown(e){if(f(d(this,ht)))return;const t=this.opts.ref.current;if(!t)return;const r=this.root.getAllThumbs();if(!r.length)return;const n=r.indexOf(t);if(this.root.isMulti&&(this.root.currentThumbIdx=n),!fe.includes(e.key))return;e.preventDefault();const i=this.root.opts.min.current,s=this.root.opts.max.current,l=this.root.opts.value.current,a=Array.isArray(l)?l[n]:l,o=this.root.opts.orientation.current,h=this.root.direction,p=this.root.opts.step.current;switch(e.key){case Ht:w(this,S,T).call(this,i);break;case Kt:w(this,S,T).call(this,s);break;case Lt:if(o!=="horizontal")break;if(e.metaKey){const c=h==="rl"?s:i;w(this,S,T).call(this,c)}else h==="rl"&&a<s?w(this,S,T).call(this,a+p):h==="lr"&&a>i&&w(this,S,T).call(this,a-p);break;case Ut:if(o!=="horizontal")break;if(e.metaKey){const c=h==="rl"?i:s;w(this,S,T).call(this,c)}else h==="rl"&&a>i?w(this,S,T).call(this,a-p):h==="lr"&&a<s&&w(this,S,T).call(this,a+p);break;case Bt:if(e.metaKey){const c=h==="tb"?i:s;w(this,S,T).call(this,c)}else h==="tb"&&a>i?w(this,S,T).call(this,a-p):h!=="tb"&&a<s&&w(this,S,T).call(this,a+p);break;case Wt:if(e.metaKey){const c=h==="tb"?s:i;w(this,S,T).call(this,c)}else h==="tb"&&a<s?w(this,S,T).call(this,a+p):h!=="tb"&&a>i&&w(this,S,T).call(this,a-p);break}this.root.opts.onValueCommit.current(this.root.opts.value.current)}get props(){return f(d(this,st))}set props(e){x(d(this,st),e)}}ht=new WeakMap,S=new WeakSet,T=function(e){this.root.isMulti?this.root.updateValue(e,this.opts.index.current):this.root.updateValue(e)},st=new WeakMap;const Dt=new ae("Slider.Root");function ve(u){const{type:e,...t}=u,r=e==="single"?new de(t):new pe(t);return Dt.set(r)}function ge(u){return new me(u,Dt.get())}function Ae(u){return new be(u,Dt.get())}var Pe=dt("<span><!></span>");function xe(u,e){pt(e,!0);let t=A(e,"id",19,Mt),r=A(e,"ref",15,null),n=A(e,"value",15),i=A(e,"onValueChange",3,Ot),s=A(e,"onValueCommit",3,Ot),l=A(e,"disabled",3,!1),a=A(e,"min",3,0),o=A(e,"max",3,100),h=A(e,"step",3,1),p=A(e,"dir",3,"ltr"),c=A(e,"autoSort",3,!0),m=A(e,"orientation",3,"horizontal"),y=A(e,"thumbPositioning",3,"contain"),R=vt(e,["$$slots","$$events","$$legacy","children","child","id","ref","value","type","onValueChange","onValueCommit","disabled","min","max","step","dir","autoSort","orientation","thumbPositioning"]);function _(){n()===void 0&&n(e.type==="single"?0:[])}_(),Vt.pre(()=>n(),()=>{_()});const M=ve({id:P.with(()=>t()),ref:P.with(()=>r(),V=>r(V)),value:P.with(()=>n(),V=>{n(V),i()(V)}),onValueCommit:P.with(()=>s()),disabled:P.with(()=>l()),min:P.with(()=>a()),max:P.with(()=>o()),step:P.with(()=>h()),dir:P.with(()=>p()),autoSort:P.with(()=>c()),orientation:P.with(()=>m()),thumbPositioning:P.with(()=>y()),type:e.type}),it=g(()=>kt(R,M.props));var nt=O(),N=C(nt);{var z=V=>{var D=O(),X=C(D),Qt=St(()=>({props:f(it),...M.snippetProps}));Y(X,()=>e.child,()=>f(Qt)),k(V,D)},ot=V=>{var D=Pe();wt(D,()=>({...f(it)}));var X=mt(D);Y(X,()=>e.children??_t,()=>M.snippetProps),bt(D),k(V,D)};Tt(N,V=>{e.child?V(z):V(ot,!1)})}k(u,nt),ft()}var ye=dt("<span><!></span>");function Se(u,e){pt(e,!0);let t=A(e,"ref",15,null),r=A(e,"id",19,Mt),n=vt(e,["$$slots","$$events","$$legacy","children","child","ref","id"]);const i=ge({id:P.with(()=>r()),ref:P.with(()=>t(),p=>t(p))}),s=g(()=>kt(n,i.props));var l=O(),a=C(l);{var o=p=>{var c=O(),m=C(c);Y(m,()=>e.child,()=>({props:f(s)})),k(p,c)},h=p=>{var c=ye();wt(c,()=>({...f(s)}));var m=mt(c);Y(m,()=>e.children??_t),bt(c),k(p,c)};Tt(a,p=>{e.child?p(o):p(h,!1)})}k(u,l),ft()}var _e=dt("<span><!></span>");function we(u,e){pt(e,!0);let t=A(e,"ref",15,null),r=A(e,"id",19,Mt),n=A(e,"disabled",3,!1),i=vt(e,["$$slots","$$events","$$legacy","children","child","ref","id","index","disabled"]);const s=Ae({id:P.with(()=>r()),ref:P.with(()=>t(),c=>t(c)),index:P.with(()=>e.index),disabled:P.with(()=>n())}),l=g(()=>kt(i,s.props));var a=O(),o=C(a);{var h=c=>{var m=O(),y=C(m),R=St(()=>({active:s.root.isThumbActive(s.opts.index.current),props:f(l)}));Y(y,()=>e.child,()=>f(R)),k(c,m)},p=c=>{var m=_e();wt(m,()=>({...f(l)}));var y=mt(m),R=St(()=>({active:s.root.isThumbActive(s.opts.index.current)}));Y(y,()=>e.children??_t,()=>f(R)),bt(m),k(c,m)};Tt(o,c=>{e.child?c(h):c(p,!1)})}k(u,a),ft()}var Te=dt('<span data-slot="slider-track"><!></span> <!>',1);function Ke(u,e){pt(e,!0);let t=A(e,"ref",15,null),r=A(e,"value",15),n=A(e,"orientation",3,"horizontal"),i=vt(e,["$$slots","$$events","$$legacy","ref","value","orientation","class"]);var s=O(),l=C(s);const a=g(()=>xt("relative flex w-full touch-none select-none items-center data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col data-[disabled]:opacity-50",e.class));Pt(l,()=>xe,(o,h)=>{h(o,oe({"data-slot":"slider",get orientation(){return n()},get class(){return f(a)}},()=>i,{get ref(){return t()},set ref(c){t(c)},get value(){return r()},set value(c){r(c)},children:(c,m)=>{let y=()=>m==null?void 0:m().thumbs;var R=Te(),_=C(R),M=mt(_);const it=g(()=>xt("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full"));Pt(M,()=>Se,(N,z)=>{z(N,{"data-slot":"slider-range",style:"width: -webkit-fill-available;",get class(){return f(it)}})}),bt(_);var nt=te(_,2);re(nt,16,y,N=>N,(N,z)=>{var ot=O(),V=C(ot);Pt(V,()=>we,(D,X)=>{X(D,{"data-slot":"slider-thumb",get index(){return z},class:"border-primary bg-background ring-ring/50 focus-visible:outline-hidden block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 disabled:pointer-events-none disabled:opacity-50"})}),k(N,ot)}),ee(N=>{ie(_,"data-orientation",n()),ne(_,1,N)},[()=>se(xt("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=vertical]:h-full data-[orientation=horizontal]:w-full data-[orientation=vertical]:w-1.5"))]),k(c,R)},$$slots:{default:!0}}))}),k(u,s),ft()}export{Ke as S};
