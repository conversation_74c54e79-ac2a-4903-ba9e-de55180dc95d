import{c as p}from"./CGtH72Kl.js";var a={};const o=a.SANITY_PROJECT_ID||"fqw18aoo",h=a.SANITY_DATASET||"production",$=a.SANITY_API_VERSION||"2023-05-03",_=p({projectId:o,dataset:h,apiVersion:$,useCdn:!0});function m(t,e={}){if(!t||!t.asset||!t.asset._ref)return"";const n=t.asset._ref;if(!n.startsWith("image-"))return"";const i=n.split("-"),r=i[1],u=i.length>3?i[3]:"jpg";if(!r)return"";let c=`https://cdn.sanity.io/images/${o}/${h}/${r}.${u}`;const s=[],f=e.width||800;s.push(`w=${f}`),e.height&&s.push(`h=${e.height}`);const d=e.fit||"max";s.push(`fit=${d}`),s.push("auto=format");const l=e.quality||80;return s.push(`q=${l}`),s.length>0&&(c+=`?${s.join("&")}`),c}async function A(t,e=10){return await _.fetch(`
    *[_type == "helpArticle" && (title match "*${t}*" || description match "*${t}*" || category match "*${t}*")] | order(viewCount desc) [0...${e}] {
      _id,
      title,
      slug,
      category,
      description,
      icon,
      tags,
      publishedAt,
      updatedAt,
      viewCount
    }
  `)}export{A as s,m as u};
