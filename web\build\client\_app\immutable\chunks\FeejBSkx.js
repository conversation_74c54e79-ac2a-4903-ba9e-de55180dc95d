var Qe=Object.defineProperty;var Re=t=>{throw TypeError(t)};var $e=(t,e,r)=>e in t?Qe(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var N=(t,e,r)=>$e(t,typeof e!="symbol"?e+"":e,r),We=(t,e,r)=>e.has(t)||Re("Cannot "+r);var i=(t,e,r)=>(We(t,e,"read from private field"),r?r.call(t):e.get(t)),f=(t,e,r)=>e.has(t)?Re("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,r),_=(t,e,r,n)=>(We(t,e,"write to private field"),n?n.call(t,r):e.set(t,r),r);import{c as S,a as y,f as Se}from"./BasJTneF.js";import{be as et,aJ as tt,k as T,v as De,d as m,g as c,x as p,i as U,o as ke,aE as Ue,aD as Ve,p as he,f as C,au as je,a as pe,e as rt,c as ve,r as ge,t as qe}from"./CGmarHxI.js";import{s as He}from"./CIt1g2O9.js";import{s as D,c as Ee}from"./ncUU1dSD.js";import{i as Fe}from"./u21ee2wt.js";import{e as Me,i as nt}from"./C3w0v0gR.js";import{c as Xe}from"./BvdI7LR8.js";import{h as Ie,e as _e}from"./B-Xjo-Yt.js";import{p as V,r as Ae,s as it}from"./Btcx8l8F.js";import{c as st,p as ot}from"./BfX7a-t9.js";import{b as ut}from"./5V1tIHTN.js";function be(t){let e;const r=st(s=>{let o=!1;const u=t.subscribe(l=>{e=l,o&&s()});return o=!0,u});function n(){return et()?(r(),e):tt(t)}return"set"in t?{get current(){return n()},set current(s){t.set(s)}}:{get current(){return n()}}}function ft(t){return typeof t=="function"}function ct(t){return t!==null&&typeof t=="object"}const at=["string","number","bigint","boolean"];function Be(t){return t==null||at.includes(typeof t)?!0:Array.isArray(t)?t.every(e=>Be(e)):typeof t=="object"?Object.getPrototypeOf(t)===Object.prototype:!1}const q=Symbol("box"),Ne=Symbol("is-writable");function dt(t){return ct(t)&&q in t}function lt(t){return h.isBox(t)&&Ne in t}function h(t){let e=T(De(t));return{[q]:!0,[Ne]:!0,get current(){return c(e)},set current(r){m(e,r,!0)}}}function ht(t,e){const r=p(t);return e?{[q]:!0,[Ne]:!0,get current(){return c(r)},set current(n){e(n)}}:{[q]:!0,get current(){return t()}}}function pt(t){return h.isBox(t)?t:ft(t)?h.with(t):h(t)}function mt(t){return Object.entries(t).reduce((e,[r,n])=>h.isBox(n)?(h.isWritableBox(n)?Object.defineProperty(e,r,{get(){return n.current},set(s){n.current=s}}):Object.defineProperty(e,r,{get(){return n.current}}),e):Object.assign(e,{[r]:n}),{})}function bt(t){return h.isWritableBox(t)?{[q]:!0,get current(){return t.current}}:t}h.from=pt;h.with=ht;h.flatten=mt;h.readonly=bt;h.isBox=dt;h.isWritableBox=lt;function yt(...t){return function(e){var r;for(const n of t)if(n){if(e.defaultPrevented)return;typeof n=="function"?n.call(this,e):(r=n.current)==null||r.call(this,e)}}}const vt=/\d/,gt=["-","_","/","."];function _t(t=""){if(!vt.test(t))return t!==t.toLowerCase()}function Pt(t){const e=[];let r="",n,s;for(const o of t){const u=gt.includes(o);if(u===!0){e.push(r),r="",n=void 0;continue}const l=_t(o);if(s===!1){if(n===!1&&l===!0){e.push(r),r=o,n=l;continue}if(n===!0&&l===!1&&r.length>1){const P=r.at(-1);e.push(r.slice(0,Math.max(0,r.length-1))),r=P+o,n=l;continue}}r+=o,n=l,s=u}return e.push(r),e}function ze(t){return t?Pt(t).map(e=>St(e)).join(""):""}function xt(t){return Ct(ze(t||""))}function St(t){return t?t[0].toUpperCase()+t.slice(1):""}function Ct(t){return t?t[0].toLowerCase()+t.slice(1):""}function ye(t){if(!t)return{};const e={};function r(n,s){if(n.startsWith("-moz-")||n.startsWith("-webkit-")||n.startsWith("-ms-")||n.startsWith("-o-")){e[ze(n)]=s;return}if(n.startsWith("--")){e[n]=s;return}e[xt(n)]=s}return ot(t,r),e}function It(...t){return(...e)=>{for(const r of t)typeof r=="function"&&r(...e)}}function wt(t,e){const r=RegExp(t,"g");return n=>{if(typeof n!="string")throw new TypeError(`expected an argument of type string, but got ${typeof n}`);return n.match(r)?n.replace(r,e):n}}const Et=wt(/[A-Z]/,t=>`-${t.toLowerCase()}`);function Ft(t){if(!t||typeof t!="object"||Array.isArray(t))throw new TypeError(`expected an argument of type object, but got ${typeof t}`);return Object.keys(t).map(e=>`${Et(e)}: ${t[e]};`).join(`
`)}function Je(t={}){return Ft(t).replace(`
`," ")}const Bt={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",transform:"translateX(-100%)"};Je(Bt);function jt(t){var e;return t.length>2&&t.startsWith("on")&&t[2]===((e=t[2])==null?void 0:e.toLowerCase())}function At(...t){const e={...t[0]};for(let r=1;r<t.length;r++){const n=t[r];for(const s in n){const o=e[s],u=n[s],l=typeof o=="function",P=typeof u=="function";if(l&&jt(s)){const g=o,a=u;e[s]=yt(g,a)}else if(l&&P)e[s]=It(o,u);else if(s==="class"){const g=Be(o),a=Be(u);g&&a?e[s]=Ie(o,u):g?e[s]=Ie(o):a&&(e[s]=Ie(u))}else if(s==="style"){const g=typeof o=="object",a=typeof u=="object",d=typeof o=="string",b=typeof u=="string";if(g&&a)e[s]={...o,...u};else if(g&&b){const v=ye(u);e[s]={...o,...v}}else if(d&&a){const v=ye(o);e[s]={...v,...u}}else if(d&&b){const v=ye(o),I=ye(u);e[s]={...v,...I}}else g?e[s]=o:a?e[s]=u:d?e[s]=o:b&&(e[s]=u)}else e[s]=u!==void 0?u:o}}return typeof e.style=="object"&&(e.style=Je(e.style).replaceAll(`
`," ")),e.hidden!==!0&&(e.hidden=void 0,delete e.hidden),e.disabled!==!0&&(e.disabled=void 0,delete e.disabled),e}function Te({id:t,ref:e,deps:r=()=>!0,onRefChange:n=()=>{},getRootNode:s=()=>typeof document<"u"?document:void 0}){const o=p(()=>r()),u=p(()=>s());U(()=>(t.current,c(o),c(u),ke(()=>{var P;const l=(P=c(u))==null?void 0:P.getElementById(t.current);l?e.current=l:e.current=null,n(e.current)}))),U(()=>()=>{e.current=null,n(null)})}function Nt(t,e){const r=p(t);U(()=>{c(r),ke(()=>e(c(r)))})}function Tt(t){return Array.isArray(t)?[...t]:typeof t=="object"&&"_errors"in t&&t._errors!==void 0?[...t._errors]:[]}function we(t,e){const r=t.split(/[[\].]/).filter(Boolean);let n=e;for(const s of r){if(typeof n!="object"||n===null)return;n=n[s]}return n}function Ot({fieldErrorsId:t=void 0,descriptionId:e=void 0,errors:r}){let n="";return e&&(n+=`${e} `),r.length&&t&&(n+=t),n?n.trim():void 0}function Rt(t){if("required"in t)return t.required?"true":void 0}function Wt(t){return t&&t.length?"true":void 0}function Pe(t){return t&&t.length?"":void 0}let Le=0;function xe(t="formsnap"){return Le++,`${t}-${Le}`}var B,H,M,O,X,z,J,K,Y,Z,G,Q,$,ee;class Lt{constructor(e){f(this,B);f(this,H);f(this,M);f(this,O);f(this,X);N(this,"form");f(this,z,p(()=>i(this,B).current));f(this,J,p(()=>Tt(we(i(this,B).current,structuredClone(i(this,H).current)))));f(this,K,p(()=>we(i(this,B).current,structuredClone(i(this,M).current))??{}));f(this,Y,p(()=>i(this,O).current?we(i(this,B).current,structuredClone(i(this,O).current))===!0:!1));f(this,Z,T(null));f(this,G,T(null));f(this,Q,T());f(this,$,T());f(this,ee,p(()=>({value:i(this,X).current[i(this,B).current],errors:this.errors,tainted:this.tainted,constraints:this.constraints})));_(this,B,e.name),this.form=e.form.current,_(this,H,be(e.form.current.errors)),_(this,M,be(e.form.current.constraints)),_(this,O,be(e.form.current.tainted)),_(this,X,be(e.form.current.form)),U(()=>{this.errorNode&&this.errorNode.id&&(this.errorId=this.errorNode.id)}),U(()=>{this.descriptionNode&&this.descriptionNode.id&&(this.descriptionId=this.descriptionNode.id)})}get name(){return c(i(this,z))}set name(e){m(i(this,z),e)}get errors(){return c(i(this,J))}set errors(e){m(i(this,J),e)}get constraints(){return c(i(this,K))}set constraints(e){m(i(this,K),e)}get tainted(){return c(i(this,Y))}set tainted(e){m(i(this,Y),e)}get errorNode(){return c(i(this,Z))}set errorNode(e){m(i(this,Z),e,!0)}get descriptionNode(){return c(i(this,G))}set descriptionNode(e){m(i(this,G),e,!0)}get errorId(){return c(i(this,Q))}set errorId(e){m(i(this,Q),e,!0)}get descriptionId(){return c(i(this,$))}set descriptionId(e){m(i(this,$),e,!0)}get snippetProps(){return c(i(this,ee))}set snippetProps(e){m(i(this,ee),e)}}B=new WeakMap,H=new WeakMap,M=new WeakMap,O=new WeakMap,X=new WeakMap,z=new WeakMap,J=new WeakMap,K=new WeakMap,Y=new WeakMap,Z=new WeakMap,G=new WeakMap,Q=new WeakMap,$=new WeakMap,ee=new WeakMap;var te,R,re,ne,ie,se;class Dt{constructor(e,r){f(this,te);f(this,R);N(this,"field");f(this,re,p(()=>Pe(this.field.errors)));f(this,ne,p(()=>({errors:this.field.errors,errorProps:this.errorProps})));f(this,ie,p(()=>({id:i(this,R).current,"data-fs-error":c(i(this,re)),"data-fs-field-errors":"","aria-live":"assertive"})));f(this,se,p(()=>({"data-fs-field-error":"","data-fs-error":c(i(this,re))})));_(this,te,e.ref),_(this,R,e.id),this.field=r,Te({id:i(this,R),ref:i(this,te),onRefChange:n=>{this.field.errorNode=n}})}get snippetProps(){return c(i(this,ne))}set snippetProps(e){m(i(this,ne),e)}get fieldErrorsProps(){return c(i(this,ie))}set fieldErrorsProps(e){m(i(this,ie),e)}get errorProps(){return c(i(this,se))}set errorProps(e){m(i(this,se),e)}}te=new WeakMap,R=new WeakMap,re=new WeakMap,ne=new WeakMap,ie=new WeakMap,se=new WeakMap;var oe,W,ue;class kt{constructor(e,r){f(this,oe);f(this,W);N(this,"field");f(this,ue,p(()=>({id:i(this,W).current,"data-fs-error":Pe(this.field.errors),"data-fs-description":""})));_(this,oe,e.ref),_(this,W,e.id),this.field=r,Te({id:i(this,W),ref:i(this,oe),onRefChange:n=>{this.field.descriptionNode=n}})}get props(){return c(i(this,ue))}set props(e){m(i(this,ue),e)}}oe=new WeakMap,W=new WeakMap,ue=new WeakMap;var fe,ce,ae,de;class Ut{constructor(e,r){f(this,fe);N(this,"field");N(this,"labelId",h(xe()));f(this,ce,T(De(xe())));f(this,ae,p(()=>({id:this.id,name:this.field.name,"data-fs-error":Pe(this.field.errors),"aria-describedby":Ot({fieldErrorsId:this.field.errorId,descriptionId:this.field.descriptionId,errors:this.field.errors}),"aria-invalid":Wt(this.field.errors),"aria-required":Rt(this.field.constraints),"data-fs-control":""})));f(this,de,p(()=>({id:this.labelId.current,"data-fs-label":"","data-fs-error":Pe(this.field.errors),for:this.id})));_(this,fe,e.id),this.field=r,Nt(()=>i(this,fe).current,n=>{this.id=n})}get id(){return c(i(this,ce))}set id(e){m(i(this,ce),e,!0)}get props(){return c(i(this,ae))}set props(e){m(i(this,ae),e)}get labelProps(){return c(i(this,de))}set labelProps(e){m(i(this,de),e)}}fe=new WeakMap,ce=new WeakMap,ae=new WeakMap,de=new WeakMap;var le,L;class Vt{constructor(e,r){f(this,le);f(this,L);N(this,"control");_(this,le,e.ref),_(this,L,e.id),this.control=r,this.control.labelId=i(this,L),Te({id:i(this,L),ref:i(this,le)})}get props(){return this.control.labelProps}}le=new WeakMap,L=new WeakMap;const Ke=Symbol.for("formsnap.form-field"),Ye=Symbol.for("formsnap.form-control");function qt(t){return Ue(Ke,new Lt(t))}function Oe(){return Ve(Ke)}function Ht(t){return new Dt(t,Oe())}function dr(t){return new kt(t,Oe())}function Mt(t){return Ue(Ye,new Ut(t,Oe()))}function Xt(){return Ve(Ye)}function lr(t){return new Vt(t,Xt())}function zt(t,e){he(e,!0);const r=qt({form:h.with(()=>e.form),name:h.with(()=>e.name)});var n=S(),s=C(n);D(s,()=>e.children??je,()=>r.snippetProps),y(t,n),pe()}function Jt(t,e){he(e,!0);let r=V(e,"id",19,xe);const n=Mt({id:h.with(()=>r())});var s=S(),o=C(s);D(o,()=>e.children??je,()=>({props:n.props})),y(t,s),pe()}var Kt=Se("<div> </div>"),Yt=Se("<div><!></div>");function Zt(t,e){he(e,!0);let r=V(e,"id",19,xe),n=V(e,"ref",15,null),s=Ae(e,["$$slots","$$events","$$legacy","id","ref","children","child"]);const o=Ht({id:h.with(()=>r()),ref:h.with(()=>n(),d=>n(d))}),u=p(()=>At(s,o.fieldErrorsProps));var l=S(),P=C(l);{var g=d=>{var b=S(),v=C(b),I=rt(()=>({props:c(u),...o.snippetProps}));D(v,()=>e.child,()=>c(I)),y(d,b)},a=d=>{var b=Yt();_e(b,()=>({...c(u)}));var v=ve(b);{var I=w=>{var x=S(),E=C(x);D(E,()=>e.children,()=>o.snippetProps),y(w,x)},F=w=>{var x=S(),E=C(x);Me(E,17,()=>o.field.errors,nt,(A,k)=>{var j=Kt();_e(j,()=>({...o.errorProps}));var Ce=ve(j,!0);ge(j),qe(()=>He(Ce,c(k))),y(A,j)}),y(w,x)};Fe(v,w=>{e.children?w(I):w(F,!1)})}ge(b),y(d,b)};Fe(P,d=>{e.child?d(g):d(a,!1)})}y(t,l),pe()}var Gt=Se("<div> </div>");function hr(t,e){he(e,!0);let r=V(e,"ref",15,null),n=Ae(e,["$$slots","$$events","$$legacy","ref","class","errorClasses","children"]);var s=S(),o=C(s);const u=p(()=>Ee("text-destructive text-sm font-medium",e.class));Xe(o,()=>Zt,(l,P)=>{P(l,it({get class(){return c(u)}},()=>n,{get ref(){return r()},set ref(a){r(a)},children:(a,d)=>{let b=()=>d==null?void 0:d().errors,v=()=>d==null?void 0:d().errorProps;var I=S(),F=C(I);{var w=E=>{var A=S(),k=C(A);D(k,()=>e.children,()=>({errors:b(),errorProps:v()})),y(E,A)},x=E=>{var A=S(),k=C(A);Me(k,16,b,j=>j,(j,Ce)=>{var me=Gt();_e(me,Ge=>({...v(),class:Ge}),[()=>Ee(e.errorClasses)]);var Ze=ve(me,!0);ge(me),qe(()=>He(Ze,Ce)),y(j,me)}),y(E,A)};Fe(F,E=>{e.children?E(w):E(x,!1)})}y(a,I)},$$slots:{default:!0}}))}),y(t,s),pe()}var Qt=Se("<div><!></div>");function pr(t,e){he(e,!0);let r=V(e,"ref",15,null),n=Ae(e,["$$slots","$$events","$$legacy","ref","class","form","name","children"]);var s=S(),o=C(s);Xe(o,()=>zt,(u,l)=>{l(u,{get form(){return e.form},get name(){return e.name},children:(g,a)=>{let d=()=>a==null?void 0:a().constraints,b=()=>a==null?void 0:a().errors,v=()=>a==null?void 0:a().tainted,I=()=>a==null?void 0:a().value;var F=Qt();_e(F,x=>({"data-slot":"form-item",class:x,...n}),[()=>Ee("space-y-2",e.class)]);var w=ve(F);D(w,()=>e.children??je,()=>({constraints:d(),errors:b(),tainted:v(),value:I()})),ge(F),ut(F,x=>r(x),()=>r()),y(g,F)},$$slots:{default:!0}})}),y(t,s),pe()}const mr=Jt;export{mr as C,pr as F,hr as a,dr as b,h as c,lr as d,At as m,xe as u};
