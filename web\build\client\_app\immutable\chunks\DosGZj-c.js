import{c as d}from"./CGtH72Kl.js";const u="fqw18aoo",f="production",l="2023-05-03";d({projectId:u,dataset:f,apiVersion:l,useCdn:!0});function g(s,t){return m(s,t||{})}function m(s,t={}){if(!s||!s.asset)return"";const r=s.asset._ref||"";if(!r.startsWith("image-"))return"";const[,n,p,a]=r.split("-");if(!n)return"";let e=`https://cdn.sanity.io/images/${u}/${f}/${n}`;a?e+=`.${a}`:e+=".jpg";const i=[],o=t.width||800;i.push(`w=${o}`),t.height&&i.push(`h=${t.height}`);const h=t.fit||"max";i.push(`fit=${h}`),i.push("auto=format");const c=t.quality||80;return i.push(`q=${c}`),i.length>0&&(e+=`?${i.join("&")}`),e}export{g as u};
