import{c as p,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as d,s as c}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function M(r,o){const s=d(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"}],["path",{d:"M3 3v5h5"}],["path",{d:"M12 7v5l4 2"}]];f(r,c({name:"history"},()=>s,{get iconNode(){return a},children:(e,$)=>{var t=p(),n=l(t);m(n,o,"default",{},null),i(e,t)},$$slots:{default:!0}}))}export{M as H};
