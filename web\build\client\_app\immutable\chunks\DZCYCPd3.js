import{c as p,a as d}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as c,s as m}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(o,t){const a=c(t,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"M8 2v4"}],["path",{d:"M16 2v4"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2"}],["path",{d:"M3 10h18"}]];f(o,m({name:"calendar"},()=>a,{get iconNode(){return e},children:(s,h)=>{var r=p(),n=i(r);l(n,t,"default",{},null),d(s,r)},$$slots:{default:!0}}))}export{y as C};
