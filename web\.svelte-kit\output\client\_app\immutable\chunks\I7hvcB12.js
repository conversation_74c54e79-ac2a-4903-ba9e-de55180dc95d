var ft=Object.defineProperty;var at=e=>{throw TypeError(e)};var pt=(e,t,r)=>t in e?ft(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var f=(e,t,r)=>pt(e,typeof t!="symbol"?t+"":t,r),nt=(e,t,r)=>t.has(e)||at("Cannot "+r);var s=(e,t,r)=>(nt(e,t,"read from private field"),r?r.call(e):t.get(e)),d=(e,t,r)=>t.has(e)?at("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r);var N=(e,t,r)=>(nt(e,t,"access private method"),r);import{c as x,a as p,f as Y}from"./BasJTneF.js";import{k as lt,v as bt,g as i,d as C,x as l,i as mt,p as q,f as R,a as J,c as Z,au as $,r as tt}from"./CGmarHxI.js";import{s as D,c as Tt}from"./ncUU1dSD.js";import{i as et}from"./u21ee2wt.js";import{e as rt}from"./B-Xjo-Yt.js";import{p as n,r as Q,s as _t}from"./Btcx8l8F.js";import{u as U,w as ut,b as h,m as st}from"./BfX7a-t9.js";import{u as it}from"./CnMg5bH0.js";import{c as wt}from"./BvdI7LR8.js";import{S as dt}from"./BJIrNhIJ.js";import{C as yt}from"./DuoUhxYL.js";import{g as ot,l as It,h as ht,b as St,k as Ct,m as xt}from"./Bd3zs5C6.js";import{u as Rt}from"./OXTnUuEm.js";import{S as kt,d as At}from"./CIOgxH3l.js";import{n as Pt}from"./DX6rZLP_.js";const Dt="data-tabs-root",Bt="data-tabs-list",ct="data-tabs-trigger",Mt="data-tabs-content";var E,G;class Et{constructor(t){f(this,"opts");f(this,"rovingFocusGroup");d(this,E,lt(bt([])));f(this,"valueToTriggerId",new dt);f(this,"valueToContentId",new dt);d(this,G,l(()=>({id:this.opts.id.current,"data-orientation":ot(this.opts.orientation.current),[Dt]:""})));this.opts=t,U(t),this.rovingFocusGroup=Rt({candidateAttr:ct,rootNodeId:this.opts.id,loop:this.opts.loop,orientation:this.opts.orientation})}get triggerIds(){return i(s(this,E))}set triggerIds(t){C(s(this,E),t,!0)}registerTrigger(t,r){return this.triggerIds.push(t),this.valueToTriggerId.set(r,t),()=>{this.triggerIds=this.triggerIds.filter(o=>o!==t),this.valueToTriggerId.delete(r)}}registerContent(t,r){return this.valueToContentId.set(r,t),()=>{this.valueToContentId.delete(r)}}setValue(t){this.opts.value.current=t}get props(){return i(s(this,G))}set props(t){C(s(this,G),t)}}E=new WeakMap,G=new WeakMap;var z,O;class Gt{constructor(t,r){f(this,"opts");f(this,"root");d(this,z,l(()=>this.root.opts.disabled.current));d(this,O,l(()=>({id:this.opts.id.current,role:"tablist","aria-orientation":St(this.root.opts.orientation.current),"data-orientation":ot(this.root.opts.orientation.current),[Bt]:"","data-disabled":ht(i(s(this,z)))})));this.opts=t,this.root=r,U(t)}get props(){return i(s(this,O))}set props(t){C(s(this,O),t)}}z=new WeakMap,O=new WeakMap;var k,I,A,H,P,j,V;class Ot{constructor(t,r){d(this,P);f(this,"opts");f(this,"root");d(this,k,l(()=>this.root.opts.value.current===this.opts.value.current));d(this,I,l(()=>this.opts.disabled.current||this.root.opts.disabled.current));d(this,A,lt(0));d(this,H,l(()=>this.root.valueToContentId.get(this.opts.value.current)));d(this,V,l(()=>({id:this.opts.id.current,role:"tab","data-state":gt(i(s(this,k))),"data-value":this.opts.value.current,"data-orientation":ot(this.root.opts.orientation.current),"data-disabled":ht(i(s(this,I))),"aria-selected":xt(i(s(this,k))),"aria-controls":i(s(this,H)),[ct]:"",disabled:Ct(i(s(this,I))),tabindex:i(s(this,A)),onclick:this.onclick,onfocus:this.onfocus,onkeydown:this.onkeydown})));this.opts=t,this.root=r,U(t),ut([()=>this.opts.id.current,()=>this.opts.value.current],([o,u])=>this.root.registerTrigger(o,u)),mt(()=>{this.root.triggerIds.length,i(s(this,k))||!this.root.opts.value.current?C(s(this,A),0):C(s(this,A),-1)}),this.onfocus=this.onfocus.bind(this),this.onclick=this.onclick.bind(this),this.onkeydown=this.onkeydown.bind(this)}onfocus(t){this.root.opts.activationMode.current!=="automatic"||i(s(this,I))||N(this,P,j).call(this)}onclick(t){i(s(this,I))||N(this,P,j).call(this)}onkeydown(t){if(!i(s(this,I))){if(t.key===kt||t.key===At){t.preventDefault(),N(this,P,j).call(this);return}this.root.rovingFocusGroup.handleKeydown(this.opts.ref.current,t)}}get props(){return i(s(this,V))}set props(t){C(s(this,V),t)}}k=new WeakMap,I=new WeakMap,A=new WeakMap,H=new WeakMap,P=new WeakSet,j=function(){this.root.opts.value.current!==this.opts.value.current&&this.root.setValue(this.opts.value.current)},V=new WeakMap;var F,K,L;class Vt{constructor(t,r){f(this,"opts");f(this,"root");d(this,F,l(()=>this.root.opts.value.current===this.opts.value.current));d(this,K,l(()=>this.root.valueToTriggerId.get(this.opts.value.current)));d(this,L,l(()=>({id:this.opts.id.current,role:"tabpanel",hidden:It(!i(s(this,F))),tabindex:0,"data-value":this.opts.value.current,"data-state":gt(i(s(this,F))),"aria-labelledby":i(s(this,K)),[Mt]:""})));this.opts=t,this.root=r,U(t),ut([()=>this.opts.id.current,()=>this.opts.value.current],([o,u])=>this.root.registerContent(o,u))}get props(){return i(s(this,L))}set props(t){C(s(this,L),t)}}F=new WeakMap,K=new WeakMap,L=new WeakMap;const W=new yt("Tabs.Root");function Ft(e){return W.set(new Et(e))}function Lt(e){return new Ot(e,W.get())}function Nt(e){return new Gt(e,W.get())}function de(e){return new Vt(e,W.get())}function gt(e){return e?"active":"inactive"}var jt=Y("<div><!></div>");function zt(e,t){q(t,!0);let r=n(t,"id",19,it),o=n(t,"ref",15,null),u=n(t,"value",15,""),m=n(t,"onValueChange",3,Pt),T=n(t,"orientation",3,"horizontal"),_=n(t,"loop",3,!0),w=n(t,"activationMode",3,"automatic"),y=n(t,"disabled",3,!1),B=Q(t,["$$slots","$$events","$$legacy","id","ref","value","onValueChange","orientation","loop","activationMode","disabled","children","child"]);const c=Ft({id:h.with(()=>r()),value:h.with(()=>u(),v=>{u(v),m()(v)}),orientation:h.with(()=>T()),loop:h.with(()=>_()),activationMode:h.with(()=>w()),disabled:h.with(()=>y()),ref:h.with(()=>o(),v=>o(v))}),g=l(()=>st(B,c.props));var a=x(),b=R(a);{var M=v=>{var S=x(),X=R(S);D(X,()=>t.child,()=>({props:i(g)})),p(v,S)},vt=v=>{var S=jt();rt(S,()=>({...i(g)}));var X=Z(S);D(X,()=>t.children??$),tt(S),p(v,S)};et(b,v=>{t.child?v(M):v(vt,!1)})}p(e,a),J()}var Ht=Y("<div><!></div>");function Kt(e,t){q(t,!0);let r=n(t,"id",19,it),o=n(t,"ref",15,null),u=Q(t,["$$slots","$$events","$$legacy","child","children","id","ref"]);const m=Nt({id:h.with(()=>r()),ref:h.with(()=>o(),c=>o(c))}),T=l(()=>st(u,m.props));var _=x(),w=R(_);{var y=c=>{var g=x(),a=R(g);D(a,()=>t.child,()=>({props:i(T)})),p(c,g)},B=c=>{var g=Ht();rt(g,()=>({...i(T)}));var a=Z(g);D(a,()=>t.children??$),tt(g),p(c,g)};et(w,c=>{t.child?c(y):c(B,!1)})}p(e,_),J()}var qt=Y("<button><!></button>");function le(e,t){q(t,!0);let r=n(t,"disabled",3,!1),o=n(t,"id",19,it),u=n(t,"type",3,"button"),m=n(t,"ref",15,null),T=Q(t,["$$slots","$$events","$$legacy","child","children","disabled","id","type","value","ref"]);const _=Lt({id:h.with(()=>o()),disabled:h.with(()=>r()??!1),value:h.with(()=>t.value),ref:h.with(()=>m(),a=>m(a))}),w=l(()=>st(T,_.props,{type:u()}));var y=x(),B=R(y);{var c=a=>{var b=x(),M=R(b);D(M,()=>t.child,()=>({props:i(w)})),p(a,b)},g=a=>{var b=qt();rt(b,()=>({...i(w)}));var M=Z(b);D(M,()=>t.children??$),tt(b),p(a,b)};et(B,a=>{t.child?a(c):a(g,!1)})}p(e,y),J()}function ue(e,t){q(t,!0);let r=n(t,"ref",15,null),o=Q(t,["$$slots","$$events","$$legacy","ref","class"]);var u=x(),m=R(u);const T=l(()=>Tt("border-border bg-muted/30 text-muted-foreground flex  w-full flex-row items-center justify-center gap-2 divide-x border-b border-t p-[4px] px-2 ",t.class));wt(m,()=>Kt,(_,w)=>{w(_,_t({"data-slot":"tabs-list",get class(){return i(T)}},()=>o,{get ref(){return r()},set ref(y){r(y)}}))}),p(e,u),J()}const he=zt;export{he as R,ue as T,le as a,de as u};
