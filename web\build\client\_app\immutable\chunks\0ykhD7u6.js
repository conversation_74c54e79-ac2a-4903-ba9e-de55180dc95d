var B=Object.defineProperty;var R=r=>{throw TypeError(r)};var D=(r,t,a)=>t in r?B(r,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):r[t]=a;var x=(r,t,a)=>D(r,typeof t!="symbol"?t+"":t,a),E=(r,t,a)=>t.has(r)||R("Cannot "+a);var _=(r,t,a)=>(E(r,t,"read from private field"),a?a.call(r):t.get(r)),A=(r,t,a)=>t.has(r)?R("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,a);import{c as S,a as p,f as H}from"./BasJTneF.js";import{x as w,g as m,d as j,p as P,f as b,a as T,c as q,au as C,r as F}from"./CGmarHxI.js";import{c as G}from"./BvdI7LR8.js";import{p as i,r as y,s as J}from"./Btcx8l8F.js";import{s as O,c as K}from"./ncUU1dSD.js";import{i as L}from"./u21ee2wt.js";import{e as M}from"./B-Xjo-Yt.js";import{u as N,b as l,m as Q}from"./BfX7a-t9.js";import{g as U,a as V,b as W}from"./Bd3zs5C6.js";import{u as X}from"./CnMg5bH0.js";const Y="data-separator-root";var s;class Z{constructor(t){x(this,"opts");A(this,s,w(()=>({id:this.opts.id.current,role:this.opts.decorative.current?"none":"separator","aria-orientation":W(this.opts.orientation.current),"aria-hidden":V(this.opts.decorative.current),"data-orientation":U(this.opts.orientation.current),[Y]:""})));this.opts=t,N(t)}get props(){return m(_(this,s))}set props(t){j(_(this,s),t)}}s=new WeakMap;function $(r){return new Z(r)}var tt=H("<div><!></div>");function rt(r,t){P(t,!0);let a=i(t,"id",19,X),n=i(t,"ref",15,null),d=i(t,"decorative",3,!1),h=i(t,"orientation",3,"horizontal"),u=y(t,["$$slots","$$events","$$legacy","id","ref","child","children","decorative","orientation"]);const v=$({ref:l.with(()=>n(),e=>n(e)),id:l.with(()=>a()),decorative:l.with(()=>d()),orientation:l.with(()=>h())}),c=w(()=>Q(u,v.props));var f=S(),z=b(f);{var k=e=>{var o=S(),g=b(o);O(g,()=>t.child,()=>({props:m(c)})),p(e,o)},I=e=>{var o=tt();M(o,()=>({...m(c)}));var g=q(o);O(g,()=>t.children??C),F(o),p(e,o)};L(z,e=>{t.child?e(k):e(I,!1)})}p(r,f),T()}function mt(r,t){P(t,!0);let a=i(t,"ref",15,null),n=y(t,["$$slots","$$events","$$legacy","ref","class"]);var d=S(),h=b(d);const u=w(()=>K("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=vertical]:h-full data-[orientation=horizontal]:w-full data-[orientation=vertical]:w-px",t.class));G(h,()=>rt,(v,c)=>{c(v,J({"data-slot":"separator-root",get class(){return m(u)}},()=>n,{get ref(){return a()},set ref(f){a(f)}}))}),p(r,d),T()}export{mt as S};
