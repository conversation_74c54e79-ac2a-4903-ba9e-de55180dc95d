var k=Object.defineProperty;var x=t=>{throw TypeError(t)};var A=(t,e,r)=>e in t?k(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var y=(t,e,r)=>A(t,typeof e!="symbol"?e+"":e,r),B=(t,e,r)=>e.has(t)||x("Cannot "+r);var b=(t,e,r)=>(B(t,e,"read from private field"),r?r.call(t):e.get(t)),L=(t,e,r)=>e.has(t)?x("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,r);import{c as g,a as f,f as D}from"./BasJTneF.js";import{x as w,g as m,d as j,p as S,f as v,a as T,c as q,au as z,r as C}from"./CGmarHxI.js";import{c as E}from"./BvdI7LR8.js";import{p as _,r as I,s as F}from"./Btcx8l8F.js";import{s as R,c as G}from"./ncUU1dSD.js";import{i as H}from"./u21ee2wt.js";import{e as J}from"./B-Xjo-Yt.js";import{u as K,b as P,m as M}from"./BfX7a-t9.js";import{u as N}from"./CnMg5bH0.js";const Q="data-label-root";var a;class U{constructor(e){y(this,"opts");L(this,a,w(()=>({id:this.opts.id.current,[Q]:"",onmousedown:this.onmousedown})));this.opts=e,this.onmousedown=this.onmousedown.bind(this),K(e)}onmousedown(e){e.detail>1&&e.preventDefault()}get props(){return m(b(this,a))}set props(e){j(b(this,a),e)}}a=new WeakMap;function V(t){return new U(t)}var W=D("<label><!></label>");function X(t,e){S(e,!0);let r=_(e,"id",19,N),n=_(e,"ref",15,null),i=I(e,["$$slots","$$events","$$legacy","children","child","id","ref","for"]);const c=V({id:P.with(()=>r()),ref:P.with(()=>n(),o=>n(o))}),l=w(()=>M(i,c.props,{for:e.for}));var d=g(),u=v(d);{var p=o=>{var s=g(),h=v(s);R(h,()=>e.child,()=>({props:m(l)})),f(o,s)},O=o=>{var s=W();J(s,()=>({...m(l),for:e.for}));var h=q(s);R(h,()=>e.children??z),C(s),f(o,s)};H(u,o=>{e.child?o(p):o(O,!1)})}f(t,d),T()}function ie(t,e){S(e,!0);let r=_(e,"ref",15,null),n=I(e,["$$slots","$$events","$$legacy","ref","class"]);var i=g(),c=v(i);const l=w(()=>G("flex select-none items-center gap-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50 group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50",e.class));E(c,()=>X,(d,u)=>{u(d,F({"data-slot":"label",get class(){return m(l)}},()=>n,{get ref(){return r()},set ref(p){r(p)}}))}),f(t,i),T()}export{ie as L};
