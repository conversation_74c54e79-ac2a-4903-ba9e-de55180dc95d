import{c as i,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as $,s as h}from"./Btcx8l8F.js";import{I as p}from"./D4f2twK-.js";function w(o,t){const r=$(t,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"M7 7h10v10"}],["path",{d:"M7 17 17 7"}]];p(o,h({name:"arrow-up-right"},()=>r,{get iconNode(){return n},children:(a,u)=>{var e=i(),s=d(e);l(s,t,"default",{},null),c(a,e)},$$slots:{default:!0}}))}function y(o,t){const r=$(t,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"M13 17V9"}],["path",{d:"M18 17V5"}],["path",{d:"M3 3v16a2 2 0 0 0 2 2h16"}],["path",{d:"M8 17v-3"}]];p(o,h({name:"chart-column-increasing"},()=>r,{get iconNode(){return n},children:(a,u)=>{var e=i(),s=d(e);l(s,t,"default",{},null),c(a,e)},$$slots:{default:!0}}))}function N(o,t){const r=$(t,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"m9 10 2 2 4-4"}],["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2"}],["path",{d:"M12 17v4"}],["path",{d:"M8 21h8"}]];p(o,h({name:"monitor-check"},()=>r,{get iconNode(){return n},children:(a,u)=>{var e=i(),s=d(e);l(s,t,"default",{},null),c(a,e)},$$slots:{default:!0}}))}function x(o,t){const r=$(t,["children","$$slots","$$events","$$legacy"]),n=[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2"}]];p(o,h({name:"workflow"},()=>r,{get iconNode(){return n},children:(a,u)=>{var e=i(),s=d(e);l(s,t,"default",{},null),c(a,e)},$$slots:{default:!0}}))}export{w as A,y as C,N as M,x as W};
