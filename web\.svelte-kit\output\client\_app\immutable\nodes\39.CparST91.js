import{f as P,a,t as T,c as le}from"../chunks/BasJTneF.js";import{p as Ct,i as Ve,f as N,a as Tt,s,c as v,d as Y,k as pe,g as e,x as A,r as d,n as h,t as K}from"../chunks/CGmarHxI.js";import{s as G}from"../chunks/CIt1g2O9.js";import{i as $}from"../chunks/u21ee2wt.js";import{e as qe,i as Ge}from"../chunks/C3w0v0gR.js";import{c as S}from"../chunks/BvdI7LR8.js";import{a as Rt}from"../chunks/B-Xjo-Yt.js";import{g as At}from"../chunks/CmxjS0TN.js";import{p as Ft}from"../chunks/Btcx8l8F.js";import{C as Qe}from"../chunks/DuGukytH.js";import{C as We}from"../chunks/Cdn-N1RY.js";import{B as W}from"../chunks/B1K98fMG.js";import{S as Nt}from"../chunks/D9yI7a4E.js";import{L as Xe}from"../chunks/BvvicRXk.js";import{B as me}from"../chunks/DaBofrVv.js";import{I as jt}from"../chunks/DMTMHyMa.js";import"../chunks/CgXBgsce.js";import{t as Z}from"../chunks/DjPYYl4Z.js";import{g as Ye}from"../chunks/BiJhC7W5.js";import{S as zt}from"../chunks/C6g8ubaU.js";import{n as Mt}from"../chunks/xCOJ4D9d.js";import{S as Lt}from"../chunks/C2MdR6K0.js";import{S as It,a as Bt,b as Q,R as Dt}from"../chunks/CGK0g3x_.js";import{S as Ot}from"../chunks/yW0TxTga.js";import{S as Ut}from"../chunks/BoNCRmBc.js";import{R as ne}from"../chunks/qwsZpUIl.js";import{C as _e}from"../chunks/DW7T7T22.js";import{X as Et}from"../chunks/CnpHcmx3.js";import{S as Ht}from"../chunks/B2lQHLf_.js";import{B as Jt}from"../chunks/hA0h0kTo.js";import{T as Vt}from"../chunks/C33xR25f.js";import{C as qt}from"../chunks/BBNNmnYR.js";import{C as Gt}from"../chunks/DkmCSZhC.js";import{I as Qt}from"../chunks/BuYRPDDz.js";import{M as Ke}from"../chunks/QtAhPN2H.js";import{T as Wt}from"../chunks/CTO_B1Jk.js";import{B as Xt}from"../chunks/CDnvByek.js";var Yt=P("<!> Mark All as Read",1),Kt=P("<!> Filters <!>",1),Zt=P("<!> Clear",1),er=P("<!> <!>",1),tr=P("<!> <!> <!> <!> <!> <!> <!> <!> <!> <!>",1),rr=P("<!> <!>",1),ar=P("Include Read <!>",1),sr=P('<div class="border-border flex flex-wrap items-center gap-4 border-t px-4 py-3"><div class="flex items-center gap-2"><!> <!></div> <div class="flex items-center space-x-2"><!> <!></div></div>'),or=P('<div class="border-border border-b px-4 py-2"><p class="text-muted-foreground text-sm"> </p></div>'),ir=P('<div class="border-border border-b px-4 py-2"><p class="text-muted-foreground text-sm"> <!> <!></p></div>'),lr=P('<!> <h2 class="mb-2 text-xl font-semibold"> </h2> <p class="text-muted-foreground"> </p> <!>',1),nr=P('<div class="bg-background/80 absolute inset-0 z-10 flex items-center justify-center backdrop-blur-sm"><div class="bg-card flex items-center gap-3 rounded-lg border p-4 shadow-lg"><!> <span class="text-sm font-medium">Applying filters...</span></div></div>'),dr=(Se,de,C)=>de(e(C).id),cr=P('<a class="text-primary hover:text-primary/80 mt-2 inline-block text-sm">View Details</a>'),vr=P('<div class="flex items-start gap-4"><div class="bg-primary/10 flex-shrink-0 rounded-full p-2"><!></div> <div class="flex-1"><div class="flex flex-wrap items-center gap-2"><h3 class="text-base font-medium"> </h3> <!> <!> <!></div> <p class="text-muted-foreground mt-1 text-sm"> </p> <!> <p class="text-muted-foreground mt-2 text-xs"> </p></div> <div class="flex items-center gap-2"><!> <!></div></div>'),fr=P('<div class="space-y-4"></div>'),ur=P('<div class="relative"><!> <!></div>'),pr=P("<!> Previous",1),mr=P('<div class="flex h-8 w-8 items-center justify-center">...</div>'),_r=P("Next <!>",1),gr=P('<div class="mt-6 flex items-center justify-center gap-2"><!> <div class="flex items-center gap-1"></div> <!></div>'),hr=P('<!> <div class="flex flex-col"><div class="border-border flex items-center justify-between border-b p-4"><h1 class="text-2xl font-bold">Notifications</h1> <div class="flex items-center gap-3"><!></div></div> <div class="border-border border-b"><div class="flex items-center gap-3 px-4 py-3"><div class="relative flex-1"><!> <!></div> <!> <!></div> <!></div> <!> <!> <!></div>',1);function ra(Se,de){Ct(de,!0);const C=Ft(de,"data",7);let ae=A(()=>C().notifications),F=A(()=>C().pagination),Ce=A(()=>C().filters),Ze=A(()=>C().unreadCount),U=A(()=>e(Ce).type||"all"),se=A(()=>e(Ce).includeRead||!1),ce=pe(!1),E=pe(!1),H=pe(""),ve=pe(!1),ge=A(()=>{if(!e(H).trim())return e(ae);const t=e(H).toLowerCase().trim();return e(ae).filter(r=>r.title.toLowerCase().includes(t)||r.message.toLowerCase().includes(t)||r.type&&r.type.toLowerCase().includes(t))}),he=A(()=>e(U)!=="all"||e(se)),Te=A(()=>e(H).trim()!==""),Re=A(()=>e(he)||e(Te));function et(t){switch(t){case"job":return Xt;case"application":return _e;case"interview":return Ke;case"error":return Wt;case"success":return _e;case"message":return Ke;case"info":case"system":default:return Qt}}function tt(t){const r=new Date(t),l=new Date().getTime()-r.getTime(),u=Math.floor(l/1e3),f=Math.floor(u/60),c=Math.floor(f/60),o=Math.floor(c/24);return u<60?"just now":f<60?`${f} minute${f!==1?"s":""} ago`:c<24?`${c} hour${c!==1?"s":""} ago`:o<7?`${o} day${o!==1?"s":""} ago`:r.toLocaleDateString()}async function Ae(t){try{(await fetch("/api/notifications",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"markAsRead",id:t})})).ok?(C().notifications=C().notifications.map(i=>i.id===t?{...i,read:!0}:i),C().unreadCount>0&&C().unreadCount--):Z.error("Failed to mark notification as read")}catch(r){console.error("Error marking notification as read:",r),Z.error("Failed to mark notification as read")}}async function rt(t){try{if((await fetch("/api/notifications",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"delete",id:t})})).ok){const i=C().notifications.find(l=>l.id===t);C().notifications=C().notifications.filter(l=>l.id!==t),i&&!i.read&&C().unreadCount>0&&C().unreadCount--,Z.success("Notification deleted")}else Z.error("Failed to delete notification")}catch(r){console.error("Error deleting notification:",r),Z.error("Failed to delete notification")}}async function at(){Y(ce,!0);try{(await fetch("/api/notifications/mark-all-read",{method:"POST"})).ok?(C().notifications=C().notifications.map(r=>({...r,read:!0})),C().unreadCount=0,Z.success("All notifications marked as read")):Z.error("Failed to mark all notifications as read")}catch(t){console.error("Error marking all notifications as read:",t),Z.error("Failed to mark all notifications as read")}finally{Y(ce,!1)}}function $e(t,r){Y(E,!0);const i=new URLSearchParams;i.set("page","1");const l=t!==void 0?t:e(U),u=r!==void 0?r:e(se);l!=="all"&&i.set("type",l),u&&i.set("includeRead","true"),Ye(`?${i.toString()}`)}function Fe(){Y(H,""),$e("all",!1)}function xe(t){Y(E,!0);const r=new URL(window.location.href);r.searchParams.set("page",t.toString()),Ye(r.search)}let st=A(()=>e(ae).map(t=>({...t,timestamp:new Date(t.createdAt)})));Ve(()=>{Mt.set(e(st))}),Ve(()=>{C().notifications,Y(E,!1)});var Ne=hr(),je=N(Ne);zt(je,{title:"Notifications - Hirli",description:"Manage your notifications and stay up-to-date with the latest news and updates from Hirli."});var ze=s(je,2),ye=v(ze),Me=s(v(ye),2),ot=v(Me);{var it=t=>{W(t,{variant:"outline",size:"sm",onclick:at,get disabled(){return e(ce)},children:(r,i)=>{var l=Yt(),u=N(l);{var f=o=>{ne(o,{class:"mr-2 h-4 w-4 animate-spin"})},c=o=>{_e(o,{class:"mr-2 h-4 w-4"})};$(u,o=>{e(ce)?o(f):o(c,!1)})}h(),a(r,l)},$$slots:{default:!0}})};$(ot,t=>{e(Ze)>0&&t(it)})}d(Me),d(ye);var be=s(ye,2),we=v(be),Pe=v(we),Le=v(Pe);Ot(Le,{class:"text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"});var lt=s(Le,2);jt(lt,{type:"text",placeholder:"Search notifications...",class:"pl-10",get value(){return e(H)},set value(t){Y(H,t,!0)}}),d(Pe);var Ie=s(Pe,2);const nt=A(()=>e(ve)?"!bg-primary/10 border-border/50 border":"");W(Ie,{variant:"outline",size:"sm",onclick:()=>Y(ve,!e(ve)),get class(){return`gap-2 ${e(nt)??""}`},children:(t,r)=>{var i=Kt(),l=N(i);Ut(l,{class:"h-4 w-4"});var u=s(l,2);{var f=c=>{me(c,{variant:"secondary",class:"ml-1 h-5 w-5 rounded-full p-0 text-xs",children:(o,R)=>{h();var n=T();K(()=>G(n,(e(U)!=="all"?1:0)+(e(se)?1:0))),a(o,n)},$$slots:{default:!0}})};$(u,c=>{e(he)&&c(f)})}a(t,i)},$$slots:{default:!0}});var dt=s(Ie,2);{var ct=t=>{W(t,{variant:"ghost",size:"sm",onclick:Fe,get disabled(){return e(E)},class:"gap-2",children:(r,i)=>{var l=Zt(),u=N(l);{var f=o=>{ne(o,{class:"h-4 w-4 animate-spin"})},c=o=>{Et(o,{class:"h-4 w-4"})};$(u,o=>{e(E)?o(f):o(c,!1)})}h(),a(r,l)},$$slots:{default:!0}})};$(dt,t=>{e(Re)&&t(ct)})}d(we);var vt=s(we,2);{var ft=t=>{var r=sr(),i=v(r),l=v(i);Xe(l,{for:"type-select",class:"text-sm font-medium",children:(R,n)=>{h();var x=T("Type:");a(R,x)},$$slots:{default:!0}});var u=s(l,2);S(u,()=>Dt,(R,n)=>{n(R,{type:"single",get value(){return e(U)},onValueChange:x=>{$e(x,void 0)},children:(x,p)=>{var y=rr(),k=N(y);S(k,()=>It,(M,D)=>{D(M,{class:"w-40",id:"type-select",get disabled(){return e(E)},children:(z,J)=>{var X=er(),V=N(X);{var L=I=>{ne(I,{class:"mr-2 h-4 w-4 animate-spin"})};$(V,I=>{e(E)&&I(L)})}var q=s(V,2);const ee=A(()=>e(U)==="all"?"All Types":e(U).charAt(0).toUpperCase()+e(U).slice(1));S(q,()=>Ht,(I,oe)=>{oe(I,{get placeholder(){return e(ee)}})}),a(z,X)},$$slots:{default:!0}})});var j=s(k,2);S(j,()=>Bt,(M,D)=>{D(M,{class:"max-h-60",children:(z,J)=>{var X=tr(),V=N(X);S(V,()=>Q,(m,_)=>{_(m,{value:"all",children:(b,B)=>{h();var w=T("All Types");a(b,w)},$$slots:{default:!0}})});var L=s(V,2);S(L,()=>Q,(m,_)=>{_(m,{value:"system",children:(b,B)=>{h();var w=T("System");a(b,w)},$$slots:{default:!0}})});var q=s(L,2);S(q,()=>Q,(m,_)=>{_(m,{value:"job",children:(b,B)=>{h();var w=T("Job");a(b,w)},$$slots:{default:!0}})});var ee=s(q,2);S(ee,()=>Q,(m,_)=>{_(m,{value:"application",children:(b,B)=>{h();var w=T("Application");a(b,w)},$$slots:{default:!0}})});var I=s(ee,2);S(I,()=>Q,(m,_)=>{_(m,{value:"interview",children:(b,B)=>{h();var w=T("Interview");a(b,w)},$$slots:{default:!0}})});var oe=s(I,2);S(oe,()=>Q,(m,_)=>{_(m,{value:"message",children:(b,B)=>{h();var w=T("Message");a(b,w)},$$slots:{default:!0}})});var te=s(oe,2);S(te,()=>Q,(m,_)=>{_(m,{value:"success",children:(b,B)=>{h();var w=T("Success");a(b,w)},$$slots:{default:!0}})});var re=s(te,2);S(re,()=>Q,(m,_)=>{_(m,{value:"error",children:(b,B)=>{h();var w=T("Error");a(b,w)},$$slots:{default:!0}})});var fe=s(re,2);S(fe,()=>Q,(m,_)=>{_(m,{value:"warning",children:(b,B)=>{h();var w=T("Warning");a(b,w)},$$slots:{default:!0}})});var ie=s(fe,2);S(ie,()=>Q,(m,_)=>{_(m,{value:"info",children:(b,B)=>{h();var w=T("Info");a(b,w)},$$slots:{default:!0}})}),a(z,X)},$$slots:{default:!0}})}),a(x,y)},$$slots:{default:!0}})}),d(i);var f=s(i,2),c=v(f);Nt(c,{id:"includeRead",get checked(){return e(se)},get disabled(){return e(E)},onCheckedChange:R=>{$e(void 0,R)}});var o=s(c,2);Xe(o,{for:"includeRead",class:"text-sm",children:(R,n)=>{h();var x=ar(),p=s(N(x));{var y=k=>{ne(k,{class:"ml-2 inline h-3 w-3 animate-spin"})};$(p,k=>{e(E)&&k(y)})}a(R,x)},$$slots:{default:!0}}),d(f),d(r),a(t,r)};$(vt,t=>{e(ve)&&t(ft)})}d(be);var Be=s(be,2);{var ut=t=>{var r=or(),i=v(r),l=v(i);d(i),d(r),K(()=>G(l,`Showing ${e(ge).length??""} of ${e(ae).length??""} notifications for
        "${e(H)??""}"`)),a(t,r)},pt=(t,r)=>{{var i=l=>{var u=ir(),f=v(u),c=v(f),o=s(c);{var R=y=>{var k=T();K(j=>G(k,`• Type: ${j??""}`),[()=>e(U).charAt(0).toUpperCase()+e(U).slice(1)]),a(y,k)};$(o,y=>{e(U)!=="all"&&y(R)})}var n=s(o,2);{var x=y=>{var k=T("• Including Read");a(y,k)},p=y=>{var k=T("• Unread Only");a(y,k)};$(n,y=>{e(se)?y(x):y(p,!1)})}d(f),d(u),K(()=>G(c,`Showing ${e(ae).length??""} filtered notifications `)),a(l,u)};$(t,l=>{e(he)&&l(i)},r)}};$(Be,t=>{e(Te)?t(ut):t(pt,!1)})}var De=s(Be,2);{var mt=t=>{var r=le(),i=N(r);S(i,()=>Qe,(l,u)=>{u(l,{class:"m-4",children:(f,c)=>{var o=le(),R=N(o);S(R,()=>We,(n,x)=>{x(n,{class:"flex flex-col items-center justify-center py-12",children:(p,y)=>{var k=lr(),j=N(k);Jt(j,{class:"text-muted-foreground mb-4 h-12 w-12 opacity-20"});var M=s(j,2),D=v(M,!0);d(M);var z=s(M,2),J=v(z,!0);d(z);var X=s(z,2);{var V=L=>{W(L,{variant:"outline",size:"sm",onclick:Fe,class:"mt-3",children:(q,ee)=>{h();var I=T("Clear filters");a(q,I)},$$slots:{default:!0}})};$(X,L=>{e(Re)&&L(V)})}K((L,q)=>{G(D,L),G(J,q)},[()=>e(H).trim()?"No matching notifications":"No notifications",()=>e(H).trim()?"Try adjusting your search or filters.":"You don't have any notifications yet."]),a(p,k)},$$slots:{default:!0}})}),a(f,o)},$$slots:{default:!0}})}),a(t,r)},_t=t=>{var r=ur(),i=v(r);{var l=f=>{var c=nr(),o=v(c),R=v(o);ne(R,{class:"h-5 w-5 animate-spin"}),h(2),d(o),d(c),a(f,c)};$(i,f=>{e(E)&&f(l)})}var u=s(i,2);S(u,()=>Lt,(f,c)=>{c(f,{orientation:"vertical",class:"h-[calc(100vh-240px)] overflow-hidden p-4",children:(o,R)=>{var n=fr();qe(n,21,()=>e(ge),Ge,(x,p)=>{var y=le();const k=A(()=>et(e(p).type));var j=N(y);const M=A(()=>e(p).read?"opacity-80 transition-opacity hover:opacity-100":"");S(j,()=>Qe,(D,z)=>{z(D,{get class(){return e(M)},children:(J,X)=>{var V=le(),L=N(V);S(L,()=>We,(q,ee)=>{ee(q,{class:"p-4",children:(I,oe)=>{var te=vr(),re=v(te),fe=v(re);S(fe,()=>e(k),(g,O)=>{O(g,{class:"text-primary h-5 w-5"})}),d(re);var ie=s(re,2),m=v(ie),_=v(m),b=v(_,!0);d(_);var B=s(_,2);{var w=g=>{me(g,{variant:"default",class:"h-1.5 w-1.5 rounded-full p-0"})};$(B,g=>{e(p).read||g(w)})}var Oe=s(B,2);{var $t=g=>{me(g,{variant:"outline",class:"text-xs",children:(O,ue)=>{h();var St=T("Global");a(O,St)},$$slots:{default:!0}})};$(Oe,g=>{e(p).global&&g($t)})}var xt=s(Oe,2);me(xt,{variant:"outline",class:"text-xs capitalize",children:(g,O)=>{h();var ue=T();K(()=>G(ue,e(p).type||"info")),a(g,ue)},$$slots:{default:!0}}),d(m);var ke=s(m,2),yt=v(ke,!0);d(ke);var Ue=s(ke,2);{var bt=g=>{var O=cr();O.__click=[dr,Ae,p],K(()=>Rt(O,"href",e(p).url)),a(g,O)};$(Ue,g=>{e(p).url&&g(bt)})}var Ee=s(Ue,2),wt=v(Ee,!0);d(Ee),d(ie);var He=s(ie,2),Je=v(He);{var Pt=g=>{W(g,{variant:"ghost",size:"sm",class:"h-8 w-8 p-0",onclick:()=>Ae(e(p).id),children:(O,ue)=>{_e(O,{class:"h-4 w-4"})},$$slots:{default:!0}})};$(Je,g=>{e(p).read||g(Pt)})}var kt=s(Je,2);W(kt,{variant:"ghost",size:"sm",class:"text-destructive hover:text-destructive/80 h-8 w-8 p-0",onclick:()=>rt(e(p).id),children:(g,O)=>{Vt(g,{class:"h-4 w-4"})},$$slots:{default:!0}}),d(He),d(te),K(g=>{G(b,e(p).title),G(yt,e(p).message),G(wt,g)},[()=>tt(e(p).createdAt)]),a(I,te)},$$slots:{default:!0}})}),a(J,V)},$$slots:{default:!0}})}),a(x,y)}),d(n),a(o,n)},$$slots:{default:!0}})}),d(r),a(t,r)};$(De,t=>{e(ge).length===0?t(mt):t(_t,!1)})}var gt=s(De,2);{var ht=t=>{var r=gr(),i=v(r);const l=A(()=>e(F).page===1);W(i,{variant:"outline",size:"sm",get disabled(){return e(l)},onclick:()=>xe(e(F).page-1),class:"gap-1",children:(o,R)=>{var n=pr(),x=N(n);qt(x,{class:"h-4 w-4"}),h(),a(o,n)},$$slots:{default:!0}});var u=s(i,2);qe(u,21,()=>Array(e(F).totalPages),Ge,(o,R,n)=>{var x=le(),p=N(x);{var y=j=>{const M=A(()=>e(F).page===n+1?"default":"outline");W(j,{get variant(){return e(M)},size:"sm",onclick:()=>xe(n+1),class:"h-8 w-8 p-0",children:(D,z)=>{h();var J=T();J.nodeValue=n+1,a(D,J)},$$slots:{default:!0}})},k=(j,M)=>{{var D=z=>{var J=mr();a(z,J)};$(j,z=>{(n+1===2&&e(F).page>3||n+1===e(F).totalPages-1&&e(F).page<e(F).totalPages-2)&&z(D)},M)}};$(p,j=>{e(F).totalPages<=7||n+1===1||n+1===e(F).totalPages||n+1>=e(F).page-1&&n+1<=e(F).page+1?j(y):j(k,!1)})}a(o,x)}),d(u);var f=s(u,2);const c=A(()=>e(F).page===e(F).totalPages);W(f,{variant:"outline",size:"sm",get disabled(){return e(c)},onclick:()=>xe(e(F).page+1),class:"gap-1",children:(o,R)=>{h();var n=_r(),x=s(N(n));Gt(x,{class:"h-4 w-4"}),a(o,n)},$$slots:{default:!0}}),d(r),a(t,r)};$(gt,t=>{e(F).totalPages>1&&!e(H).trim()&&t(ht)})}d(ze),a(Se,Ne),Tt()}At(["click"]);export{ra as component};
