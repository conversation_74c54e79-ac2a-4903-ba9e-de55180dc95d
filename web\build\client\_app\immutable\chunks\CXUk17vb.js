import{c as i,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as c}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as p,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function v(s,o){const e=p(o,["children","$$slots","$$events","$$legacy"]),n=[["line",{x1:"12",x2:"12",y1:"20",y2:"10"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16"}]];f(s,d({name:"chart-no-axes-column-increasing"},()=>e,{get iconNode(){return n},children:(t,_)=>{var r=i(),a=c(r);m(a,o,"default",{},null),l(t,r)},$$slots:{default:!0}}))}export{v as C};
