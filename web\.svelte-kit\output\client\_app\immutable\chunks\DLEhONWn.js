import{f as c,a as o}from"./BasJTneF.js";import{p as v,c as f,au as u,r as g,a as m}from"./CGmarHxI.js";import{c as h,s as x}from"./ncUU1dSD.js";import{e as p}from"./B-Xjo-Yt.js";import{b as _}from"./5V1tIHTN.js";import{p as d,r as b}from"./Btcx8l8F.js";import{c as y}from"./DM07Bv7T.js";const A=y({base:"relative grid w-full grid-cols-[0_1fr] items-start gap-y-0.5 rounded-lg border px-4 py-3 text-sm has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] has-[>svg]:gap-x-3 [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card *:data-[slot=alert-description]:text-destructive/90 [&>svg]:text-current"}},defaultVariants:{variant:"default"}});var P=c("<div><!></div>");function E(i,t){v(t,!0);let s=d(t,"ref",15,null),l=d(t,"variant",3,"default"),a=b(t,["$$slots","$$events","$$legacy","ref","class","variant","children"]);var r=P();p(r,n=>({"data-slot":"alert",class:n,...a,role:"alert"}),[()=>h(A({variant:l()}),t.class)]);var e=f(r);x(e,()=>t.children??u),g(r),_(r,n=>s(n),()=>s()),o(i,r),m()}var V=c("<div><!></div>");function F(i,t){v(t,!0);let s=d(t,"ref",15,null),l=b(t,["$$slots","$$events","$$legacy","ref","class","children"]);var a=V();p(a,e=>({"data-slot":"alert-description",class:e,...l}),[()=>h("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t.class)]);var r=f(a);x(r,()=>t.children??u),g(a),_(a,e=>s(e),()=>s()),o(i,a),m()}var j=c("<div><!></div>");function G(i,t){v(t,!0);let s=d(t,"ref",15,null),l=b(t,["$$slots","$$events","$$legacy","ref","class","children"]);var a=j();p(a,e=>({"data-slot":"alert-title",class:e,...l}),[()=>h("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t.class)]);var r=f(a);x(r,()=>t.children??u),g(a),_(a,e=>s(e),()=>s()),o(i,a),m()}export{E as A,G as a,F as b};
