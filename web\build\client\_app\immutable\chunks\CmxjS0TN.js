import{a0 as m,P as T,z as O,K as R,aR as f,aS as _,aT as S,R as k,aK as L,b2 as x,ao as q,au as v,m as D,b3 as W,aJ as j,g as B,d as C}from"./CGmarHxI.js";function Q(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const U=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function Y(e){return U.includes(e)}const z={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function Z(e){return e=e.toLowerCase(),z[e]??e}const K=["touchstart","touchmove"];function ee(e){return K.includes(e)}const F=["textarea","script","style","title"];function te(e){return F.includes(e)}function re(e,t){if(t){const r=document.body;e.autofocus=!0,m(()=>{document.activeElement===r&&e.focus()})}}function ne(e){T&&O(e)!==null&&R(e)}let w=!1;function G(){w||(w=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{var t;if(!e.defaultPrevented)for(const r of e.target.elements)(t=r.__on_r)==null||t.call(r)})},{capture:!0}))}function A(e){var t=S,r=k;f(null),_(null);try{return e()}finally{f(t),_(r)}}function ae(e,t,r,n=r){e.addEventListener(t,()=>A(r));const u=e.__on_r;u?e.__on_r=()=>{u(),n(!0)}:e.__on_r=()=>n(!0),G()}const H=new Set,J=new Set;function ue(e){if(!T)return;e.removeAttribute("onload"),e.removeAttribute("onerror");const t=e.__e;t!==void 0&&(e.__e=void 0,queueMicrotask(()=>{e.isConnected&&e.dispatchEvent(t)}))}function M(e,t,r,n={}){function u(a){if(n.capture||X.call(t,a),!a.cancelBubble)return A(()=>r==null?void 0:r.call(this,a))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?m(()=>{t.addEventListener(e,u,n)}):t.addEventListener(e,u,n),u}function ie(e,t,r,n={}){var u=M(t,e,r,n);return()=>{e.removeEventListener(t,u,n)}}function se(e,t,r,n,u){var a={capture:n,passive:u},i=M(e,t,r,a);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&L(()=>{t.removeEventListener(e,i,a)})}function oe(e){for(var t=0;t<e.length;t++)H.add(e[t]);for(var r of J)r(e)}function X(e){var E;var t=this,r=t.ownerDocument,n=e.type,u=((E=e.composedPath)==null?void 0:E.call(e))||[],a=u[0]||e.target,i=0,g=e.__root;if(g){var d=u.indexOf(g);if(d!==-1&&(t===document||t===window)){e.__root=t;return}var h=u.indexOf(t);if(h===-1)return;d<=h&&(i=d)}if(a=u[i]||e.target,a!==t){x(e,"currentTarget",{configurable:!0,get(){return a||r}});var P=S,N=k;f(null),_(null);try{for(var s,y=[];a!==null;){var p=a.assignedSlot||a.parentNode||a.host||null;try{var o=a["__"+n];if(o!=null&&(!a.disabled||e.target===a))if(q(o)){var[I,...V]=o;I.apply(a,[e,...V])}else o.call(a,e)}catch(c){s?y.push(c):s=c}if(e.cancelBubble||p===t||p===null)break;a=p}if(s){for(let c of y)queueMicrotask(()=>{throw c});throw s}}finally{e.__root=t,delete e.currentTarget,f(P),_(N)}}}let l=!1,b=Symbol();function ce(e,t,r){const n=r[t]??(r[t]={store:null,source:D(void 0),unsubscribe:v});if(n.store!==e&&!(b in r))if(n.unsubscribe(),n.store=e??null,e==null)n.source.v=void 0,n.unsubscribe=v;else{var u=!0;n.unsubscribe=W(e,a=>{u?n.source.v=a:C(n.source,a)}),u=!1}return e&&b in r?j(e):B(n.source)}function le(e,t,r){let n=r[t];return n&&n.store!==e&&(n.unsubscribe(),n.unsubscribe=v),e}function fe(e,t){return e.set(t),t}function _e(){const e={};function t(){L(()=>{for(var r in e)e[r].unsubscribe();x(e,b,{enumerable:!1,value:!0})})}return[e,t]}function de(e,t,r){return e.set(r),t}function pe(e){var t=l;try{return l=!1,[e(),l]}finally{l=t}}export{ce as a,de as b,H as c,pe as d,se as e,te as f,oe as g,X as h,ee as i,ne as j,ue as k,ae as l,le as m,Q as n,ie as o,M as p,re as q,J as r,_e as s,Z as t,G as u,Y as v,A as w,fe as x};
