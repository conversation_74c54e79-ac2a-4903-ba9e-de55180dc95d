import{w as U,a as W,f as E}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as L,s as e,n as R,t as Y,aI as q}from"./CGmarHxI.js";import{h as B}from"./BwZiefMD.js";import{a as t}from"./B-Xjo-Yt.js";import{p as o}from"./Btcx8l8F.js";var D=U(E(`<meta name="description"/> <meta name="keywords"/> <meta name="author"/> <meta name="viewport" content="width=device-width, initial-scale=1.0"/> <meta name="robots" content="index, follow"/> <meta name="theme-color"/> <meta name="application-name" content="Auto Apply"/> <meta name="apple-mobile-web-app-title" content="Auto Apply"/> <meta name="mobile-web-app-capable" content="yes"/> <meta name="apple-mobile-web-app-status-bar-style" content="default"/> <meta property="og:type"/> <meta property="og:url"/> <meta property="og:title"/> <meta property="og:description"/> <meta property="og:image"/> <meta property="og:locale"/> <meta property="og:site_name" content="Auto Apply"/> <meta property="twitter:card"/> <meta property="twitter:url"/> <meta property="twitter:title"/> <meta property="twitter:description"/> <meta property="twitter:image"/> <meta property="twitter:site" content="@autoapplyio"/> <link rel="canonical"/> <link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon/favicon-32x32.png"/> <link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon/favicon-16x16.png"/> <link rel="apple-touch-icon" sizes="180x180" href="/assets/favicon/apple-touch-icon.png"/> <link rel="manifest" href="/site.webmanifest"/> <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "url": "https://www.autoapply.io",
      "sameAs": ["https://twitter.com/autoapplyio", "https://www.linkedin.com/company/autoapply"],
      "logo": "https://www.autoapply.io/assets/logo.png",
      "name": "Auto Apply",
      "description": "Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings, auto-fills applications, and tracks your progress.",
      "email": "<EMAIL>"
    }
  <\/script> <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "url": "{url}",
      "name": "{title}",
      "description": "{description}",
      "inLanguage": "en-US",
      "isPartOf": {
        "@type": "WebSite",
        "url": "https://www.autoapply.io",
        "name": "Auto Apply",
        "description": "Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings, auto-fills applications, and tracks your progress."
      },
      "potentialAction": {
        "@type": "ReadAction",
        "target": ["{url}"]
      }
    }
  <\/script>`,1));function Q(F,a){let i=o(a,"title",8,"Auto Apply - Your job application automation assistant."),n=o(a,"description",8,"Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings, auto-fills applications, and tracks your progress."),j=o(a,"keywords",8,"job application, automation, resume, job search, AI, career, employment"),p=o(a,"image",8,"/assets/og-image.jpg"),r=o(a,"url",8,"https://autoapply.io"),x=o(a,"type",8,"website"),O=o(a,"twitterCard",8,"summary_large_image"),I=o(a,"author",8,"Auto Apply"),S=o(a,"locale",8,"en_US"),z=o(a,"themeColor",8,"#4f46e5");B(C=>{var s=D(),l=L(s),m=e(l,2),c=e(m,2),u=e(c,6),y=e(u,10),g=e(y,2),h=e(g,2),w=e(h,2),d=e(w,2),f=e(d,2),v=e(f,4),b=e(v,2),A=e(b,2),_=e(A,2),k=e(_,2),P=e(k,4);R(12),Y(()=>{q.title=i(),t(l,"content",n()),t(m,"content",j()),t(c,"content",I()),t(u,"content",z()),t(y,"content",x()),t(g,"content",r()),t(h,"content",i()),t(w,"content",n()),t(d,"content",p()),t(f,"content",S()),t(v,"content",O()),t(b,"content",r()),t(A,"content",i()),t(_,"content",n()),t(k,"content",p()),t(P,"href",r())}),W(C,s)})}export{Q as S};
