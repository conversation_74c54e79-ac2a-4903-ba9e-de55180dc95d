import{c as xe,a as e,f as o,t as g}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as Mr,f as d,g as t,e as O<PERSON>,a as <PERSON>,t as K,s as r,c as a,d as he,m as Le,r as s,n as S,o as Ie}from"../chunks/CGmarHxI.js";import{s as L}from"../chunks/CIt1g2O9.js";import{i as D}from"../chunks/u21ee2wt.js";import{e as Me,i as De}from"../chunks/C3w0v0gR.js";import{c as Vr}from"../chunks/BvdI7LR8.js";import{a as hr}from"../chunks/DDUgF6Ik.js";import{s as Yr}from"../chunks/B-Xjo-Yt.js";import{i as kr}from"../chunks/BIEMS98f.js";import{p as ar}from"../chunks/Btcx8l8F.js";import{s as Gr,b as br,a as ke}from"../chunks/CmxjS0TN.js";import{S as xr}from"../chunks/Zo6ILzvY.js";import{s as yr}from"../chunks/B8blszX7.js";import{z as Pr}from"../chunks/CrHU05dq.js";import{C as Ae}from"../chunks/DuGukytH.js";import{C as Ue}from"../chunks/Cdn-N1RY.js";import{C as Qe}from"../chunks/BkJY4La4.js";import{C as We}from"../chunks/GwmmX_iF.js";import{C as Xe}from"../chunks/D50jIuLr.js";import{F as Ze,C as er,a as wr}from"../chunks/FeejBSkx.js";import{R as Jr,S as Kr,a as Lr,b as Tr}from"../chunks/CGK0g3x_.js";import{a as Qr,R as Wr,T as Xr}from"../chunks/I7hvcB12.js";import{s as Zr}from"../chunks/BBa424ah.js";import{c as et}from"../chunks/ncUU1dSD.js";import{B as be}from"../chunks/B1K98fMG.js";import{I as Sr}from"../chunks/DMTMHyMa.js";import{A as rt,b as tt,a as at}from"../chunks/CE9Bts7j.js";import{t as le}from"../chunks/DjPYYl4Z.js";import{S as st}from"../chunks/C6g8ubaU.js";import{U as rr}from"../chunks/BSHZ37s_.js";import{U as Cr}from"../chunks/CzSntoiK.js";import{P as tr}from"../chunks/DR5zc253.js";import{T as ot}from"../chunks/DmZyh-PW.js";import{T as it}from"../chunks/C33xR25f.js";import{F as Fr}from"../chunks/CXvW3J0s.js";import{M as lt}from"../chunks/yPulTJ2h.js";import{S as nt}from"../chunks/B2lQHLf_.js";import{o as jr,e as dt,s as Ir}from"../chunks/C8B1VUaq.js";function vt(Ne,te){Mr(te,!1);let ne=ar(te,"value",8),Re=ar(te,"className",8,"");kr();const ae=Oe(()=>et("ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow",Re()));Qr(Ne,{get class(){return t(ae)},get value(){return ne()},children:(ye,ue)=>{var ce=xe(),B=d(ce);Zr(B,te,"default",{},null),e(ye,ce)},$$slots:{default:!0}}),Dr()}var mt=o('<div class="flex items-center gap-2"><!> <span> </span></div>'),ut=o('<span class="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">Owner</span>'),ct=o('<span class="rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">Member</span>'),ft=o('<div class="flex items-center justify-between"><div class="flex items-center gap-4"><div class="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><!> <!></div></div> <div><!></div></div>'),pt=o("<!> <!>",1),_t=o('<span class="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">Owner</span>'),$t=o('<div class="flex items-center justify-between py-3"><div class="flex items-center gap-3"><!> <div><p class="text-sm font-medium"> </p> <p class="text-muted-foreground text-xs"> </p></div></div> <div class="flex items-center gap-2"><!></div></div>'),gt=o('<p class="text-muted-foreground py-3 text-sm">No team members found.</p>'),ht=o('<div class="space-y-4"><h4 class="text-sm font-medium">Team Members</h4> <div class="divide-y"><!></div></div>'),bt=o("<!> <!>",1),xt=o("<!> Create a Team",1),yt=o('<div class="mb-4 flex justify-center"><div class="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full"><!></div></div> <h3 class="mb-2 text-lg font-medium">No Teams Found</h3> <p class="text-muted-foreground">You are not a member of any teams yet.</p> <div class="mt-6"><!></div>',1),Pt=o('<div class="space-y-6"><!></div>'),wt=o("<!> <!>",1),Tt=o("<!> <!>",1),St=o("<!> <!>",1),Ct=o("<div><!></div>"),Ft=o('<!> <div class="flex items-center gap-2"><div class="relative flex-1"><!> <!></div> <!></div>',1),jt=o("<!> <!>",1),It=o("<!> ",1),Mt=o('<form method="POST" action="?/invite" class="space-y-6"><!> <!></form>'),Dt=o("<!> <!>",1),kt=o("<!> <!>",1),At=o("<!> <!>",1),Ut=o("<!> <!>",1),Ot=o("<!> ",1),Nt=o('<form method="POST" action="?/createTeam" class="space-y-6"><!> <!></form>'),Rt=o("<!> <!>",1),zt=o("<!> <!>",1),Et=o('<div class="bg-muted rounded-lg border p-4"><h3 class="mb-2 text-sm font-medium">Form Debug</h3> <div class="space-y-4"><div><h4 class="text-xs font-medium">Invite Form</h4> <!></div> <div><h4 class="text-xs font-medium">Team Form</h4> <!></div></div></div>'),qt=o('<!> <div class="space-y-6"><div class="border-border flex flex-col justify-between border-b p-6"><div class="flex items-center justify-between"><div><h2 class="text-lg font-semibold">Team Settings</h2> <p class="text-muted-foreground text-sm">Manage your teams, invite team members, and create new teams.</p></div> <div class="flex items-center gap-3"><div class="flex items-center gap-2"><div></div> <span class="text-muted-foreground text-xs"><!></span></div> <!></div></div></div> <div class="grid grid-cols-1 gap-6 md:grid-cols-[1fr_300px]"><div><!></div> <!></div></div>',1);function Ca(Ne,te){Mr(te,!1);const[ne,Re]=Gr(),ae=()=>ke(qe,"$inviteData",ne),ye=()=>ke(Ar,"$inviteSubmitting",ne),ue=()=>ke(lr,"$teamData",ne),ce=()=>ke(Ur,"$teamSubmitting",ne);let B=ar(te,"data",8);const sr=[{id:"teams",label:"My Teams",icon:rr},{id:"invite",label:"Invite Members",icon:Cr},{id:"create",label:"Create Team",icon:tr}];let ze=Le("teams"),de=Le("saved"),or;function O(i,y=3e3){he(de,i),clearTimeout(or),i!=="saved"&&(or=setTimeout(()=>{he(de,"saved")},y))}const Ee=yr(B().inviteForm,{validators:Pr(jr({email:Ir().email("Invalid email address"),role:dt(["member","admin"]).default("member")})),dataType:"json",onSubmit:()=>{O("saving")},onUpdated:({form:i})=>{i.valid&&(O("saved"),le.success("Invitation sent successfully"))},onError:()=>{O("error"),le.error("Failed to send invitation")}}),{form:qe,enhance:Be,submitting:Ar}=Ee,ir=yr(B().teamForm,{validators:Pr(jr({name:Ir().min(1,"Team name is required")})),dataType:"json",onSubmit:()=>{O("saving")},onUpdated:({form:i})=>{i.valid&&(O("saved"),le.success("Team created successfully"))},onError:()=>{O("error"),le.error("Failed to create team")}}),{form:lr,enhance:He,submitting:Ur}=ir;async function Or(i,y,P){if(!confirm(`Are you sure you want to remove ${P} from the team?`))return;O("saving");const C=new FormData;C.append("teamId",i),C.append("memberId",y);try{const w=await(await fetch("?/removeTeamMember",{method:"POST",body:C})).json();w.success?(O("saved"),le.success(`${P} has been removed from the team`)):(O("error"),le.error(w.error||"Failed to remove team member"))}catch{O("error"),le.error("An error occurred while removing the team member")}}let Pe=Le(!1);kr();var nr=qt(),dr=d(nr);st(dr,{title:"Team Settings - Hirli",description:"Manage your teams, invite team members, and create new teams for collaboration on job applications and resume profiles.",keywords:"team management, team collaboration, invite members, create team, job application teams",url:"https://hirli.com/dashboard/settings/team"});var vr=r(dr,2),Ve=a(vr),mr=a(Ve),ur=r(a(mr),2),Ye=a(ur),cr=a(Ye),fr=r(cr,2),Nr=a(fr);{var Rr=i=>{var y=g("Saving...");e(i,y)},zr=(i,y)=>{{var P=F=>{var w=g("Error saving");e(F,w)},C=F=>{var w=g("Changes saved");e(F,w)};D(i,F=>{t(de)==="error"?F(P):F(C,!1)},y)}};D(Nr,i=>{t(de)==="saving"?i(Rr):i(zr,!1)})}s(fr),s(Ye);var Er=r(Ye,2);be(Er,{type:"button",variant:"outline",size:"sm",class:"hidden md:flex",onclick:()=>he(Pe,!t(Pe)),children:(i,y)=>{S();var P=g();K(()=>L(P,`${t(Pe)?"Hide":"Show"} Debug`)),e(i,P)},$$slots:{default:!0}}),s(ur),s(mr),s(Ve);var pr=r(Ve,2),Ge=a(pr),qr=a(Ge);Wr(qr,{get value(){return t(ze)},onValueChange:i=>he(ze,i),children:(i,y)=>{var P=zt(),C=d(P);Xr(C,{class:"w-full",children:(w,se)=>{var we=xe(),_r=d(we);Me(_r,1,()=>sr,De,(Te,fe)=>{vt(Te,{get value(){return t(fe).id},children:(Je,$r)=>{var N=mt(),Q=a(N);Vr(Q,()=>t(fe).icon,(R,j)=>{j(R,{class:"h-4 w-4"})});var ve=r(Q,2),pe=a(ve,!0);s(ve),s(N),K(()=>L(pe,t(fe).label)),e(Je,N)},$$slots:{default:!0}})}),e(w,we)},$$slots:{default:!0}});var F=r(C,2);Me(F,1,()=>sr,De,(w,se)=>{ot(w,{get value(){return t(se).id},class:"mt-6",children:(we,_r)=>{var Te=xe(),fe=d(Te);{var Je=N=>{var Q=Pt(),ve=a(Q);{var pe=j=>{var oe=xe(),H=d(oe);Me(H,1,()=>B().teams,De,(W,h)=>{Ae(W,{children:(k,V)=>{var f=bt(),b=d(f);We(b,{class:"p-6",children:(n,T)=>{var v=ft(),l=a(v),p=a(l),_=a(p);rr(_,{class:"text-primary h-5 w-5"}),s(p);var A=r(p,2),c=a(A);Xe(c,{children:(m,$)=>{S();var I=g();K(()=>L(I,t(h).name)),e(m,I)},$$slots:{default:!0}});var U=r(c,2);Qe(U,{children:(m,$)=>{S();var I=g();K(()=>{var q,_e;return L(I,`${(((q=t(h).members)==null?void 0:q.length)||0)??""} member${((_e=t(h).members)==null?void 0:_e.length)!==1?"s":""}`)}),e(m,I)},$$slots:{default:!0}}),s(A),s(l);var X=r(l,2),E=a(X);{var u=m=>{var $=ut();e(m,$)},Y=m=>{var $=ct();e(m,$)};D(E,m=>{t(h).ownerId===B().user.id?m(u):m(Y,!1)})}s(X),s(v),e(n,v)},$$slots:{default:!0}});var z=r(b,2);Ue(z,{class:"p-6 pt-0",children:(n,T)=>{var v=ht(),l=r(a(v),2),p=a(l);{var _=c=>{var U=xe(),X=d(U);Me(X,1,()=>t(h).members,De,(E,u)=>{var Y=$t(),m=a(Y),$=a(m);rt($,{class:"h-8 w-8",children:(x,M)=>{var Z=pt(),ee=d(Z);{var G=ie=>{const J=Oe(()=>t(u).user.name||"User");at(ie,{get src(){return t(u).user.image},get alt(){return t(J)}})};D(ee,ie=>{var J;(J=t(u).user)!=null&&J.image&&ie(G)})}var ge=r(ee,2);tt(ge,{class:"rounded-full border bg-neutral-200",children:(ie,J)=>{S();var je=g();K(re=>L(je,re),[()=>{var re,gr;return((gr=(re=t(u).user)==null?void 0:re.name)==null?void 0:gr.charAt(0))||"U"}],Oe),e(ie,je)},$$slots:{default:!0}}),e(x,Z)},$$slots:{default:!0}});var I=r($,2),q=a(I),_e=a(q,!0);s(q);var me=r(q,2),Ke=a(me,!0);s(me),s(I),s(m);var Se=r(m,2),$e=a(Se);{var Ce=x=>{var M=_t();e(x,M)},Fe=(x,M)=>{{var Z=ee=>{be(ee,{variant:"ghost",size:"sm",class:"text-red-500 hover:bg-red-50 hover:text-red-600",onclick:()=>{var G;return Or(t(h).id,t(u).id,((G=t(u).user)==null?void 0:G.name)||"this user")},children:(G,ge)=>{it(G,{class:"h-4 w-4"})},$$slots:{default:!0}})};D(x,ee=>{t(h).ownerId===B().user.id&&ee(Z)},M)}};D($e,x=>{var M;((M=t(u).user)==null?void 0:M.id)===t(h).ownerId?x(Ce):x(Fe,!1)})}s(Se),s(Y),K(()=>{var x,M;L(_e,((x=t(u).user)==null?void 0:x.name)||"Unknown User"),L(Ke,(M=t(u).user)==null?void 0:M.email)}),e(E,Y)}),e(c,U)},A=c=>{var U=gt();e(c,U)};D(p,c=>{t(h).members&&t(h).members.length>0?c(_):c(A,!1)})}s(l),s(v),e(n,v)},$$slots:{default:!0}}),e(k,f)},$$slots:{default:!0}})}),e(j,oe)},R=j=>{Ae(j,{children:(oe,H)=>{Ue(oe,{class:"p-6 text-center",children:(W,h)=>{var k=yt(),V=d(k),f=a(V),b=a(f);rr(b,{class:"text-primary h-6 w-6"}),s(f),s(V);var z=r(V,6),n=a(z);be(n,{onclick:()=>he(ze,"create"),children:(T,v)=>{var l=xt(),p=d(l);tr(p,{class:"mr-2 h-4 w-4"}),S(),e(T,l)},$$slots:{default:!0}}),s(z),e(W,k)},$$slots:{default:!0}})},$$slots:{default:!0}})};D(ve,j=>{B().teams&&B().teams.length>0?j(pe):j(R,!1)})}s(Q),e(N,Q)},$r=(N,Q)=>{{var ve=R=>{Ae(R,{children:(j,oe)=>{var H=Dt(),W=d(H);We(W,{class:"p-6",children:(k,V)=>{var f=wt(),b=d(f);Xe(b,{children:(n,T)=>{S();var v=g("Invite Team Members");e(n,v)},$$slots:{default:!0}});var z=r(b,2);Qe(z,{children:(n,T)=>{S();var v=g("Send invitations to join your team.");e(n,v)},$$slots:{default:!0}}),e(k,f)},$$slots:{default:!0}});var h=r(W,2);Ue(h,{class:"p-6 pt-0",children:(k,V)=>{var f=Mt(),b=a(f);Ze(b,{get form(){return Ee},name:"email",children:(n,T)=>{var v=jt(),l=d(v);er(l,{children:(_,A)=>{var c=Ft(),U=d(c);Fr(U,{children:($,I)=>{S();var q=g("Email Address");e($,q)},$$slots:{default:!0}});var X=r(U,2),E=a(X),u=a(E);lt(u,{class:"text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"});var Y=r(u,2);Sr(Y,{type:"email",placeholder:"<EMAIL>",class:"pl-10",get value(){return ae().email},set value($){br(qe,Ie(ae).email=$,Ie(ae))},$$legacy:!0}),s(E);var m=r(E,2);Ze(m,{get form(){return Ee},name:"role",children:($,I)=>{er($,{children:(q,_e)=>{var me=Ct(),Ke=a(me);const Se=Oe(()=>ae().role||"member");Jr(Ke,{type:"single",get value(){return t(Se)},onValueChange:$e=>qe.update(Ce=>({...Ce,role:$e})),children:($e,Ce)=>{var Fe=St(),x=d(Fe);Kr(x,{class:"w-32",children:(Z,ee)=>{nt(Z,{placeholder:"Role"})},$$slots:{default:!0}});var M=r(x,2);Lr(M,{class:"max-h-60",children:(Z,ee)=>{var G=Tt(),ge=d(G);Tr(ge,{value:"member",children:(J,je)=>{S();var re=g("Member");e(J,re)},$$slots:{default:!0}});var ie=r(ge,2);Tr(ie,{value:"admin",children:(J,je)=>{S();var re=g("Admin");e(J,re)},$$slots:{default:!0}}),e(Z,G)},$$slots:{default:!0}}),e($e,Fe)},$$slots:{default:!0}}),s(me),e(q,me)},$$slots:{default:!0}})},$$slots:{default:!0}}),s(X),e(_,c)},$$slots:{default:!0}});var p=r(l,2);wr(p,{}),e(n,v)},$$slots:{default:!0}});var z=r(b,2);be(z,{type:"submit",get disabled(){return ye()},class:"w-full",children:(n,T)=>{var v=It(),l=d(v);Cr(l,{class:"mr-2 h-4 w-4"});var p=r(l);K(()=>L(p,` ${ye()?"Sending Invitation...":"Send Invitation"}`)),e(n,v)},$$slots:{default:!0}}),s(f),hr(f,n=>Be==null?void 0:Be(n)),e(k,f)},$$slots:{default:!0}}),e(j,H)},$$slots:{default:!0}})},pe=(R,j)=>{{var oe=H=>{Ae(H,{children:(W,h)=>{var k=Rt(),V=d(k);We(V,{class:"p-6",children:(b,z)=>{var n=kt(),T=d(n);Xe(T,{children:(l,p)=>{S();var _=g("Create a New Team");e(l,_)},$$slots:{default:!0}});var v=r(T,2);Qe(v,{children:(l,p)=>{S();var _=g("Set up a new team to collaborate with others.");e(l,_)},$$slots:{default:!0}}),e(b,n)},$$slots:{default:!0}});var f=r(V,2);Ue(f,{class:"p-6 pt-0",children:(b,z)=>{var n=Nt(),T=a(n);Ze(T,{get form(){return ir},name:"name",children:(l,p)=>{var _=Ut(),A=d(_);er(A,{children:(U,X)=>{var E=At(),u=d(E);Fr(u,{children:(m,$)=>{S();var I=g("Team Name");e(m,I)},$$slots:{default:!0}});var Y=r(u,2);Sr(Y,{type:"text",placeholder:"My Awesome Team",get value(){return ue().name},set value(m){br(lr,Ie(ue).name=m,Ie(ue))},$$legacy:!0}),e(U,E)},$$slots:{default:!0}});var c=r(A,2);wr(c,{}),e(l,_)},$$slots:{default:!0}});var v=r(T,2);be(v,{type:"submit",get disabled(){return ce()},class:"w-full",children:(l,p)=>{var _=Ot(),A=d(_);tr(A,{class:"mr-2 h-4 w-4"});var c=r(A);K(()=>L(c,` ${ce()?"Creating Team...":"Create Team"}`)),e(l,_)},$$slots:{default:!0}}),s(n),hr(n,l=>He==null?void 0:He(l)),e(b,n)},$$slots:{default:!0}}),e(W,k)},$$slots:{default:!0}})};D(R,H=>{t(se).id==="create"&&H(oe)},j)}};D(N,R=>{t(se).id==="invite"?R(ve):R(pe,!1)},Q)}};D(fe,N=>{t(se).id==="teams"?N(Je):N($r,!1)})}e(we,Te)},$$slots:{default:!0}})}),e(i,P)},$$slots:{default:!0}}),s(Ge);var Br=r(Ge,2);{var Hr=i=>{var y=Et(),P=r(a(y),2),C=a(P),F=r(a(C),2);xr(F,{get data(){return ae()}}),s(C);var w=r(C,2),se=r(a(w),2);xr(se,{get data(){return ue()}}),s(w),s(P),s(y),e(i,y)};D(Br,i=>{t(Pe)&&i(Hr)})}s(pr),s(vr),K(()=>Yr(cr,1,`h-2 w-2 rounded-full ${t(de)==="saving"?"animate-pulse bg-orange-500":t(de)==="error"?"bg-red-500":"bg-green-500"}`)),e(Ne,nr),Dr(),Re()}export{Ca as component};
