import{c as p,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(t,o){const r=c(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M5 12h14"}],["path",{d:"M12 5v14"}]];f(t,d({name:"plus"},()=>r,{get iconNode(){return a},children:(e,$)=>{var s=p(),n=i(s);m(n,o,"default",{},null),l(e,s)},$$slots:{default:!0}}))}export{y as P};
