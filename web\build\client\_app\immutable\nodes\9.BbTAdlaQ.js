import{f as v,a as l,c as ve}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as He,k as et,v as mt,d as Ce,g as s,a as Ve,c as t,s as r,f as V,n as Me,r as e,t as _e,x as Fe,e as Je,m as je,l as wt,b as $t,h as St}from"../chunks/CGmarHxI.js";import{i as nt}from"../chunks/BIEMS98f.js";import{p as Oe}from"../chunks/Btcx8l8F.js";import{S as kt}from"../chunks/C6g8ubaU.js";import{s as z}from"../chunks/CIt1g2O9.js";import{i as Ie}from"../chunks/u21ee2wt.js";import{e as be,i as Le}from"../chunks/C3w0v0gR.js";import{c as X}from"../chunks/BvdI7LR8.js";import{s as Ye,a as Ae}from"../chunks/B-Xjo-Yt.js";import{B as ut}from"../chunks/B1K98fMG.js";import{C as Ze}from"../chunks/DuGukytH.js";import{C as Ke}from"../chunks/Cdn-N1RY.js";import{C as At}from"../chunks/DETxXRrJ.js";import{C as Ct}from"../chunks/GwmmX_iF.js";import{S as u}from"../chunks/BPvdPoic.js";import{B as It}from"../chunks/iTqMWrIH.js";import{C as Xe}from"../chunks/DW7T7T22.js";import{A as Pt,W as Mt,M as Lt,C as vt}from"../chunks/BV675lZR.js";import{P as Ot}from"../chunks/DvO_AOqy.js";import{S as Tt}from"../chunks/yW0TxTga.js";import{S as Et}from"../chunks/D871oxnv.js";import{Z as Rt}from"../chunks/1zwBog76.js";import{T as ot}from"../chunks/CZ8wIJN8.js";import{C as it}from"../chunks/-SpbofVw.js";import{C as Ut}from"../chunks/CXUk17vb.js";import{S as We}from"../chunks/rNI1Perp.js";import{G as lt}from"../chunks/D1zde6Ej.js";import{M as gt}from"../chunks/QtAhPN2H.js";import{A as ft}from"../chunks/B_tyjpYb.js";import{S as Dt}from"../chunks/CYoZicO9.js";import{C as Ne,a as qe,b as Be}from"../chunks/C6FI6jUA.js";import{o as ht}from"../chunks/nZgk9enP.js";import{e as rt,k as st}from"../chunks/CmxjS0TN.js";import{A as De}from"../chunks/Cs0qIT7f.js";import{S as zt}from"../chunks/FAbXdqfL.js";import"../chunks/CfcZq63z.js";import{t as Ft}from"../chunks/DjPYYl4Z.js";import{g as Jt,b as Nt}from"../chunks/9r-6KH_O.js";var qt=v('<span class="flex items-center gap-2 svelte-s4uer">Get Started <!></span>'),Bt=v('<!> <span class="svelte-s4uer">Watch Demo</span>',1),Gt=v('<div class="flex flex-col gap-4 svelte-s4uer"><div class="flex flex-row gap-4 svelte-s4uer"><!> <!> <!> <!> <!></div> <div class="flex flex-row gap-4 svelte-s4uer"><!> <!> <!> <!> <!></div> <div class="flex flex-row gap-4 svelte-s4uer"><!> <!> <!> <!> <!></div> <div class="flex flex-row gap-4 svelte-s4uer"><!> <!> <!> <!> <!></div> <div class="flex flex-row gap-4 svelte-s4uer"><!> <!> <!> <!> <!></div></div>'),Wt=v('<div class="flex h-full items-center justify-center svelte-s4uer"><div class="text-center svelte-s4uer"><p class="text-gray-600 svelte-s4uer">No jobs found in database</p></div></div>'),Ht=v('<img class="h-full w-full bg-white object-contain p-0.5 svelte-s4uer" loading="lazy"/>'),Vt=v("<div> </div>"),jt=v('<div class="flex items-start gap-3 svelte-s4uer"><div class="h-8 w-8 overflow-hidden rounded-lg svelte-s4uer"><!></div> <div class="min-w-0 flex-1 svelte-s4uer"><div class="text-sm font-semibold text-gray-900 svelte-s4uer"> </div> <div class="text-xs text-gray-600 svelte-s4uer"> </div> <div class="mt-1 flex flex-wrap gap-1 text-xs svelte-s4uer"><span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700 svelte-s4uer"> </span> <span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700 svelte-s4uer"> </span></div> <div class="mt-1 text-xs text-gray-500 svelte-s4uer"><div class="svelte-s4uer"> </div></div></div></div>'),Yt=v('<div class="mt-2 text-xs text-gray-600 svelte-s4uer"> </div>'),Zt=v('<span class="rounded bg-gray-100 px-1.5 py-0.5 text-xs text-gray-700 svelte-s4uer"> </span>'),Kt=v('<div class="mt-2 flex flex-wrap gap-1 svelte-s4uer"></div>'),Xt=v("<!> <!>",1),Qt=v('<div class="flex items-center gap-1.5 svelte-s4uer"><div><!></div> <span class="text-xs font-medium text-gray-700 svelte-s4uer"> </span></div> <div> </div>',1),er=v("<!> <!> <!>",1),tr=v('<div class="animate-scroll-up columns-2 gap-3 md:columns-3 lg:columns-4 svelte-s4uer"></div>'),rr=v(`<section class="-mt-17 -z-50 svelte-s4uer"><div class="grid lg:grid-cols-[2fr_4fr] svelte-s4uer"><div class="relative flex flex-col justify-center overflow-hidden svelte-s4uer"><div class="mt-17 relative z-10 max-w-md space-y-8 p-10 svelte-s4uer"><div class="space-y-6 svelte-s4uer"><div class="inline-flex items-center gap-2 rounded-full border border-blue-200 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700 svelte-s4uer"><!> <span class="svelte-s4uer">AI-Powered Automation</span></div> <h1 class="text-4xl font-bold leading-tight text-gray-900 lg:text-6xl svelte-s4uer">Apply to <span class="relative text-blue-600 svelte-s4uer">Hundreds <div class="absolute -bottom-1 left-0 h-2 w-full bg-gradient-to-r from-blue-200 to-indigo-200 opacity-60 svelte-s4uer"></div></span> of Jobs Automatically</h1> <p class="text-lg text-gray-600 svelte-s4uer">Let AI handle your job applications while you focus on what matters. Smart matching,
            personalized applications, and real-time tracking.</p></div> <div class="space-y-2 text-sm svelte-s4uer"><div class="flex items-center gap-3 svelte-s4uer"><div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-100 svelte-s4uer"><!></div> <span class="text-gray-700 svelte-s4uer">100+ applications in minutes</span></div> <div class="flex items-center gap-3 svelte-s4uer"><div class="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 svelte-s4uer"><!></div> <span class="text-gray-700 svelte-s4uer">AI-powered resume matching</span></div> <div class="flex items-center gap-3 svelte-s4uer"><div class="flex h-5 w-5 items-center justify-center rounded-full bg-purple-100 svelte-s4uer"><!></div> <span class="text-gray-700 svelte-s4uer">Real-time tracking & analytics</span></div></div> <div class="space-y-4 svelte-s4uer"><div class="flex flex-col gap-3 sm:flex-row svelte-s4uer"><!> <!></div> <div class="flex items-center gap-3 text-xs text-gray-500 svelte-s4uer"><div class="flex items-center gap-2 svelte-s4uer"><div class="h-1.5 w-1.5 rounded-full bg-green-500 svelte-s4uer"></div> <span class="svelte-s4uer">No credit card required</span></div> <span class="svelte-s4uer">Setup in minutes</span></div></div></div></div> <div class="bg-accent border-border relative h-[900px] overflow-hidden border-l opacity-80 svelte-s4uer"><div class="from-accent via-accent/90 pointer-events-none absolute left-0 right-0 top-0 z-10 h-24 bg-gradient-to-b to-transparent svelte-s4uer"></div> <div class="from-accent via-accent/70 pointer-events-none absolute bottom-0 left-0 right-0 z-10 h-16 bg-gradient-to-t to-transparent svelte-s4uer"></div> <div class="relative flex h-full flex-col justify-center svelte-s4uer"><div class="h-full overflow-hidden p-4 svelte-s4uer"><!></div></div></div></div></section>`);function sr(U,i){He(i,!0);let o=et(mt([])),g=et(!0);async function I(){var a,m;try{console.log("Fetching jobs from API...");const p=await fetch("/api/jobs?limit=200&random=true");if(console.log("Response status:",p.status),!p.ok)throw new Error(`HTTP error! status: ${p.status}`);const k=await p.json();return console.log("API response data:",k),console.log("Jobs count:",((a=k.jobs)==null?void 0:a.length)||0),((m=k.jobs)==null?void 0:m.length)>0&&(console.log("First job sample:",k.jobs[0]),console.log("Available fields:",Object.keys(k.jobs[0])),console.log("Description type:",typeof k.jobs[0].description),console.log("Description content:",k.jobs[0].description)),k.jobs||[]}catch(p){return console.error("Failed to fetch jobs:",p),[]}}const f=[{status:"scanning",color:"blue",text:"Scanning",icon:Tt},{status:"matching",color:"yellow",text:"Matching",icon:Mt},{status:"applying",color:"orange",text:"Applying",icon:Et},{status:"applied",color:"green",text:"Applied",icon:Lt}];function T(a){return a<5?f[0]:a<10?f[1]:a<15?f[2]:f[3]}function $(a,m){var he,Z,q;const p=(a==null?void 0:a.company)||"Unknown Company",k=p.charAt(0).toUpperCase(),O=["bg-blue-500","bg-green-500","bg-purple-500","bg-red-500","bg-yellow-500","bg-indigo-500","bg-pink-500","bg-teal-500"],te=O[p.length%O.length],j=((he=a==null?void 0:a.companyRelation)==null?void 0:he.logoUrl)||null,y=((Z=a==null?void 0:a.companyRelation)==null?void 0:Z.name)||p;let C=(a==null?void 0:a.industry)||((q=a==null?void 0:a.industryTags)==null?void 0:q[0]);if(!C){const n=((a==null?void 0:a.title)||"").toLowerCase(),de=((a==null?void 0:a.company)||"").toLowerCase();/(health|medical|nurse|doctor|therapist|clinical)/.test(n)||/(health|medical|hospital)/.test(de)?C="Healthcare":/(finance|bank|accounting|banker)/.test(n)||/(bank|financial|wells fargo)/.test(de)?C="Finance":/(market|brand|social media)/.test(n)?C="Marketing":/(sales|account manager|business development)/.test(n)?C="Sales":/(design|ui|ux|graphic)/.test(n)?C="Design":/(engineer|developer|programmer|software|scientist|biostatistician)/.test(n)?C="Technology":/(teacher|education|instructor|professor|assistant)/.test(n)||/(university|school)/.test(de)?C="Education":/(operations|logistics|supply chain|coordinator|clerk)/.test(n)?C="Operations":/(consultant|advisor)/.test(n)?C="Consulting":/(hr|human resources|benefits)/.test(n)?C="Human Resources":/(behavioral|behavior|therapist)/.test(n)?C="Healthcare":C="Other"}let Y=(a==null?void 0:a.experienceLevel)||(a==null?void 0:a.seniorityLevel);if(!Y){const n=((a==null?void 0:a.title)||"").toLowerCase();/(senior|sr\.|lead|principal|staff)/.test(n)?Y="Senior Level":/(junior|jr\.|entry|assistant|aide|technician)/.test(n)?Y="Entry Level":/(director|head of|vp|vice president|manager)/.test(n)?Y="Management":(/(coordinator|specialist|analyst)/.test(n),Y="Mid-Level")}const ne=Array.isArray(a==null?void 0:a.benefits)?a.benefits.slice(0,3):[];return{id:(a==null?void 0:a.id)||Math.random().toString(),uniqueKey:`${(a==null?void 0:a.id)||"job"}_${m}_${Date.now()}_${Math.random().toString(36).slice(2,9)}`,company:y,role:(a==null?void 0:a.title)||"Software Engineer",location:(a==null?void 0:a.location)||"Remote",salary:(a==null?void 0:a.salary)||"Competitive",companyInitial:k,logoColor:te,companyLogo:j,matchScore:Math.floor(Math.random()*30)+70,industry:C,seniority:Y,benefits:ne,description:a!=null&&a.description&&typeof a.description=="string"?a.description.slice(0,100)+"...":"",age:Math.floor(Math.random()*20)}}typeof window<"u"&&(async function(){console.log("Starting initialization..."),Ce(g,!0);const m=await I();if(console.log("Fetched jobs:",m.length),m.length>0){const p=Math.min(40,m.length),k=m.slice(0,p);console.log("Base slice:",k.length);const O=k.map((j,y)=>$(j,y)),te=k.map((j,y)=>$(j,y+p));Ce(o,[...O,...te],!0),console.log("Final streaming jobs:",s(o).length),Ce(g,!1)}else console.warn("No jobs found in database"),Ce(g,!1)}(),setInterval(()=>{if(s(o).length){for(let a=0;a<s(o).length;a++)Math.random()<.2&&(s(o)[a].age=Math.floor(Math.random()*20));Ce(o,s(o),!0)}},1500));var w=rr(),G=t(w),P=t(G),W=t(P),F=t(W),Q=t(F),ce=t(Q);It(ce,{class:"h-4 w-4"}),Me(2),e(Q),Me(4),e(F);var J=r(F,2),x=t(J),E=t(x),c=t(E);Xe(c,{class:"h-3 w-3 text-green-600"}),e(E),Me(2),e(x);var _=r(x,2),S=t(_),R=t(S);Xe(R,{class:"h-3 w-3 text-blue-600"}),e(S),Me(2),e(_);var M=r(_,2),D=t(M),A=t(D);Xe(A,{class:"h-3 w-3 text-purple-600"}),e(D),Me(2),e(M),e(J);var L=r(J,2),N=t(L),se=t(N);ut(se,{href:"/auth/sign-up",class:"group bg-gradient-to-r from-blue-600 to-indigo-600 font-semibold text-white hover:from-blue-700 hover:to-indigo-700",children:(a,m)=>{var p=qt(),k=r(t(p));Pt(k,{class:"h-4 w-4 transition-transform group-hover:translate-x-0.5"}),e(p),l(a,p)},$$slots:{default:!0}});var H=r(se,2);ut(H,{variant:"ghost",class:"group flex items-center gap-2 px-4 py-3 text-sm text-gray-600 transition-colors hover:text-gray-900",children:(a,m)=>{var p=Bt(),k=V(p);Ot(k,{class:"h-4 w-4 group-hover:text-blue-600"}),Me(2),l(a,p)},$$slots:{default:!0}}),e(N),Me(2),e(L),e(W),e(P);var ie=r(P,2),h=r(t(ie),4),d=t(h),b=t(d);{var ee=a=>{var m=Gt(),p=t(m),k=t(p);u(k,{class:"bg-primary/10 h-40 w-full"});var O=r(k,2);u(O,{class:"bg-primary/10 h-40 w-full"});var te=r(O,2);u(te,{class:"bg-primary/10 h-40 w-full"});var j=r(te,2);u(j,{class:"bg-primary/10 h-40 w-full"});var y=r(j,2);u(y,{class:"bg-primary/10 h-40 w-full"}),e(p);var C=r(p,2),Y=t(C);u(Y,{class:"bg-primary/10 h-40 w-full"});var ne=r(Y,2);u(ne,{class:"bg-primary/10 h-40 w-full"});var he=r(ne,2);u(he,{class:"bg-primary/10 h-40 w-full"});var Z=r(he,2);u(Z,{class:"bg-primary/10 h-40 w-full"});var q=r(Z,2);u(q,{class:"bg-primary/10 h-40 w-full"}),e(C);var n=r(C,2),de=t(n);u(de,{class:"bg-primary/10 h-40 w-full"});var Se=r(de,2);u(Se,{class:"bg-primary/10 h-40 w-full"});var ke=r(Se,2);u(ke,{class:"bg-primary/10 h-40 w-full"});var xe=r(ke,2);u(xe,{class:"bg-primary/10 h-40 w-full"});var Te=r(xe,2);u(Te,{class:"bg-primary/10 h-40 w-full"}),e(n);var ae=r(n,2),me=t(ae);u(me,{class:"bg-primary/10 h-40 w-full"});var ge=r(me,2);u(ge,{class:"bg-primary/10 h-40 w-full"});var fe=r(ge,2);u(fe,{class:"bg-primary/10 h-40 w-full"});var Pe=r(fe,2);u(Pe,{class:"bg-primary/10 h-40 w-full"});var we=r(Pe,2);u(we,{class:"bg-primary/10 h-40 w-full"}),e(ae);var K=r(ae,2),ue=t(K);u(ue,{class:"bg-primary/10 h-40 w-full"});var $e=r(ue,2);u($e,{class:"bg-primary/10 h-40 w-full"});var ye=r($e,2);u(ye,{class:"bg-primary/10 h-40 w-full"});var re=r(ye,2);u(re,{class:"bg-primary/10 h-40 w-full"});var oe=r(re,2);u(oe,{class:"bg-primary/10 h-40 w-full"}),e(K),e(m),l(a,m)},pe=(a,m)=>{{var p=O=>{var te=Wt();l(O,te)},k=O=>{var te=tr();be(te,21,()=>s(o),j=>j.uniqueKey,(j,y)=>{var C=ve();const Y=Fe(()=>T(s(y).age||0)),ne=Fe(()=>s(Y).icon);var he=V(C);const Z=Fe(()=>s(y).age*100);X(he,()=>Ze,(q,n)=>{n(q,{class:"mb-2 break-inside-avoid gap-0 p-0 transition-all duration-500 ease-in-out",get style(){return`animation-delay: ${s(Z)??""}ms`},children:(de,Se)=>{var ke=er(),xe=V(ke);X(xe,()=>Ct,(me,ge)=>{ge(me,{class:"border-border border-b !p-2",children:(fe,Pe)=>{var we=jt(),K=t(we),ue=t(K);{var $e=ze=>{var Ee=Ht();_e(()=>{Ae(Ee,"src",s(y).companyLogo),Ae(Ee,"alt",`${s(y).company??""} logo`)}),l(ze,Ee)},ye=ze=>{var Ee=Vt(),bt=t(Ee,!0);e(Ee),_e(()=>{Ye(Ee,1,`flex h-full w-full items-center justify-center ${s(y).logoColor??""} text-xs font-bold text-white`,"svelte-s4uer"),z(bt,s(y).companyInitial)}),l(ze,Ee)};Ie(ue,ze=>{s(y).companyLogo?ze($e):ze(ye,!1)})}e(K);var re=r(K,2),oe=t(re),B=t(oe,!0);e(oe);var le=r(oe,2),Ue=t(le,!0);e(le);var Ge=r(le,2),tt=t(Ge),xt=t(tt,!0);e(tt);var dt=r(tt,2),yt=t(dt,!0);e(dt),e(Ge);var ct=r(Ge,2),pt=t(ct),_t=t(pt);e(pt),e(ct),e(re),e(we),_e(()=>{z(B,s(y).role),z(Ue,s(y).company),z(xt,s(y).industry),z(yt,s(y).seniority),z(_t,`${s(y).location??""} • ${s(y).salary??""}`)}),l(fe,we)},$$slots:{default:!0}})});var Te=r(xe,2);X(Te,()=>Ke,(me,ge)=>{ge(me,{class:"!p-2",children:(fe,Pe)=>{var we=Xt(),K=V(we);{var ue=re=>{var oe=Yt(),B=t(oe,!0);e(oe),_e(()=>z(B,s(y).description)),l(re,oe)};Ie(K,re=>{s(y).description&&re(ue)})}var $e=r(K,2);{var ye=re=>{var oe=Kt();be(oe,21,()=>s(y).benefits,Le,(B,le)=>{var Ue=Zt(),Ge=t(Ue,!0);e(Ue),_e(()=>z(Ge,s(le))),l(B,Ue)}),e(oe),l(re,oe)};Ie($e,re=>{s(y).benefits&&s(y).benefits.length>0&&re(ye)})}l(fe,we)},$$slots:{default:!0}})});var ae=r(Te,2);X(ae,()=>At,(me,ge)=>{ge(me,{class:"border-border flex items-center justify-between border-t !p-2",children:(fe,Pe)=>{var we=Qt(),K=V(we),ue=t(K),$e=t(ue);X($e,()=>s(ne),(le,Ue)=>{Ue(le,{get class(){return`h-3 w-3 text-${s(Y).color??""}-600`}})}),e(ue);var ye=r(ue,2),re=t(ye,!0);e(ye),e(K);var oe=r(K,2),B=t(oe);e(oe),_e(()=>{Ye(ue,1,`flex h-4 w-4 items-center justify-center rounded-full bg-${s(Y).color??""}-100`,"svelte-s4uer"),z(re,s(Y).text),Ye(oe,1,`rounded-full bg-${s(Y).color??""}-50 px-2 py-0.5 text-xs font-bold text-${s(Y).color??""}-700`,"svelte-s4uer"),z(B,`${s(y).matchScore??""}% match`)}),l(fe,we)},$$slots:{default:!0}})}),l(de,ke)},$$slots:{default:!0}})}),l(j,C)}),e(te),l(O,te)};Ie(a,O=>{s(o).length===0?O(p):O(k,!1)},m)}};Ie(b,a=>{s(g)?a(ee):a(pe,!1)})}e(d),e(h),e(ie),e(G),e(w),l(U,w),Ve()}var ar=v('<div class="p-22 bg-background text-foreground group shadow-md transition-all duration-300 hover:shadow-lg"><div class="bg-primary/10 group-hover:bg-primary/20 mb-4 flex h-12 w-12 items-center justify-center rounded-lg transition-all duration-300"><!></div> <h3 class="font-normal! mb-4 text-3xl"> </h3> <p class="text-md text-muted-foreground"> </p></div>');function Qe(U,i){let o=Oe(i,"icon",8),g=Oe(i,"title",8),I=Oe(i,"description",8);var f=ar(),T=t(f),$=t(T);X($,o,(F,Q)=>{Q(F,{class:"text-primary h-6 w-6 transition-all duration-300 group-hover:scale-110"})}),e(T);var w=r(T,2),G=t(w,!0);e(w);var P=r(w,2),W=t(P,!0);e(P),e(f),_e(()=>{z(G,g()),z(W,I())}),l(U,f)}var or=v('<section id="features" class="p-12"><div class="grid grid-cols-1 divide-x divide-y border md:grid-cols-2 lg:grid-cols-4"></div></section>');function ir(U){const i=[{icon:Rt,title:"One-Click Apply",description:"Apply to hundreds of jobs across multiple platforms with a single click."},{icon:ot,title:"Smart Matching",description:"Our AI matches your resume to job requirements for higher success rates."},{icon:it,title:"Save Hours Daily",description:"Automate repetitive application tasks and focus on preparing for interviews."},{icon:Ut,title:"Application Analytics",description:"Track your application performance and optimize your job search strategy."},{icon:We,title:"Resume Optimization",description:"Get suggestions to improve your resume for specific job postings."},{icon:lt,title:"Multi-Platform Support",description:"Works with LinkedIn, Indeed, Glassdoor, ZipRecruiter, and many more."},{icon:gt,title:"AI Interview Coach",description:"Practice with realistic mock interviews tailored to your industry with instant feedback."},{icon:ft,title:"Career Advancement",description:"Get personalized recommendations for skills to develop for your dream roles."}];var o=or(),g=t(o);be(g,5,()=>i,Le,(I,f)=>{Qe(I,{get icon(){return s(f).icon},get title(){return s(f).title},get description(){return s(f).description}})}),e(g),e(o),l(U,o)}var lr=v('<div class="border-border bg-card text-card-foreground flex h-full flex-col rounded-lg border p-6 shadow-md"><div class="mb-4 flex items-center space-x-4"><div class="h-12 w-12 overflow-hidden rounded-full"><img class="h-full w-full object-cover"/></div> <div><h4 class="font-semibold"> </h4> <p class="text-muted-foreground text-sm"> </p></div></div> <div class="mb-4 flex"></div> <p class="text-muted-foreground flex-grow italic"> </p></div>');function at(U,i){let o=Oe(i,"name",8),g=Oe(i,"role",8),I=Oe(i,"testimonial",8),f=Oe(i,"rating",8),T=Oe(i,"image",8);var $=lr(),w=t($),G=t(w),P=t(G);e(G);var W=r(G,2),F=t(W),Q=t(F,!0);e(F);var ce=r(F,2),J=t(ce,!0);e(ce),e(W),e(w);var x=r(w,2);be(x,4,()=>Array(5),Le,(_,S,R)=>{const M=Je(()=>R<f()?"fill-warning text-warning":"text-muted");Dt(_,{size:16,get class(){return s(M)}})}),e(x);var E=r(x,2),c=t(E,!0);e(E),e($),_e(()=>{Ae(P,"src",T()),Ae(P,"alt",o()),z(Q,o()),z(J,g()),z(c,I())}),l(U,$)}const nr={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function dr(U,i){const o=U.scrollSnapList();return typeof i=="number"?o.map(()=>i):i(o,U)}function cr(U,i){const o=U.rootNode();return i&&i(o)||o}function Re(U={}){let i,o,g,I,f=null,T=0,$=!1,w=!1,G=!1,P=!1;function W(h,d){o=h;const{mergeOptions:b,optionsAtMedia:ee}=d,pe=b(nr,Re.globalOptions),a=b(pe,U);if(i=ee(a),o.scrollSnapList().length<=1)return;P=i.jump,g=!1,I=dr(o,i.delay);const{eventStore:m,ownerDocument:p}=o.internalEngine(),k=!!o.internalEngine().options.watchDrag,O=cr(o,i.rootNode);m.add(p,"visibilitychange",E),k&&o.on("pointerDown",_),k&&!i.stopOnInteraction&&o.on("pointerUp",S),i.stopOnMouseEnter&&m.add(O,"mouseenter",R),i.stopOnMouseEnter&&!i.stopOnInteraction&&m.add(O,"mouseleave",M),i.stopOnFocusIn&&o.on("slideFocusStart",x),i.stopOnFocusIn&&!i.stopOnInteraction&&m.add(o.containerNode(),"focusout",J),i.playOnInit&&J()}function F(){o.off("pointerDown",_).off("pointerUp",S).off("slideFocusStart",x),x(),g=!0,$=!1}function Q(){const{ownerWindow:h}=o.internalEngine();h.clearTimeout(T),T=h.setTimeout(se,I[o.selectedScrollSnap()]),f=new Date().getTime(),o.emit("autoplay:timerset")}function ce(){const{ownerWindow:h}=o.internalEngine();h.clearTimeout(T),T=0,f=null,o.emit("autoplay:timerstopped")}function J(){if(!g){if(c()){G=!0;return}$||o.emit("autoplay:play"),Q(),$=!0}}function x(){g||($&&o.emit("autoplay:stop"),ce(),$=!1)}function E(){if(c())return G=$,x();G&&J()}function c(){const{ownerDocument:h}=o.internalEngine();return h.visibilityState==="hidden"}function _(){w||x()}function S(){w||J()}function R(){w=!0,x()}function M(){w=!1,J()}function D(h){typeof h<"u"&&(P=h),J()}function A(){$&&x()}function L(){$&&J()}function N(){return $}function se(){const{index:h}=o.internalEngine(),d=h.clone().add(1).get(),b=o.scrollSnapList().length-1,ee=i.stopOnLastSnap&&d===b;if(o.canScrollNext()?o.scrollNext(P):o.scrollTo(0,P),o.emit("autoplay:select"),ee)return x();J()}function H(){if(!f)return null;const h=I[o.selectedScrollSnap()],d=new Date().getTime()-f;return h-d}return{name:"autoplay",options:U,init:W,destroy:F,play:D,stop:A,reset:L,isPlaying:N,timeUntilNext:H}}Re.globalOptions=void 0;var pr=v('<section class="py-16"><div class="container mx-auto px-4"><h2 class="text-foreground mb-4 text-center text-4xl font-bold">What Our Users Say</h2> <p class="text-muted-foreground mx-auto mb-12 max-w-3xl text-center text-lg">Thousands of job seekers have found their dream jobs faster with Auto Apply.</p> <div class="mt-12 grid grid-cols-1 gap-8 md:grid-cols-3"><div class="h-[600px] overflow-hidden"><!></div> <div class="h-[600px] overflow-hidden"><!></div> <div class="h-[600px] overflow-hidden"><!></div></div></div></section>');function ur(U,i){He(i,!1);const o=[{name:"Sarah Johnson",role:"Software Engineer",testimonial:"Auto Apply helped me submit over 200 applications in just two weeks. I landed 15 interviews and 3 job offers!",rating:5,image:"https://randomuser.me/api/portraits/women/12.jpg"},{name:"Michael Chen",role:"Marketing Specialist",testimonial:"The automation is incredible. What used to take me hours now takes minutes. Plus, their resume optimization suggestions helped me improve my response rate.",rating:5,image:"https://randomuser.me/api/portraits/men/32.jpg"},{name:"Emily Rodriguez",role:"Data Analyst",testimonial:"I was skeptical at first, but after using Auto Apply for a month, I received more interview calls than I had in the previous six months of job hunting.",rating:4,image:"https://randomuser.me/api/portraits/women/65.jpg"},{name:"David Wilson",role:"Product Manager",testimonial:"The time I saved using Auto Apply allowed me to focus on networking and preparing for interviews, which ultimately helped me land my dream job.",rating:5,image:"https://randomuser.me/api/portraits/men/75.jpg"},{name:"Lisa Thompson",role:"UX Designer",testimonial:"The platform made job hunting so much less stressful. The analytics feature showed me which applications were most effective.",rating:4,image:"https://randomuser.me/api/portraits/women/33.jpg"},{name:"James Morris",role:"Financial Analyst",testimonial:"Auto Apply's custom cover letter generation is phenomenal. Each letter feels personal and tailored to the job. This service is worth every penny.",rating:5,image:"https://randomuser.me/api/portraits/men/54.jpg"}],g=o.slice(0,Math.ceil(o.length/3)),I=o.slice(Math.ceil(o.length/3),Math.ceil(o.length*2/3)),f=o.slice(Math.ceil(o.length*2/3)),T=Re({delay:3e3,stopOnInteraction:!0}),$=Re({delay:4500,stopOnInteraction:!0}),w=Re({delay:3800,stopOnInteraction:!0}),G={align:"start",loop:!0,axis:"y"},P={align:"start",loop:!0,axis:"y"},W={align:"start",loop:!0,axis:"y"};nt();var F=pr(),Q=t(F),ce=r(t(Q),4),J=t(ce),x=t(J);const E=Je(()=>[T]);Ne(x,{get plugins(){return s(E)},get opts(){return G},class:"h-full",$$events:{mouseenter(...A){var L;(L=T.stop)==null||L.apply(this,A)},mouseleave:()=>T.play()},children:(A,L)=>{qe(A,{class:"h-full",children:(N,se)=>{var H=ve(),ie=V(H);be(ie,1,()=>g,Le,(h,d)=>{Be(h,{class:"h-auto pb-4 pt-4",children:(b,ee)=>{at(b,{get name(){return s(d).name},get role(){return s(d).role},get testimonial(){return s(d).testimonial},get rating(){return s(d).rating},get image(){return s(d).image}})},$$slots:{default:!0}})}),l(N,H)},$$slots:{default:!0}})},$$slots:{default:!0}}),e(J);var c=r(J,2),_=t(c);const S=Je(()=>[$]);Ne(_,{get plugins(){return s(S)},get opts(){return P},class:"h-full",$$events:{mouseenter(...A){var L;(L=$.stop)==null||L.apply(this,A)},mouseleave:()=>$.play()},children:(A,L)=>{qe(A,{class:"h-full",children:(N,se)=>{var H=ve(),ie=V(H);be(ie,1,()=>I,Le,(h,d)=>{Be(h,{class:"h-auto pb-4 pt-4",children:(b,ee)=>{at(b,{get name(){return s(d).name},get role(){return s(d).role},get testimonial(){return s(d).testimonial},get rating(){return s(d).rating},get image(){return s(d).image}})},$$slots:{default:!0}})}),l(N,H)},$$slots:{default:!0}})},$$slots:{default:!0}}),e(c);var R=r(c,2),M=t(R);const D=Je(()=>[w]);Ne(M,{get plugins(){return s(D)},get opts(){return W},class:"h-full",$$events:{mouseenter(...A){var L;(L=w.stop)==null||L.apply(this,A)},mouseleave:()=>w.play()},children:(A,L)=>{qe(A,{class:"h-full",children:(N,se)=>{var H=ve(),ie=V(H);be(ie,1,()=>f,Le,(h,d)=>{Be(h,{class:"h-auto pb-4 pt-4",children:(b,ee)=>{at(b,{get name(){return s(d).name},get role(){return s(d).role},get testimonial(){return s(d).testimonial},get rating(){return s(d).rating},get image(){return s(d).image}})},$$slots:{default:!0}})}),l(N,H)},$$slots:{default:!0}})},$$slots:{default:!0}}),e(R),e(ce),e(Q),e(F),l(U,F),Ve()}var vr=v('<div class="flex flex-col gap-4"><div class="flex grid grid-cols-5 items-center justify-center gap-4"><!> <!> <!> <!> <!></div> <div class="flex grid grid-cols-5 items-center justify-center gap-4"><!> <!> <!> <!> <!></div> <div class="flex grid grid-cols-5 items-center justify-center gap-4"><!> <!> <!> <!> <!></div></div>'),mr=v('<img class="max-h-10 max-w-[100px] object-contain opacity-90 grayscale-0 filter transition-all duration-300 group-hover:scale-105 group-hover:opacity-100" loading="lazy"/>'),gr=v('<div class="group"><!></div>'),fr=v('<div class="space-y-2"><h3 class="text-muted-foreground text-sm font-medium uppercase tracking-wider">Startups & Early Stage</h3> <!></div>'),hr=v('<img class="max-h-10 max-w-[100px] object-contain opacity-70 grayscale filter transition-all duration-300 group-hover:scale-105 group-hover:opacity-100 group-hover:grayscale-0" loading="lazy"/>'),xr=v('<div class="group"><!></div>'),yr=v('<div class="space-y-2"><h3 class="text-muted-foreground text-sm font-medium uppercase tracking-wider">Growth & Scale-up</h3> <!></div>'),_r=v('<img class="max-h-10 max-w-[100px] object-contain opacity-70 grayscale filter transition-all duration-300 group-hover:scale-105 group-hover:opacity-100 group-hover:grayscale-0" loading="lazy"/>'),br=v('<div class="group"><!></div>'),wr=v('<div class="space-y-2"><h3 class="text-muted-foreground text-sm font-medium uppercase tracking-wider">Enterprise & Fortune 500</h3> <!></div>'),$r=v('<div class="flex flex-col gap-6"><!> <!> <!></div> <div class="mt-12 text-center"><p class="text-muted-foreground mb-4 text-lg">Join thousands of professionals working at these amazing companies</p> <div class="text-muted-foreground flex flex-wrap justify-center gap-6 text-sm"><div class="flex items-center gap-2"><div class="h-2 w-2 rounded-full bg-green-500"></div> <span>Remote-first opportunities</span></div> <div class="flex items-center gap-2"><div class="h-2 w-2 rounded-full bg-blue-500"></div> <span>Competitive salaries</span></div> <div class="flex items-center gap-2"><div class="h-2 w-2 rounded-full bg-purple-500"></div> <span>Growth opportunities</span></div></div></div>',1),Sr=v('<div class="py-12 text-center"><p class="text-muted-foreground text-lg">Company logos are being processed and will appear here soon.</p> <p class="text-muted-foreground mt-2 text-sm">Our system is currently downloading and optimizing company logos from various sources.</p></div>'),kr=v('<section class="bg-muted py-16"><div class="container mx-auto px-4"><div class="mb-12 text-center"><h2 class="text-foreground text-3xl font-bold tracking-tight sm:text-4xl">Trusted by Leading Companies</h2> <p class="text-muted-foreground mt-4 text-lg">Join thousands of professionals working at innovative companies worldwide</p></div> <!></div></section>');function Ar(U,i){He(i,!0);let o=et(mt({startups:[],growth:[],enterprise:[]})),g=et(!0);const I=Re({delay:2e3,stopOnInteraction:!0}),f=Re({delay:4e3,stopOnInteraction:!0}),T=Re({delay:2500,stopOnInteraction:!0}),$={align:"start",loop:!0},w={align:"start",loop:!0},G={align:"start",loop:!0};async function P(){var c,_,S,R;try{console.log("🔄 Fetching companies...");const M=await fetch("/api/companies/featured");if(M.ok){const D=await M.json();console.log("✅ Companies fetched:",D),console.log("📊 Startups count:",((c=D.startups)==null?void 0:c.length)||0),console.log("📊 Growth count:",((_=D.growth)==null?void 0:_.length)||0),console.log("📊 Enterprise count:",((S=D.enterprise)==null?void 0:S.length)||0),((R=D.startups)==null?void 0:R.length)>0&&console.log("🏢 Sample startup logos:",D.startups.slice(0,3).map(A=>({name:A.name,logoUrl:A.logoUrl}))),Ce(o,D,!0)}else console.error("❌ Failed to fetch featured companies:",M.status,M.statusText)}catch(M){console.error("💥 Error fetching featured companies:",M)}finally{Ce(g,!1),console.log("📊 Final companies state:",s(o))}}ht(()=>{P()});function W(c){if(c.logoUrl){if(c.logoUrl.includes("pub-")&&c.logoUrl.includes(".r2.dev/"))return console.log(`🖼️ Logo for ${c.name}: Using direct R2 URL`),console.log(`   URL: ${c.logoUrl}`),c.logoUrl;const _=c.logoUrl.split("/").pop();if(_){const R=`https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev/logos/${_}`;return console.log(`🖼️ Logo for ${c.name}:`),console.log(`   Original: ${c.logoUrl}`),console.log(`   Worker:   ${R}`),R}}return`https://placehold.co/200x80/f1f5f9/64748b?text=${encodeURIComponent(c.name)}`}function F(c,_){const S=c.target;console.error(`❌ Failed to load logo for ${_.name}:`),console.error(`   Attempted URL: ${S.src}`),console.error(`   Original DB URL: ${_.logoUrl}`),S.src=`https://placehold.co/200x80/f1f5f9/64748b?text=${encodeURIComponent(_.name)}`}var Q=kr(),ce=t(Q),J=r(t(ce),2);{var x=c=>{var _=vr(),S=t(_),R=t(S);u(R,{class:"bg-primary/10 h-30 w-full"});var M=r(R,2);u(M,{class:"bg-primary/10 h-30 w-full"});var D=r(M,2);u(D,{class:"bg-primary/10 h-30 w-full"});var A=r(D,2);u(A,{class:"bg-primary/10 h-30 w-full"});var L=r(A,2);u(L,{class:"bg-primary/10 h-30 w-full"}),e(S);var N=r(S,2),se=t(N);u(se,{class:"bg-primary/10 h-30 w-full"});var H=r(se,2);u(H,{class:"bg-primary/10 h-30 w-full"});var ie=r(H,2);u(ie,{class:"bg-primary/10 h-30 w-full"});var h=r(ie,2);u(h,{class:"bg-primary/10 h-30 w-full"});var d=r(h,2);u(d,{class:"bg-primary/10 h-30 w-full"}),e(N);var b=r(N,2),ee=t(b);u(ee,{class:"bg-primary/10 h-30 w-full"});var pe=r(ee,2);u(pe,{class:"bg-primary/10 h-30 w-full"});var a=r(pe,2);u(a,{class:"bg-primary/10 h-30 w-full"});var m=r(a,2);u(m,{class:"bg-primary/10 h-30 w-full"});var p=r(m,2);u(p,{class:"bg-primary/10 h-30 w-full"}),e(b),e(_),l(c,_)},E=(c,_)=>{{var S=M=>{var D=$r(),A=V(D),L=t(A);{var N=d=>{var b=fr(),ee=r(t(b),2);const pe=Fe(()=>[I]);X(ee,()=>Ne,(a,m)=>{m(a,{get plugins(){return s(pe)},get opts(){return $},class:"w-full",children:(p,k)=>{var O=ve(),te=V(O);X(te,()=>qe,(j,y)=>{y(j,{class:"-ml-2 md:-ml-4",children:(C,Y)=>{var ne=ve(),he=V(ne);be(he,17,()=>s(o).startups,Z=>Z.id,(Z,q)=>{var n=ve(),de=V(n);X(de,()=>Be,(Se,ke)=>{ke(Se,{class:"basis-1/3 pl-2 md:basis-1/4 md:pl-4 lg:basis-1/6",children:(xe,Te)=>{var ae=gr(),me=t(ae);X(me,()=>Ze,(ge,fe)=>{fe(ge,{class:"border-border/50 bg-background/50 hover:border-primary/20 hover:bg-background/80 backdrop-blur-sm transition-all duration-300 hover:shadow-lg",children:(Pe,we)=>{var K=ve(),ue=V(K);X(ue,()=>Ke,($e,ye)=>{ye($e,{class:"flex h-20 items-center justify-center p-4",children:(re,oe)=>{var B=mr();_e(le=>{Ae(B,"src",le),Ae(B,"alt",`${s(q).name??""} logo`)},[()=>W(s(q))]),rt("error",B,le=>F(le,s(q))),st(B),l(re,B)},$$slots:{default:!0}})}),l(Pe,K)},$$slots:{default:!0}})}),e(ae),l(xe,ae)},$$slots:{default:!0}})}),l(Z,n)}),l(C,ne)},$$slots:{default:!0}})}),l(p,O)},$$slots:{default:!0}})}),e(b),l(d,b)};Ie(L,d=>{s(o).startups.length>0&&d(N)})}var se=r(L,2);{var H=d=>{var b=yr(),ee=r(t(b),2);const pe=Fe(()=>[f]);X(ee,()=>Ne,(a,m)=>{m(a,{get plugins(){return s(pe)},get opts(){return w},class:"w-full",children:(p,k)=>{var O=ve(),te=V(O);X(te,()=>qe,(j,y)=>{y(j,{class:"-ml-2 md:-ml-4",children:(C,Y)=>{var ne=ve(),he=V(ne);be(he,17,()=>s(o).growth,Z=>Z.id,(Z,q)=>{var n=ve(),de=V(n);X(de,()=>Be,(Se,ke)=>{ke(Se,{class:"basis-1/3 pl-2 md:basis-1/4 md:pl-4 lg:basis-1/6",children:(xe,Te)=>{var ae=xr(),me=t(ae);X(me,()=>Ze,(ge,fe)=>{fe(ge,{class:"border-border/50 bg-background/50 hover:border-primary/20 hover:bg-background/80 backdrop-blur-sm transition-all duration-300 hover:shadow-lg",children:(Pe,we)=>{var K=ve(),ue=V(K);X(ue,()=>Ke,($e,ye)=>{ye($e,{class:"flex h-20 items-center justify-center p-4",children:(re,oe)=>{var B=hr();_e(le=>{Ae(B,"src",le),Ae(B,"alt",`${s(q).name??""} logo`)},[()=>W(s(q))]),rt("error",B,le=>F(le,s(q))),st(B),l(re,B)},$$slots:{default:!0}})}),l(Pe,K)},$$slots:{default:!0}})}),e(ae),l(xe,ae)},$$slots:{default:!0}})}),l(Z,n)}),l(C,ne)},$$slots:{default:!0}})}),l(p,O)},$$slots:{default:!0}})}),e(b),l(d,b)};Ie(se,d=>{s(o).growth.length>0&&d(H)})}var ie=r(se,2);{var h=d=>{var b=wr(),ee=r(t(b),2);const pe=Fe(()=>[T]);X(ee,()=>Ne,(a,m)=>{m(a,{get plugins(){return s(pe)},get opts(){return G},class:"w-full",children:(p,k)=>{var O=ve(),te=V(O);X(te,()=>qe,(j,y)=>{y(j,{class:"-ml-2 md:-ml-4",children:(C,Y)=>{var ne=ve(),he=V(ne);be(he,17,()=>s(o).enterprise,Z=>Z.id,(Z,q)=>{var n=ve(),de=V(n);X(de,()=>Be,(Se,ke)=>{ke(Se,{class:"basis-1/3 pl-2 md:basis-1/4 md:pl-4 lg:basis-1/6",children:(xe,Te)=>{var ae=br(),me=t(ae);X(me,()=>Ze,(ge,fe)=>{fe(ge,{class:"border-border/50 bg-background/50 hover:border-primary/20 hover:bg-background/80 backdrop-blur-sm transition-all duration-300 hover:shadow-lg",children:(Pe,we)=>{var K=ve(),ue=V(K);X(ue,()=>Ke,($e,ye)=>{ye($e,{class:"flex h-20 items-center justify-center p-4",children:(re,oe)=>{var B=_r();_e(le=>{Ae(B,"src",le),Ae(B,"alt",`${s(q).name??""} logo`)},[()=>W(s(q))]),rt("error",B,le=>F(le,s(q))),st(B),l(re,B)},$$slots:{default:!0}})}),l(Pe,K)},$$slots:{default:!0}})}),e(ae),l(xe,ae)},$$slots:{default:!0}})}),l(Z,n)}),l(C,ne)},$$slots:{default:!0}})}),l(p,O)},$$slots:{default:!0}})}),e(b),l(d,b)};Ie(ie,d=>{s(o).enterprise.length>0&&d(h)})}e(A),Me(2),l(M,D)},R=M=>{var D=Sr();l(M,D)};Ie(c,M=>{s(o).startups.length>0||s(o).growth.length>0||s(o).enterprise.length>0?M(S):M(R,!1)},_)}};Ie(J,c=>{s(g)?c(x):c(E,!1)})}e(ce),e(Q),l(U,Q),Ve()}var Cr=v('<div class="p-22 border-border bg-card text-card-foreground hover:bg-card/80 dark:hover:bg-card/90 hover:border-primary/20 group rounded-none border shadow-md transition-all duration-300 hover:shadow-lg"><div class="bg-primary/10 group-hover:bg-primary/20 mb-4 flex h-12 w-12 items-center justify-center rounded-lg transition-all duration-300"><!></div> <h3 class="font-normal! mb-4 text-3xl"> </h3> <p class="text-md text-muted-foreground"> </p></div>'),Ir=v('<section id="services" class="border-border border"><div class="flex flex-col"><div class="border-border md:grid-cols-16 flex flex-col border border-t [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="p-15 text-primary col-span-8 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl"> </h3> <p class="typography font-montreal text-xl"> </p> <a href="/auto-apply" class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">Learn More <!></a></div></div></div> <div class="border-border bg-grid col-span-8 col-start-9 row-span-8 row-start-1 border border-b border-r border-t"></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"></div></div> <div class="border-border md:grid-cols-16 flex flex-col border [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="bg-grid bg-grid-primary/20 dark:bg-grid-primary/40 border-border col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l"></div> <div class="p-15 text-foreground col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl"> </h3> <p class="typography font-montreal text-xl"> </p> <a href="/job-tracker" class="bg-primary text-primary-foreground hover:bg-primary/90 dark:hover:bg-primary/70 group flex w-48 flex-row items-center justify-between rounded-md px-6 py-3 transition-all duration-200">Learn More <!></a></div></div></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"></div> <div class="md:grid-cols-16 flex flex-col border border-t-neutral-500 [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="p-15 text-primary col-span-8 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl"> </h3> <p class="typography font-montreal text-xl"> </p> <a href="/resume-builder" class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">Learn More <!></a></div></div></div> <div class="border-border bg-grid col-span-8 col-start-9 row-span-8 row-start-1 border border-b border-r border-t"></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"></div> <div class="border-border md:grid-cols-16 flex flex-col border border-t [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="border-border bg-grid bg-grid-primary/20 dark:bg-grid-primary/40 col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l"></div> <div class="p-15 text-text-primary col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl"> </h3> <p class="typography font-montreal text-xl"> </p> <a href="/co-pilot" class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">Learn More <!></a></div></div></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"></div></section>');function Pr(U){const i={automatedApply:{title:"Effortless Job Applications",description:"Submit applications to hundreds of positions with our streamlined one-click system.",secondary:[{icon:it,title:"Reclaim Your Time",description:"Save hours daily by automating repetitive tasks and focus on interview preparation."},{icon:vt,title:"Performance Insights",description:"Gain valuable analytics to optimize your application strategy and improve results."},{icon:We,title:"Resume Enhancement",description:"Receive tailored suggestions to strengthen your resume for specific opportunities."},{icon:lt,title:"Universal Platform Support",description:"Seamlessly works with LinkedIn, Indeed, Glassdoor, ZipRecruiter, and many more."}]},jobTracker:{title:"Comprehensive Application Tracking",description:"Monitor all your job applications in one intuitive, centralized dashboard.",secondary:[{icon:Xe,title:"Real-time Status Updates",description:"Track your progress through each stage of the hiring process with clarity."},{icon:it,title:"Interview Management",description:"Organize and prepare for upcoming interviews with smart scheduling and reminders."},{icon:vt,title:"Strategic Analytics",description:"Visualize your job search journey with detailed metrics and actionable insights."},{icon:We,title:"Enterprise-grade Security",description:"Rest assured your career data is protected with advanced encryption and privacy controls."}]},resumeBuilder:{title:"Professional Resume Creator",description:"Craft standout resumes that capture attention with our intuitive builder.",secondary:[{icon:ot,title:"ATS-Friendly Formatting",description:"Ensure your resume successfully navigates through automated screening systems."},{icon:ft,title:"Strategic Skills Showcase",description:"Automatically highlight relevant qualifications based on target job descriptions."},{icon:lt,title:"Versatile Export Options",description:"Download your polished resume in PDF, DOCX, or plain text formats as needed."},{icon:We,title:"Multiple Resume Versions",description:"Create and manage specialized resumes tailored for different career opportunities."}]},coPilot:{title:"AI Career Co-Pilot",description:"Navigate your career journey with AI-powered guidance every step of the way.",secondary:[{icon:gt,title:"AI Interview Coach",description:"Practice with realistic mock interviews tailored to your industry with instant feedback."},{icon:zt,title:"Personalized Insights",description:"Receive custom career advice based on your skills, experience, and goals."},{icon:ot,title:"Job Match Analysis",description:"Get AI-powered compatibility scores for job listings based on your profile."},{icon:We,title:"Career Strategy Planning",description:"Develop a strategic roadmap to achieve your long-term professional objectives."}]}};var o=Ir(),g=t(o),I=t(g),f=t(I),T=t(f),$=t(T),w=t($),G=t(w,!0);e(w);var P=r(w,2),W=t(P,!0);e(P);var F=r(P,2),Q=r(t(F));De(Q,{class:"ml-2 h-4 w-4"}),e(F),e($),e(T),e(f),Me(2),e(I);var ce=r(I,2);be(ce,5,()=>i.automatedApply.secondary,Le,(q,n)=>{var de=Cr(),Se=t(de),ke=t(Se);X(ke,()=>s(n).icon,(ge,fe)=>{fe(ge,{class:"text-primary h-6 w-6 transition-all duration-300 group-hover:scale-110"})}),e(Se);var xe=r(Se,2),Te=t(xe,!0);e(xe);var ae=r(xe,2),me=t(ae,!0);e(ae),e(de),_e(()=>{z(Te,s(n).title),z(me,s(n).description)}),l(q,de)}),e(ce),e(g);var J=r(g,2),x=r(t(J),2),E=t(x),c=t(E),_=t(c),S=t(_,!0);e(_);var R=r(_,2),M=t(R,!0);e(R);var D=r(R,2),A=r(t(D));De(A,{class:"ml-2 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1"}),e(D),e(c),e(E),e(x),e(J);var L=r(J,2);be(L,5,()=>i.jobTracker.secondary,Le,(q,n)=>{Qe(q,{get icon(){return s(n).icon},get title(){return s(n).title},get description(){return s(n).description}})}),e(L);var N=r(L,2),se=t(N),H=t(se),ie=t(H),h=t(ie),d=t(h,!0);e(h);var b=r(h,2),ee=t(b,!0);e(b);var pe=r(b,2),a=r(t(pe));De(a,{class:"ml-2 h-4 w-4"}),e(pe),e(ie),e(H),e(se),Me(2),e(N);var m=r(N,2);be(m,5,()=>i.resumeBuilder.secondary,Le,(q,n)=>{Qe(q,{get icon(){return s(n).icon},get title(){return s(n).title},get description(){return s(n).description}})}),e(m);var p=r(m,2),k=r(t(p),2),O=t(k),te=t(O),j=t(te),y=t(j,!0);e(j);var C=r(j,2),Y=t(C,!0);e(C);var ne=r(C,2),he=r(t(ne));De(he,{class:"ml-2 h-4 w-4"}),e(ne),e(te),e(O),e(k),e(p);var Z=r(p,2);be(Z,5,()=>i.coPilot.secondary,Le,(q,n)=>{Qe(q,{get icon(){return s(n).icon},get title(){return s(n).title},get description(){return s(n).description}})}),e(Z),e(o),_e(()=>{z(G,i.automatedApply.title),z(W,i.automatedApply.description),z(S,i.jobTracker.title),z(M,i.jobTracker.description),z(d,i.resumeBuilder.title),z(ee,i.resumeBuilder.description),z(y,i.coPilot.title),z(Y,i.coPilot.description)}),l(U,o)}var Mr=v('<div class="flex h-[400px] items-center justify-center"><div class="h-8 w-8 animate-spin rounded-full border-2 border-current border-t-transparent"></div></div>'),Lr=v('<div class="flex h-[400px] items-center justify-center"><p class="text-muted-foreground">No job collections available</p></div>'),Or=v('<a><div class="flex flex-col gap-1"><h3 class="text-card-foreground group-hover:text-primary text-xl font-semibold transition-colors"> </h3> <p class="text-muted-foreground text-sm"> </p></div> <span class="text-primary flex items-center text-xs font-medium">View Collection <!></span></a>'),Tr=v('<section class="text-foreground px-6 py-16"><div class="grid-cols-0 grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-8"><div class="group col-span-2 row-span-4 flex flex-col justify-center gap-8 p-6 text-left transition-all md:flex-col"><div class="flex flex-col gap-2"><h2 class="text-3xl font-bold">Explore Job Collections</h2> <p class="text-muted-foreground max-w-2xs text-lg">Discover curated job collections for popular career paths</p></div> <a class="border-border text-foreground hover:bg-primary hover:text-primary-foreground inline-flex w-[200px] items-center rounded-md border px-6 py-3 text-base font-medium shadow-sm transition-colors">View All Jobs <!></a></div> <!></div></section>');function Er(U,i){He(i,!1);let o=Oe(i,"isAuthenticated",8,!1),g=je([]),I=je(!0),f=je([]);function T(x){const E=[];for(let c=0;c<x;c++)if(c===0)E.push({colSpan:2,rowSpan:2});else{const _=Math.random()>.8?2:1,S=Math.random()>.8?2:1;E.push({colSpan:_,rowSpan:S})}return E}async function $(){var x;Ce(I,!0);try{const E=await Jt(Nt);if(E.errors)throw new Error(E.errors[0].message);Ce(g,((x=E.data)==null?void 0:x.collections)||[]),Ce(f,T(s(g).length))}catch(E){console.error("Error loading collections:",E),Ft.error("Failed to load job collections")}finally{Ce(I,!1)}}ht(()=>{$()}),nt();var w=Tr(),G=t(w),P=t(G),W=r(t(P),2),F=r(t(W));De(F,{class:"ml-2 h-4 w-4"}),e(W),e(P);var Q=r(P,2);{var ce=x=>{var E=Mr();l(x,E)},J=(x,E)=>{{var c=S=>{var R=Lr();l(S,R)},_=S=>{var R=ve(),M=V(R);be(M,1,()=>s(g),Le,(D,A,L)=>{var N=Or(),se=t(N),H=t(se),ie=t(H,!0);e(H);var h=r(H,2),d=t(h);e(h),e(se);var b=r(se,2),ee=r(t(b));De(ee,{class:"ml-1 h-4 w-4 transition-transform group-hover:translate-x-1"}),e(b),e(N),_e(pe=>{var a,m;Ae(N,"href",o()?`/dashboard/jobs?collection=${s(A).slug}`:`/jobs?collection=${s(A).slug}`),Ye(N,1,`border-border bg-card hover:border-primary/50 hover:bg-accent group -m-[.55px] flex flex-col justify-between gap-8 border p-6 transition-all ${L===0?"col-span-2 row-span-2":((a=s(f)[L])==null?void 0:a.colSpan)===2?"col-span-2":""} ${((m=s(f)[L])==null?void 0:m.rowSpan)===2?"row-span-2":""}`),z(ie,s(A).name),z(d,`Explore ${pe??""} opportunities`)},[()=>s(A).name.toLowerCase()],Je),l(D,N)}),l(S,R)};Ie(x,S=>{s(g).length===0?S(c):S(_,!1)},E)}};Ie(Q,x=>{s(I)?x(ce):x(J,!1)})}e(G),e(w),_e(()=>Ae(W,"href",o()?"/dashboard/jobs":"/jobs")),l(U,w),Ve()}var Rr=v('<section class="py-16"><div class="container mx-auto px-4"><div class="bg-primary text-primary-foreground rounded-2xl p-12 text-center"><h2 class="mb-6 text-3xl font-bold md:text-4xl">Ready to Supercharge Your Job Search?</h2> <p class="mx-auto mb-8 max-w-2xl text-xl opacity-90">Join thousands of job seekers who have landed their dream jobs faster with Auto Apply.</p> <div class="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0"><button class="bg-background text-foreground hover:bg-muted rounded-md px-8 py-6 text-lg transition-colors">Get Started Free</button> <button class="border-primary-foreground hover:bg-primary-foreground/20 group flex items-center rounded-md border px-8 py-6 text-lg transition-colors">See How It Works <!></button></div> <p class="text-primary-foreground/80 mt-6 text-sm">No credit card required. 7-day free trial on all plans.</p></div></div></section>');function Ur(U){var i=Rr(),o=t(i),g=t(o),I=r(t(g),4),f=r(t(I),2),T=r(t(f));De(T,{class:"ml-2 h-5 w-5 transition-transform group-hover:translate-x-1"}),e(f),e(I),Me(2),e(g),e(o),e(i),l(U,i)}var Dr=v("<!> <!> <!> <!> <!> <!> <!> <!>",1);function Ss(U,i){He(i,!1);const o=je();let g=Oe(i,"data",8);wt(()=>St(g()),()=>{Ce(o,g().user)}),$t(),nt();var I=Dr(),f=V(I);kt(f,{title:"Hirli - Automate Your Job Applications",description:"Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings, auto-fills applications, and tracks your progress.",keywords:"job application, automation, resume, job search, AI, career",url:"https://hirli.com",image:"/assets/og-image.jpg"});var T=r(f,2);sr(T,{});var $=r(T,2);Ar($,{});var w=r($,2);ir(w);var G=r(w,2);Pr(G);var P=r(G,2);ur(P,{});var W=r(P,2);const F=Je(()=>!!s(o));Er(W,{get isAuthenticated(){return s(F)}});var Q=r(W,2);Ur(Q),l(U,I),Ve()}export{Ss as component};
