import{c as ze,f as o,t as ee,a as s,e as Xt}from"../chunks/BasJTneF.js";import{p as St,k as Ye,v as ft,i as Gt,f as $,c as r,n as L,t as T,s as a,r as t,g as e,x as _e,d as M,a as jt,aM as er}from"../chunks/CGmarHxI.js";import{s as n}from"../chunks/CIt1g2O9.js";import{i as U}from"../chunks/u21ee2wt.js";import{e as $t,i as wt}from"../chunks/C3w0v0gR.js";import{c as F}from"../chunks/BvdI7LR8.js";import{g as tr}from"../chunks/CmxjS0TN.js";import{d as Et,s as Ut,a as yt,b as At}from"../chunks/B-Xjo-Yt.js";import{C as gt}from"../chunks/DuGukytH.js";import{C as pt}from"../chunks/Cdn-N1RY.js";import{C as Nt}from"../chunks/BkJY4La4.js";import{C as It}from"../chunks/GwmmX_iF.js";import{C as zt}from"../chunks/D50jIuLr.js";import{R as rr,T as ar}from"../chunks/I7hvcB12.js";import{B as et}from"../chunks/B1K98fMG.js";import{S as sr}from"../chunks/C6g8ubaU.js";import{p as Dt}from"../chunks/Btcx8l8F.js";import{C as or,B as ir,a as dr}from"../chunks/XnZcpgwi.js";import{L as Mt}from"../chunks/BhzFx1Wy.js";import{T as Ot}from"../chunks/Csk_I0QV.js";import{T as Yt,M as Ht}from"../chunks/lirlZJ-b.js";import{P as lr}from"../chunks/DrGkVJ95.js";import{B as Wt}from"../chunks/DaBofrVv.js";import{A as nr}from"../chunks/B-l1ubNa.js";import{C as Lt}from"../chunks/DW7T7T22.js";import{C as vr}from"../chunks/-SpbofVw.js";import{T as Bt}from"../chunks/CTO_B1Jk.js";import{a as Ft}from"../chunks/iTBjRg9v.js";import{g as Vt}from"../chunks/BiJhC7W5.js";import{C as Jt}from"../chunks/DZCYCPd3.js";import{R as ur}from"../chunks/qwsZpUIl.js";import{T as qt}from"../chunks/C88uNE8B.js";import{T as Zt}from"../chunks/DmZyh-PW.js";import{C as cr}from"../chunks/BwkAotBa.js";var mr=o('<!> <span class="font-medium text-green-600"> </span>',1),fr=o('<!> <span class="font-medium text-red-600"> </span>',1),gr=o('<!> <span class="font-medium text-gray-600">No change</span>',1),pr=o('<div class="flex items-center gap-1 text-sm"><!></div>'),_r=o('<!> <span class="sr-only">Refresh</span>',1),xr=o('<div class="flex items-center justify-between"><div><div class="flex items-center gap-3"><div><!> <!></div> <!></div></div> <div class="flex items-center space-x-2"><select class="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-3 py-1 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"><option>Daily</option><option>Weekly</option><option>Monthly</option></select> <!></div></div>'),hr=o('<div class="flex justify-center py-8"><!></div>'),br=o('<div class="bg-destructive/10 text-destructive rounded-md p-4 text-sm"><p> </p></div>'),yr=o('<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No usage data available.</p></div>'),$r=o('<div class="flex items-center gap-2"><div class="h-0.5 w-4 opacity-70" style="background-color: var(--chart-5); border-top: 2px dashed;"></div> <span>Limit</span></div>'),wr=o('<!> <div class="text-muted-foreground mt-4 flex items-center justify-between text-sm"><div class="flex items-center gap-4"><div class="flex items-center gap-2"><div class="h-3 w-3 rounded" style="background-color: var(--chart-1);"></div> <span>Usage</span></div> <!></div> <div> </div></div>',1),Ur=o("<!> <!>",1);function Dr(tt,m){St(m,!0);const Te=Dt(m,"title",3,"Usage History"),xe=Dt(m,"description",3,"Track your usage over time"),rt=Dt(m,"periodsToShow",3,6),V=Dt(m,"initialPeriodType",3,"monthly");let D=Ye(ft(V())),de=Ye(!0),v=Ye(null),G=Ye(ft([]));const Le={used:{label:"Usage",color:"var(--chart-1)"},limit:{label:"Limit",color:"var(--chart-5)"}},Fe=_e(()=>()=>e(G).map(c=>({period:qe(c.date,e(D)),used:c.used,limit:c.limit||0})));let De=_e(()=>()=>{var I,C;if(e(G).length<2)return null;const c=((I=e(G)[e(G).length-1])==null?void 0:I.used)||0,x=((C=e(G)[e(G).length-2])==null?void 0:C.used)||0;if(x===0)return null;const f=c-x,W=Math.round(f/x*100);return{change:f,percentChange:W,direction:f>0?"up":f<0?"down":"stable"}});function qe(c,x){if(x==="daily")return c.toLocaleDateString("en-US",{month:"short",day:"numeric"});if(x==="weekly"){const f=new Date(c);f.setDate(c.getDate()-c.getDay());const W=new Date(f);return W.setDate(f.getDate()+6),`${f.toLocaleDateString("en-US",{month:"short",day:"numeric"})} - ${W.toLocaleDateString("en-US",{month:"short",day:"numeric"})}`}else return c.toLocaleDateString("en-US",{month:"short",year:"numeric"})}function w(c,x){const f=[],W=new Date;for(let I=0;I<x;I++){const C=new Date;if(c==="daily"){C.setDate(W.getDate()-I);const z=`${C.getFullYear()}-${String(C.getMonth()+1).padStart(2,"0")}-${String(C.getDate()).padStart(2,"0")}`;f.unshift({date:C,period:z})}else if(c==="weekly"){C.setDate(W.getDate()-I*7);const z=new Date(C);z.setDate(C.getDate()-C.getDay());const R=`${z.getFullYear()}-W${Math.ceil((z.getDate()+z.getDay())/7)}`;f.unshift({date:z,period:R})}else{C.setMonth(W.getMonth()-I);const z=`${C.getFullYear()}-${String(C.getMonth()+1).padStart(2,"0")}`;f.unshift({date:C,period:z})}}return f}async function Y(){try{if(M(de,!0),M(v,null),!m.featureId||!m.limitId){M(G,[],!0),M(de,!1);return}const c=w(e(D),rt()),x=await fetch(`/api/feature-usage?featureId=${m.featureId}&limitId=${m.limitId}`);if(!x.ok)throw new Error("Failed to fetch usage data");const f=await x.json();M(G,c.map(({date:W,period:I})=>{const C=f.find(z=>z.period===I);return{date:W,period:I,used:C?C.used:0,limit:C?C.limit:null}}),!0)}catch(c){console.error("Error fetching usage data:",c),M(v,c.message,!0)}finally{M(de,!1)}}Gt(()=>{Y()});var N=ze(),ge=$(N);F(ge,()=>gt,(c,x)=>{x(c,{children:(f,W)=>{var I=Ur(),C=$(I);F(C,()=>It,(R,q)=>{q(R,{children:(ne,ve)=>{var l=xr(),i=r(l),p=r(i),g=r(p),u=r(g);F(u,()=>zt,(Q,K)=>{K(Q,{children:(ue,ye)=>{L();var E=ee();T(()=>n(E,Te())),s(ue,E)},$$slots:{default:!0}})});var _=a(u,2);F(_,()=>Nt,(Q,K)=>{K(Q,{children:(ue,ye)=>{L();var E=ee();T(()=>n(E,xe())),s(ue,E)},$$slots:{default:!0}})}),t(g);var d=a(g,2);{var y=Q=>{var K=pr(),ue=r(K);{var ye=h=>{var ce=mr(),H=$(ce);Ot(H,{class:"h-4 w-4 text-green-500"});var X=a(H,2),me=r(X);t(X),T(te=>n(me,`+${te??""}%`),[()=>e(De)().percentChange]),s(h,ce)},E=(h,ce)=>{{var H=me=>{var te=fr(),P=$(te);Yt(P,{class:"h-4 w-4 text-red-500"});var oe=a(P,2),Ce=r(oe);t(oe),T($e=>n(Ce,`${$e??""}%`),[()=>e(De)().percentChange]),s(me,te)},X=me=>{var te=gr(),P=$(te);Ht(P,{class:"h-4 w-4 text-gray-500"}),L(2),s(me,te)};U(h,me=>{e(De)().direction==="down"?me(H):me(X,!1)},ce)}};U(ue,h=>{e(De)().direction==="up"?h(ye):h(E,!1)})}t(K),s(Q,K)};U(d,Q=>{e(De)()&&Q(y)})}t(p),t(i);var k=a(i,2),B=r(k),pe=r(B);pe.value=pe.__value="daily";var ae=a(pe);ae.value=ae.__value="weekly";var se=a(ae);se.value=se.__value="monthly",t(B);var be=a(B,2);et(be,{variant:"outline",size:"sm",onclick:Y,get disabled(){return e(de)},children:(Q,K)=>{var ue=_r(),ye=$(ue);const E=_e(()=>`h-4 w-4 ${e(de)?"animate-spin":""}`);Mt(ye,{get class(){return e(E)}}),L(2),s(Q,ue)},$$slots:{default:!0}}),t(k),t(l),Et(B,()=>e(D),Q=>M(D,Q)),s(ne,l)},$$slots:{default:!0}})});var z=a(C,2);F(z,()=>pt,(R,q)=>{q(R,{children:(ne,ve)=>{var l=ze(),i=$(l);{var p=u=>{var _=hr(),d=r(_);Mt(d,{class:"text-primary h-8 w-8 animate-spin"}),t(_),s(u,_)},g=(u,_)=>{{var d=k=>{var B=br(),pe=r(B),ae=r(pe);t(pe),t(B),T(()=>n(ae,`Error loading usage data: ${e(v)??""}`)),s(k,B)},y=(k,B)=>{{var pe=se=>{var be=yr();s(se,be)},ae=se=>{var be=wr(),Q=$(be);F(Q,()=>or,(H,X)=>{X(H,{get config(){return Le},class:"h-60 w-full",children:(me,te)=>{const P=_e(()=>e(Fe)()),oe=_e(()=>[{key:"used",label:Le.used.label,color:Le.used.color}]);ir(me,{get data(){return e(P)},x:"period",axis:"x",legend:!0,get series(){return e(oe)},props:{xAxis:{format:$e=>$e.slice(0,6)}},tooltip:$e=>{var ie=ze(),fe=$(ie);F(fe,()=>dr,(je,Se)=>{Se(je,{})}),s($e,ie)},$$slots:{tooltip:!0}})},$$slots:{default:!0}})});var K=a(Q,2),ue=r(K),ye=a(r(ue),2);{var E=H=>{var X=$r();s(H,X)};U(ye,H=>{e(G).some(X=>X.limit!==null&&X.limit>0)&&H(E)})}t(ue);var h=a(ue,2),ce=r(h);t(h),t(K),T(H=>n(ce,`Total: ${H??""} uses`),[()=>e(G).reduce((H,X)=>H+X.used,0)]),s(se,be)};U(k,se=>{e(G).length===0?se(pe):se(ae,!1)},B)}};U(u,k=>{e(v)?k(d):k(y,!1)},_)}};U(i,u=>{e(de)?u(p):u(g,!1)})}s(ne,l)},$$slots:{default:!0}})}),s(f,I)},$$slots:{default:!0}})}),s(tt,N),jt()}var Pr=o('<div class="mt-4 flex items-center gap-2"><div><!> <span> </span></div> <span class="text-muted-foreground text-xs">vs last month</span></div>'),Tr=o('<div class="flex items-center justify-between"><div><p class="text-muted-foreground text-sm font-medium">Total Usage</p> <p class="text-2xl font-bold"> </p> <p class="text-muted-foreground mt-1 text-xs">This month</p></div> <div class="flex h-12 w-12 items-center justify-center rounded-full bg-blue-50"><!></div></div> <!>',1),Fr=o('<div class="flex items-center justify-between"><div><p class="text-muted-foreground text-sm font-medium">Active Features</p> <p class="text-2xl font-bold"> </p> <p class="text-muted-foreground mt-1 text-xs"> </p></div> <div class="flex h-12 w-12 items-center justify-center rounded-full bg-green-50"><!></div></div> <div class="mt-4"><!></div>',1),Cr=o('<div class="mt-4"><!></div>'),kr=o('<div class="flex items-center justify-between"><div><p class="text-muted-foreground text-sm font-medium">Near Limits</p> <p class="text-2xl font-bold"> </p> <p class="text-muted-foreground mt-1 text-xs">features at 80%+</p></div> <div class="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-50"><!></div></div> <!>',1),Ar=o('<div class="mt-4"><!></div>'),Mr=o('<div class="flex items-center justify-between"><div><p class="text-muted-foreground text-sm font-medium">At Limits</p> <p class="text-2xl font-bold"> </p> <p class="text-muted-foreground mt-1 text-xs">features maxed out</p></div> <div class="flex h-12 w-12 items-center justify-center rounded-full bg-red-50"><!></div></div> <!>',1),Lr=o("<!> <!>",1),Sr=o('<div><div class="flex items-center gap-3"><!> <div><p> </p> <p> </p></div></div></div>'),jr=o('<div class="grid gap-4 md:grid-cols-2"><div class="space-y-2"><p class="text-muted-foreground text-sm font-medium">Current Month</p> <p class="text-3xl font-bold"> </p> <p class="text-muted-foreground text-sm">Total uses</p></div> <div class="space-y-2"><p class="text-muted-foreground text-sm font-medium">Previous Month</p> <p class="text-3xl font-bold"> </p> <p class="text-muted-foreground text-sm">Total uses</p></div></div> <!>',1),Nr=o("<!> <!>",1),Ir=o('<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4"><!> <!> <!> <!></div> <!>',1);function zr(tt,m){St(m,!0);const Te=Dt(m,"featuresData",19,()=>[]);let xe=_e(()=>()=>{const w=Te().reduce((c,x)=>{var f;return c+(((f=x.usage)==null?void 0:f.reduce((W,I)=>W+(I.used||0),0))||0)},0),Y=Te().filter(c=>{var x;return(x=c.usage)==null?void 0:x.some(f=>f.used>0)}).length,N=Te().filter(c=>{var x;return(x=c.usage)==null?void 0:x.some(f=>f.limit&&typeof f.limit=="number"&&f.percentUsed>=80)}).length,ge=Te().filter(c=>{var x;return(x=c.usage)==null?void 0:x.some(f=>f.limit&&typeof f.limit=="number"&&f.used>=f.limit)}).length;return{totalUsage:w,featuresWithUsage:Y,featuresNearLimit:N,featuresAtLimit:ge,totalFeatures:Te().length}});function rt(w,Y){return w==="up"?{icon:Ot,color:"text-green-600",bgColor:"bg-green-50",text:`+${Y}%`}:w==="down"?{icon:Yt,color:"text-red-600",bgColor:"bg-red-50",text:`${Y}%`}:{icon:Ht,color:"text-gray-600",bgColor:"bg-gray-50",text:"No change"}}const V=_e(()=>()=>m.usageTrends?rt(m.usageTrends.trendDirection,m.usageTrends.trendPercent):null);var D=Ir(),de=$(D),v=r(de);F(v,()=>gt,(w,Y)=>{Y(w,{children:(N,ge)=>{var c=ze(),x=$(c);F(x,()=>pt,(f,W)=>{W(f,{class:"p-6",children:(I,C)=>{var z=Tr(),R=$(z),q=r(R),ne=a(r(q),2),ve=r(ne,!0);t(ne),L(2),t(q);var l=a(q,2),i=r(l);nr(i,{class:"h-6 w-6 text-blue-600"}),t(l),t(R);var p=a(R,2);{var g=u=>{var _=Pr(),d=r(_),y=r(d);{var k=ae=>{var se=ze(),be=$(se);F(be,()=>e(V).icon,(Q,K)=>{K(Q,{get class(){return`h-3 w-3 ${e(V).color??""}`}})}),s(ae,se)};U(y,ae=>{e(V).icon&&ae(k)})}var B=a(y,2),pe=r(B,!0);t(B),t(d),L(2),t(_),T(()=>{Ut(d,1,`flex items-center gap-1 rounded-full px-2 py-1 ${e(V).bgColor??""}`),Ut(B,1,`text-xs font-medium ${e(V).color??""}`),n(pe,e(V).text)}),s(u,_)};U(p,u=>{e(V)&&u(g)})}T(()=>n(ve,e(xe).totalUsage)),s(I,z)},$$slots:{default:!0}})}),s(N,c)},$$slots:{default:!0}})});var G=a(v,2);F(G,()=>gt,(w,Y)=>{Y(w,{children:(N,ge)=>{var c=ze(),x=$(c);F(x,()=>pt,(f,W)=>{W(f,{class:"p-6",children:(I,C)=>{var z=Fr(),R=$(z),q=r(R),ne=a(r(q),2),ve=r(ne,!0);t(ne);var l=a(ne,2),i=r(l);t(l),t(q);var p=a(q,2),g=r(p);Lt(g,{class:"h-6 w-6 text-green-600"}),t(p),t(R);var u=a(R,2),_=r(u);const d=_e(()=>e(xe).featuresWithUsage/e(xe).totalFeatures*100);lr(_,{get value(){return e(d)},class:"h-2"}),t(u),T(()=>{n(ve,e(xe).featuresWithUsage),n(i,`of ${e(xe).totalFeatures??""} available`)}),s(I,z)},$$slots:{default:!0}})}),s(N,c)},$$slots:{default:!0}})});var Le=a(G,2);F(Le,()=>gt,(w,Y)=>{Y(w,{children:(N,ge)=>{var c=ze(),x=$(c);F(x,()=>pt,(f,W)=>{W(f,{class:"p-6",children:(I,C)=>{var z=kr(),R=$(z),q=r(R),ne=a(r(q),2),ve=r(ne,!0);t(ne),L(2),t(q);var l=a(q,2),i=r(l);vr(i,{class:"h-6 w-6 text-yellow-600"}),t(l),t(R);var p=a(R,2);{var g=u=>{var _=Cr(),d=r(_);Wt(d,{variant:"outline",class:"border-yellow-200 text-yellow-700",children:(y,k)=>{L();var B=ee("Action needed");s(y,B)},$$slots:{default:!0}}),t(_),s(u,_)};U(p,u=>{e(xe)().featuresNearLimit>0&&u(g)})}T(u=>n(ve,u),[()=>e(xe)().featuresNearLimit]),s(I,z)},$$slots:{default:!0}})}),s(N,c)},$$slots:{default:!0}})});var Fe=a(Le,2);F(Fe,()=>gt,(w,Y)=>{Y(w,{children:(N,ge)=>{var c=ze(),x=$(c);F(x,()=>pt,(f,W)=>{W(f,{class:"p-6",children:(I,C)=>{var z=Mr(),R=$(z),q=r(R),ne=a(r(q),2),ve=r(ne,!0);t(ne),L(2),t(q);var l=a(q,2),i=r(l);Bt(i,{class:"h-6 w-6 text-red-600"}),t(l),t(R);var p=a(R,2);{var g=u=>{var _=Ar(),d=r(_);Wt(d,{variant:"destructive",class:"text-xs",children:(y,k)=>{L();var B=ee("Upgrade needed");s(y,B)},$$slots:{default:!0}}),t(_),s(u,_)};U(p,u=>{e(xe)().featuresAtLimit>0&&u(g)})}T(u=>n(ve,u),[()=>e(xe)().featuresAtLimit]),s(I,z)},$$slots:{default:!0}})}),s(N,c)},$$slots:{default:!0}})}),t(de);var De=a(de,2);{var qe=w=>{var Y=ze(),N=$(Y);F(N,()=>gt,(ge,c)=>{c(ge,{class:"mt-6",children:(x,f)=>{var W=Nr(),I=$(W);F(I,()=>It,(z,R)=>{R(z,{children:(q,ne)=>{var ve=Lr(),l=$(ve);F(l,()=>zt,(p,g)=>{g(p,{children:(u,_)=>{L();var d=ee("Monthly Comparison");s(u,d)},$$slots:{default:!0}})});var i=a(l,2);F(i,()=>Nt,(p,g)=>{g(p,{children:(u,_)=>{L();var d=ee("Compare your usage with the previous month");s(u,d)},$$slots:{default:!0}})}),s(q,ve)},$$slots:{default:!0}})});var C=a(I,2);F(C,()=>pt,(z,R)=>{R(z,{children:(q,ne)=>{var ve=jr(),l=$(ve),i=r(l),p=a(r(i),2),g=r(p,!0);t(p),L(2),t(i);var u=a(i,2),_=a(r(u),2),d=r(_,!0);t(_),L(2),t(u),t(l);var y=a(l,2);{var k=B=>{var pe=Sr(),ae=r(pe),se=r(ae);const be=_e(()=>{var h;return(h=e(V)())==null?void 0:h.color});F(se,()=>{var h;return(h=e(V)())==null?void 0:h.icon},(h,ce)=>{ce(h,{get class(){return`h-5 w-5 ${e(be)??""}`}})});var Q=a(se,2),K=r(Q),ue=r(K,!0);t(K);var ye=a(K,2),E=r(ye);t(ye),t(Q),t(ae),t(pe),T((h,ce,H,X)=>{Ut(pe,1,`mt-6 rounded-lg p-4 ${h??""}`),Ut(K,1,`font-medium ${ce??""}`),n(ue,m.usageTrends.trendDirection==="up"?"Usage increased":m.usageTrends.trendDirection==="down"?"Usage decreased":"Usage remained stable"),Ut(ye,1,`text-sm ${H??""} opacity-80`),n(E,`${X??""} compared to last month`)},[()=>{var h;return(h=e(V)())==null?void 0:h.bgColor},()=>{var h;return(h=e(V)())==null?void 0:h.color},()=>{var h;return(h=e(V)())==null?void 0:h.color},()=>{var h;return(h=e(V)())==null?void 0:h.text}]),s(B,pe)};U(y,B=>{e(V)&&B(k)})}T(()=>{n(g,m.usageTrends.currentMonthTotal),n(d,m.usageTrends.previousMonthTotal)}),s(q,ve)},$$slots:{default:!0}})}),s(x,W)},$$slots:{default:!0}})}),s(w,Y)};U(De,w=>{m.usageTrends&&w(qe)})}s(tt,D),jt()}var Rr=o("<!> <!>",1),Er=o('<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No usage data available to display.</p></div>'),Wr=Xt('<path opacity="0.8" class="transition-opacity hover:opacity-100"><title> </title></path>'),Br=o('<div class="flex items-center justify-between rounded-lg border p-3"><div class="flex items-center gap-3"><div class="h-4 w-4 rounded-full"></div> <div><p class="font-medium"> </p> <p class="text-muted-foreground text-sm capitalize"> </p></div></div> <div class="text-right"><p class="font-semibold"> </p> <!></div></div>'),Or=o('<div class="flex flex-col items-center gap-8 lg:flex-row"><div class="relative"><svg class="-rotate-90 transform"><circle fill="none" stroke="currentColor" stroke-opacity="0.1"></circle><!></svg> <div class="absolute inset-0 flex flex-col items-center justify-center"><div class="text-2xl font-bold"> </div> <div class="text-muted-foreground text-sm">Total Uses</div></div></div> <div class="flex-1 space-y-3"></div></div> <div class="mt-6 grid grid-cols-2 gap-4 md:grid-cols-4"><div class="bg-muted/50 rounded-lg p-3 text-center"><p class="text-lg font-bold"> </p> <p class="text-muted-foreground text-sm">Active Features</p></div> <div class="bg-muted/50 rounded-lg p-3 text-center"><p class="text-lg font-bold"> </p> <p class="text-muted-foreground text-sm">Total Uses</p></div> <div class="bg-muted/50 rounded-lg p-3 text-center"><p class="text-lg font-bold"> </p> <p class="text-muted-foreground text-sm">Avg per Feature</p></div> <div class="bg-muted/50 rounded-lg p-3 text-center"><p class="text-lg font-bold"> </p> <p class="text-muted-foreground text-sm">Most Used</p></div></div>',1),Yr=o("<!> <!>",1);function Hr(tt,m){St(m,!0);const Te=Dt(m,"featuresData",19,()=>[]),xe=Dt(m,"title",3,"Usage Distribution"),rt=Dt(m,"description",3,"See how your usage is distributed across features"),V=_e(()=>()=>{const w=Te().map(N=>{var c;const ge=((c=N.usage)==null?void 0:c.reduce((x,f)=>x+(f.used||0),0))||0;return{name:N.name,usage:ge,category:N.category,color:D(N.category)}}).filter(N=>N.usage>0).sort((N,ge)=>ge.usage-N.usage),Y=w.reduce((N,ge)=>N+ge.usage,0);return w.map(N=>({...N,percentage:Y>0?Math.round(N.usage/Y*100):0}))});function D(w){return{core:"#3b82f6",resume:"#10b981",job_search:"#f59e0b",applications:"#8b5cf6",analytics:"#ef4444",team:"#06b6d4",integration:"#84cc16",communication:"#f97316",automation:"#ec4899",security:"#6b7280",customization:"#14b8a6",advanced:"#7c3aed"}[w]||"#6b7280"}const de=200,v=20,G=(de-v)/2,Le=_e(()=>()=>{let w=0;return e(V)().map(Y=>{const N=w/100*360-90,ge=(w+Y.percentage)/100*360-90;w+=Y.percentage;const c=N*Math.PI/180,x=ge*Math.PI/180,f=de/2+G*Math.cos(c),W=de/2+G*Math.sin(c),I=de/2+G*Math.cos(x),C=de/2+G*Math.sin(x),z=Y.percentage>50?1:0,R=[`M ${de/2} ${de/2}`,`L ${f} ${W}`,`A ${G} ${G} 0 ${z} 1 ${I} ${C}`,"Z"].join(" ");return{...Y,pathData:R,startAngle:N,endAngle:ge}})}),Fe=_e(()=>()=>e(V)().reduce((w,Y)=>w+Y.usage,0));var De=ze(),qe=$(De);F(qe,()=>gt,(w,Y)=>{Y(w,{children:(N,ge)=>{var c=Yr(),x=$(c);F(x,()=>It,(W,I)=>{I(W,{children:(C,z)=>{var R=Rr(),q=$(R);F(q,()=>zt,(ve,l)=>{l(ve,{children:(i,p)=>{L();var g=ee();T(()=>n(g,xe())),s(i,g)},$$slots:{default:!0}})});var ne=a(q,2);F(ne,()=>Nt,(ve,l)=>{l(ve,{children:(i,p)=>{L();var g=ee();T(()=>n(g,rt())),s(i,g)},$$slots:{default:!0}})}),s(C,R)},$$slots:{default:!0}})});var f=a(x,2);F(f,()=>pt,(W,I)=>{I(W,{children:(C,z)=>{var R=ze(),q=$(R);{var ne=l=>{var i=Er();s(l,i)},ve=l=>{var i=Or(),p=$(i),g=r(p),u=r(g);yt(u,"width",de),yt(u,"height",de);var _=r(u);yt(_,"cx",de/2),yt(_,"cy",de/2),yt(_,"r",G),yt(_,"stroke-width",v);var d=a(_);$t(d,17,()=>e(Le)(),wt,(te,P)=>{var oe=Wr(),Ce=r(oe),$e=r(Ce);t(Ce),t(oe),T(()=>{yt(oe,"d",e(P).pathData),yt(oe,"fill",e(P).color),n($e,`${e(P).name??""}: ${e(P).usage??""} uses (${e(P).percentage??""}%)`)}),s(te,oe)}),t(u);var y=a(u,2),k=r(y),B=r(k,!0);t(k),L(2),t(y),t(g);var pe=a(g,2);$t(pe,21,()=>e(V)(),wt,(te,P)=>{var oe=Br(),Ce=r(oe),$e=r(Ce),ie=a($e,2),fe=r(ie),je=r(fe,!0);t(fe);var Se=a(fe,2),Re=r(Se,!0);t(Se),t(ie),t(Ce);var Ee=a(Ce,2),Ne=r(Ee),at=r(Ne,!0);t(Ne);var we=a(Ne,2);Wt(we,{variant:"outline",class:"text-xs",children:(he,st)=>{L();var Ze=ee();T(()=>n(Ze,`${e(P).percentage??""}%`)),s(he,Ze)},$$slots:{default:!0}}),t(Ee),t(oe),T(he=>{At($e,`background-color: ${e(P).color??""}`),n(je,e(P).name),n(Re,he),n(at,e(P).usage)},[()=>e(P).category.replace("_"," ")]),s(te,oe)}),t(pe),t(p);var ae=a(p,2),se=r(ae),be=r(se),Q=r(be,!0);t(be),L(2),t(se);var K=a(se,2),ue=r(K),ye=r(ue,!0);t(ue),L(2),t(K);var E=a(K,2),h=r(E),ce=r(h,!0);t(h),L(2),t(E);var H=a(E,2),X=r(H),me=r(X,!0);t(X),L(2),t(H),t(ae),T((te,P)=>{n(B,e(Fe)),n(Q,e(V).length),n(ye,e(Fe)),n(ce,te),n(me,P)},[()=>e(V)().length>0?Math.round(e(Fe)()/e(V)().length):0,()=>e(V).length>0?e(V)[0].name.slice(0,8)+"...":"N/A"]),s(l,i)};U(q,l=>{e(V).length===0?l(ne):l(ve,!1)})}s(C,R)},$$slots:{default:!0}})}),s(N,c)},$$slots:{default:!0}})}),s(tt,De),jt()}var Vr=o("<!> Export Data",1),Jr=o("<!> Refreshing...",1),qr=o("<!> Refresh",1),Zr=o('<div class="flex justify-center py-8"><!></div>'),Gr=o("<div></div>"),Kr=o('<div class="relative h-full w-full rounded-sm bg-gray-200"><div class="absolute bottom-0 left-1/2 top-0 w-0.5 -translate-x-1/2 transform bg-gray-400"></div> <div class="absolute bottom-0 top-0 h-2 w-2 -translate-x-1/2 -translate-y-1/4 transform rounded-full bg-blue-500"></div></div>'),Qr=o('<div class="mt-3 flex h-2 w-full items-center"><!></div>'),Xr=o('<div class="mt-3 flex h-2 w-full items-center"><div class="bg-primary h-full rounded-sm"></div></div>'),ea=o(`<div class="bg-warning/10 border-warning/20 mt-4 rounded-md border p-4 text-sm"><div class="flex items-start gap-3"><div class="text-warning mt-0.5"><!></div> <div><p class="text-warning font-medium"> </p> <p class="text-muted-foreground mt-1">You've reached the maximum usage for some features.</p> <div class="mt-3"><!></div></div></div></div>`),ta=o('<div class="grid gap-4 md:grid-cols-3"><div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Features Used</p> <h4 class="mt-1 text-2xl font-bold"> </h4> <p class="text-muted-foreground mt-1 text-xs"> </p></div> <div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Features at Limit</p> <h4 class="mt-1 text-2xl font-bold"> </h4> <p class="text-muted-foreground mt-1 text-xs"> </p></div> <div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Usage Efficiency</p> <h4 class="mt-1 text-2xl font-bold"> </h4> <div class="mt-1"><div class="bg-muted h-1.5 w-full overflow-hidden rounded-full"><div class="bg-primary h-full"></div></div> <p class="text-muted-foreground mt-1 text-xs"> </p></div></div></div> <div class="mt-4 grid gap-4 md:grid-cols-3"><div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Usage Trend</p> <h4 class="mt-1 flex items-center text-2xl font-bold"> <!></h4> <div class="mt-3 flex h-2 w-full items-center gap-1"></div> <p class="text-muted-foreground mt-2 text-xs"><!></p></div> <div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Usage Comparison</p> <h4 class="mt-1 text-2xl font-bold"><!></h4> <!> <p class="text-muted-foreground mt-2 text-xs"><!></p></div> <div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Most Valuable Feature</p> <h4 class="mt-1 text-2xl font-bold"> </h4> <!> <p class="text-muted-foreground mt-2 text-xs"> </p></div></div> <!>',1),ra=o('<div class="mb-4 flex items-center justify-between"><div><h3 class="text-lg font-medium">Feature Usage Details</h3> <p class="text-muted-foreground text-sm">Track your usage across all features</p></div> <div class="flex gap-2"><!> <!></div></div> <!>',1),aa=o("<!> <!>",1),sa=o("<!> <!>",1),oa=o('<div class="flex justify-center py-8"><!></div>'),ia=o('<div class="rounded-md border p-10 text-center"><div class="bg-muted mx-auto mb-4 flex h-6 w-6 items-center justify-center rounded-full"><!></div> <h3 class="mb-2 text-lg font-medium">No Usage Data Available</h3> <p class="text-muted-foreground mx-auto max-w-md"> </p></div>'),da=o('<div class="mt-4"><!></div>'),la=o('<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No feature usage data available yet.</p> <p class="text-muted-foreground mt-2 text-sm">Start using features to see your usage statistics.</p> <!></div>'),na=(tt,m,Te,xe)=>{M(m,e(Te).id,!0),e(Te).usage&&e(Te).usage.length>0?M(xe,e(Te).usage[0].limitId,!0):M(xe,"")},va=o('<span class="bg-primary/10 text-primary ml-2 rounded-full px-2 py-0.5 text-xs font-medium"> </span>'),ua=o('<button><div class="flex items-center gap-3"><div class="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full"><span class="text-primary text-sm"> </span></div> <div class="flex-1"><h4 class="font-medium"> </h4> <div class="flex items-center justify-between"><p class="text-muted-foreground text-sm"> </p> <!></div></div></div></button>'),ca=o('<div class="space-y-2"><h4 class="text-muted-foreground text-sm font-medium uppercase"> </h4> <div class="space-y-2"></div></div>'),ma=o('<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">Select a feature to view its usage details.</p></div>'),fa=o('<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No usage data available for this feature.</p></div>'),ga=o('<p class="text-muted-foreground text-xs">No usage yet</p>'),pa=o('<p class="text-muted-foreground text-xs"> </p>'),_a=o('<div class="bg-muted h-2 w-full overflow-hidden rounded-full"><div></div></div>'),xa=o('<div class="bg-muted h-2 w-full overflow-hidden rounded-full"><div class="bg-primary/30 h-full" style="width: 0%"></div></div>'),ha=o('<div class="text-muted-foreground mt-3 text-xs"><!></div>'),ba=o('<div class="rounded-md border p-4"><div class="mb-2 flex items-center justify-between"><h4 class="font-medium"> </h4> <div class="text-right"><span class="font-medium"> </span> <!></div></div> <p class="text-muted-foreground mb-3 text-sm"> </p> <!> <!> <div class="mt-4 flex flex-wrap gap-2"><!> <!></div></div>'),ya=o('<div class="space-y-4"></div>'),$a=o('<div class="grid gap-6 md:grid-cols-2"><div class="space-y-6"><h3 class="text-lg font-medium">Features by Category</h3> <!></div> <div class="space-y-4"><h3 class="text-lg font-medium"> </h3> <!></div></div>'),wa=(tt,m,Te)=>{var rt,V;const xe=tt.target;((V=(rt=e(m).find(D=>D.id===xe.value))==null?void 0:rt.usage)==null?void 0:V.length)>0&&M(Te,e(m).find(D=>D.id===xe.value).usage[0].limitId,!0)},Ua=o("<option> </option>"),Da=o("<option> </option>"),Pa=o('<select class="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-3 py-1 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"><option disabled>Select Limit</option><!></select>'),Ta=o("<!> <span>Date Range</span> <!>",1),Fa=o('<div class="mb-6"><div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between"><div><h3 class="text-lg font-medium">Usage History</h3> <p class="text-muted-foreground text-sm"> </p></div> <div class="flex flex-wrap items-center gap-2"><select class="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-3 py-1 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"><option disabled>Select Feature</option><!></select> <!> <div class="ml-auto flex items-center gap-2"><div class="relative"><!></div> <div class="flex items-center gap-1 overflow-hidden rounded-md border"><!> <!> <!></div></div></div></div></div> <!> <div class="mt-8 rounded-md border p-4"><h4 class="mb-2 text-sm font-medium">Usage Tips</h4> <ul class="text-muted-foreground space-y-2 text-sm"><li class="flex items-start gap-2"><!> <span>Usage is tracked on a monthly basis and resets at the beginning of each month.</span></li> <li class="flex items-start gap-2"><!> <span>Usage limits are based on your account type and settings.</span></li> <li class="flex items-start gap-2"><!> <span>Contact support if you need temporary limit increases for special projects.</span></li></ul></div>',1),Ca=o('<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">Select a feature and limit to view usage history.</p></div>'),ka=o('<div class="space-y-6"><!></div>'),Aa=o("<!> <!> <!>",1),Ma=o("<!> <!>",1),La=o('<!> <div class="border-border flex flex-col justify-between border-b p-6"><h2 class="text-lg font-semibold">Feature Usage</h2> <p class="text-muted-foreground text-foreground/80">Track your feature usage and subscription limits.</p></div> <div class="space-y-6"><!> <!> <!></div> <!>',1);function gs(tt,m){var q,ne,ve;St(m,!0);let Te=ft(((q=m==null?void 0:m.data)==null?void 0:q.user)||{}),xe=ft(((ne=m==null?void 0:m.data)==null?void 0:ne.featureUsage)||[]),rt=ft(((ve=m==null?void 0:m.data)==null?void 0:ve.usageTrends)||{currentMonthTotal:0,previousMonthTotal:0,trendPercent:0,trendDirection:"stable"}),V=Ye(ft([])),D=Ye(ft(xe)),de=Ye(ft({})),v=Ye(null),G=Ye(!0),Le=Ye(null),Fe=Ye(""),De=Ye(""),qe=Ye("30d"),w=_e(()=>e(D).find(l=>l.id===e(Fe)));function Y(){try{const l=e(D).map(d=>(d.usage||[]).map(k=>({Feature:d.name,Category:d.category,Limit:k.limitName,Used:k.used,Limit_Value:k.limit,Remaining:k.remaining,Percentage:k.percentUsed,Period:k.period}))).flat();if(l.length===0){alert("No usage data to export");return}const i=Object.keys(l[0]),p=[i.join(","),...l.map(d=>i.map(y=>`"${d[y]||""}"`).join(","))].join(`
`),g=new Blob([p],{type:"text/csv"}),u=URL.createObjectURL(g),_=document.createElement("a");_.href=u,_.download=`usage-data-${new Date().toISOString().split("T")[0]}.csv`,_.click(),URL.revokeObjectURL(u)}catch(l){console.error("Error exporting data:",l),alert("Failed to export data")}}N();async function N(){var l;M(G,!0),M(Le,null);try{if(!Te.id)throw new Error("User ID is required to fetch usage data");if((l=m==null?void 0:m.data)!=null&&l.featureUsage){M(D,m.data.featureUsage,!0),console.log("Using server-side feature usage data:",e(D));const i=e(D).length,p=e(D).filter(d=>d.usage&&d.usage.length>0&&d.usage.some(y=>y.used>0&&!y.placeholder)).length,g=e(D).filter(d=>d.usage&&d.usage.length>0&&d.usage.some(y=>y.limit!=="unlimited")).length,u=e(D).filter(d=>d.usage&&d.usage.length>0&&d.usage.some(y=>!y.placeholder&&y.limit!=="unlimited"&&typeof y.limit=="number"&&y.used>=y.limit)).length,_=e(D).filter(d=>d.usage&&d.usage.length>0&&d.usage.some(y=>!y.placeholder&&y.used>0)).map(d=>{const y=d.usage.filter(k=>!k.placeholder).reduce((k,B)=>B.percentUsed===void 0?k:!k||k.percentUsed===void 0||B.percentUsed>k.percentUsed?B:k,null)||d.usage[0];return{featureId:d.id,featureName:d.name,used:y.used,limit:y.limit,percentUsed:y.percentUsed}}).filter(d=>d.percentUsed!==void 0).sort((d,y)=>(y.percentUsed||0)-(d.percentUsed||0)).slice(0,5);M(v,{totalFeatures:i,featuresUsed:p,featuresWithLimits:g,featuresAtLimit:u,topFeatures:_},!0),console.log("Calculated usage summary:",e(v))}else console.warn("No feature usage data available from server"),M(D,[],!0),M(v,{totalFeatures:0,featuresUsed:0,featuresWithLimits:0,featuresAtLimit:0,topFeatures:[]},!0);if(M(de,e(D).reduce((i,p)=>{const g=p.category;return i[g]||(i[g]=[]),i[g].push(p),i},{}),!0),M(V,e(D).flatMap(i=>{var p;return((p=i.usage)==null?void 0:p.map(g=>({id:`${i.id}-${g.limitId}`,featureId:i.id,featureName:i.name,limitId:g.limitId,limitName:g.limitName,used:g.used,limit:g.limit,remaining:g.remaining,percentUsed:g.percentUsed,period:g.period})))||[]}),!0),!e(Fe)&&e(D).length>0){const i=e(D).find(p=>p.usage&&p.usage.length>0);i?(M(Fe,i.id,!0),i.usage&&i.usage.length>0&&M(De,i.usage[0].limitId,!0)):e(D).length>0&&M(Fe,e(D)[0].id,!0)}e(Le)||(e(D).length===0?M(Le,"No feature usage data found. This could be because you haven't used any features yet, or because the feature usage tracking system is still being set up."):e(D).every(i=>!i.usage||i.usage.length===0)?M(Le,"Features are available but no usage data has been recorded yet. Start using the application to see your usage statistics."):e(D).every(i=>i.usage&&i.usage.every(p=>p.placeholder===!0))&&console.info("All features have placeholder usage data. No actual usage has been recorded yet."))}catch(i){console.error("Error loading feature usage:",i),M(Le,i.message||"Failed to load feature usage",!0),M(D,[],!0),M(v,null),M(de,{},!0),M(V,[],!0)}finally{M(G,!1)}}let ge=Ye(ft([]));Gt(()=>{M(ge,e(D).filter(l=>l.usage&&l.usage.length>0).sort((l,i)=>l.name.localeCompare(i.name)),!0)});let c=_e(()=>()=>{const l={},i={[Ft.Core]:"Core Features",[Ft.JobSearch]:"Job Search",[Ft.Resume]:"Resume",[Ft.Applications]:"Applications",[Ft.Analytics]:"Analytics",[Ft.Team]:"Team",[Ft.Integration]:"Integration"};return Object.entries(e(de)).forEach(([p,g])=>{const u=g.filter(_=>_.usage&&_.usage.length>0);u.length>0&&(l[p]={name:i[p]||p,features:u.sort((_,d)=>_.name.localeCompare(d.name))})}),l});var x=La(),f=$(x);sr(f,{title:"Feature Usage | Hirli",description:"Track your feature usage and subscription limits",keywords:"feature usage, subscription, limits, tracking",url:"https://hirli.com/dashboard/settings/usage"});var W=a(f,4),I=r(W);zr(I,{get usageSummary(){return e(v)},get usageTrends(){return rt},get featuresData(){return e(D)}});var C=a(I,2);Hr(C,{get featuresData(){return e(D)}});var z=a(C,2);F(z,()=>gt,(l,i)=>{i(l,{children:(p,g)=>{var u=ze(),_=$(u);F(_,()=>pt,(d,y)=>{y(d,{children:(k,B)=>{var pe=ra(),ae=$(pe),se=a(r(ae),2),be=r(se);et(be,{variant:"outline",size:"sm",onclick:Y,get disabled(){return e(G)},children:(E,h)=>{var ce=Vr(),H=$(ce);Jt(H,{class:"mr-2 h-4 w-4"}),L(),s(E,ce)},$$slots:{default:!0}});var Q=a(be,2);et(Q,{variant:"outline",size:"sm",onclick:N,get disabled(){return e(G)},children:(E,h)=>{var ce=ze(),H=$(ce);{var X=te=>{var P=Jr(),oe=$(P);Mt(oe,{class:"mr-2 h-4 w-4 animate-spin"}),L(),s(te,P)},me=te=>{var P=qr(),oe=$(P);ur(oe,{class:"mr-2 h-4 w-4"}),L(),s(te,P)};U(H,te=>{e(G)?te(X):te(me,!1)})}s(E,ce)},$$slots:{default:!0}}),t(se),t(ae);var K=a(ae,2);{var ue=E=>{var h=Zr(),ce=r(h);Mt(ce,{class:"text-primary h-8 w-8 animate-spin"}),t(h),s(E,h)},ye=(E,h)=>{{var ce=H=>{var X=ta(),me=$(X),te=r(me),P=a(r(te),2),oe=r(P,!0);t(P);var Ce=a(P,2),$e=r(Ce);t(Ce),t(te);var ie=a(te,2),fe=a(r(ie),2),je=r(fe,!0);t(fe);var Se=a(fe,2),Re=r(Se,!0);t(Se),t(ie);var Ee=a(ie,2),Ne=a(r(Ee),2),at=r(Ne,!0);t(Ne);var we=a(Ne,2),he=r(we),st=r(he);t(he);var Ze=a(he,2),ot=r(Ze,!0);t(Ze),t(we),t(Ee),t(me);var We=a(me,2),Xe=r(We),nt=a(r(Xe),2),_t=r(nt),xt=a(_t);{var Ge=b=>{Ot(b,{class:"ml-2 h-5 w-5 text-green-500"})},it=(b,j)=>{{var A=O=>{Yt(O,{class:"ml-2 h-5 w-5 text-red-500"})},Z=O=>{Ht(O,{class:"ml-2 h-5 w-5 text-gray-500"})};U(b,O=>{var J;((J=m.data.usageTrends)==null?void 0:J.trendDirection)==="down"?O(A):O(Z,!1)},j)}};U(xt,b=>{var j;((j=m.data.usageTrends)==null?void 0:j.trendDirection)==="up"?b(Ge):b(it,!1)})}t(nt);var vt=a(nt,2);$t(vt,20,()=>Array(6),wt,(b,j,A)=>{var Z=Gr();const O=_e(()=>{var Ue;return A<Math.min(5,Math.abs(((Ue=m.data.usageTrends)==null?void 0:Ue.trendPercent)||0)/20)}),J=_e(()=>{var Ue,Ae;return((Ue=m.data.usageTrends)==null?void 0:Ue.trendDirection)==="up"?"bg-green-500":((Ae=m.data.usageTrends)==null?void 0:Ae.trendDirection)==="down"?"bg-red-500":"bg-gray-300"});T(()=>Ut(Z,1,`h-full flex-1 rounded-sm ${e(O)?e(J):"bg-gray-200"}`)),s(b,Z)}),t(vt);var ut=a(vt,2),Be=r(ut);{var Oe=b=>{var j=ee("Increased usage compared to last month");s(b,j)},ht=(b,j)=>{{var A=O=>{var J=ee("Decreased usage compared to last month");s(O,J)},Z=O=>{var J=ee("Usage stable compared to last month");s(O,J)};U(b,O=>{var J;((J=m.data.usageTrends)==null?void 0:J.trendDirection)==="down"?O(A):O(Z,!1)},j)}};U(Be,b=>{var j;((j=m.data.usageTrends)==null?void 0:j.trendDirection)==="up"?b(Oe):b(ht,!1)})}t(ut),t(Xe);var S=a(Xe,2),He=a(r(S),2),ke=r(He);{var Ie=b=>{var j=ee();T(()=>n(j,e(v).featuresUsed>e(v).totalFeatures/2?"Above Average":"Below Average")),s(b,j)},bt=b=>{var j=ee("No Data");s(b,j)};U(ke,b=>{e(v).featuresUsed>0?b(Ie):b(bt,!1)})}t(He);var Ke=a(He,2);{var dt=b=>{var j=Qr(),A=r(j);{var Z=O=>{var J=Kr();const Ue=_e(()=>Math.min(100,Math.max(0,e(v).featuresUsed/e(v).totalFeatures*100)));var Ae=a(r(J),2);t(J),T(()=>At(Ae,`left: ${e(Ue)}%`)),s(O,J)};U(A,O=>{e(v).totalFeatures>0&&O(Z)})}t(j),s(b,j)};U(Ke,b=>{e(v).featuresUsed>0&&b(dt)})}var re=a(Ke,2),Pe=r(re);{var le=b=>{var j=ee();T(()=>n(j,e(v).featuresUsed>e(v).totalFeatures/2?"You use more features than average":"You use fewer features than average")),s(b,j)},Ve=b=>{var j=ee("Start using features to see comparison");s(b,j)};U(Pe,b=>{e(v).featuresUsed>0?b(le):b(Ve,!1)})}t(re),t(S);var Me=a(S,2),Je=a(r(Me),2),Qe=r(Je,!0);t(Je);var ct=a(Je,2);{var Pt=b=>{var j=Xr(),A=r(j);t(j),T(Z=>At(A,Z),[()=>`width: ${Math.min(100,e(v).topFeatures[0].percentUsed||0)}%`]),s(b,j)};U(ct,b=>{e(v).topFeatures&&e(v).topFeatures.length>0&&b(Pt)})}var mt=a(ct,2),Tt=r(mt,!0);t(mt),t(Me),t(We);var Ct=a(We,2);{var kt=b=>{var j=ea(),A=r(j),Z=r(A),O=r(Z);Bt(O,{size:16}),t(Z);var J=a(Z,2),Ue=r(J),Ae=r(Ue);t(Ue);var lt=a(Ue,4),Rt=r(lt);et(Rt,{variant:"outline",size:"sm",class:"text-warning border-warning/20 hover:bg-warning/10",onclick:()=>Vt("/dashboard/settings/account"),children:(Kt,Sa)=>{L();var Qt=ee("View Account Settings");s(Kt,Qt)},$$slots:{default:!0}}),t(lt),t(J),t(A),t(j),T(()=>n(Ae,`You've reached usage limits on ${e(v).featuresAtLimit??""} feature${e(v).featuresAtLimit>1?"s":""}.`)),s(b,j)};U(Ct,b=>{e(v).featuresAtLimit>0&&b(kt)})}T((b,j)=>{var A;n(oe,e(v).featuresUsed),n($e,`of ${e(v).totalFeatures??""} total features`),n(je,e(v).featuresAtLimit),n(Re,e(v).featuresAtLimit>0?"Consider upgrading your plan":"You have room to grow"),n(at,b),At(st,j),n(ot,e(v)&&e(v).featuresUsed>0?`Using ${e(v).featuresUsed} of ${e(v).totalFeatures} available features`:"No features used yet"),n(_t,`${(((A=m.data.usageTrends)==null?void 0:A.trendPercent)||0)??""}% `),n(Qe,e(v).topFeatures&&e(v).topFeatures.length>0?e(v).topFeatures[0].featureName:"None"),n(Tt,e(v).topFeatures&&e(v).topFeatures.length>0?`Used ${e(v).topFeatures[0].used} times this month`:"No feature usage recorded yet")},[()=>e(v)&&e(v).totalFeatures>0?Math.round(e(v).featuresUsed/e(v).totalFeatures*100)+"%":"0%",()=>`width: ${e(v)&&e(v).totalFeatures>0?Math.round(e(v).featuresUsed/e(v).totalFeatures*100):0}%`]),s(H,X)};U(E,H=>{e(v)&&H(ce)},h)}};U(K,E=>{e(G)&&!e(v)?E(ue):E(ye,!1)})}s(k,pe)},$$slots:{default:!0}})}),s(p,u)},$$slots:{default:!0}})}),t(W);var R=a(W,2);F(R,()=>gt,(l,i)=>{i(l,{children:(p,g)=>{var u=Ma(),_=$(u);F(_,()=>It,(y,k)=>{k(y,{children:(B,pe)=>{var ae=aa(),se=$(ae);F(se,()=>zt,(Q,K)=>{K(Q,{children:(ue,ye)=>{L();var E=ee("Detailed Usage");s(ue,E)},$$slots:{default:!0}})});var be=a(se,2);F(be,()=>Nt,(Q,K)=>{K(Q,{children:(ue,ye)=>{L();var E=ee("View detailed usage for each feature");s(ue,E)},$$slots:{default:!0}})}),s(B,ae)},$$slots:{default:!0}})});var d=a(_,2);F(d,()=>pt,(y,k)=>{k(y,{children:(B,pe)=>{var ae=ze(),se=$(ae);F(se,()=>rr,(be,Q)=>{Q(be,{value:"features",children:(K,ue)=>{var ye=Aa(),E=$(ye);F(E,()=>ar,(H,X)=>{X(H,{children:(me,te)=>{var P=sa(),oe=$(P);F(oe,()=>qt,($e,ie)=>{ie($e,{value:"features",children:(fe,je)=>{L();var Se=ee("By Feature");s(fe,Se)},$$slots:{default:!0}})});var Ce=a(oe,2);F(Ce,()=>qt,($e,ie)=>{ie($e,{value:"history",children:(fe,je)=>{L();var Se=ee("Usage History");s(fe,Se)},$$slots:{default:!0}})}),s(me,P)},$$slots:{default:!0}})});var h=a(E,2);F(h,()=>Zt,(H,X)=>{X(H,{value:"features",class:"pt-4",children:(me,te)=>{var P=ze(),oe=$(P);{var Ce=ie=>{var fe=oa(),je=r(fe);Mt(je,{class:"text-primary h-8 w-8 animate-spin"}),t(fe),s(ie,fe)},$e=(ie,fe)=>{{var je=Re=>{var Ee=ia(),Ne=r(Ee),at=r(Ne);Bt(at,{class:"text-muted-foreground h-6 w-6"}),t(Ne);var we=a(Ne,4),he=r(we,!0);t(we),t(Ee),T(()=>n(he,e(Le))),s(Re,Ee)},Se=(Re,Ee)=>{{var Ne=we=>{var he=la(),st=a(r(he),4);{var Ze=ot=>{var We=da(),Xe=r(We);et(Xe,{variant:"outline",size:"sm",onclick:()=>{M(ge,e(D),!0)},children:(nt,_t)=>{L();var xt=ee("Show All Available Features");s(nt,xt)},$$slots:{default:!0}}),t(We),s(ot,We)};U(st,ot=>{e(D).length>0&&ot(Ze)})}t(he),s(we,he)},at=we=>{var he=$a(),st=r(he),Ze=a(r(st),2);$t(Ze,17,()=>Object.entries(e(c)),wt,(Ge,it)=>{var vt=_e(()=>er(e(it),2));let ut=()=>e(vt)[1];var Be=ca(),Oe=r(Be),ht=r(Oe,!0);t(Oe);var S=a(Oe,2);$t(S,21,()=>ut().features,wt,(He,ke)=>{var Ie=ua();Ie.__click=[na,Fe,ke,De];var bt=r(Ie),Ke=r(bt),dt=r(Ke),re=r(dt,!0);t(dt),t(Ke);var Pe=a(Ke,2),le=r(Pe),Ve=r(le,!0);t(le);var Me=a(le,2),Je=r(Me),Qe=r(Je,!0);t(Je);var ct=a(Je,2);{var Pt=mt=>{var Tt=va(),Ct=r(Tt);t(Tt),T(kt=>n(Ct,`${kt??""}%`),[()=>Math.round(e(ke).usage[0].percentUsed)]),s(mt,Tt)};U(ct,mt=>{e(ke).usage&&e(ke).usage.length>0&&e(ke).usage[0].percentUsed!==null&&mt(Pt)})}t(Me),t(Pe),t(bt),t(Ie),T(()=>{Ut(Ie,1,`w-full rounded-md border p-3 text-left transition-colors ${e(Fe)===e(ke).id?"border-primary bg-primary/5":"hover:bg-muted/50"}`),n(re,e(ke).icon||e(ke).name[0]),n(Ve,e(ke).name),n(Qe,e(ke).description)}),s(He,Ie)}),t(S),t(Be),T(()=>n(ht,ut().name)),s(Ge,Be)}),t(st);var ot=a(st,2),We=r(ot),Xe=r(We,!0);t(We);var nt=a(We,2);{var _t=Ge=>{var it=ma();s(Ge,it)},xt=(Ge,it)=>{{var vt=Be=>{var Oe=fa();s(Be,Oe)},ut=Be=>{var Oe=ya();$t(Oe,21,()=>e(w).usage,wt,(ht,S)=>{var He=ba(),ke=r(He),Ie=r(ke),bt=r(Ie,!0);t(Ie);var Ke=a(Ie,2),dt=r(Ke),re=r(dt);t(dt);var Pe=a(dt,2);{var le=A=>{var Z=ga();s(A,Z)},Ve=(A,Z)=>{{var O=J=>{var Ue=pa(),Ae=r(Ue);t(Ue),T(lt=>n(Ae,`${lt??""}% used`),[()=>Math.round(e(S).percentUsed)]),s(J,Ue)};U(A,J=>{e(S).percentUsed!==null&&e(S).percentUsed!==void 0&&J(O)},Z)}};U(Pe,A=>{e(S).placeholder?A(le):A(Ve,!1)})}t(Ke),t(ke);var Me=a(ke,2),Je=r(Me,!0);t(Me);var Qe=a(Me,2);{var ct=A=>{var Z=_a(),O=r(Z);t(Z),T(J=>{Ut(O,1,`h-full ${e(S).percentUsed>=90?"bg-destructive":e(S).percentUsed>=75?"bg-warning":"bg-primary"}`),At(O,J)},[()=>`width: ${Math.min(100,e(S).percentUsed)}%`]),s(A,Z)},Pt=(A,Z)=>{{var O=J=>{var Ue=xa();s(J,Ue)};U(A,J=>{e(S).placeholder&&J(O)},Z)}};U(Qe,A=>{!e(S).placeholder&&e(S).percentUsed!==null&&e(S).percentUsed!==void 0?A(ct):A(Pt,!1)})}var mt=a(Qe,2);{var Tt=A=>{var Z=ha(),O=r(Z);{var J=Ae=>{var lt=ee();T(Rt=>n(lt,`Period: ${Rt??""}`),[()=>new Date(e(S).period+"-01").toLocaleDateString(void 0,{year:"numeric",month:"long"})]),s(Ae,lt)},Ue=Ae=>{var lt=ee();T(()=>n(lt,`Period: ${e(S).period??""}`)),s(Ae,lt)};U(O,Ae=>{e(S).period.includes("-")?Ae(J):Ae(Ue,!1)})}t(Z),s(A,Z)};U(mt,A=>{e(S).period&&A(Tt)})}var Ct=a(mt,2),kt=r(Ct);et(kt,{variant:"outline",size:"sm",onclick:()=>{M(De,e(S).limitId,!0)},children:(A,Z)=>{L();var O=ee("View History");s(A,O)},$$slots:{default:!0}});var b=a(kt,2);{var j=A=>{const Z=_e(()=>e(S).percentUsed>=90?"destructive":"outline"),O=_e(()=>e(S).percentUsed>=75&&e(S).percentUsed<90?"text-warning border-warning/20 hover:bg-warning/10":"");et(A,{get variant(){return e(Z)},size:"sm",get class(){return e(O)},onclick:()=>Vt("/dashboard/settings/account"),children:(J,Ue)=>{L();var Ae=ee();T(()=>n(Ae,e(S).percentUsed>=90?"View Limits":"Check Usage")),s(J,Ae)},$$slots:{default:!0}})};U(b,A=>{!e(S).placeholder&&e(S).percentUsed>=75&&e(S).limit!=="unlimited"&&A(j)})}t(Ct),t(He),T(()=>{n(bt,e(S).limitName),n(re,`${e(S).used??""} / ${(e(S).limit==="unlimited"?"Unlimited":e(S).limit)??""}`),n(Je,e(S).description||"")}),s(ht,He)}),t(Oe),s(Be,Oe)};U(Ge,Be=>{!e(w).usage||e(w).usage.length===0?Be(vt):Be(ut,!1)},it)}};U(nt,Ge=>{e(w)?Ge(xt,!1):Ge(_t)})}t(ot),t(he),T(()=>n(Xe,e(w)?e(w).name:"Select a Feature")),s(we,he)};U(Re,we=>{e(ge).length===0?we(Ne):we(at,!1)},Ee)}};U(ie,Re=>{e(Le)?Re(je):Re(Se,!1)},fe)}};U(oe,ie=>{e(G)?ie(Ce):ie($e,!1)})}s(me,P)},$$slots:{default:!0}})});var ce=a(h,2);F(ce,()=>Zt,(H,X)=>{X(H,{value:"history",class:"pt-4",children:(me,te)=>{var P=ka(),oe=r(P);{var Ce=ie=>{var fe=Fa(),je=$(fe),Se=r(je),Re=r(Se),Ee=a(r(Re),2),Ne=r(Ee);t(Ee),t(Re);var at=a(Re,2),we=r(at);we.__change=[wa,D,De];var he=r(we);he.value=he.__value="";var st=a(he);$t(st,17,()=>e(D).filter(re=>re.usage&&re.usage.length>0),wt,(re,Pe)=>{var le=Ua(),Ve={},Me=r(le,!0);t(le),T(()=>{Ve!==(Ve=e(Pe).id)&&(le.value=(le.__value=e(Pe).id)??""),n(Me,e(Pe).name)}),s(re,le)}),t(we);var Ze=a(we,2);{var ot=re=>{var Pe=Pa(),le=r(Pe);le.value=le.__value="";var Ve=a(le);$t(Ve,17,()=>e(w).usage,wt,(Me,Je)=>{var Qe=Da(),ct={},Pt=r(Qe,!0);t(Qe),T(()=>{ct!==(ct=e(Je).limitId)&&(Qe.value=(Qe.__value=e(Je).limitId)??""),n(Pt,e(Je).limitName)}),s(Me,Qe)}),t(Pe),Et(Pe,()=>e(De),Me=>M(De,Me)),s(re,Pe)};U(Ze,re=>{e(w)&&e(w).usage&&e(w).usage.length>0&&re(ot)})}var We=a(Ze,2),Xe=r(We),nt=r(Xe);et(nt,{variant:"outline",size:"sm",class:"flex h-8 items-center gap-1",children:(re,Pe)=>{var le=Ta(),Ve=$(le);Jt(Ve,{class:"h-4 w-4"});var Me=a(Ve,4);cr(Me,{class:"h-4 w-4"}),s(re,le)},$$slots:{default:!0}}),t(Xe);var _t=a(Xe,2),xt=r(_t);const Ge=_e(()=>e(qe)==="30d"?"default":"ghost");et(xt,{get variant(){return e(Ge)},size:"sm",class:"h-8 rounded-none",onclick:()=>M(qe,"30d"),children:(re,Pe)=>{L();var le=ee("30d");s(re,le)},$$slots:{default:!0}});var it=a(xt,2);const vt=_e(()=>e(qe)==="90d"?"default":"ghost");et(it,{get variant(){return e(vt)},size:"sm",class:"h-8 rounded-none",onclick:()=>M(qe,"90d"),children:(re,Pe)=>{L();var le=ee("90d");s(re,le)},$$slots:{default:!0}});var ut=a(it,2);const Be=_e(()=>e(qe)==="1y"?"default":"ghost");et(ut,{get variant(){return e(Be)},size:"sm",class:"h-8 rounded-none",onclick:()=>M(qe,"1y"),children:(re,Pe)=>{L();var le=ee("1y");s(re,le)},$$slots:{default:!0}}),t(_t),t(We),t(at),t(Se),t(je);var Oe=a(je,2);Dr(Oe,{get featureId(){return e(Fe)},get limitId(){return e(De)}});var ht=a(Oe,2),S=a(r(ht),2),He=r(S),ke=r(He);Lt(ke,{size:16,class:"mt-0.5"}),L(2),t(He);var Ie=a(He,2),bt=r(Ie);Lt(bt,{size:16,class:"mt-0.5"}),L(2),t(Ie);var Ke=a(Ie,2),dt=r(Ke);Lt(dt,{size:16,class:"mt-0.5"}),L(2),t(Ke),t(S),t(ht),T(()=>{var re;return n(Ne,`Track your usage over time for ${((re=e(w))==null?void 0:re.name)||"selected feature"}`)}),Et(we,()=>e(Fe),re=>M(Fe,re)),s(ie,fe)},$e=ie=>{var fe=Ca();s(ie,fe)};U(oe,ie=>{e(Fe)&&e(De)?ie(Ce):ie($e,!1)})}t(P),s(me,P)},$$slots:{default:!0}})}),s(K,ye)},$$slots:{default:!0}})}),s(B,ae)},$$slots:{default:!0}})}),s(p,u)},$$slots:{default:!0}})}),s(tt,x),jt()}tr(["click","change"]);export{gs as component};
