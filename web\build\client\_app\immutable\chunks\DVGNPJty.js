import{c,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as i}from"./BBa424ah.js";import{l as $,s as h}from"./Btcx8l8F.js";import{I as u}from"./D4f2twK-.js";function x(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"}],["path",{d:"M10 12h4"}]];u(n,h({name:"archive"},()=>o,{get iconNode(){return a},children:(r,p)=>{var e=c(),s=d(e);i(s,t,"default",{},null),l(r,e)},$$slots:{default:!0}}))}function N(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"m9 12 2 2 4-4"}]];u(n,h({name:"circle-check"},()=>o,{get iconNode(){return a},children:(r,p)=>{var e=c(),s=d(e);i(s,t,"default",{},null),l(r,e)},$$slots:{default:!0}}))}function M(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2"}],["path",{d:"M12 3v18"}]];u(n,h({name:"columns-2"},()=>o,{get iconNode(){return a},children:(r,p)=>{var e=c(),s=d(e);i(s,t,"default",{},null),l(r,e)},$$slots:{default:!0}}))}function z(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M12 17h.01"}],["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z"}],["path",{d:"M9.1 9a3 3 0 0 1 5.82 1c0 2-3 3-3 3"}]];u(n,h({name:"file-question"},()=>o,{get iconNode(){return a},children:(r,p)=>{var e=c(),s=d(e);i(s,t,"default",{},null),l(r,e)},$$slots:{default:!0}}))}function P(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1"}]];u(n,h({name:"layout-grid"},()=>o,{get iconNode(){return a},children:(r,p)=>{var e=c(),s=d(e);i(s,t,"default",{},null),l(r,e)},$$slots:{default:!0}}))}function w(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"}]];u(n,h({name:"phone"},()=>o,{get iconNode(){return a},children:(r,p)=>{var e=c(),s=d(e);i(s,t,"default",{},null),l(r,e)},$$slots:{default:!0}}))}function C(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18"}],["path",{d:"M4 22h16"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z"}]];u(n,h({name:"trophy"},()=>o,{get iconNode(){return a},children:(r,p)=>{var e=c(),s=d(e);i(s,t,"default",{},null),l(r,e)},$$slots:{default:!0}}))}export{x as A,N as C,z as F,P as L,w as P,C as T,M as a};
