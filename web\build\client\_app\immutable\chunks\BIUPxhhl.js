import{c as l,a}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as c}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as $,s as d}from"./Btcx8l8F.js";import{I as p}from"./D4f2twK-.js";function h(t,o){const n=$(o,["children","$$slots","$$events","$$legacy"]),r=[["circle",{cx:"11",cy:"11",r:"8"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11"}]];p(t,d({name:"zoom-in"},()=>n,{get iconNode(){return r},children:(s,f)=>{var e=l(),i=c(e);m(i,o,"default",{},null),a(s,e)},$$slots:{default:!0}}))}function z(t,o){const n=$(o,["children","$$slots","$$events","$$legacy"]),r=[["circle",{cx:"11",cy:"11",r:"8"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11"}]];p(t,d({name:"zoom-out"},()=>n,{get iconNode(){return r},children:(s,f)=>{var e=l(),i=c(e);m(i,o,"default",{},null),a(s,e)},$$slots:{default:!0}}))}export{z as Z,h as a};
