import{c as n,a as m}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as c,s as l}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(r,o){const e=c(o,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2"}]];f(r,l({name:"video"},()=>e,{get iconNode(){return s},children:(a,$)=>{var t=n(),i=p(t);d(i,o,"default",{},null),m(a,t)},$$slots:{default:!0}}))}export{y as V};
