import{c as n,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(s,o){const t=c(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"}]];f(s,d({name:"bookmark"},()=>t,{get iconNode(){return a},children:(e,$)=>{var r=n(),m=i(r);l(m,o,"default",{},null),p(e,r)},$$slots:{default:!0}}))}export{y as B};
