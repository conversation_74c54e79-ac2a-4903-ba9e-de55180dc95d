import{w as i}from"./CGmarHxI.js";const l=i({isOpen:!1,section:"pro",currentPlanId:null,onSelectPlan:null});function a(n){const e=n.section||"pro",o=`/pricing?section=${e}`;typeof window<"u"&&(window.location.href=o),l.update(c=>({...c,isOpen:!1,section:e,currentPlanId:n.currentPlanId||null,onSelectPlan:n.onSelectPlan||null}))}function r(){l.update(n=>({...n,isOpen:!1}))}export{r as c,a as o,l as p};
