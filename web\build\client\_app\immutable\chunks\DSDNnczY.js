import{c as p,a as m}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(e,o){const r=c(o,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"m3 11 18-5v12L3 14v-3z"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6"}]];f(e,d({name:"megaphone"},()=>r,{get iconNode(){return s},children:(a,$)=>{var t=p(),n=i(t);l(n,o,"default",{},null),m(a,t)},$$slots:{default:!0}}))}export{y as M};
