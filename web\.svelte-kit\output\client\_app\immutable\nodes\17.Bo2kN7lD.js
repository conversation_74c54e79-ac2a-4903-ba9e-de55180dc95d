import{f as b,a as S}from"../chunks/BasJTneF.js";import{p as w,u as P,i as A,f as x,a as C,s as f,c as I,j as O,d as s,k as p,g as n,r as L}from"../chunks/CGmarHxI.js";import{S as j}from"../chunks/C6g8ubaU.js";import{g as T}from"../chunks/BiJhC7W5.js";import{S as _}from"../chunks/B0MU434M.js";import"../chunks/CgXBgsce.js";import{t as e}from"../chunks/DjPYYl4Z.js";var V=b(`<!> <div class="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]"><div class="mb-8 flex flex-col space-y-2 text-left"><h1 class="text-3xl font-semibold tracking-tight">Sign in</h1> <p class="text-muted-foreground text-md font-light">Don't have an account? <a class="underline" href="/auth/sign-up">Create one</a></p></div> <!></div>`,1);function $(h,m){w(m,!0);let c=p(!1),r=p(null);function y(){const t=new URL(window.location.href),o=t.searchParams.get("error"),i=t.searchParams.get("provider");if(console.log("Checking for errors:",{error:o,provider:i}),s(r,{type:"info",title:"Debug Toast",description:"Testing if toast is working",duration:5e3},!0),o){console.log("Found error parameter:",o);const a=i?i.charAt(0).toUpperCase()+i.slice(1):"the provider";switch(o){case"WrongProvider":console.log("Showing WrongProvider toast");let l="You previously created your account using a different sign-in method.";i&&i!=="unknown"?l+=` You cannot use ${a} to sign in to this account.`:l+=" Please use the same method you used when you created your account.",s(r,{type:"error",title:"Sign In Error",description:l,duration:15e3},!0);break;case"OAuthAccountNotLinked":e.error("Sign In Error",{description:"Email already exists with a different provider.",duration:8e3});break;case"OAuthSignin":e.error("Sign In Error",{description:"Error starting the OAuth sign-in flow. Please try again.",duration:8e3});break;case"OAuthCallback":e.error("Sign In Error",{description:"Error completing the OAuth sign-in flow. Please try again.",duration:8e3});break;case"OAuthCreateAccount":e.error("Sign In Error",{description:"Error creating a user account. Please try again.",duration:8e3});break;case"EmailCreateAccount":e.error("Sign In Error",{description:"Error creating a user account with email. Please try again.",duration:8e3});break;case"Callback":e.error("Sign In Error",{description:"Error during the OAuth callback. Please try again.",duration:8e3});break;case"EmailSignin":e.error("Sign In Error",{description:"Error sending the email sign-in link. Please try again.",duration:8e3});break;case"CredentialsSignin":e.error("Sign In Error",{description:"Invalid credentials. Please check your email and password.",duration:8e3});break;case"SessionRequired":e.error("Authentication Required",{description:"Please sign in to access this page.",duration:8e3});break;case"Configuration":console.log("Showing Configuration error toast"),s(r,{type:"error",title:"Server Configuration Error",description:"There is a problem with the server configuration. Please contact support.",duration:15e3},!0);break;case"AccessDenied":e.error("Access Denied",{description:"You do not have permission to sign in.",duration:8e3});break;case"Verification":e.error("Verification Error",{description:"The verification token is invalid or has expired. Please try signing in again.",duration:8e3});break;case"ServerError":default:e.error("Authentication Error",{description:`Error type: ${o}`,duration:8e3});break}}}P(()=>{const t=new URL(window.location.href),o=t.searchParams.get("verified"),i=t.searchParams.get("error");O().then(()=>{o==="true"&&s(r,{type:"success",title:"Email Verified",description:"Your email has been successfully verified. You can now sign in to your account.",duration:6e3},!0),i&&(console.log("Error parameter found in $effect, calling checkForErrors()"),y())})}),A(()=>{n(r)&&(n(r).type==="success"?e.success(n(r).title,{description:n(r).description,duration:n(r).duration}):n(r).type==="error"?e.error(n(r).title,{description:n(r).description,duration:n(r).duration}):n(r).type==="info"&&e.info(n(r).title,{description:n(r).description,duration:n(r).duration}),setTimeout(()=>{s(r,null)},100))});async function E(t,o){s(c,!0);const i=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:o})});if(s(c,!1),i.ok)console.log("Login successful"),e.success("Login Successful",{description:"Welcome back! You are now logged in."}),T("/dashboard");else{const a=await i.json();console.error("Login failed",a),a.error==="Email not verified"?e.error("Email Not Verified",{description:a.message||"Please verify your email address before signing in.",action:{label:"Resend",onClick:()=>k(t)}}):e.error("Login Error",{description:(a==null?void 0:a.message)||"Please check your credentials and try again."})}}async function k(t){try{const o=await fetch("/api/auth/resend-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t})});if(o.ok)e.success("Verification Email Sent",{description:"Please check your inbox for the verification email."});else{const i=await o.json();e.error("Error",{description:(i==null?void 0:i.message)||"Failed to resend verification email."})}}catch(o){console.error("Error resending verification email:",o),e.error("Error",{description:"An error occurred while resending the verification email."})}}var d=V(),u=x(d);j(u,{title:"Sign In | Hirli",description:"Sign in to your Hirli account to access your job applications, resume, and personalized job recommendations.",keywords:"sign in, login, job application, career, automation, Hirli account"});var g=f(u,2),v=f(I(g),2);_(v,{get isLoading(){return n(c)},onEmailPasswordLogin:E}),L(g),S(h,d),C()}export{$ as component};
