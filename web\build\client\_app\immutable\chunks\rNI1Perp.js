import{c as i,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function z(r,o){const t=m(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"}]];f(r,d({name:"shield"},()=>t,{get iconNode(){return a},children:(e,$)=>{var s=i(),n=p(s);c(n,o,"default",{},null),l(e,s)},$$slots:{default:!0}}))}export{z as S};
