import{c as Ie,a as r,f as s,t as P}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as Ce,l as Y,b as we,f as _,a as Ue,d as Z,m as ee,h as le,g as e,s as o,n as f,t as T,c as U,r as m,e as se}from"../chunks/CGmarHxI.js";import{s as I}from"../chunks/CIt1g2O9.js";import{i as J}from"../chunks/u21ee2wt.js";import{e as ae,i as oe}from"../chunks/C3w0v0gR.js";import{i as Re}from"../chunks/BIEMS98f.js";import{C as ve}from"../chunks/DuGukytH.js";import{C as je}from"../chunks/Cdn-N1RY.js";import{C as ce}from"../chunks/BkJY4La4.js";import{C as he}from"../chunks/DETxXRrJ.js";import{C as pe}from"../chunks/GwmmX_iF.js";import{C as _e}from"../chunks/D50jIuLr.js";import{R as ke,T as Ge}from"../chunks/I7hvcB12.js";import{B as Fe}from"../chunks/B1K98fMG.js";import{S as Ne}from"../chunks/C6g8ubaU.js";import{p as ge}from"../chunks/Btcx8l8F.js";import{P as ze}from"../chunks/DrGkVJ95.js";import{P as Le,R as Se,T as Be,a as Ve}from"../chunks/ChRM_Un0.js";import{s as Ee}from"../chunks/BBa424ah.js";import{A as Me,a as Je,b as qe}from"../chunks/DLEhONWn.js";import{c as Te}from"../chunks/BMZasLyv.js";import{o as Oe}from"../chunks/DrHxToS6.js";import{T as We}from"../chunks/BNVswwUK.js";import{U as He}from"../chunks/G5Oo-PmU.js";import{I as Ke}from"../chunks/BuYRPDDz.js";import{b as Pe,F as Qe}from"../chunks/YNp1uWxB.js";import{a as be}from"../chunks/iTBjRg9v.js";import{T as ye}from"../chunks/C88uNE8B.js";import{T as Ae}from"../chunks/DmZyh-PW.js";var Xe=s(" <!>",1),Ye=s("<!> <!> <!>",1);function De(ue,q){Ce(q,!1);const b=ee(),u=ee(),L=ee();let R=ge(q,"userData",8),S=ge(q,"featureId",8),E=ge(q,"limitId",8,void 0),O=ge(q,"showUpgradePrompt",8,!0);function ne(){var j;Oe({section:"pro",currentPlanId:R().role||((j=R().subscription)==null?void 0:j.planId)||"free"})}Y(()=>le(R()),()=>{Z(b,Te(R()))}),Y(()=>(le(E()),e(b),le(S())),()=>{Z(u,E()?e(b).canPerformAction(S(),E()):e(b).hasAccess(S()))}),Y(()=>(le(E()),e(b),le(S())),()=>{Z(L,E()?e(b).getBlockReason(S(),E()):e(b).getBlockReason(S()))}),we(),Re();var K=Ie(),de=_(K);{var ie=j=>{var k=Ie(),re=_(k);Ee(re,q,"default",{},null),r(j,k)},xe=(j,k)=>{{var re=G=>{Me(G,{children:(B,Q)=>{var F=Ye(),V=_(F);We(V,{class:"size-4"});var N=o(V,2);Je(N,{children:(y,n)=>{f();var l=P("Access Restricted");r(y,l)},$$slots:{default:!0}});var g=o(N,2);qe(g,{children:(y,n)=>{f();var l=Xe(),t=_(l),p=o(t);Fe(p,{variant:"link",class:"h-auto p-0",onclick:ne,children:(w,z)=>{f();var h=P("Upgrade your plan");r(w,h)},$$slots:{default:!0}}),T(()=>I(t,`${e(L)??""} `)),r(y,l)},$$slots:{default:!0}}),r(B,F)},$$slots:{default:!0}})};J(j,G=>{O()&&G(re)},k)}};J(de,j=>{e(u)?j(ie):j(xe,!1)})}r(ue,K),Ue()}var Ze=s('<p class="text-sm">Resets on the 1st of each month</p>'),er=s("<!> <!>",1),rr=s('<div class="flex items-center gap-2"><span class="text-muted-foreground text-sm"> </span> <!></div>'),tr=s('<div class="w-full"><!></div>'),sr=s("<!> Upload Resume",1),ar=s('<div class="text-muted-foreground text-sm">Unlimited scans available</div>'),or=s('<div class="text-muted-foreground text-sm"> </div>'),lr=s('<div class="space-y-4"><div class="flex items-center justify-between"><h2 class="text-2xl font-bold">Resume Analyzer</h2> <!></div> <p class="text-muted-foreground">Upload your resume to analyze it against job descriptions.</p> <!> <div class="flex items-center justify-between"><div><!></div> <!></div></div>');function nr(ue,q){Ce(q,!1);const b=ee(),u=ee(),L=ee(),R=ee(),S=ee(),E=ee();let O=ge(q,"userData",8);function ne(){console.log("Uploading resume...")}Y(()=>le(O()),()=>{Z(b,Te(O()))}),Y(()=>le(O()),()=>{var K;Z(u,((K=O().usage)==null?void 0:K.resume_scanner_resume_scans_per_month)||0)}),Y(()=>e(b),()=>{Z(L,e(b).getNumericLimitValue("resume_scanner","resume_scans_per_month",10))}),Y(()=>e(L),()=>{Z(R,e(L)===1/0)}),Y(()=>(e(R),e(L),e(u)),()=>{Z(S,e(R)?1/0:Math.max(0,e(L)-e(u)))}),Y(()=>(e(R),e(u),e(L)),()=>{Z(E,e(R)?0:Math.min(e(u)/e(L)*100,100))}),we(),Re(),Le(ue,{children:(K,de)=>{De(K,{get userData(){return O()},featureId:"resume_scanner",limitId:"resume_scans_per_month",children:(ie,xe)=>{var j=lr(),k=U(j),re=o(U(k),2);{var G=l=>{var t=rr(),p=U(t),w=U(p);m(p);var z=o(p,2);Se(z,{children:(h,x)=>{var H=er(),A=_(H);Be(A,{asChild:!0,children:(v,c)=>{Ke(v,{class:"text-muted-foreground h-4 w-4"})},$$slots:{default:!0}});var X=o(A,2);Ve(X,{children:(v,c)=>{var C=Ze();r(v,C)},$$slots:{default:!0}}),r(h,H)},$$slots:{default:!0}}),m(t),T(()=>I(w,`${e(u)??""} / ${e(L)??""} scans used`)),r(l,t)};J(re,l=>{e(R)||l(G)})}m(k);var B=o(k,4);{var Q=l=>{var t=tr(),p=U(t);ze(p,{get value(){return e(E)},class:"h-2"}),m(t),r(l,t)};J(B,l=>{e(R)||l(Q)})}var F=o(B,2),V=U(F),N=U(V);Fe(N,{onclick:ne,children:(l,t)=>{var p=sr(),w=_(p);He(w,{class:"mr-2 h-4 w-4"}),f(),r(l,p)},$$slots:{default:!0}}),m(V);var g=o(V,2);{var y=l=>{var t=ar();r(l,t)},n=l=>{var t=or(),p=U(t);m(t),T(()=>I(p,`${e(S)??""}
            ${e(S)===1?"scan":"scans"} remaining`)),r(l,t)};J(g,l=>{e(R)?l(y):l(n,!1)})}m(F),m(j),r(ie,j)},$$slots:{default:!0}})},$$slots:{default:!0}}),Ue()}var dr=s("<!> <!>",1),ir=s("<!> <!>",1),vr=s("<!> <!>",1),cr=s('<div class="space-y-4"><p class="text-muted-foreground">Generate personalized cover letters for your job applications.</p> <!></div>'),pr=s("<!> <!>",1),_r=s("<!> <!> <!> <!>",1),ur=s("<!> <!>",1),mr=s('<span class="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-600">Available</span>'),$r=s('<span class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">Unavailable</span>'),fr=s('<span class="text-muted-foreground text-xs"> </span> <!>',1),gr=s("<!> <!>",1),xr=s('<div class="grid gap-4 md:grid-cols-3"></div>'),hr=s("<!> <!>",1),Pr=s("<div> </div>"),br=s('<span class="text-muted-foreground text-xs"></span>'),yr=s('<span class="text-muted-foreground text-xs">No limits</span>'),Ar=s('<span class="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-600">Available</span>'),Cr=s('<span class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">Unavailable</span>'),Ur=s("<!> <!>",1),Rr=s("<!> <!>",1),Fr=s('<div class="grid gap-4 md:grid-cols-3"></div>'),Tr=s("<!> <!>",1),Ir=s("<div> </div>"),jr=s('<span class="text-muted-foreground text-xs"></span>'),wr=s('<span class="text-muted-foreground text-xs">No limits</span>'),Dr=s('<span class="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-600">Available</span>'),kr=s('<span class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">Unavailable</span>'),Gr=s("<!> <!>",1),Nr=s("<!> <!>",1),zr=s('<div class="grid gap-4 md:grid-cols-3"></div>'),Lr=s("<!> <!>",1),Sr=s("<div> </div>"),Br=s('<span class="text-muted-foreground text-xs"></span>'),Vr=s('<span class="text-muted-foreground text-xs">No limits</span>'),Er=s('<span class="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-600">Available</span>'),Mr=s('<span class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">Unavailable</span>'),Jr=s("<!> <!>",1),qr=s("<!> <!>",1),Or=s('<div class="grid gap-4 md:grid-cols-3"></div>'),Wr=s("<!> <!> <!> <!> <!>",1),Hr=s('<!> <div class="container py-10"><h1 class="mb-6 text-3xl font-bold">Features Demo</h1> <p class="text-muted-foreground mb-8">This page demonstrates the feature access control system. Current plan: <strong> </strong></p> <div class="grid gap-6 md:grid-cols-2"><!> <!></div> <h2 class="mb-6 mt-12 text-2xl font-bold">Available Features</h2> <!></div>',1);function Ct(ue,q){Ce(q,!1);const b={id:"123",role:"casual",usage:{resume_scanner_resume_scans_per_month:25,resume_builder_resume_versions:5,job_save_saved_jobs:50,application_tracker_applications_per_month:15}},u=Te(b);Pe(be.Resume);const L={id:b.role,name:b.role.charAt(0).toUpperCase()+b.role.slice(1)};Re();var R=Hr(),S=_(R);Ne(S,{title:"Features Demo"});var E=o(S,2),O=o(U(E),2),ne=o(U(O)),K=U(ne,!0);m(ne),m(O);var de=o(O,2),ie=U(de);ve(ie,{children:(k,re)=>{var G=ir(),B=_(G);pe(B,{class:"p-6",children:(F,V)=>{var N=dr(),g=_(N);_e(g,{children:(n,l)=>{f();var t=P("Resume Analyzer Demo");r(n,t)},$$slots:{default:!0}});var y=o(g,2);ce(y,{children:(n,l)=>{f();var t=P("This component is protected by the FeatureGuard component.");r(n,t)},$$slots:{default:!0}}),r(F,N)},$$slots:{default:!0}});var Q=o(B,2);je(Q,{class:"p-6 pt-0",children:(F,V)=>{nr(F,{get userData(){return b}})},$$slots:{default:!0}}),r(k,G)},$$slots:{default:!0}});var xe=o(ie,2);ve(xe,{children:(k,re)=>{var G=pr(),B=_(G);pe(B,{class:"p-6",children:(F,V)=>{var N=vr(),g=_(N);_e(g,{children:(n,l)=>{f();var t=P("Cover Letter Generator Demo");r(n,t)},$$slots:{default:!0}});var y=o(g,2);ce(y,{children:(n,l)=>{f();var t=P("This component is protected by the FeatureGuard component.");r(n,t)},$$slots:{default:!0}}),r(F,N)},$$slots:{default:!0}});var Q=o(B,2);je(Q,{class:"p-6 pt-0",children:(F,V)=>{De(F,{get userData(){return b},featureId:"cover_letter_generator",limitId:"cover_letters_per_month",children:(N,g)=>{var y=cr(),n=o(U(y),2);Fe(n,{children:(l,t)=>{f();var p=P("Generate Cover Letter");r(l,p)},$$slots:{default:!0}}),m(y),r(N,y)},$$slots:{default:!0}})},$$slots:{default:!0}}),r(k,G)},$$slots:{default:!0}}),m(de);var j=o(de,4);ke(j,{value:"all",children:(k,re)=>{var G=Wr(),B=_(G);Ge(B,{class:"w-full",children:(g,y)=>{var n=_r(),l=_(n);ye(l,{value:"all",children:(z,h)=>{f();var x=P("All Features");r(z,x)},$$slots:{default:!0}});var t=o(l,2);ye(t,{value:"resume",children:(z,h)=>{f();var x=P("Resume Features");r(z,x)},$$slots:{default:!0}});var p=o(t,2);ye(p,{value:"job_search",children:(z,h)=>{f();var x=P("Job Search Features");r(z,x)},$$slots:{default:!0}});var w=o(p,2);ye(w,{value:"applications",children:(z,h)=>{f();var x=P("Application Features");r(z,x)},$$slots:{default:!0}}),r(g,n)},$$slots:{default:!0}});var Q=o(B,2);Ae(Q,{value:"all",class:"mt-6",children:(g,y)=>{var n=xr();ae(n,5,()=>Qe,oe,(l,t)=>{const p=se(()=>u.hasAccess(e(t).id)?"border-green-200":"opacity-60");ve(l,{get class(){return e(p)},children:(w,z)=>{var h=gr(),x=_(h);pe(x,{class:"p-4",children:(A,X)=>{var v=ur(),c=_(v);_e(c,{children:($,D)=>{f();var d=P();T(()=>I(d,e(t).name)),r($,d)},$$slots:{default:!0}});var C=o(c,2);ce(C,{children:($,D)=>{f();var d=P();T(()=>I(d,e(t).description)),r($,d)},$$slots:{default:!0}}),r(A,v)},$$slots:{default:!0}});var H=o(x,2);he(H,{class:"flex items-center justify-between p-4 pt-0",children:(A,X)=>{var v=fr(),c=_(v),C=U(c,!0);m(c);var $=o(c,2);{var D=M=>{var a=mr();r(M,a)},d=M=>{var a=$r();r(M,a)};J($,M=>{u.hasAccess(e(t).id)?M(D):M(d,!1)})}T(()=>I(C,e(t).category)),r(A,v)},$$slots:{default:!0}}),r(w,h)},$$slots:{default:!0}})}),m(n),r(g,n)},$$slots:{default:!0}});var F=o(Q,2);Ae(F,{value:"resume",class:"mt-6",children:(g,y)=>{var n=Fr();ae(n,5,()=>Pe(be.Resume),oe,(l,t)=>{const p=se(()=>u.hasAccess(e(t).id)?"border-green-200":"opacity-60");ve(l,{get class(){return e(p)},children:(w,z)=>{var h=Rr(),x=_(h);pe(x,{class:"p-4",children:(A,X)=>{var v=hr(),c=_(v);_e(c,{children:($,D)=>{f();var d=P();T(()=>I(d,e(t).name)),r($,d)},$$slots:{default:!0}});var C=o(c,2);ce(C,{children:($,D)=>{f();var d=P();T(()=>I(d,e(t).description)),r($,d)},$$slots:{default:!0}}),r(A,v)},$$slots:{default:!0}});var H=o(x,2);he(H,{class:"flex items-center justify-between p-4 pt-0",children:(A,X)=>{var v=Ur(),c=_(v);{var C=a=>{var i=br();ae(i,5,()=>e(t).limits,oe,(me,te)=>{var W=Pr(),$e=U(W);m(W),T(fe=>I($e,`${e(te).name??""}: ${fe??""}`),[()=>u.getLimitValue(e(t).id,e(te).id)||"N/A"],se),r(me,W)}),m(i),r(a,i)},$=a=>{var i=yr();r(a,i)};J(c,a=>{e(t).limits&&e(t).limits.length>0&&u.hasAccess(e(t).id)?a(C):a($,!1)})}var D=o(c,2);{var d=a=>{var i=Ar();r(a,i)},M=a=>{var i=Cr();r(a,i)};J(D,a=>{u.hasAccess(e(t).id)?a(d):a(M,!1)})}r(A,v)},$$slots:{default:!0}}),r(w,h)},$$slots:{default:!0}})}),m(n),r(g,n)},$$slots:{default:!0}});var V=o(F,2);Ae(V,{value:"job_search",class:"mt-6",children:(g,y)=>{var n=zr();ae(n,5,()=>Pe(be.JobSearch),oe,(l,t)=>{const p=se(()=>u.hasAccess(e(t).id)?"border-green-200":"opacity-60");ve(l,{get class(){return e(p)},children:(w,z)=>{var h=Nr(),x=_(h);pe(x,{class:"p-4",children:(A,X)=>{var v=Tr(),c=_(v);_e(c,{children:($,D)=>{f();var d=P();T(()=>I(d,e(t).name)),r($,d)},$$slots:{default:!0}});var C=o(c,2);ce(C,{children:($,D)=>{f();var d=P();T(()=>I(d,e(t).description)),r($,d)},$$slots:{default:!0}}),r(A,v)},$$slots:{default:!0}});var H=o(x,2);he(H,{class:"flex items-center justify-between p-4 pt-0",children:(A,X)=>{var v=Gr(),c=_(v);{var C=a=>{var i=jr();ae(i,5,()=>e(t).limits,oe,(me,te)=>{var W=Ir(),$e=U(W);m(W),T(fe=>I($e,`${e(te).name??""}: ${fe??""}`),[()=>u.getLimitValue(e(t).id,e(te).id)||"N/A"],se),r(me,W)}),m(i),r(a,i)},$=a=>{var i=wr();r(a,i)};J(c,a=>{e(t).limits&&e(t).limits.length>0&&u.hasAccess(e(t).id)?a(C):a($,!1)})}var D=o(c,2);{var d=a=>{var i=Dr();r(a,i)},M=a=>{var i=kr();r(a,i)};J(D,a=>{u.hasAccess(e(t).id)?a(d):a(M,!1)})}r(A,v)},$$slots:{default:!0}}),r(w,h)},$$slots:{default:!0}})}),m(n),r(g,n)},$$slots:{default:!0}});var N=o(V,2);Ae(N,{value:"applications",class:"mt-6",children:(g,y)=>{var n=Or();ae(n,5,()=>Pe(be.Applications),oe,(l,t)=>{const p=se(()=>u.hasAccess(e(t).id)?"border-green-200":"opacity-60");ve(l,{get class(){return e(p)},children:(w,z)=>{var h=qr(),x=_(h);pe(x,{class:"p-4",children:(A,X)=>{var v=Lr(),c=_(v);_e(c,{children:($,D)=>{f();var d=P();T(()=>I(d,e(t).name)),r($,d)},$$slots:{default:!0}});var C=o(c,2);ce(C,{children:($,D)=>{f();var d=P();T(()=>I(d,e(t).description)),r($,d)},$$slots:{default:!0}}),r(A,v)},$$slots:{default:!0}});var H=o(x,2);he(H,{class:"flex items-center justify-between p-4 pt-0",children:(A,X)=>{var v=Jr(),c=_(v);{var C=a=>{var i=Br();ae(i,5,()=>e(t).limits,oe,(me,te)=>{var W=Sr(),$e=U(W);m(W),T(fe=>I($e,`${e(te).name??""}: ${fe??""}`),[()=>u.getLimitValue(e(t).id,e(te).id)||"N/A"],se),r(me,W)}),m(i),r(a,i)},$=a=>{var i=Vr();r(a,i)};J(c,a=>{e(t).limits&&e(t).limits.length>0&&u.hasAccess(e(t).id)?a(C):a($,!1)})}var D=o(c,2);{var d=a=>{var i=Er();r(a,i)},M=a=>{var i=Mr();r(a,i)};J(D,a=>{u.hasAccess(e(t).id)?a(d):a(M,!1)})}r(A,v)},$$slots:{default:!0}}),r(w,h)},$$slots:{default:!0}})}),m(n),r(g,n)},$$slots:{default:!0}}),r(k,G)},$$slots:{default:!0}}),m(E),T(()=>I(K,L.name)),r(ue,R),Ue()}export{Ct as component};
