import{c as l,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(a,o){const r=c(o,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"}]];f(a,d({name:"link"},()=>r,{get iconNode(){return s},children:(e,$)=>{var t=l(),n=p(t);m(n,o,"default",{},null),i(e,t)},$$slots:{default:!0}}))}export{y as L};
