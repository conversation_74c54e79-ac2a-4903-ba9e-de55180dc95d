import{c as n,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(t,o){const s=l(o,["children","$$slots","$$events","$$legacy"]),e=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"}]];f(t,d({name:"copy"},()=>s,{get iconNode(){return e},children:(a,$)=>{var r=n(),c=i(r);m(c,o,"default",{},null),p(a,r)},$$slots:{default:!0}}))}export{x as C};
