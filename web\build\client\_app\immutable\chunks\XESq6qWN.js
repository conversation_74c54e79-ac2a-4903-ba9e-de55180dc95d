var S=e=>{throw TypeError(e)};var U=(e,t,r)=>t.has(e)||S("Cannot "+r);var p=(e,t,r)=>(U(e,t,"read from private field"),r?r.call(e):t.get(e)),T=(e,t,r)=>t.has(e)?S("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),_=(e,t,r,i)=>(U(e,t,"write to private field"),i?i.call(e,r):t.set(e,r),r);import{c as E,a as P}from"./BasJTneF.js";import{k as A,i as B,g as u,d as o,v as b,x as k,p as D,f as x,a as j,au as q}from"./CGmarHxI.js";import{s as z}from"./ncUU1dSD.js";import{i as F}from"./u21ee2wt.js";import{b as M,w as g,e as G}from"./BfX7a-t9.js";import{o as v}from"./CmxjS0TN.js";import{a as H}from"./OOsIR5sE.js";var f,l;class J{constructor(t){T(this,f,A(void 0));T(this,l);B(()=>{o(p(this,f),p(this,l),!0),_(this,l,t())})}get current(){return u(p(this,f))}}f=new WeakMap,l=new WeakMap;function K(e,t){const r=M(e);function i(n){return t[r.current][n]??r.current}return{state:r,dispatch:n=>{r.current=i(n)}}}function L(e,t){let r=A(b({})),i=A("none");const N=e.current?"mounted":"unmounted";let n=A(null);const s=new J(()=>e.current);g([()=>t.current,()=>e.current],([a,d])=>{!a||!d||H(()=>{o(n,document.getElementById(a),!0)})});const{state:m,dispatch:c}=K(N,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});g(()=>e.current,a=>{if(u(n)||o(n,document.getElementById(t.current),!0),!u(n)||!(a!==s.current))return;const I=u(i),y=h(u(n));a?c("MOUNT"):y==="none"||u(r).display==="none"?c("UNMOUNT"):c(s&&I!==y?"ANIMATION_OUT":"UNMOUNT")});function O(a){if(u(n)||o(n,document.getElementById(t.current),!0),!u(n))return;const d=h(u(n)),I=d.includes(a.animationName)||d==="none";a.target===u(n)&&I&&c("ANIMATION_END")}function C(a){u(n)||o(n,document.getElementById(t.current),!0),u(n)&&a.target===u(n)&&o(i,h(u(n)),!0)}g(()=>m.current,()=>{if(u(n)||o(n,document.getElementById(t.current),!0),!u(n))return;const a=h(u(n));o(i,m.current==="mounted"?a:"none",!0)}),g(()=>u(n),a=>{if(a)return o(r,getComputedStyle(a),!0),G(v(a,"animationstart",C),v(a,"animationcancel",O),v(a,"animationend",O))});const w=k(()=>["mounted","unmountSuspended"].includes(m.current));return{get current(){return u(w)}}}function h(e){return e&&getComputedStyle(e).animationName||"none"}function et(e,t){D(t,!0);const r=L(M.with(()=>t.present),M.with(()=>t.id));var i=E(),N=x(i);{var n=s=>{var m=E(),c=x(m);z(c,()=>t.presence??q,()=>({present:r})),P(s,m)};F(N,s=>{(t.forceMount||t.present||r.current)&&s(n)})}P(e,i),j()}export{et as P,J as a,K as u};
