import{g as t,k as f,d as l,i as d}from"./CGmarHxI.js";function b(s,o=250){let e=f(null);function r(...n){if(t(e))t(e).timeout&&clearTimeout(t(e).timeout);else{let u,i;const a=new Promise((c,m)=>{u=c,i=m});l(e,{timeout:null,runner:null,promise:a,resolve:u,reject:i},!0)}return t(e).runner=async()=>{if(!t(e))return;const u=t(e);l(e,null);try{u.resolve(await s.apply(this,n))}catch(i){u.reject(i)}},t(e).timeout=setTimeout(t(e).runner,typeof o=="function"?o():o),t(e).promise}return r.cancel=async()=>{(!t(e)||t(e).timeout===null)&&(await new Promise(n=>setTimeout(n,0)),!t(e)||t(e).timeout===null)||(clearTimeout(t(e).timeout),t(e).reject("Cancelled"),l(e,null))},r.runScheduledNow=async()=>{var n,u;(!t(e)||!t(e).timeout)&&(await new Promise(i=>setTimeout(i,0)),!t(e)||!t(e).timeout)||(clearTimeout(t(e).timeout),t(e).timeout=null,await((u=(n=t(e)).runner)==null?void 0:u.call(n)))},Object.defineProperty(r,"pending",{enumerable:!0,get(){var n;return!!((n=t(e))!=null&&n.timeout)}}),r}function p(s,o){d(()=>{let e=0;const r=s();if(!r)return;const n=new ResizeObserver(()=>{cancelAnimationFrame(e),e=window.requestAnimationFrame(o)});return n.observe(r),()=>{window.cancelAnimationFrame(e),n.unobserve(r)}})}export{p as a,b as u};
