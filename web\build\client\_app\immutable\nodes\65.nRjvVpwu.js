import{f as v,a,t as U,c as _e,e as Br}from"../chunks/BasJTneF.js";import{p as Rr,k as M,v as Ce,i as zr,f as g,a as Fr,s as o,c as l,d,g as e,t as T,x as oe,r as s,n as P}from"../chunks/CGmarHxI.js";import{s as $}from"../chunks/CIt1g2O9.js";import{i as h}from"../chunks/u21ee2wt.js";import{e as De,i as Se}from"../chunks/C3w0v0gR.js";import{c as G}from"../chunks/BvdI7LR8.js";import{g as Or}from"../chunks/CmxjS0TN.js";import{d as Ve}from"../chunks/B-Xjo-Yt.js";import{C as Jr}from"../chunks/DuGukytH.js";import{C as qr}from"../chunks/Cdn-N1RY.js";import{C as Hr}from"../chunks/DETxXRrJ.js";import{C as Qr}from"../chunks/GwmmX_iF.js";import{B as Z}from"../chunks/B1K98fMG.js";import{B as he}from"../chunks/DaBofrVv.js";import{I as Yr}from"../chunks/DMTMHyMa.js";import{P as Gr}from"../chunks/DrGkVJ95.js";import{A as Kr,a as Vr,b as Wr,c as Xr,d as Zr,e as et,f as rt,R as tt}from"../chunks/BnikQ10_.js";import{S as at}from"../chunks/C6g8ubaU.js";import{g as Te}from"../chunks/BiJhC7W5.js";import"../chunks/CgXBgsce.js";import{t as ge}from"../chunks/DjPYYl4Z.js";import{m as ot,c as st,p as lt}from"../chunks/Dqigtbi1.js";import{S as it}from"../chunks/yW0TxTga.js";import{L as We}from"../chunks/BhzFx1Wy.js";import{P as Xe}from"../chunks/DR5zc253.js";import{C as nt}from"../chunks/-SpbofVw.js";import{S as dt}from"../chunks/CYoZicO9.js";import{S as vt}from"../chunks/DumgozFE.js";import{T as ct}from"../chunks/C33xR25f.js";import{U as ft}from"../chunks/BSHZ37s_.js";import{U as mr}from"../chunks/B_6ivTD3.js";import{B as ut}from"../chunks/CDnvByek.js";import{F as pr}from"../chunks/ChqRiddM.js";import{M as mt}from"../chunks/CwgkX8t9.js";import{C as pt}from"../chunks/BBNNmnYR.js";import{C as _t}from"../chunks/DkmCSZhC.js";var gt=v("<!> Upgrade Plan",1),ht=v("<!> Creating...",1),xt=v("<!> Create New Profile",1),bt=v("<option> </option>"),Pt=v('<select class="border-input bg-background ring-offset-background h-9 w-[180px] rounded-md border px-3 py-1 text-sm"><option>All Job Types</option><!></select>'),wt=v("<option> </option>"),yt=v('<select class="border-input bg-background ring-offset-background h-9 w-[180px] rounded-md border px-3 py-1 text-sm"><option>All Industries</option><!></select>'),$t=v('<select class="border-input bg-background ring-offset-background h-9 w-[150px] rounded-md border px-3 py-1 text-sm"><option>All Profiles</option><option>My Profiles</option><option>Team Profiles</option></select>'),kt=v('<div class="mb-6 rounded-lg border border-orange-200 bg-gradient-to-r from-orange-50 to-red-50 p-6 shadow-sm"><div class="flex items-start gap-4"><div class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100 text-orange-600"><svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg></div> <div class="flex-1"><h3 class="mb-2 text-lg font-semibold text-orange-900">Profile Limit Reached</h3> <p class="mb-4 text-orange-800"> </p> <div class="flex gap-3"><!> <!></div></div></div></div>'),jt=v('<p class="text-muted-foreground text-sm"> </p>'),Ct=v('<p class="text-muted-foreground text-sm">Personal Profile</p>'),Dt=v('<div class="flex items-center gap-3"><div class="bg-primary/20 text-primary flex h-12 w-12 items-center justify-center rounded-full"><!></div> <div><h3 class="text-lg font-semibold leading-tight"> </h3> <!></div></div> <!>',1),St=v('<p class="text-muted-foreground flex items-center gap-1 text-sm"><!> </p>'),Tt=v('<div class="flex items-center gap-3"><div class="bg-muted flex h-8 w-8 items-center justify-center rounded-full"><!></div> <div class="flex-1"><p class="font-medium"> </p> <!></div></div>'),At=v('<div class="flex items-center gap-3"><div class="bg-muted flex h-8 w-8 items-center justify-center rounded-full"><!></div> <span class="font-medium"> </span></div>'),It=v('<div class="flex items-center gap-3"><div class="bg-muted flex h-8 w-8 items-center justify-center rounded-full"><!></div> <span class="font-medium"> </span></div>'),Et=v('<div class="mb-4"><h4 class="text-muted-foreground mb-2 text-sm font-medium">Top Skills</h4> <div class="flex flex-wrap gap-1"><!> <!></div></div>'),Lt=v('<div class="mb-4"><h4 class="text-muted-foreground mb-2 text-sm font-medium">Preferences</h4> <div class="flex flex-wrap gap-1"><!> <!></div></div>'),Nt=v('<!> <div class="mt-1 flex items-center justify-between px-4 text-sm"><span class="text-muted-foreground">Profile Completion</span> <span class="font-medium"> </span></div> <div class="p-4"><div class="mb-4 space-y-3"><!> <!> <!></div> <!> <!> <div class="border-muted flex items-center justify-between border-t pt-4"><div class="text-muted-foreground flex items-center gap-1 text-xs"><!> </div> <div class="flex items-center gap-1"><!> <span class="text-sm font-medium"> </span></div></div></div>',1),Ut=v("<!> Edit",1),Mt=v("<!> Delete",1),Bt=v('<!> <div class="border-border border-r"></div> <!>',1),Rt=v("<!> <!> <!>",1),zt=v("<!> Previous",1),Ft=v("Next <!>",1),Ot=v('<div class="mt-8 flex items-center justify-center gap-2"><!> <div class="flex items-center gap-1"></div> <!></div>'),Jt=v('<div class="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3"></div> <!>',1),qt=Br('<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path></svg> Upgrade Plan',1),Ht=v("<!> Creating...",1),Qt=v("<!> Create New Profile",1),Yt=v('<div class="border-border flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center"><!> <h3 class="mb-2 text-xl font-semibold">No profiles found</h3> <p class="text-muted-foreground mb-6 max-w-md text-sm"><!></p> <!></div>'),Gt=v("<!> Deleting...",1),Kt=v("<!> <!>",1),Vt=v("<!> <!> <!>",1),Wt=v('<!> <div class="border-border flex flex-col gap-6 border-b p-6"><div class="flex items-center justify-between"><div><h2 class="text-2xl font-bold">Profiles</h2> <p class="text-muted-foreground">Create and manage your profiles for job applications, automations, and more.</p></div> <!></div> <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between"><div class="flex flex-1 gap-3"><div class="relative max-w-sm flex-1"><!> <!></div> <!> <!> <!></div> <!></div></div> <div class="p-6"><!> <!></div> <!>',1);function La(_r,f){Rr(f,!0);let ee=M(Ce([...f.data.profiles])),se=M(""),Ae=M(!1),Ue=M(""),xe=M(!1),ve=M(null),be=M(!1),Pe=M(Ce(f.data.filters.search)),we=M(Ce(f.data.filters.jobType)),ye=M(Ce(f.data.filters.industry)),$e=M(Ce(f.data.filters.owner)),ce=M(!1),fe=M(!1),ue=M(!1),ke=M(5),Me=oe(()=>e(ee).length);zr(()=>{const r=f.data.user.role==="free"?1:5;d(ke,r,!0),e(Me)>=e(ke)?d(ue,!0):d(ue,!1)});let Ze;function Ie(){if(e(ce))return;d(ce,!0);const r=new URLSearchParams;e(Pe)&&r.set("search",e(Pe)),e(we)&&r.set("jobType",e(we)),e(ye)&&r.set("industry",e(ye)),e($e)&&e($e)!=="all"&&r.set("owner",e($e)),r.set("page","1");const t=r.toString()?`?${r.toString()}`:"";Te(t,{replaceState:!0}).finally(()=>{d(ce,!1)})}function gr(){clearTimeout(Ze),Ze=setTimeout(()=>{Ie()},300)}function Be(r){if(e(ce))return;const t=new URLSearchParams(window.location.search);t.set("page",r.toString()),Te(`?${t.toString()}`,{replaceState:!0})}async function er(){if(!e(fe))try{d(fe,!0),d(se,""),d(Ae,!1),d(Ue,""),console.log("Creating new profile...");const r=await fetch("/api/profiles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:"New Profile"})});console.log("API response status:",r.status);const t=await r.json();if(console.log("API response data:",t),!r.ok){if(r.status===403&&t.limitReached){console.log("Profile limit reached:",t.error),d(ue,!0),d(Me,t.currentCount||e(Me),!0),d(ke,t.limit||e(ke),!0),d(Ae,!0),d(Ue,t.error,!0),ge.error(t.error);return}throw d(se,t.error||"Failed to create profile",!0),ge.error(e(se)),new Error(e(se))}if(t.profileId)console.log(`Profile created successfully: ${t.profileId}`),t.profile&&d(ee,[...e(ee),{...t.profile,data:null,team:null,user:f.data.user,defaultDocument:null}],!0),ge.success("Profile created successfully!"),Te(`/dashboard/settings/profile/${t.profileId}`);else throw d(se,t.error||"No profile ID returned",!0),ge.error(e(se)),new Error(e(se))}catch(r){console.error("Error creating profile:",r),d(se,r instanceof Error?r.message:"Unknown error",!0),ge.error(e(se))}finally{d(fe,!1)}}function Re(){Te("/pricing")}function hr(r){return new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function xr(r){d(ve,r,!0),d(xe,!0)}async function br(){if(e(ve))try{d(be,!0);const r=await fetch(`/api/profile/${e(ve).id}`,{method:"DELETE"});if(!r.ok){let t="Failed to delete profile",c="";try{const u=await r.json();t=u.error||t,c=u.details||""}catch{t=r.statusText||t}throw console.error("Profile deletion failed:",{status:r.status,message:t,details:c,profileId:e(ve).id}),new Error(t)}d(ee,e(ee).filter(t=>t.id!==e(ve).id),!0),ge.success("Profile deleted successfully"),d(xe,!1),d(ve,null)}catch(r){console.error("Error deleting profile:",r),ge.error(r instanceof Error?r.message:"Failed to delete profile")}finally{d(be,!1)}}var rr=Wt(),tr=g(rr);at(tr,{title:"Profiles - Hirli",description:"Create and manage your resume profiles for different job types and industries. Organize your job applications with customized profiles.",keywords:"resume profiles, job applications, career profiles, job search, resume management",url:"https://hirli.com/dashboard/settings/profile"});var ze=o(tr,2),Fe=l(ze),Pr=o(l(Fe),2);{var wr=r=>{Z(r,{onclick:Re,variant:"default",class:"gap-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600",children:(t,c)=>{var u=gt(),m=g(u);Xe(m,{class:"h-4 w-4"}),P(),a(t,u)},$$slots:{default:!0}})},yr=r=>{Z(r,{onclick:er,get disabled(){return e(fe)},class:"gap-2",children:(t,c)=>{var u=_e(),m=g(u);{var b=p=>{var k=ht(),i=g(k);We(i,{class:"h-4 w-4 animate-spin"}),P(),a(p,k)},n=p=>{var k=xt(),i=g(k);Xe(i,{class:"h-4 w-4"}),P(),a(p,k)};h(m,p=>{e(fe)?p(b):p(n,!1)})}a(t,u)},$$slots:{default:!0}})};h(Pr,r=>{e(ue)?r(wr):r(yr,!1)})}s(Fe);var ar=o(Fe,2),Oe=l(ar),Je=l(Oe),or=l(Je);it(or,{class:"text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"});var $r=o(or,2);Yr($r,{placeholder:"Search profiles...",oninput:gr,class:"pl-9",get value(){return e(Pe)},set value(r){d(Pe,r,!0)}}),s(Je);var sr=o(Je,2);{var kr=r=>{var t=Pt();t.__change=Ie;var c=l(t);c.value=c.__value="";var u=o(c);De(u,17,()=>f.data.filters.jobTypes,Se,(m,b)=>{var n=bt(),p={},k=l(n,!0);s(n),T(()=>{p!==(p=e(b))&&(n.value=(n.__value=e(b))??""),$(k,e(b))}),a(m,n)}),s(t),Ve(t,()=>e(we),m=>d(we,m)),a(r,t)};h(sr,r=>{f.data.filters.jobTypes.length>0&&r(kr)})}var lr=o(sr,2);{var jr=r=>{var t=yt();t.__change=Ie;var c=l(t);c.value=c.__value="";var u=o(c);De(u,17,()=>f.data.filters.industries,Se,(m,b)=>{var n=wt(),p={},k=l(n,!0);s(n),T(()=>{p!==(p=e(b))&&(n.value=(n.__value=e(b))??""),$(k,e(b))}),a(m,n)}),s(t),Ve(t,()=>e(ye),m=>d(ye,m)),a(r,t)};h(lr,r=>{f.data.filters.industries.length>0&&r(jr)})}var Cr=o(lr,2);{var Dr=r=>{var t=$t();t.__change=Ie;var c=l(t);c.value=c.__value="all";var u=o(c);u.value=u.__value="user";var m=o(u);m.value=m.__value="team",s(t),Ve(t,()=>e($e),b=>d($e,b)),a(r,t)};h(Cr,r=>{f.data.hasTeamAccess&&r(Dr)})}s(Oe);var Sr=o(Oe,2);he(Sr,{variant:"secondary",class:"text-sm",children:(r,t)=>{P();var c=U();T(()=>$(c,`${e(ee).length??""} profile${e(ee).length!==1?"s":""}`)),a(r,c)},$$slots:{default:!0}}),s(ar),s(ze);var qe=o(ze,2),ir=l(qe);{var Tr=r=>{var t=kt(),c=l(t),u=o(l(c),2),m=o(l(u),2),b=l(m,!0);s(m);var n=o(m,2),p=l(n);Z(p,{onclick:Re,class:"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600",children:(i,x)=>{P();var j=U("Upgrade Plan");a(i,j)},$$slots:{default:!0}});var k=o(p,2);Z(k,{variant:"outline",onclick:()=>{d(Ae,!1),d(ue,!1)},class:"border-orange-300 text-orange-700 hover:bg-orange-100",children:(i,x)=>{P();var j=U("Dismiss");a(i,j)},$$slots:{default:!0}}),s(n),s(u),s(c),s(t),T(()=>$(b,e(Ue)||`You've reached your profile limit of ${e(ke)}. Upgrade your plan to create more profiles and unlock additional features.`)),a(r,t)};h(ir,r=>{(e(Ae)||e(ue))&&r(Tr)})}var Ar=o(ir,2);{var Ir=r=>{var t=Jt(),c=g(t);De(c,21,()=>e(ee),Se,(b,n)=>{var p=_e();const k=oe(()=>{var B;return(B=e(n).data)!=null&&B.data?lt(e(n).data.data):{}}),i=oe(()=>ot(e(k))),x=oe(()=>st(e(i))),j=oe(()=>e(n).team&&e(n).userId!==f.data.user.id);var q=g(p);G(q,()=>Jr,(B,y)=>{y(B,{class:"group relative gap-0 overflow-hidden p-0 transition-all hover:shadow-md",children:(E,R)=>{var z=Rt(),C=g(z);G(C,()=>Qr,(K,H)=>{H(K,{class:"border-border flex items-start justify-between border-b !p-4",children:(O,ie)=>{var re=Dt(),F=g(re),V=l(F),te=l(V);{var pe=D=>{ft(D,{class:"h-6 w-6"})},A=D=>{mr(D,{class:"h-6 w-6"})};h(te,D=>{e(j)?D(pe):D(A,!1)})}s(V);var L=o(V,2),ne=l(L),He=l(ne,!0);s(ne);var Ee=o(ne,2);{var Qe=D=>{var ae=jt(),Ne=l(ae);s(ae),T(()=>$(Ne,`Team: ${e(n).team.name??""}`)),a(D,ae)},Ye=D=>{var ae=Ct();a(D,ae)};h(Ee,D=>{e(j)?D(Qe):D(Ye,!1)})}s(L),s(F);var Ge=o(F,2);{var Le=D=>{he(D,{variant:"secondary",class:"bg-primary/10 text-primary border-primary/20",children:(ae,Ne)=>{P();var je=U();T(()=>$(je,e(i).jobType)),a(ae,je)},$$slots:{default:!0}})};h(Ge,D=>{e(i).jobType&&D(Le)})}T(()=>$(He,e(n).name)),a(O,re)},$$slots:{default:!0}})});var S=o(C,2);G(S,()=>qr,(K,H)=>{H(K,{class:"p-0",children:(O,ie)=>{var re=Nt(),F=g(re);Gr(F,{get value(){return e(x)},class:"h-2 w-full rounded-none"});var V=o(F,2),te=o(l(V),2),pe=l(te);s(te),s(V);var A=o(V,2),L=l(A),ne=l(L);{var He=w=>{var _=Tt(),I=l(_),W=l(I);mr(W,{class:"h-4 w-4"}),s(I);var Q=o(I,2),X=l(Q),le=l(X,!0);s(X);var J=o(X,2);{var de=Y=>{var N=St(),fr=l(N);mt(fr,{class:"h-3 w-3"});var Mr=o(fr);s(N),T(()=>{var ur;return $(Mr,` ${(((ur=e(i).personalInfo)==null?void 0:ur.location)||e(i).location)??""}`)}),a(Y,N)};h(J,Y=>{var N;((N=e(i).personalInfo)!=null&&N.location||e(i).location)&&Y(de)})}s(Q),s(_),T(()=>{var Y;return $(le,((Y=e(i).personalInfo)==null?void 0:Y.fullName)||e(i).fullName)}),a(w,_)};h(ne,w=>{var _;((_=e(i).personalInfo)!=null&&_.fullName||e(i).fullName)&&w(He)})}var Ee=o(ne,2);{var Qe=w=>{var _=At(),I=l(_),W=l(I);ut(W,{class:"h-4 w-4"}),s(I);var Q=o(I,2),X=l(Q,!0);s(Q),s(_),T(()=>$(X,e(i).industry)),a(w,_)};h(Ee,w=>{e(i).industry&&w(Qe)})}var Ye=o(Ee,2);{var Ge=w=>{var _=It(),I=l(_),W=l(I);pr(W,{class:"h-4 w-4"}),s(I);var Q=o(I,2),X=l(Q,!0);s(Q),s(_),T(()=>$(X,e(n).defaultDocument.label)),a(w,_)};h(Ye,w=>{e(n).defaultDocument&&w(Ge)})}s(L);var Le=o(L,2);{var D=w=>{var _=Et(),I=o(l(_),2),W=l(I);De(W,17,()=>e(i).skillsData.list.slice(0,4),Se,(le,J)=>{he(le,{variant:"outline",class:"bg-primary/5 text-xs",children:(de,Y)=>{P();var N=U();T(()=>$(N,e(J))),a(de,N)},$$slots:{default:!0}})});var Q=o(W,2);{var X=le=>{he(le,{variant:"outline",class:"bg-muted text-xs",children:(J,de)=>{P();var Y=U();T(()=>$(Y,`+${e(i).skillsData.list.length-4}`)),a(J,Y)},$$slots:{default:!0}})};h(Q,le=>{e(i).skillsData.list.length>4&&le(X)})}s(I),s(_),a(w,_)};h(Le,w=>{var _;(_=e(i).skillsData)!=null&&_.list&&e(i).skillsData.list.length>0&&w(D)})}var ae=o(Le,2);{var Ne=w=>{var _=Lt(),I=o(l(_),2),W=l(I);{var Q=J=>{he(J,{variant:"outline",class:"text-xs",children:(de,Y)=>{P();var N=U();T(()=>$(N,e(i).jobPreferences.jobSearchStatus)),a(de,N)},$$slots:{default:!0}})};h(W,J=>{e(i).jobPreferences.jobSearchStatus&&J(Q)})}var X=o(W,2);{var le=J=>{he(J,{variant:"outline",class:"text-xs",children:(de,Y)=>{P();var N=U();T(()=>$(N,e(i).jobPreferences.remotePreference)),a(de,N)},$$slots:{default:!0}})};h(X,J=>{e(i).jobPreferences.remotePreference&&J(le)})}s(I),s(_),a(w,_)};h(ae,w=>{e(i).jobPreferences&&w(Ne)})}var je=o(ae,2),Ke=l(je),nr=l(Ke);nt(nr,{class:"h-3 w-3"});var Nr=o(nr);s(Ke);var dr=o(Ke,2),vr=l(dr);dt(vr,{class:"text-primary h-4 w-4 fill-current"});var cr=o(vr,2),Ur=l(cr);s(cr),s(dr),s(je),s(A),T(w=>{$(pe,`${e(x)??""}%`),$(Nr,` ${w??""}`),$(Ur,`${e(x)??""}%`)},[()=>hr(e(n).updatedAt)]),a(O,re)},$$slots:{default:!0}})});var me=o(S,2);G(me,()=>Hr,(K,H)=>{H(K,{class:"bg-muted/50 flex gap-0 border-t !p-0",children:(O,ie)=>{var re=Bt(),F=g(re);Z(F,{variant:"ghost",class:"flex-1 rounded-none",onclick:()=>Te(`/dashboard/settings/profile/${e(n).id}`),children:(te,pe)=>{var A=Ut(),L=g(A);vt(L,{class:"mr-2 h-4 w-4"}),P(),a(te,A)},$$slots:{default:!0}});var V=o(F,4);Z(V,{variant:"ghost",class:"text-destructive hover:text-destructive hover:bg-destructive/10 flex-1 rounded-none",onclick:()=>xr(e(n)),children:(te,pe)=>{var A=Mt(),L=g(A);ct(L,{class:"mr-2 h-4 w-4"}),P(),a(te,A)},$$slots:{default:!0}}),a(O,re)},$$slots:{default:!0}})}),a(E,z)},$$slots:{default:!0}})}),a(b,p)}),s(c);var u=o(c,2);{var m=b=>{var n=Ot(),p=l(n);const k=oe(()=>!f.data.pagination.hasPrevPage||e(ce));Z(p,{variant:"outline",size:"sm",get disabled(){return e(k)},onclick:()=>Be(f.data.pagination.page-1),class:"gap-1",children:(q,B)=>{var y=zt(),E=g(y);pt(E,{class:"h-4 w-4"}),P(),a(q,y)},$$slots:{default:!0}});var i=o(p,2);De(i,21,()=>Array(Math.min(f.data.pagination.totalPages,7)),Se,(q,B,y)=>{var E=_e();const R=oe(()=>f.data.pagination.totalPages<=7||f.data.pagination.page<=4?y+1:f.data.pagination.page>=f.data.pagination.totalPages-3?f.data.pagination.totalPages-6+y:f.data.pagination.page-3+y);var z=g(E);{var C=S=>{const me=oe(()=>f.data.pagination.page===e(R)?"default":"outline");Z(S,{get variant(){return e(me)},size:"sm",onclick:()=>Be(e(R)),get disabled(){return e(ce)},class:"h-8 w-8 p-0",children:(K,H)=>{P();var O=U();T(()=>$(O,e(R))),a(K,O)},$$slots:{default:!0}})};h(z,S=>{e(R)>=1&&e(R)<=f.data.pagination.totalPages&&S(C)})}a(q,E)}),s(i);var x=o(i,2);const j=oe(()=>!f.data.pagination.hasNextPage||e(ce));Z(x,{variant:"outline",size:"sm",get disabled(){return e(j)},onclick:()=>Be(f.data.pagination.page+1),class:"gap-1",children:(q,B)=>{P();var y=Ft(),E=o(g(y));_t(E,{class:"h-4 w-4"}),a(q,y)},$$slots:{default:!0}}),s(n),a(b,n)};h(u,b=>{f.data.pagination.totalPages>1&&b(m)})}a(r,t)},Er=r=>{var t=Yt(),c=l(t);pr(c,{class:"text-muted-foreground mb-4 h-16 w-16"});var u=o(c,4),m=l(u);{var b=x=>{var j=U("No profiles match your current filters. Try adjusting your search criteria.");a(x,j)},n=x=>{var j=U(`Create your first resume profile to start applying for jobs and managing your
          applications.`);a(x,j)};h(m,x=>{e(Pe)||e(we)||e(ye)?x(b):x(n,!1)})}s(u);var p=o(u,2);{var k=x=>{Z(x,{onclick:Re,variant:"default",class:"gap-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600",children:(j,q)=>{var B=qt();P(),a(j,B)},$$slots:{default:!0}})},i=x=>{Z(x,{onclick:er,get disabled(){return e(fe)},class:"gap-2",children:(j,q)=>{var B=_e(),y=g(B);{var E=z=>{var C=Ht(),S=g(C);We(S,{class:"h-4 w-4 animate-spin"}),P(),a(z,C)},R=z=>{var C=Qt(),S=g(C);Xe(S,{class:"h-4 w-4"}),P(),a(z,C)};h(y,z=>{e(fe)?z(E):z(R,!1)})}a(j,B)},$$slots:{default:!0}})};h(p,x=>{e(ue)?x(k):x(i,!1)})}s(t),a(r,t)};h(Ar,r=>{e(ee)&&e(ee).length>0?r(Ir):r(Er,!1)})}s(qe);var Lr=o(qe,2);G(Lr,()=>tt,(r,t)=>{t(r,{get open(){return e(xe)},set open(c){d(xe,c,!0)},children:(c,u)=>{var m=_e(),b=g(m);G(b,()=>Kr,(n,p)=>{p(n,{class:"gap-0 p-0 sm:max-w-[425px]",children:(k,i)=>{var x=Vt(),j=g(x);G(j,()=>Vr,(y,E)=>{E(y,{children:(R,z)=>{var C=_e(),S=g(C);G(S,()=>Wr,(me,K)=>{K(me,{class:"border-border border-b p-2",children:(H,O)=>{P();var ie=U("Delete Profile");a(H,ie)},$$slots:{default:!0}})}),a(R,C)},$$slots:{default:!0}})});var q=o(j,2);G(q,()=>Xr,(y,E)=>{E(y,{class:"p-2",children:(R,z)=>{P();var C=U();T(()=>{var S;return $(C,`Are you sure you want to delete the profile "${((S=e(ve))==null?void 0:S.name)??""}"? This action cannot be
      undone.`)}),a(R,C)},$$slots:{default:!0}})});var B=o(q,2);G(B,()=>Zr,(y,E)=>{E(y,{class:"border-border border-t p-2",children:(R,z)=>{var C=Kt(),S=g(C);G(S,()=>et,(H,O)=>{O(H,{onclick:()=>d(xe,!1),children:(ie,re)=>{P();var F=U("Cancel");a(ie,F)},$$slots:{default:!0}})});var me=o(S,2);const K=oe(()=>e(be)?"cursor-not-allowed opacity-70":"bg-destructive text-destructive-foreground hover:bg-destructive/90");G(me,()=>rt,(H,O)=>{O(H,{onclick:br,get disabled(){return e(be)},get class(){return e(K)},children:(ie,re)=>{var F=_e(),V=g(F);{var te=A=>{var L=Gt(),ne=g(L);We(ne,{class:"mr-2 h-4 w-4 animate-spin"}),P(),a(A,L)},pe=A=>{var L=U("Delete");a(A,L)};h(V,A=>{e(be)?A(te):A(pe,!1)})}a(ie,F)},$$slots:{default:!0}})}),a(R,C)},$$slots:{default:!0}})}),a(k,x)},$$slots:{default:!0}})}),a(c,m)},$$slots:{default:!0}})}),a(_r,rr),Fr()}Or(["change"]);export{La as component};
