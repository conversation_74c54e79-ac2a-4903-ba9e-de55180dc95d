import{c as n,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function A(r,t){const s=m(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"}]];f(r,d({name:"activity"},()=>s,{get iconNode(){return a},children:(e,$)=>{var o=n(),i=p(o);c(i,t,"default",{},null),l(e,o)},$$slots:{default:!0}}))}export{A};
