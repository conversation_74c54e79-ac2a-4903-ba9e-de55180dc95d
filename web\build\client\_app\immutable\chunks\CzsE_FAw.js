import{o as h,$ as d,P as o,aF as k}from"./CGmarHxI.js";import{l as n}from"./CmxjS0TN.js";function i(e,a,l=a){var f=k();n(e,"input",r=>{var c=r?e.defaultValue:e.value;if(c=v(e)?s(c):c,l(c),f&&c!==(c=a())){var u=e.selectionStart,_=e.selectionEnd;e.value=c??"",_!==null&&(e.selectionStart=u,e.selectionEnd=Math.min(_,e.value.length))}}),(o&&e.defaultValue!==e.value||h(a)==null&&e.value)&&l(v(e)?s(e.value):e.value),d(()=>{var r=a();v(e)&&r===s(e.value)||e.type==="date"&&!r&&!e.value||r!==e.value&&(e.value=r??"")})}function y(e,a,l=a){n(e,"change",f=>{var r=f?e.defaultChecked:e.checked;l(r)}),(o&&e.defaultChecked!==e.checked||h(a)==null)&&l(e.checked),d(()=>{var f=a();e.checked=!!f})}function v(e){var a=e.type;return a==="number"||a==="range"}function s(e){return e===""?null:+e}function C(e,a,l=a){n(e,"change",()=>{l(e.files)}),o&&e.files&&l(e.files),d(()=>{e.files=a()})}export{C as a,i as b,y as c};
