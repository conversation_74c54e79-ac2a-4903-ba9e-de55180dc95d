import"./BasJTneF.js";import"./CgXBgsce.js";import{t as f}from"./DjPYYl4Z.js";import{w as y}from"./26EXiO5K.js";const b=new Map,$=new Map,k=3e3,g=new Set;function v(d,e,r){b.set(d,{onCompleted:e,onFailed:r}),console.log(`Registered parsing listener for resume ${d}`)}function E(d){b.delete(d),console.log(`Unregistered parsing listener for resume ${d}`)}function I(d,e,r){return`${d}:${e}:${r||"no-job-id"}`}function T(d,e,r){const i=Date.now(),a=I(d,e,r);if(!$.has(a))return!1;const s=$.get(a);return i-s<k}function _(d,e,r){const i=Date.now(),a=I(d,e,r);$.set(a,i),setTimeout(()=>{$.delete(a)},k*2)}function P(d){const{resumeId:e,status:r,parsedData:i,error:a,profileId:s,jobId:l,messageId:c}=d;if(!e){console.warn("Received parsing status update without resumeId:",d);return}if((r==="pending"||r==="processing")&&typeof window<"u"){const t=new CustomEvent("resume-parsing-status",{detail:{resumeId:e,profileId:s,status:"parsing",isParsing:!0,message:"Resume parsing in progress",timestamp:new Date().toISOString()}});window.dispatchEvent(t),console.log("Dispatched resume-parsing-status event with isParsing=true");try{fetch(`/api/resume/${e}/parsing-status`).then(n=>n.json()).then(n=>{n.isParsing||(console.log(`Resume ${e} is not marked as parsing in the database, creating worker process`),fetch("/api/worker-process",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"resume-parsing",status:"PENDING",data:{resumeId:e,profileId:s,userId:null,timestamp:new Date().toISOString()}})}).catch(p=>{console.error("Error creating worker process:",p)}))}).catch(n=>{console.error("Error checking parsing status:",n)})}catch(n){console.error("Error ensuring worker process exists:",n)}}if(s)try{fetch(`/api/resume/${e}`).then(t=>t.json()).then(t=>{if(t&&t.document&&t.document.id){const n=t.document.id;t.document.profileId!==s?(console.log(`Associating document ${n} with profile ${s}`),fetch(`/api/document/${n}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({profileId:s})}).then(p=>{p.ok?console.log(`Document ${n} associated with profile ${s}`):console.warn(`Failed to associate document ${n} with profile ${s}`)}).catch(p=>{console.error("Error associating document with profile:",p)})):console.log(`Document ${n} is already associated with profile ${s}`)}}).catch(t=>{console.error("Error fetching resume data:",t)})}catch(t){console.error("Error ensuring document-profile association:",t)}const o=c??`${e}:${r}:${l??""}:${Date.now()}`;if(r!=="completed"){if(T(e,r,l)){console.log(`Skipping duplicate notification for resume ${e} with status ${r}`);return}if(g.has(o)){console.log(`Skipping already processed message ID: ${o}`);return}}else if(console.log(`Processing completion notification for resume ${e} even if duplicate`),typeof window<"u"){const t=new CustomEvent("resume-parsing-completed",{detail:{resumeId:e,profileId:s,message:"Resume parsing completed successfully",timestamp:new Date().toISOString()}});window.dispatchEvent(t),console.log("Dispatched resume-parsing-completed event from resume-parsing-handler")}_(e,r,l),g.add(o),setTimeout(()=>{g.delete(o)},k*2),console.log(`Processing parsing status update for resume ${e}:`,r),console.log("Parsed data:",i),console.log("Profile ID:",s),console.log("Job ID:",l),console.log("Message ID:",o);const u=b.get(e);if(!u){console.log(`No listener registered for resume ${e}`),r==="completed"?typeof window<"u"?fetch(`/api/resume/${e}/status`).then(t=>t.json()).then(t=>{if(console.log(`Fetched latest resume status for ${e}:`,t),!t.isParsed){console.warn("Resume notification indicates parsing complete, but database shows isParsed=false"),f.error("Resume parsing failed: Database not updated properly");return}f.success("Resume parsing completed"),s&&(console.log(`Updating ProfileData record for profile ${s} with parsed resume data from resume ${e}`),fetch(`/api/profile/${s}/data`).then(n=>{if(!n.ok)throw new Error("Failed to update profile data");return n.json()}).then(n=>{console.log("Profile data updated successfully:",n),f.success("Profile updated with resume data"),typeof window<"u"&&setTimeout(()=>{window.location.reload()},1500)}).catch(n=>{console.error("Error updating profile data:",n),f.error("Failed to update profile with resume data")}))}).catch(t=>{console.error("Error fetching resume status:",t),f.error("Error verifying resume parsing status")}):f.success("Resume parsing completed"):r==="failed"&&f.error(`Resume parsing failed: ${a??"Unknown error"}`);return}if(typeof window<"u"&&(window.parsingListeners=b),r==="completed")if(typeof window<"u"){const t=async(n=0,p=3,w=1e3)=>{try{console.log(`Checking database status for resume ${e} (attempt ${n+1}/${p+1})`);const m=await(await fetch(`/api/resume/${e}/status`)).json();if(console.log(`Fetched latest resume status for ${e}:`,m),m.isParsed){console.log(`Resume ${e} is marked as parsed in the database`),u.onCompleted(e,i??m.parsedData);return}if(!m.isParsed&&i){console.warn("Resume notification indicates parsing complete, but database shows isParsed=false. Updating database and using the parsed data from the notification.");try{await fetch(`/api/resume/${e}/update-status`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({isParsed:!0,parsedData:i})}),console.log("Updated resume status in database to match notification"),u.onCompleted(e,i)}catch(h){console.error("Failed to update resume status in database:",h),u.onCompleted(e,i)}return}if(!m.isParsed&&!i&&n<p){console.warn(`Resume notification indicates parsing complete, but database shows isParsed=false. Attempting to force update the database (attempt ${n+1}/${p})`);try{if((await fetch(`/api/resume/${e}/update-status`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({isParsed:!0,forceUpdate:!0})})).ok){console.log(`Successfully forced update of resume ${e} status to isParsed=true`),await new Promise(S=>setTimeout(S,500)),await t(n+1,p,w*1.5);return}else console.warn(`Failed to force update resume ${e} status`)}catch(h){console.error("Error forcing update of resume status:",h)}await new Promise(h=>setTimeout(h,w)),await t(n+1,p,w*1.5);return}if(!m.isParsed&&!i){console.warn("Resume notification indicates parsing complete, but database shows isParsed=false and no parsed data was provided after multiple retries"),u.onFailed(e,"Database not updated properly");return}u.onCompleted(e,i??m.parsedData)}catch(D){if(console.error("Error fetching resume status:",D),n<p){console.warn(`Error checking resume status, retrying in ${w}ms`),await new Promise(m=>setTimeout(m,w)),await t(n+1,p,w*1.5);return}u.onFailed(e,"Error verifying resume parsing status")}};(async()=>await t())()}else u.onCompleted(e,i);else r==="failed"&&u.onFailed(e,a??"Unknown error")}function F(){console.log("Initializing resume parsing WebSocket handler with improved deduplication..."),$.clear(),g.clear(),console.log("Deduplication caches cleared");let d=0;const e=y.messages.subscribe(i=>{var l,c;if(i.length===0)return;const a=i.filter(o=>(o.timestamp?new Date(o.timestamp).getTime():Date.now())>d);if(a.length===0)return;const s=a[0];s.timestamp&&(d=new Date(s.timestamp).getTime());for(const o of a){const u=`ws:${o.type}:${o.timestamp??Date.now()}`;if(!g.has(u)){if(g.add(u),o.type==="resume_parsing_status"||o.type==="resume_parsing_completed"){console.log(`Received resume parsing message (${o.type}):`,o);let t;o.data?t={...o.data,messageId:u,status:o.type==="resume_parsing_completed"?"completed":o.data.status}:t={...o,messageId:u,status:o.type==="resume_parsing_completed"?"completed":o.status},o.type==="resume_parsing_completed"&&t.status!=="completed"&&(t.status="completed"),console.log("Processed message data:",t),P(t)}else if(o.type==="notification"&&(console.log("Checking notification message for resume parsing data:",o),(l=o.data)!=null&&l.resumeId&&((c=o.data)!=null&&c.status))){console.log("Found resume parsing data in notification message:",o.data);const t={...o.data,messageId:u};P(t)}}}}),r=i=>{const{type:a,data:s}=i.detail,l=`event:${a}:${(s==null?void 0:s.timestamp)??Date.now()}`;if(g.has(l)){console.log(`Skipping already processed event message: ${l}`);return}if(g.add(l),a==="resume_parsing_status"||a==="resume_parsing_completed"){console.log(`Received resume parsing event (${a}):`,s);let c;if(s.data?c={...s.data,messageId:l,status:a==="resume_parsing_completed"?"completed":s.data.status}:c={...s,messageId:l,status:a==="resume_parsing_completed"?"completed":s.status},a==="resume_parsing_completed"&&c.status!=="completed"&&(c.status="completed"),a==="resume_parsing_status"&&(c.status==="pending"||c.status==="processing")&&typeof window<"u"){const o=new CustomEvent("resume-parsing-status",{detail:{resumeId:c.resumeId,profileId:c.profileId,userId:c.userId,isParsing:!0,status:c.status,message:"Resume parsing in progress",timestamp:new Date().toISOString()}});window.dispatchEvent(o),console.log("Dispatched resume-parsing-status event from event handler with isParsing=true")}console.log("Processed message data:",c),P(c)}else if(a==="notification"&&(console.log("Checking notification event for resume parsing data:",s),s!=null&&s.resumeId&&(s!=null&&s.status))){console.log("Found resume parsing data in notification event:",s);const c={...s.data??s,messageId:l};P(c)}};return typeof window<"u"&&window.addEventListener("websocket-message",r),console.log("Resume parsing handler using existing WebSocket connection"),typeof window<"u"&&(window.debugResumeParser={listeners:b,handleMessage:i=>P(i),registerListener:v,unregisterListener:E,testConnection:()=>{const i=y.getStatus();if(console.log(`Current WebSocket status: ${i}`),i!=="connected")return console.log("WebSocket not connected"),!1;const a=y.send({type:"ping",data:{time:Date.now()}});return console.log(`Test ping sent: ${a}`),a},forceReconnect:()=>{console.log("Forcing WebSocket reconnection..."),y.disconnect(),setTimeout(()=>{y.initialize()},500)}}),console.log("Resume parsing WebSocket handler initialized"),()=>{e(),typeof window<"u"&&window.removeEventListener("websocket-message",r),$.clear(),g.clear(),console.log("Resume parsing WebSocket handler cleaned up and caches cleared")}}export{F as i,v as r,E as u};
