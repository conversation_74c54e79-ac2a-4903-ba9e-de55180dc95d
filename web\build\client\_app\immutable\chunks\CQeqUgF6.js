import{c as l,a as o,f as D}from"./BasJTneF.js";import{p as P,f as n,a as k,g as p,x as y,c as B,au as I,r as S}from"./CGmarHxI.js";import{s as c}from"./ncUU1dSD.js";import{i as T}from"./u21ee2wt.js";import{e as j}from"./B-Xjo-Yt.js";import{p as s,r as q}from"./Btcx8l8F.js";import{b as i,m as z}from"./BfX7a-t9.js";import{a as A}from"./DMoa_yM9.js";import{u as C}from"./CnMg5bH0.js";var E=D("<button><!></button>");function Q(g,r){P(r,!0);let u=s(r,"id",19,C),m=s(r,"ref",15,null),h=s(r,"disabled",3,!1),b=q(r,["$$slots","$$events","$$legacy","id","ref","children","child","disabled"]);const v=A({id:i.with(()=>u()),ref:i.with(()=>m(),a=>m(a)),disabled:i.with(()=>!!h())}),d=y(()=>z(b,v.props));var f=l(),_=n(f);{var w=a=>{var e=l(),t=n(e);c(t,()=>r.child,()=>({props:p(d)})),o(a,e)},x=a=>{var e=E();j(e,()=>({...p(d)}));var t=B(e);c(t,()=>r.children??I),S(e),o(a,e)};T(_,a=>{r.child?a(w):a(x,!1)})}o(g,f),k()}export{Q as D};
