import{c as i,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as m}from"./CGmarHxI.js";import{s as p}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(s,o){const t=c(o,["children","$$slots","$$events","$$legacy"]),e=[["polyline",{points:"4 17 10 11 4 5"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19"}]];f(s,d({name:"terminal"},()=>t,{get iconNode(){return e},children:(n,$)=>{var r=i(),a=m(r);p(a,o,"default",{},null),l(n,r)},$$slots:{default:!0}}))}export{x as T};
