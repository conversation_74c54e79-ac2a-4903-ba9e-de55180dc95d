import{f as h,a as t,t as y,c as H}from"../chunks/BasJTneF.js";import{o as qt}from"../chunks/nZgk9enP.js";import{p as Ht,k as I,v as Xe,f as m,a as zt,s as o,c as T,d as c,n as $,g as e,x as pe,r as k,t as W}from"../chunks/CGmarHxI.js";import{s as ee}from"../chunks/CIt1g2O9.js";import{i as J}from"../chunks/u21ee2wt.js";import{e as at,i as ot}from"../chunks/C3w0v0gR.js";import{c as r}from"../chunks/BvdI7LR8.js";import{r as gt,f as xt,a as Jt,s as Mt}from"../chunks/B-Xjo-Yt.js";import{e as Vt}from"../chunks/CmxjS0TN.js";import{C as lt}from"../chunks/DuGukytH.js";import{C as st}from"../chunks/Cdn-N1RY.js";import{C as nt}from"../chunks/BkJY4La4.js";import{C as it}from"../chunks/GwmmX_iF.js";import{C as dt}from"../chunks/D50jIuLr.js";import{B as Ze}from"../chunks/B1K98fMG.js";import{R as Pt,S as wt,a as yt,b as Ct}from"../chunks/CGK0g3x_.js";import{I as Te}from"../chunks/DMTMHyMa.js";import{T as Gt}from"../chunks/VNuMAkuB.js";import{T as Qt,a as Wt,b as Tt,c as _e,d as Xt,e as $e}from"../chunks/LESefvxV.js";import"../chunks/CgXBgsce.js";import{t as D}from"../chunks/DjPYYl4Z.js";import{S as kt}from"../chunks/B2lQHLf_.js";import{T as Zt}from"../chunks/CTO_B1Jk.js";import{C as ct}from"../chunks/-SpbofVw.js";import{C as ut}from"../chunks/DZCYCPd3.js";import{C as vt}from"../chunks/DW7T7T22.js";import{C as mt}from"../chunks/BAIxhb6t.js";import{C as At}from"../chunks/CKg8MWp_.js";var er=h(`<div class="mb-4 rounded-md border border-amber-200 bg-amber-50 p-4 text-amber-800"><div class="flex items-center"><!> <h3 class="text-sm font-medium">Resend API Key Not Configured</h3></div> <div class="mt-2 text-sm"><p>The Resend API key is not configured. You need to set the RESEND_API_KEY environment
        variable to use audience and broadcast features.</p></div></div>`),tr=h("<!> <!>",1),rr=h("<!> <!>",1),ar=h("<!> <!>",1),or=h('<p class="text-muted-foreground mt-1 text-xs">No audiences found. Create an audience in Resend first.</p>'),lr=h('<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>'),sr=h("<!> Preview",1),nr=h('<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>'),ir=h("<!> ",1),dr=h('<form><div class="space-y-4"><div><label for="templateName" class="mb-1 block text-sm font-medium">Template</label> <!> <input type="hidden" name="templateName"/></div> <div><label for="audienceId" class="mb-1 block text-sm font-medium">Audience</label> <!> <input type="hidden" name="audienceId"/> <!></div> <div><label for="subject" class="mb-1 block text-sm font-medium">Subject</label> <!></div> <div><label for="title" class="mb-1 block text-sm font-medium">Title</label> <!></div> <div><label for="content" class="mb-1 block text-sm font-medium">Content</label> <!></div> <div><label for="imageUrl" class="mb-1 block text-sm font-medium">Image URL (Optional)</label> <!></div> <div class="grid grid-cols-1 gap-4 md:grid-cols-2"><div><label for="ctaText" class="mb-1 block text-sm font-medium">CTA Text (Optional)</label> <!></div> <div><label for="ctaUrl" class="mb-1 block text-sm font-medium">CTA URL (Optional)</label> <!></div></div> <div><label for="scheduledAt" class="mb-1 block text-sm font-medium">Schedule (Optional)</label> <!> <p class="text-muted-foreground mt-1 text-xs">Leave empty to send immediately</p></div> <div class="flex justify-between pt-4"><!> <!></div></div></form>'),cr=h("<!> <!>",1),ur=h("<!> <!>",1),vr=h('<div class="overflow-hidden rounded-md border"><iframe title="Email Preview" class="h-[600px] w-full border-0" sandbox="allow-same-origin"></iframe></div>'),mr=h('<div class="flex h-[600px] items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>'),fr=h('<div class="text-muted-foreground flex h-[600px] flex-col items-center justify-center"><p>Select a template and click Preview to see how your email will look</p> <!></div>'),pr=h("<!> <!>",1),_r=h("<!> <!>",1),$r=h('<div class="flex h-40 items-center justify-center"><div class="h-6 w-6 animate-spin rounded-full border-2 border-current border-t-transparent"></div></div>'),hr=h("<!> <!> <!> <!> <!> <!> <!>",1),br=h('<div class="flex items-center"><span><!> </span></div>'),gr=h("<!> <!> <!> <!> <!> <!> <!>",1),xr=h("<!> <!>",1),Pr=h('<div class="text-muted-foreground flex h-40 items-center justify-center"><p>No broadcasts found</p></div>'),wr=h("<!> <!>",1),yr=h('<!> <div class="grid grid-cols-1 gap-6 md:grid-cols-2"><!> <!></div> <!>',1);function Zr(St,jt){Ht(jt,!0);let ze=I(Xe([])),ke=I(Xe([])),Ae=I(Xe([])),O=I(null),X=I(null),le=I(""),se=I(""),ne=I(""),Se=I(""),je=I(""),Ue=I(""),Ee=I(""),Je=I(!1),Me=I(""),Ie=I(!1),et=I(!0),he=Xe({isAvailable:!1,hasApiKey:!1,error:null});const Ve="/api/email";qt(async()=>{await ft()});async function ft(){c(et,!0);try{c(ze,[{name:"newsletter",label:"Newsletter"},{name:"announcement",label:"Announcement"},{name:"product-update",label:"Product Update"},{name:"feature-announcement",label:"Feature Announcement"},{name:"promotional",label:"Promotional"}],!0);try{const a=await fetch(`${Ve}/audiences`);if(a.ok){const u=await a.json();c(ke,u,!0),he.isAvailable=!0,he.hasApiKey=!0}else{const u=await a.json();u.error&&u.error.includes("API key")?(he.hasApiKey=!1,console.warn("Resend API key not configured. Please set the RESEND_API_KEY environment variable.")):console.error("Error loading audiences:",u)}}catch(a){console.error("Error loading audiences:",a),he.error=a.message,he.isAvailable=!1}try{const a=await fetch(`${Ve}/broadcasts`);if(a.ok){const u=await a.json();c(Ae,u,!0)}else{const u=await a.json();u.error&&u.error.includes("API key")?console.warn("Resend API key not configured for broadcasts"):console.error("Error loading broadcasts:",u)}}catch(a){console.error("Error loading broadcasts:",a)}}catch(a){console.error("Error loading data:",a),D.error("Failed to load data")}finally{c(et,!1)}}function pt(a){return a?new Date(a).toLocaleString():"-"}function Ut(a){switch(a){case"draft":return"bg-gray-100 text-gray-800";case"scheduled":return"bg-blue-100 text-blue-800";case"sent":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}function Ge(a){switch(a){case"draft":return ct;case"scheduled":return ut;case"sent":return vt;case"cancelled":return mt;default:return At}}async function Et(){if(!e(O)){D.error("Please select a template");return}if(!e(X)){D.error("Please select an audience");return}if(!e(le)){D.error("Subject is required");return}if(!e(se)){D.error("Title is required");return}if(!e(ne)){D.error("Content is required");return}c(Je,!0);try{const a=await fetch(`${Ve}/broadcasts`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({templateName:e(O).name,audienceId:e(X).id,subject:e(le),title:e(se),content:e(ne),imageUrl:e(Se),ctaText:e(je),ctaUrl:e(Ue),scheduledAt:e(Ee)})});if(a.ok)D.success("Broadcast created successfully"),c(O,null),c(X,null),c(le,""),c(se,""),c(ne,""),c(Se,""),c(je,""),c(Ue,""),c(Ee,""),c(Me,""),await ft();else{const u=await a.json();D.error(u.error||"Failed to create broadcast")}}catch(a){console.error("Error creating broadcast:",a),D.error("Failed to create broadcast")}finally{c(Je,!1)}}async function It(a){if(confirm("Are you sure you want to cancel this broadcast?"))try{const u=await fetch(`${Ve}/broadcasts/cancel`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({broadcastId:a})});if(u.ok)D.success("Broadcast cancelled successfully"),c(Ae,e(Ae).map(F=>F.id===a?{...F,status:"cancelled"}:F),!0);else{const F=await u.json();D.error(F.error||"Failed to cancel broadcast")}}catch(u){console.error("Error cancelling broadcast:",u),D.error("Failed to cancel broadcast")}}async function _t(){if(!e(O)){D.error("Please select a template");return}c(Ie,!0);try{const a={title:e(se)||"Newsletter Title",content:e(ne)||"<p>Newsletter content goes here.</p>",imageUrl:e(Se)||"",ctaText:e(je)||"",ctaUrl:e(Ue)||"",firstName:"Preview",unsubscribeUrl:"#",currentYear:new Date().getFullYear()},u=`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${e(le)||"Email Preview"}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; padding: 20px 0; }
            .content { padding: 20px 0; }
            .footer { text-align: center; padding: 20px 0; font-size: 12px; color: #666; border-top: 1px solid #eee; }
            .button { display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; }
            img { max-width: 100%; height: auto; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${a.title}</h1>
          </div>
          <div class="content">
            ${a.content}
            ${a.imageUrl?`<img src="${a.imageUrl}" alt="Newsletter Image" />`:""}
            ${a.ctaText&&a.ctaUrl?`<p style="text-align: center; margin-top: 20px;"><a href="${a.ctaUrl}" class="button">${a.ctaText}</a></p>`:""}
          </div>
          <div class="footer">
            <p>This is a preview of your ${e(O).label} email.</p>
            <p>© ${a.currentYear} Your Company. All rights reserved.</p>
            <p><a href="${a.unsubscribeUrl}">Unsubscribe</a></p>
          </div>
        </body>
        </html>
      `;c(Me,u),D.success("Preview generated")}catch(a){console.error("Error generating preview:",a),D.error("Failed to generate preview")}finally{c(Ie,!1)}}var $t=yr(),ht=m($t);{var Nt=a=>{var u=er(),F=T(u),Qe=T(F);Zt(Qe,{class:"mr-2 h-5 w-5"}),$(2),k(F),$(2),k(u),t(a,u)};J(ht,a=>{he.hasApiKey||a(Nt)})}var tt=o(ht,2),bt=T(tt);r(bt,()=>lt,(a,u)=>{u(a,{children:(F,Qe)=>{var te=cr(),re=m(te);r(re,()=>it,(M,V)=>{V(M,{children:(G,Re)=>{var A=tr(),N=m(A);r(N,()=>dt,(S,f)=>{f(S,{children:(b,R)=>{$();var j=y("Create New Broadcast");t(b,j)},$$slots:{default:!0}})});var z=o(N,2);r(z,()=>nt,(S,f)=>{f(S,{children:(b,R)=>{$();var j=y("Send a broadcast email to a selected audience");t(b,j)},$$slots:{default:!0}})}),t(G,A)},$$slots:{default:!0}})});var Ne=o(re,2);r(Ne,()=>st,(M,V)=>{V(M,{children:(G,Re)=>{var A=dr(),N=T(A),z=T(N),S=o(T(z),2);const f=pe(()=>e(O)?{value:e(O).name,label:e(O).label}:null);r(S,()=>Pt,(s,i)=>{i(s,{get selected(){return e(f)},onSelectedChange:n=>{if(n){const Y=e(ze).find(d=>d.name===n.value);Y&&c(O,Y,!0)}else c(O,null)},children:(n,Y)=>{var d=rr(),p=m(d);r(p,()=>wt,(w,l)=>{l(w,{class:"w-full",children:(x,E)=>{var U=H(),v=m(U);r(v,()=>kt,(_,C)=>{C(_,{placeholder:"Select template"})}),t(x,U)},$$slots:{default:!0}})});var g=o(p,2);r(g,()=>yt,(w,l)=>{l(w,{children:(x,E)=>{var U=H(),v=m(U);at(v,17,()=>e(ze),ot,(_,C)=>{var Q=H(),oe=m(Q);const me=pe(()=>({value:e(C).name,label:e(C).label}));r(oe,()=>Ct,(K,Z)=>{Z(K,{get value(){return e(me)},children:(fe,We)=>{$();var q=y();W(()=>ee(q,e(C).label)),t(fe,q)},$$slots:{default:!0}})}),t(_,Q)}),t(x,U)},$$slots:{default:!0}})}),t(n,d)},$$slots:{default:!0}})});var b=o(S,2);gt(b),k(z);var R=o(z,2),j=o(T(R),2);const B=pe(()=>e(X)?{value:e(X).id,label:e(X).name}:null);r(j,()=>Pt,(s,i)=>{i(s,{get selected(){return e(B)},onSelectedChange:n=>{if(n){const Y=e(ke).find(d=>d.id===n.value);Y&&c(X,Y,!0)}else c(X,null)},children:(n,Y)=>{var d=ar(),p=m(d);r(p,()=>wt,(w,l)=>{l(w,{class:"w-full",children:(x,E)=>{var U=H(),v=m(U);r(v,()=>kt,(_,C)=>{C(_,{placeholder:"Select audience"})}),t(x,U)},$$slots:{default:!0}})});var g=o(p,2);r(g,()=>yt,(w,l)=>{l(w,{children:(x,E)=>{var U=H(),v=m(U);at(v,17,()=>e(ke),ot,(_,C)=>{var Q=H(),oe=m(Q);const me=pe(()=>({value:e(C).id,label:e(C).name}));r(oe,()=>Ct,(K,Z)=>{Z(K,{get value(){return e(me)},children:(fe,We)=>{$();var q=y();W(()=>ee(q,e(C).name)),t(fe,q)},$$slots:{default:!0}})}),t(_,Q)}),t(x,U)},$$slots:{default:!0}})}),t(n,d)},$$slots:{default:!0}})});var L=o(j,2);gt(L);var Be=o(L,2);{var De=s=>{var i=or();t(s,i)};J(Be,s=>{e(ke).length===0&&s(De)})}k(R);var ie=o(R,2),Oe=o(T(ie),2);r(Oe,()=>Te,(s,i)=>{i(s,{id:"subject",name:"subject",placeholder:"Email subject line",get value(){return e(le)},set value(n){c(le,n,!0)}})}),k(ie);var be=o(ie,2),Fe=o(T(be),2);r(Fe,()=>Te,(s,i)=>{i(s,{id:"title",name:"title",placeholder:"Email title",get value(){return e(se)},set value(n){c(se,n,!0)}})}),k(be);var ae=o(be,2),rt=o(T(ae),2);r(rt,()=>Gt,(s,i)=>{i(s,{id:"content",name:"content",placeholder:"Email content (HTML supported)",rows:6,get value(){return e(ne)},set value(n){c(ne,n,!0)}})}),k(ae);var de=o(ae,2),Le=o(T(de),2);r(Le,()=>Te,(s,i)=>{i(s,{id:"imageUrl",name:"imageUrl",placeholder:"https://example.com/image.jpg",get value(){return e(Se)},set value(n){c(Se,n,!0)}})}),k(de);var ce=o(de,2),Ye=T(ce),ue=o(T(Ye),2);r(ue,()=>Te,(s,i)=>{i(s,{id:"ctaText",name:"ctaText",placeholder:"Call to action text",get value(){return e(je)},set value(n){c(je,n,!0)}})}),k(Ye);var ge=o(Ye,2),Ke=o(T(ge),2);r(Ke,()=>Te,(s,i)=>{i(s,{id:"ctaUrl",name:"ctaUrl",placeholder:"https://example.com/action",get value(){return e(Ue)},set value(n){c(Ue,n,!0)}})}),k(ge),k(ce);var P=o(ce,2),xe=o(T(P),2);r(xe,()=>Te,(s,i)=>{i(s,{id:"scheduledAt",name:"scheduledAt",type:"datetime-local",get value(){return e(Ee)},set value(n){c(Ee,n,!0)}})}),$(2),k(P);var qe=o(P,2),ve=T(qe);const Pe=pe(()=>e(Ie)||!e(O));r(ve,()=>Ze,(s,i)=>{i(s,{type:"button",variant:"outline",onclick:_t,get disabled(){return e(Pe)},children:(n,Y)=>{var d=sr(),p=m(d);{var g=w=>{var l=lr();t(w,l)};J(p,w=>{e(Ie)&&w(g)})}$(),t(n,d)},$$slots:{default:!0}})});var we=o(ve,2);const He=pe(()=>e(Je)||!e(O)||!e(X)||!e(le)||!e(se)||!e(ne));r(we,()=>Ze,(s,i)=>{i(s,{type:"submit",get disabled(){return e(He)},children:(n,Y)=>{var d=ir(),p=m(d);{var g=l=>{var x=nr();t(l,x)};J(p,l=>{e(Je)&&l(g)})}var w=o(p);W(()=>ee(w,` ${e(Ee)?"Schedule":"Send"} Broadcast`)),t(n,d)},$$slots:{default:!0}})}),k(qe),k(N),k(A),W(()=>{var s,i;xt(b,((s=e(O))==null?void 0:s.name)||""),xt(L,((i=e(X))==null?void 0:i.id)||"")}),Vt("submit",A,s=>{s.preventDefault(),Et()}),t(G,A)},$$slots:{default:!0}})}),t(F,te)},$$slots:{default:!0}})});var Rt=o(bt,2);r(Rt,()=>lt,(a,u)=>{u(a,{children:(F,Qe)=>{var te=pr(),re=m(te);r(re,()=>it,(M,V)=>{V(M,{children:(G,Re)=>{var A=ur(),N=m(A);r(N,()=>dt,(S,f)=>{f(S,{children:(b,R)=>{$();var j=y("Preview");t(b,j)},$$slots:{default:!0}})});var z=o(N,2);r(z,()=>nt,(S,f)=>{f(S,{children:(b,R)=>{$();var j=y("Preview how your email will look");t(b,j)},$$slots:{default:!0}})}),t(G,A)},$$slots:{default:!0}})});var Ne=o(re,2);r(Ne,()=>st,(M,V)=>{V(M,{children:(G,Re)=>{var A=H(),N=m(A);{var z=f=>{var b=vr(),R=T(b);k(b),W(()=>Jt(R,"srcdoc",e(Me))),t(f,b)},S=(f,b)=>{{var R=B=>{var L=mr();t(B,L)},j=B=>{var L=fr(),Be=o(T(L),2);const De=pe(()=>!e(O));r(Be,()=>Ze,(ie,Oe)=>{Oe(ie,{variant:"outline",class:"mt-4",onclick:_t,get disabled(){return e(De)},children:(be,Fe)=>{$();var ae=y("Preview");t(be,ae)},$$slots:{default:!0}})}),k(L),t(B,L)};J(f,B=>{e(Ie)?B(R):B(j,!1)},b)}};J(N,f=>{e(Me)?f(z):f(S,!1)})}t(G,A)},$$slots:{default:!0}})}),t(F,te)},$$slots:{default:!0}})}),k(tt);var Bt=o(tt,2);r(Bt,()=>lt,(a,u)=>{u(a,{class:"mt-6",children:(F,Qe)=>{var te=wr(),re=m(te);r(re,()=>it,(M,V)=>{V(M,{children:(G,Re)=>{var A=_r(),N=m(A);r(N,()=>dt,(S,f)=>{f(S,{children:(b,R)=>{$();var j=y("Broadcast History");t(b,j)},$$slots:{default:!0}})});var z=o(N,2);r(z,()=>nt,(S,f)=>{f(S,{children:(b,R)=>{$();var j=y("View and manage your email broadcasts");t(b,j)},$$slots:{default:!0}})}),t(G,A)},$$slots:{default:!0}})});var Ne=o(re,2);r(Ne,()=>st,(M,V)=>{V(M,{children:(G,Re)=>{var A=H(),N=m(A);{var z=f=>{var b=$r();t(f,b)},S=(f,b)=>{{var R=B=>{var L=H(),Be=m(L);r(Be,()=>Qt,(De,ie)=>{ie(De,{children:(Oe,be)=>{var Fe=xr(),ae=m(Fe);r(ae,()=>Wt,(de,Le)=>{Le(de,{children:(ce,Ye)=>{var ue=H(),ge=m(ue);r(ge,()=>Tt,(Ke,P)=>{P(Ke,{children:(xe,qe)=>{var ve=hr(),Pe=m(ve);r(Pe,()=>_e,(d,p)=>{p(d,{children:(g,w)=>{$();var l=y("Subject");t(g,l)},$$slots:{default:!0}})});var we=o(Pe,2);r(we,()=>_e,(d,p)=>{p(d,{children:(g,w)=>{$();var l=y("Template");t(g,l)},$$slots:{default:!0}})});var He=o(we,2);r(He,()=>_e,(d,p)=>{p(d,{children:(g,w)=>{$();var l=y("Audience");t(g,l)},$$slots:{default:!0}})});var s=o(He,2);r(s,()=>_e,(d,p)=>{p(d,{children:(g,w)=>{$();var l=y("Status");t(g,l)},$$slots:{default:!0}})});var i=o(s,2);r(i,()=>_e,(d,p)=>{p(d,{children:(g,w)=>{$();var l=y("Scheduled");t(g,l)},$$slots:{default:!0}})});var n=o(i,2);r(n,()=>_e,(d,p)=>{p(d,{children:(g,w)=>{$();var l=y("Created");t(g,l)},$$slots:{default:!0}})});var Y=o(n,2);r(Y,()=>_e,(d,p)=>{p(d,{children:(g,w)=>{$();var l=y("Actions");t(g,l)},$$slots:{default:!0}})}),t(xe,ve)},$$slots:{default:!0}})}),t(ce,ue)},$$slots:{default:!0}})});var rt=o(ae,2);r(rt,()=>Xt,(de,Le)=>{Le(de,{children:(ce,Ye)=>{var ue=H(),ge=m(ue);at(ge,17,()=>e(Ae),ot,(Ke,P)=>{var xe=H(),qe=m(xe);r(qe,()=>Tt,(ve,Pe)=>{Pe(ve,{children:(we,He)=>{var s=gr(),i=m(s);r(i,()=>$e,(l,x)=>{x(l,{children:(E,U)=>{$();var v=y();W(()=>ee(v,e(P).subject)),t(E,v)},$$slots:{default:!0}})});var n=o(i,2);r(n,()=>$e,(l,x)=>{x(l,{children:(E,U)=>{$();var v=y();W(_=>ee(v,_),[()=>{var _;return((_=e(ze).find(C=>C.name===e(P).templateName))==null?void 0:_.label)||e(P).templateName}]),t(E,v)},$$slots:{default:!0}})});var Y=o(n,2);r(Y,()=>$e,(l,x)=>{x(l,{children:(E,U)=>{$();var v=y();W(_=>ee(v,_),[()=>{var _;return((_=e(ke).find(C=>C.id===e(P).audienceId))==null?void 0:_.name)||e(P).audienceId}]),t(E,v)},$$slots:{default:!0}})});var d=o(Y,2);r(d,()=>$e,(l,x)=>{x(l,{children:(E,U)=>{var v=br(),_=T(v),C=T(_);{var Q=K=>{ct(K,{class:"mr-1 inline-block h-3 w-3"})},oe=(K,Z)=>{{var fe=q=>{ut(q,{class:"mr-1 inline-block h-3 w-3"})},We=(q,Dt)=>{{var Ot=ye=>{vt(ye,{class:"mr-1 inline-block h-3 w-3"})},Ft=(ye,Lt)=>{{var Yt=Ce=>{mt(Ce,{class:"mr-1 inline-block h-3 w-3"})},Kt=Ce=>{At(Ce,{class:"mr-1 inline-block h-3 w-3"})};J(ye,Ce=>{Ge(e(P).status)===mt?Ce(Yt):Ce(Kt,!1)},Lt)}};J(q,ye=>{Ge(e(P).status)===vt?ye(Ot):ye(Ft,!1)},Dt)}};J(K,q=>{Ge(e(P).status)===ut?q(fe):q(We,!1)},Z)}};J(C,K=>{Ge(e(P).status)===ct?K(Q):K(oe,!1)})}var me=o(C);k(_),k(v),W((K,Z)=>{Mt(_,1,`rounded-full px-2 py-1 text-xs ${K??""}`),ee(me,` ${Z??""}`)},[()=>Ut(e(P).status),()=>e(P).status.charAt(0).toUpperCase()+e(P).status.slice(1)]),t(E,v)},$$slots:{default:!0}})});var p=o(d,2);r(p,()=>$e,(l,x)=>{x(l,{children:(E,U)=>{$();var v=y();W(_=>ee(v,_),[()=>pt(e(P).scheduledAt)]),t(E,v)},$$slots:{default:!0}})});var g=o(p,2);r(g,()=>$e,(l,x)=>{x(l,{children:(E,U)=>{$();var v=y();W(_=>ee(v,_),[()=>pt(e(P).createdAt)]),t(E,v)},$$slots:{default:!0}})});var w=o(g,2);r(w,()=>$e,(l,x)=>{x(l,{children:(E,U)=>{var v=H(),_=m(v);{var C=Q=>{var oe=H(),me=m(oe);r(me,()=>Ze,(K,Z)=>{Z(K,{variant:"outline",size:"sm",class:"text-red-500 hover:text-red-700",onclick:()=>It(e(P).id),children:(fe,We)=>{$();var q=y("Cancel");t(fe,q)},$$slots:{default:!0}})}),t(Q,oe)};J(_,Q=>{e(P).status==="scheduled"&&Q(C)})}t(E,v)},$$slots:{default:!0}})}),t(we,s)},$$slots:{default:!0}})}),t(Ke,xe)}),t(ce,ue)},$$slots:{default:!0}})}),t(Oe,Fe)},$$slots:{default:!0}})}),t(B,L)},j=B=>{var L=Pr();t(B,L)};J(f,B=>{e(Ae).length>0?B(R):B(j,!1)},b)}};J(N,f=>{e(et)?f(z):f(S,!1)})}t(G,A)},$$slots:{default:!0}})}),t(F,te)},$$slots:{default:!0}})}),t(St,$t),zt()}export{Zr as component};
