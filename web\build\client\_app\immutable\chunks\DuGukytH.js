import{f,a as i}from"./BasJTneF.js";import{p as l,a as c,c as p,au as m,r as n}from"./CGmarHxI.js";import{s as u,c as h}from"./ncUU1dSD.js";import{e as v}from"./B-Xjo-Yt.js";import{b}from"./5V1tIHTN.js";import{p as x,r as g}from"./Btcx8l8F.js";var _=f("<div><!></div>");function q(o,r){l(r,!0);let e=x(r,"ref",15,null),t=g(r,["$$slots","$$events","$$legacy","ref","class","children"]);var a=_();v(a,s=>({"data-slot":"card",class:s,...t}),[()=>h("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r.class)]);var d=p(a);u(d,()=>r.children??m),n(a),b(a,s=>e(s),()=>e()),i(o,a),c()}export{q as C};
