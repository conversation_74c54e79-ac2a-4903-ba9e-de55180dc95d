import{d as r}from"./Cf6rS4LV.js";const t={dashboard:{enabled:!0,bypassInDevelopment:!0,description:"Main dashboard access"},profile:{enabled:!0,bypassInDevelopment:!0,description:"User profile management"},automation:{enabled:!0,bypassInDevelopment:!0,description:"Job automation and application features"},resume_scanner:{enabled:!0,bypassInDevelopment:!0,description:"Resume scanning and analysis"},ats_optimization:{enabled:!0,bypassInDevelopment:!0,description:"ATS optimization features"},cover_letter_generator:{enabled:!0,bypassInDevelopment:!0,description:"AI-powered cover letter generation"},ai_matching:{enabled:!0,bypassInDevelopment:!0,description:"AI job matching"},analytics:{enabled:!0,bypassInDevelopment:!0,description:"Job market analytics and insights"},team_collaboration:{enabled:!0,bypassInDevelopment:!0,description:"Team collaboration features"},linkedin_integration:{enabled:!0,bypassInDevelopment:!0,description:"LinkedIn integration"},email_support:{enabled:!0,bypassInDevelopment:!0,description:"Email support access"},api_access:{enabled:!0,bypassInDevelopment:!0,description:"API access for integrations"},custom_branding:{enabled:!0,bypassInDevelopment:!0,description:"Custom branding options"}},a={DISABLE_ALL_LIMITS:typeof window<"u"&&localStorage.getItem("disable-feature-limits")==="true",DISABLED_FEATURES:typeof window<"u"?(localStorage.getItem("disabled-features")||"").split(",").filter(Boolean):[],ENABLE_ALL_FEATURES:typeof window<"u"&&localStorage.getItem("enable-all-features")==="true",DEVELOPMENT_BYPASS:r};function i(e){if(a.DISABLED_FEATURES.includes("*"))return!1;if(a.ENABLE_ALL_FEATURES)return!0;if(a.DISABLED_FEATURES.includes(e))return!1;const n=t[e];return n?n.enabled:(console.warn(`Feature '${e}' not found in FEATURE_FLAGS`),!1)}function s(e){if(a.DISABLE_ALL_LIMITS)return!0;const n=t[e];return n!=null&&n.bypassInDevelopment&&a.DEVELOPMENT_BYPASS,!1}function l(){return Object.entries(t).filter(([e])=>i(e)).map(([e])=>e)}function u(e){return t[e]||null}function p(e,n){t[e]&&(t[e].enabled=n,console.log(`Feature '${e}' ${n?"enabled":"disabled"}`))}export{a as ENVIRONMENT_CONFIG,t as FEATURE_FLAGS,l as getEnabledFeatures,u as getFeatureConfig,i as isFeatureEnabled,s as shouldBypassLimits,p as toggleFeature};
