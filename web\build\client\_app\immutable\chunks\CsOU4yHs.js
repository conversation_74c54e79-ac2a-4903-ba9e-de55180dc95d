import{c as l,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as n}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(e,r){const t=m(r,["children","$$slots","$$events","$$legacy"]),s=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}],["path",{d:"M12 17h.01"}]];f(e,d({name:"circle-help"},()=>t,{get iconNode(){return s},children:(a,$)=>{var o=l(),c=i(o);n(c,r,"default",{},null),p(a,o)},$$slots:{default:!0}}))}export{x as C};
