import{f as c,a as o,t as h,c as pt}from"../chunks/BasJTneF.js";import{o as Wt}from"../chunks/nZgk9enP.js";import{p as Ot,w as Bt,k as ut,v as Ut,i as Yt,f as E,t as _,a as Qt,c as a,s as e,n as f,g as i,x as $e,r as t,d as ke}from"../chunks/CGmarHxI.js";import{s as p}from"../chunks/CIt1g2O9.js";import{i as x}from"../chunks/u21ee2wt.js";import{e as Ae,i as Ce}from"../chunks/C3w0v0gR.js";import{c as N}from"../chunks/BvdI7LR8.js";import{b as Ht}from"../chunks/B-Xjo-Yt.js";import{s as Kt,a as Xt}from"../chunks/CmxjS0TN.js";import"../chunks/CgXBgsce.js";import{t as ne}from"../chunks/DjPYYl4Z.js";import{B as de}from"../chunks/B1K98fMG.js";import{C as Ye}from"../chunks/DuGukytH.js";import{C as Qe}from"../chunks/Cdn-N1RY.js";import{C as He}from"../chunks/BkJY4La4.js";import{C as Ke}from"../chunks/GwmmX_iF.js";import{C as Xe}from"../chunks/D50jIuLr.js";import{B as ee}from"../chunks/DaBofrVv.js";import{S as qt}from"../chunks/D9yI7a4E.js";import{A as Gt,a as Vt,b as Zt,c as jt,d as ea,e as ta,f as aa,R as ra}from"../chunks/BnikQ10_.js";import{f as mt,a as qe}from"../chunks/ncUU1dSD.js";import{R as sa}from"../chunks/C4zOxlM4.js";import{R as oa}from"../chunks/B8CsXmVA.js";import{A as ia}from"../chunks/Ce6y1v79.js";import{R as la}from"../chunks/qwsZpUIl.js";import{F as na}from"../chunks/ChqRiddM.js";import{T as Je}from"../chunks/CZ8wIJN8.js";import{D as ft}from"../chunks/6BxQgNmX.js";import{B as _t}from"../chunks/C2AK_5VT.js";import{C as Ge}from"../chunks/-SpbofVw.js";import{C as xt}from"../chunks/D6Qh9vtB.js";import{C as da}from"../chunks/BAIxhb6t.js";import{C as va}from"../chunks/DW7T7T22.js";import{P as ca}from"../chunks/DvO_AOqy.js";import{B as pa}from"../chunks/CDnvByek.js";import{E as ua}from"../chunks/zNKWipEG.js";import{M as ma}from"../chunks/CwgkX8t9.js";import{C as fa}from"../chunks/DZCYCPd3.js";var _a=c("<!> Back to Automation",1),xa=c("<!> ",1),ga=c("<!> Stop Run",1),$a=c("<!> Pending",1),ha=c("<!> Refresh",1),ya=c("<!> <!>",1),Sa=c("<!> ",1),wa=c("<!> <!>",1),ba=c('<span class="text-gray-400">No skills specified</span>'),ka=c('<div><div class="text-sm font-medium text-gray-400">Stopped</div> <div> </div></div>'),Aa=c('<div class="mb-4"><div class="bg-secondary h-2 w-full rounded-full"><div class="bg-primary h-full rounded-full transition-all"></div></div> <div class="mt-2 text-sm text-gray-400"> </div></div> <div class="grid grid-cols-1 gap-6 md:grid-cols-2"><div><h3 class="mb-2 text-lg font-semibold">Profile</h3> <div class="rounded-lg border p-4"><div class="mb-2 text-lg font-medium"> </div> <div class="mb-4 text-sm text-gray-400"> </div> <div class="mb-2 text-sm font-medium text-gray-400">Resume</div> <div class="mb-4"><!></div> <div class="mb-2 text-sm font-medium text-gray-400">Skills</div> <div class="flex flex-wrap gap-1"><!></div></div></div> <div><h3 class="mb-2 text-lg font-semibold">Search Parameters</h3> <div class="rounded-lg border p-4"><div class="mb-4"><div class="text-sm font-medium text-gray-400">Keywords</div> <div><!></div></div> <div class="mb-4"><div class="text-sm font-medium text-gray-400">Location</div> <div><!></div></div> <div class="mb-4"><div class="text-sm font-medium text-gray-400">Started</div> <div> </div></div> <!></div></div></div>',1),Pa=c("<!> <!>",1),Ca=c("<!> <!>",1),Ma=c('<div><span class="text-xs text-gray-400">Jobs Selected</span> <div class="text-sm"> </div></div>'),Da=c('<div><span class="text-xs text-gray-400">Salary Range</span> <div class="text-sm"> </div></div>'),Ra=c('<div><span class="text-xs text-gray-400">Experience Range</span> <div class="text-sm"> </div></div>'),Ea=c('<div><span class="text-xs text-gray-400">Remote Preference</span> <div class="text-sm capitalize"> </div></div>'),Ta=c('<div><span class="text-xs text-gray-400">Job Types</span> <div class="mt-1 flex flex-wrap gap-1"></div></div>'),Ja=c('<div><span class="text-xs text-gray-400">Company Size</span> <div class="mt-1 flex flex-wrap gap-1"></div></div>'),za=c('<div><span class="text-xs text-gray-400">Preferred Companies</span> <div class="mt-1 flex flex-wrap gap-1"><!> <!></div></div>'),La=c('<div><span class="text-xs text-gray-400">Excluded Companies</span> <div class="mt-1 flex flex-wrap gap-1"><!> <!></div></div>'),Na=c('<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border p-4"><h4 class="mb-3 flex items-center text-sm font-medium text-gray-400"><!> Auto-Apply Status</h4> <div class="space-y-3"><div class="flex items-center justify-between"><span class="text-sm">Auto-Apply Enabled</span> <!></div> <!> <div><span class="text-xs text-gray-400">Max Jobs to Apply</span> <div class="text-sm"> </div></div> <div><span class="text-xs text-gray-400">Min Match Score</span> <div class="text-sm"> </div></div></div></div> <div class="rounded-lg border p-4"><h4 class="mb-3 flex items-center text-sm font-medium text-gray-400"><!> Salary & Experience</h4> <div class="space-y-3"><!> <!> <!></div></div> <div class="rounded-lg border p-4"><h4 class="mb-3 flex items-center text-sm font-medium text-gray-400"><!> Preferences</h4> <div class="space-y-3"><!> <!> <!> <!></div></div></div>'),Fa=c("<!> <!>",1),Ia=c("<!> ",1),Wa=c('<div class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center"><!> <h3 class="text-xl font-semibold text-gray-300">No jobs found yet</h3> <p class="mt-2 text-gray-400"><!></p></div>'),Oa=c("<!> ",1),Ba=c('<div class="mb-6 rounded-lg border border-blue-500/20 bg-blue-500/5 p-4"><div class="mb-4 flex items-center justify-between"><div><h3 class="text-lg font-medium text-blue-400">Enable Auto-Apply</h3> <p class="text-sm text-gray-400">Select jobs you want to automatically apply to</p></div> <div class="flex items-center gap-2"><span class="text-sm text-gray-400"> </span></div></div> <div class="mb-4 flex flex-wrap gap-2"><!> <!> <!> <!></div> <div class="flex items-center gap-2"><!></div></div>'),Ua=c('<div class="flex items-center gap-1"><!> </div>'),Ya=c("<!> Apply",1),Qa=c('<div class="flex items-start justify-between"><div class="min-w-0 flex-1"><div class="flex items-center gap-2"><!> <!> <!> <!></div> <!></div> <div class="flex items-center gap-3"><!> <!></div></div>'),Ha=c('<div><div class="flex items-center gap-1 text-sm font-medium text-gray-400"><!> Location</div> <div class="text-sm"> </div></div>'),Ka=c('<div><div class="flex items-center gap-1 text-sm font-medium text-gray-400"><!> Salary</div> <div class="text-sm"><!></div></div>'),Xa=c('<div><div class="text-sm font-medium text-gray-400">Type</div> <div class="text-sm capitalize"> </div></div>'),qa=c('<div><div class="flex items-center gap-1 text-sm font-medium text-gray-400"><!> Posted</div> <div class="text-sm"> </div></div>'),Ga=c('<div class="mt-4"><div class="text-sm font-medium text-gray-400">Description</div> <div class="mt-1 line-clamp-3 text-sm"> </div></div>'),Va=c('<div class="mt-4"><div class="text-sm font-medium text-gray-400">Skills</div> <div class="mt-1 flex flex-wrap gap-1"><!> <!></div></div>'),Za=c('<div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4"><!> <!> <!> <!></div> <!> <!>',1),ja=c("<!> <!>",1),er=c('<!> <div class="grid gap-4"></div>',1),tr=c(" <br/><br/> This will automatically submit applications to the selected jobs using your profile and resume.",1),ar=c("<!> <!>",1),rr=c("<!> Enable Auto-Apply",1),sr=c("<!> <!>",1),or=c("<!> <!>",1),ir=c('<div class="container mt-6 flex max-w-full flex-col gap-10 p-6"><div class="flex flex-col gap-8"><div class="flex items-center gap-4"><!> <h1 class="text-xl font-normal text-white">Automation Run Details</h1> <!> <div class="ml-auto flex items-center gap-2"><!> <!></div></div></div> <!> <!> <div><div class="mb-4 flex items-center justify-between"><div class="flex items-center gap-4"><h2 class="text-2xl font-semibold text-gray-300"> </h2> <!></div> <div class="flex items-center gap-2 text-sm text-gray-400"><span> </span> <span>•</span> <span> </span> <span>•</span> <span> </span></div></div> <!></div></div> <!>',1);function Qr(gt,ze){Ot(ze,!0);const[$t,ht]=Kt(),r=()=>Xt(Me,"$automationRun",$t);let Me=Bt(ze.data.automationRun),W=ut(Ut(new Set)),De=ut(!1);const Ve=[{id:"1",title:"Senior Frontend Developer",company:"TechCorp Inc.",location:"San Francisco, CA",salary:"$120k - $160k",salaryMin:120,salaryMax:160,employmentType:"full-time",postedDate:new Date(Date.now()-2*24*60*60*1e3).toISOString(),matchScore:92,description:"We are looking for a Senior Frontend Developer to join our team. You will be responsible for building user interfaces using React, TypeScript, and modern web technologies.",skills:["React","TypeScript","JavaScript","CSS","HTML","Node.js"],applyLink:"https://example.com/apply/1",applicationStatus:null},{id:"2",title:"Full Stack Engineer",company:"StartupXYZ",location:"Remote",salary:"$100k - $140k",salaryMin:100,salaryMax:140,employmentType:"full-time",postedDate:new Date(Date.now()-1*24*60*60*1e3).toISOString(),matchScore:88,description:"Join our fast-growing startup as a Full Stack Engineer. Work with React, Node.js, and PostgreSQL to build scalable web applications.",skills:["React","Node.js","PostgreSQL","JavaScript","AWS"],applyLink:"https://example.com/apply/2",applicationStatus:null},{id:"3",title:"React Developer",company:"Digital Agency Co.",location:"New York, NY",salary:"$90k - $120k",salaryMin:90,salaryMax:120,employmentType:"full-time",postedDate:new Date(Date.now()-3*24*60*60*1e3).toISOString(),matchScore:85,description:"We need a React Developer to help build modern web applications for our clients. Experience with Redux and TypeScript preferred.",skills:["React","Redux","TypeScript","CSS","JavaScript"],applyLink:"https://example.com/apply/3",applicationStatus:null},{id:"4",title:"Frontend Engineer",company:"Enterprise Solutions Ltd.",location:"Austin, TX",salary:"$110k - $150k",salaryMin:110,salaryMax:150,employmentType:"full-time",postedDate:new Date(Date.now()-4*24*60*60*1e3).toISOString(),matchScore:78,description:"Looking for a Frontend Engineer to work on enterprise-level applications. Strong knowledge of React and modern JavaScript required.",skills:["React","JavaScript","HTML","CSS","Git"],applyLink:"https://example.com/apply/4",applicationStatus:null},{id:"5",title:"Software Developer",company:"MegaCorp Industries",location:"Seattle, WA",salary:"$95k - $130k",salaryMin:95,salaryMax:130,employmentType:"full-time",postedDate:new Date(Date.now()-5*24*60*60*1e3).toISOString(),matchScore:72,description:"Join our development team to build innovative software solutions. Experience with React and backend technologies is a plus.",skills:["React","JavaScript","Python","SQL"],applyLink:"https://example.com/apply/5",applicationStatus:null}],he=Ve.length>0?Ve:ze.data.jobs||[];function Ze(s){i(W).has(s)?i(W).delete(s):i(W).add(s),ke(W,new Set(i(W)),!0)}function Le(s=80){i(W).clear(),he.forEach(u=>{u.matchScore&&u.matchScore>=s&&i(W).add(u.id)}),ke(W,new Set(i(W)),!0)}function yt(){i(W).clear(),ke(W,new Set(i(W)),!0)}function je(){if(i(W).size===0){ne.error("Please select at least one job to enable auto-apply");return}ke(De,!0)}async function St(){try{const s=Array.from(i(W));console.log("Enabling auto-apply for jobs:",s),ne.success(`Auto-apply enabled for ${s.length} job${s.length===1?"":"s"}`),ke(De,!1),Me.update(u=>({...u,autoApplyEnabled:!0,selectedJobIds:s}))}catch(s){console.error("Error enabling auto-apply:",s),ne.error("Failed to enable auto-apply")}}async function wt(){try{const s=await fetch(`/api/automation/runs/${r().id}/stop`,{method:"POST"});if(s.ok){const u=await s.json();Me.set({...r(),status:"stopped",stoppedAt:u.stoppedAt}),ne.success("Automation run stopped")}else{const u=await s.json();ne.error(u.message||"Failed to stop automation run")}}catch(s){console.error("Error stopping automation run:",s),ne.error("An error occurred while stopping the automation run")}}async function Re(){try{const s=await fetch(`/api/automation/runs/${r().id}`);if(s.ok){const u=await s.json();Me.set(u),ne.success("Data refreshed"),(u.status==="running"||u.status==="pending")&&setTimeout(Re,5e3)}else ne.error("Failed to refresh data")}catch(s){console.error("Error refreshing data:",s),ne.error("An error occurred while refreshing data")}}let ve=null;Wt(()=>{const u=`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}/api/automation/ws`;return ve=new WebSocket(u),ve.onopen=()=>{console.log("WebSocket connection established")},ve.onmessage=S=>{try{const g=JSON.parse(S.data);if(console.log("WebSocket message received:",g),g.type==="status"&&g.data.runId===r().id){const T=r().status,P=g.data.status;if(Me.update(l=>({...l,status:P,progress:g.data.progress||l.progress,error:g.data.error||l.error})),T!==P&&["completed","failed","stopped"].includes(P))switch(P){case"completed":ne.success("Automation completed successfully!",{description:`${g.data.jobsFound??0} jobs were processed.`,duration:5e3});break;case"failed":ne.error("Automation run failed",{description:g.data.error??"Please check the details and try again.",duration:5e3});break;case"stopped":ne.warning("Automation run stopped",{description:`${g.data.jobsProcessed??0} jobs were processed before stopping.`,duration:5e3});break}}else g.type==="jobs"&&g.data.runId===r().id&&Re()}catch(g){console.error("Error processing WebSocket message:",g)}},ve.onclose=()=>{console.log("WebSocket connection closed")},ve.onerror=S=>{console.error("WebSocket error:",S),r()&&(r().status==="running"||r().status==="pending")&&setTimeout(Re,5e3)},()=>{ve&&ve.readyState===WebSocket.OPEN&&ve.close()}}),Yt(()=>{r()&&(r().status==="running"||r().status==="pending")&&(!ve||ve.readyState!==WebSocket.OPEN)&&setTimeout(Re,5e3)});function bt(s){switch(s){case"running":return"default";case"completed":return"outline";case"failed":return"destructive";case"stopped":return"secondary";default:return"secondary"}}function kt(s){switch(s){case"running":return ca;case"completed":return va;case"failed":return da;case"stopped":return xt;case"pending":return Ge;default:return Ge}}function et(s){return s.status==="completed"?100:(s.status==="failed"||s.status==="stopped",s.progress||0)}function ye(s){if(!s)return{};if(s.data)try{return typeof s.data.data=="string"?JSON.parse(s.data.data):s.data.data&&typeof s.data.data=="object"?s.data.data:typeof s.data=="string"?JSON.parse(s.data):s.data}catch(u){return console.error("Error parsing profile data:",u),{}}return s}var tt=ir(),Ne=E(tt),Fe=a(Ne),at=a(Fe),rt=a(at);de(rt,{variant:"ghost",onclick:()=>window.location.href="/dashboard/automation",children:(s,u)=>{var S=_a(),g=E(S);ia(g,{class:"mr-2 h-4 w-4"}),f(),o(s,S)},$$slots:{default:!0}});var st=e(rt,4);const At=$e(()=>bt(r().status));ee(st,{get variant(){return i(At)},class:"ml-2",children:(s,u)=>{var S=xa();const g=$e(()=>kt(r().status));var T=E(S);N(T,()=>i(g),(l,M)=>{M(l,{class:"mr-1 h-3 w-3"})});var P=e(T);_(l=>p(P,` ${l??""}`),[()=>r().status.charAt(0).toUpperCase()+r().status.slice(1)]),o(s,S)},$$slots:{default:!0}});var ot=e(st,2),it=a(ot);{var Pt=s=>{de(s,{variant:"outline",size:"sm",onclick:wt,children:(u,S)=>{var g=ga(),T=E(g);xt(T,{class:"mr-2 h-4 w-4"}),f(),o(u,g)},$$slots:{default:!0}})},Ct=(s,u)=>{{var S=g=>{de(g,{variant:"outline",size:"sm",disabled:!0,children:(T,P)=>{var l=$a(),M=E(l);Ge(M,{class:"mr-2 h-4 w-4"}),f(),o(T,l)},$$slots:{default:!0}})};x(s,g=>{r().status==="pending"&&g(S)},u)}};x(it,s=>{r().status==="running"?s(Pt):s(Ct,!1)})}var Mt=e(it,2);de(Mt,{variant:"outline",size:"sm",onclick:Re,children:(s,u)=>{var S=ha(),g=E(S);la(g,{class:"mr-2 h-4 w-4"}),f(),o(s,S)},$$slots:{default:!0}}),t(ot),t(at),t(Fe);var lt=e(Fe,2);N(lt,()=>Ye,(s,u)=>{u(s,{children:(S,g)=>{var T=Pa(),P=E(T);N(P,()=>Ke,(M,G)=>{G(M,{class:"p-6",children:(te,ie)=>{var C=ya(),F=E(C);N(F,()=>Xe,(Y,Q)=>{Q(Y,{children:(O,q)=>{f();var J=h("Run Information");o(O,J)},$$slots:{default:!0}})});var U=e(F,2);N(U,()=>He,(Y,Q)=>{Q(Y,{children:(O,q)=>{f();var J=h("Details about this automation run");o(O,J)},$$slots:{default:!0}})}),o(te,C)},$$slots:{default:!0}})});var l=e(P,2);N(l,()=>Qe,(M,G)=>{G(M,{class:"p-6 pt-0",children:(te,ie)=>{var C=Aa(),F=E(C),U=a(F),Y=a(U);t(U);var Q=e(U,2),O=a(Q);t(Q),t(F);var q=e(F,2),J=a(q),H=e(a(J),2),z=a(H),y=a(z,!0);t(z);var L=e(z,2),ae=a(L,!0);t(L);var B=e(L,4),le=a(B);{var ue=R=>{ee(R,{variant:"outline",children:(K,v)=>{var n=Sa(),m=E(n);na(m,{class:"mr-1 h-3 w-3"});var b=e(m);_(()=>{var X;return p(b,` ${((X=r().profile.resumes[0].document)==null?void 0:X.label)||"Resume"}`)}),o(K,n)},$$slots:{default:!0}})},me=R=>{ee(R,{variant:"outline",class:"text-gray-400",children:(K,v)=>{f();var n=h("No resume");o(K,n)},$$slots:{default:!0}})};x(le,R=>{var K;(K=r().profile)!=null&&K.resumes&&Array.isArray(r().profile.resumes)&&r().profile.resumes.length>0?R(ue):R(me,!1)})}t(B);var ce=e(B,4),we=a(ce);{var fe=R=>{var K=wa(),v=E(K);Ae(v,1,()=>ye(r().profile).skills.slice(0,5),Ce,(b,X)=>{ee(b,{variant:"secondary",class:"text-xs",children:(oe,j)=>{f();var V=h();_(()=>p(V,i(X))),o(oe,V)},$$slots:{default:!0}})});var n=e(v,2);{var m=b=>{ee(b,{variant:"secondary",class:"text-xs",children:(X,oe)=>{f();var j=h();_(V=>p(j,`+${V??""} more`),[()=>ye(r().profile).skills.length-5]),o(X,j)},$$slots:{default:!0}})};x(n,b=>{ye(r().profile).skills.length>5&&b(m)})}o(R,K)},_e=R=>{var K=ba();o(R,K)};x(we,R=>{r().profile&&ye(r().profile).skills&&ye(r().profile).skills.length>0?R(fe):R(_e,!1)})}t(ce),t(H),t(J);var xe=e(J,2),ge=e(a(xe),2),w=a(ge),k=e(a(w),2),d=a(k);const $=$e(()=>r().keywords||"");sa(d,{get keywordIds(){return i($)},fallback:"None specified"}),t(k),t(w);var A=e(w,2),I=e(a(A),2),D=a(I);const se=$e(()=>r().location||"");oa(D,{get locationIds(){return i(se)},fallback:"None specified"}),t(I),t(A);var Z=e(A,2),re=e(a(Z),2),Se=a(re);t(re),t(Z);var Pe=e(Z,2);{var pe=R=>{var K=ka(),v=e(a(K),2),n=a(v);t(v),t(K),_((m,b)=>p(n,`${m??""} (${b??""} ago)`),[()=>mt(r().stoppedAt),()=>qe(new Date(r().stoppedAt))]),o(R,K)};x(Pe,R=>{r().stoppedAt&&R(pe)})}t(ge),t(xe),t(q),_((R,K,v,n,m,b)=>{Ht(Y,`width: ${R??""}%`),p(O,`Progress: ${K??""}%`),p(y,v),p(ae,n),p(Se,`${m??""} (${b??""} ago)`)},[()=>et(r()),()=>et(r()),()=>r().profile&&ye(r().profile).fullName||"Unnamed Profile",()=>r().profile&&(ye(r().profile).title||ye(r().profile).headline)||"No title specified",()=>mt(r().createdAt),()=>qe(new Date(r().createdAt))]),o(te,C)},$$slots:{default:!0}})}),o(S,T)},$$slots:{default:!0}})});var nt=e(lt,2);N(nt,()=>Ye,(s,u)=>{u(s,{children:(S,g)=>{var T=Fa(),P=E(T);N(P,()=>Ke,(M,G)=>{G(M,{class:"p-6",children:(te,ie)=>{var C=Ca(),F=E(C);N(F,()=>Xe,(Y,Q)=>{Q(Y,{children:(O,q)=>{f();var J=h("Automation Configuration");o(O,J)},$$slots:{default:!0}})});var U=e(F,2);N(U,()=>He,(Y,Q)=>{Q(Y,{children:(O,q)=>{f();var J=h("Detailed automation parameters and settings");o(O,J)},$$slots:{default:!0}})}),o(te,C)},$$slots:{default:!0}})});var l=e(P,2);N(l,()=>Qe,(M,G)=>{G(M,{class:"p-6 pt-0",children:(te,ie)=>{var C=Na(),F=a(C),U=a(F),Y=a(U);Je(Y,{class:"mr-2 h-4 w-4"}),f(),t(U);var Q=e(U,2),O=a(Q),q=e(a(O),2);const J=$e(()=>r().autoApplyEnabled?"default":"secondary");ee(q,{get variant(){return i(J)},children:(v,n)=>{f();var m=h();_(()=>p(m,r().autoApplyEnabled?"Enabled":"Disabled")),o(v,m)},$$slots:{default:!0}}),t(O);var H=e(O,2);{var z=v=>{var n=Ma(),m=e(a(n),2),b=a(m,!0);t(m),t(n),_(()=>{var X;return p(b,((X=r().selectedJobIds)==null?void 0:X.length)||0)}),o(v,n)};x(H,v=>{r().autoApplyEnabled&&v(z)})}var y=e(H,2),L=e(a(y),2),ae=a(L,!0);t(L),t(y);var B=e(y,2),le=e(a(B),2),ue=a(le);t(le),t(B),t(Q),t(F);var me=e(F,2),ce=a(me),we=a(ce);ft(we,{class:"mr-2 h-4 w-4"}),f(),t(ce);var fe=e(ce,2),_e=a(fe);{var xe=v=>{var n=Da(),m=e(a(n),2),b=a(m);t(m),t(n),_(()=>p(b,`$${(r().salaryMin||0)??""}k - $${r().salaryMax||200}k`)),o(v,n)};x(_e,v=>{(r().salaryMin||r().salaryMax)&&v(xe)})}var ge=e(_e,2);{var w=v=>{var n=Ra(),m=e(a(n),2),b=a(m);t(m),t(n),_(()=>p(b,`${(r().experienceLevelMin||0)??""} - ${r().experienceLevelMax||10} years`)),o(v,n)};x(ge,v=>{(r().experienceLevelMin||r().experienceLevelMax)&&v(w)})}var k=e(ge,2);{var d=v=>{var n=Ea(),m=e(a(n),2),b=a(m,!0);t(m),t(n),_(()=>p(b,r().remotePreference)),o(v,n)};x(k,v=>{r().remotePreference&&v(d)})}t(fe),t(me);var $=e(me,2),A=a($),I=a(A);_t(I,{class:"mr-2 h-4 w-4"}),f(),t(A);var D=e(A,2),se=a(D);{var Z=v=>{var n=Ta(),m=e(a(n),2);Ae(m,5,()=>r().jobTypes,Ce,(b,X)=>{ee(b,{variant:"secondary",class:"text-xs capitalize",children:(oe,j)=>{f();var V=h();_(()=>p(V,i(X))),o(oe,V)},$$slots:{default:!0}})}),t(m),t(n),o(v,n)};x(se,v=>{r().jobTypes&&r().jobTypes.length>0&&v(Z)})}var re=e(se,2);{var Se=v=>{var n=Ja(),m=e(a(n),2);Ae(m,5,()=>r().companySizePreference,Ce,(b,X)=>{ee(b,{variant:"secondary",class:"text-xs capitalize",children:(oe,j)=>{f();var V=h();_(()=>p(V,i(X))),o(oe,V)},$$slots:{default:!0}})}),t(m),t(n),o(v,n)};x(re,v=>{r().companySizePreference&&r().companySizePreference.length>0&&v(Se)})}var Pe=e(re,2);{var pe=v=>{var n=za(),m=e(a(n),2),b=a(m);Ae(b,1,()=>r().preferredCompanies.slice(0,3),Ce,(j,V)=>{ee(j,{variant:"outline",class:"text-xs",children:(Ee,be)=>{f();var Te=h();_(()=>p(Te,i(V))),o(Ee,Te)},$$slots:{default:!0}})});var X=e(b,2);{var oe=j=>{ee(j,{variant:"outline",class:"text-xs",children:(V,Ee)=>{f();var be=h();_(()=>p(be,`+${r().preferredCompanies.length-3} more`)),o(V,be)},$$slots:{default:!0}})};x(X,j=>{r().preferredCompanies.length>3&&j(oe)})}t(m),t(n),o(v,n)};x(Pe,v=>{r().preferredCompanies&&r().preferredCompanies.length>0&&v(pe)})}var R=e(Pe,2);{var K=v=>{var n=La(),m=e(a(n),2),b=a(m);Ae(b,1,()=>r().excludeCompanies.slice(0,3),Ce,(j,V)=>{ee(j,{variant:"destructive",class:"text-xs",children:(Ee,be)=>{f();var Te=h();_(()=>p(Te,i(V))),o(Ee,Te)},$$slots:{default:!0}})});var X=e(b,2);{var oe=j=>{ee(j,{variant:"destructive",class:"text-xs",children:(V,Ee)=>{f();var be=h();_(()=>p(be,`+${r().excludeCompanies.length-3} more`)),o(V,be)},$$slots:{default:!0}})};x(X,j=>{r().excludeCompanies.length>3&&j(oe)})}t(m),t(n),o(v,n)};x(R,v=>{r().excludeCompanies&&r().excludeCompanies.length>0&&v(K)})}t(D),t($),t(C),_(()=>{p(ae,r().maxJobsToApply||10),p(ue,`${r().minMatchScore||70}%`)}),o(te,C)},$$slots:{default:!0}})}),o(S,T)},$$slots:{default:!0}})});var dt=e(nt,2),Ie=a(dt),We=a(Ie),Oe=a(We),Dt=a(Oe);t(Oe);var Rt=e(Oe,2);{var Et=s=>{const u=$e(()=>i(W).size===0);de(s,{variant:"outline",size:"sm",onclick:je,get disabled(){return i(u)},class:"h-8 text-xs",children:(S,g)=>{var T=Ia(),P=E(T);Je(P,{class:"mr-1 h-3 w-3"});var l=e(P);_(()=>p(l,` Apply Selected (${i(W).size??""})`)),o(S,T)},$$slots:{default:!0}})};x(Rt,s=>{he.length>0&&!r().autoApplyEnabled&&s(Et)})}t(We);var vt=e(We,2),Be=a(vt),Tt=a(Be);t(Be);var Ue=e(Be,4),Jt=a(Ue);t(Ue);var ct=e(Ue,4),zt=a(ct);t(ct),t(vt),t(Ie);var Lt=e(Ie,2);{var Nt=s=>{var u=Wa(),S=a(u);pa(S,{class:"mb-4 h-12 w-12 text-gray-400"});var g=e(S,4),T=a(g);{var P=M=>{var G=h("The automation is still running. Jobs will appear here as they are found.");o(M,G)},l=(M,G)=>{{var te=C=>{var F=h("The automation is pending. Jobs will appear here once it starts running.");o(C,F)},ie=C=>{var F=h("No jobs were found during this automation run.");o(C,F)};x(M,C=>{r().status==="pending"?C(te):C(ie,!1)},G)}};x(T,M=>{r().status==="running"?M(P):M(l,!1)})}t(g),t(u),o(s,u)},Ft=s=>{var u=er(),S=E(u);{var g=P=>{var l=Ba(),M=a(l),G=e(a(M),2),te=a(G),ie=a(te);t(te),t(G),t(M);var C=e(M,2),F=a(C);de(F,{variant:"outline",size:"sm",onclick:()=>Le(90),children:(H,z)=>{f();var y=h("Select 90%+ Match");o(H,y)},$$slots:{default:!0}});var U=e(F,2);de(U,{variant:"outline",size:"sm",onclick:()=>Le(80),children:(H,z)=>{f();var y=h("Select 80%+ Match");o(H,y)},$$slots:{default:!0}});var Y=e(U,2);de(Y,{variant:"outline",size:"sm",onclick:()=>Le(70),children:(H,z)=>{f();var y=h("Select 70%+ Match");o(H,y)},$$slots:{default:!0}});var Q=e(Y,2);de(Q,{variant:"ghost",size:"sm",onclick:yt,children:(H,z)=>{f();var y=h("Clear All");o(H,y)},$$slots:{default:!0}}),t(C);var O=e(C,2),q=a(O);const J=$e(()=>i(W).size===0);de(q,{onclick:je,get disabled(){return i(J)},class:"bg-blue-600 hover:bg-blue-700",children:(H,z)=>{var y=Oa(),L=E(y);Je(L,{class:"mr-2 h-4 w-4"});var ae=e(L);_(()=>p(ae,` Enable Auto-Apply (${i(W).size??""} jobs)`)),o(H,y)},$$slots:{default:!0}}),t(O),t(l),_(()=>p(ie,`${i(W).size??""} selected`)),o(P,l)};x(S,P=>{r().autoApplyEnabled||P(g)})}var T=e(S,2);Ae(T,21,()=>he,P=>P.id,(P,l)=>{var M=pt(),G=E(M);N(G,()=>Ye,(te,ie)=>{ie(te,{class:"flex flex-col",children:(C,F)=>{var U=ja(),Y=E(U);N(Y,()=>Ke,(O,q)=>{q(O,{class:"p-6",children:(J,H)=>{var z=Qa(),y=a(z),L=a(y),ae=a(L);N(ae,()=>Xe,(d,$)=>{$(d,{class:"text-lg",children:(A,I)=>{f();var D=h();_(()=>p(D,i(l).title)),o(A,D)},$$slots:{default:!0}})});var B=e(ae,2);{var le=d=>{ee(d,{variant:"outline",class:"text-xs",children:($,A)=>{f();var I=h();_(()=>p(I,`${i(l).matchScore??""}% match`)),o($,I)},$$slots:{default:!0}})};x(B,d=>{i(l).matchScore&&d(le)})}var ue=e(B,2);{var me=d=>{const $=$e(()=>i(l).applicationStatus==="applied"?"default":"secondary");ee(d,{get variant(){return i($)},class:"text-xs",children:(A,I)=>{f();var D=h();_(()=>p(D,i(l).applicationStatus)),o(A,D)},$$slots:{default:!0}})};x(ue,d=>{i(l).applicationStatus&&d(me)})}var ce=e(ue,2);{var we=d=>{ee(d,{variant:"default",class:"bg-blue-600 text-xs",children:($,A)=>{f();var I=h("Auto-Apply Enabled");o($,I)},$$slots:{default:!0}})};x(ce,d=>{var $;r().autoApplyEnabled&&(($=r().selectedJobIds)!=null&&$.includes(i(l).id))&&d(we)})}t(L);var fe=e(L,2);N(fe,()=>He,(d,$)=>{$(d,{class:"mt-1",children:(A,I)=>{var D=Ua(),se=a(D);_t(se,{class:"h-3 w-3"});var Z=e(se);t(D),_(()=>p(Z,` ${i(l).company??""}`)),o(A,D)},$$slots:{default:!0}})}),t(y);var _e=e(y,2),xe=a(_e);{var ge=d=>{const $=$e(()=>i(W).has(i(l).id));qt(d,{get checked(){return i($)},onCheckedChange:A=>{Ze(A?i(l).id:i(l).id)}})};x(xe,d=>{r().autoApplyEnabled||d(ge)})}var w=e(xe,2);{var k=d=>{de(d,{variant:"outline",size:"sm",onclick:()=>window.open(i(l).applyLink||i(l).url,"_blank"),children:($,A)=>{var I=Ya(),D=E(I);ua(D,{class:"mr-2 h-4 w-4"}),f(),o($,I)},$$slots:{default:!0}})};x(w,d=>{(i(l).applyLink||i(l).url)&&d(k)})}t(_e),t(z),o(J,z)},$$slots:{default:!0}})});var Q=e(Y,2);N(Q,()=>Qe,(O,q)=>{q(O,{class:"p-6 pt-0",children:(J,H)=>{var z=Za(),y=E(z),L=a(y);{var ae=w=>{var k=Ha(),d=a(k),$=a(d);ma($,{class:"h-3 w-3"}),f(),t(d);var A=e(d,2),I=a(A,!0);t(A),t(k),_(()=>p(I,i(l).location)),o(w,k)};x(L,w=>{i(l).location&&w(ae)})}var B=e(L,2);{var le=w=>{var k=Ka(),d=a(k),$=a(d);ft($,{class:"h-3 w-3"}),f(),t(d);var A=e(d,2),I=a(A);{var D=Z=>{var re=h();_(()=>p(re,i(l).salary)),o(Z,re)},se=(Z,re)=>{{var Se=pe=>{var R=h();_(()=>p(R,`$${i(l).salaryMin??""}k - $${i(l).salaryMax??""}k`)),o(pe,R)},Pe=(pe,R)=>{{var K=n=>{var m=h();_(()=>p(m,`$${i(l).salaryMin??""}k+`)),o(n,m)},v=(n,m)=>{{var b=X=>{var oe=h();_(()=>p(oe,`Up to $${i(l).salaryMax??""}k`)),o(X,oe)};x(n,X=>{i(l).salaryMax&&X(b)},m)}};x(pe,n=>{i(l).salaryMin?n(K):n(v,!1)},R)}};x(Z,pe=>{i(l).salaryMin&&i(l).salaryMax?pe(Se):pe(Pe,!1)},re)}};x(I,Z=>{i(l).salary?Z(D):Z(se,!1)})}t(A),t(k),o(w,k)};x(B,w=>{(i(l).salary||i(l).salaryMin||i(l).salaryMax)&&w(le)})}var ue=e(B,2);{var me=w=>{var k=Xa(),d=e(a(k),2),$=a(d,!0);t(d),t(k),_(()=>p($,i(l).employmentType)),o(w,k)};x(ue,w=>{i(l).employmentType&&w(me)})}var ce=e(ue,2);{var we=w=>{var k=qa(),d=a(k),$=a(d);fa($,{class:"h-3 w-3"}),f(),t(d);var A=e(d,2),I=a(A);t(A),t(k),_(D=>p(I,`${D??""} ago`),[()=>qe(new Date(i(l).postedDate||i(l).postedAt))]),o(w,k)};x(ce,w=>{(i(l).postedDate||i(l).postedAt)&&w(we)})}t(y);var fe=e(y,2);{var _e=w=>{var k=Ga(),d=e(a(k),2),$=a(d,!0);t(d),t(k),_(()=>p($,i(l).description)),o(w,k)};x(fe,w=>{i(l).description&&w(_e)})}var xe=e(fe,2);{var ge=w=>{var k=Va(),d=e(a(k),2),$=a(d);Ae($,17,()=>i(l).skills.slice(0,5),Ce,(D,se)=>{ee(D,{variant:"secondary",class:"text-xs",children:(Z,re)=>{f();var Se=h();_(()=>p(Se,i(se))),o(Z,Se)},$$slots:{default:!0}})});var A=e($,2);{var I=D=>{ee(D,{variant:"secondary",class:"text-xs",children:(se,Z)=>{f();var re=h();_(()=>p(re,`+${i(l).skills.length-5} more`)),o(se,re)},$$slots:{default:!0}})};x(A,D=>{i(l).skills.length>5&&D(I)})}t(d),t(k),o(w,k)};x(xe,w=>{i(l).skills&&i(l).skills.length>0&&w(ge)})}o(J,z)},$$slots:{default:!0}})}),o(C,U)},$$slots:{default:!0}})}),o(P,M)}),t(T),o(s,u)};x(Lt,s=>{he.length===0?s(Nt):s(Ft,!1)})}t(dt),t(Ne);var It=e(Ne,2);N(It,()=>ra,(s,u)=>{u(s,{get open(){return i(De)},set open(S){ke(De,S,!0)},children:(S,g)=>{var T=pt(),P=E(T);N(P,()=>Gt,(l,M)=>{M(l,{children:(G,te)=>{var ie=or(),C=E(ie);N(C,()=>Vt,(U,Y)=>{Y(U,{children:(Q,O)=>{var q=ar(),J=E(q);N(J,()=>Zt,(z,y)=>{y(z,{children:(L,ae)=>{f();var B=h("Confirm Auto-Apply");o(L,B)},$$slots:{default:!0}})});var H=e(J,2);N(H,()=>jt,(z,y)=>{y(z,{children:(L,ae)=>{f();var B=tr(),le=E(B);f(3),_(()=>p(le,`Are you sure you want to enable auto-apply for ${i(W).size??""} selected job${i(W).size===1?"":"s"}? `)),o(L,B)},$$slots:{default:!0}})}),o(Q,q)},$$slots:{default:!0}})});var F=e(C,2);N(F,()=>ea,(U,Y)=>{Y(U,{children:(Q,O)=>{var q=sr(),J=E(q);N(J,()=>ta,(z,y)=>{y(z,{onclick:()=>ke(De,!1),children:(L,ae)=>{f();var B=h("Cancel");o(L,B)},$$slots:{default:!0}})});var H=e(J,2);N(H,()=>aa,(z,y)=>{y(z,{onclick:St,children:(L,ae)=>{var B=rr(),le=E(B);Je(le,{class:"mr-2 h-4 w-4"}),f(),o(L,B)},$$slots:{default:!0}})}),o(Q,q)},$$slots:{default:!0}})}),o(G,ie)},$$slots:{default:!0}})}),o(S,T)},$$slots:{default:!0}})}),_(s=>{p(Dt,`Jobs Found (${he.length??""})`),p(Tt,`Applied: ${(r().jobsApplied||0)??""}`),p(Jt,`Skipped: ${(r().jobsSkipped||0)??""}`),p(zt,`Avg Match: ${s??""}%`)},[()=>he.length>0?(he.reduce((s,u)=>s+(u.matchScore||0),0)/he.length).toFixed(1):0]),o(gt,tt),Qt(),ht()}export{Qr as component};
