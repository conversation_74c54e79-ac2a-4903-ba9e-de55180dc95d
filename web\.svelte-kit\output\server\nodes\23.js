import * as server from '../entries/pages/blog/_slug_/_page.server.ts.js';

export const index = 23;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/blog/_slug_/_page.svelte.js')).default;
export { server };
export const server_id = "src/routes/blog/[slug]/+page.server.ts";
export const imports = ["_app/immutable/nodes/23.DIA-vf8m.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/DosGZj-c.js","_app/immutable/chunks/CGtH72Kl.js","_app/immutable/chunks/C1FmrZbK.js","_app/immutable/chunks/BMgaXnEE.js","_app/immutable/chunks/DYwWIJ9y.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/Dy6ycI81.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/DETxXRrJ.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/Cs0qIT7f.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/Ce6y1v79.js"];
export const stylesheets = ["_app/immutable/assets/PortableText.DSHKgSkc.css"];
export const fonts = [];
