import{a2 as S,a3 as y,a4 as K,g as l,a5 as M,a6 as A,v as N,d as U,o as D,a7 as C,a8 as G,a1 as T,a9 as q,aa as $,m as z,ab as L,ac as _,ad as V,ae as Z,af as F,ag as H,e as J,ah as Q}from"./CGmarHxI.js";import{d as W}from"./CmxjS0TN.js";const X={get(e,r){if(!e.exclude.includes(r))return e.props[r]},set(e,r){return!1},getOwnPropertyDescriptor(e,r){if(!e.exclude.includes(r)&&r in e.props)return{enumerable:!0,configurable:!0,value:e.props[r]}},has(e,r){return e.exclude.includes(r)?!1:r in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(r=>!e.exclude.includes(r))}};function ue(e,r,n){return new Proxy({props:e,exclude:r},X)}const k={get(e,r){if(!e.exclude.includes(r))return l(e.version),r in e.special?e.special[r]():e.props[r]},set(e,r,n){return r in e.special||(e.special[r]=re({get[r](){return e.props[r]}},r,K)),e.special[r](n),L(e.version),!0},getOwnPropertyDescriptor(e,r){if(!e.exclude.includes(r)&&r in e.props)return{enumerable:!0,configurable:!0,value:e.props[r]}},deleteProperty(e,r){return e.exclude.includes(r)||(e.exclude.push(r),L(e.version)),!0},has(e,r){return e.exclude.includes(r)?!1:r in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(r=>!e.exclude.includes(r))}};function ie(e,r){return new Proxy({props:e,exclude:r,special:{},version:C(0)},k)}const ee={get(e,r){let n=e.props.length;for(;n--;){let s=e.props[n];if(_(s)&&(s=s()),typeof s=="object"&&s!==null&&r in s)return s[r]}},set(e,r,n){let s=e.props.length;for(;s--;){let i=e.props[s];_(i)&&(i=i());const a=S(i,r);if(a&&a.set)return a.set(n),!0}return!1},getOwnPropertyDescriptor(e,r){let n=e.props.length;for(;n--;){let s=e.props[n];if(_(s)&&(s=s()),typeof s=="object"&&s!==null&&r in s){const i=S(s,r);return i&&!i.configurable&&(i.configurable=!0),i}}},has(e,r){if(r===T||r===q)return!1;for(let n of e.props)if(_(n)&&(n=n()),n!=null&&r in n)return!0;return!1},ownKeys(e){const r=[];for(let n of e.props)if(_(n)&&(n=n()),!!n){for(const s in n)r.includes(s)||r.push(s);for(const s of Object.getOwnPropertySymbols(n))r.includes(s)||r.push(s)}return r}};function ae(...e){return new Proxy({props:e},ee)}function g(e){var r;return((r=e.ctx)==null?void 0:r.d)??!1}function re(e,r,n,s){var E;var i=(n&$)!==0,a=!Z||(n&F)!==0,v=(n&G)!==0,B=(n&V)!==0,O=!1,c;v?[c,O]=W(()=>e[r]):c=e[r];var Y=T in e||q in e,d=v&&(((E=S(e,r))==null?void 0:E.set)??(Y&&r in e&&(u=>e[r]=u)))||void 0,f=s,b=!0,m=!1,I=()=>(m=!0,b&&(b=!1,B?f=D(s):f=s),f);c===void 0&&s!==void 0&&(d&&a&&y(),c=I(),d&&d(c));var o;if(a)o=()=>{var u=e[r];return u===void 0?I():(b=!0,m=!1,u)};else{var R=(i?A:J)(()=>e[r]);R.f|=H,o=()=>{var u=l(R);return u!==void 0&&(f=void 0),u===void 0?f:u}}if(!(n&K)&&a)return o;if(d){var j=e.$$legacy;return function(u,p){return arguments.length>0?((!a||!p||j||O)&&d(p?o():u),u):o()}}var P=!1,w=!1,h=z(c),t=A(()=>{var u=o(),p=l(h);return P?(P=!1,w=!0,p):(w=!1,h.v=u)});return v&&l(t),i||(t.equals=M),function(u,p){if(Q!==null&&(P=w,o(),l(h)),arguments.length>0){const x=p?l(t):a&&v?N(u):u;if(!t.equals(x)){if(P=!0,U(h,x),m&&f!==void 0&&(f=x),g(t))return u;D(()=>l(t))}return u}return g(t)?t.v:l(t)}}export{ie as l,re as p,ue as r,ae as s};
