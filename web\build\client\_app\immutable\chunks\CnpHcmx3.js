import{c as p,a as m}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(r,o){const s=c(o,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"M18 6 6 18"}],["path",{d:"m6 6 12 12"}]];f(r,d({name:"x"},()=>s,{get iconNode(){return e},children:(a,$)=>{var t=p(),n=i(t);l(n,o,"default",{},null),m(a,t)},$$slots:{default:!0}}))}export{y as X};
