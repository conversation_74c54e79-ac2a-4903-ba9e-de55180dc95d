import{f as $,t as _,a as r,c as ge}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as we,a as ye,f as R,g as e,e as ie,s as t,c as n,d as g,m as J,t as re,n as p,r as l,l as Ne,h as Ue,b as rt,x as at}from"../chunks/CGmarHxI.js";import{s as z}from"../chunks/CIt1g2O9.js";import{i as G}from"../chunks/u21ee2wt.js";import{e as Ve,i as st}from"../chunks/C3w0v0gR.js";import{i as Ie}from"../chunks/BIEMS98f.js";import{p as Z}from"../chunks/Btcx8l8F.js";import{B as ae,b as ot}from"../chunks/B1K98fMG.js";import{R as lt,P as nt,S as it,a as dt,b as ut,c as ct,d as ft}from"../chunks/CTn0v-X8.js";import{C as mt}from"../chunks/DuGukytH.js";import{C as vt}from"../chunks/Cdn-N1RY.js";import{C as pt}from"../chunks/BkJY4La4.js";import{C as gt}from"../chunks/GwmmX_iF.js";import{C as _t}from"../chunks/D50jIuLr.js";import{c as ht,o as $t}from"../chunks/nZgk9enP.js";import{R as xt,a as bt}from"../chunks/tdzGgazS.js";import{R as Ye,S as qe,a as Ge,b as be}from"../chunks/CGK0g3x_.js";import{I as Te}from"../chunks/DMTMHyMa.js";import{L as De}from"../chunks/BvvicRXk.js";import{t as se}from"../chunks/DjPYYl4Z.js";import{D as wt}from"../chunks/CodWuqwu.js";import{D as yt,a as Pt,b as St,c as Rt}from"../chunks/CKh8VGVX.js";import{S as Je}from"../chunks/B2lQHLf_.js";import{g as We}from"../chunks/BiJhC7W5.js";import{s as At}from"../chunks/BBa424ah.js";import{c as kt}from"../chunks/BMZasLyv.js";import{T as Ut}from"../chunks/CTO_B1Jk.js";import{L as Dt}from"../chunks/DHNQRrgO.js";import{a as Tt}from"../chunks/YNp1uWxB.js";import{t as It,c as Lt}from"../chunks/bK-q0z-2.js";import{S as Ft}from"../chunks/FAbXdqfL.js";var Ct=$("<!> <!>",1),jt=$("<!> <!>",1),zt=$("<div><!> <!></div>"),Bt=$('<!> <div class="grid gap-4 py-4"><!> <div><!> <!></div> <div><!> <!></div></div> <!>',1),Mt=$("<!> <!>",1);function Oe(E,o){we(o,!1);let f=Z(o,"profiles",24,()=>[]),i=J(""),s=J(null),h=J(""),I=J(!1),K=J(!1);const Q=ht();async function oe(){var A,L,F,S;if(!(!e(s)||!e(i)&&f().length>0||!e(h))){g(I,!0);try{const x=new FormData;x.append("file",e(s)),e(i)&&(x.append("profileId",e(i)),console.log("Uploading resume with profileId:",e(i))),x.append("label",e(h)),x.append("documentType","resume"),x.append("parseIntoProfile","true"),console.log("Sending resume upload request with form data:",{file:(A=e(s))==null?void 0:A.name,fileType:(L=e(s))==null?void 0:L.type,fileSize:(F=e(s))==null?void 0:F.size,profileId:e(i),label:e(h),documentType:"resume",parseIntoProfile:!0});const Y=await fetch("/api/resume/upload",{method:"POST",body:x});if(console.log("Response status:",Y.status),Y.ok){const q=await Y.json();console.log("Response from server:",q);const le=(S=q.resume)==null?void 0:S.id;console.log("Resume from server:",q.resume),se.success("Resume uploaded",{description:"Your resume was uploaded successfully.",action:{label:"View",onClick:()=>{Q("uploaded",{resumeId:le})}}}),Q("resume-uploaded",q),g(K,!1),N()}else se.error("Upload failed",{description:"Please try again or check your file."})}catch(x){se.error("Unexpected error",{description:"Something went wrong during upload."}),console.error("Upload error:",x)}finally{g(I,!1)}}}function N(){g(i,""),g(s,null),g(h,"")}Ie(),xt(E,{get open(){return e(K)},set open(A){g(K,A)},children:(A,L)=>{var F=Mt(),S=R(F);const x=ie(()=>ot({variant:"outline"}));wt(S,{get class(){return e(x)},children:(q,le)=>{p();var T=_("Upload Resume");r(q,T)},$$slots:{default:!0}});var Y=t(S,2);bt(Y,{class:"sm:max-w-md",children:(q,le)=>{var T=Bt(),W=R(T);yt(W,{children:(c,U)=>{var a=Ct(),m=R(a);Pt(m,{children:(v,j)=>{p();var y=_("Upload a Resume");r(v,y)},$$slots:{default:!0}});var D=t(m,2);St(D,{children:(v,j)=>{p();var y=_("Select a profile and upload your PDF resume.");r(v,y)},$$slots:{default:!0}}),r(c,a)},$$slots:{default:!0}});var H=t(W,2),u=n(H);{var B=c=>{var U=zt(),a=n(U);De(a,{for:"profile",children:(v,j)=>{p();var y=_("Select Profile");r(v,y)},$$slots:{default:!0}});var m=t(a,2);const D=ie(()=>({value:e(i)}));Ye(m,{get selected(){return e(D)},onSelectedChange:v=>v&&g(i,v.value),children:(v,j)=>{var y=jt(),X=R(y);qe(X,{children:(ue,ce)=>{Je(ue,{placeholder:"Choose a profile"})},$$slots:{default:!0}});var de=t(X,2);Ge(de,{children:(ue,ce)=>{var _e=ge(),fe=R(_e);Ve(fe,1,f,st,(Pe,ne)=>{const he=ie(()=>String(e(ne).id));be(Pe,{get value(){return e(he)},children:(Se,Le)=>{p();var $e=_();re(()=>z($e,e(ne).name)),r(Se,$e)},$$slots:{default:!0}})}),r(ue,_e)},$$slots:{default:!0}}),r(v,y)},$$slots:{default:!0}}),l(U),r(c,U)};G(u,c=>{f().length>0&&c(B)})}var k=t(u,2),w=n(k);De(w,{for:"label",children:(c,U)=>{p();var a=_("Resume Name");r(c,a)},$$slots:{default:!0}});var O=t(w,2);Te(O,{id:"label",placeholder:"e.g. Senior Engineer Resume",get value(){return e(h)},set value(c){g(h,c)},$$legacy:!0}),l(k);var C=t(k,2),M=n(C);De(M,{for:"resume-upload",children:(c,U)=>{p();var a=_("PDF Resume");r(c,a)},$$slots:{default:!0}});var V=t(M,2);Te(V,{id:"resume-upload",type:"file",accept:".pdf",onchange:c=>{var U,a;return g(s,((a=(U=c.target)==null?void 0:U.files)==null?void 0:a[0])||null)},class:"w-full rounded border border-dashed bg-gray-800 px-4 py-2 text-sm text-white"}),l(C),l(H);var ee=t(H,2);Rt(ee,{children:(c,U)=>{const a=ie(()=>e(I)||!(e(s)instanceof File)||f().length>0&&!e(i)||!e(h));ae(c,{type:"button",onclick:oe,get disabled(){return e(a)},children:(m,D)=>{p();var v=_();re(()=>z(v,e(I)?"Uploading...":"Upload")),r(m,v)},$$slots:{default:!0}})},$$slots:{default:!0}}),r(q,T)},$$slots:{default:!0}}),r(A,F)},$$slots:{default:!0},$$legacy:!0}),ye()}var Et=$('<div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8 text-center"><div class="bg-muted mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full"><!></div> <h3 class="mb-2 text-lg font-medium"><!></h3> <p class="text-muted-foreground mb-4 max-w-md"> </p> <!></div>');function Nt(E,o){we(o,!1);let f=Z(o,"userData",8),i=Z(o,"featureId",8),s=Z(o,"limitId",8,void 0),h=Z(o,"showUpgradeButton",8,!0),I=Z(o,"upgradeButtonText",8,"Upgrade Plan"),K=Z(o,"upgradeButtonLink",8,"/dashboard/settings/billing"),Q=Z(o,"limitReachedMessage",8,"You have reached the limit for this feature."),oe=Z(o,"notIncludedMessage",8,"This feature is not included in your current plan."),N,A=J(!1),L=J(!1),F=J("");$t(()=>{S()});function S(){try{if(f())if(N=kt(f()),g(A,N.hasAccess(i())),e(A)&&s())if(g(L,N.hasReachedLimit(i(),s())),e(L)){const T=N.getLimitValue(i(),s());g(F,`${Q()} (Limit: ${T})`)}else g(F,"");else e(A)?g(F,""):g(F,oe())}catch(T){console.error("Error in FeatureGuard:",T),g(A,!1),g(L,!1),g(F,"Error checking feature access.")}}Ne(()=>(Ue(f()),Ue(i())),()=>{f()&&i()&&S()}),Ne(()=>Ue(s()),()=>{s()&&S()}),rt(),Ie();var x=ge(),Y=R(x);{var q=T=>{var W=ge(),H=R(W);At(H,o,"default",{},null),r(T,W)},le=T=>{var W=Et(),H=n(W),u=n(H);{var B=a=>{Ut(a,{class:"text-warning h-6 w-6"})},k=a=>{Dt(a,{class:"text-muted-foreground h-6 w-6"})};G(u,a=>{e(L)?a(B):a(k,!1)})}l(H);var w=t(H,2),O=n(w);{var C=a=>{var m=_("Limit Reached");r(a,m)},M=a=>{var m=_("Feature Not Available");r(a,m)};G(O,a=>{e(L)?a(C):a(M,!1)})}l(w);var V=t(w,2),ee=n(V,!0);l(V);var c=t(V,2);{var U=a=>{ae(a,{variant:"outline",onclick:()=>We(K()),children:(m,D)=>{p();var v=_();re(()=>z(v,I())),r(m,v)},$$slots:{default:!0}})};G(c,a=>{h()&&a(U)})}l(W),re(()=>z(ee,e(F))),r(T,W)};G(Y,T=>{e(A)&&!e(L)?T(q):T(le,!1)})}r(E,x),ye()}async function Ot(E){try{const o=Tt(E);if(!o)return!1;if(!o.limits||o.limits.length===0)return!0;for(const f of o.limits){const{limitReached:i}=await Lt(E,f.id);if(i)return!1}return!0}catch(o){return console.error("Error checking if user can use feature:",o),!1}}async function Vt(E,o,f=1){try{if(!await Ot(E))return{success:!1,canUse:!1,message:"You have reached your limit for this feature"};const s=await It(E,o,f);return{success:s.success,canUse:!s.limitReached,message:s.message,error:s.error}}catch(i){return console.error("Error tracking feature usage:",i),{success:!1,canUse:!1,error:i.message||"Failed to track feature usage"}}}var Yt=$('<div class="h-4 w-4 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div> Analyzing...',1),qt=$("<!> Analyze with ATS",1);function Gt(E,o){we(o,!0);let f=!1,i=null;async function s(){if(!o.resumeId){se.error("Resume ID is required");return}if(!o.isParsed){se.error("Resume must be parsed before analysis");return}f=!0,i=null;try{const h=await Vt("ats_optimization","ats_scans_monthly");if(!h.canUse){se.error(h.message||"You have reached your limit for ATS scans"),f=!1;return}const I=await fetch("/api/ai/ats",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({resumeId:o.resumeId})});if(!I.ok){const Q=await I.json();throw new Error(Q.error||"Failed to start ATS analysis")}const K=await I.json();se.success("ATS analysis started"),We(`/dashboard/documents/${o.resumeId}/ats`)}catch(h){i=h instanceof Error?h.message:"An error occurred",se.error(`Error: ${i}`),console.error("Error starting ATS analysis:",h)}finally{f=!1}}Nt(E,{get userData(){return o.userData},featureId:"ats_optimization",limitId:"ats_scans_monthly",showUpgradeButton:!0,upgradeButtonText:"Upgrade for ATS Analysis",limitReachedMessage:"You've reached your monthly limit for ATS scans",notIncludedMessage:"ATS Analysis is not included in your current plan",children:(h,I)=>{const K=at(()=>f||o.disabled||!o.isParsed);ae(h,{variant:"outline",size:"sm",class:"flex items-center gap-2",get disabled(){return e(K)},$$events:{click:s},children:(Q,oe)=>{var N=ge(),A=R(N);{var L=S=>{var x=Yt();p(),r(S,x)},F=S=>{var x=qt(),Y=R(x);Ft(Y,{class:"h-4 w-4 text-blue-500"}),p(),r(S,x)};G(A,S=>{f?S(L):S(F,!1)})}r(Q,N)},$$slots:{default:!0}})},$$slots:{default:!0}}),ye()}var Jt=$("<!> <!> <!>",1),Wt=$("<!> <!>",1),Ht=$('<div class="py-10 text-center"><p class="text-muted-foreground">No resumes available. Please upload a resume to get started.</p> <!></div>'),Kt=$("<!> <!>",1),Qt=$('<p class="text-destructive mt-2 text-xs">This resume is not connected to a profile.</p>'),Xt=$(" <!>",1),Zt=$("<!> <!>",1),er=$('<div class="mb-12 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3"></div>'),tr=$("<!> <!>",1),rr=$('<div class="space-y-1 text-sm"><p><strong>Title:</strong> </p> <p><strong>Location:</strong> </p> <p><strong>Applications:</strong> </p></div>'),ar=$('<p class="text-muted-foreground text-sm">No recent job search activity.</p>'),sr=$('<!> <div class="space-y-6"><div><h2 class="text-muted-foreground mb-2 text-sm font-medium">Metadata</h2> <div class="space-y-1 text-sm"><p><strong>Label:</strong> </p> <p><strong>Uploaded:</strong> </p> <p><strong>Filename:</strong> </p> <p><strong>Score:</strong> <span class="bg-warning text-warning-foreground inline-block rounded px-2 py-0.5 text-xs"> </span></p> <!></div></div> <div><h2 class="text-muted-foreground mb-2 text-sm font-medium">Associated Profile</h2> <div class="space-y-1 text-sm"><p><strong>Name:</strong> </p> <!></div></div> <div><h2 class="text-muted-foreground mb-2 text-sm font-medium">Latest Job Search</h2> <!></div> <div class="border-border border-t pt-4"><h2 class="text-muted-foreground mb-2 text-sm font-medium">Actions</h2> <div class="flex flex-wrap gap-2"><!> <!> <!> <!></div></div></div>',1),or=$("<!> <!>",1),lr=$('<div class="container mx-auto mt-6 flex flex-col gap-4 p-6"><div class="mb-6 flex flex-col items-center justify-between gap-8"><div class="flex w-full flex-row items-start justify-between gap-8"><div class="flex flex-col gap-2"><h1 class="text-foreground text-2xl font-bold">Resume Workspace</h1> <p class="text-muted-foreground text-md">Manage resume parsing, optimization, and performance data</p></div> <!></div> <div class="mb-6 flex w-full flex-col justify-between gap-6 sm:flex-row"><!> <div class="flex w-full flex-col gap-2 sm:w-[200px]"><!></div></div></div> <!></div> <!>',1);function Mr(E,o){we(o,!1);let f=Z(o,"data",8),i=J(!1),s=J(null),h=J(""),I=J("");function K(){return f().resumes.filter(u=>{var O,C,M;const k=(((O=u.document)==null?void 0:O.label)||"").toLowerCase().includes(e(h).toLowerCase()),w=((M=(C=u.profile)==null?void 0:C.name)==null?void 0:M.toLowerCase().includes(e(I).toLowerCase()))||!e(I);return k&&w})}function Q(u){g(s,u),g(i,!0)}Ie();var oe=lr(),N=R(oe),A=n(N),L=n(A),F=t(n(L),2);Oe(F,{get profiles(){return f().profiles},$$events:{"resume-uploaded":u=>{console.log("Resume uploaded event received:",u.detail),setTimeout(()=>{console.log("Reloading page to show new resume..."),window.location.reload()},2e3)}}}),l(L);var S=t(L,2),x=n(S);Te(x,{id:"searchTerm",type:"text",class:"flex w-full flex-col gap-2 rounded border px-4 py-2 text-sm sm:w-[300px]",placeholder:"Search resumes by label",get value(){return e(h)},set value(u){g(h,u)},$$legacy:!0});var Y=t(x,2),q=n(Y);Ye(q,{class:"rounded border px-4 py-2 text-sm",get value(){return e(I)},set value(u){g(I,u)},children:(u,B)=>{var k=Wt(),w=R(k);qe(w,{children:(C,M)=>{Je(C,{placeholder:"All Profiles"})},$$slots:{default:!0}});var O=t(w,2);Ge(O,{children:(C,M)=>{var V=Jt(),ee=R(V);be(ee,{value:"all",children:(a,m)=>{p();var D=_("All Profiles");r(a,D)},$$slots:{default:!0}});var c=t(ee,2);be(c,{value:"completed",children:(a,m)=>{p();var D=_("Completed");r(a,D)},$$slots:{default:!0}});var U=t(c,2);be(U,{value:"incomplete",children:(a,m)=>{p();var D=_("Incomplete");r(a,D)},$$slots:{default:!0}}),r(C,V)},$$slots:{default:!0}}),r(u,k)},$$slots:{default:!0},$$legacy:!0}),l(Y),l(S),l(A);var le=t(A,2);{var T=u=>{var B=Ht(),k=t(n(B),2);Oe(k,{get profiles(){return f().profiles},$$events:{"resume-uploaded":w=>{console.log("Resume uploaded event received in empty state:",w.detail),setTimeout(()=>{console.log("Reloading page to show new resume..."),window.location.reload()},2e3)}}}),l(B),r(u,B)},W=u=>{var B=er();Ve(B,5,K,k=>k.id,(k,w)=>{mt(k,{onclick:()=>Q(e(w)),class:"hover:border-primary cursor-pointer",children:(O,C)=>{var M=Zt(),V=R(M);gt(V,{children:(c,U)=>{var a=Kt(),m=R(a);_t(m,{children:(v,j)=>{p();var y=_();re(()=>{var X;return z(y,((X=e(w).document)==null?void 0:X.label)||"Unnamed Resume")}),r(v,y)},$$slots:{default:!0}});var D=t(m,2);pt(D,{class:"text-muted-foreground",children:(v,j)=>{p();var y=_();re(X=>z(y,`Uploaded: ${X??""}`),[()=>new Date(e(w).createdAt).toLocaleDateString()],ie),r(v,y)},$$slots:{default:!0}}),r(c,a)},$$slots:{default:!0}});var ee=t(V,2);vt(ee,{class:"text-muted-foreground text-sm",children:(c,U)=>{p();var a=Xt(),m=R(a),D=t(m);{var v=j=>{var y=Qt();r(j,y)};G(D,j=>{e(w).profile||j(v)})}re(()=>{var j;return z(m,`Profile: ${((j=e(w).profile)==null?void 0:j.name)??"N/A"??""} `)}),r(c,a)},$$slots:{default:!0}}),r(O,M)},$$slots:{default:!0}})}),l(B),r(u,B)};G(le,u=>{f().resumes.length===0?u(T):u(W,!1)})}l(N);var H=t(N,2);lt(H,{get open(){return e(i)},set open(u){g(i,u)},children:(u,B)=>{nt(u,{children:(k,w)=>{var O=or(),C=R(O);it(C,{});var M=t(C,2);dt(M,{class:"bg-background text-foreground w-[500px] max-w-full overflow-y-auto p-6",children:(V,ee)=>{var c=ge(),U=R(c);{var a=m=>{var D=sr(),v=R(D);ut(v,{children:(d,P)=>{var b=tr(),te=R(b);ct(te,{class:"mb-2 text-xl font-semibold",children:(ve,xe)=>{p();var pe=_("Resume Overview");r(ve,pe)},$$slots:{default:!0}});var me=t(te,2);ft(me,{class:"text-muted-foreground mb-6",children:(ve,xe)=>{p();var pe=_("View score, associated profiles, recent job search activity, and more.");r(ve,pe)},$$slots:{default:!0}}),r(d,b)},$$slots:{default:!0}});var j=t(v,2),y=n(j),X=t(n(y),2),de=n(X),ue=t(n(de));l(de);var ce=t(de,2),_e=t(n(ce));l(ce);var fe=t(ce,2),Pe=t(n(fe));l(fe);var ne=t(fe,2),he=t(n(ne),2),Se=n(he,!0);l(he),l(ne);var Le=t(ne,2);{var $e=d=>{ae(d,{variant:"ghost",size:"sm",onclick:()=>window.open(`/uploads${e(s).document.fileUrl}`,"_blank"),children:(P,b)=>{p();var te=_("Download PDF");r(P,te)},$$slots:{default:!0}})};G(Le,d=>{var P;(P=e(s).document)!=null&&P.fileUrl&&d($e)})}l(X),l(y);var Re=t(y,2),Fe=t(n(Re),2),Ae=n(Fe),He=t(n(Ae));l(Ae);var Ke=t(Ae,2);ae(Ke,{size:"sm",variant:"secondary",children:(d,P)=>{p();var b=_("Open Profile");r(d,b)},$$slots:{default:!0}}),l(Fe),l(Re);var ke=t(Re,2),Qe=t(n(ke),2);{var Xe=d=>{var P=rr(),b=n(P),te=t(n(b));l(b);var me=t(b,2),ve=t(n(me));l(me);var xe=t(me,2),pe=t(n(xe));l(xe),l(P),re(()=>{var Ee;z(te,` ${e(s).jobSearch.title??""}`),z(ve,` ${e(s).jobSearch.location??""}`),z(pe,` ${((Ee=e(s).jobSearch._count)==null?void 0:Ee.results)??0??""} total`)}),r(d,P)},Ze=d=>{var P=ar();r(d,P)};G(Qe,d=>{e(s).jobSearch?d(Xe):d(Ze,!1)})}l(ke);var Ce=t(ke,2),je=t(n(Ce),2),ze=n(je);ae(ze,{size:"sm",onclick:()=>window.location.href=`/dashboard/resumes/${e(s).id}/optimization`,children:(d,P)=>{p();var b=_("Optimization");r(d,b)},$$slots:{default:!0}});var Be=t(ze,2);ae(Be,{size:"sm",onclick:()=>window.location.href=`/dashboard/resumes/${e(s).id}/recommendations`,children:(d,P)=>{p();var b=_("Recommendations");r(d,b)},$$slots:{default:!0}});var Me=t(Be,2);ae(Me,{size:"sm",onclick:()=>window.location.href=`/dashboard/resumes/${e(s).id}/raw`,children:(d,P)=>{p();var b=_("Raw PDF");r(d,b)},$$slots:{default:!0}});var et=t(Me,2);{var tt=d=>{Gt(d,{get resumeId(){return e(s).id},get userData(){return f().user},get isParsed(){return e(s).isParsed}})};G(et,d=>{e(s).isParsed&&d(tt)})}l(je),l(Ce),l(j),re((d,P)=>{var b,te;z(ue,` ${((b=e(s).document)==null?void 0:b.label)||"Unnamed Resume"}`),z(_e,` ${d??""}`),z(Pe,` ${P??""}`),z(Se,e(s).score?`${e(s).score}%`:"N/A"),z(He,` ${((te=e(s).profile)==null?void 0:te.name)??""}`)},[()=>new Date(e(s).createdAt).toLocaleDateString(),()=>{var d,P,b;return((d=e(s).document)==null?void 0:d.fileName)||((b=(P=e(s).document)==null?void 0:P.fileUrl)==null?void 0:b.split("/").pop())||"Unknown"}],ie),r(m,D)};G(U,m=>{e(s)&&m(a)})}r(V,c)},$$slots:{default:!0}}),r(k,O)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),r(E,oe),ye()}export{Mr as component};
