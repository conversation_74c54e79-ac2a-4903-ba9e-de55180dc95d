import{f as o,a,c as ve,t as ne}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as ot,t as C,a as it,f as F,c as t,s as r,n as q,r as e,g as i,m as lt,e as Te,d as dt}from"../chunks/CGmarHxI.js";import{s as c}from"../chunks/CIt1g2O9.js";import{i as B}from"../chunks/u21ee2wt.js";import{e as K,i as M}from"../chunks/C3w0v0gR.js";import{c as vt}from"../chunks/BvdI7LR8.js";import{i as nt}from"../chunks/BIEMS98f.js";import{p as ct}from"../chunks/Btcx8l8F.js";import{B as Le}from"../chunks/B1K98fMG.js";import{B as ce}from"../chunks/DaBofrVv.js";import{A as V,a as X,b as Y,c as Z}from"../chunks/BPr9JIwg.js";import{S as Ce,a as Ue}from"../chunks/C2MdR6K0.js";import{g as pt}from"../chunks/BiJhC7W5.js";import{A as ft}from"../chunks/Ce6y1v79.js";import{U as Fe}from"../chunks/B_6ivTD3.js";import{F as ut}from"../chunks/ChqRiddM.js";import{B as pe}from"../chunks/CDnvByek.js";import{G as We,L as Je}from"../chunks/DQB68x0Z.js";import{W as Re,B as ze}from"../chunks/mCB4pHNc.js";import{A as Ge}from"../chunks/B_tyjpYb.js";var mt=o("<!> Back",1),_t=o("<!> ",1),xt=o('<div class="space-y-1 pr-4"></div> <!>',1),ht=o('<p class="whitespace-pre-wrap"> </p>'),gt=o('<p class="text-muted-foreground italic">No summary provided</p>'),$t=o('<div class="space-y-4"><h2 class="text-xl font-semibold">Profile Overview</h2> <div class="rounded-lg border p-4"><div class="grid grid-cols-1 gap-4 md:grid-cols-2"><div><h3 class="text-muted-foreground text-sm font-medium">Full Name</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Email</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Job Title</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Industry</h3> <p> </p></div></div></div> <h3 class="text-lg font-medium">Summary</h3> <div class="rounded-lg border p-4"><!></div></div>'),bt=o('<div class="flex items-center gap-2"><!> <span class="text-lg font-medium">Contact Details</span></div>'),yt=o('<div class="grid grid-cols-1 gap-4 md:grid-cols-2"><div><h3 class="text-muted-foreground text-sm font-medium">Full Name</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Email</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Phone</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Location</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Website</h3> <p> </p></div></div>'),Pt=o("<!> <!>",1),wt=o('<div class="space-y-4"><h2 class="text-xl font-semibold">Personal Information</h2> <!></div>'),kt=o('<div class="flex items-center gap-2"><!> <span class="text-lg font-medium">Career Preferences</span></div>'),Nt=o('<div><h3 class="text-muted-foreground text-sm font-medium">Desired Roles</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Preferred Locations</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Remote Preference</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Desired Industries</h3> <p> </p></div>',1),Dt=o('<div class="text-muted-foreground col-span-2 italic">No job preferences specified</div>'),jt=o('<div class="grid grid-cols-1 gap-4 md:grid-cols-2"><!></div>'),It=o("<!> <!>",1),At=o('<div class="space-y-4"><h2 class="text-xl font-semibold">Job Preferences</h2> <!></div>'),St=o('<div class="flex items-center gap-2"><!> <span class="text-lg font-medium"> </span></div>'),Et=o('<div class="space-y-2"><div><h3 class="text-muted-foreground text-sm font-medium">Company</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Title</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Duration</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Description</h3> <p class="whitespace-pre-wrap"> </p></div></div>'),Bt=o("<!> <!>",1),Tt=o('<div class="text-muted-foreground rounded-lg border p-4 italic">No work experience added</div>'),Lt=o('<div class="space-y-4"><h2 class="text-xl font-semibold">Work Experience</h2> <!></div>'),Ct=o('<div class="flex items-center gap-2"><!> <span class="text-lg font-medium"> </span></div>'),Ut=o('<div class="space-y-2"><div><h3 class="text-muted-foreground text-sm font-medium">School</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Degree</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Field of Study</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Duration</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Description</h3> <p class="whitespace-pre-wrap"> </p></div></div>'),Ft=o("<!> <!>",1),Wt=o('<div class="text-muted-foreground rounded-lg border p-4 italic">No education added</div>'),Jt=o('<div class="space-y-4"><h2 class="text-xl font-semibold">Education</h2> <!></div>'),Rt=o('<div class="flex items-center gap-2"><!> <span class="text-lg font-medium">Skills & Expertise</span></div>'),zt=o('<div><h3 class="text-muted-foreground mb-2 text-sm font-medium">Technical Skills</h3> <div class="flex flex-wrap gap-2"></div></div>'),Gt=o('<div><h3 class="text-muted-foreground mb-2 text-sm font-medium">Soft Skills</h3> <div class="flex flex-wrap gap-2"></div></div>'),Ot=o('<div><h3 class="text-muted-foreground mb-2 text-sm font-medium">Skills</h3> <div class="flex flex-wrap gap-2"></div></div>'),qt=o('<div class="space-y-4"><!> <!> <!></div>'),Ht=o('<div class="text-muted-foreground italic">No skills specified</div>'),Kt=o("<!> <!>",1),Mt=o('<div class="space-y-4"><h2 class="text-xl font-semibold">Skills</h2> <!></div>'),Qt=o('<div class="flex items-center gap-2"><!> <span class="text-lg font-medium">Language Proficiency</span></div>'),Vt=o('<div class="flex items-center justify-between"><span> </span> <!></div>'),Xt=o('<div class="space-y-2"></div>'),Yt=o("<!> <!>",1),Zt=o('<div class="text-muted-foreground rounded-lg border p-4 italic">No languages added</div>'),er=o('<div class="space-y-4"><h2 class="text-xl font-semibold">Languages</h2> <!></div>'),tr=o('<div class="flex items-center gap-2"><!> <span class="text-lg font-medium">Awards & Achievements</span></div>'),rr=o("<li> </li>"),ar=o('<ul class="list-disc space-y-2 pl-5"></ul>'),sr=o("<!> <!>",1),or=o('<div class="text-muted-foreground rounded-lg border p-4 italic">No achievements added</div>'),ir=o('<div class="space-y-4"><h2 class="text-xl font-semibold">Achievements</h2> <!></div>'),lr=o('<div class="flex items-center gap-2"><!> <span class="text-lg font-medium"> </span></div>'),dr=o('<div class="space-y-2"><div><h3 class="text-muted-foreground text-sm font-medium">Issuing Organization</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Issue Date</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Expiration Date</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Credential ID</h3> <p> </p></div></div>'),vr=o("<!> <!>",1),nr=o('<div class="text-muted-foreground rounded-lg border p-4 italic">No certifications added</div>'),cr=o('<div class="space-y-4"><h2 class="text-xl font-semibold">Certifications</h2> <!></div>'),pr=o('<div class="h-full w-full"><!> <!> <!> <!> <!> <!> <!> <!> <!></div> <!>',1),fr=o('<div class="container mx-auto p-4"><div class="mb-6"><div class="mb-2 flex items-center gap-2"><!> <span class="text-muted-foreground">/</span> <span class="text-muted-foreground max-w-[200px] truncate"> </span></div> <div class="flex items-center justify-between"><h1 class="text-2xl font-bold"> </h1></div></div> <div class="grid grid-cols-1 gap-6 md:grid-cols-4"><div><!></div> <div class="md:col-span-3"><!></div></div></div>');function Lr(Oe,$e){ot($e,!1);let be=ct($e,"data",8);const re=be().profile,s=be().profileData||{},qe=[{id:"profile",label:"Profile",icon:Fe},{id:"personal",label:"Personal Info",icon:ut},{id:"preferences",label:"Job Preferences",icon:pe},{id:"experience",label:"Experience",icon:pe},{id:"education",label:"Education",icon:We},{id:"skills",label:"Skills",icon:Re},{id:"languages",label:"Languages",icon:Je},{id:"achievements",label:"Achievements",icon:Ge},{id:"certifications",label:"Certifications",icon:ze}];let G=lt("profile");function He(ee){dt(G,ee)}function Ke(){pt("/dashboard")}nt();var fe=fr(),ue=t(fe),me=t(ue),ye=t(me);Le(ye,{variant:"ghost",size:"sm",class:"h-auto p-0",onclick:Ke,children:(ee,je)=>{var Q=mt(),H=F(Q);ft(H,{class:"mr-1 h-4 w-4"}),q(),a(ee,Q)},$$slots:{default:!0}});var Pe=r(ye,4),Me=t(Pe,!0);e(Pe),e(me);var we=r(me,2),ke=t(we),Qe=t(ke,!0);e(ke),e(we),e(ue);var Ne=r(ue,2),_e=t(Ne),Ve=t(_e);Ce(Ve,{class:"h-[calc(100vh-200px)]",children:(ee,je)=>{var Q=xt(),H=F(Q);K(H,5,()=>qe,M,(xe,te)=>{const he=Te(()=>i(G)===i(te).id?"default":"ghost");Le(xe,{get variant(){return i(he)},class:"w-full justify-start",onclick:()=>He(i(te).id),children:(oe,Ie)=>{var ae=_t(),ie=F(ae);vt(ie,()=>i(te).icon,(ge,de)=>{de(ge,{class:"mr-2 h-4 w-4"})});var le=r(ie);C(()=>c(le,` ${i(te).label??""}`)),a(oe,ae)},$$slots:{default:!0}})}),e(H);var se=r(H,2);Ue(se,{orientation:"vertical"}),a(ee,Q)},$$slots:{default:!0}}),e(_e);var De=r(_e,2),Xe=t(De);Ce(Xe,{class:"h-[calc(100vh-200px)]",children:(ee,je)=>{var Q=pr(),H=F(Q),se=t(H);{var xe=f=>{var u=$t(),T=r(t(u),2),L=t(T),W=t(L),l=r(t(W),2),k=t(l,!0);e(l),e(W);var U=r(W,2),N=r(t(U),2),z=t(N,!0);e(N),e(U);var y=r(U,2),d=r(t(y),2),_=t(d,!0);e(d),e(y);var $=r(y,2),J=r(t($),2),h=t(J,!0);e(J),e($),e(L),e(T);var p=r(T,4),P=t(p);{var b=v=>{var n=ht(),x=t(n,!0);e(n),C(()=>{var j;return c(x,s.summary||((j=s.personalInfo)==null?void 0:j.summary))}),a(v,n)},D=v=>{var n=gt();a(v,n)};B(P,v=>{var n;s.summary||(n=s.personalInfo)!=null&&n.summary?v(b):v(D,!1)})}e(p),e(u),C(()=>{var v,n,x;c(k,s.fullName||((v=s.personalInfo)==null?void 0:v.fullName)||"Not specified"),c(z,s.email||((n=s.personalInfo)==null?void 0:n.email)||"Not specified"),c(_,s.jobType||((x=s.personalInfo)==null?void 0:x.jobTitle)||"Not specified"),c(h,s.industry||"Not specified")}),a(f,u)};B(se,f=>{i(G)==="profile"&&f(xe)})}var te=r(se,2);{var he=f=>{var u=wt(),T=r(t(u),2);V(T,{type:"single",collapsible:!0,children:(L,W)=>{X(L,{value:"personal-info",children:(l,k)=>{var U=Pt(),N=F(U);Y(N,{class:"flex w-full items-center justify-between px-4 py-2",children:(y,d)=>{var _=bt(),$=t(_);Fe($,{class:"h-5 w-5"}),q(2),e(_),a(y,_)},$$slots:{default:!0}});var z=r(N,2);Z(z,{class:"px-4 pb-4",children:(y,d)=>{var _=yt(),$=t(_),J=r(t($),2),h=t(J,!0);e(J),e($);var p=r($,2),P=r(t(p),2),b=t(P,!0);e(P),e(p);var D=r(p,2),v=r(t(D),2),n=t(v,!0);e(v),e(D);var x=r(D,2),j=r(t(x),2),g=t(j,!0);e(j),e(x);var m=r(x,2),I=r(t(m),2),A=t(I,!0);e(I),e(m),e(_),C(()=>{var E,R,w,S,O;c(h,s.fullName||((E=s.personalInfo)==null?void 0:E.fullName)||"Not specified"),c(b,s.email||((R=s.personalInfo)==null?void 0:R.email)||"Not specified"),c(n,s.phone||((w=s.personalInfo)==null?void 0:w.phone)||"Not specified"),c(g,s.location||((S=s.personalInfo)==null?void 0:S.location)||"Not specified"),c(A,s.website||((O=s.personalInfo)==null?void 0:O.website)||"Not specified")}),a(y,_)},$$slots:{default:!0}}),a(l,U)},$$slots:{default:!0}})},$$slots:{default:!0}}),e(u),a(f,u)};B(te,f=>{i(G)==="personal"&&f(he)})}var oe=r(te,2);{var Ie=f=>{var u=At(),T=r(t(u),2);V(T,{type:"single",collapsible:!0,children:(L,W)=>{X(L,{value:"job-preferences",children:(l,k)=>{var U=It(),N=F(U);Y(N,{class:"flex w-full items-center justify-between px-4 py-2",children:(y,d)=>{var _=kt(),$=t(_);pe($,{class:"h-5 w-5"}),q(2),e(_),a(y,_)},$$slots:{default:!0}});var z=r(N,2);Z(z,{class:"px-4 pb-4",children:(y,d)=>{var _=jt(),$=t(_);{var J=p=>{var P=Nt(),b=F(P),D=r(t(b),2),v=t(D,!0);e(D),e(b);var n=r(b,2),x=r(t(n),2),j=t(x,!0);e(x),e(n);var g=r(n,2),m=r(t(g),2),I=t(m,!0);e(m),e(g);var A=r(g,2),E=r(t(A),2),R=t(E,!0);e(E),e(A),C((w,S,O)=>{c(v,w),c(j,S),c(I,s.jobPreferences.remotePreference||"Not specified"),c(R,O)},[()=>{var w;return((w=s.jobPreferences.interestedRoles)==null?void 0:w.join(", "))||"Not specified"},()=>{var w;return((w=s.jobPreferences.preferredLocations)==null?void 0:w.join(", "))||"Not specified"},()=>{var w;return((w=s.jobPreferences.desiredIndustries)==null?void 0:w.join(", "))||"Not specified"}],Te),a(p,P)},h=p=>{var P=Dt();a(p,P)};B($,p=>{s.jobPreferences?p(J):p(h,!1)})}e(_),a(y,_)},$$slots:{default:!0}}),a(l,U)},$$slots:{default:!0}})},$$slots:{default:!0}}),e(u),a(f,u)};B(oe,f=>{i(G)==="preferences"&&f(Ie)})}var ae=r(oe,2);{var ie=f=>{var u=Lt(),T=r(t(u),2);{var L=l=>{V(l,{type:"multiple",children:(k,U)=>{var N=ve(),z=F(N);K(z,1,()=>s.workExperience,M,(y,d,_)=>{X(y,{value:`exp-${_}`,children:($,J)=>{var h=Bt(),p=F(h);Y(p,{class:"flex w-full items-center justify-between px-4 py-2",children:(b,D)=>{var v=St(),n=t(v);pe(n,{class:"h-5 w-5"});var x=r(n,2),j=t(x);e(x),e(v),C(()=>c(j,`${(i(d).title||i(d).jobTitle)??""} at ${i(d).company??""}`)),a(b,v)},$$slots:{default:!0}});var P=r(p,2);Z(P,{class:"px-4 pb-4",children:(b,D)=>{var v=Et(),n=t(v),x=r(t(n),2),j=t(x,!0);e(x),e(n);var g=r(n,2),m=r(t(g),2),I=t(m,!0);e(m),e(g);var A=r(g,2),E=r(t(A),2),R=t(E);e(E),e(A);var w=r(A,2),S=r(t(w),2),O=t(S,!0);e(S),e(w),e(v),C(()=>{c(j,i(d).company||"Not specified"),c(I,i(d).title||i(d).jobTitle||"Not specified"),c(R,`${i(d).startDate||"Unknown"} - ${(i(d).current?"Present":i(d).endDate||"Unknown")??""}`),c(O,i(d).description||"No description provided")}),a(b,v)},$$slots:{default:!0}}),a($,h)},$$slots:{default:!0}})}),a(k,N)},$$slots:{default:!0}})},W=l=>{var k=Tt();a(l,k)};B(T,l=>{s.workExperience&&s.workExperience.length>0?l(L):l(W,!1)})}e(u),a(f,u)};B(ae,f=>{i(G)==="experience"&&f(ie)})}var le=r(ae,2);{var ge=f=>{var u=Jt(),T=r(t(u),2);{var L=l=>{V(l,{type:"multiple",children:(k,U)=>{var N=ve(),z=F(N);K(z,1,()=>s.education,M,(y,d,_)=>{X(y,{value:`edu-${_}`,children:($,J)=>{var h=Ft(),p=F(h);Y(p,{class:"flex w-full items-center justify-between px-4 py-2",children:(b,D)=>{var v=Ct(),n=t(v);We(n,{class:"h-5 w-5"});var x=r(n,2),j=t(x);e(x),e(v),C(()=>c(j,`${i(d).degree||"Degree"} at ${(i(d).school||i(d).institution)??""}`)),a(b,v)},$$slots:{default:!0}});var P=r(p,2);Z(P,{class:"px-4 pb-4",children:(b,D)=>{var v=Ut(),n=t(v),x=r(t(n),2),j=t(x,!0);e(x),e(n);var g=r(n,2),m=r(t(g),2),I=t(m,!0);e(m),e(g);var A=r(g,2),E=r(t(A),2),R=t(E,!0);e(E),e(A);var w=r(A,2),S=r(t(w),2),O=t(S);e(S),e(w);var Ee=r(w,2),Be=r(t(Ee),2),st=t(Be,!0);e(Be),e(Ee),e(v),C(()=>{c(j,i(d).school||i(d).institution||"Not specified"),c(I,i(d).degree||"Not specified"),c(R,i(d).field||"Not specified"),c(O,`${i(d).startDate||"Unknown"} - ${(i(d).current?"Present":i(d).endDate||"Unknown")??""}`),c(st,i(d).description||"No description provided")}),a(b,v)},$$slots:{default:!0}}),a($,h)},$$slots:{default:!0}})}),a(k,N)},$$slots:{default:!0}})},W=l=>{var k=Wt();a(l,k)};B(T,l=>{s.education&&s.education.length>0?l(L):l(W,!1)})}e(u),a(f,u)};B(le,f=>{i(G)==="education"&&f(ge)})}var de=r(le,2);{var Ye=f=>{var u=Mt(),T=r(t(u),2);V(T,{type:"single",collapsible:!0,children:(L,W)=>{X(L,{value:"skills",children:(l,k)=>{var U=Kt(),N=F(U);Y(N,{class:"flex w-full items-center justify-between px-4 py-2",children:(y,d)=>{var _=Rt(),$=t(_);Re($,{class:"h-5 w-5"}),q(2),e(_),a(y,_)},$$slots:{default:!0}});var z=r(N,2);Z(z,{class:"px-4 pb-4",children:(y,d)=>{var _=ve(),$=F(_);{var J=p=>{var P=qt(),b=t(P);{var D=g=>{var m=zt(),I=r(t(m),2);K(I,5,()=>s.skillsData.technical,M,(A,E)=>{ce(A,{variant:"secondary",children:(R,w)=>{q();var S=ne();C(()=>c(S,i(E))),a(R,S)},$$slots:{default:!0}})}),e(I),e(m),a(g,m)};B(b,g=>{var m;(m=s.skillsData)!=null&&m.technical&&s.skillsData.technical.length>0&&g(D)})}var v=r(b,2);{var n=g=>{var m=Gt(),I=r(t(m),2);K(I,5,()=>s.skillsData.soft,M,(A,E)=>{ce(A,{variant:"secondary",children:(R,w)=>{q();var S=ne();C(()=>c(S,i(E))),a(R,S)},$$slots:{default:!0}})}),e(I),e(m),a(g,m)};B(v,g=>{var m;(m=s.skillsData)!=null&&m.soft&&s.skillsData.soft.length>0&&g(n)})}var x=r(v,2);{var j=g=>{var m=Ot(),I=r(t(m),2);K(I,5,()=>Array.isArray(s.skills)?s.skills:[s.skills],M,(A,E)=>{ce(A,{variant:"secondary",children:(R,w)=>{q();var S=ne();C(()=>c(S,i(E))),a(R,S)},$$slots:{default:!0}})}),e(I),e(m),a(g,m)};B(x,g=>{var m,I;(!((m=s.skillsData)!=null&&m.technical)||s.skillsData.technical.length===0)&&(!((I=s.skillsData)!=null&&I.soft)||s.skillsData.soft.length===0)&&s.skills&&g(j)})}e(P),a(p,P)},h=p=>{var P=Ht();a(p,P)};B($,p=>{s.skills||s.skillsData?p(J):p(h,!1)})}a(y,_)},$$slots:{default:!0}}),a(l,U)},$$slots:{default:!0}})},$$slots:{default:!0}}),e(u),a(f,u)};B(de,f=>{i(G)==="skills"&&f(Ye)})}var Ae=r(de,2);{var Ze=f=>{var u=er(),T=r(t(u),2);{var L=l=>{V(l,{type:"single",collapsible:!0,children:(k,U)=>{X(k,{value:"languages",children:(N,z)=>{var y=Yt(),d=F(y);Y(d,{class:"flex w-full items-center justify-between px-4 py-2",children:($,J)=>{var h=Qt(),p=t(h);Je(p,{class:"h-5 w-5"}),q(2),e(h),a($,h)},$$slots:{default:!0}});var _=r(d,2);Z(_,{class:"px-4 pb-4",children:($,J)=>{var h=Xt();K(h,5,()=>s.languages,M,(p,P)=>{var b=Vt(),D=t(b),v=t(D,!0);e(D);var n=r(D,2);ce(n,{children:(x,j)=>{q();var g=ne();C(()=>c(g,i(P).proficiency)),a(x,g)},$$slots:{default:!0}}),e(b),C(()=>c(v,i(P).name)),a(p,b)}),e(h),a($,h)},$$slots:{default:!0}}),a(N,y)},$$slots:{default:!0}})},$$slots:{default:!0}})},W=l=>{var k=Zt();a(l,k)};B(T,l=>{s.languages&&s.languages.length>0?l(L):l(W,!1)})}e(u),a(f,u)};B(Ae,f=>{i(G)==="languages"&&f(Ze)})}var Se=r(Ae,2);{var et=f=>{var u=ir(),T=r(t(u),2);{var L=l=>{V(l,{type:"single",collapsible:!0,children:(k,U)=>{X(k,{value:"achievements",children:(N,z)=>{var y=sr(),d=F(y);Y(d,{class:"flex w-full items-center justify-between px-4 py-2",children:($,J)=>{var h=tr(),p=t(h);Ge(p,{class:"h-5 w-5"}),q(2),e(h),a($,h)},$$slots:{default:!0}});var _=r(d,2);Z(_,{class:"px-4 pb-4",children:($,J)=>{var h=ar();K(h,5,()=>s.achievements,M,(p,P)=>{var b=rr(),D=t(b,!0);e(b),C(()=>c(D,i(P).title||i(P))),a(p,b)}),e(h),a($,h)},$$slots:{default:!0}}),a(N,y)},$$slots:{default:!0}})},$$slots:{default:!0}})},W=l=>{var k=or();a(l,k)};B(T,l=>{s.achievements&&s.achievements.length>0?l(L):l(W,!1)})}e(u),a(f,u)};B(Se,f=>{i(G)==="achievements"&&f(et)})}var tt=r(Se,2);{var rt=f=>{var u=cr(),T=r(t(u),2);{var L=l=>{V(l,{type:"multiple",children:(k,U)=>{var N=ve(),z=F(N);K(z,1,()=>s.certifications,M,(y,d,_)=>{X(y,{value:`cert-${_}`,children:($,J)=>{var h=vr(),p=F(h);Y(p,{class:"flex w-full items-center justify-between px-4 py-2",children:(b,D)=>{var v=lr(),n=t(v);ze(n,{class:"h-5 w-5"});var x=r(n,2),j=t(x,!0);e(x),e(v),C(()=>c(j,i(d).name)),a(b,v)},$$slots:{default:!0}});var P=r(p,2);Z(P,{class:"px-4 pb-4",children:(b,D)=>{var v=dr(),n=t(v),x=r(t(n),2),j=t(x,!0);e(x),e(n);var g=r(n,2),m=r(t(g),2),I=t(m,!0);e(m),e(g);var A=r(g,2),E=r(t(A),2),R=t(E,!0);e(E),e(A);var w=r(A,2),S=r(t(w),2),O=t(S,!0);e(S),e(w),e(v),C(()=>{c(j,i(d).issuer||"Not specified"),c(I,i(d).issueDate||"Not specified"),c(R,i(d).expirationDate||"No expiration"),c(O,i(d).credentialId||"Not specified")}),a(b,v)},$$slots:{default:!0}}),a($,h)},$$slots:{default:!0}})}),a(k,N)},$$slots:{default:!0}})},W=l=>{var k=nr();a(l,k)};B(T,l=>{s.certifications&&s.certifications.length>0?l(L):l(W,!1)})}e(u),a(f,u)};B(tt,f=>{i(G)==="certifications"&&f(rt)})}e(H);var at=r(H,2);Ue(at,{orientation:"vertical"}),a(ee,Q)},$$slots:{default:!0}}),e(De),e(Ne),e(fe),C(()=>{c(Me,(re==null?void 0:re.name)||"Profile"),c(Qe,(re==null?void 0:re.name)||"Profile")}),a(Oe,fe),it()}export{Lr as component};
