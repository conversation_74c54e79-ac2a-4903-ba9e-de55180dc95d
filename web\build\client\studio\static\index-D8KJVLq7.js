import{a as I,r as H,b3 as G,b4 as R,j as u,N as T,p as g,v as w,y as A,b5 as L,Q as V,ab as K,b6 as N,B as Q,b7 as W}from"./sanity-DV0NwVOn.js";const _=g(Q)`
  position: relative;
`;function q(d){const t=I.c(3),{children:o}=d,{collapsed:s}=N();let e;return t[0]!==o||t[1]!==s?(e=u.jsx(_,{hidden:s,height:"fill",overflow:"auto",children:o}),t[0]=o,t[1]=s,t[2]=e):e=t[2],e}function z(d){const t=I.c(11),{actionHandlers:o,index:s,menuItems:e,menuItemGroups:a,title:n}=d,{features:c}=w();if(!(e!=null&&e.length)&&!n)return null;let l;t[0]!==o||t[1]!==a||t[2]!==e?(l=u.jsx(W,{menuItems:e,menuItemGroups:a,actionHandlers:o}),t[0]=o,t[1]=a,t[2]=e,t[3]=l):l=t[3];let r;t[4]!==c.backButton||t[5]!==s?(r=c.backButton&&s>0&&u.jsx(A,{as:V,"data-as":"a",icon:L,mode:"bleed",tooltipProps:{content:"Back"}}),t[4]=c.backButton,t[5]=s,t[6]=r):r=t[6];let i;return t[7]!==l||t[8]!==r||t[9]!==n?(i=u.jsx(K,{actions:l,backButton:r,title:n}),t[7]=l,t[8]=r,t[9]=n,t[10]=i):i=t[10],i}function J(d){const t=I.c(37);let o,s,e,a;t[0]!==d?({index:o,pane:s,paneKey:e,...a}=d,t[0]=d,t[1]=o,t[2]=s,t[3]=e,t[4]=a):(o=t[1],s=t[2],e=t[3],a=t[4]);let n,c,l,r,i;if(t[5]!==s){const{child:C,component:S,menuItems:v,menuItemGroups:U,type:D,...$}=s;c=C,n=S,r=v,l=U,i=$,t[5]=s,t[6]=n,t[7]=c,t[8]=l,t[9]=r,t[10]=i}else n=t[6],c=t[7],l=t[8],r=t[9],i=t[10];const[j,y]=H.useState(null),{title:E}=G(s),B=E===void 0?"":E;let p,m;t[11]!==i||t[12]!==a?({key:m,...p}={...a,...i},t[11]=i,t[12]=a,t[13]=p,t[14]=m):(p=t[13],m=t[14]);const k=j==null?void 0:j.actionHandlers;let x;t[15]!==o||t[16]!==l||t[17]!==r||t[18]!==k||t[19]!==B?(x=u.jsx(z,{actionHandlers:k,index:o,menuItems:r,menuItemGroups:l,title:B}),t[15]=o,t[16]=l,t[17]=r,t[18]=k,t[19]=B,t[20]=x):x=t[20];let f;t[21]!==n||t[22]!==c||t[23]!==p||t[24]!==m||t[25]!==e?(f=R.isValidElementType(n)&&u.jsx(n,{...p,ref:y,child:c,paneKey:e},m),t[21]=n,t[22]=c,t[23]=p,t[24]=m,t[25]=e,t[26]=f):f=t[26];let b;t[27]!==n?(b=H.isValidElement(n)&&n,t[27]=n,t[28]=b):b=t[28];let P;t[29]!==f||t[30]!==b?(P=u.jsxs(q,{children:[f,b]}),t[29]=f,t[30]=b,t[31]=P):P=t[31];let h;return t[32]!==e||t[33]!==a.isSelected||t[34]!==x||t[35]!==P?(h=u.jsxs(T,{id:e,minWidth:320,selected:a.isSelected,children:[x,P]}),t[32]=e,t[33]=a.isSelected,t[34]=x,t[35]=P,t[36]=h):h=t[36],h}export{J as default};
