import{f as I,a as y,t as A,c as we}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as ie}from"../chunks/nZgk9enP.js";import{p as xe,m as b,l as _e,b as Pe,f as R,a as je,s as x,g as r,e as Se,c as Q,d as l,ab as $e,n as se,r as V,t as Je}from"../chunks/CGmarHxI.js";import{s as Te}from"../chunks/CIt1g2O9.js";import{i as H}from"../chunks/u21ee2wt.js";import{e as z}from"../chunks/CmxjS0TN.js";import{i as Ue}from"../chunks/BIEMS98f.js";import{p as ke}from"../chunks/Btcx8l8F.js";import{t as v}from"../chunks/DjPYYl4Z.js";import{S as Le}from"../chunks/C6g8ubaU.js";import{J as Ae,a as Re}from"../chunks/DV_57wcZ.js";import{R as Ie,a as Fe}from"../chunks/tdzGgazS.js";import{g as ae}from"../chunks/BiJhC7W5.js";import{g as Ee,J as Ce}from"../chunks/9r-6KH_O.js";import{D as De,a as qe,b as Oe}from"../chunks/CKh8VGVX.js";var Ye=I(`<div class="mt-4 rounded-lg bg-yellow-50 p-4 text-center text-sm text-yellow-800"><p> <a href="/auth/sign-in" class="font-medium text-blue-600 hover:underline">Sign in</a> for unlimited
      searches.</p></div>`),Be=I("<!> <!>",1),Me=I('<!> <div class="flex justify-end gap-4"><button class="border-input bg-background ring-offset-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Cancel</button> <button class="bg-primary text-primary-foreground ring-offset-background hover:bg-primary/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Sign In</button> <button class="border-input bg-background ring-offset-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Sign Up</button></div>',1),Ne=I("<!> <!> <!> <!> <!>",1);function at(le,K){xe(K,!1);let h=ke(K,"data",8),P=b([]),F=b(!1),E=b(!1),C=1,W=b(h().totalJobCount||0),U=b(h().selectedJob||null),u=b(h().searchParams||{title:"",locations:[],locationType:[],experience:[],category:[],education:[],salary:"",state:"",country:"US"}),k=b(!1),D=b(""),_=b(0),L;async function j(e={},o=!1){var n,t,f,g,m,w;console.log("loadJobs called with params:",e),o&&(l(P,[]),C=1),l(F,!0);try{const i={page:C,limit:20};if(e.title&&(i.title=e.title),(n=e.locations)!=null&&n.length)try{const d=e.locations.map(c=>{if(c.includes("|")){const[Y,S,B,M]=c.split("|");return{id:Y,name:S,stateCode:B,country:M}}else return console.log("Location not in expected format:",c),{id:c,name:c,stateCode:"",country:"US"}});console.log("Parsed location data:",d),i.locations=d.map(c=>c.id),d.length>0&&(i.location=`${d[0].name}, ${d[0].stateCode}`)}catch(d){console.error("Error parsing locations:",d,e.locations)}else e.location&&(i.location=e.location);(t=e.locationType)!=null&&t.length&&(i.locationType=e.locationType),(f=e.experience)!=null&&f.length&&(i.experienceLevel=e.experience),e.salary&&(i.salary=e.salary),e.state&&(i.state=e.state),e.country&&(i.country=e.country),e.collection&&(i.collection=e.collection),(g=e.companies)!=null&&g.length&&(console.log("Adding companies to filter:",e.companies),i.companies=e.companies);const p=await Ee(Ce,{filter:i});if(p.errors)throw console.error("GraphQL errors:",p.errors),new Error(`Failed to fetch jobs: ${p.errors[0].message}`);const s=(m=p.data)==null?void 0:m.jobListings,a=(s==null?void 0:s.jobs)||[];l(W,((w=s==null?void 0:s.pagination)==null?void 0:w.totalCount)||0),o?l(P,a):l(P,[...r(P),...a]);const T=new URL(window.location.href).searchParams.get("jobId");if(T&&!r(U)){const d=r(P).find(c=>c.id===T);d&&l(U,d)}return a}catch(i){return console.error("Error loading jobs:",i),v.error(`Failed to load jobs: ${i instanceof Error?i.message:"Unknown error"}`),[]}finally{l(F,!1)}}async function X(e){if(!(e.title||e.locations&&e.locations.length>0||e.locationType&&e.locationType.length>0||e.experience&&e.experience.length>0||e.salary))return v.warning("Please enter at least one search filter"),Promise.resolve([]);if(!h().user){if(r(_)>0)return v.error(`Please wait ${r(_)} seconds before searching again`),Promise.resolve([]);l(_,60),ce()}l(u,e),l(E,!0);try{Z(e);const n=await j(e,!0);return n.length===0?v.info("No jobs found matching your criteria. Try broadening your search."):v.success(`Found ${n.length} jobs matching your search`),n}catch(n){return console.error("Search error:",n),v.error(`Search failed: ${n instanceof Error?n.message:"Unknown error"}`),[]}finally{l(E,!1)}}function ce(){clearInterval(L),L=setInterval(()=>{$e(_,-1),r(_)<=0&&clearInterval(L)},1e3)}async function de(){return C++,await j(r(u))}function q(e){l(D,e),l(k,!0)}function ue(e){if(!h().user){q("apply");return}v.success("Application started",{description:"Tracking this application in your dashboard"})}async function fe(e){if(!h().user){q("save");return}try{const o=await fetch(`/api/jobs/${e.id}/save`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({notes:""})}),n=await o.json();if(!o.ok)throw new Error(n.error||"Failed to save job");return v.success("Job saved",{description:"Added to your saved jobs"}),!0}catch(o){return console.error("Error saving job:",o),v.error("Failed to save job"),!1}}function ge(e){l(U,e)}function Z(e){var m,w,i,p;console.log("Updating URL with params:",e);const o=new URL(window.location.href),n=new URLSearchParams(o.search),t=new URLSearchParams,f=["title","locations","locationType","experience","salary","state","country","datePosted","easyApply","companies"];for(const[s,a]of n.entries())f.includes(s)||t.append(s,a);for(const s of f)n.has(s)&&e[s]===void 0&&t.set(s,n.get(s));if(e.title!==void 0&&(e.title?t.set("title",e.title):t.delete("title")),e.locations!==void 0)if((m=e.locations)!=null&&m.length){const s=e.locations.map(a=>typeof a=="string"?a:a.id?a.id:a);t.set("locations",s.join(","))}else t.delete("locations");if(e.locationType!==void 0&&((w=e.locationType)!=null&&w.length?t.set("locationType",e.locationType.join(",")):t.delete("locationType")),e.experience!==void 0&&((i=e.experience)!=null&&i.length?t.set("experience",e.experience.join(",")):t.delete("experience")),e.salary!==void 0&&(e.salary?t.set("salary",e.salary):t.delete("salary")),e.datePosted!==void 0&&(e.datePosted?t.set("datePosted",e.datePosted):t.delete("datePosted")),e.easyApply!==void 0&&(e.easyApply===!0?t.set("easyApply","true"):t.delete("easyApply")),e.companies!==void 0)if((p=e.companies)!=null&&p.length){const s=e.companies.map(a=>typeof a=="string"?a:a.id?a.id:a);console.log("Formatted company values:",s),t.set("companies",s.join(","))}else t.delete("companies");e.state!==void 0&&(e.state?t.set("state",e.state):t.delete("state")),e.country!==void 0&&(e.country?t.set("country",e.country):t.delete("country"));const g=`${o.origin}${o.pathname}?${t.toString()}`;console.log("Updated URL:",g),window.history.replaceState({},"",g)}ie(()=>((async()=>{if(Object.values(r(u)).some(o=>o&&(!Array.isArray(o)||o.length>0)))await j(r(u),!0);else{const n=new URL(window.location.href).searchParams.get("collection");n?await j({collection:n},!0):await j({},!0)}})(),()=>{clearInterval(L)})),ie(()=>{const e=()=>{var t,f,g;const o=new URL(window.location.href),n={title:o.searchParams.get("title")||"",locations:((t=o.searchParams.get("locations"))==null?void 0:t.split(",").filter(Boolean))||[],locationType:((f=o.searchParams.get("locationType"))==null?void 0:f.split(",").filter(Boolean))||[],experience:((g=o.searchParams.get("experience"))==null?void 0:g.split(",").filter(Boolean))||[],salary:o.searchParams.get("salary")||"",state:o.searchParams.get("state")||"",country:o.searchParams.get("country")||"US",category:[],education:[]};l(u,n),j(n,!0)};return window.addEventListener("popstate",e),()=>{window.removeEventListener("popstate",e)}}),_e(()=>r(u),()=>{r(u)&&Object.values(r(u)).some(e=>e&&(!Array.isArray(e)||e.length>0))&&typeof window<"u"&&Z(r(u))}),Pe(),Ue();var ee=Ne(),te=R(ee);Le(te,{title:"Job Search | Hirli",description:"Search for jobs that match your skills and experience. Find remote, hybrid, and on-site opportunities across various industries.",keywords:"job search, job listings, career opportunities, remote jobs, job board"});var oe=x(te,2);Ae(oe,{onSearch:X,get isSearching(){return r(E)},get initialParams(){return r(u)},get user(){return h().user}});var ne=x(oe,2);const he=Se(()=>!!h().user);Re(ne,{get jobs(){return r(P)},get isAuthenticated(){return r(he)},get isLoading(){return r(F)},onLoadMore:de,onApply:ue,onSave:fe,onSignInRequired:q,get selectedJob(){return r(U)},onSelectJob:ge,get searchParams(){return r(u)},get totalJobCount(){return r(W)},onFilterChange:X});var re=x(ne,2);{var pe=e=>{var o=Ye(),n=Q(o),t=Q(n);se(2),V(n),V(o),Je(()=>Te(t,`You can search again in ${r(_)??""} seconds. `)),y(e,o)};H(re,e=>{!h().user&&r(_)>0&&e(pe)})}var ye=x(re,2);Ie(ye,{get open(){return r(k)},set open(e){l(k,e)},children:(e,o)=>{Fe(e,{children:(n,t)=>{var f=Me(),g=R(f);De(g,{children:(s,a)=>{var O=Be(),T=R(O);qe(T,{children:(c,Y)=>{se();var S=A("Sign in required");y(c,S)},$$slots:{default:!0}});var d=x(T,2);Oe(d,{children:(c,Y)=>{var S=we(),B=R(S);{var M=$=>{var N=A("You need to sign in to apply for jobs and track your applications.");y($,N)},be=($,N)=>{{var ve=J=>{var G=A("You need to sign in to save jobs to your profile.");y(J,G)},me=J=>{var G=A("You need to sign in to access this feature.");y(J,G)};H($,J=>{r(D)==="save"?J(ve):J(me,!1)},N)}};H(B,$=>{r(D)==="apply"?$(M):$(be,!1)})}y(c,S)},$$slots:{default:!0}}),y(s,O)},$$slots:{default:!0}});var m=x(g,2),w=Q(m),i=x(w,2),p=x(i,2);V(m),z("click",w,()=>l(k,!1)),z("click",i,()=>ae("/auth/sign-in")),z("click",p,()=>ae("/auth/sign-up")),y(n,f)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),y(le,ee),je()}export{at as component};
