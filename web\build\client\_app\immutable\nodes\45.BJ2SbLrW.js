import{f as d,a as e,c as L,t as T}from"../chunks/BasJTneF.js";import{o as Tr}from"../chunks/nZgk9enP.js";import{p as Or,f as n,a as Ur,s as l,d as z,k as Y,g as o,c as A,r as F,n as y,t as ar,x as ur}from"../chunks/CGmarHxI.js";import{s as tr}from"../chunks/CIt1g2O9.js";import{i as O}from"../chunks/u21ee2wt.js";import{k as Br}from"../chunks/DT9WCdWY.js";import{e as Dr,i as Gr}from"../chunks/C3w0v0gR.js";import{c as s}from"../chunks/BvdI7LR8.js";import{C as sr}from"../chunks/DuGukytH.js";import{C as or}from"../chunks/Cdn-N1RY.js";import{C as ir}from"../chunks/BkJY4La4.js";import{C as nr}from"../chunks/GwmmX_iF.js";import{C as lr}from"../chunks/D50jIuLr.js";import{B as Q}from"../chunks/B1K98fMG.js";import{I as Lr}from"../chunks/DMTMHyMa.js";import"../chunks/CgXBgsce.js";import{t as $}from"../chunks/DjPYYl4Z.js";import{g as dr}from"../chunks/BiJhC7W5.js";import{S as Nr}from"../chunks/C6g8ubaU.js";import{A as qr}from"../chunks/Cs0qIT7f.js";import{P as pr,S as Hr}from"../chunks/tjBMsfLi.js";import{A as _r}from"../chunks/B-l1ubNa.js";import{M as Jr}from"../chunks/yPulTJ2h.js";import{B as Kr}from"../chunks/hA0h0kTo.js";import{L as Vr}from"../chunks/BhzFx1Wy.js";var Yr=d('<div class="flex h-64 items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>'),Qr=d('<div class="flex items-center gap-2"><div class="bg-primary/10 rounded-full p-2"><div class="text-primary h-4 w-4"><!></div></div> <!></div> <!>',1),Rr=d(" <!>",1),Wr=d("<!> <!>",1),Xr=d("<!> <!>",1),Zr=d('<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>'),re=d("<!> Grant Admin Access",1),ee=d('<div class="flex items-end gap-4"><div class="flex-1"><label for="email" class="mb-1 block text-sm font-medium">User Email</label> <!></div> <!></div>'),ae=d("<!> <!>",1),te=d("<!> <!>",1),se=d("<!> Initializing...",1),oe=d("<!> Manage Features",1),ie=d("<!> View Feature Usage",1),ne=d(`<div class="space-y-4"><p class="text-muted-foreground text-sm">Initialize feature data for the application. This will create the necessary feature
            records in the database for tracking usage.</p> <div class="flex flex-col gap-2 sm:flex-row"><!> <!> <!></div></div>`),le=d("<!> <!>",1),de=d('<div class="border-border flex flex-col gap-1 border-b p-4"><h2 class="text-xl font-semibold">Admin Features</h2> <p class="text-muted-foreground mt-1">Access administrative features and tools</p></div> <div class="grid divide-x divide-y md:grid-cols-2 lg:grid-cols-3"><!> <!> <!></div>',1),ce=d(`<div class="flex h-64 flex-col items-center justify-center"><!> <h2 class="mb-2 text-2xl font-bold">Access Denied</h2> <p class="text-muted-foreground mb-4">You don't have permission to access admin settings.</p> <!></div>`),me=d("<!> <!>",1);function Ue(gr,$r){Or($r,!0);const hr=[{title:"Plan Management",description:"Configure subscription plans and features",icon:pr,href:"/dashboard/settings/admin/plans"},{title:"Feature Usage",description:"Monitor and analyze feature usage across all users",icon:_r,href:"/dashboard/settings/admin/feature-usage"},{title:"Email Management",description:"Configure email templates and settings",icon:Jr,href:"/dashboard/settings/admin/email"},{title:"Notifications",description:"Send and manage notifications to users",icon:Kr,href:"/dashboard/settings/admin/notifications"}];let N=Y(!1);async function xr(){if(!o(N)){z(N,!0);try{console.log("Starting feature data initialization from admin page..."),$.loading("Initializing feature data...");const r=await fetch("/api/admin/initialize-features",{method:"POST"});if(console.log("Initialize features response status:",r.status),!r.ok){console.error("Initialize features response not OK:",r.status,r.statusText),$.dismiss(),$.error(`Failed to initialize feature data: ${r.statusText}`);return}const p=await r.json();console.log("Initialize features result:",p),p.success?($.dismiss(),$.success("Feature data initialized successfully!"),console.log("Feature initialization results:",p.results)):($.dismiss(),$.error(`Failed to initialize feature data: ${p.error}`))}catch(r){console.error("Error initializing feature data:",r),r instanceof Error&&(console.error("Error name:",r.name),console.error("Error message:",r.message),console.error("Error stack:",r.stack)),$.dismiss(),$.error(`Error initializing feature data: ${r.message||"Unknown error"}`)}finally{z(N,!1)}}}let cr=Y(!1),X=Y(!0),U=Y(""),R=Y(!1);Tr(async()=>{await br()});async function br(){z(X,!0);try{const r=await fetch("/api/user/me");if(r.ok){const p=await r.json();z(cr,p.isAdmin===!0)}}catch(r){console.error("Error checking admin status:",r)}finally{z(X,!1)}}async function yr(){if(!o(U)){$.error("Email is required");return}z(R,!0);try{const r=await fetch("/api/user/set-admin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:o(U),isAdmin:!0})});if(r.ok){const p=await r.json();$.success(`${p.email} is now an admin`),z(U,"")}else{const p=await r.json();$.error(p.error||"Failed to set admin status")}}catch(r){console.error("Error setting admin status:",r),$.error("Failed to set admin status")}finally{z(R,!1)}}var mr=me(),fr=n(mr);Nr(fr,{title:"Admin Settings - Hirli"});var Pr=l(fr,2);{var wr=r=>{var p=Yr();e(r,p)},kr=(r,p)=>{{var zr=B=>{var D=de(),q=l(n(D),2),W=A(q);Dr(W,17,()=>hr,Gr,(H,h)=>{var G=L(),rr=n(G);s(rr,()=>sr,(I,M)=>{M(I,{children:(J,C)=>{var P=Wr(),w=n(P);s(w,()=>nr,(c,f)=>{f(c,{children:(x,_)=>{var m=Qr(),a=n(m),i=A(a),t=A(i),j=A(t);{var g=v=>{var u=L(),k=n(u);Br(k,()=>o(h).icon,V=>{var S=L(),Cr=n(S);{var Er=er=>{var vr=L();const Sr=ur(()=>o(h).icon);var Ir=n(vr);s(Ir,()=>o(Sr),(Mr,jr)=>{jr(Mr,{})}),e(er,vr)};O(Cr,er=>{typeof o(h).icon=="function"&&er(Er)})}e(V,S)}),e(v,u)};O(j,v=>{o(h).icon&&v(g)})}F(t),F(i);var b=l(i,2);s(b,()=>lr,(v,u)=>{u(v,{children:(k,V)=>{y();var S=T();ar(()=>tr(S,o(h).title)),e(k,S)},$$slots:{default:!0}})}),F(a);var E=l(a,2);s(E,()=>ir,(v,u)=>{u(v,{children:(k,V)=>{y();var S=T();ar(()=>tr(S,o(h).description)),e(k,S)},$$slots:{default:!0}})}),e(x,m)},$$slots:{default:!0}})});var K=l(w,2);s(K,()=>or,(c,f)=>{f(c,{children:(x,_)=>{var m=L(),a=n(m);s(a,()=>Q,(i,t)=>{t(i,{class:"w-full",variant:"outline",onclick:()=>dr(o(h).href),children:(j,g)=>{y();var b=Rr(),E=n(b),v=l(E);qr(v,{class:"ml-2 h-4 w-4"}),ar(()=>tr(E,`Go to ${o(h).title??""} `)),e(j,b)},$$slots:{default:!0}})}),e(x,m)},$$slots:{default:!0}})}),e(J,P)},$$slots:{default:!0}})}),e(H,G)});var Z=l(W,2);s(Z,()=>sr,(H,h)=>{h(H,{children:(G,rr)=>{var I=ae(),M=n(I);s(M,()=>nr,(C,P)=>{P(C,{children:(w,K)=>{var c=Xr(),f=n(c);s(f,()=>lr,(_,m)=>{m(_,{children:(a,i)=>{y();var t=T("Add Admin User");e(a,t)},$$slots:{default:!0}})});var x=l(f,2);s(x,()=>ir,(_,m)=>{m(_,{children:(a,i)=>{y();var t=T("Grant admin privileges to another user");e(a,t)},$$slots:{default:!0}})}),e(w,c)},$$slots:{default:!0}})});var J=l(M,2);s(J,()=>or,(C,P)=>{P(C,{children:(w,K)=>{var c=ee(),f=A(c),x=l(A(f),2);s(x,()=>Lr,(a,i)=>{i(a,{id:"email",type:"email",placeholder:"Enter user email",get value(){return o(U)},set value(t){z(U,t,!0)}})}),F(f);var _=l(f,2);const m=ur(()=>o(R)||!o(U));s(_,()=>Q,(a,i)=>{i(a,{onclick:yr,get disabled(){return o(m)},children:(t,j)=>{var g=re(),b=n(g);{var E=v=>{var u=Zr();e(v,u)};O(b,v=>{o(R)&&v(E)})}y(),e(t,g)},$$slots:{default:!0}})}),F(c),e(w,c)},$$slots:{default:!0}})}),e(G,I)},$$slots:{default:!0}})});var Fr=l(Z,2);s(Fr,()=>sr,(H,h)=>{h(H,{children:(G,rr)=>{var I=le(),M=n(I);s(M,()=>nr,(C,P)=>{P(C,{children:(w,K)=>{var c=te(),f=n(c);s(f,()=>lr,(_,m)=>{m(_,{children:(a,i)=>{y();var t=T("Feature Management");e(a,t)},$$slots:{default:!0}})});var x=l(f,2);s(x,()=>ir,(_,m)=>{m(_,{children:(a,i)=>{y();var t=T("Initialize and manage feature data");e(a,t)},$$slots:{default:!0}})}),e(w,c)},$$slots:{default:!0}})});var J=l(M,2);s(J,()=>or,(C,P)=>{P(C,{children:(w,K)=>{var c=ne(),f=l(A(c),2),x=A(f);s(x,()=>Q,(a,i)=>{i(a,{variant:"outline",onclick:xr,get disabled(){return o(N)},children:(t,j)=>{var g=L(),b=n(g);{var E=u=>{var k=se(),V=n(k);Vr(V,{class:"mr-2 h-4 w-4 animate-spin"}),y(),e(u,k)},v=u=>{var k=T("Initialize Feature Data");e(u,k)};O(b,u=>{o(N)?u(E):u(v,!1)})}e(t,g)},$$slots:{default:!0}})});var _=l(x,2);s(_,()=>Q,(a,i)=>{i(a,{onclick:()=>dr("/dashboard/settings/admin/features"),children:(t,j)=>{var g=oe(),b=n(g);pr(b,{class:"mr-2 h-4 w-4"}),y(),e(t,g)},$$slots:{default:!0}})});var m=l(_,2);s(m,()=>Q,(a,i)=>{i(a,{onclick:()=>dr("/dashboard/settings/admin/feature-usage"),children:(t,j)=>{var g=ie(),b=n(g);_r(b,{class:"mr-2 h-4 w-4"}),y(),e(t,g)},$$slots:{default:!0}})}),F(f),F(c),e(w,c)},$$slots:{default:!0}})}),e(G,I)},$$slots:{default:!0}})}),F(q),e(B,D)},Ar=B=>{var D=ce(),q=A(D);Hr(q,{class:"mb-4 h-16 w-16 text-red-500"});var W=l(q,6);O(W,Z=>{}),F(D),e(B,D)};O(r,B=>{o(cr)?B(zr):B(Ar,!1)},p)}};O(Pr,r=>{o(X)?r(wr):r(kr,!1)})}e(gr,mr),Ur()}export{Ue as component};
