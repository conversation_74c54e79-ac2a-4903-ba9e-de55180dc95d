import{f as m,a as f}from"./BasJTneF.js";import{p as i,a as p}from"./CGmarHxI.js";import{e as l}from"./B-Xjo-Yt.js";import{b as n}from"./5V1tIHTN.js";import{p as c,r as d}from"./Btcx8l8F.js";import{c as u}from"./ncUU1dSD.js";var v=m("<div></div>");function x(e,t){i(t,!0);let s=c(t,"ref",15,null),o=d(t,["$$slots","$$events","$$legacy","ref","class"]);var r=v();l(r,a=>({"data-slot":"skeleton",class:a,...o}),[()=>u("bg-accent animate-pulse rounded-md",t.class)]),n(r,a=>s(a),()=>s()),f(e,r),p()}export{x as S};
