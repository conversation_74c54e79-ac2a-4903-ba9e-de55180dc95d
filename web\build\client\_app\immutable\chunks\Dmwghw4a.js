var Me=Object.defineProperty;var re=r=>{throw TypeError(r)};var Ne=(r,t,e)=>t in r?Me(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var g=(r,t,e)=>Ne(r,typeof t!="symbol"?t+"":t,e),Bt=(r,t,e)=>t.has(r)||re("Cannot "+e);var n=(r,t,e)=>(Bt(r,t,"read from private field"),e?e.call(r):t.get(r)),f=(r,t,e)=>t.has(r)?re("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,e),K=(r,t,e,s)=>(Bt(r,t,"write to private field"),s?s.call(r,e):t.set(r,e),e),m=(r,t,e)=>(Bt(r,t,"access private method"),e);import{f as Et,a as R,c as y,t as xe}from"./BasJTneF.js";import{k as P,g as d,d as S,v as Oe,x as _,u as we,i as ye,p as F,c as It,au as Pt,r as kt,a as U,f as O,s as oe,n as be,t as Ve}from"./CGmarHxI.js";import{c as Zt}from"./BvdI7LR8.js";import{p as v,r as H,s as zt}from"./Btcx8l8F.js";import{s as L,c as $t}from"./ncUU1dSD.js";import{s as Ie}from"./CIt1g2O9.js";import{i as yt}from"./u21ee2wt.js";import{e as Dt}from"./B-Xjo-Yt.js";import{u as w,w as Z,a as Pe,b as T,m as Gt}from"./BfX7a-t9.js";import{u as Lt}from"./CnMg5bH0.js";import{n as ie}from"./DX6rZLP_.js";import{k as ke}from"./DT9WCdWY.js";import{s as De}from"./BniYvUIG.js";import{d as Ge,e as Le}from"./BaVT73bJ.js";import{a as W}from"./OOsIR5sE.js";import{C as te}from"./DuoUhxYL.js";import{d as Be,o as Fe,h as Ue,m as He,n as qe}from"./Bd3zs5C6.js";import{g as je,c as Ft}from"./BosuxZz1.js";import{d as We,E as Xe,H as Ye,A as Je,k as Ke,p as Qe,c as Ze,j as ze,n as $e}from"./CIOgxH3l.js";function ts(r,t){let e=r.nextElementSibling;for(;e;){if(e.matches(t))return e;e=e.nextElementSibling}}function es(r,t){let e=r.previousElementSibling;for(;e;){if(e.matches(t))return e;e=e.previousElementSibling}}var ue={exports:{}};/*! https://mths.be/cssescape v1.5.1 by @mathias | MIT license */(function(r,t){(function(e,s){r.exports=s(e)})(typeof Ft<"u"?Ft:Ft,function(e){if(e.CSS&&e.CSS.escape)return e.CSS.escape;var s=function(a){if(arguments.length==0)throw new TypeError("`CSS.escape` requires an argument.");for(var l=String(a),h=l.length,o=-1,i,u="",C=l.charCodeAt(0);++o<h;){if(i=l.charCodeAt(o),i==0){u+="�";continue}if(i>=1&&i<=31||i==127||o==0&&i>=48&&i<=57||o==1&&i>=48&&i<=57&&C==45){u+="\\"+i.toString(16)+" ";continue}if(o==0&&h==1&&i==45){u+="\\"+l.charAt(o);continue}if(i>=128||i==45||i==95||i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122){u+=l.charAt(o);continue}u+="\\"+l.charAt(o)}return u};return e.CSS||(e.CSS={}),e.CSS.escape=s,s})})(ue);var ss=ue.exports;const ce=je(ss),rs="data-command-root",os="data-command-list",is="data-command-input",ns="data-command-separator",as="data-command-loading",ls="data-command-empty",de="data-command-group",he="data-command-group-items",fe="data-command-group-heading",me="data-command-item",us="data-command-input-label",D="data-value",Q=`[${de}]`,Ut=`[${he}]`,cs=`[${fe}]`,pe=`[${me}]`,Ht=`${pe}:not([aria-disabled="true"])`,k=new te("Command.Root"),ds=new te("Command.List"),Mt=new te("Command.Group"),ne={search:"",value:"",filtered:{count:0,items:new Map,groups:new Set}};var Y,z,$,tt,et,st,rt,c,ge,Nt,Wt,xt,Ot,wt,X,Se,Xt,Yt,Jt,ot;class hs{constructor(t){f(this,c);g(this,"opts");f(this,Y,!1);g(this,"sortAfterTick",!1);g(this,"sortAndFilterAfterTick",!1);g(this,"allItems",new Set);g(this,"allGroups",new Map);g(this,"allIds",new Map);f(this,z,P(0));f(this,$,P(null));f(this,tt,P(null));f(this,et,P(null));f(this,st,P(ne));f(this,rt,P(Oe(ne)));f(this,ot,_(()=>({id:this.opts.id.current,role:"application",[rs]:"",tabindex:-1,onkeydown:this.onkeydown})));this.opts=t;const e={...this._commandState,value:this.opts.value.current??""};this._commandState=e,this.commandState=e,w(t),this.onkeydown=this.onkeydown.bind(this)}get key(){return d(n(this,z))}set key(t){S(n(this,z),t,!0)}get viewportNode(){return d(n(this,$))}set viewportNode(t){S(n(this,$),t,!0)}get inputNode(){return d(n(this,tt))}set inputNode(t){S(n(this,tt),t,!0)}get labelNode(){return d(n(this,et))}set labelNode(t){S(n(this,et),t,!0)}get commandState(){return d(n(this,st))}set commandState(t){S(n(this,st),t)}get _commandState(){return d(n(this,rt))}set _commandState(t){S(n(this,rt),t,!0)}setState(t,e,s){Object.is(this._commandState[t],e)||(this._commandState[t]=e,t==="search"?(m(this,c,wt).call(this),m(this,c,xt).call(this)):t==="value"&&(s||m(this,c,Se).call(this)),m(this,c,Nt).call(this))}setValue(t,e){t!==this.opts.value.current&&t===""&&W(()=>{this.key++}),this.setState("value",t,e),this.opts.value.current=t}getValidItems(){const t=this.opts.ref.current;return t?Array.from(t.querySelectorAll(Ht)).filter(s=>!!s):[]}updateSelectedToIndex(t){const s=this.getValidItems()[t];s&&this.setValue(s.getAttribute(D)??"")}updateSelectedByItem(t){const e=m(this,c,X).call(this),s=this.getValidItems(),a=s.findIndex(h=>h===e);let l=s[a+t];this.opts.loop.current&&(l=a+t<0?s[s.length-1]:a+t===s.length?s[0]:s[a+t]),l&&this.setValue(l.getAttribute(D)??"")}updateSelectedByGroup(t){const e=m(this,c,X).call(this);let s=e==null?void 0:e.closest(Q),a;for(;s&&!a;)s=t>0?ts(s,Q):es(s,Q),a=s==null?void 0:s.querySelector(Ht);a?this.setValue(a.getAttribute(D)??""):this.updateSelectedByItem(t)}registerValue(t,e){var s;return t&&t===((s=this.allIds.get(t))==null?void 0:s.value)||this.allIds.set(t,{value:t,keywords:e}),this._commandState.filtered.items.set(t,m(this,c,Wt).call(this,t,e)),this.sortAfterTick||(this.sortAfterTick=!0,W(()=>{m(this,c,xt).call(this),this.sortAfterTick=!1})),()=>{this.allIds.delete(t)}}registerItem(t,e){return this.allItems.add(t),e&&(this.allGroups.has(e)?this.allGroups.get(e).add(t):this.allGroups.set(e,new Set([t]))),this.sortAndFilterAfterTick||(this.sortAndFilterAfterTick=!0,W(()=>{m(this,c,wt).call(this),m(this,c,xt).call(this),this.sortAndFilterAfterTick=!1})),m(this,c,Nt).call(this),()=>{const s=m(this,c,X).call(this);this.allIds.delete(t),this.allItems.delete(t),this.commandState.filtered.items.delete(t),m(this,c,wt).call(this),(s==null?void 0:s.getAttribute("id"))===t&&m(this,c,Ot).call(this),m(this,c,Nt).call(this)}}registerGroup(t){return this.allGroups.has(t)||this.allGroups.set(t,new Set),()=>{this.allIds.delete(t),this.allGroups.delete(t)}}onkeydown(t){switch(t.key){case $e:case ze:{this.opts.vimBindings.current&&t.ctrlKey&&m(this,c,Yt).call(this,t);break}case Ze:m(this,c,Yt).call(this,t);break;case Qe:case Ke:{this.opts.vimBindings.current&&t.ctrlKey&&m(this,c,Jt).call(this,t);break}case Je:m(this,c,Jt).call(this,t);break;case Ye:t.preventDefault(),this.updateSelectedToIndex(0);break;case Xe:t.preventDefault(),m(this,c,Xt).call(this);break;case We:if(!t.isComposing&&t.keyCode!==229){t.preventDefault();const e=m(this,c,X).call(this);e&&(e==null||e.click())}}}get props(){return d(n(this,ot))}set props(t){S(n(this,ot),t)}}Y=new WeakMap,z=new WeakMap,$=new WeakMap,tt=new WeakMap,et=new WeakMap,st=new WeakMap,rt=new WeakMap,c=new WeakSet,ge=function(){return De(this._commandState)},Nt=function(){n(this,Y)||(K(this,Y,!0),W(()=>{var s,a;K(this,Y,!1);const t=m(this,c,ge).call(this);!Object.is(this.commandState,t)&&(this.commandState=t,(a=(s=this.opts.onStateChange)==null?void 0:s.current)==null||a.call(s,t))}))},Wt=function(t,e){const s=this.opts.filter.current??ve;return t?s(t,this._commandState.search,e):0},xt=function(){var h;if(!this._commandState.search||this.opts.shouldFilter.current===!1){m(this,c,Ot).call(this);return}const t=this._commandState.filtered.items,e=[];for(const o of this._commandState.filtered.groups){const i=this.allGroups.get(o);let u=0;if(!i){e.push([o,u]);continue}for(const C of i){const p=t.get(C);u=Math.max(p??0,u)}e.push([o,u])}const s=this.viewportNode,a=this.getValidItems().sort((o,i)=>{const u=o.getAttribute("data-value"),C=i.getAttribute("data-value"),p=t.get(u)??0;return(t.get(C)??0)-p});for(const o of a){const i=o.closest(Ut);if(i){const u=o.parentElement===i?o:o.closest(`${Ut} > *`);u&&i.appendChild(u)}else{const u=o.parentElement===s?o:o.closest(`${Ut} > *`);u&&(s==null||s.appendChild(u))}}const l=e.sort((o,i)=>i[1]-o[1]);for(const o of l){const i=s==null?void 0:s.querySelector(`${Q}[${D}="${ce(o[0])}"]`);(h=i==null?void 0:i.parentElement)==null||h.appendChild(i)}m(this,c,Ot).call(this)},Ot=function(){W(()=>{const t=this.getValidItems().find(s=>s.getAttribute("aria-disabled")!=="true"),e=t==null?void 0:t.getAttribute(D);this.setValue(e||"")})},wt=function(){var e,s;if(!this._commandState.search||this.opts.shouldFilter.current===!1){this._commandState.filtered.count=this.allItems.size;return}this._commandState.filtered.groups=new Set;let t=0;for(const a of this.allItems){const l=((e=this.allIds.get(a))==null?void 0:e.value)??"",h=((s=this.allIds.get(a))==null?void 0:s.keywords)??[],o=m(this,c,Wt).call(this,l,h);this._commandState.filtered.items.set(a,o),o>0&&t++}for(const[a,l]of this.allGroups)for(const h of l){const o=this._commandState.filtered.items.get(h);if(o&&o>0){this._commandState.filtered.groups.add(a);break}}this._commandState.filtered.count=t},X=function(){const t=this.opts.ref.current;if(!t)return;const e=t.querySelector(`${Ht}[data-selected]`);if(e)return e},Se=function(){W(()=>{var a,l,h,o;const t=m(this,c,X).call(this);if(!t)return;const e=(a=t.parentElement)==null?void 0:a.parentElement;if(!e)return;const s=Ge(e);if(s&&((l=s.dataset)==null?void 0:l.value)===((h=t.dataset)==null?void 0:h.value)){const i=(o=t==null?void 0:t.closest(Q))==null?void 0:o.querySelector(cs);i==null||i.scrollIntoView({block:"nearest"});return}t.scrollIntoView({block:"nearest"})})},Xt=function(){return this.updateSelectedToIndex(this.getValidItems().length-1)},Yt=function(t){t.preventDefault(),t.metaKey?m(this,c,Xt).call(this):t.altKey?this.updateSelectedByGroup(1):this.updateSelectedByItem(1)},Jt=function(t){t.preventDefault(),t.metaKey?this.updateSelectedToIndex(0):t.altKey?this.updateSelectedByGroup(-1):this.updateSelectedByItem(-1)},ot=new WeakMap;var it,nt,at;class fs{constructor(t,e){g(this,"opts");g(this,"root");f(this,it,!0);f(this,nt,_(()=>this.root._commandState.filtered.count===0&&n(this,it)===!1||this.opts.forceMount.current));f(this,at,_(()=>({id:this.opts.id.current,role:"presentation",[ls]:""})));this.opts=t,this.root=e,we(()=>{K(this,it,!1)}),w({...t,deps:()=>this.shouldRender})}get shouldRender(){return d(n(this,nt))}set shouldRender(t){S(n(this,nt),t)}get props(){return d(n(this,at))}set props(t){S(n(this,at),t)}}it=new WeakMap,nt=new WeakMap,at=new WeakMap;var lt,ut,ct,dt;class ms{constructor(t,e){g(this,"opts");g(this,"root");f(this,lt,P(null));f(this,ut,P(""));f(this,ct,_(()=>this.opts.forceMount.current||this.root.opts.shouldFilter.current===!1||!this.root.commandState.search?!0:this.root._commandState.filtered.groups.has(this.trueValue)));f(this,dt,_(()=>({id:this.opts.id.current,role:"presentation",hidden:this.shouldRender?void 0:!0,"data-value":this.trueValue,[de]:""})));this.opts=t,this.root=e,this.trueValue=t.value.current??t.id.current,w({...t,deps:()=>this.shouldRender}),Z(()=>this.trueValue,()=>this.root.registerGroup(this.trueValue)),ye(()=>this.opts.value.current?(this.trueValue=this.opts.value.current,this.root.registerValue(this.opts.value.current)):this.headingNode&&this.headingNode.textContent?(this.trueValue=this.headingNode.textContent.trim().toLowerCase(),this.root.registerValue(this.trueValue)):(this.trueValue=`-----${this.opts.id.current}`,this.root.registerValue(this.trueValue)))}get headingNode(){return d(n(this,lt))}set headingNode(t){S(n(this,lt),t,!0)}get trueValue(){return d(n(this,ut))}set trueValue(t){S(n(this,ut),t,!0)}get shouldRender(){return d(n(this,ct))}set shouldRender(t){S(n(this,ct),t)}get props(){return d(n(this,dt))}set props(t){S(n(this,dt),t)}}lt=new WeakMap,ut=new WeakMap,ct=new WeakMap,dt=new WeakMap;var ht;class ps{constructor(t,e){g(this,"opts");g(this,"group");f(this,ht,_(()=>({id:this.opts.id.current,[fe]:""})));this.opts=t,this.group=e,w({...t,onRefChange:s=>{this.group.headingNode=s}})}get props(){return d(n(this,ht))}set props(t){S(n(this,ht),t)}}ht=new WeakMap;var ft;class gs{constructor(t,e){g(this,"opts");g(this,"group");f(this,ft,_(()=>{var t;return{id:this.opts.id.current,role:"group",[he]:"","aria-labelledby":((t=this.group.headingNode)==null?void 0:t.id)??void 0}}));this.opts=t,this.group=e,w(t)}get props(){return d(n(this,ft))}set props(t){S(n(this,ft),t)}}ft=new WeakMap;var bt,mt;class Ss{constructor(t,e){g(this,"opts");g(this,"root");f(this,bt,_(()=>{var e;const t=(e=this.root.viewportNode)==null?void 0:e.querySelector(`${pe}[${D}="${ce(this.root.opts.value.current)}"]`);if(t)return(t==null?void 0:t.getAttribute("id"))??void 0}));f(this,mt,_(()=>{var t,e;return{id:this.opts.id.current,type:"text",[is]:"",autocomplete:"off",autocorrect:"off",spellcheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":Be(!0),"aria-controls":((t=this.root.viewportNode)==null?void 0:t.id)??void 0,"aria-labelledby":((e=this.root.labelNode)==null?void 0:e.id)??void 0,"aria-activedescendant":d(n(this,bt))}}));this.opts=t,this.root=e,w({...t,onRefChange:s=>{this.root.inputNode=s}}),Z(()=>this.opts.ref.current,()=>{const s=this.opts.ref.current;s&&this.opts.autofocus.current&&Le(10,()=>s.focus())}),Z(()=>this.opts.value.current,()=>{this.root.commandState.search!==this.opts.value.current&&this.root.setState("search",this.opts.value.current)})}get props(){return d(n(this,mt))}set props(t){S(n(this,mt),t)}}bt=new WeakMap,mt=new WeakMap;var G,Vt,pt,gt,St,B,_e,Kt,_t;class _s{constructor(t,e){f(this,B);g(this,"opts");g(this,"root");f(this,G,null);f(this,Vt,_(()=>{var t;return this.opts.forceMount.current||((t=n(this,G))==null?void 0:t.opts.forceMount.current)===!0}));f(this,pt,P(""));f(this,gt,_(()=>{if(this.opts.ref.current,d(n(this,Vt))||this.root.opts.shouldFilter.current===!1||!this.root.commandState.search)return!0;const t=this.root.commandState.filtered.items.get(this.trueValue);return t===void 0?!1:t>0}));f(this,St,_(()=>this.root.opts.value.current===this.trueValue&&this.trueValue!==""));f(this,_t,_(()=>({id:this.opts.id.current,"aria-disabled":qe(this.opts.disabled.current),"aria-selected":He(this.isSelected),"data-disabled":Ue(this.opts.disabled.current),"data-selected":Fe(this.isSelected),"data-value":this.trueValue,[me]:"",role:"option",onpointermove:this.onpointermove,onclick:this.onclick})));this.opts=t,this.root=e,K(this,G,Mt.getOr(null)),this.trueValue=t.value.current,w({...t,deps:()=>!!this.root.commandState.search}),Z([()=>this.trueValue,()=>{var s;return(s=n(this,G))==null?void 0:s.trueValue},()=>this.opts.forceMount.current],()=>{var s;if(!this.opts.forceMount.current)return this.root.registerItem(this.trueValue,(s=n(this,G))==null?void 0:s.trueValue)}),Z([()=>this.opts.value.current,()=>this.opts.ref.current],()=>{var s,a;!this.opts.value.current&&((s=this.opts.ref.current)!=null&&s.textContent)&&(this.trueValue=this.opts.ref.current.textContent.trim()),this.root.registerValue(this.trueValue,t.keywords.current.map(l=>l.trim())),(a=this.opts.ref.current)==null||a.setAttribute(D,this.trueValue)}),this.onclick=this.onclick.bind(this),this.onpointermove=this.onpointermove.bind(this)}get trueValue(){return d(n(this,pt))}set trueValue(t){S(n(this,pt),t,!0)}get shouldRender(){return d(n(this,gt))}set shouldRender(t){S(n(this,gt),t)}get isSelected(){return d(n(this,St))}set isSelected(t){S(n(this,St),t)}onpointermove(t){this.opts.disabled.current||this.root.opts.disablePointerSelection.current||m(this,B,Kt).call(this)}onclick(t){this.opts.disabled.current||m(this,B,_e).call(this)}get props(){return d(n(this,_t))}set props(t){S(n(this,_t),t)}}G=new WeakMap,Vt=new WeakMap,pt=new WeakMap,gt=new WeakMap,St=new WeakMap,B=new WeakSet,_e=function(){var t;this.opts.disabled.current||(m(this,B,Kt).call(this),(t=this.opts.onSelect)==null||t.current())},Kt=function(){this.opts.disabled.current||this.root.setValue(this.trueValue,!0)},_t=new WeakMap;var Ct;class Cs{constructor(t){g(this,"opts");f(this,Ct,_(()=>({id:this.opts.id.current,role:"progressbar","aria-valuenow":this.opts.progress.current,"aria-valuemin":0,"aria-valuemax":100,"aria-label":"Loading...",[as]:""})));this.opts=t,w(t)}get props(){return d(n(this,Ct))}set props(t){S(n(this,Ct),t)}}Ct=new WeakMap;var vt,At;class vs{constructor(t,e){g(this,"opts");g(this,"root");f(this,vt,_(()=>!this.root._commandState.search||this.opts.forceMount.current));f(this,At,_(()=>({id:this.opts.id.current,"aria-hidden":"true",[ns]:""})));this.opts=t,this.root=e,w({...t,deps:()=>this.shouldRender})}get shouldRender(){return d(n(this,vt))}set shouldRender(t){S(n(this,vt),t)}get props(){return d(n(this,At))}set props(t){S(n(this,At),t)}}vt=new WeakMap,At=new WeakMap;var Tt;class As{constructor(t,e){g(this,"opts");g(this,"root");f(this,Tt,_(()=>({id:this.opts.id.current,role:"listbox","aria-label":this.opts.ariaLabel.current,[os]:""})));this.opts=t,this.root=e,w(t)}get props(){return d(n(this,Tt))}set props(t){S(n(this,Tt),t)}}Tt=new WeakMap;var Rt;class Ts{constructor(t,e){g(this,"opts");g(this,"root");f(this,Rt,_(()=>{var t;return{id:this.opts.id.current,[us]:"",for:(t=this.opts.for)==null?void 0:t.current,style:Pe}}));this.opts=t,this.root=e,w({...t,onRefChange:s=>{this.root.labelNode=s}})}get props(){return d(n(this,Rt))}set props(t){S(n(this,Rt),t)}}Rt=new WeakMap;function Rs(r){return k.set(new hs(r))}function Es(r){return new fs(r,k.get())}function cr(r){const t=Mt.getOr(null);return new _s({...r,group:t},k.get())}function dr(r){return Mt.set(new ms(r,k.get()))}function hr(r){return new ps(r,Mt.get())}function fr(r){return new gs(r,Mt.get())}function mr(r){return new Ss(r,k.get())}function pr(r){return new Cs(r)}function gr(r){return new vs(r,k.get())}function Ms(r){return ds.set(new As(r,k.get()))}function Ns(r){return new Ts(r,k.get())}var xs=Et("<label><!></label>");function Os(r,t){F(t,!0);let e=v(t,"id",19,Lt),s=v(t,"ref",15,null),a=H(t,["$$slots","$$events","$$legacy","id","ref","children"]);const l=Ns({id:T.with(()=>e()),ref:T.with(()=>s(),u=>s(u))}),h=_(()=>Gt(a,l.props));var o=xs();Dt(o,()=>({...d(h)}));var i=It(o);L(i,()=>t.children??Pt),kt(o),R(r,o),U()}var ws=Et("<!> <!>",1),ys=Et("<div><!> <!></div>");function bs(r,t){F(t,!0);const e=A=>{Os(A,{children:(I,q)=>{be();var j=xe();Ve(()=>Ie(j,p())),R(I,j)},$$slots:{default:!0}})};let s=v(t,"id",19,Lt),a=v(t,"ref",15,null),l=v(t,"value",15,""),h=v(t,"onValueChange",3,ie),o=v(t,"onStateChange",3,ie),i=v(t,"loop",3,!1),u=v(t,"shouldFilter",3,!0),C=v(t,"filter",3,ve),p=v(t,"label",3,""),x=v(t,"vimBindings",3,!0),b=v(t,"disablePointerSelection",3,!1),M=H(t,["$$slots","$$events","$$legacy","id","ref","value","onValueChange","onStateChange","loop","shouldFilter","filter","label","vimBindings","disablePointerSelection","children","child"]);const E=Rs({id:T.with(()=>s()),ref:T.with(()=>a(),A=>a(A)),filter:T.with(()=>C()),shouldFilter:T.with(()=>u()),loop:T.with(()=>i()),value:T.with(()=>l(),A=>{l()!==A&&(l(A),h()(A))}),vimBindings:T.with(()=>x()),disablePointerSelection:T.with(()=>b()),onStateChange:T.with(()=>o())}),N=A=>E.updateSelectedToIndex(A),V=A=>E.updateSelectedByGroup(A),J=A=>E.updateSelectedByItem(A),Ae=()=>E.getValidItems(),ee=_(()=>Gt(M,E.props));var se=y(),Te=O(se);{var Re=A=>{var I=ws(),q=O(I);e(q);var j=oe(q,2);L(j,()=>t.child,()=>({props:d(ee)})),R(A,I)},Ee=A=>{var I=ys();Dt(I,()=>({...d(ee)}));var q=It(I);e(q);var j=oe(q,2);L(j,()=>t.children??Pt),kt(I),R(A,I)};yt(Te,A=>{t.child?A(Re):A(Ee,!1)})}return R(r,se),U({updateSelectedToIndex:N,updateSelectedByGroup:V,updateSelectedByItem:J,getValidItems:Ae})}var Vs=Et("<div><!></div>");function Is(r,t){F(t,!0);let e=v(t,"id",19,Lt),s=v(t,"ref",15,null),a=v(t,"forceMount",3,!1),l=H(t,["$$slots","$$events","$$legacy","id","ref","children","child","forceMount"]);const h=Es({id:T.with(()=>e()),ref:T.with(()=>s(),p=>s(p)),forceMount:T.with(()=>a())}),o=_(()=>Gt(h.props,l));var i=y(),u=O(i);{var C=p=>{var x=y(),b=O(x);{var M=N=>{var V=y(),J=O(V);L(J,()=>t.child,()=>({props:d(o)})),R(N,V)},E=N=>{var V=Vs();Dt(V,()=>({...d(o)}));var J=It(V);L(J,()=>t.children??Pt),kt(V),R(N,V)};yt(b,N=>{t.child?N(M):N(E,!1)})}R(p,x)};yt(u,p=>{h.shouldRender&&p(C)})}R(r,i),U()}var Ps=Et("<div><!></div>");function ks(r,t){F(t,!0);let e=v(t,"id",19,Lt),s=v(t,"ref",15,null),a=H(t,["$$slots","$$events","$$legacy","id","ref","child","children","aria-label"]);const l=Ms({id:T.with(()=>e()),ref:T.with(()=>s(),u=>s(u)),ariaLabel:T.with(()=>t["aria-label"]??"Suggestions...")}),h=_(()=>Gt(a,l.props));var o=y(),i=O(o);ke(i,()=>l.root._commandState.search==="",u=>{var C=y(),p=O(C);{var x=M=>{var E=y(),N=O(E);L(N,()=>t.child,()=>({props:d(h)})),R(M,E)},b=M=>{var E=Ps();Dt(E,()=>({...d(h)}));var N=It(E);L(N,()=>t.children??Pt),kt(E),R(M,E)};yt(p,M=>{t.child?M(x):M(b,!1)})}R(u,C)}),R(r,o),U()}const ae=1,Ds=.9,Gs=.8,Ls=.17,qt=.1,jt=.999,Bs=.9999,Fs=.99,Us=/[\\/_+.#"@[({&]/,Hs=/[\\/_+.#"@[({&]/g,qs=/[\s-]/,Ce=/[\s-]/g;function Qt(r,t,e,s,a,l,h){if(l===t.length)return a===r.length?ae:Fs;const o=`${a},${l}`;if(h[o]!==void 0)return h[o];const i=s.charAt(l);let u=e.indexOf(i,a),C=0,p,x,b,M;for(;u>=0;)p=Qt(r,t,e,s,u+1,l+1,h),p>C&&(u===a?p*=ae:Us.test(r.charAt(u-1))?(p*=Gs,b=r.slice(a,u-1).match(Hs),b&&a>0&&(p*=jt**b.length)):qs.test(r.charAt(u-1))?(p*=Ds,M=r.slice(a,u-1).match(Ce),M&&a>0&&(p*=jt**M.length)):(p*=Ls,a>0&&(p*=jt**(u-a))),r.charAt(u)!==t.charAt(l)&&(p*=Bs)),(p<qt&&e.charAt(u-1)===s.charAt(l+1)||s.charAt(l+1)===s.charAt(l)&&e.charAt(u-1)!==s.charAt(l))&&(x=Qt(r,t,e,s,u+1,l+2,h),x*qt>p&&(p=x*qt)),p>C&&(C=p),u=e.indexOf(i,u+1);return h[o]=C,C}function le(r){return r.toLowerCase().replace(Ce," ")}function ve(r,t,e){return r=e&&e.length>0?`${`${r} ${e==null?void 0:e.join(" ")}`}`:r,Qt(r,t,le(r),le(t),0,0,{})}function Sr(r,t){F(t,!0);let e=v(t,"ref",15,null),s=v(t,"value",15,""),a=H(t,["$$slots","$$events","$$legacy","ref","value","class"]);var l=y(),h=O(l);const o=_(()=>$t("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",t.class));Zt(h,()=>bs,(i,u)=>{u(i,zt({"data-slot":"command",get class(){return d(o)}},()=>a,{get value(){return s()},set value(C){s(C)},get ref(){return e()},set ref(C){e(C)}}))}),R(r,l),U()}function _r(r,t){F(t,!0);let e=v(t,"ref",15,null),s=H(t,["$$slots","$$events","$$legacy","ref","class"]);var a=y(),l=O(a);const h=_(()=>$t("py-6 text-center text-sm",t.class));Zt(l,()=>Is,(o,i)=>{i(o,zt({"data-slot":"command-empty",get class(){return d(h)}},()=>s,{get ref(){return e()},set ref(u){e(u)}}))}),R(r,a),U()}function Cr(r,t){F(t,!0);let e=v(t,"ref",15,null),s=H(t,["$$slots","$$events","$$legacy","ref","class"]);var a=y(),l=O(a);const h=_(()=>$t("max-h-[300px] scroll-py-1 overflow-y-auto overflow-x-hidden",t.class));Zt(l,()=>ks,(o,i)=>{i(o,zt({"data-slot":"command-list",get class(){return d(h)}},()=>s,{get ref(){return e()},set ref(u){e(u)}}))}),R(r,a),U()}export{Sr as C,Cr as a,_r as b,mr as c,dr as d,hr as e,fr as f,pr as g,gr as h,cr as u};
