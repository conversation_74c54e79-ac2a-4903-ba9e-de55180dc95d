import{c as p,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function P(r,o){const t=c(o,["children","$$slots","$$events","$$legacy"]),a=[["polygon",{points:"6 3 20 12 6 21 6 3"}]];f(r,d({name:"play"},()=>t,{get iconNode(){return a},children:(e,$)=>{var s=p(),n=i(s);m(n,o,"default",{},null),l(e,s)},$$slots:{default:!0}}))}export{P};
