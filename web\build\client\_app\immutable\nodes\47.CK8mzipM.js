import{f as r,a as e,t as $,c as me}from"../chunks/BasJTneF.js";import{o as Kt}from"../chunks/nZgk9enP.js";import{p as wt,f as n,c as s,s as t,t as Z,e as le,r as l,n as u,g as a,a as Dt,k as ye,v as Qe,d as se}from"../chunks/CGmarHxI.js";import{c as Se}from"../chunks/BvdI7LR8.js";import{R as Qt,T as Xt}from"../chunks/I7hvcB12.js";import{C as Fe}from"../chunks/DuGukytH.js";import{C as Ae}from"../chunks/Cdn-N1RY.js";import{C as nt}from"../chunks/BkJY4La4.js";import{C as it}from"../chunks/GwmmX_iF.js";import{C as dt}from"../chunks/D50jIuLr.js";import"../chunks/CgXBgsce.js";import{t as Oe}from"../chunks/DjPYYl4Z.js";import{R as Lt,n as mt,S as Ot,o as Et}from"../chunks/CyaAPBlz.js";import{s as I}from"../chunks/CIt1g2O9.js";import{i as M}from"../chunks/u21ee2wt.js";import{e as Me,i as ze}from"../chunks/C3w0v0gR.js";import{b as Tt,s as Zt}from"../chunks/B-Xjo-Yt.js";import{i as Ft}from"../chunks/BIEMS98f.js";import{p as F}from"../chunks/Btcx8l8F.js";import{B as _e}from"../chunks/B1K98fMG.js";import{R as gt,S as ht,a as xt,b as vt}from"../chunks/CGK0g3x_.js";import{T as At,a as Mt,b as pt,c as Te,d as zt,e as Re}from"../chunks/LESefvxV.js";import{R as Ut,P as Nt,a as Bt}from"../chunks/3WmhYGjL.js";import{S as bt}from"../chunks/B2lQHLf_.js";import{S as ct}from"../chunks/CVVv9lPb.js";import{R as Pt}from"../chunks/qwsZpUIl.js";import{D as Vt}from"../chunks/tr-scC-m.js";import{M as Ue}from"../chunks/yPulTJ2h.js";import{M as yt,U as Rt}from"../chunks/BBh-2PfQ.js";import{T as ft}from"../chunks/CTO_B1Jk.js";import{C as ea}from"../chunks/BBNNmnYR.js";import{C as ta}from"../chunks/DkmCSZhC.js";import{a as aa}from"../chunks/CbynRejM.js";import{o as _t,p as $t}from"../chunks/ncUU1dSD.js";import{T as kt}from"../chunks/C88uNE8B.js";import{T as Ct}from"../chunks/DmZyh-PW.js";function jt(qe,T,ce){return aa(qe,-7,ce)}var ra=r('<span class="mr-2">📅</span> <!>',1),sa=r("<!> <!>",1),la=r('<!> <div class="px-2 py-1.5 text-sm font-semibold"> </div> <!>',1),oa=r("<!> <!>",1),na=r("<!> <!>",1),ia=r('<!> <span class="sr-only">Refresh</span>',1),da=r('<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>'),va=r("<!> Export Data",1),ca=r('<div class="flex h-64 flex-col items-center justify-center gap-4"><p class="text-muted-foreground text-center">Click the button below to load email analytics data</p> <!></div>'),ua=r('<div class="flex h-64 items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>'),fa=r('<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Total Emails</p> <h3 class="mt-1 text-2xl font-bold"> </h3></div> <div class="bg-primary/10 rounded-full p-2"><!></div></div> <div class="text-muted-foreground mt-4 text-sm"><span class="font-medium text-green-600"> </span> delivery rate</div>',1),ma=r('<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Open Rate</p> <h3 class="mt-1 text-2xl font-bold"> </h3></div> <div class="rounded-full bg-blue-100 p-2"><!></div></div> <div class="text-muted-foreground mt-4 text-sm"><span class="font-medium"> </span> emails opened</div>',1),pa=r('<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Click Rate</p> <h3 class="mt-1 text-2xl font-bold"> </h3></div> <div class="rounded-full bg-purple-100 p-2"><!></div></div> <div class="text-muted-foreground mt-4 text-sm"><span class="font-medium"> </span> emails clicked</div>',1),_a=r('<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Bounce Rate</p> <h3 class="mt-1 text-2xl font-bold"> </h3></div> <div class="rounded-full bg-red-100 p-2"><!></div></div> <div class="text-muted-foreground mt-4 text-sm"><span class="font-medium"> </span> emails bounced</div>',1),$a=r("<!> <!>",1),ga=r('<div class="text-muted-foreground flex h-64 items-center justify-center"><p>No data available for the selected time period</p></div>'),ha=r('<div class="text-muted-foreground flex h-64 items-center justify-center"><!> <p class="ml-2">Chart visualization would appear here</p></div>'),xa=r("<!> <!>",1),ba=r("<!> <!>",1),Pa=r('<div class="text-muted-foreground flex h-64 items-center justify-center"><p>No data available for the selected time period</p></div>'),ya=r('<div class="text-muted-foreground flex h-64 items-center justify-center"><!> <p class="ml-2">Chart visualization would appear here</p></div>'),wa=r("<!> <!>",1),Da=r("<!> <!>",1),Sa=r('<div class="text-muted-foreground flex h-40 items-center justify-center"><p>No data available for the selected time period</p></div>'),Ea=r("<!> <!> <!> <!> <!>",1),Ta=r('<div class="flex items-center"><div class="mr-2 h-2 w-16 rounded-full bg-gray-200"><div class="h-2 rounded-full bg-blue-600"></div></div> <span> </span></div>'),Ra=r('<div class="flex items-center"><div class="mr-2 h-2 w-16 rounded-full bg-gray-200"><div class="h-2 rounded-full bg-purple-600"></div></div> <span> </span></div>'),ka=r("<!> <!> <!> <!> <!>",1),Ca=r("<!> <!>",1),ja=r("<!> <!>",1),La=r('<div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"><!> <!> <!> <!></div> <div class="mb-6 grid grid-cols-1 gap-6 lg:grid-cols-2"><!> <!></div> <!>',1),Oa=r('<div class="mb-6 flex flex-wrap gap-4"><div><label for="timeRange" class="mb-1 block text-sm font-medium">Time Range</label> <div><!></div></div> <div><label for="templateFilter" class="mb-1 block text-sm font-medium">Template</label> <!></div> <div class="ml-auto flex gap-2"><div><div class="mb-1 block text-sm font-medium opacity-0">Refresh</div> <!></div> <div><div class="mb-1 block text-sm font-medium opacity-0">Export</div> <!></div></div></div> <!>',1);function Fa(qe,T){wt(T,!1);let ce=F(T,"dateRange",8),Xe=F(T,"calendarDateRange",8),Ne=F(T,"templateFilter",12),V=F(T,"templates",24,()=>[]),ee=F(T,"emailStats",24,()=>({total:0,delivered:0,opened:0,clicked:0,bounced:0})),Ye=F(T,"chartData",24,()=>[]),ke=F(T,"topEmails",24,()=>[]),$e=F(T,"isLoading",8,!1),W=F(T,"isExporting",8,!1),ue=F(T,"handleCalendarDateChange",8),Ge=F(T,"handleFilterChange",8),Be=F(T,"loadEmailStats",8),He=F(T,"exportData",8);Ft();var be=Oa(),ge=n(be),Ce=s(ge),Ie=t(s(Ce),2),Ze=s(Ie);Ut(Ze,{children:(oe,f)=>{var d=sa(),w=n(d);Nt(w,{children:(c,P)=>{_e(c,{variant:"outline",class:"w-[250px] justify-start",children:(E,ne)=>{var R=ra(),H=t(n(R),2);{var z=k=>{var h=$();Z((p,D)=>I(h,`${p??""} - ${D??""}`),[()=>new Date(ce().startDate).toLocaleDateString(),()=>new Date(ce().endDate).toLocaleDateString()],le),e(k,h)},q=k=>{var h=$("Select date range");e(k,h)};M(H,k=>{ce().startDate&&ce().endDate?k(z):k(q,!1)})}e(E,R)},$$slots:{default:!0}})},$$slots:{default:!0}});var m=t(w,2);Bt(m,{class:"w-auto p-0",align:"start",children:(c,P)=>{Lt(c,{get value(){return Xe()},onValueChange:E=>{if(E!=null&&E.start&&(E!=null&&E.end)){const ne=E.start.toDate(mt()),R=E.end.toDate(mt()),H=new CustomEvent("change",{detail:{startDate:ne,endDate:R,calendarValue:E}});ue()(H)}},numberOfMonths:2})},$$slots:{default:!0}}),e(oe,d)},$$slots:{default:!0}}),l(Ie),l(Ce);var Pe=t(Ce,2),Ee=t(s(Pe),2);gt(Ee,{type:"single",get value(){return Ne()},onValueChange:oe=>{Ne(oe),Ge()()},children:(oe,f)=>{var d=na(),w=n(d);ht(w,{class:"w-[250px]",children:(c,P)=>{bt(c,{placeholder:"Select template"})},$$slots:{default:!0}});var m=t(w,2);xt(m,{class:"w-[250px]",children:(c,P)=>{var E=oa(),ne=n(E);ct(ne,{children:(z,q)=>{vt(z,{value:"all",children:(k,h)=>{u();var p=$("All Templates");e(k,p)},$$slots:{default:!0}})},$$slots:{default:!0}});var R=t(ne,2);{var H=z=>{var q=me(),k=n(q);Me(k,1,()=>[...new Set(V().filter(h=>h.name!=="all").map(h=>h.category||"Other"))],ze,(h,p)=>{var D=la(),_=n(D);Ot(_,{});var S=t(_,2),C=s(S,!0);l(S);var x=t(S,2);ct(x,{children:(L,v)=>{var y=me(),b=n(y);Me(b,1,()=>V().filter(g=>g.name!=="all"&&(g.category||"Other")===a(p)),ze,(g,o)=>{const i=le(()=>a(o).description||"");vt(g,{get value(){return a(o).name},get title(){return a(i)},children:(Y,B)=>{u();var O=$();Z(()=>I(O,a(o).label)),e(Y,O)},$$slots:{default:!0}})}),e(L,y)},$$slots:{default:!0}}),Z(()=>I(C,a(p))),e(h,D)}),e(z,q)};M(R,z=>{V().length>1&&z(H)})}e(c,E)},$$slots:{default:!0}}),e(oe,d)},$$slots:{default:!0}}),l(Pe);var Ve=t(Pe,2),je=s(Ve),lt=t(s(je),2);_e(lt,{variant:"outline",size:"icon",onclick:()=>Be()(),get disabled(){return $e()},class:"h-10 w-10",children:(oe,f)=>{var d=ia(),w=n(d);const m=le(()=>`h-4 w-4 ${$e()?"animate-spin":""}`);Pt(w,{get class(){return a(m)}}),u(2),e(oe,d)},$$slots:{default:!0}}),l(je);var Le=t(je,2),et=t(s(Le),2);_e(et,{variant:"outline",get disabled(){return W()},onclick:()=>He()(),children:(oe,f)=>{var d=va(),w=n(d);{var m=P=>{var E=da();e(P,E)},c=P=>{Vt(P,{class:"mr-2 h-4 w-4"})};M(w,P=>{W()?P(m):P(c,!1)})}u(),e(oe,d)},$$slots:{default:!0}}),l(Le),l(Ve),l(ge);var ot=t(ge,2);{var tt=oe=>{var f=ca(),d=t(s(f),2);_e(d,{onclick:()=>Be()(),children:(w,m)=>{u();var c=$("Load Analytics Data");e(w,c)},$$slots:{default:!0}}),l(f),e(oe,f)},Je=(oe,f)=>{{var d=m=>{var c=ua();e(m,c)},w=m=>{var c=La(),P=n(c),E=s(P);Fe(E,{children:(p,D)=>{Ae(p,{class:"p-6",children:(_,S)=>{var C=fa(),x=n(C),L=s(x),v=t(s(L),2),y=s(v,!0);l(v),l(L);var b=t(L,2),g=s(b);Ue(g,{class:"text-primary h-5 w-5"}),l(b),l(x);var o=t(x,2),i=s(o),Y=s(i);l(i),u(),l(o),Z((B,O)=>{I(y,B),I(Y,`${O??""}%`)},[()=>ee().total.toLocaleString(),()=>Math.round(ee().delivered/ee().total*100)||0],le),e(_,C)},$$slots:{default:!0}})},$$slots:{default:!0}});var ne=t(E,2);Fe(ne,{children:(p,D)=>{Ae(p,{class:"p-6",children:(_,S)=>{var C=ma(),x=n(C),L=s(x),v=t(s(L),2),y=s(v);l(v),l(L);var b=t(L,2),g=s(b);Ue(g,{class:"h-5 w-5 text-blue-600"}),l(b),l(x);var o=t(x,2),i=s(o),Y=s(i,!0);l(i),u(),l(o),Z((B,O)=>{I(y,`${B??""}%`),I(Y,O)},[()=>Math.round(ee().opened/ee().delivered*100)||0,()=>ee().opened.toLocaleString()],le),e(_,C)},$$slots:{default:!0}})},$$slots:{default:!0}});var R=t(ne,2);Fe(R,{children:(p,D)=>{Ae(p,{class:"p-6",children:(_,S)=>{var C=pa(),x=n(C),L=s(x),v=t(s(L),2),y=s(v);l(v),l(L);var b=t(L,2),g=s(b);yt(g,{class:"h-5 w-5 text-purple-600"}),l(b),l(x);var o=t(x,2),i=s(o),Y=s(i,!0);l(i),u(),l(o),Z((B,O)=>{I(y,`${B??""}%`),I(Y,O)},[()=>Math.round(ee().clicked/ee().opened*100)||0,()=>ee().clicked.toLocaleString()],le),e(_,C)},$$slots:{default:!0}})},$$slots:{default:!0}});var H=t(R,2);Fe(H,{children:(p,D)=>{Ae(p,{class:"p-6",children:(_,S)=>{var C=_a(),x=n(C),L=s(x),v=t(s(L),2),y=s(v);l(v),l(L);var b=t(L,2),g=s(b);ft(g,{class:"h-5 w-5 text-red-600"}),l(b),l(x);var o=t(x,2),i=s(o),Y=s(i,!0);l(i),u(),l(o),Z((B,O)=>{I(y,`${B??""}%`),I(Y,O)},[()=>Math.round(ee().bounced/ee().total*100)||0,()=>ee().bounced.toLocaleString()],le),e(_,C)},$$slots:{default:!0}})},$$slots:{default:!0}}),l(P);var z=t(P,2),q=s(z);Fe(q,{children:(p,D)=>{var _=xa(),S=n(_);it(S,{children:(x,L)=>{var v=$a(),y=n(v);dt(y,{children:(g,o)=>{u();var i=$("Email Activity Over Time");e(g,i)},$$slots:{default:!0}});var b=t(y,2);nt(b,{children:(g,o)=>{u();var i=$("Email events over the selected time period");e(g,i)},$$slots:{default:!0}}),e(x,v)},$$slots:{default:!0}});var C=t(S,2);Ae(C,{children:(x,L)=>{var v=me(),y=n(v);{var b=o=>{var i=ga();e(o,i)},g=o=>{var i=ha(),Y=s(i);Ue(Y,{class:"h-8 w-8 opacity-50"}),u(2),l(i),e(o,i)};M(y,o=>{Ye().length===0?o(b):o(g,!1)})}e(x,v)},$$slots:{default:!0}}),e(p,_)},$$slots:{default:!0}});var k=t(q,2);Fe(k,{children:(p,D)=>{var _=wa(),S=n(_);it(S,{children:(x,L)=>{var v=ba(),y=n(v);dt(y,{children:(g,o)=>{u();var i=$("Event Distribution");e(g,i)},$$slots:{default:!0}});var b=t(y,2);nt(b,{children:(g,o)=>{u();var i=$("Distribution of email events by type");e(g,i)},$$slots:{default:!0}}),e(x,v)},$$slots:{default:!0}});var C=t(S,2);Ae(C,{children:(x,L)=>{var v=me(),y=n(v);{var b=o=>{var i=Pa();e(o,i)},g=o=>{var i=ya(),Y=s(i);Ue(Y,{class:"h-8 w-8 opacity-50"}),u(2),l(i),e(o,i)};M(y,o=>{ee().total===0?o(b):o(g,!1)})}e(x,v)},$$slots:{default:!0}}),e(p,_)},$$slots:{default:!0}}),l(z);var h=t(z,2);Fe(h,{children:(p,D)=>{var _=ja(),S=n(_);it(S,{children:(x,L)=>{var v=Da(),y=n(v);dt(y,{children:(g,o)=>{u();var i=$("Top Performing Emails");e(g,i)},$$slots:{default:!0}});var b=t(y,2);nt(b,{children:(g,o)=>{u();var i=$("Emails with the highest open and click rates");e(g,i)},$$slots:{default:!0}}),e(x,v)},$$slots:{default:!0}});var C=t(S,2);Ae(C,{children:(x,L)=>{var v=me(),y=n(v);{var b=o=>{var i=Sa();e(o,i)},g=o=>{At(o,{children:(i,Y)=>{var B=Ca(),O=n(B);Mt(O,{children:(A,he)=>{pt(A,{children:(U,fe)=>{var te=Ea(),j=n(te);Te(j,{children:(ie,N)=>{u();var ae=$("Template");e(ie,ae)},$$slots:{default:!0}});var Q=t(j,2);Te(Q,{children:(ie,N)=>{u();var ae=$("Subject");e(ie,ae)},$$slots:{default:!0}});var J=t(Q,2);Te(J,{children:(ie,N)=>{u();var ae=$("Sent");e(ie,ae)},$$slots:{default:!0}});var xe=t(J,2);Te(xe,{children:(ie,N)=>{u();var ae=$("Open Rate");e(ie,ae)},$$slots:{default:!0}});var X=t(xe,2);Te(X,{children:(ie,N)=>{u();var ae=$("Click Rate");e(ie,ae)},$$slots:{default:!0}}),e(U,te)},$$slots:{default:!0}})},$$slots:{default:!0}});var pe=t(O,2);zt(pe,{children:(A,he)=>{var U=me(),fe=n(U);Me(fe,1,ke,ze,(te,j)=>{pt(te,{children:(Q,J)=>{var xe=ka(),X=n(xe);Re(X,{children:(ve,K)=>{u();var G=$();Z(()=>I(G,a(j).template)),e(ve,G)},$$slots:{default:!0}});var ie=t(X,2);Re(ie,{children:(ve,K)=>{u();var G=$();Z(()=>I(G,a(j).subject)),e(ve,G)},$$slots:{default:!0}});var N=t(ie,2);Re(N,{children:(ve,K)=>{u();var G=$();Z(re=>I(G,re),[()=>a(j).sent.toLocaleString()],le),e(ve,G)},$$slots:{default:!0}});var ae=t(N,2);Re(ae,{children:(ve,K)=>{var G=Ta(),re=s(G),we=s(re);l(re);var De=t(re,2),Ke=s(De);l(De),l(G),Z(()=>{Tt(we,`width: ${a(j).openRate??""}%`),I(Ke,`${a(j).openRate??""}%`)}),e(ve,G)},$$slots:{default:!0}});var de=t(ae,2);Re(de,{children:(ve,K)=>{var G=Ra(),re=s(G),we=s(re);l(re);var De=t(re,2),Ke=s(De);l(De),l(G),Z(()=>{Tt(we,`width: ${a(j).clickRate??""}%`),I(Ke,`${a(j).clickRate??""}%`)}),e(ve,G)},$$slots:{default:!0}}),e(Q,xe)},$$slots:{default:!0}})}),e(A,U)},$$slots:{default:!0}}),e(i,B)},$$slots:{default:!0}})};M(y,o=>{ke().length===0?o(b):o(g,!1)})}e(x,v)},$$slots:{default:!0}}),e(p,_)},$$slots:{default:!0}}),e(m,c)};M(oe,m=>{$e()?m(d):m(w,!1)},f)}};M(ot,oe=>{!ee().total&&!$e()?oe(tt):oe(Je,!1)})}e(qe,be),Dt()}var Aa=r('<div class="flex items-center"><!> </div>'),Ma=r("<!> <!>",1),za=r('<!> <div class="px-2 py-1.5 text-sm font-semibold"> </div> <!>',1),Ua=r("<!> <!>",1),Na=r("<!> <!>",1),Ba=r('<span class="mr-2">📅</span> <!>',1),Ia=r("<!> <!>",1),Wa=r('<!> <span class="sr-only">Refresh</span>',1),qa=r('<!> <span class="sr-only">Refresh</span>',1),Ya=r("<div><!> <!></div> <!>",1),Ga=r('<div class="flex h-64 items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>'),Ha=r('<div class="flex h-40 flex-col items-center justify-center gap-4"><p class="text-muted-foreground text-center">Click the button below to load email events</p> <!></div>'),Ja=r("<!> <!> <!> <!> <!>",1),Ka=r('<div class="flex items-center"><span><!> </span></div>'),Qa=r("<!> <!> <!> <!> <!>",1),Xa=r("<!> <!>",1),Za=r('<!> <span class="sr-only">Previous Page</span>',1),Va=r('<span class="text-muted-foreground">...</span>'),er=r('<span class="text-muted-foreground">...</span>'),tr=r("<!> <!> <!> <!> <!>",1),ar=r('<!> <span class="sr-only">Next Page</span>',1),rr=r('<div class="flex items-center justify-between"><div class="text-muted-foreground text-sm"> </div> <div class="flex items-center space-x-2"><!> <!> <!></div></div>'),sr=r('<div class="space-y-4"><!> <!></div>'),lr=r("<!> <!>",1),or=r('<div class="mb-6 flex flex-wrap gap-4"><div><label for="eventType" class="mb-1 block text-sm font-medium">Event Type</label> <!></div> <div><label for="templateFilter" class="mb-1 block text-sm font-medium">Template</label> <!></div> <div><label for="timeRange2" class="mb-1 block text-sm font-medium">Time Range</label> <div><!></div></div> <div class="ml-auto flex gap-2"><div><div class="mb-1 block text-sm font-medium opacity-0">Refresh</div> <!></div></div></div> <!>',1);function nr(qe,T){wt(T,!1);let ce=F(T,"dateRange",8),Xe=F(T,"calendarDateRange",8),Ne=F(T,"templateFilter",12),V=F(T,"eventType",12),ee=F(T,"templates",24,()=>[]),Ye=F(T,"eventTypeOptions",24,()=>[]),ke=F(T,"emailEvents",24,()=>[]),$e=F(T,"isLoading",8,!1),W=F(T,"currentPage",8,1),ue=F(T,"totalPages",8,1),Ge=F(T,"totalEvents",8,0),Be=F(T,"itemsPerPage",8,10),He=F(T,"handleCalendarDateChange",8),be=F(T,"loadEmailEvents",8),ge=F(T,"goToPage",8),Ce=F(T,"getEventTypeBadgeClass",8),Ie=F(T,"formatDate",8);Ft();var Ze=or(),Pe=n(Ze),Ee=s(Pe),Ve=t(s(Ee),2);gt(Ve,{type:"single",get value(){return V()},onValueChange:d=>{V(d),be()()},children:(d,w)=>{var m=Ma(),c=n(m);ht(c,{class:"w-[180px]",children:(E,ne)=>{bt(E,{placeholder:"Select event type"})},$$slots:{default:!0}});var P=t(c,2);xt(P,{class:"w-[180px]",children:(E,ne)=>{ct(E,{children:(R,H)=>{var z=me(),q=n(z);Me(q,1,Ye,ze,(k,h)=>{vt(k,{get value(){return a(h).value},children:(p,D)=>{var _=Aa(),S=s(_);{var C=v=>{Ue(v,{class:"mr-2 h-4 w-4 text-green-500"})},x=(v,y)=>{{var b=o=>{Ue(o,{class:"mr-2 h-4 w-4 text-blue-500"})},g=(o,i)=>{{var Y=O=>{yt(O,{class:"mr-2 h-4 w-4 text-purple-500"})},B=(O,pe)=>{{var A=U=>{ft(U,{class:"mr-2 h-4 w-4 text-red-500"})},he=(U,fe)=>{{var te=Q=>{ft(Q,{class:"mr-2 h-4 w-4 text-orange-500"})},j=(Q,J)=>{{var xe=X=>{Rt(X,{class:"mr-2 h-4 w-4 text-gray-500"})};M(Q,X=>{a(h).value==="unsubscribed"&&X(xe)},J)}};M(U,Q=>{a(h).value==="complained"?Q(te):Q(j,!1)},fe)}};M(O,U=>{a(h).value==="bounced"?U(A):U(he,!1)},pe)}};M(o,O=>{a(h).value==="clicked"?O(Y):O(B,!1)},i)}};M(v,o=>{a(h).value==="opened"?o(b):o(g,!1)},y)}};M(S,v=>{a(h).value==="delivered"?v(C):v(x,!1)})}var L=t(S);l(_),Z(()=>I(L,` ${a(h).label??""}`)),e(p,_)},$$slots:{default:!0}})}),e(R,z)},$$slots:{default:!0}})},$$slots:{default:!0}}),e(d,m)},$$slots:{default:!0}}),l(Ee);var je=t(Ee,2),lt=t(s(je),2);gt(lt,{type:"single",get value(){return Ne()},onValueChange:d=>{Ne(d),be()()},children:(d,w)=>{var m=Na(),c=n(m);ht(c,{class:"w-[250px]",children:(E,ne)=>{bt(E,{placeholder:"Select template"})},$$slots:{default:!0}});var P=t(c,2);xt(P,{class:"w-[250px]",children:(E,ne)=>{var R=Ua(),H=n(R);ct(H,{children:(k,h)=>{vt(k,{value:"all",children:(p,D)=>{u();var _=$("All Templates");e(p,_)},$$slots:{default:!0}})},$$slots:{default:!0}});var z=t(H,2);{var q=k=>{var h=me(),p=n(h);Me(p,1,()=>[...new Set(ee().filter(D=>D.name!=="all").map(D=>D.category||"Other"))],ze,(D,_)=>{var S=za(),C=n(S);Ot(C,{});var x=t(C,2),L=s(x,!0);l(x);var v=t(x,2);ct(v,{children:(y,b)=>{var g=me(),o=n(g);Me(o,1,()=>ee().filter(i=>i.name!=="all"&&(i.category||"Other")===a(_)),ze,(i,Y)=>{const B=le(()=>a(Y).description||"");vt(i,{get value(){return a(Y).name},get title(){return a(B)},children:(O,pe)=>{u();var A=$();Z(()=>I(A,a(Y).label)),e(O,A)},$$slots:{default:!0}})}),e(y,g)},$$slots:{default:!0}}),Z(()=>I(L,a(_))),e(D,S)}),e(k,h)};M(z,k=>{ee().length>1&&k(q)})}e(E,R)},$$slots:{default:!0}}),e(d,m)},$$slots:{default:!0}}),l(je);var Le=t(je,2),et=t(s(Le),2),ot=s(et);Ut(ot,{children:(d,w)=>{var m=Ia(),c=n(m);Nt(c,{children:(E,ne)=>{_e(E,{variant:"outline",class:"w-[250px] justify-start",children:(R,H)=>{var z=Ba(),q=t(n(z),2);{var k=p=>{var D=$();Z((_,S)=>I(D,`${_??""} - ${S??""}`),[()=>new Date(ce().startDate).toLocaleDateString(),()=>new Date(ce().endDate).toLocaleDateString()],le),e(p,D)},h=p=>{var D=$("Select date range");e(p,D)};M(q,p=>{ce().startDate&&ce().endDate?p(k):p(h,!1)})}e(R,z)},$$slots:{default:!0}})},$$slots:{default:!0}});var P=t(c,2);Bt(P,{class:"w-auto p-0",align:"start",children:(E,ne)=>{Lt(E,{get value(){return Xe()},onValueChange:R=>{if(R!=null&&R.start&&(R!=null&&R.end)){const H=R.start.toDate(mt()),z=R.end.toDate(mt()),q=new CustomEvent("change",{detail:{startDate:H,endDate:z,calendarValue:R}});He()(q)}},numberOfMonths:2})},$$slots:{default:!0}}),e(d,m)},$$slots:{default:!0}}),l(et),l(Le);var tt=t(Le,2),Je=s(tt),oe=t(s(Je),2);_e(oe,{variant:"outline",size:"icon",onclick:()=>be()(),get disabled(){return $e()},class:"h-10 w-10",children:(d,w)=>{var m=Wa(),c=n(m);const P=le(()=>`h-4 w-4 ${$e()?"animate-spin":""}`);Pt(c,{get class(){return a(P)}}),u(2),e(d,m)},$$slots:{default:!0}}),l(Je),l(tt),l(Pe);var f=t(Pe,2);Fe(f,{children:(d,w)=>{var m=lr(),c=n(m);it(c,{class:"flex flex-row items-center justify-between",children:(E,ne)=>{var R=Ya(),H=n(R),z=s(H);dt(z,{children:(h,p)=>{u();var D=$("Email Event Log");e(h,D)},$$slots:{default:!0}});var q=t(z,2);nt(q,{children:(h,p)=>{u();var D=$("Detailed log of email events");e(h,D)},$$slots:{default:!0}}),l(H);var k=t(H,2);_e(k,{variant:"outline",size:"sm",onclick:()=>be()(),get disabled(){return $e()},class:"h-8 w-8 p-0",children:(h,p)=>{var D=qa(),_=n(D);const S=le(()=>`h-4 w-4 ${$e()?"animate-spin":""}`);Pt(_,{get class(){return a(S)}}),u(2),e(h,D)},$$slots:{default:!0}}),e(E,R)},$$slots:{default:!0}});var P=t(c,2);Ae(P,{children:(E,ne)=>{var R=me(),H=n(R);{var z=k=>{var h=Ga();e(k,h)},q=(k,h)=>{{var p=_=>{var S=Ha(),C=t(s(S),2);_e(C,{onclick:()=>be()(),children:(x,L)=>{u();var v=$("Load Email Events");e(x,v)},$$slots:{default:!0}}),l(S),e(_,S)},D=_=>{var S=sr(),C=s(S);At(C,{children:(v,y)=>{var b=Xa(),g=n(b);Mt(g,{children:(i,Y)=>{pt(i,{children:(B,O)=>{var pe=Ja(),A=n(pe);Te(A,{children:(j,Q)=>{u();var J=$("Event");e(j,J)},$$slots:{default:!0}});var he=t(A,2);Te(he,{children:(j,Q)=>{u();var J=$("Email");e(j,J)},$$slots:{default:!0}});var U=t(he,2);Te(U,{children:(j,Q)=>{u();var J=$("Template");e(j,J)},$$slots:{default:!0}});var fe=t(U,2);Te(fe,{children:(j,Q)=>{u();var J=$("Timestamp");e(j,J)},$$slots:{default:!0}});var te=t(fe,2);Te(te,{children:(j,Q)=>{u();var J=$("Details");e(j,J)},$$slots:{default:!0}}),e(B,pe)},$$slots:{default:!0}})},$$slots:{default:!0}});var o=t(g,2);zt(o,{children:(i,Y)=>{var B=me(),O=n(B);Me(O,1,ke,ze,(pe,A)=>{pt(pe,{children:(he,U)=>{var fe=Qa(),te=n(fe);Re(te,{children:(X,ie)=>{var N=Ka(),ae=s(N),de=s(ae);{var ve=re=>{Ue(re,{class:"mr-1 inline-block h-3 w-3"})},K=(re,we)=>{{var De=We=>{Ue(We,{class:"mr-1 inline-block h-3 w-3"})},Ke=(We,St)=>{{var ut=at=>{yt(at,{class:"mr-1 inline-block h-3 w-3"})},It=(at,Wt)=>{{var qt=rt=>{ft(rt,{class:"mr-1 inline-block h-3 w-3"})},Yt=(rt,Gt)=>{{var Ht=st=>{Rt(st,{class:"mr-1 inline-block h-3 w-3"})},Jt=st=>{Ue(st,{class:"mr-1 inline-block h-3 w-3"})};M(rt,st=>{a(A).type==="unsubscribed"?st(Ht):st(Jt,!1)},Gt)}};M(at,rt=>{a(A).type==="bounced"||a(A).type==="complained"?rt(qt):rt(Yt,!1)},Wt)}};M(We,at=>{a(A).type==="clicked"?at(ut):at(It,!1)},St)}};M(re,We=>{a(A).type==="opened"?We(De):We(Ke,!1)},we)}};M(de,re=>{a(A).type==="delivered"?re(ve):re(K,!1)})}var G=t(de);l(ae),l(N),Z((re,we)=>{Zt(ae,1,`rounded-full px-2 py-1 text-xs ${re??""} mr-2`),I(G,` ${we??""}`)},[()=>Ce()(a(A).type),()=>a(A).type.charAt(0).toUpperCase()+a(A).type.slice(1)],le),e(X,N)},$$slots:{default:!0}});var j=t(te,2);Re(j,{children:(X,ie)=>{u();var N=$();Z(()=>I(N,a(A).email)),e(X,N)},$$slots:{default:!0}});var Q=t(j,2);Re(Q,{children:(X,ie)=>{u();var N=$();Z(()=>I(N,a(A).templateName||"-")),e(X,N)},$$slots:{default:!0}});var J=t(Q,2);Re(J,{children:(X,ie)=>{u();var N=$();Z(ae=>I(N,ae),[()=>Ie()(a(A).timestamp)],le),e(X,N)},$$slots:{default:!0}});var xe=t(J,2);Re(xe,{children:(X,ie)=>{var N=me(),ae=n(N);{var de=K=>{_e(K,{variant:"ghost",size:"sm",class:"h-8 px-2",children:(G,re)=>{u();var we=$("View Details");e(G,we)},$$slots:{default:!0}})},ve=K=>{var G=$("-");e(K,G)};M(ae,K=>{a(A).data?K(de):K(ve,!1)})}e(X,N)},$$slots:{default:!0}}),e(he,fe)},$$slots:{default:!0}})}),e(i,B)},$$slots:{default:!0}}),e(v,b)},$$slots:{default:!0}});var x=t(C,2);{var L=v=>{var y=rr(),b=s(y),g=s(b);l(b);var o=t(b,2),i=s(o);const Y=le(()=>W()===1);_e(i,{variant:"outline",size:"sm",get disabled(){return a(Y)},onclick:()=>ge()(W()-1),children:(U,fe)=>{var te=Za(),j=n(te);ea(j,{class:"h-4 w-4"}),u(2),e(U,te)},$$slots:{default:!0}});var B=t(i,2);{var O=U=>{var fe=me(),te=n(fe);Me(te,1,()=>Array(ue()),ze,(j,Q,J)=>{const xe=le(()=>W()===J+1?"default":"outline");_e(j,{get variant(){return a(xe)},size:"sm",onclick:()=>ge()(J+1),children:(X,ie)=>{u();var N=$();N.nodeValue=J+1,e(X,N)},$$slots:{default:!0}})}),e(U,fe)},pe=U=>{var fe=tr(),te=n(fe);const j=le(()=>W()===1?"default":"outline");_e(te,{get variant(){return a(j)},size:"sm",onclick:()=>ge()(1),children:(de,ve)=>{u();var K=$("1");e(de,K)},$$slots:{default:!0}});var Q=t(te,2);{var J=de=>{var ve=Va();e(de,ve)};M(Q,de=>{W()>3&&de(J)})}var xe=t(Q,2);Me(xe,0,()=>Array(3),ze,(de,ve,K)=>{var G=me(),re=n(G);{var we=De=>{const Ke=le(()=>W()===W()-1+K?"default":"outline");_e(De,{get variant(){return a(Ke)},size:"sm",onclick:()=>ge()(W()-1+K),children:(We,St)=>{u();var ut=$();Z(()=>I(ut,W()-1+K)),e(We,ut)},$$slots:{default:!0}})};M(re,De=>{W()-1+K>1&&W()-1+K<ue()&&De(we)})}e(de,G)});var X=t(xe,2);{var ie=de=>{var ve=er();e(de,ve)};M(X,de=>{W()<ue()-2&&de(ie)})}var N=t(X,2);const ae=le(()=>W()===ue()?"default":"outline");_e(N,{get variant(){return a(ae)},size:"sm",onclick:()=>ge()(ue()),children:(de,ve)=>{u();var K=$();Z(()=>I(K,ue())),e(de,K)},$$slots:{default:!0}}),e(U,fe)};M(B,U=>{ue()<=5?U(O):U(pe,!1)})}var A=t(B,2);const he=le(()=>W()===ue());_e(A,{variant:"outline",size:"sm",get disabled(){return a(he)},onclick:()=>ge()(W()+1),children:(U,fe)=>{var te=ar(),j=n(te);ta(j,{class:"h-4 w-4"}),u(2),e(U,te)},$$slots:{default:!0}}),l(o),l(y),Z(U=>I(g,`Showing ${(W()-1)*Be()+1} to ${U??""} of ${Ge()??""} events`),[()=>Math.min(W()*Be(),Ge())],le),e(v,y)};M(x,v=>{ue()>1&&v(L)})}l(S),e(_,S)};M(k,_=>{ke().length===0?_(p):_(D,!1)},h)}};M(H,k=>{$e()?k(z):k(q,!1)})}e(E,R)},$$slots:{default:!0}}),e(d,m)},$$slots:{default:!0}}),e(qe,Ze),Dt()}var ir=r("<!> <!>",1),dr=r("<!> <!>",1),vr=r("<!> <!> <!>",1),cr=r("<!> <!>",1);function Jr(qe,T){wt(T,!0);let ce=ye(!0),Xe=ye(Qe([])),Ne=ye(Qe({total:0,delivered:0,opened:0,clicked:0,bounced:0,complained:0,unsubscribed:0})),V=ye(Qe({startDate:jt(new Date),endDate:new Date}));const ee=jt(new Date),Ye=new Date;let ke=ye(Qe({start:new Et(ee.getFullYear(),ee.getMonth()+1,ee.getDate()),end:new Et(Ye.getFullYear(),Ye.getMonth()+1,Ye.getDate())})),$e="all",W="all",ue=ye(Qe([])),Ge=ye(Qe([])),Be=ye(Qe([])),He=ye(!1),be=ye(1),ge=100,Ce=ye(0),Ie=ye(1);const Ze=[{value:"all",label:"All Events"},{value:"delivered",label:"Delivered"},{value:"opened",label:"Opened"},{value:"clicked",label:"Clicked"},{value:"bounced",label:"Bounced"},{value:"complained",label:"Complained"},{value:"unsubscribed",label:"Unsubscribed"}];Kt(async()=>{await je(),await Pe(),await Ee(),se(ce,!1)});async function Pe(){var f;se(ce,!0);try{const d=new URLSearchParams;a(V).startDate&&a(V).endDate&&(d.append("startDate",_t(a(V).startDate).toISOString()),d.append("endDate",$t(a(V).endDate).toISOString()));const w=d.toString()?`?${d.toString()}`:"",m=await fetch(`/api/email/analytics/stats${w}`);if(m.ok){const c=await m.json();se(Ne,c.stats||{total:0,delivered:0,opened:0,clicked:0,bounced:0,complained:0,unsubscribed:0},!0),se(Ge,c.chartData||[],!0),se(Be,((f=c.topEmails)==null?void 0:f.map(P=>({template:P.template,subject:P.subject||"No Subject",sent:P.sent||0,openRate:P.openRate||0,clickRate:P.clickRate||0})))||[],!0)}else{const c=await m.json();Oe.error(c.error||"Failed to load email stats")}}catch(d){console.error("Error loading email stats:",d),Oe.error("Failed to load email stats")}finally{se(ce,!1)}}async function Ee(){se(ce,!0);try{const f=new URLSearchParams;a(V).startDate&&a(V).endDate&&(f.append("startDate",_t(a(V).startDate).toISOString()),f.append("endDate",$t(a(V).endDate).toISOString())),f.append("page",a(be).toString()),f.append("limit",ge.toString());const d=new URLSearchParams(f);d.append("count","true");const w=await fetch(`/api/email/analytics/events?${d.toString()}`);if(w.ok){const c=await w.json();se(Ce,c.count||0,!0),se(Ie,Math.ceil(a(Ce)/ge)||1,!0)}const m=await fetch(`/api/email/analytics/events?${f.toString()}`);if(m.ok){const c=await m.json();se(Xe,c.map(P=>({id:P.id,email:P.email,templateName:P.templateName,type:P.type,timestamp:P.timestamp,data:P.data})),!0)}else{const c=await m.json();Oe.error(c.error||"Failed to load email events")}}catch(f){console.error("Error loading email events:",f),Oe.error("Failed to load email events")}finally{se(ce,!1)}}function Ve(f){f>=1&&f<=a(Ie)&&(se(be,f,!0),Ee())}async function je(){try{const f=await fetch("/api/email/templates/list");if(f.ok){const d=await f.json();d.allTemplates&&Array.isArray(d.allTemplates)?se(ue,[{name:"all",label:"All Templates"},...d.allTemplates.map(w=>({name:w.name,label:w.label||w.name,category:w.category||"Uncategorized",description:w.description||""}))],!0):se(ue,[{name:"all",label:"All Templates"},{name:"welcome",label:"Welcome Email",category:"Transactional",description:"Sent to new users"},{name:"verification",label:"Email Verification",category:"Transactional",description:"Verify email address"},{name:"password-reset",label:"Password Reset",category:"Transactional",description:"Reset password"}],!0)}else{const d=await f.json();Oe.error(d.error||"Failed to load templates"),se(ue,[{name:"all",label:"All Templates"},{name:"welcome",label:"Welcome Email",category:"Transactional",description:"Sent to new users"},{name:"verification",label:"Email Verification",category:"Transactional",description:"Verify email address"},{name:"password-reset",label:"Password Reset",category:"Transactional",description:"Reset password"}],!0)}}catch(f){console.error("Error loading templates:",f),Oe.error("Failed to load templates"),se(ue,[{name:"all",label:"All Templates"},{name:"welcome",label:"Welcome Email",category:"Transactional",description:"Sent to new users"},{name:"verification",label:"Email Verification",category:"Transactional",description:"Verify email address"},{name:"password-reset",label:"Password Reset",category:"Transactional",description:"Reset password"}],!0)}}function lt(){Pe()}function Le(f){const{startDate:d,endDate:w,calendarValue:m}=f.detail;d&&w&&(console.log("Date range changed:",{startDate:d,endDate:w}),se(V,{startDate:d,endDate:w},!0),m&&se(ke,m,!0),se(be,1),Pe(),Ee())}function et(f){return new Date(f).toLocaleString()}function ot(f){switch(f){case"delivered":return"bg-green-100 text-green-800";case"opened":return"bg-blue-100 text-blue-800";case"clicked":return"bg-purple-100 text-purple-800";case"bounced":return"bg-red-100 text-red-800";case"complained":return"bg-orange-100 text-orange-800";case"unsubscribed":return"bg-gray-100 text-gray-800";default:return"bg-gray-100 text-gray-800"}}async function tt(){se(He,!0);try{const f=new URLSearchParams;a(V).startDate&&a(V).endDate&&(f.append("startDate",_t(a(V).startDate).toISOString()),f.append("endDate",$t(a(V).endDate).toISOString()));const d=await fetch(`/api/email/analytics/export?${f.toString()}`);if(d.ok){const w=await d.blob(),m=window.URL.createObjectURL(w),c=document.createElement("a");c.style.display="none",c.href=m,c.download=`email-analytics-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(c),c.click(),window.URL.revokeObjectURL(m),Oe.success("Data exported successfully")}else{const w=await d.json();Oe.error(w.error||"Failed to export data")}}catch(f){console.error("Error exporting data:",f),Oe.error("Failed to export data")}finally{se(He,!1)}}var Je=me(),oe=n(Je);Se(oe,()=>Fe,(f,d)=>{d(f,{children:(w,m)=>{var c=cr(),P=n(c);Se(P,()=>it,(ne,R)=>{R(ne,{children:(H,z)=>{var q=ir(),k=n(q);Se(k,()=>dt,(p,D)=>{D(p,{children:(_,S)=>{u();var C=$("Email Analytics");e(_,C)},$$slots:{default:!0}})});var h=t(k,2);Se(h,()=>nt,(p,D)=>{D(p,{children:(_,S)=>{u();var C=$("Track and analyze email performance metrics.");e(_,C)},$$slots:{default:!0}})}),e(H,q)},$$slots:{default:!0}})});var E=t(P,2);Se(E,()=>Ae,(ne,R)=>{R(ne,{children:(H,z)=>{var q=me(),k=n(q);Se(k,()=>Qt,(h,p)=>{p(h,{value:"overview",class:"w-full",children:(D,_)=>{var S=vr(),C=n(S);Se(C,()=>Xt,(v,y)=>{y(v,{class:"mb-4",children:(b,g)=>{var o=dr(),i=n(o);Se(i,()=>kt,(B,O)=>{O(B,{value:"overview",children:(pe,A)=>{u();var he=$("Overview");e(pe,he)},$$slots:{default:!0}})});var Y=t(i,2);Se(Y,()=>kt,(B,O)=>{O(B,{value:"events",children:(pe,A)=>{u();var he=$("Event Log");e(pe,he)},$$slots:{default:!0}})}),e(b,o)},$$slots:{default:!0}})});var x=t(C,2);Se(x,()=>Ct,(v,y)=>{y(v,{value:"overview",children:(b,g)=>{Fa(b,{get dateRange(){return a(V)},get calendarDateRange(){return a(ke)},templateFilter:W,get templates(){return a(ue)},get emailStats(){return a(Ne)},get chartData(){return a(Ge)},get topEmails(){return a(Be)},get isLoading(){return a(ce)},get isExporting(){return a(He)},handleCalendarDateChange:Le,handleFilterChange:lt,loadEmailStats:Pe,exportData:tt})},$$slots:{default:!0}})});var L=t(x,2);Se(L,()=>Ct,(v,y)=>{y(v,{value:"events",children:(b,g)=>{nr(b,{get dateRange(){return a(V)},get calendarDateRange(){return a(ke)},templateFilter:W,eventType:$e,get templates(){return a(ue)},get eventTypeOptions(){return Ze},get emailEvents(){return a(Xe)},get isLoading(){return a(ce)},get currentPage(){return a(be)},get totalPages(){return a(Ie)},get totalEvents(){return a(Ce)},itemsPerPage:ge,handleCalendarDateChange:Le,loadEmailEvents:Ee,goToPage:Ve,getEventTypeBadgeClass:ot,formatDate:et})},$$slots:{default:!0}})}),e(D,S)},$$slots:{default:!0}})}),e(H,q)},$$slots:{default:!0}})}),e(w,c)},$$slots:{default:!0}})}),e(qe,Je),Dt()}export{Jr as component};
