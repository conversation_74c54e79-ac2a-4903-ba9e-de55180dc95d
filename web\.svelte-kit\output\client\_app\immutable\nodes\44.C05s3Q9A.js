import{f as E,c as X,a as r,t as H}from"../chunks/BasJTneF.js";import{p as $e,s as t,f as m,c as b,g as S,x as ae,r as $,t as Se,k as Fe,n as B,a as ye,d as be,o as Ae,i as ct}from"../chunks/CGmarHxI.js";import{s as Re}from"../chunks/CIt1g2O9.js";import{i as ge}from"../chunks/u21ee2wt.js";import{e as Te,i as Xe}from"../chunks/C3w0v0gR.js";import{c as e}from"../chunks/BvdI7LR8.js";import{a as ut}from"../chunks/DDUgF6Ik.js";import{g as Ne,s as xe,a as he,b as Ie}from"../chunks/CmxjS0TN.js";import"../chunks/CgXBgsce.js";import{a as Oe}from"../chunks/BiJhC7W5.js";import{s as vt}from"../chunks/B8blszX7.js";import{T as ft,R as mt}from"../chunks/I7hvcB12.js";import{t as re}from"../chunks/DjPYYl4Z.js";import{S as pt}from"../chunks/C6g8ubaU.js";import{b as _t}from"../chunks/5V1tIHTN.js";import{s as De}from"../chunks/Btcx8l8F.js";import{F as K,C as oe,a as Q}from"../chunks/FeejBSkx.js";import{I as Ue}from"../chunks/DMTMHyMa.js";import{T as gt}from"../chunks/VNuMAkuB.js";import{B as nt}from"../chunks/B1K98fMG.js";import{A as ht,a as bt,b as $t}from"../chunks/CE9Bts7j.js";import{u as Ge,a as Me,s as yt,b as xt,c as Pt,g as wt,d as kt,e as Ct,f as Dt}from"../chunks/BA1W9HJN.js";import{U as St}from"../chunks/G5Oo-PmU.js";import{F as G}from"../chunks/ByFxH6T3.js";import{X as At}from"../chunks/CnpHcmx3.js";import{R as Ve,S as ze,a as He,b as Ye}from"../chunks/CGK0g3x_.js";import{S as ue}from"../chunks/D9yI7a4E.js";import{F as Je}from"../chunks/CXvW3J0s.js";import{S as We}from"../chunks/B2lQHLf_.js";import{S as qe}from"../chunks/CVVv9lPb.js";import{s as Le}from"../chunks/B-Xjo-Yt.js";import{s as Ke}from"../chunks/BlYzNxlg.js";import{S as Rt,M as Tt}from"../chunks/aemnuA_0.js";import{M as jt}from"../chunks/2KCyzleV.js";import{C as it}from"../chunks/DLZV8qTT.js";import{U as Et}from"../chunks/B_6ivTD3.js";import{B as Qe}from"../chunks/CDnvByek.js";import{F as Ft}from"../chunks/ChqRiddM.js";import{E as Bt}from"../chunks/7AwcL9ec.js";import{S as It}from"../chunks/BoNCRmBc.js";import{T as Ut}from"../chunks/C88uNE8B.js";import{T as Mt}from"../chunks/DmZyh-PW.js";var Lt=E('<button type="button" class="bg-destructive text-destructive-foreground hover:bg-destructive/90 absolute -right-2 -top-2 rounded-full p-1 shadow-sm" aria-label="Remove profile picture"><!></button>'),Ot=E("<!> ",1),Nt=E('<div class="font-medium">Full Name</div> <!>',1),Vt=E("<!> <!> <!>",1),zt=E('<div class="font-medium">Email Address</div> <!>',1),Ht=E("<!> <!> <!>",1),Yt=E('<div class="font-medium">Phone Number</div> <!>',1),Jt=E("<!> <!> <!>",1),Wt=E('<div class="font-medium">Bio</div> <!>',1),qt=E("<!> <!> <!>",1),Xt=E('<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Personal Information</h4> <p class="text-muted-foreground text-sm">Update your personal details and profile picture.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><div class="flex flex-col items-center space-y-4 sm:flex-row sm:space-x-6 sm:space-y-0"><div class="relative"><!> <!></div> <div class="flex flex-col space-y-2"><div class="text-sm font-medium">Profile Picture</div> <div class="text-muted-foreground text-xs">Upload a photo to personalize your profile.</div> <div class="flex items-center space-x-2"><!> <input type="file" accept="image/*" class="hidden"/></div></div></div> <!> <!> <!> <!></div>',1);function Ze(Z,d){$e(d,!0);const[se,ne]=xe(),y=()=>he(d.formData,"$formData",se);let Y,M=Fe(!1),L=ae(()=>y().profilePicture||null);async function N(o){const l=o.target.files;if(!l||l.length===0)return;const C=l[0];if(!C.type.startsWith("image/")){re.error("Please select an image file");return}if(C.size>5*1024*1024){re.error("Image size should be less than 5MB");return}be(M,!0);try{const k=new FormData;k.append("profilePicture",C);const R=await fetch("/api/profile-picture",{method:"POST",body:k});if(!R.ok)throw new Error(`Server responded with ${R.status}`);const c=await R.json();c.success?(console.log("Upload success, image URL:",c.imageUrl),console.log("Updating profile image in store with:",c.imageUrl),Ge(c.imageUrl),console.log("Invalidating user data to refresh UI"),await Oe("app:user"),d.formData.update(D=>({...D,profilePicture:c.imageUrl})),setTimeout(()=>{console.log("Submitting form with new profile picture");const D=document.getElementById("submit-button");D?(D.click(),re.success("Profile picture updated"),setTimeout(()=>{window.location.reload()},500)):(console.error("Submit button not found"),re.error("Failed to save profile picture"))},100)):re.error(c.error||"Failed to upload profile picture")}catch(k){re.error("Failed to upload profile picture"),console.error("Upload error:",k)}finally{be(M,!1)}}async function W(){try{const o=await fetch("/api/profile-picture",{method:"DELETE"});if(!o.ok)throw new Error(`Server responded with ${o.status}`);const a=await o.json();a.success?(console.log("Profile picture removed successfully"),console.log("Updating profile image in store with null"),Ge(null),console.log("Invalidating user data to refresh UI"),await Oe("app:user"),d.formData.update(l=>({...l,profilePicture:null})),Y&&(Y.value=""),setTimeout(()=>{console.log("Submitting form after removing profile picture");const l=document.getElementById("submit-button");l?(l.click(),re.success("Profile picture removed")):(console.error("Submit button not found"),re.error("Failed to save changes"))},100)):re.error(a.error||"Failed to remove profile picture")}catch(o){re.error("Failed to remove profile picture"),console.error("Remove error:",o)}}var q=Xt(),J=t(m(q),2),j=b(J),u=b(j),P=b(u);e(P,()=>ht,(o,a)=>{a(o,{class:"h-24 w-24",children:(l,C)=>{var k=X(),R=m(k);{var c=f=>{var g=X(),T=m(g);e(T,()=>bt,(I,F)=>{F(I,{get src(){return S(L)},alt:"Profile"})}),r(f,g)},D=f=>{var g=X(),T=m(g);e(T,()=>$t,(I,F)=>{F(I,{class:"text-lg",children:(U,V)=>{B();var O=H();Se(ee=>Re(O,ee),[()=>y().name?y().name.charAt(0).toUpperCase():"U"]),r(U,O)},$$slots:{default:!0}})}),r(f,g)};ge(R,f=>{S(L)?f(c):f(D,!1)})}r(l,k)},$$slots:{default:!0}})});var A=t(P,2);{var h=o=>{var a=Lt();a.__click=W;var l=b(a);At(l,{class:"h-4 w-4"}),$(a),r(o,a)};ge(A,o=>{S(L)&&o(h)})}$(u);var p=t(u,2),v=t(b(p),4),x=b(v);nt(x,{type:"button",variant:"outline",size:"sm",class:"flex items-center gap-2",get disabled(){return S(M)},onclick:()=>Y.click(),children:(o,a)=>{var l=Ot(),C=m(l);St(C,{class:"h-4 w-4"});var k=t(C);Se(()=>Re(k,` ${S(M)?"Uploading...":"Upload"}`)),r(o,l)},$$slots:{default:!0}});var w=t(x,2);w.__change=N,_t(w,o=>Y=o,()=>Y),$(v),$(p),$(j);var _=t(j,2);e(_,()=>K,(o,a)=>{a(o,{get form(){return d.form},name:"name",children:(l,C)=>{var k=Vt(),R=m(k);e(R,()=>oe,(f,g)=>{g(f,{children:(I,F)=>{let U=()=>F==null?void 0:F().props;var V=Nt(),O=t(m(V),2);Ue(O,De(U,{type:"text",get value(){return y().name},set value(ee){Ie(d.formData,Ae(y).name=ee,Ae(y))}})),r(I,V)},$$slots:{default:!0}})});var c=t(R,2);e(c,()=>G,(f,g)=>{g(f,{children:(T,I)=>{B();var F=H("Your name as it appears on your profile");r(T,F)},$$slots:{default:!0}})});var D=t(c,2);e(D,()=>Q,(f,g)=>{g(f,{})}),r(l,k)},$$slots:{default:!0}})});var i=t(_,2);e(i,()=>K,(o,a)=>{a(o,{get form(){return d.form},name:"email",children:(l,C)=>{var k=Ht(),R=m(k);e(R,()=>oe,(f,g)=>{g(f,{children:(I,F)=>{let U=()=>F==null?void 0:F().props;var V=zt(),O=t(m(V),2);Ue(O,De(U,{type:"email",get value(){return y().email}})),r(I,V)},$$slots:{default:!0}})});var c=t(R,2);e(c,()=>G,(f,g)=>{g(f,{children:(T,I)=>{B();var F=H("Your email address (cannot be changed)");r(T,F)},$$slots:{default:!0}})});var D=t(c,2);e(D,()=>Q,(f,g)=>{g(f,{})}),r(l,k)},$$slots:{default:!0}})});var n=t(i,2);e(n,()=>K,(o,a)=>{a(o,{get form(){return d.form},name:"phone",children:(l,C)=>{var k=Jt(),R=m(k);e(R,()=>oe,(f,g)=>{g(f,{children:(I,F)=>{let U=()=>F==null?void 0:F().props;var V=Yt(),O=t(m(V),2);Ue(O,De(U,{type:"tel",get value(){return y().phone},set value(ee){Ie(d.formData,Ae(y).phone=ee,Ae(y))}})),r(I,V)},$$slots:{default:!0}})});var c=t(R,2);e(c,()=>G,(f,g)=>{g(f,{children:(T,I)=>{B();var F=H("Your contact phone number");r(T,F)},$$slots:{default:!0}})});var D=t(c,2);e(D,()=>Q,(f,g)=>{g(f,{})}),r(l,k)},$$slots:{default:!0}})});var s=t(n,2);e(s,()=>K,(o,a)=>{a(o,{get form(){return d.form},name:"bio",children:(l,C)=>{var k=qt(),R=m(k);e(R,()=>oe,(f,g)=>{g(f,{children:(I,F)=>{let U=()=>F==null?void 0:F().props;var V=Wt(),O=t(m(V),2);gt(O,De(U,{get value(){return y().bio},set value(ee){Ie(d.formData,Ae(y).bio=ee,Ae(y))}})),r(I,V)},$$slots:{default:!0}})});var c=t(R,2);e(c,()=>G,(f,g)=>{g(f,{children:(T,I)=>{B();var F=H("A brief description about yourself");r(T,F)},$$slots:{default:!0}})});var D=t(c,2);e(D,()=>Q,(f,g)=>{g(f,{})}),r(l,k)},$$slots:{default:!0}})}),$(J),r(Z,q),ye(),ne()}Ne(["click","change"]);var Gt=E("<!> <!>",1),Kt=E('<div class="space-y-0.5"><!> <!></div> <!>',1),Qt=E("<!> <!> <!>",1),Zt=E('<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Data Collection</div> <!></div> <!></div> <!>',1),er=E('<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Third-Party Data Sharing</div> <!></div> <!></div> <!>',1),tr=E('<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Privacy Settings</h4> <p class="text-muted-foreground text-sm">Control your privacy and data preferences.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><!> <!> <!></div>',1);function et(Z,d){$e(d,!0);const[se,ne]=xe(),y=()=>he(d.formData,"$formData",se),Y=[{value:"public",label:"Public"},{value:"private",label:"Private"}];var M=tr(),L=t(m(M),2),N=b(L);e(N,()=>K,(J,j)=>{j(J,{get form(){return d.form},name:"profileVisibility",children:(u,P)=>{var A=Qt(),h=m(A);e(h,()=>oe,(x,w)=>{w(x,{children:(i,n)=>{let s=()=>n==null?void 0:n().props;var o=Kt(),a=m(o),l=b(a);e(l,()=>Je,(c,D)=>{D(c,{children:(f,g)=>{B();var T=H("Profile Visibility");r(f,T)},$$slots:{default:!0}})});var C=t(l,2);e(C,()=>G,(c,D)=>{D(c,{children:(f,g)=>{B();var T=H("Who can see your profile information");r(f,T)},$$slots:{default:!0}})}),$(a);var k=t(a,2);const R=ae(()=>y().profileVisibility||"public");e(k,()=>Ve,(c,D)=>{D(c,De(s,{type:"single",get value(){return S(R)},onValueChange:f=>{d.formData.update(g=>({...g,profileVisibility:f})),setTimeout(()=>{const g=document.getElementById("submit-button");g==null||g.click()},100)},children:(f,g)=>{var T=Gt(),I=m(T);e(I,()=>ze,(U,V)=>{V(U,{class:"w-full",children:(O,ee)=>{var z=X(),ie=m(z);e(ie,()=>We,(le,de)=>{de(le,{placeholder:"Select visibility"})}),r(O,z)},$$slots:{default:!0}})});var F=t(I,2);e(F,()=>He,(U,V)=>{V(U,{class:"max-h-60",children:(O,ee)=>{var z=X(),ie=m(z);e(ie,()=>qe,(le,de)=>{de(le,{children:(fe,je)=>{var pe=X(),Pe=m(pe);Te(Pe,17,()=>Y,te=>te.value,(te,ve)=>{var _e=X(),we=m(_e);e(we,()=>Ye,(me,ke)=>{ke(me,{get value(){return S(ve).value},get label(){return S(ve).label},children:(Ce,Ee)=>{B();var ce=H();Se(()=>Re(ce,S(ve).label)),r(Ce,ce)},$$slots:{default:!0}})}),r(te,_e)}),r(fe,pe)},$$slots:{default:!0}})}),r(O,z)},$$slots:{default:!0}})}),r(f,T)},$$slots:{default:!0}}))}),r(i,o)},$$slots:{default:!0}})});var p=t(h,2);e(p,()=>G,(x,w)=>{w(x,{children:(_,i)=>{B();var n=H("Who can see your profile information");r(_,n)},$$slots:{default:!0}})});var v=t(p,2);e(v,()=>Q,(x,w)=>{w(x,{})}),r(u,A)},$$slots:{default:!0}})});var W=t(N,2);e(W,()=>K,(J,j)=>{j(J,{get form(){return d.form},name:"allowDataCollection",children:(u,P)=>{var A=Zt(),h=m(A),p=b(h),v=t(b(p),2);e(v,()=>G,(_,i)=>{i(_,{children:(n,s)=>{B();var o=H("Allow us to collect usage data to improve your experience");r(n,o)},$$slots:{default:!0}})}),$(p);var x=t(p,2);e(x,()=>oe,(_,i)=>{i(_,{children:(n,s)=>{var o=X(),a=m(o);const l=ae(()=>!!y().allowDataCollection);e(a,()=>ue,(C,k)=>{k(C,{get checked(){return S(l)},onCheckedChange:R=>{d.formData.update(c=>({...c,allowDataCollection:R})),setTimeout(()=>{const c=document.getElementById("submit-button");c==null||c.click()},100)}})}),r(n,o)},$$slots:{default:!0}})}),$(h);var w=t(h,2);e(w,()=>Q,(_,i)=>{i(_,{})}),r(u,A)},$$slots:{default:!0}})});var q=t(W,2);e(q,()=>K,(J,j)=>{j(J,{get form(){return d.form},name:"allowThirdPartySharing",children:(u,P)=>{var A=er(),h=m(A),p=b(h),v=t(b(p),2);e(v,()=>G,(_,i)=>{i(_,{children:(n,s)=>{B();var o=H("Allow sharing your data with trusted partners");r(n,o)},$$slots:{default:!0}})}),$(p);var x=t(p,2);e(x,()=>oe,(_,i)=>{i(_,{children:(n,s)=>{var o=X(),a=m(o);const l=ae(()=>!!y().allowThirdPartySharing);e(a,()=>ue,(C,k)=>{k(C,{get checked(){return S(l)},onCheckedChange:R=>{d.formData.update(c=>({...c,allowThirdPartySharing:R})),setTimeout(()=>{const c=document.getElementById("submit-button");c==null||c.click()},100)}})}),r(n,o)},$$slots:{default:!0}})}),$(h);var w=t(h,2);e(w,()=>Q,(_,i)=>{i(_,{})}),r(u,A)},$$slots:{default:!0}})}),$(L),r(Z,M),ye(),ne()}var rr=(Z,d)=>d("light"),or=(Z,d)=>d("dark"),ar=(Z,d)=>d("system"),sr=E('<div class="space-y-2"><div class="font-medium">Theme Preference</div> <!> <div class="flex flex-col gap-4 pt-2 sm:flex-row"><button type="button"><!> <span>Light</span></button> <button type="button"><!> <span>Dark</span></button> <button type="button"><!> <span>System</span></button></div></div> <!>',1),nr=E('<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">High Contrast Mode</div> <!></div> <!></div> <!>',1),ir=E('<div class="space-y-4"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Push Notifications</div> <!></div> <!></div></div> <!>',1),lr=E('<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Accessibility Settings</h4> <p class="text-muted-foreground text-sm">Customize your experience for better accessibility.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><!> <!> <!></div>',1);function tt(Z,d){$e(d,!0);const[se,ne]=xe(),y=()=>he(d.formData,"$formData",se),Y=()=>he(Pt,"$store",se);if(y()){y().theme&&Ke(y().theme);const u={};y().highContrast!==void 0&&(u.highContrast=!!y().highContrast),y().reducedMotion!==void 0&&(u.reducedMotion=!!y().reducedMotion),y().largeText!==void 0&&(u.largeText=!!y().largeText),y().screenReader!==void 0&&(u.screenReader=!!y().screenReader),y().viewMode!==void 0&&(u.viewMode=y().viewMode),Object.keys(u).length>0&&Me(u)}function M(u){console.log("handleThemeChange called with:",u),Me({theme:u}),Ke(u),d.formData.update(A=>({...A,theme:u}));const P=document.getElementById("submit-button");P&&P.click()}function L(u,P){console.log(`Changing ${u} to ${P}`),Me({[u]:P}),d.formData.update(h=>({...h,[u]:P}));const A=document.getElementById("submit-button");A&&A.click()}var N=lr(),W=t(m(N),2),q=b(W);e(q,()=>K,(u,P)=>{P(u,{get form(){return d.form},name:"theme",children:(A,h)=>{var p=sr(),v=m(p),x=t(b(v),2);e(x,()=>G,(C,k)=>{k(C,{children:(R,c)=>{B();var D=H("Choose your preferred theme for the application");r(R,D)},$$slots:{default:!0}})});var w=t(x,2),_=b(w);_.__click=[rr,M];var i=b(_);Rt(i,{class:"mb-2 h-6 w-6 text-yellow-500"}),B(2),$(_);var n=t(_,2);n.__click=[or,M];var s=b(n);Tt(s,{class:"mb-2 h-6 w-6 text-blue-400"}),B(2),$(n);var o=t(n,2);o.__click=[ar,M];var a=b(o);jt(a,{class:"mb-2 h-6 w-6 text-gray-500"}),B(2),$(o),$(w),$(v);var l=t(v,2);e(l,()=>Q,(C,k)=>{k(C,{})}),Se(()=>{var C,k,R,c,D,f,g,T,I;Le(_,1,`hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 ${((R=(k=(C=Y())==null?void 0:C.account)==null?void 0:k.accessibility)==null?void 0:R.theme)==="light"?"border-primary":"border-muted"}`),Le(n,1,`hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 ${((f=(D=(c=Y())==null?void 0:c.account)==null?void 0:D.accessibility)==null?void 0:f.theme)==="dark"?"border-primary":"border-muted"}`),Le(o,1,`hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 ${((I=(T=(g=Y())==null?void 0:g.account)==null?void 0:T.accessibility)==null?void 0:I.theme)==="system"?"border-primary":"border-muted"}`)}),r(A,p)},$$slots:{default:!0}})});var J=t(q,2);e(J,()=>K,(u,P)=>{P(u,{get form(){return d.form},name:"highContrast",children:(A,h)=>{var p=nr(),v=m(p),x=b(v),w=t(b(x),2);e(w,()=>G,(n,s)=>{s(n,{children:(o,a)=>{B();var l=H("Increase contrast for better visibility");r(o,l)},$$slots:{default:!0}})}),$(x);var _=t(x,2);e(_,()=>oe,(n,s)=>{s(n,{children:(o,a)=>{const l=ae(()=>!!y().highContrast);ue(o,{get checked(){return S(l)},onCheckedChange:C=>L("highContrast",C)})},$$slots:{default:!0}})}),$(v);var i=t(v,2);e(i,()=>Q,(n,s)=>{s(n,{})}),r(A,p)},$$slots:{default:!0}})});var j=t(J,2);e(j,()=>K,(u,P)=>{P(u,{get form(){return d.form},name:"pushNotifications",children:(A,h)=>{var p=ir(),v=m(p),x=b(v),w=b(x),_=t(b(w),2);e(_,()=>G,(s,o)=>{o(s,{children:(a,l)=>{B();var C=H("Enable or disable browser push notifications");r(a,C)},$$slots:{default:!0}})}),$(w);var i=t(w,2);e(i,()=>oe,(s,o)=>{o(s,{children:(a,l)=>{const C=ae(()=>!!y().pushNotifications);ue(a,{get checked(){return S(C)},onCheckedChange:async k=>{if(k){const R=await yt();if(R.success){d.formData.update(D=>({...D,pushNotifications:!0}));const c=document.getElementById("submit-button");c&&c.click(),re.success("Push notifications enabled successfully!")}else d.formData.update(c=>({...c,pushNotifications:!1})),re.error(R.error||"Failed to enable push notifications")}else{const R=await xt();if(R.success){d.formData.update(D=>({...D,pushNotifications:!1}));const c=document.getElementById("submit-button");c&&c.click(),re.success("Push notifications disabled successfully")}else d.formData.update(c=>({...c,pushNotifications:!0})),re.error(R.error||"Failed to disable push notifications")}}})},$$slots:{default:!0}})}),$(x),$(v);var n=t(v,2);e(n,()=>Q,(s,o)=>{o(s,{})}),r(A,p)},$$slots:{default:!0}})}),$(W),r(Z,N),ye(),ne()}Ne(["click"]);var dr=E("<!> <span>Reset Preferences</span>",1),cr=E('<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Cookie Preferences</h4> <p class="text-muted-foreground text-sm">Manage how we use cookies on our website.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Essential Cookies</div> <p class="text-muted-foreground text-sm">Required for the website to function properly. Cannot be disabled.</p></div> <!></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Functional Cookies</div> <p class="text-muted-foreground text-sm">Enable personalized features and remember your preferences.</p></div> <!></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Analytics Cookies</div> <p class="text-muted-foreground text-sm">Help us understand how visitors use our website.</p></div> <!></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Advertising Cookies</div> <p class="text-muted-foreground text-sm">Used to show you relevant ads on other websites.</p></div> <!></div> <div class="mt-4 text-sm text-gray-500 dark:text-gray-400"><p>For more information about how we use cookies, please see our <a href="/legal/cookie-policy" class="text-primary hover:underline">Cookie Policy</a>.</p></div> <div class="mt-6 border-t border-gray-200 pt-6 dark:border-gray-800"><div class="flex items-center justify-between"><div><h4 class="text-base font-medium">Reset Cookie Preferences</h4> <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This will clear all cookie preferences and show the consent banner again.</p></div> <!></div></div></div>',1);function rt(Z,d){$e(d,!0);const[se,ne]=xe(),y=()=>he(d.formData,"$formData",se);let Y=Fe(null);ct(()=>{if(typeof window<"u"){const s=wt();be(Y,s,!0),d.formData.update(o=>({...o,cookiePreferences:{functional:s.functional,analytics:s.analytics,advertising:s.advertising}}))}});function M(s,o){d.formData.update(a=>{const l={...a.cookiePreferences||{},[s]:o},C={essential:!0,...l};kt(C),be(Y,C,!0),re.success("Cookie preferences updated");const k=document.getElementById("submit-button");return k==null||k.click(),{...a,cookiePreferences:l}})}function L(){Ct(),d.formData.update(o=>({...o,cookiePreferences:{functional:!1,analytics:!1,advertising:!1}})),be(Y,{essential:!0,functional:!1,analytics:!1,advertising:!1},!0);const s=document.getElementById("submit-button");s==null||s.click(),re.success("Cookie preferences reset. Refresh the page to see the consent banner again.")}var N=cr(),W=t(m(N),2),q=b(W),J=t(b(q),2);ue(J,{checked:!0,disabled:!0}),$(q);var j=t(q,2),u=t(b(j),2);const P=ae(()=>{var s;return((s=y().cookiePreferences)==null?void 0:s.functional)??!0});ue(u,{get checked(){return S(P)},onCheckedChange:s=>M("functional",s)}),$(j);var A=t(j,2),h=t(b(A),2);const p=ae(()=>{var s;return((s=y().cookiePreferences)==null?void 0:s.analytics)??!0});ue(h,{get checked(){return S(p)},onCheckedChange:s=>M("analytics",s)}),$(A);var v=t(A,2),x=t(b(v),2);const w=ae(()=>{var s;return((s=y().cookiePreferences)==null?void 0:s.advertising)??!1});ue(x,{get checked(){return S(w)},onCheckedChange:s=>M("advertising",s)}),$(v);var _=t(v,4),i=b(_),n=t(b(i),2);e(n,()=>nt,(s,o)=>{o(s,{variant:"destructive",size:"sm",onclick:L,class:"flex items-center gap-2",children:(a,l)=>{var C=dr(),k=m(C);it(k,{class:"h-4 w-4"}),B(2),r(a,C)},$$slots:{default:!0}})}),$(i),$(_),$(W),r(Z,N),ye(),ne()}var ur=E('<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Auto-Parse Resumes</div> <!></div> <!></div> <!>',1),vr=E('<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Auto-Save Applications</div> <!></div> <!></div> <!>',1),fr=E('<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Application Reminders</div> <!></div> <!></div> <!>',1),mr=E('<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Application Preferences</h4> <p class="text-muted-foreground text-sm">Configure how the application handles your job applications and resumes.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><!> <!> <!></div>',1);function ot(Z,d){$e(d,!0);const[se,ne]=xe(),y=()=>he(d.formData,"$formData",se);function Y(J,j){d.formData.update(u=>({...u,[J]:j})),setTimeout(()=>{const u=document.getElementById("submit-button");u==null||u.click()},100)}var M=mr(),L=t(m(M),2),N=b(L);e(N,()=>K,(J,j)=>{j(J,{get form(){return d.form},name:"autoParseResumes",children:(u,P)=>{var A=ur(),h=m(A),p=b(h),v=t(b(p),2);e(v,()=>G,(_,i)=>{i(_,{children:(n,s)=>{B();var o=H("Automatically parse resume data when uploading new resumes");r(n,o)},$$slots:{default:!0}})}),$(p);var x=t(p,2);e(x,()=>oe,(_,i)=>{i(_,{children:(n,s)=>{const o=ae(()=>!!y().autoParseResumes);ue(n,{get checked(){return S(o)},onCheckedChange:a=>Y("autoParseResumes",a)})},$$slots:{default:!0}})}),$(h);var w=t(h,2);e(w,()=>Q,(_,i)=>{i(_,{})}),r(u,A)},$$slots:{default:!0}})});var W=t(N,2);e(W,()=>K,(J,j)=>{j(J,{get form(){return d.form},name:"autoSaveApplications",children:(u,P)=>{var A=vr(),h=m(A),p=b(h),v=t(b(p),2);e(v,()=>G,(_,i)=>{i(_,{children:(n,s)=>{B();var o=H("Automatically save job applications as you fill them out");r(n,o)},$$slots:{default:!0}})}),$(p);var x=t(p,2);e(x,()=>oe,(_,i)=>{i(_,{children:(n,s)=>{const o=ae(()=>!!y().autoSaveApplications);ue(n,{get checked(){return S(o)},onCheckedChange:a=>Y("autoSaveApplications",a)})},$$slots:{default:!0}})}),$(h);var w=t(h,2);e(w,()=>Q,(_,i)=>{i(_,{})}),r(u,A)},$$slots:{default:!0}})});var q=t(W,2);e(q,()=>K,(J,j)=>{j(J,{get form(){return d.form},name:"applicationReminders",children:(u,P)=>{var A=fr(),h=m(A),p=b(h),v=t(b(p),2);e(v,()=>G,(_,i)=>{i(_,{children:(n,s)=>{B();var o=H("Receive reminders about pending applications and follow-ups");r(n,o)},$$slots:{default:!0}})}),$(p);var x=t(p,2);e(x,()=>oe,(_,i)=>{i(_,{children:(n,s)=>{const o=ae(()=>!!y().applicationReminders);ue(n,{get checked(){return S(o)},onCheckedChange:a=>Y("applicationReminders",a)})},$$slots:{default:!0}})}),$(h);var w=t(h,2);e(w,()=>Q,(_,i)=>{i(_,{})}),r(u,A)},$$slots:{default:!0}})}),$(L),r(Z,M),ye(),ne()}var pr=E("<!> <!>",1),_r=E('<div class="space-y-0.5"><!> <!></div> <!>',1),gr=E("<!> <!>",1),hr=E('<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Show Salary in Listings</div> <!></div> <!></div> <!>',1),br=E('<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Auto-Apply Enabled</div> <!></div> <!></div> <!>',1),$r=E('<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Job Search Preferences</h4> <p class="text-muted-foreground text-sm">Configure your default job search and application preferences.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><!> <!> <!></div>',1);function at(Z,d){$e(d,!0);const[se,ne]=xe(),y=()=>he(d.formData,"$formData",se),Y=[{value:"remote",label:"Remote Only"},{value:"hybrid",label:"Hybrid"},{value:"onsite",label:"On-site Only"},{value:"flexible",label:"Flexible"}];function M(j,u){d.formData.update(P=>({...P,[j]:u})),setTimeout(()=>{const P=document.getElementById("submit-button");P==null||P.click()},100)}var L=$r(),N=t(m(L),2),W=b(N);e(W,()=>K,(j,u)=>{u(j,{get form(){return d.form},name:"defaultRemotePreference",children:(P,A)=>{var h=gr(),p=m(h);e(p,()=>oe,(x,w)=>{w(x,{children:(i,n)=>{let s=()=>n==null?void 0:n().props;var o=_r(),a=m(o),l=b(a);e(l,()=>Je,(c,D)=>{D(c,{children:(f,g)=>{B();var T=H("Default Remote Preference");r(f,T)},$$slots:{default:!0}})});var C=t(l,2);e(C,()=>G,(c,D)=>{D(c,{children:(f,g)=>{B();var T=H("Your preferred work arrangement for job searches");r(f,T)},$$slots:{default:!0}})}),$(a);var k=t(a,2);const R=ae(()=>y().defaultRemotePreference||"hybrid");e(k,()=>Ve,(c,D)=>{D(c,De(s,{type:"single",get value(){return S(R)},onValueChange:f=>M("defaultRemotePreference",f),children:(f,g)=>{var T=pr(),I=m(T);e(I,()=>ze,(U,V)=>{V(U,{class:"w-full",children:(O,ee)=>{var z=X(),ie=m(z);e(ie,()=>We,(le,de)=>{de(le,{placeholder:"Select remote preference"})}),r(O,z)},$$slots:{default:!0}})});var F=t(I,2);e(F,()=>He,(U,V)=>{V(U,{class:"max-h-60",children:(O,ee)=>{var z=X(),ie=m(z);e(ie,()=>qe,(le,de)=>{de(le,{children:(fe,je)=>{var pe=X(),Pe=m(pe);Te(Pe,17,()=>Y,te=>te.value,(te,ve)=>{var _e=X(),we=m(_e);e(we,()=>Ye,(me,ke)=>{ke(me,{get value(){return S(ve).value},get label(){return S(ve).label},children:(Ce,Ee)=>{B();var ce=H();Se(()=>Re(ce,S(ve).label)),r(Ce,ce)},$$slots:{default:!0}})}),r(te,_e)}),r(fe,pe)},$$slots:{default:!0}})}),r(O,z)},$$slots:{default:!0}})}),r(f,T)},$$slots:{default:!0}}))}),r(i,o)},$$slots:{default:!0}})});var v=t(p,2);e(v,()=>Q,(x,w)=>{w(x,{})}),r(P,h)},$$slots:{default:!0}})});var q=t(W,2);e(q,()=>K,(j,u)=>{u(j,{get form(){return d.form},name:"showSalaryInListings",children:(P,A)=>{var h=hr(),p=m(h),v=b(p),x=t(b(v),2);e(x,()=>G,(i,n)=>{n(i,{children:(s,o)=>{B();var a=H("Display salary information when available in job listings");r(s,a)},$$slots:{default:!0}})}),$(v);var w=t(v,2);e(w,()=>oe,(i,n)=>{n(i,{children:(s,o)=>{const a=ae(()=>!!y().showSalaryInListings);ue(s,{get checked(){return S(a)},onCheckedChange:l=>M("showSalaryInListings",l)})},$$slots:{default:!0}})}),$(p);var _=t(p,2);e(_,()=>Q,(i,n)=>{n(i,{})}),r(P,h)},$$slots:{default:!0}})});var J=t(q,2);e(J,()=>K,(j,u)=>{u(j,{get form(){return d.form},name:"autoApplyEnabled",children:(P,A)=>{var h=br(),p=m(h),v=b(p),x=t(b(v),2);e(x,()=>G,(i,n)=>{n(i,{children:(s,o)=>{B();var a=H("Enable automatic job application features (requires premium plan)");r(s,a)},$$slots:{default:!0}})}),$(v);var w=t(v,2);e(w,()=>oe,(i,n)=>{n(i,{children:(s,o)=>{const a=ae(()=>!!y().autoApplyEnabled);ue(s,{get checked(){return S(a)},onCheckedChange:l=>M("autoApplyEnabled",l)})},$$slots:{default:!0}})}),$(p);var _=t(p,2);e(_,()=>Q,(i,n)=>{n(i,{})}),r(P,h)},$$slots:{default:!0}})}),$(N),r(Z,L),ye(),ne()}var yr=E('<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Default Resume Parsing</div> <!></div> <!></div> <!>',1),xr=E('<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Auto-Update Profile</div> <!></div> <!></div> <!>',1),Pr=E("<!> <!>",1),wr=E('<div class="space-y-0.5"><!> <!></div> <!>',1),kr=E("<!> <!>",1),Cr=E('<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Resume Preferences</h4> <p class="text-muted-foreground text-sm">Configure how your resumes are handled and processed.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><!> <!> <!></div>',1);function st(Z,d){$e(d,!0);const[se,ne]=xe(),y=()=>he(d.formData,"$formData",se),Y=[{value:"public",label:"Public"},{value:"private",label:"Private"}];function M(j,u){d.formData.update(P=>({...P,[j]:u})),setTimeout(()=>{const P=document.getElementById("submit-button");P==null||P.click()},100)}var L=Cr(),N=t(m(L),2),W=b(N);e(W,()=>K,(j,u)=>{u(j,{get form(){return d.form},name:"defaultResumeParsingEnabled",children:(P,A)=>{var h=yr(),p=m(h),v=b(p),x=t(b(v),2);e(x,()=>G,(i,n)=>{n(i,{children:(s,o)=>{B();var a=H("Enable resume parsing by default when uploading new resumes");r(s,a)},$$slots:{default:!0}})}),$(v);var w=t(v,2);e(w,()=>oe,(i,n)=>{n(i,{children:(s,o)=>{const a=ae(()=>!!y().defaultResumeParsingEnabled);ue(s,{get checked(){return S(a)},onCheckedChange:l=>M("defaultResumeParsingEnabled",l)})},$$slots:{default:!0}})}),$(p);var _=t(p,2);e(_,()=>Q,(i,n)=>{n(i,{})}),r(P,h)},$$slots:{default:!0}})});var q=t(W,2);e(q,()=>K,(j,u)=>{u(j,{get form(){return d.form},name:"autoUpdateProfileFromResume",children:(P,A)=>{var h=xr(),p=m(h),v=b(p),x=t(b(v),2);e(x,()=>G,(i,n)=>{n(i,{children:(s,o)=>{B();var a=H("Automatically update your profile with parsed resume data");r(s,a)},$$slots:{default:!0}})}),$(v);var w=t(v,2);e(w,()=>oe,(i,n)=>{n(i,{children:(s,o)=>{const a=ae(()=>!!y().autoUpdateProfileFromResume);ue(s,{get checked(){return S(a)},onCheckedChange:l=>M("autoUpdateProfileFromResume",l)})},$$slots:{default:!0}})}),$(p);var _=t(p,2);e(_,()=>Q,(i,n)=>{n(i,{})}),r(P,h)},$$slots:{default:!0}})});var J=t(q,2);e(J,()=>K,(j,u)=>{u(j,{get form(){return d.form},name:"resumePrivacyLevel",children:(P,A)=>{var h=kr(),p=m(h);e(p,()=>oe,(x,w)=>{w(x,{children:(i,n)=>{let s=()=>n==null?void 0:n().props;var o=wr(),a=m(o),l=b(a);e(l,()=>Je,(c,D)=>{D(c,{children:(f,g)=>{B();var T=H("Resume Privacy Level");r(f,T)},$$slots:{default:!0}})});var C=t(l,2);e(C,()=>G,(c,D)=>{D(c,{children:(f,g)=>{B();var T=H("Control who can access your resume information");r(f,T)},$$slots:{default:!0}})}),$(a);var k=t(a,2);const R=ae(()=>y().resumePrivacyLevel||"private");e(k,()=>Ve,(c,D)=>{D(c,De(s,{type:"single",get value(){return S(R)},onValueChange:f=>M("resumePrivacyLevel",f),children:(f,g)=>{var T=Pr(),I=m(T);e(I,()=>ze,(U,V)=>{V(U,{class:"w-full",children:(O,ee)=>{var z=X(),ie=m(z);e(ie,()=>We,(le,de)=>{de(le,{placeholder:"Select privacy level"})}),r(O,z)},$$slots:{default:!0}})});var F=t(I,2);e(F,()=>He,(U,V)=>{V(U,{class:"max-h-60",children:(O,ee)=>{var z=X(),ie=m(z);e(ie,()=>qe,(le,de)=>{de(le,{children:(fe,je)=>{var pe=X(),Pe=m(pe);Te(Pe,17,()=>Y,te=>te.value,(te,ve)=>{var _e=X(),we=m(_e);e(we,()=>Ye,(me,ke)=>{ke(me,{get value(){return S(ve).value},get label(){return S(ve).label},children:(Ce,Ee)=>{B();var ce=H();Se(()=>Re(ce,S(ve).label)),r(Ce,ce)},$$slots:{default:!0}})}),r(te,_e)}),r(fe,pe)},$$slots:{default:!0}})}),r(O,z)},$$slots:{default:!0}})}),r(f,T)},$$slots:{default:!0}}))}),r(i,o)},$$slots:{default:!0}})});var v=t(p,2);e(v,()=>Q,(x,w)=>{w(x,{})}),r(P,h)},$$slots:{default:!0}})}),$(N),r(Z,L),ye(),ne()}var Dr=E('<div class="flex items-center gap-2"><!> <span> </span></div>'),Sr=E('<!> <form method="POST" class="space-y-8"><!> <button id="submit-button" type="submit" class="hidden" aria-label="Save settings"></button></form>',1),Ar=E(`<!> <div class=" flex flex-col justify-between p-6"><div class="flex items-center justify-between"><div class="flex flex-col"><h2 class="text-lg font-semibold">Account Settings</h2> <p class="text-muted-foreground text-sm">Manage your personal information, application preferences, job search settings, and privacy
        options.</p></div></div></div> <!>`,1);function bo(Z,d){$e(d,!0);const[se,ne]=xe(),y=()=>he(q,"$submitting",se),Y=[{id:"personal",label:"Personal",icon:Et,component:Ze},{id:"applications",label:"Applications",icon:Qe,component:ot},{id:"job-search",label:"Job Search",icon:Qe,component:at},{id:"resume",label:"Resume",icon:Ft,component:st},{id:"privacy",label:"Privacy",icon:Bt,component:et},{id:"accessibility",label:"Accessibility",icon:It,component:tt},{id:"cookies",label:"Cookies",icon:it,component:rt}];let M=Fe("personal");const L=vt(d.data.form,{dataType:"json",validationMethod:"auto",taintedMessage:!1,resetForm:!1,applyAction:!0,onUpdated:({form:v})=>{console.log("Form updated:",v.data),v.valid&&re.success("Account settings updated successfully")},onError:({result:v})=>{var x;console.error("Form error:",v),re.error(((x=v==null?void 0:v.error)==null?void 0:x.message)||"Failed to update account settings")}}),{form:N,enhance:W,submitting:q,delayed:J}=L;let j,u=Fe(!1);function P(){clearTimeout(j),j=setTimeout(()=>{if(!y()&&!S(u)){be(u,!0);const v=document.getElementById("submit-button");v==null||v.click(),setTimeout(()=>{be(u,!1)},1e3)}},500)}var A=Ar(),h=m(A);pt(h,{title:"Account Settings - Hirli",description:"Manage your account settings, personal information, application preferences, job search settings, resume preferences, privacy, and accessibility options.",keywords:"account settings, user profile, application preferences, job search, resume settings, privacy settings, accessibility",url:"https://hirli.com/dashboard/settings/account"});var p=t(h,4);e(p,()=>mt,(v,x)=>{x(v,{get value(){return S(M)},onValueChange:w=>be(M,w,!0),children:(w,_)=>{var i=Sr(),n=m(i);e(n,()=>ft,(a,l)=>{l(a,{children:(C,k)=>{var R=X(),c=m(R);Te(c,17,()=>Y,Xe,(D,f)=>{var g=X(),T=m(g);e(T,()=>Ut,(I,F)=>{F(I,{get value(){return S(f).id},children:(U,V)=>{var O=Dr(),ee=b(O);e(ee,()=>S(f).icon,(le,de)=>{de(le,{class:"h-4 w-4"})});var z=t(ee,2),ie=b(z,!0);$(z),$(O),Se(()=>Re(ie,S(f).label)),r(U,O)},$$slots:{default:!0}})}),r(D,g)}),r(C,R)},$$slots:{default:!0}})});var s=t(n,2);s.__change=P;var o=b(s);Te(o,17,()=>Y,Xe,(a,l)=>{var C=X(),k=m(C);e(k,()=>Mt,(R,c)=>{c(R,{get value(){return S(l).id},class:"p-0",children:(D,f)=>{var g=X(),T=m(g);{var I=U=>{Ze(U,{get form(){return L},get formData(){return N}})},F=(U,V)=>{{var O=z=>{ot(z,{get form(){return L},get formData(){return N}})},ee=(z,ie)=>{{var le=fe=>{at(fe,{get form(){return L},get formData(){return N}})},de=(fe,je)=>{{var pe=te=>{st(te,{get form(){return L},get formData(){return N}})},Pe=(te,ve)=>{{var _e=me=>{et(me,{get form(){return L},get formData(){return N}})},we=(me,ke)=>{{var Ce=ce=>{tt(ce,{get form(){return L},get formData(){return N}})},Ee=(ce,lt)=>{{var dt=Be=>{rt(Be,{get form(){return L},get formData(){return N}})};ge(ce,Be=>{S(l).id==="cookies"&&Be(dt)},lt)}};ge(me,ce=>{S(l).id==="accessibility"?ce(Ce):ce(Ee,!1)},ke)}};ge(te,me=>{S(l).id==="privacy"?me(_e):me(we,!1)},ve)}};ge(fe,te=>{S(l).id==="resume"?te(pe):te(Pe,!1)},je)}};ge(z,fe=>{S(l).id==="job-search"?fe(le):fe(de,!1)},ie)}};ge(U,z=>{S(l).id==="applications"?z(O):z(ee,!1)},V)}};ge(T,U=>{S(l).id==="personal"?U(I):U(F,!1)})}r(D,g)},$$slots:{default:!0}})}),r(a,C)}),B(2),$(s),ut(s,(a,l)=>W==null?void 0:W(a,l),()=>({onSubmit:()=>(console.log("Form submitted"),async({result:a,update:l})=>{var C;console.log("Form submission result:",a),a.type==="success"&&(console.log("Form submission successful, updating form"),(C=a.data)!=null&&C.user&&(console.log("Updating user profile store with:",a.data.user),Dt.set({image:a.data.user.image||null,name:a.data.user.name||null,id:a.data.user.id||null,email:a.data.user.email||null,role:a.data.user.role||null}),console.log("Invalidating user data to refresh UI"),await Oe("app:user")),await l(),console.log("Form updated"))})})),r(w,i)},$$slots:{default:!0}})}),r(Z,A),ye(),ne()}Ne(["change"]);export{bo as component};
