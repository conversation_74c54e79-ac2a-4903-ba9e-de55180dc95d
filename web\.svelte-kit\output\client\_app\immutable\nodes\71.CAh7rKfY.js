import{c as Q,a as e,f as d,t as N}from"../chunks/BasJTneF.js";import{p as st,f as l,a as ot,g as a,e as na,x as pe,c as n,au as ya,r as o,t as q,s as r,n as m,k as Ge,v as It,i as Gt,d as y,o as z,l as os,b as ns,m as Ht,h as Cr}from"../chunks/CGmarHxI.js";import{s as C}from"../chunks/CIt1g2O9.js";import{i as ve}from"../chunks/u21ee2wt.js";import{c as i}from"../chunks/BvdI7LR8.js";import{s as Oa,a as Rt,b as Ke,g as Fa,e as $a,j as ls}from"../chunks/CmxjS0TN.js";import{s as Ga}from"../chunks/B8blszX7.js";import{z as er}from"../chunks/CrHU05dq.js";import"../chunks/CgXBgsce.js";import{t as ht}from"../chunks/DjPYYl4Z.js";import{o as Va,s as Ze,l as is,d as ds}from"../chunks/C8B1VUaq.js";import{B as Bt}from"../chunks/DaBofrVv.js";import{b as tr,B as lt}from"../chunks/B1K98fMG.js";import{T as kr,R as Ar}from"../chunks/I7hvcB12.js";import{R as ea,S as ta,a as aa,b as yt}from"../chunks/CGK0g3x_.js";import{R as ar,P as rr,a as sr}from"../chunks/3WmhYGjL.js";import{g as cs,u as vs,a as us,b as fs,C as ms,c as ps,d as _s,e as gs,f as hs,h as $s,i as xs,j as bs,k as ys,l as ws,$ as or,m as xa,n as Ot,S as Ps,R as Ss}from"../chunks/CyaAPBlz.js";import{S as Cs}from"../chunks/C6g8ubaU.js";import{e as bt,i as Ft}from"../chunks/C3w0v0gR.js";import{a as nr}from"../chunks/DDUgF6Ik.js";import{e as wa,r as Xt,a as Vt,b as Dr,s as Ur}from"../chunks/B-Xjo-Yt.js";import{b as Qt}from"../chunks/CzsE_FAw.js";import{p as T,r as wt,s as Dt}from"../chunks/Btcx8l8F.js";import{a as Pa,R as Sa,D as Ra,P as zr}from"../chunks/tdzGgazS.js";import{L as pt}from"../chunks/BvvicRXk.js";import{I as va}from"../chunks/DMTMHyMa.js";import{T as la}from"../chunks/VNuMAkuB.js";import{M as Wa}from"../chunks/Ci8yIwIB.js";import{s as sa,c as At}from"../chunks/ncUU1dSD.js";import{w as jr,b as ct,m as lr}from"../chunks/BfX7a-t9.js";import{u as ir}from"../chunks/CnMg5bH0.js";import{b as Lr}from"../chunks/5V1tIHTN.js";import{C as ks}from"../chunks/CQdOabBG.js";import{n as Ir}from"../chunks/DX6rZLP_.js";import{D as Ca,a as ka,b as Ma,c as Ea}from"../chunks/CKh8VGVX.js";import{S as ra}from"../chunks/B2lQHLf_.js";import{S as Ka}from"../chunks/CVVv9lPb.js";import{C as Yt}from"../chunks/DZCYCPd3.js";import{L as dr}from"../chunks/BhzFx1Wy.js";import{P as ia}from"../chunks/DR5zc253.js";import{a as Br}from"../chunks/BYB878do.js";import{C as qr}from"../chunks/DuGukytH.js";import{C as ba}from"../chunks/T7uRAIbG.js";import{B as cr}from"../chunks/C2AK_5VT.js";import{M as vr}from"../chunks/CwgkX8t9.js";import{c as As}from"../chunks/BHEV2D3b.js";import{S as Xa}from"../chunks/C2MdR6K0.js";import{D as Ua,a as za,R as La}from"../chunks/WD4kvFhR.js";import{S as Ds}from"../chunks/D871oxnv.js";import{E as ur}from"../chunks/8b74MdfD.js";import{D as Jr}from"../chunks/BgDjIxoO.js";import{D as ja}from"../chunks/Dz4exfp3.js";import{D as qt}from"../chunks/Z6UAQTuv.js";import{A as fr}from"../chunks/Cs0qIT7f.js";import{T as js,C as Is,P as Ts,A as Qr,a as Ns,F as Tr,L as Os}from"../chunks/DVGNPJty.js";import{T as Hr}from"../chunks/C33xR25f.js";import{C as mr}from"../chunks/BAIxhb6t.js";import{U as Yr}from"../chunks/BSHZ37s_.js";import{C as Ia}from"../chunks/CKg8MWp_.js";import{C as pr}from"../chunks/-SpbofVw.js";import{B as Fs}from"../chunks/CIPPbbaT.js";import{i as _r}from"../chunks/BIEMS98f.js";import{R as Vs,P as Rs,S as Ms,a as Es,b as Us,c as zs,d as Ls}from"../chunks/CTn0v-X8.js";import{S as Nr}from"../chunks/P6MDDUUJ.js";import{S as Bs}from"../chunks/0ykhD7u6.js";import{X as qs}from"../chunks/CnpHcmx3.js";import{S as Js}from"../chunks/BHzYYMdu.js";import{S as Ta}from"../chunks/DumgozFE.js";import{M as Or}from"../chunks/QtAhPN2H.js";import{C as Na}from"../chunks/DW7T7T22.js";import{T as Da}from"../chunks/C88uNE8B.js";import{B as Fr}from"../chunks/CDnvByek.js";import{T as Vr}from"../chunks/DmZyh-PW.js";import{S as Za}from"../chunks/yW0TxTga.js";import{L as Qs}from"../chunks/CrpvsheG.js";import{E as Hs}from"../chunks/DdoUfFy4.js";import{D as Ys}from"../chunks/tr-scC-m.js";import{U as Ws}from"../chunks/G5Oo-PmU.js";var Ks=d("<div><!></div>");function Zs(P,t){st(t,!0);let v=T(t,"id",19,ir),c=T(t,"ref",15,null),s=T(t,"value",15),u=T(t,"onValueChange",3,Ir),h=T(t,"placeholder",15),$=T(t,"onPlaceholderChange",3,Ir),A=T(t,"weekdayFormat",3,"narrow"),b=T(t,"pagedNavigation",3,!1),be=T(t,"isDateDisabled",3,()=>!1),se=T(t,"isDateUnavailable",3,()=>!1),J=T(t,"fixedWeeks",3,!1),H=T(t,"numberOfMonths",3,1),he=T(t,"locale",3,"en"),le=T(t,"calendarLabel",3,"Event"),M=T(t,"disabled",3,!1),Y=T(t,"readonly",3,!1),me=T(t,"minValue",3,void 0),E=T(t,"maxValue",3,void 0),S=T(t,"preventDeselect",3,!1),x=T(t,"disableDaysOutsideMonth",3,!0),_e=T(t,"initialFocus",3,!1),te=wt(t,["$$slots","$$events","$$legacy","child","children","id","ref","value","onValueChange","placeholder","onPlaceholderChange","weekdayFormat","weekStartsOn","pagedNavigation","isDateDisabled","isDateUnavailable","fixedWeeks","numberOfMonths","locale","calendarLabel","disabled","readonly","minValue","maxValue","preventDeselect","type","disableDaysOutsideMonth","initialFocus"]);const D=cs({defaultValue:s()});function Pe(){h()===void 0&&h(D)}Pe(),jr.pre(()=>h(),()=>{Pe()});function Z(){s()===void 0&&s(t.type==="single"?void 0:[])}Z(),jr.pre(()=>s(),()=>{Z()});const De=vs({id:ct.with(()=>v()),ref:ct.with(()=>c(),re=>c(re)),weekdayFormat:ct.with(()=>A()),weekStartsOn:ct.with(()=>t.weekStartsOn),pagedNavigation:ct.with(()=>b()),isDateDisabled:ct.with(()=>be()),isDateUnavailable:ct.with(()=>se()),fixedWeeks:ct.with(()=>J()),numberOfMonths:ct.with(()=>H()),locale:ct.with(()=>he()),calendarLabel:ct.with(()=>le()),readonly:ct.with(()=>Y()),disabled:ct.with(()=>M()),minValue:ct.with(()=>me()),maxValue:ct.with(()=>E()),disableDaysOutsideMonth:ct.with(()=>x()),initialFocus:ct.with(()=>_e()),placeholder:ct.with(()=>h(),re=>{h(re),$()(re)}),preventDeselect:ct.with(()=>S()),value:ct.with(()=>s(),re=>{s(re),u()(re)}),type:ct.with(()=>t.type),defaultPlaceholder:D}),Oe=pe(()=>lr(te,De.props));var ae=Q(),Te=l(ae);{var j=re=>{var w=Q(),ue=l(w),Ce=na(()=>({props:a(Oe),...De.snippetProps}));sa(ue,()=>t.child,()=>a(Ce)),e(re,w)},ie=re=>{var w=Ks();wa(w,()=>({...a(Oe)}));var ue=n(w);sa(ue,()=>t.children??ya,()=>De.snippetProps),o(w),e(re,w)};ve(Te,re=>{t.child?re(j):re(ie,!1)})}e(P,ae),ot()}var Xs=d("<div><!></div>");function Gs(P,t){st(t,!0);let v=T(t,"ref",15,null),c=T(t,"id",19,ir),s=wt(t,["$$slots","$$events","$$legacy","children","child","ref","id"]);const u=us({id:ct.with(()=>c()),ref:ct.with(()=>v(),se=>v(se))}),h=pe(()=>lr(s,u.props));var $=Q(),A=l($);{var b=se=>{var J=Q(),H=l(J),he=na(()=>({props:a(h),...u.snippetProps}));sa(H,()=>t.child,()=>a(he)),e(se,J)},be=se=>{var J=Xs();wa(J,()=>({...a(h)}));var H=n(J);{var he=M=>{var Y=Q(),me=l(Y);sa(me,()=>t.children??ya,()=>u.snippetProps),e(M,Y)},le=M=>{var Y=N();q(()=>C(Y,u.cell.opts.date.current.day)),e(M,Y)};ve(H,M=>{t.children?M(he):M(le,!1)})}o(J),e(se,J)};ve(A,se=>{t.child?se(b):se(be,!1)})}e(P,$),ot()}var eo=d("<td><!></td>");function to(P,t){st(t,!0);let v=T(t,"ref",15,null),c=T(t,"id",19,ir),s=wt(t,["$$slots","$$events","$$legacy","children","child","ref","id","date","month"]);const u=fs({id:ct.with(()=>c()),ref:ct.with(()=>v(),se=>v(se)),date:ct.with(()=>t.date),month:ct.with(()=>t.month)}),h=pe(()=>lr(s,u.props));var $=Q(),A=l($);{var b=se=>{var J=Q(),H=l(J),he=na(()=>({props:a(h),...u.snippetProps}));sa(H,()=>t.child,()=>a(he)),e(se,J)},be=se=>{var J=eo();wa(J,()=>({...a(h)}));var H=n(J);sa(H,()=>t.children??ya,()=>u.snippetProps),o(J),e(se,J)};ve(A,se=>{t.child?se(b):se(be,!1)})}e(P,$),ot()}var ao=d("<div><!></div>");function ro(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class","children"]);var s=ao();wa(s,h=>({"data-slot":"select-label",class:h,...c}),[()=>At("text-muted-foreground px-2 py-1.5 text-xs",t.class)]);var u=n(s);sa(u,()=>t.children??ya),o(s),Lr(s,h=>v(h),()=>v()),e(P,s),ot()}const so=Va({company:Ze().min(1,{message:"Company is required"}),position:Ze().min(1,{message:"Position is required"}),location:Ze().optional(),appliedDate:Ze().min(1,{message:"Applied date is required"}),status:Ze().min(1,{message:"Status is required"}),nextAction:Ze().optional(),notes:Ze().optional(),url:Ze().url({message:"Must be a valid URL"}).optional().or(is("")),jobType:Ze().min(1,{message:"Job type is required"}),resumeUploaded:Ze().min(1,{message:"Resume status is required"})});var oo=d("<!> <!> <!>",1),no=d("<!> <!>",1),lo=d("<!> <!>",1);function Wr(P,t){st(t,!0);let v=T(t,"ref",15,null),c=T(t,"value",15),s=T(t,"placeholder",15),u=T(t,"weekdayFormat",3,"short"),h=wt(t,["$$slots","$$events","$$legacy","ref","value","placeholder","class","weekdayFormat"]);var $=Q(),A=l($);const b=pe(()=>At("p-3",t.class));i(A,()=>Zs,(be,se)=>{se(be,Dt({get weekdayFormat(){return u()},get class(){return a(b)}},()=>h,{get value(){return c()},set value(H){c(H)},get ref(){return v()},set ref(H){v(H)},get placeholder(){return s()},set placeholder(H){s(H)},children:(H,he)=>{let le=()=>he==null?void 0:he().months,M=()=>he==null?void 0:he().weekdays;var Y=lo(),me=l(Y);i(me,()=>uo,(S,x)=>{x(S,{children:(_e,te)=>{var D=oo(),Pe=l(D);i(Pe,()=>yo,(Oe,ae)=>{ae(Oe,{})});var Z=r(Pe,2);i(Z,()=>po,(Oe,ae)=>{ae(Oe,{})});var De=r(Z,2);i(De,()=>xo,(Oe,ae)=>{ae(Oe,{})}),e(_e,D)},$$slots:{default:!0}})});var E=r(me,2);i(E,()=>mo,(S,x)=>{x(S,{children:(_e,te)=>{var D=Q(),Pe=l(D);bt(Pe,16,le,Z=>Z,(Z,De)=>{var Oe=Q(),ae=l(Oe);i(ae,()=>vo,(Te,j)=>{j(Te,{children:(ie,re)=>{var w=no(),ue=l(w);i(ue,()=>go,($e,p)=>{p($e,{children:(O,U)=>{var oe=Q(),L=l(oe);i(L,()=>Rr,(fe,ye)=>{ye(fe,{class:"flex",children:(g,B)=>{var V=Q(),G=l(V);bt(G,16,M,de=>de,(de,Ne)=>{var Re=Q(),qe=l(Re);i(qe,()=>ho,(Xe,et)=>{et(Xe,{children:(tt,mt)=>{m();var Ye=N();q(ke=>C(Ye,ke),[()=>Ne.slice(0,2)]),e(tt,Ye)},$$slots:{default:!0}})}),e(de,Re)}),e(g,V)},$$slots:{default:!0}})}),e(O,oe)},$$slots:{default:!0}})});var Ce=r(ue,2);i(Ce,()=>_o,($e,p)=>{p($e,{children:(O,U)=>{var oe=Q(),L=l(oe);bt(L,16,()=>De.weeks,fe=>fe,(fe,ye)=>{var g=Q(),B=l(g);i(B,()=>Rr,(V,G)=>{G(V,{class:"mt-2 w-full",children:(de,Ne)=>{var Re=Q(),qe=l(Re);bt(qe,16,()=>ye,Xe=>Xe,(Xe,et)=>{var tt=Q(),mt=l(tt);i(mt,()=>io,(Ye,ke)=>{ke(Ye,{get date(){return et},get month(){return De.value},children:(Me,je)=>{var F=Q(),we=l(F);i(we,()=>co,(R,f)=>{f(R,{})}),e(Me,F)},$$slots:{default:!0}})}),e(Xe,tt)}),e(de,Re)},$$slots:{default:!0}})}),e(fe,g)}),e(O,oe)},$$slots:{default:!0}})}),e(ie,w)},$$slots:{default:!0}})}),e(Z,Oe)}),e(_e,D)},$$slots:{default:!0}})}),e(H,Y)},$$slots:{default:!0}}))}),e(P,$),ot()}function io(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class"]);var s=Q(),u=l(s);const h=pe(()=>At("[&:has([data-selected])]:bg-accent [&:has([data-selected][data-outside-month])]:bg-accent/50 relative size-8 p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([data-selected])]:rounded-md",t.class));i(u,()=>to,($,A)=>{A($,Dt({get class(){return a(h)}},()=>c,{get ref(){return v()},set ref(b){v(b)}}))}),e(P,s),ot()}function co(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class"]);var s=Q(),u=l(s);const h=pe(()=>At(tr({variant:"ghost"}),"size-8 select-none p-0 font-normal","[&[data-today]:not([data-selected])]:bg-accent [&[data-today]:not([data-selected])]:text-accent-foreground","data-selected:bg-primary data-selected:text-primary-foreground data-selected:hover:bg-primary data-selected:hover:text-primary-foreground data-selected:focus:bg-primary data-selected:focus:text-primary-foreground data-selected:opacity-100 dark:data-selected:hover:bg-primary dark:data-selected:focus:bg-primary","data-disabled:text-muted-foreground data-disabled:opacity-50","data-unavailable:text-destructive-foreground data-unavailable:line-through","data-[outside-month]:text-muted-foreground [&[data-outside-month][data-selected]]:bg-accent/50 [&[data-outside-month][data-selected]]:text-muted-foreground data-[outside-month]:pointer-events-none data-[outside-month]:opacity-50 [&[data-outside-month][data-selected]]:opacity-30",t.class));i(u,()=>Gs,($,A)=>{A($,Dt({get class(){return a(h)}},()=>c,{get ref(){return v()},set ref(b){v(b)}}))}),e(P,s),ot()}function vo(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class"]);var s=Q(),u=l(s);const h=pe(()=>At("w-full border-collapse space-y-1",t.class));i(u,()=>ms,($,A)=>{A($,Dt({get class(){return a(h)}},()=>c,{get ref(){return v()},set ref(b){v(b)}}))}),e(P,s),ot()}function uo(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class"]);var s=Q(),u=l(s);const h=pe(()=>At("relative flex w-full items-center justify-between pt-1",t.class));i(u,()=>ps,($,A)=>{A($,Dt({get class(){return a(h)}},()=>c,{get ref(){return v()},set ref(b){v(b)}}))}),e(P,s),ot()}var fo=d("<div><!></div>");function mo(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class","children"]);var s=fo();wa(s,h=>({class:h,...c}),[()=>At("mt-4 flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0",t.class)]);var u=n(s);sa(u,()=>t.children??ya),o(s),Lr(s,h=>v(h),()=>v()),e(P,s),ot()}function Rr(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class"]);var s=Q(),u=l(s);const h=pe(()=>At("flex",t.class));i(u,()=>_s,($,A)=>{A($,Dt({get class(){return a(h)}},()=>c,{get ref(){return v()},set ref(b){v(b)}}))}),e(P,s),ot()}function po(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class"]);var s=Q(),u=l(s);const h=pe(()=>At("text-sm font-medium",t.class));i(u,()=>gs,($,A)=>{A($,Dt({get class(){return a(h)}},()=>c,{get ref(){return v()},set ref(b){v(b)}}))}),e(P,s),ot()}function _o(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class"]);var s=Q(),u=l(s);const h=pe(()=>At(t.class));i(u,()=>hs,($,A)=>{A($,Dt({get class(){return a(h)}},()=>c,{get ref(){return v()},set ref(b){v(b)}}))}),e(P,s),ot()}function go(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class"]);var s=Q(),u=l(s);const h=pe(()=>At(t.class));i(u,()=>$s,($,A)=>{A($,Dt({get class(){return a(h)}},()=>c,{get ref(){return v()},set ref(b){v(b)}}))}),e(P,s),ot()}function ho(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class"]);var s=Q(),u=l(s);const h=pe(()=>At("text-muted-foreground w-8 rounded-md text-[0.8rem] font-normal",t.class));i(u,()=>xs,($,A)=>{A($,Dt({get class(){return a(h)}},()=>c,{get ref(){return v()},set ref(b){v(b)}}))}),e(P,s),ot()}const $o=P=>{ks(P,{class:"size-4"})};function xo(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class","children"]);var s=Q(),u=l(s);const h=pe(()=>At(tr({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100",t.class)),$=pe(()=>t.children||$o);i(u,()=>bs,(A,b)=>{b(A,Dt({get class(){return a(h)},get children(){return a($)}},()=>c,{get ref(){return v()},set ref(be){v(be)}}))}),e(P,s),ot()}const bo=P=>{ws(P,{class:"size-4"})};function yo(P,t){st(t,!0);let v=T(t,"ref",15,null),c=wt(t,["$$slots","$$events","$$legacy","ref","class","children"]);var s=Q(),u=l(s);const h=pe(()=>At(tr({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100",t.class)),$=pe(()=>t.children||bo);i(u,()=>ys,(A,b)=>{b(A,Dt({get class(){return a(h)},get children(){return a($)}},()=>c,{get ref(){return v()},set ref(be){v(be)}}))}),e(P,s),ot()}var wo=d("<!> <!>",1),Po=d('<p class="text-sm text-red-500"> </p>'),So=d('<p class="text-sm text-red-500"> </p>'),Co=d('<p class="text-sm text-red-500"> </p>'),ko=d("<!> <!>",1),Ao=d('<p class="text-sm text-red-500"> </p>'),Do=d("<!> ",1),jo=d("<!> <!>",1),Io=d('<p class="text-sm text-red-500"> </p>'),To=d("<!> <!>",1),No=d('<p class="text-sm text-red-500"> </p>'),Oo=d("<!> <!>",1),Fo=d("<!> <!>",1),Vo=d('<p class="text-sm text-red-500"> </p>'),Ro=d('<p class="text-sm text-red-500"> </p>'),Mo=d('<p class="text-sm text-red-500"> </p>'),Eo=d('<p class="text-sm text-red-500"> </p>'),Uo=d("<!> Saving...",1),zo=d("<!> Add Application",1),Lo=d('<!> <div class="flex-1 overflow-y-auto"><form method="POST" action="?/addJob"><div class="grid grid-cols-1 gap-4 py-4 md:grid-cols-2"><div class="space-y-2"><!> <!> <input type="hidden" name="company"/> <!></div> <div class="space-y-2"><!> <!> <input type="hidden" name="position"/> <!></div> <div class="space-y-2"><!> <!> <input type="hidden" name="location"/> <!></div> <div class="space-y-2"><!> <!> <input type="hidden" name="jobType"/> <!></div> <div class="space-y-2"><!> <input type="hidden" name="appliedDate"/> <!> <!></div> <div class="space-y-2"><!> <!> <input type="hidden" name="status"/> <!></div> <div class="space-y-2"><!> <!> <input type="hidden" name="resumeUploaded"/> <!></div> <div class="space-y-2"><!> <!> <!></div> <div class="space-y-2"><!> <!> <!></div> <div class="space-y-2 md:col-span-2"><!> <!> <!></div></div> <div class="mt-4 flex justify-end space-x-2"><!> <!></div></form></div>',1);function Bo(P,t){st(t,!0);const[v,c]=Oa(),s=()=>Rt(t.form,"$form",v),u=()=>Rt(t.errors,"$errors",v),h=()=>Rt(t.constraints,"$constraints",v),$=()=>Rt(t.submitting,"$submitting",v);let A=T(t,"open",15),b=Ge(It([])),be=Ge(It([])),se=Ge(It([])),J=Ge(It([])),H=Ge(It([])),he=Ge(It([])),le=Ge(It([])),M=Ge(!1);const Y=new or("en-US",{dateStyle:"medium"});let me=Ge(It(xa(Ot()))),E=Ge(!1);const S=()=>a(b),x=()=>a(se),_e=()=>a(J);Gt(()=>{A()&&!a(M)&&a(b).length===0&&te()});async function te(){if(!a(M)){y(M,!0);try{await Promise.all([D(),Pe(),Z(),De()])}catch(p){console.error("Error loading initial data:",p)}finally{y(M,!1)}}}async function D(){try{const p=await fetch("/api/locations?limit=20");if(p.ok){const O=await p.json();y(b,O.map(U=>{var oe,L;return{value:`${U.id}|${U.name}|${((oe=U.state)==null?void 0:oe.code)||""}|${U.country||"US"}`,label:`${U.name}, ${((L=U.state)==null?void 0:L.code)||U.country||"US"}`}}),!0)}}catch(p){console.error("Error loading locations:",p)}}async function Pe(){try{const p=await fetch("/api/documents?type=resume");if(p.ok){const O=await p.json(),U=O.documents||O;y(be,[{value:"Yes",label:"Yes - Resume uploaded"},{value:"No",label:"No - No resume uploaded"},{value:"N/A",label:"N/A - Not applicable"}],!0),U.length>0&&a(be).push({value:"separator",label:"--- Your Resumes ---"},...U.map(oe=>({value:oe.id,label:`${oe.label||oe.fileName||"Unnamed Resume"}`})))}}catch(p){console.error("Error loading documents:",p),y(be,[{value:"Yes",label:"Yes - Resume uploaded"},{value:"No",label:"No - resume uploaded"},{value:"N/A",label:"N/A - Not applicable"}],!0)}}async function Z(){try{const p=await fetch("/api/occupations?limit=20");if(p.ok){const O=await p.json();y(se,O.map(U=>({value:U.title,label:U.title})),!0)}}catch(p){console.error("Error loading occupations:",p),y(se,[],!0)}}async function De(){try{const p=await fetch("/api/companies?limit=20");if(p.ok){const O=await p.json();y(J,O.map(U=>({value:U.name,label:U.name})),!0)}}catch(p){console.error("Error loading companies:",p),y(J,[],!0)}}async function Oe(p){if(!p||p.length<2)return[...a(se)];try{const O=await fetch(`/api/occupations?search=${encodeURIComponent(p)}&limit=20`);if(O.ok){const oe=(await O.json()).map(L=>({value:L.title,label:L.title}));return oe.length===0?[{value:p,label:`Add "${p}" as custom position`}]:oe}}catch(O){console.error("Error searching occupations:",O)}return[{value:p,label:`Add "${p}" as custom position`}]}async function ae(p){if(!p||p.length<2)return[...a(J)];try{const O=await fetch(`/api/companies?search=${encodeURIComponent(p)}&limit=20`);if(O.ok){const oe=(await O.json()).map(L=>({value:L.name,label:L.name}));return oe.length===0?[{value:p,label:`Add "${p}" as custom company`}]:oe}}catch(O){console.error("Error searching companies:",O)}return[{value:p,label:`Add "${p}" as custom company`}]}async function Te(p){if(!p||p.length<2)return[...a(b)];try{const O=await fetch(`/api/locations?search=${encodeURIComponent(p)}&limit=20`);if(O.ok)return(await O.json()).map(oe=>{var L,fe;return{value:`${oe.id}|${oe.name}|${((L=oe.state)==null?void 0:L.code)||""}|${oe.country||"US"}`,label:`${oe.name}, ${((fe=oe.state)==null?void 0:fe.code)||oe.country||"US"}`}})}catch(O){console.error("Error searching locations:",O)}return[...a(b)]}function j(p){if(y(H,p,!0),p.length>0){const O=p[0].split("|");O.length>=3?Ke(t.form,z(s).location=`${O[1]}, ${O[2]}`,z(s)):Ke(t.form,z(s).location=p[0],z(s))}else Ke(t.form,z(s).location="",z(s))}function ie(p){y(he,p,!0),Ke(t.form,z(s).position=p.join(", "),z(s))}function re(p){y(le,p,!0),Ke(t.form,z(s).company=p.join(", "),z(s))}function w(p){if(p){const O=p.toDate(Ot());Ke(t.form,z(s).appliedDate=O.toISOString().split("T")[0],z(s))}else Ke(t.form,z(s).appliedDate="",z(s))}Gt(()=>{a(me)&&w(a(me))});function ue(){t.reset(),y(H,[],!0),y(he,[],!0),y(le,[],!0),y(me,xa(Ot()),!0),y(E,!1),A(!1)}var Ce=Q(),$e=l(Ce);i($e,()=>Sa,(p,O)=>{O(p,{get open(){return A()},set open(U){A(U)},children:(U,oe)=>{var L=Q(),fe=l(L);i(fe,()=>Pa,(ye,g)=>{g(ye,{class:"flex max-h-[90vh] flex-col overflow-hidden sm:max-w-[600px]",children:(B,V)=>{var G=Lo(),de=l(G);i(de,()=>Ca,(_,ee)=>{ee(_,{children:(K,ca)=>{var Pt=wo(),St=l(Pt);i(St,()=>ka,(ut,_t)=>{_t(ut,{children:(xt,Jt)=>{m();var ft=N("Add New Job Application");e(xt,ft)},$$slots:{default:!0}})});var Ct=r(St,2);i(Ct,()=>Ma,(ut,_t)=>{_t(ut,{children:(xt,Jt)=>{m();var ft=N("Enter the details of your new job application.");e(xt,ft)},$$slots:{default:!0}})}),e(K,Pt)},$$slots:{default:!0}})});var Ne=r(de,2),Re=n(Ne),qe=n(Re),Xe=n(qe),et=n(Xe);pt(et,{for:"company",children:(_,ee)=>{m();var K=N("Company *");e(_,K)},$$slots:{default:!0}});var tt=r(et,2);const mt=pe(_e);Wa(tt,{get options(){return a(mt)},get selectedValues(){return a(le)},placeholder:"Search for companies...",searchPlaceholder:"Search companies...",emptyMessage:"No companies found",width:"w-full",maxDisplayItems:1,searchOptions:ae,onSelectedValuesChange:re});var Ye=r(tt,2);Xt(Ye);var ke=r(Ye,2);{var Me=_=>{var ee=Po(),K=n(ee,!0);o(ee),q(()=>C(K,u().company)),e(_,ee)};ve(ke,_=>{u().company&&_(Me)})}o(Xe);var je=r(Xe,2),F=n(je);pt(F,{for:"position",children:(_,ee)=>{m();var K=N("Position *");e(_,K)},$$slots:{default:!0}});var we=r(F,2);const R=pe(x);Wa(we,{get options(){return a(R)},get selectedValues(){return a(he)},placeholder:"Search for positions...",searchPlaceholder:"Search occupations...",emptyMessage:"No positions found",width:"w-full",maxDisplayItems:1,searchOptions:Oe,onSelectedValuesChange:ie});var f=r(we,2);Xt(f);var k=r(f,2);{var W=_=>{var ee=So(),K=n(ee,!0);o(ee),q(()=>C(K,u().position)),e(_,ee)};ve(k,_=>{u().position&&_(W)})}o(je);var at=r(je,2),Se=n(at);pt(Se,{for:"location",children:(_,ee)=>{m();var K=N("Location");e(_,K)},$$slots:{default:!0}});var Ee=r(Se,2);const Le=pe(S);Wa(Ee,{get options(){return a(Le)},get selectedValues(){return a(H)},placeholder:"Search for cities...",searchPlaceholder:"Search locations...",emptyMessage:"No locations found",width:"w-full",maxDisplayItems:1,searchOptions:Te,onSelectedValuesChange:j});var Ie=r(Ee,2);Xt(Ie);var Ae=r(Ie,2);{var ce=_=>{var ee=Co(),K=n(ee,!0);o(ee),q(()=>C(K,u().location)),e(_,ee)};ve(Ae,_=>{u().location&&_(ce)})}o(at);var X=r(at,2),I=n(X);pt(I,{for:"jobType",children:(_,ee)=>{m();var K=N("Job Type *");e(_,K)},$$slots:{default:!0}});var xe=r(I,2);i(xe,()=>ea,(_,ee)=>{ee(_,{type:"single",get value(){return s().jobType},onValueChange:K=>{Ke(t.form,z(s).jobType=K||"",z(s))},children:(K,ca)=>{var Pt=ko(),St=l(Pt);i(St,()=>ta,(ut,_t)=>{_t(ut,{id:"jobType",class:"w-full",children:(xt,Jt)=>{var ft=Q(),kt=l(ft);i(kt,()=>ra,(Nt,jt)=>{jt(Nt,{placeholder:"Select job type"})}),e(xt,ft)},$$slots:{default:!0}})});var Ct=r(St,2);i(Ct,()=>aa,(ut,_t)=>{_t(ut,{children:(xt,Jt)=>{var ft=Q(),kt=l(ft);i(kt,()=>Ka,(Nt,jt)=>{jt(Nt,{children:(Kt,Aa)=>{var Et=Q(),ua=l(Et);bt(ua,17,()=>t.jobTypes,Ft,(fa,Ut)=>{var Zt=Q(),ma=l(Zt);i(ma,()=>yt,(pa,_a)=>{_a(pa,{get value(){return a(Ut)},children:(zt,oa)=>{m();var Lt=N();q(()=>C(Lt,a(Ut))),e(zt,Lt)},$$slots:{default:!0}})}),e(fa,Zt)}),e(Kt,Et)},$$slots:{default:!0}})}),e(xt,ft)},$$slots:{default:!0}})}),e(K,Pt)},$$slots:{default:!0}})});var ne=r(xe,2);Xt(ne);var ge=r(ne,2);{var Ue=_=>{var ee=Ao(),K=n(ee,!0);o(ee),q(()=>C(K,u().jobType)),e(_,ee)};ve(ge,_=>{u().jobType&&_(Ue)})}o(X);var Ve=r(X,2),Fe=n(Ve);pt(Fe,{for:"appliedDate",children:(_,ee)=>{m();var K=N("Applied Date *");e(_,K)},$$slots:{default:!0}});var Je=r(Fe,2);Xt(Je);var it=r(Je,2);i(it,()=>ar,(_,ee)=>{ee(_,{get open(){return a(E)},set open(K){y(E,K,!0)},children:(K,ca)=>{var Pt=jo(),St=l(Pt);i(St,()=>rr,(ut,_t)=>{_t(ut,{children:(xt,Jt)=>{const ft=pe(()=>At("w-full justify-start text-left font-normal",!a(me)&&"text-muted-foreground"));lt(xt,{id:"appliedDate",variant:"outline",get class(){return a(ft)},children:(kt,Nt)=>{var jt=Do(),Kt=l(jt);Yt(Kt,{class:"mr-2 h-4 w-4"});var Aa=r(Kt);q(Et=>C(Aa,` ${Et??""}`),[()=>a(me)?Y.format(a(me).toDate(Ot())):"Select date"]),e(kt,jt)},$$slots:{default:!0}})},$$slots:{default:!0}})});var Ct=r(St,2);i(Ct,()=>sr,(ut,_t)=>{_t(ut,{class:"w-auto p-0",children:(xt,Jt)=>{const ft=pe(()=>xa(Ot()));Wr(xt,{type:"single",get value(){return a(me)},get maxValue(){return a(ft)},onValueChange:kt=>{y(me,kt,!0),w(kt),y(E,!1)}})},$$slots:{default:!0}})}),e(K,Pt)},$$slots:{default:!0}})});var Be=r(it,2);{var Qe=_=>{var ee=Io(),K=n(ee,!0);o(ee),q(()=>C(K,u().appliedDate)),e(_,ee)};ve(Be,_=>{u().appliedDate&&_(Qe)})}o(Ve);var We=r(Ve,2),vt=n(We);pt(vt,{for:"status",children:(_,ee)=>{m();var K=N("Status *");e(_,K)},$$slots:{default:!0}});var dt=r(vt,2);i(dt,()=>ea,(_,ee)=>{ee(_,{type:"single",get value(){return s().status},onValueChange:K=>{Ke(t.form,z(s).status=K||"",z(s))},children:(K,ca)=>{var Pt=To(),St=l(Pt);i(St,()=>ta,(ut,_t)=>{_t(ut,{id:"status",class:"w-full",children:(xt,Jt)=>{var ft=Q(),kt=l(ft);i(kt,()=>ra,(Nt,jt)=>{jt(Nt,{placeholder:"Select status"})}),e(xt,ft)},$$slots:{default:!0}})});var Ct=r(St,2);i(Ct,()=>aa,(ut,_t)=>{_t(ut,{children:(xt,Jt)=>{var ft=Q(),kt=l(ft);i(kt,()=>Ka,(Nt,jt)=>{jt(Nt,{children:(Kt,Aa)=>{var Et=Q(),ua=l(Et);bt(ua,17,()=>t.jobStatuses,Ft,(fa,Ut)=>{var Zt=Q(),ma=l(Zt);i(ma,()=>yt,(pa,_a)=>{_a(pa,{get value(){return a(Ut)},children:(zt,oa)=>{m();var Lt=N();q(()=>C(Lt,a(Ut))),e(zt,Lt)},$$slots:{default:!0}})}),e(fa,Zt)}),e(Kt,Et)},$$slots:{default:!0}})}),e(xt,ft)},$$slots:{default:!0}})}),e(K,Pt)},$$slots:{default:!0}})});var $t=r(dt,2);Xt($t);var Tt=r($t,2);{var rt=_=>{var ee=No(),K=n(ee,!0);o(ee),q(()=>C(K,u().status)),e(_,ee)};ve(Tt,_=>{u().status&&_(rt)})}o(We);var He=r(We,2),ze=n(He);pt(ze,{for:"resumeUploaded",children:(_,ee)=>{m();var K=N("Resume *");e(_,K)},$$slots:{default:!0}});var gt=r(ze,2);i(gt,()=>ea,(_,ee)=>{ee(_,{type:"single",get value(){return s().resumeUploaded},onValueChange:K=>{Ke(t.form,z(s).resumeUploaded=K||"",z(s))},children:(K,ca)=>{var Pt=Fo(),St=l(Pt);i(St,()=>ta,(ut,_t)=>{_t(ut,{id:"resumeUploaded",class:"w-full",children:(xt,Jt)=>{var ft=Q(),kt=l(ft);i(kt,()=>ra,(Nt,jt)=>{jt(Nt,{placeholder:"Select resume or status"})}),e(xt,ft)},$$slots:{default:!0}})});var Ct=r(St,2);i(Ct,()=>aa,(ut,_t)=>{_t(ut,{children:(xt,Jt)=>{var ft=Q(),kt=l(ft);i(kt,()=>Ka,(Nt,jt)=>{jt(Nt,{children:(Kt,Aa)=>{var Et=Q(),ua=l(Et);bt(ua,17,()=>a(be),Ft,(fa,Ut)=>{var Zt=Q(),ma=l(Zt);{var pa=zt=>{var oa=Oo(),Lt=l(oa);i(Lt,()=>Ps,(ga,ha)=>{ha(ga,{})});var Ha=r(Lt,2);i(Ha,()=>ro,(ga,ha)=>{ha(ga,{class:"text-muted-foreground px-2 py-1 text-xs",children:(Pr,Ya)=>{m();var Sr=N();q(()=>C(Sr,a(Ut).label)),e(Pr,Sr)},$$slots:{default:!0}})}),e(zt,oa)},_a=zt=>{var oa=Q(),Lt=l(oa);i(Lt,()=>yt,(Ha,ga)=>{ga(Ha,{get value(){return a(Ut).value},children:(ha,Pr)=>{m();var Ya=N();q(()=>C(Ya,a(Ut).label)),e(ha,Ya)},$$slots:{default:!0}})}),e(zt,oa)};ve(ma,zt=>{a(Ut).value==="separator"?zt(pa):zt(_a,!1)})}e(fa,Zt)}),e(Kt,Et)},$$slots:{default:!0}})}),e(xt,ft)},$$slots:{default:!0}})}),e(K,Pt)},$$slots:{default:!0}})});var nt=r(gt,2);Xt(nt);var Ba=r(nt,2);{var qa=_=>{var ee=Vo(),K=n(ee,!0);o(ee),q(()=>C(K,u().resumeUploaded)),e(_,ee)};ve(Ba,_=>{u().resumeUploaded&&_(qa)})}o(He);var Mt=r(He,2),Wt=n(Mt);pt(Wt,{for:"url",children:(_,ee)=>{m();var K=N("Job URL");e(_,K)},$$slots:{default:!0}});var da=r(Wt,2);va(da,Dt({id:"url",name:"url",placeholder:"https://example.com/job/123"},()=>h().url,{get value(){return s().url},set value(_){Ke(t.form,z(s).url=_,z(s))}}));var Ja=r(da,2);{var Gr=_=>{var ee=Ro(),K=n(ee,!0);o(ee),q(()=>C(K,u().url)),e(_,ee)};ve(Ja,_=>{u().url&&_(Gr)})}o(Mt);var Qa=r(Mt,2),gr=n(Qa);pt(gr,{for:"nextAction",children:(_,ee)=>{m();var K=N("Next Action");e(_,K)},$$slots:{default:!0}});var hr=r(gr,2);va(hr,Dt({id:"nextAction",name:"nextAction",placeholder:"Follow up with recruiter"},()=>h().nextAction,{get value(){return s().nextAction},set value(_){Ke(t.form,z(s).nextAction=_,z(s))}}));var es=r(hr,2);{var ts=_=>{var ee=Mo(),K=n(ee,!0);o(ee),q(()=>C(K,u().nextAction)),e(_,ee)};ve(es,_=>{u().nextAction&&_(ts)})}o(Qa);var $r=r(Qa,2),xr=n($r);pt(xr,{for:"notes",children:(_,ee)=>{m();var K=N("Notes");e(_,K)},$$slots:{default:!0}});var br=r(xr,2);la(br,Dt({id:"notes",name:"notes",placeholder:"Any additional notes about this application"},()=>h().notes,{get value(){return s().notes},set value(_){Ke(t.form,z(s).notes=_,z(s))}}));var as=r(br,2);{var rs=_=>{var ee=Eo(),K=n(ee,!0);o(ee),q(()=>C(K,u().notes)),e(_,ee)};ve(as,_=>{u().notes&&_(rs)})}o($r),o(qe);var yr=r(qe,2),wr=n(yr);lt(wr,{type:"button",variant:"outline",onclick:ue,children:(_,ee)=>{m();var K=N("Cancel");e(_,K)},$$slots:{default:!0}});var ss=r(wr,2);lt(ss,{type:"submit",get disabled(){return $()},class:"bg-primary text-primary-foreground hover:bg-primary/90",children:(_,ee)=>{var K=Q(),ca=l(K);{var Pt=Ct=>{var ut=Uo(),_t=l(ut);dr(_t,{class:"mr-2 h-4 w-4 animate-spin"}),m(),e(Ct,ut)},St=Ct=>{var ut=zo(),_t=l(ut);ia(_t,{class:"mr-2 h-4 w-4"}),m(),e(Ct,ut)};ve(ca,Ct=>{$()?Ct(Pt):Ct(St,!1)})}e(_,K)},$$slots:{default:!0}}),o(yr),o(Re),nr(Re,(_,ee)=>{var K;return(K=t.enhance)==null?void 0:K.call(t,_,ee)},()=>({onResult:({result:_})=>{_.type==="success"&&ue()}})),o(Ne),Qt(Ye,()=>s().company,_=>Ke(t.form,z(s).company=_,z(s))),Qt(f,()=>s().position,_=>Ke(t.form,z(s).position=_,z(s))),Qt(Ie,()=>s().location,_=>Ke(t.form,z(s).location=_,z(s))),Qt(ne,()=>s().jobType,_=>Ke(t.form,z(s).jobType=_,z(s))),Qt(Je,()=>s().appliedDate,_=>Ke(t.form,z(s).appliedDate=_,z(s))),Qt($t,()=>s().status,_=>Ke(t.form,z(s).status=_,z(s))),Qt(nt,()=>s().resumeUploaded,_=>Ke(t.form,z(s).resumeUploaded=_,z(s))),e(B,G)},$$slots:{default:!0}})}),e(U,L)},$$slots:{default:!0}})}),e(P,Ce),ot(),c()}Va({id:ds(),company:Ze(),position:Ze(),location:Ze(),appliedDate:Ze(),status:Ze(),nextAction:Ze().optional(),notes:Ze().optional(),logo:Ze().optional(),url:Ze().optional(),jobType:Ze(),resumeUploaded:Ze()});const Kr={Saved:"bg-gray-100 text-gray-800",Applied:"bg-blue-100 text-blue-800","Phone Screen":"bg-cyan-100 text-cyan-800",Interview:"bg-purple-100 text-purple-800",Assessment:"bg-yellow-100 text-yellow-800","Final Round":"bg-indigo-100 text-indigo-800",Offer:"bg-green-100 text-green-800",Accepted:"bg-emerald-100 text-emerald-800",Rejected:"bg-red-100 text-red-800"},Mr={Saved:Fs,Applied:pr,"Phone Screen":Ts,Interview:Yt,Assessment:Ia,"Final Round":Yr,Offer:Is,Accepted:js,Rejected:mr};var qo=P=>P.stopPropagation(),Jo=d('<div class="mt-2 border-t pt-2"><a target="_blank" rel="noopener noreferrer" class="text-primary text-xs hover:underline">View Job Posting</a></div>'),Qo=d('<div class="mb-3 flex items-start justify-between"><!></div> <div class="mb-3 flex items-center gap-3"><div class="bg-muted h-10 w-10 flex-shrink-0 overflow-hidden rounded-lg"><img class="h-full w-full object-cover"/></div> <div class="min-w-0 flex-1"><h3 class="truncate text-sm font-semibold"> </h3> <div class="text-muted-foreground flex items-center text-xs"><!> <span class="truncate"> </span></div></div></div> <div class="text-muted-foreground mb-3 flex items-center text-xs"><!> <span class="truncate"> </span></div> <div class="flex items-center justify-between"><div class="text-muted-foreground flex items-center text-xs"><!> <span> </span></div> <!></div> <!>',1);function Ho(P,t){st(t,!0);let v=T(t,"isSelected",3,!1),c=T(t,"onSelectionChange",3,()=>{});const s=pe(()=>`cursor-pointer p-3 hover:shadow-md transition-all ${v()?"ring-2 ring-primary":""}`);function u(h){if(!h)return"N/A";const $=new Date(h);return isNaN($.getTime())?"Invalid date":new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric"}).format($)}qr(P,{get class(){return a(s)},draggable:"true",ondragstart:h=>{var $;($=h.dataTransfer)==null||$.setData("text/plain",t.application.id.toString())},onclick:()=>t.openApplicationDetails(t.application),children:(h,$)=>{var A=Qo(),b=l(A),be=n(b);ba(be,{get checked(){return v()},onCheckedChange:w=>c()(!!w),onclick:w=>w.stopPropagation(),"aria-label":"Select application",class:"mt-0.5"}),o(b);var se=r(b,2),J=n(se),H=n(J);o(J);var he=r(J,2),le=n(he),M=n(le,!0);o(le);var Y=r(le,2),me=n(Y);cr(me,{class:"mr-1 h-3 w-3 flex-shrink-0"});var E=r(me,2),S=n(E,!0);o(E),o(Y),o(he),o(se);var x=r(se,2),_e=n(x);vr(_e,{class:"mr-1 h-3 w-3 flex-shrink-0"});var te=r(_e,2),D=n(te,!0);o(te),o(x);var Pe=r(x,2),Z=n(Pe),De=n(Z);Yt(De,{class:"mr-1 h-3 w-3"});var Oe=r(De,2),ae=n(Oe,!0);o(Oe),o(Z);var Te=r(Z,2);{var j=w=>{Bt(w,{variant:"outline",class:"text-xs",children:(ue,Ce)=>{m();var $e=N();q(()=>C($e,t.application.priority||"Urgent")),e(ue,$e)},$$slots:{default:!0}})};ve(Te,w=>{(t.application.priority||t.application.urgent)&&w(j)})}o(Pe);var ie=r(Pe,2);{var re=w=>{var ue=Jo(),Ce=n(ue);Ce.__click=[qo],o(ue),q(()=>Vt(Ce,"href",t.application.url)),e(w,ue)};ve(ie,w=>{t.application.url&&w(re)})}q(w=>{Vt(H,"src",t.application.logo),Vt(H,"alt",t.application.company),C(M,t.application.position),C(S,t.application.company),C(D,t.application.location),C(ae,w)},[()=>u(t.application.appliedDate)]),e(h,A)},$$slots:{default:!0}}),ot()}Fa(["click"]);function Zr(P,{from:t,to:v},c={}){var{delay:s=0,duration:u=_e=>Math.sqrt(_e)*120,easing:h=As}=c,$=getComputedStyle(P),A=$.transform==="none"?"":$.transform,[b,be]=$.transformOrigin.split(" ").map(parseFloat);b/=P.clientWidth,be/=P.clientHeight;var se=Yo(P),J=P.clientWidth/v.width/se,H=P.clientHeight/v.height/se,he=t.left+t.width*b,le=t.top+t.height*be,M=v.left+v.width*b,Y=v.top+v.height*be,me=(he-M)*J,E=(le-Y)*H,S=t.width/v.width,x=t.height/v.height;return{delay:s,duration:typeof u=="function"?u(Math.sqrt(me*me+E*E)):u,easing:h,css:(_e,te)=>{var D=te*me,Pe=te*E,Z=_e+te*S,De=_e+te*x;return`transform: ${A} translate(${D}px, ${Pe}px) scale(${Z}, ${De});`}}}function Yo(P){if("currentCSSZoom"in P)return P.currentCSSZoom;for(var t=P,v=1;t!==null;)v*=+getComputedStyle(t).zoom,t=t.parentElement;return v}var Wo=d("<!> Actions",1),Ko=d("<!> ",1),Zo=d("<!> Archive Selected",1),Xo=d("<!> Delete Selected",1),Go=d("<!> <!> <!> <!> <!> <!>",1),en=d("<!> <!>",1),tn=d('<div class=" pb-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><!> <!></div> <div class="flex items-center gap-2"><!></div></div></div>'),an=d('<div class="animate-fade-in-up"><!></div>'),rn=d('<div class="text-muted-foreground flex h-32 items-center justify-center align-middle"><div class="flex flex-col items-center justify-center gap-0 text-center"><!> <p class="mt-2 text-sm">No items yet</p></div></div>'),sn=d('<section class="border-border/30 hover:border-primary/50 hover:bg-muted/20 h-full space-y-3 rounded-lg border-2 border-dashed p-2 transition-all duration-300"><!> <!></section>'),on=d('<div class="floating-card hover-lift animate-fade-in-up flex w-80 flex-col" role="region"><div class="gradient-card border-b p-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><!> <h3 class="text-foreground text-sm font-semibold uppercase tracking-wide"> </h3></div> <!></div></div> <div class="flex-1 overflow-y-auto p-2"><!></div></div>'),nn=d('<div class="flex h-full min-w-max gap-2 p-2"></div>'),ln=d("<!> <!>",1);function dn(P,t){st(t,!0);let v=T(t,"onFinalize",3,(S,x)=>{}),c=T(t,"selectedItems",19,()=>new Set),s=T(t,"onSelectionChange",3,()=>{}),u=T(t,"columnVisibility",19,()=>({})),h=T(t,"onBulkMove",3,(S,x)=>{});const $=pe(()=>t.columns.filter(S=>u()[S.id]!==!1).map(S=>({...S,items:[...t.groupedApplications[S.id]||[]].map(x=>({...x,columnId:S.id}))}))),A=pe(()=>a($).flatMap(S=>S.items)),b=pe(()=>a(A).length>0&&a(A).every(S=>c().has(S.id.toString()))),be=pe(()=>a(A).some(S=>c().has(S.id.toString())));function se(){a(b)?a(A).forEach(S=>{s()(S.id.toString(),!1)}):a(A).forEach(S=>{s()(S.id.toString(),!0)})}function J(S){const x=S.items;x.every(te=>c().has(te.id.toString()))?x.forEach(te=>{s()(te.id.toString(),!1)}):x.forEach(te=>{s()(te.id.toString(),!0)})}function H(S){return S.items.length>0&&S.items.every(x=>c().has(x.id.toString()))}function he(S){return S.items.some(x=>c().has(x.id.toString()))}const le=300;var M=ln(),Y=l(M);{var me=S=>{var x=tn(),_e=n(x),te=n(_e),D=n(te);const Pe=pe(()=>a(be)&&!a(b));ba(D,{get checked(){return a(b)},get indeterminate(){return a(Pe)},onCheckedChange:se,"aria-label":"Select all applications"});var Z=r(D,2);Bt(Z,{variant:"secondary",class:"gradient-primary shadow-colored text-white",children:(ae,Te)=>{m();var j=N();q(()=>C(j,`${c().size??""} selected`)),e(ae,j)},$$slots:{default:!0}}),o(te);var De=r(te,2),Oe=n(De);i(Oe,()=>La,(ae,Te)=>{Te(ae,{children:(j,ie)=>{var re=en(),w=l(re);i(w,()=>Ua,(Ce,$e)=>{$e(Ce,{children:(p,O)=>{lt(p,{variant:"outline",size:"sm",class:"hover-lift",children:(U,oe)=>{var L=Wo(),fe=l(L);ur(fe,{class:"mr-2 h-4 w-4"}),m(),e(U,L)},$$slots:{default:!0}})},$$slots:{default:!0}})});var ue=r(w,2);i(ue,()=>za,(Ce,$e)=>{$e(Ce,{align:"end",children:(p,O)=>{var U=Go(),oe=l(U);i(oe,()=>Jr,(V,G)=>{G(V,{children:(de,Ne)=>{m();var Re=N("Move Selected To");e(de,Re)},$$slots:{default:!0}})});var L=r(oe,2);i(L,()=>ja,(V,G)=>{G(V,{})});var fe=r(L,2);bt(fe,17,()=>t.columns,Ft,(V,G)=>{var de=Q(),Ne=l(de);i(Ne,()=>qt,(Re,qe)=>{qe(Re,{class:"interactive",onclick:()=>{const Xe=Array.from(c());h()(a(G).id,Xe)},children:(Xe,et)=>{var tt=Ko(),mt=l(tt);fr(mt,{class:"mr-2 h-4 w-4"});var Ye=r(mt);q(()=>C(Ye,` ${a(G).name??""}`)),e(Xe,tt)},$$slots:{default:!0}})}),e(V,de)});var ye=r(fe,2);i(ye,()=>ja,(V,G)=>{G(V,{})});var g=r(ye,2);i(g,()=>qt,(V,G)=>{G(V,{class:"interactive",children:(de,Ne)=>{var Re=Zo(),qe=l(Re);Qr(qe,{class:"mr-2 h-4 w-4"}),m(),e(de,Re)},$$slots:{default:!0}})});var B=r(g,2);i(B,()=>qt,(V,G)=>{G(V,{class:"text-destructive interactive",children:(de,Ne)=>{var Re=Xo(),qe=l(Re);Hr(qe,{class:"mr-2 h-4 w-4"}),m(),e(de,Re)},$$slots:{default:!0}})}),e(p,U)},$$slots:{default:!0}})}),e(j,re)},$$slots:{default:!0}})}),o(De),o(_e),o(x),e(S,x)};ve(Y,S=>{c().size>0&&S(me)})}var E=r(Y,2);Xa(E,{orientation:"horizontal",class:"h-[calc(100vh-157px)] w-full",children:(S,x)=>{var _e=nn();bt(_e,23,()=>a($),te=>te.id,(te,D,Pe)=>{var Z=on(),De=n(Z),Oe=n(De),ae=n(Oe),Te=n(ae);const j=pe(()=>H(a(D))),ie=pe(()=>he(a(D))&&!H(a(D)));ba(Te,{get checked(){return a(j)},get indeterminate(){return a(ie)},onCheckedChange:()=>J(a(D)),get"aria-label"(){return`Select all in ${a(D).name??""}`}});var re=r(Te,2),w=n(re,!0);o(re),o(ae);var ue=r(ae,2);Bt(ue,{variant:"secondary",class:"gradient-accent shadow-soft text-xs",children:(p,O)=>{m();var U=N();q(()=>C(U,a(D).items.length)),e(p,U)},$$slots:{default:!0}}),o(Oe),o(De);var Ce=r(De,2),$e=n(Ce);Xa($e,{orientation:"vertical",class:"h-full w-full",children:(p,O)=>{var U=sn(),oe=n(U);bt(oe,27,()=>a(D).items,ye=>ye.id,(ye,g,B)=>{var V=an(),G=n(V);const de=pe(()=>c().has(a(g).id.toString()));Ho(G,{get application(){return a(g)},get openApplicationDetails(){return t.openApplicationDetails},get isSelected(){return a(de)},onSelectionChange:Ne=>s()(a(g).id.toString(),Ne)}),o(V),q(()=>Dr(V,`animation-delay: ${a(Pe)*100+a(B)*50}ms`)),Br(V,()=>Zr,()=>({duration:le})),e(ye,V)});var L=r(oe,2);{var fe=ye=>{var g=rn(),B=n(g),V=n(B);Ds(V,{class:"text-2xl opacity-50"}),m(2),o(B),o(g),e(ye,g)};ve(L,ye=>{a(D).items.length===0&&ye(fe)})}o(U),q(()=>Vt(U,"aria-label",`Drop zone for ${a(D).name??""}`)),$a("dragover",U,ye=>{ye.preventDefault(),ye.currentTarget.classList.add("border-primary","bg-primary/5")}),$a("dragleave",U,ye=>{ye.currentTarget.classList.remove("border-primary","bg-primary/5")}),$a("drop",U,ye=>{ye.preventDefault(),ye.currentTarget.classList.remove("border-primary","bg-primary/5");const g=ye.dataTransfer.getData("text/plain");if(g){let B=null,V=null;for(const G of a($)){const de=G.items.find(Ne=>Ne.id.toString()===g);if(de){B={...de},V=G.id;break}}B&&V!==a(D).id&&(B.status=a(D).id,v()(a(D).id,[B]))}}),e(p,U)},$$slots:{default:!0}}),o(Ce),o(Z),q(()=>{Dr(Z,`animation-delay: ${a(Pe)*100}ms`),Vt(Z,"aria-label",`Column ${a(D).name}`),C(w,a(D).name)}),e(te,Z)}),o(_e),e(S,_e)},$$slots:{default:!0}}),e(P,M),ot()}var cn=(P,t)=>t.openApplicationDetails(t.application),vn=(P,t)=>P.key==="Enter"&&t.openApplicationDetails(t.application),un=d("<!> ",1),fn=d('<div class="flex items-center gap-4"><div class="flex-shrink-0"><!></div> <div class="bg-muted h-12 w-12 flex-shrink-0 overflow-hidden rounded-lg"><img class="h-full w-full object-cover"/></div> <div class="min-w-0 flex-1"><h3 class="truncate text-base font-semibold"> </h3> <div class="text-muted-foreground mt-1 flex items-center text-sm"><!> <span class="truncate"> </span></div> <div class="text-muted-foreground flex items-center text-sm"><!> <span class="truncate"> </span></div></div> <div class="flex items-center gap-8 text-center"><div class="min-w-[80px]"><div class="text-muted-foreground text-xs">Applied</div> <div class="text-sm font-medium"> </div></div> <div class="min-w-[100px]"><div class="text-muted-foreground mb-1 text-xs">Status</div> <!></div> <div class="flex-shrink-0"><!></div></div></div>'),mn=d('<div role="button" tabindex="0" draggable="true" class="w-full text-left"><!></div>');function pn(P,t){st(t,!0);let v=T(t,"isSelected",3,!1),c=T(t,"onSelectionChange",3,()=>{});const s=pe(()=>`cursor-pointer p-4 hover:shadow-md transition-all ${v()?"ring-2 ring-primary":""}`);var u=mn();u.__click=[cn,t],u.__keydown=[vn,t];var h=n(u);qr(h,{get class(){return a(s)},children:($,A)=>{var b=fn(),be=n(b),se=n(be);ba(se,{get checked(){return v()},onCheckedChange:w=>c()(!!w),onclick:w=>w.stopPropagation(),"aria-label":"Select application"}),o(be);var J=r(be,2),H=n(J);o(J);var he=r(J,2),le=n(he),M=n(le,!0);o(le);var Y=r(le,2),me=n(Y);cr(me,{class:"mr-1 h-3 w-3 flex-shrink-0"});var E=r(me,2),S=n(E,!0);o(E),o(Y);var x=r(Y,2),_e=n(x);vr(_e,{class:"mr-1 h-3 w-3 flex-shrink-0"});var te=r(_e,2),D=n(te,!0);o(te),o(x),o(he);var Pe=r(he,2),Z=n(Pe),De=r(n(Z),2),Oe=n(De,!0);o(De),o(Z);var ae=r(Z,2),Te=r(n(ae),2);const j=pe(()=>`${Kr[t.application.status]||"bg-gray-100 text-gray-800"} flex items-center gap-1`);Bt(Te,{get class(){return a(j)},children:(w,ue)=>{var Ce=un(),$e=l(Ce);{var p=U=>{var oe=Q();const L=pe(()=>Mr[t.application.status]);var fe=l(oe);i(fe,()=>a(L),(ye,g)=>{g(ye,{class:"h-3 w-3"})}),e(U,oe)};ve($e,U=>{Mr[t.application.status]&&U(p)})}var O=r($e);q(()=>C(O,` ${t.application.status??""}`)),e(w,Ce)},$$slots:{default:!0}}),o(ae);var ie=r(ae,2),re=n(ie);lt(re,{variant:"ghost",size:"icon",class:"h-8 w-8",children:(w,ue)=>{ur(w,{class:"h-4 w-4"})},$$slots:{default:!0}}),o(ie),o(Pe),o(b),q(()=>{Vt(H,"src",t.application.logo),Vt(H,"alt",t.application.company),C(M,t.application.position),C(S,t.application.company),C(D,t.application.location),C(Oe,t.application.appliedDate)}),e($,b)},$$slots:{default:!0}}),o(u),q(()=>Vt(u,"aria-label",`${t.application.position} at ${t.application.company}`)),$a("dragstart",u,$=>{$.dataTransfer.setData("text/plain",t.application.id.toString()),$.dataTransfer.effectAllowed="move",$.currentTarget.classList.add("dragging")}),$a("dragend",u,$=>{$.currentTarget.classList.remove("dragging")}),e(P,u),ot()}Fa(["click","keydown"]);var _n=d("<!> Actions",1),gn=d("<!> ",1),hn=d("<!> Archive Selected",1),$n=d("<!> Delete Selected",1),xn=d("<!> <!> <!> <!> <!> <!>",1),bn=d("<!> <!>",1),yn=d('<div class="bg-muted/50 border-b p-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><!> <!></div> <div class="flex items-center gap-2"><!></div></div></div>'),wn=d("<div><!></div>"),Pn=d('<div class="flex h-32 items-center justify-center text-center"><div><p class="text-muted-foreground">No applications found</p> <p class="text-muted-foreground text-sm">Try adjusting your filters</p></div></div>'),Sn=d('<div class="space-y-3 p-4"><!></div>'),Cn=d("<!> <!>",1);function Er(P,t){st(t,!0);let v=T(t,"filteredApplications",19,()=>[]),c=T(t,"selectedItems",19,()=>new Set),s=T(t,"onSelectionChange",3,()=>{}),u=T(t,"onBulkMove",3,(le,M)=>{}),h=T(t,"columns",19,()=>[]);const $=300,A=pe(()=>v().length>0&&v().every(le=>c().has(le.id.toString()))),b=pe(()=>v().some(le=>c().has(le.id.toString())));function be(){a(A)?v().forEach(le=>{s()(le.id.toString(),!1)}):v().forEach(le=>{s()(le.id.toString(),!0)})}var se=Cn(),J=l(se);{var H=le=>{var M=yn(),Y=n(M),me=n(Y),E=n(me);const S=pe(()=>a(b)&&!a(A));ba(E,{get checked(){return a(A)},get indeterminate(){return a(S)},onCheckedChange:be,"aria-label":"Select all applications"});var x=r(E,2);Bt(x,{variant:"secondary",children:(D,Pe)=>{m();var Z=N();q(()=>C(Z,`${c().size??""} selected`)),e(D,Z)},$$slots:{default:!0}}),o(me);var _e=r(me,2),te=n(_e);i(te,()=>La,(D,Pe)=>{Pe(D,{children:(Z,De)=>{var Oe=bn(),ae=l(Oe);i(ae,()=>Ua,(j,ie)=>{ie(j,{children:(re,w)=>{lt(re,{variant:"outline",size:"sm",children:(ue,Ce)=>{var $e=_n(),p=l($e);ur(p,{class:"mr-2 h-4 w-4"}),m(),e(ue,$e)},$$slots:{default:!0}})},$$slots:{default:!0}})});var Te=r(ae,2);i(Te,()=>za,(j,ie)=>{ie(j,{children:(re,w)=>{var ue=xn(),Ce=l(ue);i(Ce,()=>Jr,(L,fe)=>{fe(L,{children:(ye,g)=>{m();var B=N("Move Selected To");e(ye,B)},$$slots:{default:!0}})});var $e=r(Ce,2);i($e,()=>ja,(L,fe)=>{fe(L,{})});var p=r($e,2);bt(p,17,h,Ft,(L,fe)=>{var ye=Q(),g=l(ye);i(g,()=>qt,(B,V)=>{V(B,{onclick:()=>{const G=Array.from(c());u()(a(fe).id,G)},children:(G,de)=>{var Ne=gn(),Re=l(Ne);fr(Re,{class:"mr-2 h-4 w-4"});var qe=r(Re);q(()=>C(qe,` ${a(fe).name??""}`)),e(G,Ne)},$$slots:{default:!0}})}),e(L,ye)});var O=r(p,2);i(O,()=>ja,(L,fe)=>{fe(L,{})});var U=r(O,2);i(U,()=>qt,(L,fe)=>{fe(L,{children:(ye,g)=>{var B=hn(),V=l(B);Qr(V,{class:"mr-2 h-4 w-4"}),m(),e(ye,B)},$$slots:{default:!0}})});var oe=r(U,2);i(oe,()=>qt,(L,fe)=>{fe(L,{class:"text-destructive",children:(ye,g)=>{var B=$n(),V=l(B);Hr(V,{class:"mr-2 h-4 w-4"}),m(),e(ye,B)},$$slots:{default:!0}})}),e(re,ue)},$$slots:{default:!0}})}),e(Z,Oe)},$$slots:{default:!0}})}),o(_e),o(Y),o(M),e(le,M)};ve(J,le=>{c().size>0&&le(H)})}var he=r(J,2);Xa(he,{class:"h-[600px] w-full",children:(le,M)=>{var Y=Sn(),me=n(Y);{var E=x=>{var _e=Q(),te=l(_e);bt(te,25,v,D=>D.id,(D,Pe)=>{var Z=wn(),De=n(Z);const Oe=pe(()=>c().has(a(Pe).id.toString()));pn(De,{get application(){return a(Pe)},get openApplicationDetails(){return t.openApplicationDetails},get isSelected(){return a(Oe)},onSelectionChange:ae=>s()(a(Pe).id.toString(),ae)}),o(Z),Br(Z,()=>Zr,()=>({duration:$})),e(D,Z)}),e(x,_e)},S=x=>{var _e=Pn();e(x,_e)};ve(me,x=>{v().length>0?x(E):x(S,!1)})}o(Y),e(le,Y)},$$slots:{default:!0}}),e(P,se),ot()}var kn=d('<div class="bg-primary h-2 w-2 rounded-sm"></div>'),An=d('<div class="flex items-center"><div class="border-primary mr-2 flex h-4 w-4 items-center justify-center rounded border"><!></div> <span> </span></div>'),Dn=d("<!> <!>",1);function jn(P,t){st(t,!0);let v=T(t,"tableModel",3,null),c=T(t,"viewMode",3,"list"),s=T(t,"kanbanColumnVisibility",15);const u=[{id:"company",label:"Company"},{id:"position",label:"Position"},{id:"status",label:"Status"},{id:"jobType",label:"Job Type"},{id:"location",label:"Location"},{id:"appliedDate",label:"Applied Date"},{id:"nextAction",label:"Next Action"}],h=[{id:"Saved",label:"Saved"},{id:"Applied",label:"Applied"},{id:"Phone Screen",label:"Phone Screen"},{id:"Interview",label:"Interview"},{id:"Assessment",label:"Assessment"},{id:"Final Round",label:"Final Round"},{id:"Offer",label:"Offer"},{id:"Accepted",label:"Accepted"},{id:"Rejected",label:"Rejected"}],$=pe(()=>c()==="kanban"?h:u);function A(J){var H,he,le;if(c()==="kanban")s(s()[J]=!s()[J],!0),s({...s()});else if(v()){const M=(le=(he=(H=v()).getAllColumns)==null?void 0:he.call(H))==null?void 0:le.find(Y=>Y.id===J);M!=null&&M.toggleVisibility&&M.toggleVisibility()}}function b(J){var H,he,le;if(c()==="kanban")return s()[J]??!0;if(v()){const M=(le=(he=(H=v()).getAllColumns)==null?void 0:he.call(H))==null?void 0:le.find(Y=>Y.id===J);if(M!=null&&M.getIsVisible)return M.getIsVisible()}return!0}var be=Q(),se=l(be);i(se,()=>La,(J,H)=>{H(J,{children:(he,le)=>{var M=Dn(),Y=l(M);i(Y,()=>Ua,(E,S)=>{S(E,{children:(x,_e)=>{lt(x,{variant:"outline",children:(te,D)=>{Ns(te,{class:"h-4 w-4"})},$$slots:{default:!0}})},$$slots:{default:!0}})});var me=r(Y,2);i(me,()=>za,(E,S)=>{S(E,{align:"end",class:"w-[150px]",children:(x,_e)=>{var te=Q(),D=l(te);bt(D,17,()=>a($),Ft,(Pe,Z)=>{var De=Q(),Oe=l(De);i(Oe,()=>qt,(ae,Te)=>{Te(ae,{onclick:()=>A(a(Z).id),children:(j,ie)=>{var re=An(),w=n(re),ue=n(w);{var Ce=O=>{var U=kn();e(O,U)};ve(ue,O=>{b(a(Z).id)&&O(Ce)})}o(w);var $e=r(w,2),p=n($e,!0);o($e),o(re),q(()=>C(p,a(Z).label)),e(j,re)},$$slots:{default:!0}})}),e(Pe,De)}),e(x,te)},$$slots:{default:!0}})}),e(he,M)},$$slots:{default:!0}})}),e(P,be),ot()}const In=Va({stageName:Ze().min(1,{message:"Interview stage is required"}),stageDate:Ze().min(1,{message:"Date is required"}),outcome:Ze().optional().nullable(),feedback:Ze().optional().nullable(),interviewers:Ze().optional().nullable(),duration:Ze().optional().nullable(),notes:Ze().optional().nullable(),nextAction:Ze().optional().nullable()}),Tn={stageName:"",stageDate:new Date().toISOString().split("T")[0],outcome:"",feedback:"",interviewers:"",duration:"",notes:"",nextAction:""},Nn=Va({question:Ze().min(1,{message:"Question is required"}),category:Ze().min(1,{message:"Category is required"}),difficulty:Ze().optional().nullable(),userResponse:Ze().optional().nullable(),userConfidence:Ze().optional().nullable(),notes:Ze().optional().nullable()}),On={question:"",category:"",difficulty:"",userResponse:"",userConfidence:"",notes:""};var Fn=d("<!> <!>",1),Vn=d("<!> <!>",1),Rn=d('<p class="text-destructive text-sm"> </p>'),Mn=d("<!> ",1),En=d("<!> <!>",1),Un=d('<p class="text-destructive text-sm"> </p>'),zn=d("<!> <!>",1),Ln=d("<!> Saving...",1),Bn=d("<!> <!>",1),qn=d('<!> <form method="POST" class="space-y-4"><p class="text-muted-foreground mb-2 text-sm">Fields marked with * are required</p> <!> <div class="space-y-2"><!> <!> <!></div> <div class="space-y-2"><!> <input type="hidden" name="stageDate"/> <!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <!></form>',1),Jn=d("<!> <!>",1);function Qn(P,t){st(t,!0);const[v,c]=Oa(),s=()=>Rt(b,"$form",v),u=()=>Rt(be,"$errors",v),h=()=>Rt(J,"$submitting",v);let $=T(t,"open",3,!1);const A=new or("en-US",{dateStyle:"long"}),{form:b,errors:be,enhance:se,submitting:J}=Ga(Tn,{validators:er(In),dataType:"json",onSubmit:async()=>{try{const E=a(H)?a(H).toDate(Ot()):new Date,S=await fetch(`/api/applications/${t.applicationId}/interviews`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({stageName:s().stageName,stageDate:E,outcome:s().outcome||null,feedback:s().feedback||null,interviewers:s().interviewers||null,duration:s().duration?parseInt(s().duration,10):null,notes:s().notes||null,nextAction:s().nextAction||null})});if(!S.ok){const x=await S.json();throw new Error(x.error||"Failed to create interview")}ht.success("Interview added successfully"),t.onSuccess();return}catch(E){console.error("Error creating interview:",E),ht.error(E.message||"Failed to create interview");return}}});let H=Ge(It(xa(Ot())));function he(E){E?Ke(b,z(s).stageDate=E.toString(),z(s)):Ke(b,z(s).stageDate="",z(s))}Gt(()=>{a(H)&&he(a(H))});const le=[{value:"Phone Screen",label:"Phone Screen"},{value:"Technical Interview",label:"Technical Interview"},{value:"Behavioral Interview",label:"Behavioral Interview"},{value:"Onsite Interview",label:"Onsite Interview"},{value:"Final Interview",label:"Final Interview"},{value:"HR Interview",label:"HR Interview"},{value:"Case Study",label:"Case Study"},{value:"Coding Challenge",label:"Coding Challenge"},{value:"Take-home Assignment",label:"Take-home Assignment"},{value:"Other",label:"Other"}],M=[{value:"Scheduled",label:"Scheduled"},{value:"Pending",label:"Pending"},{value:"Passed",label:"Passed"},{value:"Failed",label:"Failed"}];var Y=Q(),me=l(Y);i(me,()=>Sa,(E,S)=>{S(E,{get open(){return $()},children:(x,_e)=>{var te=Jn(),D=l(te);i(D,()=>Ra,(Z,De)=>{De(Z,{})});var Pe=r(D,2);i(Pe,()=>Pa,(Z,De)=>{De(Z,{class:"sm:max-w-[500px]",children:(Oe,ae)=>{var Te=qn(),j=l(Te);i(j,()=>Ca,(f,k)=>{k(f,{children:(W,at)=>{var Se=Fn(),Ee=l(Se);i(Ee,()=>ka,(Ie,Ae)=>{Ae(Ie,{children:(ce,X)=>{m();var I=N("Add Interview");e(ce,I)},$$slots:{default:!0}})});var Le=r(Ee,2);i(Le,()=>Ma,(Ie,Ae)=>{Ae(Ie,{children:(ce,X)=>{m();var I=N("Record details about an interview stage for this application.");e(ce,I)},$$slots:{default:!0}})}),e(W,Se)},$$slots:{default:!0}})});var ie=r(j,2),re=r(n(ie),2);ve(re,f=>{});var w=r(re,2),ue=n(w);pt(ue,{for:"stageName",children:(f,k)=>{m();var W=N("Interview Stage*");e(f,W)},$$slots:{default:!0}});var Ce=r(ue,2);i(Ce,()=>ea,(f,k)=>{k(f,{type:"single",get value(){return s().stageName},set value(W){Ke(b,z(s).stageName=W,z(s))},children:(W,at)=>{var Se=Vn(),Ee=l(Se);i(Ee,()=>ta,(Ie,Ae)=>{Ae(Ie,{class:"h-10 w-full px-3 py-2",children:(ce,X)=>{var I=Q(),xe=l(I);i(xe,()=>ra,(ne,ge)=>{ge(ne,{placeholder:"Select interview stage"})}),e(ce,I)},$$slots:{default:!0}})});var Le=r(Ee,2);i(Le,()=>aa,(Ie,Ae)=>{Ae(Ie,{class:"z-50 max-h-60 w-full overflow-y-auto",children:(ce,X)=>{var I=Q(),xe=l(I);bt(xe,17,()=>le,Ft,(ne,ge)=>{var Ue=Q(),Ve=l(Ue);i(Ve,()=>yt,(Fe,Je)=>{Je(Fe,{get value(){return a(ge).value},children:(it,Be)=>{m();var Qe=N();q(()=>C(Qe,a(ge).label)),e(it,Qe)},$$slots:{default:!0}})}),e(ne,Ue)}),e(ce,I)},$$slots:{default:!0}})}),e(W,Se)},$$slots:{default:!0}})});var $e=r(Ce,2);{var p=f=>{var k=Rn(),W=n(k,!0);o(k),q(()=>C(W,u().stageName)),e(f,k)};ve($e,f=>{u().stageName&&f(p)})}o(w);var O=r(w,2),U=n(O);pt(U,{for:"stageDate",children:(f,k)=>{m();var W=N("Date*");e(f,W)},$$slots:{default:!0}});var oe=r(U,2);Xt(oe);var L=r(oe,2);i(L,()=>ar,(f,k)=>{k(f,{children:(W,at)=>{var Se=En(),Ee=l(Se);i(Ee,()=>rr,(Ie,Ae)=>{Ae(Ie,{children:(ce,X)=>{const I=pe(()=>At("w-full justify-start text-left font-normal",!a(H)&&"text-muted-foreground"));lt(ce,{id:"stageDate",variant:"outline",get class(){return a(I)},children:(xe,ne)=>{var ge=Mn(),Ue=l(ge);Yt(Ue,{class:"mr-2 h-4 w-4"});var Ve=r(Ue);q(Fe=>C(Ve,` ${Fe??""}`),[()=>a(H)?A.format(a(H).toDate(Ot())):"Select date"]),e(xe,ge)},$$slots:{default:!0}})},$$slots:{default:!0}})});var Le=r(Ee,2);i(Le,()=>sr,(Ie,Ae)=>{Ae(Ie,{class:"w-auto p-0",children:(ce,X)=>{Wr(ce,{type:"single",get value(){return a(H)},onValueChange:I=>{y(H,I,!0),he(I),setTimeout(()=>{const xe=document.getElementById("stageDate");xe&&xe.click()},100)},initialFocus:!0})},$$slots:{default:!0}})}),e(W,Se)},$$slots:{default:!0}})});var fe=r(L,2);{var ye=f=>{var k=Un(),W=n(k,!0);o(k),q(()=>C(W,u().stageDate)),e(f,k)};ve(fe,f=>{u().stageDate&&f(ye)})}o(O);var g=r(O,2),B=n(g);pt(B,{for:"outcome",children:(f,k)=>{m();var W=N("Outcome");e(f,W)},$$slots:{default:!0}});var V=r(B,2);i(V,()=>ea,(f,k)=>{k(f,{type:"single",get value(){return s().outcome},set value(W){Ke(b,z(s).outcome=W,z(s))},children:(W,at)=>{var Se=zn(),Ee=l(Se);i(Ee,()=>ta,(Ie,Ae)=>{Ae(Ie,{class:"h-10 w-full px-3 py-2",children:(ce,X)=>{var I=Q(),xe=l(I);i(xe,()=>ra,(ne,ge)=>{ge(ne,{placeholder:"Select outcome"})}),e(ce,I)},$$slots:{default:!0}})});var Le=r(Ee,2);i(Le,()=>aa,(Ie,Ae)=>{Ae(Ie,{class:"z-50 max-h-60 w-full overflow-y-auto",children:(ce,X)=>{var I=Q(),xe=l(I);bt(xe,17,()=>M,Ft,(ne,ge)=>{var Ue=Q(),Ve=l(Ue);i(Ve,()=>yt,(Fe,Je)=>{Je(Fe,{get value(){return a(ge).value},children:(it,Be)=>{m();var Qe=N();q(()=>C(Qe,a(ge).label)),e(it,Qe)},$$slots:{default:!0}})}),e(ne,Ue)}),e(ce,I)},$$slots:{default:!0}})}),e(W,Se)},$$slots:{default:!0}})}),o(g);var G=r(g,2),de=n(G);pt(de,{for:"interviewers",children:(f,k)=>{m();var W=N("Interviewers");e(f,W)},$$slots:{default:!0}});var Ne=r(de,2);va(Ne,{get value(){return s().interviewers},set value(f){Ke(b,z(s).interviewers=f,z(s))}}),o(G);var Re=r(G,2),qe=n(Re);pt(qe,{for:"duration",children:(f,k)=>{m();var W=N("Duration (minutes)");e(f,W)},$$slots:{default:!0}});var Xe=r(qe,2);va(Xe,{type:"number",get value(){return s().duration},set value(f){Ke(b,z(s).duration=f,z(s))}}),o(Re);var et=r(Re,2),tt=n(et);pt(tt,{for:"feedback",children:(f,k)=>{m();var W=N("Feedback");e(f,W)},$$slots:{default:!0}});var mt=r(tt,2);la(mt,{get value(){return s().feedback},set value(f){Ke(b,z(s).feedback=f,z(s))}}),o(et);var Ye=r(et,2),ke=n(Ye);pt(ke,{for:"nextAction",children:(f,k)=>{m();var W=N("Next Action");e(f,W)},$$slots:{default:!0}});var Me=r(ke,2);la(Me,{get value(){return s().nextAction},set value(f){Ke(b,z(s).nextAction=f,z(s))}}),o(Ye);var je=r(Ye,2),F=n(je);pt(F,{for:"notes",children:(f,k)=>{m();var W=N("Notes");e(f,W)},$$slots:{default:!0}});var we=r(F,2);la(we,{get value(){return s().notes},set value(f){Ke(b,z(s).notes=f,z(s))}}),o(je);var R=r(je,2);i(R,()=>Ea,(f,k)=>{k(f,{children:(W,at)=>{var Se=Bn(),Ee=l(Se);lt(Ee,{type:"button",variant:"outline",get onclick(){return t.onClose},children:(ce,X)=>{m();var I=N("Cancel");e(ce,I)},$$slots:{default:!0}});var Le=r(Ee,2);const Ie=pe(()=>h()||!s().stageName||s().stageName.trim()===""),Ae=pe(()=>!s().stageName||s().stageName.trim()===""?"cursor-not-allowed opacity-50":"");lt(Le,{type:"submit",get disabled(){return a(Ie)},get class(){return a(Ae)},children:(ce,X)=>{var I=Q(),xe=l(I);{var ne=Ue=>{var Ve=Ln(),Fe=l(Ve);dr(Fe,{class:"mr-2 h-4 w-4 animate-spin"}),m(),e(Ue,Ve)},ge=Ue=>{var Ve=N("Save Interview");e(Ue,Ve)};ve(xe,Ue=>{h()?Ue(ne):Ue(ge,!1)})}e(ce,I)},$$slots:{default:!0}}),e(W,Se)},$$slots:{default:!0}})}),o(ie),nr(ie,(f,k)=>se==null?void 0:se(f,k),()=>({onSubmit:()=>!s().stageName||s().stageName.trim()===""?(ht.error("Please select an interview stage"),!1):!0})),Qt(oe,()=>s().stageDate,f=>Ke(b,z(s).stageDate=f,z(s))),e(Oe,Te)},$$slots:{default:!0}})}),e(x,te)},$$slots:{default:!0}})}),e(P,Y),ot(),c()}var Hn=d("<!> <!>",1),Yn=d('<p class="text-destructive text-sm"> </p>'),Wn=d("<!> <!>",1),Kn=d('<p class="text-destructive text-sm"> </p>'),Zn=d("<!> Saving...",1),Xn=d("<!> <!>",1),Gn=d('<!> <form method="POST" class="space-y-4"><p class="text-muted-foreground mb-2 text-sm">Fields marked with * are required</p> <div class="space-y-2"><!> <!> <!></div> <div class="space-y-2"><!> <!> <!></div> <div class="space-y-2"><div class="flex items-center justify-between"><!> <span class="text-muted-foreground text-sm"> </span></div> <!> <div class="text-muted-foreground mt-1 flex justify-between text-xs"><span>Very Easy</span> <span>Very Hard</span></div></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><div class="flex items-center justify-between"><!> <span class="text-muted-foreground text-sm"> </span></div> <!> <div class="text-muted-foreground mt-1 flex justify-between text-xs"><span>Not Confident</span> <span>Very Confident</span></div></div> <div class="space-y-2"><!> <!></div> <!></form>',1),el=d("<!> <!>",1);function tl(P,t){st(t,!0);const[v,c]=Oa(),s=()=>Rt(A,"$form",v),u=()=>Rt(b,"$errors",v),h=()=>Rt(se,"$submitting",v);let $=T(t,"open",3,!1);const{form:A,errors:b,enhance:be,submitting:se}=Ga(On,{validators:er(Nn),dataType:"json",onSubmit:async()=>{try{const me=await fetch(`/api/applications/${t.applicationId}/interviews/${t.interviewId}/questions`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({question:s().question,category:s().category,difficulty:s().difficulty?parseInt(s().difficulty,10):null,userResponse:s().userResponse||null,userConfidence:s().userConfidence?parseInt(s().userConfidence,10):null,notes:s().notes||null})});if(!me.ok){const E=await me.json();throw new Error(E.error||"Failed to add question")}ht.success("Question added successfully"),t.onSuccess();return}catch(me){console.error("Error adding question:",me),ht.error(me.message||"Failed to add question");return}}}),J=[{value:"Technical",label:"Technical"},{value:"Behavioral",label:"Behavioral"},{value:"Problem Solving",label:"Problem Solving"},{value:"System Design",label:"System Design"},{value:"Coding",label:"Coding"},{value:"Cultural Fit",label:"Cultural Fit"},{value:"Background",label:"Background"},{value:"Experience",label:"Experience"},{value:"Other",label:"Other"}],H=[{value:"1",label:"Very Easy"},{value:"2",label:"Easy"},{value:"3",label:"Medium"},{value:"4",label:"Hard"},{value:"5",label:"Very Hard"}],he=[{value:"1",label:"Not Confident"},{value:"2",label:"Slightly Confident"},{value:"3",label:"Moderately Confident"},{value:"4",label:"Confident"},{value:"5",label:"Very Confident"}];let le=Ge(!1);Gt(()=>{y(le,s().question&&s().question.trim()!==""&&s().category&&s().category.trim()!=="",!0)});var M=Q(),Y=l(M);i(Y,()=>Sa,(me,E)=>{E(me,{get open(){return $()},children:(S,x)=>{var _e=el(),te=l(_e);i(te,()=>Ra,(Pe,Z)=>{Z(Pe,{})});var D=r(te,2);i(D,()=>Pa,(Pe,Z)=>{Z(Pe,{class:"sm:max-w-[500px]",children:(De,Oe)=>{var ae=Gn(),Te=l(ae);i(Te,()=>Ca,(R,f)=>{f(R,{children:(k,W)=>{var at=Hn(),Se=l(at);i(Se,()=>ka,(Le,Ie)=>{Ie(Le,{children:(Ae,ce)=>{m();var X=N("Add Interview Question");e(Ae,X)},$$slots:{default:!0}})});var Ee=r(Se,2);i(Ee,()=>Ma,(Le,Ie)=>{Ie(Le,{children:(Ae,ce)=>{m();var X=N("Record a question asked during the interview.");e(Ae,X)},$$slots:{default:!0}})}),e(k,at)},$$slots:{default:!0}})});var j=r(Te,2),ie=r(n(j),2),re=n(ie);pt(re,{for:"question",children:(R,f)=>{m();var k=N("Question*");e(R,k)},$$slots:{default:!0}});var w=r(re,2);la(w,{get value(){return s().question},set value(R){Ke(A,z(s).question=R,z(s))}});var ue=r(w,2);{var Ce=R=>{var f=Yn(),k=n(f,!0);o(f),q(()=>C(k,u().question)),e(R,f)};ve(ue,R=>{u().question&&R(Ce)})}o(ie);var $e=r(ie,2),p=n($e);pt(p,{for:"category",children:(R,f)=>{m();var k=N("Category*");e(R,k)},$$slots:{default:!0}});var O=r(p,2);i(O,()=>ea,(R,f)=>{f(R,{type:"single",get value(){return s().category},set value(k){Ke(A,z(s).category=k,z(s))},children:(k,W)=>{var at=Wn(),Se=l(at);i(Se,()=>ta,(Le,Ie)=>{Ie(Le,{class:"h-10 w-full",children:(Ae,ce)=>{var X=Q(),I=l(X);i(I,()=>ra,(xe,ne)=>{ne(xe,{placeholder:"Select question category"})}),e(Ae,X)},$$slots:{default:!0}})});var Ee=r(Se,2);i(Ee,()=>aa,(Le,Ie)=>{Ie(Le,{class:"z-50 max-h-60 w-full overflow-y-auto",children:(Ae,ce)=>{var X=Q(),I=l(X);bt(I,17,()=>J,Ft,(xe,ne)=>{var ge=Q(),Ue=l(ge);i(Ue,()=>yt,(Ve,Fe)=>{Fe(Ve,{get value(){return a(ne).value},children:(Je,it)=>{m();var Be=N();q(()=>C(Be,a(ne).label)),e(Je,Be)},$$slots:{default:!0}})}),e(xe,ge)}),e(Ae,X)},$$slots:{default:!0}})}),e(k,at)},$$slots:{default:!0}})});var U=r(O,2);{var oe=R=>{var f=Kn(),k=n(f,!0);o(f),q(()=>C(k,u().category)),e(R,f)};ve(U,R=>{u().category&&R(oe)})}o($e);var L=r($e,2),fe=n(L),ye=n(fe);pt(ye,{for:"difficulty",children:(R,f)=>{m();var k=N();q(W=>C(k,`Difficulty: ${W??""}`),[()=>{var W;return s().difficulty?(W=H[parseInt(s().difficulty)-1])==null?void 0:W.label:"Not set"}]),e(R,k)},$$slots:{default:!0}});var g=r(ye,2),B=n(g);o(g),o(fe);var V=r(fe,2);const G=pe(()=>s().difficulty?parseInt(s().difficulty):1);Nr(V,{get value(){return a(G)},onValueChange:R=>{Ke(A,z(s).difficulty=R.toString(),z(s))},min:1,max:5,step:1,class:"py-4"}),m(2),o(L);var de=r(L,2),Ne=n(de);pt(Ne,{for:"userResponse",children:(R,f)=>{m();var k=N("Your Response");e(R,k)},$$slots:{default:!0}});var Re=r(Ne,2);la(Re,{get value(){return s().userResponse},set value(R){Ke(A,z(s).userResponse=R,z(s))}}),o(de);var qe=r(de,2),Xe=n(qe),et=n(Xe);pt(et,{for:"userConfidence",children:(R,f)=>{m();var k=N();q(W=>C(k,`Confidence: ${W??""}`),[()=>{var W;return s().userConfidence?(W=he[parseInt(s().userConfidence)-1])==null?void 0:W.label:"Not set"}]),e(R,k)},$$slots:{default:!0}});var tt=r(et,2),mt=n(tt);o(tt),o(Xe);var Ye=r(Xe,2);const ke=pe(()=>s().userConfidence?parseInt(s().userConfidence):1);Nr(Ye,{get value(){return a(ke)},onValueChange:R=>{Ke(A,z(s).userConfidence=R.toString(),z(s))},min:1,max:5,step:1,class:"py-4"}),m(2),o(qe);var Me=r(qe,2),je=n(Me);pt(je,{for:"notes",children:(R,f)=>{m();var k=N("Notes");e(R,k)},$$slots:{default:!0}});var F=r(je,2);la(F,{get value(){return s().notes},set value(R){Ke(A,z(s).notes=R,z(s))}}),o(Me);var we=r(Me,2);i(we,()=>Ea,(R,f)=>{f(R,{children:(k,W)=>{var at=Xn(),Se=l(at);lt(Se,{type:"button",variant:"outline",get onclick(){return t.onClose},children:(Ae,ce)=>{m();var X=N("Cancel");e(Ae,X)},$$slots:{default:!0}});var Ee=r(Se,2);const Le=pe(()=>h()||!a(le)),Ie=pe(()=>a(le)?"":"cursor-not-allowed opacity-50");lt(Ee,{type:"submit",get disabled(){return a(Le)},get class(){return a(Ie)},children:(Ae,ce)=>{var X=Q(),I=l(X);{var xe=ge=>{var Ue=Zn(),Ve=l(Ue);dr(Ve,{class:"mr-2 h-4 w-4 animate-spin"}),m(),e(ge,Ue)},ne=ge=>{var Ue=N("Save Question");e(ge,Ue)};ve(I,ge=>{h()?ge(xe):ge(ne,!1)})}e(Ae,X)},$$slots:{default:!0}}),e(k,at)},$$slots:{default:!0}})}),o(j),nr(j,(R,f)=>be==null?void 0:be(R,f),()=>({onSubmit:()=>a(le)?!0:(ht.error("Please fill in all required fields"),!1)})),q(()=>{C(B,`${s().difficulty||"0"}/5`),C(mt,`${s().userConfidence||"0"}/5`)}),e(De,ae)},$$slots:{default:!0}})}),e(S,_e)},$$slots:{default:!0}})}),e(P,M),ot(),c()}var al=d("<!> <!>",1),rl=d("<!> Cancel",1),sl=d("<!> Save Changes",1),ol=d("<!> <!>",1),nl=d('<!> <div class="grid gap-4 py-4"><div class="grid gap-2"><textarea class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[150px] w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm focus-visible:outline-none focus-visible:ring-1"></textarea></div></div> <!>',1),ll=d("<!> <!>",1);function Xr(P,t){st(t,!1);let v=T(t,"open",12,!1),c=T(t,"title",8,""),s=T(t,"fieldValue",8,""),u=T(t,"fieldType",8,"notes"),h=T(t,"applicationId",8,""),$=T(t,"onClose",8,()=>{}),A=T(t,"onSave",8),b=Ht("");async function be(){try{const J=await fetch(`/api/applications/${h()}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({[u()]:a(b)})});if(!J.ok)throw new Error(`Failed to update ${u()}: ${J.statusText}`);A()(a(b)),v(!1),ht.success(`${c()} updated successfully`)}catch(J){console.error(`Error updating ${u()}:`,J),ht.error(`Failed to update ${u()}`)}}function se(){v(!1),$()()}os(()=>(Cr(v()),Cr(s())),()=>{v()&&y(b,s()||"")}),ns(),_r(),Sa(P,{onOpenChange:se,get open(){return v()},set open(J){v(J)},children:(J,H)=>{zr(J,{children:(he,le)=>{var M=ll(),Y=l(M);Ra(Y,{class:"bg-background/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 backdrop-blur-sm"});var me=r(Y,2);Pa(me,{class:"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg md:w-full",children:(E,S)=>{var x=nl(),_e=l(x);Ca(_e,{children:(De,Oe)=>{var ae=al(),Te=l(ae);ka(Te,{class:"text-lg font-semibold",children:(ie,re)=>{m();var w=N();q(()=>C(w,c())),e(ie,w)},$$slots:{default:!0}});var j=r(Te,2);Ma(j,{class:"text-muted-foreground text-sm",children:(ie,re)=>{m();var w=N();q(()=>C(w,`Make changes to the ${u()==="notes"?"notes":"next action"} for this application.`)),e(ie,w)},$$slots:{default:!0}}),e(De,ae)},$$slots:{default:!0}});var te=r(_e,2),D=n(te),Pe=n(D);ls(Pe),o(D),o(te);var Z=r(te,2);Ea(Z,{class:"flex items-center justify-end space-x-2",children:(De,Oe)=>{var ae=ol(),Te=l(ae);lt(Te,{variant:"outline",onclick:se,children:(ie,re)=>{var w=rl(),ue=l(w);qs(ue,{class:"mr-1.5 h-4 w-4"}),m(),e(ie,w)},$$slots:{default:!0}});var j=r(Te,2);lt(j,{variant:"default",onclick:be,children:(ie,re)=>{var w=sl(),ue=l(w);Js(ue,{class:"mr-1.5 h-4 w-4"}),m(),e(ie,w)},$$slots:{default:!0}}),e(De,ae)},$$slots:{default:!0}}),q(()=>Vt(Pe,"placeholder",u()==="notes"?"Add notes about this application...":"What's the next step for this application?")),Qt(Pe,()=>a(b),De=>y(b,De)),e(E,x)},$$slots:{default:!0}}),e(he,M)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),ot()}var il=d("<!> <span> </span>",1),dl=d('<div class="flex items-center gap-1.5"><!> <span> </span></div>'),cl=d('<div class="flex items-center justify-between"><div class="flex items-center gap-3"><!> <!></div> <div class="text-muted-foreground flex items-center gap-4 text-sm"><div class="flex items-center gap-1.5"><!> <span> </span></div> <!></div></div>'),vl=d('<div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm"><!> <div class="flex-1 overflow-hidden"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Interviewers</p> <p class="mt-1.5 text-sm"> </p></div></div>'),ul=d('<div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm"><!> <div class="flex-1 overflow-hidden"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Feedback</p> <p class="mt-1.5 text-sm"> </p></div></div>'),fl=d('<!> <span class="sr-only">Edit Next Action</span>',1),ml=d('<!> <span class="sr-only">Edit Notes</span>',1),pl=d("<!> <span>Add Question</span>",1),_l=d('<div class="bg-muted/30 mt-4 rounded-md p-4 text-center shadow-sm"><p class="text-muted-foreground text-sm">No questions recorded yet.</p></div>'),gl=d("<div></div>"),hl=d('<div class="mt-3 flex items-center gap-2"><span class="text-muted-foreground text-xs font-medium">Confidence:</span> <div class="flex gap-0.5"></div></div>'),$l=d('<div class="bg-muted/20 mt-4 rounded-md p-4"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Your Response</p> <p class="mt-1.5 text-sm"> </p> <!></div>'),xl=d('<div class="mt-3 p-3"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Notes</p> <p class="mt-1.5 text-sm"> </p></div>'),bl=d('<div class="hover:border-primary/30 rounded-lg border p-4 shadow-sm transition-colors"><div class="flex items-start"><div class="flex-1 overflow-hidden"><h6 class="text-sm font-medium"> </h6> <div class="mt-2 flex flex-wrap items-center gap-2"><!> <!></div></div></div> <!> <!></div>'),yl=d('<div class="mt-4 space-y-4"></div>'),wl=d('<!> <div class="grid gap-6 py-4"><div class="grid grid-cols-1 gap-4 md:grid-cols-2"><!> <!></div> <div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm"><!> <div class="flex-1 overflow-hidden"><div class="flex items-center justify-between"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Next Action</p> <!></div> <p class="mt-1.5 text-sm"> </p></div></div> <div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm"><!> <div class="flex-1 overflow-hidden"><div class="flex items-center justify-between"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Notes</p> <!></div> <p class="mt-1.5 text-sm"> </p></div></div> <!> <div><div class="flex items-center justify-between"><h5 class="text-sm font-medium">Questions</h5> <!></div> <!></div></div> <!>',1),Pl=d("<!> <!>",1),Sl=d("<!> <!>",1);function Cl(P,t){st(t,!1);let v=T(t,"open",12,!1),c=T(t,"interview",12),s=T(t,"onClose",8,()=>{}),u=T(t,"onAddQuestion",8,E=>{}),h=Ht(!1),$=Ht(""),A=Ht(""),b=Ht("");function be(E){return E?new Date(E).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"N/A"}function se(E){if(!E)return{variant:"outline",icon:null};switch(E.toLowerCase()){case"passed":case"offer":case"accepted":return{variant:"success",icon:Na};case"failed":case"rejected":return{variant:"destructive",icon:mr};case"pending":case"scheduled":case"in progress":return{variant:"warning",icon:Ia};default:return{variant:"outline",icon:null}}}function J(){y($,"notes"),y(A,"Interview Notes"),y(b,c().notes||""),y(h,!0)}function H(){y($,"nextAction"),y(A,"Next Action"),y(b,c().nextAction||""),y(h,!0)}async function he(E){try{const S=await fetch(`/api/applications/${c().applicationId}/interviews/${c().id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({[a($)]:E})});if(!S.ok)throw new Error(`Failed to update ${a($)}: ${S.statusText}`);c(c()[a($)]=E,!0),ht.success(`${a(A)} updated successfully`)}catch(S){console.error(`Error updating ${a($)}:`,S),ht.error(`Failed to update ${a($)}`)}}_r();var le=Sl(),M=l(le);Sa(M,{onOpenChange:s(),get open(){return v()},set open(E){v(E)},children:(E,S)=>{zr(E,{children:(x,_e)=>{var te=Pl(),D=l(te);Ra(D,{class:"bg-background/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 backdrop-blur-sm"});var Pe=r(D,2);Pa(Pe,{class:"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid w-full max-w-3xl translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg md:w-full",children:(Z,De)=>{var Oe=wl(),ae=l(Oe);Ca(ae,{children:(ke,Me)=>{var je=cl(),F=n(je),we=n(F);ka(we,{class:"text-xl font-semibold",children:(Ae,ce)=>{m();var X=N();q(()=>C(X,c().stageName)),e(Ae,X)},$$slots:{default:!0}});var R=r(we,2);{var f=Ae=>{const ce=na(()=>se(c().outcome));Bt(Ae,{get variant(){return a(ce).variant},class:"flex items-center gap-1.5 px-2.5 py-1",children:(X,I)=>{var xe=il(),ne=l(xe);{var ge=Fe=>{a(ce).icon(Fe,{class:"h-3.5 w-3.5"})};ve(ne,Fe=>{a(ce).icon&&Fe(ge)})}var Ue=r(ne,2),Ve=n(Ue,!0);o(Ue),q(()=>C(Ve,c().outcome)),e(X,xe)},$$slots:{default:!0}})};ve(R,Ae=>{c().outcome&&Ae(f)})}o(F);var k=r(F,2),W=n(k),at=n(W);Yt(at,{class:"h-4 w-4"});var Se=r(at,2),Ee=n(Se,!0);o(Se),o(W);var Le=r(W,2);{var Ie=Ae=>{var ce=dl(),X=n(ce);pr(X,{class:"h-4 w-4"});var I=r(X,2),xe=n(I);o(I),o(ce),q(()=>C(xe,`${c().duration??""} min`)),e(Ae,ce)};ve(Le,Ae=>{c().duration&&Ae(Ie)})}o(k),o(je),q(Ae=>C(Ee,Ae),[()=>be(c().stageDate)],na),e(ke,je)},$$slots:{default:!0}});var Te=r(ae,2),j=n(Te),ie=n(j);{var re=ke=>{var Me=vl(),je=n(Me);Yr(je,{class:"text-primary/70 mt-0.5 h-5 w-5"});var F=r(je,2),we=r(n(F),2),R=n(we,!0);o(we),o(F),o(Me),q(()=>C(R,c().interviewers)),e(ke,Me)};ve(ie,ke=>{c().interviewers&&ke(re)})}var w=r(ie,2);{var ue=ke=>{var Me=ul(),je=n(Me);Or(je,{class:"text-primary/70 mt-0.5 h-5 w-5"});var F=r(je,2),we=r(n(F),2),R=n(we,!0);o(we),o(F),o(Me),q(()=>C(R,c().feedback)),e(ke,Me)};ve(w,ke=>{c().feedback&&ke(ue)})}o(j);var Ce=r(j,2),$e=n(Ce);fr($e,{class:"text-primary/70 mt-0.5 h-5 w-5"});var p=r($e,2),O=n(p),U=r(n(O),2);lt(U,{variant:"ghost",size:"sm",class:"h-7 w-7 p-0",onclick:H,children:(ke,Me)=>{var je=fl(),F=l(je);Ta(F,{class:"h-3.5 w-3.5"}),m(2),e(ke,je)},$$slots:{default:!0}}),o(O);var oe=r(O,2),L=n(oe,!0);o(oe),o(p),o(Ce);var fe=r(Ce,2),ye=n(fe);Or(ye,{class:"text-primary/70 mt-0.5 h-5 w-5"});var g=r(ye,2),B=n(g),V=r(n(B),2);lt(V,{variant:"ghost",size:"sm",class:"h-7 w-7 p-0",onclick:J,children:(ke,Me)=>{var je=ml(),F=l(je);Ta(F,{class:"h-3.5 w-3.5"}),m(2),e(ke,je)},$$slots:{default:!0}}),o(B);var G=r(B,2),de=n(G,!0);o(G),o(g),o(fe);var Ne=r(fe,2);Bs(Ne,{class:"my-2"});var Re=r(Ne,2),qe=n(Re),Xe=r(n(qe),2);lt(Xe,{variant:"outline",size:"sm",onclick:()=>u()(c().id),class:"flex items-center gap-1.5 shadow-sm",children:(ke,Me)=>{var je=pl(),F=l(je);ia(F,{class:"h-3.5 w-3.5"}),m(2),e(ke,je)},$$slots:{default:!0}}),o(qe);var et=r(qe,2);{var tt=ke=>{var Me=_l();e(ke,Me)},mt=ke=>{var Me=yl();bt(Me,5,()=>c().questions,Ft,(je,F)=>{var we=bl(),R=n(we),f=n(R),k=n(f),W=n(k,!0);o(k);var at=r(k,2),Se=n(at);Bt(Se,{variant:"outline",class:"px-2 py-0.5",children:(I,xe)=>{m();var ne=N();q(()=>C(ne,a(F).category)),e(I,ne)},$$slots:{default:!0}});var Ee=r(Se,2);{var Le=I=>{Bt(I,{variant:"secondary",class:"px-2 py-0.5",children:(xe,ne)=>{m();var ge=N();q(()=>C(ge,`Difficulty: ${a(F).difficulty??""}/5`)),e(xe,ge)},$$slots:{default:!0}})};ve(Ee,I=>{a(F).difficulty&&I(Le)})}o(at),o(f),o(R);var Ie=r(R,2);{var Ae=I=>{var xe=$l(),ne=r(n(xe),2),ge=n(ne,!0);o(ne);var Ue=r(ne,2);{var Ve=Fe=>{var Je=hl(),it=r(n(Je),2);bt(it,4,()=>Array(5),Ft,(Be,Qe,We)=>{var vt=gl();q(()=>Ur(vt,1,`h-2 w-5 rounded-sm ${We<a(F).userConfidence?"bg-primary":"bg-muted"}`)),e(Be,vt)}),o(it),o(Je),e(Fe,Je)};ve(Ue,Fe=>{a(F).userConfidence&&Fe(Ve)})}o(xe),q(()=>C(ge,a(F).userResponse)),e(I,xe)};ve(Ie,I=>{a(F).userResponse&&I(Ae)})}var ce=r(Ie,2);{var X=I=>{var xe=xl(),ne=r(n(xe),2),ge=n(ne,!0);o(ne),o(xe),q(()=>C(ge,a(F).notes)),e(I,xe)};ve(ce,I=>{a(F).notes&&I(X)})}o(we),q(()=>C(W,a(F).question)),e(je,we)}),o(Me),e(ke,Me)};ve(et,ke=>{var Me;(Me=c().questions)!=null&&Me.length?ke(mt,!1):ke(tt)})}o(Re),o(Te);var Ye=r(Te,2);Ea(Ye,{class:"flex items-center justify-end",children:(ke,Me)=>{lt(ke,{variant:"outline",onclick:s(),children:(je,F)=>{m();var we=N("Close");e(je,we)},$$slots:{default:!0}})},$$slots:{default:!0}}),q(()=>{C(L,c().nextAction||"No next action set."),C(de,c().notes||"No notes added yet.")}),e(Z,Oe)},$$slots:{default:!0}}),e(x,te)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0});var Y=r(M,2);const me=na(()=>{var E;return(E=c())==null?void 0:E.applicationId});Xr(Y,{get title(){return a(A)},get fieldValue(){return a(b)},get fieldType(){return a($)},get applicationId(){return a(me)},onSave:he,get open(){return a(h)},set open(E){y(h,E)},$$legacy:!0}),e(P,le),ot()}var kl=d("<!> <span>Add Interview</span>",1),Al=d('<div class="flex flex-col items-center justify-center py-12"><div class="border-primary h-10 w-10 animate-spin rounded-full border-b-2 border-t-2"></div> <p class="text-muted-foreground mt-4 text-sm">Loading interview data...</p></div>'),Dl=d('<div class="bg-destructive/10 text-destructive rounded-lg p-6 text-center shadow-sm"><!> <p class="font-medium"> </p> <p class="text-muted-foreground mt-1 text-sm">There was a problem loading your interview data.</p> <!></div>'),jl=d("<!> Add Your First Interview",1),Il=d(`<div class="bg-muted/20 rounded-lg border border-dashed p-10 text-center"><!> <h4 class="text-base font-medium">No interviews yet</h4> <p class="text-muted-foreground mx-auto mt-2 max-w-md">Track your interview process by adding interview stages and questions to keep a record of
        your progress.</p> <!></div>`),Tl=(P,t,v)=>t(a(v)),Nl=(P,t,v)=>P.key==="Enter"&&t(a(v)),Ol=d("<!> <span> </span>",1),Fl=d('<div class="mt-4 border-t pt-3"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Next Action</p> <p class="mt-1 line-clamp-2 text-sm"> </p></div>'),Vl=d('<div class="text-muted-foreground flex items-center gap-1.5 text-sm"><!> <span> </span></div>'),Rl=d('<div class="text-muted-foreground text-sm">No questions</div>'),Ml=d("<!> Add Question",1),El=d('<button type="button" class="hover:border-primary/30 w-full cursor-pointer rounded-lg border p-5 text-left shadow-sm transition-all hover:shadow-md"><div class="flex items-start justify-between"><div class="flex flex-col gap-2"><h3 class="text-lg font-semibold"> </h3> <div class="text-muted-foreground flex items-center gap-1.5 text-sm"><!> <span> </span></div></div> <!></div> <!> <div class="mt-4 flex items-center justify-between"><!> <!></div></button>'),Ul=d('<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"></div>'),zl=d('<div class="mt-6 flex flex-col gap-5"><div class="flex flex-row items-center justify-between"><div class="flex flex-col"><h3 class="text-lg font-medium">Interviews</h3> <p class="text-muted-foreground mt-1 text-sm">Track your interview stages and questions</p></div> <!></div> <!></div> <!> <!> <!>',1);function Ll(P,t){st(t,!0);let v=Ge(It([])),c=Ge(!0),s=Ge(null),u=Ge(!1),h=Ge(!1),$=Ge(!1),A=Ge(null),b=Ge(null);Gt(()=>{t.applicationId&&be()});async function be(){y(c,!0),y(s,null);try{console.log("Fetching interviews for application ID:",t.applicationId);const j=await fetch("/api/applications/check");if(console.log("Application check response:",j.ok?"OK":"Failed",j.status),j.ok){const w=await j.json();console.log("Application check data:",w)}const ie=await fetch(`/api/applications/${t.applicationId}/interviews`);if(console.log("Interview API response status:",ie.status,ie.statusText),!ie.ok)throw new Error(`Failed to fetch interview stages: ${ie.statusText}`);const re=await ie.json();console.log("Interview data received:",re),y(v,re.interviewStages||[],!0),a(v).length===0&&console.log("No interview stages found for this application")}catch(j){console.error("Error fetching interview stages:",j),y(s,j.message,!0),ht.error("Failed to load interview data")}finally{y(c,!1)}}function se(j){return new Date(j).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function J(j){return j?{Passed:{variant:"success",icon:Na},Failed:{variant:"destructive",icon:mr},Pending:{variant:"outline",icon:pr},Scheduled:{variant:"secondary",icon:Yt}}[j]||{variant:"outline",icon:Ia}:null}function H(){y(u,!0)}function he(j){y(A,j,!0),y(h,!0)}function le(){be(),y(u,!1),ht.success("Interview added successfully")}function M(){be(),y(h,!1),ht.success("Question added successfully")}function Y(j){y(b,j,!0),y($,!0)}function me(j){y(A,j,!0),y(h,!0),y($,!1)}function E(){y($,!1),be()}var S=zl(),x=l(S),_e=n(x),te=r(n(_e),2);{var D=j=>{lt(j,{variant:"outline",size:"sm",onclick:H,class:"flex items-center gap-2 shadow-sm hover:shadow",children:(ie,re)=>{var w=kl(),ue=l(w);ia(ue,{class:"h-4 w-4"}),m(2),e(ie,w)},$$slots:{default:!0}})};ve(te,j=>{(a(v).length>0||a(c)||a(s))&&j(D)})}o(_e);var Pe=r(_e,2);{var Z=j=>{var ie=Al();e(j,ie)},De=(j,ie)=>{{var re=ue=>{var Ce=Dl(),$e=n(Ce);Ia($e,{class:"mx-auto mb-2 h-8 w-8"});var p=r($e,2),O=n(p,!0);o(p);var U=r(p,4);lt(U,{variant:"outline",size:"sm",class:"mt-4 shadow-sm",onclick:be,children:(oe,L)=>{m();var fe=N("Try Again");e(oe,fe)},$$slots:{default:!0}}),o(Ce),q(()=>C(O,a(s))),e(ue,Ce)},w=(ue,Ce)=>{{var $e=O=>{var U=Il(),oe=n(U);Tr(oe,{class:"text-muted-foreground mx-auto mb-3 h-10 w-10"});var L=r(oe,6);lt(L,{variant:"outline",size:"sm",onclick:H,class:"mt-5 shadow-sm hover:shadow",children:(fe,ye)=>{var g=jl(),B=l(g);ia(B,{class:"mr-1.5 h-4 w-4"}),m(),e(fe,g)},$$slots:{default:!0}}),o(U),e(O,U)},p=O=>{var U=Ul();bt(U,21,()=>a(v),Ft,(oe,L)=>{var fe=El();fe.__click=[Tl,Y,L],fe.__keydown=[Nl,Y,L];var ye=n(fe),g=n(ye),B=n(g),V=n(B,!0);o(B);var G=r(B,2),de=n(G);Yt(de,{class:"h-4 w-4"});var Ne=r(de,2),Re=n(Ne,!0);o(Ne),o(G),o(g);var qe=r(g,2);{var Xe=F=>{const we=pe(()=>J(a(L).outcome));Bt(F,{get variant(){return a(we).variant},class:"flex items-center gap-1.5 px-2.5 py-1",children:(R,f)=>{var k=Ol(),W=l(k);{var at=Le=>{var Ie=Q(),Ae=l(Ie);i(Ae,()=>a(we).icon,(ce,X)=>{X(ce,{class:"h-3.5 w-3.5"})}),e(Le,Ie)};ve(W,Le=>{a(we).icon&&Le(at)})}var Se=r(W,2),Ee=n(Se,!0);o(Se),q(()=>C(Ee,a(L).outcome)),e(R,k)},$$slots:{default:!0}})};ve(qe,F=>{a(L).outcome&&F(Xe)})}o(ye);var et=r(ye,2);{var tt=F=>{var we=Fl(),R=r(n(we),2),f=n(R,!0);o(R),o(we),q(()=>C(f,a(L).nextAction)),e(F,we)};ve(et,F=>{a(L).nextAction&&F(tt)})}var mt=r(et,2),Ye=n(mt);{var ke=F=>{var we=Vl(),R=n(we);Tr(R,{class:"h-4 w-4"});var f=r(R,2),k=n(f);o(f),o(we),q(()=>C(k,`${a(L).questions.length??""}
                  ${a(L).questions.length===1?"question":"questions"}`)),e(F,we)},Me=F=>{var we=Rl();e(F,we)};ve(Ye,F=>{var we;(we=a(L).questions)!=null&&we.length?F(ke):F(Me,!1)})}var je=r(Ye,2);lt(je,{variant:"ghost",size:"sm",class:"h-7 px-2",onclick:F=>{F.stopPropagation(),he(a(L).id)},children:(F,we)=>{var R=Ml(),f=l(R);ia(f,{class:"mr-1 h-3.5 w-3.5"}),m(),e(F,R)},$$slots:{default:!0}}),o(mt),o(fe),q(F=>{C(V,a(L).stageName),C(Re,F)},[()=>se(a(L).stageDate)]),e(oe,fe)}),o(U),e(O,U)};ve(ue,O=>{a(v).length===0?O($e):O(p,!1)},Ce)}};ve(j,ue=>{a(s)?ue(re):ue(w,!1)},ie)}};ve(Pe,j=>{a(c)?j(Z):j(De,!1)})}o(x);var Oe=r(x,2);Qn(Oe,{get applicationId(){return t.applicationId},get open(){return a(u)},onClose:()=>y(u,!1),onSuccess:le});var ae=r(Oe,2);tl(ae,{get applicationId(){return t.applicationId},get interviewId(){return a(A)},get open(){return a(h)},onClose:()=>y(h,!1),onSuccess:M});var Te=r(ae,2);Cl(Te,{get open(){return a($)},get interview(){return a(b)},onClose:E,onAddQuestion:me}),e(P,S),ot()}Fa(["click","keydown"]);var Bl=d("<!> <!>",1),ql=d('<!> <span class="sr-only">Edit Next Action</span>',1),Jl=d('<!> <span class="sr-only">Edit Notes</span>',1),Ql=d('<div><h4 class="text-muted-foreground mb-1 text-sm font-medium">Job Posting</h4> <a target="_blank" rel="noopener noreferrer" class="text-primary text-sm hover:underline">View Original</a></div>'),Hl=d('<div class="mt-6 space-y-6"><div class="flex items-center gap-4"><div class="bg-muted h-16 w-16 overflow-hidden rounded-full"><img class="h-full w-full object-cover"/></div> <div><h3 class="text-xl font-semibold"> </h3> <div class="text-muted-foreground flex items-center"><!> <span> </span></div> <div class="text-muted-foreground flex items-center"><!> <span> </span></div></div></div> <div class="grid grid-cols-2 gap-4"><div><h4 class="text-muted-foreground mb-1 text-sm font-medium">Status</h4> <div> </div></div> <div><h4 class="text-muted-foreground mb-1 text-sm font-medium">Applied Date</h4> <div class="flex items-center"><!> <span> </span></div></div> <div><h4 class="text-muted-foreground mb-1 text-sm font-medium">Job Type</h4> <p> </p></div> <div><h4 class="text-muted-foreground mb-1 text-sm font-medium">Resume Uploaded</h4> <p> </p></div></div> <div class="bg-muted/10 rounded-md border p-4"><div class="mb-2 flex items-center justify-between"><h4 class="text-sm font-medium">Next Action</h4> <!></div> <p class="text-sm"> </p></div> <div class="bg-muted/10 rounded-md border p-4"><div class="mb-2 flex items-center justify-between"><h4 class="text-sm font-medium">Notes</h4> <!></div> <p class="whitespace-pre-line text-sm"> </p></div> <!> <!></div>'),Yl=d("<!> <!>",1),Wl=d("<!> <!>",1),Kl=d("<!> <!>",1);function Zl(P,t){st(t,!1);let v=T(t,"sheetOpen",12),c=T(t,"selectedApplication",12),s=T(t,"statusColors",8),u=Ht(!1),h=Ht(""),$=Ht(""),A=Ht("");function b(){y(h,"notes"),y($,"Notes"),y(A,c().notes||""),y(u,!0)}function be(){y(h,"nextAction"),y($,"Next Action"),y(A,c().nextAction||""),y(u,!0)}function se(M){a(h)==="notes"?c(c().notes=M,!0):a(h)==="nextAction"&&c(c().nextAction=M,!0)}_r();var J=Kl(),H=l(J);Vs(H,{get open(){return v()},set open(M){v(M)},children:(M,Y)=>{Rs(M,{children:(me,E)=>{var S=Wl(),x=l(S);Ms(x,{});var _e=r(x,2);Es(_e,{side:"right",class:"bg-background text-foreground overflow-y-auto p-6 sm:max-w-lg",children:(te,D)=>{var Pe=Yl(),Z=l(Pe);Us(Z,{children:(ae,Te)=>{var j=Bl(),ie=l(j);zs(ie,{children:(w,ue)=>{m();var Ce=N();q(()=>{var $e;return C(Ce,(($e=c())==null?void 0:$e.position)||"Job Details")}),e(w,Ce)},$$slots:{default:!0}});var re=r(ie,2);Ls(re,{children:(w,ue)=>{m();var Ce=N();q(()=>{var $e;return C(Ce,(($e=c())==null?void 0:$e.company)||"")}),e(w,Ce)},$$slots:{default:!0}}),e(ae,j)},$$slots:{default:!0}});var De=r(Z,2);{var Oe=ae=>{var Te=Hl(),j=n(Te),ie=n(j),re=n(ie);o(ie);var w=r(ie,2),ue=n(w),Ce=n(ue,!0);o(ue);var $e=r(ue,2),p=n($e);cr(p,{class:"mr-1 h-4 w-4"});var O=r(p,2),U=n(O,!0);o(O),o($e);var oe=r($e,2),L=n(oe);vr(L,{class:"mr-1 h-4 w-4"});var fe=r(L,2),ye=n(fe,!0);o(fe),o(oe),o(w),o(j);var g=r(j,2),B=n(g),V=r(n(B),2),G=n(V,!0);o(V),o(B);var de=r(B,2),Ne=r(n(de),2),Re=n(Ne);Yt(Re,{class:"mr-1 h-3 w-3"});var qe=r(Re,2),Xe=n(qe,!0);o(qe),o(Ne),o(de);var et=r(de,2),tt=r(n(et),2),mt=n(tt,!0);o(tt),o(et);var Ye=r(et,2),ke=r(n(Ye),2),Me=n(ke,!0);o(ke),o(Ye),o(g);var je=r(g,2),F=n(je),we=r(n(F),2);lt(we,{variant:"ghost",size:"sm",class:"h-7 w-7 p-0",onclick:()=>be(),children:(X,I)=>{var xe=ql(),ne=l(xe);Ta(ne,{class:"h-3.5 w-3.5"}),m(2),e(X,xe)},$$slots:{default:!0}}),o(F);var R=r(F,2),f=n(R,!0);o(R),o(je);var k=r(je,2),W=n(k),at=r(n(W),2);lt(at,{variant:"ghost",size:"sm",class:"h-7 w-7 p-0",onclick:()=>b(),children:(X,I)=>{var xe=Jl(),ne=l(xe);Ta(ne,{class:"h-3.5 w-3.5"}),m(2),e(X,xe)},$$slots:{default:!0}}),o(W);var Se=r(W,2),Ee=n(Se,!0);o(Se),o(k);var Le=r(k,2);{var Ie=X=>{var I=Ql(),xe=r(n(I),2);o(I),q(()=>Vt(xe,"href",c().url)),e(X,I)};ve(Le,X=>{c().url&&X(Ie)})}var Ae=r(Le,2);{var ce=X=>{Ll(X,{get applicationId(){return c().id}})};ve(Ae,X=>{c().id&&X(ce)})}o(Te),q(()=>{Vt(re,"src",c().logo),Vt(re,"alt",c().company),C(Ce,c().position),C(U,c().company),C(ye,c().location),Ur(V,1,`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${s()[c().status]}`),C(G,c().status),C(Xe,c().appliedDate),C(mt,c().jobType||"Not specified"),C(Me,c().resumeUploaded||"No"),C(f,c().nextAction||"None"),C(Ee,c().notes||"No notes")}),e(ae,Te)};ve(De,ae=>{c()&&ae(Oe)})}e(te,Pe)},$$slots:{default:!0}}),e(me,S)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0});var he=r(H,2);const le=na(()=>{var M;return(M=c())==null?void 0:M.id});Xr(he,{get title(){return a($)},get fieldValue(){return a(A)},get fieldType(){return a(h)},get applicationId(){return a(le)},onSave:se,get open(){return a(u)},set open(M){y(u,M)},$$legacy:!0}),e(P,J),ot()}var Xl=d("<!> <span>Active Applications</span>",1),Gl=d("<!> <span>Archived</span>",1),ei=d("<!> <!>",1),ti=(P,t)=>{P.stopPropagation(),y(t,void 0)},ai=d('<button class="text-muted-foreground hover:text-foreground ml-2" aria-label="Clear date range">×</button>'),ri=d("<!> <!> <!>",1),si=d("<!> <!>",1),oi=d("<!> <!> <!> <!> <!> <!>",1),ni=d("<!> <!>",1),li=d("<!> <!> <!> <!> <!> <!>",1),ii=d("<!> <!>",1),di=d("<!> <!>",1),ci=d("<!> Add Application",1),vi=d("<!> Export",1),ui=d("<!> Import",1),fi=d("<!> <!> <!>",1),mi=d("<!> <!>",1),pi=d("<!> Add Your First Application",1),_i=d(`<div class="rounded-lg border p-6"><div class="text-center"><!> <h3 class="mt-4 text-lg font-medium">No applications yet</h3> <p class="text-muted-foreground mt-2">Start tracking your job applications by adding them manually or applying to jobs through
            our platform.</p> <!></div></div>`),gi=d('<div class="rounded-lg border p-6"><div class="text-center"><!> <h3 class="mt-4 text-lg font-medium">No applications found</h3> <p class="text-muted-foreground mt-2">No applications match your search criteria. Try adjusting your search terms.</p> <!></div></div>'),hi=d('<div class="border-border space-y-4 border-b p-2"><div class="flex items-center justify-between"><div class="flex items-center gap-2"><div class="relative"><!> <!></div> <!> <!> <!></div> <div class="flex items-center gap-2"><!> <!> <!></div></div></div> <!>',1),$i=d('<div class="rounded-lg border p-6"><div class="text-center"><!> <h3 class="mt-4 text-lg font-medium">No archived applications</h3> <p class="text-muted-foreground mt-2">Applications that are rejected or result in offers will appear here.</p></div></div>'),xi=d('<div class="border-border flex items-center justify-between border-b p-2"><div class="flex items-center gap-4"><!></div> <div class="relative"><!> <!></div></div> <div class="p-4"><!></div>',1),bi=d("<!> <!> <!>",1),yi=d("<!> <!> <!> <!>",1);function Hd(P,t){st(t,!0);const[v,c]=Oa(),s=()=>Rt(u,"$form",v),{form:u,enhance:h,reset:$,errors:A,constraints:b,submitting:be}=Ga(t.data.form,{validators:er(so),dataType:"json",resetForm:!0,onResult:({result:g})=>{var B,V;if(g.type==="success"){const G={id:((V=(B=g.data)==null?void 0:B.application)==null?void 0:V.id)||crypto.randomUUID(),company:s().company||"",position:s().position||"",location:s().location||"Remote",appliedDate:s().appliedDate||"",status:s().status||"Applied",nextAction:s().nextAction||"",notes:s().notes||"",logo:"https://placehold.co/100x100",url:s().url||"",jobType:s().jobType||"Full-time",resumeUploaded:s().resumeUploaded||"No"};y(Y,[...a(Y),G],!0),y(H,!1),ht.success("New job application added successfully!")}else g.type==="failure"&&ht.error("Failed to add job application. Please try again.")}});console.log(t.data);let se=Ge(!1),J=Ge(null),H=Ge(!1);const he=["Full-time","Part-time","Contract","Freelance","Internship"],le=["Applied","Interview","Assessment","Offer","Rejected","Phone Screen"];function M(g){y(J,g,!0),y(se,!0)}let Y=Ge(It(t.data.applications||[])),me=Ge(!0),E=Ge("kanban"),S=Ge(It(new Set)),x=It({appliedFromDate:"",appliedUntilDate:"",jobType:"",status:"",searchTerm:""}),_e=Ge(""),te=Ge(""),D=Ge(void 0),Pe=Ge(!1);const Z=new or("en-US",{dateStyle:"medium"});Gt(()=>{x.jobType=a(_e),x.status=a(te)}),Gt(()=>{var g,B,V;(g=a(D))!=null&&g.start&&((B=a(D))!=null&&B.end)?(x.appliedFromDate=a(D).start.toString(),x.appliedUntilDate=a(D).end.toString()):(V=a(D))!=null&&V.start?(x.appliedFromDate=a(D).start.toString(),x.appliedUntilDate=""):(x.appliedFromDate="",x.appliedUntilDate="")});let De=null,Oe=Ge(It({Saved:!0,Applied:!0,"Phone Screen":!0,Interview:!0,Assessment:!0,"Final Round":!0,Offer:!0,Accepted:!0,Rejected:!0}));const ae=[{id:"Saved",name:"Saved"},{id:"Applied",name:"Applied"},{id:"Phone Screen",name:"Phone Screen"},{id:"Interview",name:"Interview"},{id:"Assessment",name:"Assessment"},{id:"Final Round",name:"Final Round"},{id:"Offer",name:"Offer"},{id:"Accepted",name:"Accepted"},{id:"Rejected",name:"Rejected"}];let Te=pe(()=>()=>a(Y).filter(g=>{const B=g.status==="Accepted",V=a(me)?!B:B,G=x.searchTerm===""||g.position.toLowerCase().includes(x.searchTerm.toLowerCase())||g.company.toLowerCase().includes(x.searchTerm.toLowerCase()),de=x.jobType===""||g.jobType===x.jobType,Ne=x.status===""||g.status===x.status,Re=x.appliedFromDate===""||new Date(g.appliedDate)>=new Date(x.appliedFromDate),qe=x.appliedUntilDate===""||new Date(g.appliedDate)<=new Date(x.appliedUntilDate);return V&&G&&de&&Ne&&Re&&qe})),j=pe(()=>()=>ae.reduce((g,B)=>(g[B.id]=a(Te)().filter(V=>V.status===B.id),g),{}));Gt(()=>{{const g=document.createElement("style");g.textContent=`
        .dragging {
          opacity: 0.7;
          transform: scale(0.95);
          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
      `,document.head.appendChild(g)}});function ie(g,B){const V=B;V.length>0&&(y(Y,a(Y).map(G=>V.find(Ne=>Ne.id===G.id)?(ht.success(`Moved "${G.position}" to ${g}`),{...G,status:g}):G),!0),y(j,ae.reduce((G,de)=>(G[de.id]=a(Y).filter(Ne=>Ne.status===de.id),G),{}),!0))}function re(g,B){B.length!==0&&(y(Y,a(Y).map(V=>B.includes(V.id.toString())?{...V,status:g}:V),!0),y(j,ae.reduce((V,G)=>(V[G.id]=a(Y).filter(de=>de.status===G.id),V),{}),!0),a(S).clear(),y(S,new Set,!0),ht.success(`Moved ${B.length} applications to ${g}`))}function w(g,B){B?a(S).add(g):a(S).delete(g),y(S,new Set(a(S)),!0)}let ue=Ge(!1);async function Ce(){if(!a(ue)){y(ue,!0);try{const B=[["Company","Position","Location","Applied Date","Status","Job Type","Next Action"].join(","),...a(Te)().map(Ne=>{var Re,qe,Xe,et,tt,mt,Ye;return[`"${((Re=Ne.company)==null?void 0:Re.replace(/"/g,'""'))||""}"`,`"${((qe=Ne.position)==null?void 0:qe.replace(/"/g,'""'))||""}"`,`"${((Xe=Ne.location)==null?void 0:Xe.replace(/"/g,'""'))||""}"`,`"${((et=Ne.appliedDate)==null?void 0:et.replace(/"/g,'""'))||""}"`,`"${((tt=Ne.status)==null?void 0:tt.replace(/"/g,'""'))||""}"`,`"${((mt=Ne.jobType)==null?void 0:mt.replace(/"/g,'""'))||""}"`,`"${((Ye=Ne.nextAction)==null?void 0:Ye.replace(/"/g,'""'))||""}"`].join(",")})].join(`
`),V=new Blob([B],{type:"text/csv;charset=utf-8;"}),G=URL.createObjectURL(V),de=document.createElement("a");de.setAttribute("href",G),de.setAttribute("download","job_applications.csv"),de.style.visibility="hidden",document.body.appendChild(de),de.click(),document.body.removeChild(de),ht.success("CSV exported successfully!")}catch{ht.error("Failed to export CSV")}finally{setTimeout(()=>{y(ue,!1)},500)}}}let $e=Ge(!1);function p(){const g=document.createElement("input");g.type="file",g.accept=".csv",g.onchange=B=>{var G,de;((de=(G=B.target)==null?void 0:G.files)==null?void 0:de[0])&&(y($e,!0),setTimeout(()=>{y($e,!1),ht.success("CSV imported successfully!")},1e3))},g.click()}var O=yi(),U=l(O);Cs(U,{title:"Job Tracker | Hirli",description:"Track your job applications in one place. Organize, monitor, and optimize your entire job search process with our intuitive job tracker.",keywords:"job tracker, job applications, job search, application tracking, job status management, application organization"});var oe=r(U,2);const L=pe(()=>a(me)?"active":"archived");i(oe,()=>Ar,(g,B)=>{B(g,{get value(){return a(L)},onValueChange:V=>y(me,V==="active"),children:(V,G)=>{var de=bi(),Ne=l(de);i(Ne,()=>kr,(Xe,et)=>{et(Xe,{class:"border-t-0",children:(tt,mt)=>{var Ye=ei(),ke=l(Ye);i(ke,()=>Da,(je,F)=>{F(je,{value:"active",class:"flex-1 gap-2",children:(we,R)=>{var f=Xl(),k=l(f);Fr(k,{class:"h-4 w-4"}),m(2),e(we,f)},$$slots:{default:!0}})});var Me=r(ke,2);i(Me,()=>Da,(je,F)=>{F(je,{value:"archived",class:"flex-1 gap-2",children:(we,R)=>{var f=Gl(),k=l(f);Na(k,{class:"h-4 w-4"}),m(2),e(we,f)},$$slots:{default:!0}})}),e(tt,Ye)},$$slots:{default:!0}})});var Re=r(Ne,2);i(Re,()=>Vr,(Xe,et)=>{et(Xe,{value:"active",class:"h-[calc(100vh-105px)] space-y-0",children:(tt,mt)=>{var Ye=hi(),ke=l(Ye),Me=n(ke),je=n(Me),F=n(je),we=n(F);Za(we,{class:"text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"});var R=r(we,2);va(R,{type:"text",placeholder:"Search for roles or companies...",class:"w-64 pl-9",get value(){return x.searchTerm},set value(ce){x.searchTerm=ce}}),o(F);var f=r(F,2);i(f,()=>ar,(ce,X)=>{X(ce,{get open(){return a(Pe)},set open(I){y(Pe,I,!0)},children:(I,xe)=>{var ne=si(),ge=l(ne);i(ge,()=>rr,(Ve,Fe)=>{Fe(Ve,{children:(Je,it)=>{lt(Je,{variant:"outline",class:" justify-start text-left font-normal",children:(Be,Qe)=>{var We=ri(),vt=l(We);Yt(vt,{class:"h-4 w-4"});var dt=r(vt,2);{var $t=ze=>{var gt=Q(),nt=l(gt);{var Ba=Mt=>{var Wt=N();q((da,Ja)=>C(Wt,`${da??""} - ${Ja??""}`),[()=>Z.format(a(D).start.toDate(Ot())),()=>Z.format(a(D).end.toDate(Ot()))]),e(Mt,Wt)},qa=Mt=>{var Wt=N();q(da=>C(Wt,da),[()=>Z.format(a(D).start.toDate(Ot()))]),e(Mt,Wt)};ve(nt,Mt=>{a(D).end?Mt(Ba):Mt(qa,!1)})}e(ze,gt)},Tt=ze=>{var gt=N("Date Range");e(ze,gt)};ve(dt,ze=>{a(D)&&a(D).start?ze($t):ze(Tt,!1)})}var rt=r(dt,2);{var He=ze=>{var gt=ai();gt.__click=[ti,D],e(ze,gt)};ve(rt,ze=>{a(D)&&a(D).start&&ze(He)})}e(Be,We)},$$slots:{default:!0}})},$$slots:{default:!0}})});var Ue=r(ge,2);i(Ue,()=>sr,(Ve,Fe)=>{Fe(Ve,{class:"w-auto p-0",align:"start",children:(Je,it)=>{const Be=pe(()=>{var We;return(We=a(D))==null?void 0:We.start}),Qe=pe(()=>xa(Ot()));Ss(Je,{get placeholder(){return a(Be)},numberOfMonths:2,get maxValue(){return a(Qe)},get value(){return a(D)},set value(We){y(D,We,!0)}})},$$slots:{default:!0}})}),e(I,ne)},$$slots:{default:!0}})});var k=r(f,2);i(k,()=>ea,(ce,X)=>{X(ce,{type:"single",get value(){return a(_e)},onValueChange:I=>y(_e,I||"",!0),children:(I,xe)=>{var ne=ni(),ge=l(ne);i(ge,()=>ta,(Ve,Fe)=>{Fe(Ve,{children:(Je,it)=>{var Be=Q(),Qe=l(Be);const We=pe(()=>a(_e)?a(_e):"Job Type");i(Qe,()=>ra,(vt,dt)=>{dt(vt,{get placeholder(){return a(We)}})}),e(Je,Be)},$$slots:{default:!0}})});var Ue=r(ge,2);i(Ue,()=>aa,(Ve,Fe)=>{Fe(Ve,{children:(Je,it)=>{var Be=oi(),Qe=l(Be);i(Qe,()=>yt,(rt,He)=>{He(rt,{value:"",children:(ze,gt)=>{m();var nt=N("Job Type");e(ze,nt)},$$slots:{default:!0}})});var We=r(Qe,2);i(We,()=>yt,(rt,He)=>{He(rt,{value:"Full-time",children:(ze,gt)=>{m();var nt=N("Full-time");e(ze,nt)},$$slots:{default:!0}})});var vt=r(We,2);i(vt,()=>yt,(rt,He)=>{He(rt,{value:"Part-time",children:(ze,gt)=>{m();var nt=N("Part-time");e(ze,nt)},$$slots:{default:!0}})});var dt=r(vt,2);i(dt,()=>yt,(rt,He)=>{He(rt,{value:"Contract",children:(ze,gt)=>{m();var nt=N("Contract");e(ze,nt)},$$slots:{default:!0}})});var $t=r(dt,2);i($t,()=>yt,(rt,He)=>{He(rt,{value:"Freelance",children:(ze,gt)=>{m();var nt=N("Freelance");e(ze,nt)},$$slots:{default:!0}})});var Tt=r($t,2);i(Tt,()=>yt,(rt,He)=>{He(rt,{value:"Internship",children:(ze,gt)=>{m();var nt=N("Internship");e(ze,nt)},$$slots:{default:!0}})}),e(Je,Be)},$$slots:{default:!0}})}),e(I,ne)},$$slots:{default:!0}})});var W=r(k,2);i(W,()=>ea,(ce,X)=>{X(ce,{type:"single",get value(){return a(te)},onValueChange:I=>y(te,I||"",!0),children:(I,xe)=>{var ne=ii(),ge=l(ne);i(ge,()=>ta,(Ve,Fe)=>{Fe(Ve,{children:(Je,it)=>{var Be=Q(),Qe=l(Be);const We=pe(()=>a(te)?a(te):"Status");i(Qe,()=>ra,(vt,dt)=>{dt(vt,{get placeholder(){return a(We)}})}),e(Je,Be)},$$slots:{default:!0}})});var Ue=r(ge,2);i(Ue,()=>aa,(Ve,Fe)=>{Fe(Ve,{children:(Je,it)=>{var Be=li(),Qe=l(Be);i(Qe,()=>yt,(rt,He)=>{He(rt,{value:"",children:(ze,gt)=>{m();var nt=N("Status");e(ze,nt)},$$slots:{default:!0}})});var We=r(Qe,2);i(We,()=>yt,(rt,He)=>{He(rt,{value:"Applied",children:(ze,gt)=>{m();var nt=N("Applied");e(ze,nt)},$$slots:{default:!0}})});var vt=r(We,2);i(vt,()=>yt,(rt,He)=>{He(rt,{value:"Interview",children:(ze,gt)=>{m();var nt=N("Interview");e(ze,nt)},$$slots:{default:!0}})});var dt=r(vt,2);i(dt,()=>yt,(rt,He)=>{He(rt,{value:"Assessment",children:(ze,gt)=>{m();var nt=N("Assessment");e(ze,nt)},$$slots:{default:!0}})});var $t=r(dt,2);i($t,()=>yt,(rt,He)=>{He(rt,{value:"Offer",children:(ze,gt)=>{m();var nt=N("Offer");e(ze,nt)},$$slots:{default:!0}})});var Tt=r($t,2);i(Tt,()=>yt,(rt,He)=>{He(rt,{value:"Rejected",children:(ze,gt)=>{m();var nt=N("Rejected");e(ze,nt)},$$slots:{default:!0}})}),e(Je,Be)},$$slots:{default:!0}})}),e(I,ne)},$$slots:{default:!0}})}),o(je);var at=r(je,2),Se=n(at);i(Se,()=>Ar,(ce,X)=>{X(ce,{get value(){return a(E)},onValueChange:I=>y(E,I,!0),children:(I,xe)=>{var ne=Q(),ge=l(ne);i(ge,()=>kr,(Ue,Ve)=>{Ve(Ue,{class:"bg-muted flex items-center gap-1 rounded-md border-none p-1.5",children:(Fe,Je)=>{var it=di(),Be=l(it);i(Be,()=>Da,(We,vt)=>{vt(We,{value:"kanban",class:"rounded-sm",children:(dt,$t)=>{Os(dt,{class:"h-3 w-3"})},$$slots:{default:!0}})});var Qe=r(Be,2);i(Qe,()=>Da,(We,vt)=>{vt(We,{value:"list",class:"rounded-sm",children:(dt,$t)=>{Qs(dt,{class:"h-3 w-3"})},$$slots:{default:!0}})}),e(Fe,it)},$$slots:{default:!0}})}),e(I,ne)},$$slots:{default:!0}})});var Ee=r(Se,2);jn(Ee,{tableModel:De,get viewMode(){return a(E)},get kanbanColumnVisibility(){return a(Oe)},set kanbanColumnVisibility(ce){y(Oe,ce,!0)}});var Le=r(Ee,2);i(Le,()=>La,(ce,X)=>{X(ce,{children:(I,xe)=>{var ne=mi(),ge=l(ne);i(ge,()=>Ua,(Ve,Fe)=>{Fe(Ve,{children:(Je,it)=>{lt(Je,{variant:"outline",children:(Be,Qe)=>{Hs(Be,{class:"h-4 w-4"})},$$slots:{default:!0}})},$$slots:{default:!0}})});var Ue=r(ge,2);i(Ue,()=>za,(Ve,Fe)=>{Fe(Ve,{align:"end",children:(Je,it)=>{var Be=fi(),Qe=l(Be);i(Qe,()=>qt,(dt,$t)=>{$t(dt,{onclick:()=>y(H,!0),children:(Tt,rt)=>{var He=ci(),ze=l(He);ia(ze,{class:"mr-2 h-4 w-4"}),m(),e(Tt,He)},$$slots:{default:!0}})});var We=r(Qe,2);i(We,()=>qt,(dt,$t)=>{$t(dt,{onclick:Ce,get disabled(){return a(ue)},children:(Tt,rt)=>{var He=vi(),ze=l(He);Ys(ze,{class:"mr-2 h-4 w-4"}),m(),e(Tt,He)},$$slots:{default:!0}})});var vt=r(We,2);i(vt,()=>qt,(dt,$t)=>{$t(dt,{onclick:p,get disabled(){return a($e)},children:(Tt,rt)=>{var He=ui(),ze=l(He);Ws(ze,{class:"mr-2 h-4 w-4"}),m(),e(Tt,He)},$$slots:{default:!0}})}),e(Je,Be)},$$slots:{default:!0}})}),e(I,ne)},$$slots:{default:!0}})}),o(at),o(Me),o(ke);var Ie=r(ke,2);{var Ae=(ce,X)=>{{var I=ne=>{var ge=_i(),Ue=n(ge),Ve=n(Ue);Fr(Ve,{class:"text-muted-foreground mx-auto h-12 w-12"});var Fe=r(Ve,6);lt(Fe,{onclick:()=>y(H,!0),class:"mt-4",children:(Je,it)=>{var Be=pi(),Qe=l(Be);ia(Qe,{class:"mr-2 h-4 w-4"}),m(),e(Je,Be)},$$slots:{default:!0}}),o(Ue),o(ge),e(ne,ge)},xe=(ne,ge)=>{{var Ue=Fe=>{var Je=gi(),it=n(Je),Be=n(it);Za(Be,{class:"text-muted-foreground mx-auto h-12 w-12"});var Qe=r(Be,6);lt(Qe,{variant:"outline",onclick:()=>x.searchTerm="",class:"mt-4",children:(We,vt)=>{m();var dt=N("Clear Search");e(We,dt)},$$slots:{default:!0}}),o(it),o(Je),e(Fe,Je)},Ve=(Fe,Je)=>{{var it=Qe=>{const We=pe(()=>a(j)());dn(Qe,{get columns(){return ae},get groupedApplications(){return a(We)},openApplicationDetails:M,get selectedItems(){return a(S)},onSelectionChange:w,get columnVisibility(){return a(Oe)},onFinalize:ie,onBulkMove:re})},Be=Qe=>{const We=pe(()=>a(Te)());Er(Qe,{get filteredApplications(){return a(We)},openApplicationDetails:M,get selectedItems(){return a(S)},onSelectionChange:w,onBulkMove:re,get columns(){return ae}})};ve(Fe,Qe=>{a(E)==="kanban"?Qe(it):Qe(Be,!1)},Je)}};ve(ne,Fe=>{a(Te)().length===0?Fe(Ue):Fe(Ve,!1)},ge)}};ve(ce,ne=>{a(Y).length===0?ne(I):ne(xe,!1)},X)}};ve(Ie,ce=>{ce(Ae,!1)})}e(tt,Ye)},$$slots:{default:!0}})});var qe=r(Re,2);i(qe,()=>Vr,(Xe,et)=>{et(Xe,{value:"archived",class:"h-[calc(100vh-105px)]",children:(tt,mt)=>{var Ye=xi(),ke=l(Ye),Me=n(ke),je=n(Me);Bt(je,{variant:"secondary",class:"text-sm",children:(Se,Ee)=>{m();var Le=N();q(Ie=>C(Le,`${Ie??""}
          archived`),[()=>a(Y).filter(Ie=>Ie.status==="Rejected"||Ie.status==="Offer").length]),e(Se,Le)},$$slots:{default:!0}}),o(Me);var F=r(Me,2),we=n(F);Za(we,{class:"text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"});var R=r(we,2);va(R,{type:"text",placeholder:"Search archived applications...",class:"w-64 pl-9",get value(){return x.searchTerm},set value(Se){x.searchTerm=Se}}),o(F),o(ke);var f=r(ke,2),k=n(f);{var W=Se=>{var Ee=$i(),Le=n(Ee),Ie=n(Le);Na(Ie,{class:"text-muted-foreground mx-auto h-12 w-12"}),m(4),o(Le),o(Ee),e(Se,Ee)},at=Se=>{const Ee=pe(()=>a(Te)());Er(Se,{get filteredApplications(){return a(Ee)},openApplicationDetails:M,get selectedItems(){return a(S)},onSelectionChange:w,onBulkMove:re,get columns(){return ae}})};ve(k,Se=>{a(Y).filter(Ee=>Ee.status==="Rejected"||Ee.status==="Offer").length===0?Se(W):Se(at,!1)})}o(f),e(tt,Ye)},$$slots:{default:!0}})}),e(V,de)},$$slots:{default:!0}})});var fe=r(oe,2);Zl(fe,{get selectedApplication(){return a(J)},get statusColors(){return Kr},get sheetOpen(){return a(se)},set sheetOpen(g){y(se,g,!0)}});var ye=r(fe,2);Bo(ye,{get form(){return u},get errors(){return A},get constraints(){return b},get submitting(){return be},get enhance(){return h},get reset(){return $},get jobTypes(){return he},get jobStatuses(){return le},get open(){return a(H)},set open(g){y(H,g,!0)}}),e(P,O),ot(),c()}Fa(["click"]);export{Hd as component};
