import{y as g,z as L,H as O,A as H,B as m,C as c,D as N,E as I,F as _,G as C,I as M,J as Y,K as P,L as S,M as V,N as $,O as j,P as w,p as k,Q as z,R as B,a as F}from"./CGmarHxI.js";import{c as G,r as b,h,i as J}from"./CmxjS0TN.js";import{r as K}from"./BwZiefMD.js";import{b as Q}from"./BasJTneF.js";let R=!0;function x(t){R=t}function ee(t,e){var r=e==null?"":typeof e=="object"?e+"":e;r!==(t.__t??(t.__t=t.nodeValue))&&(t.__t=r,t.nodeValue=r+"")}function W(t,e){return A(t,e)}function te(t,e){g(),e.intro=e.intro??!1;const r=e.target,u=w,l=_;try{for(var a=L(r);a&&(a.nodeType!==8||a.data!==O);)a=H(a);if(!a)throw m;c(!0),N(a),I();const i=A(t,{...e,anchor:a});if(_===null||_.nodeType!==8||_.data!==C)throw M(),m;return c(!1),i}catch(i){if(i===m)return e.recover===!1&&Y(),g(),P(r),c(!1),W(t,e);throw i}finally{c(u),N(l),K()}}const d=new Map;function A(t,{target:e,anchor:r,props:u={},events:l,context:a,intro:i=!0}){g();var y=new Set,p=o=>{for(var s=0;s<o.length;s++){var n=o[s];if(!y.has(n)){y.add(n);var f=J(n);e.addEventListener(n,h,{passive:f});var T=d.get(n);T===void 0?(document.addEventListener(n,h,{passive:f}),d.set(n,1)):d.set(n,T+1)}}};p(S(G)),b.add(p);var v=void 0,D=V(()=>{var o=r??e.appendChild($());return j(()=>{if(a){k({});var s=z;s.c=a}l&&(u.$$events=l),w&&Q(o,null),R=i,v=t(o,u)||{},R=!0,w&&(B.nodes_end=_),a&&F()}),()=>{var f;for(var s of y){e.removeEventListener(s,h);var n=d.get(s);--n===0?(document.removeEventListener(s,h),d.delete(s)):d.set(s,n)}b.delete(p),o!==r&&((f=o.parentNode)==null||f.removeChild(o))}});return E.set(v,D),v}let E=new WeakMap;function re(t,e){const r=E.get(t);return r?(E.delete(t),r(e)):Promise.resolve()}export{x as a,R as b,te as h,W as m,ee as s,re as u};
