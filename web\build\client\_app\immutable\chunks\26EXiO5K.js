import{w as d,aJ as $}from"./CGmarHxI.js";const o=d("disconnected"),v=d(null),h=d([]),i=new Set,D=1e4;let t=null,W=!1;const P={initialize:O,disconnect:E,getStatus:C,status:o,messages:h,send:N};function O(){if(W){console.log("WebSocket already initialized, skipping initialization");return}W=!0,console.log("Initializing WebSocket singleton with improved deduplication..."),i.clear(),I(),window.addEventListener("beforeunload",()=>{E()})}function I(){if(t&&(t.readyState===WebSocket.OPEN||t.readyState===WebSocket.CONNECTING)){console.log("WebSocket already connected or connecting, skipping connection attempt");return}o.set("connecting"),console.log("Connecting to WebSocket server...");const c=window.location.protocol==="https:"?"wss:":"ws:",_=window.location.port==="5173"||window.location.port==="5174"||window.location.port==="5175"?"localhost:3000":window.location.host,p=`${c}//${_}/ws?t=${Date.now()}`;console.log(`Connecting to WebSocket at ${p}`);try{t=new WebSocket(p),t.addEventListener("open",()=>{o.set("connected"),console.log("WebSocket connection established successfully")}),t.addEventListener("message",r=>{var g,u,m,f,w,S,y,b,k;try{const e=JSON.parse(r.data);if(e.type!=="echo"&&e.type!=="ping"){e.timestamp||(e.timestamp=new Date().toISOString());const s=`${e.type}:${e.timestamp}:${JSON.stringify(e.data??{})}`.substring(0,100);if(i.has(s)&&e.type!=="resume_parsing_completed"&&e.type!=="notification"){console.log(`Skipping duplicate WebSocket message: ${e.type} (${s.substring(0,20)}...)`);return}if(i.has(s)&&(e.type==="resume_parsing_completed"||e.type==="notification")&&console.log(`Processing ${e.type} message even though it's a duplicate: ${s.substring(0,20)}...`),i.add(s),setTimeout(()=>{i.delete(s)},D),e.type==="resume_parsing_status"||e.type==="resume_parsing_completed"){if(console.log(`Received resume parsing WebSocket message: ${e.type}`,e),e.type==="resume_parsing_completed"){if(console.log("Resume parsing completed, dispatching notification event"),typeof window<"u"){const n=new CustomEvent("resume-parsing-completed",{detail:{resumeId:(g=e.data)==null?void 0:g.resumeId,profileId:(u=e.data)==null?void 0:u.profileId,userId:(m=e.data)==null?void 0:m.userId,message:"Resume parsing completed successfully",timestamp:e.timestamp||new Date().toISOString()}});window.dispatchEvent(n),console.log("Dispatched resume-parsing-completed event")}}else if(e.type==="resume_parsing_status"&&(((f=e.data)==null?void 0:f.status)==="pending"||((w=e.data)==null?void 0:w.status)==="processing")&&(console.log("Resume parsing in progress, dispatching status event"),typeof window<"u")){const n=new CustomEvent("resume-parsing-status",{detail:{resumeId:(S=e.data)==null?void 0:S.resumeId,profileId:(y=e.data)==null?void 0:y.profileId,userId:(b=e.data)==null?void 0:b.userId,isParsing:!0,status:(k=e.data)==null?void 0:k.status,message:"Resume parsing in progress",timestamp:e.timestamp||new Date().toISOString()}});window.dispatchEvent(n),console.log("Dispatched resume-parsing-status event with isParsing=true")}}if(h.update(n=>{const a=[e,...n];return a.length>100?a.slice(0,100):a}),console.log(`Received WebSocket message: ${e.type}`),typeof window<"u"){const n=new CustomEvent("websocket-message",{detail:{type:e.type,data:e}});window.dispatchEvent(n)}}}catch(e){console.error("Error parsing WebSocket message:",e)}}),t.addEventListener("close",()=>{o.set("disconnected"),t=null,console.log("WebSocket connection closed"),setTimeout(()=>{t||I()},3e3)}),t.addEventListener("error",()=>{o.set("error"),v.set("WebSocket connection error"),console.log("WebSocket connection error occurred")})}catch(r){o.set("error"),v.set("Failed to create WebSocket connection"),console.log(`Failed to create WebSocket connection: ${r}`)}}function E(){t&&(console.log("Disconnecting from WebSocket server..."),t&&(t.close(1e3,"Client disconnected"),t=null,console.log("Closed WebSocket connection")),o.set("disconnected"),i.clear(),console.log("WebSocket disconnected and message cache cleared"))}function C(){return $(o)}function N(c){if(!t||t.readyState!==WebSocket.OPEN)return!1;try{return t.send(JSON.stringify(c)),!0}catch(l){return console.error("Error sending WebSocket message:",l),!1}}export{P as w};
