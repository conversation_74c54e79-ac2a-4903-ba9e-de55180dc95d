import{c,f as R,a as o}from"./BasJTneF.js";import{p as V,f,c as M,r as P,t as J,a as j,g as k,x as q,au as K}from"./CGmarHxI.js";import{c as L}from"./BvdI7LR8.js";import{p as t,r as A,s as N}from"./Btcx8l8F.js";import{s as z,c as O}from"./ncUU1dSD.js";import{i as I}from"./u21ee2wt.js";import{k as Q}from"./DT9WCdWY.js";import{a as T,e as U}from"./B-Xjo-Yt.js";import{b as r,m as W}from"./BfX7a-t9.js";import{u as X}from"./Dmwghw4a.js";import{n as Y}from"./DX6rZLP_.js";import{u as Z}from"./CnMg5bH0.js";var $=R("<div><!></div>"),ee=R('<div style="display: contents;" data-item-wrapper=""><!></div>');function te(u,e){V(e,!0);let i=t(e,"id",19,Z),n=t(e,"ref",15,null),d=t(e,"value",3,""),v=t(e,"disabled",3,!1),_=t(e,"onSelect",3,Y),p=t(e,"forceMount",3,!1),g=t(e,"keywords",19,()=>[]),h=A(e,["$$slots","$$events","$$legacy","id","ref","value","disabled","children","child","onSelect","forceMount","keywords"]);const l=X({id:r.with(()=>i()),ref:r.with(()=>n(),b=>n(b)),value:r.with(()=>d()),disabled:r.with(()=>v()),onSelect:r.with(()=>_()),forceMount:r.with(()=>p()),keywords:r.with(()=>g())}),x=q(()=>W(h,l.props));var S=c(),B=f(S);Q(B,()=>l.root.key,b=>{var m=ee(),D=M(m);{var E=w=>{var C=c(),F=f(C);{var G=s=>{var a=c(),y=f(a);z(y,()=>e.child,()=>({props:k(x)})),o(s,a)},H=s=>{var a=$();U(a,()=>({...k(x)}));var y=M(a);z(y,()=>e.children??K),P(a),o(s,a)};I(F,s=>{e.child?s(G):s(H,!1)})}o(w,C)};I(D,w=>{l.shouldRender&&w(E)})}P(m),J(()=>T(m,"data-value",l.trueValue)),o(b,m)}),o(u,S),j()}function ve(u,e){V(e,!0);let i=t(e,"ref",15,null),n=A(e,["$$slots","$$events","$$legacy","ref","class"]);var d=c(),v=f(d);const _=q(()=>O("aria-selected:bg-accent aria-selected:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground outline-hidden relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",e.class));L(v,()=>te,(p,g)=>{g(p,N({"data-slot":"command-item",get class(){return k(_)}},()=>n,{get ref(){return i()},set ref(h){i(h)}}))}),o(u,d),j()}export{ve as C};
