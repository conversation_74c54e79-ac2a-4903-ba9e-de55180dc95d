import{N as ae,S as re,aj as W,D as k,P as C,z as ne,E as ie,g as z,e as fe,U as le,V as se,W as B,C as M,F as R,G as te,X as J,O as Q,Y as ue,ak as b,L as Z,a0 as ve,R as F,al as y,am as G,an as L,m as _e,a7 as K,ao as de,ap as ce,aq as oe,K as he,ar as Ee,as as pe,at as me,A as Ae}from"./CGmarHxI.js";let g=null;function Ne(f){g=f}function we(f,e){return e}function Te(f,e,a,t){for(var _=[],c=e.length,u=0;u<c;u++)oe(e[u].e,_,!0);var o=c>0&&_.length===0&&a!==null;if(o){var A=a.parentNode;he(A),A.append(a),t.clear(),x(f,e[0].prev,e[c-1].next)}Ee(_,()=>{for(var E=0;E<c;E++){var d=e[E];o||(t.delete(d.k),x(f,d.prev,d.next)),pe(d.e,!o)}})}function Re(f,e,a,t,_,c=null){var u=f,o={flags:e,items:new Map,first:null},A=(e&W)!==0;if(A){var E=f;u=C?k(ne(E)):E.appendChild(ae())}C&&ie();var d=null,N=!1,n=fe(()=>{var s=a();return de(s)?s:s==null?[]:Z(s)});re(()=>{var s=z(n),i=s.length;if(N&&i===0)return;N=i===0;let v=!1;if(C){var p=le(u)===se;p!==(i===0)&&(u=B(),k(u),M(!1),v=!0)}if(C){for(var m=null,T,h=0;h<i;h++){if(R.nodeType===8&&R.data===te){u=R,v=!0,M(!1);break}var r=s[h],l=t(r,h);T=$(R,o,m,null,r,l,h,_,e,a),o.items.set(l,T),m=T}i>0&&k(B())}C||Ie(s,o,u,_,e,t,a),c!==null&&(i===0?d?J(d):d=Q(()=>c(u)):d!==null&&ue(d,()=>{d=null})),v&&M(!0),z(n)}),C&&(u=R)}function Ie(f,e,a,t,_,c,u){var q,V,Y,U;var o=(_&ce)!==0,A=(_&(y|L))!==0,E=f.length,d=e.items,N=e.first,n=N,s,i=null,v,p=[],m=[],T,h,r,l;if(o)for(l=0;l<E;l+=1)T=f[l],h=c(T,l),r=d.get(h),r!==void 0&&((q=r.a)==null||q.measure(),(v??(v=new Set)).add(r));for(l=0;l<E;l+=1){if(T=f[l],h=c(T,l),r=d.get(h),r===void 0){var j=n?n.e.nodes_start:a;i=$(j,e,i,i===null?e.first:i.next,T,h,l,t,_,u),d.set(h,i),p=[],m=[],n=i.next;continue}if(A&&xe(r,T,l,_),r.e.f&b&&(J(r.e),o&&((V=r.a)==null||V.unfix(),(v??(v=new Set)).delete(r))),r!==n){if(s!==void 0&&s.has(r)){if(p.length<m.length){var S=m[0],I;i=S.prev;var O=p[0],D=p[p.length-1];for(I=0;I<p.length;I+=1)P(p[I],S,a);for(I=0;I<m.length;I+=1)s.delete(m[I]);x(e,O.prev,D.next),x(e,i,O),x(e,D,S),n=S,i=D,l-=1,p=[],m=[]}else s.delete(r),P(r,n,a),x(e,r.prev,r.next),x(e,r,i===null?e.first:i.next),x(e,i,r),i=r;continue}for(p=[],m=[];n!==null&&n.k!==h;)n.e.f&b||(s??(s=new Set)).add(n),m.push(n),n=n.next;if(n===null)continue;r=n}p.push(r),i=r,n=r.next}if(n!==null||s!==void 0){for(var w=s===void 0?[]:Z(s);n!==null;)n.e.f&b||w.push(n),n=n.next;var H=w.length;if(H>0){var ee=_&W&&E===0?a:null;if(o){for(l=0;l<H;l+=1)(Y=w[l].a)==null||Y.measure();for(l=0;l<H;l+=1)(U=w[l].a)==null||U.fix()}Te(e,w,ee,d)}}o&&ve(()=>{var X;if(v!==void 0)for(r of v)(X=r.a)==null||X.apply()}),F.first=e.first&&e.first.e,F.last=i&&i.e}function xe(f,e,a,t){t&y&&G(f.v,e),t&L?G(f.i,a):f.i=a}function $(f,e,a,t,_,c,u,o,A,E){var d=g,N=(A&y)!==0,n=(A&me)===0,s=N?n?_e(_):K(_):_,i=A&L?K(u):u,v={i,v:s,k:c,a:null,e:null,prev:a,next:t};g=v;try{return v.e=Q(()=>o(f,s,i,E),C),v.e.prev=a&&a.e,v.e.next=t&&t.e,a===null?e.first=v:(a.next=v,a.e.next=v.e),t!==null&&(t.prev=v,t.e.prev=v.e),v}finally{g=d}}function P(f,e,a){for(var t=f.next?f.next.e.nodes_start:a,_=e?e.e.nodes_start:a,c=f.e.nodes_start;c!==t;){var u=Ae(c);_.before(c),c=u}}function x(f,e,a){e===null?f.first=a:(e.next=a,e.e.next=a&&a.e),a!==null&&(a.prev=e,a.e.prev=e&&e.e)}export{g as c,Re as e,we as i,Ne as s};
