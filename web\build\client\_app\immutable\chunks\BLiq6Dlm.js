import{c as n,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as c}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as m,s as l}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(e,s){const r=m(s,["children","$$slots","$$events","$$legacy"]),o=[["path",{d:"M12 12h.01"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2"}]];f(e,l({name:"briefcase-business"},()=>r,{get iconNode(){return o},children:(a,h)=>{var t=n(),i=c(t);d(i,s,"default",{},null),p(a,t)},$$slots:{default:!0}}))}export{x as B};
