import{c as p,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function M(r,t){const s=m(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5"}],["path",{d:"M9 18h6"}],["path",{d:"M10 22h4"}]];f(r,d({name:"lightbulb"},()=>s,{get iconNode(){return a},children:(e,h)=>{var o=p(),n=l(o);c(n,t,"default",{},null),i(e,o)},$$slots:{default:!0}}))}export{M as L};
