import{w as v}from"./Dc4vaUpe.js";import{w}from"./CGmarHxI.js";const n={theme:"system",ui:{sidebarCollapsed:!1,activeModals:{},lastViewedSection:null,viewMode:"list"},features:{},searchHistory:[],notificationPreferences:{job_alerts:{type:"job_alerts",channels:{email:!0,push:!0,in_app:!0},enabled:!0},application_updates:{type:"application_updates",channels:{email:!0,push:!0,in_app:!0},enabled:!0},messages:{type:"messages",channels:{email:!0,push:!0,in_app:!0},enabled:!0},system:{type:"system",channels:{email:!0,push:!1,in_app:!0},enabled:!0}},account:{phone:"",bio:"",language:"en",timezone:"UTC",dateFormat:"MM/DD/YYYY",timeFormat:"12h",accessibility:{theme:"system",highContrast:!1,reducedMotion:!1,largeText:!1,screenReader:!1},privacy:{profileVisibility:"public",activityVisibility:"public",allowDataCollection:!0,allowThirdPartySharing:!1},cookiePreferences:{functional:!0,analytics:!0,advertising:!1}}};function S(){try{const e=localStorage.getItem("theme"),o=localStorage.getItem("ui_state"),i=localStorage.getItem("search_history"),a=localStorage.getItem("account_preferences");return{...n,theme:e||n.theme,ui:o?{...n.ui,...JSON.parse(o)}:n.ui,searchHistory:i?JSON.parse(i).map(t=>({...t,timestamp:new Date(t.timestamp)})):n.searchHistory,account:a?{...n.account,...JSON.parse(a)}:n.account}}catch(e){return console.error("Failed to load state from localStorage:",e),n}}const y=v(S());y.subscribe(e=>{try{localStorage.setItem("theme",e.theme),localStorage.setItem("ui_state",JSON.stringify(e.ui)),localStorage.setItem("search_history",JSON.stringify(e.searchHistory)),localStorage.setItem("account_preferences",JSON.stringify(e.account))}catch(o){console.error("Failed to save state to localStorage:",o)}});function T(e){y.update(o=>{if(Object.keys(e).length===0)return o;const i={...o.account.accessibility};e.theme!==void 0&&(i.theme=e.theme),e.highContrast!==void 0&&(i.highContrast=e.highContrast),e.reducedMotion!==void 0&&(i.reducedMotion=e.reducedMotion),e.largeText!==void 0&&(i.largeText=e.largeText),e.screenReader!==void 0&&(i.screenReader=e.screenReader),e.sidebarCollapsed!==void 0&&(i.sidebarCollapsed=e.sidebarCollapsed),e.viewMode!==void 0&&(i.viewMode=e.viewMode);const a={...o};return e.sidebarCollapsed!==void 0&&(a.ui={...a.ui,sidebarCollapsed:e.sidebarCollapsed}),e.viewMode!==void 0&&(a.ui={...a.ui,viewMode:e.viewMode}),{...a,account:{...a.account,accessibility:i}}})}function _(e){e&&y.update(o=>{const i={...o.account};if(e.phone!==void 0&&(i.phone=e.phone),e.bio!==void 0&&(i.bio=e.bio),e.language!==void 0&&(i.language=e.language),e.timezone!==void 0&&(i.timezone=e.timezone),e.dateFormat!==void 0&&(i.dateFormat=e.dateFormat),e.timeFormat!==void 0&&(i.timeFormat=e.timeFormat),e.accessibility){i.accessibility={...i.accessibility};const t=e.accessibility;t.theme!==void 0&&(i.accessibility.theme=t.theme),t.highContrast!==void 0&&(i.accessibility.highContrast=t.highContrast),t.reducedMotion!==void 0&&(i.accessibility.reducedMotion=t.reducedMotion),t.largeText!==void 0&&(i.accessibility.largeText=t.largeText),t.screenReader!==void 0&&(i.accessibility.screenReader=t.screenReader),t.sidebarCollapsed!==void 0&&(i.accessibility.sidebarCollapsed=t.sidebarCollapsed),t.viewMode!==void 0&&(i.accessibility.viewMode=t.viewMode)}if(e.privacy){i.privacy={...i.privacy};const t=e.privacy;t.profileVisibility!==void 0&&(i.privacy.profileVisibility=t.profileVisibility),t.activityVisibility!==void 0&&(i.privacy.activityVisibility=t.activityVisibility),t.allowDataCollection!==void 0&&(i.privacy.allowDataCollection=t.allowDataCollection),t.allowThirdPartySharing!==void 0&&(i.privacy.allowThirdPartySharing=t.allowThirdPartySharing)}if(e.cookiePreferences){i.cookiePreferences={...i.cookiePreferences};const t=e.cookiePreferences;t.functional!==void 0&&(i.cookiePreferences.functional=t.functional),t.analytics!==void 0&&(i.cookiePreferences.analytics=t.analytics),t.advertising!==void 0&&(i.cookiePreferences.advertising=t.advertising)}const a={...o};if(e.accessibility){const t=e.accessibility,s=t.sidebarCollapsed!==void 0?t.sidebarCollapsed:a.ui.sidebarCollapsed;a.ui={...a.ui,sidebarCollapsed:s,viewMode:t.viewMode!==void 0?t.viewMode:a.ui.viewMode},console.log(`Sidebar initialized: collapsed=${s}`)}return{...a,account:i}})}const r="hi_cp",d="hi_consent",c={essential:!0,functional:!1,analytics:!1,advertising:!1};function C(){if(typeof window>"u")return c;const e=localStorage.getItem(r);if(e)try{return JSON.parse(e).p||c}catch(i){console.error("Error parsing Hirli cookie preferences from localStorage:",i)}const o=l(r);if(o)try{let i;try{i=atob(o)}catch{i=o}const a=JSON.parse(i);return localStorage.setItem(r,JSON.stringify(a)),a.p||c}catch(i){if(console.error("Error parsing Hirli cookie preferences from cookie:",i),l(d)==="true")return{...c,functional:!0}}return c}function O(e){return C()[e]===!0}function V(){return typeof window>"u"?!1:l(d)==="true"||localStorage.getItem(r)!==null?!0:l(r)!==null}function F(e){if(typeof window>"u")return;const o={v:"1.0",t:new Date().getTime(),p:e},i=JSON.stringify(o);localStorage.setItem(r,i);try{p(r,btoa(i),{days:365})}catch{p(r,"true",{days:365}),console.warn("Could not store full cookie preferences, using simplified version")}p(d,"true",{days:365})}function N(){typeof window>"u"||(localStorage.removeItem(r),m(r),m(d))}function p(e,o,i={}){if(typeof window>"u")return;const{days:a=365,path:t="/",domain:s,secure:b=!0}=i,u=new Date;u.setDate(u.getDate()+a);let f=`${encodeURIComponent(e)}=${encodeURIComponent(o)}; expires=${u.toUTCString()}; path=${t}; SameSite=Lax`;s&&(f+=`; domain=${s}`),b&&(f+="; Secure"),document.cookie=f}function l(e){if(typeof window>"u")return null;const o=`${encodeURIComponent(e)}=`,i=document.cookie.split(";");for(let a=0;a<i.length;a++){let t=i[a];for(;t.charAt(0)===" ";)t=t.substring(1,t.length);if(t.indexOf(o)===0)return decodeURIComponent(t.substring(o.length,t.length))}return null}function m(e,o="/",i){typeof window>"u"||(document.cookie=`${encodeURIComponent(e)}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${o}; SameSite=Lax`)}const k={image:null,name:null,id:null,email:null,role:null},h=w(k);function R(e){console.log("updateProfileImage called with:",e),h.update(o=>{console.log("Current profile before update:",o);const i={...o,image:e};return console.log("Updated profile:",i),i})}function x(e){e&&h.set({image:e.image||null,name:e.name||null,id:e.id||null,email:e.email||null,role:e.role||null})}function g(){return"serviceWorker"in navigator&&"PushManager"in window&&"Notification"in window}function P(){return g()?Notification.requestPermission():Promise.resolve("denied")}async function J(){if(await P()!=="granted")return{success:!1,error:"Permission denied"};const o=await fetch("/api/push/vapid-key");if(!o.ok)return{success:!1,error:"Couldn’t fetch VAPID key"};const{publicKey:i}=await o.json(),t=await(await navigator.serviceWorker.ready).pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:i});return(await fetch("/api/push/subscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({subscription:t})})).ok?{success:!0}:{success:!1,error:"Failed to save subscription"}}async function H(){const o=await(await navigator.serviceWorker.ready).pushManager.getSubscription();return o&&await o.unsubscribe(),(await fetch("/api/push/unsubscribe",{method:"POST"})).ok?{success:!0}:{success:!1,error:"Failed to unregister on server"}}async function $(){return g()?!!await(await navigator.serviceWorker.ready).pushManager.getSubscription():!1}export{T as a,H as b,y as c,F as d,N as e,h as f,C as g,V as h,O as i,x as j,_ as k,$ as l,J as s,R as u};
