var vr=Object.defineProperty;var fr=(s,e,o)=>e in s?vr(s,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):s[e]=o;var rt=(s,e,o)=>fr(s,typeof e!="symbol"?e+"":e,o);import{c as ce,a as r,f as L,t as oe,e as be}from"../chunks/BasJTneF.js";import{c as mr,o as St}from"../chunks/nZgk9enP.js";import{p as Me,f as M,a as Fe,g as a,x as me,c as y,au as Et,r as C,w as Rt,l as Ut,b as Nt,s as v,o as Qe,e as dt,h as jt,n as H,m as ot,d as O,t as J,aL as gr,aD as qe,k as Se,v as Ne}from"../chunks/CGmarHxI.js";import{i as E}from"../chunks/u21ee2wt.js";import"../chunks/CgXBgsce.js";import{t as ye}from"../chunks/DjPYYl4Z.js";import{s as $}from"../chunks/CIt1g2O9.js";import{e as $e,i as Ye}from"../chunks/C3w0v0gR.js";import{s as _r,b as ht,a as Mt,e as Ft,g as Ot}from"../chunks/CmxjS0TN.js";import{b as Ht}from"../chunks/5V1tIHTN.js";import{p as hr}from"../chunks/CWmzcjye.js";import{i as Bt}from"../chunks/BIEMS98f.js";import{p as z,r as He,s as Zt}from"../chunks/Btcx8l8F.js";import{R as qt,P as br,D as xr,a as Gt}from"../chunks/tdzGgazS.js";import{R as wt,S as yt,a as Ct,b as We}from"../chunks/CGK0g3x_.js";import{I as pt}from"../chunks/DMTMHyMa.js";import{B as Te}from"../chunks/B1K98fMG.js";import{L as it}from"../chunks/BvvicRXk.js";import{D as $t,a as Yt,b as Kt,c as Wt}from"../chunks/CKh8VGVX.js";import{S as Pt}from"../chunks/B2lQHLf_.js";import{C as Lt,F as wr,a as yr}from"../chunks/BxlgRp1U.js";import{L as Cr}from"../chunks/BhzFx1Wy.js";import{g as ut}from"../chunks/BiJhC7W5.js";import{P as pr}from"../chunks/DR5zc253.js";import{S as Pr}from"../chunks/yW0TxTga.js";import{F as ct}from"../chunks/ChqRiddM.js";import{E as Dr}from"../chunks/7AwcL9ec.js";import{S as Sr}from"../chunks/C6g8ubaU.js";import{c as U}from"../chunks/BvdI7LR8.js";import{s as vt,c as ft}from"../chunks/ncUU1dSD.js";import{C as At}from"../chunks/T7uRAIbG.js";import{D as Jt,a as Xt,R as Qt}from"../chunks/WD4kvFhR.js";import{e as Be,a as Ve,c as Rr,s as er}from"../chunks/B-Xjo-Yt.js";import{P as Mr,S as Fr,a as Lr,b as Ar,c as kr,d as Vr,R as zr}from"../chunks/CTn0v-X8.js";import{B as lt}from"../chunks/DaBofrVv.js";import{A as tr,a as rr,b as ar,c as or,d as lr,e as nr,f as ir,R as sr}from"../chunks/BnikQ10_.js";import{U as Ir}from"../chunks/CSGDlQPw.js";import{S as Tr}from"../chunks/KVutzy_p.js";import{S as Er}from"../chunks/DumgozFE.js";import{D as Ur}from"../chunks/tr-scC-m.js";import{T as Nr}from"../chunks/C33xR25f.js";import{C as jr}from"../chunks/DW7T7T22.js";import{D as et}from"../chunks/Z6UAQTuv.js";import{D as dr}from"../chunks/Dz4exfp3.js";import{C as Or}from"../chunks/BBNNmnYR.js";import{C as Hr}from"../chunks/DkmCSZhC.js";import{D as Br}from"../chunks/BgDjIxoO.js";import{S as Zr}from"../chunks/CfcZq63z.js";import{P as qr,a as Gr,R as $r}from"../chunks/3WmhYGjL.js";import{h as Yr,C as Kr,a as Wr,b as Jr}from"../chunks/Dmwghw4a.js";import{C as Xr}from"../chunks/CDeW2UsS.js";import{C as Qr}from"../chunks/B5tu6DNS.js";import{b as bt,m as ea}from"../chunks/BfX7a-t9.js";import{u as ta}from"../chunks/CnMg5bH0.js";import{S as ra}from"../chunks/0ykhD7u6.js";import{o as aa,s as at}from"../chunks/C8B1VUaq.js";import{a as oa,b as xt,c as la,d as na,e as kt,T as ia}from"../chunks/LESefvxV.js";var sa=L("<div><!></div>");function da(s,e){Me(e,!0);let o=z(e,"id",19,ta),h=z(e,"ref",15,null),n=z(e,"forceMount",3,!1),m=He(e,["$$slots","$$events","$$legacy","id","ref","forceMount","children","child"]);const w=Yr({id:bt.with(()=>o()),ref:bt.with(()=>h(),g=>h(g)),forceMount:bt.with(()=>n())}),f=me(()=>ea(m,w.props));var _=ce(),c=M(_);{var d=g=>{var x=ce(),i=M(x);{var u=p=>{var l=ce(),t=M(l);vt(t,()=>e.child,()=>({props:a(f)})),r(p,l)},D=p=>{var l=sa();Be(l,()=>({...a(f)}));var t=y(l);vt(t,()=>e.children??Et),C(l),r(p,l)};E(i,p=>{e.child?p(u):p(D,!1)})}r(g,x)};E(c,g=>{w.shouldRender&&g(d)})}r(s,_),Fe()}var ca=L("<span><!></span>");function ua(s,e){Me(e,!0);let o=z(e,"ref",15,null),h=He(e,["$$slots","$$events","$$legacy","ref","class","children"]);var n=ca();Be(n,w=>({"data-slot":"dropdown-menu-shortcut",class:w,...h}),[()=>ft("text-muted-foreground ml-auto text-xs tracking-widest",e.class)]);var m=y(n);vt(m,()=>e.children??Et),C(n),Ht(n,w=>o(w),()=>o()),r(s,n),Fe()}function va(s,e){Me(e,!0);let o=z(e,"ref",15,null),h=He(e,["$$slots","$$events","$$legacy","ref","class"]);var n=ce(),m=M(n);const w=me(()=>ft("bg-border -mx-1 h-px",e.class));U(m,()=>da,(f,_)=>{_(f,Zt({"data-slot":"command-separator",get class(){return a(w)}},()=>h,{get ref(){return o()},set ref(c){o(c)}}))}),r(s,n),Fe()}var fa=L("<!> <!>",1),ma=L("<!> <!>",1),ga=L('<p class="text-xs text-red-600"> </p>'),_a=L("<!> <!>",1),ha=L('<div class="flex flex-col gap-2"><!> <!></div>'),ba=L('<p class="text-xs text-red-600"> </p>'),xa=L('<div class="flex flex-col items-center"><!> <p class="mb-1 text-sm text-gray-500">Selected:</p> <p class="text-md break-all text-gray-500"> </p></div>'),wa=L('<div class="flex flex-col items-center justify-center pb-6 pt-5"><!> <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">Click to upload</span> or drag and drop</p> <p class="text-xs text-gray-500">PDF, DOC, DOCX (MAX. 5MB)</p></div>'),ya=L('<!> <input id="file" type="file" accept=".pdf,.doc,.docx" class="hidden"/>',1),Ca=L('<p class="text-xs text-red-600"> </p>'),pa=L("<!> Uploading...",1),Pa=L('<!> <form><div class="grid gap-4"><div class="flex flex-col gap-2"><!> <!> <!></div> <!> <div class="flex flex-col gap-2"><!> <!> <!></div> <div class="flex w-full flex-col justify-center gap-1"><!> <!></div> <!></div></form>',1),Da=L("<!> <!>",1);function Sa(s,e){Me(e,!1);const[o,h]=_r(),n=()=>Mt(x,"$formData",o),m=()=>Mt(i,"$formErrors",o);let w=z(e,"profiles",24,()=>[]),f=z(e,"documentTypes",24,()=>[{id:"resume",name:"Resume"},{id:"cover_letter",name:"Cover Letter"},{id:"question_response",name:"Question Response"},{id:"letter_of_recommendation",name:"Letter of Recommendation"},{id:"references",name:"References"},{id:"employment_certification",name:"Employment Certification"}]),_=z(e,"initialDocumentType",8,"resume"),c=z(e,"open",12,!1),d=ot(!1),g=ot();const x=Rt({documentType:_(),profileId:"",label:"",file:null}),i=Rt({documentType:"",label:"",file:""});async function u(){var t;i.set({documentType:"",label:"",file:""});let l=!1;if(n().documentType||(i.update(P=>({...P,documentType:"Document type is required"})),l=!0),n().label||(i.update(P=>({...P,label:"Document name is required"})),l=!0),n().file||(i.update(P=>({...P,file:"File is required"})),l=!0),!l){O(d,!0);try{const P=new FormData;P.append("file",n().file),n().profileId&&P.append("profileId",n().profileId),P.append("label",n().label),P.append("documentType",n().documentType);const X=n().documentType==="resume"?"/api/resume/upload":"/api/document/upload",Q=await fetch(X,{method:"POST",body:P});if(console.log("Response status:",Q.status),Q.status===401){console.error("Authentication error"),ye.error("Authentication error",{description:"Please sign in again to continue."}),window.location.href="/auth/sign-in";return}else if(Q.ok){const le=await Q.json();console.log("Response from server:",le);let se;if(n().documentType==="resume"&&le.resume){const de=le.resume;console.log("Resume from server:",de),se={...de.document,type:"resume",resumeId:de.id}}else if(le.document)se=le.document;else{console.error("No document data in response"),ye.error("Upload failed",{description:"Server response missing document data. Please try again."});return}const ge=((t=f().find(de=>de.id===se.type))==null?void 0:t.name)||"Document";ye.success(`${ge} uploaded`,{description:`Your ${ge.toLowerCase()} was uploaded successfully.`}),c(!1),D()}else try{const le=await Q.json();console.error("Upload error response:",le),le.limitReached?ye.error("Document limit reached",{description:le.message||"You have reached your document upload limit."}):ye.error(le.error||"Upload failed",{description:le.details||"Please try again or check your file."})}catch{const se=await Q.text();console.error("Upload error response (text):",se),ye.error("Upload failed",{description:"Please try again or check your file."})}}catch(P){console.error("Upload error:",P),P.message&&P.message.includes("File type")?ye.error("Invalid file type",{description:P.message}):P.message&&P.message.includes("Database error")?ye.error("Database error",{description:"There was an error saving the document to the database."}):P.message&&P.message.includes("Failed to save file")?ye.error("File save error",{description:"There was an error saving the file to the server."}):ye.error("Unexpected error",{description:"Something went wrong during upload. Please try again."})}finally{O(d,!1)}}}function D(){x.set({documentType:_(),profileId:"",label:"",file:null}),i.set({documentType:"",label:"",file:""}),a(g)&&gr(g,a(g).value="")}function p(l){var X;const P=((X=l.target.files)==null?void 0:X[0])||null;x.update(Q=>({...Q,file:P}))}mr(),Ut(()=>jt(c()),()=>{c()||D()}),Nt(),Bt(),qt(s,{get open(){return c()},set open(l){c(l)},children:(l,t)=>{br(l,{children:(P,X)=>{var Q=Da(),le=M(Q);xr(le,{});var se=v(le,2);Gt(se,{class:"sm:max-w-md",children:(ge,de)=>{var G=Pa(),A=M(G);$t(A,{class:"mb-4 flex flex-col gap-1",children:(Y,ee)=>{var T=fa(),ne=M(T);Yt(ne,{children:(W,ie)=>{H();var te=oe("Upload a Document");r(W,te)},$$slots:{default:!0}});var pe=v(ne,2);Kt(pe,{children:(W,ie)=>{H();var te=oe("Select a document type and upload your file.");r(W,te)},$$slots:{default:!0}}),r(Y,T)},$$slots:{default:!0}});var S=v(A,2),j=y(S),k=y(j),B=y(k);it(B,{for:"documentType",class:"block text-xs",children:(Y,ee)=>{H();var T=oe("Document Type");r(Y,T)},$$slots:{default:!0}});var re=v(B,2);wt(re,{type:"single",get value(){return n().documentType},set value(Y){ht(x,Qe(n).documentType=Y,Qe(n))},children:(Y,ee)=>{var T=ma(),ne=M(T);yt(ne,{class:"p-2",children:(W,ie)=>{const te=dt(()=>{var he;return n().documentType?(he=f().find(ue=>ue.id===n().documentType))==null?void 0:he.name:"Choose a document type"});Pt(W,{get placeholder(){return a(te)}})},$$slots:{default:!0}});var pe=v(ne,2);Ct(pe,{children:(W,ie)=>{var te=ce(),he=M(te);$e(he,1,f,Ye,(ue,fe)=>{We(ue,{get value(){return a(fe).id},children:(Re,Le)=>{H();var Ae=oe();J(()=>$(Ae,a(fe).name)),r(Re,Ae)},$$slots:{default:!0}})}),r(W,te)},$$slots:{default:!0}}),r(Y,T)},$$slots:{default:!0},$$legacy:!0});var N=v(re,2);{var R=Y=>{var ee=ga(),T=y(ee,!0);C(ee),J(()=>$(T,m().documentType)),r(Y,ee)};E(N,Y=>{m().documentType&&Y(R)})}C(k);var b=v(k,2);{var F=Y=>{var ee=ha(),T=y(ee);it(T,{for:"profileId",class:"block text-xs",children:(pe,W)=>{H();var ie=oe("Select Profile (Optional)");r(pe,ie)},$$slots:{default:!0}});var ne=v(T,2);wt(ne,{type:"single",get value(){return n().profileId},set value(pe){ht(x,Qe(n).profileId=pe,Qe(n))},children:(pe,W)=>{var ie=_a(),te=M(ie);yt(te,{class:"p-2",children:(ue,fe)=>{const Re=dt(()=>{var Le;return n().profileId?(Le=w().find(Ae=>Ae.id===n().profileId))==null?void 0:Le.name:"Choose a profile (optional)"});Pt(ue,{get placeholder(){return a(Re)}})},$$slots:{default:!0}});var he=v(te,2);Ct(he,{children:(ue,fe)=>{var Re=ce(),Le=M(Re);$e(Le,1,w,Ye,(Ae,we)=>{We(Ae,{get value(){return a(we).id},children:(Ie,Pe)=>{H();var ze=oe();J(()=>$(ze,a(we).name)),r(Ie,ze)},$$slots:{default:!0}})}),r(ue,Re)},$$slots:{default:!0}}),r(pe,ie)},$$slots:{default:!0},$$legacy:!0}),C(ee),r(Y,ee)};E(b,Y=>{w().length>0&&Y(F)})}var V=v(b,2),q=y(V);it(q,{for:"label",class:"block text-xs",children:(Y,ee)=>{H();var T=oe("Document Name");r(Y,T)},$$slots:{default:!0}});var I=v(q,2);pt(I,{id:"label",placeholder:"e.g. Software Engineer Resume",get value(){return n().label},set value(Y){ht(x,Qe(n).label=Y,Qe(n))},$$legacy:!0});var Z=v(I,2);{var K=Y=>{var ee=ba(),T=y(ee,!0);C(ee),J(()=>$(T,m().label)),r(Y,ee)};E(Z,Y=>{m().label&&Y(K)})}C(V);var ae=v(V,2),ve=y(ae);it(ve,{for:"file",class:"border-border flex h-40 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed",children:(Y,ee)=>{var T=ya(),ne=M(T);{var pe=te=>{var he=xa(),ue=y(he);Lt(ue,{class:"mb-3 h-12 w-12 text-green-500"});var fe=v(ue,4),Re=y(fe,!0);C(fe),C(he),J(()=>$(Re,n().file.name)),r(te,he)},W=te=>{var he=wa(),ue=y(he);Lt(ue,{class:"mb-3 h-12 w-12 text-gray-500"}),H(4),C(he),r(te,he)};E(ne,te=>{n().file?te(pe):te(W,!1)})}var ie=v(ne,2);Ht(ie,te=>O(g,te),()=>a(g)),Ft("change",ie,p),r(Y,T)},$$slots:{default:!0}});var _e=v(ve,2);{var xe=Y=>{var ee=Ca(),T=y(ee,!0);C(ee),J(()=>$(T,m().file)),r(Y,ee)};E(_e,Y=>{m().file&&Y(xe)})}C(ae);var Ce=v(ae,2);Wt(Ce,{children:(Y,ee)=>{const T=dt(()=>a(d)||!n().file||n().label.length===0||n().documentType.length===0);Te(Y,{variant:"outline",type:"submit",get disabled(){return a(T)},children:(ne,pe)=>{var W=ce(),ie=M(W);{var te=ue=>{var fe=pa(),Re=M(fe);Cr(Re,{class:"mr-2 h-4 w-4 animate-spin"}),H(),r(ue,fe)},he=ue=>{var fe=oe("Upload");r(ue,fe)};E(ie,ue=>{a(d)?ue(te):ue(he,!1)})}r(ne,W)},$$slots:{default:!0}})},$$slots:{default:!0}}),C(j),C(S),Ft("submit",S,hr(u)),r(ge,G)},$$slots:{default:!0}}),r(P,Q)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),Fe(),h()}var Ra=L("<!> <!>",1),Ma=L(`<div class="mb-4 rounded-md bg-yellow-900/30 p-4 text-yellow-300"><h4 class="mb-2 font-semibold">No Profile Found</h4> <p class="text-sm">You don't have any profiles yet. A profile contains your personal information,
            education, and work experience. Your resume will be created without a profile, and you
            can add your information manually in the resume builder.</p> <p class="mt-2 text-sm font-medium">Don't worry! You can still create a resume and add your information directly.</p></div>`),Fa=(s,e,o)=>e(s,o),La=L(`<div class="rounded-md border border-zinc-700 p-4 text-center text-gray-400"><p>You don't have any existing resumes yet.</p> <p class="mt-2 text-sm">Create your first resume using the "Start From Scratch" option above.</p></div>`),Aa=L('<div class="p-4 text-center text-gray-400"> </div>'),ka=(s,e,o)=>e(a(o).id),Va=(s,e,o,h)=>e(s,()=>o(a(h).id)),za=L('<span class="mr-2 inline-block rounded bg-purple-900 px-2 py-0.5 text-xs text-purple-300">Generated</span>'),Ia=L('<span class="mr-2 inline-block rounded bg-blue-900 px-2 py-0.5 text-xs text-blue-300">Created</span>'),Ta=L('<span class="mr-2 inline-block rounded bg-green-900 px-2 py-0.5 text-xs text-green-300">Uploaded</span>'),Ea=(s,e,o)=>e(a(o).id,s),Ua=L('<div role="button" tabindex="0" class="flex cursor-pointer items-center border-b border-zinc-700 p-4 hover:bg-zinc-800"><div class="mr-3 text-blue-500"><!></div> <div class="flex-1"><p class="font-medium text-white"> </p> <div class="flex items-center"><!> <span class="text-xs text-gray-400"> </span></div></div> <div class="flex items-center gap-2"><button class="rounded-full p-2 text-gray-400 hover:bg-zinc-700 hover:text-white" title="View Resume"><!></button> <button class="rounded bg-zinc-700 px-3 py-1 text-sm text-white hover:bg-blue-600">Select</button></div></div>'),Na=L('<div class="relative mb-4"><!> <!></div> <div class="max-h-60 overflow-y-auto rounded border border-zinc-700"><!></div>',1),ja=L(`<!> <div class="grid gap-6 py-4"><!> <div class="mb-4"><label for="resume-name" class="mb-2 block text-sm font-medium text-white">Resume Name</label> <!></div> <div role="button" tabindex="0" class="flex cursor-pointer items-center justify-between rounded-lg border border-zinc-700 p-6 hover:border-blue-500 hover:bg-zinc-800"><div class="flex items-center"><div class="mr-4 flex h-10 w-10 items-center justify-center rounded-full bg-blue-600 text-white"><!></div> <div><h3 class="text-lg font-medium text-white">Start From Scratch</h3> <p class="text-sm text-gray-400"><!></p></div></div> <div class="text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"></path></svg></div></div> <div class="flex items-center justify-center"><div class="w-full border-t border-zinc-700"></div> <span class="mx-4 text-sm text-gray-400">OR</span> <div class="w-full border-t border-zinc-700"></div></div> <div><h3 class="mb-2 text-lg font-medium text-white">Use Existing Resume</h3> <p class="mb-4 text-sm text-gray-400">A new resume will be created with information prefilled from the selected resume. Uploaded
          resumes can not be used as a base and will not appear in this list.</p> <!></div></div> <!>`,1);function Oa(s,e){Me(e,!1);const o=ot();let h=z(e,"open",12,!1),n=z(e,"profiles",24,()=>[]),m=z(e,"resumes",24,()=>[]),w=ot(""),f=ot("New Resume"),_=n().length>0?n()[0].id:null,c=n().length===0;async function d(){if(!a(f).trim()){ye.error("Resume name required",{description:"Please enter a name for your resume."});return}try{const u=await fetch("/api/resumes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:a(f).trim(),profileId:_})});if(u.ok){const D=await u.json();console.log("Resume created:",D),ye.success("Resume created successfully",{description:"Redirecting to resume builder..."}),h(!1);try{const p=D.documentId||D.id;console.log("Navigating to builder with ID:",p),ut(`/dashboard/builder/${p}`)}catch(p){console.error("Navigation error:",p),ye.error("Navigation error",{description:"Could not navigate to the resume builder. Please try again."})}}else{const D=await u.json();console.error("Failed to create resume:",D),ye.error("Failed to create resume",{description:D.error||"Unknown error occurred"})}}catch(u){console.error("Error creating resume:",u),ye.error("Error creating resume",{description:u instanceof Error?u.message:"Unknown error occurred"})}}function g(u){console.log("Use existing resume:",u),h(!1)}function x(u,D){D.stopPropagation(),console.log("View resume:",u)}function i(u,D){(u.key==="Enter"||u.key===" ")&&(u.preventDefault(),D())}Ut(()=>(jt(m()),a(w)),()=>{O(o,m().filter(u=>u.label.toLowerCase().includes(a(w).toLowerCase())))}),Nt(),Bt(),qt(s,{get open(){return h()},set open(u){h(u)},children:(u,D)=>{Gt(u,{class:"bg-zinc-900 text-white sm:max-w-[600px]",children:(p,l)=>{var t=ja(),P=M(t);$t(P,{children:(I,Z)=>{var K=Ra(),ae=M(K);Yt(ae,{class:"text-2xl font-bold",children:(_e,xe)=>{H();var Ce=oe("How would you like to create this new resume?");r(_e,Ce)},$$slots:{default:!0}});var ve=v(ae,2);Kt(ve,{class:"text-gray-400",children:(_e,xe)=>{H();var Ce=oe("Use an existing resume as base or start from your profile information.");r(_e,Ce)},$$slots:{default:!0}}),r(I,K)},$$slots:{default:!0}});var X=v(P,2),Q=y(X);{var le=I=>{var Z=Ma();r(I,Z)};E(Q,I=>{c&&I(le)})}var se=v(Q,2),ge=v(y(se),2);pt(ge,{id:"resume-name",type:"text",placeholder:"Enter a name for your resume",class:"border-zinc-700 bg-zinc-800 text-white placeholder:text-gray-500",get value(){return a(f)},set value(I){O(f,I)},$$legacy:!0}),C(se);var de=v(se,2);de.__click=d,de.__keydown=[Fa,i,d];var G=y(de),A=y(G),S=y(A);pr(S,{class:"h-5 w-5"}),C(A);var j=v(A,2),k=v(y(j),2),B=y(k);{var re=I=>{var Z=oe("Start from your profile information and tailor this resume.");r(I,Z)},N=I=>{var Z=oe("Create a new resume and add your information manually.");r(I,Z)};E(B,I=>{n().length>0?I(re):I(N,!1)})}C(k),C(j),C(G),H(2),C(de);var R=v(de,4),b=v(y(R),4);{var F=I=>{var Z=La();r(I,Z)},V=I=>{var Z=Na(),K=M(Z),ae=y(K);Pr(ae,{class:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"});var ve=v(ae,2);pt(ve,{type:"text",placeholder:"Search by name",class:"border-zinc-700 bg-zinc-800 pl-10 text-white placeholder:text-gray-500",get value(){return a(w)},set value(ee){O(w,ee)},$$legacy:!0}),C(K);var _e=v(K,2),xe=y(_e);{var Ce=ee=>{var T=Aa(),ne=y(T,!0);C(T),J(()=>$(ne,a(w)?"No resumes match your search":"No existing resumes found")),r(ee,T)},Y=ee=>{var T=ce(),ne=M(T);$e(ne,1,()=>a(o),Ye,(pe,W)=>{var ie=Ua();ie.__click=[ka,g,W],ie.__keydown=[Va,i,g,W];var te=y(ie),he=y(te);ct(he,{class:"h-5 w-5"}),C(te);var ue=v(te,2),fe=y(ue),Re=y(fe,!0);C(fe);var Le=v(fe,2),Ae=y(Le);{var we=je=>{var Ee=za();r(je,Ee)},Ie=(je,Ee)=>{{var Ze=Ue=>{var Ge=Ia();r(Ue,Ge)},Ke=Ue=>{var Ge=Ta();r(Ue,Ge)};E(je,Ue=>{a(W).source==="created"?Ue(Ze):Ue(Ke,!1)},Ee)}};E(Ae,je=>{a(W).source==="generated"?je(we):je(Ie,!1)})}var Pe=v(Ae,2),ze=y(Pe);C(Pe),C(Le),C(ue);var ke=v(ue,2),De=y(ke);De.__click=[Ea,x,W];var Oe=y(De);Dr(Oe,{class:"h-4 w-4"}),C(De),H(2),C(ke),C(ie),J(je=>{$(Re,a(W).label||"Untitled Resume"),$(ze,`Modified: ${je??""}`)},[()=>new Date(a(W).updatedAt||a(W).createdAt).toLocaleDateString()],dt),r(pe,ie)}),r(ee,T)};E(xe,ee=>{a(o).length===0?ee(Ce):ee(Y,!1)})}C(_e),r(I,Z)};E(b,I=>{m().length===0?I(F):I(V,!1)})}C(R),C(X);var q=v(X,2);Wt(q,{children:(I,Z)=>{Te(I,{variant:"outline",onclick:()=>h(!1),class:"border-zinc-700 text-white hover:bg-zinc-800 hover:text-white",children:(K,ae)=>{H();var ve=oe("Cancel");r(K,ve)},$$slots:{default:!0}})},$$slots:{default:!0}}),r(p,t)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),Fe()}Ot(["click","keydown"]);class cr{constructor(e,o={}){rt(this,"component");rt(this,"props");this.component=e,this.props=o}}class Ha{constructor(e,o){rt(this,"snippet");rt(this,"params");this.snippet=e,this.params=o}}function st(s,e={}){return new cr(s,e)}function Vt(s,e){Me(e,!0);var o=ce(),h=M(o);{var n=w=>{var f=oe();J(()=>$(f,e.content)),r(w,f)},m=(w,f)=>{{var _=c=>{var d=ce();const g=me(()=>e.content(e.context));var x=M(d);{var i=D=>{var p=ce();const l=me(()=>{const{component:P,props:X}=a(g);return{Component:P,props:X}});var t=M(p);U(t,()=>a(l).Component,(P,X)=>{X(P,Zt(()=>a(l).props))}),r(D,p)},u=(D,p)=>{{var l=P=>{var X=ce();const Q=me(()=>{const{snippet:se,params:ge}=a(g);return{snippet:se,params:ge}});var le=M(X);vt(le,()=>a(Q).snippet,()=>a(Q).params),r(P,X)},t=P=>{var X=oe();J(()=>$(X,a(g))),r(P,X)};E(D,P=>{a(g)instanceof Ha?P(l):P(t,!1)},p)}};E(x,D=>{a(g)instanceof cr?D(i):D(u,!1)})}r(c,d)};E(w,c=>{e.content instanceof Function&&c(_)},f)}};E(h,w=>{typeof e.content=="string"?w(n):w(m,!1)})}r(s,o),Fe()}/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function Ba(s,e){const o=[],h=n=>{n.forEach(m=>{o.push(m);const w=e(m);w!=null&&w.length&&h(w)})};return h(s),o}function Je(s,e,o){let h=[],n;return m=>{let w;o.key&&o.debug&&(w=Date.now());const f=s(m);if(!(f.length!==h.length||f.some((d,g)=>h[g]!==d)))return n;h=f;let c;if(o.key&&o.debug&&(c=Date.now()),n=e(...f),o==null||o.onChange==null||o.onChange(n),o.key&&o.debug&&o!=null&&o.debug()){const d=Math.round((Date.now()-w)*100)/100,g=Math.round((Date.now()-c)*100)/100,x=g/16,i=(u,D)=>{for(u=String(u);u.length<D;)u=" "+u;return u};console.info(`%c⏱ ${i(g,5)} /${i(d,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*x,120))}deg 100% 31%);`,o==null?void 0:o.key)}return n}}function Xe(s,e,o,h){return{debug:()=>{var n;return(n=s==null?void 0:s.debugAll)!=null?n:s[e]},key:!1,onChange:h}}function Za(s,e,o,h){const n=()=>{var w;return(w=m.getValue())!=null?w:s.options.renderFallbackValue},m={id:`${e.id}_${o.id}`,row:e,column:o,getValue:()=>e.getValue(h),renderValue:n,getContext:Je(()=>[s,o,e,m],(w,f,_,c)=>({table:w,column:f,row:_,cell:c,getValue:c.getValue,renderValue:c.renderValue}),Xe(s.options,"debugCells"))};return s._features.forEach(w=>{w.createCell==null||w.createCell(m,o,e,s)},{}),m}const Dt=(s,e,o,h,n,m,w)=>{let f={id:e,index:h,original:o,depth:n,parentId:w,_valuesCache:{},_uniqueValuesCache:{},getValue:_=>{if(f._valuesCache.hasOwnProperty(_))return f._valuesCache[_];const c=s.getColumn(_);if(c!=null&&c.accessorFn)return f._valuesCache[_]=c.accessorFn(f.original,h),f._valuesCache[_]},getUniqueValues:_=>{if(f._uniqueValuesCache.hasOwnProperty(_))return f._uniqueValuesCache[_];const c=s.getColumn(_);if(c!=null&&c.accessorFn)return c.columnDef.getUniqueValues?(f._uniqueValuesCache[_]=c.columnDef.getUniqueValues(f.original,h),f._uniqueValuesCache[_]):(f._uniqueValuesCache[_]=[f.getValue(_)],f._uniqueValuesCache[_])},renderValue:_=>{var c;return(c=f.getValue(_))!=null?c:s.options.renderFallbackValue},subRows:[],getLeafRows:()=>Ba(f.subRows,_=>_.subRows),getParentRow:()=>f.parentId?s.getRow(f.parentId,!0):void 0,getParentRows:()=>{let _=[],c=f;for(;;){const d=c.getParentRow();if(!d)break;_.push(d),c=d}return _.reverse()},getAllCells:Je(()=>[s.getAllLeafColumns()],_=>_.map(c=>Za(s,f,c,c.id)),Xe(s.options,"debugRows")),_getAllCellsByColumnId:Je(()=>[f.getAllCells()],_=>_.reduce((c,d)=>(c[d.column.id]=d,c),{}),Xe(s.options,"debugRows"))};for(let _=0;_<s._features.length;_++){const c=s._features[_];c==null||c.createRow==null||c.createRow(f,s)}return f};function qa(){return s=>Je(()=>[s.options.data],e=>{const o={rows:[],flatRows:[],rowsById:{}},h=function(n,m,w){m===void 0&&(m=0);const f=[];for(let c=0;c<n.length;c++){const d=Dt(s,s._getRowId(n[c],c,w),n[c],c,m,void 0,w==null?void 0:w.id);if(o.flatRows.push(d),o.rowsById[d.id]=d,f.push(d),s.options.getSubRows){var _;d.originalSubRows=s.options.getSubRows(n[c],c),(_=d.originalSubRows)!=null&&_.length&&(d.subRows=h(d.originalSubRows,m+1,d))}}return f};return o.rows=h(e),o},Xe(s.options,"debugTable","getRowModel",()=>s._autoResetPageIndex()))}function Ga(s){const e=[],o=h=>{var n;e.push(h),(n=h.subRows)!=null&&n.length&&h.getIsExpanded()&&h.subRows.forEach(o)};return s.rows.forEach(o),{rows:e,flatRows:s.flatRows,rowsById:s.rowsById}}function $a(s,e,o){return o.options.filterFromLeafRows?Ya(s,e,o):Ka(s,e,o)}function Ya(s,e,o){var h;const n=[],m={},w=(h=o.options.maxLeafRowFilterDepth)!=null?h:100,f=function(_,c){c===void 0&&(c=0);const d=[];for(let x=0;x<_.length;x++){var g;let i=_[x];const u=Dt(o,i.id,i.original,i.index,i.depth,void 0,i.parentId);if(u.columnFilters=i.columnFilters,(g=i.subRows)!=null&&g.length&&c<w){if(u.subRows=f(i.subRows,c+1),i=u,e(i)&&!u.subRows.length){d.push(i),m[i.id]=i,n.push(i);continue}if(e(i)||u.subRows.length){d.push(i),m[i.id]=i,n.push(i);continue}}else i=u,e(i)&&(d.push(i),m[i.id]=i,n.push(i))}return d};return{rows:f(s),flatRows:n,rowsById:m}}function Ka(s,e,o){var h;const n=[],m={},w=(h=o.options.maxLeafRowFilterDepth)!=null?h:100,f=function(_,c){c===void 0&&(c=0);const d=[];for(let x=0;x<_.length;x++){let i=_[x];if(e(i)){var g;if((g=i.subRows)!=null&&g.length&&c<w){const D=Dt(o,i.id,i.original,i.index,i.depth,void 0,i.parentId);D.subRows=f(i.subRows,c+1),i=D}d.push(i),n.push(i),m[i.id]=i}}return d};return{rows:f(s),flatRows:n,rowsById:m}}function Wa(){return s=>Je(()=>[s.getPreFilteredRowModel(),s.getState().columnFilters,s.getState().globalFilter],(e,o,h)=>{if(!e.rows.length||!(o!=null&&o.length)&&!h){for(let x=0;x<e.flatRows.length;x++)e.flatRows[x].columnFilters={},e.flatRows[x].columnFiltersMeta={};return e}const n=[],m=[];(o??[]).forEach(x=>{var i;const u=s.getColumn(x.id);if(!u)return;const D=u.getFilterFn();D&&n.push({id:x.id,filterFn:D,resolvedValue:(i=D.resolveFilterValue==null?void 0:D.resolveFilterValue(x.value))!=null?i:x.value})});const w=(o??[]).map(x=>x.id),f=s.getGlobalFilterFn(),_=s.getAllLeafColumns().filter(x=>x.getCanGlobalFilter());h&&f&&_.length&&(w.push("__global__"),_.forEach(x=>{var i;m.push({id:x.id,filterFn:f,resolvedValue:(i=f.resolveFilterValue==null?void 0:f.resolveFilterValue(h))!=null?i:h})}));let c,d;for(let x=0;x<e.flatRows.length;x++){const i=e.flatRows[x];if(i.columnFilters={},n.length)for(let u=0;u<n.length;u++){c=n[u];const D=c.id;i.columnFilters[D]=c.filterFn(i,D,c.resolvedValue,p=>{i.columnFiltersMeta[D]=p})}if(m.length){for(let u=0;u<m.length;u++){d=m[u];const D=d.id;if(d.filterFn(i,D,d.resolvedValue,p=>{i.columnFiltersMeta[D]=p})){i.columnFilters.__global__=!0;break}}i.columnFilters.__global__!==!0&&(i.columnFilters.__global__=!1)}}const g=x=>{for(let i=0;i<w.length;i++)if(x.columnFilters[w[i]]===!1)return!1;return!0};return $a(e.rows,g,s)},Xe(s.options,"debugTable","getFilteredRowModel",()=>s._autoResetPageIndex()))}function Ja(s){return e=>Je(()=>[e.getState().pagination,e.getPrePaginationRowModel(),e.options.paginateExpandedRows?void 0:e.getState().expanded],(o,h)=>{if(!h.rows.length)return h;const{pageSize:n,pageIndex:m}=o;let{rows:w,flatRows:f,rowsById:_}=h;const c=n*m,d=c+n;w=w.slice(c,d);let g;e.options.paginateExpandedRows?g={rows:w,flatRows:f,rowsById:_}:g=Ga({rows:w,flatRows:f,rowsById:_}),g.flatRows=[];const x=i=>{g.flatRows.push(i),i.subRows.length&&i.subRows.forEach(x)};return g.rows.forEach(x),g},Xe(e.options,"debugTable"))}function Xa(){return s=>Je(()=>[s.getState().sorting,s.getPreSortedRowModel()],(e,o)=>{if(!o.rows.length||!(e!=null&&e.length))return o;const h=s.getState().sorting,n=[],m=h.filter(_=>{var c;return(c=s.getColumn(_.id))==null?void 0:c.getCanSort()}),w={};m.forEach(_=>{const c=s.getColumn(_.id);c&&(w[_.id]={sortUndefined:c.columnDef.sortUndefined,invertSorting:c.columnDef.invertSorting,sortingFn:c.getSortingFn()})});const f=_=>{const c=_.map(d=>({...d}));return c.sort((d,g)=>{for(let i=0;i<m.length;i+=1){var x;const u=m[i],D=w[u.id],p=D.sortUndefined,l=(x=u==null?void 0:u.desc)!=null?x:!1;let t=0;if(p){const P=d.getValue(u.id),X=g.getValue(u.id),Q=P===void 0,le=X===void 0;if(Q||le){if(p==="first")return Q?-1:1;if(p==="last")return Q?1:-1;t=Q&&le?0:Q?p:-p}}if(t===0&&(t=D.sortingFn(d,g,u.id)),t!==0)return l&&(t*=-1),D.invertSorting&&(t*=-1),t}return d.index-g.index}),c.forEach(d=>{var g;n.push(d),(g=d.subRows)!=null&&g.length&&(d.subRows=f(d.subRows))}),c};return{rows:f(o.rows),flatRows:n,rowsById:o.rowsById}},Xe(s.options,"debugTable","getSortedRowModel",()=>s._autoResetPageIndex()))}var Qa=be("<title> </title>"),eo=be("<desc> </desc>"),to=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 2C7.77614 2 8 2.22386 8 2.5L8 11.2929L11.1464 8.14645C11.3417 7.95118 11.6583 7.95118 11.8536 8.14645C12.0488 8.34171 12.0488 8.65829 11.8536 8.85355L7.85355 12.8536C7.75979 12.9473 7.63261 13 7.5 13C7.36739 13 7.24021 12.9473 7.14645 12.8536L3.14645 8.85355C2.95118 8.65829 2.95118 8.34171 3.14645 8.14645C3.34171 7.95118 3.65829 7.95118 3.85355 8.14645L7 11.2929L7 2.5C7 2.22386 7.22386 2 7.5 2Z"></path></svg>');function ro(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"arrow down"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=to();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=Qa(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=eo(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}var ao=be("<title> </title>"),oo=be("<desc> </desc>"),lo=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z"></path></svg>');function no(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"arrow right"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=lo();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=ao(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=oo(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}var io=be("<title> </title>"),so=be("<desc> </desc>"),co=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z"></path></svg>');function uo(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"check"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=co();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=io(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=so(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}var vo=be("<title> </title>"),fo=be("<desc> </desc>"),mo=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M0.877075 7.49991C0.877075 3.84222 3.84222 0.877075 7.49991 0.877075C11.1576 0.877075 14.1227 3.84222 14.1227 7.49991C14.1227 11.1576 11.1576 14.1227 7.49991 14.1227C3.84222 14.1227 0.877075 11.1576 0.877075 7.49991ZM7.49991 1.82708C4.36689 1.82708 1.82708 4.36689 1.82708 7.49991C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C10.6329 13.1727 13.1727 10.6329 13.1727 7.49991C13.1727 4.36689 10.6329 1.82708 7.49991 1.82708Z"></path></svg>');function go(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"circle"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=mo();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=vo(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=fo(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}var _o=be("<title> </title>"),ho=be("<desc> </desc>"),bo=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z"></path></svg>');function zt(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"cross 2"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=bo();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=_o(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=ho(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}var xo=be("<title> </title>"),wo=be("<desc> </desc>"),yo=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM12.5 8.625C13.1213 8.625 13.625 8.12132 13.625 7.5C13.625 6.87868 13.1213 6.375 12.5 6.375C11.8787 6.375 11.375 6.87868 11.375 7.5C11.375 8.12132 11.8787 8.625 12.5 8.625Z"></path></svg>');function Co(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"dots horizontal"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=yo();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=xo(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=wo(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}var po=be("<title> </title>"),Po=be("<desc> </desc>"),Do=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M6.85355 3.85355C7.04882 3.65829 7.04882 3.34171 6.85355 3.14645C6.65829 2.95118 6.34171 2.95118 6.14645 3.14645L2.14645 7.14645C1.95118 7.34171 1.95118 7.65829 2.14645 7.85355L6.14645 11.8536C6.34171 12.0488 6.65829 12.0488 6.85355 11.8536C7.04882 11.6583 7.04882 11.3417 6.85355 11.1464L3.20711 7.5L6.85355 3.85355ZM12.8536 3.85355C13.0488 3.65829 13.0488 3.34171 12.8536 3.14645C12.6583 2.95118 12.3417 2.95118 12.1464 3.14645L8.14645 7.14645C7.95118 7.34171 7.95118 7.65829 8.14645 7.85355L12.1464 11.8536C12.3417 12.0488 12.6583 12.0488 12.8536 11.8536C13.0488 11.6583 13.0488 11.3417 12.8536 11.1464L9.20711 7.5L12.8536 3.85355Z"></path></svg>');function So(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"double arrow left"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=Do();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=po(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=Po(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}var Ro=be("<title> </title>"),Mo=be("<desc> </desc>"),Fo=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M2.14645 11.1464C1.95118 11.3417 1.95118 11.6583 2.14645 11.8536C2.34171 12.0488 2.65829 12.0488 2.85355 11.8536L6.85355 7.85355C7.04882 7.65829 7.04882 7.34171 6.85355 7.14645L2.85355 3.14645C2.65829 2.95118 2.34171 2.95118 2.14645 3.14645C1.95118 3.34171 1.95118 3.65829 2.14645 3.85355L5.79289 7.5L2.14645 11.1464ZM8.14645 11.1464C7.95118 11.3417 7.95118 11.6583 8.14645 11.8536C8.34171 12.0488 8.65829 12.0488 8.85355 11.8536L12.8536 7.85355C13.0488 7.65829 13.0488 7.34171 12.8536 7.14645L8.85355 3.14645C8.65829 2.95118 8.34171 2.95118 8.14645 3.14645C7.95118 3.34171 7.95118 3.65829 8.14645 3.85355L11.7929 7.5L8.14645 11.1464Z"></path></svg>');function Lo(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"double arrow right"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=Fo();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=Ro(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=Mo(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}var Ao=be("<title> </title>"),ko=be("<desc> </desc>"),Vo=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M3 2.5C3 2.22386 3.22386 2 3.5 2H9.08579C9.21839 2 9.34557 2.05268 9.43934 2.14645L11.8536 4.56066C11.9473 4.65443 12 4.78161 12 4.91421V12.5C12 12.7761 11.7761 13 11.5 13H3.5C3.22386 13 3 12.7761 3 12.5V2.5ZM3.5 1C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H11.5C12.3284 14 13 13.3284 13 12.5V4.91421C13 4.51639 12.842 4.13486 12.5607 3.85355L10.1464 1.43934C9.86514 1.15804 9.48361 1 9.08579 1H3.5ZM4.5 4C4.22386 4 4 4.22386 4 4.5C4 4.77614 4.22386 5 4.5 5H7.5C7.77614 5 8 4.77614 8 4.5C8 4.22386 7.77614 4 7.5 4H4.5ZM4.5 7C4.22386 7 4 7.22386 4 7.5C4 7.77614 4.22386 8 4.5 8H10.5C10.7761 8 11 7.77614 11 7.5C11 7.22386 10.7761 7 10.5 7H4.5ZM4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11H10.5C10.7761 11 11 10.7761 11 10.5C11 10.2239 10.7761 10 10.5 10H4.5Z"></path></svg>');function zo(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"file text"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=Vo();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=Ao(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=ko(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}var Io=be("<title> </title>"),To=be("<desc> </desc>"),Eo=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M5.5 3C4.67157 3 4 3.67157 4 4.5C4 5.32843 4.67157 6 5.5 6C6.32843 6 7 5.32843 7 4.5C7 3.67157 6.32843 3 5.5 3ZM3 5C3.01671 5 3.03323 4.99918 3.04952 4.99758C3.28022 6.1399 4.28967 7 5.5 7C6.71033 7 7.71978 6.1399 7.95048 4.99758C7.96677 4.99918 7.98329 5 8 5H13.5C13.7761 5 14 4.77614 14 4.5C14 4.22386 13.7761 4 13.5 4H8C7.98329 4 7.96677 4.00082 7.95048 4.00242C7.71978 2.86009 6.71033 2 5.5 2C4.28967 2 3.28022 2.86009 3.04952 4.00242C3.03323 4.00082 3.01671 4 3 4H1.5C1.22386 4 1 4.22386 1 4.5C1 4.77614 1.22386 5 1.5 5H3ZM11.9505 10.9976C11.7198 12.1399 10.7103 13 9.5 13C8.28967 13 7.28022 12.1399 7.04952 10.9976C7.03323 10.9992 7.01671 11 7 11H1.5C1.22386 11 1 10.7761 1 10.5C1 10.2239 1.22386 10 1.5 10H7C7.01671 10 7.03323 10.0008 7.04952 10.0024C7.28022 8.8601 8.28967 8 9.5 8C10.7103 8 11.7198 8.8601 11.9505 10.0024C11.9668 10.0008 11.9833 10 12 10H13.5C13.7761 10 14 10.2239 14 10.5C14 10.7761 13.7761 11 13.5 11H12C11.9833 11 11.9668 10.9992 11.9505 10.9976ZM8 10.5C8 9.67157 8.67157 9 9.5 9C10.3284 9 11 9.67157 11 10.5C11 11.3284 10.3284 12 9.5 12C8.67157 12 8 11.3284 8 10.5Z"></path></svg>');function Uo(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"mixer horizontal"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=Eo();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=Io(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=To(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}var No=be("<title> </title>"),jo=be("<desc> </desc>"),Oo=be('<svg><!><!><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49991 0.876892C3.84222 0.876892 0.877075 3.84204 0.877075 7.49972C0.877075 11.1574 3.84222 14.1226 7.49991 14.1226C11.1576 14.1226 14.1227 11.1574 14.1227 7.49972C14.1227 3.84204 11.1576 0.876892 7.49991 0.876892ZM1.82707 7.49972C1.82707 4.36671 4.36689 1.82689 7.49991 1.82689C10.6329 1.82689 13.1727 4.36671 13.1727 7.49972C13.1727 10.6327 10.6329 13.1726 7.49991 13.1726C4.36689 13.1726 1.82707 10.6327 1.82707 7.49972ZM7.50003 4C7.77617 4 8.00003 4.22386 8.00003 4.5V7H10.5C10.7762 7 11 7.22386 11 7.5C11 7.77614 10.7762 8 10.5 8H8.00003V10.5C8.00003 10.7761 7.77617 11 7.50003 11C7.22389 11 7.00003 10.7761 7.00003 10.5V8H4.50003C4.22389 8 4.00003 7.77614 4.00003 7.5C4.00003 7.22386 4.22389 7 4.50003 7H7.00003V4.5C7.00003 4.22386 7.22389 4 7.50003 4Z"></path></svg>');function Ho(s,e){var D,p;Me(e,!0);const o=qe("iconCtx")??{};let h=z(e,"size",19,()=>o.size||"24"),n=z(e,"role",19,()=>o.role||"img"),m=z(e,"color",19,()=>o.color||"currentColor"),w=z(e,"ariaLabel",3,"plus circled"),f=He(e,["$$slots","$$events","$$legacy","size","role","color","title","desc","ariaLabel"]),_=`${((D=e.title)==null?void 0:D.id)||""} ${((p=e.desc)==null?void 0:p.id)||""}`;const c=me(()=>{var l,t;return!!((l=e.title)!=null&&l.id||(t=e.desc)!=null&&t.id)});var d=Oo();Be(d,()=>({xmlns:"http://www.w3.org/2000/svg",...f,role:n(),width:h(),height:h(),fill:m(),"aria-label":w(),"aria-describedby":a(c)?_:void 0,viewBox:"0 0 15 15"}));var g=y(d);{var x=l=>{var t=No(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.title.id),$(P,e.title.title)}),r(l,t)};E(g,l=>{var t;(t=e.title)!=null&&t.id&&e.title.title&&l(x)})}var i=v(g);{var u=l=>{var t=jo(),P=y(t,!0);C(t),J(()=>{Ve(t,"id",e.desc.id),$(P,e.desc.desc)}),r(l,t)};E(i,l=>{var t;(t=e.desc)!=null&&t.id&&e.desc.desc&&l(u)})}H(),C(d),r(s,d),Fe()}function It(s){if(!s)return"N/A";const e=typeof s=="string"?new Date(s):s;return isNaN(e.getTime())?"Invalid date":new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(e)}var Bo=L(" <!> <!>",1),Zo=L("<!> <!>",1),qo=L('<div><h4 class="text-muted-foreground text-sm font-medium">Last Updated</h4> <p> </p></div>'),Go=L('<div><h4 class="text-muted-foreground text-sm font-medium">Score</h4> <p> </p></div>'),$o=L("<!> Edit",1),Yo=L("<!> ",1),Ko=L("<!> Set as Default",1),Wo=L("<!> Delete",1),Jo=L('<div class="flex flex-wrap justify-between gap-2"><div class="flex flex-wrap gap-2"><!> <!> <!></div> <!></div>'),Xo=L('<!> <div class="py-6"><div class="border-border bg-card mb-6 rounded-lg border p-4"><div class="aspect-[3/4] w-full overflow-hidden rounded-md" style="height: 500px;"><!></div></div> <div class="space-y-4"><div class="grid grid-cols-2 gap-4"><div><h4 class="text-muted-foreground text-sm font-medium">Type</h4> <p> </p></div> <div><h4 class="text-muted-foreground text-sm font-medium">Created</h4> <p> </p></div> <!> <!></div></div></div> <!>',1),Qo=L("<!> <!>",1),el=L("<!> <!>",1),tl=L("<!> <!>",1),rl=L("<!> <!>",1),al=L("<!> <!>",1);function ol(s,e){Me(e,!0);const o=z(e,"document",7),h=z(e,"open",3,!1),n=z(e,"onClose",3,()=>{}),m=z(e,"onDeleted",3,p=>{}),w=z(e,"onSetDefault",3,p=>{});let f=Se(!1);function _(){n()()}function c(){ut(`/dashboard/documents/${o().id}`)}async function d(){if(o().fileUrl)window.open(o().fileUrl,"_blank");else if(o().type==="resume")try{ye.loading("Generating resume PDF...");const p=await fetch(`/api/resume/${o().id}/download`);if(p.ok){const l=await p.blob(),t=URL.createObjectURL(l);window.open(t,"_blank"),o().fileUrl=`/uploads/generated/${o().label.replace(/[^a-zA-Z0-9_\-\.]/g,"_")}.pdf`,ye.success("Resume PDF generated successfully")}else ye.error("Failed to generate resume PDF")}catch(p){console.error("Error generating resume PDF:",p),ye.error("An error occurred while generating the resume PDF")}else ye.error("No file available for download")}async function g(){try{(await fetch(`/api/documents/${o().id}`,{method:"DELETE"})).ok?(ye.success("Document deleted successfully"),_(),m()(o().id)):ye.error("Failed to delete document")}catch(p){console.error("Error deleting document:",p),ye.error("An error occurred while deleting the document")}O(f,!1)}function x(){ye.success("Document set as default"),w()(o().id)}var i=al(),u=M(i);U(u,()=>zr,(p,l)=>{l(p,{get open(){return h()},onOpenChange:_,children:(t,P)=>{var X=ce(),Q=M(X);U(Q,()=>Mr,(le,se)=>{se(le,{children:(ge,de)=>{var G=Qo(),A=M(G);U(A,()=>Fr,(j,k)=>{k(j,{})});var S=v(A,2);U(S,()=>Lr,(j,k)=>{k(j,{class:"sm:max-w-xl",children:(B,re)=>{var N=Xo(),R=M(N);U(R,()=>Ar,(W,ie)=>{ie(W,{children:(te,he)=>{var ue=Zo(),fe=M(ue);U(fe,()=>kr,(Le,Ae)=>{Ae(Le,{class:"flex items-center gap-2",children:(we,Ie)=>{H();var Pe=Bo(),ze=M(Pe),ke=v(ze);{var De=Ee=>{const Ze=me(()=>o().source==="generated"?"default":"outline");lt(Ee,{get variant(){return a(Ze)},class:"ml-2",children:(Ke,Ue)=>{H();var Ge=oe();J(()=>$(Ge,o().source)),r(Ke,Ge)},$$slots:{default:!0}})};E(ke,Ee=>{o().source&&Ee(De)})}var Oe=v(ke,2);{var je=Ee=>{lt(Ee,{variant:"secondary",class:"ml-2",children:(Ze,Ke)=>{H();var Ue=oe("Default");r(Ze,Ue)},$$slots:{default:!0}})};E(Oe,Ee=>{o().isDefault&&Ee(je)})}J(()=>$(ze,`${o().label??""} `)),r(we,Pe)},$$slots:{default:!0}})});var Re=v(fe,2);U(Re,()=>Vr,(Le,Ae)=>{Ae(Le,{children:(we,Ie)=>{H();var Pe=oe("View and manage your document");r(we,Pe)},$$slots:{default:!0}})}),r(te,ue)},$$slots:{default:!0}})});var b=v(R,2),F=y(b),V=y(F),q=y(V);Ir(q,{get document(){return o()}}),C(V),C(F);var I=v(F,2),Z=y(I),K=y(Z),ae=v(y(K),2),ve=y(ae,!0);C(ae),C(K);var _e=v(K,2),xe=v(y(_e),2),Ce=y(xe,!0);C(xe),C(_e);var Y=v(_e,2);{var ee=W=>{var ie=qo(),te=v(y(ie),2),he=y(te,!0);C(te),C(ie),J(ue=>$(he,ue),[()=>It(o().updatedAt)]),r(W,ie)};E(Y,W=>{o().updatedAt&&W(ee)})}var T=v(Y,2);{var ne=W=>{var ie=Go(),te=v(y(ie),2),he=y(te);C(te),C(ie),J(()=>$(he,`${o().score??""}/100`)),r(W,ie)};E(T,W=>{o().score!==void 0&&o().score!==null&&W(ne)})}C(Z),C(I),C(b);var pe=v(b,2);U(pe,()=>Tr,(W,ie)=>{ie(W,{children:(te,he)=>{var ue=Jo(),fe=y(ue),Re=y(fe);Te(Re,{variant:"outline",size:"sm",onclick:c,children:(Pe,ze)=>{var ke=$o(),De=M(ke);Er(De,{class:"mr-2 h-4 w-4"}),H(),r(Pe,ke)},$$slots:{default:!0}});var Le=v(Re,2);Te(Le,{variant:"outline",size:"sm",onclick:d,children:(Pe,ze)=>{var ke=Yo(),De=M(ke);Ur(De,{class:"mr-2 h-4 w-4"});var Oe=v(De);J(()=>$(Oe,` ${o().fileUrl?"Download":o().type==="resume"?"Generate & Download":"Download"}`)),r(Pe,ke)},$$slots:{default:!0}});var Ae=v(Le,2);{var we=Pe=>{Te(Pe,{variant:"outline",size:"sm",onclick:x,children:(ze,ke)=>{var De=Ko(),Oe=M(De);jr(Oe,{class:"mr-2 h-4 w-4"}),H(),r(ze,De)},$$slots:{default:!0}})};E(Ae,Pe=>{o().isDefault||Pe(we)})}C(fe);var Ie=v(fe,2);Te(Ie,{variant:"destructive",size:"sm",onclick:()=>O(f,!0),children:(Pe,ze)=>{var ke=Wo(),De=M(ke);Nr(De,{class:"mr-2 h-4 w-4"}),H(),r(Pe,ke)},$$slots:{default:!0}}),C(ue),r(te,ue)},$$slots:{default:!0}})}),J(W=>{$(ve,o().type),$(Ce,W)},[()=>It(o().createdAt)]),r(B,N)},$$slots:{default:!0}})}),r(ge,G)},$$slots:{default:!0}})}),r(t,X)},$$slots:{default:!0}})});var D=v(u,2);U(D,()=>sr,(p,l)=>{l(p,{get open(){return a(f)},children:(t,P)=>{var X=ce(),Q=M(X);U(Q,()=>tr,(le,se)=>{se(le,{children:(ge,de)=>{var G=rl(),A=M(G);U(A,()=>rr,(j,k)=>{k(j,{children:(B,re)=>{var N=el(),R=M(N);U(R,()=>ar,(F,V)=>{V(F,{children:(q,I)=>{H();var Z=oe("Are you sure?");r(q,Z)},$$slots:{default:!0}})});var b=v(R,2);U(b,()=>or,(F,V)=>{V(F,{children:(q,I)=>{H();var Z=oe();J(()=>$(Z,`This will permanently delete the document "${o().label??""}". This action cannot be undone.`)),r(q,Z)},$$slots:{default:!0}})}),r(B,N)},$$slots:{default:!0}})});var S=v(A,2);U(S,()=>lr,(j,k)=>{k(j,{children:(B,re)=>{var N=tl(),R=M(N);U(R,()=>nr,(F,V)=>{V(F,{onclick:()=>O(f,!1),children:(q,I)=>{H();var Z=oe("Cancel");r(q,Z)},$$slots:{default:!0}})});var b=v(R,2);U(b,()=>ir,(F,V)=>{V(F,{onclick:g,children:(q,I)=>{H();var Z=oe("Delete");r(q,Z)},$$slots:{default:!0}})}),r(B,N)},$$slots:{default:!0}})}),r(ge,G)},$$slots:{default:!0}})}),r(t,X)},$$slots:{default:!0}})}),r(s,i),Fe()}var ll=L('<!> <span class="sr-only">Open Menu</span>',1),nl=L("Delete <!>",1),il=L("<!> <!> <!> <!> <!>",1),sl=L("<!> <!>",1),dl=L("<!> <!>",1),cl=L("<!> <!>",1),ul=L("<!> <!>",1),vl=L("<!> <!> <!>",1);function fl(s,e){Me(e,!0);const o=z(e,"on_deleted",3,void 0);let h=Se(!1),n=Se(!1),m=Se(null);function w(x){x||O(m,null)}async function f(){try{(await fetch(`/api/documents/${e.row.id}`,{method:"DELETE"})).ok?(ye.success("Document deleted successfully"),o()&&o()(e.row.id)):ye.error("Failed to delete document")}catch(x){console.error("Error deleting document:",x),ye.error("An error occurred while deleting the document")}O(n,!1)}var _=vl(),c=M(_);U(c,()=>Qt,(x,i)=>{i(x,{onOpenChange:w,children:(u,D)=>{var p=sl(),l=M(p);U(l,()=>Jt,(P,X)=>{X(P,{children:(Q,le)=>{Te(Q,{variant:"ghost",class:"flex h-8 w-8 p-0",children:(se,ge)=>{var de=ll(),G=M(de);Co(G,{class:"h-4 w-4"}),H(2),r(se,de)},$$slots:{default:!0}})},$$slots:{default:!0}})});var t=v(l,2);U(t,()=>Xt,(P,X)=>{X(P,{class:"w-[160px]",align:"end",children:(Q,le)=>{var se=il(),ge=M(se);const de=me(()=>a(m)==="view"?"bg-accent text-accent-foreground":"");U(ge,()=>et,(N,R)=>{R(N,{onclick:()=>{O(m,"view"),O(h,!0)},get class(){return a(de)},children:(b,F)=>{H();var V=oe("View");r(b,V)},$$slots:{default:!0}})});var G=v(ge,2);const A=me(()=>a(m)==="edit"?"bg-accent text-accent-foreground":"");U(G,()=>et,(N,R)=>{R(N,{onclick:()=>{O(m,"edit"),ut(`/dashboard/documents/${e.row.id}`)},get class(){return a(A)},children:(b,F)=>{H();var V=oe("Edit");r(b,V)},$$slots:{default:!0}})});var S=v(G,2);{var j=N=>{var R=ce(),b=M(R);const F=me(()=>a(m)==="builder"?"bg-accent text-accent-foreground":"");U(b,()=>et,(V,q)=>{q(V,{onclick:()=>{O(m,"builder"),ut(`/dashboard/builder/${e.row.id}`)},get class(){return a(F)},children:(I,Z)=>{H();var K=oe("Edit in Builder");r(I,K)},$$slots:{default:!0}})}),r(N,R)};E(S,N=>{e.row.type==="resume"&&N(j)})}var k=v(S,2);U(k,()=>dr,(N,R)=>{R(N,{})});var B=v(k,2);const re=me(()=>`text-destructive ${a(m)==="delete"?"bg-accent text-accent-foreground":""}`);U(B,()=>et,(N,R)=>{R(N,{get class(){return a(re)},onclick:()=>{O(m,"delete"),O(n,!0)},children:(b,F)=>{H();var V=nl(),q=v(M(V));U(q,()=>ua,(I,Z)=>{Z(I,{children:(K,ae)=>{H();var ve=oe("⌘⌫");r(K,ve)},$$slots:{default:!0}})}),r(b,V)},$$slots:{default:!0}})}),r(Q,se)},$$slots:{default:!0}})}),r(u,p)},$$slots:{default:!0}})});var d=v(c,2);ol(d,{get document(){return e.row},get open(){return a(h)},onClose:()=>O(h,!1),onDeleted:x=>{o()&&o()(x)}});var g=v(d,2);U(g,()=>sr,(x,i)=>{i(x,{get open(){return a(n)},children:(u,D)=>{var p=ce(),l=M(p);U(l,()=>tr,(t,P)=>{P(t,{children:(X,Q)=>{var le=ul(),se=M(le);U(se,()=>rr,(de,G)=>{G(de,{children:(A,S)=>{var j=dl(),k=M(j);U(k,()=>ar,(re,N)=>{N(re,{children:(R,b)=>{H();var F=oe("Are you sure?");r(R,F)},$$slots:{default:!0}})});var B=v(k,2);U(B,()=>or,(re,N)=>{N(re,{children:(R,b)=>{H();var F=oe();J(()=>$(F,`This will permanently delete the document "${e.row.label??""}". This action cannot be undone.`)),r(R,F)},$$slots:{default:!0}})}),r(A,j)},$$slots:{default:!0}})});var ge=v(se,2);U(ge,()=>lr,(de,G)=>{G(de,{children:(A,S)=>{var j=cl(),k=M(j);U(k,()=>nr,(re,N)=>{N(re,{onclick:()=>O(n,!1),children:(R,b)=>{H();var F=oe("Cancel");r(R,F)},$$slots:{default:!0}})});var B=v(k,2);U(B,()=>ir,(re,N)=>{N(re,{onclick:f,children:(R,b)=>{H();var F=oe("Delete");r(R,F)},$$slots:{default:!0}})}),r(A,j)},$$slots:{default:!0}})}),r(X,le)},$$slots:{default:!0}})}),r(u,p)},$$slots:{default:!0}})}),r(s,_),Fe()}var ml=L("<!> <!> <!> <!> <!>",1),gl=L("<!> <!>",1),_l=L('<span class="sr-only">Go to first page</span> <!>',1),hl=L('<span class="sr-only">Go to previous page</span> <!>',1),bl=L('<span class="sr-only">Go to next page</span> <!>',1),xl=L('<span class="sr-only">Go to last page</span> <!>',1),wl=L('<div class="flex items-center justify-between px-2"><div class="text-muted-foreground flex-1 text-sm"> </div> <div class="flex items-center space-x-6 lg:space-x-8"><div class="flex items-center space-x-2"><p class="text-sm font-medium">Rows per page</p> <!></div> <div class="flex w-[100px] items-center justify-center text-sm font-medium"> </div> <div class="flex items-center space-x-2"><!> <!> <!> <!></div></div></div>');function yl(s,e){Me(e,!0);const o=z(e,"selectedRowCount",3,0),h=z(e,"totalRowCount",3,0),n=z(e,"onPaginationChange",3,void 0);let m=Se(10),w=Se(0);function f(){var G,A;if(e.tableModel){const S=e.tableModel.getState?e.tableModel.getState():{pagination:{pageSize:10,pageIndex:0}};O(m,((G=S.pagination)==null?void 0:G.pageSize)||10,!0),O(w,((A=S.pagination)==null?void 0:A.pageIndex)||0,!0)}}f();var _=wl(),c=y(_),d=y(c);C(c);var g=v(c,2),x=y(g),i=v(y(x),2);const u=me(()=>String(a(m)));U(i,()=>wt,(G,A)=>{A(G,{type:"single",get value(){return a(u)},onValueChange:S=>{if(e.tableModel){const j=Number(S),k=0;O(m,j,!0),O(w,k);const B={pageIndex:k,pageSize:j};e.tableModel.setState?e.tableModel.setState({pagination:B}):e.tableModel.onPaginationChange&&e.tableModel.onPaginationChange(B),e.tableModel.setPageIndex&&e.tableModel.setPageIndex(0),n()&&n()(B),f()}},children:(S,j)=>{var k=gl(),B=M(k);U(B,()=>yt,(N,R)=>{R(N,{class:"h-8 w-[70px]",children:(b,F)=>{var V=ce(),q=M(V);const I=me(()=>String(a(m)));U(q,()=>Pt,(Z,K)=>{K(Z,{get placeholder(){return a(I)}})}),r(b,V)},$$slots:{default:!0}})});var re=v(B,2);U(re,()=>Ct,(N,R)=>{R(N,{class:"min-w-[70px]",children:(b,F)=>{var V=ml(),q=M(V);U(q,()=>We,(ve,_e)=>{_e(ve,{value:"10",children:(xe,Ce)=>{H();var Y=oe("10");r(xe,Y)},$$slots:{default:!0}})});var I=v(q,2);U(I,()=>We,(ve,_e)=>{_e(ve,{value:"20",children:(xe,Ce)=>{H();var Y=oe("20");r(xe,Y)},$$slots:{default:!0}})});var Z=v(I,2);U(Z,()=>We,(ve,_e)=>{_e(ve,{value:"30",children:(xe,Ce)=>{H();var Y=oe("30");r(xe,Y)},$$slots:{default:!0}})});var K=v(Z,2);U(K,()=>We,(ve,_e)=>{_e(ve,{value:"40",children:(xe,Ce)=>{H();var Y=oe("40");r(xe,Y)},$$slots:{default:!0}})});var ae=v(K,2);U(ae,()=>We,(ve,_e)=>{_e(ve,{value:"50",children:(xe,Ce)=>{H();var Y=oe("50");r(xe,Y)},$$slots:{default:!0}})}),r(b,V)},$$slots:{default:!0}})}),r(S,k)},$$slots:{default:!0}})}),C(x);var D=v(x,2),p=y(D);C(D);var l=v(D,2),t=y(l);const P=me(()=>!(e.tableModel&&e.tableModel.getCanPreviousPage&&e.tableModel.getCanPreviousPage()));Te(t,{variant:"outline",class:"hidden h-8 w-8 p-0 lg:flex",onclick:()=>{if(e.tableModel){O(w,0);const A={pageIndex:0,pageSize:a(m)};e.tableModel.setPageIndex?e.tableModel.setPageIndex(0):e.tableModel.setState&&e.tableModel.setState({pagination:A}),n()&&n()(A),f()}},get disabled(){return a(P)},children:(G,A)=>{var S=_l(),j=v(M(S),2);So(j,{size:"15"}),r(G,S)},$$slots:{default:!0}});var X=v(t,2);const Q=me(()=>!(e.tableModel&&e.tableModel.getCanPreviousPage&&e.tableModel.getCanPreviousPage()));Te(X,{variant:"outline",class:"h-8 w-8 p-0",onclick:()=>{if(e.tableModel){const G=Math.max(0,a(w)-1);O(w,G,!0);const A={pageIndex:G,pageSize:a(m)};e.tableModel.previousPage?e.tableModel.previousPage():e.tableModel.setPageIndex?e.tableModel.setPageIndex(G):e.tableModel.setState&&e.tableModel.setState({pagination:A}),n()&&n()(A),f()}},get disabled(){return a(Q)},children:(G,A)=>{var S=hl(),j=v(M(S),2);Or(j,{size:"15"}),r(G,S)},$$slots:{default:!0}});var le=v(X,2);const se=me(()=>!(e.tableModel&&e.tableModel.getCanNextPage&&e.tableModel.getCanNextPage()));Te(le,{variant:"outline",class:"h-8 w-8 p-0",get disabled(){return a(se)},onclick:()=>{if(e.tableModel){const G=Math.ceil(h()/a(m))-1,A=Math.min(G,a(w)+1);O(w,A,!0);const S={pageIndex:A,pageSize:a(m)};e.tableModel.nextPage?e.tableModel.nextPage():e.tableModel.setPageIndex?e.tableModel.setPageIndex(A):e.tableModel.setState&&e.tableModel.setState({pagination:S}),n()&&n()(S),f()}},children:(G,A)=>{var S=bl(),j=v(M(S),2);Hr(j,{size:"15"}),r(G,S)},$$slots:{default:!0}});var ge=v(le,2);const de=me(()=>!(e.tableModel&&e.tableModel.getCanNextPage&&e.tableModel.getCanNextPage()));Te(ge,{variant:"outline",class:"hidden h-8 w-8 p-0 lg:flex",get disabled(){return a(de)},onclick:()=>{if(e.tableModel){const A=Math.ceil(h()/a(m))-1;O(w,A,!0);const S={pageIndex:A,pageSize:a(m)};e.tableModel.setPageIndex&&e.tableModel.getPageCount?e.tableModel.setPageIndex(e.tableModel.getPageCount()-1):e.tableModel.setPageIndex?e.tableModel.setPageIndex(A):e.tableModel.setState&&e.tableModel.setState({pagination:S}),n()&&n()(S),f()}},children:(G,A)=>{var S=xl(),j=v(M(S),2);Lo(j,{size:"15"}),r(G,S)},$$slots:{default:!0}}),C(l),C(g),C(_),J(G=>{$(d,`${o()??""} of ${h()??""} row(s) selected.`),$(p,`Page ${a(w)+1} of ${G??""}`)},[()=>e.tableModel&&e.tableModel.getPageCount?e.tableModel.getPageCount():1]),r(s,_),Fe()}var Cl=L("<!> View",1),pl=L('<div class="bg-primary h-2 w-2 rounded-sm"></div>'),Pl=L('<div class="flex items-center"><div class="border-primary mr-2 flex h-4 w-4 items-center justify-center rounded border"><!></div> <span> </span></div>'),Dl=L('<span class="text-muted-foreground">No columns available</span>'),Sl=L("<!> <!> <!>",1),Rl=L("<!> <!>",1),Ml=L('<div class="flex items-center gap-2"><!> <!></div>');function Fl(s,e){var c,d;Me(e,!0);const o=["document","type","createdAt","updatedAt"];let h={};if((c=e.tableModel)!=null&&c.getState&&typeof e.tableModel.getState=="function"){const g=e.tableModel.getState();h=(g==null?void 0:g.columnVisibility)||{}}else(d=e.tableModel)!=null&&d.state&&e.tableModel.state.columnVisibility&&(h=e.tableModel.state.columnVisibility);let n=Se(Ne({document:!0,type:!0,createdAt:!0,updatedAt:!0}));o.forEach(g=>{h[g]===!1?a(n)[g]=!1:a(n)[g]=!0});function m(g){if(!e.tableModel){console.error("tableModel is not available");return}const x={...a(n)};x[g]=!x[g],O(n,x,!0);try{e.tableModel.setColumnVisibility&&typeof e.tableModel.setColumnVisibility=="function"?e.tableModel.setColumnVisibility(x):e.tableModel.onColumnVisibilityChange&&typeof e.tableModel.onColumnVisibilityChange=="function"?e.tableModel.onColumnVisibilityChange(x):e.tableModel.setState&&typeof e.tableModel.setState=="function"?e.tableModel.setState({columnVisibility:x}):typeof document<"u"&&document.dispatchEvent(new CustomEvent("columnVisibilityChange",{detail:{visibility:x}}))}catch(i){console.error("Error updating column visibility:",i),typeof document<"u"&&document.dispatchEvent(new CustomEvent("columnVisibilityChange",{detail:{visibility:x}}))}}var w=ce(),f=M(w);{var _=g=>{var x=Ml(),i=y(x);U(i,()=>Qt,(p,l)=>{l(p,{children:(t,P)=>{var X=Rl(),Q=M(X);U(Q,()=>Jt,(se,ge)=>{ge(se,{children:(de,G)=>{Te(de,{variant:"outline",size:"sm",class:"ml-auto hidden h-8 lg:flex",children:(A,S)=>{var j=Cl(),k=M(j);Uo(k,{class:"mr-2 h-4 w-4"}),H(),r(A,j)},$$slots:{default:!0}})},$$slots:{default:!0}})});var le=v(Q,2);U(le,()=>Xt,(se,ge)=>{ge(se,{align:"end",children:(de,G)=>{var A=Sl(),S=M(A);U(S,()=>Br,(N,R)=>{R(N,{children:(b,F)=>{H();var V=oe("Toggle columns");r(b,V)},$$slots:{default:!0}})});var j=v(S,2);U(j,()=>dr,(N,R)=>{R(N,{})});var k=v(j,2);{var B=N=>{var R=ce(),b=M(R);$e(b,17,()=>e.tableModel.getAllColumns()||[],Ye,(F,V)=>{var q=ce(),I=M(q);{var Z=K=>{var ae=ce(),ve=M(ae);U(ve,()=>et,(_e,xe)=>{xe(_e,{onclick:()=>m(a(V).id),children:(Ce,Y)=>{var ee=Pl(),T=y(ee),ne=y(T);{var pe=te=>{var he=pl();r(te,he)};E(ne,te=>{a(n)[a(V).id]!==!1&&te(pe)})}C(T);var W=v(T,2),ie=y(W,!0);C(W),C(ee),J(te=>$(ie,te),[()=>a(V).id==="document"?"Document Name":a(V).id==="type"?"Type":a(V).id==="createdAt"?"Created":a(V).id==="updatedAt"?"Edited":a(V).id.charAt(0).toUpperCase()+a(V).id.slice(1)]),r(Ce,ee)},$$slots:{default:!0}})}),r(K,ae)};E(I,K=>{o.includes(a(V).id)&&K(Z)})}r(F,q)}),r(N,R)},re=N=>{var R=ce(),b=M(R);U(b,()=>et,(F,V)=>{V(F,{children:(q,I)=>{var Z=Dl();r(q,Z)},$$slots:{default:!0}})}),r(N,R)};E(k,N=>{e.tableModel.getAllColumns&&typeof e.tableModel.getAllColumns=="function"?N(B):N(re,!1)})}r(de,A)},$$slots:{default:!0}})}),r(t,X)},$$slots:{default:!0}})});var u=v(i,2);{var D=p=>{Te(p,{variant:"ghost",size:"sm",onclick:()=>{const l={};o.forEach(t=>{l[t]=!0}),O(n,l,!0);try{e.tableModel.setColumnVisibility&&typeof e.tableModel.setColumnVisibility=="function"?e.tableModel.setColumnVisibility(l):e.tableModel.onColumnVisibilityChange&&typeof e.tableModel.onColumnVisibilityChange=="function"?e.tableModel.onColumnVisibilityChange(l):e.tableModel.setState&&typeof e.tableModel.setState=="function"?e.tableModel.setState({columnVisibility:l}):document.dispatchEvent(new CustomEvent("columnVisibilityChange",{detail:{visibility:l}}))}catch(t){console.error("Error resetting column visibility:",t),document.dispatchEvent(new CustomEvent("columnVisibilityChange",{detail:{visibility:l}}))}},class:"h-8",children:(l,t)=>{H();var P=oe("Reset");r(l,P)},$$slots:{default:!0}})};E(u,p=>{typeof window<"u"&&window.location.hostname==="localhost"&&p(D)})}C(x),r(g,x)};E(f,g=>{e.tableModel&&g(_)})}r(s,w),Fe()}var Ll=L('<!> <!> <div class="hidden space-x-1 lg:flex"><!></div>',1),Al=L("<!> <!>",1),kl=L("<div>No results found.</div>"),Vl=(s,e,o)=>e(a(o).value),zl=(s,e,o)=>s.key==="Enter"&&e(a(o).value),Il=L('<span class="ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs"> </span>'),Tl=L('<button type="button" class="hover:bg-accent relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-left text-sm outline-none"><div class="flex w-full items-center"><div><!></div> <!> <span> </span> <!></div></button>'),El=(s,e)=>s.key==="Enter"&&e(),Ul=L('<!> <button type="button" class="hover:bg-accent flex w-full cursor-pointer select-none items-center justify-center rounded-sm px-2 py-1.5 text-sm outline-none">Clear filters</button>',1),Nl=L("<!> <!> <!>",1),jl=L("<!> <!>",1),Ol=L("<!> <!>",1);function Tt(s,e){Me(e,!0);const o=z(e,"title",3,""),h=z(e,"options",19,()=>[]),n=z(e,"counts",19,()=>({})),m=z(e,"filterValues",19,()=>[]),w=z(e,"onFilterChange",3,void 0);let f=Se(!1);function _(x){const i=[...m()],u=i.indexOf(x);u!==-1?i.splice(u,1):i.push(x),w()&&w()(i)}function c(){w()&&w()([]),O(f,!1)}var d=ce(),g=M(d);U(g,()=>$r,(x,i)=>{i(x,{get open(){return a(f)},set open(u){O(f,u,!0)},children:(u,D)=>{var p=Ol(),l=M(p);U(l,()=>qr,(P,X)=>{X(P,{children:(Q,le)=>{const se=me(()=>ft("h-8 border-dashed",m().length>0&&"bg-muted border-muted text-muted-foreground"));Te(Q,{variant:"outline",size:"sm",get class(){return a(se)},children:(ge,de)=>{var G=Al(),A=M(G);Ho(A,{class:"mr-2 h-4 w-4"});var S=v(A),j=v(S);{var k=B=>{var re=Ll(),N=M(re);ra(N,{orientation:"vertical",class:"mx-2 h-4"});var R=v(N,2);lt(R,{variant:"secondary",class:"rounded-sm px-1 font-normal lg:hidden",children:(I,Z)=>{H();var K=oe();J(()=>$(K,m().length)),r(I,K)},$$slots:{default:!0}});var b=v(R,2),F=y(b);{var V=I=>{lt(I,{variant:"secondary",class:"rounded-sm px-1 font-normal",children:(Z,K)=>{H();var ae=oe();J(()=>$(ae,`${m().length??""} Selected`)),r(Z,ae)},$$slots:{default:!0}})},q=I=>{var Z=ce(),K=M(Z);$e(K,17,m,Ye,(ae,ve)=>{lt(ae,{variant:"secondary",class:"rounded-sm px-1 font-normal",children:(_e,xe)=>{H();var Ce=oe();J(()=>$(Ce,a(ve))),r(_e,Ce)},$$slots:{default:!0}})}),r(I,Z)};E(F,I=>{m().length>2?I(V):I(q,!1)})}C(b),r(B,re)};E(j,B=>{m().length>0&&B(k)})}J(()=>$(S,` ${o()??""} `)),r(ge,G)},$$slots:{default:!0}})},$$slots:{default:!0}})});var t=v(l,2);U(t,()=>Gr,(P,X)=>{X(P,{class:"w-[200px] p-0",sideOffset:8,children:(Q,le)=>{var se=ce(),ge=M(se);U(ge,()=>Kr,(de,G)=>{G(de,{children:(A,S)=>{var j=jl(),k=M(j);U(k,()=>Qr,(re,N)=>{N(re,{get placeholder(){return o()}})});var B=v(k,2);U(B,()=>Wr,(re,N)=>{N(re,{children:(R,b)=>{var F=Nl(),V=M(F);U(V,()=>Jr,(K,ae)=>{ae(K,{children:(ve,_e)=>{var xe=kl();r(ve,xe)},$$slots:{default:!0}})});var q=v(V,2);U(q,()=>Xr,(K,ae)=>{ae(K,{children:(ve,_e)=>{var xe=ce(),Ce=M(xe);$e(Ce,17,h,Ye,(Y,ee)=>{var T=Tl();const ne=me(()=>a(ee).icon);T.__click=[Vl,_,ee],T.__keydown=[zl,_,ee];var pe=y(T),W=y(pe),ie=y(W);{var te=we=>{uo(we,{class:"h-3 w-3"})};E(ie,we=>{m().includes(a(ee).value)&&we(te)})}C(W);var he=v(W,2);{var ue=we=>{var Ie=ce(),Pe=M(Ie);U(Pe,()=>a(ne),(ze,ke)=>{ke(ze,{class:"text-muted-foreground mr-2 h-4 w-4"})}),r(we,Ie)};E(he,we=>{a(ne)&&we(ue)})}var fe=v(he,2),Re=y(fe,!0);C(fe);var Le=v(fe,2);{var Ae=we=>{var Ie=Il(),Pe=y(Ie,!0);C(Ie),J(()=>$(Pe,n()[a(ee).value])),r(we,Ie)};E(Le,we=>{n()[a(ee).value]&&we(Ae)})}C(pe),C(T),J(we=>{er(W,1,we),$(Re,a(ee).label)},[()=>Rr(ft("border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border",m().includes(a(ee).value)?"bg-primary text-primary-foreground":"opacity-50"))]),r(Y,T)}),r(ve,xe)},$$slots:{default:!0}})});var I=v(q,2);{var Z=K=>{var ae=Ul(),ve=M(ae);U(ve,()=>va,(xe,Ce)=>{Ce(xe,{})});var _e=v(ve,2);_e.__click=c,_e.__keydown=[El,c],r(K,ae)};E(I,K=>{m().length>0&&K(Z)})}r(R,F)},$$slots:{default:!0}})}),r(A,j)},$$slots:{default:!0}})}),r(Q,se)},$$slots:{default:!0}})}),r(u,p)},$$slots:{default:!0}})}),r(s,d),Fe()}Ot(["click","keydown"]);aa({id:at(),title:at(),status:at(),label:at(),priority:at()});const Hl=[{id:"resume",name:"Resume"},{id:"cover_letter",name:"Cover Letter"},{id:"portfolio",name:"Portfolio"},{id:"other",name:"Other"}],Bl=[{label:"generated",value:"Generated",icon:ro},{label:"created",value:"Created",icon:go},{label:"uploaded",value:"Uploaded",icon:no}];var Zl=L("Reset <!>",1),ql=L('<span class="font-semibold">Type:</span> ',1),Gl=L('<span class="ml-1 font-semibold">Source:</span> ',1),$l=L("Clear All <!>",1),Yl=L('<span class="mr-2">Filters: <!> <!></span> <!>',1),Kl=L('<div class="flex flex-col gap-4"><div class="flex items-center justify-between"><div class="flex flex-1 items-center space-x-2"><div class="relative"><!></div> <!></div> <!></div> <div class="flex items-center space-x-2"><!> <!> <div class="text-muted-foreground ml-4 flex items-center text-xs"><!></div></div></div>');function Wl(s,e){Me(e,!0);const o=z(e,"data",19,()=>[]),h=z(e,"searchTerm",3,""),n=z(e,"typeFilter",19,()=>[]),m=z(e,"sourceFilter",19,()=>[]),w=z(e,"onSearchChange",3,void 0),f=z(e,"onTypeFilterChange",3,void 0),_=z(e,"onSourceFilterChange",3,void 0),c=Hl.map(k=>({value:k.id,label:k.name,icon:zo})),d=Bl.map(k=>({value:k.label,label:k.value,icon:k.icon}));let g=Se(Ne({})),x=Se(Ne({}));function i(){const k=Array.isArray(o())?o():[];O(g,c.reduce((B,re)=>(B[re.value]=k.filter(N=>N.type===re.value).length,B),{}),!0),O(x,{generated:k.filter(B=>B.source==="generated").length,uploaded:k.filter(B=>B.source==="uploaded").length,created:k.filter(B=>B.source==="created").length},!0)}i();const u=me(()=>h()!==""||n().length>0||m().length>0);var D=Kl(),p=y(D),l=y(p),t=y(l),P=y(t);Zr(P,{get value(){return h()},placeholder:"Filter documents...",className:"h-8 w-[150px] lg:w-[250px]",paramName:"search",onSearch:k=>{w()&&w()(k)}}),C(t);var X=v(t,2);{var Q=k=>{Te(k,{onclick:()=>{if(w()&&w()(""),f()&&f()([]),_()&&_()([]),typeof window<"u"){const B=new URL(window.location.href),re=new URLSearchParams(B.search);re.delete("search");const N=`${B.pathname}?${re.toString()}`;window.history.replaceState({},"",N)}},variant:"ghost",class:"h-8 px-2 lg:px-3",children:(B,re)=>{H();var N=Zl(),R=v(M(N));zt(R,{class:"ml-2 h-4 w-4"}),r(B,N)},$$slots:{default:!0}})};E(X,k=>{a(u)&&k(Q)})}C(l);var le=v(l,2);{var se=k=>{Fl(k,{get tableModel(){return e.tableModel}})};E(le,k=>{e.tableModel&&k(se)})}C(p);var ge=v(p,2),de=y(ge);Tt(de,{title:"Type",get options(){return c},get filterValues(){return n()},get counts(){return a(g)},onFilterChange:k=>{f()&&f()(k)}});var G=v(de,2);Tt(G,{title:"Source",get options(){return d},get filterValues(){return m()},get counts(){return a(x)},onFilterChange:k=>{_()&&_()(k)}});var A=v(G,2),S=y(A);{var j=k=>{var B=Yl(),re=M(B),N=v(y(re));{var R=q=>{var I=ql(),Z=v(M(I));J(K=>$(Z,` ${K??""}`),[()=>n().join(", ")]),r(q,I)};E(N,q=>{n().length>0&&q(R)})}var b=v(N,2);{var F=q=>{var I=Gl(),Z=v(M(I));J(K=>$(Z,` ${K??""}`),[()=>m().join(", ")]),r(q,I)};E(b,q=>{m().length>0&&q(F)})}C(re);var V=v(re,2);Te(V,{variant:"ghost",size:"sm",class:"h-6 px-2 text-xs",onclick:()=>{f()&&f()([]),_()&&_()([])},children:(q,I)=>{H();var Z=$l(),K=v(M(Z));zt(K,{class:"ml-1 h-3 w-3"}),r(q,Z)},$$slots:{default:!0}}),r(k,B)};E(S,k=>{(n().length>0||m().length>0)&&k(j)})}C(A),C(ge),C(D),r(s,D),Fe()}var Jl=L('<span class="ml-2 rounded bg-blue-100 px-2 py-0.5 text-xs text-blue-800">Default</span>'),Xl=L('<span class="ml-2 rounded bg-amber-100 px-2 py-0.5 text-xs text-amber-800">Draft</span>'),Ql=L("<span> </span>"),en=L('<div class="flex w-[250px] items-center"><!> <span class="max-w-[500px] truncate font-medium"> </span> <!> <!></div>');function tn(s,e){Me(e,!0);function o(u){var p;if(!u)return ct;switch((p=u.split(".").pop())==null?void 0:p.toLowerCase()){case"pdf":return ct;case"xlsx":case"xls":case"csv":return yr;case"doc":case"docx":return wr;default:return ct}}const h=o(e.document.fileUrl);var n=en(),m=y(n);{var w=u=>{h(u,{class:"mr-2 h-4 w-4"})};E(m,u=>{h&&u(w)})}var f=v(m,2),_=y(f,!0);C(f);var c=v(f,2);{var d=u=>{var D=Jl();r(u,D)};E(c,u=>{e.document.isDefault&&u(d)})}var g=v(c,2);{var x=u=>{var D=Xl();r(u,D)},i=(u,D)=>{{var p=l=>{var t=Ql(),P=y(t,!0);C(t),J(()=>{er(t,1,`ml-2 rounded px-2 py-0.5 text-xs ${e.document.source==="generated"?"bg-purple-100 text-purple-800":e.document.source==="created"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`),$(P,e.document.source==="generated"?"Generated":e.document.source==="created"?"Created":"Uploaded")}),r(l,t)};E(u,l=>{e.document.source&&l(p)},D)}};E(g,u=>{(!e.document.fileUrl||e.document.fileUrl==="/placeholder.pdf")&&e.document.source==="created"?u(x):u(i,!1)})}C(n),J(()=>$(_,e.document.label)),r(s,n),Fe()}var rn=L("<!> <!>",1),an=L('<div class="w-full space-y-4"><!> <div class="border-border w-full rounded-md border"><!></div> <!></div>'),on=L('<div class="flex h-24 items-center justify-center"><p>Loading...</p></div>');function ln(s,e){Me(e,!0);const o=z(e,"data",19,()=>[]),h=z(e,"searchTerm",3,""),n=z(e,"typeFilter",19,()=>[]),m=z(e,"sourceFilter",19,()=>[]),w=z(e,"pagination",19,()=>({pageIndex:0,pageSize:10}));let f=Se(Ne([{id:"createdAt",desc:!0}])),_=Se(Ne([])),c=Se(Ne(n())),d=Se(Ne(m())),g=Se(Ne({})),x=Se(Ne(w())),i=Se(null),u=Se(Ne([])),D=Se(Ne({document:!0,type:!0,createdAt:!0,updatedAt:!0,select:!0,actions:!0}));function p(R,b){if(!b||b.trim()==="")return R;const F=b.toLowerCase().trim();return R.filter(V=>!!(V.label&&V.label.toLowerCase().includes(F)||V.type&&V.type.toLowerCase().includes(F)||V.content&&V.content.toLowerCase().includes(F)))}function l(){const R=[];if(a(c)&&a(c).length>0&&R.push({id:"type",value:a(c)}),a(d)&&a(d).length>0&&R.push({id:"source",value:a(d)}),O(_,R,!0),a(i)){const b=a(i).getColumn("document");b&&b.columnDef&&(b.columnDef.meta||(b.columnDef.meta={}),b.columnDef.meta={...b.columnDef.meta,sourceFilter:a(d)||[]})}}function t(R){console.log("Document deleted:",R);const b=new CustomEvent("documentDeleted",{detail:R});document.dispatchEvent(b)}function P(R){const{data:b,columns:F,state:V,onSortingChange:q,onColumnFiltersChange:I,onRowSelectionChange:Z,onPaginationChange:K,onColumnVisibilityChange:ae,getCoreRowModel:ve,getFilteredRowModel:_e,getPaginationRowModel:xe,getSortedRowModel:Ce,enableRowSelection:Y}=R;return{data:b,columns:F,state:V,onSortingChange:q,onColumnFiltersChange:I,onRowSelectionChange:Z,onPaginationChange:K,onColumnVisibilityChange:ae,getCoreRowModel:ve,getFilteredRowModel:_e,getPaginationRowModel:xe,getSortedRowModel:Ce,enableRowSelection:Y,getHeaderGroups:()=>[{headers:F.map(T=>({column:{columnDef:T,id:T.id,getContext:()=>({})},colSpan:1,isPlaceholder:!1,id:T.id,getContext:()=>({})}))}],getRowModel:()=>({rows:(Array.isArray(b)?b:[]).map((ne,pe)=>({id:ne.id||`row-${pe}`,original:ne,getVisibleCells:()=>F.map(W=>({column:{columnDef:W,id:W.id},getContext:()=>({row:{original:ne,id:ne.id,getValue:ie=>ne[ie]}})}))}))}),getAllColumns:()=>F,getColumn:T=>F.find(ne=>ne.id===T),getState:()=>V,setState:T=>{Object.assign(V,T),T.sorting&&q&&q(T.sorting),T.columnFilters&&I&&I(T.columnFilters),T.rowSelection&&Z&&Z(T.rowSelection),T.pagination&&K&&K(T.pagination),T.columnVisibility&&ae&&ae(T.columnVisibility)},setColumnVisibility:T=>{ae&&ae(T)},getIsAllPageRowsSelected:()=>{const T=Array.isArray(b)?b:[];return T.length>0&&T.every(ne=>V.rowSelection[ne.id])},getIsSomePageRowsSelected:()=>(Array.isArray(b)?b:[]).some(ne=>V.rowSelection[ne.id]),toggleAllPageRowsSelected:T=>{const ne={...V.rowSelection};(Array.isArray(b)?b:[]).forEach(W=>{T?ne[W.id]=!0:delete ne[W.id]}),Z(ne)}}}function X(){{const R=Array.isArray(a(u))?a(u):[];console.log("Initializing table with data:",R.length,"items"),O(i,P({data:R,columns:[{id:"select",header:()=>st(At,{checked:a(u).length>0&&a(u).every(b=>a(g)[b.id]),indeterminate:a(u).some(b=>a(g)[b.id])&&!(a(u).length>0&&a(u).every(b=>a(g)[b.id])),onCheckedChange:b=>{ge(!!b)},"aria-label":"Select all"}),cell:({row:b})=>st(At,{checked:a(g)[b.original.id]||!1,onCheckedChange:F=>{se(b.original.id,!!F)},"aria-label":"Select row",class:"translate-y-[2px]"}),enableSorting:!1,width:"15px"},{accessorKey:"label",header:"Document Name",id:"document",enableSorting:!0,width:"60%",minWidth:"200px",meta:{sourceFilter:a(d)||[]},cell:({row:b})=>st(tn,{document:b.original})},{accessorKey:"type",header:"Type",id:"type",enableSorting:!0,width:"120px",cell:({row:b})=>b.getValue("type")},{accessorKey:"createdAt",header:"Created",id:"createdAt",enableSorting:!0,sortingFn:"datetime",sortDescFirst:!0,width:"190px",cell:({row:b})=>{const F=b.getValue("createdAt");if(!F)return"N/A";const V=new Date(F);return isNaN(V.getTime())?"Invalid date":new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(V)}},{accessorKey:"updatedAt",header:"Edited",id:"updatedAt",enableSorting:!0,sortingFn:"datetime",sortDescFirst:!0,width:"190px",cell:({row:b})=>{const F=b.getValue("updatedAt");if(!F)return"N/A";const V=new Date(F);return isNaN(V.getTime())?"Invalid date":new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(V)}},{id:"actions",width:"10%",minWidth:"60px",cell:({row:b})=>st(fl,{row:b.original,on_deleted:F=>t(F)})}],state:{sorting:a(f),columnFilters:a(_),rowSelection:a(g),pagination:a(x),columnVisibility:a(D),globalFilter:""},onSortingChange:b=>{O(f,typeof b=="function"?b(a(f)):b,!0)},onColumnFiltersChange:b=>{O(_,typeof b=="function"?b(a(_)):b,!0)},onRowSelectionChange:b=>{O(g,typeof b=="function"?b(a(g)):b,!0)},onPaginationChange:b=>{O(x,typeof b=="function"?b(a(x)):b,!0)},onColumnVisibilityChange:b=>{O(D,typeof b=="function"?b(a(D)):b,!0)},getCoreRowModel:qa(),getFilteredRowModel:Wa(),getPaginationRowModel:Ja(),getSortedRowModel:Xa(),enableRowSelection:!0}),!0)}}function Q(){return(Array.isArray(o())?o():[]).map(F=>({...F,id:F.id??`temp-${Math.random().toString(36).substring(2,9)}`,label:F.label??"Untitled Document",type:F.type??"document",createdAt:F.createdAt??new Date().toISOString(),updatedAt:F.updatedAt??F.createdAt??new Date().toISOString(),source:F.source??"uploaded"}))}function le(){return Object.keys(a(g)).length}function se(R,b){const F={...a(g)};b!==void 0?b?F[R]=!0:delete F[R]:F[R]?delete F[R]:F[R]=!0,O(g,F,!0)}function ge(R){const b={...a(g)};(Array.isArray(a(u))?a(u):[]).forEach(V=>{R?b[V.id]=!0:delete b[V.id]}),O(g,b,!0)}function de(R){dispatchEvent(new CustomEvent("searchChange",{detail:R})),A()}function G(R){O(x,R,!0),dispatchEvent(new CustomEvent("paginationChange",{detail:R}))}function A(){let R=p(o(),h());R=(F=>a(_).length?F.filter(V=>a(_).every(q=>{const I=V[q.id];return Array.isArray(q.value)?q.value.length===0||q.value.includes(I):!0})):F)(R),O(u,R,!0),a(i)?a(i).setState&&(a(i).data=R):X()}function S(R){O(c,R,!0),dispatchEvent(new CustomEvent("typeFilterChange",{detail:R})),A()}function j(R){O(d,R,!0),dispatchEvent(new CustomEvent("sourceFilterChange",{detail:R})),A()}O(u,p(o(),h()),!0),X(),l();var k=ce(),B=M(k);{var re=R=>{var b=an(),F=y(b);const V=me(Q);Wl(F,{get tableModel(){return a(i)},get data(){return a(V)},get searchTerm(){return h()},get typeFilter(){return a(c)},get sourceFilter(){return a(d)},onSearchChange:ae=>de(ae),onTypeFilterChange:ae=>S(ae),onSourceFilterChange:ae=>j(ae)});var q=v(F,2),I=y(q);U(I,()=>ia,(ae,ve)=>{ve(ae,{class:"w-full",children:(_e,xe)=>{var Ce=rn(),Y=M(Ce);U(Y,()=>oa,(T,ne)=>{ne(T,{children:(pe,W)=>{var ie=ce(),te=M(ie);$e(te,17,()=>a(i).getHeaderGroups(),Ye,(he,ue)=>{var fe=ce(),Re=M(fe);U(Re,()=>xt,(Le,Ae)=>{Ae(Le,{children:(we,Ie)=>{var Pe=ce(),ze=M(Pe);$e(ze,17,()=>a(ue).headers,Ye,(ke,De)=>{var Oe=ce(),je=M(Oe);const Ee=me(()=>a(De).column.columnDef.width?`width: ${a(De).column.columnDef.width}; min-width: ${a(De).column.columnDef.minWidth||"50px"};`:"");U(je,()=>la,(Ze,Ke)=>{Ke(Ze,{get colSpan(){return a(De).colSpan},get style(){return a(Ee)},children:(Ue,Ge)=>{var nt=ce(),mt=M(nt);{var gt=tt=>{const _t=me(()=>a(De).getContext?a(De).getContext():{});Vt(tt,{get content(){return a(De).column.columnDef.header},get context(){return a(_t)}})};E(mt,tt=>{a(De).isPlaceholder||tt(gt)})}r(Ue,nt)},$$slots:{default:!0}})}),r(ke,Oe)}),r(we,Pe)},$$slots:{default:!0}})}),r(he,fe)}),r(pe,ie)},$$slots:{default:!0}})});var ee=v(Y,2);U(ee,()=>na,(T,ne)=>{ne(T,{children:(pe,W)=>{var ie=ce(),te=M(ie);{var he=fe=>{var Re=ce(),Le=M(Re);$e(Le,17,()=>a(i).getRowModel().rows,Ye,(Ae,we)=>{var Ie=ce(),Pe=M(Ie);const ze=me(()=>a(g)[a(we).id]?"bg-muted":"");U(Pe,()=>xt,(ke,De)=>{De(ke,{get class(){return a(ze)},children:(Oe,je)=>{var Ee=ce(),Ze=M(Ee);$e(Ze,17,()=>a(we).getVisibleCells(),Ye,(Ke,Ue)=>{var Ge=ce(),nt=M(Ge);const mt=me(()=>a(Ue).column.columnDef.width?`width: ${a(Ue).column.columnDef.width}; min-width: ${a(Ue).column.columnDef.minWidth||"50px"};`:"");U(nt,()=>kt,(gt,tt)=>{tt(gt,{get style(){return a(mt)},children:(_t,cn)=>{const ur=me(()=>a(Ue).getContext());Vt(_t,{get content(){return a(Ue).column.columnDef.cell},get context(){return a(ur)}})},$$slots:{default:!0}})}),r(Ke,Ge)}),r(Oe,Ee)},$$slots:{default:!0}})}),r(Ae,Ie)}),r(fe,Re)},ue=fe=>{var Re=ce(),Le=M(Re);U(Le,()=>xt,(Ae,we)=>{we(Ae,{children:(Ie,Pe)=>{var ze=ce(),ke=M(ze);U(ke,()=>kt,(De,Oe)=>{Oe(De,{get colspan(){return a(i).getAllColumns().length},class:"h-24 text-center",children:(je,Ee)=>{H();var Ze=oe("No results.");r(je,Ze)},$$slots:{default:!0}})}),r(Ie,ze)},$$slots:{default:!0}})}),r(fe,Re)};E(te,fe=>{a(i).getRowModel().rows.length?fe(he):fe(ue,!1)})}r(pe,ie)},$$slots:{default:!0}})}),r(_e,Ce)},$$slots:{default:!0}})}),C(q);var Z=v(q,2);const K=me(le);yl(Z,{get tableModel(){return a(i)},get selectedRowCount(){return a(K)},get totalRowCount(){return a(u).length},onPaginationChange:G}),C(b),r(R,b)},N=R=>{var b=on();r(R,b)};E(B,R=>{a(i)?R(re):R(N,!1)})}r(s,k),Fe()}var nn=L('<div class="flex h-64 w-full items-center justify-center"><div class="text-center"><p class="text-muted-foreground mb-4">No documents found</p> <div class="flex justify-center space-x-2"><!> <!></div></div></div>'),sn=L('<div class="flex h-64 w-full items-center justify-center"><p class="text-muted-foreground">Loading documents...</p></div>'),dn=L('<!> <div class="flex h-full flex-col"><div class="flex items-center justify-between px-6 py-4"><div><h1 class="text-2xl font-bold">Documents</h1> <p class="text-muted-foreground text-sm">Manage your resumes, cover letters, and other documents</p></div> <div class="flex space-x-2"><!> <!></div></div> <div class="border-border w-full flex-1 border"><!></div></div> <!> <!>',1);function gi(s,e){Me(e,!0);let o=Se(Ne([])),h=Se(Ne([])),n=Se(!1),m=Se(!1),w="resume",f=Se(!1),_=Se(""),c=Se(Ne([])),d=Se(Ne([])),g=Se(Ne({pageIndex:0,pageSize:10}));function x(){const A=new URL(window.location.href),j=new URLSearchParams(A.search).get("search");if(j)try{O(_,decodeURIComponent(j),!0),console.log("Initialized search term from URL:",a(_))}catch(k){console.error("Error decoding search parameter:",k),O(_,j,!0)}}St(()=>{if(a(f))return;x(),console.log("Page onMount - data:",e.data),e.data&&(O(h,e.data.profiles||[],!0),console.log("Profiles:",a(h)),console.log("Documents from server:",e.data.documents),O(o,(e.data.documents||[]).map(S=>{var j,k;return console.log("Processing document:",S),{...S,type:S.type||"resume",isDefault:S.isDefault??!1,fileName:((j=S.fileUrl)==null?void 0:j.split("/").pop())??"unknown.pdf",id:S.id??`temp-${Math.random().toString(36).substring(2,9)}`,label:S.label??"Untitled Document",createdAt:S.createdAt?new Date(S.createdAt).toISOString():new Date().toISOString(),updatedAt:S.updatedAt?new Date(S.updatedAt).toISOString():new Date().toISOString(),parsedAt:S.parsedAt??null,isParsed:S.isParsed??!1,fileUrl:S.fileUrl??"",source:S.source||"uploaded",score:S.score??null,profile:((k=S.profile)==null?void 0:k.id)??null,jobSearch:S.jobSearch?JSON.stringify(S.jobSearch):null}}),!0),console.log("Processed documents:",a(o)),O(f,!0));const A=()=>{x()};return window.addEventListener("popstate",A),()=>{window.removeEventListener("popstate",A)}});function i(A){var k,B;console.log("Document uploaded event received:",A.detail);const S=A.detail,j={...S,id:S.id??`temp-${Math.random().toString(36).substring(2,9)}`,type:S.type||w,label:S.label??"Untitled Document",createdAt:S.createdAt?new Date(S.createdAt).toISOString():new Date().toISOString(),updatedAt:S.updatedAt?new Date(S.updatedAt).toISOString():new Date().toISOString(),source:S.source||"uploaded",isDefault:S.isDefault??!1,isParsed:S.isParsed??!1,parsedAt:S.parsedAt??null,fileName:S.fileName??((k=S.fileUrl)==null?void 0:k.split("/").pop())??"unknown.pdf",fileUrl:S.fileUrl??"",score:S.score??null,profile:((B=S.profile)==null?void 0:B.id)??null,jobSearch:S.jobSearch?JSON.stringify(S.jobSearch):null};console.log("Processed document to add:",j),O(o,[j,...a(o)],!0),ye.success("Document uploaded",{description:"Your document has been uploaded successfully."}),O(n,!1)}St(()=>{{const A=S=>{const j=S.detail;O(o,a(o).filter(k=>k.id!==j),!0),ye.success("Document deleted",{description:"The document has been deleted successfully."})};return document.addEventListener("documentDeleted",A),()=>{document.removeEventListener("documentDeleted",A)}}});var u=dn(),D=M(u);Sr(D,{title:"Documents | Hirli",description:"Manage your resumes, cover letters, and other professional documents. Upload, create, and organize your career documents.",keywords:"resume management, document management, cover letters, professional documents, career documents, job application documents"});var p=v(D,2),l=y(p),t=v(y(l),2),P=y(t);Te(P,{variant:"outline",onclick:()=>O(n,!0),children:(A,S)=>{H();var j=oe("Upload Document");r(A,j)},$$slots:{default:!0}});var X=v(P,2);Te(X,{variant:"default",onclick:()=>O(m,!0),children:(A,S)=>{H();var j=oe("Create Resume");r(A,j)},$$slots:{default:!0}}),C(t),C(l);var Q=v(l,2),le=y(Q);{var se=A=>{ln(A,{get data(){return a(o)},get searchTerm(){return a(_)},get typeFilter(){return a(c)},get sourceFilter(){return a(d)},get pagination(){return a(g)},$$events:{searchChange:S=>O(_,S.detail,!0),typeFilterChange:S=>O(c,S.detail,!0),sourceFilterChange:S=>O(d,S.detail,!0),paginationChange:S=>O(g,S.detail,!0)}})},ge=(A,S)=>{{var j=B=>{var re=nn(),N=y(re),R=v(y(N),2),b=y(R);Te(b,{variant:"outline",onclick:()=>O(n,!0),children:(V,q)=>{H();var I=oe("Upload Document");r(V,I)},$$slots:{default:!0}});var F=v(b,2);Te(F,{variant:"default",onclick:()=>O(m,!0),children:(V,q)=>{H();var I=oe("Create Resume");r(V,I)},$$slots:{default:!0}}),C(R),C(N),C(re),r(B,re)},k=B=>{var re=sn();r(B,re)};E(A,B=>{a(f)?B(j):B(k,!1)},S)}};E(le,A=>{a(o)&&a(o).length>0?A(se):A(ge,!1)})}C(Q),C(p);var de=v(p,2);Sa(de,{get profiles(){return a(h)},get open(){return a(n)},set open(A){O(n,A,!0)},$$events:{uploaded:i}});var G=v(de,2);Oa(G,{get profiles(){return a(h)},get open(){return a(m)},set open(A){O(m,A,!0)}}),r(s,u),Fe()}export{gi as component};
