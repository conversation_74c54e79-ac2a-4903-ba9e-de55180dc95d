import{d as n}from"./sanity-DV0NwVOn.js";const i=n("canvas",{"action.link-document":"Link to Canvas","action.link-document-disabled.not-in-dashboard":"Open this document in Dashboard to link to Canvas","action.link-document-disabled.missing-permissions":"You don't have permissions to link this document to Canvas","action.link-document-disabled.version-document":"Version documents are not yet supported in Canvas","action.link-document-disabled.initial-value-not-resolved":"Please wait until the document initial values are resolved","action.unlink-document":"Unlink from Canvas","action.edit-document":"Edit in Canvas","banner.linked-to-canvas":"This document is linked to Canvas","banner.edit-document-in-canvas":"Edit in Canvas","dialog.confirm-document-changes.title":"Confirm document changes","dialog.confirm-document-changes.description":`This document needs to be updated to be compatible with Canvas.
 Existing content may be edited or removed as part of this process.`,"dialog.confirm-document-changes.confirm":"Accept and continue","dialog.confirm-document-changes.cancel":"Cancel","dialog.confirm-document-changes.footer-description":"You can unlink from Canvas at any time","dialog.link-to-canvas.title":"Link to Canvas","dialog.link-to-canvas.validating":"Validating","dialog.link-to-canvas.redirecting":"Taking you to Canvas to complete linking...","dialog.link-to-canvas.error":"Failed to link to Canvas","dialog.unlink-from-canvas.title":"Unlink from Canvas","dialog.unlink-from-canvas.unlinking":"You're unlinking  <strong>“{{documentTitle}}”</strong> from Canvas.","dialog.unlink-from-canvas.cancel":"Cancel","dialog.unlink-from-canvas.unlink-action":"Unlink now","dialog.unlink-from-canvas.description":"Once unlinked, it will be editable here and future edits in Canvas will no longer automatically map to this document.","dialog.unlink-from-canvas.success":"Unlinked from Canvas","dialog.unlink-from-canvas.error":"Failed to unlink from Canvas","navigate-to-canvas-doc.error.missing-permissions":"Missing permissions to navigate to Canvas"});export{i as default};
