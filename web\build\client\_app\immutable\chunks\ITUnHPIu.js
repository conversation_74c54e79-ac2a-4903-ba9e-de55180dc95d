import{c as h,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as n}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as i,s as c}from"./Btcx8l8F.js";import{I as m}from"./D4f2twK-.js";function g(o,t){const r=i(t,["children","$$slots","$$events","$$legacy"]),e=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18"}],["path",{d:"M16 10h.01"}],["path",{d:"M12 10h.01"}],["path",{d:"M8 10h.01"}],["path",{d:"M12 14h.01"}],["path",{d:"M8 14h.01"}],["path",{d:"M12 18h.01"}],["path",{d:"M8 18h.01"}]];m(o,c({name:"calculator"},()=>r,{get iconNode(){return e},children:(s,f)=>{var a=h(),p=n(a);d(p,t,"default",{},null),l(s,a)},$$slots:{default:!0}}))}export{g as C};
