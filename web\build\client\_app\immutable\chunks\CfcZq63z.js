import{f as A,a as B}from"./BasJTneF.js";import{o as G,a as H}from"./nZgk9enP.js";import{p as J,c as w,k as x,t as Q,a as W,s as E,g as l,d as C,r as S,x as N}from"./CGmarHxI.js";import{g as X}from"./CmxjS0TN.js";import{i as P}from"./u21ee2wt.js";import{c as Y,a as b,f as Z,s as $,r as ee}from"./B-Xjo-Yt.js";import{b as te}from"./5V1tIHTN.js";import{p as n}from"./Btcx8l8F.js";import{b as U}from"./Cf6rS4LV.js";import{c as I}from"./ncUU1dSD.js";import{S as k}from"./yW0TxTga.js";function ae(c,t){const a=c.target;t(a.value),console.log(`SearchInput: Input changed to "${t()}"`)}function oe(c,t,a,h,u){c.key==="Enter"&&(c.preventDefault(),console.log(`SearchInput: Enter key pressed with value "${t()}"`),l(a)?(h(),u()&&(console.log(`SearchInput: Calling onSearch with "${t()}" (Enter key)`),u()(t()))):console.log(`SearchInput: Not updating URL - browser: ${U}, initialized: ${l(a)}`))}var re=A('<div class="w-full"><div class="relative"><!> <div><input type="text"/></div> <!></div></div>');function ge(c,t){J(t,!0);let a=n(t,"value",15,""),h=n(t,"placeholder",3,"Search..."),u=n(t,"className",3,""),o=n(t,"paramName",3,"title"),z=n(t,"onSearch",3,void 0),T=n(t,"disabled",3,!1),d=n(t,"iconPosition",3,"left"),y=n(t,"iconClass",3,""),D=n(t,"inputClass",3,""),j=n(t,"autofocus",3,!1),g=x(null),p=null,v=x(!1);function _(){if(!o())return;const e=new URL(window.location.href),r=new URLSearchParams(e.search);a()?(console.log(`SearchInput: Setting URL param ${o()} to "${a()}"`),r.set(o(),encodeURIComponent(a()))):(r.delete(o()),console.log(`SearchInput: Removing URL param ${o()} (empty value)`));const i=`${e.pathname}?${r.toString()}`;console.log(`SearchInput: New URL: ${i}`),window.history.replaceState({},"",i),window.dispatchEvent(new CustomEvent("url-updated",{detail:{paramName:o(),value:a(),allParams:Object.fromEntries(r.entries())}}))}function q(){if(!o())return;const e=new URL(window.location.href),i=new URLSearchParams(e.search).get(o());if(i)try{a(decodeURIComponent(i)),console.log(`SearchInput: Initialized ${o()} from URL: "${a()}"`),console.log("SearchInput: Value initialized from URL, waiting for user to press Enter to search")}catch(V){console.error(`SearchInput: Error decoding URL parameter ${o()}:`,V),a(i)}else console.log(`SearchInput: No ${o()} parameter found in URL`)}function F(){if(!(!j()||!p))try{const e=p.querySelector("input");e&&typeof e.focus=="function"&&e.focus()}catch(e){console.error("Error focusing input element:",e)}}G(()=>{console.log(`SearchInput: Component mounted for ${o()}`),q(),F(),setTimeout(()=>{C(v,!0),console.log(`SearchInput: Component initialized for ${o()}`),a()&&U&&(console.log(`SearchInput: Updating URL with initial value: "${a()}"`),_())},100);const e=r=>{const i=r.detail;i.paramName!==o()&&U&&l(v)&&console.log(`SearchInput: URL updated by another component: ${i.paramName}`)};return window.addEventListener("url-updated",e),()=>{window.removeEventListener("url-updated",e)}}),H(()=>{l(g)&&(clearTimeout(l(g)),C(g,null))});var m=re(),R=w(m),L=w(R);{var K=e=>{const r=N(()=>I("text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2",y()));k(e,{get class(){return l(r)}})};P(L,e=>{d()==="left"&&e(K)})}var f=E(L,2),s=w(f);ee(s),s.__input=[ae,a],s.__keydown=[oe,a,v,_,z],S(f),te(f,e=>p=e,()=>p);var M=E(f,2);{var O=e=>{const r=N(()=>I("text-muted-foreground absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2",y()));k(e,{get class(){return l(r)}})};P(M,e=>{d()==="right"&&e(O)})}S(R),S(m),Q(e=>{b(m,"data-param-name",o()),b(s,"placeholder",h()),Z(s,a()),s.disabled=T(),$(s,1,e),b(s,"data-search-input",o())},[()=>Y(I("border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",u(),d()==="left"?"pl-9":"",d()==="right"?"pr-9":"",D()))]),B(c,m),W()}X(["input","keydown"]);export{ge as S};
