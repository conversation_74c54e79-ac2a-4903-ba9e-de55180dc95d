import{f as _,a as u,c as ce,t as me}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as le,c as r,r as a,s as d,g as x,x as de,t as U,a as ue,f as G,l as be,d as V,m as ye,h as we,b as De,e as ee,n as re,k as oe,v as ve}from"../chunks/CGmarHxI.js";import{p as Q}from"../chunks/Btcx8l8F.js";import{S as Ce}from"../chunks/C6g8ubaU.js";import{o as Se,a as Re}from"../chunks/nZgk9enP.js";import{s as S}from"../chunks/CIt1g2O9.js";import{e as pe,i as ge}from"../chunks/C3w0v0gR.js";import{c as O}from"../chunks/BvdI7LR8.js";import{C as he}from"../chunks/DuGukytH.js";import{C as _e}from"../chunks/Cdn-N1RY.js";import{C as ke}from"../chunks/BkJY4La4.js";import{C as je}from"../chunks/GwmmX_iF.js";import{C as Te}from"../chunks/D50jIuLr.js";import{t as Me}from"../chunks/DjPYYl4Z.js";import{i as L}from"../chunks/u21ee2wt.js";import{b as xe}from"../chunks/B-Xjo-Yt.js";import{i as Ae}from"../chunks/BIEMS98f.js";import{B as Ue}from"../chunks/DaBofrVv.js";import{B as Fe,a as Pe,C as qe}from"../chunks/XnZcpgwi.js";import{T as fe}from"../chunks/CTO_B1Jk.js";import{C as He}from"../chunks/-SpbofVw.js";import{C as Ee}from"../chunks/BAIxhb6t.js";import{C as Ie}from"../chunks/DW7T7T22.js";import{A as ze}from"../chunks/BPr9JIwg.js";import{M as Je}from"../chunks/CcFQTcQh.js";import{H as $e}from"../chunks/CqJi5rQC.js";var Be=_('<div class="flex items-center justify-center"><div class="text-muted-foreground text-sm">No data available</div></div>'),We=_('<div class="text-muted-foreground mt-1 flex justify-between text-xs"><span> </span> <span> </span></div>'),Le=_("<!> <!>",1),Oe=_('<div class="w-full"><h4 class="mb-2 text-sm font-medium"> </h4> <!></div>');function Ve(I,n){le(n,!0);let f=Q(n,"historyData",19,()=>[]),R=Q(n,"metric",3,"successRate"),b=Q(n,"title",3,"Last 30 Days"),k=Q(n,"height",3,100);const y=de(()=>()=>{if(!f()||f().length===0){const l=[],D=new Date;for(let A=29;A>=0;A--){const $=new Date;$.setDate(D.getDate()-A);const q=Math.random();let p="operational";q>.9?p="outage":q>.8?p="degraded":q>.7&&(p="maintenance"),l.push({date:c($.toISOString()),status:p,value:p==="operational"?100:p==="degraded"?80:p==="maintenance"?60:0,statusColor:F(p)})}return l}return f().map(l=>({date:c(l.date),status:l.status,value:l[R()]||0,statusColor:F(l.status)}))});function c(l){return new Date(l).toLocaleDateString("en-US",{month:"short",day:"numeric"})}function F(l){switch(l){case"operational":return"var(--chart-3)";case"degraded":return"var(--chart-4)";case"outage":return"var(--chart-5)";case"maintenance":return"var(--chart-2)";default:return"hsl(var(--muted-foreground))"}}const K={operational:{label:"Operational",color:"var(--chart-3)"},degraded:{label:"Degraded",color:"var(--chart-4)"},outage:{label:"Outage",color:"var(--chart-5)"},maintenance:{label:"Maintenance",color:"var(--chart-2)"}};var z=Oe(),P=r(z),Y=r(P,!0);a(P);var J=d(P,2);{var T=l=>{var D=Be();U(()=>xe(D,`height: ${k()??""}px;`)),u(l,D)},M=l=>{var D=Le(),A=G(D);O(A,()=>qe,(p,H)=>{H(p,{get config(){return K},class:"w-full",get style(){return`height: ${k()??""}px;`},children:(s,o)=>{const e=de(()=>x(y)());Fe(s,{get data(){return x(e)},x:"date",axis:"x",series:[{key:"value",label:"Status",color:"var(--chart-1)"}],props:{xAxis:{format:t=>t.slice(0,3)}},tooltip:t=>{var i=ce(),w=G(i);O(w,()=>Pe,(j,m)=>{m(j,{})}),u(t,i)},$$slots:{tooltip:!0}})},$$slots:{default:!0}})});var $=d(A,2);{var q=p=>{var H=We(),s=r(H),o=r(s,!0);a(s);var e=d(s,2),g=r(e,!0);a(e),a(H),U((t,i)=>{S(o,t),S(g,i)},[()=>c(f()[0].date),()=>c(f()[f().length-1].date)]),u(p,H)};L($,p=>{f()&&f().length>0&&p(q)})}u(l,D)};L(J,l=>{x(y).length===0?l(T):l(M,!1)})}a(z),U(()=>S(Y,b())),u(I,z),ue()}var Qe=_("<!> ",1),Ge=_('<p class="text-muted-foreground mb-3 text-sm"> </p>'),Ke=_('<div class="flex justify-between"><span class="text-muted-foreground">Response Time:</span> <span class="font-medium"> </span></div>'),Xe=_('<div class="flex justify-between"><span class="text-muted-foreground">Success Rate:</span> <span class="font-medium"> </span></div>'),Ye=_('<div class="flex justify-between"><span class="text-muted-foreground">Error Rate:</span> <span class="font-medium"> </span></div>'),Ze=_('<div class="flex justify-between"><span class="text-muted-foreground">Requests:</span> <span class="font-medium"> </span></div>'),Ne=_('<div class="flex justify-between"><span class="text-muted-foreground">Queue Size:</span> <span class="font-medium"> </span></div>'),et=_('<div class="flex justify-between"><span class="text-muted-foreground">Processing:</span> <span class="font-medium"> </span></div>'),tt=_('<div class="grid grid-cols-2 gap-2 text-xs"><!> <!> <!> <!> <!> <!></div>'),at=_('<div class="rounded-lg border p-4"><div class="mb-3 flex items-center justify-between"><div class="flex items-center gap-3"><div class="h-3 w-3 rounded-full"></div> <h3 class="font-medium"> </h3></div> <!></div> <!> <div class="mb-4"><!></div> <!></div>');function st(I,n){le(n,!1);const f=ye();let R=Q(n,"name",8),b=Q(n,"status",8),k=Q(n,"description",8,""),y=Q(n,"historyData",24,()=>[]),c=Q(n,"metrics",24,()=>({}));function F(t){switch(t){case"operational":return Ie;case"degraded":return fe;case"outage":return Ee;case"maintenance":return He;default:return fe}}function K(t){switch(t){case"operational":return"#4CAF50";case"degraded":return"#FFC107";case"outage":return"#F44336";case"maintenance":return"#2196F3";default:return"#9E9E9E"}}function z(t){switch(t){case"operational":return"success";case"degraded":return"warning";case"outage":return"destructive";case"maintenance":return"secondary";default:return"outline"}}function P(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function Y(t){return t>=1e3?`${(t/1e3).toFixed(1)}s`:`${t}ms`}be(()=>we(b()),()=>{V(f,K(b()))}),De(),Ae();var J=at(),T=r(J),M=r(T),l=r(M),D=d(l,2),A=r(D,!0);a(D),a(M);var $=d(M,2);const q=ee(()=>z(b()));Ue($,{get variant(){return x(q)},children:(t,i)=>{var w=Qe(),j=G(w);O(j,()=>F(b()),(E,ne)=>{ne(E,{class:"mr-1 h-3 w-3"})});var m=d(j);U(E=>S(m,` ${E??""}`),[()=>b().charAt(0).toUpperCase()+b().slice(1)],ee),u(t,w)},$$slots:{default:!0}}),a(T);var p=d(T,2);{var H=t=>{var i=Ge(),w=r(i,!0);a(i),U(()=>S(w,k())),u(t,i)};L(p,t=>{k()&&t(H)})}var s=d(p,2),o=r(s);Ve(o,{get historyData(){return y()},metric:"successRate",title:"Last 30 Days",height:80}),a(s);var e=d(s,2);{var g=t=>{var i=tt(),w=r(i);{var j=v=>{var h=Ke(),C=d(r(h),2),B=r(C,!0);a(C),a(h),U(W=>S(B,W),[()=>Y(c().responseTime)],ee),u(v,h)};L(w,v=>{c().responseTime!==void 0&&v(j)})}var m=d(w,2);{var E=v=>{var h=Xe(),C=d(r(h),2),B=r(C);a(C),a(h),U(W=>S(B,`${W??""}%`),[()=>c().successRate.toFixed(1)],ee),u(v,h)};L(m,v=>{c().successRate!==void 0&&v(E)})}var ne=d(m,2);{var Z=v=>{var h=Ye(),C=d(r(h),2),B=r(C);a(C),a(h),U(W=>S(B,`${W??""}%`),[()=>c().errorRate.toFixed(1)],ee),u(v,h)};L(ne,v=>{c().errorRate!==void 0&&v(Z)})}var te=d(ne,2);{var X=v=>{var h=Ze(),C=d(r(h),2),B=r(C,!0);a(C),a(h),U(W=>S(B,W),[()=>P(c().requestCount)],ee),u(v,h)};L(te,v=>{c().requestCount!==void 0&&v(X)})}var N=d(te,2);{var ae=v=>{var h=Ne(),C=d(r(h),2),B=r(C,!0);a(C),a(h),U(W=>S(B,W),[()=>P(c().queueSize)],ee),u(v,h)};L(N,v=>{c().queueSize!==void 0&&v(ae)})}var se=d(N,2);{var ie=v=>{var h=et(),C=d(r(h),2),B=r(C,!0);a(C),a(h),U(W=>S(B,W),[()=>P(c().processingCount)],ee),u(v,h)};L(se,v=>{c().processingCount!==void 0&&v(ie)})}a(i),u(t,i)};L(e,t=>{c()&&Object.keys(c()).length>0&&t(g)})}a(J),U(()=>{xe(l,`background-color: ${x(f)??""}`),S(A,R())}),u(I,J),ue()}var rt=_('<div class="grid gap-4 md:grid-cols-4"><div class="flex flex-col items-center justify-center rounded-lg border p-4"><div class="text-2xl font-bold"> </div> <p class="text-muted-foreground text-sm">Uptime (30 days)</p></div> <div class="flex flex-col items-center justify-center rounded-lg border p-4"><div class="text-2xl font-bold"> </div> <p class="text-muted-foreground text-sm">Email Delivery Rate</p></div> <div class="flex flex-col items-center justify-center rounded-lg border p-4"><div class="text-2xl font-bold"> </div> <p class="text-muted-foreground text-sm">API Response Time</p></div> <div class="flex flex-col items-center justify-center rounded-lg border p-4"><div class="text-2xl font-bold"> </div> <p class="text-muted-foreground text-sm">Job Success Rate</p></div></div>');function nt(I,n){le(n,!0);var f=ce(),R=G(f);O(R,()=>he,(b,k)=>{k(b,{children:(y,c)=>{var F=ce(),K=G(F);O(K,()=>_e,(z,P)=>{P(z,{children:(Y,J)=>{var T=rt(),M=r(T),l=r(M),D=r(l);a(l),re(2),a(M);var A=d(M,2),$=r(A),q=r($);a($),re(2),a(A);var p=d(A,2),H=r(p),s=r(H);a(H),re(2),a(p);var o=d(p,2),e=r(o),g=r(e);a(e),re(2),a(o),a(T),U((t,i,w)=>{S(D,`${t??""}%`),S(q,`${i??""}%`),S(s,`${n.metrics.apiResponseTime??""}ms`),S(g,`${w??""}%`)},[()=>n.metrics.uptime.toFixed(2),()=>n.metrics.emailDeliveryRate.toFixed(1),()=>n.metrics.jobSuccessRate.toFixed(1)]),u(Y,T)},$$slots:{default:!0}})}),u(y,F)},$$slots:{default:!0}})}),u(I,f),ue()}var it=_('<div class="rounded-lg border p-6 text-center"><div class="flex items-center justify-center"><div class="mr-2 h-4 w-4 rounded-full bg-green-500"></div> <p class="text-muted-foreground">No notices reported for the past 7 days</p></div></div>'),ot=_('<div class="max-auto container mb-8 px-8"><h2 class="mb-4 text-xl font-semibold">Recent Notices</h2> <!></div>');function dt(I,n){le(n,!0);var f=ot(),R=d(r(f),2);{var b=y=>{var c=ce(),F=G(c);O(F,()=>ze,(K,z)=>{z(K,{type:"multiple",class:"w-full space-y-4",children:(P,Y)=>{var J=ce(),T=G(J);pe(T,17,()=>n.recentIncidents,ge,(M,l,D)=>{Je(M,{get incident(){return x(l)},index:D})}),u(P,J)},$$slots:{default:!0}})}),u(y,c)},k=y=>{var c=it();u(y,c)};L(R,y=>{n.recentIncidents.length>0?y(b):y(k,!1)})}a(f),u(I,f),ue()}var ct=_("<!> <!>",1),lt=_('<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"></div>'),ut=_("<!> <!>",1),vt=_('<div class="flex flex-col gap-4"><div class="border-border flex items-center justify-between border-b p-6"><div class="flex flex-col"><h1 class="text-3xl font-bold">System Status</h1> <p class="text-muted-foreground">Current status of Hirli services</p></div> <div class="flex items-center gap-2 self-end"><p class="text-muted-foreground text-sm"> </p></div></div> <!> <!> <!></div>');function mt(I,n){le(n,!0);let f=oe(ve([])),R=oe(ve({uptime:99.9,emailDeliveryRate:0,apiResponseTime:250,jobSuccessRate:0})),b=oe(ve(new Date)),k=oe(!1),y=oe(ve([]));function c(s){var o,e,g,t,i,w,j;if(s.services&&s.services.length>0)return s.services.map(m=>({name:m.name,status:m.status,description:m.description,lastUpdated:new Date(m.lastCheckedAt||Date.now())}));if(s.serviceHealth){const m=s.serviceHealth;return[{name:"Matches",status:((o=m.api)==null?void 0:o.status)||"operational",description:"Job matching and recommendations",lastUpdated:new Date},{name:"Jobs",status:((e=m.api)==null?void 0:e.status)||"operational",description:"Job search and listings",lastUpdated:new Date},{name:"Tracker",status:((g=m.database)==null?void 0:g.status)||"operational",description:"Application tracking",lastUpdated:new Date},{name:"Documents",status:((t=m.database)==null?void 0:t.status)||"operational",description:"Resume and document management",lastUpdated:new Date},{name:"Automation",status:((i=m.worker)==null?void 0:i.status)||"operational",description:"Automated job application tools",lastUpdated:new Date},{name:"System",status:((w=m.database)==null?void 0:w.status)||"operational",description:"Core system services",lastUpdated:new Date},{name:"Website",status:((j=m.web)==null?void 0:j.status)||"operational",description:"Website and user interface",lastUpdated:new Date}]}else return[{name:"Matches",status:"unknown",description:"Job matching and recommendations"},{name:"Jobs",status:"unknown",description:"Job search and listings"},{name:"Tracker",status:"unknown",description:"Application tracking"},{name:"Documents",status:"unknown",description:"Resume and document management"},{name:"Automation",status:"unknown",description:"Automated job application tools"},{name:"System",status:"unknown",description:"Core system services"},{name:"Website",status:"unknown",description:"Website and user interface"}]}function F(){return n.pageData.maintenance?[...n.pageData.maintenance.inProgress||[],...n.pageData.maintenance.past||[],...n.pageData.maintenance.upcoming||[]].map(o=>{let e;switch(o.status){case"in-progress":e="monitoring";break;case"scheduled":e="identified";break;case"completed":e="resolved";break;case"cancelled":e="resolved";break;default:e="investigating"}return{...o,status:e,severity:o.severity||"info",date:new Date(o.startTime),description:o.description+(o.affectedServices&&o.affectedServices.length>0?`
Affected services: ${o.affectedServices.join(", ")}`:""),progress:K(o.startTime,o.endTime)}}):[]}function K(s,o){const e=new Date(s).getTime(),g=new Date(o).getTime(),t=Date.now();return t<=e?0:t>=g?100:Math.round((t-e)/(g-e)*100)}function z(s){return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(s)}async function P(){var s,o;if(!x(k)){V(k,!0);try{const e=await fetch("/api/health");if(!e.ok)throw new Error(`Failed to fetch health data: ${e.status}`);const g=await e.json(),t={...n.pageData,serviceHealth:g};V(f,c(t),!0),V(R,{uptime:t.uptime||99.9,emailDeliveryRate:((s=t.email)==null?void 0:s.deliveryRate)||0,apiResponseTime:t.apiResponseTime||250,jobSuccessRate:((o=t.jobs)==null?void 0:o.successRate)||0},!0),V(b,new Date,!0)}catch(e){console.error("Error fetching system status:",e),Me.error("Failed to fetch system status")}finally{V(k,!1)}}}function Y(s){var w,j,m,E;const o={responseTime:0,successRate:0,requestCount:0,errorRate:0},e=n.pageData.serviceHealth||{},g={Matches:{status:"operational",details:{}},Jobs:{status:"operational",details:{}},Tracker:{status:"operational",details:{}},Documents:{status:"operational",details:{}},Automation:{status:"operational",details:{}},System:{status:"operational",details:{}},Website:{status:"operational",details:{}}};e.web&&(g.Website=e.web),e.api&&(g.Jobs=e.api,g.Matches=e.api),e.worker&&(g.Automation=e.worker),e.database&&(g.System=e.database,g.Tracker=e.database,g.Documents=e.database);const i=(g[s]||{}).details||{};return{responseTime:i.responseTime||o.responseTime,successRate:i.successRate||(s==="Jobs"?x(R).jobSuccessRate:s==="System"?((w=e.database)==null?void 0:w.status)==="operational"?100:80:s==="Website"?((j=e.web)==null?void 0:j.status)==="operational"?100:80:o.successRate),requestCount:i.requestCount||o.requestCount,errorRate:i.errorRate||(s==="Jobs"?100-x(R).jobSuccessRate:s==="System"?((m=e.database)==null?void 0:m.status)==="operational"?0:20:s==="Website"?((E=e.web)==null?void 0:E.status)==="operational"?0:20:o.errorRate),...i.queueSize!==void 0?{queueSize:i.queueSize}:{},...i.processingCount!==void 0?{processingCount:i.processingCount}:{},...i.memoryUsage!==void 0?{memoryUsage:i.memoryUsage}:{},...i.dbSizeMB!==void 0?{dbSizeMB:i.dbSizeMB}:{},...i.activeConnections!==void 0?{activeConnections:i.activeConnections}:{},...i.connectedClients!==void 0?{connectedClients:i.connectedClients}:{}}}function J(){var s,o;V(f,c(n.pageData),!0),V(R,{uptime:n.pageData.uptime||99.9,emailDeliveryRate:((s=n.pageData.email)==null?void 0:s.deliveryRate)||0,apiResponseTime:n.pageData.apiResponseTime||250,jobSuccessRate:((o=n.pageData.jobs)==null?void 0:o.successRate)||0},!0),V(b,new Date(n.pageData.lastUpdated||Date.now()),!0),V(y,F(),!0)}let T;Se(()=>{J(),T=setInterval(P,6e4)}),Re(()=>{T&&clearInterval(T)});var M=vt(),l=r(M),D=d(r(l),2),A=r(D),$=r(A);a(A),a(D),a(l);var q=d(l,2);nt(q,{get metrics(){return x(R)}});var p=d(q,2);O(p,()=>he,(s,o)=>{o(s,{children:(e,g)=>{var t=ut(),i=G(t);O(i,()=>je,(j,m)=>{m(j,{children:(E,ne)=>{var Z=ct(),te=G(Z);O(te,()=>Te,(N,ae)=>{ae(N,{children:(se,ie)=>{re();var v=me("Service Status");u(se,v)},$$slots:{default:!0}})});var X=d(te,2);O(X,()=>ke,(N,ae)=>{ae(N,{children:(se,ie)=>{re();var v=me("Current status of core application services");u(se,v)},$$slots:{default:!0}})}),u(E,Z)},$$slots:{default:!0}})});var w=d(i,2);O(w,()=>_e,(j,m)=>{m(j,{children:(E,ne)=>{var Z=lt();pe(Z,21,()=>x(f),ge,(te,X)=>{const N=de(()=>x(X).description||""),ae=de(()=>{var ie;return((ie=n.pageData.serviceHistory)==null?void 0:ie[x(X).name])||[]}),se=de(()=>Y(x(X).name));st(te,{get name(){return x(X).name},get status(){return x(X).status},get description(){return x(N)},get historyData(){return x(ae)},get metrics(){return x(se)}})}),a(Z),u(E,Z)},$$slots:{default:!0}})}),u(e,t)},$$slots:{default:!0}})});var H=d(p,2);dt(H,{get recentIncidents(){return x(y)}}),a(M),U(s=>S($,`Last updated: ${s??""}`),[()=>z(x(b))]),u(I,M),ue()}var ft=_('<!> <!> <div class="container mb-8"><a href="/system-status/history" class="flex w-full items-center justify-center rounded-lg border p-3 text-center hover:bg-gray-50 dark:hover:bg-gray-900"><!> <span>View notice history</span></a></div>',1);function Bt(I,n){let f=Q(n,"data",8);var R=ft(),b=G(R);Ce(b,{title:"System Status | Hirli",description:"Check the current status of Hirli services and systems.",keywords:"system status, service status, uptime, Hirli status"});var k=d(b,2);mt(k,{get pageData(){return f()}});var y=d(k,2),c=r(y),F=r(c);$e(F,{class:"mr-2 h-4 w-4"}),re(2),a(c),a(y),u(I,R)}export{Bt as component};
