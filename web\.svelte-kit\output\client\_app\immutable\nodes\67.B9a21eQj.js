import{f,a as o,c as Me,t as ae}from"../chunks/BasJTneF.js";import{p as Le,f as $,s as a,c as r,n as v,r as e,g as s,k as ye,t as W,x as Se,a as Te,d as K,v as vr}from"../chunks/CGmarHxI.js";import{i as re}from"../chunks/u21ee2wt.js";import{c as P}from"../chunks/BvdI7LR8.js";import{T as cr,R as fr}from"../chunks/I7hvcB12.js";import{o as ur}from"../chunks/nZgk9enP.js";import{s as g}from"../chunks/CIt1g2O9.js";import{e as Ve,i as qe}from"../chunks/C3w0v0gR.js";import{B as mr,a as pr,C as xr}from"../chunks/XnZcpgwi.js";import{T as Ie}from"../chunks/Csk_I0QV.js";import{U as Qe}from"../chunks/BSHZ37s_.js";import{C as hr}from"../chunks/DZCYCPd3.js";import{B as Ee}from"../chunks/BnV6AXQp.js";import"../chunks/CgXBgsce.js";import{t as le}from"../chunks/DjPYYl4Z.js";import{B as ce}from"../chunks/B1K98fMG.js";import{I as Je}from"../chunks/DMTMHyMa.js";import{C as _r}from"../chunks/DuGukytH.js";import{C as gr}from"../chunks/Cdn-N1RY.js";import{C as yr}from"../chunks/BkJY4La4.js";import{C as wr}from"../chunks/GwmmX_iF.js";import{C as br}from"../chunks/D50jIuLr.js";import{B as Xe}from"../chunks/DaBofrVv.js";import{S as $r}from"../chunks/0ykhD7u6.js";import{G as kr}from"../chunks/BEVim9wJ.js";import{S as ke}from"../chunks/BAawoUIy.js";import{C as Ze}from"../chunks/Dt_Sfkn6.js";import{M as Cr}from"../chunks/yPulTJ2h.js";import{p as Rr}from"../chunks/Btcx8l8F.js";import{R as Pr}from"../chunks/qwsZpUIl.js";import{S as Dr}from"../chunks/C6g8ubaU.js";import{T as Ue}from"../chunks/C88uNE8B.js";import{T as Fe}from"../chunks/DmZyh-PW.js";var Sr=f('<div class="flex h-48 items-center justify-center"><div class="text-muted-foreground text-sm">Loading chart data...</div></div>'),Lr=f('<div class="flex h-48 items-center justify-center"><div class="text-muted-foreground text-sm">No data available</div></div>'),Tr=f('<div class="text-muted-foreground text-center text-sm">Loading referral code history...</div>'),jr=f('<div><span class="text-muted-foreground">Deactivated:</span> </div>'),Hr=f('<div class="flex items-center justify-between text-xs"><span> </span> <!></div>'),Ar=f('<div class="text-muted-foreground text-xs"> </div>'),Br=f('<div class="mt-3"><div class="text-muted-foreground mb-2 text-xs">Recent referrals with this code:</div> <div class="space-y-1"><!> <!></div></div>'),Ur=f('<div class="rounded-lg border p-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="font-mono text-lg font-semibold"> </div> <!></div> <div class="text-right"><div class="text-sm font-medium"> </div> <div class="text-muted-foreground text-xs"> </div></div></div> <div class="mt-3 grid grid-cols-2 gap-4 text-sm"><div><span class="text-muted-foreground">Created:</span> </div> <!></div> <!></div>'),Fr=f('<div class="space-y-4"></div>'),Ir=f(`<div class="text-muted-foreground text-center text-sm">No referral code history yet. Generate referrals with your current code to see analytics
        here.</div>`),Mr=f('<div class="border-border flex items-end justify-between border-b p-4"><div class="flex flex-col"><h4 class="text-md font-normal">Referral Analytics</h4> <p class="text-muted-foreground text-sm">Track your referral performance over time</p></div> <div class="flex items-center gap-2"><!> <span class="text-sm font-medium">Growth Trend</span></div></div> <div class="border-border grid grid-cols-1 gap-4 divide-x border-b sm:grid-cols-3"><div class="p-4"><div class="flex items-center gap-2"><!> <span class="text-sm font-medium">Total Referrals</span></div> <div class="mt-2"><div class="text-2xl font-bold"> </div> <div class="text-muted-foreground text-xs">All time</div></div></div> <div class="p-4"><div class="flex items-center gap-2"><!> <span class="text-sm font-medium">This Month</span></div> <div class="mt-2"><div class="text-2xl font-bold"> </div> <div class="text-muted-foreground text-xs">New referrals</div></div></div> <div class="p-4"><div class="flex items-center gap-2"><!> <span class="text-sm font-medium">Success Rate</span></div> <div class="mt-2"><div class="text-2xl font-bold"> </div> <div class="text-muted-foreground text-xs">Conversion rate</div></div></div></div> <div class="h-64 p-4"><div class="mb-4 flex items-center justify-between"><h4 class="font-medium">Monthly Referrals</h4> <div class="flex items-center gap-4 text-xs"><div class="flex items-center gap-1"><div class="h-3 w-3 rounded-full" style="background-color: var(--chart-1);"></div> <span>Monthly</span></div> <div class="flex items-center gap-1"><div class="h-3 w-3 rounded-full" style="background-color: var(--chart-2);"></div> <span>Cumulative</span></div></div></div> <!></div> <div class="bg-secondary m-4 space-y-4 rounded-md p-4"><div class="flex flex-col"><h4 class="flex gap-2 text-sm"><!> Referral Code History</h4> <p class="text-muted-foreground text-xs">Track all your referral codes and their performance over time.</p></div> <div><!></div></div>',1);function Jr(fe,n){Le(n,!0);let l=ye(null),p=ye(!0);const L=async()=>{try{const u=await fetch("/api/referrals/analytics");u.ok&&K(l,await u.json(),!0)}catch(u){console.error("Error loading analytics:",u)}finally{K(p,!1)}};ur(()=>{L()});const C=Se(()=>{var u;return((u=s(l))==null?void 0:u.monthlyAnalytics)||[]}),B={referrals:{label:"Monthly Referrals",color:"var(--chart-1)"},cumulative:{label:"Cumulative",color:"var(--chart-2)"}};var T=Mr(),j=$(T),N=a(r(j),2),k=r(N);Ie(k,{class:"text-primary h-5 w-5"}),v(2),e(N),e(j);var R=a(j,2),Q=r(R),H=r(Q),te=r(H);Qe(te,{class:"text-primary h-4 w-4"}),v(2),e(H);var O=a(H,2),X=r(O),D=r(X,!0);e(X),v(2),e(O),e(Q);var U=a(Q,2),F=r(U),Y=r(F);hr(Y,{class:"text-success h-4 w-4"}),v(2),e(F);var G=a(F,2),z=r(G),b=r(z,!0);e(z),v(2),e(G),e(U);var y=a(U,2),h=r(y),E=r(h);Ie(E,{class:"text-warning h-4 w-4"}),v(2),e(h);var t=a(h,2),i=r(t),d=r(i);e(i),v(2),e(t),e(y),e(R);var c=a(R,2),se=a(r(c),2);{var V=u=>{var M=Sr();o(u,M)},m=(u,M)=>{{var ne=A=>{var w=Lr();o(A,w)},_e=A=>{var w=Me(),oe=$(w);P(oe,()=>xr,(x,Z)=>{Z(x,{get config(){return B},class:"h-48 w-full",children:(q,ee)=>{const ie=Se(()=>[{key:"referrals",label:B.referrals.label,color:B.referrals.color}]);mr(q,{get data(){return s(C)},x:"month",axis:"x",legend:!0,get series(){return s(ie)},props:{xAxis:{format:pe=>pe.slice(0,3)}},tooltip:pe=>{var we=Me(),Ce=$(we);P(Ce,()=>pr,(be,ge)=>{ge(be,{})}),o(pe,we)},$$slots:{tooltip:!0}})},$$slots:{default:!0}})}),o(A,w)};re(u,A=>{s(C).length===0?A(ne):A(_e,!1)},M)}};re(se,u=>{s(p)?u(V):u(m,!1)})}e(c);var _=a(c,2),de=r(_),I=r(de),Ne=r(I);Ie(Ne,{class:"h-4 w-4"}),v(),e(I),v(2),e(de);var je=a(de,2),We=r(je);{var He=u=>{var M=Tr();o(u,M)},Ae=(u,M)=>{{var ne=A=>{var w=Fr();Ve(w,21,()=>s(l).codeHistory,qe,(oe,x)=>{var Z=Ur(),q=r(Z),ee=r(q),ie=r(ee),me=r(ie,!0);e(ie);var pe=a(ie,2);{var we=S=>{Ee(S,{class:"bg-primary text-white",children:(J,$e)=>{v();var xe=ae("Current");o(J,xe)},$$slots:{default:!0}})},Ce=S=>{Ee(S,{class:"bg-secondary text-muted-foreground",children:(J,$e)=>{v();var xe=ae("Historical");o(J,xe)},$$slots:{default:!0}})};re(pe,S=>{s(x).isActive?S(we):S(Ce,!1)})}e(ee);var be=a(ee,2),ge=r(be),Oe=r(ge);e(ge);var ve=a(ge,2),Re=r(ve);e(ve),e(be),e(q);var ue=a(q,2),Ye=r(ue),er=a(r(Ye));e(Ye);var rr=a(Ye,2);{var ar=S=>{var J=jr(),$e=a(r(J));e(J),W(xe=>g($e,` ${xe??""}`),[()=>new Date(s(x).deactivatedAt).toLocaleDateString()]),o(S,J)};re(rr,S=>{s(x).deactivatedAt&&S(ar)})}e(ue);var tr=a(ue,2);{var sr=S=>{var J=Br(),$e=a(r(J),2),xe=r($e);Ve(xe,17,()=>s(x).referrals.slice(0,3),qe,(Pe,he)=>{var De=Hr(),Ge=r(De),lr=r(Ge,!0);e(Ge);var dr=a(Ge,2);const nr=Se(()=>`text-xs ${s(he).status==="completed"?"bg-primary text-white":"bg-secondary text-muted-foreground"}`);Ee(dr,{get class(){return s(nr)},children:(Be,ze)=>{v();var Ke=ae();W(()=>g(Ke,s(he).status||"pending")),o(Be,Ke)},$$slots:{default:!0}}),e(De),W(()=>{var Be,ze;return g(lr,((Be=s(he).referred)==null?void 0:Be.name)||((ze=s(he).referred)==null?void 0:ze.email)||"Unknown User")}),o(Pe,De)});var or=a(xe,2);{var ir=Pe=>{var he=Ar(),De=r(he);e(he),W(()=>g(De,`+${s(x).referrals.length-3} more referrals`)),o(Pe,he)};re(or,Pe=>{s(x).referrals.length>3&&Pe(ir)})}e($e),e(J),o(S,J)};re(tr,S=>{var J;((J=s(x).referrals)==null?void 0:J.length)>0&&S(sr)})}e(Z),W(S=>{g(me,s(x).referralCode),g(Oe,`${s(x).referralCount??""} referrals`),g(Re,`${s(x).completedReferrals??""} completed, ${s(x).pendingReferrals??""}
                  pending`),g(er,` ${S??""}`)},[()=>new Date(s(x).createdAt).toLocaleDateString()]),o(oe,Z)}),e(w),o(A,w)},_e=A=>{var w=Ir();o(A,w)};re(u,A=>{var w,oe;((oe=(w=s(l))==null?void 0:w.codeHistory)==null?void 0:oe.length)>0?A(ne):A(_e,!1)},M)}};re(We,u=>{s(p)?u(He):u(Ae,!1)})}e(je),e(_),W(()=>{var u,M,ne;g(D,((u=n.referralData)==null?void 0:u.referralCount)||0),g(b,s(p)?"...":((M=s(C)[s(C).length-1])==null?void 0:M.referrals)||0),g(d,`${(s(p)?"...":((ne=s(l))==null?void 0:ne.conversionRate)||0)??""}%`)}),o(fe,T),Te()}var Nr=f("<!> <!>",1),Or=f('<div class="flex items-center justify-between"><div><p class="font-medium"> </p> <p class="text-muted-foreground text-sm"> </p></div> <!></div> <!>',1),Yr=f('<div class="space-y-3"></div>'),Gr=f("<!> <!>",1),zr=f(`<div class="space-y-4"><div class="border-border grid gap-4 divide-x border-b md:grid-cols-3"><div class="p-4"><div class="flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="text-sm font-medium">Total Referrals</h3> <!></div> <div><div class="text-2xl font-bold"> </div> <p class="text-muted-foreground text-xs">People you've referred</p></div></div> <div class="p-4"><div class="flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="text-sm font-medium">Rewards Earned</h3> <!></div> <div><div class="text-2xl font-bold">$0</div> <p class="text-muted-foreground text-xs">Coming soon</p></div></div> <div class="p-4"><div class="flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="text-sm font-medium">Your Code</h3> <!></div> <div><div class="font-mono text-2xl font-bold"> </div> <p class="text-muted-foreground text-xs">Your unique referral code</p></div></div></div> <div class="flex flex-col gap-8 px-4"><div class="grid grid-cols-2 gap-4"><div class="flex flex-col gap-4"><div class="flex flex-col gap-1"><h2 class="text-lg font-medium">Invite friends & earn rewards</h2> <p class="text-muted-foreground text-sm">Refer a friend and earn $10 credit for every paying user. They'll get 50% off
            their first month on Starter or Pro plans. <strong>Start sharing today!</strong></p></div> <div class="w-2/3"><h3 class="mb-3 font-medium">Share your link</h3> <div class="space-y-4"><div class="flex gap-2"><!> <!> <!></div></div></div></div></div></div> <!></div>`);function Er(fe,n){Le(n,!0);let l=ye(!1);const p=async()=>{var m;if((m=n.referralData)!=null&&m.referralLink){K(l,!0);try{await navigator.clipboard.writeText(n.referralData.referralLink),le.success("Referral link copied to clipboard!")}catch(_){console.error("Error copying to clipboard:",_),le.error("Failed to copy referral link")}finally{K(l,!1)}}},L=async()=>{var m;if((m=n.referralData)!=null&&m.referralLink)if(navigator.share)try{await navigator.share({title:"Join Hirli with my referral link",text:"Sign up for Hirli using my referral link and get started with job automation!",url:n.referralData.referralLink})}catch(_){console.error("Error sharing:",_),p()}else p()};var C=zr(),B=r(C),T=r(B),j=r(T),N=a(r(j),2);Qe(N,{class:"text-muted-foreground h-4 w-4"}),e(j);var k=a(j,2),R=r(k),Q=r(R,!0);e(R),v(2),e(k),e(T);var H=a(T,2),te=r(H),O=a(r(te),2);kr(O,{class:"text-muted-foreground h-4 w-4"}),e(te),v(2),e(H);var X=a(H,2),D=r(X),U=a(r(D),2);ke(U,{class:"text-muted-foreground h-4 w-4"}),e(D);var F=a(D,2),Y=r(F),G=r(Y,!0);e(Y),v(2),e(F),e(X),e(B);var z=a(B,2),b=r(z),y=r(b),h=a(r(y),2),E=a(r(h),2),t=r(E),i=r(t);Je(i,{get value(){return n.referralData.referralLink},readonly:!0,class:"font-mono"});var d=a(i,2);ce(d,{variant:"outline",size:"sm",onclick:p,get disabled(){return s(l)},children:(m,_)=>{Ze(m,{class:"h-4 w-4"})},$$slots:{default:!0}});var c=a(d,2);ce(c,{variant:"outline",size:"sm",onclick:L,children:(m,_)=>{ke(m,{class:"h-4 w-4"})},$$slots:{default:!0}}),e(t),e(E),e(h),e(y),e(b),e(z);var se=a(z,2);{var V=m=>{var _=Me(),de=$(_);P(de,()=>_r,(I,Ne)=>{Ne(I,{children:(je,We)=>{var He=Gr(),Ae=$(He);P(Ae,()=>wr,(M,ne)=>{ne(M,{children:(_e,A)=>{var w=Nr(),oe=$(w);P(oe,()=>br,(Z,q)=>{q(Z,{children:(ee,ie)=>{v();var me=ae("Recent Referrals");o(ee,me)},$$slots:{default:!0}})});var x=a(oe,2);P(x,()=>yr,(Z,q)=>{q(Z,{children:(ee,ie)=>{v();var me=ae("People who signed up using your referral code.");o(ee,me)},$$slots:{default:!0}})}),o(_e,w)},$$slots:{default:!0}})});var u=a(Ae,2);P(u,()=>gr,(M,ne)=>{ne(M,{children:(_e,A)=>{var w=Yr();Ve(w,21,()=>n.referralData.referrals.slice(0,5),qe,(oe,x)=>{var Z=Or(),q=$(Z),ee=r(q),ie=r(ee),me=r(ie,!0);e(ie);var pe=a(ie,2),we=r(pe);e(pe),e(ee);var Ce=a(ee,2);const be=Se(()=>s(x).status==="completed"?"default":"secondary");Xe(Ce,{get variant(){return s(be)},children:(ve,Re)=>{v();var ue=ae();W(()=>g(ue,s(x).status||"pending")),o(ve,ue)},$$slots:{default:!0}}),e(q);var ge=a(q,2);{var Oe=ve=>{$r(ve,{})};re(ge,ve=>{s(x)!==n.referralData.referrals[n.referralData.referrals.length-1]&&ve(Oe)})}W(ve=>{var Re,ue;g(me,((Re=s(x).referred)==null?void 0:Re.name)||((ue=s(x).referred)==null?void 0:ue.email)||"Unknown User"),g(we,`Joined ${ve??""}`)},[()=>new Date(s(x).createdAt).toLocaleDateString()]),o(oe,Z)}),e(w),o(_e,w)},$$slots:{default:!0}})}),o(je,He)},$$slots:{default:!0}})}),o(m,_)};re(se,m=>{n.referralData.referrals&&n.referralData.referrals.length>0&&m(V)})}e(C),W(()=>{g(Q,n.referralData.referralCount||0),g(G,n.referralData.referralCode)}),o(fe,C),Te()}var Vr=f("<!> ",1),qr=f('<!> <span class="text-xs">Twitter</span>',1),Wr=f('<!> <span class="text-xs">Facebook</span>',1),Kr=f('<!> <span class="text-xs">LinkedIn</span>',1),Qr=f('<!> <span class="text-xs">Email</span>',1),Xr=f('<!> <span class="text-xs">Native Share</span>',1),Zr=f('<div class="border-border flex flex-col gap-0 border-b p-4"><h4 class="text-md font-normal">Share Your Referral Link</h4> <p class="text-muted-foreground text-sm">Choose how you want to share your referral link and start earning rewards.</p></div> <div class="space-y-6 p-4"><div class="bg-muted/50 rounded-lg border p-4"><h4 class="mb-2 font-medium">Share Message Preview:</h4> <p class="text-muted-foreground text-sm"> </p></div> <div class="space-y-4"><h4 class="font-medium">Your Referral Link</h4> <div class="flex gap-2"><!> <!></div></div> <div class="space-y-4"><h4 class="font-medium">Share Options</h4> <div class="grid grid-cols-2 gap-3 sm:grid-cols-3"><!> <!> <!> <!> <!></div></div> <div class="rounded-lg border bg-blue-50 p-4 dark:bg-blue-950/20"><h4 class="mb-2 flex items-center gap-2 font-medium"><!> Tips for Better Results</h4> <ul class="text-muted-foreground space-y-1 text-sm"><li>• Share with people actively looking for jobs</li> <li>• Explain how Hirli can help automate their job search</li> <li>• Follow up to help them get started</li> <li>• Share in relevant professional groups</li></ul></div></div>',1);function ea(fe,n){Le(n,!0);let l=ye(!1);const p=async()=>{var t;if((t=n.referralData)!=null&&t.referralLink){K(l,!0);try{await navigator.clipboard.writeText(n.referralData.referralLink),le.success("Referral link copied to clipboard!")}catch(i){console.error("Error copying to clipboard:",i),le.error("Failed to copy referral link")}finally{K(l,!1)}}},L=async()=>{var t;if((t=n.referralData)!=null&&t.referralLink)if(navigator.share)try{await navigator.share({title:"Join Hirli with my referral link",text:"Sign up for Hirli using my referral link and get started with job automation!",url:n.referralData.referralLink})}catch(i){console.error("Error sharing:",i),p()}else p()},C=()=>{var t;return(t=n.referralData)!=null&&t.referralLink?`🚀 Join me on Hirli and automate your job search! Use my referral link to get started: ${n.referralData.referralLink}`:""},B=()=>{const t=encodeURIComponent("Join Hirli - Automate Your Job Search"),i=encodeURIComponent(C());window.open(`mailto:?subject=${t}&body=${i}`)},T=t=>{const i=encodeURIComponent(C()),d=encodeURIComponent(n.referralData.referralLink);let c="";switch(t){case"twitter":c=`https://twitter.com/intent/tweet?text=${i}`;break;case"facebook":c=`https://www.facebook.com/sharer/sharer.php?u=${d}`;break;case"linkedin":c=`https://www.linkedin.com/sharing/share-offsite/?url=${d}`;break}c&&window.open(c,"_blank","width=600,height=400")};var j=Zr(),N=a($(j),2),k=r(N),R=a(r(k),2),Q=r(R,!0);e(R),e(k);var H=a(k,2),te=a(r(H),2),O=r(te);Je(O,{get value(){return n.referralData.referralLink},readonly:!0,class:"font-mono"});var X=a(O,2);ce(X,{onclick:p,get disabled(){return s(l)},children:(t,i)=>{var d=Vr(),c=$(d);Ze(c,{class:"mr-2 h-4 w-4"});var se=a(c);W(()=>g(se,` ${s(l)?"Copied!":"Copy"}`)),o(t,d)},$$slots:{default:!0}}),e(te),e(H);var D=a(H,2),U=a(r(D),2),F=r(U);ce(F,{variant:"outline",class:"flex h-auto flex-col gap-2 py-4",onclick:()=>T("twitter"),children:(t,i)=>{var d=qr(),c=$(d);ke(c,{class:"h-5 w-5"}),v(2),o(t,d)},$$slots:{default:!0}});var Y=a(F,2);ce(Y,{variant:"outline",class:"flex h-auto flex-col gap-2 py-4",onclick:()=>T("facebook"),children:(t,i)=>{var d=Wr(),c=$(d);ke(c,{class:"h-5 w-5"}),v(2),o(t,d)},$$slots:{default:!0}});var G=a(Y,2);ce(G,{variant:"outline",class:"flex h-auto flex-col gap-2 py-4",onclick:()=>T("linkedin"),children:(t,i)=>{var d=Kr(),c=$(d);ke(c,{class:"h-5 w-5"}),v(2),o(t,d)},$$slots:{default:!0}});var z=a(G,2);ce(z,{variant:"outline",class:"flex h-auto flex-col gap-2 py-4",onclick:B,children:(t,i)=>{var d=Qr(),c=$(d);Cr(c,{class:"h-5 w-5"}),v(2),o(t,d)},$$slots:{default:!0}});var b=a(z,2);ce(b,{variant:"outline",class:"flex h-auto flex-col gap-2 py-4",onclick:L,children:(t,i)=>{var d=Xr(),c=$(d);ke(c,{class:"h-5 w-5"}),v(2),o(t,d)},$$slots:{default:!0}}),e(U),e(D);var y=a(D,2),h=r(y),E=r(h);Ie(E,{class:"h-4 w-4"}),v(),e(h),v(2),e(y),e(N),W(t=>g(Q,t),[C]),o(fe,j),Te()}var ra=f('<div><div><h3>You Were Referred By</h3></div> <div><div class="flex items-center gap-2"><p class="font-medium"> </p> <!></div></div></div>'),aa=f(`<div class="border-border flex flex-col gap-0 border-b p-4"><h4 class="text-md font-normal">Customize Your Referral Code</h4> <p class="text-muted-foreground text-sm">Create a custom referral code that's easy to remember and share.</p></div> <div class="space-y-4 p-4"><div class="space-y-2"><div class="flex gap-2"><!> <!> <!></div> <p class="text-muted-foreground text-xs">Custom codes must be 4-12 characters long and contain only letters and numbers.</p></div> <div><h3>Current Referral Information</h3></div> <div class="space-y-4"><div class="grid grid-cols-2 gap-4"><div><div class="text-sm font-medium">Referral Code</div> <p class="font-mono text-lg"> </p></div> <div><div class="text-sm font-medium">Total Referrals</div> <p class="text-lg font-semibold"> </p></div></div> <div><div class="text-sm font-medium">Full Referral URL</div> <!></div></div> <!></div>`,1);function ta(fe,n){Le(n,!0);let l=Rr(n,"referralData",15),p=ye(""),L=ye(!1);const C=async()=>{K(L,!0);try{const t=await fetch("/api/referrals",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"regenerate"})});if(t.ok){const i=await t.json();l(l().referralCode=i.referralCode,!0),l(l().referralLink=i.referralLink,!0),le.success("New referral code generated!")}else{const i=await t.json();le.error(i.error||"Failed to generate new code")}}catch(t){console.error("Error generating new code:",t),le.error("Failed to generate new code")}finally{K(L,!1)}},B=async()=>{if(!s(p).trim()){le.error("Please enter a custom code");return}K(L,!0);try{const t=await fetch("/api/referrals",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"create",customCode:s(p).trim()})});if(t.ok){const i=await t.json();l(l().referralCode=i.referralCode,!0),l(l().referralLink=i.referralLink,!0),K(p,""),le.success("Custom referral code set!")}else{const i=await t.json();le.error(i.error||"Failed to set custom code")}}catch(t){console.error("Error setting custom code:",t),le.error("Failed to set custom code")}finally{K(L,!1)}};var T=aa(),j=a($(T),2),N=r(j),k=r(N),R=r(k);Je(R,{placeholder:"Enter custom code (4-12 characters)",class:"font-mono",get value(){return s(p)},set value(t){K(p,t,!0)}});var Q=a(R,2);const H=Se(()=>s(L)||!s(p).trim());ce(Q,{onclick:B,get disabled(){return s(H)},children:(t,i)=>{v();var d=ae("Set Code");o(t,d)},$$slots:{default:!0}});var te=a(Q,2);ce(te,{variant:"outline",onclick:C,get disabled(){return s(L)},children:(t,i)=>{Pr(t,{class:"mr-2 h-4 w-4"})},$$slots:{default:!0}}),e(k),v(2),e(N);var O=a(N,4),X=r(O),D=r(X),U=a(r(D),2),F=r(U,!0);e(U),e(D);var Y=a(D,2),G=a(r(Y),2),z=r(G,!0);e(G),e(Y),e(X);var b=a(X,2),y=a(r(b),2);Je(y,{get value(){return l().referralLink},readonly:!0,class:"font-mono text-xs"}),e(b),e(O);var h=a(O,2);{var E=t=>{var i=ra(),d=a(r(i),2),c=r(d),se=r(c),V=r(se,!0);e(se);var m=a(se,2);Xe(m,{variant:"outline",children:(_,de)=>{v();var I=ae("Referrer");o(_,I)},$$slots:{default:!0}}),e(c),e(d),e(i),W(()=>g(V,l().referredBy.name||l().referredBy.email)),o(t,i)};re(h,t=>{l().referredBy&&t(E)})}e(j),W(()=>{g(F,l().referralCode),g(z,l().referralCount||0)}),o(fe,T),Te()}var sa=f("<!> <!> <!> <!>",1),oa=f("<!> <!> <!> <!> <!>",1),ia=f('<div class="flex items-center justify-center py-8"><div class="text-muted-foreground">Failed to load referral data</div></div>'),la=f('<!> <div class="flex h-full flex-col"><div class="flex flex-col justify-between p-6"><h2 class="text-lg font-semibold">Referral Program</h2> <p class="text-muted-foreground">Share Hirli with friends and earn rewards for successful referrals.</p></div> <div class="flex-1"><!></div></div>',1);function Na(fe,n){Le(n,!0);let l=ye(vr(n.data.referralData));var p=la(),L=$(p);Dr(L,{title:"Referral Program | Hirli",description:"Share Hirli with friends and earn rewards for successful referrals.",keywords:"referral program, share, earn, rewards, referrals, Hirli",url:"https://hirli.com/dashboard/settings/referrals"});var C=a(L,2),B=a(r(C),2),T=r(B);{var j=k=>{var R=Me(),Q=$(R);P(Q,()=>fr,(H,te)=>{te(H,{value:"overview",class:"w-full",children:(O,X)=>{var D=oa(),U=$(D);P(U,()=>cr,(b,y)=>{y(b,{children:(h,E)=>{var t=sa(),i=$(t);P(i,()=>Ue,(V,m)=>{m(V,{value:"overview",children:(_,de)=>{v();var I=ae("Overview");o(_,I)},$$slots:{default:!0}})});var d=a(i,2);P(d,()=>Ue,(V,m)=>{m(V,{value:"analytics",children:(_,de)=>{v();var I=ae("Analytics");o(_,I)},$$slots:{default:!0}})});var c=a(d,2);P(c,()=>Ue,(V,m)=>{m(V,{value:"share",children:(_,de)=>{v();var I=ae("Share & Earn");o(_,I)},$$slots:{default:!0}})});var se=a(c,2);P(se,()=>Ue,(V,m)=>{m(V,{value:"settings",children:(_,de)=>{v();var I=ae("Settings");o(_,I)},$$slots:{default:!0}})}),o(h,t)},$$slots:{default:!0}})});var F=a(U,2);P(F,()=>Fe,(b,y)=>{y(b,{value:"overview",children:(h,E)=>{Er(h,{get referralData(){return s(l)}})},$$slots:{default:!0}})});var Y=a(F,2);P(Y,()=>Fe,(b,y)=>{y(b,{value:"analytics",children:(h,E)=>{Jr(h,{get referralData(){return s(l)}})},$$slots:{default:!0}})});var G=a(Y,2);P(G,()=>Fe,(b,y)=>{y(b,{value:"share",children:(h,E)=>{ea(h,{get referralData(){return s(l)}})},$$slots:{default:!0}})});var z=a(G,2);P(z,()=>Fe,(b,y)=>{y(b,{value:"settings",children:(h,E)=>{ta(h,{get referralData(){return s(l)},set referralData(t){K(l,t,!0)}})},$$slots:{default:!0}})}),o(O,D)},$$slots:{default:!0}})}),o(k,R)},N=k=>{var R=ia();o(k,R)};re(T,k=>{s(l)?k(j):k(N,!1)})}e(B),e(C),o(fe,p),Te()}export{Na as component};
