import{c,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as i}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function b(t,o){const s=m(o,["children","$$slots","$$events","$$legacy"]),e=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"}],["path",{d:"M2 12h20"}]];f(t,d({name:"globe"},()=>s,{get iconNode(){return e},children:(a,$)=>{var r=c(),n=p(r);i(n,o,"default",{},null),l(a,r)},$$slots:{default:!0}}))}export{b as G};
