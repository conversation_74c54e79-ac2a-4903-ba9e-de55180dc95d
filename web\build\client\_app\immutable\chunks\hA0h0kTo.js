import{c as n,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function A(r,o){const s=c(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"}]];f(r,d({name:"bell"},()=>s,{get iconNode(){return a},children:(e,$)=>{var t=n(),l=i(t);m(l,o,"default",{},null),p(e,t)},$$slots:{default:!0}}))}export{A as B};
