import{c as s,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as i}from"./BBa424ah.js";import{l as $,s as u}from"./Btcx8l8F.js";import{I as h}from"./D4f2twK-.js";function N(n,e){const o=$(e,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8"}]];h(n,u({name:"bold"},()=>o,{get iconNode(){return a},children:(r,f)=>{var t=s(),l=d(t);i(l,e,"default",{},null),c(r,t)},$$slots:{default:!0}}))}function x(n,e){const o=$(e,["children","$$slots","$$events","$$legacy"]),a=[["circle",{cx:"9",cy:"12",r:"1"}],["circle",{cx:"9",cy:"5",r:"1"}],["circle",{cx:"9",cy:"19",r:"1"}],["circle",{cx:"15",cy:"12",r:"1"}],["circle",{cx:"15",cy:"5",r:"1"}],["circle",{cx:"15",cy:"19",r:"1"}]];h(n,u({name:"grip-vertical"},()=>o,{get iconNode(){return a},children:(r,f)=>{var t=s(),l=d(t);i(l,e,"default",{},null),c(r,t)},$$slots:{default:!0}}))}function M(n,e){const o=$(e,["children","$$slots","$$events","$$legacy"]),a=[["line",{x1:"19",x2:"10",y1:"4",y2:"4"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20"}]];h(n,u({name:"italic"},()=>o,{get iconNode(){return a},children:(r,f)=>{var t=s(),l=d(t);i(l,e,"default",{},null),c(r,t)},$$slots:{default:!0}}))}function P(n,e){const o=$(e,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M10 12h11"}],["path",{d:"M10 18h11"}],["path",{d:"M10 6h11"}],["path",{d:"M4 10h2"}],["path",{d:"M4 6h1v4"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"}]];h(n,u({name:"list-ordered"},()=>o,{get iconNode(){return a},children:(r,f)=>{var t=s(),l=d(t);i(l,e,"default",{},null),c(r,t)},$$slots:{default:!0}}))}function z(n,e){const o=$(e,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"}],["path",{d:"m15 5 4 4"}]];h(n,u({name:"pencil"},()=>o,{get iconNode(){return a},children:(r,f)=>{var t=s(),l=d(t);i(l,e,"default",{},null),c(r,t)},$$slots:{default:!0}}))}function L(n,e){const o=$(e,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M21 7v6h-6"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7"}]];h(n,u({name:"redo"},()=>o,{get iconNode(){return a},children:(r,f)=>{var t=s(),l=d(t);i(l,e,"default",{},null),c(r,t)},$$slots:{default:!0}}))}function I(n,e){const o=$(e,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"}],["path",{d:"M3 3v5h5"}]];h(n,u({name:"rotate-ccw"},()=>o,{get iconNode(){return a},children:(r,f)=>{var t=s(),l=d(t);i(l,e,"default",{},null),c(r,t)},$$slots:{default:!0}}))}function R(n,e){const o=$(e,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M6 4v6a6 6 0 0 0 12 0V4"}],["line",{x1:"4",x2:"20",y1:"20",y2:"20"}]];h(n,u({name:"underline"},()=>o,{get iconNode(){return a},children:(r,f)=>{var t=s(),l=d(t);i(l,e,"default",{},null),c(r,t)},$$slots:{default:!0}}))}function U(n,e){const o=$(e,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M3 7v6h6"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13"}]];h(n,u({name:"undo"},()=>o,{get iconNode(){return a},children:(r,f)=>{var t=s(),l=d(t);i(l,e,"default",{},null),c(r,t)},$$slots:{default:!0}}))}export{N as B,x as G,M as I,P as L,z as P,I as R,R as U,U as a,L as b};
