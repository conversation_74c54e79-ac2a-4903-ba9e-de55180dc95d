var ot=e=>{throw TypeError(e)};var Gt=(e,t,n)=>t.has(e)||ot("Cannot "+n);var A=(e,t,n)=>(Gt(e,t,"read from private field"),n?n.call(e):t.get(e)),C=(e,t,n)=>t.has(e)?ot("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n);import{o as je}from"./nZgk9enP.js";import{w as Me,k as P,g as O,d as N,j as $e}from"./CGmarHxI.js";new URL("sveltekit-internal://");function Ht(e,t){return e==="/"||t==="ignore"?e:t==="never"?e.endsWith("/")?e.slice(0,-1):e:t==="always"&&!e.endsWith("/")?e+"/":e}function Kt(e){return e.split("%25").map(decodeURI).join("%25")}function Wt(e){for(const t in e)e[t]=decodeURIComponent(e[t]);return e}function Te({href:e}){return e.split("#")[0]}function Yt(e,t,n,r=!1){const a=new URL(e);Object.defineProperty(a,"searchParams",{value:new Proxy(a.searchParams,{get(i,o){if(o==="get"||o==="getAll"||o==="has")return l=>(n(l),i[o](l));t();const c=Reflect.get(i,o);return typeof c=="function"?c.bind(i):c}}),enumerable:!0,configurable:!0});const s=["href","pathname","search","toString","toJSON"];r&&s.push("hash");for(const i of s)Object.defineProperty(a,i,{get(){return t(),e[i]},enumerable:!0,configurable:!0});return a}function Jt(...e){let t=5381;for(const n of e)if(typeof n=="string"){let r=n.length;for(;r;)t=t*33^n.charCodeAt(--r)}else if(ArrayBuffer.isView(n)){const r=new Uint8Array(n.buffer,n.byteOffset,n.byteLength);let a=r.length;for(;a;)t=t*33^r[--a]}else throw new TypeError("value must be a string or TypedArray");return(t>>>0).toString(36)}function zt(e){const t=atob(e),n=new Uint8Array(t.length);for(let r=0;r<t.length;r++)n[r]=t.charCodeAt(r);return n.buffer}const Xt=window.fetch;window.fetch=(e,t)=>((e instanceof Request?e.method:(t==null?void 0:t.method)||"GET")!=="GET"&&Y.delete(qe(e)),Xt(e,t));const Y=new Map;function Zt(e,t){const n=qe(e,t),r=document.querySelector(n);if(r!=null&&r.textContent){let{body:a,...s}=JSON.parse(r.textContent);const i=r.getAttribute("data-ttl");return i&&Y.set(n,{body:a,init:s,ttl:1e3*Number(i)}),r.getAttribute("data-b64")!==null&&(a=zt(a)),Promise.resolve(new Response(a,s))}return window.fetch(e,t)}function Qt(e,t,n){if(Y.size>0){const r=qe(e,n),a=Y.get(r);if(a){if(performance.now()<a.ttl&&["default","force-cache","only-if-cached",void 0].includes(n==null?void 0:n.cache))return new Response(a.body,a.init);Y.delete(r)}}return window.fetch(t,n)}function qe(e,t){let r=`script[data-sveltekit-fetched][data-url=${JSON.stringify(e instanceof Request?e.url:e)}]`;if(t!=null&&t.headers||t!=null&&t.body){const a=[];t.headers&&a.push([...new Headers(t.headers)].join(",")),t.body&&(typeof t.body=="string"||ArrayBuffer.isView(t.body))&&a.push(t.body),r+=`[data-hash="${Jt(...a)}"]`}return r}const en=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function tn(e){const t=[];return{pattern:e==="/"?/^\/$/:new RegExp(`^${rn(e).map(r=>{const a=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(r);if(a)return t.push({name:a[1],matcher:a[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const s=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(r);if(s)return t.push({name:s[1],matcher:s[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!r)return;const i=r.split(/\[(.+?)\](?!\])/);return"/"+i.map((c,l)=>{if(l%2){if(c.startsWith("x+"))return Ce(String.fromCharCode(parseInt(c.slice(2),16)));if(c.startsWith("u+"))return Ce(String.fromCharCode(...c.slice(2).split("-").map(p=>parseInt(p,16))));const h=en.exec(c),[,u,w,f,m]=h;return t.push({name:f,matcher:m,optional:!!u,rest:!!w,chained:w?l===1&&i[0]==="":!1}),w?"(.*?)":u?"([^/]*)?":"([^/]+?)"}return Ce(c)}).join("")}).join("")}/?$`),params:t}}function nn(e){return!/^\([^)]+\)$/.test(e)}function rn(e){return e.slice(1).split("/").filter(nn)}function an(e,t,n){const r={},a=e.slice(1),s=a.filter(o=>o!==void 0);let i=0;for(let o=0;o<t.length;o+=1){const c=t[o];let l=a[o-i];if(c.chained&&c.rest&&i&&(l=a.slice(o-i,o+1).filter(h=>h).join("/"),i=0),l===void 0){c.rest&&(r[c.name]="");continue}if(!c.matcher||n[c.matcher](l)){r[c.name]=l;const h=t[o+1],u=a[o+1];h&&!h.rest&&h.optional&&u&&c.chained&&(i=0),!h&&!u&&Object.keys(r).length===s.length&&(i=0);continue}if(c.optional&&c.chained){i++;continue}return}if(!i)return r}function Ce(e){return e.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function on({nodes:e,server_loads:t,dictionary:n,matchers:r}){const a=new Set(t);return Object.entries(n).map(([o,[c,l,h]])=>{const{pattern:u,params:w}=tn(o),f={id:o,exec:m=>{const p=u.exec(m);if(p)return an(p,w,r)},errors:[1,...h||[]].map(m=>e[m]),layouts:[0,...l||[]].map(i),leaf:s(c)};return f.errors.length=f.layouts.length=Math.max(f.errors.length,f.layouts.length),f});function s(o){const c=o<0;return c&&(o=~o),[c,e[o]]}function i(o){return o===void 0?o:[a.has(o),e[o]]}}function vt(e,t=JSON.parse){try{return t(sessionStorage[e])}catch{}}function st(e,t,n=JSON.stringify){const r=n(t);try{sessionStorage[e]=r}catch{}}var gt;const T=((gt=globalThis.__sveltekit_swdb3o)==null?void 0:gt.base)??"";var mt;const sn=((mt=globalThis.__sveltekit_swdb3o)==null?void 0:mt.assets)??T,cn="1748980944399",bt="sveltekit:snapshot",At="sveltekit:scroll",St="sveltekit:states",ln="sveltekit:pageurl",q="sveltekit:history",Z="sveltekit:navigation",F={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},he=location.origin;function Ge(e){if(e instanceof URL)return e;let t=document.baseURI;if(!t){const n=document.getElementsByTagName("base");t=n.length?n[0].href:document.URL}return new URL(e,t)}function He(){return{x:pageXOffset,y:pageYOffset}}function M(e,t){return e.getAttribute(`data-sveltekit-${t}`)}const it={...F,"":F.hover};function kt(e){let t=e.assignedSlot??e.parentNode;return(t==null?void 0:t.nodeType)===11&&(t=t.host),t}function Et(e,t){for(;e&&e!==t;){if(e.nodeName.toUpperCase()==="A"&&e.hasAttribute("href"))return e;e=kt(e)}}function De(e,t,n){let r;try{if(r=new URL(e instanceof SVGAElement?e.href.baseVal:e.href,document.baseURI),n&&r.hash.match(/^#[^/]/)){const o=location.hash.split("#")[1]||"/";r.hash=`#${o}${r.hash}`}}catch{}const a=e instanceof SVGAElement?e.target.baseVal:e.target,s=!r||!!a||ke(r,t,n)||(e.getAttribute("rel")||"").split(/\s+/).includes("external"),i=(r==null?void 0:r.origin)===he&&e.hasAttribute("download");return{url:r,external:s,target:a,download:i}}function we(e){let t=null,n=null,r=null,a=null,s=null,i=null,o=e;for(;o&&o!==document.documentElement;)r===null&&(r=M(o,"preload-code")),a===null&&(a=M(o,"preload-data")),t===null&&(t=M(o,"keepfocus")),n===null&&(n=M(o,"noscroll")),s===null&&(s=M(o,"reload")),i===null&&(i=M(o,"replacestate")),o=kt(o);function c(l){switch(l){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:it[r??"off"],preload_data:it[a??"off"],keepfocus:c(t),noscroll:c(n),reload:c(s),replace_state:c(i)}}function ct(e){const t=Me(e);let n=!0;function r(){n=!0,t.update(i=>i)}function a(i){n=!1,t.set(i)}function s(i){let o;return t.subscribe(c=>{(o===void 0||n&&c!==o)&&i(o=c)})}return{notify:r,set:a,subscribe:s}}const Rt={v:()=>{}};function fn(){const{set:e,subscribe:t}=Me(!1);let n;async function r(){clearTimeout(n);try{const a=await fetch(`${sn}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!a.ok)return!1;const i=(await a.json()).version!==cn;return i&&(e(!0),Rt.v(),clearTimeout(n)),i}catch{return!1}}return{subscribe:t,check:r}}function ke(e,t,n){return e.origin!==he||!e.pathname.startsWith(t)?!0:n?!(e.pathname===t+"/"||e.pathname===t+"/index.html"||e.protocol==="file:"&&e.pathname.replace(/\/[^/]+\.html?$/,"")===t):!1}function qn(e){}function Gn(e){const t=new DataView(e);let n="";for(let r=0;r<e.byteLength;r++)n+=String.fromCharCode(t.getUint8(r));return dn(n)}function lt(e){const t=un(e),n=new ArrayBuffer(t.length),r=new DataView(n);for(let a=0;a<n.byteLength;a++)r.setUint8(a,t.charCodeAt(a));return n}const Ut="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function un(e){e.length%4===0&&(e=e.replace(/==?$/,""));let t="",n=0,r=0;for(let a=0;a<e.length;a++)n<<=6,n|=Ut.indexOf(e[a]),r+=6,r===24&&(t+=String.fromCharCode((n&16711680)>>16),t+=String.fromCharCode((n&65280)>>8),t+=String.fromCharCode(n&255),n=r=0);return r===12?(n>>=4,t+=String.fromCharCode(n)):r===18&&(n>>=2,t+=String.fromCharCode((n&65280)>>8),t+=String.fromCharCode(n&255)),t}function dn(e){let t="";for(let n=0;n<e.length;n+=3){const r=[void 0,void 0,void 0,void 0];r[0]=e.charCodeAt(n)>>2,r[1]=(e.charCodeAt(n)&3)<<4,e.length>n+1&&(r[1]|=e.charCodeAt(n+1)>>4,r[2]=(e.charCodeAt(n+1)&15)<<2),e.length>n+2&&(r[2]|=e.charCodeAt(n+2)>>6,r[3]=e.charCodeAt(n+2)&63);for(let a=0;a<r.length;a++)typeof r[a]>"u"?t+="=":t+=Ut[r[a]]}return t}const hn=-1,pn=-2,gn=-3,mn=-4,wn=-5,yn=-6;function Hn(e,t){return It(JSON.parse(e),t)}function It(e,t){if(typeof e=="number")return a(e,!0);if(!Array.isArray(e)||e.length===0)throw new Error("Invalid input");const n=e,r=Array(n.length);function a(s,i=!1){if(s===hn)return;if(s===gn)return NaN;if(s===mn)return 1/0;if(s===wn)return-1/0;if(s===yn)return-0;if(i)throw new Error("Invalid input");if(s in r)return r[s];const o=n[s];if(!o||typeof o!="object")r[s]=o;else if(Array.isArray(o))if(typeof o[0]=="string"){const c=o[0],l=t==null?void 0:t[c];if(l)return r[s]=l(a(o[1]));switch(c){case"Date":r[s]=new Date(o[1]);break;case"Set":const h=new Set;r[s]=h;for(let f=1;f<o.length;f+=1)h.add(a(o[f]));break;case"Map":const u=new Map;r[s]=u;for(let f=1;f<o.length;f+=2)u.set(a(o[f]),a(o[f+1]));break;case"RegExp":r[s]=new RegExp(o[1],o[2]);break;case"Object":r[s]=Object(o[1]);break;case"BigInt":r[s]=BigInt(o[1]);break;case"null":const w=Object.create(null);r[s]=w;for(let f=1;f<o.length;f+=2)w[o[f]]=a(o[f+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const f=globalThis[c],m=o[1],p=lt(m),d=new f(p);r[s]=d;break}case"ArrayBuffer":{const f=o[1],m=lt(f);r[s]=m;break}default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(o.length);r[s]=c;for(let l=0;l<o.length;l+=1){const h=o[l];h!==pn&&(c[l]=a(h))}}else{const c={};r[s]=c;for(const l in o){const h=o[l];c[l]=a(h)}}return r[s]}return a(0)}const Lt=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);[...Lt];const _n=new Set([...Lt]);[..._n];function vn(e){return e.filter(t=>t!=null)}class Ee{constructor(t,n){this.status=t,typeof n=="string"?this.body={message:n}:n?this.body=n:this.body={message:`Error: ${t}`}}toString(){return JSON.stringify(this.body)}}class Ke{constructor(t,n){this.status=t,this.location=n}}class We extends Error{constructor(t,n,r){super(r),this.status=t,this.text=n}}const bn="x-sveltekit-invalidated",An="x-sveltekit-trailing-slash";function ye(e){return e instanceof Ee||e instanceof We?e.status:500}function Sn(e){return e instanceof We?e.text:"Internal Error"}let k,Q,Pe;const kn=je.toString().includes("$$")||/function \w+\(\) \{\}/.test(je.toString());var re,ae,oe,se,ie,ce,le,fe,wt,ue,yt,de,_t;kn?(k={data:{},form:null,error:null,params:{},route:{id:null},state:{},status:-1,url:new URL("https://example.com")},Q={current:null},Pe={current:!1}):(k=new(wt=class{constructor(){C(this,re,P({}));C(this,ae,P(null));C(this,oe,P(null));C(this,se,P({}));C(this,ie,P({id:null}));C(this,ce,P({}));C(this,le,P(-1));C(this,fe,P(new URL("https://example.com")))}get data(){return O(A(this,re))}set data(t){N(A(this,re),t)}get form(){return O(A(this,ae))}set form(t){N(A(this,ae),t)}get error(){return O(A(this,oe))}set error(t){N(A(this,oe),t)}get params(){return O(A(this,se))}set params(t){N(A(this,se),t)}get route(){return O(A(this,ie))}set route(t){N(A(this,ie),t)}get state(){return O(A(this,ce))}set state(t){N(A(this,ce),t)}get status(){return O(A(this,le))}set status(t){N(A(this,le),t)}get url(){return O(A(this,fe))}set url(t){N(A(this,fe),t)}},re=new WeakMap,ae=new WeakMap,oe=new WeakMap,se=new WeakMap,ie=new WeakMap,ce=new WeakMap,le=new WeakMap,fe=new WeakMap,wt),Q=new(yt=class{constructor(){C(this,ue,P(null))}get current(){return O(A(this,ue))}set current(t){N(A(this,ue),t)}},ue=new WeakMap,yt),Pe=new(_t=class{constructor(){C(this,de,P(!1))}get current(){return O(A(this,de))}set current(t){N(A(this,de),t)}},de=new WeakMap,_t),Rt.v=()=>Pe.current=!0);function Ye(e){Object.assign(k,e)}const En="/__data.json",Rn=".html__data.json";function Un(e){return e.endsWith(".html")?e.replace(/\.html$/,Rn):e.replace(/\/$/,"")+En}const In=new Set(["icon","shortcut icon","apple-touch-icon"]),B=vt(At)??{},ee=vt(bt)??{},D={url:ct({}),page:ct({}),navigating:Me(null),updated:fn()};function Je(e){B[e]=He()}function Ln(e,t){let n=e+1;for(;B[n];)delete B[n],n+=1;for(n=t+1;ee[n];)delete ee[n],n+=1}function K(e){return location.href=e.href,new Promise(()=>{})}async function xt(){if("serviceWorker"in navigator){const e=await navigator.serviceWorker.getRegistration(T||"/");e&&await e.update()}}function ft(){}let ze,Ve,_e,j,Fe,S;const ve=[],be=[];let L=null;const me=new Map,Xe=new Set,xn=new Set,J=new Set;let y={branch:[],error:null,url:null},Ze=!1,Ae=!1,ut=!0,te=!1,W=!1,Tt=!1,Re=!1,G,R,x,$;const z=new Set;let Oe;async function Jn(e,t,n){var a,s,i,o;document.URL!==location.href&&(location.href=location.href),S=e,await((s=(a=e.hooks).init)==null?void 0:s.call(a)),ze=on(e),j=document.documentElement,Fe=t,Ve=e.nodes[0],_e=e.nodes[1],Ve(),_e(),R=(i=history.state)==null?void 0:i[q],x=(o=history.state)==null?void 0:o[Z],R||(R=x=Date.now(),history.replaceState({...history.state,[q]:R,[Z]:x},""));const r=B[R];r&&(history.scrollRestoration="manual",scrollTo(r.x,r.y)),n?await Dn(Fe,n):await X({type:"enter",url:Ge(S.hash?Vn(new URL(location.href)):location.href),replace_state:!0}),$n()}async function Ct(){if(await(Oe||(Oe=Promise.resolve())),!Oe)return;Oe=null;const e=$={},t=await ge(y.url,!0);L=null;const n=t&&await tt(t);if(!(!n||e!==$)){if(n.type==="redirect")return pe(new URL(n.location,y.url).href,{},1,e);n.props.page&&Object.assign(k,n.props.page),y=n.state,Pt(),G.$set(n.props),Ye(n.props.page)}}function Pt(){ve.length=0,Re=!1}function Ot(e){be.some(t=>t==null?void 0:t.snapshot)&&(ee[e]=be.map(t=>{var n;return(n=t==null?void 0:t.snapshot)==null?void 0:n.capture()}))}function Nt(e){var t;(t=ee[e])==null||t.forEach((n,r)=>{var a,s;(s=(a=be[r])==null?void 0:a.snapshot)==null||s.restore(n)})}function dt(){Je(R),st(At,B),Ot(x),st(bt,ee)}async function pe(e,t,n,r){return X({type:"goto",url:Ge(e),keepfocus:t.keepFocus,noscroll:t.noScroll,replace_state:t.replaceState,state:t.state,redirect_count:n,nav_token:r,accept:()=>{t.invalidateAll&&(Re=!0),t.invalidate&&t.invalidate.forEach(Bt)}})}async function Tn(e){if(e.id!==(L==null?void 0:L.id)){const t={};z.add(t),L={id:e.id,token:t,promise:tt({...e,preload:t}).then(n=>(z.delete(t),n.type==="loaded"&&n.state.error&&(L=null),n))}}return L.promise}async function Ne(e){var n;const t=(n=await ge(e,!1))==null?void 0:n.route;t&&await Promise.all([...t.layouts,t.leaf].map(r=>r==null?void 0:r[1]()))}function jt(e,t,n){var a;y=e.state;const r=document.querySelector("style[data-sveltekit]");if(r&&r.remove(),Object.assign(k,e.props.page),G=new S.root({target:t,props:{...e.props,stores:D,components:be},hydrate:n,sync:!1}),Nt(x),n){const s={from:null,to:{params:y.params,route:{id:((a=y.route)==null?void 0:a.id)??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};J.forEach(i=>i(s))}Ae=!0}function ne({url:e,params:t,branch:n,status:r,error:a,route:s,form:i}){let o="never";if(T&&(e.pathname===T||e.pathname===T+"/"))o="always";else for(const f of n)(f==null?void 0:f.slash)!==void 0&&(o=f.slash);e.pathname=Ht(e.pathname,o),e.search=e.search;const c={type:"loaded",state:{url:e,params:t,branch:n,error:a,route:s},props:{constructors:vn(n).map(f=>f.node.component),page:Ie(k)}};i!==void 0&&(c.props.form=i);let l={},h=!k,u=0;for(let f=0;f<Math.max(n.length,y.branch.length);f+=1){const m=n[f],p=y.branch[f];(m==null?void 0:m.data)!==(p==null?void 0:p.data)&&(h=!0),m&&(l={...l,...m.data},h&&(c.props[`data_${u}`]=l),u+=1)}return(!y.url||e.href!==y.url.href||y.error!==a||i!==void 0&&i!==k.form||h)&&(c.props.page={error:a,params:t,route:{id:(s==null?void 0:s.id)??null},state:{},status:r,url:new URL(e),form:i??null,data:h?l:k.data}),c}async function Qe({loader:e,parent:t,url:n,params:r,route:a,server_data_node:s}){var h,u,w;let i=null,o=!0;const c={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},l=await e();if((h=l.universal)!=null&&h.load){let f=function(...p){for(const d of p){const{href:v}=new URL(d,n);c.dependencies.add(v)}};const m={route:new Proxy(a,{get:(p,d)=>(o&&(c.route=!0),p[d])}),params:new Proxy(r,{get:(p,d)=>(o&&c.params.add(d),p[d])}),data:(s==null?void 0:s.data)??null,url:Yt(n,()=>{o&&(c.url=!0)},p=>{o&&c.search_params.add(p)},S.hash),async fetch(p,d){p instanceof Request&&(d={body:p.method==="GET"||p.method==="HEAD"?void 0:await p.blob(),cache:p.cache,credentials:p.credentials,headers:[...p.headers].length?p.headers:void 0,integrity:p.integrity,keepalive:p.keepalive,method:p.method,mode:p.mode,redirect:p.redirect,referrer:p.referrer,referrerPolicy:p.referrerPolicy,signal:p.signal,...d});const{resolved:v,promise:U}=$t(p,d,n);return o&&f(v.href),U},setHeaders:()=>{},depends:f,parent(){return o&&(c.parent=!0),t()},untrack(p){o=!1;try{return p()}finally{o=!0}}};i=await l.universal.load.call(null,m)??null}return{node:l,loader:e,server:s,universal:(u=l.universal)!=null&&u.load?{type:"data",data:i,uses:c}:null,data:i??(s==null?void 0:s.data)??null,slash:((w=l.universal)==null?void 0:w.trailingSlash)??(s==null?void 0:s.slash)}}function $t(e,t,n){let r=e instanceof Request?e.url:e;const a=new URL(r,n);a.origin===n.origin&&(r=a.href.slice(n.origin.length));const s=Ae?Qt(r,a.href,t):Zt(r,t);return{resolved:a,promise:s}}function ht(e,t,n,r,a,s){if(Re)return!0;if(!a)return!1;if(a.parent&&e||a.route&&t||a.url&&n)return!0;for(const i of a.search_params)if(r.has(i))return!0;for(const i of a.params)if(s[i]!==y.params[i])return!0;for(const i of a.dependencies)if(ve.some(o=>o(new URL(i))))return!0;return!1}function et(e,t){return(e==null?void 0:e.type)==="data"?e:(e==null?void 0:e.type)==="skip"?t??null:null}function Cn(e,t){if(!e)return new Set(t.searchParams.keys());const n=new Set([...e.searchParams.keys(),...t.searchParams.keys()]);for(const r of n){const a=e.searchParams.getAll(r),s=t.searchParams.getAll(r);a.every(i=>s.includes(i))&&s.every(i=>a.includes(i))&&n.delete(r)}return n}function pt({error:e,url:t,route:n,params:r}){return{type:"loaded",state:{error:e,url:t,route:n,params:r,branch:[]},props:{page:Ie(k),constructors:[]}}}async function tt({id:e,invalidating:t,url:n,params:r,route:a,preload:s}){if((L==null?void 0:L.id)===e)return z.delete(L.token),L.promise;const{errors:i,layouts:o,leaf:c}=a,l=[...o,c];i.forEach(g=>g==null?void 0:g().catch(()=>{})),l.forEach(g=>g==null?void 0:g[1]().catch(()=>{}));let h=null;const u=y.url?e!==Se(y.url):!1,w=y.route?a.id!==y.route.id:!1,f=Cn(y.url,n);let m=!1;const p=l.map((g,_)=>{var V;const b=y.branch[_],E=!!(g!=null&&g[0])&&((b==null?void 0:b.loader)!==g[1]||ht(m,w,u,f,(V=b.server)==null?void 0:V.uses,r));return E&&(m=!0),E});if(p.some(Boolean)){try{h=await Mt(n,p)}catch(g){const _=await H(g,{url:n,params:r,route:{id:e}});return z.has(s)?pt({error:_,url:n,params:r,route:a}):Ue({status:ye(g),error:_,url:n,route:a})}if(h.type==="redirect")return h}const d=h==null?void 0:h.nodes;let v=!1;const U=l.map(async(g,_)=>{var Le;if(!g)return;const b=y.branch[_],E=d==null?void 0:d[_];if((!E||E.type==="skip")&&g[1]===(b==null?void 0:b.loader)&&!ht(v,w,u,f,(Le=b.universal)==null?void 0:Le.uses,r))return b;if(v=!0,(E==null?void 0:E.type)==="error")throw E;return Qe({loader:g[1],url:n,params:r,route:a,parent:async()=>{var at;const rt={};for(let xe=0;xe<_;xe+=1)Object.assign(rt,(at=await U[xe])==null?void 0:at.data);return rt},server_data_node:et(E===void 0&&g[0]?{type:"skip"}:E??null,g[0]?b==null?void 0:b.server:void 0)})});for(const g of U)g.catch(()=>{});const I=[];for(let g=0;g<l.length;g+=1)if(l[g])try{I.push(await U[g])}catch(_){if(_ instanceof Ke)return{type:"redirect",location:_.location};if(z.has(s))return pt({error:await H(_,{params:r,url:n,route:{id:a.id}}),url:n,params:r,route:a});let b=ye(_),E;if(d!=null&&d.includes(_))b=_.status??b,E=_.error;else if(_ instanceof Ee)E=_.body;else{if(await D.updated.check())return await xt(),await K(n);E=await H(_,{params:r,url:n,route:{id:a.id}})}const V=await Dt(g,I,i);return V?ne({url:n,params:r,branch:I.slice(0,V.idx).concat(V.node),status:b,error:E,route:a}):await Ft(n,{id:a.id},E,b)}else I.push(void 0);return ne({url:n,params:r,branch:I,status:200,error:null,route:a,form:t?void 0:null})}async function Dt(e,t,n){for(;e--;)if(n[e]){let r=e;for(;!t[r];)r-=1;try{return{idx:r+1,node:{node:await n[e](),loader:n[e],data:{},server:null,universal:null}}}catch{continue}}}async function Ue({status:e,error:t,url:n,route:r}){const a={};let s=null;if(S.server_loads[0]===0)try{const o=await Mt(n,[!0]);if(o.type!=="data"||o.nodes[0]&&o.nodes[0].type!=="data")throw 0;s=o.nodes[0]??null}catch{(n.origin!==he||n.pathname!==location.pathname||Ze)&&await K(n)}try{const o=await Qe({loader:Ve,url:n,params:a,route:r,parent:()=>Promise.resolve({}),server_data_node:et(s)}),c={node:await _e(),loader:_e,universal:null,server:null,data:null};return ne({url:n,params:a,branch:[o,c],status:e,error:t,route:null})}catch(o){if(o instanceof Ke)return pe(new URL(o.location,location.href),{},0);throw o}}async function Pn(e){const t=e.href;if(me.has(t))return me.get(t);let n;try{const r=(async()=>{let a=await S.hooks.reroute({url:new URL(e),fetch:async(s,i)=>$t(s,i,e).promise})??e;if(typeof a=="string"){const s=new URL(e);S.hash?s.hash=a:s.pathname=a,a=s}return a})();me.set(t,r),n=await r}catch{me.delete(t);return}return n}async function ge(e,t){if(e&&!ke(e,T,S.hash)){const n=await Pn(e);if(!n)return;const r=On(n);for(const a of ze){const s=a.exec(r);if(s)return{id:Se(e),invalidating:t,route:a,params:Wt(s),url:e}}}}function On(e){return Kt(S.hash?e.hash.replace(/^#/,"").replace(/[?#].+/,""):e.pathname.slice(T.length))||"/"}function Se(e){return(S.hash?e.hash.replace(/^#/,""):e.pathname)+e.search}function Vt({url:e,type:t,intent:n,delta:r}){let a=!1;const s=nt(y,n,e,t);r!==void 0&&(s.navigation.delta=r);const i={...s.navigation,cancel:()=>{a=!0,s.reject(new Error("navigation cancelled"))}};return te||Xe.forEach(o=>o(i)),a?null:s}async function X({type:e,url:t,popped:n,keepfocus:r,noscroll:a,replace_state:s,state:i={},redirect_count:o=0,nav_token:c={},accept:l=ft,block:h=ft}){const u=$;$=c;const w=await ge(t,!1),f=e==="enter"?nt(y,w,t,e):Vt({url:t,type:e,delta:n==null?void 0:n.delta,intent:w});if(!f){h(),$===c&&($=u);return}const m=R,p=x;l(),te=!0,Ae&&f.navigation.type!=="enter"&&D.navigating.set(Q.current=f.navigation);let d=w&&await tt(w);if(!d){if(ke(t,T,S.hash))return await K(t);d=await Ft(t,{id:null},await H(new We(404,"Not Found",`Not found: ${t.pathname}`),{url:t,params:{},route:{id:null}}),404)}if(t=(w==null?void 0:w.url)||t,$!==c)return f.reject(new Error("navigation aborted")),!1;if(d.type==="redirect")if(o>=20)d=await Ue({status:500,error:await H(new Error("Redirect loop"),{url:t,params:{},route:{id:null}}),url:t,route:{id:null}});else return await pe(new URL(d.location,t).href,{},o+1,c),!1;else d.props.page.status>=400&&await D.updated.check()&&(await xt(),await K(t));if(Pt(),Je(m),Ot(p),d.props.page.url.pathname!==t.pathname&&(t.pathname=d.props.page.url.pathname),i=n?n.state:i,!n){const g=s?0:1,_={[q]:R+=g,[Z]:x+=g,[St]:i};(s?history.replaceState:history.pushState).call(history,_,"",t),s||Ln(R,x)}if(L=null,d.props.page.state=i,Ae){y=d.state,d.props.page&&(d.props.page.url=t);const g=(await Promise.all(Array.from(xn,_=>_(f.navigation)))).filter(_=>typeof _=="function");if(g.length>0){let _=function(){g.forEach(b=>{J.delete(b)})};g.push(_),g.forEach(b=>{J.add(b)})}G.$set(d.props),Ye(d.props.page),Tt=!0}else jt(d,Fe,!1);const{activeElement:v}=document;await $e();const U=n?n.scroll:a?He():null;if(ut){const g=t.hash&&document.getElementById(decodeURIComponent(S.hash?t.hash.split("#")[2]??"":t.hash.slice(1)));U?scrollTo(U.x,U.y):g?g.scrollIntoView():scrollTo(0,0)}const I=document.activeElement!==v&&document.activeElement!==document.body;!r&&!I&&Be(),ut=!0,d.props.page&&Object.assign(k,d.props.page),te=!1,e==="popstate"&&Nt(x),f.fulfil(void 0),J.forEach(g=>g(f.navigation)),D.navigating.set(Q.current=null)}async function Ft(e,t,n,r){return e.origin===he&&e.pathname===location.pathname&&!Ze?await Ue({status:r,error:n,url:e,route:t}):await K(e)}function Nn(){let e,t,n;j.addEventListener("mousemove",o=>{const c=o.target;clearTimeout(e),e=setTimeout(()=>{s(c,F.hover)},20)});function r(o){o.defaultPrevented||s(o.composedPath()[0],F.tap)}j.addEventListener("mousedown",r),j.addEventListener("touchstart",r,{passive:!0});const a=new IntersectionObserver(o=>{for(const c of o)c.isIntersecting&&(Ne(new URL(c.target.href)),a.unobserve(c.target))},{threshold:0});async function s(o,c){const l=Et(o,j),h=l===t&&c>=n;if(!l||h)return;const{url:u,external:w,download:f}=De(l,T,S.hash);if(w||f)return;const m=we(l),p=u&&Se(y.url)===Se(u);if(!(m.reload||p))if(c<=m.preload_data){t=l,n=F.tap;const d=await ge(u,!1);if(!d)return;Tn(d)}else c<=m.preload_code&&(t=l,n=c,Ne(u))}function i(){a.disconnect();for(const o of j.querySelectorAll("a")){const{url:c,external:l,download:h}=De(o,T,S.hash);if(l||h)continue;const u=we(o);u.reload||(u.preload_code===F.viewport&&a.observe(o),u.preload_code===F.eager&&Ne(c))}}J.add(i),i()}function H(e,t){if(e instanceof Ee)return e.body;const n=ye(e),r=Sn(e);return S.hooks.handleError({error:e,event:t,status:n,message:r})??{message:r}}function jn(e,t){je(()=>(e.add(t),()=>{e.delete(t)}))}function zn(e){jn(Xe,e)}function Xn(e,t={}){return e=new URL(Ge(e)),e.origin!==he?Promise.reject(new Error("goto: invalid URL")):pe(e,t,0)}function Zn(e){return Bt(e),Ct()}function Bt(e){if(typeof e=="function")ve.push(e);else{const{href:t}=new URL(e,location.href);ve.push(n=>n.href===t)}}function Qn(){return Re=!0,Ct()}async function er(e){if(e.type==="error"){const t=new URL(location.href),{branch:n,route:r}=y;if(!r)return;const a=await Dt(y.branch.length,n,r.errors);if(a){const s=ne({url:t,params:y.params,branch:n.slice(0,a.idx).concat(a.node),status:e.status??500,error:e.error,route:r});y=s.state,G.$set(s.props),Ye(s.props.page),$e().then(Be)}}else e.type==="redirect"?await pe(e.location,{invalidateAll:!0},0):(k.form=e.data,k.status=e.status,G.$set({form:null,page:Ie(k)}),await $e(),G.$set({form:e.data}),e.type==="success"&&Be())}function $n(){var t;history.scrollRestoration="manual",addEventListener("beforeunload",n=>{let r=!1;if(dt(),!te){const a=nt(y,void 0,null,"leave"),s={...a.navigation,cancel:()=>{r=!0,a.reject(new Error("navigation cancelled"))}};Xe.forEach(i=>i(s))}r?(n.preventDefault(),n.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&dt()}),(t=navigator.connection)!=null&&t.saveData||Nn(),j.addEventListener("click",async n=>{if(n.button||n.which!==1||n.metaKey||n.ctrlKey||n.shiftKey||n.altKey||n.defaultPrevented)return;const r=Et(n.composedPath()[0],j);if(!r)return;const{url:a,external:s,target:i,download:o}=De(r,T,S.hash);if(!a)return;if(i==="_parent"||i==="_top"){if(window.parent!==window)return}else if(i&&i!=="_self")return;const c=we(r);if(!(r instanceof SVGAElement)&&a.protocol!==location.protocol&&!(a.protocol==="https:"||a.protocol==="http:")||o)return;const[h,u]=(S.hash?a.hash.replace(/^#/,""):a.href).split("#"),w=h===Te(location);if(s||c.reload&&(!w||!u)){Vt({url:a,type:"link"})?te=!0:n.preventDefault();return}if(u!==void 0&&w){const[,f]=y.url.href.split("#");if(f===u){if(n.preventDefault(),u===""||u==="top"&&r.ownerDocument.getElementById("top")===null)window.scrollTo({top:0});else{const m=r.ownerDocument.getElementById(decodeURIComponent(u));m&&(m.scrollIntoView(),m.focus())}return}if(W=!0,Je(R),e(a),!c.replace_state)return;W=!1}n.preventDefault(),await new Promise(f=>{requestAnimationFrame(()=>{setTimeout(f,0)}),setTimeout(f,100)}),await X({type:"link",url:a,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??a.href===location.href})}),j.addEventListener("submit",n=>{if(n.defaultPrevented)return;const r=HTMLFormElement.prototype.cloneNode.call(n.target),a=n.submitter;if(((a==null?void 0:a.formTarget)||r.target)==="_blank"||((a==null?void 0:a.formMethod)||r.method)!=="get")return;const o=new URL((a==null?void 0:a.hasAttribute("formaction"))&&(a==null?void 0:a.formAction)||r.action);if(ke(o,T,!1))return;const c=n.target,l=we(c);if(l.reload)return;n.preventDefault(),n.stopPropagation();const h=new FormData(c),u=a==null?void 0:a.getAttribute("name");u&&h.append(u,(a==null?void 0:a.getAttribute("value"))??""),o.search=new URLSearchParams(h).toString(),X({type:"form",url:o,keepfocus:l.keepfocus,noscroll:l.noscroll,replace_state:l.replace_state??o.href===location.href})}),addEventListener("popstate",async n=>{var r;if((r=n.state)!=null&&r[q]){const a=n.state[q];if($={},a===R)return;const s=B[a],i=n.state[St]??{},o=new URL(n.state[ln]??location.href),c=n.state[Z],l=y.url?Te(location)===Te(y.url):!1;if(c===x&&(Tt||l)){i!==k.state&&(k.state=i),e(o),B[R]=He(),s&&scrollTo(s.x,s.y),R=a;return}const u=a-R;await X({type:"popstate",url:o,popped:{state:i,scroll:s,delta:u},accept:()=>{R=a,x=c},block:()=>{history.go(-u)},nav_token:$})}else if(!W){const a=new URL(location.href);e(a),S.hash&&location.reload()}}),addEventListener("hashchange",()=>{W&&(W=!1,history.replaceState({...history.state,[q]:++R,[Z]:x},"",location.href))});for(const n of document.querySelectorAll("link"))In.has(n.rel)&&(n.href=n.href);addEventListener("pageshow",n=>{n.persisted&&D.navigating.set(Q.current=null)});function e(n){y.url=k.url=n,D.page.set(Ie(k)),D.page.notify()}}async function Dn(e,{status:t=200,error:n,node_ids:r,params:a,route:s,server_route:i,data:o,form:c}){Ze=!0;const l=new URL(location.href);let h;({params:a={},route:s={id:null}}=await ge(l,!1)||{}),h=ze.find(({id:f})=>f===s.id);let u,w=!0;try{const f=r.map(async(p,d)=>{const v=o[d];return v!=null&&v.uses&&(v.uses=qt(v.uses)),Qe({loader:S.nodes[p],url:l,params:a,route:s,parent:async()=>{const U={};for(let I=0;I<d;I+=1)Object.assign(U,(await f[I]).data);return U},server_data_node:et(v)})}),m=await Promise.all(f);if(h){const p=h.layouts;for(let d=0;d<p.length;d++)p[d]||m.splice(d,0,void 0)}u=ne({url:l,params:a,branch:m,status:t,error:n,form:c,route:h??null})}catch(f){if(f instanceof Ke){await K(new URL(f.location,location.href));return}u=await Ue({status:ye(f),error:await H(f,{url:l,params:a,route:s}),url:l,route:s}),e.textContent="",w=!1}u.props.page&&(u.props.page.state={}),jt(u,e,w)}async function Mt(e,t){var s;const n=new URL(e);n.pathname=Un(e.pathname),e.pathname.endsWith("/")&&n.searchParams.append(An,"1"),n.searchParams.append(bn,t.map(i=>i?"1":"0").join(""));const r=window.fetch,a=await r(n.href,{});if(!a.ok){let i;throw(s=a.headers.get("content-type"))!=null&&s.includes("application/json")?i=await a.json():a.status===404?i="Not Found":a.status===500&&(i="Internal Error"),new Ee(a.status,i)}return new Promise(async i=>{var w;const o=new Map,c=a.body.getReader(),l=new TextDecoder;function h(f){return It(f,{...S.decoders,Promise:m=>new Promise((p,d)=>{o.set(m,{fulfil:p,reject:d})})})}let u="";for(;;){const{done:f,value:m}=await c.read();if(f&&!u)break;for(u+=!m&&u?`
`:l.decode(m,{stream:!0});;){const p=u.indexOf(`
`);if(p===-1)break;const d=JSON.parse(u.slice(0,p));if(u=u.slice(p+1),d.type==="redirect")return i(d);if(d.type==="data")(w=d.nodes)==null||w.forEach(v=>{(v==null?void 0:v.type)==="data"&&(v.uses=qt(v.uses),v.data=h(v.data))}),i(d);else if(d.type==="chunk"){const{id:v,data:U,error:I}=d,g=o.get(v);o.delete(v),I?g.reject(h(I)):g.fulfil(h(U))}}}})}function qt(e){return{dependencies:new Set((e==null?void 0:e.dependencies)??[]),params:new Set((e==null?void 0:e.params)??[]),parent:!!(e!=null&&e.parent),route:!!(e!=null&&e.route),url:!!(e!=null&&e.url),search_params:new Set((e==null?void 0:e.search_params)??[])}}function Be(){const e=document.querySelector("[autofocus]");if(e)e.focus();else{const t=document.body,n=t.getAttribute("tabindex");t.tabIndex=-1,t.focus({preventScroll:!0,focusVisible:!1}),n!==null?t.setAttribute("tabindex",n):t.removeAttribute("tabindex");const r=getSelection();if(r&&r.type!=="None"){const a=[];for(let s=0;s<r.rangeCount;s+=1)a.push(r.getRangeAt(s));setTimeout(()=>{if(r.rangeCount===a.length){for(let s=0;s<r.rangeCount;s+=1){const i=a[s],o=r.getRangeAt(s);if(i.commonAncestorContainer!==o.commonAncestorContainer||i.startContainer!==o.startContainer||i.endContainer!==o.endContainer||i.startOffset!==o.startOffset||i.endOffset!==o.endOffset)return}r.removeAllRanges()}})}}}function nt(e,t,n,r){var c,l;let a,s;const i=new Promise((h,u)=>{a=h,s=u});return i.catch(()=>{}),{navigation:{from:{params:e.params,route:{id:((c=e.route)==null?void 0:c.id)??null},url:e.url},to:n&&{params:(t==null?void 0:t.params)??null,route:{id:((l=t==null?void 0:t.route)==null?void 0:l.id)??null},url:n},willUnload:!t,type:r,complete:i},fulfil:a,reject:s}}function Ie(e){return{data:e.data,error:e.error,form:e.form,params:e.params,route:e.route,state:e.state,status:e.status,url:e.url}}function Vn(e){const t=new URL(e);return t.hash=decodeURIComponent(e.hash),t}export{pn as H,gn as N,mn as P,hn as U,Zn as a,T as b,wn as c,yn as d,Gn as e,S as f,Xn as g,er as h,Qn as i,zn as j,Jn as k,qn as l,Hn as p,D as s};
