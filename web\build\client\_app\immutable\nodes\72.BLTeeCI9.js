import{f,t as k,a as r,c as Ae}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as vt}from"../chunks/nZgk9enP.js";import{f as A,n as L,t as G,s as t,c as l,r as o,p as _e,g as e,e as Z,a as $e,bb as ct,d as i,m as J,x as ft,aL as He}from"../chunks/CGmarHxI.js";import{s as R}from"../chunks/CIt1g2O9.js";import{i as ae}from"../chunks/u21ee2wt.js";import{s as ue,c as ve}from"../chunks/B-Xjo-Yt.js";import{i as pe}from"../chunks/BIEMS98f.js";import{F as ne,a as Le}from"../chunks/iTBjRg9v.js";import{S as ke}from"../chunks/BPvdPoic.js";import{A as we,a as Ce,b as Re}from"../chunks/DLEhONWn.js";import{p as V}from"../chunks/Btcx8l8F.js";import{C as Oe}from"../chunks/DuGukytH.js";import{C as ze}from"../chunks/Cdn-N1RY.js";import{C as Ye}from"../chunks/GwmmX_iF.js";import{C as qe}from"../chunks/D50jIuLr.js";import{e as Fe,i as Se}from"../chunks/C3w0v0gR.js";import{A as mt,a as gt,b as _t,c as $t}from"../chunks/BPr9JIwg.js";import{B as tt}from"../chunks/DaBofrVv.js";import{C as rt}from"../chunks/BkJY4La4.js";import{C as at}from"../chunks/DETxXRrJ.js";import{P as pt}from"../chunks/DrGkVJ95.js";import{B as Pe}from"../chunks/B1K98fMG.js";import{r as ht,t as xt}from"../chunks/bK-q0z-2.js";import{R as yt,a as bt}from"../chunks/tdzGgazS.js";import{D as Pt}from"../chunks/CodWuqwu.js";import{T as wt}from"../chunks/C33xR25f.js";import{D as Ct,a as Rt,b as Ft,c as St}from"../chunks/CKh8VGVX.js";import{C as Je}from"../chunks/CKg8MWp_.js";import{C as st}from"../chunks/DW7T7T22.js";import{L as We}from"../chunks/BvvicRXk.js";import{R as Ke,S as Qe,a as Xe,b as Ze}from"../chunks/CGK0g3x_.js";import{I as Ut}from"../chunks/DMTMHyMa.js";import{S as et}from"../chunks/B2lQHLf_.js";import{R as Lt}from"../chunks/qwsZpUIl.js";var kt=f('<p class="text-muted-foreground text-xs"> </p>'),At=f('<p class="text-destructive text-xs"> </p>'),Nt=f('<div class="text-2xl font-bold"> </div> <!> <!>',1),It=f("<!> <!>",1);function be(y,v){let d=V(v,"title",8),m=V(v,"value",8),$=V(v,"subtitle",8,""),F=V(v,"warning",8,""),b=V(v,"showWarning",8,!1);Oe(y,{children:(x,c)=>{var a=It(),P=A(a);Ye(P,{class:"pb-2",children:(D,M)=>{qe(D,{class:"text-sm font-medium",children:(p,w)=>{L();var C=k();G(()=>R(C,d())),r(p,C)},$$slots:{default:!0}})},$$slots:{default:!0}});var T=t(P,2);ze(T,{children:(D,M)=>{var p=Nt(),w=A(p),C=l(w,!0);o(w);var O=t(w,2);{var B=u=>{var g=kt(),n=l(g,!0);o(g),G(()=>R(n,$())),r(u,g)};ae(O,u=>{$()&&u(B)})}var S=t(O,2);{var N=u=>{var g=At(),n=l(g,!0);o(g),G(()=>R(n,F())),r(u,g)};ae(S,u=>{b()&&F()&&u(N)})}G(()=>R(C,m()!==void 0?m():"N/A")),r(D,p)},$$slots:{default:!0}}),r(x,a)},$$slots:{default:!0}})}var Tt=f('<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"><!> <!> <!> <!> <!></div>');function Dt(y,v){_e(v,!1);let d=V(v,"usageSummary",8),m=V(v,"resumeUsage",8,null);pe();var $=Tt(),F=l($);be(F,{title:"Total Features",get value(){return d().totalFeatures}});var b=t(F,2);const x=Z(()=>d().featuresUsed!==void 0&&d().totalFeatures!==void 0&&d().totalFeatures>0?`${Math.round(d().featuresUsed/d().totalFeatures*100)}% of total`:"");be(b,{title:"Features Used",get value(){return d().featuresUsed},get subtitle(){return e(x)}});var c=t(b,2);be(c,{title:"Features with Limits",get value(){return d().featuresWithLimits}});var a=t(c,2);const P=Z(()=>d().featuresAtLimit>0);be(a,{title:"Features at Limit",get value(){return d().featuresAtLimit},warning:"Consider upgrading your plan",get showWarning(){return e(P)}});var T=t(a,2);{var D=M=>{const p=Z(()=>m().limit?`${m().remaining} remaining this month`:"Unlimited"),w=Z(()=>m().limit&&m().remaining===0);be(M,{title:"Resume Submissions",get value(){return m().used},get subtitle(){return e(p)},warning:"Consider upgrading your plan",get showWarning(){return e(w)}})};ae(T,M=>{m()&&m().used!==void 0&&M(D)})}o($),r(y,$),$e()}function Bt(y,v){return y==="unlimited"?"Unlimited":v?`${y} ${v}`:`${y}`}function Et(y){return y===void 0?"bg-primary":y>=90?"bg-destructive":y>=70?"bg-warning":"bg-primary"}function jt(y){switch(y){case ne.Included:return"bg-primary";case ne.Limited:return"bg-warning";case ne.Unlimited:return"bg-success";case ne.NotIncluded:return"bg-destructive";default:return"bg-muted"}}function Mt(y){switch(y){case ne.Included:return"Included";case ne.Limited:return"Limited";case ne.Unlimited:return"Unlimited";case ne.NotIncluded:return"Not Included";default:return"Unknown"}}function Wt(y){return y.split("_").map(v=>v.charAt(0).toUpperCase()+v.slice(1)).join(" ")}var Ot=f("<!> Reset Usage",1),zt=f("<!> <!>",1),Yt=f("<!> <!> <!>",1),qt=f("<!> <!> <!>",1),Jt=f("<!> <!>",1),Vt=f("<!> <!> <!> <!>",1),Gt=f("<!> <!>",1);function ot(y,v){_e(v,!1);let d=V(v,"onReset",8),m=V(v,"featureId",8,void 0),$=V(v,"limitId",8,void 0),F=V(v,"featureName",8,void 0),b=V(v,"limitName",8,void 0),x=J(!1),c=J(!1),a=J(null),P=J(!1);function T(){return m()&&$()?`Are you sure you want to reset usage for ${F()||m()} - ${b()||$()}?`:m()?`Are you sure you want to reset all usage for ${F()||m()}?`:"Are you sure you want to reset all feature usage?"}async function D(){i(x,!0),i(a,null),i(c,!1);try{const p=await ht(m(),$());p.success?(i(c,!0),d()(),setTimeout(()=>{i(P,!1),i(c,!1)},1500)):i(a,p.error||"Failed to reset feature usage")}catch(p){console.error("Error resetting feature usage:",p),i(a,p.message||"An error occurred while resetting feature usage")}finally{i(x,!1)}}function M(p){i(P,p.detail),e(P)||(i(a,null),i(c,!1))}pe(),yt(y,{get open(){return e(P)},set open(p){i(P,p)},$$events:{change:M},children:(p,w)=>{var C=Gt(),O=A(C);Pt(O,{asChild:!0,children:ct,$$slots:{default:(S,N)=>{const u=Z(()=>N.builder),g=Z(()=>[e(u)]);Pe(S,{variant:"outline",size:"sm",class:"text-destructive hover:bg-destructive hover:text-destructive-foreground",get builders(){return e(g)},children:(n,s)=>{var U=Ot(),I=A(U);wt(I,{class:"mr-2 h-4 w-4"}),L(),r(n,U)},$$slots:{default:!0}})}}});var B=t(O,2);bt(B,{children:(S,N)=>{var u=Vt(),g=A(u);Ct(g,{children:(E,j)=>{var H=zt(),z=A(H);Rt(z,{children:(_,W)=>{L();var h=k("Reset Feature Usage");r(_,h)},$$slots:{default:!0}});var K=t(z,2);Ft(K,{children:(_,W)=>{L();var h=k();G(Y=>R(h,`${Y??""}
        This action cannot be undone.`),[T],Z),r(_,h)},$$slots:{default:!0}}),r(E,H)},$$slots:{default:!0}});var n=t(g,2);{var s=E=>{we(E,{variant:"destructive",children:(j,H)=>{var z=Yt(),K=A(z);Je(K,{class:"h-4 w-4"});var _=t(K,2);Ce(_,{children:(h,Y)=>{L();var q=k("Error");r(h,q)},$$slots:{default:!0}});var W=t(_,2);Re(W,{children:(h,Y)=>{L();var q=k();G(()=>R(q,e(a))),r(h,q)},$$slots:{default:!0}}),r(j,z)},$$slots:{default:!0}})};ae(n,E=>{e(a)&&E(s)})}var U=t(n,2);{var I=E=>{we(E,{children:(j,H)=>{var z=qt(),K=A(z);st(K,{class:"h-4 w-4 text-green-500"});var _=t(K,2);Ce(_,{children:(h,Y)=>{L();var q=k("Success");r(h,q)},$$slots:{default:!0}});var W=t(_,2);Re(W,{children:(h,Y)=>{L();var q=k("Feature usage reset successfully");r(h,q)},$$slots:{default:!0}}),r(j,z)},$$slots:{default:!0}})};ae(U,E=>{e(c)&&E(I)})}var re=t(U,2);St(re,{children:(E,j)=>{var H=Jt(),z=A(H);Pe(z,{variant:"outline",get disabled(){return e(x)},$$events:{click:()=>i(P,!1)},children:(_,W)=>{L();var h=k("Cancel");r(_,h)},$$slots:{default:!0}});var K=t(z,2);Pe(K,{variant:"destructive",get disabled(){return e(x)},$$events:{click:D},children:(_,W)=>{L();var h=k();G(()=>R(h,e(x)?"Resetting...":"Reset")),r(_,h)},$$slots:{default:!0}}),r(E,H)},$$slots:{default:!0}}),r(S,u)},$$slots:{default:!0}}),r(p,C)},$$slots:{default:!0},$$legacy:!0}),$e()}var Ht=f('<div class="flex items-start justify-between"><!> <!></div> <!>',1),Kt=f('<div class="text-muted-foreground text-xs">Unlimited usage</div>'),Qt=f('<div class="space-y-2"><div class="flex justify-between text-sm"><span> </span> <span> </span></div> <!></div>'),Xt=f('<div class="space-y-4"></div>'),Zt=f('<div class="text-muted-foreground text-sm">No usage limits for this feature.</div>'),er=f('<div class="flex w-full justify-end"><!></div>'),tr=f("<!> <!> <!>",1);function rr(y,v){_e(v,!1);let d=V(v,"feature",8),m=V(v,"onReset",8);pe(),Oe(y,{children:($,F)=>{var b=tr(),x=A(b);Ye(x,{children:(P,T)=>{var D=Ht(),M=A(D),p=l(M);qe(p,{children:(B,S)=>{L();var N=k();G(()=>R(N,d().name)),r(B,N)},$$slots:{default:!0}});var w=t(p,2);const C=Z(()=>jt(d().accessLevel));tt(w,{variant:"outline",get class(){return e(C)},children:(B,S)=>{L();var N=k();G(u=>R(N,u),[()=>Mt(d().accessLevel)],Z),r(B,N)},$$slots:{default:!0}}),o(M);var O=t(M,2);rt(O,{children:(B,S)=>{L();var N=k();G(()=>R(N,d().description)),r(B,N)},$$slots:{default:!0}}),r(P,D)},$$slots:{default:!0}});var c=t(x,2);ze(c,{children:(P,T)=>{var D=Ae(),M=A(D);{var p=C=>{var O=Xt();Fe(O,5,()=>d().limits,Se,(B,S)=>{var N=Qt(),u=l(N),g=l(u),n=l(g,!0);o(g);var s=t(g,2),U=l(s);o(s),o(u);var I=t(u,2);{var re=j=>{const H=Z(()=>e(S).percentUsed||0),z=Z(()=>Et(e(S).percentUsed));pt(j,{get value(){return e(H)},max:100,get class(){return e(z)}})},E=j=>{var H=Kt();r(j,H)};ae(I,j=>{e(S).value!=="unlimited"&&typeof e(S).value=="number"?j(re):j(E,!1)})}o(N),G(j=>{R(n,e(S).name),R(U,`${e(S).used??""} / ${j??""}`)},[()=>Bt(e(S).value,e(S).unit)],Z),r(B,N)}),o(O),r(C,O)},w=C=>{var O=Zt();r(C,O)};ae(M,C=>{d().limits&&d().limits.length>0?C(p):C(w,!1)})}r(P,D)},$$slots:{default:!0}});var a=t(c,2);at(a,{children:(P,T)=>{var D=er(),M=l(D);ot(M,{get onReset(){return m()},get featureId(){return d().id},get featureName(){return d().name}}),o(D),r(P,D)},$$slots:{default:!0}}),r($,b)},$$slots:{default:!0}}),$e()}var ar=f('<div class="flex items-center gap-2"><span> </span> <!></div>'),sr=f('<div class="grid grid-cols-1 gap-4 pt-4 md:grid-cols-2"></div>'),or=f("<!> <!>",1);function lr(y,v){_e(v,!1);let d=V(v,"categories",24,()=>[]),m=V(v,"featuresByCategory",24,()=>({})),$=V(v,"onReset",8);pe(),mt(y,{type:"single",class:"w-full",children:(F,b)=>{var x=Ae(),c=A(x);Fe(c,1,d,Se,(a,P)=>{gt(a,{get value(){return e(P)},children:(T,D)=>{var M=or(),p=A(M);_t(p,{children:(C,O)=>{var B=ar(),S=l(B),N=l(S,!0);o(S);var u=t(S,2);tt(u,{variant:"outline",children:(g,n)=>{L();var s=k();G(()=>R(s,m()[e(P)].length)),r(g,s)},$$slots:{default:!0}}),o(B),G(g=>R(N,g),[()=>Wt(e(P))],Z),r(C,B)},$$slots:{default:!0}});var w=t(p,2);$t(w,{children:(C,O)=>{var B=sr();Fe(B,5,()=>m()[e(P)],Se,(S,N)=>{rr(S,{get feature(){return e(N)},get onReset(){return $()}})}),o(B),r(C,B)},$$slots:{default:!0}}),r(T,M)},$$slots:{default:!0}})}),r(F,x)},$$slots:{default:!0}}),$e()}var nr=f("<!> <!>",1),ir=f("<!> <!>",1),dr=f("<!> <!>",1),ur=f('<div class="grid gap-2"><!> <!></div> <div class="grid gap-2"><!> <!></div>',1),vr=f("<!> <!> <!>",1),cr=f("<!> <!> <!>",1),fr=f('<div class="grid gap-4"><div class="grid gap-2"><!> <!></div> <!> <!> <!></div>'),mr=f("<!> <!> <!>",1);function gr(y,v){_e(v,!1);let d=V(v,"features",24,()=>[]),m=V(v,"onTrackSuccess",8,()=>{}),$=J(""),F=J(""),b=J(1),x=J(!1),c=J(!1),a=J(null),P=J(null),T=J([]);function D(w){var C;i($,w.detail),i(P,d().find(O=>O.id===e($))||null),i(T,((C=e(P))==null?void 0:C.limits)||[]),i(F,e(T).length>0?e(T)[0].id:""),e(T).length>0&&e(T)[0]}function M(w){i(F,w.detail),e(T).find(C=>C.id===e(F))}async function p(){if(!e($)||!e(F)){i(a,"Please select a feature and limit");return}i(x,!0),i(a,null),i(c,!1);try{const w=await xt(e($),e(F),e(b));w.success?(i(c,!0),m()(),setTimeout(()=>{i(c,!1)},3e3)):(i(a,w.error||"Failed to track feature usage"),w.limitReached&&i(a,"You have reached your limit for this feature"))}catch(w){console.error("Error tracking feature:",w),i(a,w.message||"An error occurred while tracking feature usage")}finally{i(x,!1)}}pe(),Oe(y,{class:"w-full",children:(w,C)=>{var O=mr(),B=A(O);Ye(B,{children:(u,g)=>{var n=nr(),s=A(n);qe(s,{children:(I,re)=>{L();var E=k("Track Feature Usage");r(I,E)},$$slots:{default:!0}});var U=t(s,2);rt(U,{children:(I,re)=>{L();var E=k("Test tracking feature usage to see how it affects your limits");r(I,E)},$$slots:{default:!0}}),r(u,n)},$$slots:{default:!0}});var S=t(B,2);ze(S,{children:(u,g)=>{var n=fr(),s=l(n),U=l(s);We(U,{for:"feature",children:(_,W)=>{L();var h=k("Feature");r(_,h)},$$slots:{default:!0}});var I=t(U,2);Ke(I,{$$events:{change:D},children:(_,W)=>{var h=ir(),Y=A(h);Qe(Y,{id:"feature",children:(ee,oe)=>{et(ee,{placeholder:"Select a feature"})},$$slots:{default:!0}});var q=t(Y,2);Xe(q,{children:(ee,oe)=>{var Q=Ae(),X=A(Q);Fe(X,1,d,Se,(te,se)=>{Ze(te,{get value(){return e(se).id},children:(de,ce)=>{L();var ie=k();G(()=>R(ie,e(se).name)),r(de,ie)},$$slots:{default:!0}})}),r(ee,Q)},$$slots:{default:!0}}),r(_,h)},$$slots:{default:!0}}),o(s);var re=t(s,2);{var E=_=>{var W=ur(),h=A(W),Y=l(h);We(Y,{for:"limit",children:(X,te)=>{L();var se=k("Limit");r(X,se)},$$slots:{default:!0}});var q=t(Y,2);Ke(q,{$$events:{change:M},children:(X,te)=>{var se=dr(),de=A(se);const ce=Z(()=>e(T).length===0);Qe(de,{id:"limit",get disabled(){return e(ce)},children:(he,xe)=>{et(he,{placeholder:"Select a limit"})},$$slots:{default:!0}});var ie=t(de,2);Xe(ie,{children:(he,xe)=>{var fe=Ae(),Ne=A(fe);Fe(Ne,1,()=>e(T),Se,(ye,me)=>{Ze(ye,{get value(){return e(me).id},children:(Ie,Ue)=>{L();var ge=k();G(()=>R(ge,e(me).name)),r(Ie,ge)},$$slots:{default:!0}})}),r(he,fe)},$$slots:{default:!0}}),r(X,se)},$$slots:{default:!0}}),o(h);var ee=t(h,2),oe=l(ee);We(oe,{for:"amount",children:(X,te)=>{L();var se=k("Amount");r(X,se)},$$slots:{default:!0}});var Q=t(oe,2);Ut(Q,{id:"amount",type:"number",min:"1",placeholder:"1",get value(){return e(b)},set value(X){i(b,X)},$$legacy:!0}),o(ee),r(_,W)};ae(re,_=>{e(P)&&_(E)})}var j=t(re,2);{var H=_=>{we(_,{variant:"destructive",children:(W,h)=>{var Y=vr(),q=A(Y);Je(q,{class:"h-4 w-4"});var ee=t(q,2);Ce(ee,{children:(Q,X)=>{L();var te=k("Error");r(Q,te)},$$slots:{default:!0}});var oe=t(ee,2);Re(oe,{children:(Q,X)=>{L();var te=k();G(()=>R(te,e(a))),r(Q,te)},$$slots:{default:!0}}),r(W,Y)},$$slots:{default:!0}})};ae(j,_=>{e(a)&&_(H)})}var z=t(j,2);{var K=_=>{we(_,{children:(W,h)=>{var Y=cr(),q=A(Y);st(q,{class:"h-4 w-4 text-green-500"});var ee=t(q,2);Ce(ee,{children:(Q,X)=>{L();var te=k("Success");r(Q,te)},$$slots:{default:!0}});var oe=t(ee,2);Re(oe,{children:(Q,X)=>{L();var te=k("Feature usage tracked successfully");r(Q,te)},$$slots:{default:!0}}),r(W,Y)},$$slots:{default:!0}})};ae(z,_=>{e(c)&&_(K)})}o(n),r(u,n)},$$slots:{default:!0}});var N=t(S,2);at(N,{children:(u,g)=>{const n=Z(()=>e(x)||!e($)||!e(F));Pe(u,{get disabled(){return e(n)},class:"w-full",$$events:{click:p},children:(s,U)=>{L();var I=k();G(()=>R(I,e(x)?"Tracking...":"Track Usage")),r(s,I)},$$slots:{default:!0}})},$$slots:{default:!0}}),r(w,O)},$$slots:{default:!0}}),$e()}var _r=f("<!> ",1);function $r(y,v){const d=V(v,"loading",3,!1);Pe(y,{variant:"outline",size:"sm",get onclick(){return v.onRefresh},get disabled(){return d()},class:"ml-auto",children:(m,$)=>{var F=_r(),b=A(F);const x=ft(()=>d()?"animate-spin":"");Lt(b,{get class(){return`mr-2 h-4 w-4 ${e(x)??""}`}});var c=t(b);G(()=>R(c,` ${d()?"Refreshing...":"Refresh"}`)),r(m,F)},$$slots:{default:!0}})}var pr=f('<div class="space-y-4"><!> <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"><!> <!> <!></div></div>'),hr=f("<!> <!> <!>",1),xr=f('<div class="h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>'),yr=f('<!> <div class="text-muted-foreground mb-4 text-xs"><div class="flex items-center gap-2"><span> </span> <!></div></div> <div class="grid grid-cols-1 gap-6 lg:grid-cols-3"><div class="lg:col-span-1"><!> <div class="mt-4 rounded-md border p-4"><h3 class="mb-2 text-sm font-semibold">Debug Information</h3> <div class="space-y-2"><div><h4 class="text-xs font-medium">Resume Usage:</h4> <pre class="bg-muted overflow-auto rounded p-2 text-xs"> </pre></div> <div><h4 class="text-xs font-medium">API Status:</h4> <ul class="text-xs"><li>Loading: <span> </span></li> <li>Error: <span> </span></li> <li>Features loaded: <span> </span></li> <li>Categories: <span> </span></li> <li>Usage data: <span> </span></li> <li>Usage summary: <span> </span></li> <li>Resume usage: <span> </span></li></ul></div></div></div></div> <div class="lg:col-span-2"><!></div></div>',1),br=f('<div class="container mx-auto p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h2 class="text-3xl font-bold tracking-tight">Feature Usage</h2> <p class="text-muted-foreground">Track your feature usage and limits based on your subscription plan.</p></div> <div class="flex items-center gap-2"><!> <!></div></div> <!></div></div>');function sa(y,v){_e(v,!1);let d=J(!0),m=J(null),$=J(null),F=J(null),b=J({}),x=J([]),c=J([]),a=J(null);async function P(){i(d,!0),i(m,null);try{console.log("Loading feature usage data...");const u=await fetch("/dashboard/usage?type=all");if(!u.ok)throw new Error(`API error: ${u.status} ${u.statusText}`);const g=await u.json();if(console.log("API call complete:",g),g.features&&(i($,g.features),console.log("Feature usage data:",e($)),e($).error?i(m,e($).error):!e($).features||e($).features.length===0?i(m,"No feature usage data available."):(i(c,e($).features),i(b,{}),e($).features.forEach(n=>{const s=n.category||"other";e(b)[s]||He(b,e(b)[s]=[]),e(b)[s].push(n)}),i(x,Object.keys(e(b)).sort((n,s)=>n===Le.Core?-1:s===Le.Core?1:n.localeCompare(s)))),i(F,{totalFeatures:e(c).length,featuresUsed:e(c).filter(n=>n.limits.some(s=>s.used>0)).length,featuresWithLimits:e(c).filter(n=>n.limits.some(s=>s.value!=="unlimited")).length,featuresAtLimit:e(c).filter(n=>n.limits.some(s=>s.value!=="unlimited"&&typeof s.value=="number"&&s.used>=s.value)).length,topFeatures:[]}),console.log("Usage summary data:",e(F))),g.resume&&(i(a,g.resume),console.log("Resume usage data:",e(a)),e(a)&&e(a).used!==void 0&&e(c))){let n=e(c).find(s=>s.id==="resume_scanner");if(n){const s=n.limits.find(U=>U.id==="resume_scans_per_month");s?(s.used=e(a).used||0,s.value=e(a).limit||"unlimited",s.remaining=e(a).remaining||null,s.percentUsed=e(a).limit&&e(a).used!==void 0?Math.min(100,e(a).used/e(a).limit*100):null):n.limits.push({id:"resume_scans_per_month",name:"Resume Scans",description:"Number of resumes you can scan per month",type:"monthly",unit:"scans",value:e(a).limit||"unlimited",used:e(a).used||0,remaining:e(a).remaining||null,percentUsed:e(a).limit&&e(a).used!==void 0?Math.min(100,e(a).used/e(a).limit*100):null,period:new Date().toISOString().substring(0,7),lastUpdated:new Date})}else if(e(c)){n={id:"resume_scanner",name:"Resume Scanner",description:"Scan and analyze resumes",category:"resume",accessLevel:ne.Limited,limits:[{id:"resume_scans_per_month",name:"Resume Scans",description:"Number of resumes you can scan per month",type:"monthly",unit:"scans",value:e(a).limit||"unlimited",used:e(a).used||0,remaining:e(a).remaining||null,percentUsed:e(a).limit&&e(a).used!==void 0?Math.min(100,e(a).used/e(a).limit*100):null,period:new Date().toISOString().substring(0,7),lastUpdated:new Date}]},e(c).push(n);const s="resume";e(b)[s]||(He(b,e(b)[s]=[]),i(x,[...e(x),s].sort((U,I)=>U===Le.Core?-1:I===Le.Core?1:U.localeCompare(I)))),e(b)[s].push(n)}}}catch(u){console.error("Error loading feature usage data:",u),i(m,u.message||"Failed to load feature usage data.")}finally{console.log("Setting loading to false"),i(d,!1)}}function T(){console.log("Refresh button clicked, loading usage data..."),P()}vt(()=>{console.log("Component mounted, loading usage data..."),setTimeout(()=>{P()},100)}),pe();var D=br(),M=l(D),p=l(M),w=t(l(p),2),C=l(w);ot(C,{onReset:T});var O=t(C,2);$r(O,{get loading(){return e(d)},onRefresh:T}),o(w),o(p);var B=t(p,2);{var S=u=>{var g=pr(),n=l(g);ke(n,{class:"h-[200px] w-full"});var s=t(n,2),U=l(s);ke(U,{class:"h-[150px] w-full"});var I=t(U,2);ke(I,{class:"h-[150px] w-full"});var re=t(I,2);ke(re,{class:"h-[150px] w-full"}),o(s),o(g),r(u,g)},N=(u,g)=>{{var n=U=>{we(U,{variant:"destructive",children:(I,re)=>{var E=hr(),j=A(E);Je(j,{class:"h-4 w-4"});var H=t(j,2);Ce(H,{children:(K,_)=>{L();var W=k("Error");r(K,W)},$$slots:{default:!0}});var z=t(H,2);Re(z,{children:(K,_)=>{L();var W=k();G(()=>R(W,e(m))),r(K,W)},$$slots:{default:!0}}),r(I,E)},$$slots:{default:!0}})},s=U=>{var I=yr(),re=A(I);{var E=le=>{Dt(le,{get usageSummary(){return e(F)},get resumeUsage(){return e(a)}})};ae(re,le=>{e(F)&&le(E)})}var j=t(re,2),H=l(j),z=l(H),K=l(z);o(z);var _=t(z,2);{var W=le=>{var Me=xr();r(le,Me)};ae(_,le=>{e(d)&&le(W)})}o(H),o(j);var h=t(j,2),Y=l(h),q=l(Y);gr(q,{get features(){return e(c)},onTrackSuccess:T});var ee=t(q,2),oe=t(l(ee),2),Q=l(oe),X=t(l(Q),2),te=l(X);o(X),o(Q);var se=t(Q,2),de=t(l(se),2),ce=l(de),ie=t(l(ce)),he=l(ie,!0);o(ie),o(ce);var xe=t(ce,2),fe=t(l(xe)),Ne=l(fe,!0);o(fe),o(xe);var ye=t(xe,2),me=t(l(ye)),Ie=l(me,!0);o(me),o(ye);var Ue=t(ye,2),ge=t(l(Ue)),lt=l(ge,!0);o(ge),o(Ue);var Te=t(Ue,2),De=t(l(Te)),nt=l(De,!0);o(De),o(Te);var Be=t(Te,2),Ee=t(l(Be)),it=l(Ee,!0);o(Ee),o(Be);var Ve=t(Be,2),je=t(l(Ve)),dt=l(je,!0);o(je),o(Ve),o(de),o(se),o(oe),o(ee),o(Y);var Ge=t(Y,2),ut=l(Ge);lr(ut,{get categories(){return e(x)},get featuresByCategory(){return e(b)},onReset:T}),o(Ge),o(h),G((le,Me)=>{R(K,`Loading state: ${e(d)?"Loading...":"Complete"}`),R(te,`
                  ${le??""}
                `),ue(ie,1,ve(e(d)?"font-semibold text-yellow-500":"text-green-500")),R(he,e(d)?"Yes":"No"),ue(fe,1,ve(e(m)?"font-semibold text-red-500":"text-green-500")),R(Ne,e(m)?e(m):"None"),ue(me,1,ve(e(c)&&e(c).length>0?"text-green-500":"font-semibold text-red-500")),R(Ie,e(c)?e(c).length:0),ue(ge,1,ve(e(x).length>0?"text-green-500":"font-semibold text-red-500")),R(lt,Me),ue(De,1,ve(e($)?"text-green-500":"font-semibold text-red-500")),R(nt,e($)?"Loaded":"Not loaded"),ue(Ee,1,ve(e(F)?"text-green-500":"font-semibold text-red-500")),R(it,e(F)?"Loaded":"Not loaded"),ue(je,1,ve(e(a)?"text-green-500":"font-semibold text-red-500")),R(dt,e(a)?"Loaded":"Not loaded")},[()=>e(a)?JSON.stringify(e(a),null,2):"No resume usage data available",()=>e(x).length>0?e(x).join(", "):"None"],Z),r(U,I)};ae(u,U=>{e(m)?U(n):U(s,!1)},g)}};ae(B,u=>{e(d)?u(S):u(N,!1)})}o(M),o(D),r(y,D),$e()}export{sa as component};
