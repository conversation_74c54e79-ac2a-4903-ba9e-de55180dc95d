import{N as o,w as e,b as n}from"./BGYDhraB.js";const p="listItem",s="textStyle",i=/^\s*([-+*])\s$/,a=o.create({name:"bulletList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:"ul"}]},renderHTML({HTMLAttributes:t}){return["ul",n(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleBulletList:()=>({commands:t,chain:r})=>this.options.keepAttributes?r().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(p,this.editor.getAttributes(s)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let t=e({find:i,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(t=e({find:i,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(s),editor:this.editor})),[t]}});export{a as BulletList,a as default,i as inputRegex};
