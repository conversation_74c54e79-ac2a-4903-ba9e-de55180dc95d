import{c as p,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as d,s as c}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(t,o){const r=d(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["polyline",{points:"17 8 12 3 7 8"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15"}]];f(t,c({name:"upload"},()=>r,{get iconNode(){return a},children:(e,$)=>{var s=p(),n=i(s);m(n,o,"default",{},null),l(e,s)},$$slots:{default:!0}}))}export{x as U};
