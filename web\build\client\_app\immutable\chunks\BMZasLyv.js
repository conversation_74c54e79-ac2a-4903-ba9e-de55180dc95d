var v=Object.defineProperty;var F=(r,e,t)=>e in r?v(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var h=(r,e,t)=>F(r,typeof e!="symbol"?e+"":e,t);import{F as a,L as m}from"./iTBjRg9v.js";import{g,a as y,F as p}from"./YNp1uWxB.js";class o{constructor(e){h(this,"userData");this.userData=e}hasAccess(e){if(!this.userData.plan)return!1;const t=this.getPlanFeature(e);return t?t.accessLevel!==a.NotIncluded:!1}getAccessLevel(e){if(!this.userData.plan)return a.NotIncluded;const t=this.getPlanFeature(e);return(t==null?void 0:t.accessLevel)||a.NotIncluded}hasLimit(e,t){if(!this.userData.plan)return!1;const i=this.getPlanFeature(e);return!i||i.accessLevel===a.Unlimited?!1:!!this.getLimitValue(e,t)}getLimitValue(e,t){var s;if(!this.userData.plan)return;const i=this.getPlanFeature(e);if(!i)return;if(i.accessLevel===a.Unlimited)return"unlimited";if(i.accessLevel!==a.Limited)return;const n=(s=i.limits)==null?void 0:s.find(c=>c.limitId===t);return n==null?void 0:n.value}getNumericLimitValue(e,t,i){const n=this.getLimitValue(e,t);return n===void 0?i:n==="unlimited"?1/0:n}getCurrentUsage(e,t){const i=g(t);if(!i)return 0;if(i.type===m.Monthly){const s=new Date,c=`${s.getFullYear()}-${String(s.getMonth()+1).padStart(2,"0")}`,u=this.userData.usage.find(l=>l.featureId===e&&l.limitId===t&&l.period===c);return(u==null?void 0:u.used)||0}const n=this.userData.usage.find(s=>s.featureId===e&&s.limitId===t);return(n==null?void 0:n.used)||0}hasReachedLimit(e,t){if(!this.userData.plan)return!0;const i=this.getPlanFeature(e);if(!i)return!0;if(i.accessLevel===a.Unlimited||i.accessLevel!==a.Limited)return!1;const n=this.getLimitValue(e,t);return n===void 0||n==="unlimited"?!1:this.getCurrentUsage(e,t)>=n}getRemainingUsage(e,t){if(!this.userData.plan)return 0;const i=this.getPlanFeature(e);if(!i)return 0;if(i.accessLevel===a.Unlimited||i.accessLevel!==a.Limited)return 1/0;const n=this.getLimitValue(e,t);if(n===void 0||n==="unlimited")return 1/0;const s=this.getCurrentUsage(e,t);return Math.max(0,n-s)}canPerformAction(e,t){return this.hasAccess(e)?t?!this.hasReachedLimit(e,t):!0:!1}getBlockReason(e,t){const i=y(e);if(!i)return"Feature not found";if(!this.hasAccess(e))return`Your current plan does not include the ${i.name} feature.`;if(!t)return null;if(this.hasReachedLimit(e,t)){const n=g(t);if(!n)return"Limit not found";const s=this.getLimitValue(e,t);return s===void 0||s==="unlimited"?null:`You've reached your limit of ${s} ${n.unit||""} for ${i.name}.`}return null}getAvailableFeatures(){return p.filter(e=>this.hasAccess(e.id))}getAvailableFeaturesByCategory(){const e=this.getAvailableFeatures(),t={};for(const i of e)t[i.category]||(t[i.category]=[]),t[i.category].push(i);return t}getPlanFeature(e){if(this.userData.plan)return this.userData.plan.features.find(t=>t.featureId===e)}static fromUserData(e){return new o(e)}}function D(r){var c;const e=((c=r.subscription)==null?void 0:c.planId)||r.role||"free",t=r.plan;let i;r.subscription&&(i={planId:r.subscription.planId||e,startDate:r.subscription.startDate||new Date,endDate:r.subscription.endDate,cancelAtPeriodEnd:r.subscription.cancelAtPeriodEnd||!1,status:r.subscription.status||"active",trialEndDate:r.subscription.trialEndDate});const n=[];if(r.usage){for(const u of p)if(u.limits)for(const l of u.limits){const f=`${u.id}_${l.id}`;if(f in r.usage){const d=new Date,L=l.type===m.Monthly?`${d.getFullYear()}-${String(d.getMonth()+1).padStart(2,"0")}`:void 0;n.push({featureId:u.id,limitId:l.id,used:r.usage[f],period:L,lastUpdated:new Date})}}}const s={id:r.id,subscription:i,plan:t,usage:n};return o.fromUserData(s)}export{D as c};
