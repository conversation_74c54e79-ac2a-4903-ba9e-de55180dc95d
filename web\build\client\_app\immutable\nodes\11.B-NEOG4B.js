const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["../chunks/Bjxev4T5.js","../chunks/Cf6rS4LV.js","../chunks/CGmarHxI.js"])))=>i.map(i=>d[i]);
import{_ as ft}from"../chunks/C1FmrZbK.js";import{c as ae,f as $,a as e,t as l}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as We,f as v,c as n,n as a,s as t,r as d,t as S,g as i,x as M,d as Qe,k as qe,aM as _t,a as Xe}from"../chunks/CGmarHxI.js";import{i as ke}from"../chunks/u21ee2wt.js";import{i as mt}from"../chunks/BIEMS98f.js";import{s as F}from"../chunks/CIt1g2O9.js";import{e as $t}from"../chunks/C3w0v0gR.js";import{c as o}from"../chunks/BvdI7LR8.js";import{B as q}from"../chunks/B1K98fMG.js";import{I as pt}from"../chunks/DMTMHyMa.js";import{C as ht}from"../chunks/DuGukytH.js";import{C as gt}from"../chunks/Cdn-N1RY.js";import{C as bt}from"../chunks/BkJY4La4.js";import{C as xt}from"../chunks/GwmmX_iF.js";import{C as Et}from"../chunks/D50jIuLr.js";import{T as Pt,a as At,b as He,c as se,d as yt,e as oe}from"../chunks/LESefvxV.js";import{B as j}from"../chunks/DaBofrVv.js";import{S as Je}from"../chunks/D9yI7a4E.js";import{getEnabledFeatures as Lt,FEATURE_FLAGS as Q,ENVIRONMENT_CONFIG as le,shouldBypassLimits as Ke,isFeatureEnabled as St,toggleFeature as De}from"../chunks/Bjxev4T5.js";import{S as Ft}from"../chunks/BoNCRmBc.js";import{R as Tt}from"../chunks/qwsZpUIl.js";import{E as wt}from"../chunks/7AwcL9ec.js";import{E as Ct}from"../chunks/6UJoWgvL.js";import{S as It}from"../chunks/C6g8ubaU.js";var Dt=$("<!> Feature Control Panel",1),kt=$('<div class="flex items-center justify-between"><div><!> <!></div> <!></div>'),Bt=$("<!> Log Status",1),Nt=$("<!> <!> <!> <!> <!>",1),Rt=$('<div class="flex items-center gap-2"><!> <!></div>'),Mt=$("<!> <!> <!> <!> <!>",1),jt=$("<!> <!>",1),Ot=$('<div class="py-8 text-center text-gray-500">No features match your search criteria.</div>'),Ut=$('<div class="rounded-lg border p-4"><h3 class="mb-3 font-semibold">Environment Configuration</h3> <div class="grid grid-cols-2 gap-4 text-sm"><div class="flex justify-between"><span>Disable All Limits:</span> <!></div> <div class="flex justify-between"><span>Development Bypass:</span> <!></div> <div class="flex justify-between"><span>Enable All Features:</span> <!></div> <div class="flex justify-between"><span>Disabled Features:</span> <!></div></div></div> <div class="flex flex-wrap items-center gap-4"><!> <div class="flex items-center gap-2"><!> <span class="text-sm">Show only enabled</span></div> <div class="flex gap-2"><!> <!> <!></div></div> <div class="rounded-md border"><!></div> <!>',1),zt=$("<!> <!>",1);function Vt(me,$e){We($e,!0);let C=qe(""),O=qe(!1);const H=M(()=>()=>Object.entries(Q).filter(([x,U])=>{var z;if(i(C).trim()){const I=i(C).toLowerCase();return x.toLowerCase().includes(I)||((z=U.description)==null?void 0:z.toLowerCase().includes(I))}return i(O)?U.enabled:!0}));function pe(u,x){De(u,x),Q[u]={...Q[u]}}function he(){Object.keys(Q).forEach(u=>{De(u,!0)})}function J(){Object.keys(Q).forEach(u=>{De(u,!1)})}function ne(){}var K=ae(),de=v(K);o(de,()=>ht,(u,x)=>{x(u,{children:(U,z)=>{var I=zt(),p=v(I);o(p,()=>xt,(D,be)=>{be(D,{children:(xe,Ze)=>{var V=kt(),k=n(V),W=n(k);o(W,()=>Et,(B,T)=>{T(B,{class:"flex items-center gap-2",children:(w,Z)=>{var h=Dt(),Pe=v(h);Ft(Pe,{class:"h-5 w-5"}),a(),e(w,h)},$$slots:{default:!0}})});var X=t(W,2);o(X,()=>bt,(B,T)=>{T(B,{children:(w,Z)=>{a();var h=l("Manage feature flags and access controls across the application");e(w,h)},$$slots:{default:!0}})}),d(k);var Ee=t(k,2);j(Ee,{variant:"outline",children:(B,T)=>{a();var w=l();S((Z,h)=>F(w,`${Z??""} / ${h??""} enabled`),[()=>Lt().length,()=>Object.keys(Q).length]),e(B,w)},$$slots:{default:!0}}),d(V),e(xe,V)},$$slots:{default:!0}})});var ge=t(p,2);o(ge,()=>gt,(D,be)=>{be(D,{class:"space-y-6",children:(xe,Ze)=>{var V=Ut(),k=v(V),W=t(n(k),2),X=n(W),Ee=t(n(X),2);const B=M(()=>le.DISABLE_ALL_LIMITS?"default":"outline");j(Ee,{get variant(){return i(B)},children:(r,g)=>{a();var s=l();S(()=>F(s,le.DISABLE_ALL_LIMITS?"Yes":"No")),e(r,s)},$$slots:{default:!0}}),d(X);var T=t(X,2),w=t(n(T),2);const Z=M(()=>"outline");j(w,{get variant(){return i(Z)},children:(r,g)=>{a();var s=l();S(()=>F(s,"No")),e(r,s)},$$slots:{default:!0}}),d(T);var h=t(T,2),Pe=t(n(h),2);const et=M(()=>le.ENABLE_ALL_FEATURES?"default":"outline");j(Pe,{get variant(){return i(et)},children:(r,g)=>{a();var s=l();S(()=>F(s,le.ENABLE_ALL_FEATURES?"Yes":"No")),e(r,s)},$$slots:{default:!0}}),d(h);var Be=t(h,2),tt=t(n(Be),2);j(tt,{variant:"outline",children:(r,g)=>{a();var s=l();S(()=>F(s,le.DISABLED_FEATURES.length||"None")),e(r,s)},$$slots:{default:!0}}),d(Be),d(W),d(k);var Ae=t(k,2),Ne=n(Ae);pt(Ne,{placeholder:"Search features...",class:"max-w-sm",get value(){return i(C)},set value(r){Qe(C,r,!0)}});var ye=t(Ne,2),rt=n(ye);Je(rt,{get checked(){return i(O)},set checked(r){Qe(O,r,!0)}}),a(2),d(ye);var Re=t(ye,2),Me=n(Re);q(Me,{variant:"outline",size:"sm",onclick:he,children:(r,g)=>{a();var s=l("Enable All");e(r,s)},$$slots:{default:!0}});var je=t(Me,2);q(je,{variant:"outline",size:"sm",onclick:J,children:(r,g)=>{a();var s=l("Disable All");e(r,s)},$$slots:{default:!0}});var at=t(je,2);q(at,{variant:"outline",size:"sm",onclick:ne,children:(r,g)=>{var s=Bt(),Oe=v(s);Tt(Oe,{class:"mr-2 h-4 w-4"}),a(),e(r,s)},$$slots:{default:!0}}),d(Re),d(Ae);var Le=t(Ae,2),st=n(Le);o(st,()=>Pt,(r,g)=>{g(r,{children:(s,Oe)=>{var Ue=jt(),ze=v(Ue);o(ze,()=>At,(Se,Fe)=>{Fe(Se,{children:(Te,dt)=>{var ee=ae(),we=v(ee);o(we,()=>He,(te,ie)=>{ie(te,{children:(ve,N)=>{var E=Nt(),re=v(E);o(re,()=>se,(b,f)=>{f(b,{children:(_,R)=>{a();var m=l("Feature ID");e(_,m)},$$slots:{default:!0}})});var ue=t(re,2);o(ue,()=>se,(b,f)=>{f(b,{children:(_,R)=>{a();var m=l("Description");e(_,m)},$$slots:{default:!0}})});var ce=t(ue,2);o(ce,()=>se,(b,f)=>{f(b,{children:(_,R)=>{a();var m=l("Status");e(_,m)},$$slots:{default:!0}})});var fe=t(ce,2);o(fe,()=>se,(b,f)=>{f(b,{children:(_,R)=>{a();var m=l("Bypass Limits");e(_,m)},$$slots:{default:!0}})});var Ce=t(fe,2);o(Ce,()=>se,(b,f)=>{f(b,{children:(_,R)=>{a();var m=l("Actions");e(_,m)},$$slots:{default:!0}})}),e(ve,E)},$$slots:{default:!0}})}),e(Te,ee)},$$slots:{default:!0}})});var nt=t(ze,2);o(nt,()=>yt,(Se,Fe)=>{Fe(Se,{children:(Te,dt)=>{var ee=ae(),we=v(ee);$t(we,17,()=>i(H)(),([te,ie])=>te,(te,ie)=>{var ve=M(()=>_t(i(ie),2));let N=()=>i(ve)[0],E=()=>i(ve)[1];var re=ae(),ue=v(re);o(ue,()=>He,(ce,fe)=>{fe(ce,{children:(Ce,b)=>{var f=Mt(),_=v(f);o(_,()=>oe,(P,A)=>{A(P,{class:"font-mono text-sm",children:(y,_e)=>{a();var c=l();S(()=>F(c,N())),e(y,c)},$$slots:{default:!0}})});var R=t(_,2);o(R,()=>oe,(P,A)=>{A(P,{class:"max-w-xs truncate",children:(y,_e)=>{a();var c=l();S(()=>F(c,E().description||"No description")),e(y,c)},$$slots:{default:!0}})});var m=t(R,2);o(m,()=>oe,(P,A)=>{A(P,{children:(y,_e)=>{const c=M(()=>E().enabled?"default":"secondary");j(y,{get variant(){return i(c)},children:(Y,Ie)=>{a();var L=l();S(()=>F(L,E().enabled?"Enabled":"Disabled")),e(Y,L)},$$slots:{default:!0}})},$$slots:{default:!0}})});var Ve=t(m,2);o(Ve,()=>oe,(P,A)=>{A(P,{children:(y,_e)=>{const c=M(()=>Ke(N())?"default":"outline");j(y,{get variant(){return i(c)},children:(Y,Ie)=>{a();var L=l();S(Ye=>F(L,Ye),[()=>Ke(N())?"Yes":"No"]),e(Y,L)},$$slots:{default:!0}})},$$slots:{default:!0}})});var it=t(Ve,2);o(it,()=>oe,(P,A)=>{A(P,{children:(y,_e)=>{var c=Rt(),Y=n(c);Je(Y,{get checked(){return E().enabled},onCheckedChange:L=>pe(N(),L)});var Ie=t(Y,2);q(Ie,{variant:"ghost",size:"sm",onclick:()=>console.log("Feature details:",{featureId:N(),config:E(),isEnabled:St(N())}),children:(L,Ye)=>{var Ge=ae(),vt=v(Ge);{var ut=G=>{wt(G,{class:"h-4 w-4"})},ct=G=>{Ct(G,{class:"h-4 w-4"})};ke(vt,G=>{E().enabled?G(ut):G(ct,!1)})}e(L,Ge)},$$slots:{default:!0}}),d(c),e(y,c)},$$slots:{default:!0}})}),e(Ce,f)},$$slots:{default:!0}})}),e(te,re)}),e(Te,ee)},$$slots:{default:!0}})}),e(s,Ue)},$$slots:{default:!0}})}),d(Le);var ot=t(Le,2);{var lt=r=>{var g=Ot();e(r,g)};ke(ot,r=>{i(H)().length===0&&r(lt)})}e(xe,V)},$$slots:{default:!0}})}),e(U,I)},$$slots:{default:!0}})}),e(me,K),Xe()}var Yt=$('<!> <div class="mt-8 space-y-4"><h2 class="text-xl font-semibold">Quick Actions</h2> <div class="grid grid-cols-1 md:grid-cols-3 gap-4"><div class="rounded-lg border p-4"><h3 class="font-medium mb-2">Development Mode</h3> <p class="text-sm text-muted-foreground mb-3">Set environment variables to control features globally</p> <div class="space-y-2 text-xs font-mono"><div>VITE_DISABLE_FEATURE_LIMITS=true</div> <div>VITE_ENABLE_ALL_FEATURES=true</div> <div>VITE_DISABLED_FEATURES=automation,ai</div></div></div> <div class="rounded-lg border p-4"><h3 class="font-medium mb-2">Runtime Control</h3> <p class="text-sm text-muted-foreground mb-3">Toggle features on/off without restarting the application</p> <!></div> <div class="rounded-lg border p-4"><h3 class="font-medium mb-2">Debug Mode</h3> <p class="text-sm text-muted-foreground mb-3">Enable debug mode to see feature check details</p> <!></div></div></div>',1),Gt=$('<!> <div class="container mx-auto py-8"><div class="mb-8"><h1 class="text-3xl font-bold">Feature Control Panel</h1> <p class="text-muted-foreground mt-2">Manage feature flags and access controls across the application</p></div> <!></div>',1);function pr(me,$e){We($e,!1),mt();var C=Gt(),O=v(C);It(O,{title:"Feature Control Panel | Admin",description:"Manage feature flags and access controls"});var H=t(O,2),pe=t(n(H),2);{var he=J=>{var ne=Yt(),K=v(ne);Vt(K,{});var de=t(K,2),u=t(n(de),2),x=t(n(u),2),U=t(n(x),4);q(U,{variant:"outline",size:"sm",onclick:()=>{ft(async()=>{const{toggleFeature:p}=await import("../chunks/Bjxev4T5.js");return{toggleFeature:p}},__vite__mapDeps([0,1,2]),import.meta.url).then(({toggleFeature:p})=>{p("automation",!1),alert("Automation feature disabled")})},children:(p,ge)=>{a();var D=l("Disable Automation");e(p,D)},$$slots:{default:!0}}),d(x);var z=t(x,2),I=t(n(z),4);q(I,{variant:"outline",size:"sm",onclick:()=>{localStorage.setItem("feature-debug","true"),alert("Debug mode enabled. Refresh the page to see debug info.")},children:(p,ge)=>{a();var D=l("Enable Debug");e(p,D)},$$slots:{default:!0}}),d(z),d(u),d(de),e(J,ne)};ke(pe,J=>{J(he,!1)})}d(H),e(me,C),Xe()}export{pr as component};
