import{c as n,a as d}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as m,s as l}from"./Btcx8l8F.js";import{I as h}from"./D4f2twK-.js";function y(r,t){const o=m(t,["children","$$slots","$$events","$$legacy"]),p=[["path",{d:"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4"}],["path",{d:"M14 13.12c0 2.38 0 6.38-1 8.88"}],["path",{d:"M17.29 21.02c.12-.6.43-2.3.5-3.02"}],["path",{d:"M2 12a10 10 0 0 1 18-6"}],["path",{d:"M2 16h.01"}],["path",{d:"M21.8 16c.2-2 .131-5.354 0-6"}],["path",{d:"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2"}],["path",{d:"M8.65 22c.21-.66.45-1.32.57-2"}],["path",{d:"M9 6.8a6 6 0 0 1 9 5.2v2"}]];h(r,l({name:"fingerprint"},()=>o,{get iconNode(){return p},children:(s,f)=>{var a=n(),e=i(a);c(e,t,"default",{},null),d(s,a)},$$slots:{default:!0}}))}export{y as F};
