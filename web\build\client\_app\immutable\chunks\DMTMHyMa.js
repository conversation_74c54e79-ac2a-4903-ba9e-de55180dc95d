import{c as h,a as o,f as p}from"./BasJTneF.js";import{p as w,f as k,a as I}from"./CGmarHxI.js";import{i as P}from"./u21ee2wt.js";import{r as u,e as f}from"./B-Xjo-Yt.js";import{a as j,b as c}from"./CzsE_FAw.js";import{b as m}from"./5V1tIHTN.js";import{p as s,r as q}from"./Btcx8l8F.js";import{c as b}from"./ncUU1dSD.js";var z=p("<input/>"),A=p("<input/>");function K(v,e){w(e,!0);let a=s(e,"ref",15,null),n=s(e,"value",15),g=s(e,"files",15),d=q(e,["$$slots","$$events","$$legacy","ref","value","type","files","class"]);var l=h(),x=k(l);{var y=t=>{var r=z();u(r),f(r,i=>({"data-slot":"input",class:i,type:"file",...d}),[()=>b("selection:bg-primary dark:bg-input/30 selection:text-primary-foreground border-input ring-offset-background placeholder:text-muted-foreground shadow-xs flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-2 text-sm font-medium outline-none transition-[color,box-shadow] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e.class)]),m(r,i=>a(i),()=>a()),j(r,g),c(r,n),o(t,r)},_=t=>{var r=A();u(r),f(r,i=>({"data-slot":"input",class:i,type:e.type,...d}),[()=>b("border-input bg-background selection:bg-primary dark:bg-input/30 selection:text-primary-foreground ring-offset-background placeholder:text-muted-foreground shadow-xs flex h-9 w-full min-w-0 rounded-md border px-3 py-1 text-base outline-none transition-[color,box-shadow] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e.class)]),m(r,i=>a(i),()=>a()),c(r,n),o(t,r)};P(x,t=>{e.type==="file"?t(y):t(_,!1)})}o(v,l),I()}export{K as I};
