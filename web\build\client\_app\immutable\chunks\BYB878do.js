import{R as S,T as L,aO as P,aP as U,_ as q,o as z,aQ as M,ac as j,a0 as y,au as g,aR as x,aS as I,aT as G,aU as K,aV as Q}from"./CGmarHxI.js";import{b as V}from"./CIt1g2O9.js";import{c as W}from"./C3w0v0gR.js";import{w as D}from"./CmxjS0TN.js";const H=()=>performance.now(),b={tick:i=>requestAnimationFrame(i),now:()=>H(),tasks:new Set};function $(){const i=b.now();b.tasks.forEach(t=>{t.c(i)||(b.tasks.delete(t),t.f())}),b.tasks.size!==0&&b.tick($)}function J(i){let t;return b.tasks.size===0&&b.tick($),{promise:new Promise(r=>{b.tasks.add(t={c:i,f:r})}),abort(){b.tasks.delete(t)}}}function E(i,t){D(()=>{i.dispatchEvent(new CustomEvent(t))})}function X(i){if(i==="float")return"cssFloat";if(i==="offset")return"cssOffset";if(i.startsWith("--"))return i;const t=i.split("-");return t.length===1?t[0]:t[0]+t.slice(1).map(r=>r[0].toUpperCase()+r.slice(1)).join("")}function B(i){const t={},r=i.split(";");for(const e of r){const[a,f]=e.split(":");if(!a||f===void 0)break;const s=X(a.trim());t[s]=f.trim()}return t}const Y=i=>i;function at(i,t,r){var e=W,a,f,s,l=null;e.a??(e.a={element:i,measure(){a=this.element.getBoundingClientRect()},apply(){if(s==null||s.abort(),f=this.element.getBoundingClientRect(),a.left!==f.left||a.right!==f.right||a.top!==f.top||a.bottom!==f.bottom){const d=t()(this.element,{from:a,to:f},r==null?void 0:r());s=N(this.element,d,void 0,1,()=>{s==null||s.abort(),s=void 0})}},fix(){if(!i.getAnimations().length){var{position:d,width:p,height:u}=getComputedStyle(i);if(d!=="absolute"&&d!=="fixed"){var v=i.style;l={position:v.position,width:v.width,height:v.height,transform:v.transform},v.position="absolute",v.width=p,v.height=u;var n=i.getBoundingClientRect();if(a.left!==n.left||a.top!==n.top){var o=`translate(${a.left-n.left}px, ${a.top-n.top}px)`;v.transform=v.transform?`${v.transform} ${o}`:o}}}},unfix(){if(l){var d=i.style;d.position=l.position,d.width=l.width,d.height=l.height,d.transform=l.transform}}}),e.a.element=i}function et(i,t,r,e){var a=(i&K)!==0,f=(i&Q)!==0,s=a&&f,l=(i&M)!==0,d=s?"both":a?"in":"out",p,u=t.inert,v=t.style.overflow,n,o;function m(){var c=G,R=S;x(null),I(null);try{return p??(p=r()(t,(e==null?void 0:e())??{},{direction:d}))}finally{x(c),I(R)}}var h={is_global:l,in(){var c;if(t.inert=u,!a){o==null||o.abort(),(c=o==null?void 0:o.reset)==null||c.call(o);return}f||n==null||n.abort(),E(t,"introstart"),n=N(t,m(),o,1,()=>{E(t,"introend"),n==null||n.abort(),n=p=void 0,t.style.overflow=v})},out(c){if(!f){c==null||c(),p=void 0;return}t.inert=!0,E(t,"outrostart"),o=N(t,m(),n,0,()=>{E(t,"outroend"),c==null||c()})},stop:()=>{n==null||n.abort(),o==null||o.abort()}},w=S;if((w.transitions??(w.transitions=[])).push(h),a&&V){var T=l;if(!T){for(var _=w.parent;_&&_.f&L;)for(;(_=_.parent)&&!(_.f&P););T=!_||(_.f&U)!==0}T&&q(()=>{z(()=>h.in())})}}function N(i,t,r,e,a){var f=e===1;if(j(t)){var s,l=!1;return y(()=>{if(!l){var w=t({direction:f?"in":"out"});s=N(i,w,r,e,a)}}),{abort:()=>{l=!0,s==null||s.abort()},deactivate:()=>s.deactivate(),reset:()=>s.reset(),t:()=>s.t()}}if(r==null||r.deactivate(),!(t!=null&&t.duration))return a(),{abort:g,deactivate:g,reset:g,t:()=>e};const{delay:d=0,css:p,tick:u,easing:v=Y}=t;var n=[];if(f&&r===void 0&&(u&&u(0,1),p)){var o=B(p(0,1));n.push(o,o)}var m=()=>1-e,h=i.animate(n,{duration:d,fill:"forwards"});return h.onfinish=()=>{h.cancel();var w=(r==null?void 0:r.t())??1-e;r==null||r.abort();var T=e-w,_=t.duration*Math.abs(T),c=[];if(_>0){var R=!1;if(p)for(var A=Math.ceil(_/16.666666666666668),k=0;k<=A;k+=1){var F=w+T*v(k/A),O=B(p(F,1-F));c.push(O),R||(R=O.overflow==="hidden")}R&&(i.style.overflow="hidden"),m=()=>{var C=h.currentTime;return w+T*v(C/_)},u&&J(()=>{if(h.playState!=="running")return!1;var C=m();return u(C,1-C),!0})}h=i.animate(c,{duration:_,fill:"forwards"}),h.onfinish=()=>{m=()=>e,u==null||u(e,1-e),a()}},{abort:()=>{h&&(h.cancel(),h.effect=null,h.onfinish=g)},deactivate:()=>{a=g},reset:()=>{e===0&&(u==null||u(1,0))},t:()=>m()}}export{at as a,J as l,b as r,et as t};
