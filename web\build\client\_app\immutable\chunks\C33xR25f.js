import{c as p,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function g(r,o){const s=m(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M3 6h18"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17"}]];f(r,d({name:"trash-2"},()=>s,{get iconNode(){return a},children:(e,h)=>{var t=p(),n=i(t);l(n,o,"default",{},null),c(e,t)},$$slots:{default:!0}}))}export{g as T};
