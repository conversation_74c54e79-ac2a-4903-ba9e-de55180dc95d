import{f as d,a as e,c as z,t as T}from"../chunks/BasJTneF.js";import{o as mr}from"../chunks/nZgk9enP.js";import{p as pr,k as X,v as He,f as c,a as $r,s,c as l,d as q,g as a,n as w,r as i,t as M,x as hr}from"../chunks/CGmarHxI.js";import{s as B}from"../chunks/CIt1g2O9.js";import{i as Y}from"../chunks/u21ee2wt.js";import{e as gr,i as xr}from"../chunks/C3w0v0gR.js";import{h as br}from"../chunks/DYwWIJ9y.js";import{c as t}from"../chunks/BvdI7LR8.js";import{g as wr}from"../chunks/CmxjS0TN.js";import{s as qe}from"../chunks/B-Xjo-Yt.js";import{C as ye}from"../chunks/DuGukytH.js";import{C as Pe}from"../chunks/Cdn-N1RY.js";import{C as yr}from"../chunks/BkJY4La4.js";import{C as Pr}from"../chunks/GwmmX_iF.js";import{C as Cr}from"../chunks/D50jIuLr.js";import{B as le}from"../chunks/B1K98fMG.js";import{T as kr,a as jr,b as er,c as pe,d as Tr,e as $e}from"../chunks/LESefvxV.js";import{a as Er,R as Fr}from"../chunks/tdzGgazS.js";import"../chunks/CgXBgsce.js";import{t as I}from"../chunks/DjPYYl4Z.js";import{R as Qe}from"../chunks/qwsZpUIl.js";import{C as Jr}from"../chunks/-SpbofVw.js";import{C as Rr}from"../chunks/DW7T7T22.js";import{C as qr}from"../chunks/BAIxhb6t.js";import{T as Dr}from"../chunks/CTO_B1Jk.js";import{D as Sr,a as Nr,b as Or,c as Ar}from"../chunks/CKh8VGVX.js";import{E as Mr}from"../chunks/7AwcL9ec.js";import{R as Ir}from"../chunks/CTQ8y7hr.js";var Lr=d('<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>'),zr=d("<!> Refresh",1),Br=d('<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Waiting</p> <h3 class="mt-1 text-2xl font-bold"> </h3></div> <div class="rounded-full bg-yellow-100 p-2"><!></div></div>'),Hr=d('<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Processing</p> <h3 class="mt-1 text-2xl font-bold"> </h3></div> <div class="rounded-full bg-blue-100 p-2"><!></div></div>'),Qr=d('<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Completed</p> <h3 class="mt-1 text-2xl font-bold"> </h3></div> <div class="rounded-full bg-green-100 p-2"><!></div></div>'),Wr=d('<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Failed</p> <h3 class="mt-1 text-2xl font-bold"> </h3></div> <div class="rounded-full bg-red-100 p-2"><!></div></div>'),Gr=d("<!> Process All Jobs",1),Kr=d("<!> Clear Failed Jobs",1),Ur=d("<!> <!>",1),Vr=d('<div class="flex h-40 items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>'),Xr=d('<div class="text-muted-foreground flex h-40 items-center justify-center"><p>No jobs in the queue</p></div>'),Yr=d("<!> <!> <!> <!> <!> <!>",1),Zr=d("<span> </span>"),et=d('<div class="flex space-x-2"><!> <!></div>'),rt=d("<!> <!> <!> <!> <!> <!>",1),tt=d("<!> <!>",1),at=d("<!> <!>",1),ot=d("<!> <!>",1),st=(Ce,ae)=>q(ae,"html"),lt=(Ce,ae)=>q(ae,"text"),it=(Ce,ae)=>q(ae,"json"),dt=d('<div class="prose prose-sm max-w-none"><!></div>'),nt=d('<p class="text-muted-foreground">No HTML content available</p>'),vt=d('<div class="h-96 overflow-auto rounded-md border p-4"><!></div>'),ct=d('<pre class="whitespace-pre-wrap text-sm"> </pre>'),ut=d('<p class="text-muted-foreground">No text content available</p>'),ft=d('<div class="h-96 overflow-auto rounded-md border p-4"><!></div>'),_t=d('<div class="h-96 overflow-auto rounded-md border p-4"><pre class="whitespace-pre-wrap text-sm"> </pre></div>'),mt=d("<!> <!>",1),pt=d('<!> <div class="mb-4"><div class="flex border-b"><button>HTML</button> <button>Text</button> <button>JSON</button></div> <div class="mt-4"><!></div></div> <!>',1),$t=d('<div class="space-y-6"><div class="flex items-center justify-between"><h2 class="text-3xl font-bold tracking-tight">Email Queue</h2> <!></div> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4"><!> <!> <!> <!></div> <div class="flex flex-wrap gap-4"><!> <!></div> <!></div> <!>',1);function Gt(Ce,ae){pr(ae,!0);let he=X(!0),ke=X(!1),ie=X(He({waiting:0,processing:0,completed:0,failed:0})),De=X(He([])),Se=X(null),de=X(null),ne=X(He({html:"",text:"",subject:""})),Z=X("html"),je=X(!1);mr(async()=>(await ve(),q(Se,setInterval(ve,1e4),!0),()=>{a(Se)&&clearInterval(a(Se))}));async function ve(){q(he,!0);try{const r=await fetch("/api/email/queue-status");if(r.ok){const o=await r.json();q(ie,o.queue,!0),q(De,o.recentJobs,!0)}else{const o=await r.json();I.error(o.error||"Failed to load queue status")}}catch(r){console.error("Error loading queue status:",r),I.error("Failed to load queue status")}finally{q(he,!1)}}function rr(r){return r?new Date(r).toLocaleString():"-"}function tr(r){switch(r){case"completed":return"bg-green-100 text-green-800";case"processing":return"bg-blue-100 text-blue-800";case"waiting":return"bg-yellow-100 text-yellow-800";case"failed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}async function ar(){try{const r=await fetch("/api/email/process-queue",{method:"POST"});if(r.ok)I.success("Processing all jobs"),await ve();else{const o=await r.json();I.error(o.error||"Failed to process jobs")}}catch(r){console.error("Error processing jobs:",r),I.error("Failed to process jobs")}}async function or(){try{const r=await fetch("/api/email/clear-failed",{method:"POST"});if(r.ok)I.success("Failed jobs cleared"),await ve();else{const o=await r.json();I.error(o.error||"Failed to clear failed jobs")}}catch(r){console.error("Error clearing failed jobs:",r),I.error("Failed to clear failed jobs")}}async function sr(r){q(de,r,!0),q(je,!0);try{const o=await fetch(`/api/email/view?id=${r.id}`);if(o.ok)q(ne,await o.json(),!0);else{const u=await o.json();I.error(u.error||"Failed to load email content")}}catch(o){console.error("Error loading email content:",o),I.error("Failed to load email content")}}async function We(r){if(!(!r||!r.id)){q(ke,!0);try{const o=await fetch("/api/email/retry-failed",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:r.id})});if(o.ok)I.success("Email has been requeued"),await ve();else{const u=await o.json();I.error(u.error||"Failed to retry email")}}catch(o){console.error("Error retrying email:",o),I.error("Failed to retry email")}finally{q(ke,!1)}}}var Ge=$t(),Ne=c(Ge),Oe=l(Ne),lr=s(l(Oe),2);t(lr,()=>le,(r,o)=>{o(r,{variant:"outline",size:"sm",onclick:ve,get disabled(){return a(he)},children:(u,ee)=>{var n=zr(),y=c(n);{var O=h=>{var G=Lr();e(h,G)},E=h=>{Qe(h,{class:"mr-2 h-4 w-4"})};Y(y,h=>{a(he)?h(O):h(E,!1)})}w(),e(u,n)},$$slots:{default:!0}})}),i(Oe);var Ae=s(Oe,2),Ke=l(Ae);t(Ke,()=>ye,(r,o)=>{o(r,{children:(u,ee)=>{var n=z(),y=c(n);t(y,()=>Pe,(O,E)=>{E(O,{class:"p-6",children:(h,G)=>{var g=Br(),v=l(g),x=s(l(v),2),F=l(x,!0);i(x),i(v);var $=s(v,2),m=l($);Jr(m,{class:"h-5 w-5 text-yellow-600"}),i($),i(g),M(()=>B(F,a(ie).waiting)),e(h,g)},$$slots:{default:!0}})}),e(u,n)},$$slots:{default:!0}})});var Ue=s(Ke,2);t(Ue,()=>ye,(r,o)=>{o(r,{children:(u,ee)=>{var n=z(),y=c(n);t(y,()=>Pe,(O,E)=>{E(O,{class:"p-6",children:(h,G)=>{var g=Hr(),v=l(g),x=s(l(v),2),F=l(x,!0);i(x),i(v);var $=s(v,2),m=l($);Qe(m,{class:"h-5 w-5 text-blue-600"}),i($),i(g),M(()=>B(F,a(ie).processing)),e(h,g)},$$slots:{default:!0}})}),e(u,n)},$$slots:{default:!0}})});var Ve=s(Ue,2);t(Ve,()=>ye,(r,o)=>{o(r,{children:(u,ee)=>{var n=z(),y=c(n);t(y,()=>Pe,(O,E)=>{E(O,{class:"p-6",children:(h,G)=>{var g=Qr(),v=l(g),x=s(l(v),2),F=l(x,!0);i(x),i(v);var $=s(v,2),m=l($);Rr(m,{class:"h-5 w-5 text-green-600"}),i($),i(g),M(()=>B(F,a(ie).completed)),e(h,g)},$$slots:{default:!0}})}),e(u,n)},$$slots:{default:!0}})});var ir=s(Ve,2);t(ir,()=>ye,(r,o)=>{o(r,{children:(u,ee)=>{var n=z(),y=c(n);t(y,()=>Pe,(O,E)=>{E(O,{class:"p-6",children:(h,G)=>{var g=Wr(),v=l(g),x=s(l(v),2),F=l(x,!0);i(x),i(v);var $=s(v,2),m=l($);qr(m,{class:"h-5 w-5 text-red-600"}),i($),i(g),M(()=>B(F,a(ie).failed)),e(h,g)},$$slots:{default:!0}})}),e(u,n)},$$slots:{default:!0}})}),i(Ae);var Me=s(Ae,2),Xe=l(Me);t(Xe,()=>le,(r,o)=>{o(r,{variant:"default",onclick:ar,children:(u,ee)=>{var n=Gr(),y=c(n);Qe(y,{class:"mr-2 h-4 w-4"}),w(),e(u,n)},$$slots:{default:!0}})});var dr=s(Xe,2);const nr=hr(()=>a(ie).failed===0);t(dr,()=>le,(r,o)=>{o(r,{variant:"destructive",onclick:or,get disabled(){return a(nr)},children:(u,ee)=>{var n=Kr(),y=c(n);Dr(y,{class:"mr-2 h-4 w-4"}),w(),e(u,n)},$$slots:{default:!0}})}),i(Me);var vr=s(Me,2);t(vr,()=>ye,(r,o)=>{o(r,{children:(u,ee)=>{var n=at(),y=c(n);t(y,()=>Pr,(E,h)=>{h(E,{children:(G,g)=>{var v=Ur(),x=c(v);t(x,()=>Cr,($,m)=>{m($,{children:(K,ce)=>{w();var oe=T("Recent Jobs");e(K,oe)},$$slots:{default:!0}})});var F=s(x,2);t(F,()=>yr,($,m)=>{m($,{children:(K,ce)=>{w();var oe=T("Most recent jobs in the email queue");e(K,oe)},$$slots:{default:!0}})}),e(G,v)},$$slots:{default:!0}})});var O=s(y,2);t(O,()=>Pe,(E,h)=>{h(E,{children:(G,g)=>{var v=z(),x=c(v);{var F=m=>{var K=Vr();e(m,K)},$=(m,K)=>{{var ce=re=>{var ue=Xr();e(re,ue)},oe=re=>{var ue=z(),Ie=c(ue);t(Ie,()=>kr,(L,H)=>{H(L,{children:(V,fe)=>{var P=tt(),f=c(P);t(f,()=>jr,(J,C)=>{C(J,{children:(p,A)=>{var R=z(),U=c(R);t(U,()=>er,(te,j)=>{j(te,{children:(_e,Ye)=>{var Te=Yr(),Ee=c(Te);t(Ee,()=>pe,(D,S)=>{S(D,{children:(N,se)=>{w();var _=T("ID");e(N,_)},$$slots:{default:!0}})});var Fe=s(Ee,2);t(Fe,()=>pe,(D,S)=>{S(D,{children:(N,se)=>{w();var _=T("Type");e(N,_)},$$slots:{default:!0}})});var Le=s(Fe,2);t(Le,()=>pe,(D,S)=>{S(D,{children:(N,se)=>{w();var _=T("Recipient");e(N,_)},$$slots:{default:!0}})});var ge=s(Le,2);t(ge,()=>pe,(D,S)=>{S(D,{children:(N,se)=>{w();var _=T("Status");e(N,_)},$$slots:{default:!0}})});var xe=s(ge,2);t(xe,()=>pe,(D,S)=>{S(D,{children:(N,se)=>{w();var _=T("Created At");e(N,_)},$$slots:{default:!0}})});var Je=s(xe,2);t(Je,()=>pe,(D,S)=>{S(D,{children:(N,se)=>{w();var _=T("Actions");e(N,_)},$$slots:{default:!0}})}),e(_e,Te)},$$slots:{default:!0}})}),e(p,R)},$$slots:{default:!0}})});var k=s(f,2);t(k,()=>Tr,(J,C)=>{C(J,{children:(p,A)=>{var R=z(),U=c(R);gr(U,17,()=>a(De),xr,(te,j)=>{var _e=z(),Ye=c(_e);t(Ye,()=>er,(Te,Ee)=>{Ee(Te,{children:(Fe,Le)=>{var ge=rt(),xe=c(ge);t(xe,()=>$e,(_,Q)=>{Q(_,{class:"font-mono text-xs",children:(W,be)=>{w();var b=T();M(()=>B(b,a(j).id||"-")),e(W,b)},$$slots:{default:!0}})});var Je=s(xe,2);t(Je,()=>$e,(_,Q)=>{Q(_,{children:(W,be)=>{w();var b=T();M(()=>B(b,a(j).type||"-")),e(W,b)},$$slots:{default:!0}})});var D=s(Je,2);t(D,()=>$e,(_,Q)=>{Q(_,{children:(W,be)=>{w();var b=T();M(()=>B(b,a(j).to||a(j).email||"-")),e(W,b)},$$slots:{default:!0}})});var S=s(D,2);t(S,()=>$e,(_,Q)=>{Q(_,{children:(W,be)=>{var b=Zr(),me=l(b,!0);i(b),M(ze=>{qe(b,1,`rounded-full px-2 py-1 text-xs ${ze??""}`),B(me,a(j).status||"waiting")},[()=>tr(a(j).status||"waiting")]),e(W,b)},$$slots:{default:!0}})});var N=s(S,2);t(N,()=>$e,(_,Q)=>{Q(_,{children:(W,be)=>{w();var b=T();M(me=>B(b,me),[()=>rr(a(j).createdAt)]),e(W,b)},$$slots:{default:!0}})});var se=s(N,2);t(se,()=>$e,(_,Q)=>{Q(_,{children:(W,be)=>{var b=et(),me=l(b);t(me,()=>le,(we,Re)=>{Re(we,{variant:"outline",size:"sm",onclick:()=>sr(a(j)),children:(Be,Ze)=>{Mr(Be,{class:"h-4 w-4"})},$$slots:{default:!0}})});var ze=s(me,2);{var ur=we=>{var Re=z(),Be=c(Re);t(Be,()=>le,(Ze,fr)=>{fr(Ze,{variant:"outline",size:"sm",onclick:()=>We(a(j)),get disabled(){return a(ke)},children:(_r,ht)=>{Ir(_r,{class:"h-4 w-4"})},$$slots:{default:!0}})}),e(we,Re)};Y(ze,we=>{a(j).status==="failed"&&we(ur)})}i(b),e(W,b)},$$slots:{default:!0}})}),e(Fe,ge)},$$slots:{default:!0}})}),e(te,_e)}),e(p,R)},$$slots:{default:!0}})}),e(V,P)},$$slots:{default:!0}})}),e(re,ue)};Y(m,re=>{a(De).length===0?re(ce):re(oe,!1)},K)}};Y(x,m=>{a(he)?m(F):m($,!1)})}e(G,v)},$$slots:{default:!0}})}),e(u,n)},$$slots:{default:!0}})}),i(Ne);var cr=s(Ne,2);t(cr,()=>Fr,(r,o)=>{o(r,{get open(){return a(je)},set open(u){q(je,u,!0)},children:(u,ee)=>{var n=z(),y=c(n);t(y,()=>Er,(O,E)=>{E(O,{class:"max-w-3xl",children:(h,G)=>{var g=pt(),v=c(g);t(v,()=>Sr,(L,H)=>{H(L,{children:(V,fe)=>{var P=ot(),f=c(P);t(f,()=>Nr,(J,C)=>{C(J,{children:(p,A)=>{w();var R=T("Email Content");e(p,R)},$$slots:{default:!0}})});var k=s(f,2);t(k,()=>Or,(J,C)=>{C(J,{children:(p,A)=>{w();var R=T();M(()=>{var U,te,j;return B(R,`${((U=a(de))==null?void 0:U.type)||"Email"} to ${((te=a(de))==null?void 0:te.to)||((j=a(de))==null?void 0:j.email)||"recipient"}`)}),e(p,R)},$$slots:{default:!0}})}),e(V,P)},$$slots:{default:!0}})});var x=s(v,2),F=l(x),$=l(F);$.__click=[st,Z];var m=s($,2);m.__click=[lt,Z];var K=s(m,2);K.__click=[it,Z],i(F);var ce=s(F,2),oe=l(ce);{var re=L=>{var H=vt(),V=l(H);{var fe=f=>{var k=dt(),J=l(k);br(J,()=>a(ne).html),i(k),e(f,k)},P=f=>{var k=nt();e(f,k)};Y(V,f=>{a(ne).html?f(fe):f(P,!1)})}i(H),e(L,H)},ue=(L,H)=>{{var V=P=>{var f=ft(),k=l(f);{var J=p=>{var A=ct(),R=l(A,!0);i(A),M(()=>B(R,a(ne).text)),e(p,A)},C=p=>{var A=ut();e(p,A)};Y(k,p=>{a(ne).text?p(J):p(C,!1)})}i(f),e(P,f)},fe=P=>{var f=_t(),k=l(f),J=l(k,!0);i(k),i(f),M(C=>B(J,C),[()=>JSON.stringify(a(ne),null,2)]),e(P,f)};Y(L,P=>{a(Z)==="text"?P(V):P(fe,!1)},H)}};Y(oe,L=>{a(Z)==="html"?L(re):L(ue,!1)})}i(ce),i(x);var Ie=s(x,2);t(Ie,()=>Ar,(L,H)=>{H(L,{children:(V,fe)=>{var P=mt(),f=c(P);t(f,()=>le,(C,p)=>{p(C,{variant:"outline",onclick:()=>q(je,!1),children:(A,R)=>{w();var U=T("Close");e(A,U)},$$slots:{default:!0}})});var k=s(f,2);{var J=C=>{var p=z(),A=c(p);t(A,()=>le,(R,U)=>{U(R,{onclick:()=>We(a(de)),get disabled(){return a(ke)},children:(te,j)=>{w();var _e=T("Retry Email");e(te,_e)},$$slots:{default:!0}})}),e(C,p)};Y(k,C=>{var p;((p=a(de))==null?void 0:p.status)==="failed"&&C(J)})}e(V,P)},$$slots:{default:!0}})}),M(()=>{qe($,1,`px-4 py-2 ${a(Z)==="html"?"border-primary border-b-2 font-medium":""}`),qe(m,1,`px-4 py-2 ${a(Z)==="text"?"border-primary border-b-2 font-medium":""}`),qe(K,1,`px-4 py-2 ${a(Z)==="json"?"border-primary border-b-2 font-medium":""}`)}),e(h,g)},$$slots:{default:!0}})}),e(u,n)},$$slots:{default:!0}})}),e(Ce,Ge),$r()}wr(["click"]);export{Gt as component};
