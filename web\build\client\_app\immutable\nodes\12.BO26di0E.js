import{f as o,a as e,t as n,c as Ge}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as Ve}from"../chunks/nZgk9enP.js";import{p as He,a as Ie,f as v,s as t,d as k,m as G,g as _,c as $,n as r,r as f,t as ye,e as Je}from"../chunks/CGmarHxI.js";import{s as ke}from"../chunks/CIt1g2O9.js";import{i as we}from"../chunks/u21ee2wt.js";import{i as Ke}from"../chunks/BIEMS98f.js";import"../chunks/BiJhC7W5.js";import{B as R}from"../chunks/B1K98fMG.js";import{C as Y}from"../chunks/DuGukytH.js";import{C as Z}from"../chunks/Cdn-N1RY.js";import{C as ee}from"../chunks/BkJY4La4.js";import{C as Fe}from"../chunks/DETxXRrJ.js";import{C as te}from"../chunks/GwmmX_iF.js";import{C as re}from"../chunks/D50jIuLr.js";import{I as se}from"../chunks/DMTMHyMa.js";import{L as Pe}from"../chunks/BvvicRXk.js";import{S as oe}from"../chunks/0ykhD7u6.js";import{A as Me,a as ze,b as Qe}from"../chunks/DLEhONWn.js";import{R as Xe,T as Ye}from"../chunks/I7hvcB12.js";import{t as q}from"../chunks/DjPYYl4Z.js";import{T as ae}from"../chunks/C88uNE8B.js";import{T as le}from"../chunks/DmZyh-PW.js";import{C as I}from"../chunks/BIQwBPm4.js";var Ze=o("<!> <!> <!>",1),et=o("<!> <!>",1),tt=o("<!> Copy",1),rt=o("<!> Copy",1),st=o('<code class="bg-muted p-2 rounded block"> </code> <!>',1),ot=o("<!> <!>",1),at=o("<!> Copy",1),lt=o("<!> Copy",1),dt=o('<div class="space-y-2"><h3 class="text-lg font-medium">1. Generate a Webhook Secret</h3> <div class="flex gap-2"><!> <!> <!></div> <p class="text-sm text-muted-foreground">This secret will be used to verify webhook requests from Resend.</p></div> <!> <div class="space-y-2"><h3 class="text-lg font-medium">2. Set Environment Variable</h3> <!></div> <!> <div class="space-y-2"><h3 class="text-lg font-medium">3. Configure Webhook in Resend Dashboard</h3> <p>Go to the <a href="https://resend.com/webhooks" target="_blank" class="text-primary underline">Resend Webhooks page</a> and add a new webhook with these details:</p> <div class="space-y-4 mt-4"><div><!> <div class="flex gap-2"><!> <!></div></div> <div><!> <div class="flex gap-2"><!> <!></div></div></div></div> <!> <div class="space-y-2"><h3 class="text-lg font-medium">4. Select Event Types</h3> <p>Enable these event types in the Resend dashboard:</p> <ul class="list-disc pl-6 space-y-1"><li>email.delivered</li> <li>email.opened</li> <li>email.clicked</li> <li>email.bounced</li> <li>email.complained</li></ul></div>',1),it=o("<!> <!>",1),nt=o("<!> <!> <!>",1),vt=o("<!> <!>",1),ct=o('<div class="mt-4"><h3 class="text-lg font-medium mb-2">Test Result</h3> <div class="bg-muted p-4 rounded overflow-auto max-h-96"><pre> </pre></div></div>'),ut=o('<div class="space-y-4"><!> <!></div>'),pt=o("<!> <!>",1),mt=o("<!> <!>",1),$t=o(`<div class="space-y-4"><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div class="border rounded p-4"><h3 class="font-medium">email.delivered</h3> <p class="text-sm text-muted-foreground">Triggered when an email is successfully delivered to the recipient's mail server.</p></div> <div class="border rounded p-4"><h3 class="font-medium">email.opened</h3> <p class="text-sm text-muted-foreground">Triggered when a recipient opens an email (requires tracking pixel).</p></div> <div class="border rounded p-4"><h3 class="font-medium">email.clicked</h3> <p class="text-sm text-muted-foreground">Triggered when a recipient clicks a link in an email (requires link tracking).</p></div> <div class="border rounded p-4"><h3 class="font-medium">email.bounced</h3> <p class="text-sm text-muted-foreground">Triggered when an email permanently bounces.</p></div> <div class="border rounded p-4"><h3 class="font-medium">email.complained</h3> <p class="text-sm text-muted-foreground">Triggered when a recipient marks an email as spam.</p></div> <div class="border rounded p-4"><h3 class="font-medium">email.delivery_delayed</h3> <p class="text-sm text-muted-foreground">Triggered when email delivery is temporarily delayed.</p></div></div> <div class="mt-4"><a href="https://resend.com/docs/dashboard/webhooks/event-types" target="_blank" class="text-primary underline">View Resend Webhook Documentation</a></div></div>`),ft=o("<!> <!>",1),ht=o("<!> <!> <!> <!>",1),_t=o('<div class="container mx-auto py-8 max-w-4xl"><h1 class="text-3xl font-bold mb-6">Resend Webhook Setup</h1> <!></div>');function It(Te,Ee){He(Ee,!1);let de="",J=G(""),T=G(""),L=G(null),B=G(!1),K=G("setup");function ie(){const l=new Uint8Array(32);crypto.getRandomValues(l),k(T,Array.from(l).map(E=>E.toString(16).padStart(2,"0")).join(""))}function V(l){navigator.clipboard.writeText(l),q.success("Copied to clipboard")}async function Ce(){k(B,!0),k(L,null);try{const l=await fetch("/api/email/webhook/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"email.delivered",email:"<EMAIL>"})}),E=await l.json();k(L,{success:l.ok,status:l.status,data:E}),l.ok?q.success("Test webhook processed successfully"):q.error("Test webhook failed")}catch(l){k(L,{success:!1,error:l.message}),q.error("Error testing webhook")}finally{k(B,!1)}}async function Se(){k(B,!0);try{const E=await(await fetch("/api/email/analytics/check")).json();return E.exists?q.success(`EmailEvent table exists with ${E.count} records`):q.error("EmailEvent table does not exist"),E}catch(l){return q.error("Error checking EmailEvent table"),{exists:!1,error:l.message}}finally{k(B,!1)}}Ve(()=>{de=`${window.location.protocol}//${window.location.host}`,k(J,`${de}/api/email/webhook`),_(T)||ie()}),Ke();var F=_t(),Re=t($(F),2);Xe(Re,{get value(){return _(K)},set value(l){k(K,l)},children:(l,E)=>{var ne=ht(),ve=v(ne);Ye(ve,{class:"grid w-full grid-cols-3",children:(W,M)=>{var C=Ze(),N=v(C);ae(N,{value:"setup",children:(b,x)=>{r();var u=n("Setup Guide");e(b,u)},$$slots:{default:!0}});var y=t(N,2);ae(y,{value:"test",children:(b,x)=>{r();var u=n("Test Webhook");e(b,u)},$$slots:{default:!0}});var w=t(y,2);ae(w,{value:"events",children:(b,x)=>{r();var u=n("Event Types");e(b,u)},$$slots:{default:!0}}),e(W,C)},$$slots:{default:!0}});var ce=t(ve,2);le(ce,{value:"setup",children:(W,M)=>{Y(W,{children:(C,N)=>{var y=nt(),w=v(y);te(w,{children:(u,h)=>{var d=et(),p=v(d);re(p,{children:(s,a)=>{r();var m=n("Setup Resend Webhooks");e(s,m)},$$slots:{default:!0}});var c=t(p,2);ee(c,{children:(s,a)=>{r();var m=n("Follow these steps to configure webhooks in your Resend account.");e(s,m)},$$slots:{default:!0}}),e(u,d)},$$slots:{default:!0}});var b=t(w,2);Z(b,{class:"space-y-6",children:(u,h)=>{var d=dt(),p=v(d),c=t($(p),2),s=$(c);se(s,{get value(){return _(T)},readonly:!0});var a=t(s,2);R(a,{variant:"outline",$$events:{click:()=>V(_(T))},children:(g,D)=>{var i=tt(),S=v(i);I(S,{class:"h-4 w-4 mr-2"}),r(),e(g,i)},$$slots:{default:!0}});var m=t(a,2);R(m,{variant:"outline",$$events:{click:ie},children:(g,D)=>{r();var i=n("Regenerate");e(g,i)},$$slots:{default:!0}}),f(c),r(2),f(p);var A=t(p,2);oe(A,{});var O=t(A,2),U=t($(O),2);Me(U,{children:(g,D)=>{var i=ot(),S=v(i);ze(S,{children:(Q,Be)=>{r();var H=n("Add this to your .env file");e(Q,H)},$$slots:{default:!0}});var qe=t(S,2);Qe(qe,{children:(Q,Be)=>{var H=st(),X=v(H),De=$(X);f(X);var Le=t(X,2);R(Le,{variant:"outline",class:"mt-2",$$events:{click:()=>V(`RESEND_WEBHOOK_SECRET=${_(T)}`)},children:(Ne,gt)=>{var xe=rt(),Ue=v(xe);I(Ue,{class:"h-4 w-4 mr-2"}),r(),e(Ne,xe)},$$slots:{default:!0}}),ye(()=>ke(De,`RESEND_WEBHOOK_SECRET=${_(T)??""}`)),e(Q,H)},$$slots:{default:!0}}),e(g,i)},$$slots:{default:!0}}),f(O);var P=t(O,2);oe(P,{});var j=t(P,2),pe=t($(j),4),z=$(pe),me=$(z);Pe(me,{for:"webhook-url",children:(g,D)=>{r();var i=n("Webhook URL");e(g,i)},$$slots:{default:!0}});var $e=t(me,2),fe=$($e);se(fe,{id:"webhook-url",get value(){return _(J)},readonly:!0});var Ae=t(fe,2);R(Ae,{variant:"outline",$$events:{click:()=>V(_(J))},children:(g,D)=>{var i=at(),S=v(i);I(S,{class:"h-4 w-4 mr-2"}),r(),e(g,i)},$$slots:{default:!0}}),f($e),f(z);var he=t(z,2),_e=$(he);Pe(_e,{for:"webhook-secret",children:(g,D)=>{r();var i=n("Webhook Secret");e(g,i)},$$slots:{default:!0}});var ge=t(_e,2),be=$(ge);se(be,{id:"webhook-secret",get value(){return _(T)},readonly:!0});var Oe=t(be,2);R(Oe,{variant:"outline",$$events:{click:()=>V(_(T))},children:(g,D)=>{var i=lt(),S=v(i);I(S,{class:"h-4 w-4 mr-2"}),r(),e(g,i)},$$slots:{default:!0}}),f(ge),f(he),f(pe),f(j);var je=t(j,2);oe(je,{}),r(2),e(u,d)},$$slots:{default:!0}});var x=t(b,2);Fe(x,{class:"flex justify-between",children:(u,h)=>{var d=it(),p=v(d);R(p,{variant:"outline",$$events:{click:Se},children:(s,a)=>{r();var m=n("Check EmailEvent Table");e(s,m)},$$slots:{default:!0}});var c=t(p,2);R(c,{$$events:{click:()=>k(K,"test")},children:(s,a)=>{r();var m=n("Test Webhook");e(s,m)},$$slots:{default:!0}}),e(u,d)},$$slots:{default:!0}}),e(C,y)},$$slots:{default:!0}})},$$slots:{default:!0}});var ue=t(ce,2);le(ue,{value:"test",children:(W,M)=>{Y(W,{children:(C,N)=>{var y=pt(),w=v(y);te(w,{children:(x,u)=>{var h=vt(),d=v(h);re(d,{children:(c,s)=>{r();var a=n("Test Webhook");e(c,a)},$$slots:{default:!0}});var p=t(d,2);ee(p,{children:(c,s)=>{r();var a=n("Send a test webhook event to verify your setup.");e(c,a)},$$slots:{default:!0}}),e(x,h)},$$slots:{default:!0}});var b=t(w,2);Z(b,{children:(x,u)=>{var h=ut(),d=$(h);R(d,{get disabled(){return _(B)},$$events:{click:Ce},children:(s,a)=>{var m=Ge(),A=v(m);{var O=P=>{var j=n("Testing...");e(P,j)},U=P=>{var j=n("Send Test Event");e(P,j)};we(A,P=>{_(B)?P(O):P(U,!1)})}e(s,m)},$$slots:{default:!0}});var p=t(d,2);{var c=s=>{var a=ct(),m=t($(a),2),A=$(m),O=$(A,!0);f(A),f(m),f(a),ye(U=>ke(O,U),[()=>JSON.stringify(_(L),null,2)],Je),e(s,a)};we(p,s=>{_(L)&&s(c)})}f(h),e(x,h)},$$slots:{default:!0}}),e(C,y)},$$slots:{default:!0}})},$$slots:{default:!0}});var We=t(ue,2);le(We,{value:"events",children:(W,M)=>{Y(W,{children:(C,N)=>{var y=ft(),w=v(y);te(w,{children:(x,u)=>{var h=mt(),d=v(h);re(d,{children:(c,s)=>{r();var a=n("Resend Event Types");e(c,a)},$$slots:{default:!0}});var p=t(d,2);ee(p,{children:(c,s)=>{r();var a=n("These are the event types that Resend can send to your webhook endpoint.");e(c,a)},$$slots:{default:!0}}),e(x,h)},$$slots:{default:!0}});var b=t(w,2);Z(b,{children:(x,u)=>{var h=$t();e(x,h)},$$slots:{default:!0}}),e(C,y)},$$slots:{default:!0}})},$$slots:{default:!0}}),e(l,ne)},$$slots:{default:!0},$$legacy:!0}),f(F),e(Te,F),Ie()}export{It as component};
