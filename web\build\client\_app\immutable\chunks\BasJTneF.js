import{z as f,a$ as T,N as m,b0 as h,b1 as x,R as v,P as u,F as s,D as y,E as g}from"./CGmarHxI.js";function w(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function o(e,t){var r=v;r.nodes_start===null&&(r.nodes_start=e,r.nodes_end=t)}function M(e,t){var r=(t&h)!==0,c=(t&x)!==0,a,i=!e.startsWith("<!>");return()=>{if(u)return o(s,null),s;a===void 0&&(a=w(i?e:"<!>"+e),r||(a=f(a)));var n=c||T?document.importNode(a,!0):a.cloneNode(!0);if(r){var _=f(n),d=n.lastChild;o(_,d)}else o(n,n);return n}}function N(e,t,r="svg"){var c=!e.startsWith("<!>"),a=(t&h)!==0,i=`<${r}>${c?e:"<!>"+e}</${r}>`,n;return()=>{if(u)return o(s,null),s;if(!n){var _=w(i),d=f(_);if(a)for(n=document.createDocumentFragment();f(d);)n.appendChild(f(d));else n=f(d)}var l=n.cloneNode(!0);if(a){var C=f(l),E=l.lastChild;o(C,E)}else o(l,l);return l}}function R(e,t){return N(e,t,"svg")}function S(e){return()=>b(e())}function b(e){if(u)return e;const t=e.nodeType===11,r=e.tagName==="SCRIPT"?[e]:e.querySelectorAll("script"),c=v;for(const i of r){const n=document.createElement("script");for(var a of i.attributes)n.setAttribute(a.name,a.value);n.textContent=i.textContent,(t?e.firstChild===i:e===i)&&(c.nodes_start=n),(t?e.lastChild===i:e===i)&&(c.nodes_end=n),i.replaceWith(n)}return e}function $(e=""){if(!u){var t=m(e+"");return o(t,t),t}var r=s;return r.nodeType!==3&&(r.before(r=m()),y(r)),o(r,r),r}function D(){if(u)return o(s,null),s;var e=document.createDocumentFragment(),t=document.createComment(""),r=m();return e.append(t,r),o(t,r),e}function F(e,t){if(u){v.nodes_end=s,g();return}e!==null&&e.before(t)}function I(){var e,t;if(u&&s&&s.nodeType===8&&((e=s.textContent)!=null&&e.startsWith("#"))){const r=s.textContent.substring(1);return g(),r}return(t=window.__svelte??(window.__svelte={})).uid??(t.uid=1),`c${window.__svelte.uid++}`}const A="5";var p;typeof window<"u"&&((p=window.__svelte??(window.__svelte={})).v??(p.v=new Set)).add(A);export{F as a,o as b,D as c,w as d,R as e,M as f,I as p,$ as t,S as w};
