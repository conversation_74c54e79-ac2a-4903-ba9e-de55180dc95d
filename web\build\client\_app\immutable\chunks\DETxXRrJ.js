import{f,a as p}from"./BasJTneF.js";import{p as m,a as c,c as l,au as d,r as n}from"./CGmarHxI.js";import{c as v,s as h}from"./ncUU1dSD.js";import{e as u}from"./B-Xjo-Yt.js";import{b as _}from"./5V1tIHTN.js";import{p as b,r as x}from"./Btcx8l8F.js";var C=f("<div><!></div>");function w(e,r){m(r,!0);let s=b(r,"ref",15,null),o=x(r,["$$slots","$$events","$$legacy","ref","class","children"]);var t=C();u(t,a=>({"data-slot":"card-footer",class:a,...o}),[()=>v("[.border-t]:pt-6 flex items-center px-6",r.class)]);var i=l(t);h(i,()=>r.children??d),n(t),_(t,a=>s(a),()=>s()),p(e,t),c()}export{w as C};
