var Dp=Object.defineProperty;var eu=d=>{throw TypeError(d)};var Fp=(d,t,e)=>t in d?Dp(d,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):d[t]=e;var R=(d,t,e)=>Fp(d,typeof t!="symbol"?t+"":t,e),cc=(d,t,e)=>t.has(d)||eu("Cannot "+e);var n=(d,t,e)=>(cc(d,t,"read from private field"),e?e.call(d):t.get(d)),m=(d,t,e)=>t.has(d)?eu("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(d):t.set(d,e),p=(d,t,e,s)=>(cc(d,t,"write to private field"),s?s.call(d,e):t.set(d,e),e),b=(d,t,e)=>(cc(d,t,"access private method"),e);var te=(d,t,e,s)=>({set _(i){p(d,t,i,e)},get _(){return n(d,t,s)}});const ae=typeof process=="object"&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&process.type!=="browser"),yc=[.001,0,0,.001,0,0],dc=1.35,Pe={ANY:1,DISPLAY:2,PRINT:4,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,IS_EDITING:128,OPLIST:256},bi={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},Np="pdfjs_internal_editor_",z={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15,SIGNATURE:101},Y={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},Op={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},qt={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},Jl={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},_t={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},Sr={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},ic={ERRORS:0,WARNINGS:1,INFOS:5},Ph={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93,rawFillPath:94},jl={moveTo:0,lineTo:1,curveTo:2,closePath:3},Bp={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let nc=ic.WARNINGS;function Hp(d){Number.isInteger(d)&&(nc=d)}function $p(){return nc}function rc(d){nc>=ic.INFOS&&console.log(`Info: ${d}`)}function U(d){nc>=ic.WARNINGS&&console.log(`Warning: ${d}`)}function rt(d){throw new Error(d)}function Et(d,t){d||rt(t)}function Gp(d){switch(d==null?void 0:d.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function Cu(d,t=null,e=null){if(!d)return null;if(e&&typeof d=="string"){if(e.addDefaultProtocol&&d.startsWith("www.")){const i=d.match(/\./g);(i==null?void 0:i.length)>=2&&(d=`http://${d}`)}if(e.tryConvertEncoding)try{d=Wp(d)}catch{}}const s=t?URL.parse(d,t):URL.parse(d);return Gp(s)?s:null}function xu(d,t,e=!1){const s=URL.parse(d);return s?(s.hash=t,s.href):e&&Cu(d,"http://example.com")?d.split("#",1)[0]+`${t?`#${t}`:""}`:""}function X(d,t,e,s=!1){return Object.defineProperty(d,t,{value:e,enumerable:!s,configurable:!0,writable:!1}),e}const yr=function(){function t(e,s){this.message=e,this.name=s}return t.prototype=new Error,t.constructor=t,t}();class su extends yr{constructor(t,e){super(t,"PasswordException"),this.code=e}}class uc extends yr{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class wc extends yr{constructor(t){super(t,"InvalidPDFException")}}class Ih extends yr{constructor(t,e,s){super(t,"ResponseException"),this.status=e,this.missing=s}}class zp extends yr{constructor(t){super(t,"FormatError")}}class tn extends yr{constructor(t){super(t,"AbortException")}}function Tu(d){(typeof d!="object"||(d==null?void 0:d.length)===void 0)&&rt("Invalid argument for bytesToString");const t=d.length,e=8192;if(t<e)return String.fromCharCode.apply(null,d);const s=[];for(let i=0;i<t;i+=e){const r=Math.min(i+e,t),a=d.subarray(i,r);s.push(String.fromCharCode.apply(null,a))}return s.join("")}function Bl(d){typeof d!="string"&&rt("Invalid argument for stringToBytes");const t=d.length,e=new Uint8Array(t);for(let s=0;s<t;++s)e[s]=d.charCodeAt(s)&255;return e}function Up(d){return String.fromCharCode(d>>24&255,d>>16&255,d>>8&255,d&255)}function Vp(){const d=new Uint8Array(4);return d[0]=1,new Uint32Array(d.buffer,0,1)[0]===1}function jp(){try{return new Function(""),!0}catch{return!1}}class Wt{static get isLittleEndian(){return X(this,"isLittleEndian",Vp())}static get isEvalSupported(){return X(this,"isEvalSupported",jp())}static get isOffscreenCanvasSupported(){return X(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas<"u")}static get isImageDecoderSupported(){return X(this,"isImageDecoderSupported",typeof ImageDecoder<"u")}static get platform(){if(typeof navigator<"u"&&typeof(navigator==null?void 0:navigator.platform)=="string"&&typeof(navigator==null?void 0:navigator.userAgent)=="string"){const{platform:t,userAgent:e}=navigator;return X(this,"platform",{isAndroid:e.includes("Android"),isLinux:t.includes("Linux"),isMac:t.includes("Mac"),isWindows:t.includes("Win"),isFirefox:e.includes("Firefox")})}return X(this,"platform",{isAndroid:!1,isLinux:!1,isMac:!1,isWindows:!1,isFirefox:!1})}static get isCSSRoundSupported(){var t,e;return X(this,"isCSSRoundSupported",(e=(t=globalThis.CSS)==null?void 0:t.supports)==null?void 0:e.call(t,"width: round(1.5px, 1px)"))}}const fc=Array.from(Array(256).keys(),d=>d.toString(16).padStart(2,"0"));var ci,Zl,vc;class O{static makeHexColor(t,e,s){return`#${fc[t]}${fc[e]}${fc[s]}`}static scaleMinMax(t,e){let s;t[0]?(t[0]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[3],e[3]*=t[3]):(s=e[0],e[0]=e[1],e[1]=s,s=e[2],e[2]=e[3],e[3]=s,t[1]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e,s=0){const i=t[s],r=t[s+1];t[s]=i*e[0]+r*e[2]+e[4],t[s+1]=i*e[1]+r*e[3]+e[5]}static applyTransformToBezier(t,e,s=0){const i=e[0],r=e[1],a=e[2],o=e[3],l=e[4],h=e[5];for(let c=0;c<6;c+=2){const u=t[s+c],f=t[s+c+1];t[s+c]=u*i+f*a+l,t[s+c+1]=u*r+f*o+h}}static applyInverseTransform(t,e){const s=t[0],i=t[1],r=e[0]*e[3]-e[1]*e[2];t[0]=(s*e[3]-i*e[2]+e[2]*e[5]-e[4]*e[3])/r,t[1]=(-s*e[1]+i*e[0]+e[4]*e[1]-e[5]*e[0])/r}static axialAlignedBoundingBox(t,e,s){const i=e[0],r=e[1],a=e[2],o=e[3],l=e[4],h=e[5],c=t[0],u=t[1],f=t[2],g=t[3];let y=i*c+l,A=y,w=i*f+l,v=w,_=o*u+h,S=_,E=o*g+h,C=E;if(r!==0||a!==0){const x=r*c,T=r*f,P=a*u,k=a*g;y+=P,v+=P,w+=k,A+=k,_+=x,C+=x,E+=T,S+=T}s[0]=Math.min(s[0],y,w,A,v),s[1]=Math.min(s[1],_,E,S,C),s[2]=Math.max(s[2],y,w,A,v),s[3]=Math.max(s[3],_,E,S,C)}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t,e){const s=t[0],i=t[1],r=t[2],a=t[3],o=s**2+i**2,l=s*r+i*a,h=r**2+a**2,c=(o+h)/2,u=Math.sqrt(c**2-(o*h-l**2));e[0]=Math.sqrt(c+u||1),e[1]=Math.sqrt(c-u||1)}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const s=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),i=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(s>i)return null;const r=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return r>a?null:[s,r,i,a]}static pointBoundingBox(t,e,s){s[0]=Math.min(s[0],t),s[1]=Math.min(s[1],e),s[2]=Math.max(s[2],t),s[3]=Math.max(s[3],e)}static rectBoundingBox(t,e,s,i,r){r[0]=Math.min(r[0],t,s),r[1]=Math.min(r[1],e,i),r[2]=Math.max(r[2],t,s),r[3]=Math.max(r[3],e,i)}static bezierBoundingBox(t,e,s,i,r,a,o,l,h){h[0]=Math.min(h[0],t,o),h[1]=Math.min(h[1],e,l),h[2]=Math.max(h[2],t,o),h[3]=Math.max(h[3],e,l),b(this,ci,vc).call(this,t,s,r,o,e,i,a,l,3*(-t+3*(s-r)+o),6*(t-2*s+r),3*(s-t),h),b(this,ci,vc).call(this,t,s,r,o,e,i,a,l,3*(-e+3*(i-a)+l),6*(e-2*i+a),3*(i-e),h)}}ci=new WeakSet,Zl=function(t,e,s,i,r,a,o,l,h,c){if(h<=0||h>=1)return;const u=1-h,f=h*h,g=f*h,y=u*(u*(u*t+3*h*e)+3*f*s)+g*i,A=u*(u*(u*r+3*h*a)+3*f*o)+g*l;c[0]=Math.min(c[0],y),c[1]=Math.min(c[1],A),c[2]=Math.max(c[2],y),c[3]=Math.max(c[3],A)},vc=function(t,e,s,i,r,a,o,l,h,c,u,f){if(Math.abs(h)<1e-12){Math.abs(c)>=1e-12&&b(this,ci,Zl).call(this,t,e,s,i,r,a,o,l,-u/c,f);return}const g=c**2-4*u*h;if(g<0)return;const y=Math.sqrt(g),A=2*h;b(this,ci,Zl).call(this,t,e,s,i,r,a,o,l,(-c+y)/A,f),b(this,ci,Zl).call(this,t,e,s,i,r,a,o,l,(-c-y)/A,f)},m(O,ci);function Wp(d){return decodeURIComponent(escape(d))}let pc=null,iu=null;function qp(d){return pc||(pc=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,iu=new Map([["ﬅ","ſt"]])),d.replaceAll(pc,(t,e,s)=>e?e.normalize("NFKC"):iu.get(s))}function Ru(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID();const d=new Uint8Array(32);return crypto.getRandomValues(d),Tu(d)}const Od="pdfjs_internal_id_";function Xp(d,t,e){if(!Array.isArray(e)||e.length<2)return!1;const[s,i,...r]=e;if(!d(s)&&!Number.isInteger(s)||!t(i))return!1;const a=r.length;let o=!0;switch(i.name){case"XYZ":if(a<2||a>3)return!1;break;case"Fit":case"FitB":return a===0;case"FitH":case"FitBH":case"FitV":case"FitBV":if(a>1)return!1;break;case"FitR":if(a!==4)return!1;o=!1;break;default:return!1}for(const l of r)if(!(typeof l=="number"||o&&l===null))return!1;return!0}function oe(d,t,e){return Math.min(Math.max(d,t),e)}function Pu(d){return Uint8Array.prototype.toBase64?d.toBase64():btoa(Tu(d))}function Yp(d){return Uint8Array.fromBase64?Uint8Array.fromBase64(d):Bl(atob(d))}typeof Promise.try!="function"&&(Promise.try=function(d,...t){return new Promise(e=>{e(d(...t))})});typeof Math.sumPrecise!="function"&&(Math.sumPrecise=function(d){return d.reduce((t,e)=>t+e,0)});const Ps="http://www.w3.org/2000/svg",cn=class cn{};R(cn,"CSS",96),R(cn,"PDF",72),R(cn,"PDF_TO_CSS_UNITS",cn.CSS/cn.PDF);let en=cn;async function Hl(d,t="text"){if(Er(d,document.baseURI)){const e=await fetch(d);if(!e.ok)throw new Error(e.statusText);switch(t){case"arraybuffer":return e.arrayBuffer();case"blob":return e.blob();case"json":return e.json()}return e.text()}return new Promise((e,s)=>{const i=new XMLHttpRequest;i.open("GET",d,!0),i.responseType=t,i.onreadystatechange=()=>{if(i.readyState===XMLHttpRequest.DONE){if(i.status===200||i.status===0){switch(t){case"arraybuffer":case"blob":case"json":e(i.response);return}e(i.responseText);return}s(new Error(i.statusText))}},i.send(null)})}class $l{constructor({viewBox:t,userUnit:e,scale:s,rotation:i,offsetX:r=0,offsetY:a=0,dontFlip:o=!1}){this.viewBox=t,this.userUnit=e,this.scale=s,this.rotation=i,this.offsetX=r,this.offsetY=a,s*=e;const l=(t[2]+t[0])/2,h=(t[3]+t[1])/2;let c,u,f,g;switch(i%=360,i<0&&(i+=360),i){case 180:c=-1,u=0,f=0,g=1;break;case 90:c=0,u=1,f=1,g=0;break;case 270:c=0,u=-1,f=-1,g=0;break;case 0:c=1,u=0,f=0,g=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}o&&(f=-f,g=-g);let y,A,w,v;c===0?(y=Math.abs(h-t[1])*s+r,A=Math.abs(l-t[0])*s+a,w=(t[3]-t[1])*s,v=(t[2]-t[0])*s):(y=Math.abs(l-t[0])*s+r,A=Math.abs(h-t[1])*s+a,w=(t[2]-t[0])*s,v=(t[3]-t[1])*s),this.transform=[c*s,u*s,f*s,g*s,y-c*s*l-f*s*h,A-u*s*l-g*s*h],this.width=w,this.height=v}get rawDims(){const t=this.viewBox;return X(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:s=this.offsetX,offsetY:i=this.offsetY,dontFlip:r=!1}={}){return new $l({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:r})}convertToViewportPoint(t,e){const s=[t,e];return O.applyTransform(s,this.transform),s}convertToViewportRectangle(t){const e=[t[0],t[1]];O.applyTransform(e,this.transform);const s=[t[2],t[3]];return O.applyTransform(s,this.transform),[e[0],e[1],s[0],s[1]]}convertToPdfPoint(t,e){const s=[t,e];return O.applyInverseTransform(s,this.transform),s}}class Bd extends yr{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function ac(d){const t=d.length;let e=0;for(;e<t&&d[e].trim()==="";)e++;return d.substring(e,e+5).toLowerCase()==="data:"}function Hd(d){return typeof d=="string"&&/\.pdf$/i.test(d)}function Kp(d){return[d]=d.split(/[#?]/,1),d.substring(d.lastIndexOf("/")+1)}function Qp(d,t="document.pdf"){if(typeof d!="string")return t;if(ac(d))return U('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),t;const e=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,s=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,i=e.exec(d);let r=s.exec(i[1])||s.exec(i[2])||s.exec(i[3]);if(r&&(r=r[0],r.includes("%")))try{r=s.exec(decodeURIComponent(r))[0]}catch{}return r||t}class nu{constructor(){R(this,"started",Object.create(null));R(this,"times",[])}time(t){t in this.started&&U(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||U(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:s}of this.times)e=Math.max(s.length,e);for(const{name:s,start:i,end:r}of this.times)t.push(`${s.padEnd(e)} ${r-i}ms
`);return t.join("")}}function Er(d,t){const e=t?URL.parse(d,t):URL.parse(d);return(e==null?void 0:e.protocol)==="http:"||(e==null?void 0:e.protocol)==="https:"}function rs(d){d.preventDefault()}function St(d){d.preventDefault(),d.stopPropagation()}var go;class $d{static toDateObject(t){if(!t||typeof t!="string")return null;n(this,go)||p(this,go,new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const e=n(this,go).exec(t);if(!e)return null;const s=parseInt(e[1],10);let i=parseInt(e[2],10);i=i>=1&&i<=12?i-1:0;let r=parseInt(e[3],10);r=r>=1&&r<=31?r:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let l=parseInt(e[6],10);l=l>=0&&l<=59?l:0;const h=e[7]||"Z";let c=parseInt(e[8],10);c=c>=0&&c<=23?c:0;let u=parseInt(e[9],10)||0;return u=u>=0&&u<=59?u:0,h==="-"?(a+=c,o+=u):h==="+"&&(a-=c,o-=u),new Date(Date.UTC(s,i,r,a,o,l))}}go=new WeakMap,m($d,go);function Jp(d,{scale:t=1,rotation:e=0}){const{width:s,height:i}=d.attributes.style,r=[0,0,parseInt(s),parseInt(i)];return new $l({viewBox:r,userUnit:1,scale:t,rotation:e})}function Gd(d){if(d.startsWith("#")){const t=parseInt(d.slice(1),16);return[(t&16711680)>>16,(t&65280)>>8,t&255]}return d.startsWith("rgb(")?d.slice(4,-1).split(",").map(t=>parseInt(t)):d.startsWith("rgba(")?d.slice(5,-1).split(",").map(t=>parseInt(t)).slice(0,3):(U(`Not a valid color format: "${d}"`),[0,0,0])}function Zp(d){const t=document.createElement("span");t.style.visibility="hidden",t.style.colorScheme="only light",document.body.append(t);for(const e of d.keys()){t.style.color=e;const s=window.getComputedStyle(t).color;d.set(e,Gd(s))}t.remove()}function pt(d){const{a:t,b:e,c:s,d:i,e:r,f:a}=d.getTransform();return[t,e,s,i,r,a]}function os(d){const{a:t,b:e,c:s,d:i,e:r,f:a}=d.getTransform().invertSelf();return[t,e,s,i,r,a]}function mr(d,t,e=!1,s=!0){if(t instanceof $l){const{pageWidth:i,pageHeight:r}=t.rawDims,{style:a}=d,o=Wt.isCSSRoundSupported,l=`var(--total-scale-factor) * ${i}px`,h=`var(--total-scale-factor) * ${r}px`,c=o?`round(down, ${l}, var(--scale-round-x))`:`calc(${l})`,u=o?`round(down, ${h}, var(--scale-round-y))`:`calc(${h})`;!e||t.rotation%180===0?(a.width=c,a.height=u):(a.width=u,a.height=c)}s&&d.setAttribute("data-main-rotation",t.rotation)}class ui{constructor(){const{pixelRatio:t}=ui;this.sx=t,this.sy=t}get scaled(){return this.sx!==1||this.sy!==1}get symmetric(){return this.sx===this.sy}limitCanvas(t,e,s,i){let r=1/0,a=1/0,o=1/0;s>0&&(r=Math.sqrt(s/(t*e))),i!==-1&&(a=i/t,o=i/e);const l=Math.min(r,a,o);return this.sx>l||this.sy>l?(this.sx=l,this.sy=l,!0):!1}static get pixelRatio(){return globalThis.devicePixelRatio||1}}const _c=["image/apng","image/avif","image/bmp","image/gif","image/jpeg","image/png","image/svg+xml","image/webp","image/x-icon"];var Ai,un,Fe,yi,mo,Nr,bo,Bh,Iu,jt,ku,Mu,Ya,Lu,th;const Ms=class Ms{constructor(t){m(this,jt);m(this,Ai,null);m(this,un,null);m(this,Fe);m(this,yi,null);m(this,mo,null);m(this,Nr,null);p(this,Fe,t),n(Ms,bo)||p(Ms,bo,Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button",signature:"pdfjs-editor-remove-signature-button"}))}render(){const t=p(this,Ai,document.createElement("div"));t.classList.add("editToolbar","hidden"),t.setAttribute("role","toolbar");const e=n(this,Fe)._uiManager._signal;t.addEventListener("contextmenu",rs,{signal:e}),t.addEventListener("pointerdown",b(Ms,Bh,Iu),{signal:e});const s=p(this,yi,document.createElement("div"));s.className="buttons",t.append(s);const i=n(this,Fe).toolbarPosition;if(i){const{style:r}=t,a=n(this,Fe)._uiManager.direction==="ltr"?1-i[0]:i[0];r.insetInlineEnd=`${100*a}%`,r.top=`calc(${100*i[1]}% + var(--editor-toolbar-vert-offset))`}return b(this,jt,Lu).call(this),t}get div(){return n(this,Ai)}hide(){var t;n(this,Ai).classList.add("hidden"),(t=n(this,un))==null||t.hideDropdown()}show(){var t;n(this,Ai).classList.remove("hidden"),(t=n(this,mo))==null||t.shown()}async addAltText(t){const e=await t.render();b(this,jt,Ya).call(this,e),n(this,yi).prepend(e,n(this,jt,th)),p(this,mo,t)}addColorPicker(t){p(this,un,t);const e=t.renderButton();b(this,jt,Ya).call(this,e),n(this,yi).prepend(e,n(this,jt,th))}async addEditSignatureButton(t){const e=p(this,Nr,await t.renderEditButton(n(this,Fe)));b(this,jt,Ya).call(this,e),n(this,yi).prepend(e,n(this,jt,th))}updateEditSignatureButton(t){n(this,Nr)&&(n(this,Nr).title=t)}remove(){var t;n(this,Ai).remove(),(t=n(this,un))==null||t.destroy(),p(this,un,null)}};Ai=new WeakMap,un=new WeakMap,Fe=new WeakMap,yi=new WeakMap,mo=new WeakMap,Nr=new WeakMap,bo=new WeakMap,Bh=new WeakSet,Iu=function(t){t.stopPropagation()},jt=new WeakSet,ku=function(t){n(this,Fe)._focusEventsAllowed=!1,St(t)},Mu=function(t){n(this,Fe)._focusEventsAllowed=!0,St(t)},Ya=function(t){const e=n(this,Fe)._uiManager._signal;t.addEventListener("focusin",b(this,jt,ku).bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",b(this,jt,Mu).bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",rs,{signal:e})},Lu=function(){const{editorType:t,_uiManager:e}=n(this,Fe),s=document.createElement("button");s.className="delete",s.tabIndex=0,s.setAttribute("data-l10n-id",n(Ms,bo)[t]),b(this,jt,Ya).call(this,s),s.addEventListener("click",i=>{e.delete()},{signal:e._signal}),n(this,yi).append(s)},th=function(){const t=document.createElement("div");return t.className="divider",t},m(Ms,Bh),m(Ms,bo,null);let Sc=Ms;var Ao,fn,pn,sn,Du,Fu,Nu;class tg{constructor(t){m(this,sn);m(this,Ao,null);m(this,fn,null);m(this,pn);p(this,pn,t)}show(t,e,s){const[i,r]=b(this,sn,Fu).call(this,e,s),{style:a}=n(this,fn)||p(this,fn,b(this,sn,Du).call(this));t.append(n(this,fn)),a.insetInlineEnd=`${100*i}%`,a.top=`calc(${100*r}% + var(--editor-toolbar-vert-offset))`}hide(){n(this,fn).remove()}}Ao=new WeakMap,fn=new WeakMap,pn=new WeakMap,sn=new WeakSet,Du=function(){const t=p(this,fn,document.createElement("div"));t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",rs,{signal:n(this,pn)._signal});const e=p(this,Ao,document.createElement("div"));return e.className="buttons",t.append(e),b(this,sn,Nu).call(this),t},Fu=function(t,e){let s=0,i=0;for(const r of t){const a=r.y+r.height;if(a<s)continue;const o=r.x+(e?r.width:0);if(a>s){i=o,s=a;continue}e?o>i&&(i=o):o<i&&(i=o)}return[e?1-i:i,s]},Nu=function(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const s=n(this,pn)._signal;t.addEventListener("contextmenu",rs,{signal:s}),t.addEventListener("click",()=>{n(this,pn).highlightSelection("floating_button")},{signal:s}),n(this,Ao).append(t)};function zd(d,t,e){for(const s of e)t.addEventListener(s,d[s].bind(d))}var Hh;class eg{constructor(){m(this,Hh,0)}get id(){return`${Np}${te(this,Hh)._++}`}}Hh=new WeakMap;var Or,yo,Yt,Br,eh;const Yd=class Yd{constructor(){m(this,Br);m(this,Or,Ru());m(this,yo,0);m(this,Yt,null)}static get _isSVGFittingCanvas(){const t='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',s=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),i=new Image;i.src=t;const r=i.decode().then(()=>(s.drawImage(i,0,0,1,1,0,0,1,3),new Uint32Array(s.getImageData(0,0,1,1).data.buffer)[0]===0));return X(this,"_isSVGFittingCanvas",r)}async getFromFile(t){const{lastModified:e,name:s,size:i,type:r}=t;return b(this,Br,eh).call(this,`${e}_${s}_${i}_${r}`,t)}async getFromUrl(t){return b(this,Br,eh).call(this,t,t)}async getFromBlob(t,e){const s=await e;return b(this,Br,eh).call(this,t,s)}async getFromId(t){n(this,Yt)||p(this,Yt,new Map);const e=n(this,Yt).get(t);if(!e)return null;if(e.bitmap)return e.refCounter+=1,e;if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:s}=e;return delete e.blobPromise,this.getFromBlob(e.id,s)}return this.getFromUrl(e.url)}getFromCanvas(t,e){n(this,Yt)||p(this,Yt,new Map);let s=n(this,Yt).get(t);if(s!=null&&s.bitmap)return s.refCounter+=1,s;const i=new OffscreenCanvas(e.width,e.height);return i.getContext("2d").drawImage(e,0,0),s={bitmap:i.transferToImageBitmap(),id:`image_${n(this,Or)}_${te(this,yo)._++}`,refCounter:1,isSvg:!1},n(this,Yt).set(t,s),n(this,Yt).set(s.id,s),s}getSvgUrl(t){const e=n(this,Yt).get(t);return e!=null&&e.isSvg?e.svgUrl:null}deleteId(t){var i;n(this,Yt)||p(this,Yt,new Map);const e=n(this,Yt).get(t);if(!e||(e.refCounter-=1,e.refCounter!==0))return;const{bitmap:s}=e;if(!e.url&&!e.file){const r=new OffscreenCanvas(s.width,s.height);r.getContext("bitmaprenderer").transferFromImageBitmap(s),e.blobPromise=r.convertToBlob()}(i=s.close)==null||i.call(s),e.bitmap=null}isValidId(t){return t.startsWith(`image_${n(this,Or)}_`)}};Or=new WeakMap,yo=new WeakMap,Yt=new WeakMap,Br=new WeakSet,eh=async function(t,e){n(this,Yt)||p(this,Yt,new Map);let s=n(this,Yt).get(t);if(s===null)return null;if(s!=null&&s.bitmap)return s.refCounter+=1,s;try{s||(s={bitmap:null,id:`image_${n(this,Or)}_${te(this,yo)._++}`,refCounter:0,isSvg:!1});let i;if(typeof e=="string"?(s.url=e,i=await Hl(e,"blob")):e instanceof File?i=s.file=e:e instanceof Blob&&(i=e),i.type==="image/svg+xml"){const r=Yd._isSVGFittingCanvas,a=new FileReader,o=new Image,l=new Promise((h,c)=>{o.onload=()=>{s.bitmap=o,s.isSvg=!0,h()},a.onload=async()=>{const u=s.svgUrl=a.result;o.src=await r?`${u}#svgView(preserveAspectRatio(none))`:u},o.onerror=a.onerror=c});a.readAsDataURL(i),await l}else s.bitmap=await createImageBitmap(i);s.refCounter=1}catch(i){U(i),s=null}return n(this,Yt).set(t,s),s&&n(this,Yt).set(s.id,s),s};let Ec=Yd;var At,wi,wo,ft;class sg{constructor(t=128){m(this,At,[]);m(this,wi,!1);m(this,wo);m(this,ft,-1);p(this,wo,t)}add({cmd:t,undo:e,post:s,mustExec:i,type:r=NaN,overwriteIfSameType:a=!1,keepUndo:o=!1}){if(i&&t(),n(this,wi))return;const l={cmd:t,undo:e,post:s,type:r};if(n(this,ft)===-1){n(this,At).length>0&&(n(this,At).length=0),p(this,ft,0),n(this,At).push(l);return}if(a&&n(this,At)[n(this,ft)].type===r){o&&(l.undo=n(this,At)[n(this,ft)].undo),n(this,At)[n(this,ft)]=l;return}const h=n(this,ft)+1;h===n(this,wo)?n(this,At).splice(0,1):(p(this,ft,h),h<n(this,At).length&&n(this,At).splice(h)),n(this,At).push(l)}undo(){if(n(this,ft)===-1)return;p(this,wi,!0);const{undo:t,post:e}=n(this,At)[n(this,ft)];t(),e==null||e(),p(this,wi,!1),p(this,ft,n(this,ft)-1)}redo(){if(n(this,ft)<n(this,At).length-1){p(this,ft,n(this,ft)+1),p(this,wi,!0);const{cmd:t,post:e}=n(this,At)[n(this,ft)];t(),e==null||e(),p(this,wi,!1)}}hasSomethingToUndo(){return n(this,ft)!==-1}hasSomethingToRedo(){return n(this,ft)<n(this,At).length-1}cleanType(t){if(n(this,ft)!==-1){for(let e=n(this,ft);e>=0;e--)if(n(this,At)[e].type!==t){n(this,At).splice(e+1,n(this,ft)-e),p(this,ft,e);return}n(this,At).length=0,p(this,ft,-1)}}destroy(){p(this,At,null)}}At=new WeakMap,wi=new WeakMap,wo=new WeakMap,ft=new WeakMap;var $h,Ou;class Gl{constructor(t){m(this,$h);this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=Wt.platform;for(const[s,i,r={}]of t)for(const a of s){const o=a.startsWith("mac+");e&&o?(this.callbacks.set(a.slice(4),{callback:i,options:r}),this.allKeys.add(a.split("+").at(-1))):!e&&!o&&(this.callbacks.set(a,{callback:i,options:r}),this.allKeys.add(a.split("+").at(-1)))}}exec(t,e){if(!this.allKeys.has(e.key))return;const s=this.callbacks.get(b(this,$h,Ou).call(this,e));if(!s)return;const{callback:i,options:{bubbles:r=!1,args:a=[],checker:o=null}}=s;o&&!o(t,e)||(i.bind(t,...a,e)(),r||St(e))}}$h=new WeakSet,Ou=function(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e};const Gh=class Gh{get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return Zp(t),X(this,"_colors",t)}convert(t){const e=Gd(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[s,i]of this._colors)if(i.every((r,a)=>r===e[a]))return Gh._colorsMapping.get(s);return e}getHexCode(t){const e=this._colors.get(t);return e?O.makeHexColor(...e):t}};R(Gh,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]));let Cc=Gh;var Hr,me,Rt,Bt,$r,Ds,Gr,Ne,vi,_i,zr,gn,cs,We,mn,vo,_o,Ur,So,ds,Si,Vr,Ei,us,zh,Ci,Eo,xi,bn,An,Ti,Co,Mt,et,Fs,Ri,yn,xo,To,Pi,fs,Ns,Ro,Oe,I,sh,xc,Bu,Hu,ih,$u,Gu,zu,Tc,Uu,Rc,Pc,Vu,ee,Is,ju,Wu,Ic,qu,Ka,kc;const Mr=class Mr{constructor(t,e,s,i,r,a,o,l,h,c,u,f,g,y){m(this,I);m(this,Hr,new AbortController);m(this,me,null);m(this,Rt,new Map);m(this,Bt,new Map);m(this,$r,null);m(this,Ds,null);m(this,Gr,null);m(this,Ne,new sg);m(this,vi,null);m(this,_i,null);m(this,zr,0);m(this,gn,new Set);m(this,cs,null);m(this,We,null);m(this,mn,new Set);R(this,"_editorUndoBar",null);m(this,vo,!1);m(this,_o,!1);m(this,Ur,!1);m(this,So,null);m(this,ds,null);m(this,Si,null);m(this,Vr,null);m(this,Ei,!1);m(this,us,null);m(this,zh,new eg);m(this,Ci,!1);m(this,Eo,!1);m(this,xi,null);m(this,bn,null);m(this,An,null);m(this,Ti,null);m(this,Co,null);m(this,Mt,z.NONE);m(this,et,new Set);m(this,Fs,null);m(this,Ri,null);m(this,yn,null);m(this,xo,null);m(this,To,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1});m(this,Pi,[0,0]);m(this,fs,null);m(this,Ns,null);m(this,Ro,null);m(this,Oe,null);const A=this._signal=n(this,Hr).signal;p(this,Ns,t),p(this,Ro,e),p(this,$r,s),p(this,Ri,i),this._eventBus=r,r._on("editingaction",this.onEditingAction.bind(this),{signal:A}),r._on("pagechanging",this.onPageChanging.bind(this),{signal:A}),r._on("scalechanging",this.onScaleChanging.bind(this),{signal:A}),r._on("rotationchanging",this.onRotationChanging.bind(this),{signal:A}),r._on("setpreference",this.onSetPreference.bind(this),{signal:A}),r._on("switchannotationeditorparams",w=>this.updateParams(w.type,w.value),{signal:A}),b(this,I,$u).call(this),b(this,I,Vu).call(this),b(this,I,Tc).call(this),p(this,Ds,a.annotationStorage),p(this,So,a.filterFactory),p(this,yn,o),p(this,Vr,l||null),p(this,vo,h),p(this,_o,c),p(this,Ur,u),p(this,Co,f||null),this.viewParameters={realScale:en.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1,this._editorUndoBar=g||null,this._supportsPinchToZoom=y!==!1}static get _keyboardManager(){const t=Mr.prototype,e=a=>n(a,Ns).contains(document.activeElement)&&document.activeElement.tagName!=="BUTTON"&&a.hasSomethingToControl(),s=(a,{target:o})=>{if(o instanceof HTMLInputElement){const{type:l}=o;return l!=="text"&&l!=="number"}return!0},i=this.TRANSLATE_SMALL,r=this.TRANSLATE_BIG;return X(this,"_keyboardManager",new Gl([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:s}],[["ctrl+z","mac+meta+z"],t.undo,{checker:s}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:s}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:s}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(a,{target:o})=>!(o instanceof HTMLButtonElement)&&n(a,Ns).contains(o)&&!a.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(a,{target:o})=>!(o instanceof HTMLButtonElement)&&n(a,Ns).contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-r,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[r,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-r],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,r],checker:e}]]))}destroy(){var t,e,s,i,r,a,o,l;(t=n(this,Oe))==null||t.resolve(),p(this,Oe,null),(e=n(this,Hr))==null||e.abort(),p(this,Hr,null),this._signal=null;for(const h of n(this,Bt).values())h.destroy();n(this,Bt).clear(),n(this,Rt).clear(),n(this,mn).clear(),(s=n(this,Ti))==null||s.clear(),p(this,me,null),n(this,et).clear(),n(this,Ne).destroy(),(i=n(this,$r))==null||i.destroy(),(r=n(this,Ri))==null||r.destroy(),(a=n(this,us))==null||a.hide(),p(this,us,null),(o=n(this,An))==null||o.destroy(),p(this,An,null),n(this,ds)&&(clearTimeout(n(this,ds)),p(this,ds,null)),n(this,fs)&&(clearTimeout(n(this,fs)),p(this,fs,null)),(l=this._editorUndoBar)==null||l.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return n(this,Co)}get useNewAltTextFlow(){return n(this,_o)}get useNewAltTextWhenAddingImage(){return n(this,Ur)}get hcmFilter(){return X(this,"hcmFilter",n(this,yn)?n(this,So).addHCMFilter(n(this,yn).foreground,n(this,yn).background):"none")}get direction(){return X(this,"direction",getComputedStyle(n(this,Ns)).direction)}get highlightColors(){return X(this,"highlightColors",n(this,Vr)?new Map(n(this,Vr).split(",").map(t=>t.split("=").map(e=>e.trim()))):null)}get highlightColorNames(){return X(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,t=>t.reverse())):null)}setCurrentDrawingSession(t){t?(this.unselectAll(),this.disableUserSelect(!0)):this.disableUserSelect(!1),p(this,_i,t)}setMainHighlightColorPicker(t){p(this,An,t)}editAltText(t,e=!1){var s;(s=n(this,$r))==null||s.editAltText(this,t,e)}getSignature(t){var e;(e=n(this,Ri))==null||e.getSignature({uiManager:this,editor:t})}get signatureManager(){return n(this,Ri)}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal}),this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){switch(t){case"enableNewAltTextWhenAddingImage":p(this,Ur,e);break}}onPageChanging({pageNumber:t}){p(this,zr,t-1)}focusMainContainer(){n(this,Ns).focus()}findParent(t,e){for(const s of n(this,Bt).values()){const{x:i,y:r,width:a,height:o}=s.div.getBoundingClientRect();if(t>=i&&t<=i+a&&e>=r&&e<=r+o)return s}return null}disableUserSelect(t=!1){n(this,Ro).classList.toggle("noUserSelect",t)}addShouldRescale(t){n(this,mn).add(t)}removeShouldRescale(t){n(this,mn).delete(t)}onScaleChanging({scale:t}){var e;this.commitOrRemove(),this.viewParameters.realScale=t*en.PDF_TO_CSS_UNITS;for(const s of n(this,mn))s.onScaleChanging();(e=n(this,_i))==null||e.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:s,anchorOffset:i,focusNode:r,focusOffset:a}=e,o=e.toString(),h=b(this,I,sh).call(this,e).closest(".textLayer"),c=this.getSelectionBoxes(h);if(!c)return;e.empty();const u=b(this,I,xc).call(this,h),f=n(this,Mt)===z.NONE,g=()=>{u==null||u.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:c,anchorNode:s,anchorOffset:i,focusNode:r,focusOffset:a,text:o}),f&&this.showAllEditors("highlight",!0,!0)};if(f){this.switchToMode(z.HIGHLIGHT,g);return}g()}addToAnnotationStorage(t){!t.isEmpty()&&n(this,Ds)&&!n(this,Ds).has(t.id)&&n(this,Ds).setValue(t.id,t)}blur(){if(this.isShiftKeyDown=!1,n(this,Ei)&&(p(this,Ei,!1),b(this,I,ih).call(this,"main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of n(this,et))if(e.div.contains(t)){p(this,bn,[e,t]),e._focusEventsAllowed=!1;break}}focus(){if(!n(this,bn))return;const[t,e]=n(this,bn);p(this,bn,null),e.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this._signal}),e.focus()}addEditListeners(){b(this,I,Tc).call(this),b(this,I,Rc).call(this)}removeEditListeners(){b(this,I,Uu).call(this),b(this,I,Pc).call(this)}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const s of n(this,We))if(s.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy",t.preventDefault();return}}drop(t){for(const e of t.dataTransfer.items)for(const s of n(this,We))if(s.isHandlingMimeForPasting(e.type)){s.paste(e,this.currentLayer),t.preventDefault();return}}copy(t){var s;if(t.preventDefault(),(s=n(this,me))==null||s.commitOrRemove(),!this.hasSelection)return;const e=[];for(const i of n(this,et)){const r=i.serialize(!0);r&&e.push(r)}e.length!==0&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const r of e.items)for(const a of n(this,We))if(a.isHandlingMimeForPasting(r.type)){a.paste(r,this.currentLayer);return}let s=e.getData("application/pdfjs");if(!s)return;try{s=JSON.parse(s)}catch(r){U(`paste: "${r.message}".`);return}if(!Array.isArray(s))return;this.unselectAll();const i=this.currentLayer;try{const r=[];for(const l of s){const h=await i.deserialize(l);if(!h)return;r.push(h)}const a=()=>{for(const l of r)b(this,I,Ic).call(this,l);b(this,I,kc).call(this,r)},o=()=>{for(const l of r)l.remove()};this.addCommands({cmd:a,undo:o,mustExec:!0})}catch(r){U(`paste: "${r.message}".`)}}keydown(t){!this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!0),n(this,Mt)!==z.NONE&&!this.isEditorHandlingKeyboard&&Mr._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!1,n(this,Ei)&&(p(this,Ei,!1),b(this,I,ih).call(this,"main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu");break}}setEditingState(t){t?(b(this,I,Gu).call(this),b(this,I,Rc).call(this),b(this,I,ee).call(this,{isEditing:n(this,Mt)!==z.NONE,isEmpty:b(this,I,Ka).call(this),hasSomethingToUndo:n(this,Ne).hasSomethingToUndo(),hasSomethingToRedo:n(this,Ne).hasSomethingToRedo(),hasSelectedEditor:!1})):(b(this,I,zu).call(this),b(this,I,Pc).call(this),b(this,I,ee).call(this,{isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!n(this,We)){p(this,We,t);for(const e of n(this,We))b(this,I,Is).call(this,e.defaultPropertiesToUpdate)}}getId(){return n(this,zh).id}get currentLayer(){return n(this,Bt).get(n(this,zr))}getLayer(t){return n(this,Bt).get(t)}get currentPageIndex(){return n(this,zr)}addLayer(t){n(this,Bt).set(t.pageIndex,t),n(this,Ci)?t.enable():t.disable()}removeLayer(t){n(this,Bt).delete(t.pageIndex)}async updateMode(t,e=null,s=!1){var i,r,a;if(n(this,Mt)!==t&&!(n(this,Oe)&&(await n(this,Oe).promise,!n(this,Oe)))){if(p(this,Oe,Promise.withResolvers()),(i=n(this,_i))==null||i.commitOrRemove(),p(this,Mt,t),t===z.NONE){this.setEditingState(!1),b(this,I,Wu).call(this),(r=this._editorUndoBar)==null||r.hide(),n(this,Oe).resolve();return}t===z.SIGNATURE&&await((a=n(this,Ri))==null?void 0:a.loadSignatures()),this.setEditingState(!0),await b(this,I,ju).call(this),this.unselectAll();for(const o of n(this,Bt).values())o.updateMode(t);if(!e){s&&this.addNewEditorFromKeyboard(),n(this,Oe).resolve();return}for(const o of n(this,Rt).values())o.annotationElementId===e?(this.setSelected(o),o.enterInEditMode()):o.unselect();n(this,Oe).resolve()}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==n(this,Mt)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){var s;if(n(this,We)){switch(t){case Y.CREATE:this.currentLayer.addNewEditor(e);return;case Y.HIGHLIGHT_DEFAULT_COLOR:(s=n(this,An))==null||s.updateColor(e);break;case Y.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(n(this,xo)||p(this,xo,new Map)).set(t,e),this.showAllEditors("highlight",e);break}for(const i of n(this,et))i.updateParams(t,e);for(const i of n(this,We))i.updateDefaultParams(t,e)}}showAllEditors(t,e,s=!1){var r;for(const a of n(this,Rt).values())a.editorType===t&&a.show(e);(((r=n(this,xo))==null?void 0:r.get(Y.HIGHLIGHT_SHOW_ALL))??!0)!==e&&b(this,I,Is).call(this,[[Y.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(n(this,Eo)!==t){p(this,Eo,t);for(const e of n(this,Bt).values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}getEditors(t){const e=[];for(const s of n(this,Rt).values())s.pageIndex===t&&e.push(s);return e}getEditor(t){return n(this,Rt).get(t)}addEditor(t){n(this,Rt).set(t.id,t)}removeEditor(t){var e,s;t.div.contains(document.activeElement)&&(n(this,ds)&&clearTimeout(n(this,ds)),p(this,ds,setTimeout(()=>{this.focusMainContainer(),p(this,ds,null)},0))),n(this,Rt).delete(t.id),t.annotationElementId&&((e=n(this,Ti))==null||e.delete(t.annotationElementId)),this.unselect(t),(!t.annotationElementId||!n(this,gn).has(t.annotationElementId))&&((s=n(this,Ds))==null||s.remove(t.id))}addDeletedAnnotationElement(t){n(this,gn).add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return n(this,gn).has(t)}removeDeletedAnnotationElement(t){n(this,gn).delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}setActiveEditor(t){n(this,me)!==t&&(p(this,me,t),t&&b(this,I,Is).call(this,t.propertiesToUpdate))}updateUI(t){n(this,I,qu)===t&&b(this,I,Is).call(this,t.propertiesToUpdate)}updateUIForDefaultProperties(t){b(this,I,Is).call(this,t.defaultPropertiesToUpdate)}toggleSelected(t){if(n(this,et).has(t)){n(this,et).delete(t),t.unselect(),b(this,I,ee).call(this,{hasSelectedEditor:this.hasSelection});return}n(this,et).add(t),t.select(),b(this,I,Is).call(this,t.propertiesToUpdate),b(this,I,ee).call(this,{hasSelectedEditor:!0})}setSelected(t){var e;(e=n(this,_i))==null||e.commitOrRemove();for(const s of n(this,et))s!==t&&s.unselect();n(this,et).clear(),n(this,et).add(t),t.select(),b(this,I,Is).call(this,t.propertiesToUpdate),b(this,I,ee).call(this,{hasSelectedEditor:!0})}isSelected(t){return n(this,et).has(t)}get firstSelectedEditor(){return n(this,et).values().next().value}unselect(t){t.unselect(),n(this,et).delete(t),b(this,I,ee).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return n(this,et).size!==0}get isEnterHandled(){return n(this,et).size===1&&this.firstSelectedEditor.isEnterHandled}undo(){var t;n(this,Ne).undo(),b(this,I,ee).call(this,{hasSomethingToUndo:n(this,Ne).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:b(this,I,Ka).call(this)}),(t=this._editorUndoBar)==null||t.hide()}redo(){n(this,Ne).redo(),b(this,I,ee).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:n(this,Ne).hasSomethingToRedo(),isEmpty:b(this,I,Ka).call(this)})}addCommands(t){n(this,Ne).add(t),b(this,I,ee).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:b(this,I,Ka).call(this)})}cleanUndoStack(t){n(this,Ne).cleanType(t)}delete(){var r;this.commitOrRemove();const t=(r=this.currentLayer)==null?void 0:r.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...n(this,et)],s=()=>{var a;(a=this._editorUndoBar)==null||a.show(i,e.length===1?e[0].editorType:e.length);for(const o of e)o.remove()},i=()=>{for(const a of e)b(this,I,Ic).call(this,a)};this.addCommands({cmd:s,undo:i,mustExec:!0})}commitOrRemove(){var t;(t=n(this,me))==null||t.commitOrRemove()}hasSomethingToControl(){return n(this,me)||this.hasSelection}selectAll(){for(const t of n(this,et))t.commit();b(this,I,kc).call(this,n(this,Rt).values())}unselectAll(){var t;if(!(n(this,me)&&(n(this,me).commitOrRemove(),n(this,Mt)!==z.NONE))&&!((t=n(this,_i))!=null&&t.commitOrRemove())&&this.hasSelection){for(const e of n(this,et))e.unselect();n(this,et).clear(),b(this,I,ee).call(this,{hasSelectedEditor:!1})}}translateSelectedEditors(t,e,s=!1){if(s||this.commitOrRemove(),!this.hasSelection)return;n(this,Pi)[0]+=t,n(this,Pi)[1]+=e;const[i,r]=n(this,Pi),a=[...n(this,et)],o=1e3;n(this,fs)&&clearTimeout(n(this,fs)),p(this,fs,setTimeout(()=>{p(this,fs,null),n(this,Pi)[0]=n(this,Pi)[1]=0,this.addCommands({cmd:()=>{for(const l of a)n(this,Rt).has(l.id)&&(l.translateInPage(i,r),l.translationDone())},undo:()=>{for(const l of a)n(this,Rt).has(l.id)&&(l.translateInPage(-i,-r),l.translationDone())},mustExec:!1})},o));for(const l of a)l.translateInPage(t,e),l.translationDone()}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),p(this,cs,new Map);for(const t of n(this,et))n(this,cs).set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!n(this,cs))return!1;this.disableUserSelect(!1);const t=n(this,cs);p(this,cs,null);let e=!1;for(const[{x:i,y:r,pageIndex:a},o]of t)o.newX=i,o.newY=r,o.newPageIndex=a,e||(e=i!==o.savedX||r!==o.savedY||a!==o.savedPageIndex);if(!e)return!1;const s=(i,r,a,o)=>{if(n(this,Rt).has(i.id)){const l=n(this,Bt).get(o);l?i._setParentAndPosition(l,r,a):(i.pageIndex=o,i.x=r,i.y=a)}};return this.addCommands({cmd:()=>{for(const[i,{newX:r,newY:a,newPageIndex:o}]of t)s(i,r,a,o)},undo:()=>{for(const[i,{savedX:r,savedY:a,savedPageIndex:o}]of t)s(i,r,a,o)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(n(this,cs))for(const s of n(this,cs).keys())s.drag(t,e)}rebuild(t){if(t.parent===null){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){var t;return((t=this.getActive())==null?void 0:t.shouldGetKeyboardEvents())||n(this,et).size===1&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return n(this,me)===t}getActive(){return n(this,me)}getMode(){return n(this,Mt)}get imageManager(){return X(this,"imageManager",new Ec)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let h=0,c=e.rangeCount;h<c;h++)if(!t.contains(e.getRangeAt(h).commonAncestorContainer))return null;const{x:s,y:i,width:r,height:a}=t.getBoundingClientRect();let o;switch(t.getAttribute("data-main-rotation")){case"90":o=(h,c,u,f)=>({x:(c-i)/a,y:1-(h+u-s)/r,width:f/a,height:u/r});break;case"180":o=(h,c,u,f)=>({x:1-(h+u-s)/r,y:1-(c+f-i)/a,width:u/r,height:f/a});break;case"270":o=(h,c,u,f)=>({x:1-(c+f-i)/a,y:(h-s)/r,width:f/a,height:u/r});break;default:o=(h,c,u,f)=>({x:(h-s)/r,y:(c-i)/a,width:u/r,height:f/a});break}const l=[];for(let h=0,c=e.rangeCount;h<c;h++){const u=e.getRangeAt(h);if(!u.collapsed)for(const{x:f,y:g,width:y,height:A}of u.getClientRects())y===0||A===0||l.push(o(f,g,y,A))}return l.length===0?null:l}addChangedExistingAnnotation({annotationElementId:t,id:e}){(n(this,Gr)||p(this,Gr,new Map)).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){var e;(e=n(this,Gr))==null||e.delete(t)}renderAnnotationElement(t){var i;const e=(i=n(this,Gr))==null?void 0:i.get(t.data.id);if(!e)return;const s=n(this,Ds).getRawValue(e);s&&(n(this,Mt)===z.NONE&&!s.hasBeenModified||s.renderAnnotationElement(t))}setMissingCanvas(t,e,s){var r;const i=(r=n(this,Ti))==null?void 0:r.get(t);i&&(i.setCanvas(e,s),n(this,Ti).delete(t))}addMissingCanvas(t,e){(n(this,Ti)||p(this,Ti,new Map)).set(t,e)}};Hr=new WeakMap,me=new WeakMap,Rt=new WeakMap,Bt=new WeakMap,$r=new WeakMap,Ds=new WeakMap,Gr=new WeakMap,Ne=new WeakMap,vi=new WeakMap,_i=new WeakMap,zr=new WeakMap,gn=new WeakMap,cs=new WeakMap,We=new WeakMap,mn=new WeakMap,vo=new WeakMap,_o=new WeakMap,Ur=new WeakMap,So=new WeakMap,ds=new WeakMap,Si=new WeakMap,Vr=new WeakMap,Ei=new WeakMap,us=new WeakMap,zh=new WeakMap,Ci=new WeakMap,Eo=new WeakMap,xi=new WeakMap,bn=new WeakMap,An=new WeakMap,Ti=new WeakMap,Co=new WeakMap,Mt=new WeakMap,et=new WeakMap,Fs=new WeakMap,Ri=new WeakMap,yn=new WeakMap,xo=new WeakMap,To=new WeakMap,Pi=new WeakMap,fs=new WeakMap,Ns=new WeakMap,Ro=new WeakMap,Oe=new WeakMap,I=new WeakSet,sh=function({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t},xc=function(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const s of n(this,Bt).values())if(s.hasTextLayer(t))return s;return null},Bu=function(){const t=document.getSelection();if(!t||t.isCollapsed)return;const s=b(this,I,sh).call(this,t).closest(".textLayer"),i=this.getSelectionBoxes(s);i&&(n(this,us)||p(this,us,new tg(this)),n(this,us).show(s,i,this.direction==="ltr"))},Hu=function(){var r,a,o;const t=document.getSelection();if(!t||t.isCollapsed){n(this,Fs)&&((r=n(this,us))==null||r.hide(),p(this,Fs,null),b(this,I,ee).call(this,{hasSelectedText:!1}));return}const{anchorNode:e}=t;if(e===n(this,Fs))return;const i=b(this,I,sh).call(this,t).closest(".textLayer");if(!i){n(this,Fs)&&((a=n(this,us))==null||a.hide(),p(this,Fs,null),b(this,I,ee).call(this,{hasSelectedText:!1}));return}if((o=n(this,us))==null||o.hide(),p(this,Fs,e),b(this,I,ee).call(this,{hasSelectedText:!0}),!(n(this,Mt)!==z.HIGHLIGHT&&n(this,Mt)!==z.NONE)&&(n(this,Mt)===z.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),p(this,Ei,this.isShiftKeyDown),!this.isShiftKeyDown)){const l=n(this,Mt)===z.HIGHLIGHT?b(this,I,xc).call(this,i):null;l==null||l.toggleDrawing();const h=new AbortController,c=this.combinedSignal(h),u=f=>{f.type==="pointerup"&&f.button!==0||(h.abort(),l==null||l.toggleDrawing(!0),f.type==="pointerup"&&b(this,I,ih).call(this,"main_toolbar"))};window.addEventListener("pointerup",u,{signal:c}),window.addEventListener("blur",u,{signal:c})}},ih=function(t=""){n(this,Mt)===z.HIGHLIGHT?this.highlightSelection(t):n(this,vo)&&b(this,I,Bu).call(this)},$u=function(){document.addEventListener("selectionchange",b(this,I,Hu).bind(this),{signal:this._signal})},Gu=function(){if(n(this,Si))return;p(this,Si,new AbortController);const t=this.combinedSignal(n(this,Si));window.addEventListener("focus",this.focus.bind(this),{signal:t}),window.addEventListener("blur",this.blur.bind(this),{signal:t})},zu=function(){var t;(t=n(this,Si))==null||t.abort(),p(this,Si,null)},Tc=function(){if(n(this,xi))return;p(this,xi,new AbortController);const t=this.combinedSignal(n(this,xi));window.addEventListener("keydown",this.keydown.bind(this),{signal:t}),window.addEventListener("keyup",this.keyup.bind(this),{signal:t})},Uu=function(){var t;(t=n(this,xi))==null||t.abort(),p(this,xi,null)},Rc=function(){if(n(this,vi))return;p(this,vi,new AbortController);const t=this.combinedSignal(n(this,vi));document.addEventListener("copy",this.copy.bind(this),{signal:t}),document.addEventListener("cut",this.cut.bind(this),{signal:t}),document.addEventListener("paste",this.paste.bind(this),{signal:t})},Pc=function(){var t;(t=n(this,vi))==null||t.abort(),p(this,vi,null)},Vu=function(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})},ee=function(t){Object.entries(t).some(([s,i])=>n(this,To)[s]!==i)&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(n(this,To),t)}),n(this,Mt)===z.HIGHLIGHT&&t.hasSelectedEditor===!1&&b(this,I,Is).call(this,[[Y.HIGHLIGHT_FREE,!0]]))},Is=function(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})},ju=async function(){if(!n(this,Ci)){p(this,Ci,!0);const t=[];for(const e of n(this,Bt).values())t.push(e.enable());await Promise.all(t);for(const e of n(this,Rt).values())e.enable()}},Wu=function(){if(this.unselectAll(),n(this,Ci)){p(this,Ci,!1);for(const t of n(this,Bt).values())t.disable();for(const t of n(this,Rt).values())t.disable()}},Ic=function(t){const e=n(this,Bt).get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))},qu=function(){let t=null;for(t of n(this,et));return t},Ka=function(){if(n(this,Rt).size===0)return!0;if(n(this,Rt).size===1)for(const t of n(this,Rt).values())return t.isEmpty();return!1},kc=function(t){for(const e of n(this,et))e.unselect();n(this,et).clear();for(const e of t)e.isEmpty()||(n(this,et).add(e),e.select());b(this,I,ee).call(this,{hasSelectedEditor:this.hasSelection})},R(Mr,"TRANSLATE_SMALL",1),R(Mr,"TRANSLATE_BIG",10);let br=Mr;var Lt,ps,qe,jr,gs,be,Wr,ms,de,Os,wn,bs,Ii,ns,Qa,nh;const se=class se{constructor(t){m(this,ns);m(this,Lt,null);m(this,ps,!1);m(this,qe,null);m(this,jr,null);m(this,gs,null);m(this,be,null);m(this,Wr,!1);m(this,ms,null);m(this,de,null);m(this,Os,null);m(this,wn,null);m(this,bs,!1);p(this,de,t),p(this,bs,t._uiManager.useNewAltTextFlow),n(se,Ii)||p(se,Ii,Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"}))}static initialize(t){se._l10n??(se._l10n=t)}async render(){const t=p(this,qe,document.createElement("button"));t.className="altText",t.tabIndex="0";const e=p(this,jr,document.createElement("span"));t.append(e),n(this,bs)?(t.classList.add("new"),t.setAttribute("data-l10n-id",n(se,Ii).missing),e.setAttribute("data-l10n-id",n(se,Ii)["missing-label"])):(t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button"),e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label"));const s=n(this,de)._uiManager._signal;t.addEventListener("contextmenu",rs,{signal:s}),t.addEventListener("pointerdown",r=>r.stopPropagation(),{signal:s});const i=r=>{r.preventDefault(),n(this,de)._uiManager.editAltText(n(this,de)),n(this,bs)&&n(this,de)._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:n(this,ns,Qa)}})};return t.addEventListener("click",i,{capture:!0,signal:s}),t.addEventListener("keydown",r=>{r.target===t&&r.key==="Enter"&&(p(this,Wr,!0),i(r))},{signal:s}),await b(this,ns,nh).call(this),t}finish(){n(this,qe)&&(n(this,qe).focus({focusVisible:n(this,Wr)}),p(this,Wr,!1))}isEmpty(){return n(this,bs)?n(this,Lt)===null:!n(this,Lt)&&!n(this,ps)}hasData(){return n(this,bs)?n(this,Lt)!==null||!!n(this,Os):this.isEmpty()}get guessedText(){return n(this,Os)}async setGuessedText(t){n(this,Lt)===null&&(p(this,Os,t),p(this,wn,await se._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t})),b(this,ns,nh).call(this))}toggleAltTextBadge(t=!1){var e;if(!n(this,bs)||n(this,Lt)){(e=n(this,ms))==null||e.remove(),p(this,ms,null);return}if(!n(this,ms)){const s=p(this,ms,document.createElement("div"));s.className="noAltTextBadge",n(this,de).div.append(s)}n(this,ms).classList.toggle("hidden",!t)}serialize(t){let e=n(this,Lt);return!t&&n(this,Os)===e&&(e=n(this,wn)),{altText:e,decorative:n(this,ps),guessedText:n(this,Os),textWithDisclaimer:n(this,wn)}}get data(){return{altText:n(this,Lt),decorative:n(this,ps)}}set data({altText:t,decorative:e,guessedText:s,textWithDisclaimer:i,cancel:r=!1}){s&&(p(this,Os,s),p(this,wn,i)),!(n(this,Lt)===t&&n(this,ps)===e)&&(r||(p(this,Lt,t),p(this,ps,e)),b(this,ns,nh).call(this))}toggle(t=!1){n(this,qe)&&(!t&&n(this,be)&&(clearTimeout(n(this,be)),p(this,be,null)),n(this,qe).disabled=!t)}shown(){n(this,de)._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:n(this,ns,Qa)}})}destroy(){var t,e;(t=n(this,qe))==null||t.remove(),p(this,qe,null),p(this,jr,null),p(this,gs,null),(e=n(this,ms))==null||e.remove(),p(this,ms,null)}};Lt=new WeakMap,ps=new WeakMap,qe=new WeakMap,jr=new WeakMap,gs=new WeakMap,be=new WeakMap,Wr=new WeakMap,ms=new WeakMap,de=new WeakMap,Os=new WeakMap,wn=new WeakMap,bs=new WeakMap,Ii=new WeakMap,ns=new WeakSet,Qa=function(){return n(this,Lt)&&"added"||n(this,Lt)===null&&this.guessedText&&"review"||"missing"},nh=async function(){var i,r,a;const t=n(this,qe);if(!t)return;if(n(this,bs)){if(t.classList.toggle("done",!!n(this,Lt)),t.setAttribute("data-l10n-id",n(se,Ii)[n(this,ns,Qa)]),(i=n(this,jr))==null||i.setAttribute("data-l10n-id",n(se,Ii)[`${n(this,ns,Qa)}-label`]),!n(this,Lt)){(r=n(this,gs))==null||r.remove();return}}else{if(!n(this,Lt)&&!n(this,ps)){t.classList.remove("done"),(a=n(this,gs))==null||a.remove();return}t.classList.add("done"),t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=n(this,gs);if(!e){p(this,gs,e=document.createElement("span")),e.className="tooltip",e.setAttribute("role","tooltip"),e.id=`alt-text-tooltip-${n(this,de).id}`;const o=100,l=n(this,de)._uiManager._signal;l.addEventListener("abort",()=>{clearTimeout(n(this,be)),p(this,be,null)},{once:!0}),t.addEventListener("mouseenter",()=>{p(this,be,setTimeout(()=>{p(this,be,null),n(this,gs).classList.add("show"),n(this,de)._reportTelemetry({action:"alt_text_tooltip"})},o))},{signal:l}),t.addEventListener("mouseleave",()=>{var h;n(this,be)&&(clearTimeout(n(this,be)),p(this,be,null)),(h=n(this,gs))==null||h.classList.remove("show")},{signal:l})}n(this,ps)?e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip"):(e.removeAttribute("data-l10n-id"),e.textContent=n(this,Lt)),e.parentNode||t.append(e);const s=n(this,de).getElementForAltText();s==null||s.setAttribute("aria-describedby",e.id)},m(se,Ii,null),R(se,"_l10n",null);let kh=se;var qr,vn,Po,Io,ko,Mo,Lo,Bs,_n,Hs,Sn,$s,nn,Xu,Yu,Ku;const Kd=class Kd{constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:s=null,onPinchStart:i=null,onPinching:r=null,onPinchEnd:a=null,signal:o}){m(this,nn);m(this,qr);m(this,vn,!1);m(this,Po,null);m(this,Io);m(this,ko);m(this,Mo);m(this,Lo);m(this,Bs,null);m(this,_n);m(this,Hs,null);m(this,Sn);m(this,$s,null);p(this,qr,t),p(this,Po,s),p(this,Io,e),p(this,ko,i),p(this,Mo,r),p(this,Lo,a),p(this,Sn,new AbortController),p(this,_n,AbortSignal.any([o,n(this,Sn).signal])),t.addEventListener("touchstart",b(this,nn,Xu).bind(this),{passive:!1,signal:n(this,_n)})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return 35/ui.pixelRatio}destroy(){var t,e;(t=n(this,Sn))==null||t.abort(),p(this,Sn,null),(e=n(this,Bs))==null||e.abort(),p(this,Bs,null)}};qr=new WeakMap,vn=new WeakMap,Po=new WeakMap,Io=new WeakMap,ko=new WeakMap,Mo=new WeakMap,Lo=new WeakMap,Bs=new WeakMap,_n=new WeakMap,Hs=new WeakMap,Sn=new WeakMap,$s=new WeakMap,nn=new WeakSet,Xu=function(t){var i,r,a;if((i=n(this,Io))!=null&&i.call(this))return;if(t.touches.length===1){if(n(this,Bs))return;const o=p(this,Bs,new AbortController),l=AbortSignal.any([n(this,_n),o.signal]),h=n(this,qr),c={capture:!0,signal:l,passive:!1},u=f=>{var g;f.pointerType==="touch"&&((g=n(this,Bs))==null||g.abort(),p(this,Bs,null))};h.addEventListener("pointerdown",f=>{f.pointerType==="touch"&&(St(f),u(f))},c),h.addEventListener("pointerup",u,c),h.addEventListener("pointercancel",u,c);return}if(!n(this,$s)){p(this,$s,new AbortController);const o=AbortSignal.any([n(this,_n),n(this,$s).signal]),l=n(this,qr),h={signal:o,capture:!1,passive:!1};l.addEventListener("touchmove",b(this,nn,Yu).bind(this),h);const c=b(this,nn,Ku).bind(this);l.addEventListener("touchend",c,h),l.addEventListener("touchcancel",c,h),h.capture=!0,l.addEventListener("pointerdown",St,h),l.addEventListener("pointermove",St,h),l.addEventListener("pointercancel",St,h),l.addEventListener("pointerup",St,h),(r=n(this,ko))==null||r.call(this)}if(St(t),t.touches.length!==2||(a=n(this,Po))!=null&&a.call(this)){p(this,Hs,null);return}let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]),p(this,Hs,{touch0X:e.screenX,touch0Y:e.screenY,touch1X:s.screenX,touch1Y:s.screenY})},Yu=function(t){var E;if(!n(this,Hs)||t.touches.length!==2)return;St(t);let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]);const{screenX:i,screenY:r}=e,{screenX:a,screenY:o}=s,l=n(this,Hs),{touch0X:h,touch0Y:c,touch1X:u,touch1Y:f}=l,g=u-h,y=f-c,A=a-i,w=o-r,v=Math.hypot(A,w)||1,_=Math.hypot(g,y)||1;if(!n(this,vn)&&Math.abs(_-v)<=Kd.MIN_TOUCH_DISTANCE_TO_PINCH)return;if(l.touch0X=i,l.touch0Y=r,l.touch1X=a,l.touch1Y=o,!n(this,vn)){p(this,vn,!0);return}const S=[(i+a)/2,(r+o)/2];(E=n(this,Mo))==null||E.call(this,S,_,v)},Ku=function(t){var e;t.touches.length>=2||(n(this,$s)&&(n(this,$s).abort(),p(this,$s,null),(e=n(this,Lo))==null||e.call(this)),n(this,Hs)&&(St(t),p(this,Hs,null),p(this,vn,!1)))};let Mh=Kd;var En,Xe,ht,Xr,ki,Do,Cn,Ht,xn,Gs,Mi,Fo,Tn,Ae,No,Rn,zs,As,Yr,Kr,Be,Pn,Oo,Uh,G,Mc,Bo,Lc,rh,Qu,Ju,Dc,ah,Fc,Zu,tf,ef,Nc,sf,Oc,nf,rf,af,Bc,Ja;const j=class j{constructor(t){m(this,G);m(this,En,null);m(this,Xe,null);m(this,ht,null);m(this,Xr,!1);m(this,ki,null);m(this,Do,"");m(this,Cn,!1);m(this,Ht,null);m(this,xn,null);m(this,Gs,null);m(this,Mi,null);m(this,Fo,"");m(this,Tn,!1);m(this,Ae,null);m(this,No,!1);m(this,Rn,!1);m(this,zs,!1);m(this,As,null);m(this,Yr,0);m(this,Kr,0);m(this,Be,null);m(this,Pn,null);R(this,"_isCopy",!1);R(this,"_editToolbar",null);R(this,"_initialOptions",Object.create(null));R(this,"_initialData",null);R(this,"_isVisible",!0);R(this,"_uiManager",null);R(this,"_focusEventsAllowed",!0);m(this,Oo,!1);m(this,Uh,j._zIndex++);this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:s,pageHeight:i,pageX:r,pageY:a}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[s,i],this.pageTranslation=[r,a];const[o,l]=this.parentDimensions;this.x=t.x/o,this.y=t.y/l,this.isAttachedToDOM=!1,this.deleted=!1}static get _resizerKeyboardManager(){const t=j.prototype._resizeWithKeyboard,e=br.TRANSLATE_SMALL,s=br.TRANSLATE_BIG;return X(this,"_resizerKeyboardManager",new Gl([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-s,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[s,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-s]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,s]}],[["Escape","mac+Escape"],j.prototype._stopResizingWithKeyboard]]))}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get isDrawer(){return!1}static get _defaultLineColor(){return X(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new ig({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){if(j._l10n??(j._l10n=t),j._l10nResizer||(j._l10nResizer=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"})),j._borderLineWidth!==-1)return;const s=getComputedStyle(document.documentElement);j._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){rt("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return n(this,Oo)}set _isDraggable(t){var e;p(this,Oo,t),(e=this.div)==null||e.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(t*2),this.y+=this.width*t/(e*2);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(t*2),this.y-=this.width*t/(e*2);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=n(this,Uh)}setParent(t){t!==null?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):b(this,G,Ja).call(this),this.parent=t}focusin(t){this._focusEventsAllowed&&(n(this,Tn)?p(this,Tn,!1):this.parent.setSelected(this))}focusout(t){var s;if(!this._focusEventsAllowed||!this.isAttachedToDOM)return;const e=t.relatedTarget;e!=null&&e.closest(`#${this.id}`)||(t.preventDefault(),(s=this.parent)!=null&&s.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,s,i){const[r,a]=this.parentDimensions;[s,i]=this.screenToPageTranslation(s,i),this.x=(t+s)/r,this.y=(e+i)/a,this.fixAndSetPosition()}_moveAfterPaste(t,e){const[s,i]=this.parentDimensions;this.setAt(t*s,e*i,this.width*s,this.height*i),this._onTranslated()}translate(t,e){b(this,G,Mc).call(this,this.parentDimensions,t,e)}translateInPage(t,e){n(this,Ae)||p(this,Ae,[this.x,this.y,this.width,this.height]),b(this,G,Mc).call(this,this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}translationDone(){this._onTranslated(this.x,this.y)}drag(t,e){n(this,Ae)||p(this,Ae,[this.x,this.y,this.width,this.height]);const{div:s,parentDimensions:[i,r]}=this;if(this.x+=t/i,this.y+=e/r,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:u,y:f}=this.div.getBoundingClientRect();this.parent.findNewParent(this,u,f)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:a,y:o}=this;const[l,h]=this.getBaseTranslation();a+=l,o+=h;const{style:c}=s;c.left=`${(100*a).toFixed(2)}%`,c.top=`${(100*o).toFixed(2)}%`,this._onTranslating(a,o),s.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!n(this,Ae)&&(n(this,Ae)[0]!==this.x||n(this,Ae)[1]!==this.y)}get _hasBeenResized(){return!!n(this,Ae)&&(n(this,Ae)[2]!==this.width||n(this,Ae)[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:s}=j,i=s/t,r=s/e;switch(this.rotation){case 90:return[-i,r];case 180:return[i,r];case 270:return[i,-r];default:return[-i,-r]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[s,i]}=this;let{x:r,y:a,width:o,height:l}=this;if(o*=s,l*=i,r*=s,a*=i,this._mustFixPosition)switch(t){case 0:r=oe(r,0,s-o),a=oe(a,0,i-l);break;case 90:r=oe(r,0,s-l),a=oe(a,o,i);break;case 180:r=oe(r,o,s),a=oe(a,l,i);break;case 270:r=oe(r,l,s),a=oe(a,0,i-o);break}this.x=r/=s,this.y=a/=i;const[h,c]=this.getBaseTranslation();r+=h,a+=c,e.left=`${(100*r).toFixed(2)}%`,e.top=`${(100*a).toFixed(2)}%`,this.moveInDOM()}screenToPageTranslation(t,e){var s;return b(s=j,Bo,Lc).call(s,t,e,this.parentRotation)}pageTranslationToScreen(t,e){var s;return b(s=j,Bo,Lc).call(s,t,e,360-this.parentRotation)}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,s]}=this;return[e*t,s*t]}setDims(t,e){const[s,i]=this.parentDimensions,{style:r}=this.div;r.width=`${(100*t/s).toFixed(2)}%`,n(this,Cn)||(r.height=`${(100*e/i).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:s}=t,i=s.endsWith("%"),r=!n(this,Cn)&&e.endsWith("%");if(i&&r)return;const[a,o]=this.parentDimensions;i||(t.width=`${(100*parseFloat(s)/a).toFixed(2)}%`),!n(this,Cn)&&!r&&(t.height=`${(100*parseFloat(e)/o).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}_onResized(){}static _round(t){return Math.round(t*1e4)/1e4}_onResizing(){}altTextFinish(){var t;(t=n(this,ht))==null||t.finish()}async addEditToolbar(){return this._editToolbar||n(this,Rn)?this._editToolbar:(this._editToolbar=new Sc(this),this.div.append(this._editToolbar.render()),n(this,ht)&&await this._editToolbar.addAltText(n(this,ht)),this._editToolbar)}removeEditToolbar(){var t;this._editToolbar&&(this._editToolbar.remove(),this._editToolbar=null,(t=n(this,ht))==null||t.destroy())}addContainer(t){var s;const e=(s=this._editToolbar)==null?void 0:s.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){n(this,ht)||(kh.initialize(j._l10n),p(this,ht,new kh(this)),n(this,En)&&(n(this,ht).data=n(this,En),p(this,En,null)),await this.addEditToolbar())}get altTextData(){var t;return(t=n(this,ht))==null?void 0:t.data}set altTextData(t){n(this,ht)&&(n(this,ht).data=t)}get guessedAltText(){var t;return(t=n(this,ht))==null?void 0:t.guessedText}async setGuessedAltText(t){var e;await((e=n(this,ht))==null?void 0:e.setGuessedText(t))}serializeAltText(t){var e;return(e=n(this,ht))==null?void 0:e.serialize(t)}hasAltText(){return!!n(this,ht)&&!n(this,ht).isEmpty()}hasAltTextData(){var t;return((t=n(this,ht))==null?void 0:t.hasData())??!1}render(){var a;const t=this.div=document.createElement("div");t.setAttribute("data-editor-rotation",(360-this.rotation)%360),t.className=this.name,t.setAttribute("id",this.id),t.tabIndex=n(this,Xr)?-1:0,t.setAttribute("role","application"),this.defaultL10nId&&t.setAttribute("data-l10n-id",this.defaultL10nId),this._isVisible||t.classList.add("hidden"),this.setInForeground(),b(this,G,Oc).call(this);const[e,s]=this.parentDimensions;this.parentRotation%180!==0&&(t.style.maxWidth=`${(100*s/e).toFixed(2)}%`,t.style.maxHeight=`${(100*e/s).toFixed(2)}%`);const[i,r]=this.getInitialTranslation();return this.translate(i,r),zd(this,t,["keydown","pointerdown"]),this.isResizable&&this._uiManager._supportsPinchToZoom&&(n(this,Pn)||p(this,Pn,new Mh({container:t,isPinchingDisabled:()=>!this.isSelected,onPinchStart:b(this,G,Zu).bind(this),onPinching:b(this,G,tf).bind(this),onPinchEnd:b(this,G,ef).bind(this),signal:this._uiManager._signal}))),(a=this._uiManager._editorUndoBar)==null||a.hide(),t}pointerdown(t){const{isMac:e}=Wt.platform;if(t.button!==0||t.ctrlKey&&e){t.preventDefault();return}if(p(this,Tn,!0),this._isDraggable){b(this,G,sf).call(this,t);return}b(this,G,Nc).call(this,t)}get isSelected(){return this._uiManager.isSelected(this)}_onStartDragging(){}_onStopDragging(){}moveInDOM(){n(this,As)&&clearTimeout(n(this,As)),p(this,As,setTimeout(()=>{var t;p(this,As,null),(t=this.parent)==null||t.moveEditorInDOM(this)},0))}_setParentAndPosition(t,e,s){t.changeParent(this),this.x=e,this.y=s,this.fixAndSetPosition(),this._onTranslated()}getRect(t,e,s=this.rotation){const i=this.parentScale,[r,a]=this.pageDimensions,[o,l]=this.pageTranslation,h=t/i,c=e/i,u=this.x*r,f=this.y*a,g=this.width*r,y=this.height*a;switch(s){case 0:return[u+h+o,a-f-c-y+l,u+h+g+o,a-f-c+l];case 90:return[u+c+o,a-f+h+l,u+c+y+o,a-f+h+g+l];case 180:return[u-h-g+o,a-f+c+l,u-h+o,a-f+c+y+l];case 270:return[u-c-y+o,a-f-h-g+l,u-c+o,a-f-h+l];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[s,i,r,a]=t,o=r-s,l=a-i;switch(this.rotation){case 0:return[s,e-a,o,l];case 90:return[s,e-i,l,o];case 180:return[r,e-i,o,l];case 270:return[r,e-a,l,o];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){p(this,Rn,!0)}disableEditMode(){p(this,Rn,!1)}isInEditMode(){return n(this,Rn)}shouldGetKeyboardEvents(){return n(this,zs)}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:s,right:i}=this.getClientDimensions(),{innerHeight:r,innerWidth:a}=window;return e<a&&i>0&&t<r&&s>0}rebuild(){b(this,G,Oc).call(this)}rotate(t){}resize(){}serializeDeleted(){var t;return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:((t=this._initialData)==null?void 0:t.popupRef)||""}}serialize(t=!1,e=null){rt("An editor must be serializable")}static async deserialize(t,e,s){const i=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:s});i.rotation=t.rotation,p(i,En,t.accessibilityData),i._isCopy=t.isCopy||!1;const[r,a]=i.pageDimensions,[o,l,h,c]=i.getRectInCurrentCoords(t.rect,a);return i.x=o/r,i.y=l/a,i.width=h/r,i.height=c/a,i}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||this.serialize()!==null)}remove(){var t,e;if((t=n(this,Mi))==null||t.abort(),p(this,Mi,null),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),n(this,As)&&(clearTimeout(n(this,As)),p(this,As,null)),b(this,G,Ja).call(this),this.removeEditToolbar(),n(this,Be)){for(const s of n(this,Be).values())clearTimeout(s);p(this,Be,null)}this.parent=null,(e=n(this,Pn))==null||e.destroy(),p(this,Pn,null)}get isResizable(){return!1}makeResizable(){this.isResizable&&(b(this,G,Qu).call(this),n(this,Ht).classList.remove("hidden"))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||t.key!=="Enter")return;this._uiManager.setSelected(this),p(this,Gs,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const e=n(this,Ht).children;if(!n(this,Xe)){p(this,Xe,Array.from(e));const a=b(this,G,nf).bind(this),o=b(this,G,rf).bind(this),l=this._uiManager._signal;for(const h of n(this,Xe)){const c=h.getAttribute("data-resizer-name");h.setAttribute("role","spinbutton"),h.addEventListener("keydown",a,{signal:l}),h.addEventListener("blur",o,{signal:l}),h.addEventListener("focus",b(this,G,af).bind(this,c),{signal:l}),h.setAttribute("data-l10n-id",j._l10nResizer[c])}}const s=n(this,Xe)[0];let i=0;for(const a of e){if(a===s)break;i++}const r=(360-this.rotation+this.parentRotation)%360/90*(n(this,Xe).length/4);if(r!==i){if(r<i)for(let o=0;o<i-r;o++)n(this,Ht).append(n(this,Ht).firstChild);else if(r>i)for(let o=0;o<r-i;o++)n(this,Ht).firstChild.before(n(this,Ht).lastChild);let a=0;for(const o of e){const h=n(this,Xe)[a++].getAttribute("data-resizer-name");o.setAttribute("data-l10n-id",j._l10nResizer[h])}}b(this,G,Bc).call(this,0),p(this,zs,!0),n(this,Ht).firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}_resizeWithKeyboard(t,e){n(this,zs)&&b(this,G,Fc).call(this,n(this,Fo),{deltaX:t,deltaY:e,fromKeyboard:!0})}_stopResizingWithKeyboard(){b(this,G,Ja).call(this),this.div.focus()}select(){var t,e,s;if(this.makeResizable(),(t=this.div)==null||t.classList.add("selectedEditor"),!this._editToolbar){this.addEditToolbar().then(()=>{var i,r;(i=this.div)!=null&&i.classList.contains("selectedEditor")&&((r=this._editToolbar)==null||r.show())});return}(e=this._editToolbar)==null||e.show(),(s=n(this,ht))==null||s.toggleAltTextBadge(!1)}unselect(){var t,e,s,i,r;(t=n(this,Ht))==null||t.classList.add("hidden"),(e=this.div)==null||e.classList.remove("selectedEditor"),(s=this.div)!=null&&s.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),(i=this._editToolbar)==null||i.hide(),(r=n(this,ht))==null||r.toggleAltTextBadge(!0)}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getElementForAltText(){return this.div}get contentDiv(){return this.div}get isEditing(){return n(this,No)}set isEditing(t){p(this,No,t),this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){p(this,Cn,!0);const s=t/e,{style:i}=this.div;i.aspectRatio=s,i.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){n(this,Be)||p(this,Be,new Map);const{action:s}=t;let i=n(this,Be).get(s);i&&clearTimeout(i),i=setTimeout(()=>{this._reportTelemetry(t),n(this,Be).delete(s),n(this,Be).size===0&&p(this,Be,null)},j._telemetryTimeout),n(this,Be).set(s,i);return}t.type||(t.type=this.editorType),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),p(this,Xr,!1)}disable(){this.div&&(this.div.tabIndex=-1),p(this,Xr,!0)}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(!e)e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);else if(e.nodeName==="CANVAS"){const s=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),s.before(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;(e==null?void 0:e.nodeName)==="DIV"&&e.classList.contains("annotationContent")&&e.remove()}};En=new WeakMap,Xe=new WeakMap,ht=new WeakMap,Xr=new WeakMap,ki=new WeakMap,Do=new WeakMap,Cn=new WeakMap,Ht=new WeakMap,xn=new WeakMap,Gs=new WeakMap,Mi=new WeakMap,Fo=new WeakMap,Tn=new WeakMap,Ae=new WeakMap,No=new WeakMap,Rn=new WeakMap,zs=new WeakMap,As=new WeakMap,Yr=new WeakMap,Kr=new WeakMap,Be=new WeakMap,Pn=new WeakMap,Oo=new WeakMap,Uh=new WeakMap,G=new WeakSet,Mc=function([t,e],s,i){[s,i]=this.screenToPageTranslation(s,i),this.x+=s/t,this.y+=i/e,this._onTranslating(this.x,this.y),this.fixAndSetPosition()},Bo=new WeakSet,Lc=function(t,e,s){switch(s){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}},rh=function(t){switch(t){case 90:{const[e,s]=this.pageDimensions;return[0,-e/s,s/e,0]}case 180:return[-1,0,0,-1];case 270:{const[e,s]=this.pageDimensions;return[0,e/s,-s/e,0]}default:return[1,0,0,1]}},Qu=function(){if(n(this,Ht))return;p(this,Ht,document.createElement("div")),n(this,Ht).classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const s of t){const i=document.createElement("div");n(this,Ht).append(i),i.classList.add("resizer",s),i.setAttribute("data-resizer-name",s),i.addEventListener("pointerdown",b(this,G,Ju).bind(this,s),{signal:e}),i.addEventListener("contextmenu",rs,{signal:e}),i.tabIndex=-1}this.div.prepend(n(this,Ht))},Ju=function(t,e){var c;e.preventDefault();const{isMac:s}=Wt.platform;if(e.button!==0||e.ctrlKey&&s)return;(c=n(this,ht))==null||c.toggle(!1);const i=this._isDraggable;this._isDraggable=!1,p(this,xn,[e.screenX,e.screenY]);const r=new AbortController,a=this._uiManager.combinedSignal(r);this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",b(this,G,Fc).bind(this,t),{passive:!0,capture:!0,signal:a}),window.addEventListener("touchmove",St,{passive:!1,signal:a}),window.addEventListener("contextmenu",rs,{signal:a}),p(this,Gs,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const o=this.parent.div.style.cursor,l=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const h=()=>{var u;r.abort(),this.parent.togglePointerEvents(!0),(u=n(this,ht))==null||u.toggle(!0),this._isDraggable=i,this.parent.div.style.cursor=o,this.div.style.cursor=l,b(this,G,ah).call(this)};window.addEventListener("pointerup",h,{signal:a}),window.addEventListener("blur",h,{signal:a})},Dc=function(t,e,s,i){this.width=s,this.height=i,this.x=t,this.y=e;const[r,a]=this.parentDimensions;this.setDims(r*s,a*i),this.fixAndSetPosition(),this._onResized()},ah=function(){if(!n(this,Gs))return;const{savedX:t,savedY:e,savedWidth:s,savedHeight:i}=n(this,Gs);p(this,Gs,null);const r=this.x,a=this.y,o=this.width,l=this.height;r===t&&a===e&&o===s&&l===i||this.addCommands({cmd:b(this,G,Dc).bind(this,r,a,o,l),undo:b(this,G,Dc).bind(this,t,e,s,i),mustExec:!0})},Fc=function(t,e){const[s,i]=this.parentDimensions,r=this.x,a=this.y,o=this.width,l=this.height,h=j.MIN_SIZE/s,c=j.MIN_SIZE/i,u=b(this,G,rh).call(this,this.rotation),f=(N,$)=>[u[0]*N+u[2]*$,u[1]*N+u[3]*$],g=b(this,G,rh).call(this,360-this.rotation),y=(N,$)=>[g[0]*N+g[2]*$,g[1]*N+g[3]*$];let A,w,v=!1,_=!1;switch(t){case"topLeft":v=!0,A=(N,$)=>[0,0],w=(N,$)=>[N,$];break;case"topMiddle":A=(N,$)=>[N/2,0],w=(N,$)=>[N/2,$];break;case"topRight":v=!0,A=(N,$)=>[N,0],w=(N,$)=>[0,$];break;case"middleRight":_=!0,A=(N,$)=>[N,$/2],w=(N,$)=>[0,$/2];break;case"bottomRight":v=!0,A=(N,$)=>[N,$],w=(N,$)=>[0,0];break;case"bottomMiddle":A=(N,$)=>[N/2,$],w=(N,$)=>[N/2,0];break;case"bottomLeft":v=!0,A=(N,$)=>[0,$],w=(N,$)=>[N,0];break;case"middleLeft":_=!0,A=(N,$)=>[0,$/2],w=(N,$)=>[N,$/2];break}const S=A(o,l),E=w(o,l);let C=f(...E);const x=j._round(r+C[0]),T=j._round(a+C[1]);let P=1,k=1,B,D;if(e.fromKeyboard)({deltaX:B,deltaY:D}=e);else{const{screenX:N,screenY:$}=e,[Le,Ts]=n(this,xn);[B,D]=this.screenToPageTranslation(N-Le,$-Ts),n(this,xn)[0]=N,n(this,xn)[1]=$}if([B,D]=y(B/s,D/i),v){const N=Math.hypot(o,l);P=k=Math.max(Math.min(Math.hypot(E[0]-S[0]-B,E[1]-S[1]-D)/N,1/o,1/l),h/o,c/l)}else _?P=oe(Math.abs(E[0]-S[0]-B),h,1)/o:k=oe(Math.abs(E[1]-S[1]-D),c,1)/l;const tt=j._round(o*P),st=j._round(l*k);C=f(...w(tt,st));const q=x-C[0],Zt=T-C[1];n(this,Ae)||p(this,Ae,[this.x,this.y,this.width,this.height]),this.width=tt,this.height=st,this.x=q,this.y=Zt,this.setDims(s*tt,i*st),this.fixAndSetPosition(),this._onResizing()},Zu=function(){var t;p(this,Gs,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height}),(t=n(this,ht))==null||t.toggle(!1),this.parent.togglePointerEvents(!1)},tf=function(t,e,s){let r=.7*(s/e)+1-.7;if(r===1)return;const a=b(this,G,rh).call(this,this.rotation),o=(x,T)=>[a[0]*x+a[2]*T,a[1]*x+a[3]*T],[l,h]=this.parentDimensions,c=this.x,u=this.y,f=this.width,g=this.height,y=j.MIN_SIZE/l,A=j.MIN_SIZE/h;r=Math.max(Math.min(r,1/f,1/g),y/f,A/g);const w=j._round(f*r),v=j._round(g*r);if(w===f&&v===g)return;n(this,Ae)||p(this,Ae,[c,u,f,g]);const _=o(f/2,g/2),S=j._round(c+_[0]),E=j._round(u+_[1]),C=o(w/2,v/2);this.x=S-C[0],this.y=E-C[1],this.width=w,this.height=v,this.setDims(l*w,h*v),this.fixAndSetPosition(),this._onResizing()},ef=function(){var t;(t=n(this,ht))==null||t.toggle(!0),this.parent.togglePointerEvents(!0),b(this,G,ah).call(this)},Nc=function(t){const{isMac:e}=Wt.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)},sf=function(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let s=!1;const i=new AbortController,r=this._uiManager.combinedSignal(i),a={capture:!0,passive:!1,signal:r},o=h=>{i.abort(),p(this,ki,null),p(this,Tn,!1),this._uiManager.endDragSession()||b(this,G,Nc).call(this,h),s&&this._onStopDragging()};e&&(p(this,Yr,t.clientX),p(this,Kr,t.clientY),p(this,ki,t.pointerId),p(this,Do,t.pointerType),window.addEventListener("pointermove",h=>{s||(s=!0,this._onStartDragging());const{clientX:c,clientY:u,pointerId:f}=h;if(f!==n(this,ki)){St(h);return}const[g,y]=this.screenToPageTranslation(c-n(this,Yr),u-n(this,Kr));p(this,Yr,c),p(this,Kr,u),this._uiManager.dragSelectedEditors(g,y)},a),window.addEventListener("touchmove",St,a),window.addEventListener("pointerdown",h=>{h.pointerType===n(this,Do)&&(n(this,Pn)||h.isPrimary)&&o(h),St(h)},a));const l=h=>{if(!n(this,ki)||n(this,ki)===h.pointerId){o(h);return}St(h)};window.addEventListener("pointerup",l,{signal:r}),window.addEventListener("blur",l,{signal:r})},Oc=function(){if(n(this,Mi)||!this.div)return;p(this,Mi,new AbortController);const t=this._uiManager.combinedSignal(n(this,Mi));this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t}),this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})},nf=function(t){j._resizerKeyboardManager.exec(this,t)},rf=function(t){var e;n(this,zs)&&((e=t.relatedTarget)==null?void 0:e.parentNode)!==n(this,Ht)&&b(this,G,Ja).call(this)},af=function(t){p(this,Fo,n(this,zs)?t:"")},Bc=function(t){if(n(this,Xe))for(const e of n(this,Xe))e.tabIndex=t},Ja=function(){p(this,zs,!1),b(this,G,Bc).call(this,-1),b(this,G,ah).call(this)},m(j,Bo),R(j,"_l10n",null),R(j,"_l10nResizer",null),R(j,"_borderLineWidth",-1),R(j,"_colorManager",new Cc),R(j,"_zIndex",1),R(j,"_telemetryTimeout",1e3);let lt=j;class ig extends lt{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return this.serializeDeleted()}}const ru=3285377520,De=4294901760,ls=65535;class of{constructor(t){this.h1=t?t&4294967295:ru,this.h2=t?t&4294967295:ru}update(t){let e,s;if(typeof t=="string"){e=new Uint8Array(t.length*2),s=0;for(let A=0,w=t.length;A<w;A++){const v=t.charCodeAt(A);v<=255?e[s++]=v:(e[s++]=v>>>8,e[s++]=v&255)}}else if(ArrayBuffer.isView(t))e=t.slice(),s=e.byteLength;else throw new Error("Invalid data format, must be a string or TypedArray.");const i=s>>2,r=s-i*4,a=new Uint32Array(e.buffer,0,i);let o=0,l=0,h=this.h1,c=this.h2;const u=3432918353,f=461845907,g=u&ls,y=f&ls;for(let A=0;A<i;A++)A&1?(o=a[A],o=o*u&De|o*g&ls,o=o<<15|o>>>17,o=o*f&De|o*y&ls,h^=o,h=h<<13|h>>>19,h=h*5+3864292196):(l=a[A],l=l*u&De|l*g&ls,l=l<<15|l>>>17,l=l*f&De|l*y&ls,c^=l,c=c<<13|c>>>19,c=c*5+3864292196);switch(o=0,r){case 3:o^=e[i*4+2]<<16;case 2:o^=e[i*4+1]<<8;case 1:o^=e[i*4],o=o*u&De|o*g&ls,o=o<<15|o>>>17,o=o*f&De|o*y&ls,i&1?h^=o:c^=o}this.h1=h,this.h2=c}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=t*3981806797&De|t*36045&ls,e=e*4283543511&De|((e<<16|t>>>16)*2950163797&De)>>>16,t^=e>>>1,t=t*444984403&De|t*60499&ls,e=e*3301882366&De|((e<<16|t>>>16)*3120437893&De)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const Hc=Object.freeze({map:null,hash:"",transfer:void 0});var In,kn,$t,Vh,lf;class Ud{constructor(){m(this,Vh);m(this,In,!1);m(this,kn,null);m(this,$t,new Map);this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const s=n(this,$t).get(t);return s===void 0?e:Object.assign(e,s)}getRawValue(t){return n(this,$t).get(t)}remove(t){if(n(this,$t).delete(t),n(this,$t).size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const e of n(this,$t).values())if(e instanceof lt)return;this.onAnnotationEditor(null)}}setValue(t,e){const s=n(this,$t).get(t);let i=!1;if(s!==void 0)for(const[r,a]of Object.entries(e))s[r]!==a&&(i=!0,s[r]=a);else i=!0,n(this,$t).set(t,e);i&&b(this,Vh,lf).call(this),e instanceof lt&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(e.constructor._type)}has(t){return n(this,$t).has(t)}get size(){return n(this,$t).size}resetModified(){n(this,In)&&(p(this,In,!1),typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new hf(this)}get serializable(){if(n(this,$t).size===0)return Hc;const t=new Map,e=new of,s=[],i=Object.create(null);let r=!1;for(const[a,o]of n(this,$t)){const l=o instanceof lt?o.serialize(!1,i):o;l&&(t.set(a,l),e.update(`${a}:${JSON.stringify(l)}`),r||(r=!!l.bitmap))}if(r)for(const a of t.values())a.bitmap&&s.push(a.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:s}:Hc}get editorStats(){let t=null;const e=new Map;for(const s of n(this,$t).values()){if(!(s instanceof lt))continue;const i=s.telemetryFinalData;if(!i)continue;const{type:r}=i;e.has(r)||e.set(r,Object.getPrototypeOf(s).constructor),t||(t=Object.create(null));const a=t[r]||(t[r]=new Map);for(const[o,l]of Object.entries(i)){if(o==="type")continue;let h=a.get(o);h||(h=new Map,a.set(o,h));const c=h.get(l)??0;h.set(l,c+1)}}for(const[s,i]of e)t[s]=i.computeTelemetryFinalData(t[s]);return t}resetModifiedIds(){p(this,kn,null)}get modifiedIds(){if(n(this,kn))return n(this,kn);const t=[];for(const e of n(this,$t).values())!(e instanceof lt)||!e.annotationElementId||!e.serialize()||t.push(e.annotationElementId);return p(this,kn,{ids:new Set(t),hash:t.join(",")})}[Symbol.iterator](){return n(this,$t).entries()}}In=new WeakMap,kn=new WeakMap,$t=new WeakMap,Vh=new WeakSet,lf=function(){n(this,In)||(p(this,In,!0),typeof this.onSetModified=="function"&&this.onSetModified())};var Ho;class hf extends Ud{constructor(e){super();m(this,Ho);const{map:s,hash:i,transfer:r}=e.serializable,a=structuredClone(s,r?{transfer:r}:null);p(this,Ho,{map:a,hash:i,transfer:r})}get print(){rt("Should not call PrintAnnotationStorage.print")}get serializable(){return n(this,Ho)}get modifiedIds(){return X(this,"modifiedIds",{ids:new Set,hash:""})}}Ho=new WeakMap;var Qr;class ng{constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){m(this,Qr,new Set);this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),n(this,Qr).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,disableFontFace:e,_inspectFont:s}){if(!(!t||n(this,Qr).has(t.loadedName))){if(Et(!e,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:i,src:r,style:a}=t,o=new FontFace(i,r,a);this.addNativeFontFace(o);try{await o.load(),n(this,Qr).add(i),s==null||s(t)}catch{U(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(o)}return}rt("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const s=t.createNativeFontFace();if(s){this.addNativeFontFace(s);try{await s.loaded}catch(i){throw U(`Failed to load font '${s.family}': '${i}'.`),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise(s=>{const i=this._queueLoadingCallback(s);this._prepareFontLoadEvent(t,i)})}}get isFontLoadingAPISupported(){var e;const t=!!((e=this._document)!=null&&e.fonts);return X(this,"isFontLoadingAPISupported",t)}get isSyncFontLoadingSupported(){return X(this,"isSyncFontLoadingSupported",ae||Wt.platform.isFirefox)}_queueLoadingCallback(t){function e(){for(Et(!i.done,"completeRequest() cannot be called twice."),i.done=!0;s.length>0&&s[0].done;){const r=s.shift();setTimeout(r.callback,0)}}const{loadingRequests:s}=this,i={done:!1,complete:e,callback:t};return s.push(i),i}get _loadTestFont(){const t=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return X(this,"_loadTestFont",t)}_prepareFontLoadEvent(t,e){function s(E,C){return E.charCodeAt(C)<<24|E.charCodeAt(C+1)<<16|E.charCodeAt(C+2)<<8|E.charCodeAt(C+3)&255}function i(E,C,x,T){const P=E.substring(0,C),k=E.substring(C+x);return P+T+k}let r,a;const o=this._document.createElement("canvas");o.width=1,o.height=1;const l=o.getContext("2d");let h=0;function c(E,C){if(++h>30){U("Load test font never loaded."),C();return}if(l.font="30px "+E,l.fillText(".",0,20),l.getImageData(0,0,1,1).data[3]>0){C();return}setTimeout(c.bind(null,E,C))}const u=`lt${Date.now()}${this.loadTestFontId++}`;let f=this._loadTestFont;f=i(f,976,u.length,u);const y=16,A=1482184792;let w=s(f,y);for(r=0,a=u.length-3;r<a;r+=4)w=w-A+s(u,r)|0;r<u.length&&(w=w-A+s(u+"XXX",r)|0),f=i(f,y,4,Up(w));const v=`url(data:font/opentype;base64,${btoa(f)});`,_=`@font-face {font-family:"${u}";src:${v}}`;this.insertRule(_);const S=this._document.createElement("div");S.style.visibility="hidden",S.style.width=S.style.height="10px",S.style.position="absolute",S.style.top=S.style.left="0px";for(const E of[t.loadedName,u]){const C=this._document.createElement("span");C.textContent="Hi",C.style.fontFamily=E,S.append(C)}this._document.body.append(S),c(u,()=>{S.remove(),e.complete()})}}Qr=new WeakMap;class rg{constructor(t,e=null){this.compiledGlyphs=Object.create(null);for(const s in t)this[s]=t[s];this._inspectFont=e}createNativeFontFace(){var e;if(!this.data||this.disableFontFace)return null;let t;if(!this.cssFontInfo)t=new FontFace(this.loadedName,this.data,{});else{const s={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(s.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,s)}return(e=this._inspectFont)==null||e.call(this,this),t}createFontFaceRule(){var s;if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${Pu(this.data)});`;let e;if(!this.cssFontInfo)e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;else{let i=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(i+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${i}src:${t}}`}return(s=this._inspectFont)==null||s.call(this,this,t),e}getPathGenerator(t,e){if(this.compiledGlyphs[e]!==void 0)return this.compiledGlyphs[e];const s=this.loadedName+"_path_"+e;let i;try{i=t.get(s)}catch(a){U(`getPathGenerator - ignoring character: "${a}".`)}const r=new Path2D(i||"");return this.fontExtraProperties||t.delete(s),this.compiledGlyphs[e]=r}}const Wl={DATA:1,ERROR:2},vt={CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function au(){}function he(d){if(d instanceof tn||d instanceof wc||d instanceof su||d instanceof Ih||d instanceof uc)return d;switch(d instanceof Error||typeof d=="object"&&d!==null||rt('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),d.name){case"AbortException":return new tn(d.message);case"InvalidPDFException":return new wc(d.message);case"PasswordException":return new su(d.message,d.code);case"ResponseException":return new Ih(d.message,d.status,d.missing);case"UnknownErrorException":return new uc(d.message,d.details)}return new uc(d.message,d.toString())}var Jr,Ve,cf,df,uf,oh;class Za{constructor(t,e,s){m(this,Ve);m(this,Jr,new AbortController);this.sourceName=t,this.targetName=e,this.comObj=s,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),s.addEventListener("message",b(this,Ve,cf).bind(this),{signal:n(this,Jr).signal})}on(t,e){const s=this.actionHandler;if(s[t])throw new Error(`There is already an actionName called "${t}"`);s[t]=e}send(t,e,s){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},s)}sendWithPromise(t,e,s){const i=this.callbackId++,r=Promise.withResolvers();this.callbackCapabilities[i]=r;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:i,data:e},s)}catch(a){r.reject(a)}return r.promise}sendWithStream(t,e,s,i){const r=this.streamId++,a=this.sourceName,o=this.targetName,l=this.comObj;return new ReadableStream({start:h=>{const c=Promise.withResolvers();return this.streamControllers[r]={controller:h,startCall:c,pullCall:null,cancelCall:null,isClosed:!1},l.postMessage({sourceName:a,targetName:o,action:t,streamId:r,data:e,desiredSize:h.desiredSize},i),c.promise},pull:h=>{const c=Promise.withResolvers();return this.streamControllers[r].pullCall=c,l.postMessage({sourceName:a,targetName:o,stream:vt.PULL,streamId:r,desiredSize:h.desiredSize}),c.promise},cancel:h=>{Et(h instanceof Error,"cancel must have a valid reason");const c=Promise.withResolvers();return this.streamControllers[r].cancelCall=c,this.streamControllers[r].isClosed=!0,l.postMessage({sourceName:a,targetName:o,stream:vt.CANCEL,streamId:r,reason:he(h)}),c.promise}},s)}destroy(){var t;(t=n(this,Jr))==null||t.abort(),p(this,Jr,null)}}Jr=new WeakMap,Ve=new WeakSet,cf=function({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream){b(this,Ve,uf).call(this,t);return}if(t.callback){const s=t.callbackId,i=this.callbackCapabilities[s];if(!i)throw new Error(`Cannot resolve callback ${s}`);if(delete this.callbackCapabilities[s],t.callback===Wl.DATA)i.resolve(t.data);else if(t.callback===Wl.ERROR)i.reject(he(t.reason));else throw new Error("Unexpected callback case");return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const s=this.sourceName,i=t.sourceName,r=this.comObj;Promise.try(e,t.data).then(function(a){r.postMessage({sourceName:s,targetName:i,callback:Wl.DATA,callbackId:t.callbackId,data:a})},function(a){r.postMessage({sourceName:s,targetName:i,callback:Wl.ERROR,callbackId:t.callbackId,reason:he(a)})});return}if(t.streamId){b(this,Ve,df).call(this,t);return}e(t.data)},df=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,r=this.comObj,a=this,o=this.actionHandler[t.action],l={enqueue(h,c=1,u){if(this.isCancelled)return;const f=this.desiredSize;this.desiredSize-=c,f>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),r.postMessage({sourceName:s,targetName:i,stream:vt.ENQUEUE,streamId:e,chunk:h},u)},close(){this.isCancelled||(this.isCancelled=!0,r.postMessage({sourceName:s,targetName:i,stream:vt.CLOSE,streamId:e}),delete a.streamSinks[e])},error(h){Et(h instanceof Error,"error must have a valid reason"),!this.isCancelled&&(this.isCancelled=!0,r.postMessage({sourceName:s,targetName:i,stream:vt.ERROR,streamId:e,reason:he(h)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};l.sinkCapability.resolve(),l.ready=l.sinkCapability.promise,this.streamSinks[e]=l,Promise.try(o,t.data,l).then(function(){r.postMessage({sourceName:s,targetName:i,stream:vt.START_COMPLETE,streamId:e,success:!0})},function(h){r.postMessage({sourceName:s,targetName:i,stream:vt.START_COMPLETE,streamId:e,reason:he(h)})})},uf=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,r=this.comObj,a=this.streamControllers[e],o=this.streamSinks[e];switch(t.stream){case vt.START_COMPLETE:t.success?a.startCall.resolve():a.startCall.reject(he(t.reason));break;case vt.PULL_COMPLETE:t.success?a.pullCall.resolve():a.pullCall.reject(he(t.reason));break;case vt.PULL:if(!o){r.postMessage({sourceName:s,targetName:i,stream:vt.PULL_COMPLETE,streamId:e,success:!0});break}o.desiredSize<=0&&t.desiredSize>0&&o.sinkCapability.resolve(),o.desiredSize=t.desiredSize,Promise.try(o.onPull||au).then(function(){r.postMessage({sourceName:s,targetName:i,stream:vt.PULL_COMPLETE,streamId:e,success:!0})},function(h){r.postMessage({sourceName:s,targetName:i,stream:vt.PULL_COMPLETE,streamId:e,reason:he(h)})});break;case vt.ENQUEUE:if(Et(a,"enqueue should have stream controller"),a.isClosed)break;a.controller.enqueue(t.chunk);break;case vt.CLOSE:if(Et(a,"close should have stream controller"),a.isClosed)break;a.isClosed=!0,a.controller.close(),b(this,Ve,oh).call(this,a,e);break;case vt.ERROR:Et(a,"error should have stream controller"),a.controller.error(he(t.reason)),b(this,Ve,oh).call(this,a,e);break;case vt.CANCEL_COMPLETE:t.success?a.cancelCall.resolve():a.cancelCall.reject(he(t.reason)),b(this,Ve,oh).call(this,a,e);break;case vt.CANCEL:if(!o)break;const l=he(t.reason);Promise.try(o.onCancel||au,l).then(function(){r.postMessage({sourceName:s,targetName:i,stream:vt.CANCEL_COMPLETE,streamId:e,success:!0})},function(h){r.postMessage({sourceName:s,targetName:i,stream:vt.CANCEL_COMPLETE,streamId:e,reason:he(h)})}),o.sinkCapability.reject(l),o.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}},oh=async function(t,e){var s,i,r;await Promise.allSettled([(s=t.startCall)==null?void 0:s.promise,(i=t.pullCall)==null?void 0:i.promise,(r=t.cancelCall)==null?void 0:r.promise]),delete this.streamControllers[e]};var $o;class ff{constructor({enableHWA:t=!1}){m(this,$o,!1);p(this,$o,t)}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const s=this._createCanvas(t,e);return{canvas:s,context:s.getContext("2d",{willReadFrequently:!n(this,$o)})}}reset(t,e,s){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||s<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=s}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){rt("Abstract method `_createCanvas` called.")}}$o=new WeakMap;class ag extends ff{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){const s=this._document.createElement("canvas");return s.width=t,s.height=e,s}}class pf{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then(s=>({cMapData:s,isCompressed:this.isCompressed})).catch(s=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)})}async _fetch(t){rt("Abstract method `_fetch` called.")}}class ou extends pf{async _fetch(t){const e=await Hl(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):Bl(e)}}class gf{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,s,i,r){return"none"}destroy(t=!1){}}var Mn,Zr,Us,Vs,Kt,Ln,Dn,M,Xt,to,Cr,lh,xr,mf,$c,Tr,eo,so,Gc,io;class og extends gf{constructor({docId:e,ownerDocument:s=globalThis.document}){super();m(this,M);m(this,Mn);m(this,Zr);m(this,Us);m(this,Vs);m(this,Kt);m(this,Ln);m(this,Dn,0);p(this,Vs,e),p(this,Kt,s)}addFilter(e){if(!e)return"none";let s=n(this,M,Xt).get(e);if(s)return s;const[i,r,a]=b(this,M,lh).call(this,e),o=e.length===1?i:`${i}${r}${a}`;if(s=n(this,M,Xt).get(o),s)return n(this,M,Xt).set(e,s),s;const l=`g_${n(this,Vs)}_transfer_map_${te(this,Dn)._++}`,h=b(this,M,xr).call(this,l);n(this,M,Xt).set(e,h),n(this,M,Xt).set(o,h);const c=b(this,M,Tr).call(this,l);return b(this,M,so).call(this,i,r,a,c),h}addHCMFilter(e,s){var y;const i=`${e}-${s}`,r="base";let a=n(this,M,to).get(r);if((a==null?void 0:a.key)===i||(a?((y=a.filter)==null||y.remove(),a.key=i,a.url="none",a.filter=null):(a={key:i,url:"none",filter:null},n(this,M,to).set(r,a)),!e||!s))return a.url;const o=b(this,M,io).call(this,e);e=O.makeHexColor(...o);const l=b(this,M,io).call(this,s);if(s=O.makeHexColor(...l),n(this,M,Cr).style.color="",e==="#000000"&&s==="#ffffff"||e===s)return a.url;const h=new Array(256);for(let A=0;A<=255;A++){const w=A/255;h[A]=w<=.03928?w/12.92:((w+.055)/1.055)**2.4}const c=h.join(","),u=`g_${n(this,Vs)}_hcm_filter`,f=a.filter=b(this,M,Tr).call(this,u);b(this,M,so).call(this,c,c,c,f),b(this,M,$c).call(this,f);const g=(A,w)=>{const v=o[A]/255,_=l[A]/255,S=new Array(w+1);for(let E=0;E<=w;E++)S[E]=v+E/w*(_-v);return S.join(",")};return b(this,M,so).call(this,g(0,5),g(1,5),g(2,5),f),a.url=b(this,M,xr).call(this,u),a.url}addAlphaFilter(e){let s=n(this,M,Xt).get(e);if(s)return s;const[i]=b(this,M,lh).call(this,[e]),r=`alpha_${i}`;if(s=n(this,M,Xt).get(r),s)return n(this,M,Xt).set(e,s),s;const a=`g_${n(this,Vs)}_alpha_map_${te(this,Dn)._++}`,o=b(this,M,xr).call(this,a);n(this,M,Xt).set(e,o),n(this,M,Xt).set(r,o);const l=b(this,M,Tr).call(this,a);return b(this,M,Gc).call(this,i,l),o}addLuminosityFilter(e){let s=n(this,M,Xt).get(e||"luminosity");if(s)return s;let i,r;if(e?([i]=b(this,M,lh).call(this,[e]),r=`luminosity_${i}`):r="luminosity",s=n(this,M,Xt).get(r),s)return n(this,M,Xt).set(e,s),s;const a=`g_${n(this,Vs)}_luminosity_map_${te(this,Dn)._++}`,o=b(this,M,xr).call(this,a);n(this,M,Xt).set(e,o),n(this,M,Xt).set(r,o);const l=b(this,M,Tr).call(this,a);return b(this,M,mf).call(this,l),e&&b(this,M,Gc).call(this,i,l),o}addHighlightHCMFilter(e,s,i,r,a){var _;const o=`${s}-${i}-${r}-${a}`;let l=n(this,M,to).get(e);if((l==null?void 0:l.key)===o||(l?((_=l.filter)==null||_.remove(),l.key=o,l.url="none",l.filter=null):(l={key:o,url:"none",filter:null},n(this,M,to).set(e,l)),!s||!i))return l.url;const[h,c]=[s,i].map(b(this,M,io).bind(this));let u=Math.round(.2126*h[0]+.7152*h[1]+.0722*h[2]),f=Math.round(.2126*c[0]+.7152*c[1]+.0722*c[2]),[g,y]=[r,a].map(b(this,M,io).bind(this));f<u&&([u,f,g,y]=[f,u,y,g]),n(this,M,Cr).style.color="";const A=(S,E,C)=>{const x=new Array(256),T=(f-u)/C,P=S/255,k=(E-S)/(255*C);let B=0;for(let D=0;D<=C;D++){const tt=Math.round(u+D*T),st=P+D*k;for(let q=B;q<=tt;q++)x[q]=st;B=tt+1}for(let D=B;D<256;D++)x[D]=x[B-1];return x.join(",")},w=`g_${n(this,Vs)}_hcm_${e}_filter`,v=l.filter=b(this,M,Tr).call(this,w);return b(this,M,$c).call(this,v),b(this,M,so).call(this,A(g[0],y[0],5),A(g[1],y[1],5),A(g[2],y[2],5),v),l.url=b(this,M,xr).call(this,w),l.url}destroy(e=!1){var s,i,r,a;e&&((s=n(this,Ln))!=null&&s.size)||((i=n(this,Us))==null||i.parentNode.parentNode.remove(),p(this,Us,null),(r=n(this,Zr))==null||r.clear(),p(this,Zr,null),(a=n(this,Ln))==null||a.clear(),p(this,Ln,null),p(this,Dn,0))}}Mn=new WeakMap,Zr=new WeakMap,Us=new WeakMap,Vs=new WeakMap,Kt=new WeakMap,Ln=new WeakMap,Dn=new WeakMap,M=new WeakSet,Xt=function(){return n(this,Zr)||p(this,Zr,new Map)},to=function(){return n(this,Ln)||p(this,Ln,new Map)},Cr=function(){if(!n(this,Us)){const e=n(this,Kt).createElement("div"),{style:s}=e;s.visibility="hidden",s.contain="strict",s.width=s.height=0,s.position="absolute",s.top=s.left=0,s.zIndex=-1;const i=n(this,Kt).createElementNS(Ps,"svg");i.setAttribute("width",0),i.setAttribute("height",0),p(this,Us,n(this,Kt).createElementNS(Ps,"defs")),e.append(i),i.append(n(this,Us)),n(this,Kt).body.append(e)}return n(this,Us)},lh=function(e){if(e.length===1){const h=e[0],c=new Array(256);for(let f=0;f<256;f++)c[f]=h[f]/255;const u=c.join(",");return[u,u,u]}const[s,i,r]=e,a=new Array(256),o=new Array(256),l=new Array(256);for(let h=0;h<256;h++)a[h]=s[h]/255,o[h]=i[h]/255,l[h]=r[h]/255;return[a.join(","),o.join(","),l.join(",")]},xr=function(e){if(n(this,Mn)===void 0){p(this,Mn,"");const s=n(this,Kt).URL;s!==n(this,Kt).baseURI&&(ac(s)?U('#createUrl: ignore "data:"-URL for performance reasons.'):p(this,Mn,xu(s,"")))}return`url(${n(this,Mn)}#${e})`},mf=function(e){const s=n(this,Kt).createElementNS(Ps,"feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),e.append(s)},$c=function(e){const s=n(this,Kt).createElementNS(Ps,"feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),e.append(s)},Tr=function(e){const s=n(this,Kt).createElementNS(Ps,"filter");return s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("id",e),n(this,M,Cr).append(s),s},eo=function(e,s,i){const r=n(this,Kt).createElementNS(Ps,s);r.setAttribute("type","discrete"),r.setAttribute("tableValues",i),e.append(r)},so=function(e,s,i,r){const a=n(this,Kt).createElementNS(Ps,"feComponentTransfer");r.append(a),b(this,M,eo).call(this,a,"feFuncR",e),b(this,M,eo).call(this,a,"feFuncG",s),b(this,M,eo).call(this,a,"feFuncB",i)},Gc=function(e,s){const i=n(this,Kt).createElementNS(Ps,"feComponentTransfer");s.append(i),b(this,M,eo).call(this,i,"feFuncA",e)},io=function(e){return n(this,M,Cr).style.color=e,Gd(getComputedStyle(n(this,M,Cr)).getPropertyValue("color"))};class bf{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch(s=>{throw new Error(`Unable to load font data at: ${e}`)})}async _fetch(t){rt("Abstract method `_fetch` called.")}}class lu extends bf{async _fetch(t){const e=await Hl(t,"arraybuffer");return new Uint8Array(e)}}class Af{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `wasmUrl` API parameter is provided.");if(!t)throw new Error("Wasm filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch(s=>{throw new Error(`Unable to load wasm data at: ${e}`)})}async _fetch(t){rt("Abstract method `_fetch` called.")}}class hu extends Af{async _fetch(t){const e=await Hl(t,"arraybuffer");return new Uint8Array(e)}}ae&&U("Please use the `legacy` build in Node.js environments.");async function Vd(d){const e=await process.getBuiltinModule("fs").promises.readFile(d);return new Uint8Array(e)}class lg extends gf{}class hg extends ff{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire(import.meta.url)("@napi-rs/canvas").createCanvas(t,e)}}class cg extends pf{async _fetch(t){return Vd(t)}}class dg extends bf{async _fetch(t){return Vd(t)}}class ug extends Af{async _fetch(t){return Vd(t)}}const Ut={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function zc(d,t){if(!t)return;const e=t[2]-t[0],s=t[3]-t[1],i=new Path2D;i.rect(t[0],t[1],e,s),d.clip(i)}class jd{isModifyingCurrentTransform(){return!1}getPattern(){rt("Abstract method `getPattern` called.")}}class fg extends jd{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;this._type==="axial"?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const s of this._colorStops)e.addColorStop(s[0],s[1]);return e}getPattern(t,e,s,i){let r;if(i===Ut.STROKE||i===Ut.FILL){const a=e.current.getClippedPathBoundingBox(i,pt(t))||[0,0,0,0],o=Math.ceil(a[2]-a[0])||1,l=Math.ceil(a[3]-a[1])||1,h=e.cachedCanvases.getCanvas("pattern",o,l),c=h.context;c.clearRect(0,0,c.canvas.width,c.canvas.height),c.beginPath(),c.rect(0,0,c.canvas.width,c.canvas.height),c.translate(-a[0],-a[1]),s=O.transform(s,[1,0,0,1,a[0],a[1]]),c.transform(...e.baseTransform),this.matrix&&c.transform(...this.matrix),zc(c,this._bbox),c.fillStyle=this._createGradient(c),c.fill(),r=t.createPattern(h.canvas,"no-repeat");const u=new DOMMatrix(s);r.setTransform(u)}else zc(t,this._bbox),r=this._createGradient(t);return r}}function gc(d,t,e,s,i,r,a,o){const l=t.coords,h=t.colors,c=d.data,u=d.width*4;let f;l[e+1]>l[s+1]&&(f=e,e=s,s=f,f=r,r=a,a=f),l[s+1]>l[i+1]&&(f=s,s=i,i=f,f=a,a=o,o=f),l[e+1]>l[s+1]&&(f=e,e=s,s=f,f=r,r=a,a=f);const g=(l[e]+t.offsetX)*t.scaleX,y=(l[e+1]+t.offsetY)*t.scaleY,A=(l[s]+t.offsetX)*t.scaleX,w=(l[s+1]+t.offsetY)*t.scaleY,v=(l[i]+t.offsetX)*t.scaleX,_=(l[i+1]+t.offsetY)*t.scaleY;if(y>=_)return;const S=h[r],E=h[r+1],C=h[r+2],x=h[a],T=h[a+1],P=h[a+2],k=h[o],B=h[o+1],D=h[o+2],tt=Math.round(y),st=Math.round(_);let q,Zt,N,$,Le,Ts,an,Rs;for(let kt=tt;kt<=st;kt++){if(kt<w){const gt=kt<y?0:(y-kt)/(y-w);q=g-(g-A)*gt,Zt=S-(S-x)*gt,N=E-(E-T)*gt,$=C-(C-P)*gt}else{let gt;kt>_?gt=1:w===_?gt=0:gt=(w-kt)/(w-_),q=A-(A-v)*gt,Zt=x-(x-k)*gt,N=T-(T-B)*gt,$=P-(P-D)*gt}let bt;kt<y?bt=0:kt>_?bt=1:bt=(y-kt)/(y-_),Le=g-(g-v)*bt,Ts=S-(S-k)*bt,an=E-(E-B)*bt,Rs=C-(C-D)*bt;const vr=Math.round(Math.min(q,Le)),pi=Math.round(Math.max(q,Le));let gi=u*kt+vr*4;for(let gt=vr;gt<=pi;gt++)bt=(q-gt)/(q-Le),bt<0?bt=0:bt>1&&(bt=1),c[gi++]=Zt-(Zt-Ts)*bt|0,c[gi++]=N-(N-an)*bt|0,c[gi++]=$-($-Rs)*bt|0,c[gi++]=255}}function pg(d,t,e){const s=t.coords,i=t.colors;let r,a;switch(t.type){case"lattice":const o=t.verticesPerRow,l=Math.floor(s.length/o)-1,h=o-1;for(r=0;r<l;r++){let c=r*o;for(let u=0;u<h;u++,c++)gc(d,e,s[c],s[c+1],s[c+o],i[c],i[c+1],i[c+o]),gc(d,e,s[c+o+1],s[c+1],s[c+o],i[c+o+1],i[c+1],i[c+o])}break;case"triangles":for(r=0,a=s.length;r<a;r+=3)gc(d,e,s[r],s[r+1],s[r+2],i[r],i[r+1],i[r+2]);break;default:throw new Error("illegal figure")}}class gg extends jd{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[6],this._background=t[7],this.matrix=null}_createMeshCanvas(t,e,s){const o=Math.floor(this._bounds[0]),l=Math.floor(this._bounds[1]),h=Math.ceil(this._bounds[2])-o,c=Math.ceil(this._bounds[3])-l,u=Math.min(Math.ceil(Math.abs(h*t[0]*1.1)),3e3),f=Math.min(Math.ceil(Math.abs(c*t[1]*1.1)),3e3),g=h/u,y=c/f,A={coords:this._coords,colors:this._colors,offsetX:-o,offsetY:-l,scaleX:1/g,scaleY:1/y},w=u+2*2,v=f+2*2,_=s.getCanvas("mesh",w,v),S=_.context,E=S.createImageData(u,f);if(e){const x=E.data;for(let T=0,P=x.length;T<P;T+=4)x[T]=e[0],x[T+1]=e[1],x[T+2]=e[2],x[T+3]=255}for(const x of this._figures)pg(E,x,A);return S.putImageData(E,2,2),{canvas:_.canvas,offsetX:o-2*g,offsetY:l-2*y,scaleX:g,scaleY:y}}isModifyingCurrentTransform(){return!0}getPattern(t,e,s,i){zc(t,this._bbox);const r=new Float32Array(2);if(i===Ut.SHADING)O.singularValueDecompose2dScale(pt(t),r);else if(this.matrix){O.singularValueDecompose2dScale(this.matrix,r);const[o,l]=r;O.singularValueDecompose2dScale(e.baseTransform,r),r[0]*=o,r[1]*=l}else O.singularValueDecompose2dScale(e.baseTransform,r);const a=this._createMeshCanvas(r,i===Ut.SHADING?null:this._background,e.cachedCanvases);return i!==Ut.SHADING&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(a.offsetX,a.offsetY),t.scale(a.scaleX,a.scaleY),t.createPattern(a.canvas,"no-repeat")}}class mg extends jd{getPattern(){return"hotpink"}}function bg(d){switch(d[0]){case"RadialAxial":return new fg(d);case"Mesh":return new gg(d);case"Dummy":return new mg}throw new Error(`Unknown IR type: ${d[0]}`)}const cu={COLORED:1,UNCOLORED:2},jh=class jh{constructor(t,e,s,i){this.color=t[1],this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.ctx=e,this.canvasGraphicsFactory=s,this.baseTransform=i}createPatternCanvas(t){const{bbox:e,operatorList:s,paintType:i,tilingType:r,color:a,canvasGraphicsFactory:o}=this;let{xstep:l,ystep:h}=this;l=Math.abs(l),h=Math.abs(h),rc("TilingType: "+r);const c=e[0],u=e[1],f=e[2],g=e[3],y=f-c,A=g-u,w=new Float32Array(2);O.singularValueDecompose2dScale(this.matrix,w);const[v,_]=w;O.singularValueDecompose2dScale(this.baseTransform,w);const S=v*w[0],E=_*w[1];let C=y,x=A,T=!1,P=!1;const k=Math.ceil(l*S),B=Math.ceil(h*E),D=Math.ceil(y*S),tt=Math.ceil(A*E);k>=D?C=l:T=!0,B>=tt?x=h:P=!0;const st=this.getSizeAndScale(C,this.ctx.canvas.width,S),q=this.getSizeAndScale(x,this.ctx.canvas.height,E),Zt=t.cachedCanvases.getCanvas("pattern",st.size,q.size),N=Zt.context,$=o.createCanvasGraphics(N);if($.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext($,i,a),N.translate(-st.scale*c,-q.scale*u),$.transform(st.scale,0,0,q.scale,0,0),N.save(),this.clipBbox($,c,u,f,g),$.baseTransform=pt($.ctx),$.executeOperatorList(s),$.endDrawing(),N.restore(),T||P){const Le=Zt.canvas;T&&(C=l),P&&(x=h);const Ts=this.getSizeAndScale(C,this.ctx.canvas.width,S),an=this.getSizeAndScale(x,this.ctx.canvas.height,E),Rs=Ts.size,kt=an.size,bt=t.cachedCanvases.getCanvas("pattern-workaround",Rs,kt),vr=bt.context,pi=T?Math.floor(y/l):0,gi=P?Math.floor(A/h):0;for(let gt=0;gt<=pi;gt++)for(let Ua=0;Ua<=gi;Ua++)vr.drawImage(Le,Rs*gt,kt*Ua,Rs,kt,0,0,Rs,kt);return{canvas:bt.canvas,scaleX:Ts.scale,scaleY:an.scale,offsetX:c,offsetY:u}}return{canvas:Zt.canvas,scaleX:st.scale,scaleY:q.scale,offsetX:c,offsetY:u}}getSizeAndScale(t,e,s){const i=Math.max(jh.MAX_PATTERN_SIZE,e);let r=Math.ceil(t*s);return r>=i?r=i:s=r/t,{scale:s,size:r}}clipBbox(t,e,s,i,r){const a=i-e,o=r-s;t.ctx.rect(e,s,a,o),O.axialAlignedBoundingBox([e,s,i,r],pt(t.ctx),t.current.minMax),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const i=t.ctx,r=t.current;switch(e){case cu.COLORED:const a=this.ctx;i.fillStyle=a.fillStyle,i.strokeStyle=a.strokeStyle,r.fillColor=a.fillStyle,r.strokeColor=a.strokeStyle;break;case cu.UNCOLORED:const o=O.makeHexColor(s[0],s[1],s[2]);i.fillStyle=o,i.strokeStyle=o,r.fillColor=o,r.strokeColor=o;break;default:throw new zp(`Unsupported paint type: ${e}`)}}isModifyingCurrentTransform(){return!1}getPattern(t,e,s,i){let r=s;i!==Ut.SHADING&&(r=O.transform(r,e.baseTransform),this.matrix&&(r=O.transform(r,this.matrix)));const a=this.createPatternCanvas(e);let o=new DOMMatrix(r);o=o.translate(a.offsetX,a.offsetY),o=o.scale(1/a.scaleX,1/a.scaleY);const l=t.createPattern(a.canvas,"repeat");return l.setTransform(o),l}};R(jh,"MAX_PATTERN_SIZE",3e3);let Uc=jh;function Ag({src:d,srcPos:t=0,dest:e,width:s,height:i,nonBlackColor:r=4294967295,inverseDecode:a=!1}){const o=Wt.isLittleEndian?4278190080:255,[l,h]=a?[r,o]:[o,r],c=s>>3,u=s&7,f=d.length;e=new Uint32Array(e.buffer);let g=0;for(let y=0;y<i;y++){for(const w=t+c;t<w;t++){const v=t<f?d[t]:255;e[g++]=v&128?h:l,e[g++]=v&64?h:l,e[g++]=v&32?h:l,e[g++]=v&16?h:l,e[g++]=v&8?h:l,e[g++]=v&4?h:l,e[g++]=v&2?h:l,e[g++]=v&1?h:l}if(u===0)continue;const A=t<f?d[t++]:255;for(let w=0;w<u;w++)e[g++]=A&1<<7-w?h:l}return{srcPos:t,destPos:g}}const du=16,uu=100,yg=15,fu=10,pe=16,mc=new DOMMatrix,Ie=new Float32Array(2),kr=new Float32Array([1/0,1/0,-1/0,-1/0]);function wg(d,t){if(d._removeMirroring)throw new Error("Context is already forwarding operations.");d.__originalSave=d.save,d.__originalRestore=d.restore,d.__originalRotate=d.rotate,d.__originalScale=d.scale,d.__originalTranslate=d.translate,d.__originalTransform=d.transform,d.__originalSetTransform=d.setTransform,d.__originalResetTransform=d.resetTransform,d.__originalClip=d.clip,d.__originalMoveTo=d.moveTo,d.__originalLineTo=d.lineTo,d.__originalBezierCurveTo=d.bezierCurveTo,d.__originalRect=d.rect,d.__originalClosePath=d.closePath,d.__originalBeginPath=d.beginPath,d._removeMirroring=()=>{d.save=d.__originalSave,d.restore=d.__originalRestore,d.rotate=d.__originalRotate,d.scale=d.__originalScale,d.translate=d.__originalTranslate,d.transform=d.__originalTransform,d.setTransform=d.__originalSetTransform,d.resetTransform=d.__originalResetTransform,d.clip=d.__originalClip,d.moveTo=d.__originalMoveTo,d.lineTo=d.__originalLineTo,d.bezierCurveTo=d.__originalBezierCurveTo,d.rect=d.__originalRect,d.closePath=d.__originalClosePath,d.beginPath=d.__originalBeginPath,delete d._removeMirroring},d.save=function(){t.save(),this.__originalSave()},d.restore=function(){t.restore(),this.__originalRestore()},d.translate=function(e,s){t.translate(e,s),this.__originalTranslate(e,s)},d.scale=function(e,s){t.scale(e,s),this.__originalScale(e,s)},d.transform=function(e,s,i,r,a,o){t.transform(e,s,i,r,a,o),this.__originalTransform(e,s,i,r,a,o)},d.setTransform=function(e,s,i,r,a,o){t.setTransform(e,s,i,r,a,o),this.__originalSetTransform(e,s,i,r,a,o)},d.resetTransform=function(){t.resetTransform(),this.__originalResetTransform()},d.rotate=function(e){t.rotate(e),this.__originalRotate(e)},d.clip=function(e){t.clip(e),this.__originalClip(e)},d.moveTo=function(e,s){t.moveTo(e,s),this.__originalMoveTo(e,s)},d.lineTo=function(e,s){t.lineTo(e,s),this.__originalLineTo(e,s)},d.bezierCurveTo=function(e,s,i,r,a,o){t.bezierCurveTo(e,s,i,r,a,o),this.__originalBezierCurveTo(e,s,i,r,a,o)},d.rect=function(e,s,i,r){t.rect(e,s,i,r),this.__originalRect(e,s,i,r)},d.closePath=function(){t.closePath(),this.__originalClosePath()},d.beginPath=function(){t.beginPath(),this.__originalBeginPath()}}class vg{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,s){let i;return this.cache[t]!==void 0?(i=this.cache[t],this.canvasFactory.reset(i,e,s)):(i=this.canvasFactory.create(e,s),this.cache[t]=i),i}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function ql(d,t,e,s,i,r,a,o,l,h){const[c,u,f,g,y,A]=pt(d);if(u===0&&f===0){const _=a*c+y,S=Math.round(_),E=o*g+A,C=Math.round(E),x=(a+l)*c+y,T=Math.abs(Math.round(x)-S)||1,P=(o+h)*g+A,k=Math.abs(Math.round(P)-C)||1;return d.setTransform(Math.sign(c),0,0,Math.sign(g),S,C),d.drawImage(t,e,s,i,r,0,0,T,k),d.setTransform(c,u,f,g,y,A),[T,k]}if(c===0&&g===0){const _=o*f+y,S=Math.round(_),E=a*u+A,C=Math.round(E),x=(o+h)*f+y,T=Math.abs(Math.round(x)-S)||1,P=(a+l)*u+A,k=Math.abs(Math.round(P)-C)||1;return d.setTransform(0,Math.sign(u),Math.sign(f),0,S,C),d.drawImage(t,e,s,i,r,0,0,k,T),d.setTransform(c,u,f,g,y,A),[k,T]}d.drawImage(t,e,s,i,r,a,o,l,h);const w=Math.hypot(c,u),v=Math.hypot(f,g);return[w*l,v*h]}class pu{constructor(t,e){R(this,"alphaIsShape",!1);R(this,"fontSize",0);R(this,"fontSizeScale",1);R(this,"textMatrix",null);R(this,"textMatrixScale",1);R(this,"fontMatrix",yc);R(this,"leading",0);R(this,"x",0);R(this,"y",0);R(this,"lineX",0);R(this,"lineY",0);R(this,"charSpacing",0);R(this,"wordSpacing",0);R(this,"textHScale",1);R(this,"textRenderingMode",qt.FILL);R(this,"textRise",0);R(this,"fillColor","#000000");R(this,"strokeColor","#000000");R(this,"patternFill",!1);R(this,"patternStroke",!1);R(this,"fillAlpha",1);R(this,"strokeAlpha",1);R(this,"lineWidth",1);R(this,"activeSMask",null);R(this,"transferMaps","none");this.clipBox=new Float32Array([0,0,t,e]),this.minMax=kr.slice()}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t.minMax=this.minMax.slice(),t}getPathBoundingBox(t=Ut.FILL,e=null){const s=this.minMax.slice();if(t===Ut.STROKE){e||rt("Stroke bounding box must include transform."),O.singularValueDecompose2dScale(e,Ie);const i=Ie[0]*this.lineWidth/2,r=Ie[1]*this.lineWidth/2;s[0]-=i,s[1]-=r,s[2]+=i,s[3]+=r}return s}updateClipFromPath(){const t=O.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minMax[0]===1/0}startNewPathAndClipBox(t){this.clipBox.set(t,0),this.minMax.set(kr,0)}getClippedPathBoundingBox(t=Ut.FILL,e=null){return O.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function gu(d,t){if(t instanceof ImageData){d.putImageData(t,0,0);return}const e=t.height,s=t.width,i=e%pe,r=(e-i)/pe,a=i===0?r:r+1,o=d.createImageData(s,pe);let l=0,h;const c=t.data,u=o.data;let f,g,y,A;if(t.kind===Jl.GRAYSCALE_1BPP){const w=c.byteLength,v=new Uint32Array(u.buffer,0,u.byteLength>>2),_=v.length,S=s+7>>3,E=4294967295,C=Wt.isLittleEndian?4278190080:255;for(f=0;f<a;f++){for(y=f<r?pe:i,h=0,g=0;g<y;g++){const x=w-l;let T=0;const P=x>S?s:x*8-7,k=P&-8;let B=0,D=0;for(;T<k;T+=8)D=c[l++],v[h++]=D&128?E:C,v[h++]=D&64?E:C,v[h++]=D&32?E:C,v[h++]=D&16?E:C,v[h++]=D&8?E:C,v[h++]=D&4?E:C,v[h++]=D&2?E:C,v[h++]=D&1?E:C;for(;T<P;T++)B===0&&(D=c[l++],B=128),v[h++]=D&B?E:C,B>>=1}for(;h<_;)v[h++]=0;d.putImageData(o,0,f*pe)}}else if(t.kind===Jl.RGBA_32BPP){for(g=0,A=s*pe*4,f=0;f<r;f++)u.set(c.subarray(l,l+A)),l+=A,d.putImageData(o,0,g),g+=pe;f<a&&(A=s*i*4,u.set(c.subarray(l,l+A)),d.putImageData(o,0,g))}else if(t.kind===Jl.RGB_24BPP)for(y=pe,A=s*y,f=0;f<a;f++){for(f>=r&&(y=i,A=s*y),h=0,g=A;g--;)u[h++]=c[l++],u[h++]=c[l++],u[h++]=c[l++],u[h++]=255;d.putImageData(o,0,f*pe)}else throw new Error(`bad image kind: ${t.kind}`)}function mu(d,t){if(t.bitmap){d.drawImage(t.bitmap,0,0);return}const e=t.height,s=t.width,i=e%pe,r=(e-i)/pe,a=i===0?r:r+1,o=d.createImageData(s,pe);let l=0;const h=t.data,c=o.data;for(let u=0;u<a;u++){const f=u<r?pe:i;({srcPos:l}=Ag({src:h,srcPos:l,dest:c,width:s,height:f,nonBlackColor:0})),d.putImageData(o,0,u*pe)}}function ja(d,t){const e=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of e)d[s]!==void 0&&(t[s]=d[s]);d.setLineDash!==void 0&&(t.setLineDash(d.getLineDash()),t.lineDashOffset=d.lineDashOffset)}function Xl(d){d.strokeStyle=d.fillStyle="#000000",d.fillRule="nonzero",d.globalAlpha=1,d.lineWidth=1,d.lineCap="butt",d.lineJoin="miter",d.miterLimit=10,d.globalCompositeOperation="source-over",d.font="10px sans-serif",d.setLineDash!==void 0&&(d.setLineDash([]),d.lineDashOffset=0);const{filter:t}=d;t!=="none"&&t!==""&&(d.filter="none")}function bu(d,t){if(t)return!0;O.singularValueDecompose2dScale(d,Ie);const e=Math.fround(ui.pixelRatio*en.PDF_TO_CSS_UNITS);return Ie[0]<=e&&Ie[1]<=e}const _g=["butt","round","square"],Sg=["miter","round","bevel"],Eg={},Au={};var as,Vc,jc,Wc;const Qd=class Qd{constructor(t,e,s,i,r,{optionalContentConfig:a,markedContentStack:o=null},l,h){m(this,as);this.ctx=t,this.current=new pu(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=s,this.canvasFactory=i,this.filterFactory=r,this.groupStack=[],this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=o||[],this.optionalContentConfig=a,this.cachedCanvases=new vg(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=l,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=h,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return typeof t=="string"?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:s=!1,background:i=null}){const r=this.ctx.canvas.width,a=this.ctx.canvas.height,o=this.ctx.fillStyle;if(this.ctx.fillStyle=i||"#ffffff",this.ctx.fillRect(0,0,r,a),this.ctx.fillStyle=o,s){const l=this.cachedCanvases.getCanvas("transparent",r,a);this.compositeCtx=this.ctx,this.transparentCanvas=l.canvas,this.ctx=l.context,this.ctx.save(),this.ctx.transform(...pt(this.compositeCtx))}this.ctx.save(),Xl(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=pt(this.ctx)}executeOperatorList(t,e,s,i){const r=t.argsArray,a=t.fnArray;let o=e||0;const l=r.length;if(l===o)return o;const h=l-o>fu&&typeof s=="function",c=h?Date.now()+yg:0;let u=0;const f=this.commonObjs,g=this.objs;let y;for(;;){if(i!==void 0&&o===i.nextBreakPoint)return i.breakIt(o,s),o;if(y=a[o],y!==Ph.dependency)this[y].apply(this,r[o]);else for(const A of r[o]){const w=A.startsWith("g_")?f:g;if(!w.has(A))return w.get(A,s),o}if(o++,o===l)return o;if(h&&++u>fu){if(Date.now()>c)return s(),o;u=0}}}endDrawing(){b(this,as,Vc).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())typeof HTMLCanvasElement<"u"&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),b(this,as,jc).call(this)}_scaleImage(t,e){const s=t.width??t.displayWidth,i=t.height??t.displayHeight;let r=Math.max(Math.hypot(e[0],e[1]),1),a=Math.max(Math.hypot(e[2],e[3]),1),o=s,l=i,h="prescale1",c,u;for(;r>2&&o>1||a>2&&l>1;){let f=o,g=l;r>2&&o>1&&(f=o>=16384?Math.floor(o/2)-1||1:Math.ceil(o/2),r/=o/f),a>2&&l>1&&(g=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l)/2,a/=l/g),c=this.cachedCanvases.getCanvas(h,f,g),u=c.context,u.clearRect(0,0,f,g),u.drawImage(t,0,0,o,l,0,0,f,g),t=c.canvas,o=f,l=g,h=h==="prescale1"?"prescale2":"prescale1"}return{img:t,paintWidth:o,paintHeight:l}}_createMaskCanvas(t){const e=this.ctx,{width:s,height:i}=t,r=this.current.fillColor,a=this.current.patternFill,o=pt(e);let l,h,c,u;if((t.bitmap||t.data)&&t.count>1){const k=t.bitmap||t.data.buffer;h=JSON.stringify(a?o:[o.slice(0,4),r]),l=this._cachedBitmapsMap.get(k),l||(l=new Map,this._cachedBitmapsMap.set(k,l));const B=l.get(h);if(B&&!a){const D=Math.round(Math.min(o[0],o[2])+o[4]),tt=Math.round(Math.min(o[1],o[3])+o[5]);return{canvas:B,offsetX:D,offsetY:tt}}c=B}c||(u=this.cachedCanvases.getCanvas("maskCanvas",s,i),mu(u.context,t));let f=O.transform(o,[1/s,0,0,-1/i,0,0]);f=O.transform(f,[1,0,0,1,0,-i]);const g=kr.slice();O.axialAlignedBoundingBox([0,0,s,i],f,g);const[y,A,w,v]=g,_=Math.round(w-y)||1,S=Math.round(v-A)||1,E=this.cachedCanvases.getCanvas("fillCanvas",_,S),C=E.context,x=y,T=A;C.translate(-x,-T),C.transform(...f),c||(c=this._scaleImage(u.canvas,os(C)),c=c.img,l&&a&&l.set(h,c)),C.imageSmoothingEnabled=bu(pt(C),t.interpolate),ql(C,c,0,0,c.width,c.height,0,0,s,i),C.globalCompositeOperation="source-in";const P=O.transform(os(C),[1,0,0,1,-x,-T]);return C.fillStyle=a?r.getPattern(e,this,P,Ut.FILL):r,C.fillRect(0,0,s,i),l&&!a&&(this.cachedCanvases.delete("fillCanvas"),l.set(h,E.canvas)),{canvas:E.canvas,offsetX:Math.round(x),offsetY:Math.round(T)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=_g[t]}setLineJoin(t){this.ctx.lineJoin=Sg[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const s=this.ctx;s.setLineDash!==void 0&&(s.setLineDash(t),s.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,s]of t)switch(e){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=s;break;case"ca":this.ctx.globalAlpha=this.current.fillAlpha=s;break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":this.current.activeSMask=s?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(s);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(s,t,e);this.suspendedCtx=this.ctx;const r=this.ctx=i.context;r.setTransform(this.suspendedCtx.getTransform()),ja(this.suspendedCtx,r),wg(r,this.suspendedCtx),this.setGState([["BM","source-over"]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),ja(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,s=this.suspendedCtx;this.composeSMask(s,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,s,i){const r=i[0],a=i[1],o=i[2]-r,l=i[3]-a;o===0||l===0||(this.genericComposeSMask(e.context,s,o,l,e.subtype,e.backdrop,e.transferMap,r,a,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(s.canvas,0,0),t.restore())}genericComposeSMask(t,e,s,i,r,a,o,l,h,c,u){let f=t.canvas,g=l-c,y=h-u;if(a){const w=O.makeHexColor(...a);if(g<0||y<0||g+s>f.width||y+i>f.height){const v=this.cachedCanvases.getCanvas("maskExtension",s,i),_=v.context;_.drawImage(f,-g,-y),_.globalCompositeOperation="destination-atop",_.fillStyle=w,_.fillRect(0,0,s,i),_.globalCompositeOperation="source-over",f=v.canvas,g=y=0}else{t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const v=new Path2D;v.rect(g,y,s,i),t.clip(v),t.globalCompositeOperation="destination-atop",t.fillStyle=w,t.fillRect(g,y,s,i),t.restore()}}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),r==="Alpha"&&o?e.filter=this.filterFactory.addAlphaFilter(o):r==="Luminosity"&&(e.filter=this.filterFactory.addLuminosityFilter(o));const A=new Path2D;A.rect(l,h,s,i),e.clip(A),e.globalCompositeOperation="destination-in",e.drawImage(f,g,y,s,i,l,h,s,i),e.restore()}save(){this.inSMaskMode&&ja(this.ctx,this.suspendedCtx),this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){if(this.stateStack.length===0){this.inSMaskMode&&this.endSMaskMode();return}this.current=this.stateStack.pop(),this.ctx.restore(),this.inSMaskMode&&ja(this.suspendedCtx,this.ctx),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}transform(t,e,s,i,r,a){this.ctx.transform(t,e,s,i,r,a),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,s){let[i]=e;if(!s){i||(i=e[0]=new Path2D),this[t](i);return}if(!(i instanceof Path2D)){const r=e[0]=new Path2D;for(let a=0,o=i.length;a<o;)switch(i[a++]){case jl.moveTo:r.moveTo(i[a++],i[a++]);break;case jl.lineTo:r.lineTo(i[a++],i[a++]);break;case jl.curveTo:r.bezierCurveTo(i[a++],i[a++],i[a++],i[a++],i[a++],i[a++]);break;case jl.closePath:r.closePath();break;default:U(`Unrecognized drawing path operator: ${i[a-1]}`);break}i=r}O.axialAlignedBoundingBox(s,pt(this.ctx),this.current.minMax),this[t](i)}closePath(){this.ctx.closePath()}stroke(t,e=!0){const s=this.ctx,i=this.current.strokeColor;if(s.globalAlpha=this.current.strokeAlpha,this.contentVisible)if(typeof i=="object"&&(i!=null&&i.getPattern)){const r=i.isModifyingCurrentTransform()?s.getTransform():null;if(s.save(),s.strokeStyle=i.getPattern(s,this,os(s),Ut.STROKE),r){const a=new Path2D;a.addPath(t,s.getTransform().invertSelf().multiplySelf(r)),t=a}this.rescaleAndStroke(t,!1),s.restore()}else this.rescaleAndStroke(t,!0);e&&this.consumePath(t,this.current.getClippedPathBoundingBox(Ut.STROKE,pt(this.ctx))),s.globalAlpha=this.current.fillAlpha}closeStroke(t){this.stroke(t)}fill(t,e=!0){const s=this.ctx,i=this.current.fillColor,r=this.current.patternFill;let a=!1;if(r){const l=i.isModifyingCurrentTransform()?s.getTransform():null;if(s.save(),s.fillStyle=i.getPattern(s,this,os(s),Ut.FILL),l){const h=new Path2D;h.addPath(t,s.getTransform().invertSelf().multiplySelf(l)),t=h}a=!0}const o=this.current.getClippedPathBoundingBox();this.contentVisible&&o!==null&&(this.pendingEOFill?(s.fill(t,"evenodd"),this.pendingEOFill=!1):s.fill(t)),a&&s.restore(),e&&this.consumePath(t,o)}eoFill(t){this.pendingEOFill=!0,this.fill(t)}fillStroke(t){this.fill(t,!1),this.stroke(t,!1),this.consumePath(t)}eoFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}closeFillStroke(t){this.fillStroke(t)}closeEOFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}endPath(t){this.consumePath(t)}rawFillPath(t){this.ctx.fill(t)}clip(){this.pendingClip=Eg}eoClip(){this.pendingClip=Au}beginText(){this.current.textMatrix=null,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(t===void 0)return;const s=new Path2D,i=e.getTransform().invertSelf();for(const{transform:r,x:a,y:o,fontSize:l,path:h}of t)s.addPath(h,new DOMMatrix(r).preMultiplySelf(i).translate(a,o).scale(l,-l));e.clip(s),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){var c;const s=this.commonObjs.get(t),i=this.current;if(!s)throw new Error(`Can't find font for ${t}`);if(i.fontMatrix=s.fontMatrix||yc,(i.fontMatrix[0]===0||i.fontMatrix[3]===0)&&U("Invalid font matrix for font "+t),e<0?(e=-e,i.fontDirection=-1):i.fontDirection=1,this.current.font=s,this.current.fontSize=e,s.isType3Font)return;const r=s.loadedName||"sans-serif",a=((c=s.systemFontInfo)==null?void 0:c.css)||`"${r}", ${s.fallbackName}`;let o="normal";s.black?o="900":s.bold&&(o="bold");const l=s.italic?"italic":"normal";let h=e;e<du?h=du:e>uu&&(h=uu),this.current.fontSizeScale=e/h,this.ctx.font=`${l} ${o} ${h}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t){const{current:e}=this;e.textMatrix=t,e.textMatrixScale=Math.hypot(t[0],t[1]),e.x=e.lineX=0,e.y=e.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,s,i,r){const a=this.ctx,o=this.current,l=o.font,h=o.textRenderingMode,c=o.fontSize/o.fontSizeScale,u=h&qt.FILL_STROKE_MASK,f=!!(h&qt.ADD_TO_PATH_FLAG),g=o.patternFill&&!l.missingFile,y=o.patternStroke&&!l.missingFile;let A;if((l.disableFontFace||f||g||y)&&(A=l.getPathGenerator(this.commonObjs,t)),l.disableFontFace||g||y){a.save(),a.translate(e,s),a.scale(c,-c);let w;if((u===qt.FILL||u===qt.FILL_STROKE)&&(i?(w=a.getTransform(),a.setTransform(...i),a.fill(b(this,as,Wc).call(this,A,w,i))):a.fill(A)),u===qt.STROKE||u===qt.FILL_STROKE)if(r){w||(w=a.getTransform()),a.setTransform(...r);const{a:v,b:_,c:S,d:E}=w,C=O.inverseTransform(r),x=O.transform([v,_,S,E,0,0],C);O.singularValueDecompose2dScale(x,Ie),a.lineWidth*=Math.max(Ie[0],Ie[1])/c,a.stroke(b(this,as,Wc).call(this,A,w,r))}else a.lineWidth/=c,a.stroke(A);a.restore()}else(u===qt.FILL||u===qt.FILL_STROKE)&&a.fillText(t,e,s),(u===qt.STROKE||u===qt.FILL_STROKE)&&a.strokeText(t,e,s);f&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:pt(a),x:e,y:s,fontSize:c,path:A})}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let i=3;i<e.length;i+=4)if(e[i]>0&&e[i]<255){s=!0;break}return X(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,s=e.font;if(s.isType3Font)return this.showType3Text(t);const i=e.fontSize;if(i===0)return;const r=this.ctx,a=e.fontSizeScale,o=e.charSpacing,l=e.wordSpacing,h=e.fontDirection,c=e.textHScale*h,u=t.length,f=s.vertical,g=f?1:-1,y=s.defaultVMetrics,A=i*e.fontMatrix[0],w=e.textRenderingMode===qt.FILL&&!s.disableFontFace&&!e.patternFill;r.save(),e.textMatrix&&r.transform(...e.textMatrix),r.translate(e.x,e.y+e.textRise),h>0?r.scale(c,-1):r.scale(c,1);let v,_;if(e.patternFill){r.save();const T=e.fillColor.getPattern(r,this,os(r),Ut.FILL);v=pt(r),r.restore(),r.fillStyle=T}if(e.patternStroke){r.save();const T=e.strokeColor.getPattern(r,this,os(r),Ut.STROKE);_=pt(r),r.restore(),r.strokeStyle=T}let S=e.lineWidth;const E=e.textMatrixScale;if(E===0||S===0){const T=e.textRenderingMode&qt.FILL_STROKE_MASK;(T===qt.STROKE||T===qt.FILL_STROKE)&&(S=this.getSinglePixelWidth())}else S/=E;if(a!==1&&(r.scale(a,a),S/=a),r.lineWidth=S,s.isInvalidPDFjsFont){const T=[];let P=0;for(const k of t)T.push(k.unicode),P+=k.width;r.fillText(T.join(""),0,0),e.x+=P*A*c,r.restore(),this.compose();return}let C=0,x;for(x=0;x<u;++x){const T=t[x];if(typeof T=="number"){C+=g*T*i/1e3;continue}let P=!1;const k=(T.isSpace?l:0)+o,B=T.fontChar,D=T.accent;let tt,st,q=T.width;if(f){const N=T.vmetric||y,$=-(T.vmetric?N[1]:q*.5)*A,Le=N[2]*A;q=N?-N[0]:q,tt=$/a,st=(C+Le)/a}else tt=C/a,st=0;if(s.remeasure&&q>0){const N=r.measureText(B).width*1e3/i*a;if(q<N&&this.isFontSubpixelAAEnabled){const $=q/N;P=!0,r.save(),r.scale($,1),tt/=$}else q!==N&&(tt+=(q-N)/2e3*i/a)}if(this.contentVisible&&(T.isInFont||s.missingFile)){if(w&&!D)r.fillText(B,tt,st);else if(this.paintChar(B,tt,st,v,_),D){const N=tt+i*D.offset.x/a,$=st-i*D.offset.y/a;this.paintChar(D.fontChar,N,$,v,_)}}const Zt=f?q*A-k*h:q*A+k*h;C+=Zt,P&&r.restore()}f?e.y-=C:e.x+=C*c,r.restore(),this.compose()}showType3Text(t){const e=this.ctx,s=this.current,i=s.font,r=s.fontSize,a=s.fontDirection,o=i.vertical?1:-1,l=s.charSpacing,h=s.wordSpacing,c=s.textHScale*a,u=s.fontMatrix||yc,f=t.length,g=s.textRenderingMode===qt.INVISIBLE;let y,A,w,v;if(!(g||r===0)){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),s.textMatrix&&e.transform(...s.textMatrix),e.translate(s.x,s.y+s.textRise),e.scale(c,a),y=0;y<f;++y){if(A=t[y],typeof A=="number"){v=o*A*r/1e3,this.ctx.translate(v,0),s.x+=v*c;continue}const _=(A.isSpace?h:0)+l,S=i.charProcOperatorList[A.operatorListId];S?this.contentVisible&&(this.save(),e.scale(r,r),e.transform(...u),this.executeOperatorList(S),this.restore()):U(`Type3 character "${A.operatorListId}" is not available.`);const E=[A.width,0];O.applyTransform(E,u),w=E[0]*r+_,e.translate(w,0),s.x+=w*c}e.restore()}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,s,i,r,a){const o=new Path2D;o.rect(s,i,r-s,a-i),this.ctx.clip(o),this.endPath()}getColorN_Pattern(t){let e;if(t[0]==="TilingPattern"){const s=this.baseTransform||pt(this.ctx),i={createCanvasGraphics:r=>new Qd(r,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new Uc(t,this.ctx,i,s)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments),this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t,e,s){this.ctx.strokeStyle=this.current.strokeColor=O.makeHexColor(t,e,s),this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent",this.current.patternStroke=!1}setFillRGBColor(t,e,s){this.ctx.fillStyle=this.current.fillColor=O.makeHexColor(t,e,s),this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent",this.current.patternFill=!1}_getPattern(t,e=null){let s;return this.cachedPatterns.has(t)?s=this.cachedPatterns.get(t):(s=bg(this.getObject(t)),this.cachedPatterns.set(t,s)),e&&(s.matrix=e),s}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const s=this._getPattern(t);e.fillStyle=s.getPattern(e,this,os(e),Ut.SHADING);const i=os(e);if(i){const{width:r,height:a}=e.canvas,o=kr.slice();O.axialAlignedBoundingBox([0,0,r,a],i,o);const[l,h,c,u]=o;this.ctx.fillRect(l,h,c-l,u-h)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){rt("Should not call beginInlineImage")}beginImageData(){rt("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=pt(this.ctx),e)){O.axialAlignedBoundingBox(e,this.baseTransform,this.current.minMax);const[s,i,r,a]=e,o=new Path2D;o.rect(s,i,r-s,a-i),this.ctx.clip(o),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||rc("TODO: Support non-isolated groups."),t.knockout&&U("Knockout groups not supported.");const s=pt(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let i=kr.slice();O.axialAlignedBoundingBox(t.bbox,pt(e),i);const r=[0,0,e.canvas.width,e.canvas.height];i=O.intersect(i,r)||[0,0,0,0];const a=Math.floor(i[0]),o=Math.floor(i[1]),l=Math.max(Math.ceil(i[2])-a,1),h=Math.max(Math.ceil(i[3])-o,1);this.current.startNewPathAndClipBox([0,0,l,h]);let c="groupAt"+this.groupLevel;t.smask&&(c+="_smask_"+this.smaskCounter++%2);const u=this.cachedCanvases.getCanvas(c,l,h),f=u.context;f.translate(-a,-o),f.transform(...s);let g=new Path2D;const[y,A,w,v]=t.bbox;if(g.rect(y,A,w-y,v-A),t.matrix){const _=new Path2D;_.addPath(g,new DOMMatrix(t.matrix)),g=_}f.clip(g),t.smask?this.smaskStack.push({canvas:u.canvas,context:f,offsetX:a,offsetY:o,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(a,o),e.save()),ja(e,f),this.ctx=f,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const i=pt(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...i);const r=kr.slice();O.axialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],i,r),this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(r)}}beginAnnotation(t,e,s,i,r){if(b(this,as,Vc).call(this),Xl(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const a=e[2]-e[0],o=e[3]-e[1];if(r&&this.annotationCanvasMap){s=s.slice(),s[4]-=e[0],s[5]-=e[1],e=e.slice(),e[0]=e[1]=0,e[2]=a,e[3]=o,O.singularValueDecompose2dScale(pt(this.ctx),Ie);const{viewportScale:l}=this,h=Math.ceil(a*this.outputScaleX*l),c=Math.ceil(o*this.outputScaleY*l);this.annotationCanvas=this.canvasFactory.create(h,c);const{canvas:u,context:f}=this.annotationCanvas;this.annotationCanvasMap.set(t,u),this.annotationCanvas.savedCtx=this.ctx,this.ctx=f,this.ctx.save(),this.ctx.setTransform(Ie[0],0,0,-Ie[1],0,o*Ie[1]),Xl(this.ctx)}else{Xl(this.ctx),this.endPath();const l=new Path2D;l.rect(e[0],e[1],a,o),this.ctx.clip(l)}}this.current=new pu(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...s),this.transform(...i)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),b(this,as,jc).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;t=this.getObject(t.data,t),t.count=e;const s=this.ctx,i=this._createMaskCanvas(t),r=i.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(r,i.offsetX,i.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,s=0,i=0,r,a){if(!this.contentVisible)return;t=this.getObject(t.data,t);const o=this.ctx;o.save();const l=pt(o);o.transform(e,s,i,r,0,0);const h=this._createMaskCanvas(t);o.setTransform(1,0,0,1,h.offsetX-l[4],h.offsetY-l[5]);for(let c=0,u=a.length;c<u;c+=2){const f=O.transform(l,[e,s,i,r,a[c],a[c+1]]);o.drawImage(h.canvas,f[4],f[5])}o.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;for(const r of t){const{data:a,width:o,height:l,transform:h}=r,c=this.cachedCanvases.getCanvas("maskCanvas",o,l),u=c.context;u.save();const f=this.getObject(a,r);mu(u,f),u.globalCompositeOperation="source-in",u.fillStyle=i?s.getPattern(u,this,os(e),Ut.FILL):s,u.fillRect(0,0,o,l),u.restore(),e.save(),e.transform(...h),e.scale(1,-1),ql(e,c.canvas,0,0,o,l,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);if(!e){U("Dependent image isn't ready yet");return}this.paintInlineImageXObject(e)}paintImageXObjectRepeat(t,e,s,i){if(!this.contentVisible)return;const r=this.getObject(t);if(!r){U("Dependent image isn't ready yet");return}const a=r.width,o=r.height,l=[];for(let h=0,c=i.length;h<c;h+=2)l.push({transform:[e,0,0,s,i[h],i[h+1]],x:0,y:0,w:a,h:o});this.paintInlineImageXObjectGroup(r,l)}applyTransferMapsToCanvas(t){return this.current.transferMaps!=="none"&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if(this.current.transferMaps==="none")return t.bitmap;const{bitmap:e,width:s,height:i}=t,r=this.cachedCanvases.getCanvas("inlineImage",s,i),a=r.context;return a.filter=this.current.transferMaps,a.drawImage(e,0,0),a.filter="none",r.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,i=this.ctx;this.save();const{filter:r}=i;r!=="none"&&r!==""&&(i.filter="none"),i.scale(1/e,-1/s);let a;if(t.bitmap)a=this.applyTransferMapsToBitmap(t);else if(typeof HTMLElement=="function"&&t instanceof HTMLElement||!t.data)a=t;else{const h=this.cachedCanvases.getCanvas("inlineImage",e,s).context;gu(h,t),a=this.applyTransferMapsToCanvas(h)}const o=this._scaleImage(a,os(i));i.imageSmoothingEnabled=bu(pt(i),t.interpolate),ql(i,o.img,0,0,o.paintWidth,o.paintHeight,0,-s,e,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const s=this.ctx;let i;if(t.bitmap)i=t.bitmap;else{const r=t.width,a=t.height,l=this.cachedCanvases.getCanvas("inlineImage",r,a).context;gu(l,t),i=this.applyTransferMapsToCanvas(l)}for(const r of e)s.save(),s.transform(...r.transform),s.scale(1,-1),ql(s,i,r.x,r.y,r.w,r.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){t==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t,e){const s=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(e);const i=this.ctx;this.pendingClip&&(s||(this.pendingClip===Au?i.clip(t,"evenodd"):i.clip(t)),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox)}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=pt(this.ctx);if(t[1]===0&&t[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),s=Math.hypot(t[0],t[2]),i=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(s,i)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:t}=this.current,{a:e,b:s,c:i,d:r}=this.ctx.getTransform();let a,o;if(s===0&&i===0){const l=Math.abs(e),h=Math.abs(r);if(l===h)if(t===0)a=o=1/l;else{const c=l*t;a=o=c<1?1/c:1}else if(t===0)a=1/l,o=1/h;else{const c=l*t,u=h*t;a=c<1?1/c:1,o=u<1?1/u:1}}else{const l=Math.abs(e*r-s*i),h=Math.hypot(e,s),c=Math.hypot(i,r);if(t===0)a=c/l,o=h/l;else{const u=t*l;a=c>u?c/u:1,o=h>u?h/u:1}}this._cachedScaleForStroking[0]=a,this._cachedScaleForStroking[1]=o}return this._cachedScaleForStroking}rescaleAndStroke(t,e){const{ctx:s,current:{lineWidth:i}}=this,[r,a]=this.getScaleForStroking();if(r===a){s.lineWidth=(i||1)*r,s.stroke(t);return}const o=s.getLineDash();e&&s.save(),s.scale(r,a),mc.a=1/r,mc.d=1/a;const l=new Path2D;if(l.addPath(t,mc),o.length>0){const h=Math.max(r,a);s.setLineDash(o.map(c=>c/h)),s.lineDashOffset/=h}s.lineWidth=i||1,s.stroke(l),e&&s.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}};as=new WeakSet,Vc=function(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null,this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)},jc=function(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(t!=="none"){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}},Wc=function(t,e,s){const i=new Path2D;return i.addPath(t,new DOMMatrix(s).invertSelf().multiplySelf(e)),i};let Dr=Qd;for(const d in Ph)Dr.prototype[d]!==void 0&&(Dr.prototype[Ph[d]]=Dr.prototype[d]);var Go,zo;class li{static get workerPort(){return n(this,Go)}static set workerPort(t){if(!(typeof Worker<"u"&&t instanceof Worker)&&t!==null)throw new Error("Invalid `workerPort` type.");p(this,Go,t)}static get workerSrc(){return n(this,zo)}static set workerSrc(t){if(typeof t!="string")throw new Error("Invalid `workerSrc` type.");p(this,zo,t)}}Go=new WeakMap,zo=new WeakMap,m(li,Go,null),m(li,zo,"");var ta,Uo;class Cg{constructor({parsedData:t,rawData:e}){m(this,ta);m(this,Uo);p(this,ta,t),p(this,Uo,e)}getRaw(){return n(this,Uo)}get(t){return n(this,ta).get(t)??null}[Symbol.iterator](){return n(this,ta).entries()}}ta=new WeakMap,Uo=new WeakMap;const Rr=Symbol("INTERNAL");var Vo,jo,Wo,ea;class xg{constructor(t,{name:e,intent:s,usage:i,rbGroups:r}){m(this,Vo,!1);m(this,jo,!1);m(this,Wo,!1);m(this,ea,!0);p(this,Vo,!!(t&Pe.DISPLAY)),p(this,jo,!!(t&Pe.PRINT)),this.name=e,this.intent=s,this.usage=i,this.rbGroups=r}get visible(){if(n(this,Wo))return n(this,ea);if(!n(this,ea))return!1;const{print:t,view:e}=this.usage;return n(this,Vo)?(e==null?void 0:e.viewState)!=="OFF":n(this,jo)?(t==null?void 0:t.printState)!=="OFF":!0}_setVisible(t,e,s=!1){t!==Rr&&rt("Internal method `_setVisible` called."),p(this,Wo,s),p(this,ea,e)}}Vo=new WeakMap,jo=new WeakMap,Wo=new WeakMap,ea=new WeakMap;var Li,it,sa,ia,qo,qc;class Tg{constructor(t,e=Pe.DISPLAY){m(this,qo);m(this,Li,null);m(this,it,new Map);m(this,sa,null);m(this,ia,null);if(this.renderingIntent=e,this.name=null,this.creator=null,t!==null){this.name=t.name,this.creator=t.creator,p(this,ia,t.order);for(const s of t.groups)n(this,it).set(s.id,new xg(e,s));if(t.baseState==="OFF")for(const s of n(this,it).values())s._setVisible(Rr,!1);for(const s of t.on)n(this,it).get(s)._setVisible(Rr,!0);for(const s of t.off)n(this,it).get(s)._setVisible(Rr,!1);p(this,sa,this.getHash())}}isVisible(t){if(n(this,it).size===0)return!0;if(!t)return rc("Optional content group not defined."),!0;if(t.type==="OCG")return n(this,it).has(t.id)?n(this,it).get(t.id).visible:(U(`Optional content group not found: ${t.id}`),!0);if(t.type==="OCMD"){if(t.expression)return b(this,qo,qc).call(this,t.expression);if(!t.policy||t.policy==="AnyOn"){for(const e of t.ids){if(!n(this,it).has(e))return U(`Optional content group not found: ${e}`),!0;if(n(this,it).get(e).visible)return!0}return!1}else if(t.policy==="AllOn"){for(const e of t.ids){if(!n(this,it).has(e))return U(`Optional content group not found: ${e}`),!0;if(!n(this,it).get(e).visible)return!1}return!0}else if(t.policy==="AnyOff"){for(const e of t.ids){if(!n(this,it).has(e))return U(`Optional content group not found: ${e}`),!0;if(!n(this,it).get(e).visible)return!0}return!1}else if(t.policy==="AllOff"){for(const e of t.ids){if(!n(this,it).has(e))return U(`Optional content group not found: ${e}`),!0;if(n(this,it).get(e).visible)return!1}return!0}return U(`Unknown optional content policy ${t.policy}.`),!0}return U(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0,s=!0){var r;const i=n(this,it).get(t);if(!i){U(`Optional content group not found: ${t}`);return}if(s&&e&&i.rbGroups.length)for(const a of i.rbGroups)for(const o of a)o!==t&&((r=n(this,it).get(o))==null||r._setVisible(Rr,!1,!0));i._setVisible(Rr,!!e,!0),p(this,Li,null)}setOCGState({state:t,preserveRB:e}){let s;for(const i of t){switch(i){case"ON":case"OFF":case"Toggle":s=i;continue}const r=n(this,it).get(i);if(r)switch(s){case"ON":this.setVisibility(i,!0,e);break;case"OFF":this.setVisibility(i,!1,e);break;case"Toggle":this.setVisibility(i,!r.visible,e);break}}p(this,Li,null)}get hasInitialVisibility(){return n(this,sa)===null||this.getHash()===n(this,sa)}getOrder(){return n(this,it).size?n(this,ia)?n(this,ia).slice():[...n(this,it).keys()]:null}getGroup(t){return n(this,it).get(t)||null}getHash(){if(n(this,Li)!==null)return n(this,Li);const t=new of;for(const[e,s]of n(this,it))t.update(`${e}:${s.visible}`);return p(this,Li,t.hexdigest())}[Symbol.iterator](){return n(this,it).entries()}}Li=new WeakMap,it=new WeakMap,sa=new WeakMap,ia=new WeakMap,qo=new WeakSet,qc=function(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let i=1;i<e;i++){const r=t[i];let a;if(Array.isArray(r))a=b(this,qo,qc).call(this,r);else if(n(this,it).has(r))a=n(this,it).get(r).visible;else return U(`Optional content group not found: ${r}`),!0;switch(s){case"And":if(!a)return!1;break;case"Or":if(a)return!0;break;case"Not":return!a;default:return!0}}return s==="And"};class Rg{constructor(t,{disableRange:e=!1,disableStream:s=!1}){Et(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:i,initialData:r,progressiveDone:a,contentDispositionFilename:o}=t;if(this._queuedChunks=[],this._progressiveDone=a,this._contentDispositionFilename=o,(r==null?void 0:r.length)>0){const l=r instanceof Uint8Array&&r.byteLength===r.buffer.byteLength?r.buffer:new Uint8Array(r).buffer;this._queuedChunks.push(l)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!s,this._isRangeSupported=!e,this._contentLength=i,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener((l,h)=>{this._onReceiveData({begin:l,chunk:h})}),t.addProgressListener((l,h)=>{this._onProgress({loaded:l,total:h})}),t.addProgressiveReadListener(l=>{this._onReceiveData({chunk:l})}),t.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const s=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(t===void 0)this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s);else{const i=this._rangeReaders.some(function(r){return r._begin!==t?!1:(r._enqueue(s),!0)});Et(i,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){var t;return((t=this._fullRequestReader)==null?void 0:t._loaded)??0}_onProgress(t){var e,s,i,r;t.total===void 0?(s=(e=this._rangeReaders[0])==null?void 0:e.onProgress)==null||s.call(e,{loaded:t.loaded}):(r=(i=this._fullRequestReader)==null?void 0:i.onProgress)==null||r.call(i,{loaded:t.loaded,total:t.total})}_onProgressiveDone(){var t;(t=this._fullRequestReader)==null||t.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){Et(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new Pg(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new Ig(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeReaders.slice(0))s.cancel(t);this._pdfDataRangeTransport.abort()}}class Pg{constructor(t,e,s=!1,i=null){this._stream=t,this._done=s||!1,this._filename=Hd(i)?i:null,this._queuedChunks=e||[],this._loaded=0;for(const r of this._queuedChunks)this._loaded+=r.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){this._done||(this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunks.push(t),this._loaded+=t.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class Ig{constructor(t,e,s){this._stream=t,this._begin=e,this._end=s,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length===0)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function kg(d){let t=!0,e=s("filename\\*","i").exec(d);if(e){e=e[1];let c=o(e);return c=unescape(c),c=l(c),c=h(c),r(c)}if(e=a(d),e){const c=h(e);return r(c)}if(e=s("filename","i").exec(d),e){e=e[1];let c=o(e);return c=h(c),r(c)}function s(c,u){return new RegExp("(?:^|;)\\s*"+c+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',u)}function i(c,u){if(c){if(!/^[\x00-\xFF]+$/.test(u))return u;try{const f=new TextDecoder(c,{fatal:!0}),g=Bl(u);u=f.decode(g),t=!1}catch{}}return u}function r(c){return t&&/[\x80-\xff]/.test(c)&&(c=i("utf-8",c),t&&(c=i("iso-8859-1",c))),c}function a(c){const u=[];let f;const g=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(f=g.exec(c))!==null;){let[,A,w,v]=f;if(A=parseInt(A,10),A in u){if(A===0)break;continue}u[A]=[w,v]}const y=[];for(let A=0;A<u.length&&A in u;++A){let[w,v]=u[A];v=o(v),w&&(v=unescape(v),A===0&&(v=l(v))),y.push(v)}return y.join("")}function o(c){if(c.startsWith('"')){const u=c.slice(1).split('\\"');for(let f=0;f<u.length;++f){const g=u[f].indexOf('"');g!==-1&&(u[f]=u[f].slice(0,g),u.length=f+1),u[f]=u[f].replaceAll(/\\(.)/g,"$1")}c=u.join('"')}return c}function l(c){const u=c.indexOf("'");if(u===-1)return c;const f=c.slice(0,u),y=c.slice(u+1).replace(/^[^']*'/,"");return i(f,y)}function h(c){return!c.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(c)?c:c.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(u,f,g,y){if(g==="q"||g==="Q")return y=y.replaceAll("_"," "),y=y.replaceAll(/=([0-9a-fA-F]{2})/g,function(A,w){return String.fromCharCode(parseInt(w,16))}),i(f,y);try{y=atob(y)}catch{}return i(f,y)})}return""}function yf(d,t){const e=new Headers;if(!d||!t||typeof t!="object")return e;for(const s in t){const i=t[s];i!==void 0&&e.append(s,i)}return e}function oc(d){var t;return((t=URL.parse(d))==null?void 0:t.origin)??null}function wf({responseHeaders:d,isHttp:t,rangeChunkSize:e,disableRange:s}){const i={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(d.get("Content-Length"),10);return!Number.isInteger(r)||(i.suggestedLength=r,r<=2*e)||s||!t||d.get("Accept-Ranges")!=="bytes"||(d.get("Content-Encoding")||"identity")!=="identity"||(i.allowRangeRequests=!0),i}function vf(d){const t=d.get("Content-Disposition");if(t){let e=kg(t);if(e.includes("%"))try{e=decodeURIComponent(e)}catch{}if(Hd(e))return e}return null}function zl(d,t){return new Ih(`Unexpected server response (${d}) while retrieving PDF "${t}".`,d,d===404||d===0&&t.startsWith("file:"))}function _f(d){return d===200||d===206}function Sf(d,t,e){return{method:"GET",headers:d,signal:e.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}function Ef(d){return d instanceof Uint8Array?d.buffer:d instanceof ArrayBuffer?d:(U(`getArrayBuffer - unexpected data format: ${d}`),new Uint8Array(d).buffer)}class yu{constructor(t){R(this,"_responseOrigin",null);this.source=t,this.isHttp=/^https?:/i.test(t.url),this.headers=yf(this.isHttp,t.httpHeaders),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var t;return((t=this._fullRequestReader)==null?void 0:t._loaded)??0}getFullReader(){return Et(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new Mg(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new Lg(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class Mg{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange;const s=new Headers(t.headers),i=e.url;fetch(i,Sf(s,this._withCredentials,this._abortController)).then(r=>{if(t._responseOrigin=oc(r.url),!_f(r.status))throw zl(r.status,i);this._reader=r.body.getReader(),this._headersCapability.resolve();const a=r.headers,{allowRangeRequests:o,suggestedLength:l}=wf({responseHeaders:a,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=o,this._contentLength=l||this._contentLength,this._filename=vf(a),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new tn("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var s;await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded,total:this._contentLength}),{value:Ef(t),done:!1})}cancel(t){var e;(e=this._reader)==null||e.cancel(t),this._abortController.abort()}}class Lg{constructor(t,e,s){this._stream=t,this._reader=null,this._loaded=0;const i=t.source;this._withCredentials=i.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!i.disableStream,this._abortController=new AbortController;const r=new Headers(t.headers);r.append("Range",`bytes=${e}-${s-1}`);const a=i.url;fetch(a,Sf(r,this._withCredentials,this._abortController)).then(o=>{const l=oc(o.url);if(l!==t._responseOrigin)throw new Error(`Expected range response-origin "${l}" to match "${t._responseOrigin}".`);if(!_f(o.status))throw zl(o.status,a);this._readCapability.resolve(),this._reader=o.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){var s;await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded}),{value:Ef(t),done:!1})}cancel(t){var e;(e=this._reader)==null||e.cancel(t),this._abortController.abort()}}const bc=200,Ac=206;function Dg(d){const t=d.response;return typeof t!="string"?t:Bl(t).buffer}class Fg{constructor({url:t,httpHeaders:e,withCredentials:s}){R(this,"_responseOrigin",null);this.url=t,this.isHttp=/^https?:/i.test(t),this.headers=yf(this.isHttp,e),this.withCredentials=s||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,s=this.currXhrId++,i=this.pendingRequests[s]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const[r,a]of this.headers)e.setRequestHeader(r,a);return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),i.expectedStatus=Ac):i.expectedStatus=bc,e.responseType="arraybuffer",Et(t.onError,"Expected `onError` callback to be provided."),e.onerror=()=>{t.onError(e.status)},e.onreadystatechange=this.onStateChange.bind(this,s),e.onprogress=this.onProgress.bind(this,s),i.onHeadersReceived=t.onHeadersReceived,i.onDone=t.onDone,i.onError=t.onError,i.onProgress=t.onProgress,e.send(null),s}onProgress(t,e){var i;const s=this.pendingRequests[t];s&&((i=s.onProgress)==null||i.call(s,e))}onStateChange(t,e){const s=this.pendingRequests[t];if(!s)return;const i=s.xhr;if(i.readyState>=2&&s.onHeadersReceived&&(s.onHeadersReceived(),delete s.onHeadersReceived),i.readyState!==4||!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],i.status===0&&this.isHttp){s.onError(i.status);return}const r=i.status||bc;if(!(r===bc&&s.expectedStatus===Ac)&&r!==s.expectedStatus){s.onError(i.status);return}const o=Dg(i);if(r===Ac){const l=i.getResponseHeader("Content-Range"),h=/bytes (\d+)-(\d+)\/(\d+)/.exec(l);h?s.onDone({begin:parseInt(h[1],10),chunk:o}):(U('Missing or invalid "Content-Range" header.'),s.onError(0))}else o?s.onDone({begin:0,chunk:o}):s.onError(i.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class Ng{constructor(t){this._source=t,this._manager=new Fg(t),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return Et(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new Og(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const s=new Bg(this._manager,t,e);return s.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class Og{constructor(t,e){this._manager=t,this._url=e.url,this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=oc(e.responseURL);const s=e.getAllResponseHeaders(),i=new Headers(s?s.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map(o=>{const[l,...h]=o.split(": ");return[l,h.join(": ")]}):[]),{allowRangeRequests:r,suggestedLength:a}=wf({responseHeaders:i,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});r&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=vf(i),this._isRangeSupported&&this._manager.abortRequest(t),this._headersCapability.resolve()}_onDone(t){if(t&&(this._requests.length>0?this._requests.shift().resolve({value:t.chunk,done:!1}):this._cachedChunks.push(t.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=zl(t,this._url),this._headersCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){var e;(e=this.onProgress)==null||e.call(this,{loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){if(await this._headersCapability.promise,this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class Bg{constructor(t,e,s){this._manager=t,this._url=t.url,this._requestId=t.request({begin:e,end:s,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_onHeadersReceived(){var e;const t=oc((e=this._manager.getRequestXhr(this._requestId))==null?void 0:e.responseURL);t!==this._manager._responseOrigin&&(this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`),this._onError(0))}_close(){var t;(t=this.onClosed)==null||t.call(this,this)}_onDone(t){const e=t.chunk;this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunk=e,this._done=!0;for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError??(this._storedError=zl(t,this._url));for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){var e;this.isStreamingSupported||(e=this.onProgress)==null||e.call(this,{loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const Hg=/^[a-z][a-z0-9\-+.]+:/i;function $g(d){if(Hg.test(d))return new URL(d);const t=process.getBuiltinModule("url");return new URL(t.pathToFileURL(d))}class Gg{constructor(t){this.source=t,this.url=$g(t.url),Et(this.url.protocol==="file:","PDFNodeStream only supports file:// URLs."),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var t;return((t=this._fullRequestReader)==null?void 0:t._loaded)??0}getFullReader(){return Et(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=new zg(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new Ug(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class zg{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers();const s=process.getBuiltinModule("fs");s.promises.lstat(this._url).then(i=>{this._contentLength=i.size,this._setReadableStream(s.createReadStream(this._url)),this._headersCapability.resolve()},i=>{i.code==="ENOENT"&&(i=zl(0,this._url.href)),this._storedError=i,this._headersCapability.reject(i)})}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var s;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new tn("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class Ug{constructor(t,e,s){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const i=t.source;this._isStreamingSupported=!i.disableStream;const r=process.getBuiltinModule("fs");this._setReadableStream(r.createReadStream(this._url,{start:e,end:s-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){var s;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),this._storedError&&this._readableStream.destroy(this._storedError)}}const Vg=1e5,wu=30;var Eu,Di,ue,Xo,Yo,Fn,js,Ko,Qo,Nn,na,ra,Fi,aa,Jo,oa,On,Zo,tl,la,Bn,el,Ni,ha,fi,Cf,xf,Xc,Me,hh,Yc,Tf,Rf;const xt=class xt{constructor({textContentSource:t,container:e,viewport:s}){m(this,fi);m(this,Di,Promise.withResolvers());m(this,ue,null);m(this,Xo,!1);m(this,Yo,!!((Eu=globalThis.FontInspector)!=null&&Eu.enabled));m(this,Fn,null);m(this,js,null);m(this,Ko,0);m(this,Qo,0);m(this,Nn,null);m(this,na,null);m(this,ra,0);m(this,Fi,0);m(this,aa,Object.create(null));m(this,Jo,[]);m(this,oa,null);m(this,On,[]);m(this,Zo,new WeakMap);m(this,tl,null);var l;if(t instanceof ReadableStream)p(this,oa,t);else if(typeof t=="object")p(this,oa,new ReadableStream({start(h){h.enqueue(t),h.close()}}));else throw new Error('No "textContentSource" parameter specified.');p(this,ue,p(this,na,e)),p(this,Fi,s.scale*ui.pixelRatio),p(this,ra,s.rotation),p(this,js,{div:null,properties:null,ctx:null});const{pageWidth:i,pageHeight:r,pageX:a,pageY:o}=s.rawDims;p(this,tl,[1,0,0,-1,-a,o+r]),p(this,Qo,i),p(this,Ko,r),b(l=xt,Me,Tf).call(l),mr(e,s),n(this,Di).promise.finally(()=>{n(xt,ha).delete(this),p(this,js,null),p(this,aa,null)}).catch(()=>{})}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=Wt.platform;return X(this,"fontFamilyMap",new Map([["sans-serif",`${t&&e?"Calibri, ":""}sans-serif`],["monospace",`${t&&e?"Lucida Console, ":""}monospace`]]))}render(){const t=()=>{n(this,Nn).read().then(({value:e,done:s})=>{if(s){n(this,Di).resolve();return}n(this,Fn)??p(this,Fn,e.lang),Object.assign(n(this,aa),e.styles),b(this,fi,Cf).call(this,e.items),t()},n(this,Di).reject)};return p(this,Nn,n(this,oa).getReader()),n(xt,ha).add(this),t(),n(this,Di).promise}update({viewport:t,onBefore:e=null}){var r;const s=t.scale*ui.pixelRatio,i=t.rotation;if(i!==n(this,ra)&&(e==null||e(),p(this,ra,i),mr(n(this,na),{rotation:i})),s!==n(this,Fi)){e==null||e(),p(this,Fi,s);const a={div:null,properties:null,ctx:b(r=xt,Me,hh).call(r,n(this,Fn))};for(const o of n(this,On))a.properties=n(this,Zo).get(o),a.div=o,b(this,fi,Xc).call(this,a)}}cancel(){var e;const t=new tn("TextLayer task cancelled.");(e=n(this,Nn))==null||e.cancel(t).catch(()=>{}),p(this,Nn,null),n(this,Di).reject(t)}get textDivs(){return n(this,On)}get textContentItemsStr(){return n(this,Jo)}static cleanup(){if(!(n(this,ha).size>0)){n(this,la).clear();for(const{canvas:t}of n(this,Bn).values())t.remove();n(this,Bn).clear()}}};Di=new WeakMap,ue=new WeakMap,Xo=new WeakMap,Yo=new WeakMap,Fn=new WeakMap,js=new WeakMap,Ko=new WeakMap,Qo=new WeakMap,Nn=new WeakMap,na=new WeakMap,ra=new WeakMap,Fi=new WeakMap,aa=new WeakMap,Jo=new WeakMap,oa=new WeakMap,On=new WeakMap,Zo=new WeakMap,tl=new WeakMap,la=new WeakMap,Bn=new WeakMap,el=new WeakMap,Ni=new WeakMap,ha=new WeakMap,fi=new WeakSet,Cf=function(t){var i,r;if(n(this,Xo))return;(r=n(this,js)).ctx??(r.ctx=b(i=xt,Me,hh).call(i,n(this,Fn)));const e=n(this,On),s=n(this,Jo);for(const a of t){if(e.length>Vg){U("Ignoring additional textDivs for performance reasons."),p(this,Xo,!0);return}if(a.str===void 0){if(a.type==="beginMarkedContentProps"||a.type==="beginMarkedContent"){const o=n(this,ue);p(this,ue,document.createElement("span")),n(this,ue).classList.add("markedContent"),a.id!==null&&n(this,ue).setAttribute("id",`${a.id}`),o.append(n(this,ue))}else a.type==="endMarkedContent"&&p(this,ue,n(this,ue).parentNode);continue}s.push(a.str),b(this,fi,xf).call(this,a)}},xf=function(t){var A;const e=document.createElement("span"),s={angle:0,canvasWidth:0,hasText:t.str!=="",hasEOL:t.hasEOL,fontSize:0};n(this,On).push(e);const i=O.transform(n(this,tl),t.transform);let r=Math.atan2(i[1],i[0]);const a=n(this,aa)[t.fontName];a.vertical&&(r+=Math.PI/2);let o=n(this,Yo)&&a.fontSubstitution||a.fontFamily;o=xt.fontFamilyMap.get(o)||o;const l=Math.hypot(i[2],i[3]),h=l*b(A=xt,Me,Rf).call(A,o,a,n(this,Fn));let c,u;r===0?(c=i[4],u=i[5]-h):(c=i[4]+h*Math.sin(r),u=i[5]-h*Math.cos(r));const f="calc(var(--total-scale-factor) *",g=e.style;n(this,ue)===n(this,na)?(g.left=`${(100*c/n(this,Qo)).toFixed(2)}%`,g.top=`${(100*u/n(this,Ko)).toFixed(2)}%`):(g.left=`${f}${c.toFixed(2)}px)`,g.top=`${f}${u.toFixed(2)}px)`),g.fontSize=`${f}${(n(xt,Ni)*l).toFixed(2)}px)`,g.fontFamily=o,s.fontSize=l,e.setAttribute("role","presentation"),e.textContent=t.str,e.dir=t.dir,n(this,Yo)&&(e.dataset.fontName=a.fontSubstitutionLoadedName||t.fontName),r!==0&&(s.angle=r*(180/Math.PI));let y=!1;if(t.str.length>1)y=!0;else if(t.str!==" "&&t.transform[0]!==t.transform[3]){const w=Math.abs(t.transform[0]),v=Math.abs(t.transform[3]);w!==v&&Math.max(w,v)/Math.min(w,v)>1.5&&(y=!0)}if(y&&(s.canvasWidth=a.vertical?t.height:t.width),n(this,Zo).set(e,s),n(this,js).div=e,n(this,js).properties=s,b(this,fi,Xc).call(this,n(this,js)),s.hasText&&n(this,ue).append(e),s.hasEOL){const w=document.createElement("br");w.setAttribute("role","presentation"),n(this,ue).append(w)}},Xc=function(t){var o;const{div:e,properties:s,ctx:i}=t,{style:r}=e;let a="";if(n(xt,Ni)>1&&(a=`scale(${1/n(xt,Ni)})`),s.canvasWidth!==0&&s.hasText){const{fontFamily:l}=r,{canvasWidth:h,fontSize:c}=s;b(o=xt,Me,Yc).call(o,i,c*n(this,Fi),l);const{width:u}=i.measureText(e.textContent);u>0&&(a=`scaleX(${h*n(this,Fi)/u}) ${a}`)}s.angle!==0&&(a=`rotate(${s.angle}deg) ${a}`),a.length>0&&(r.transform=a)},Me=new WeakSet,hh=function(t=null){let e=n(this,Bn).get(t||(t=""));if(!e){const s=document.createElement("canvas");s.className="hiddenCanvasElement",s.lang=t,document.body.append(s),e=s.getContext("2d",{alpha:!1,willReadFrequently:!0}),n(this,Bn).set(t,e),n(this,el).set(e,{size:0,family:""})}return e},Yc=function(t,e,s){const i=n(this,el).get(t);e===i.size&&s===i.family||(t.font=`${e}px ${s}`,i.size=e,i.family=s)},Tf=function(){if(n(this,Ni)!==null)return;const t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.style.position="absolute",t.textContent="X",document.body.append(t),p(this,Ni,t.getBoundingClientRect().height),t.remove()},Rf=function(t,e,s){const i=n(this,la).get(t);if(i)return i;const r=b(this,Me,hh).call(this,s);r.canvas.width=r.canvas.height=wu,b(this,Me,Yc).call(this,r,wu,t);const a=r.measureText(""),o=a.fontBoundingBoxAscent,l=Math.abs(a.fontBoundingBoxDescent);r.canvas.width=r.canvas.height=0;let h=.8;return o?h=o/(o+l):(Wt.platform.isFirefox&&U("Enable the `dom.textMetrics.fontBoundingBox.enabled` preference in `about:config` to improve TextLayer rendering."),e.ascent?h=e.ascent:e.descent&&(h=1+e.descent)),n(this,la).set(t,h),h},m(xt,Me),m(xt,la,new Map),m(xt,Bn,new Map),m(xt,el,new WeakMap),m(xt,Ni,null),m(xt,ha,new Set);let fo=xt;class po{static textContent(t){const e=[],s={items:e,styles:Object.create(null)};function i(r){var l;if(!r)return;let a=null;const o=r.name;if(o==="#text")a=r.value;else if(po.shouldBuildText(o))(l=r==null?void 0:r.attributes)!=null&&l.textContent?a=r.attributes.textContent:r.value&&(a=r.value);else return;if(a!==null&&e.push({str:a}),!!r.children)for(const h of r.children)i(h)}return i(t),s}static shouldBuildText(t){return!(t==="textarea"||t==="input"||t==="option"||t==="select")}}const jg=65536,Wg=100;function qg(d={}){typeof d=="string"||d instanceof URL?d={url:d}:(d instanceof ArrayBuffer||ArrayBuffer.isView(d))&&(d={data:d});const t=new Qc,{docId:e}=t,s=d.url?Xg(d.url):null,i=d.data?Yg(d.data):null,r=d.httpHeaders||null,a=d.withCredentials===!0,o=d.password??null,l=d.range instanceof Pf?d.range:null,h=Number.isInteger(d.rangeChunkSize)&&d.rangeChunkSize>0?d.rangeChunkSize:jg;let c=d.worker instanceof Fr?d.worker:null;const u=d.verbosity,f=typeof d.docBaseUrl=="string"&&!ac(d.docBaseUrl)?d.docBaseUrl:null,g=Yl(d.cMapUrl),y=d.cMapPacked!==!1,A=d.CMapReaderFactory||(ae?cg:ou),w=Yl(d.iccUrl),v=Yl(d.standardFontDataUrl),_=d.StandardFontDataFactory||(ae?dg:lu),S=Yl(d.wasmUrl),E=d.WasmFactory||(ae?ug:hu),C=d.stopAtErrors!==!0,x=Number.isInteger(d.maxImageSize)&&d.maxImageSize>-1?d.maxImageSize:-1,T=d.isEvalSupported!==!1,P=typeof d.isOffscreenCanvasSupported=="boolean"?d.isOffscreenCanvasSupported:!ae,k=typeof d.isImageDecoderSupported=="boolean"?d.isImageDecoderSupported:!ae&&(Wt.platform.isFirefox||!globalThis.chrome),B=Number.isInteger(d.canvasMaxAreaInBytes)?d.canvasMaxAreaInBytes:-1,D=typeof d.disableFontFace=="boolean"?d.disableFontFace:ae,tt=d.fontExtraProperties===!0,st=d.enableXfa===!0,q=d.ownerDocument||globalThis.document,Zt=d.disableRange===!0,N=d.disableStream===!0,$=d.disableAutoFetch===!0,Le=d.pdfBug===!0,Ts=d.CanvasFactory||(ae?hg:ag),an=d.FilterFactory||(ae?lg:og),Rs=d.enableHWA===!0,kt=d.useWasm!==!1,bt=l?l.length:d.length??NaN,vr=typeof d.useSystemFonts=="boolean"?d.useSystemFonts:!ae&&!D,pi=typeof d.useWorkerFetch=="boolean"?d.useWorkerFetch:!!(A===ou&&_===lu&&E===hu&&g&&v&&S&&Er(g,document.baseURI)&&Er(v,document.baseURI)&&Er(S,document.baseURI)),gi=null;Hp(u);const gt={canvasFactory:new Ts({ownerDocument:q,enableHWA:Rs}),filterFactory:new an({docId:e,ownerDocument:q}),cMapReaderFactory:pi?null:new A({baseUrl:g,isCompressed:y}),standardFontDataFactory:pi?null:new _({baseUrl:v}),wasmFactory:pi?null:new E({baseUrl:S})};if(!c){const Va={verbosity:u,port:li.workerPort};c=Va.port?Fr.fromPort(Va):new Fr(Va),t._worker=c}const Ua={docId:e,apiVersion:"5.2.133",data:i,password:o,disableAutoFetch:$,rangeChunkSize:h,length:bt,docBaseUrl:f,enableXfa:st,evaluatorOptions:{maxImageSize:x,disableFontFace:D,ignoreErrors:C,isEvalSupported:T,isOffscreenCanvasSupported:P,isImageDecoderSupported:k,canvasMaxAreaInBytes:B,fontExtraProperties:tt,useSystemFonts:vr,useWasm:kt,useWorkerFetch:pi,cMapUrl:g,iccUrl:w,standardFontDataUrl:v,wasmUrl:S}},Mp={ownerDocument:q,pdfBug:Le,styleElement:gi,loadingParams:{disableAutoFetch:$,enableXfa:st}};return c.promise.then(function(){if(t.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const Va=c.messageHandler.sendWithPromise("GetDocRequest",Ua,i?[i.buffer]:null);let hc;if(l)hc=new Rg(l,{disableRange:Zt,disableStream:N});else if(!i){if(!s)throw new Error("getDocument - no `url` parameter provided.");let _r;if(ae)if(Er(s)){if(typeof fetch>"u"||typeof Response>"u"||!("body"in Response.prototype))throw new Error("getDocument - the Fetch API was disabled in Node.js, see `--no-experimental-fetch`.");_r=yu}else _r=Gg;else _r=Er(s)?yu:Ng;hc=new _r({url:s,length:bt,httpHeaders:r,withCredentials:a,rangeChunkSize:h,disableRange:Zt,disableStream:N})}return Va.then(_r=>{if(t.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const tu=new Za(e,_r,c.port),Lp=new em(tu,t,hc,Mp,gt);t._transport=Lp,tu.send("Ready",null)})}).catch(t._capability.reject),t}function Xg(d){if(d instanceof URL)return d.href;if(typeof d=="string"){if(ae)return d;const t=URL.parse(d,window.location);if(t)return t.href}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function Yg(d){if(ae&&typeof Buffer<"u"&&d instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(d instanceof Uint8Array&&d.byteLength===d.buffer.byteLength)return d;if(typeof d=="string")return Bl(d);if(d instanceof ArrayBuffer||ArrayBuffer.isView(d)||typeof d=="object"&&!isNaN(d==null?void 0:d.length))return new Uint8Array(d);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}function Yl(d){if(typeof d!="string")return null;if(d.endsWith("/"))return d;throw new Error(`Invalid factory url: "${d}" must include trailing slash.`)}const Kc=d=>typeof d=="object"&&Number.isInteger(d==null?void 0:d.num)&&d.num>=0&&Number.isInteger(d==null?void 0:d.gen)&&d.gen>=0,Kg=d=>typeof d=="object"&&typeof(d==null?void 0:d.name)=="string",Qg=Xp.bind(null,Kc,Kg);var Wh;const qh=class qh{constructor(){R(this,"_capability",Promise.withResolvers());R(this,"_transport",null);R(this,"_worker",null);R(this,"docId",`d${te(qh,Wh)._++}`);R(this,"destroyed",!1);R(this,"onPassword",null);R(this,"onProgress",null)}get promise(){return this._capability.promise}async destroy(){var t,e,s,i;this.destroyed=!0;try{(t=this._worker)!=null&&t.port&&(this._worker._pendingDestroy=!0),await((e=this._transport)==null?void 0:e.destroy())}catch(r){throw(s=this._worker)!=null&&s.port&&delete this._worker._pendingDestroy,r}this._transport=null,(i=this._worker)==null||i.destroy(),this._worker=null}async getData(){return this._transport.getData()}};Wh=new WeakMap,m(qh,Wh,0);let Qc=qh;class Pf{constructor(t,e,s=!1,i=null){this.length=t,this.initialData=e,this.progressiveDone=s,this.contentDispositionFilename=i,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const s of this._rangeListeners)s(t,e)}onDataProgress(t,e){this._readyCapability.promise.then(()=>{for(const s of this._progressListeners)s(t,e)})}onDataProgressiveRead(t){this._readyCapability.promise.then(()=>{for(const e of this._progressiveReadListeners)e(t)})}onDataProgressiveDone(){this._readyCapability.promise.then(()=>{for(const t of this._progressiveDoneListeners)t()})}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){rt("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class Jg{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return X(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}var Ws,Hn,no;class Zg{constructor(t,e,s,i=!1){m(this,Hn);m(this,Ws,!1);this._pageIndex=t,this._pageInfo=e,this._transport=s,this._stats=i?new nu:null,this._pdfBug=i,this.commonObjs=s.commonObjs,this.objs=new If,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:s=0,offsetY:i=0,dontFlip:r=!1}={}){return new $l({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:r})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return X(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){var t;return((t=this._transport._htmlForXfa)==null?void 0:t.children[this._pageIndex])||null}render({canvasContext:t,viewport:e,intent:s="display",annotationMode:i=bi.ENABLE,transform:r=null,background:a=null,optionalContentConfigPromise:o=null,annotationCanvasMap:l=null,pageColors:h=null,printAnnotationStorage:c=null,isEditing:u=!1}){var E,C;(E=this._stats)==null||E.time("Overall");const f=this._transport.getRenderingIntent(s,i,c,u),{renderingIntent:g,cacheKey:y}=f;p(this,Ws,!1),o||(o=this._transport.getOptionalContentConfig(g));let A=this._intentStates.get(y);A||(A=Object.create(null),this._intentStates.set(y,A)),A.streamReaderCancelTimeout&&(clearTimeout(A.streamReaderCancelTimeout),A.streamReaderCancelTimeout=null);const w=!!(g&Pe.PRINT);A.displayReadyCapability||(A.displayReadyCapability=Promise.withResolvers(),A.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(C=this._stats)==null||C.time("Page Request"),this._pumpOperatorList(f));const v=x=>{var T;A.renderTasks.delete(_),w&&p(this,Ws,!0),b(this,Hn,no).call(this),x?(_.capability.reject(x),this._abortOperatorList({intentState:A,reason:x instanceof Error?x:new Error(x)})):_.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),(T=globalThis.Stats)!=null&&T.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},_=new Zc({callback:v,params:{canvasContext:t,viewport:e,transform:r,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:l,operatorList:A.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!w,pdfBug:this._pdfBug,pageColors:h});(A.renderTasks||(A.renderTasks=new Set)).add(_);const S=_.task;return Promise.all([A.displayReadyCapability.promise,o]).then(([x,T])=>{var P;if(this.destroyed){v();return}if((P=this._stats)==null||P.time("Rendering"),!(T.renderingIntent&g))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");_.initializeGraphics({transparency:x,optionalContentConfig:T}),_.operatorListChanged()}).catch(v),S}getOperatorList({intent:t="display",annotationMode:e=bi.ENABLE,printAnnotationStorage:s=null,isEditing:i=!1}={}){var h;function r(){o.operatorList.lastChunk&&(o.opListReadCapability.resolve(o.operatorList),o.renderTasks.delete(l))}const a=this._transport.getRenderingIntent(t,e,s,i,!0);let o=this._intentStates.get(a.cacheKey);o||(o=Object.create(null),this._intentStates.set(a.cacheKey,o));let l;return o.opListReadCapability||(l=Object.create(null),l.operatorListChanged=r,o.opListReadCapability=Promise.withResolvers(),(o.renderTasks||(o.renderTasks=new Set)).add(l),o.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(h=this._stats)==null||h.time("Page Request"),this._pumpOperatorList(a)),o.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:t===!0,disableNormalization:e===!0},{highWaterMark:100,size(i){return i.items.length}})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then(s=>po.textContent(s));const e=this.streamTextContent(t);return new Promise(function(s,i){function r(){a.read().then(function({value:l,done:h}){if(h){s(o);return}o.lang??(o.lang=l.lang),Object.assign(o.styles,l.styles),o.items.push(...l.items),r()},i)}const a=e.getReader(),o={items:[],styles:Object.create(null),lang:null};r()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const s of e.renderTasks)t.push(s.completed),s.cancel();return this.objs.clear(),p(this,Ws,!1),Promise.all(t)}cleanup(t=!1){p(this,Ws,!0);const e=b(this,Hn,no).call(this);return t&&e&&this._stats&&(this._stats=new nu),e}_startRenderPage(t,e){var i,r;const s=this._intentStates.get(e);s&&((i=this._stats)==null||i.timeEnd("Page Request"),(r=s.displayReadyCapability)==null||r.resolve(t))}_renderPageChunk(t,e){for(let s=0,i=t.length;s<i;s++)e.operatorList.fnArray.push(t.fnArray[s]),e.operatorList.argsArray.push(t.argsArray[s]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const s of e.renderTasks)s.operatorListChanged();t.lastChunk&&b(this,Hn,no).call(this)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:s,modifiedIds:i}){const{map:r,transfer:a}=s,l=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:r,modifiedIds:i},a).getReader(),h=this._intentStates.get(e);h.streamReader=l;const c=()=>{l.read().then(({value:u,done:f})=>{if(f){h.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(u,h),c())},u=>{if(h.streamReader=null,!this._transport.destroyed){if(h.operatorList){h.operatorList.lastChunk=!0;for(const f of h.renderTasks)f.operatorListChanged();b(this,Hn,no).call(this)}if(h.displayReadyCapability)h.displayReadyCapability.reject(u);else if(h.opListReadCapability)h.opListReadCapability.reject(u);else throw u}})};c()}_abortOperatorList({intentState:t,reason:e,force:s=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!s){if(t.renderTasks.size>0)return;if(e instanceof Bd){let i=Wg;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),t.streamReaderCancelTimeout=setTimeout(()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})},i);return}}if(t.streamReader.cancel(new tn(e.message)).catch(()=>{}),t.streamReader=null,!this._transport.destroyed){for(const[i,r]of this._intentStates)if(r===t){this._intentStates.delete(i);break}this.cleanup()}}}get stats(){return this._stats}}Ws=new WeakMap,Hn=new WeakSet,no=function(){if(!n(this,Ws)||this.destroyed)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),p(this,Ws,!1),!0};var qs,Xh;class tm{constructor(){m(this,qs,new Map);m(this,Xh,Promise.resolve())}postMessage(t,e){const s={data:structuredClone(t,e?{transfer:e}:null)};n(this,Xh).then(()=>{for(const[i]of n(this,qs))i.call(this,s)})}addEventListener(t,e,s=null){let i=null;if((s==null?void 0:s.signal)instanceof AbortSignal){const{signal:r}=s;if(r.aborted){U("LoopbackPort - cannot use an `aborted` signal.");return}const a=()=>this.removeEventListener(t,e);i=()=>r.removeEventListener("abort",a),r.addEventListener("abort",a)}n(this,qs).set(e,i)}removeEventListener(t,e){const s=n(this,qs).get(e);s==null||s(),n(this,qs).delete(e)}terminate(){for(const[,t]of n(this,qs))t==null||t();n(this,qs).clear()}}qs=new WeakMap,Xh=new WeakMap;var Yh,$n,Gn,ca,ch,da,dh;const ct=class ct{constructor({name:t=null,port:e=null,verbosity:s=$p()}={}){m(this,ca);var i;if(this.name=t,this.destroyed=!1,this.verbosity=s,this._readyCapability=Promise.withResolvers(),this._port=null,this._webWorker=null,this._messageHandler=null,e){if((i=n(ct,Gn))!=null&&i.has(e))throw new Error("Cannot use more than one PDFWorker per port.");(n(ct,Gn)||p(ct,Gn,new WeakMap)).set(e,this),this._initializeFromPort(e);return}this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t,this._messageHandler=new Za("main","worker",t),this._messageHandler.on("ready",function(){}),b(this,ca,ch).call(this)}_initialize(){if(n(ct,$n)||n(ct,da,dh)){this._setupFakeWorker();return}let{workerSrc:t}=ct;try{ct._isSameOrigin(window.location,t)||(t=ct._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),s=new Za("main","worker",e),i=()=>{r.abort(),s.destroy(),e.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},r=new AbortController;e.addEventListener("error",()=>{this._webWorker||i()},{signal:r.signal}),s.on("test",o=>{if(r.abort(),this.destroyed||!o){i();return}this._messageHandler=s,this._port=e,this._webWorker=e,b(this,ca,ch).call(this)}),s.on("ready",o=>{if(r.abort(),this.destroyed){i();return}try{a()}catch{this._setupFakeWorker()}});const a=()=>{const o=new Uint8Array;s.send("test",o,[o.buffer])};a();return}catch{rc("The worker has been disabled.")}this._setupFakeWorker()}_setupFakeWorker(){n(ct,$n)||(U("Setting up fake worker."),p(ct,$n,!0)),ct._setupFakeWorkerGlobal.then(t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new tm;this._port=e;const s=`fake${te(ct,Yh)._++}`,i=new Za(s+"_worker",s,e);t.setup(i,e),this._messageHandler=new Za(s,s+"_worker",e),b(this,ca,ch).call(this)}).catch(t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))})}destroy(){var t,e,s;this.destroyed=!0,(t=this._webWorker)==null||t.terminate(),this._webWorker=null,(e=n(ct,Gn))==null||e.delete(this._port),this._port=null,(s=this._messageHandler)==null||s.destroy(),this._messageHandler=null}static fromPort(t){var s;if(!(t!=null&&t.port))throw new Error("PDFWorker.fromPort - invalid method signature.");const e=(s=n(this,Gn))==null?void 0:s.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new ct(t)}static get workerSrc(){if(li.workerSrc)return li.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _setupFakeWorkerGlobal(){return X(this,"_setupFakeWorkerGlobal",(async()=>n(this,da,dh)?n(this,da,dh):(await import(this.workerSrc)).WorkerMessageHandler)())}};Yh=new WeakMap,$n=new WeakMap,Gn=new WeakMap,ca=new WeakSet,ch=function(){this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})},da=new WeakSet,dh=function(){var t;try{return((t=globalThis.pdfjsWorker)==null?void 0:t.WorkerMessageHandler)||null}catch{return null}},m(ct,da),m(ct,Yh,0),m(ct,$n,!1),m(ct,Gn),ae&&(p(ct,$n,!0),li.workerSrc||(li.workerSrc="./pdf.worker.mjs")),ct._isSameOrigin=(t,e)=>{const s=URL.parse(t);if(!(s!=null&&s.origin)||s.origin==="null")return!1;const i=new URL(e,s);return s.origin===i.origin},ct._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))};let Fr=ct;var Xs,ys,ua,fa,Ys,zn,ro;class em{constructor(t,e,s,i,r){m(this,zn);m(this,Xs,new Map);m(this,ys,new Map);m(this,ua,new Map);m(this,fa,new Map);m(this,Ys,null);this.messageHandler=t,this.loadingTask=e,this.commonObjs=new If,this.fontLoader=new ng({ownerDocument:i.ownerDocument,styleElement:i.styleElement}),this.loadingParams=i.loadingParams,this._params=i,this.canvasFactory=r.canvasFactory,this.filterFactory=r.filterFactory,this.cMapReaderFactory=r.cMapReaderFactory,this.standardFontDataFactory=r.standardFontDataFactory,this.wasmFactory=r.wasmFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=s,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}get annotationStorage(){return X(this,"annotationStorage",new Ud)}getRenderingIntent(t,e=bi.ENABLE,s=null,i=!1,r=!1){let a=Pe.DISPLAY,o=Hc;switch(t){case"any":a=Pe.ANY;break;case"display":break;case"print":a=Pe.PRINT;break;default:U(`getRenderingIntent - invalid intent: ${t}`)}const l=a&Pe.PRINT&&s instanceof hf?s:this.annotationStorage;switch(e){case bi.DISABLE:a+=Pe.ANNOTATIONS_DISABLE;break;case bi.ENABLE:break;case bi.ENABLE_FORMS:a+=Pe.ANNOTATIONS_FORMS;break;case bi.ENABLE_STORAGE:a+=Pe.ANNOTATIONS_STORAGE,o=l.serializable;break;default:U(`getRenderingIntent - invalid annotationMode: ${e}`)}i&&(a+=Pe.IS_EDITING),r&&(a+=Pe.OPLIST);const{ids:h,hash:c}=l.modifiedIds,u=[a,o.hash,c];return{renderingIntent:a,cacheKey:u.join("_"),annotationStorageSerializable:o,modifiedIds:h}}destroy(){var s;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),(s=n(this,Ys))==null||s.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const i of n(this,ys).values())t.push(i._destroy());n(this,ys).clear(),n(this,ua).clear(),n(this,fa).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then(()=>{var i,r;this.commonObjs.clear(),this.fontLoader.clear(),n(this,Xs).clear(),this.filterFactory.destroy(),fo.cleanup(),(i=this._networkStream)==null||i.cancelAllRequests(new tn("Worker was terminated.")),(r=this.messageHandler)==null||r.destroy(),this.messageHandler=null,this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",(s,i)=>{Et(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=r=>{this._lastProgress={loaded:r.loaded,total:r.total}},i.onPull=()=>{this._fullReader.read().then(function({value:r,done:a}){if(a){i.close();return}Et(r instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(r),1,[r])}).catch(r=>{i.error(r)})},i.onCancel=r=>{this._fullReader.cancel(r),i.ready.catch(a=>{if(!this.destroyed)throw a})}}),t.on("ReaderHeadersReady",async s=>{var o;await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:r,contentLength:a}=this._fullReader;return(!i||!r)&&(this._lastProgress&&((o=e.onProgress)==null||o.call(e,this._lastProgress)),this._fullReader.onProgress=l=>{var h;(h=e.onProgress)==null||h.call(e,{loaded:l.loaded,total:l.total})}),{isStreamingSupported:i,isRangeSupported:r,contentLength:a}}),t.on("GetRangeReader",(s,i)=>{Et(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const r=this._networkStream.getRangeReader(s.begin,s.end);if(!r){i.close();return}i.onPull=()=>{r.read().then(function({value:a,done:o}){if(o){i.close();return}Et(a instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(a),1,[a])}).catch(a=>{i.error(a)})},i.onCancel=a=>{r.cancel(a),i.ready.catch(o=>{if(!this.destroyed)throw o})}}),t.on("GetDoc",({pdfInfo:s})=>{this._numPages=s.numPages,this._htmlForXfa=s.htmlForXfa,delete s.htmlForXfa,e._capability.resolve(new Jg(s,this))}),t.on("DocException",s=>{e._capability.reject(he(s))}),t.on("PasswordRequest",s=>{p(this,Ys,Promise.withResolvers());try{if(!e.onPassword)throw he(s);const i=r=>{r instanceof Error?n(this,Ys).reject(r):n(this,Ys).resolve({password:r})};e.onPassword(i,s.code)}catch(i){n(this,Ys).reject(i)}return n(this,Ys).promise}),t.on("DataLoaded",s=>{var i;(i=e.onProgress)==null||i.call(e,{loaded:s.length,total:s.length}),this.downloadInfoCapability.resolve(s)}),t.on("StartRenderPage",s=>{if(this.destroyed)return;n(this,ys).get(s.pageIndex)._startRenderPage(s.transparency,s.cacheKey)}),t.on("commonobj",([s,i,r])=>{var a;if(this.destroyed||this.commonObjs.has(s))return null;switch(i){case"Font":if("error"in r){const c=r.error;U(`Error during font loading: ${c}`),this.commonObjs.resolve(s,c);break}const o=this._params.pdfBug&&((a=globalThis.FontInspector)!=null&&a.enabled)?(c,u)=>globalThis.FontInspector.fontAdded(c,u):null,l=new rg(r,o);this.fontLoader.bind(l).catch(()=>t.sendWithPromise("FontFallback",{id:s})).finally(()=>{!l.fontExtraProperties&&l.data&&(l.data=null),this.commonObjs.resolve(s,l)});break;case"CopyLocalImage":const{imageRef:h}=r;Et(h,"The imageRef must be defined.");for(const c of n(this,ys).values())for(const[,u]of c.objs)if((u==null?void 0:u.ref)===h)return u.dataLen?(this.commonObjs.resolve(s,structuredClone(u)),u.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(s,r);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}),t.on("obj",([s,i,r,a])=>{var l;if(this.destroyed)return;const o=n(this,ys).get(i);if(!o.objs.has(s)){if(o._intentStates.size===0){(l=a==null?void 0:a.bitmap)==null||l.close();return}switch(r){case"Image":case"Pattern":o.objs.resolve(s,a);break;default:throw new Error(`Got unknown object type ${r}`)}}}),t.on("DocProgress",s=>{var i;this.destroyed||(i=e.onProgress)==null||i.call(e,{loaded:s.loaded,total:s.total})}),t.on("FetchBinaryData",async s=>{if(this.destroyed)throw new Error("Worker was destroyed.");const i=this[s.type];if(!i)throw new Error(`${s.type} not initialized, see the \`useWorkerFetch\` parameter.`);return i.fetch(s)})}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){var s;this.annotationStorage.size<=0&&U("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:((s=this._fullReader)==null?void 0:s.filename)??null},e).finally(()=>{this.annotationStorage.resetModified()})}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,s=n(this,ua).get(e);if(s)return s;const i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then(r=>{if(this.destroyed)throw new Error("Transport destroyed");r.refStr&&n(this,fa).set(r.refStr,t);const a=new Zg(e,r,this,this._params.pdfBug);return n(this,ys).set(e,a),a});return n(this,ua).set(e,i),i}getPageIndex(t){return Kc(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return b(this,zn,ro).call(this,"GetFieldObjects")}hasJSActions(){return b(this,zn,ro).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return typeof t!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return b(this,zn,ro).call(this,"GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return b(this,zn,ro).call(this,"GetOptionalContentConfig").then(e=>new Tg(e,t))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=n(this,Xs).get(t);if(e)return e;const s=this.messageHandler.sendWithPromise(t,null).then(i=>{var r,a;return{info:i[0],metadata:i[1]?new Cg(i[1]):null,contentDispositionFilename:((r=this._fullReader)==null?void 0:r.filename)??null,contentLength:((a=this._fullReader)==null?void 0:a.contentLength)??null}});return n(this,Xs).set(t,s),s}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const e of n(this,ys).values())if(!e.cleanup())throw new Error(`startCleanup: Page ${e.pageNumber} is currently rendering.`);this.commonObjs.clear(),t||this.fontLoader.clear(),n(this,Xs).clear(),this.filterFactory.destroy(!0),fo.cleanup()}}cachedPageNumber(t){if(!Kc(t))return null;const e=t.gen===0?`${t.num}R`:`${t.num}R${t.gen}`;return n(this,fa).get(e)??null}}Xs=new WeakMap,ys=new WeakMap,ua=new WeakMap,fa=new WeakMap,Ys=new WeakMap,zn=new WeakSet,ro=function(t,e=null){const s=n(this,Xs).get(t);if(s)return s;const i=this.messageHandler.sendWithPromise(t,e);return n(this,Xs).set(t,i),i};const Wa=Symbol("INITIAL_DATA");var ye,sl,Jc;class If{constructor(){m(this,sl);m(this,ye,Object.create(null))}get(t,e=null){if(e){const i=b(this,sl,Jc).call(this,t);return i.promise.then(()=>e(i.data)),null}const s=n(this,ye)[t];if(!s||s.data===Wa)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return s.data}has(t){const e=n(this,ye)[t];return!!e&&e.data!==Wa}delete(t){const e=n(this,ye)[t];return!e||e.data===Wa?!1:(delete n(this,ye)[t],!0)}resolve(t,e=null){const s=b(this,sl,Jc).call(this,t);s.data=e,s.resolve()}clear(){var t;for(const e in n(this,ye)){const{data:s}=n(this,ye)[e];(t=s==null?void 0:s.bitmap)==null||t.close()}p(this,ye,Object.create(null))}*[Symbol.iterator](){for(const t in n(this,ye)){const{data:e}=n(this,ye)[t];e!==Wa&&(yield[t,e])}}}ye=new WeakMap,sl=new WeakSet,Jc=function(t){var e;return(e=n(this,ye))[t]||(e[t]={...Promise.withResolvers(),data:Wa})};var Oi;class sm{constructor(t){m(this,Oi,null);R(this,"onContinue",null);R(this,"onError",null);p(this,Oi,t)}get promise(){return n(this,Oi).capability.promise}cancel(t=0){n(this,Oi).cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=n(this,Oi).operatorList;if(!t)return!1;const{annotationCanvasMap:e}=n(this,Oi);return t.form||t.canvas&&(e==null?void 0:e.size)>0}}Oi=new WeakMap;var Bi,Un;const dn=class dn{constructor({callback:t,params:e,objs:s,commonObjs:i,annotationCanvasMap:r,operatorList:a,pageIndex:o,canvasFactory:l,filterFactory:h,useRequestAnimationFrame:c=!1,pdfBug:u=!1,pageColors:f=null}){m(this,Bi,null);this.callback=t,this.params=e,this.objs=s,this.commonObjs=i,this.annotationCanvasMap=r,this.operatorListIdx=null,this.operatorList=a,this._pageIndex=o,this.canvasFactory=l,this.filterFactory=h,this._pdfBug=u,this.pageColors=f,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=c===!0&&typeof window<"u",this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new sm(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){var o,l;if(this.cancelled)return;if(this._canvas){if(n(dn,Un).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");n(dn,Un).add(this._canvas)}this._pdfBug&&((o=globalThis.StepperManager)!=null&&o.enabled)&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:s,viewport:i,transform:r,background:a}=this.params;this.gfx=new Dr(s,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:r,viewport:i,transparency:t,background:a}),this.operatorListIdx=0,this.graphicsReady=!0,(l=this.graphicsReadyCallback)==null||l.call(this)}cancel(t=null,e=0){var s,i,r;this.running=!1,this.cancelled=!0,(s=this.gfx)==null||s.endDrawing(),n(this,Bi)&&(window.cancelAnimationFrame(n(this,Bi)),p(this,Bi,null)),n(dn,Un).delete(this._canvas),t||(t=new Bd(`Rendering cancelled, page ${this._pageIndex+1}`,e)),this.callback(t),(r=(i=this.task).onError)==null||r.call(i,t)}operatorListChanged(){var t;if(!this.graphicsReady){this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound);return}(t=this.stepper)==null||t.updateOperatorList(this.operatorList),!this.running&&this._continue()}_continue(){this.running=!0,!this.cancelled&&(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?p(this,Bi,window.requestAnimationFrame(()=>{p(this,Bi,null),this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),n(dn,Un).delete(this._canvas),this.callback())))}};Bi=new WeakMap,Un=new WeakMap,m(dn,Un,new WeakSet);let Zc=dn;const im="5.2.133",nm="4f7761353";function vu(d){return Math.floor(Math.max(0,Math.min(1,d))*255).toString(16).padStart(2,"0")}function qa(d){return Math.max(0,Math.min(255,255*d))}class _u{static CMYK_G([t,e,s,i]){return["G",1-Math.min(1,.3*t+.59*s+.11*e+i)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return t=qa(t),[t,t,t]}static G_HTML([t]){const e=vu(t);return`#${e}${e}${e}`}static RGB_G([t,e,s]){return["G",.3*t+.59*e+.11*s]}static RGB_rgb(t){return t.map(qa)}static RGB_HTML(t){return`#${t.map(vu).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,s,i]){return["RGB",1-Math.min(1,t+i),1-Math.min(1,s+i),1-Math.min(1,e+i)]}static CMYK_rgb([t,e,s,i]){return[qa(1-Math.min(1,t+i)),qa(1-Math.min(1,s+i)),qa(1-Math.min(1,e+i))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,s]){const i=1-t,r=1-e,a=1-s,o=Math.min(i,r,a);return["CMYK",i,r,a,o]}}class rm{create(t,e,s=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const i=this._createSVG("svg:svg");return i.setAttribute("version","1.1"),s||(i.setAttribute("width",`${t}px`),i.setAttribute("height",`${e}px`)),i.setAttribute("preserveAspectRatio","none"),i.setAttribute("viewBox",`0 0 ${t} ${e}`),i}createElement(t){if(typeof t!="string")throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){rt("Abstract method `_createSVG` called.")}}class Lh extends rm{_createSVG(t){return document.createElementNS(Ps,t)}}class kf{static setupStorage(t,e,s,i,r){const a=i.getValue(e,{value:null});switch(s.name){case"textarea":if(a.value!==null&&(t.textContent=a.value),r==="print")break;t.addEventListener("input",o=>{i.setValue(e,{value:o.target.value})});break;case"input":if(s.attributes.type==="radio"||s.attributes.type==="checkbox"){if(a.value===s.attributes.xfaOn?t.setAttribute("checked",!0):a.value===s.attributes.xfaOff&&t.removeAttribute("checked"),r==="print")break;t.addEventListener("change",o=>{i.setValue(e,{value:o.target.checked?o.target.getAttribute("xfaOn"):o.target.getAttribute("xfaOff")})})}else{if(a.value!==null&&t.setAttribute("value",a.value),r==="print")break;t.addEventListener("input",o=>{i.setValue(e,{value:o.target.value})})}break;case"select":if(a.value!==null){t.setAttribute("value",a.value);for(const o of s.children)o.attributes.value===a.value?o.attributes.selected=!0:o.attributes.hasOwnProperty("selected")&&delete o.attributes.selected}t.addEventListener("input",o=>{const l=o.target.options,h=l.selectedIndex===-1?"":l[l.selectedIndex].value;i.setValue(e,{value:h})});break}}static setAttributes({html:t,element:e,storage:s=null,intent:i,linkService:r}){const{attributes:a}=e,o=t instanceof HTMLAnchorElement;a.type==="radio"&&(a.name=`${a.name}-${i}`);for(const[l,h]of Object.entries(a))if(h!=null)switch(l){case"class":h.length&&t.setAttribute(l,h.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",h);break;case"style":Object.assign(t.style,h);break;case"textContent":t.textContent=h;break;default:(!o||l!=="href"&&l!=="newWindow")&&t.setAttribute(l,h)}o&&r.addLinkAttributes(t,a.href,a.newWindow),s&&a.dataId&&this.setupStorage(t,a.dataId,e,s)}static render(t){var u,f;const e=t.annotationStorage,s=t.linkService,i=t.xfaHtml,r=t.intent||"display",a=document.createElement(i.name);i.attributes&&this.setAttributes({html:a,element:i,intent:r,linkService:s});const o=r!=="richText",l=t.div;if(l.append(a),t.viewport){const g=`matrix(${t.viewport.transform.join(",")})`;l.style.transform=g}o&&l.setAttribute("class","xfaLayer xfaFont");const h=[];if(i.children.length===0){if(i.value){const g=document.createTextNode(i.value);a.append(g),o&&po.shouldBuildText(i.name)&&h.push(g)}return{textDivs:h}}const c=[[i,-1,a]];for(;c.length>0;){const[g,y,A]=c.at(-1);if(y+1===g.children.length){c.pop();continue}const w=g.children[++c.at(-1)[1]];if(w===null)continue;const{name:v}=w;if(v==="#text"){const S=document.createTextNode(w.value);h.push(S),A.append(S);continue}const _=(u=w==null?void 0:w.attributes)!=null&&u.xmlns?document.createElementNS(w.attributes.xmlns,v):document.createElement(v);if(A.append(_),w.attributes&&this.setAttributes({html:_,element:w,storage:e,intent:r,linkService:s}),((f=w.children)==null?void 0:f.length)>0)c.push([w,-1,_]);else if(w.value){const S=document.createTextNode(w.value);o&&po.shouldBuildText(v)&&h.push(S),_.append(S)}}for(const g of l.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))g.setAttribute("readOnly",!0);return{textDivs:h}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const Ul=1e3,am=9,Ar=new WeakSet;class Su{static create(t){switch(t.data.annotationType){case _t.LINK:return new Mf(t);case _t.TEXT:return new om(t);case _t.WIDGET:switch(t.data.fieldType){case"Tx":return new lm(t);case"Btn":return t.data.radioButton?new Ff(t):t.data.checkBox?new cm(t):new dm(t);case"Ch":return new um(t);case"Sig":return new hm(t)}return new wr(t);case _t.POPUP:return new ed(t);case _t.FREETEXT:return new $f(t);case _t.LINE:return new pm(t);case _t.SQUARE:return new gm(t);case _t.CIRCLE:return new mm(t);case _t.POLYLINE:return new Gf(t);case _t.CARET:return new Am(t);case _t.INK:return new Wd(t);case _t.POLYGON:return new bm(t);case _t.HIGHLIGHT:return new zf(t);case _t.UNDERLINE:return new ym(t);case _t.SQUIGGLY:return new wm(t);case _t.STRIKEOUT:return new vm(t);case _t.STAMP:return new Uf(t);case _t.FILEATTACHMENT:return new _m(t);default:return new wt(t)}}}var Vn,pa,ga,il,td;const Jd=class Jd{constructor(t,{isRenderable:e=!1,ignoreBorder:s=!1,createQuadrilaterals:i=!1}={}){m(this,il);m(this,Vn,null);m(this,pa,!1);m(this,ga,null);this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(s)),i&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:s}){return!!(t!=null&&t.str||e!=null&&e.str||s!=null&&s.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return Jd._hasPopupData(this.data)}updateEdited(t){var s;if(!this.container)return;n(this,Vn)||p(this,Vn,{rect:this.data.rect.slice(0)});const{rect:e}=t;e&&b(this,il,td).call(this,e),(s=n(this,ga))==null||s.popup.updateEdited(t)}resetEdited(){var t;n(this,Vn)&&(b(this,il,td).call(this,n(this,Vn).rect),(t=n(this,ga))==null||t.popup.resetEdited(),p(this,Vn,null))}_createContainer(t){const{data:e,parent:{page:s,viewport:i}}=this,r=document.createElement("section");r.setAttribute("data-annotation-id",e.id),this instanceof wr||(r.tabIndex=Ul);const{style:a}=r;if(a.zIndex=this.parent.zIndex++,e.alternativeText&&(r.title=e.alternativeText),e.noRotate&&r.classList.add("norotate"),!e.rect||this instanceof ed){const{rotation:A}=e;return!e.hasOwnCanvas&&A!==0&&this.setRotation(A,r),r}const{width:o,height:l}=this;if(!t&&e.borderStyle.width>0){a.borderWidth=`${e.borderStyle.width}px`;const A=e.borderStyle.horizontalCornerRadius,w=e.borderStyle.verticalCornerRadius;if(A>0||w>0){const _=`calc(${A}px * var(--total-scale-factor)) / calc(${w}px * var(--total-scale-factor))`;a.borderRadius=_}else if(this instanceof Ff){const _=`calc(${o}px * var(--total-scale-factor)) / calc(${l}px * var(--total-scale-factor))`;a.borderRadius=_}switch(e.borderStyle.style){case Sr.SOLID:a.borderStyle="solid";break;case Sr.DASHED:a.borderStyle="dashed";break;case Sr.BEVELED:U("Unimplemented border style: beveled");break;case Sr.INSET:U("Unimplemented border style: inset");break;case Sr.UNDERLINE:a.borderBottomStyle="solid";break}const v=e.borderColor||null;v?(p(this,pa,!0),a.borderColor=O.makeHexColor(v[0]|0,v[1]|0,v[2]|0)):a.borderWidth=0}const h=O.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]),{pageWidth:c,pageHeight:u,pageX:f,pageY:g}=i.rawDims;a.left=`${100*(h[0]-f)/c}%`,a.top=`${100*(h[1]-g)/u}%`;const{rotation:y}=e;return e.hasOwnCanvas||y===0?(a.width=`${100*o/c}%`,a.height=`${100*l/u}%`):this.setRotation(y,r),r}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:s,pageHeight:i}=this.parent.viewport.rawDims;let{width:r,height:a}=this;t%180!==0&&([r,a]=[a,r]),e.style.width=`${100*r/s}%`,e.style.height=`${100*a/i}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(e,s,i)=>{const r=i.detail[e],a=r[0],o=r.slice(1);i.target.style[s]=_u[`${a}_HTML`](o),this.annotationStorage.setValue(this.data.id,{[s]:_u[`${a}_rgb`](o)})};return X(this,"_commonActions",{display:e=>{const{display:s}=e.detail,i=s%2===1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:s===1||s===2})},print:e=>{this.annotationStorage.setValue(this.data.id,{noPrint:!e.detail.print})},hidden:e=>{const{hidden:s}=e.detail;this.container.style.visibility=s?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:s,noView:s})},focus:e=>{setTimeout(()=>e.target.focus({preventScroll:!1}),0)},userName:e=>{e.target.title=e.detail.userName},readonly:e=>{e.target.disabled=e.detail.readonly},required:e=>{this._setRequired(e.target,e.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:e=>{const s=e.detail.rotation;this.setRotation(s),this.annotationStorage.setValue(this.data.id,{rotation:s})}})}_dispatchEventFromSandbox(t,e){const s=this._commonActions;for(const i of Object.keys(e.detail)){const r=t[i]||s[i];r==null||r(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const s=this._commonActions;for(const[i,r]of Object.entries(e)){const a=s[i];if(a){const o={detail:{[i]:r},target:t};a(o),delete e[i]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,s,i,r]=this.data.rect.map(A=>Math.fround(A));if(t.length===8){const[A,w,v,_]=t.subarray(2,6);if(i===A&&r===w&&e===v&&s===_)return}const{style:a}=this.container;let o;if(n(this,pa)){const{borderColor:A,borderWidth:w}=a;a.borderWidth=0,o=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${A}" stroke-width="${w}">`],this.container.classList.add("hasBorder")}const l=i-e,h=r-s,{svgFactory:c}=this,u=c.createElement("svg");u.classList.add("quadrilateralsContainer"),u.setAttribute("width",0),u.setAttribute("height",0);const f=c.createElement("defs");u.append(f);const g=c.createElement("clipPath"),y=`clippath_${this.data.id}`;g.setAttribute("id",y),g.setAttribute("clipPathUnits","objectBoundingBox"),f.append(g);for(let A=2,w=t.length;A<w;A+=8){const v=t[A],_=t[A+1],S=t[A+2],E=t[A+3],C=c.createElement("rect"),x=(S-e)/l,T=(r-_)/h,P=(v-S)/l,k=(_-E)/h;C.setAttribute("x",x),C.setAttribute("y",T),C.setAttribute("width",P),C.setAttribute("height",k),g.append(C),o==null||o.push(`<rect vector-effect="non-scaling-stroke" x="${x}" y="${T}" width="${P}" height="${k}"/>`)}n(this,pa)&&(o.push("</g></svg>')"),a.backgroundImage=o.join("")),this.container.append(u),this.container.style.clipPath=`url(#${y})`}_createPopup(){const{data:t}=this,e=p(this,ga,new ed({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation},parent:this.parent,elements:[this]}));this.parent.div.append(e.render())}render(){rt("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const s=[];if(this._fieldObjects){const i=this._fieldObjects[t];if(i)for(const{page:r,id:a,exportValues:o}of i){if(r===-1||a===e)continue;const l=typeof o=="string"?o:null,h=document.querySelector(`[data-element-id="${a}"]`);if(h&&!Ar.has(h)){U(`_getElementsByName - element not allowed: ${a}`);continue}s.push({id:a,exportValue:l,domElement:h})}return s}for(const i of document.getElementsByName(t)){const{exportValue:r}=i,a=i.getAttribute("data-element-id");a!==e&&Ar.has(i)&&s.push({id:a,exportValue:r,domElement:i})}return s}show(){var t;this.container&&(this.container.hidden=!1),(t=this.popup)==null||t.maybeShow()}hide(){var t;this.container&&(this.container.hidden=!0),(t=this.popup)==null||t.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{var s;(s=this.linkService.eventBus)==null||s.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})})}get width(){return this.data.rect[2]-this.data.rect[0]}get height(){return this.data.rect[3]-this.data.rect[1]}};Vn=new WeakMap,pa=new WeakMap,ga=new WeakMap,il=new WeakSet,td=function(t){const{container:{style:e},data:{rect:s,rotation:i},parent:{viewport:{rawDims:{pageWidth:r,pageHeight:a,pageX:o,pageY:l}}}}=this;s==null||s.splice(0,4,...t),e.left=`${100*(t[0]-o)/r}%`,e.top=`${100*(a-t[3]+l)/a}%`,i===0?(e.width=`${100*(t[2]-t[0])/r}%`,e.height=`${100*(t[3]-t[1])/a}%`):this.setRotation(i)};let wt=Jd;var ke,on,Lf,Df;class Mf extends wt{constructor(e,s=null){super(e,{isRenderable:!0,ignoreBorder:!!(s!=null&&s.ignoreBorder),createQuadrilaterals:!0});m(this,ke);this.isTooltipOnly=e.data.isTooltipOnly}render(){const{data:e,linkService:s}=this,i=document.createElement("a");i.setAttribute("data-element-id",e.id);let r=!1;return e.url?(s.addLinkAttributes(i,e.url,e.newWindow),r=!0):e.action?(this._bindNamedAction(i,e.action),r=!0):e.attachment?(b(this,ke,Lf).call(this,i,e.attachment,e.attachmentDest),r=!0):e.setOCGState?(b(this,ke,Df).call(this,i,e.setOCGState),r=!0):e.dest?(this._bindLink(i,e.dest),r=!0):(e.actions&&(e.actions.Action||e.actions["Mouse Up"]||e.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,e),r=!0),e.resetForm?(this._bindResetFormAction(i,e.resetForm),r=!0):this.isTooltipOnly&&!r&&(this._bindLink(i,""),r=!0)),this.container.classList.add("linkAnnotation"),r&&this.container.append(i),this.container}_bindLink(e,s){e.href=this.linkService.getDestinationHash(s),e.onclick=()=>(s&&this.linkService.goToDestination(s),!1),(s||s==="")&&b(this,ke,on).call(this)}_bindNamedAction(e,s){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeNamedAction(s),!1),b(this,ke,on).call(this)}_bindJSAction(e,s){e.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const r of Object.keys(s.actions)){const a=i.get(r);a&&(e[a]=()=>{var o;return(o=this.linkService.eventBus)==null||o.dispatch("dispatcheventinsandbox",{source:this,detail:{id:s.id,name:r}}),!1})}e.onclick||(e.onclick=()=>!1),b(this,ke,on).call(this)}_bindResetFormAction(e,s){const i=e.onclick;if(i||(e.href=this.linkService.getAnchorUrl("")),b(this,ke,on).call(this),!this._fieldObjects){U('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),i||(e.onclick=()=>!1);return}e.onclick=()=>{var u;i==null||i();const{fields:r,refs:a,include:o}=s,l=[];if(r.length!==0||a.length!==0){const f=new Set(a);for(const g of r){const y=this._fieldObjects[g]||[];for(const{id:A}of y)f.add(A)}for(const g of Object.values(this._fieldObjects))for(const y of g)f.has(y.id)===o&&l.push(y)}else for(const f of Object.values(this._fieldObjects))l.push(...f);const h=this.annotationStorage,c=[];for(const f of l){const{id:g}=f;switch(c.push(g),f.type){case"text":{const A=f.defaultValue||"";h.setValue(g,{value:A});break}case"checkbox":case"radiobutton":{const A=f.defaultValue===f.exportValues;h.setValue(g,{value:A});break}case"combobox":case"listbox":{const A=f.defaultValue||"";h.setValue(g,{value:A});break}default:continue}const y=document.querySelector(`[data-element-id="${g}"]`);if(y){if(!Ar.has(y)){U(`_bindResetFormAction - element not allowed: ${g}`);continue}}else continue;y.dispatchEvent(new Event("resetform"))}return this.enableScripting&&((u=this.linkService.eventBus)==null||u.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:c,name:"ResetForm"}})),!1}}}ke=new WeakSet,on=function(){this.container.setAttribute("data-internal-link","")},Lf=function(e,s,i=null){e.href=this.linkService.getAnchorUrl(""),s.description&&(e.title=s.description),e.onclick=()=>{var r;return(r=this.downloadManager)==null||r.openOrDownloadData(s.content,s.filename,i),!1},b(this,ke,on).call(this)},Df=function(e,s){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeSetOCGState(s),!1),b(this,ke,on).call(this)};class om extends wt{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class wr extends wt{render(){return this.container}showElementAndHideCanvas(t){var e;this.data.hasOwnCanvas&&(((e=t.previousSibling)==null?void 0:e.nodeName)==="CANVAS"&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return Wt.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,s,i,r){s.includes("mouse")?t.addEventListener(s,a=>{var o;(o=this.linkService.eventBus)==null||o.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:r(a),shift:a.shiftKey,modifier:this._getKeyModifier(a)}})}):t.addEventListener(s,a=>{var o;if(s==="blur"){if(!e.focused||!a.relatedTarget)return;e.focused=!1}else if(s==="focus"){if(e.focused)return;e.focused=!0}r&&((o=this.linkService.eventBus)==null||o.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:r(a)}}))})}_setEventListeners(t,e,s,i){var r,a,o;for(const[l,h]of s)(h==="Action"||(r=this.data.actions)!=null&&r[h])&&((h==="Focus"||h==="Blur")&&(e||(e={focused:!1})),this._setEventListener(t,e,l,h,i),h==="Focus"&&!((a=this.data.actions)!=null&&a.Blur)?this._setEventListener(t,e,"blur","Blur",null):h==="Blur"&&!((o=this.data.actions)!=null&&o.Focus)&&this._setEventListener(t,e,"focus","Focus",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=e===null?"transparent":O.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,i=this.data.defaultAppearanceData.fontSize||am,r=t.style;let a;const o=2,l=h=>Math.round(10*h)/10;if(this.data.multiLine){const h=Math.abs(this.data.rect[3]-this.data.rect[1]-o),c=Math.round(h/(dc*i))||1,u=h/c;a=Math.min(i,l(u/dc))}else{const h=Math.abs(this.data.rect[3]-this.data.rect[1]-o);a=Math.min(i,l(h/dc))}r.fontSize=`calc(${a}px * var(--total-scale-factor))`,r.color=O.makeHexColor(s[0],s[1],s[2]),this.data.textAlignment!==null&&(r.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class lm extends wr{constructor(t){const e=t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue;super(t,{isRenderable:e})}setPropertyOnSiblings(t,e,s,i){const r=this.annotationStorage;for(const a of this._getElementsByName(t.name,t.id))a.domElement&&(a.domElement[e]=s),r.setValue(a.id,{[i]:s})}render(){var i,r;const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let s=null;if(this.renderForms){const a=t.getValue(e,{value:this.data.fieldValue});let o=a.value||"";const l=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;l&&o.length>l&&(o=o.slice(0,l));let h=a.formattedValue||((i=this.data.textContent)==null?void 0:i.join(`
`))||null;h&&this.data.comb&&(h=h.replaceAll(/\s+/g,""));const c={userValue:o,formattedValue:h,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(s=document.createElement("textarea"),s.textContent=h??o,this.data.doNotScroll&&(s.style.overflowY="hidden")):(s=document.createElement("input"),s.type=this.data.password?"password":"text",s.setAttribute("value",h??o),this.data.doNotScroll&&(s.style.overflowX="hidden")),this.data.hasOwnCanvas&&(s.hidden=!0),Ar.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,s.name=this.data.fieldName,s.tabIndex=Ul,this._setRequired(s,this.data.required),l&&(s.maxLength=l),s.addEventListener("input",f=>{t.setValue(e,{value:f.target.value}),this.setPropertyOnSiblings(s,"value",f.target.value,"value"),c.formattedValue=null}),s.addEventListener("resetform",f=>{const g=this.data.defaultFieldValue??"";s.value=c.userValue=g,c.formattedValue=null});let u=f=>{const{formattedValue:g}=c;g!=null&&(f.target.value=g),f.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){s.addEventListener("focus",g=>{var A;if(c.focused)return;const{target:y}=g;c.userValue&&(y.value=c.userValue),c.lastCommittedValue=y.value,c.commitKey=1,(A=this.data.actions)!=null&&A.Focus||(c.focused=!0)}),s.addEventListener("updatefromsandbox",g=>{this.showElementAndHideCanvas(g.target);const y={value(A){c.userValue=A.detail.value??"",t.setValue(e,{value:c.userValue.toString()}),A.target.value=c.userValue},formattedValue(A){const{formattedValue:w}=A.detail;c.formattedValue=w,w!=null&&A.target!==document.activeElement&&(A.target.value=w),t.setValue(e,{formattedValue:w})},selRange(A){A.target.setSelectionRange(...A.detail.selRange)},charLimit:A=>{var S;const{charLimit:w}=A.detail,{target:v}=A;if(w===0){v.removeAttribute("maxLength");return}v.setAttribute("maxLength",w);let _=c.userValue;!_||_.length<=w||(_=_.slice(0,w),v.value=c.userValue=_,t.setValue(e,{value:_}),(S=this.linkService.eventBus)==null||S.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:_,willCommit:!0,commitKey:1,selStart:v.selectionStart,selEnd:v.selectionEnd}}))}};this._dispatchEventFromSandbox(y,g)}),s.addEventListener("keydown",g=>{var w;c.commitKey=1;let y=-1;if(g.key==="Escape"?y=0:g.key==="Enter"&&!this.data.multiLine?y=2:g.key==="Tab"&&(c.commitKey=3),y===-1)return;const{value:A}=g.target;c.lastCommittedValue!==A&&(c.lastCommittedValue=A,c.userValue=A,(w=this.linkService.eventBus)==null||w.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:A,willCommit:!0,commitKey:y,selStart:g.target.selectionStart,selEnd:g.target.selectionEnd}}))});const f=u;u=null,s.addEventListener("blur",g=>{var A,w;if(!c.focused||!g.relatedTarget)return;(A=this.data.actions)!=null&&A.Blur||(c.focused=!1);const{value:y}=g.target;c.userValue=y,c.lastCommittedValue!==y&&((w=this.linkService.eventBus)==null||w.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:y,willCommit:!0,commitKey:c.commitKey,selStart:g.target.selectionStart,selEnd:g.target.selectionEnd}})),f(g)}),(r=this.data.actions)!=null&&r.Keystroke&&s.addEventListener("beforeinput",g=>{var C;c.lastCommittedValue=null;const{data:y,target:A}=g,{value:w,selectionStart:v,selectionEnd:_}=A;let S=v,E=_;switch(g.inputType){case"deleteWordBackward":{const x=w.substring(0,v).match(/\w*[^\w]*$/);x&&(S-=x[0].length);break}case"deleteWordForward":{const x=w.substring(v).match(/^[^\w]*\w*/);x&&(E+=x[0].length);break}case"deleteContentBackward":v===_&&(S-=1);break;case"deleteContentForward":v===_&&(E+=1);break}g.preventDefault(),(C=this.linkService.eventBus)==null||C.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:w,change:y||"",willCommit:!1,selStart:S,selEnd:E}})}),this._setEventListeners(s,c,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],g=>g.target.value)}if(u&&s.addEventListener("blur",u),this.data.comb){const g=(this.data.rect[2]-this.data.rect[0])/l;s.classList.add("comb"),s.style.letterSpacing=`calc(${g}px * var(--total-scale-factor) - 1ch)`}}else s=document.createElement("div"),s.textContent=this.data.fieldValue,s.style.verticalAlign="middle",s.style.display="table-cell",this.data.hasOwnCanvas&&(s.hidden=!0);return this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class hm extends wr{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class cm extends wr{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.exportValue===e.fieldValue}).value;typeof i=="string"&&(i=i!=="Off",t.setValue(s,{value:i})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const r=document.createElement("input");return Ar.add(r),r.setAttribute("data-element-id",s),r.disabled=e.readOnly,this._setRequired(r,this.data.required),r.type="checkbox",r.name=e.fieldName,i&&r.setAttribute("checked",!0),r.setAttribute("exportValue",e.exportValue),r.tabIndex=Ul,r.addEventListener("change",a=>{const{name:o,checked:l}=a.target;for(const h of this._getElementsByName(o,s)){const c=l&&h.exportValue===e.exportValue;h.domElement&&(h.domElement.checked=c),t.setValue(h.id,{value:c})}t.setValue(s,{value:l})}),r.addEventListener("resetform",a=>{const o=e.defaultFieldValue||"Off";a.target.checked=o===e.exportValue}),this.enableScripting&&this.hasJSActions&&(r.addEventListener("updatefromsandbox",a=>{const o={value(l){l.target.checked=l.detail.value!=="Off",t.setValue(s,{value:l.target.checked})}};this._dispatchEventFromSandbox(o,a)}),this._setEventListeners(r,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],a=>a.target.checked)),this._setBackgroundColor(r),this._setDefaultPropertiesFromJS(r),this.container.append(r),this.container}}class Ff extends wr{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.fieldValue===e.buttonValue}).value;if(typeof i=="string"&&(i=i!==e.buttonValue,t.setValue(s,{value:i})),i)for(const a of this._getElementsByName(e.fieldName,s))t.setValue(a.id,{value:!1});const r=document.createElement("input");if(Ar.add(r),r.setAttribute("data-element-id",s),r.disabled=e.readOnly,this._setRequired(r,this.data.required),r.type="radio",r.name=e.fieldName,i&&r.setAttribute("checked",!0),r.tabIndex=Ul,r.addEventListener("change",a=>{const{name:o,checked:l}=a.target;for(const h of this._getElementsByName(o,s))t.setValue(h.id,{value:!1});t.setValue(s,{value:l})}),r.addEventListener("resetform",a=>{const o=e.defaultFieldValue;a.target.checked=o!=null&&o===e.buttonValue}),this.enableScripting&&this.hasJSActions){const a=e.buttonValue;r.addEventListener("updatefromsandbox",o=>{const l={value:h=>{const c=a===h.detail.value;for(const u of this._getElementsByName(h.target.name)){const f=c&&u.id===s;u.domElement&&(u.domElement.checked=f),t.setValue(u.id,{value:f})}}};this._dispatchEventFromSandbox(l,o)}),this._setEventListeners(r,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],o=>o.target.checked)}return this._setBackgroundColor(r),this._setDefaultPropertiesFromJS(r),this.container.append(r),this.container}}class dm extends Mf{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",s=>{this._dispatchEventFromSandbox({},s)})),t}}class um extends wr{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,s=t.getValue(e,{value:this.data.fieldValue}),i=document.createElement("select");Ar.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,this._setRequired(i,this.data.required),i.name=this.data.fieldName,i.tabIndex=Ul;let r=this.data.combo&&this.data.options.length>0;this.data.combo||(i.size=this.data.options.length,this.data.multiSelect&&(i.multiple=!0)),i.addEventListener("resetform",c=>{const u=this.data.defaultFieldValue;for(const f of i.options)f.selected=f.value===u});for(const c of this.data.options){const u=document.createElement("option");u.textContent=c.displayValue,u.value=c.exportValue,s.value.includes(c.exportValue)&&(u.setAttribute("selected",!0),r=!1),i.append(u)}let a=null;if(r){const c=document.createElement("option");c.value=" ",c.setAttribute("hidden",!0),c.setAttribute("selected",!0),i.prepend(c),a=()=>{c.remove(),i.removeEventListener("input",a),a=null},i.addEventListener("input",a)}const o=c=>{const u=c?"value":"textContent",{options:f,multiple:g}=i;return g?Array.prototype.filter.call(f,y=>y.selected).map(y=>y[u]):f.selectedIndex===-1?null:f[f.selectedIndex][u]};let l=o(!1);const h=c=>{const u=c.target.options;return Array.prototype.map.call(u,f=>({displayValue:f.textContent,exportValue:f.value}))};return this.enableScripting&&this.hasJSActions?(i.addEventListener("updatefromsandbox",c=>{const u={value(f){a==null||a();const g=f.detail.value,y=new Set(Array.isArray(g)?g:[g]);for(const A of i.options)A.selected=y.has(A.value);t.setValue(e,{value:o(!0)}),l=o(!1)},multipleSelection(f){i.multiple=!0},remove(f){const g=i.options,y=f.detail.remove;g[y].selected=!1,i.remove(y),g.length>0&&Array.prototype.findIndex.call(g,w=>w.selected)===-1&&(g[0].selected=!0),t.setValue(e,{value:o(!0),items:h(f)}),l=o(!1)},clear(f){for(;i.length!==0;)i.remove(0);t.setValue(e,{value:null,items:[]}),l=o(!1)},insert(f){const{index:g,displayValue:y,exportValue:A}=f.detail.insert,w=i.children[g],v=document.createElement("option");v.textContent=y,v.value=A,w?w.before(v):i.append(v),t.setValue(e,{value:o(!0),items:h(f)}),l=o(!1)},items(f){const{items:g}=f.detail;for(;i.length!==0;)i.remove(0);for(const y of g){const{displayValue:A,exportValue:w}=y,v=document.createElement("option");v.textContent=A,v.value=w,i.append(v)}i.options.length>0&&(i.options[0].selected=!0),t.setValue(e,{value:o(!0),items:h(f)}),l=o(!1)},indices(f){const g=new Set(f.detail.indices);for(const y of f.target.options)y.selected=g.has(y.index);t.setValue(e,{value:o(!0)}),l=o(!1)},editable(f){f.target.disabled=!f.detail.editable}};this._dispatchEventFromSandbox(u,c)}),i.addEventListener("input",c=>{var g;const u=o(!0),f=o(!1);t.setValue(e,{value:u}),c.preventDefault(),(g=this.linkService.eventBus)==null||g.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:l,change:f,changeEx:u,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(i,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],c=>c.target.value)):i.addEventListener("input",function(c){t.setValue(e,{value:o(!0)})}),this.data.combo&&this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class ed extends wt{constructor(t){const{data:e,elements:s}=t;super(t,{isRenderable:wt._hasPopupData(e)}),this.elements=s,this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new fm({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const s of this.elements)s.popup=t,s.container.ariaHasPopup="dialog",e.push(s.data.id),s.addHighlightArea();return this.container.setAttribute("aria-controls",e.map(s=>`${Od}${s}`).join(",")),this.container}}var ma,Kh,Qh,ba,Aa,mt,Ks,ya,nl,rl,wa,Qs,Ye,Js,al,Zs,ol,jn,Wn,at,uh,sd,Nf,Of,Bf,Hf,fh,ph,id;class fm{constructor({container:t,color:e,elements:s,titleObj:i,modificationDate:r,contentsObj:a,richText:o,parent:l,rect:h,parentRect:c,open:u}){m(this,at);m(this,ma,b(this,at,Bf).bind(this));m(this,Kh,b(this,at,id).bind(this));m(this,Qh,b(this,at,ph).bind(this));m(this,ba,b(this,at,fh).bind(this));m(this,Aa,null);m(this,mt,null);m(this,Ks,null);m(this,ya,null);m(this,nl,null);m(this,rl,null);m(this,wa,null);m(this,Qs,!1);m(this,Ye,null);m(this,Js,null);m(this,al,null);m(this,Zs,null);m(this,ol,null);m(this,jn,null);m(this,Wn,!1);var f;p(this,mt,t),p(this,ol,i),p(this,Ks,a),p(this,Zs,o),p(this,rl,l),p(this,Aa,e),p(this,al,h),p(this,wa,c),p(this,nl,s),p(this,ya,$d.toDateObject(r)),this.trigger=s.flatMap(g=>g.getElementsToTriggerPopup());for(const g of this.trigger)g.addEventListener("click",n(this,ba)),g.addEventListener("mouseenter",n(this,Qh)),g.addEventListener("mouseleave",n(this,Kh)),g.classList.add("popupTriggerArea");for(const g of s)(f=g.container)==null||f.addEventListener("keydown",n(this,ma));n(this,mt).hidden=!0,u&&b(this,at,fh).call(this)}render(){if(n(this,Ye))return;const t=p(this,Ye,document.createElement("div"));if(t.className="popup",n(this,Aa)){const r=t.style.outlineColor=O.makeHexColor(...n(this,Aa));t.style.backgroundColor=`color-mix(in srgb, ${r} 30%, white)`}const e=document.createElement("span");e.className="header";const s=document.createElement("h1");if(e.append(s),{dir:s.dir,str:s.textContent}=n(this,ol),t.append(e),n(this,ya)){const r=document.createElement("span");r.classList.add("popupDate"),r.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string"),r.setAttribute("data-l10n-args",JSON.stringify({dateObj:n(this,ya).valueOf()})),e.append(r)}const i=n(this,at,uh);if(i)kf.render({xfaHtml:i,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const r=this._formatContents(n(this,Ks));t.append(r)}n(this,mt).append(t)}_formatContents({str:t,dir:e}){const s=document.createElement("p");s.classList.add("popupContent"),s.dir=e;const i=t.split(/(?:\r\n?|\n)/);for(let r=0,a=i.length;r<a;++r){const o=i[r];s.append(document.createTextNode(o)),r<a-1&&s.append(document.createElement("br"))}return s}updateEdited({rect:t,popupContent:e}){var s;n(this,jn)||p(this,jn,{contentsObj:n(this,Ks),richText:n(this,Zs)}),t&&p(this,Js,null),e&&(p(this,Zs,b(this,at,Of).call(this,e)),p(this,Ks,null)),(s=n(this,Ye))==null||s.remove(),p(this,Ye,null)}resetEdited(){var t;n(this,jn)&&({contentsObj:te(this,Ks)._,richText:te(this,Zs)._}=n(this,jn),p(this,jn,null),(t=n(this,Ye))==null||t.remove(),p(this,Ye,null),p(this,Js,null))}forceHide(){p(this,Wn,this.isVisible),n(this,Wn)&&(n(this,mt).hidden=!0)}maybeShow(){n(this,Wn)&&(n(this,Ye)||b(this,at,ph).call(this),p(this,Wn,!1),n(this,mt).hidden=!1)}get isVisible(){return n(this,mt).hidden===!1}}ma=new WeakMap,Kh=new WeakMap,Qh=new WeakMap,ba=new WeakMap,Aa=new WeakMap,mt=new WeakMap,Ks=new WeakMap,ya=new WeakMap,nl=new WeakMap,rl=new WeakMap,wa=new WeakMap,Qs=new WeakMap,Ye=new WeakMap,Js=new WeakMap,al=new WeakMap,Zs=new WeakMap,ol=new WeakMap,jn=new WeakMap,Wn=new WeakMap,at=new WeakSet,uh=function(){const t=n(this,Zs),e=n(this,Ks);return t!=null&&t.str&&(!(e!=null&&e.str)||e.str===t.str)&&n(this,Zs).html||null},sd=function(){var t,e,s;return((s=(e=(t=n(this,at,uh))==null?void 0:t.attributes)==null?void 0:e.style)==null?void 0:s.fontSize)||0},Nf=function(){var t,e,s;return((s=(e=(t=n(this,at,uh))==null?void 0:t.attributes)==null?void 0:e.style)==null?void 0:s.color)||null},Of=function(t){const e=[],s={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},i={style:{color:n(this,at,Nf),fontSize:n(this,at,sd)?`calc(${n(this,at,sd)}px * var(--total-scale-factor))`:""}};for(const r of t.split(`
`))e.push({name:"span",value:r,attributes:i});return s},Bf=function(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||(t.key==="Enter"||t.key==="Escape"&&n(this,Qs))&&b(this,at,fh).call(this)},Hf=function(){if(n(this,Js)!==null)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:s,pageX:i,pageY:r}}}=n(this,rl);let a=!!n(this,wa),o=a?n(this,wa):n(this,al);for(const y of n(this,nl))if(!o||O.intersect(y.data.rect,o)!==null){o=y.data.rect,a=!0;break}const l=O.normalizeRect([o[0],t[3]-o[1]+t[1],o[2],t[3]-o[3]+t[1]]),c=a?o[2]-o[0]+5:0,u=l[0]+c,f=l[1];p(this,Js,[100*(u-i)/e,100*(f-r)/s]);const{style:g}=n(this,mt);g.left=`${n(this,Js)[0]}%`,g.top=`${n(this,Js)[1]}%`},fh=function(){p(this,Qs,!n(this,Qs)),n(this,Qs)?(b(this,at,ph).call(this),n(this,mt).addEventListener("click",n(this,ba)),n(this,mt).addEventListener("keydown",n(this,ma))):(b(this,at,id).call(this),n(this,mt).removeEventListener("click",n(this,ba)),n(this,mt).removeEventListener("keydown",n(this,ma)))},ph=function(){n(this,Ye)||this.render(),this.isVisible?n(this,Qs)&&n(this,mt).classList.add("focused"):(b(this,at,Hf).call(this),n(this,mt).hidden=!1,n(this,mt).style.zIndex=parseInt(n(this,mt).style.zIndex)+1e3)},id=function(){n(this,mt).classList.remove("focused"),!(n(this,Qs)||!this.isVisible)&&(n(this,mt).hidden=!0,n(this,mt).style.zIndex=parseInt(n(this,mt).style.zIndex)-1e3)};class $f extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=z.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const s=document.createElement("span");s.textContent=e,t.append(s)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}var ll;class pm extends wt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});m(this,ll,null)}render(){this.container.classList.add("lineAnnotation");const{data:e,width:s,height:i}=this,r=this.svgFactory.create(s,i,!0),a=p(this,ll,this.svgFactory.createElement("svg:line"));return a.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),a.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),a.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),a.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),a.setAttribute("stroke-width",e.borderStyle.width||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),r.append(a),this.container.append(r),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,ll)}addHighlightArea(){this.container.classList.add("highlightArea")}}ll=new WeakMap;var hl;class gm extends wt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});m(this,hl,null)}render(){this.container.classList.add("squareAnnotation");const{data:e,width:s,height:i}=this,r=this.svgFactory.create(s,i,!0),a=e.borderStyle.width,o=p(this,hl,this.svgFactory.createElement("svg:rect"));return o.setAttribute("x",a/2),o.setAttribute("y",a/2),o.setAttribute("width",s-a),o.setAttribute("height",i-a),o.setAttribute("stroke-width",a||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),r.append(o),this.container.append(r),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,hl)}addHighlightArea(){this.container.classList.add("highlightArea")}}hl=new WeakMap;var cl;class mm extends wt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});m(this,cl,null)}render(){this.container.classList.add("circleAnnotation");const{data:e,width:s,height:i}=this,r=this.svgFactory.create(s,i,!0),a=e.borderStyle.width,o=p(this,cl,this.svgFactory.createElement("svg:ellipse"));return o.setAttribute("cx",s/2),o.setAttribute("cy",i/2),o.setAttribute("rx",s/2-a/2),o.setAttribute("ry",i/2-a/2),o.setAttribute("stroke-width",a||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),r.append(o),this.container.append(r),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,cl)}addHighlightArea(){this.container.classList.add("highlightArea")}}cl=new WeakMap;var dl;class Gf extends wt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});m(this,dl,null);this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:e,vertices:s,borderStyle:i,popupRef:r},width:a,height:o}=this;if(!s)return this.container;const l=this.svgFactory.create(a,o,!0);let h=[];for(let u=0,f=s.length;u<f;u+=2){const g=s[u]-e[0],y=e[3]-s[u+1];h.push(`${g},${y}`)}h=h.join(" ");const c=p(this,dl,this.svgFactory.createElement(this.svgElementName));return c.setAttribute("points",h),c.setAttribute("stroke-width",i.width||1),c.setAttribute("stroke","transparent"),c.setAttribute("fill","transparent"),l.append(c),this.container.append(l),!r&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,dl)}addHighlightArea(){this.container.classList.add("highlightArea")}}dl=new WeakMap;class bm extends Gf{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class Am extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}var ul,qn,fl,nd;class Wd extends wt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});m(this,fl);m(this,ul,null);m(this,qn,[]);this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=this.data.it==="InkHighlight"?z.HIGHLIGHT:z.INK}render(){this.container.classList.add(this.containerClassName);const{data:{rect:e,rotation:s,inkLists:i,borderStyle:r,popupRef:a}}=this,{transform:o,width:l,height:h}=b(this,fl,nd).call(this,s,e),c=this.svgFactory.create(l,h,!0),u=p(this,ul,this.svgFactory.createElement("svg:g"));c.append(u),u.setAttribute("stroke-width",r.width||1),u.setAttribute("stroke-linecap","round"),u.setAttribute("stroke-linejoin","round"),u.setAttribute("stroke-miterlimit",10),u.setAttribute("stroke","transparent"),u.setAttribute("fill","transparent"),u.setAttribute("transform",o);for(let f=0,g=i.length;f<g;f++){const y=this.svgFactory.createElement(this.svgElementName);n(this,qn).push(y),y.setAttribute("points",i[f].join(",")),u.append(y)}return!a&&this.hasPopupData&&this._createPopup(),this.container.append(c),this._editOnDoubleClick(),this.container}updateEdited(e){super.updateEdited(e);const{thickness:s,points:i,rect:r}=e,a=n(this,ul);if(s>=0&&a.setAttribute("stroke-width",s||1),i)for(let o=0,l=n(this,qn).length;o<l;o++)n(this,qn)[o].setAttribute("points",i[o].join(","));if(r){const{transform:o,width:l,height:h}=b(this,fl,nd).call(this,this.data.rotation,r);a.parentElement.setAttribute("viewBox",`0 0 ${l} ${h}`),a.setAttribute("transform",o)}}getElementsToTriggerPopup(){return n(this,qn)}addHighlightArea(){this.container.classList.add("highlightArea")}}ul=new WeakMap,qn=new WeakMap,fl=new WeakSet,nd=function(e,s){switch(e){case 90:return{transform:`rotate(90) translate(${-s[0]},${s[1]}) scale(1,-1)`,width:s[3]-s[1],height:s[2]-s[0]};case 180:return{transform:`rotate(180) translate(${-s[2]},${s[1]}) scale(1,-1)`,width:s[2]-s[0],height:s[3]-s[1]};case 270:return{transform:`rotate(270) translate(${-s[2]},${s[3]}) scale(1,-1)`,width:s[3]-s[1],height:s[2]-s[0]};default:return{transform:`translate(${-s[0]},${s[3]}) scale(1,-1)`,width:s[2]-s[0],height:s[3]-s[1]}}};class zf extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}),this.annotationEditorType=z.HIGHLIGHT}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this._editOnDoubleClick(),this.container}}class ym extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class wm extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class vm extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class Uf extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.annotationEditorType=z.STAMP}render(){return this.container.classList.add("stampAnnotation"),this.container.setAttribute("role","img"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}var pl,gl,rd;class _m extends wt{constructor(e){var i;super(e,{isRenderable:!0});m(this,gl);m(this,pl,null);const{file:s}=this.data;this.filename=s.filename,this.content=s.content,(i=this.linkService.eventBus)==null||i.dispatch("fileattachmentannotation",{source:this,...s})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:e,data:s}=this;let i;s.hasAppearance||s.fillAlpha===0?i=document.createElement("div"):(i=document.createElement("img"),i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(s.name)?"paperclip":"pushpin"}.svg`,s.fillAlpha&&s.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(s.fillAlpha*100)}%);`)),i.addEventListener("dblclick",b(this,gl,rd).bind(this)),p(this,pl,i);const{isMac:r}=Wt.platform;return e.addEventListener("keydown",a=>{a.key==="Enter"&&(r?a.metaKey:a.ctrlKey)&&b(this,gl,rd).call(this)}),!s.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea"),e.append(i),e}getElementsToTriggerPopup(){return n(this,pl)}addHighlightArea(){this.container.classList.add("highlightArea")}}pl=new WeakMap,gl=new WeakSet,rd=function(){var e;(e=this.downloadManager)==null||e.openOrDownloadData(this.content,this.filename)};var ml,Xn,Hi,bl,rn,od,ld;const Zd=class Zd{constructor({div:t,accessibilityManager:e,annotationCanvasMap:s,annotationEditorUIManager:i,page:r,viewport:a,structTreeLayer:o}){m(this,rn);m(this,ml,null);m(this,Xn,null);m(this,Hi,new Map);m(this,bl,null);this.div=t,p(this,ml,e),p(this,Xn,s),p(this,bl,o||null),this.page=r,this.viewport=a,this.zIndex=0,this._annotationEditorUIManager=i}hasEditableAnnotations(){return n(this,Hi).size>0}async render(t){var a;const{annotations:e}=t,s=this.div;mr(s,this.viewport);const i=new Map,r={data:null,layer:s,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:t.renderForms!==!1,svgFactory:new Lh,annotationStorage:t.annotationStorage||new Ud,enableScripting:t.enableScripting===!0,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const o of e){if(o.noHTML)continue;const l=o.annotationType===_t.POPUP;if(l){const u=i.get(o.id);if(!u)continue;r.elements=u}else if(o.rect[2]===o.rect[0]||o.rect[3]===o.rect[1])continue;r.data=o;const h=Su.create(r);if(!h.isRenderable)continue;if(!l&&o.popupRef){const u=i.get(o.popupRef);u?u.push(h):i.set(o.popupRef,[h])}const c=h.render();o.hidden&&(c.style.visibility="hidden"),await b(this,rn,od).call(this,c,o.id),h._isEditable&&(n(this,Hi).set(h.data.id,h),(a=this._annotationEditorUIManager)==null||a.renderAnnotationElement(h))}b(this,rn,ld).call(this)}async addLinkAnnotations(t,e){const s={data:null,layer:this.div,linkService:e,svgFactory:new Lh,parent:this};for(const i of t){i.borderStyle||(i.borderStyle=Zd._defaultBorderStyle),s.data=i;const r=Su.create(s);if(!r.isRenderable)continue;const a=r.render();await b(this,rn,od).call(this,a,i.id)}}update({viewport:t}){const e=this.div;this.viewport=t,mr(e,{rotation:t.rotation}),b(this,rn,ld).call(this),e.hidden=!1}getEditableAnnotations(){return Array.from(n(this,Hi).values())}getEditableAnnotation(t){return n(this,Hi).get(t)}static get _defaultBorderStyle(){return X(this,"_defaultBorderStyle",Object.freeze({width:1,rawWidth:1,style:Sr.SOLID,dashArray:[3],horizontalCornerRadius:0,verticalCornerRadius:0}))}};ml=new WeakMap,Xn=new WeakMap,Hi=new WeakMap,bl=new WeakMap,rn=new WeakSet,od=async function(t,e){var a,o;const s=t.firstChild||t,i=s.id=`${Od}${e}`,r=await((a=n(this,bl))==null?void 0:a.getAriaAttributes(i));if(r)for(const[l,h]of r)s.setAttribute(l,h);this.div.append(t),(o=n(this,ml))==null||o.moveElementInDOM(this.div,t,s,!1)},ld=function(){var e;if(!n(this,Xn))return;const t=this.div;for(const[s,i]of n(this,Xn)){const r=t.querySelector(`[data-annotation-id="${s}"]`);if(!r)continue;i.className="annotationContent";const{firstChild:a}=r;a?a.nodeName==="CANVAS"?a.replaceWith(i):a.classList.contains("annotationContent")?a.after(i):a.before(i):r.append(i);const o=n(this,Hi).get(s);o&&(o._hasNoCanvas?((e=this._annotationEditorUIManager)==null||e.setMissingCanvas(s,r.id,i),o._hasNoCanvas=!1):o.canvas=i)}n(this,Xn).clear()};let ad=Zd;const Kl=/\r\n?|\n/g;var Ke,we,Al,Yn,ve,Ct,Vf,jf,Wf,gh,di,mh,bh,qf,cd,Xf;const ot=class ot extends lt{constructor(e){super({...e,name:"freeTextEditor"});m(this,Ct);m(this,Ke);m(this,we,"");m(this,Al,`${this.id}-editor`);m(this,Yn,null);m(this,ve);p(this,Ke,e.color||ot._defaultColor||lt._defaultLineColor),p(this,ve,e.fontSize||ot._defaultFontSize)}static get _keyboardManager(){const e=ot.prototype,s=a=>a.isEmpty(),i=br.TRANSLATE_SMALL,r=br.TRANSLATE_BIG;return X(this,"_keyboardManager",new Gl([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],e.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],e.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],e._translateEmpty,{args:[-i,0],checker:s}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],e._translateEmpty,{args:[-r,0],checker:s}],[["ArrowRight","mac+ArrowRight"],e._translateEmpty,{args:[i,0],checker:s}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],e._translateEmpty,{args:[r,0],checker:s}],[["ArrowUp","mac+ArrowUp"],e._translateEmpty,{args:[0,-i],checker:s}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],e._translateEmpty,{args:[0,-r],checker:s}],[["ArrowDown","mac+ArrowDown"],e._translateEmpty,{args:[0,i],checker:s}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],e._translateEmpty,{args:[0,r],checker:s}]]))}static initialize(e,s){lt.initialize(e,s);const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(e,s){switch(e){case Y.FREETEXT_SIZE:ot._defaultFontSize=s;break;case Y.FREETEXT_COLOR:ot._defaultColor=s;break}}updateParams(e,s){switch(e){case Y.FREETEXT_SIZE:b(this,Ct,Vf).call(this,s);break;case Y.FREETEXT_COLOR:b(this,Ct,jf).call(this,s);break}}static get defaultPropertiesToUpdate(){return[[Y.FREETEXT_SIZE,ot._defaultFontSize],[Y.FREETEXT_COLOR,ot._defaultColor||lt._defaultLineColor]]}get propertiesToUpdate(){return[[Y.FREETEXT_SIZE,n(this,ve)],[Y.FREETEXT_COLOR,n(this,Ke)]]}_translateEmpty(e,s){this._uiManager.translateSelectedEditors(e,s,!0)}getInitialTranslation(){const e=this.parentScale;return[-ot._internalPadding*e,-(ot._internalPadding+n(this,ve))*e]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(this.isInEditMode())return;this.parent.setEditingState(!1),this.parent.updateToolbar(z.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),p(this,Yn,new AbortController);const e=this._uiManager.combinedSignal(n(this,Yn));this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:e}),this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:e}),this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:e}),this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:e}),this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:e})}disableEditMode(){var e;this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",n(this,Al)),this._isDraggable=!0,(e=n(this,Yn))==null||e.abort(),p(this,Yn,null),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"))}focusin(e){this._focusEventsAllowed&&(super.focusin(e),e.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(e){var s;this.width||(this.enableEditMode(),e&&this.editorDiv.focus(),(s=this._initialOptions)!=null&&s.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const e=n(this,we),s=p(this,we,b(this,Ct,Wf).call(this).trimEnd());if(e===s)return;const i=r=>{if(p(this,we,r),!r){this.remove();return}b(this,Ct,bh).call(this),this._uiManager.rebuild(this),b(this,Ct,gh).call(this)};this.addCommands({cmd:()=>{i(s)},undo:()=>{i(e)},mustExec:!1}),b(this,Ct,gh).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(e){this.enterInEditMode()}keydown(e){e.target===this.div&&e.key==="Enter"&&(this.enterInEditMode(),e.preventDefault())}editorDivKeydown(e){ot._keyboardManager.exec(this,e)}editorDivFocus(e){this.isEditing=!0}editorDivBlur(e){this.isEditing=!1}editorDivInput(e){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let e,s;(this._isCopy||this.annotationElementId)&&(e=this.x,s=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",n(this,Al)),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2"),this.editorDiv.setAttribute("data-l10n-attrs","default-content"),this.enableEditing(),this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;if(i.fontSize=`calc(${n(this,ve)}px * var(--total-scale-factor))`,i.color=n(this,Ke),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),zd(this,this.div,["dblclick","keydown"]),this._isCopy||this.annotationElementId){const[r,a]=this.parentDimensions;if(this.annotationElementId){const{position:o}=this._initialData;let[l,h]=this.getInitialTranslation();[l,h]=this.pageTranslationToScreen(l,h);const[c,u]=this.pageDimensions,[f,g]=this.pageTranslation;let y,A;switch(this.rotation){case 0:y=e+(o[0]-f)/c,A=s+this.height-(o[1]-g)/u;break;case 90:y=e+(o[0]-f)/c,A=s-(o[1]-g)/u,[l,h]=[h,-l];break;case 180:y=e-this.width+(o[0]-f)/c,A=s-(o[1]-g)/u,[l,h]=[-l,-h];break;case 270:y=e+(o[0]-f-this.height*u)/c,A=s+(o[1]-g-this.width*c)/u,[l,h]=[-h,l];break}this.setAt(y*r,A*a,l,h)}else this._moveAfterPaste(e,s);b(this,Ct,bh).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}editorDivPaste(e){var y,A,w;const s=e.clipboardData||window.clipboardData,{types:i}=s;if(i.length===1&&i[0]==="text/plain")return;e.preventDefault();const r=b(y=ot,di,cd).call(y,s.getData("text")||"").replaceAll(Kl,`
`);if(!r)return;const a=window.getSelection();if(!a.rangeCount)return;this.editorDiv.normalize(),a.deleteFromDocument();const o=a.getRangeAt(0);if(!r.includes(`
`)){o.insertNode(document.createTextNode(r)),this.editorDiv.normalize(),a.collapseToStart();return}const{startContainer:l,startOffset:h}=o,c=[],u=[];if(l.nodeType===Node.TEXT_NODE){const v=l.parentElement;if(u.push(l.nodeValue.slice(h).replaceAll(Kl,"")),v!==this.editorDiv){let _=c;for(const S of this.editorDiv.childNodes){if(S===v){_=u;continue}_.push(b(A=ot,di,mh).call(A,S))}}c.push(l.nodeValue.slice(0,h).replaceAll(Kl,""))}else if(l===this.editorDiv){let v=c,_=0;for(const S of this.editorDiv.childNodes)_++===h&&(v=u),v.push(b(w=ot,di,mh).call(w,S))}p(this,we,`${c.join(`
`)}${r}${u.join(`
`)}`),b(this,Ct,bh).call(this);const f=new Range;let g=Math.sumPrecise(c.map(v=>v.length));for(const{firstChild:v}of this.editorDiv.childNodes)if(v.nodeType===Node.TEXT_NODE){const _=v.nodeValue.length;if(g<=_){f.setStart(v,g),f.setEnd(v,g);break}g-=_}a.removeAllRanges(),a.addRange(f)}get contentDiv(){return this.editorDiv}static async deserialize(e,s,i){var o;let r=null;if(e instanceof $f){const{data:{defaultAppearanceData:{fontSize:l,fontColor:h},rect:c,rotation:u,id:f,popupRef:g},textContent:y,textPosition:A,parent:{page:{pageNumber:w}}}=e;if(!y||y.length===0)return null;r=e={annotationType:z.FREETEXT,color:Array.from(h),fontSize:l,value:y.join(`
`),position:A,pageIndex:w-1,rect:c.slice(0),rotation:u,id:f,deleted:!1,popupRef:g}}const a=await super.deserialize(e,s,i);return p(a,ve,e.fontSize),p(a,Ke,O.makeHexColor(...e.color)),p(a,we,b(o=ot,di,cd).call(o,e.value)),a.annotationElementId=e.id||null,a._initialData=r,a}serialize(e=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const s=ot._internalPadding*this.parentScale,i=this.getRect(s,s),r=lt._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:n(this,Ke)),a={annotationType:z.FREETEXT,color:r,fontSize:n(this,ve),value:b(this,Ct,qf).call(this),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?(a.isCopy=!0,a):this.annotationElementId&&!b(this,Ct,Xf).call(this,a)?null:(a.id=this.annotationElementId,a)}renderAnnotationElement(e){const s=super.renderAnnotationElement(e);if(this.deleted)return s;const{style:i}=s;i.fontSize=`calc(${n(this,ve)}px * var(--total-scale-factor))`,i.color=n(this,Ke),s.replaceChildren();for(const a of n(this,we).split(`
`)){const o=document.createElement("div");o.append(a?document.createTextNode(a):document.createElement("br")),s.append(o)}const r=ot._internalPadding*this.parentScale;return e.updateEdited({rect:this.getRect(r,r),popupContent:n(this,we)}),s}resetAnnotationElement(e){super.resetAnnotationElement(e),e.resetEdited()}};Ke=new WeakMap,we=new WeakMap,Al=new WeakMap,Yn=new WeakMap,ve=new WeakMap,Ct=new WeakSet,Vf=function(e){const s=r=>{this.editorDiv.style.fontSize=`calc(${r}px * var(--total-scale-factor))`,this.translate(0,-(r-n(this,ve))*this.parentScale),p(this,ve,r),b(this,Ct,gh).call(this)},i=n(this,ve);this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})},jf=function(e){const s=r=>{p(this,Ke,this.editorDiv.style.color=r)},i=n(this,Ke);this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})},Wf=function(){var i;const e=[];this.editorDiv.normalize();let s=null;for(const r of this.editorDiv.childNodes)(s==null?void 0:s.nodeType)===Node.TEXT_NODE&&r.nodeName==="BR"||(e.push(b(i=ot,di,mh).call(i,r)),s=r);return e.join(`
`)},gh=function(){const[e,s]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:r,div:a}=this,o=a.style.display,l=a.classList.contains("hidden");a.classList.remove("hidden"),a.style.display="hidden",r.div.append(this.div),i=a.getBoundingClientRect(),a.remove(),a.style.display=o,a.classList.toggle("hidden",l)}this.rotation%180===this.parentRotation%180?(this.width=i.width/e,this.height=i.height/s):(this.width=i.height/e,this.height=i.width/s),this.fixAndSetPosition()},di=new WeakSet,mh=function(e){return(e.nodeType===Node.TEXT_NODE?e.nodeValue:e.innerText).replaceAll(Kl,"")},bh=function(){if(this.editorDiv.replaceChildren(),!!n(this,we))for(const e of n(this,we).split(`
`)){const s=document.createElement("div");s.append(e?document.createTextNode(e):document.createElement("br")),this.editorDiv.append(s)}},qf=function(){return n(this,we).replaceAll(" "," ")},cd=function(e){return e.replaceAll(" "," ")},Xf=function(e){const{value:s,fontSize:i,color:r,pageIndex:a}=this._initialData;return this._hasBeenMoved||e.value!==s||e.fontSize!==i||e.color.some((o,l)=>o!==r[l])||e.pageIndex!==a},m(ot,di),R(ot,"_freeTextDefaultContent",""),R(ot,"_internalPadding",0),R(ot,"_defaultColor",null),R(ot,"_defaultFontSize",10),R(ot,"_type","freetext"),R(ot,"_editorType",z.FREETEXT);let hd=ot;class L{toSVGPath(){rt("Abstract method `toSVGPath` must be implemented.")}get box(){rt("Abstract getter `box` must be implemented.")}serialize(t,e){rt("Abstract method `serialize` must be implemented.")}static _rescale(t,e,s,i,r,a){a||(a=new Float32Array(t.length));for(let o=0,l=t.length;o<l;o+=2)a[o]=e+t[o]*i,a[o+1]=s+t[o+1]*r;return a}static _rescaleAndSwap(t,e,s,i,r,a){a||(a=new Float32Array(t.length));for(let o=0,l=t.length;o<l;o+=2)a[o]=e+t[o+1]*i,a[o+1]=s+t[o]*r;return a}static _translate(t,e,s,i){i||(i=new Float32Array(t.length));for(let r=0,a=t.length;r<a;r+=2)i[r]=e+t[r],i[r+1]=s+t[r+1];return i}static svgRound(t){return Math.round(t*1e4)}static _normalizePoint(t,e,s,i,r){switch(r){case 90:return[1-e/s,t/i];case 180:return[1-t/s,1-e/i];case 270:return[e/s,1-t/i];default:return[t/s,e/i]}}static _normalizePagePoint(t,e,s){switch(s){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,s,i,r,a){return[(t+5*s)/6,(e+5*i)/6,(5*s+r)/6,(5*i+a)/6,(s+r)/2,(i+a)/2]}}R(L,"PRECISION",1e-4);var _e,Qe,va,_a,ws,W,Kn,Qn,yl,wl,Sa,Ea,$i,vl,Jh,Zh,It,ao,Yf,Kf,Qf,Jf,Zf,tp;const Ls=class Ls{constructor({x:t,y:e},s,i,r,a,o=0){m(this,It);m(this,_e);m(this,Qe,[]);m(this,va);m(this,_a);m(this,ws,[]);m(this,W,new Float32Array(18));m(this,Kn);m(this,Qn);m(this,yl);m(this,wl);m(this,Sa);m(this,Ea);m(this,$i,[]);p(this,_e,s),p(this,Ea,r*i),p(this,_a,a),n(this,W).set([NaN,NaN,NaN,NaN,t,e],6),p(this,va,o),p(this,wl,n(Ls,vl)*i),p(this,yl,n(Ls,Zh)*i),p(this,Sa,i),n(this,$i).push(t,e)}isEmpty(){return isNaN(n(this,W)[8])}add({x:t,y:e}){var k;p(this,Kn,t),p(this,Qn,e);const[s,i,r,a]=n(this,_e);let[o,l,h,c]=n(this,W).subarray(8,12);const u=t-h,f=e-c,g=Math.hypot(u,f);if(g<n(this,yl))return!1;const y=g-n(this,wl),A=y/g,w=A*u,v=A*f;let _=o,S=l;o=h,l=c,h+=w,c+=v,(k=n(this,$i))==null||k.push(t,e);const E=-v/y,C=w/y,x=E*n(this,Ea),T=C*n(this,Ea);return n(this,W).set(n(this,W).subarray(2,8),0),n(this,W).set([h+x,c+T],4),n(this,W).set(n(this,W).subarray(14,18),12),n(this,W).set([h-x,c-T],16),isNaN(n(this,W)[6])?(n(this,ws).length===0&&(n(this,W).set([o+x,l+T],2),n(this,ws).push(NaN,NaN,NaN,NaN,(o+x-s)/r,(l+T-i)/a),n(this,W).set([o-x,l-T],14),n(this,Qe).push(NaN,NaN,NaN,NaN,(o-x-s)/r,(l-T-i)/a)),n(this,W).set([_,S,o,l,h,c],6),!this.isEmpty()):(n(this,W).set([_,S,o,l,h,c],6),Math.abs(Math.atan2(S-l,_-o)-Math.atan2(v,w))<Math.PI/2?([o,l,h,c]=n(this,W).subarray(2,6),n(this,ws).push(NaN,NaN,NaN,NaN,((o+h)/2-s)/r,((l+c)/2-i)/a),[o,l,_,S]=n(this,W).subarray(14,18),n(this,Qe).push(NaN,NaN,NaN,NaN,((_+o)/2-s)/r,((S+l)/2-i)/a),!0):([_,S,o,l,h,c]=n(this,W).subarray(0,6),n(this,ws).push(((_+5*o)/6-s)/r,((S+5*l)/6-i)/a,((5*o+h)/6-s)/r,((5*l+c)/6-i)/a,((o+h)/2-s)/r,((l+c)/2-i)/a),[h,c,o,l,_,S]=n(this,W).subarray(12,18),n(this,Qe).push(((_+5*o)/6-s)/r,((S+5*l)/6-i)/a,((5*o+h)/6-s)/r,((5*l+c)/6-i)/a,((o+h)/2-s)/r,((l+c)/2-i)/a),!0))}toSVGPath(){if(this.isEmpty())return"";const t=n(this,ws),e=n(this,Qe);if(isNaN(n(this,W)[6])&&!this.isEmpty())return b(this,It,Yf).call(this);const s=[];s.push(`M${t[4]} ${t[5]}`);for(let i=6;i<t.length;i+=6)isNaN(t[i])?s.push(`L${t[i+4]} ${t[i+5]}`):s.push(`C${t[i]} ${t[i+1]} ${t[i+2]} ${t[i+3]} ${t[i+4]} ${t[i+5]}`);b(this,It,Qf).call(this,s);for(let i=e.length-6;i>=6;i-=6)isNaN(e[i])?s.push(`L${e[i+4]} ${e[i+5]}`):s.push(`C${e[i]} ${e[i+1]} ${e[i+2]} ${e[i+3]} ${e[i+4]} ${e[i+5]}`);return b(this,It,Kf).call(this,s),s.join(" ")}newFreeDrawOutline(t,e,s,i,r,a){return new ep(t,e,s,i,r,a)}getOutlines(){var u;const t=n(this,ws),e=n(this,Qe),s=n(this,W),[i,r,a,o]=n(this,_e),l=new Float32Array((((u=n(this,$i))==null?void 0:u.length)??0)+2);for(let f=0,g=l.length-2;f<g;f+=2)l[f]=(n(this,$i)[f]-i)/a,l[f+1]=(n(this,$i)[f+1]-r)/o;if(l[l.length-2]=(n(this,Kn)-i)/a,l[l.length-1]=(n(this,Qn)-r)/o,isNaN(s[6])&&!this.isEmpty())return b(this,It,Jf).call(this,l);const h=new Float32Array(n(this,ws).length+24+n(this,Qe).length);let c=t.length;for(let f=0;f<c;f+=2){if(isNaN(t[f])){h[f]=h[f+1]=NaN;continue}h[f]=t[f],h[f+1]=t[f+1]}c=b(this,It,tp).call(this,h,c);for(let f=e.length-6;f>=6;f-=6)for(let g=0;g<6;g+=2){if(isNaN(e[f+g])){h[c]=h[c+1]=NaN,c+=2;continue}h[c]=e[f+g],h[c+1]=e[f+g+1],c+=2}return b(this,It,Zf).call(this,h,c),this.newFreeDrawOutline(h,l,n(this,_e),n(this,Sa),n(this,va),n(this,_a))}};_e=new WeakMap,Qe=new WeakMap,va=new WeakMap,_a=new WeakMap,ws=new WeakMap,W=new WeakMap,Kn=new WeakMap,Qn=new WeakMap,yl=new WeakMap,wl=new WeakMap,Sa=new WeakMap,Ea=new WeakMap,$i=new WeakMap,vl=new WeakMap,Jh=new WeakMap,Zh=new WeakMap,It=new WeakSet,ao=function(){const t=n(this,W).subarray(4,6),e=n(this,W).subarray(16,18),[s,i,r,a]=n(this,_e);return[(n(this,Kn)+(t[0]-e[0])/2-s)/r,(n(this,Qn)+(t[1]-e[1])/2-i)/a,(n(this,Kn)+(e[0]-t[0])/2-s)/r,(n(this,Qn)+(e[1]-t[1])/2-i)/a]},Yf=function(){const[t,e,s,i]=n(this,_e),[r,a,o,l]=b(this,It,ao).call(this);return`M${(n(this,W)[2]-t)/s} ${(n(this,W)[3]-e)/i} L${(n(this,W)[4]-t)/s} ${(n(this,W)[5]-e)/i} L${r} ${a} L${o} ${l} L${(n(this,W)[16]-t)/s} ${(n(this,W)[17]-e)/i} L${(n(this,W)[14]-t)/s} ${(n(this,W)[15]-e)/i} Z`},Kf=function(t){const e=n(this,Qe);t.push(`L${e[4]} ${e[5]} Z`)},Qf=function(t){const[e,s,i,r]=n(this,_e),a=n(this,W).subarray(4,6),o=n(this,W).subarray(16,18),[l,h,c,u]=b(this,It,ao).call(this);t.push(`L${(a[0]-e)/i} ${(a[1]-s)/r} L${l} ${h} L${c} ${u} L${(o[0]-e)/i} ${(o[1]-s)/r}`)},Jf=function(t){const e=n(this,W),[s,i,r,a]=n(this,_e),[o,l,h,c]=b(this,It,ao).call(this),u=new Float32Array(36);return u.set([NaN,NaN,NaN,NaN,(e[2]-s)/r,(e[3]-i)/a,NaN,NaN,NaN,NaN,(e[4]-s)/r,(e[5]-i)/a,NaN,NaN,NaN,NaN,o,l,NaN,NaN,NaN,NaN,h,c,NaN,NaN,NaN,NaN,(e[16]-s)/r,(e[17]-i)/a,NaN,NaN,NaN,NaN,(e[14]-s)/r,(e[15]-i)/a],0),this.newFreeDrawOutline(u,t,n(this,_e),n(this,Sa),n(this,va),n(this,_a))},Zf=function(t,e){const s=n(this,Qe);return t.set([NaN,NaN,NaN,NaN,s[4],s[5]],e),e+=6},tp=function(t,e){const s=n(this,W).subarray(4,6),i=n(this,W).subarray(16,18),[r,a,o,l]=n(this,_e),[h,c,u,f]=b(this,It,ao).call(this);return t.set([NaN,NaN,NaN,NaN,(s[0]-r)/o,(s[1]-a)/l,NaN,NaN,NaN,NaN,h,c,NaN,NaN,NaN,NaN,u,f,NaN,NaN,NaN,NaN,(i[0]-r)/o,(i[1]-a)/l],e),e+=24},m(Ls,vl,8),m(Ls,Jh,2),m(Ls,Zh,n(Ls,vl)+n(Ls,Jh));let Dh=Ls;var Ca,Jn,ti,_l,Se,Sl,yt,tc,sp;class ep extends L{constructor(e,s,i,r,a,o){super();m(this,tc);m(this,Ca);m(this,Jn,new Float32Array(4));m(this,ti);m(this,_l);m(this,Se);m(this,Sl);m(this,yt);p(this,yt,e),p(this,Se,s),p(this,Ca,i),p(this,Sl,r),p(this,ti,a),p(this,_l,o),this.lastPoint=[NaN,NaN],b(this,tc,sp).call(this,o);const[l,h,c,u]=n(this,Jn);for(let f=0,g=e.length;f<g;f+=2)e[f]=(e[f]-l)/c,e[f+1]=(e[f+1]-h)/u;for(let f=0,g=s.length;f<g;f+=2)s[f]=(s[f]-l)/c,s[f+1]=(s[f+1]-h)/u}toSVGPath(){const e=[`M${n(this,yt)[4]} ${n(this,yt)[5]}`];for(let s=6,i=n(this,yt).length;s<i;s+=6){if(isNaN(n(this,yt)[s])){e.push(`L${n(this,yt)[s+4]} ${n(this,yt)[s+5]}`);continue}e.push(`C${n(this,yt)[s]} ${n(this,yt)[s+1]} ${n(this,yt)[s+2]} ${n(this,yt)[s+3]} ${n(this,yt)[s+4]} ${n(this,yt)[s+5]}`)}return e.push("Z"),e.join(" ")}serialize([e,s,i,r],a){const o=i-e,l=r-s;let h,c;switch(a){case 0:h=L._rescale(n(this,yt),e,r,o,-l),c=L._rescale(n(this,Se),e,r,o,-l);break;case 90:h=L._rescaleAndSwap(n(this,yt),e,s,o,l),c=L._rescaleAndSwap(n(this,Se),e,s,o,l);break;case 180:h=L._rescale(n(this,yt),i,s,-o,l),c=L._rescale(n(this,Se),i,s,-o,l);break;case 270:h=L._rescaleAndSwap(n(this,yt),i,r,-o,-l),c=L._rescaleAndSwap(n(this,Se),i,r,-o,-l);break}return{outline:Array.from(h),points:[Array.from(c)]}}get box(){return n(this,Jn)}newOutliner(e,s,i,r,a,o=0){return new Dh(e,s,i,r,a,o)}getNewOutline(e,s){const[i,r,a,o]=n(this,Jn),[l,h,c,u]=n(this,Ca),f=a*c,g=o*u,y=i*c+l,A=r*u+h,w=this.newOutliner({x:n(this,Se)[0]*f+y,y:n(this,Se)[1]*g+A},n(this,Ca),n(this,Sl),e,n(this,_l),s??n(this,ti));for(let v=2;v<n(this,Se).length;v+=2)w.add({x:n(this,Se)[v]*f+y,y:n(this,Se)[v+1]*g+A});return w.getOutlines()}}Ca=new WeakMap,Jn=new WeakMap,ti=new WeakMap,_l=new WeakMap,Se=new WeakMap,Sl=new WeakMap,yt=new WeakMap,tc=new WeakSet,sp=function(e){const s=n(this,yt);let i=s[4],r=s[5];const a=[i,r,i,r];let o=i,l=r;const h=e?Math.max:Math.min;for(let u=6,f=s.length;u<f;u+=6){const g=s[u+4],y=s[u+5];if(isNaN(s[u]))O.pointBoundingBox(g,y,a),l<y?(o=g,l=y):l===y&&(o=h(o,g));else{const A=[1/0,1/0,-1/0,-1/0];O.bezierBoundingBox(i,r,...s.slice(u,u+6),A),O.rectBoundingBox(...A,a),l<A[3]?(o=A[2],l=A[3]):l===A[3]&&(o=h(o,A[2]))}i=g,r=y}const c=n(this,Jn);c[0]=a[0]-n(this,ti),c[1]=a[1]-n(this,ti),c[2]=a[2]-a[0]+2*n(this,ti),c[3]=a[3]-a[1]+2*n(this,ti),this.lastPoint=[o,l]};var El,Cl,Gi,Je,le,ip,Ah,np,rp,ud;class dd{constructor(t,e=0,s=0,i=!0){m(this,le);m(this,El);m(this,Cl);m(this,Gi,[]);m(this,Je,[]);const r=[1/0,1/0,-1/0,-1/0],a=10**-4;for(const{x:g,y,width:A,height:w}of t){const v=Math.floor((g-e)/a)*a,_=Math.ceil((g+A+e)/a)*a,S=Math.floor((y-e)/a)*a,E=Math.ceil((y+w+e)/a)*a,C=[v,S,E,!0],x=[_,S,E,!1];n(this,Gi).push(C,x),O.rectBoundingBox(v,S,_,E,r)}const o=r[2]-r[0]+2*s,l=r[3]-r[1]+2*s,h=r[0]-s,c=r[1]-s,u=n(this,Gi).at(i?-1:-2),f=[u[0],u[2]];for(const g of n(this,Gi)){const[y,A,w]=g;g[0]=(y-h)/o,g[1]=(A-c)/l,g[2]=(w-c)/l}p(this,El,new Float32Array([h,c,o,l])),p(this,Cl,f)}getOutlines(){n(this,Gi).sort((e,s)=>e[0]-s[0]||e[1]-s[1]||e[2]-s[2]);const t=[];for(const e of n(this,Gi))e[3]?(t.push(...b(this,le,ud).call(this,e)),b(this,le,np).call(this,e)):(b(this,le,rp).call(this,e),t.push(...b(this,le,ud).call(this,e)));return b(this,le,ip).call(this,t)}}El=new WeakMap,Cl=new WeakMap,Gi=new WeakMap,Je=new WeakMap,le=new WeakSet,ip=function(t){const e=[],s=new Set;for(const a of t){const[o,l,h]=a;e.push([o,l,a],[o,h,a])}e.sort((a,o)=>a[1]-o[1]||a[0]-o[0]);for(let a=0,o=e.length;a<o;a+=2){const l=e[a][2],h=e[a+1][2];l.push(h),h.push(l),s.add(l),s.add(h)}const i=[];let r;for(;s.size>0;){const a=s.values().next().value;let[o,l,h,c,u]=a;s.delete(a);let f=o,g=l;for(r=[o,h],i.push(r);;){let y;if(s.has(c))y=c;else if(s.has(u))y=u;else break;s.delete(y),[o,l,h,c,u]=y,f!==o&&(r.push(f,g,o,g===l?l:h),f=o),g=g===l?h:l}r.push(f,g)}return new Sm(i,n(this,El),n(this,Cl))},Ah=function(t){const e=n(this,Je);let s=0,i=e.length-1;for(;s<=i;){const r=s+i>>1,a=e[r][0];if(a===t)return r;a<t?s=r+1:i=r-1}return i+1},np=function([,t,e]){const s=b(this,le,Ah).call(this,t);n(this,Je).splice(s,0,[t,e])},rp=function([,t,e]){const s=b(this,le,Ah).call(this,t);for(let i=s;i<n(this,Je).length;i++){const[r,a]=n(this,Je)[i];if(r!==t)break;if(r===t&&a===e){n(this,Je).splice(i,1);return}}for(let i=s-1;i>=0;i--){const[r,a]=n(this,Je)[i];if(r!==t)break;if(r===t&&a===e){n(this,Je).splice(i,1);return}}},ud=function(t){const[e,s,i]=t,r=[[e,s,i]],a=b(this,le,Ah).call(this,i);for(let o=0;o<a;o++){const[l,h]=n(this,Je)[o];for(let c=0,u=r.length;c<u;c++){const[,f,g]=r[c];if(!(h<=f||g<=l)){if(f>=l){if(g>h)r[c][1]=h;else{if(u===1)return[];r.splice(c,1),c--,u--}continue}r[c][2]=l,g>h&&r.push([e,h,g])}}}return r};var xl,xa;class Sm extends L{constructor(e,s,i){super();m(this,xl);m(this,xa);p(this,xa,e),p(this,xl,s),this.lastPoint=i}toSVGPath(){const e=[];for(const s of n(this,xa)){let[i,r]=s;e.push(`M${i} ${r}`);for(let a=2;a<s.length;a+=2){const o=s[a],l=s[a+1];o===i?(e.push(`V${l}`),r=l):l===r&&(e.push(`H${o}`),i=o)}e.push("Z")}return e.join(" ")}serialize([e,s,i,r],a){const o=[],l=i-e,h=r-s;for(const c of n(this,xa)){const u=new Array(c.length);for(let f=0;f<c.length;f+=2)u[f]=e+c[f]*l,u[f+1]=r-c[f+1]*h;o.push(u)}return o}get box(){return n(this,xl)}get classNamesForOutlining(){return["highlightOutline"]}}xl=new WeakMap,xa=new WeakMap;class fd extends Dh{newFreeDrawOutline(t,e,s,i,r,a){return new Em(t,e,s,i,r,a)}}class Em extends ep{newOutliner(t,e,s,i,r,a=0){return new fd(t,e,s,i,r,a)}}var Ze,Zn,Ta,Pt,Tl,Ra,Rl,Pl,zi,ts,Pa,Il,nt,pd,gd,md,ln,ap,mi;const ce=class ce{constructor({editor:t=null,uiManager:e=null}){m(this,nt);m(this,Ze,null);m(this,Zn,null);m(this,Ta);m(this,Pt,null);m(this,Tl,!1);m(this,Ra,!1);m(this,Rl,null);m(this,Pl);m(this,zi,null);m(this,ts,null);m(this,Pa);var s;t?(p(this,Ra,!1),p(this,Pa,Y.HIGHLIGHT_COLOR),p(this,Rl,t)):(p(this,Ra,!0),p(this,Pa,Y.HIGHLIGHT_DEFAULT_COLOR)),p(this,ts,(t==null?void 0:t._uiManager)||e),p(this,Pl,n(this,ts)._eventBus),p(this,Ta,(t==null?void 0:t.color)||((s=n(this,ts))==null?void 0:s.highlightColors.values().next().value)||"#FFFF98"),n(ce,Il)||p(ce,Il,Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"}))}static get _keyboardManager(){return X(this,"_keyboardManager",new Gl([[["Escape","mac+Escape"],ce.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],ce.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],ce.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],ce.prototype._moveToPrevious],[["Home","mac+Home"],ce.prototype._moveToBeginning],[["End","mac+End"],ce.prototype._moveToEnd]]))}renderButton(){const t=p(this,Ze,document.createElement("button"));t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.setAttribute("aria-haspopup",!0);const e=n(this,ts)._signal;t.addEventListener("click",b(this,nt,ln).bind(this),{signal:e}),t.addEventListener("keydown",b(this,nt,md).bind(this),{signal:e});const s=p(this,Zn,document.createElement("span"));return s.className="swatch",s.setAttribute("aria-hidden",!0),s.style.backgroundColor=n(this,Ta),t.append(s),t}renderMainDropdown(){const t=p(this,Pt,b(this,nt,pd).call(this));return t.setAttribute("aria-orientation","horizontal"),t.setAttribute("aria-labelledby","highlightColorPickerLabel"),t}_colorSelectFromKeyboard(t){if(t.target===n(this,Ze)){b(this,nt,ln).call(this,t);return}const e=t.target.getAttribute("data-color");e&&b(this,nt,gd).call(this,e,t)}_moveToNext(t){var e,s;if(!n(this,nt,mi)){b(this,nt,ln).call(this,t);return}if(t.target===n(this,Ze)){(e=n(this,Pt).firstChild)==null||e.focus();return}(s=t.target.nextSibling)==null||s.focus()}_moveToPrevious(t){var e,s;if(t.target===((e=n(this,Pt))==null?void 0:e.firstChild)||t.target===n(this,Ze)){n(this,nt,mi)&&this._hideDropdownFromKeyboard();return}n(this,nt,mi)||b(this,nt,ln).call(this,t),(s=t.target.previousSibling)==null||s.focus()}_moveToBeginning(t){var e;if(!n(this,nt,mi)){b(this,nt,ln).call(this,t);return}(e=n(this,Pt).firstChild)==null||e.focus()}_moveToEnd(t){var e;if(!n(this,nt,mi)){b(this,nt,ln).call(this,t);return}(e=n(this,Pt).lastChild)==null||e.focus()}hideDropdown(){var t,e;(t=n(this,Pt))==null||t.classList.add("hidden"),(e=n(this,zi))==null||e.abort(),p(this,zi,null)}_hideDropdownFromKeyboard(){var t;if(!n(this,Ra)){if(!n(this,nt,mi)){(t=n(this,Rl))==null||t.unselect();return}this.hideDropdown(),n(this,Ze).focus({preventScroll:!0,focusVisible:n(this,Tl)})}}updateColor(t){if(n(this,Zn)&&(n(this,Zn).style.backgroundColor=t),!n(this,Pt))return;const e=n(this,ts).highlightColors.values();for(const s of n(this,Pt).children)s.setAttribute("aria-selected",e.next().value===t)}destroy(){var t,e;(t=n(this,Ze))==null||t.remove(),p(this,Ze,null),p(this,Zn,null),(e=n(this,Pt))==null||e.remove(),p(this,Pt,null)}};Ze=new WeakMap,Zn=new WeakMap,Ta=new WeakMap,Pt=new WeakMap,Tl=new WeakMap,Ra=new WeakMap,Rl=new WeakMap,Pl=new WeakMap,zi=new WeakMap,ts=new WeakMap,Pa=new WeakMap,Il=new WeakMap,nt=new WeakSet,pd=function(){const t=document.createElement("div"),e=n(this,ts)._signal;t.addEventListener("contextmenu",rs,{signal:e}),t.className="dropdown",t.role="listbox",t.setAttribute("aria-multiselectable",!1),t.setAttribute("aria-orientation","vertical"),t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[s,i]of n(this,ts).highlightColors){const r=document.createElement("button");r.tabIndex="0",r.role="option",r.setAttribute("data-color",i),r.title=s,r.setAttribute("data-l10n-id",n(ce,Il)[s]);const a=document.createElement("span");r.append(a),a.className="swatch",a.style.backgroundColor=i,r.setAttribute("aria-selected",i===n(this,Ta)),r.addEventListener("click",b(this,nt,gd).bind(this,i),{signal:e}),t.append(r)}return t.addEventListener("keydown",b(this,nt,md).bind(this),{signal:e}),t},gd=function(t,e){e.stopPropagation(),n(this,Pl).dispatch("switchannotationeditorparams",{source:this,type:n(this,Pa),value:t})},md=function(t){ce._keyboardManager.exec(this,t)},ln=function(t){if(n(this,nt,mi)){this.hideDropdown();return}if(p(this,Tl,t.detail===0),n(this,zi)||(p(this,zi,new AbortController),window.addEventListener("pointerdown",b(this,nt,ap).bind(this),{signal:n(this,ts).combinedSignal(n(this,zi))})),n(this,Pt)){n(this,Pt).classList.remove("hidden");return}const e=p(this,Pt,b(this,nt,pd).call(this));n(this,Ze).append(e)},ap=function(t){var e;(e=n(this,Pt))!=null&&e.contains(t.target)||this.hideDropdown()},mi=function(){return n(this,Pt)&&!n(this,Pt).classList.contains("hidden")},m(ce,Il,null);let Fh=ce;var Ia,kl,ei,tr,ka,fe,Ml,Ll,er,He,Ee,Gt,Ma,si,Qt,La,$e,Dl,V,bd,yh,op,lp,hp,Ad,hn,Ue,Pr,cp,wh,oo,dp,up,fp,pp,gp;const J=class J extends lt{constructor(e){super({...e,name:"highlightEditor"});m(this,V);m(this,Ia,null);m(this,kl,0);m(this,ei);m(this,tr,null);m(this,ka,null);m(this,fe,null);m(this,Ml,null);m(this,Ll,0);m(this,er,null);m(this,He,null);m(this,Ee,null);m(this,Gt,!1);m(this,Ma,null);m(this,si);m(this,Qt,null);m(this,La,"");m(this,$e);m(this,Dl,"");this.color=e.color||J._defaultColor,p(this,$e,e.thickness||J._defaultThickness),p(this,si,e.opacity||J._defaultOpacity),p(this,ei,e.boxes||null),p(this,Dl,e.methodOfCreation||""),p(this,La,e.text||""),this._isDraggable=!1,this.defaultL10nId="pdfjs-editor-highlight-editor",e.highlightId>-1?(p(this,Gt,!0),b(this,V,yh).call(this,e),b(this,V,hn).call(this)):n(this,ei)&&(p(this,Ia,e.anchorNode),p(this,kl,e.anchorOffset),p(this,Ml,e.focusNode),p(this,Ll,e.focusOffset),b(this,V,bd).call(this),b(this,V,hn).call(this),this.rotate(this.rotation))}static get _keyboardManager(){const e=J.prototype;return X(this,"_keyboardManager",new Gl([[["ArrowLeft","mac+ArrowLeft"],e._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],e._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],e._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],e._moveCaret,{args:[3]}]]))}get telemetryInitialData(){return{action:"added",type:n(this,Gt)?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:n(this,$e),methodOfCreation:n(this,Dl)}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(e){return{numberOfColors:e.get("color").size}}static initialize(e,s){var i;lt.initialize(e,s),J._defaultColor||(J._defaultColor=((i=s.highlightColors)==null?void 0:i.values().next().value)||"#fff066")}static updateDefaultParams(e,s){switch(e){case Y.HIGHLIGHT_DEFAULT_COLOR:J._defaultColor=s;break;case Y.HIGHLIGHT_THICKNESS:J._defaultThickness=s;break}}translateInPage(e,s){}get toolbarPosition(){return n(this,Ma)}updateParams(e,s){switch(e){case Y.HIGHLIGHT_COLOR:b(this,V,op).call(this,s);break;case Y.HIGHLIGHT_THICKNESS:b(this,V,lp).call(this,s);break}}static get defaultPropertiesToUpdate(){return[[Y.HIGHLIGHT_DEFAULT_COLOR,J._defaultColor],[Y.HIGHLIGHT_THICKNESS,J._defaultThickness]]}get propertiesToUpdate(){return[[Y.HIGHLIGHT_COLOR,this.color||J._defaultColor],[Y.HIGHLIGHT_THICKNESS,n(this,$e)||J._defaultThickness],[Y.HIGHLIGHT_FREE,n(this,Gt)]]}async addEditToolbar(){const e=await super.addEditToolbar();return e?(this._uiManager.highlightColors&&(p(this,ka,new Fh({editor:this})),e.addColorPicker(n(this,ka))),e):null}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(b(this,V,oo).call(this))}getBaseTranslation(){return[0,0]}getRect(e,s){return super.getRect(e,s,b(this,V,oo).call(this))}onceAdded(e){this.annotationElementId||this.parent.addUndoableEditor(this),e&&this.div.focus()}remove(){b(this,V,Ad).call(this),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(b(this,V,hn).call(this),this.isAttachedToDOM||this.parent.add(this)))}setParent(e){var i;let s=!1;this.parent&&!e?b(this,V,Ad).call(this):e&&(b(this,V,hn).call(this,e),s=!this.parent&&((i=this.div)==null?void 0:i.classList.contains("selectedEditor"))),super.setParent(e),this.show(this._isVisible),s&&this.select()}rotate(e){var r,a,o;const{drawLayer:s}=this.parent;let i;n(this,Gt)?(e=(e-this.rotation+360)%360,i=b(r=J,Ue,Pr).call(r,n(this,He).box,e)):i=b(a=J,Ue,Pr).call(a,[this.x,this.y,this.width,this.height],e),s.updateProperties(n(this,Ee),{bbox:i,root:{"data-main-rotation":e}}),s.updateProperties(n(this,Qt),{bbox:b(o=J,Ue,Pr).call(o,n(this,fe).box,e),root:{"data-main-rotation":e}})}render(){if(this.div)return this.div;const e=super.render();n(this,La)&&(e.setAttribute("aria-label",n(this,La)),e.setAttribute("role","mark")),n(this,Gt)?e.classList.add("free"):this.div.addEventListener("keydown",b(this,V,cp).bind(this),{signal:this._uiManager._signal});const s=p(this,er,document.createElement("div"));e.append(s),s.setAttribute("aria-hidden","true"),s.className="internal",s.style.clipPath=n(this,tr);const[i,r]=this.parentDimensions;return this.setDims(this.width*i,this.height*r),zd(this,n(this,er),["pointerover","pointerleave"]),this.enableEditing(),e}pointerover(){var e;this.isSelected||(e=this.parent)==null||e.drawLayer.updateProperties(n(this,Qt),{rootClass:{hovered:!0}})}pointerleave(){var e;this.isSelected||(e=this.parent)==null||e.drawLayer.updateProperties(n(this,Qt),{rootClass:{hovered:!1}})}_moveCaret(e){switch(this.parent.unselect(this),e){case 0:case 2:b(this,V,wh).call(this,!0);break;case 1:case 3:b(this,V,wh).call(this,!1);break}}select(){var e;super.select(),n(this,Qt)&&((e=this.parent)==null||e.drawLayer.updateProperties(n(this,Qt),{rootClass:{hovered:!1,selected:!0}}))}unselect(){var e;super.unselect(),n(this,Qt)&&((e=this.parent)==null||e.drawLayer.updateProperties(n(this,Qt),{rootClass:{selected:!1}}),n(this,Gt)||b(this,V,wh).call(this,!1))}get _mustFixPosition(){return!n(this,Gt)}show(e=this._isVisible){super.show(e),this.parent&&(this.parent.drawLayer.updateProperties(n(this,Ee),{rootClass:{hidden:!e}}),this.parent.drawLayer.updateProperties(n(this,Qt),{rootClass:{hidden:!e}}))}static startHighlighting(e,s,{target:i,x:r,y:a}){const{x:o,y:l,width:h,height:c}=i.getBoundingClientRect(),u=new AbortController,f=e.combinedSignal(u),g=y=>{u.abort(),b(this,Ue,pp).call(this,e,y)};window.addEventListener("blur",g,{signal:f}),window.addEventListener("pointerup",g,{signal:f}),window.addEventListener("pointerdown",St,{capture:!0,passive:!1,signal:f}),window.addEventListener("contextmenu",rs,{signal:f}),i.addEventListener("pointermove",b(this,Ue,fp).bind(this,e),{signal:f}),this._freeHighlight=new fd({x:r,y:a},[o,l,h,c],e.scale,this._defaultThickness/2,s,.001),{id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0)}static async deserialize(e,s,i){var A,w,v,_;let r=null;if(e instanceof zf){const{data:{quadPoints:S,rect:E,rotation:C,id:x,color:T,opacity:P,popupRef:k},parent:{page:{pageNumber:B}}}=e;r=e={annotationType:z.HIGHLIGHT,color:Array.from(T),opacity:P,quadPoints:S,boxes:null,pageIndex:B-1,rect:E.slice(0),rotation:C,id:x,deleted:!1,popupRef:k}}else if(e instanceof Wd){const{data:{inkLists:S,rect:E,rotation:C,id:x,color:T,borderStyle:{rawWidth:P},popupRef:k},parent:{page:{pageNumber:B}}}=e;r=e={annotationType:z.HIGHLIGHT,color:Array.from(T),thickness:P,inkLists:S,boxes:null,pageIndex:B-1,rect:E.slice(0),rotation:C,id:x,deleted:!1,popupRef:k}}const{color:a,quadPoints:o,inkLists:l,opacity:h}=e,c=await super.deserialize(e,s,i);c.color=O.makeHexColor(...a),p(c,si,h||1),l&&p(c,$e,e.thickness),c.annotationElementId=e.id||null,c._initialData=r;const[u,f]=c.pageDimensions,[g,y]=c.pageTranslation;if(o){const S=p(c,ei,[]);for(let E=0;E<o.length;E+=8)S.push({x:(o[E]-g)/u,y:1-(o[E+1]-y)/f,width:(o[E+2]-o[E])/u,height:(o[E+1]-o[E+5])/f});b(A=c,V,bd).call(A),b(w=c,V,hn).call(w),c.rotate(c.rotation)}else if(l){p(c,Gt,!0);const S=l[0],E={x:S[0]-g,y:f-(S[1]-y)},C=new fd(E,[0,0,u,f],1,n(c,$e)/2,!0,.001);for(let P=0,k=S.length;P<k;P+=2)E.x=S[P]-g,E.y=f-(S[P+1]-y),C.add(E);const{id:x,clipPathId:T}=s.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:c.color,"fill-opacity":c._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:C.toSVGPath()}},!0,!0);b(v=c,V,yh).call(v,{highlightOutlines:C.getOutlines(),highlightId:x,clipPathId:T}),b(_=c,V,hn).call(_),c.rotate(c.parentRotation)}return c}serialize(e=!1){if(this.isEmpty()||e)return null;if(this.deleted)return this.serializeDeleted();const s=this.getRect(0,0),i=lt._colorManager.convert(this.color),r={annotationType:z.HIGHLIGHT,color:i,opacity:n(this,si),thickness:n(this,$e),quadPoints:b(this,V,dp).call(this),outlines:b(this,V,up).call(this,s),pageIndex:this.pageIndex,rect:s,rotation:b(this,V,oo).call(this),structTreeParentId:this._structTreeParentId};return this.annotationElementId&&!b(this,V,gp).call(this,r)?null:(r.id=this.annotationElementId,r)}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}};Ia=new WeakMap,kl=new WeakMap,ei=new WeakMap,tr=new WeakMap,ka=new WeakMap,fe=new WeakMap,Ml=new WeakMap,Ll=new WeakMap,er=new WeakMap,He=new WeakMap,Ee=new WeakMap,Gt=new WeakMap,Ma=new WeakMap,si=new WeakMap,Qt=new WeakMap,La=new WeakMap,$e=new WeakMap,Dl=new WeakMap,V=new WeakSet,bd=function(){const e=new dd(n(this,ei),.001);p(this,He,e.getOutlines()),[this.x,this.y,this.width,this.height]=n(this,He).box;const s=new dd(n(this,ei),.0025,.001,this._uiManager.direction==="ltr");p(this,fe,s.getOutlines());const{lastPoint:i}=n(this,fe);p(this,Ma,[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height])},yh=function({highlightOutlines:e,highlightId:s,clipPathId:i}){var u,f;if(p(this,He,e),p(this,fe,e.getNewOutline(n(this,$e)/2+1.5,.0025)),s>=0)p(this,Ee,s),p(this,tr,i),this.parent.drawLayer.finalizeDraw(s,{bbox:e.box,path:{d:e.toSVGPath()}}),p(this,Qt,this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:n(this,fe).box,path:{d:n(this,fe).toSVGPath()}},!0));else if(this.parent){const g=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(n(this,Ee),{bbox:b(u=J,Ue,Pr).call(u,n(this,He).box,(g-this.rotation+360)%360),path:{d:e.toSVGPath()}}),this.parent.drawLayer.updateProperties(n(this,Qt),{bbox:b(f=J,Ue,Pr).call(f,n(this,fe).box,g),path:{d:n(this,fe).toSVGPath()}})}const[a,o,l,h]=e.box;switch(this.rotation){case 0:this.x=a,this.y=o,this.width=l,this.height=h;break;case 90:{const[g,y]=this.parentDimensions;this.x=o,this.y=1-a,this.width=l*y/g,this.height=h*g/y;break}case 180:this.x=1-a,this.y=1-o,this.width=l,this.height=h;break;case 270:{const[g,y]=this.parentDimensions;this.x=1-o,this.y=a,this.width=l*y/g,this.height=h*g/y;break}}const{lastPoint:c}=n(this,fe);p(this,Ma,[(c[0]-a)/l,(c[1]-o)/h])},op=function(e){const s=(a,o)=>{var l,h;this.color=a,p(this,si,o),(l=this.parent)==null||l.drawLayer.updateProperties(n(this,Ee),{root:{fill:a,"fill-opacity":o}}),(h=n(this,ka))==null||h.updateColor(a)},i=this.color,r=n(this,si);this.addCommands({cmd:s.bind(this,e,J._defaultOpacity),undo:s.bind(this,i,r),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(e)},!0)},lp=function(e){const s=n(this,$e),i=r=>{p(this,$e,r),b(this,V,hp).call(this,r)};this.addCommands({cmd:i.bind(this,e),undo:i.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:e},!0)},hp=function(e){if(!n(this,Gt))return;b(this,V,yh).call(this,{highlightOutlines:n(this,He).getNewOutline(e/2)}),this.fixAndSetPosition();const[s,i]=this.parentDimensions;this.setDims(this.width*s,this.height*i)},Ad=function(){n(this,Ee)===null||!this.parent||(this.parent.drawLayer.remove(n(this,Ee)),p(this,Ee,null),this.parent.drawLayer.remove(n(this,Qt)),p(this,Qt,null))},hn=function(e=this.parent){n(this,Ee)===null&&({id:te(this,Ee)._,clipPathId:te(this,tr)._}=e.drawLayer.draw({bbox:n(this,He).box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":n(this,si)},rootClass:{highlight:!0,free:n(this,Gt)},path:{d:n(this,He).toSVGPath()}},!1,!0),p(this,Qt,e.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:n(this,Gt)},bbox:n(this,fe).box,path:{d:n(this,fe).toSVGPath()}},n(this,Gt))),n(this,er)&&(n(this,er).style.clipPath=n(this,tr)))},Ue=new WeakSet,Pr=function([e,s,i,r],a){switch(a){case 90:return[1-s-r,e,r,i];case 180:return[1-e-i,1-s-r,i,r];case 270:return[s,1-e-i,r,i]}return[e,s,i,r]},cp=function(e){J._keyboardManager.exec(this,e)},wh=function(e){if(!n(this,Ia))return;const s=window.getSelection();e?s.setPosition(n(this,Ia),n(this,kl)):s.setPosition(n(this,Ml),n(this,Ll))},oo=function(){return n(this,Gt)?this.rotation:0},dp=function(){if(n(this,Gt))return null;const[e,s]=this.pageDimensions,[i,r]=this.pageTranslation,a=n(this,ei),o=new Float32Array(a.length*8);let l=0;for(const{x:h,y:c,width:u,height:f}of a){const g=h*e+i,y=(1-c)*s+r;o[l]=o[l+4]=g,o[l+1]=o[l+3]=y,o[l+2]=o[l+6]=g+u*e,o[l+5]=o[l+7]=y-f*s,l+=8}return o},up=function(e){return n(this,He).serialize(e,b(this,V,oo).call(this))},fp=function(e,s){this._freeHighlight.add(s)&&e.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})},pp=function(e,s){this._freeHighlight.isEmpty()?e.drawLayer.remove(this._freeHighlightId):e.createAndAddNewEditor(s,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""},gp=function(e){const{color:s}=this._initialData;return e.color.some((i,r)=>i!==s[r])},m(J,Ue),R(J,"_defaultColor",null),R(J,"_defaultOpacity",1),R(J,"_defaultThickness",12),R(J,"_type","highlight"),R(J,"_editorType",z.HIGHLIGHT),R(J,"_freeHighlightId",-1),R(J,"_freeHighlight",null),R(J,"_freeHighlightClipId","");let Nh=J;var sr;class mp{constructor(){m(this,sr,Object.create(null))}updateProperty(t,e){this[t]=e,this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,s]of Object.entries(t))e.startsWith("_")||this.updateProperty(e,s)}updateSVGProperty(t,e){n(this,sr)[t]=e}toSVGProperties(){const t=n(this,sr);return p(this,sr,Object.create(null)),{root:t}}reset(){p(this,sr,Object.create(null))}updateAll(t=this){this.updateProperties(t)}clone(){rt("Not implemented")}}sr=new WeakMap;var Ce,Da,Dt,ir,nr,Ui,Vi,ji,rr,K,yd,wd,vd,lo,bp,vh,ho,Ir;const F=class F extends lt{constructor(e){super(e);m(this,K);m(this,Ce,null);m(this,Da);R(this,"_drawId",null);p(this,Da,e.mustBeCommitted||!1),this._addOutlines(e)}_addOutlines(e){e.drawOutlines&&(b(this,K,yd).call(this,e),b(this,K,lo).call(this))}static _mergeSVGProperties(e,s){const i=new Set(Object.keys(e));for(const[r,a]of Object.entries(s))i.has(r)?Object.assign(e[r],a):e[r]=a;return e}static getDefaultDrawingOptions(e){rt("Not implemented")}static get typesMap(){rt("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(e,s){const i=this.typesMap.get(e);i&&this._defaultDrawingOptions.updateProperty(i,s),this._currentParent&&(n(F,Dt).updateProperty(i,s),this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}updateParams(e,s){const i=this.constructor.typesMap.get(e);i&&this._updateProperty(e,i,s)}static get defaultPropertiesToUpdate(){const e=[],s=this._defaultDrawingOptions;for(const[i,r]of this.typesMap)e.push([i,s[r]]);return e}get propertiesToUpdate(){const e=[],{_drawingOptions:s}=this;for(const[i,r]of this.constructor.typesMap)e.push([i,s[r]]);return e}_updateProperty(e,s,i){const r=this._drawingOptions,a=r[s],o=l=>{var c;r.updateProperty(s,l);const h=n(this,Ce).updateProperty(s,l);h&&b(this,K,ho).call(this,h),(c=this.parent)==null||c.drawLayer.updateProperties(this._drawId,r.toSVGProperties())};this.addCommands({cmd:o.bind(this,i),undo:o.bind(this,a),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:e,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){var e;(e=this.parent)==null||e.drawLayer.updateProperties(this._drawId,F._mergeSVGProperties(n(this,Ce).getPathResizingSVGProperties(b(this,K,vh).call(this)),{bbox:b(this,K,Ir).call(this)}))}_onResized(){var e;(e=this.parent)==null||e.drawLayer.updateProperties(this._drawId,F._mergeSVGProperties(n(this,Ce).getPathResizedSVGProperties(b(this,K,vh).call(this)),{bbox:b(this,K,Ir).call(this)}))}_onTranslating(e,s){var i;(i=this.parent)==null||i.drawLayer.updateProperties(this._drawId,{bbox:b(this,K,Ir).call(this)})}_onTranslated(){var e;(e=this.parent)==null||e.drawLayer.updateProperties(this._drawId,F._mergeSVGProperties(n(this,Ce).getPathTranslatedSVGProperties(b(this,K,vh).call(this),this.parentDimensions),{bbox:b(this,K,Ir).call(this)}))}_onStartDragging(){var e;(e=this.parent)==null||e.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){var e;(e=this.parent)==null||e.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit(),this.disableEditMode(),this.disableEditing()}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(e){this.annotationElementId||this.parent.addUndoableEditor(this),this._isDraggable=!0,n(this,Da)&&(p(this,Da,!1),this.commit(),this.parent.setSelected(this),e&&this.isOnScreen&&this.div.focus())}remove(){b(this,K,vd).call(this),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(b(this,K,lo).call(this),b(this,K,ho).call(this,n(this,Ce).box),this.isAttachedToDOM||this.parent.add(this)))}setParent(e){var i;let s=!1;this.parent&&!e?(this._uiManager.removeShouldRescale(this),b(this,K,vd).call(this)):e&&(this._uiManager.addShouldRescale(this),b(this,K,lo).call(this,e),s=!this.parent&&((i=this.div)==null?void 0:i.classList.contains("selectedEditor"))),super.setParent(e),s&&this.select()}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,F._mergeSVGProperties({bbox:b(this,K,Ir).call(this)},n(this,Ce).updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&b(this,K,ho).call(this,n(this,Ce).updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;let e,s;this._isCopy&&(e=this.x,s=this.y);const i=super.render();i.classList.add("draw");const r=document.createElement("div");i.append(r),r.setAttribute("aria-hidden","true"),r.className="internal";const[a,o]=this.parentDimensions;return this.setDims(this.width*a,this.height*o),this._uiManager.addShouldRescale(this),this.disableEditing(),this._isCopy&&this._moveAfterPaste(e,s),i}static createDrawerInstance(e,s,i,r,a){rt("Not implemented")}static startDrawing(e,s,i,r){var w;const{target:a,offsetX:o,offsetY:l,pointerId:h,pointerType:c}=r;if(n(F,Vi)&&n(F,Vi)!==c)return;const{viewport:{rotation:u}}=e,{width:f,height:g}=a.getBoundingClientRect(),y=p(F,ir,new AbortController),A=e.combinedSignal(y);if(n(F,Ui)||p(F,Ui,h),n(F,Vi)??p(F,Vi,c),window.addEventListener("pointerup",v=>{var _;n(F,Ui)===v.pointerId?this._endDraw(v):(_=n(F,ji))==null||_.delete(v.pointerId)},{signal:A}),window.addEventListener("pointercancel",v=>{var _;n(F,Ui)===v.pointerId?this._currentParent.endDrawingSession():(_=n(F,ji))==null||_.delete(v.pointerId)},{signal:A}),window.addEventListener("pointerdown",v=>{n(F,Vi)===v.pointerType&&((n(F,ji)||p(F,ji,new Set)).add(v.pointerId),n(F,Dt).isCancellable()&&(n(F,Dt).removeLastElement(),n(F,Dt).isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)))},{capture:!0,passive:!1,signal:A}),window.addEventListener("contextmenu",rs,{signal:A}),a.addEventListener("pointermove",this._drawMove.bind(this),{signal:A}),a.addEventListener("touchmove",v=>{v.timeStamp===n(F,rr)&&St(v)},{signal:A}),e.toggleDrawing(),(w=s._editorUndoBar)==null||w.hide(),n(F,Dt)){e.drawLayer.updateProperties(this._currentDrawId,n(F,Dt).startNew(o,l,f,g,u));return}s.updateUIForDefaultProperties(this),p(F,Dt,this.createDrawerInstance(o,l,f,g,u)),p(F,nr,this.getDefaultDrawingOptions()),this._currentParent=e,{id:this._currentDrawId}=e.drawLayer.draw(this._mergeSVGProperties(n(F,nr).toSVGProperties(),n(F,Dt).defaultSVGProperties),!0,!1)}static _drawMove(e){var a;if(p(F,rr,-1),!n(F,Dt))return;const{offsetX:s,offsetY:i,pointerId:r}=e;if(n(F,Ui)===r){if(((a=n(F,ji))==null?void 0:a.size)>=1){this._endDraw(e);return}this._currentParent.drawLayer.updateProperties(this._currentDrawId,n(F,Dt).add(s,i)),p(F,rr,e.timeStamp),St(e)}}static _cleanup(e){e&&(this._currentDrawId=-1,this._currentParent=null,p(F,Dt,null),p(F,nr,null),p(F,Vi,null),p(F,rr,NaN)),n(F,ir)&&(n(F,ir).abort(),p(F,ir,null),p(F,Ui,NaN),p(F,ji,null))}static _endDraw(e){const s=this._currentParent;if(s){if(s.toggleDrawing(!0),this._cleanup(!1),(e==null?void 0:e.target)===s.div&&s.drawLayer.updateProperties(this._currentDrawId,n(F,Dt).end(e.offsetX,e.offsetY)),this.supportMultipleDrawings){const i=n(F,Dt),r=this._currentDrawId,a=i.getLastElement();s.addCommands({cmd:()=>{s.drawLayer.updateProperties(r,i.setLastElement(a))},undo:()=>{s.drawLayer.updateProperties(r,i.removeLastElement())},mustExec:!1,type:Y.DRAW_STEP});return}this.endDrawing(!1)}}static endDrawing(e){const s=this._currentParent;if(!s)return null;if(s.toggleDrawing(!0),s.cleanUndoStack(Y.DRAW_STEP),!n(F,Dt).isEmpty()){const{pageDimensions:[i,r],scale:a}=s,o=s.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:n(F,Dt).getOutlines(i*a,r*a,a,this._INNER_MARGIN),drawingOptions:n(F,nr),mustBeCommitted:!e});return this._cleanup(!0),o}return s.drawLayer.remove(this._currentDrawId),this._cleanup(!0),null}createDrawingOptions(e){}static deserializeDraw(e,s,i,r,a,o){rt("Not implemented")}static async deserialize(e,s,i){var u,f;const{rawDims:{pageWidth:r,pageHeight:a,pageX:o,pageY:l}}=s.viewport,h=this.deserializeDraw(o,l,r,a,this._INNER_MARGIN,e),c=await super.deserialize(e,s,i);return c.createDrawingOptions(e),b(u=c,K,yd).call(u,{drawOutlines:h}),b(f=c,K,lo).call(f),c.onScaleChanging(),c.rotate(),c}serializeDraw(e){const[s,i]=this.pageTranslation,[r,a]=this.pageDimensions;return n(this,Ce).serialize([s,i,r,a],e)}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}};Ce=new WeakMap,Da=new WeakMap,Dt=new WeakMap,ir=new WeakMap,nr=new WeakMap,Ui=new WeakMap,Vi=new WeakMap,ji=new WeakMap,rr=new WeakMap,K=new WeakSet,yd=function({drawOutlines:e,drawId:s,drawingOptions:i}){p(this,Ce,e),this._drawingOptions||(this._drawingOptions=i),s>=0?(this._drawId=s,this.parent.drawLayer.finalizeDraw(s,e.defaultProperties)):this._drawId=b(this,K,wd).call(this,e,this.parent),b(this,K,ho).call(this,e.box)},wd=function(e,s){const{id:i}=s.drawLayer.draw(F._mergeSVGProperties(this._drawingOptions.toSVGProperties(),e.defaultSVGProperties),!1,!1);return i},vd=function(){this._drawId===null||!this.parent||(this.parent.drawLayer.remove(this._drawId),this._drawId=null,this._drawingOptions.reset())},lo=function(e=this.parent){if(!(this._drawId!==null&&this.parent===e)){if(this._drawId!==null){this.parent.drawLayer.updateParent(this._drawId,e.drawLayer);return}this._drawingOptions.updateAll(),this._drawId=b(this,K,wd).call(this,n(this,Ce),e)}},bp=function([e,s,i,r]){const{parentDimensions:[a,o],rotation:l}=this;switch(l){case 90:return[s,1-e,i*(o/a),r*(a/o)];case 180:return[1-e,1-s,i,r];case 270:return[1-s,e,i*(o/a),r*(a/o)];default:return[e,s,i,r]}},vh=function(){const{x:e,y:s,width:i,height:r,parentDimensions:[a,o],rotation:l}=this;switch(l){case 90:return[1-s,e,i*(a/o),r*(o/a)];case 180:return[1-e,1-s,i,r];case 270:return[s,1-e,i*(a/o),r*(o/a)];default:return[e,s,i,r]}},ho=function(e){if([this.x,this.y,this.width,this.height]=b(this,K,bp).call(this,e),this.div){this.fixAndSetPosition();const[s,i]=this.parentDimensions;this.setDims(this.width*s,this.height*i)}this._onResized()},Ir=function(){const{x:e,y:s,width:i,height:r,rotation:a,parentRotation:o,parentDimensions:[l,h]}=this;switch((a*4+o)/90){case 1:return[1-s-r,e,r,i];case 2:return[1-e-i,1-s-r,i,r];case 3:return[s,1-e-i,r,i];case 4:return[e,s-i*(l/h),r*(h/l),i*(l/h)];case 5:return[1-s,e,i*(l/h),r*(h/l)];case 6:return[1-e-r*(h/l),1-s,r*(h/l),i*(l/h)];case 7:return[s-i*(l/h),1-e-r*(h/l),i*(l/h),r*(h/l)];case 8:return[e-i,s-r,i,r];case 9:return[1-s,e-i,r,i];case 10:return[1-e,1-s,i,r];case 11:return[s-r,1-e,r,i];case 12:return[e-r*(h/l),s,r*(h/l),i*(l/h)];case 13:return[1-s-i*(l/h),e-r*(h/l),i*(l/h),r*(h/l)];case 14:return[1-e,1-s-i*(l/h),r*(h/l),i*(l/h)];case 15:return[s,1-e,i*(l/h),r*(h/l)];default:return[e,s,i,r]}},R(F,"_currentDrawId",-1),R(F,"_currentParent",null),m(F,Dt,null),m(F,ir,null),m(F,nr,null),m(F,Ui,NaN),m(F,Vi,null),m(F,ji,null),m(F,rr,NaN),R(F,"_INNER_MARGIN",3);let Oh=F;var vs,Ft,Nt,ar,Fa,ie,zt,Ge,or,lr,hr,Na,_h;class Cm{constructor(t,e,s,i,r,a){m(this,Na);m(this,vs,new Float64Array(6));m(this,Ft);m(this,Nt);m(this,ar);m(this,Fa);m(this,ie);m(this,zt,"");m(this,Ge,0);m(this,or,new Vl);m(this,lr);m(this,hr);p(this,lr,s),p(this,hr,i),p(this,ar,r),p(this,Fa,a),[t,e]=b(this,Na,_h).call(this,t,e);const o=p(this,Ft,[NaN,NaN,NaN,NaN,t,e]);p(this,ie,[t,e]),p(this,Nt,[{line:o,points:n(this,ie)}]),n(this,vs).set(o,0)}updateProperty(t,e){t==="stroke-width"&&p(this,Fa,e)}isEmpty(){return!n(this,Nt)||n(this,Nt).length===0}isCancellable(){return n(this,ie).length<=10}add(t,e){[t,e]=b(this,Na,_h).call(this,t,e);const[s,i,r,a]=n(this,vs).subarray(2,6),o=t-r,l=e-a;return Math.hypot(n(this,lr)*o,n(this,hr)*l)<=2?null:(n(this,ie).push(t,e),isNaN(s)?(n(this,vs).set([r,a,t,e],2),n(this,Ft).push(NaN,NaN,NaN,NaN,t,e),{path:{d:this.toSVGPath()}}):(isNaN(n(this,vs)[0])&&n(this,Ft).splice(6,6),n(this,vs).set([s,i,r,a,t,e],0),n(this,Ft).push(...L.createBezierPoints(s,i,r,a,t,e)),{path:{d:this.toSVGPath()}}))}end(t,e){const s=this.add(t,e);return s||(n(this,ie).length===2?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,s,i,r){p(this,lr,s),p(this,hr,i),p(this,ar,r),[t,e]=b(this,Na,_h).call(this,t,e);const a=p(this,Ft,[NaN,NaN,NaN,NaN,t,e]);p(this,ie,[t,e]);const o=n(this,Nt).at(-1);return o&&(o.line=new Float32Array(o.line),o.points=new Float32Array(o.points)),n(this,Nt).push({line:a,points:n(this,ie)}),n(this,vs).set(a,0),p(this,Ge,0),this.toSVGPath(),null}getLastElement(){return n(this,Nt).at(-1)}setLastElement(t){return n(this,Nt)?(n(this,Nt).push(t),p(this,Ft,t.line),p(this,ie,t.points),p(this,Ge,0),{path:{d:this.toSVGPath()}}):n(this,or).setLastElement(t)}removeLastElement(){if(!n(this,Nt))return n(this,or).removeLastElement();n(this,Nt).pop(),p(this,zt,"");for(let t=0,e=n(this,Nt).length;t<e;t++){const{line:s,points:i}=n(this,Nt)[t];p(this,Ft,s),p(this,ie,i),p(this,Ge,0),this.toSVGPath()}return{path:{d:n(this,zt)}}}toSVGPath(){const t=L.svgRound(n(this,Ft)[4]),e=L.svgRound(n(this,Ft)[5]);if(n(this,ie).length===2)return p(this,zt,`${n(this,zt)} M ${t} ${e} Z`),n(this,zt);if(n(this,ie).length<=6){const i=n(this,zt).lastIndexOf("M");p(this,zt,`${n(this,zt).slice(0,i)} M ${t} ${e}`),p(this,Ge,6)}if(n(this,ie).length===4){const i=L.svgRound(n(this,Ft)[10]),r=L.svgRound(n(this,Ft)[11]);return p(this,zt,`${n(this,zt)} L ${i} ${r}`),p(this,Ge,12),n(this,zt)}const s=[];n(this,Ge)===0&&(s.push(`M ${t} ${e}`),p(this,Ge,6));for(let i=n(this,Ge),r=n(this,Ft).length;i<r;i+=6){const[a,o,l,h,c,u]=n(this,Ft).slice(i,i+6).map(L.svgRound);s.push(`C${a} ${o} ${l} ${h} ${c} ${u}`)}return p(this,zt,n(this,zt)+s.join(" ")),p(this,Ge,n(this,Ft).length),n(this,zt)}getOutlines(t,e,s,i){const r=n(this,Nt).at(-1);return r.line=new Float32Array(r.line),r.points=new Float32Array(r.points),n(this,or).build(n(this,Nt),t,e,s,n(this,ar),n(this,Fa),i),p(this,vs,null),p(this,Ft,null),p(this,Nt,null),p(this,zt,null),n(this,or)}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}vs=new WeakMap,Ft=new WeakMap,Nt=new WeakMap,ar=new WeakMap,Fa=new WeakMap,ie=new WeakMap,zt=new WeakMap,Ge=new WeakMap,or=new WeakMap,lr=new WeakMap,hr=new WeakMap,Na=new WeakSet,_h=function(t,e){return L._normalizePoint(t,e,n(this,lr),n(this,hr),n(this,ar))};var ne,Fl,Nl,xe,_s,Ss,Oa,Ba,cr,Vt,ks,Ap,yp,wp;class Vl extends L{constructor(){super(...arguments);m(this,Vt);m(this,ne);m(this,Fl,0);m(this,Nl);m(this,xe);m(this,_s);m(this,Ss);m(this,Oa);m(this,Ba);m(this,cr)}build(e,s,i,r,a,o,l){p(this,_s,s),p(this,Ss,i),p(this,Oa,r),p(this,Ba,a),p(this,cr,o),p(this,Nl,l??0),p(this,xe,e),b(this,Vt,yp).call(this)}get thickness(){return n(this,cr)}setLastElement(e){return n(this,xe).push(e),{path:{d:this.toSVGPath()}}}removeLastElement(){return n(this,xe).pop(),{path:{d:this.toSVGPath()}}}toSVGPath(){const e=[];for(const{line:s}of n(this,xe)){if(e.push(`M${L.svgRound(s[4])} ${L.svgRound(s[5])}`),s.length===6){e.push("Z");continue}if(s.length===12&&isNaN(s[6])){e.push(`L${L.svgRound(s[10])} ${L.svgRound(s[11])}`);continue}for(let i=6,r=s.length;i<r;i+=6){const[a,o,l,h,c,u]=s.subarray(i,i+6).map(L.svgRound);e.push(`C${a} ${o} ${l} ${h} ${c} ${u}`)}}return e.join("")}serialize([e,s,i,r],a){const o=[],l=[],[h,c,u,f]=b(this,Vt,Ap).call(this);let g,y,A,w,v,_,S,E,C;switch(n(this,Ba)){case 0:C=L._rescale,g=e,y=s+r,A=i,w=-r,v=e+h*i,_=s+(1-c-f)*r,S=e+(h+u)*i,E=s+(1-c)*r;break;case 90:C=L._rescaleAndSwap,g=e,y=s,A=i,w=r,v=e+c*i,_=s+h*r,S=e+(c+f)*i,E=s+(h+u)*r;break;case 180:C=L._rescale,g=e+i,y=s,A=-i,w=r,v=e+(1-h-u)*i,_=s+c*r,S=e+(1-h)*i,E=s+(c+f)*r;break;case 270:C=L._rescaleAndSwap,g=e+i,y=s+r,A=-i,w=-r,v=e+(1-c-f)*i,_=s+(1-h-u)*r,S=e+(1-c)*i,E=s+(1-h)*r;break}for(const{line:x,points:T}of n(this,xe))o.push(C(x,g,y,A,w,a?new Array(x.length):null)),l.push(C(T,g,y,A,w,a?new Array(T.length):null));return{lines:o,points:l,rect:[v,_,S,E]}}static deserialize(e,s,i,r,a,{paths:{lines:o,points:l},rotation:h,thickness:c}){const u=[];let f,g,y,A,w;switch(h){case 0:w=L._rescale,f=-e/i,g=s/r+1,y=1/i,A=-1/r;break;case 90:w=L._rescaleAndSwap,f=-s/r,g=-e/i,y=1/r,A=1/i;break;case 180:w=L._rescale,f=e/i+1,g=-s/r,y=-1/i,A=1/r;break;case 270:w=L._rescaleAndSwap,f=s/r+1,g=e/i+1,y=-1/r,A=-1/i;break}if(!o){o=[];for(const _ of l){const S=_.length;if(S===2){o.push(new Float32Array([NaN,NaN,NaN,NaN,_[0],_[1]]));continue}if(S===4){o.push(new Float32Array([NaN,NaN,NaN,NaN,_[0],_[1],NaN,NaN,NaN,NaN,_[2],_[3]]));continue}const E=new Float32Array(3*(S-2));o.push(E);let[C,x,T,P]=_.subarray(0,4);E.set([NaN,NaN,NaN,NaN,C,x],0);for(let k=4;k<S;k+=2){const B=_[k],D=_[k+1];E.set(L.createBezierPoints(C,x,T,P,B,D),(k-2)*3),[C,x,T,P]=[T,P,B,D]}}}for(let _=0,S=o.length;_<S;_++)u.push({line:w(o[_].map(E=>E??NaN),f,g,y,A),points:w(l[_].map(E=>E??NaN),f,g,y,A)});const v=new this.prototype.constructor;return v.build(u,i,r,1,h,c,a),v}get box(){return n(this,ne)}updateProperty(e,s){return e==="stroke-width"?b(this,Vt,wp).call(this,s):null}updateParentDimensions([e,s],i){const[r,a]=b(this,Vt,ks).call(this);p(this,_s,e),p(this,Ss,s),p(this,Oa,i);const[o,l]=b(this,Vt,ks).call(this),h=o-r,c=l-a,u=n(this,ne);return u[0]-=h,u[1]-=c,u[2]+=2*h,u[3]+=2*c,u}updateRotation(e){return p(this,Fl,e),{path:{transform:this.rotationTransform}}}get viewBox(){return n(this,ne).map(L.svgRound).join(" ")}get defaultProperties(){const[e,s]=n(this,ne);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${L.svgRound(e)} ${L.svgRound(s)}`}}}get rotationTransform(){const[,,e,s]=n(this,ne);let i=0,r=0,a=0,o=0,l=0,h=0;switch(n(this,Fl)){case 90:r=s/e,a=-e/s,l=e;break;case 180:i=-1,o=-1,l=e,h=s;break;case 270:r=-s/e,a=e/s,h=s;break;default:return""}return`matrix(${i} ${r} ${a} ${o} ${L.svgRound(l)} ${L.svgRound(h)})`}getPathResizingSVGProperties([e,s,i,r]){const[a,o]=b(this,Vt,ks).call(this),[l,h,c,u]=n(this,ne);if(Math.abs(c-a)<=L.PRECISION||Math.abs(u-o)<=L.PRECISION){const w=e+i/2-(l+c/2),v=s+r/2-(h+u/2);return{path:{"transform-origin":`${L.svgRound(e)} ${L.svgRound(s)}`,transform:`${this.rotationTransform} translate(${w} ${v})`}}}const f=(i-2*a)/(c-2*a),g=(r-2*o)/(u-2*o),y=c/i,A=u/r;return{path:{"transform-origin":`${L.svgRound(l)} ${L.svgRound(h)}`,transform:`${this.rotationTransform} scale(${y} ${A}) translate(${L.svgRound(a)} ${L.svgRound(o)}) scale(${f} ${g}) translate(${L.svgRound(-a)} ${L.svgRound(-o)})`}}}getPathResizedSVGProperties([e,s,i,r]){const[a,o]=b(this,Vt,ks).call(this),l=n(this,ne),[h,c,u,f]=l;if(l[0]=e,l[1]=s,l[2]=i,l[3]=r,Math.abs(u-a)<=L.PRECISION||Math.abs(f-o)<=L.PRECISION){const v=e+i/2-(h+u/2),_=s+r/2-(c+f/2);for(const{line:S,points:E}of n(this,xe))L._translate(S,v,_,S),L._translate(E,v,_,E);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${L.svgRound(e)} ${L.svgRound(s)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const g=(i-2*a)/(u-2*a),y=(r-2*o)/(f-2*o),A=-g*(h+a)+e+a,w=-y*(c+o)+s+o;if(g!==1||y!==1||A!==0||w!==0)for(const{line:v,points:_}of n(this,xe))L._rescale(v,A,w,g,y,v),L._rescale(_,A,w,g,y,_);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${L.svgRound(e)} ${L.svgRound(s)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([e,s],i){const[r,a]=i,o=n(this,ne),l=e-o[0],h=s-o[1];if(n(this,_s)===r&&n(this,Ss)===a)for(const{line:c,points:u}of n(this,xe))L._translate(c,l,h,c),L._translate(u,l,h,u);else{const c=n(this,_s)/r,u=n(this,Ss)/a;p(this,_s,r),p(this,Ss,a);for(const{line:f,points:g}of n(this,xe))L._rescale(f,l,h,c,u,f),L._rescale(g,l,h,c,u,g);o[2]*=c,o[3]*=u}return o[0]=e,o[1]=s,{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${L.svgRound(e)} ${L.svgRound(s)}`}}}get defaultSVGProperties(){const e=n(this,ne);return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${L.svgRound(e[0])} ${L.svgRound(e[1])}`,transform:this.rotationTransform||null},bbox:e}}}ne=new WeakMap,Fl=new WeakMap,Nl=new WeakMap,xe=new WeakMap,_s=new WeakMap,Ss=new WeakMap,Oa=new WeakMap,Ba=new WeakMap,cr=new WeakMap,Vt=new WeakSet,ks=function(e=n(this,cr)){const s=n(this,Nl)+e/2*n(this,Oa);return n(this,Ba)%180===0?[s/n(this,_s),s/n(this,Ss)]:[s/n(this,Ss),s/n(this,_s)]},Ap=function(){const[e,s,i,r]=n(this,ne),[a,o]=b(this,Vt,ks).call(this,0);return[e+a,s+o,i-2*a,r-2*o]},yp=function(){const e=p(this,ne,new Float32Array([1/0,1/0,-1/0,-1/0]));for(const{line:r}of n(this,xe)){if(r.length<=12){for(let l=4,h=r.length;l<h;l+=6)O.pointBoundingBox(r[l],r[l+1],e);continue}let a=r[4],o=r[5];for(let l=6,h=r.length;l<h;l+=6){const[c,u,f,g,y,A]=r.subarray(l,l+6);O.bezierBoundingBox(a,o,c,u,f,g,y,A,e),a=y,o=A}}const[s,i]=b(this,Vt,ks).call(this);e[0]=oe(e[0]-s,0,1),e[1]=oe(e[1]-i,0,1),e[2]=oe(e[2]+s,0,1),e[3]=oe(e[3]+i,0,1),e[2]-=e[0],e[3]-=e[1]},wp=function(e){const[s,i]=b(this,Vt,ks).call(this);p(this,cr,e);const[r,a]=b(this,Vt,ks).call(this),[o,l]=[r-s,a-i],h=n(this,ne);return h[0]-=o,h[1]-=l,h[2]+=2*o,h[3]+=2*l,h};class lc extends mp{constructor(t){super(),this._viewParameters=t,super.updateProperties({fill:"none",stroke:lt._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){t==="stroke-width"&&(e??(e=this["stroke-width"]),e*=this._viewParameters.realScale),super.updateSVGProperty(t,e)}clone(){const t=new lc(this._viewParameters);return t.updateAll(this),t}}var ec,vp;const Lr=class Lr extends Oh{constructor(e){super({...e,name:"inkEditor"});m(this,ec);this._willKeepAspectRatio=!0,this.defaultL10nId="pdfjs-editor-ink-editor"}static initialize(e,s){lt.initialize(e,s),this._defaultDrawingOptions=new lc(s.viewParameters)}static getDefaultDrawingOptions(e){const s=this._defaultDrawingOptions.clone();return s.updateProperties(e),s}static get supportMultipleDrawings(){return!0}static get typesMap(){return X(this,"typesMap",new Map([[Y.INK_THICKNESS,"stroke-width"],[Y.INK_COLOR,"stroke"],[Y.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(e,s,i,r,a){return new Cm(e,s,i,r,a,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(e,s,i,r,a,o){return Vl.deserialize(e,s,i,r,a,o)}static async deserialize(e,s,i){let r=null;if(e instanceof Wd){const{data:{inkLists:o,rect:l,rotation:h,id:c,color:u,opacity:f,borderStyle:{rawWidth:g},popupRef:y},parent:{page:{pageNumber:A}}}=e;r=e={annotationType:z.INK,color:Array.from(u),thickness:g,opacity:f,paths:{points:o},boxes:null,pageIndex:A-1,rect:l.slice(0),rotation:h,id:c,deleted:!1,popupRef:y}}const a=await super.deserialize(e,s,i);return a.annotationElementId=e.id||null,a._initialData=r,a}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:e,_drawingOptions:s,parent:i}=this;s.updateSVGProperty("stroke-width"),i.drawLayer.updateProperties(e,s.toSVGProperties())}static onScaleChangingWhenDrawing(){const e=this._currentParent;e&&(super.onScaleChangingWhenDrawing(),this._defaultDrawingOptions.updateSVGProperty("stroke-width"),e.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}createDrawingOptions({color:e,thickness:s,opacity:i}){this._drawingOptions=Lr.getDefaultDrawingOptions({stroke:O.makeHexColor(...e),"stroke-width":s,"stroke-opacity":i})}serialize(e=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:s,points:i,rect:r}=this.serializeDraw(e),{_drawingOptions:{stroke:a,"stroke-opacity":o,"stroke-width":l}}=this,h={annotationType:z.INK,color:lt._colorManager.convert(a),opacity:o,thickness:l,paths:{lines:s,points:i},pageIndex:this.pageIndex,rect:r,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?(h.isCopy=!0,h):this.annotationElementId&&!b(this,ec,vp).call(this,h)?null:(h.id=this.annotationElementId,h)}renderAnnotationElement(e){const{points:s,rect:i}=this.serializeDraw(!1);return e.updateEdited({rect:i,thickness:this._drawingOptions["stroke-width"],points:s}),null}};ec=new WeakSet,vp=function(e){const{color:s,thickness:i,opacity:r,pageIndex:a}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||e.color.some((o,l)=>o!==s[l])||e.thickness!==i||e.opacity!==r||e.pageIndex!==a},R(Lr,"_type","ink"),R(Lr,"_editorType",z.INK),R(Lr,"_defaultDrawingOptions",null);let _d=Lr;class Sd extends Vl{toSVGPath(){let t=super.toSVGPath();return t.endsWith("Z")||(t+="Z"),t}}const Ql=8,Xa=3;var dr,Z,Ed,es,_p,Sp,Cd,Sh,Ep,Cp,xp,xd,Td,Tp;class hi{static extractContoursFromText(t,{fontFamily:e,fontStyle:s,fontWeight:i},r,a,o,l){let h=new OffscreenCanvas(1,1),c=h.getContext("2d",{alpha:!1});const u=200,f=c.font=`${s} ${i} ${u}px ${e}`,{actualBoundingBoxLeft:g,actualBoundingBoxRight:y,actualBoundingBoxAscent:A,actualBoundingBoxDescent:w,fontBoundingBoxAscent:v,fontBoundingBoxDescent:_,width:S}=c.measureText(t),E=1.5,C=Math.ceil(Math.max(Math.abs(g)+Math.abs(y)||0,S)*E),x=Math.ceil(Math.max(Math.abs(A)+Math.abs(w)||u,Math.abs(v)+Math.abs(_)||u)*E);h=new OffscreenCanvas(C,x),c=h.getContext("2d",{alpha:!0,willReadFrequently:!0}),c.font=f,c.filter="grayscale(1)",c.fillStyle="white",c.fillRect(0,0,C,x),c.fillStyle="black",c.fillText(t,C*(E-1)/2,x*(3-E)/2);const T=b(this,Z,xd).call(this,c.getImageData(0,0,C,x).data),P=b(this,Z,xp).call(this,T),k=b(this,Z,Td).call(this,P),B=b(this,Z,Cd).call(this,T,C,x,k);return this.processDrawnLines({lines:{curves:B,width:C,height:x},pageWidth:r,pageHeight:a,rotation:o,innerMargin:l,mustSmooth:!0,areContours:!0})}static process(t,e,s,i,r){const[a,o,l]=b(this,Z,Tp).call(this,t),[h,c]=b(this,Z,Cp).call(this,a,o,l,Math.hypot(o,l)*n(this,dr).sigmaSFactor,n(this,dr).sigmaR,n(this,dr).kernelSize),u=b(this,Z,Td).call(this,c),f=b(this,Z,Cd).call(this,h,o,l,u);return this.processDrawnLines({lines:{curves:f,width:o,height:l},pageWidth:e,pageHeight:s,rotation:i,innerMargin:r,mustSmooth:!0,areContours:!0})}static processDrawnLines({lines:t,pageWidth:e,pageHeight:s,rotation:i,innerMargin:r,mustSmooth:a,areContours:o}){i%180!==0&&([e,s]=[s,e]);const{curves:l,width:h,height:c}=t,u=t.thickness??0,f=[],g=Math.min(e/h,s/c),y=g/e,A=g/s,w=[];for(const{points:_}of l){const S=a?b(this,Z,Ep).call(this,_):_;if(!S)continue;w.push(S);const E=S.length,C=new Float32Array(E),x=new Float32Array(3*(E===2?2:E-2));if(f.push({line:x,points:C}),E===2){C[0]=S[0]*y,C[1]=S[1]*A,x.set([NaN,NaN,NaN,NaN,C[0],C[1]],0);continue}let[T,P,k,B]=S;T*=y,P*=A,k*=y,B*=A,C.set([T,P,k,B],0),x.set([NaN,NaN,NaN,NaN,T,P],0);for(let D=4;D<E;D+=2){const tt=C[D]=S[D]*y,st=C[D+1]=S[D+1]*A;x.set(L.createBezierPoints(T,P,k,B,tt,st),(D-2)*3),[T,P,k,B]=[k,B,tt,st]}}if(f.length===0)return null;const v=o?new Sd:new Vl;return v.build(f,e,s,1,i,o?0:u,r),{outline:v,newCurves:w,areContours:o,thickness:u,width:h,height:c}}static async compressSignature({outlines:t,areContours:e,thickness:s,width:i,height:r}){let a=1/0,o=-1/0,l=0;for(const S of t){l+=S.length;for(let E=2,C=S.length;E<C;E++){const x=S[E]-S[E-2];a=Math.min(a,x),o=Math.max(o,x)}}let h;a>=-128&&o<=127?h=Int8Array:a>=-32768&&o<=32767?h=Int16Array:h=Int32Array;const c=t.length,u=Ql+Xa*c,f=new Uint32Array(u);let g=0;f[g++]=u*Uint32Array.BYTES_PER_ELEMENT+(l-2*c)*h.BYTES_PER_ELEMENT,f[g++]=0,f[g++]=i,f[g++]=r,f[g++]=e?0:1,f[g++]=Math.max(0,Math.floor(s??0)),f[g++]=c,f[g++]=h.BYTES_PER_ELEMENT;for(const S of t)f[g++]=S.length-2,f[g++]=S[0],f[g++]=S[1];const y=new CompressionStream("deflate-raw"),A=y.writable.getWriter();await A.ready,A.write(f);const w=h.prototype.constructor;for(const S of t){const E=new w(S.length-2);for(let C=2,x=S.length;C<x;C++)E[C-2]=S[C]-S[C-2];A.write(E)}A.close();const v=await new Response(y.readable).arrayBuffer(),_=new Uint8Array(v);return Pu(_)}static async decompressSignature(t){try{const e=Yp(t),{readable:s,writable:i}=new DecompressionStream("deflate-raw"),r=i.getWriter();await r.ready,r.write(e).then(async()=>{await r.ready,await r.close()}).catch(()=>{});let a=null,o=0;for await(const S of s)a||(a=new Uint8Array(new Uint32Array(S.buffer,0,4)[0])),a.set(S,o),o+=S.length;const l=new Uint32Array(a.buffer,0,a.length>>2),h=l[1];if(h!==0)throw new Error(`Invalid version: ${h}`);const c=l[2],u=l[3],f=l[4]===0,g=l[5],y=l[6],A=l[7],w=[],v=(Ql+Xa*y)*Uint32Array.BYTES_PER_ELEMENT;let _;switch(A){case Int8Array.BYTES_PER_ELEMENT:_=new Int8Array(a.buffer,v);break;case Int16Array.BYTES_PER_ELEMENT:_=new Int16Array(a.buffer,v);break;case Int32Array.BYTES_PER_ELEMENT:_=new Int32Array(a.buffer,v);break}o=0;for(let S=0;S<y;S++){const E=l[Xa*S+Ql],C=new Float32Array(E+2);w.push(C);for(let x=0;x<Xa-1;x++)C[x]=l[Xa*S+Ql+x+1];for(let x=0;x<E;x++)C[x+2]=C[x]+_[o++]}return{areContours:f,thickness:g,outlines:w,width:c,height:u}}catch(e){return U(`decompressSignature: ${e}`),null}}}dr=new WeakMap,Z=new WeakSet,Ed=function(t,e,s,i){return s-=t,i-=e,s===0?i>0?0:4:s===1?i+6:2-i},es=new WeakMap,_p=function(t,e,s,i,r,a,o){const l=b(this,Z,Ed).call(this,s,i,r,a);for(let h=0;h<8;h++){const c=(-h+l-o+16)%8,u=n(this,es)[2*c],f=n(this,es)[2*c+1];if(t[(s+u)*e+(i+f)]!==0)return c}return-1},Sp=function(t,e,s,i,r,a,o){const l=b(this,Z,Ed).call(this,s,i,r,a);for(let h=0;h<8;h++){const c=(h+l+o+16)%8,u=n(this,es)[2*c],f=n(this,es)[2*c+1];if(t[(s+u)*e+(i+f)]!==0)return c}return-1},Cd=function(t,e,s,i){const r=t.length,a=new Int32Array(r);for(let c=0;c<r;c++)a[c]=t[c]<=i?1:0;for(let c=1;c<s-1;c++)a[c*e]=a[c*e+e-1]=0;for(let c=0;c<e;c++)a[c]=a[e*s-1-c]=0;let o=1,l;const h=[];for(let c=1;c<s-1;c++){l=1;for(let u=1;u<e-1;u++){const f=c*e+u,g=a[f];if(g===0)continue;let y=c,A=u;if(g===1&&a[f-1]===0)o+=1,A-=1;else if(g>=1&&a[f+1]===0)o+=1,A+=1,g>1&&(l=g);else{g!==1&&(l=Math.abs(g));continue}const w=[u,c],v=A===u+1,_={isHole:v,points:w,id:o,parent:0};h.push(_);let S;for(const D of h)if(D.id===l){S=D;break}S?S.isHole?_.parent=v?S.parent:l:_.parent=v?l:S.parent:_.parent=v?l:0;const E=b(this,Z,_p).call(this,a,e,c,u,y,A,0);if(E===-1){a[f]=-o,a[f]!==1&&(l=Math.abs(a[f]));continue}let C=n(this,es)[2*E],x=n(this,es)[2*E+1];const T=c+C,P=u+x;y=T,A=P;let k=c,B=u;for(;;){const D=b(this,Z,Sp).call(this,a,e,k,B,y,A,1);C=n(this,es)[2*D],x=n(this,es)[2*D+1];const tt=k+C,st=B+x;w.push(st,tt);const q=k*e+B;if(a[q+1]===0?a[q]=-o:a[q]===1&&(a[q]=o),tt===c&&st===u&&k===T&&B===P){a[f]!==1&&(l=Math.abs(a[f]));break}else y=k,A=B,k=tt,B=st}}}return h},Sh=function(t,e,s,i){if(s-e<=4){for(let T=e;T<s-2;T+=2)i.push(t[T],t[T+1]);return}const r=t[e],a=t[e+1],o=t[s-4]-r,l=t[s-3]-a,h=Math.hypot(o,l),c=o/h,u=l/h,f=c*a-u*r,g=l/o,y=1/h,A=Math.atan(g),w=Math.cos(A),v=Math.sin(A),_=y*(Math.abs(w)+Math.abs(v)),S=y*(1-_+_**2),E=Math.max(Math.atan(Math.abs(v+w)*S),Math.atan(Math.abs(v-w)*S));let C=0,x=e;for(let T=e+2;T<s-2;T+=2){const P=Math.abs(f-c*t[T+1]+u*t[T]);P>C&&(x=T,C=P)}C>(h*E)**2?(b(this,Z,Sh).call(this,t,e,x+2,i),b(this,Z,Sh).call(this,t,x,s,i)):i.push(r,a)},Ep=function(t){const e=[],s=t.length;return b(this,Z,Sh).call(this,t,0,s,e),e.push(t[s-2],t[s-1]),e.length<=4?null:e},Cp=function(t,e,s,i,r,a){const o=new Float32Array(a**2),l=-2*i**2,h=a>>1;for(let A=0;A<a;A++){const w=(A-h)**2;for(let v=0;v<a;v++)o[A*a+v]=Math.exp((w+(v-h)**2)/l)}const c=new Float32Array(256),u=-2*r**2;for(let A=0;A<256;A++)c[A]=Math.exp(A**2/u);const f=t.length,g=new Uint8Array(f),y=new Uint32Array(256);for(let A=0;A<s;A++)for(let w=0;w<e;w++){const v=A*e+w,_=t[v];let S=0,E=0;for(let x=0;x<a;x++){const T=A+x-h;if(!(T<0||T>=s))for(let P=0;P<a;P++){const k=w+P-h;if(k<0||k>=e)continue;const B=t[T*e+k],D=o[x*a+P]*c[Math.abs(B-_)];S+=B*D,E+=D}}const C=g[v]=Math.round(S/E);y[C]++}return[g,y]},xp=function(t){const e=new Uint32Array(256);for(const s of t)e[s]++;return e},xd=function(t){const e=t.length,s=new Uint8ClampedArray(e>>2);let i=-1/0,r=1/0;for(let o=0,l=s.length;o<l;o++){if(t[(o<<2)+3]===0){i=s[o]=255;continue}const c=s[o]=t[o<<2];c>i&&(i=c),c<r&&(r=c)}const a=255/(i-r);for(let o=0;o<e;o++)s[o]=(s[o]-r)*a;return s},Td=function(t){let e,s=-1/0,i=-1/0;const r=t.findIndex(l=>l!==0);let a=r,o=r;for(e=r;e<256;e++){const l=t[e];l>s&&(e-a>i&&(i=e-a,o=e-1),s=l,a=e)}for(e=o-1;e>=0&&!(t[e]>t[e+1]);e--);return e},Tp=function(t){const e=t,{width:s,height:i}=t,{maxDim:r}=n(this,dr);let a=s,o=i;if(s>r||i>r){let f=s,g=i,y=Math.log2(Math.max(s,i)/r);const A=Math.floor(y);y=y===A?A-1:A;for(let v=0;v<y;v++){a=f,o=g,a>r&&(a=Math.ceil(a/2)),o>r&&(o=Math.ceil(o/2));const _=new OffscreenCanvas(a,o);_.getContext("2d").drawImage(t,0,0,f,g,0,0,a,o),f=a,g=o,t!==e&&t.close(),t=_.transferToImageBitmap()}const w=Math.min(r/a,r/o);a=Math.round(a*w),o=Math.round(o*w)}const h=new OffscreenCanvas(a,o).getContext("2d",{willReadFrequently:!0});h.filter="grayscale(1)",h.drawImage(t,0,0,t.width,t.height,0,0,a,o);const c=h.getImageData(0,0,a,o).data;return[b(this,Z,xd).call(this,c),a,o]},m(hi,Z),m(hi,dr,{maxDim:512,sigmaSFactor:.02,sigmaR:25,kernelSize:16}),m(hi,es,new Int32Array([0,1,-1,1,-1,0,-1,-1,0,-1,1,-1,1,0,1,1]));class qd extends mp{constructor(){super(),super.updateProperties({fill:lt._defaultLineColor,"stroke-width":0})}clone(){const t=new qd;return t.updateAll(this),t}}class Xd extends lc{constructor(t){super(t),super.updateProperties({stroke:lt._defaultLineColor,"stroke-width":1})}clone(){const t=new Xd(this._viewParameters);return t.updateAll(this),t}}var Wi,Te,qi,Xi;const ge=class ge extends Oh{constructor(e){super({...e,mustBeCommitted:!0,name:"signatureEditor"});m(this,Wi,!1);m(this,Te,null);m(this,qi,null);m(this,Xi,null);this._willKeepAspectRatio=!0,p(this,qi,e.signatureData||null),p(this,Te,null),this.defaultL10nId="pdfjs-editor-signature-editor1"}static initialize(e,s){lt.initialize(e,s),this._defaultDrawingOptions=new qd,this._defaultDrawnSignatureOptions=new Xd(s.viewParameters)}static getDefaultDrawingOptions(e){const s=this._defaultDrawingOptions.clone();return s.updateProperties(e),s}static get supportMultipleDrawings(){return!1}static get typesMap(){return X(this,"typesMap",new Map)}static get isDrawer(){return!1}get telemetryFinalData(){return{type:"signature",hasDescription:!!n(this,Te)}}static computeTelemetryFinalData(e){const s=e.get("hasDescription");return{hasAltText:s.get(!0)??0,hasNoAltText:s.get(!1)??0}}get isResizable(){return!0}onScaleChanging(){this._drawId!==null&&super.onScaleChanging()}render(){if(this.div)return this.div;let e,s;const{_isCopy:i}=this;if(i&&(this._isCopy=!1,e=this.x,s=this.y),super.render(),this._drawId===null)if(n(this,qi)){const{lines:r,mustSmooth:a,areContours:o,description:l,uuid:h,heightInPage:c}=n(this,qi),{rawDims:{pageWidth:u,pageHeight:f},rotation:g}=this.parent.viewport,y=hi.processDrawnLines({lines:r,pageWidth:u,pageHeight:f,rotation:g,innerMargin:ge._INNER_MARGIN,mustSmooth:a,areContours:o});this.addSignature(y,c,l,h)}else this.div.setAttribute("data-l10n-args",JSON.stringify({description:""})),this.div.hidden=!0,this._uiManager.getSignature(this);return i&&(this._isCopy=!0,this._moveAfterPaste(e,s)),this.div}setUuid(e){p(this,Xi,e),this.addEditToolbar()}getUuid(){return n(this,Xi)}get description(){return n(this,Te)}set description(e){p(this,Te,e),super.addEditToolbar().then(s=>{s==null||s.updateEditSignatureButton(e)})}getSignaturePreview(){const{newCurves:e,areContours:s,thickness:i,width:r,height:a}=n(this,qi),o=Math.max(r,a),l=hi.processDrawnLines({lines:{curves:e.map(h=>({points:h})),thickness:i,width:r,height:a},pageWidth:o,pageHeight:o,rotation:0,innerMargin:0,mustSmooth:!1,areContours:s});return{areContours:s,outline:l.outline}}async addEditToolbar(){const e=await super.addEditToolbar();return e?(this._uiManager.signatureManager&&n(this,Te)!==null&&(await e.addEditSignatureButton(this._uiManager.signatureManager,n(this,Xi),n(this,Te)),e.show()),e):null}addSignature(e,s,i,r){const{x:a,y:o}=this,{outline:l}=p(this,qi,e);p(this,Wi,l instanceof Sd),p(this,Te,i),this.div.setAttribute("data-l10n-args",JSON.stringify({description:i}));let h;n(this,Wi)?h=ge.getDefaultDrawingOptions():(h=ge._defaultDrawnSignatureOptions.clone(),h.updateProperties({"stroke-width":l.thickness})),this._addOutlines({drawOutlines:l,drawingOptions:h});const[c,u]=this.parentDimensions,[,f]=this.pageDimensions;let g=s/f;g=g>=1?.5:g,this.width*=g/this.height,this.width>=1&&(g*=.9/this.width,this.width=.9),this.height=g,this.setDims(c*this.width,u*this.height),this.x=a,this.y=o,this.center(),this._onResized(),this.onScaleChanging(),this.rotate(),this._uiManager.addToAnnotationStorage(this),this.setUuid(r),this._reportTelemetry({action:"pdfjs.signature.inserted",data:{hasBeenSaved:!!r,hasDescription:!!i}}),this.div.hidden=!1}getFromImage(e){const{rawDims:{pageWidth:s,pageHeight:i},rotation:r}=this.parent.viewport;return hi.process(e,s,i,r,ge._INNER_MARGIN)}getFromText(e,s){const{rawDims:{pageWidth:i,pageHeight:r},rotation:a}=this.parent.viewport;return hi.extractContoursFromText(e,s,i,r,a,ge._INNER_MARGIN)}getDrawnSignature(e){const{rawDims:{pageWidth:s,pageHeight:i},rotation:r}=this.parent.viewport;return hi.processDrawnLines({lines:e,pageWidth:s,pageHeight:i,rotation:r,innerMargin:ge._INNER_MARGIN,mustSmooth:!1,areContours:!1})}createDrawingOptions({areContours:e,thickness:s}){e?this._drawingOptions=ge.getDefaultDrawingOptions():(this._drawingOptions=ge._defaultDrawnSignatureOptions.clone(),this._drawingOptions.updateProperties({"stroke-width":s}))}serialize(e=!1){if(this.isEmpty())return null;const{lines:s,points:i,rect:r}=this.serializeDraw(e),{_drawingOptions:{"stroke-width":a}}=this,o={annotationType:z.SIGNATURE,isSignature:!0,areContours:n(this,Wi),color:[0,0,0],thickness:n(this,Wi)?0:a,pageIndex:this.pageIndex,rect:r,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?(o.paths={lines:s,points:i},o.uuid=n(this,Xi),o.isCopy=!0):o.lines=s,n(this,Te)&&(o.accessibilityData={type:"Figure",alt:n(this,Te)}),o}static deserializeDraw(e,s,i,r,a,o){return o.areContours?Sd.deserialize(e,s,i,r,a,o):Vl.deserialize(e,s,i,r,a,o)}static async deserialize(e,s,i){var a;const r=await super.deserialize(e,s,i);return p(r,Wi,e.areContours),p(r,Te,((a=e.accessibilityData)==null?void 0:a.alt)||""),p(r,Xi,e.uuid),r}};Wi=new WeakMap,Te=new WeakMap,qi=new WeakMap,Xi=new WeakMap,R(ge,"_type","signature"),R(ge,"_editorType",z.SIGNATURE),R(ge,"_defaultDrawingOptions",null);let Rd=ge;var dt,Ot,Yi,ii,Ki,Ha,ni,ur,Es,Re,$a,Q,co,uo,Eh,Ch,xh,Id,Th,Rp;class Pd extends lt{constructor(e){super({...e,name:"stampEditor"});m(this,Q);m(this,dt,null);m(this,Ot,null);m(this,Yi,null);m(this,ii,null);m(this,Ki,null);m(this,Ha,"");m(this,ni,null);m(this,ur,!1);m(this,Es,null);m(this,Re,!1);m(this,$a,!1);p(this,ii,e.bitmapUrl),p(this,Ki,e.bitmapFile),this.defaultL10nId="pdfjs-editor-stamp-editor"}static initialize(e,s){lt.initialize(e,s)}static isHandlingMimeForPasting(e){return _c.includes(e)}static paste(e,s){s.pasteEditor(z.STAMP,{bitmapFile:e.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1),super.altTextFinish()}get telemetryFinalData(){var e;return{type:"stamp",hasAltText:!!((e=this.altTextData)!=null&&e.altText)}}static computeTelemetryFinalData(e){const s=e.get("hasAltText");return{hasAltText:s.get(!0)??0,hasNoAltText:s.get(!1)??0}}async mlGuessAltText(e=null,s=!0){if(this.hasAltTextData())return null;const{mlManager:i}=this._uiManager;if(!i)throw new Error("No ML.");if(!await i.isEnabledFor("altText"))throw new Error("ML isn't enabled for alt text.");const{data:r,width:a,height:o}=e||this.copyCanvas(null,null,!0).imageData,l=await i.guess({name:"altText",request:{data:r,width:a,height:o,channels:r.length/(a*o)}});if(!l)throw new Error("No response from the AI service.");if(l.error)throw new Error("Error from the AI service.");if(l.cancel)return null;if(!l.output)throw new Error("No valid response from the AI service.");const h=l.output;return await this.setGuessedAltText(h),s&&!this.hasAltTextData()&&(this.altTextData={alt:h,decorative:!1}),h}remove(){var e;n(this,Ot)&&(p(this,dt,null),this._uiManager.imageManager.deleteId(n(this,Ot)),(e=n(this,ni))==null||e.remove(),p(this,ni,null),n(this,Es)&&(clearTimeout(n(this,Es)),p(this,Es,null))),super.remove()}rebuild(){if(!this.parent){n(this,Ot)&&b(this,Q,Eh).call(this);return}super.rebuild(),this.div!==null&&(n(this,Ot)&&n(this,ni)===null&&b(this,Q,Eh).call(this),this.isAttachedToDOM||this.parent.add(this))}onceAdded(e){this._isDraggable=!0,e&&this.div.focus()}isEmpty(){return!(n(this,Yi)||n(this,dt)||n(this,ii)||n(this,Ki)||n(this,Ot)||n(this,ur))}get isResizable(){return!0}render(){if(this.div)return this.div;let e,s;return this._isCopy&&(e=this.x,s=this.y),super.render(),this.div.hidden=!0,this.addAltTextButton(),n(this,ur)||(n(this,dt)?b(this,Q,Ch).call(this):b(this,Q,Eh).call(this)),this._isCopy&&this._moveAfterPaste(e,s),this._uiManager.addShouldRescale(this),this.div}setCanvas(e,s){const{id:i,bitmap:r}=this._uiManager.imageManager.getFromCanvas(e,s);s.remove(),i&&this._uiManager.imageManager.isValidId(i)&&(p(this,Ot,i),r&&p(this,dt,r),p(this,ur,!1),b(this,Q,Ch).call(this))}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;n(this,Es)!==null&&clearTimeout(n(this,Es)),p(this,Es,setTimeout(()=>{p(this,Es,null),b(this,Q,Id).call(this)},200))}copyCanvas(e,s,i=!1){var g;e||(e=224);const{width:r,height:a}=n(this,dt),o=new ui;let l=n(this,dt),h=r,c=a,u=null;if(s){if(r>s||a>s){const P=Math.min(s/r,s/a);h=Math.floor(r*P),c=Math.floor(a*P)}u=document.createElement("canvas");const y=u.width=Math.ceil(h*o.sx),A=u.height=Math.ceil(c*o.sy);n(this,Re)||(l=b(this,Q,xh).call(this,y,A));const w=u.getContext("2d");w.filter=this._uiManager.hcmFilter;let v="white",_="#cfcfd8";this._uiManager.hcmFilter!=="none"?_="black":(g=window.matchMedia)!=null&&g.call(window,"(prefers-color-scheme: dark)").matches&&(v="#8f8f9d",_="#42414d");const S=15,E=S*o.sx,C=S*o.sy,x=new OffscreenCanvas(E*2,C*2),T=x.getContext("2d");T.fillStyle=v,T.fillRect(0,0,E*2,C*2),T.fillStyle=_,T.fillRect(0,0,E,C),T.fillRect(E,C,E,C),w.fillStyle=w.createPattern(x,"repeat"),w.fillRect(0,0,y,A),w.drawImage(l,0,0,l.width,l.height,0,0,y,A)}let f=null;if(i){let y,A;if(o.symmetric&&l.width<e&&l.height<e)y=l.width,A=l.height;else if(l=n(this,dt),r>e||a>e){const _=Math.min(e/r,e/a);y=Math.floor(r*_),A=Math.floor(a*_),n(this,Re)||(l=b(this,Q,xh).call(this,y,A))}const v=new OffscreenCanvas(y,A).getContext("2d",{willReadFrequently:!0});v.drawImage(l,0,0,l.width,l.height,0,0,y,A),f={width:y,height:A,data:v.getImageData(0,0,y,A).data}}return{canvas:u,width:h,height:c,imageData:f}}static async deserialize(e,s,i){var w;let r=null,a=!1;if(e instanceof Uf){const{data:{rect:v,rotation:_,id:S,structParent:E,popupRef:C},container:x,parent:{page:{pageNumber:T}},canvas:P}=e;let k,B;P?(delete e.canvas,{id:k,bitmap:B}=i.imageManager.getFromCanvas(x.id,P),P.remove()):(a=!0,e._hasNoCanvas=!0);const D=((w=await s._structTree.getAriaAttributes(`${Od}${S}`))==null?void 0:w.get("aria-label"))||"";r=e={annotationType:z.STAMP,bitmapId:k,bitmap:B,pageIndex:T-1,rect:v.slice(0),rotation:_,id:S,deleted:!1,accessibilityData:{decorative:!1,altText:D},isSvg:!1,structParent:E,popupRef:C}}const o=await super.deserialize(e,s,i),{rect:l,bitmap:h,bitmapUrl:c,bitmapId:u,isSvg:f,accessibilityData:g}=e;a?(i.addMissingCanvas(e.id,o),p(o,ur,!0)):u&&i.imageManager.isValidId(u)?(p(o,Ot,u),h&&p(o,dt,h)):p(o,ii,c),p(o,Re,f);const[y,A]=o.pageDimensions;return o.width=(l[2]-l[0])/y,o.height=(l[3]-l[1])/A,o.annotationElementId=e.id||null,g&&(o.altTextData=g),o._initialData=r,p(o,$a,!!r),o}serialize(e=!1,s=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const i={annotationType:z.STAMP,bitmapId:n(this,Ot),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:n(this,Re),structTreeParentId:this._structTreeParentId};if(e)return i.bitmapUrl=b(this,Q,Th).call(this,!0),i.accessibilityData=this.serializeAltText(!0),i.isCopy=!0,i;const{decorative:r,altText:a}=this.serializeAltText(!1);if(!r&&a&&(i.accessibilityData={type:"Figure",alt:a}),this.annotationElementId){const l=b(this,Q,Rp).call(this,i);if(l.isSame)return null;l.isSameAltText?delete i.accessibilityData:i.accessibilityData.structParent=this._initialData.structParent??-1}if(i.id=this.annotationElementId,s===null)return i;s.stamps||(s.stamps=new Map);const o=n(this,Re)?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(!s.stamps.has(n(this,Ot)))s.stamps.set(n(this,Ot),{area:o,serialized:i}),i.bitmap=b(this,Q,Th).call(this,!1);else if(n(this,Re)){const l=s.stamps.get(n(this,Ot));o>l.area&&(l.area=o,l.serialized.bitmap.close(),l.serialized.bitmap=b(this,Q,Th).call(this,!1))}return i}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}}dt=new WeakMap,Ot=new WeakMap,Yi=new WeakMap,ii=new WeakMap,Ki=new WeakMap,Ha=new WeakMap,ni=new WeakMap,ur=new WeakMap,Es=new WeakMap,Re=new WeakMap,$a=new WeakMap,Q=new WeakSet,co=function(e,s=!1){if(!e){this.remove();return}p(this,dt,e.bitmap),s||(p(this,Ot,e.id),p(this,Re,e.isSvg)),e.file&&p(this,Ha,e.file.name),b(this,Q,Ch).call(this)},uo=function(){if(p(this,Yi,null),this._uiManager.enableWaiting(!1),!!n(this,ni)){if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&n(this,dt)){this._editToolbar.hide(),this._uiManager.editAltText(this,!0);return}if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&n(this,dt)){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}},Eh=function(){if(n(this,Ot)){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(n(this,Ot)).then(i=>b(this,Q,co).call(this,i,!0)).finally(()=>b(this,Q,uo).call(this));return}if(n(this,ii)){const i=n(this,ii);p(this,ii,null),this._uiManager.enableWaiting(!0),p(this,Yi,this._uiManager.imageManager.getFromUrl(i).then(r=>b(this,Q,co).call(this,r)).finally(()=>b(this,Q,uo).call(this)));return}if(n(this,Ki)){const i=n(this,Ki);p(this,Ki,null),this._uiManager.enableWaiting(!0),p(this,Yi,this._uiManager.imageManager.getFromFile(i).then(r=>b(this,Q,co).call(this,r)).finally(()=>b(this,Q,uo).call(this)));return}const e=document.createElement("input");e.type="file",e.accept=_c.join(",");const s=this._uiManager._signal;p(this,Yi,new Promise(i=>{e.addEventListener("change",async()=>{if(!e.files||e.files.length===0)this.remove();else{this._uiManager.enableWaiting(!0);const r=await this._uiManager.imageManager.getFromFile(e.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}}),b(this,Q,co).call(this,r)}i()},{signal:s}),e.addEventListener("cancel",()=>{this.remove(),i()},{signal:s})}).finally(()=>b(this,Q,uo).call(this))),e.click()},Ch=function(){var u;const{div:e}=this;let{width:s,height:i}=n(this,dt);const[r,a]=this.pageDimensions,o=.75;if(this.width)s=this.width*r,i=this.height*a;else if(s>o*r||i>o*a){const f=Math.min(o*r/s,o*a/i);s*=f,i*=f}const[l,h]=this.parentDimensions;this.setDims(s*l/r,i*h/a),this._uiManager.enableWaiting(!1);const c=p(this,ni,document.createElement("canvas"));c.setAttribute("role","img"),this.addContainer(c),this.width=s/r,this.height=i/a,(u=this._initialOptions)!=null&&u.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,(!this._uiManager.useNewAltTextWhenAddingImage||!this._uiManager.useNewAltTextFlow||this.annotationElementId)&&(e.hidden=!1),b(this,Q,Id).call(this),n(this,$a)||(this.parent.addUndoableEditor(this),p(this,$a,!0)),this._reportTelemetry({action:"inserted_image"}),n(this,Ha)&&this.div.setAttribute("aria-description",n(this,Ha))},xh=function(e,s){const{width:i,height:r}=n(this,dt);let a=i,o=r,l=n(this,dt);for(;a>2*e||o>2*s;){const h=a,c=o;a>2*e&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2)),o>2*s&&(o=o>=16384?Math.floor(o/2)-1:Math.ceil(o/2));const u=new OffscreenCanvas(a,o);u.getContext("2d").drawImage(l,0,0,h,c,0,0,a,o),l=u.transferToImageBitmap()}return l},Id=function(){const[e,s]=this.parentDimensions,{width:i,height:r}=this,a=new ui,o=Math.ceil(i*e*a.sx),l=Math.ceil(r*s*a.sy),h=n(this,ni);if(!h||h.width===o&&h.height===l)return;h.width=o,h.height=l;const c=n(this,Re)?n(this,dt):b(this,Q,xh).call(this,o,l),u=h.getContext("2d");u.filter=this._uiManager.hcmFilter,u.drawImage(c,0,0,c.width,c.height,0,0,o,l)},Th=function(e){if(e){if(n(this,Re)){const r=this._uiManager.imageManager.getSvgUrl(n(this,Ot));if(r)return r}const s=document.createElement("canvas");return{width:s.width,height:s.height}=n(this,dt),s.getContext("2d").drawImage(n(this,dt),0,0),s.toDataURL()}if(n(this,Re)){const[s,i]=this.pageDimensions,r=Math.round(this.width*s*en.PDF_TO_CSS_UNITS),a=Math.round(this.height*i*en.PDF_TO_CSS_UNITS),o=new OffscreenCanvas(r,a);return o.getContext("2d").drawImage(n(this,dt),0,0,n(this,dt).width,n(this,dt).height,0,0,r,a),o.transferToImageBitmap()}return structuredClone(n(this,dt))},Rp=function(e){var o;const{pageIndex:s,accessibilityData:{altText:i}}=this._initialData,r=e.pageIndex===s,a=(((o=e.accessibilityData)==null?void 0:o.alt)||"")===i;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&r&&a,isSameAltText:a}},R(Pd,"_type","stamp"),R(Pd,"_editorType",z.STAMP);var fr,Ga,Cs,Qi,ri,ze,Ji,za,pr,ss,ai,Jt,oi,H,Zi,ut,Pp,hs,Md,Ld,Rh;const je=class je{constructor({uiManager:t,pageIndex:e,div:s,structTreeLayer:i,accessibilityManager:r,annotationLayer:a,drawLayer:o,textLayer:l,viewport:h,l10n:c}){m(this,ut);m(this,fr);m(this,Ga,!1);m(this,Cs,null);m(this,Qi,null);m(this,ri,null);m(this,ze,new Map);m(this,Ji,!1);m(this,za,!1);m(this,pr,!1);m(this,ss,null);m(this,ai,null);m(this,Jt,null);m(this,oi,null);m(this,H);const u=[...n(je,Zi).values()];if(!je._initialized){je._initialized=!0;for(const f of u)f.initialize(c,t)}t.registerEditorTypes(u),p(this,H,t),this.pageIndex=e,this.div=s,p(this,fr,r),p(this,Cs,a),this.viewport=h,p(this,Jt,l),this.drawLayer=o,this._structTree=i,n(this,H).addLayer(this)}get isEmpty(){return n(this,ze).size===0}get isInvisible(){return this.isEmpty&&n(this,H).getMode()===z.NONE}updateToolbar(t){n(this,H).updateToolbar(t)}updateMode(t=n(this,H).getMode()){switch(b(this,ut,Rh).call(this),t){case z.NONE:this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),this.disableClick();return;case z.INK:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick();break;case z.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const s of n(je,Zi).values())e.toggle(`${s._type}Editing`,t===s._editorType);this.div.hidden=!1}hasTextLayer(t){var e;return t===((e=n(this,Jt))==null?void 0:e.div)}setEditingState(t){n(this,H).setEditingState(t)}addCommands(t){n(this,H).addCommands(t)}cleanUndoStack(t){n(this,H).cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){var e;(e=n(this,Cs))==null||e.div.classList.toggle("disabled",!t)}async enable(){p(this,pr,!0),this.div.tabIndex=0,this.togglePointerEvents(!0);const t=new Set;for(const s of n(this,ze).values())s.enableEditing(),s.show(!0),s.annotationElementId&&(n(this,H).removeChangedExistingAnnotation(s),t.add(s.annotationElementId));if(!n(this,Cs)){p(this,pr,!1);return}const e=n(this,Cs).getEditableAnnotations();for(const s of e){if(s.hide(),n(this,H).isDeletedAnnotationElement(s.data.id)||t.has(s.data.id))continue;const i=await this.deserialize(s);i&&(this.addOrRebuild(i),i.enableEditing())}p(this,pr,!1)}disable(){var i;p(this,za,!0),this.div.tabIndex=-1,this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const r of n(this,ze).values())if(r.disableEditing(),!!r.annotationElementId){if(r.serialize()!==null){t.set(r.annotationElementId,r);continue}else e.set(r.annotationElementId,r);(i=this.getEditableAnnotation(r.annotationElementId))==null||i.show(),r.remove()}if(n(this,Cs)){const r=n(this,Cs).getEditableAnnotations();for(const a of r){const{id:o}=a.data;if(n(this,H).isDeletedAnnotationElement(o))continue;let l=e.get(o);if(l){l.resetAnnotationElement(a),l.show(!1),a.show();continue}l=t.get(o),l&&(n(this,H).addChangedExistingAnnotation(l),l.renderAnnotationElement(a)&&l.show(!1)),a.show()}}b(this,ut,Rh).call(this),this.isEmpty&&(this.div.hidden=!0);const{classList:s}=this.div;for(const r of n(je,Zi).values())s.remove(`${r._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),p(this,za,!1)}getEditableAnnotation(t){var e;return((e=n(this,Cs))==null?void 0:e.getEditableAnnotation(t))||null}setActiveEditor(t){n(this,H).getActive()!==t&&n(this,H).setActiveEditor(t)}enableTextSelection(){var t;if(this.div.tabIndex=-1,(t=n(this,Jt))!=null&&t.div&&!n(this,oi)){p(this,oi,new AbortController);const e=n(this,H).combinedSignal(n(this,oi));n(this,Jt).div.addEventListener("pointerdown",b(this,ut,Pp).bind(this),{signal:e}),n(this,Jt).div.classList.add("highlighting")}}disableTextSelection(){var t;this.div.tabIndex=0,(t=n(this,Jt))!=null&&t.div&&n(this,oi)&&(n(this,oi).abort(),p(this,oi,null),n(this,Jt).div.classList.remove("highlighting"))}enableClick(){if(n(this,Qi))return;p(this,Qi,new AbortController);const t=n(this,H).combinedSignal(n(this,Qi));this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t}),this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){var t;(t=n(this,Qi))==null||t.abort(),p(this,Qi,null)}attach(t){n(this,ze).set(t.id,t);const{annotationElementId:e}=t;e&&n(this,H).isDeletedAnnotationElement(e)&&n(this,H).removeDeletedAnnotationElement(t)}detach(t){var e;n(this,ze).delete(t.id),(e=n(this,fr))==null||e.removePointerInTextLayer(t.contentDiv),!n(this,za)&&t.annotationElementId&&n(this,H).addDeletedAnnotationElement(t)}remove(t){this.detach(t),n(this,H).removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1}changeParent(t){var e;t.parent!==this&&(t.parent&&t.annotationElementId&&(n(this,H).addDeletedAnnotationElement(t.annotationElementId),lt.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),(e=t.parent)==null||e.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(!(t.parent===this&&t.isAttachedToDOM)){if(this.changeParent(t),n(this,H).addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(!n(this,pr)),n(this,H).addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){var s;if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&!n(this,ri)&&(t._focusEventsAllowed=!1,p(this,ri,setTimeout(()=>{p(this,ri,null),t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:n(this,H)._signal}),e.focus())},0))),t._structTreeParentId=(s=n(this,fr))==null?void 0:s.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||(t.parent=this),t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){const e=()=>t._uiManager.rebuild(t),s=()=>{t.remove()};this.addCommands({cmd:e,undo:s,mustExec:!1})}getNextId(){return n(this,H).getId()}combinedSignal(t){return n(this,H).combinedSignal(t)}canCreateNewEmptyEditor(){var t;return(t=n(this,ut,hs))==null?void 0:t.canCreateNewEmptyEditor()}async pasteEditor(t,e){n(this,H).updateToolbar(t),await n(this,H).updateMode(t);const{offsetX:s,offsetY:i}=b(this,ut,Ld).call(this),r=this.getNextId(),a=b(this,ut,Md).call(this,{parent:this,id:r,x:s,y:i,uiManager:n(this,H),isCentered:!0,...e});a&&this.add(a)}async deserialize(t){var e;return await((e=n(je,Zi).get(t.annotationType??t.annotationEditorType))==null?void 0:e.deserialize(t,this,n(this,H)))||null}createAndAddNewEditor(t,e,s={}){const i=this.getNextId(),r=b(this,ut,Md).call(this,{parent:this,id:i,x:t.offsetX,y:t.offsetY,uiManager:n(this,H),isCentered:e,...s});return r&&this.add(r),r}addNewEditor(t={}){this.createAndAddNewEditor(b(this,ut,Ld).call(this),!0,t)}setSelected(t){n(this,H).setSelected(t)}toggleSelected(t){n(this,H).toggleSelected(t)}unselect(t){n(this,H).unselect(t)}pointerup(t){var i;const{isMac:e}=Wt.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div||!n(this,Ji)||(p(this,Ji,!1),(i=n(this,ut,hs))!=null&&i.isDrawer&&n(this,ut,hs).supportMultipleDrawings))return;if(!n(this,Ga)){p(this,Ga,!0);return}const s=n(this,H).getMode();if(s===z.STAMP||s===z.SIGNATURE){n(this,H).unselectAll();return}this.createAndAddNewEditor(t,!1)}pointerdown(t){var i;if(n(this,H).getMode()===z.HIGHLIGHT&&this.enableTextSelection(),n(this,Ji)){p(this,Ji,!1);return}const{isMac:e}=Wt.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div)return;if(p(this,Ji,!0),(i=n(this,ut,hs))!=null&&i.isDrawer){this.startDrawingSession(t);return}const s=n(this,H).getActive();p(this,Ga,!s||s.isEmpty())}startDrawingSession(t){if(this.div.focus({preventScroll:!0}),n(this,ss)){n(this,ut,hs).startDrawing(this,n(this,H),!1,t);return}n(this,H).setCurrentDrawingSession(this),p(this,ss,new AbortController);const e=n(this,H).combinedSignal(n(this,ss));this.div.addEventListener("blur",({relatedTarget:s})=>{s&&!this.div.contains(s)&&(p(this,ai,null),this.commitOrRemove())},{signal:e}),n(this,ut,hs).startDrawing(this,n(this,H),!1,t)}pause(t){if(t){const{activeElement:e}=document;this.div.contains(e)&&p(this,ai,e);return}n(this,ai)&&setTimeout(()=>{var e;(e=n(this,ai))==null||e.focus(),p(this,ai,null)},0)}endDrawingSession(t=!1){return n(this,ss)?(n(this,H).setCurrentDrawingSession(null),n(this,ss).abort(),p(this,ss,null),p(this,ai,null),n(this,ut,hs).endDrawing(t)):null}findNewParent(t,e,s){const i=n(this,H).findParent(e,s);return i===null||i===this?!1:(i.changeParent(t),!0)}commitOrRemove(){return n(this,ss)?(this.endDrawingSession(),!0):!1}onScaleChanging(){n(this,ss)&&n(this,ut,hs).onScaleChangingWhenDrawing(this)}destroy(){var t,e;this.commitOrRemove(),((t=n(this,H).getActive())==null?void 0:t.parent)===this&&(n(this,H).commitOrRemove(),n(this,H).setActiveEditor(null)),n(this,ri)&&(clearTimeout(n(this,ri)),p(this,ri,null));for(const s of n(this,ze).values())(e=n(this,fr))==null||e.removePointerInTextLayer(s.contentDiv),s.setParent(null),s.isAttachedToDOM=!1,s.div.remove();this.div=null,n(this,ze).clear(),n(this,H).removeLayer(this)}render({viewport:t}){this.viewport=t,mr(this.div,t);for(const e of n(this,H).getEditors(this.pageIndex))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){n(this,H).commitOrRemove(),b(this,ut,Rh).call(this);const e=this.viewport.rotation,s=t.rotation;if(this.viewport=t,mr(this.div,{rotation:s}),e!==s)for(const i of n(this,ze).values())i.rotate(s)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return n(this,H).viewParameters.realScale}};fr=new WeakMap,Ga=new WeakMap,Cs=new WeakMap,Qi=new WeakMap,ri=new WeakMap,ze=new WeakMap,Ji=new WeakMap,za=new WeakMap,pr=new WeakMap,ss=new WeakMap,ai=new WeakMap,Jt=new WeakMap,oi=new WeakMap,H=new WeakMap,Zi=new WeakMap,ut=new WeakSet,Pp=function(t){n(this,H).unselectAll();const{target:e}=t;if(e===n(this,Jt).div||(e.getAttribute("role")==="img"||e.classList.contains("endOfContent"))&&n(this,Jt).div.contains(e)){const{isMac:s}=Wt.platform;if(t.button!==0||t.ctrlKey&&s)return;n(this,H).showAllEditors("highlight",!0,!0),n(this,Jt).div.classList.add("free"),this.toggleDrawing(),Nh.startHighlighting(this,n(this,H).direction==="ltr",{target:n(this,Jt).div,x:t.x,y:t.y}),n(this,Jt).div.addEventListener("pointerup",()=>{n(this,Jt).div.classList.remove("free"),this.toggleDrawing(!0)},{once:!0,signal:n(this,H)._signal}),t.preventDefault()}},hs=function(){return n(je,Zi).get(n(this,H).getMode())},Md=function(t){const e=n(this,ut,hs);return e?new e.prototype.constructor(t):null},Ld=function(){const{x:t,y:e,width:s,height:i}=this.div.getBoundingClientRect(),r=Math.max(0,t),a=Math.max(0,e),o=Math.min(window.innerWidth,t+s),l=Math.min(window.innerHeight,e+i),h=(r+o)/2-t,c=(a+l)/2-e,[u,f]=this.viewport.rotation%180===0?[h,c]:[c,h];return{offsetX:u,offsetY:f}},Rh=function(){for(const t of n(this,ze).values())t.isEmpty()&&t.remove()},R(je,"_initialized",!1),m(je,Zi,new Map([hd,_d,Pd,Nh,Rd].map(t=>[t._editorType,t])));let kd=je;var is,re,gr,Ol,sc,Ip,xs,Fd,kp,Nd;const Tt=class Tt{constructor({pageIndex:t}){m(this,xs);m(this,is,null);m(this,re,new Map);m(this,gr,new Map);this.pageIndex=t}setParent(t){if(!n(this,is)){p(this,is,t);return}if(n(this,is)!==t){if(n(this,re).size>0)for(const e of n(this,re).values())e.remove(),t.append(e);p(this,is,t)}}static get _svgFactory(){return X(this,"_svgFactory",new Lh)}draw(t,e=!1,s=!1){const i=te(Tt,Ol)._++,r=b(this,xs,Fd).call(this),a=Tt._svgFactory.createElement("defs");r.append(a);const o=Tt._svgFactory.createElement("path");a.append(o);const l=`path_p${this.pageIndex}_${i}`;o.setAttribute("id",l),o.setAttribute("vector-effect","non-scaling-stroke"),e&&n(this,gr).set(i,o);const h=s?b(this,xs,kp).call(this,a,l):null,c=Tt._svgFactory.createElement("use");return r.append(c),c.setAttribute("href",`#${l}`),this.updateProperties(r,t),n(this,re).set(i,r),{id:i,clipPathId:`url(#${h})`}}drawOutline(t,e){const s=te(Tt,Ol)._++,i=b(this,xs,Fd).call(this),r=Tt._svgFactory.createElement("defs");i.append(r);const a=Tt._svgFactory.createElement("path");r.append(a);const o=`path_p${this.pageIndex}_${s}`;a.setAttribute("id",o),a.setAttribute("vector-effect","non-scaling-stroke");let l;if(e){const u=Tt._svgFactory.createElement("mask");r.append(u),l=`mask_p${this.pageIndex}_${s}`,u.setAttribute("id",l),u.setAttribute("maskUnits","objectBoundingBox");const f=Tt._svgFactory.createElement("rect");u.append(f),f.setAttribute("width","1"),f.setAttribute("height","1"),f.setAttribute("fill","white");const g=Tt._svgFactory.createElement("use");u.append(g),g.setAttribute("href",`#${o}`),g.setAttribute("stroke","none"),g.setAttribute("fill","black"),g.setAttribute("fill-rule","nonzero"),g.classList.add("mask")}const h=Tt._svgFactory.createElement("use");i.append(h),h.setAttribute("href",`#${o}`),l&&h.setAttribute("mask",`url(#${l})`);const c=h.cloneNode();return i.append(c),h.classList.add("mainOutline"),c.classList.add("secondaryOutline"),this.updateProperties(i,t),n(this,re).set(s,i),s}finalizeDraw(t,e){n(this,gr).delete(t),this.updateProperties(t,e)}updateProperties(t,e){var l;if(!e)return;const{root:s,bbox:i,rootClass:r,path:a}=e,o=typeof t=="number"?n(this,re).get(t):t;if(o){if(s&&b(this,xs,Nd).call(this,o,s),i&&b(l=Tt,sc,Ip).call(l,o,i),r){const{classList:h}=o;for(const[c,u]of Object.entries(r))h.toggle(c,u)}if(a){const c=o.firstChild.firstChild;b(this,xs,Nd).call(this,c,a)}}}updateParent(t,e){if(e===this)return;const s=n(this,re).get(t);s&&(n(e,is).append(s),n(this,re).delete(t),n(e,re).set(t,s))}remove(t){n(this,gr).delete(t),n(this,is)!==null&&(n(this,re).get(t).remove(),n(this,re).delete(t))}destroy(){p(this,is,null);for(const t of n(this,re).values())t.remove();n(this,re).clear(),n(this,gr).clear()}};is=new WeakMap,re=new WeakMap,gr=new WeakMap,Ol=new WeakMap,sc=new WeakSet,Ip=function(t,[e,s,i,r]){const{style:a}=t;a.top=`${100*s}%`,a.left=`${100*e}%`,a.width=`${100*i}%`,a.height=`${100*r}%`},xs=new WeakSet,Fd=function(){const t=Tt._svgFactory.create(1,1,!0);return n(this,is).append(t),t.setAttribute("aria-hidden",!0),t},kp=function(t,e){const s=Tt._svgFactory.createElement("clipPath");t.append(s);const i=`clip_${e}`;s.setAttribute("id",i),s.setAttribute("clipPathUnits","objectBoundingBox");const r=Tt._svgFactory.createElement("use");return s.append(r),r.setAttribute("href",`#${e}`),r.classList.add("clip"),i},Nd=function(t,e){for(const[s,i]of Object.entries(e))i===null?t.removeAttribute(s):t.setAttribute(s,i)},m(Tt,sc),m(Tt,Ol,0);let Dd=Tt;globalThis.pdfjsTestingUtils={HighlightOutliner:dd};globalThis.pdfjsLib={AbortException:tn,AnnotationEditorLayer:kd,AnnotationEditorParamsType:Y,AnnotationEditorType:z,AnnotationEditorUIManager:br,AnnotationLayer:ad,AnnotationMode:bi,AnnotationType:_t,build:nm,ColorPicker:Fh,createValidAbsoluteUrl:Cu,DOMSVGFactory:Lh,DrawLayer:Dd,FeatureTest:Wt,fetchData:Hl,getDocument:qg,getFilenameFromUrl:Kp,getPdfFilenameFromUrl:Qp,getUuid:Ru,getXfaPageViewport:Jp,GlobalWorkerOptions:li,ImageKind:Jl,InvalidPDFException:wc,isDataScheme:ac,isPdfFile:Hd,isValidExplicitDest:Qg,MathClamp:oe,noContextMenu:rs,normalizeUnicode:qp,OPS:Ph,OutputScale:ui,PasswordResponses:Bp,PDFDataRangeTransport:Pf,PDFDateString:$d,PDFWorker:Fr,PermissionFlag:Op,PixelsPerInch:en,RenderingCancelledException:Bd,ResponseException:Ih,setLayerDimensions:mr,shadow:X,SignatureExtractor:hi,stopEvent:St,SupportedImageMimeTypes:_c,TextLayer:fo,TouchManager:Mh,updateUrlHash:xu,Util:O,VerbosityLevel:ic,version:im,XfaLayer:kf};export{tn as AbortException,kd as AnnotationEditorLayer,Y as AnnotationEditorParamsType,z as AnnotationEditorType,br as AnnotationEditorUIManager,ad as AnnotationLayer,bi as AnnotationMode,_t as AnnotationType,Fh as ColorPicker,Lh as DOMSVGFactory,Dd as DrawLayer,Wt as FeatureTest,li as GlobalWorkerOptions,Jl as ImageKind,wc as InvalidPDFException,oe as MathClamp,Ph as OPS,ui as OutputScale,Pf as PDFDataRangeTransport,$d as PDFDateString,Fr as PDFWorker,Bp as PasswordResponses,Op as PermissionFlag,en as PixelsPerInch,Bd as RenderingCancelledException,Ih as ResponseException,hi as SignatureExtractor,_c as SupportedImageMimeTypes,fo as TextLayer,Mh as TouchManager,O as Util,ic as VerbosityLevel,kf as XfaLayer,nm as build,Cu as createValidAbsoluteUrl,Hl as fetchData,qg as getDocument,Kp as getFilenameFromUrl,Qp as getPdfFilenameFromUrl,Ru as getUuid,Jp as getXfaPageViewport,ac as isDataScheme,Hd as isPdfFile,Qg as isValidExplicitDest,rs as noContextMenu,qp as normalizeUnicode,mr as setLayerDimensions,X as shadow,St as stopEvent,xu as updateUrlHash,im as version};
