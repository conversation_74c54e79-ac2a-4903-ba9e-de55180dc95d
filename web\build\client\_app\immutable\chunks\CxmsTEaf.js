import{c as n,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as m,s as p}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function v(o,r){const e=m(r,["children","$$slots","$$events","$$legacy"]),s=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10"}]];f(o,p({name:"credit-card"},()=>e,{get iconNode(){return s},children:(a,$)=>{var t=n(),i=d(t);l(i,r,"default",{},null),c(a,t)},$$slots:{default:!0}}))}export{v as C};
