import{Q as d,u as g,i as c,aw as l,o as m,ax as b,g as p,h,a6 as v}from"./CGmarHxI.js";function x(n=!1){const s=d,e=s.l.u;if(!e)return;let f=()=>h(s.s);if(n){let o=0,t={};const _=v(()=>{let i=!1;const r=s.s;for(const a in r)r[a]!==t[a]&&(t[a]=r[a],i=!0);return i&&o++,o});f=()=>p(_)}e.b.length&&g(()=>{u(s,f),l(e.b)}),c(()=>{const o=m(()=>e.m.map(b));return()=>{for(const t of o)typeof t=="function"&&t()}}),e.a.length&&c(()=>{u(s,f),l(e.a)})}function u(n,s){if(n.l.s)for(const e of n.l.s)p(e);s()}export{x as i};
