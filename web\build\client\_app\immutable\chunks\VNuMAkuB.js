import{f as n,a as d}from"./BasJTneF.js";import{p as m,a as f}from"./CGmarHxI.js";import{j as u}from"./CmxjS0TN.js";import{e as p}from"./B-Xjo-Yt.js";import{b as c}from"./CzsE_FAw.js";import{b}from"./5V1tIHTN.js";import{p as i,r as v}from"./Btcx8l8F.js";import{c as x}from"./ncUU1dSD.js";var g=n("<textarea></textarea>");function P(o,r){m(r,!0);let t=i(r,"ref",15,null),s=i(r,"value",15),l=v(r,["$$slots","$$events","$$legacy","ref","value","class"]);var e=g();u(e),p(e,a=>({"data-slot":"textarea",class:a,...l}),[()=>x("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 field-sizing-content shadow-xs flex min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base outline-none transition-[color,box-shadow] focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r.class)]),b(e,a=>t(a),()=>t()),c(e,s),d(o,e),f()}export{P as T};
