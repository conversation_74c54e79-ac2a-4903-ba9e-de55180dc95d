import{c as d,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as p}from"./BBa424ah.js";import{l as i,s as m}from"./Btcx8l8F.js";import{I as $}from"./D4f2twK-.js";function y(a,t){const e=i(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"}]];$(a,m({name:"moon"},()=>e,{get iconNode(){return s},children:(n,h)=>{var o=d(),r=l(o);p(r,t,"default",{},null),c(n,o)},$$slots:{default:!0}}))}function N(a,t){const e=i(t,["children","$$slots","$$events","$$legacy"]),s=[["circle",{cx:"12",cy:"12",r:"4"}],["path",{d:"M12 2v2"}],["path",{d:"M12 20v2"}],["path",{d:"m4.93 4.93 1.41 1.41"}],["path",{d:"m17.66 17.66 1.41 1.41"}],["path",{d:"M2 12h2"}],["path",{d:"M20 12h2"}],["path",{d:"m6.34 17.66-1.41 1.41"}],["path",{d:"m19.07 4.93-1.41 1.41"}]];$(a,m({name:"sun"},()=>e,{get iconNode(){return s},children:(n,h)=>{var o=d(),r=l(o);p(r,t,"default",{},null),c(n,o)},$$slots:{default:!0}}))}export{y as M,N as S};
