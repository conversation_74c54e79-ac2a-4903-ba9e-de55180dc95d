import{f as u,a as f}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as I}from"../chunks/nZgk9enP.js";import{p as O,f as b,a as R,s as y,c as n,d as t,m as w,g as m,r as c,n as T,t as L}from"../chunks/CGmarHxI.js";import{s as N}from"../chunks/CIt1g2O9.js";import{i as k}from"../chunks/u21ee2wt.js";import{i as z}from"../chunks/BIEMS98f.js";import{g as B}from"../chunks/BiJhC7W5.js";import{t as D}from"../chunks/DjPYYl4Z.js";import{S as E}from"../chunks/C6g8ubaU.js";var F=u('<div class="flex flex-col items-center justify-center space-y-4"><div class="border-primary h-8 w-8 animate-spin rounded-full border-2 border-t-transparent"></div> <p class="text-muted-foreground text-sm">Completing your authentication...</p></div>'),J=u('<div class="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800"><p> </p></div> <a href="/auth/oauth" class="text-sm text-blue-600 hover:underline">Return to sign in</a>',1),M=u('<!> <div class="flex min-h-[calc(100vh-4rem)] flex-col items-center justify-center"><div class="mx-auto w-full max-w-md space-y-6 p-6"><div class="flex flex-col space-y-2 text-center"><h1 class="text-2xl font-semibold tracking-tight">Google Authentication</h1> <!></div></div></div>',1);function $(A,G){O(G,!1);let a=w(!0),r=w("");I(async()=>{try{const o=new URLSearchParams(window.location.hash.substring(1)).get("access_token");if(!o){t(r,"No access token received from Google."),t(a,!1);return}const l=await fetch("https://www.googleapis.com/oauth2/v3/userinfo",{headers:{Authorization:`Bearer ${o}`}});if(!l.ok){t(r,"Failed to get user information from Google."),t(a,!1);return}const s=await l.json(),i=await fetch("/api/auth/google",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({id_token:o,user_info:s})});if(!i.ok){const d=await i.json();t(r,d.error||"Authentication failed."),t(a,!1);return}D.success("Sign In Successful",{description:"You have successfully signed in with Google."}),B("/dashboard")}catch(e){console.error("Google callback error:",e),t(r,"An error occurred during authentication."),t(a,!1)}}),z();var p=M(),h=b(p);E(h,{title:"Google Authentication | Auto Apply",description:"Completing your Google authentication"});var v=y(h,2),g=n(v),x=n(g),S=y(n(x),2);{var j=e=>{var o=F();f(e,o)},P=(e,o)=>{{var l=s=>{var i=J(),d=b(i),_=n(d),C=n(_,!0);c(_),c(d),T(2),L(()=>N(C,m(r))),f(s,i)};k(e,s=>{m(r)&&s(l)},o)}};k(S,e=>{m(a)?e(j):e(P,!1)})}c(x),c(g),c(v),f(A,p),R()}export{$ as component};
