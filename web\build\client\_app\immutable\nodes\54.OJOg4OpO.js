import{f as v,a as t,t as u,c as Le}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as Et}from"../chunks/nZgk9enP.js";import{p as Lt,l as st,b as Rt,f as $,a as Bt,s as r,c as s,d,m as K,g as e,t as D,e as se,n,r as l,q as lt}from"../chunks/CGmarHxI.js";import{s as q}from"../chunks/CIt1g2O9.js";import{i as Te}from"../chunks/u21ee2wt.js";import{e as ze,i as It}from"../chunks/C3w0v0gR.js";import{c as Ot}from"../chunks/BvdI7LR8.js";import{d as it}from"../chunks/B-Xjo-Yt.js";import{i as Mt}from"../chunks/BIEMS98f.js";import{p as Ft}from"../chunks/Btcx8l8F.js";import{C as Re}from"../chunks/DuGukytH.js";import{C as Be}from"../chunks/Cdn-N1RY.js";import{C as Ie}from"../chunks/BkJY4La4.js";import{C as Jt}from"../chunks/DETxXRrJ.js";import{C as Oe}from"../chunks/GwmmX_iF.js";import{C as Me}from"../chunks/D50jIuLr.js";import{R as Dt,T as qt}from"../chunks/I7hvcB12.js";import{T as nt,a as dt,b as Fe,c as ue,d as ct,e as $e}from"../chunks/LESefvxV.js";import{B as Se}from"../chunks/B1K98fMG.js";import{I as Ne}from"../chunks/DMTMHyMa.js";import{T as vt}from"../chunks/VNuMAkuB.js";import{L as ce}from"../chunks/BvvicRXk.js";import{S as zt}from"../chunks/D9yI7a4E.js";import{C as Ht}from"../chunks/T7uRAIbG.js";import{t as _e}from"../chunks/DjPYYl4Z.js";import{S as Gt}from"../chunks/C6g8ubaU.js";import{A as Qt,a as Vt,b as Kt}from"../chunks/CE9Bts7j.js";import{B as ft}from"../chunks/DaBofrVv.js";import{T as He}from"../chunks/C88uNE8B.js";import{T as Ge}from"../chunks/DmZyh-PW.js";import{S as Wt}from"../chunks/yW0TxTga.js";import{R as ke}from"../chunks/qwsZpUIl.js";import{S as Xt}from"../chunks/D871oxnv.js";import{B as ut}from"../chunks/hA0h0kTo.js";import{I as Yt}from"../chunks/BuYRPDDz.js";import{C as Zt}from"../chunks/DW7T7T22.js";import{T as er}from"../chunks/CTO_B1Jk.js";import{B as tr}from"../chunks/CDnvByek.js";import{M as rr}from"../chunks/QtAhPN2H.js";var or=v("<!> <!> <!>",1),ar=v("<!> <!>",1),sr=v('<form class="space-y-4"><div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <select class="border-input bg-background ring-offset-background w-full rounded-md border px-3 py-2 text-sm"><option>Info</option><option>Success</option><option>Error</option><option>Job</option><option>Message</option></select></div> <div class="flex items-center space-x-2"><!> <!></div></form>'),lr=v("<!> ",1),ir=v("<!> <!> <!>",1),nr=v("<!> <!>",1),dr=v('<div class="flex h-20 items-center justify-center"><p class="text-muted-foreground">No users found</p></div>'),cr=v("<!> <!> <!>",1),vr=v("<!> <!>",1),fr=v('<div class="flex items-center gap-2"><!> <div><p class="text-sm font-medium"> </p> <p class="text-muted-foreground text-xs"> </p></div></div>'),ur=v("<!> <!> <!>",1),$r=v("<!> <!>",1),_r=v('<div class="mb-4 flex items-center space-x-2"><div class="relative flex-1"><!> <!></div> <!></div> <div class="max-h-[400px] overflow-y-auto rounded-md border"><!></div> <div class="mt-4 flex items-center justify-between"><p class="text-muted-foreground text-sm"> </p></div>',1),pr=v("<!> <!>",1),mr=v('<div class="grid grid-cols-1 gap-6 lg:grid-cols-2"><!> <!></div>'),gr=v("<!> Refresh",1),hr=v('<div class="flex items-center justify-between"><!> <!></div> <!>',1),xr=v('<div class="flex h-40 items-center justify-center"><!></div>'),yr=v('<div class="flex h-40 flex-col items-center justify-center"><!> <p class="text-muted-foreground">No notifications have been sent yet</p></div>'),br=v("<!> <!> <!> <!> <!>",1),Pr=v('<div class="flex items-center gap-2"><!> <span class="capitalize"> </span></div>'),wr=v('<div><p class="font-medium"> </p> <p class="text-muted-foreground text-xs"> </p></div>'),Tr=v("<!> <!> <!> <!> <!>",1),Cr=v("<!> <!>",1),Sr=v("<!> <!>",1),Nr=v("<!> <!>",1),kr=v("<!> Refresh Notifications",1),jr=v("<!> Create Notification",1),Ur=v('<div class="flex h-full items-center justify-center text-gray-400">No logs yet. Create a notification to see logs.</div>'),Ar=v('<div class="mb-1 border-b pb-1 font-mono text-sm"> </div>'),Er=v('<div class="grid grid-cols-1 gap-6 lg:grid-cols-2"><div><form class="space-y-4"><div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <select id="test-type" class="border-input bg-background ring-offset-background w-full rounded-md border px-3 py-2 text-sm"><option>Info</option><option>Success</option><option>Error</option><option>Job</option><option>Message</option></select></div></form> <div class="mt-4 flex gap-2"><!> <!></div></div> <div><div class="mb-2 font-medium">Test Logs</div> <div class="h-[400px] overflow-y-auto rounded-md border bg-gray-50 p-2 dark:bg-gray-900"><!></div></div></div>'),Lr=v("<!> <!>",1),Rr=v('<div class="border-border border-b p-0"><!></div> <!> <!> <!>',1),Br=v('<!> <div class="border-border flex flex-col gap-1 border-b p-4"><div class="flex items-center justify-between"><h1 class="text-2xl font-bold">Admin Notifications</h1></div></div> <!>',1);function Co($t,Qe){Lt(Qe,!1);const be=K();let _t=Ft(Qe,"data",8);const{users:Ve,currentUser:pt}=_t();let le=K(""),ie=K(""),ve=K(""),te=K("info"),fe=K(!1),R=K(!1),O=K([]),Pe=K(""),je=K([]),Ue=K(!1),Ae=K("send"),pe=K([]);function mt(a){e(O).includes(a)?d(O,e(O).filter(z=>z!==a)):d(O,[...e(O),a])}function gt(){e(O).length===e(be).length?d(O,[]):d(O,e(be).map(a=>a.id))}async function ht(){if(!e(le)||!e(ie)){_e.error("Title and message are required");return}if(!e(fe)&&e(O).length===0){_e.error("Please select at least one user or enable global notification");return}d(R,!0);try{if(e(fe)){const a=await fetch("/api/notifications/send",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:e(le),message:e(ie),url:e(ve)||void 0,type:e(te),global:!0})}),z=await a.json();a.ok?(_e.success("Global notification sent successfully"),Ke()):_e.error(z.error||"Failed to send notification")}else{const a=e(O).map(me=>fetch("/api/notifications/send",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:e(le),message:e(ie),url:e(ve)||void 0,type:e(te),userId:me})})),H=(await Promise.allSettled(a)).filter(me=>me.status==="fulfilled").length;H>0?(_e.success(`Sent notifications to ${H} users`),Ke()):_e.error("Failed to send notifications")}await Ce()}catch(a){_e.error("An error occurred while sending notifications"),console.error("Error sending notifications:",a)}finally{d(R,!1)}}function Ke(){d(le,""),d(ie,""),d(ve,""),d(te,"info"),d(fe,!1),d(O,[]),d(Pe,"")}async function Ce(){d(Ue,!0);try{const a=await fetch("/api/notifications/history");if(!a.ok){const H=await a.json();throw new Error(H.error||"Failed to load notification history")}const z=await a.json();d(je,z.notifications)}catch(a){console.error("Error loading sent notifications:",a),_e.error("Failed to load notification history"),d(je,[])}finally{d(Ue,!1)}}function xt(a){switch(a){case"message":return rr;case"job":return tr;case"error":return er;case"success":return Zt;case"info":default:return Yt}}function W(a){d(pe,[a,...e(pe)].slice(0,50)),console.log(a)}async function yt(){if(!e(R))try{d(R,!0),W("Refreshing notifications from server...");const a=await fetch("/api/notifications");if(!a.ok){const H=await a.json();throw new Error(H.error||"Failed to refresh notifications")}const z=await a.json();W(`Successfully refreshed ${z.notifications.length} notifications`),W(`Unread count: ${z.unreadCount}`),await Ce(),W("Notification history refreshed")}catch(a){console.error("Error refreshing notifications:",a),W(`Error refreshing notifications: ${a}`)}finally{d(R,!1)}}async function bt(){if(!e(R))try{d(R,!0),W("Creating test notification...");const a={title:e(le),message:e(ie),url:e(ve)||void 0,type:e(te)};W(`Notification data: ${JSON.stringify(a)}`);const z=await fetch("/api/admin/test-notification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),H=await z.json();z.ok&&H.success?(W("Test notification created successfully"),W(`Database ID: ${H.databaseId}`),W("The notification should now appear in the dropdown"),await Ce()):W(`Error from server: ${H.error||"Unknown error"}`)}catch(a){console.error("Error creating test notification:",a),W(`Error creating test notification: ${a}`)}finally{d(R,!1)}}Et(()=>{Ce()}),st(()=>(e(Ae),e(pe)),()=>{e(Ae)==="test"&&e(pe).length===0&&d(pe,["Test tab initialized. Create a notification to test the system."])}),st(()=>e(Pe),()=>{d(be,Ve.filter(a=>a.email.toLowerCase().includes(e(Pe).toLowerCase())||a.name&&a.name.toLowerCase().includes(e(Pe).toLowerCase())))}),Rt(),Mt();var We=Br(),Xe=$(We);Gt(Xe,{title:"Admin Notifications - Hirli"});var Pt=r(Xe,4);Dt(Pt,{class:"w-full",get value(){return e(Ae)},set value(a){d(Ae,a)},children:(a,z)=>{var H=Rr(),me=$(H),wt=s(me);qt(wt,{class:"flex flex-row gap-2 divide-x",children:(ge,Je)=>{var re=or(),he=$(re);He(he,{value:"send",class:"flex-1 border-none",children:(G,X)=>{n();var B=u("Send Notifications");t(G,B)},$$slots:{default:!0}});var oe=r(he,2);He(oe,{value:"history",class:"flex-1 border-none",children:(G,X)=>{n();var B=u("Notification History");t(G,B)},$$slots:{default:!0}});var ne=r(oe,2);He(ne,{value:"test",class:"flex-1 border-none",children:(G,X)=>{n();var B=u("Test Notifications");t(G,B)},$$slots:{default:!0}}),t(ge,re)},$$slots:{default:!0}}),l(me);var Ye=r(me,2);Ge(Ye,{value:"send",children:(ge,Je)=>{var re=mr(),he=s(re);Re(he,{children:(G,X)=>{var B=ir(),N=$(B);Oe(N,{children:(C,y)=>{var f=ar(),m=$(f);Me(m,{children:(g,p)=>{n();var k=u("Send Notification");t(g,k)},$$slots:{default:!0}});var _=r(m,2);Ie(_,{children:(g,p)=>{n();var k=u("Create and send notifications to users or globally");t(g,k)},$$slots:{default:!0}}),t(C,f)},$$slots:{default:!0}});var U=r(N,2);Be(U,{children:(C,y)=>{var f=sr(),m=s(f),_=s(m);ce(_,{for:"title",children:(i,I)=>{n();var o=u("Title");t(i,o)},$$slots:{default:!0}});var g=r(_,2);Ne(g,{id:"title",placeholder:"Notification title",get value(){return e(le)},set value(i){d(le,i)},$$legacy:!0}),l(m);var p=r(m,2),k=s(p);ce(k,{for:"message",children:(i,I)=>{n();var o=u("Message");t(i,o)},$$slots:{default:!0}});var ae=r(k,2);vt(ae,{get value(){return e(ie)},set value(i){d(ie,i)},$$legacy:!0}),l(p);var j=r(p,2),de=s(j);ce(de,{for:"url",children:(i,I)=>{n();var o=u("URL (optional)");t(i,o)},$$slots:{default:!0}});var Y=r(de,2);Ne(Y,{id:"url",placeholder:"https://example.com",get value(){return e(ve)},set value(i){d(ve,i)},$$legacy:!0}),l(j);var Z=r(j,2),Q=s(Z);ce(Q,{for:"type",children:(i,I)=>{n();var o=u("Type");t(i,o)},$$slots:{default:!0}});var b=r(Q,2);D(()=>{e(te),lt(()=>{})});var S=s(b);S.value=S.__value="info";var h=r(S);h.value=h.__value="success";var M=r(h);M.value=M.__value="error";var V=r(M);V.value=V.__value="job";var ee=r(V);ee.value=ee.__value="message",l(b),l(Z);var F=r(Z,2),P=s(F);zt(P,{id:"global",get checked(){return e(fe)},set checked(i){d(fe,i)},$$legacy:!0});var E=r(P,2);ce(E,{for:"global",children:(i,I)=>{n();var o=u("Send to all users (global notification)");t(i,o)},$$slots:{default:!0}}),l(F),l(f),it(b,()=>e(te),i=>d(te,i)),t(C,f)},$$slots:{default:!0}});var A=r(U,2);Jt(A,{children:(C,y)=>{Se(C,{variant:"outline",onclick:ht,get disabled(){return e(R)},children:(f,m)=>{var _=lr(),g=$(_);{var p=j=>{ke(j,{class:"mr-2 h-4 w-4 animate-spin"})},k=j=>{Xt(j,{class:"mr-2 h-4 w-4"})};Te(g,j=>{e(R)?j(p):j(k,!1)})}var ae=r(g);D(()=>q(ae,` ${e(R)?"Sending...":"Send Notification"}`)),t(f,_)},$$slots:{default:!0}})},$$slots:{default:!0}}),t(G,B)},$$slots:{default:!0}});var oe=r(he,2);const ne=se(()=>e(fe)?"opacity-50":"");Re(oe,{get class(){return e(ne)},children:(G,X)=>{var B=pr(),N=$(B);Oe(N,{children:(A,C)=>{var y=nr(),f=$(y);Me(f,{children:(_,g)=>{n();var p=u("Select Recipients");t(_,p)},$$slots:{default:!0}});var m=r(f,2);Ie(m,{children:(_,g)=>{n();var p=u("Choose which users will receive the notification");t(_,p)},$$slots:{default:!0}}),t(A,y)},$$slots:{default:!0}});var U=r(N,2);Be(U,{children:(A,C)=>{var y=_r(),f=$(y),m=s(f),_=s(m);Wt(_,{class:"text-muted-foreground absolute left-2 top-2.5 h-4 w-4"});var g=r(_,2);Ne(g,{placeholder:"Search users...",class:"pl-8",get value(){return e(Pe)},set value(b){d(Pe,b)},$$legacy:!0}),l(m);var p=r(m,2);Se(p,{variant:"outline",onclick:gt,get disabled(){return e(fe)},children:(b,S)=>{n();var h=u();D(()=>q(h,e(O).length===e(be).length?"Deselect All":"Select All")),t(b,h)},$$slots:{default:!0}}),l(f);var k=r(f,2),ae=s(k);{var j=b=>{var S=dr();t(b,S)},de=b=>{nt(b,{children:(S,h)=>{var M=$r(),V=$(M);dt(V,{children:(F,P)=>{Fe(F,{children:(E,i)=>{var I=cr(),o=$(I);ue(o,{class:"w-[50px]"});var w=r(o,2);ue(w,{children:(x,L)=>{n();var T=u("User");t(x,T)},$$slots:{default:!0}});var c=r(w,2);ue(c,{children:(x,L)=>{n();var T=u("Role");t(x,T)},$$slots:{default:!0}}),t(E,I)},$$slots:{default:!0}})},$$slots:{default:!0}});var ee=r(V,2);ct(ee,{children:(F,P)=>{var E=Le(),i=$(E);ze(i,1,()=>e(be),I=>I.id,(I,o)=>{Fe(I,{children:(w,c)=>{var x=ur(),L=$(x);$e(L,{children:(xe,et)=>{const ye=se(()=>e(O).includes(e(o).id));Ht(xe,{get checked(){return e(ye)},onCheckedChange:()=>mt(e(o).id),get disabled(){return e(fe)}})},$$slots:{default:!0}});var T=r(L,2);$e(T,{children:(xe,et)=>{var ye=fr(),Ee=s(ye);Qt(Ee,{class:"h-8 w-8",children:(Nt,Ir)=>{var rt=vr(),ot=$(rt);const kt=se(()=>e(o).image||""),jt=se(()=>e(o).name||e(o).email);Vt(ot,{get src(){return e(kt)},get alt(){return e(jt)}});var Ut=r(ot,2);Kt(Ut,{children:(At,Or)=>{n();var at=u();D(qe=>q(at,qe),[()=>e(o).name?e(o).name.split(" ").map(qe=>qe[0]).join("").toUpperCase():e(o).email.substring(0,2).toUpperCase()],se),t(At,at)},$$slots:{default:!0}}),t(Nt,rt)},$$slots:{default:!0}});var De=r(Ee,2),we=s(De),Ct=s(we,!0);l(we);var tt=r(we,2),St=s(tt,!0);l(tt),l(De),l(ye),D(()=>{q(Ct,e(o).name||"Unnamed User"),q(St,e(o).email)}),t(xe,ye)},$$slots:{default:!0}});var J=r(T,2);$e(J,{children:(xe,et)=>{const ye=se(()=>e(o).role==="admin"?"default":"outline");ft(xe,{get variant(){return e(ye)},children:(Ee,De)=>{n();var we=u();D(()=>q(we,e(o).role||"user")),t(Ee,we)},$$slots:{default:!0}})},$$slots:{default:!0}}),t(w,x)},$$slots:{default:!0}})}),t(F,E)},$$slots:{default:!0}}),t(S,M)},$$slots:{default:!0}})};Te(ae,b=>{e(be).length===0?b(j):b(de,!1)})}l(k);var Y=r(k,2),Z=s(Y),Q=s(Z);l(Z),l(Y),D(()=>q(Q,`${e(O).length??""} of ${Ve.length??""} users selected`)),t(A,y)},$$slots:{default:!0}}),t(G,B)},$$slots:{default:!0}}),l(re),t(ge,re)},$$slots:{default:!0}});var Ze=r(Ye,2);Ge(Ze,{value:"history",children:(ge,Je)=>{Re(ge,{children:(re,he)=>{var oe=Sr(),ne=$(oe);Oe(ne,{children:(X,B)=>{var N=hr(),U=$(N),A=s(U);Me(A,{children:(f,m)=>{n();var _=u("Notification History");t(f,_)},$$slots:{default:!0}});var C=r(A,2);Se(C,{variant:"outline",size:"sm",onclick:Ce,children:(f,m)=>{var _=gr(),g=$(_);const p=se(()=>`mr-2 h-4 w-4 ${e(Ue)?"animate-spin":""}`);ke(g,{get class(){return e(p)}}),n(),t(f,_)},$$slots:{default:!0}}),l(U);var y=r(U,2);Ie(y,{children:(f,m)=>{n();var _=u("View previously sent notifications");t(f,_)},$$slots:{default:!0}}),t(X,N)},$$slots:{default:!0}});var G=r(ne,2);Be(G,{children:(X,B)=>{var N=Le(),U=$(N);{var A=y=>{var f=xr(),m=s(f);ke(m,{class:"text-muted-foreground h-8 w-8 animate-spin"}),l(f),t(y,f)},C=(y,f)=>{{var m=g=>{var p=yr(),k=s(p);ut(k,{class:"text-muted-foreground mb-2 h-12 w-12 opacity-20"}),n(2),l(p),t(g,p)},_=g=>{nt(g,{children:(p,k)=>{var ae=Cr(),j=$(ae);dt(j,{children:(Y,Z)=>{Fe(Y,{children:(Q,b)=>{var S=br(),h=$(S);ue(h,{children:(P,E)=>{n();var i=u("Type");t(P,i)},$$slots:{default:!0}});var M=r(h,2);ue(M,{children:(P,E)=>{n();var i=u("Title");t(P,i)},$$slots:{default:!0}});var V=r(M,2);ue(V,{children:(P,E)=>{n();var i=u("Recipients");t(P,i)},$$slots:{default:!0}});var ee=r(V,2);ue(ee,{children:(P,E)=>{n();var i=u("Sent By");t(P,i)},$$slots:{default:!0}});var F=r(ee,2);ue(F,{children:(P,E)=>{n();var i=u("Sent At");t(P,i)},$$slots:{default:!0}}),t(Q,S)},$$slots:{default:!0}})},$$slots:{default:!0}});var de=r(j,2);ct(de,{children:(Y,Z)=>{var Q=Le(),b=$(Q);ze(b,1,()=>e(je),S=>S.id,(S,h)=>{Fe(S,{children:(M,V)=>{var ee=Tr(),F=$(ee);$e(F,{children:(o,w)=>{var c=Pr(),x=s(c);Ot(x,()=>xt(e(h).type),(J,xe)=>{xe(J,{class:"text-muted-foreground h-4 w-4"})});var L=r(x,2),T=s(L,!0);l(L),l(c),D(()=>q(T,e(h).type)),t(o,c)},$$slots:{default:!0}});var P=r(F,2);$e(P,{children:(o,w)=>{var c=wr(),x=s(c),L=s(x,!0);l(x);var T=r(x,2),J=s(T,!0);l(T),l(c),D(()=>{q(L,e(h).title),q(J,e(h).message)}),t(o,c)},$$slots:{default:!0}});var E=r(P,2);$e(E,{children:(o,w)=>{const c=se(()=>e(h).global?"default":"outline");ft(o,{get variant(){return e(c)},children:(x,L)=>{n();var T=u();D(()=>q(T,e(h).global?"All Users":e(h).recipients)),t(x,T)},$$slots:{default:!0}})},$$slots:{default:!0}});var i=r(E,2);$e(i,{children:(o,w)=>{n();var c=u();D(()=>q(c,e(h).sentBy||pt.email)),t(o,c)},$$slots:{default:!0}});var I=r(i,2);$e(I,{children:(o,w)=>{n();var c=u();D(x=>q(c,x),[()=>new Date(e(h).sentAt).toLocaleString()],se),t(o,c)},$$slots:{default:!0}}),t(M,ee)},$$slots:{default:!0}})}),t(Y,Q)},$$slots:{default:!0}}),t(p,ae)},$$slots:{default:!0}})};Te(y,g=>{e(je).length===0?g(m):g(_,!1)},f)}};Te(U,y=>{e(Ue)?y(A):y(C,!1)})}t(X,N)},$$slots:{default:!0}}),t(re,oe)},$$slots:{default:!0}})},$$slots:{default:!0}});var Tt=r(Ze,2);Ge(Tt,{value:"test",children:(ge,Je)=>{Re(ge,{children:(re,he)=>{var oe=Lr(),ne=$(oe);Oe(ne,{children:(X,B)=>{var N=Nr(),U=$(N);Me(U,{children:(C,y)=>{n();var f=u("Test Notification System");t(C,f)},$$slots:{default:!0}});var A=r(U,2);Ie(A,{children:(C,y)=>{n();var f=u("Test creating notifications in the database and displaying them in the UI");t(C,f)},$$slots:{default:!0}}),t(X,N)},$$slots:{default:!0}});var G=r(ne,2);Be(G,{children:(X,B)=>{var N=Er(),U=s(N),A=s(U),C=s(A),y=s(C);ce(y,{for:"test-title",children:(o,w)=>{n();var c=u("Title");t(o,c)},$$slots:{default:!0}});var f=r(y,2);Ne(f,{id:"test-title",get value(){return e(le)},set value(o){d(le,o)},$$legacy:!0}),l(C);var m=r(C,2),_=s(m);ce(_,{for:"test-message",children:(o,w)=>{n();var c=u("Message");t(o,c)},$$slots:{default:!0}});var g=r(_,2);vt(g,{get value(){return e(ie)},set value(o){d(ie,o)},$$legacy:!0}),l(m);var p=r(m,2),k=s(p);ce(k,{for:"test-url",children:(o,w)=>{n();var c=u("URL (optional)");t(o,c)},$$slots:{default:!0}});var ae=r(k,2);Ne(ae,{id:"test-url",get value(){return e(ve)},set value(o){d(ve,o)},$$legacy:!0}),l(p);var j=r(p,2),de=s(j);ce(de,{for:"test-type",children:(o,w)=>{n();var c=u("Type");t(o,c)},$$slots:{default:!0}});var Y=r(de,2);D(()=>{e(te),lt(()=>{})});var Z=s(Y);Z.value=Z.__value="info";var Q=r(Z);Q.value=Q.__value="success";var b=r(Q);b.value=b.__value="error";var S=r(b);S.value=S.__value="job";var h=r(S);h.value=h.__value="message",l(Y),l(j),l(A);var M=r(A,2),V=s(M);Se(V,{variant:"outline",onclick:yt,get disabled(){return e(R)},children:(o,w)=>{var c=kr(),x=$(c);const L=se(()=>`mr-2 h-4 w-4 ${e(R)?"animate-spin":""}`);ke(x,{get class(){return e(L)}}),n(),t(o,c)},$$slots:{default:!0}});var ee=r(V,2);Se(ee,{onclick:bt,get disabled(){return e(R)},children:(o,w)=>{var c=jr(),x=$(c);{var L=J=>{ke(J,{class:"mr-2 h-4 w-4 animate-spin"})},T=J=>{ut(J,{class:"mr-2 h-4 w-4"})};Te(x,J=>{e(R)?J(L):J(T,!1)})}n(),t(o,c)},$$slots:{default:!0}}),l(M),l(U);var F=r(U,2),P=r(s(F),2),E=s(P);{var i=o=>{var w=Ur();t(o,w)},I=o=>{var w=Le(),c=$(w);ze(c,1,()=>e(pe),It,(x,L)=>{var T=Ar(),J=s(T,!0);l(T),D(()=>q(J,e(L))),t(x,T)}),t(o,w)};Te(E,o=>{e(pe).length===0?o(i):o(I,!1)})}l(P),l(F),l(N),it(Y,()=>e(te),o=>d(te,o)),t(X,N)},$$slots:{default:!0}}),t(re,oe)},$$slots:{default:!0}})},$$slots:{default:!0}}),t(a,H)},$$slots:{default:!0},$$legacy:!0}),t($t,We),Bt()}export{Co as component};
