import * as server from '../entries/pages/dashboard/settings/email/_page.server.ts.js';

export const index = 60;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/email/_page.svelte.js')).default;
export { server };
export const server_id = "src/routes/dashboard/settings/email/+page.server.ts";
export const imports = ["_app/immutable/nodes/60.Bq9n0wCt.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/BiJhC7W5.js"];
export const stylesheets = [];
export const fonts = [];
