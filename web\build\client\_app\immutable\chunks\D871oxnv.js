import{c as l,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as m}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as i,s as c}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function z(t,o){const a=i(o,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z"}],["path",{d:"m21.854 2.147-10.94 10.939"}]];f(t,c({name:"send"},()=>a,{get iconNode(){return r},children:(e,$)=>{var s=l(),n=m(s);d(n,o,"default",{},null),p(e,s)},$$slots:{default:!0}}))}export{z as S};
