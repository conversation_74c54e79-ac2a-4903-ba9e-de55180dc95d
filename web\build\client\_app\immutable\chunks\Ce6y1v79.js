import{c as p,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as m}from"./CGmarHxI.js";import{s as i}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(t,o){const s=c(o,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"m12 19-7-7 7-7"}],["path",{d:"M19 12H5"}]];f(t,d({name:"arrow-left"},()=>s,{get iconNode(){return e},children:(a,$)=>{var r=p(),n=m(r);i(n,o,"default",{},null),l(a,r)},$$slots:{default:!0}}))}export{y as A};
