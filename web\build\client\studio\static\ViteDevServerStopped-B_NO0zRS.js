import{a,j as o,C as l,b as m,S as d,T as h,r as p,H as u}from"./sanity-DV0NwVOn.js";const S="Dev server stopped",f="The development server has stopped. You may need to restart it to continue working.";class x extends Error{constructor(){super(S),this.name="ViteDevServerStoppedError",this.ViteDevServerStoppedError=!0}}const _=e=>!1,E=()=>{const e=a.c(5),[r,s]=p.useState(!1);let t;e[0]===Symbol.for("react.memo_cache_sentinel")?(t=()=>s(!0),e[0]=t):t=e[0];const v=t;let n,c;e[1]===Symbol.for("react.memo_cache_sentinel")?(n=()=>{},c=[v],e[1]=n,e[2]=c):(n=e[1],c=e[2]),p.useEffect(n,c);let i;return e[3]!==r?(i={devServerStopped:r},e[3]=r,e[4]=i):i=e[4],i},D=()=>{const{devServerStopped:e}=E();if(e)throw new x;return null},b=()=>{const e=a.c(1);let r;return e[0]===Symbol.for("react.memo_cache_sentinel")?(r=_()?o.jsx(D,{}):null,e[0]=r):r=e[0],r},R=()=>{const e=a.c(3);let r;e[0]===Symbol.for("react.memo_cache_sentinel")?(r=[4,5,6,7],e[0]=r):r=e[0];let s;e[1]===Symbol.for("react.memo_cache_sentinel")?(s=o.jsx(u,{children:S}),e[1]=s):s=e[1];let t;return e[2]===Symbol.for("react.memo_cache_sentinel")?(t=o.jsx(l,{height:"fill",overflow:"auto",paddingY:r,paddingX:4,sizing:"border",tone:"critical",children:o.jsx(m,{width:3,children:o.jsxs(d,{space:4,children:[s,o.jsx(l,{border:!0,radius:2,overflow:"auto",padding:4,tone:"inherit",children:o.jsx(d,{space:4,children:o.jsx(h,{size:2,children:f})})})]})})}),e[2]=t):t=e[2],t};export{b as DetectViteDevServerStopped,R as DevServerStoppedErrorScreen,x as ViteDevServerStoppedError};
