import{c as l,a as d}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as p,s as m}from"./Btcx8l8F.js";import{I as $}from"./D4f2twK-.js";function z(e,a){const o=p(a,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z"}],["path",{d:"M12 22V12"}],["polyline",{points:"3.29 7 12 12 20.71 7"}],["path",{d:"m7.5 4.27 9 5.15"}]];$(e,m({name:"package"},()=>o,{get iconNode(){return s},children:(n,f)=>{var t=l(),r=i(t);c(r,a,"default",{},null),d(n,t)},$$slots:{default:!0}}))}function N(e,a){const o=p(a,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"}],["path",{d:"m14.5 9.5-5 5"}],["path",{d:"m9.5 9.5 5 5"}]];$(e,m({name:"shield-x"},()=>o,{get iconNode(){return s},children:(n,f)=>{var t=l(),r=i(t);c(r,a,"default",{},null),d(n,t)},$$slots:{default:!0}}))}export{z as P,N as S};
