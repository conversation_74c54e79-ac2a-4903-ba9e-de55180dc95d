import{f as re,a as S,t as H}from"./BasJTneF.js";import"./CgXBgsce.js";import{o as ke}from"./nZgk9enP.js";import{p as xe,t as V,g as t,a as Ce,s as v,c as b,m as k,d as n,r as h,n as ne}from"./CGmarHxI.js";import{s as te}from"./CIt1g2O9.js";import{i as Y}from"./u21ee2wt.js";import{r as ie}from"./B-Xjo-Yt.js";import{e as R}from"./CmxjS0TN.js";import{b as ae}from"./CzsE_FAw.js";import{p as Ae}from"./CWmzcjye.js";import{i as Pe}from"./BIEMS98f.js";import{p as Q}from"./Btcx8l8F.js";import{C as Se}from"./T7uRAIbG.js";import{s as Ie}from"./sDlmbjaf.js";import{g as N}from"./BiJhC7W5.js";import{t as f}from"./DjPYYl4Z.js";import{F as _e}from"./CY_6SfHi.js";var Te=re('<button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"><!> <!></button>'),Ue=re('<form class="grid gap-6"><div class="flex flex-col items-center justify-center gap-4"><!> <button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"><img alt="Google" src="/assets/svg/google.svg" class="mr-2 h-4 w-4"/> Continue with Google</button> <button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"><img alt="LinkedIn" src="/assets/svg/linkedin.svg" class="mr-2 h-4 w-4"/> Continue with LinkedIn</button></div> <div class="relative"><div class="absolute inset-0 flex items-center"><span class="border-border w-full border-2 border-t"></span></div> <div class="relative z-10 flex justify-center text-xs uppercase"><span class="text-muted-foreground bg-background px-2">Or</span></div></div> <div class="grid gap-2"><input id="email" type="email" placeholder="<EMAIL>" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm"/></div> <div><input id="password" type="password" placeholder="Password" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm"/></div> <div class="flex items-center justify-between"><div class="flex items-center"><!> <label for="remember-me" class="text-foreground ml-2 block text-sm">Remember me</label></div> <div class="text-sm"><a href="/auth/forgot-password" class="text-primary hover:text-primary/80 font-medium">Forgot your password?</a></div></div> <button class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 cursor-pointer items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50" type="submit"> </button></form>');function Ve(se,I){xe(I,!1);let le=Q(I,"onEmailPasswordLogin",8),_=Q(I,"isLoading",8,!1),x=Q(I,"callbackUrl",8,"/dashboard"),F=k(""),K=k(""),X=k(!1),C=k(!1),T=k(null),L=!1,y=!1,s=k(!1),l=k(!1),p=!1;const U={value:!1};function de(e){const o=atob(e.replace(/-/g,"+").replace(/_/g,"/")),i=new Uint8Array(o.length);for(let c=0;c<o.length;c++)i[c]=o.charCodeAt(c);return i.buffer}function ce(){le()(t(F),t(K))}function ue(){window.location.href=`/auth/signin/linkedin?callbackUrl=${encodeURIComponent(x())}`}function fe(){window.location.href=`/auth/signin/google?callbackUrl=${encodeURIComponent(x())}`}async function pe(){try{if(!window.PublicKeyCredential)return console.log("WebAuthn is not supported in this browser"),!1;if(!window.PublicKeyCredential.isConditionalMediationAvailable)return console.log("Conditional mediation is not supported in this browser"),!1;const e=await window.PublicKeyCredential.isConditionalMediationAvailable();if(console.log("Conditional mediation available:",e),window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable){const o=await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();console.log("Platform authenticator available:",o),o||console.log("No platform authenticator available, conditional UI may not work")}console.log("Current domain:",window.location.hostname);try{if("getCredentials"in navigator.credentials){console.log("Attempting to check for existing passkeys...");const o=await navigator.credentials.getCredentials({mediation:"silent",publicKey:{rpId:window.location.hostname}});n(l,!!(o&&o.length>0)),console.log("Passkeys found for this domain:",t(l)),t(l)&&console.log("Number of passkeys:",o.length)}}catch(o){console.log("Could not check for existing passkeys:",o),n(l,!1)}return e}catch(e){return console.error("Error checking conditional mediation support:",e),!1}}let w,g,Z=0,r=null;function M(){console.log("Canceling any pending requests and timeouts"),r&&(console.log("Aborting fetch request"),r.abort(),r=null),w!==void 0&&(console.log("Clearing conditional mediation timeout"),window.clearTimeout(w),w=void 0),g!==void 0&&(console.log("Clearing debounce timeout"),window.clearTimeout(g),g=void 0)}function ge(){g!==void 0&&window.clearTimeout(g);const e=Date.now();if(e-Z<2e3){console.log("Skipping conditional mediation - attempted too recently");return}Z=e,g=window.setTimeout(()=>{p||me()},300)}async function me(){if(U.value||p){console.warn("startConditionalMediation already run or in progress");return}U.value=!0,y=!0,M(),n(s,!1),p=!0;try{console.log("Starting conditional mediation..."),w=window.setTimeout(()=>{y&&(console.log("Conditional mediation timeout - no prompt appeared"),n(s,!0),t(l)?f.info("Passkey prompt was canceled. You can use the passkey button to try again."):console.log("No passkeys detected, not showing toast"))},2e3),r=new AbortController;const e=await fetch("/auth/passkey",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"getOptions"}),signal:r.signal});if(!e.ok){console.error("Failed to get authentication options");return}const o=await e.json();console.log("Authentication options received:",o),o.challenge&&(o.challenge=de(o.challenge)),o.allowCredentials&&(o.allowCredentials=o.allowCredentials.map(a=>{if(a.id){const A=a.id,P=atob(A.replace(/-/g,"+").replace(/_/g,"/")),E=new Uint8Array(P.length);for(let u=0;u<P.length;u++)E[u]=P.charCodeAt(u);return{...a,id:E.buffer}}return a})),console.log("Processed options:",o),console.log("Starting conditional UI with options:",{mediation:"conditional",publicKey:{...o,challenge:"ArrayBuffer (binary data)"}}),console.log("WebAuthn options being sent:",{...o,challenge:o.challenge?"ArrayBuffer (binary data)":void 0,allowCredentials:o.allowCredentials?o.allowCredentials.map(a=>({type:a.type,id:"ArrayBuffer (binary data)",transports:a.transports})):void 0}),console.log("Current domain:",window.location.hostname),console.log("RP ID in options:",o.rpId);const i=await navigator.credentials.get({mediation:"conditional",publicKey:o});if(!i){console.log("No credential selected");return}console.log("Credential received:",i);const c={id:i.id,rawId:i.id,response:{authenticatorData:i.response.authenticatorData,clientDataJSON:i.response.clientDataJSON,signature:i.response.signature,userHandle:i.response.userHandle},type:i.type},d=await fetch("/auth/passkey",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"verify",authenticationResponse:c}),signal:r==null?void 0:r.signal});if(!d.ok)throw new Error("Failed to verify authentication");const m=await d.json();m.success?m.user?(n(T,m.user),f.success(`Welcome back, ${m.user.name}!`),setTimeout(()=>{N(m.redirectUrl||x())},1e3)):N(m.redirectUrl||x()):console.error("Passkey verification failed:",m.error)}catch(e){console.error("Conditional mediation error:",e),e.name==="NotAllowedError"?(console.log("Passkey operation was not allowed or canceled by the user"),t(l)?(console.log("User has passkeys but canceled the prompt"),n(s,!0)):(console.log("User likely has no passkeys, not showing manual button"),n(s,!1))):e.name==="SecurityError"?(console.error("Security error during passkey operation"),f.error("Security error during passkey operation"),n(s,t(l))):e.name==="AbortError"?(console.log("Passkey operation was aborted"),n(s,t(l))):e.name==="InvalidStateError"?(console.error("Invalid state during passkey operation"),f.error("Invalid state during passkey operation"),n(s,t(l))):e.name==="OperationError"&&e.message.includes("already pending")?(console.log("A WebAuthn request is already pending"),n(s,t(l))):(f.error(`Passkey error: ${e.message}`),n(s,t(l)))}finally{p=!1,y=!1,w!==void 0&&(window.clearTimeout(w),w=void 0),console.log("Conditional mediation completed, lock released")}}async function be(){if(console.log("Manual passkey sign-in initiated"),p){console.warn("WebAuthn request already in progress, cannot start a new one"),f.error("Please wait for the current operation to complete");return}y&&(console.warn("Forcing cleanup of ongoing conditional mediation"),y=!1,M(),await new Promise(e=>setTimeout(e,500)));try{p=!0,n(C,!0),r=new AbortController;const e=await fetch("/auth/passkey",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"getOptions"}),signal:r.signal});if(!e.ok)throw new Error("Failed to get authentication options");const o=await e.json();console.log("Authentication options received for manual sign-in:",o);const i=await Ie(o),c=await fetch("/auth/passkey",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"verify",authenticationResponse:i}),signal:r.signal});if(!c.ok)throw new Error("Failed to verify authentication");const d=await c.json();d.success?d.user?(n(T,d.user),f.success(`Welcome back, ${d.user.name}!`),setTimeout(()=>{N(d.redirectUrl||x())},1e3)):N(d.redirectUrl||x()):f.error(d.error||"Failed to sign in with passkey")}catch(e){console.error("Passkey authentication error:",e),e.name==="NotAllowedError"?console.log("Passkey operation was canceled or timed out"):e.name==="AbortError"?console.log("Passkey operation was aborted"):e.name==="SecurityError"?f.error("Security error during passkey operation"):e.name==="InvalidStateError"?f.error("Invalid state during passkey operation"):f.error(e.message||"Failed to sign in with passkey")}finally{p=!1,n(C,!1),r&&(r=null),console.log("Manual passkey sign-in completed, lock released")}}ke(()=>(console.log("Component mounted"),p=!1,y=!1,n(s,!1),n(C,!1),U.value=!1,M(),pe().then(e=>{L=e,console.log("Conditional mediation supported:",L),L&&ge()}),()=>{console.log("Component unmounted, cleaning up"),p=!1,y=!1,n(s,!1),n(C,!1),U.value=!1,M(),g!==void 0&&(window.clearTimeout(g),g=void 0)})),Pe();var j=Ue(),W=b(j),$=b(W);{var he=e=>{var o=Te(),i=b(o);_e(i,{class:"mr-2 h-4 w-4"});var c=v(i,2);{var d=a=>{var A=H("Signing in...");S(a,A)},m=(a,A)=>{{var P=u=>{var O=H();V(()=>te(O,`Welcome back, ${t(T).name??""}`)),S(u,O)},E=u=>{var O=H("Sign in with Passkey");S(u,O)};Y(a,u=>{t(T)?u(P):u(E,!1)},A)}};Y(c,a=>{t(C)?a(d):a(m,!1)})}h(o),V(()=>o.disabled=t(C)),R("click",o,be),S(e,o)};Y($,e=>{t(l)&&t(s)&&e(he)})}var ee=v($,2),ye=v(ee,2);h(W);var D=v(W,4),q=b(D);ie(q),h(D);var J=v(D,2),B=b(J);ie(B),h(J);var G=v(J,2),oe=b(G),we=b(oe);Se(we,{id:"remember-me",name:"remember-me",get checked(){return t(X)},set checked(e){n(X,e)},$$legacy:!0}),ne(2),h(oe),ne(2),h(G);var z=v(G,2),ve=b(z,!0);h(z),h(j),V(()=>{q.disabled=_(),B.disabled=_(),z.disabled=_(),te(ve,_()?"Signing in...":"Sign in")}),R("click",ee,fe),R("click",ye,ue),ae(q,()=>t(F),e=>n(F,e)),ae(B,()=>t(K),e=>n(K,e)),R("submit",j,Ae(ce)),S(se,j),Ce()}export{Ve as S};
