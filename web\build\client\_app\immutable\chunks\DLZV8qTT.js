import{c as n,a as d}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as l}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function k(a,o){const r=c(o,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5"}],["path",{d:"M8.5 8.5v.01"}],["path",{d:"M16 15.5v.01"}],["path",{d:"M12 12v.01"}],["path",{d:"M11 17v.01"}],["path",{d:"M7 14v.01"}]];f(a,l({name:"cookie"},()=>r,{get iconNode(){return s},children:(e,$)=>{var t=n(),p=i(t);m(p,o,"default",{},null),d(e,t)},$$slots:{default:!0}}))}export{k as C};
