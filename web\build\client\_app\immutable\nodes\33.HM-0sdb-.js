import{f as l,a as r,t as g}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as He,m as Z,g as e,f as S,a as Je,s as a,c as o,d as x,n as m,r as n,t as F,e as ee,aL as ce}from"../chunks/CGmarHxI.js";import{s as $}from"../chunks/CIt1g2O9.js";import{i as b}from"../chunks/u21ee2wt.js";import{s as Me}from"../chunks/B-Xjo-Yt.js";import{i as Ve}from"../chunks/BIEMS98f.js";import{p as Ke}from"../chunks/Btcx8l8F.js";import{g as te}from"../chunks/BiJhC7W5.js";import{t as h}from"../chunks/DjPYYl4Z.js";import{S as Qe}from"../chunks/C6g8ubaU.js";import{B as Y}from"../chunks/B1K98fMG.js";import{I as We}from"../chunks/DMTMHyMa.js";import{C as Xe}from"../chunks/DuGukytH.js";import{C as Ze}from"../chunks/Cdn-N1RY.js";import{C as et}from"../chunks/BkJY4La4.js";import{C as tt}from"../chunks/DETxXRrJ.js";import{C as rt}from"../chunks/GwmmX_iF.js";import{C as at}from"../chunks/D50jIuLr.js";import{R as st,A as ot,a as nt,b as lt,c as dt,d as it,e as ct,f as ut}from"../chunks/BnikQ10_.js";import{U as vt}from"../chunks/CSGDlQPw.js";var pt=l('<div class="flex h-64 items-center justify-center"><p class="text-lg text-gray-500">Loading document...</p></div>'),mt=l('<span class="mr-2">ATS Optimization</span> <span class="inline-flex h-5 items-center justify-center rounded-full bg-blue-100 px-2 text-xs font-medium text-blue-800">AI</span>',1),ft=l('<div class="mt-4"><!></div>'),_t=l("<!> <!>",1),gt=l('<div><span class="mb-2 block text-sm font-medium">Last Edited</span> <div class="flex flex-col"><span class="text-sm text-gray-500"> </span> <span class="text-xs text-gray-500"> </span></div></div>'),xt=l('<div><span class="mb-2 block text-sm font-medium">Source</span> <span> </span></div>'),$t=l('<span class="inline-block rounded bg-blue-100 px-2 py-0.5 text-xs text-blue-800">Default</span>'),bt=l('<span class="inline-block rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-800">Regular</span>'),ht=l('<div><span class="mb-2 block text-sm font-medium">Status</span> <!></div>'),yt=l('<span class="text-xs text-gray-500"> </span>'),Pt=l('<span class="inline-block rounded bg-green-100 px-2 py-0.5 text-xs text-green-800">Parsed</span> <!>',1),Dt=l('<span class="inline-block rounded bg-yellow-100 px-2 py-0.5 text-xs text-yellow-800">Not Parsed</span> <!>',1),kt=l('<div><span class="mb-2 block text-sm font-medium">Parse Status</span> <div class="flex items-center gap-2"><!></div></div>'),At=l('<div class="space-y-4"><div><label for="documentName" class="mb-2 block text-sm font-medium">Document Name</label> <!></div> <div><span class="mb-2 block text-sm font-medium">Document Type</span> <p class="text-sm text-gray-500"> </p></div> <div><span class="mb-2 block text-sm font-medium">Created</span> <div class="flex flex-col"><span class="text-sm text-gray-500"> </span> <span class="text-xs text-gray-500"> </span></div></div> <!> <div><span class="mb-2 block text-sm font-medium">File Name</span> <p class="text-sm text-gray-500"> </p></div> <!> <!> <!></div>'),Ct=l("<!> <!>",1),wt=l("<!> <!> <!>",1),St=l('<div class="grid grid-cols-1 gap-6 lg:grid-cols-2"><div class="order-2 lg:order-1"><div style="height: 600px;"><!></div> <!></div> <div class="order-1 lg:order-2"><!></div></div>'),Et=l('<div class="flex h-64 items-center justify-center"><p class="text-lg text-gray-500">Document not found</p></div>'),Tt=l("<!> <!>",1),Lt=l("<!> <!>",1),Nt=l("<!> <!>",1),jt=l('<!> <div class="container mx-auto p-6"><div class="mb-6 flex items-center justify-between"><div><h1 class="text-3xl font-bold">Edit Document</h1> <p class="text-gray-500">Update your document details</p></div> <div class="flex gap-2"><!> <!></div></div> <!></div> <!>',1);function rr(De,ue){He(ue,!1);let ke=Ke(ue,"data",8),t=Z(ke().document||null),Ae=!e(t),Q=Z(!1),z=Z(e(t)?e(t).label:""),q=Z(!1);async function Ce(){if(e(t)){x(Q,!0);try{const s=await fetch(`/api/documents/${e(t).id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({label:e(z)})});if(s.ok){const i=await s.json();h.success("Document updated",{description:"Your document has been updated successfully."}),ce(t,e(t).label=i.label||e(z)),ce(t,e(t).updatedAt=i.updatedAt||new Date().toISOString()),i.source&&ce(t,e(t).source=i.source),x(z,e(t).label)}else h.error("Error updating document",{description:"Could not update the document. Please try again."})}catch(s){console.error("Error updating document:",s),h.error("Error updating document",{description:"Could not update the document. Please try again."})}finally{x(Q,!1)}}}async function we(){if(e(t))try{h.loading("Sending resume for parsing...",{id:"parse-resume"});const s=await fetch(`/api/documents/${e(t).id}/parse`,{method:"POST"});if(s.ok)h.success("Resume parsing started",{id:"parse-resume",description:"Your resume is being analyzed. This may take a moment."}),x(t,{...e(t),isParsed:!1,parsedAt:null});else{let i="Failed to start resume parsing";try{const u=await s.json();console.error("Parse API error details:",u),u.error&&(i=u.error)}catch(u){console.error("Could not parse error response:",u)}console.error(i),h.error("Resume parsing failed",{id:"parse-resume",description:i})}}catch(s){console.error("Error sending resume to parsing queue:",s),h.error("Resume parsing failed",{id:"parse-resume",description:"An unexpected error occurred. Please try again."})}}function Se(){e(t)&&fetch(`/api/documents/${e(t).id}`,{method:"DELETE"}).then(s=>{var i;s.ok?(h.success("Document deleted",{description:`"${(i=e(t))==null?void 0:i.label}" has been removed from your documents.`}),te("/dashboard/documents")):h.error("Delete failed",{description:"There was a problem deleting your document. Please try again."}),x(q,!1)}).catch(s=>{console.error("Delete error:",s),h.error("Delete failed",{description:"There was a problem deleting your document. Please try again."}),x(q,!1)})}Ve();var ve=jt(),pe=S(ve);Qe(pe,{title:"Edit Document | Auto Apply",description:"Edit your document details"});var re=a(pe,2),ae=o(re),me=a(o(ae),2),fe=o(me);Y(fe,{variant:"outline",onclick:()=>te("/dashboard/documents"),children:(s,i)=>{m();var u=g("Cancel");r(s,u)},$$slots:{default:!0}});var Ee=a(fe,2);Y(Ee,{onclick:()=>x(q,!0),variant:"destructive",children:(s,i)=>{m();var u=g("Delete");r(s,u)},$$slots:{default:!0}}),n(me),n(ae);var Te=a(ae,2);{var Le=s=>{var i=pt();r(s,i)},Ne=(s,i)=>{{var u=y=>{var E=St(),G=o(E),N=o(G),se=o(N);vt(se,{get document(){return e(t)}}),n(N);var j=a(N,2);{var R=D=>{var v=ft(),I=o(v);Y(I,{variant:"outline",onclick:()=>te(`/dashboard/documents/${e(t).id}/ats`),children:(W,oe)=>{var ne=mt();m(2),r(W,ne)},$$slots:{default:!0}}),n(v),r(D,v)};b(j,D=>{e(t).type==="resume"&&e(t).resume&&D(R)})}n(G);var B=a(G,2),P=o(B);Xe(P,{children:(D,v)=>{var I=wt(),W=S(I);rt(W,{children:(H,ge)=>{var k=_t(),A=S(k);at(A,{children:(C,T)=>{m();var O=g("Document Details");r(C,O)},$$slots:{default:!0}});var J=a(A,2);et(J,{children:(C,T)=>{m();var O=g("Update the information for your document");r(C,O)},$$slots:{default:!0}}),r(H,k)},$$slots:{default:!0}});var oe=a(W,2);Ze(oe,{children:(H,ge)=>{var k=At(),A=o(k),J=a(o(A),2);We(J,{id:"documentName",placeholder:"Enter document name",get value(){return e(z)},set value(d){x(z,d)},$$legacy:!0}),n(A);var C=a(A,2),T=a(o(C),2),O=o(T,!0);n(T),n(C);var L=a(C,2),xe=a(o(L),2),le=o(xe),Re=o(le,!0);n(le);var $e=a(le,2),Ie=o($e,!0);n($e),n(xe),n(L);var be=a(L,2);{var Oe=d=>{var c=gt(),f=a(o(c),2),w=o(f),M=o(w,!0);n(w);var _=a(w,2),p=o(_,!0);n(_),n(f),n(c),F((U,V)=>{$(M,U),$(p,V)},[()=>new Date(e(t).updatedAt).toLocaleDateString(),()=>new Date(e(t).updatedAt).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})],ee),r(d,c)};b(be,d=>{e(t).updatedAt&&d(Oe)})}var de=a(be,2),he=a(o(de),2),Ue=o(he,!0);n(he),n(de);var ye=a(de,2);{var ze=d=>{var c=xt(),f=a(o(c),2),w=o(f,!0);n(f),n(c),F(()=>{Me(f,1,`inline-block rounded px-2 py-0.5 text-xs ${e(t).source==="generated"?"bg-purple-100 text-purple-800":e(t).source==="created"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`),$(w,e(t).source==="generated"?"Generated":e(t).source==="created"?"Created":"Uploaded")}),r(d,c)};b(ye,d=>{e(t).source&&d(ze)})}var Pe=a(ye,2);{var qe=d=>{var c=ht(),f=a(o(c),2);{var w=_=>{var p=$t();r(_,p)},M=_=>{var p=bt();r(_,p)};b(f,_=>{e(t).isDefault?_(w):_(M,!1)})}n(c),r(d,c)};b(Pe,d=>{e(t).isDefault!==void 0&&d(qe)})}var Be=a(Pe,2);{var Fe=d=>{var c=kt(),f=a(o(c),2),w=o(f);{var M=p=>{var U=Pt(),V=a(S(U),2);{var ie=X=>{var K=yt(),Ye=o(K);n(K),F(Ge=>$(Ye,`on ${Ge??""}`),[()=>new Date(e(t).parsedAt).toLocaleDateString()],ee),r(X,K)};b(V,X=>{e(t).parsedAt&&X(ie)})}r(p,U)},_=p=>{var U=Dt(),V=a(S(U),2);Y(V,{size:"sm",variant:"outline",class:"ml-2",onclick:we,children:(ie,X)=>{m();var K=g("Parse Now");r(ie,K)},$$slots:{default:!0}}),r(p,U)};b(w,p=>{e(t).isParsed?p(M):p(_,!1)})}n(f),n(c),r(d,c)};b(Be,d=>{e(t).type==="resume"&&d(Fe)})}n(k),F((d,c)=>{$(O,e(t).type==="resume"?"Resume":e(t).type==="cover_letter"?"Cover Letter":e(t).type),$(Re,d),$(Ie,c),$(Ue,e(t).fileName||"unknown.pdf")},[()=>new Date(e(t).createdAt).toLocaleDateString(),()=>new Date(e(t).createdAt).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})],ee),r(H,k)},$$slots:{default:!0}});var ne=a(oe,2);tt(ne,{class:"flex justify-between",children:(H,ge)=>{var k=Ct(),A=S(k);Y(A,{variant:"outline",onclick:()=>te("/dashboard/documents"),children:(T,O)=>{m();var L=g("Cancel");r(T,L)},$$slots:{default:!0}});var J=a(A,2);const C=ee(()=>e(Q)||e(z)===e(t).label);Y(J,{onclick:Ce,get disabled(){return e(C)},children:(T,O)=>{m();var L=g();F(()=>$(L,e(Q)?"Saving...":"Save Changes")),r(T,L)},$$slots:{default:!0}}),r(H,k)},$$slots:{default:!0}}),r(D,I)},$$slots:{default:!0}}),n(B),n(E),r(y,E)},_e=y=>{var E=Et();r(y,E)};b(s,y=>{e(t)?y(u):y(_e,!1)},i)}};b(Te,s=>{Ae?s(Le):s(Ne,!1)})}n(re);var je=a(re,2);st(je,{get open(){return e(q)},set open(s){x(q,s)},children:(s,i)=>{ot(s,{children:(u,_e)=>{var y=Nt(),E=S(y);nt(E,{children:(N,se)=>{var j=Tt(),R=S(j);lt(R,{children:(P,D)=>{m();var v=g("Are you sure?");r(P,v)},$$slots:{default:!0}});var B=a(R,2);dt(B,{children:(P,D)=>{m();var v=g();F(()=>{var I;return $(v,`This will permanently delete "${((I=e(t))==null?void 0:I.label)??""}" from your documents. This action cannot be
        undone.`)}),r(P,v)},$$slots:{default:!0}}),r(N,j)},$$slots:{default:!0}});var G=a(E,2);it(G,{children:(N,se)=>{var j=Lt(),R=S(j);ct(R,{onclick:()=>x(q,!1),children:(P,D)=>{m();var v=g("Cancel");r(P,v)},$$slots:{default:!0}});var B=a(R,2);ut(B,{onclick:Se,children:(P,D)=>{m();var v=g("Delete");r(P,v)},$$slots:{default:!0}}),r(N,j)},$$slots:{default:!0}}),r(u,y)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),r(De,ve),Je()}export{rr as component};
