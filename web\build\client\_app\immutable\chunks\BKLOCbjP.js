import{c as f,a as o,f as x}from"./BasJTneF.js";import{p as P,f as d,a as w,g as p,x as k,c as y,au as I,r as S}from"./CGmarHxI.js";import{s as c}from"./ncUU1dSD.js";import{i as j}from"./u21ee2wt.js";import{e as q}from"./B-Xjo-Yt.js";import{p as n,r as z}from"./Btcx8l8F.js";import{b as l,m as A}from"./BfX7a-t9.js";import{u as B}from"./DMoa_yM9.js";import{u as C}from"./CnMg5bH0.js";var E=x("<div><!></div>");function Q(v,e){P(e,!0);let h=n(e,"id",19,C),s=n(e,"ref",15,null),u=z(e,["$$slots","$$events","$$legacy","id","children","child","ref"]);const _=B({id:l.with(()=>h()),ref:l.with(()=>s(),r=>s(r))}),t=k(()=>A(u,_.props));var m=f(),g=d(m);{var b=r=>{var a=f(),i=d(a);c(i,()=>e.child,()=>({props:p(t)})),o(r,a)},D=r=>{var a=E();q(a,()=>({...p(t)}));var i=y(a);c(i,()=>e.children??I),S(a),o(r,a)};j(g,r=>{e.child?r(b):r(D,!1)})}o(v,m),w()}export{Q as D};
