import{f as v,a as i,t as S,c as Ve}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as Je,a as Ke}from"../chunks/nZgk9enP.js";import{p as Qe,f as N,t as u,c as s,a as Xe,s as o,o as p,g as n,e as B,r,n as C,q as Ze,d as pe,m as ea}from"../chunks/CGmarHxI.js";import{s as m}from"../chunks/CIt1g2O9.js";import{i as h}from"../chunks/u21ee2wt.js";import{e as me,i as fe}from"../chunks/C3w0v0gR.js";import{c as aa}from"../chunks/BvdI7LR8.js";import{a as ta}from"../chunks/DDUgF6Ik.js";import{d as ra,a as _e}from"../chunks/B-Xjo-Yt.js";import{a as R,s as sa,b as j,e as oa}from"../chunks/CmxjS0TN.js";import{i as ia}from"../chunks/BIEMS98f.js";import{p as na}from"../chunks/Btcx8l8F.js";import{t as ge}from"../chunks/DjPYYl4Z.js";import{S as la}from"../chunks/C6g8ubaU.js";import{B as da}from"../chunks/B1K98fMG.js";import{I as V}from"../chunks/DMTMHyMa.js";import{T as va}from"../chunks/VNuMAkuB.js";import{L as M}from"../chunks/BvvicRXk.js";import{s as ca}from"../chunks/B8blszX7.js";import{i as ua,c as pa,s as ma}from"../chunks/NEMeLqAU.js";import{M as fa}from"../chunks/QtAhPN2H.js";import{M as _a}from"../chunks/yPulTJ2h.js";import{C as ga}from"../chunks/CsOU4yHs.js";import{M as ba}from"../chunks/BQS6hE8b.js";import{S as ha}from"../chunks/D871oxnv.js";import{A as be}from"../chunks/Cs0qIT7f.js";var xa=v('<span class="flex items-center">Loading chat <span class="ml-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></span></span>'),$a=v(" <!>",1),ya=v('<button class="text-primary inline-flex items-center text-sm font-medium hover:underline"><!></button>'),wa=v('<a class="text-primary inline-flex items-center text-sm font-medium hover:underline"> <!></a>'),Ca=v('<div class="border-border flex-1 border p-12"><div class="flex items-start gap-4"><div class="bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="mb-2 text-xl font-medium"> </h3> <p class="text-muted-foreground mb-4 text-sm"> </p> <!></div></div></div>'),ka=v('<p class="text-destructive text-sm"> </p>'),Sa=v('<p class="text-destructive text-sm"> </p>'),ja=v("<option> </option>"),Ma=v('<p class="text-destructive text-sm"> </p>'),Pa=v('<p class="text-destructive text-sm"> </p>'),qa=v('<p class="text-destructive text-sm"> </p>'),Ba=v("<span>Sending...</span>"),La=v('<span class="flex items-center justify-center"><!> Send Message</span>'),Ha=v(`<!> <section class="bg-background text-foreground relative grid grid-cols-4"><div class="border-border col-span-1 flex h-full flex-col divide-y border-r p-12"></div> <div class="col-span-3"><div class="border-border flex flex-col gap-4 border-b p-12"><h1 class="text-4xl font-bold">Contact Us</h1> <p class="text-muted-foreground text-lg">Have questions or need assistance? We're here to help.</p></div> <div class="py-18 px-22"><form method="POST" class="space-y-6"><div class="grid gap-6 sm:grid-cols-2"><div class="space-y-2"><!> <!> <!></div> <div class="space-y-2"><!> <!> <!></div></div> <div class="space-y-2"><!> <select id="department" name="department" class="border-input bg-background ring-offset-background focus-visible:ring-ring w-full rounded-md border px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"><option disabled selected>Select a department</option><!></select> <!></div> <div class="space-y-2"><!> <!> <!></div> <div class="space-y-2"><!> <!> <!></div> <!></form></div></div></section>`,1);function lt(he,J){Qe(J,!1);const[L,xe]=sa(),l=()=>R(w,"$form",L),d=()=>R(ye,"$errors",L),K=()=>R(we,"$submitting",L);let $e=na(J,"data",8);const{form:w,errors:ye,enhance:H,submitting:we}=ca($e().form,{onResult:({result:e})=>{e.type==="success"?ge.success("Your message has been sent successfully!"):ge.error("An error occurred. Please try again.")}}),Ce=[{value:"general",label:"General Inquiries"},{value:"support",label:"Technical Support"},{value:"sales",label:"Sales"},{value:"partnerships",label:"Partnerships"},{value:"careers",label:"Careers"},{value:"press",label:"Press & Media"},{value:"legal",label:"Legal"}];let P=ea(!1);Je(()=>{console.log("Contact page mounted, initializing Botpress..."),ua(void 0,!0)}),Ke(()=>{console.log("Contact page unmounted, cleaning up Botpress..."),pa()});const ke=[{icon:fa,title:"Live Chat",description:"Chat with our support team in real-time during business hours.",action:"Start Chat",link:"#",onClick:()=>{console.log("Opening chat from contact page..."),pe(P,!0),ma(!0),setTimeout(()=>{pe(P,!1)},2e3)}},{icon:_a,title:"Email Support",description:"Send us an email and we'll respond within 24 hours.",action:"Email Us",link:"mailto:<EMAIL>"},{icon:ga,title:"Help Center",description:"Browse our knowledge base for answers to common questions.",action:"Visit Help Center",link:"/help"},{icon:ba,title:"Submit Feedback",description:"Share your thoughts and suggestions to help us improve.",action:"Give Feedback",link:"https://autoapply.featurebase.app/"}];ia();var Q=Ha(),X=N(Q);la(X,{title:"Contact Us | Hirli",description:"Get in touch with the Hirli team. We're here to help with any questions or issues you may have about our job application automation platform.",keywords:"contact, support, help, customer service, Hirli support"});var Z=o(X,2),O=s(Z);me(O,5,()=>ke,fe,(e,a)=>{var t=Ca(),x=s(t),$=s(x),U=s($);aa(U,()=>n(a).icon,(g,c)=>{c(g,{class:"h-5 w-5"})}),r($);var f=o($,2),_=s(f),W=s(_,!0);r(_);var z=o(_,2),Ue=s(z,!0);r(z);var We=o(z,2);{var ze=g=>{var c=ya(),q=s(c);{var D=b=>{var y=xa();i(b,y)},Ne=b=>{var y=$a(),ue=N(y),Re=o(ue);be(Re,{class:"ml-1 h-4 w-4"}),u(()=>m(ue,`${n(a).action??""} `)),i(b,y)};h(q,b=>{n(P)&&n(a).title==="Live Chat"?b(D):b(Ne,!1)})}r(c),u(()=>c.disabled=n(P)),oa("click",c,function(...b){var y;(y=n(a).onClick)==null||y.apply(this,b)}),i(g,c)},De=g=>{var c=wa(),q=s(c),D=o(q);be(D,{class:"ml-1 h-4 w-4"}),r(c),u(()=>{_e(c,"href",n(a).link),m(q,`${n(a).action??""} `)}),i(g,c)};h(We,g=>{n(a).onClick?g(ze):g(De,!1)})}r(f),r(x),r(t),u(()=>{m(W,n(a).title),m(Ue,n(a).description)}),i(e,t)}),r(O);var ee=o(O,2),ae=o(s(ee),2),T=s(ae),Y=s(T),A=s(Y),te=s(A);M(te,{for:"name",children:(e,a)=>{C();var t=S("Name");i(e,t)},$$slots:{default:!0}});var re=o(te,2);const Se=B(()=>d().name?"true":void 0);V(re,{id:"name",name:"name",placeholder:"Your name",get"aria-invalid"(){return n(Se)},get value(){return l().name},set value(e){j(w,p(l).name=e,p(l))},$$legacy:!0});var je=o(re,2);{var Me=e=>{var a=ka(),t=s(a,!0);r(a),u(()=>m(t,d().name)),i(e,a)};h(je,e=>{d().name&&e(Me)})}r(A);var se=o(A,2),oe=s(se);M(oe,{for:"email",children:(e,a)=>{C();var t=S("Email");i(e,t)},$$slots:{default:!0}});var ie=o(oe,2);const Pe=B(()=>d().email?"true":void 0);V(ie,{id:"email",name:"email",type:"email",placeholder:"Your email address",get"aria-invalid"(){return n(Pe)},get value(){return l().email},set value(e){j(w,p(l).email=e,p(l))},$$legacy:!0});var qe=o(ie,2);{var Be=e=>{var a=Sa(),t=s(a,!0);r(a),u(()=>m(t,d().email)),i(e,a)};h(qe,e=>{d().email&&e(Be)})}r(se),r(Y);var E=o(Y,2),ne=s(E);M(ne,{for:"department",children:(e,a)=>{C();var t=S("Department");i(e,t)},$$slots:{default:!0}});var k=o(ne,2);u(()=>{l(),Ze(()=>{d()})});var F=s(k);F.value=F.__value="";var Le=o(F);me(Le,1,()=>Ce,fe,(e,a)=>{var t=ja(),x={},$=s(t,!0);r(t),u(()=>{x!==(x=n(a).value)&&(t.value=(t.__value=n(a).value)??""),m($,n(a).label)}),i(e,t)}),r(k);var He=o(k,2);{var Oe=e=>{var a=Ma(),t=s(a,!0);r(a),u(()=>m(t,d().department)),i(e,a)};h(He,e=>{d().department&&e(Oe)})}r(E);var G=o(E,2),le=s(G);M(le,{for:"subject",children:(e,a)=>{C();var t=S("Subject");i(e,t)},$$slots:{default:!0}});var de=o(le,2);const Te=B(()=>d().subject?"true":void 0);V(de,{id:"subject",name:"subject",placeholder:"What is your message about?",get"aria-invalid"(){return n(Te)},get value(){return l().subject},set value(e){j(w,p(l).subject=e,p(l))},$$legacy:!0});var Ye=o(de,2);{var Ae=e=>{var a=Pa(),t=s(a,!0);r(a),u(()=>m(t,d().subject)),i(e,a)};h(Ye,e=>{d().subject&&e(Ae)})}r(G);var I=o(G,2),ve=s(I);M(ve,{for:"message",children:(e,a)=>{C();var t=S("Message");i(e,t)},$$slots:{default:!0}});var ce=o(ve,2);const Ee=B(()=>d().message?"true":void 0);va(ce,{id:"message",name:"message",placeholder:"Your message",rows:5,get"aria-invalid"(){return n(Ee)},get value(){return l().message},set value(e){j(w,p(l).message=e,p(l))},$$legacy:!0});var Fe=o(ce,2);{var Ge=e=>{var a=qa(),t=s(a,!0);r(a),u(()=>m(t,d().message)),i(e,a)};h(Fe,e=>{d().message&&e(Ge)})}r(I);var Ie=o(I,2);da(Ie,{type:"submit",class:"w-full",variant:"default",size:"lg",get disabled(){return K()},children:(e,a)=>{var t=Ve(),x=N(t);{var $=f=>{var _=Ba();i(f,_)},U=f=>{var _=La(),W=s(_);ha(W,{class:"mr-2 h-4 w-4"}),C(),r(_),i(f,_)};h(x,f=>{K()?f($):f(U,!1)})}i(e,t)},$$slots:{default:!0}}),r(T),ta(T,e=>H==null?void 0:H(e)),r(ae),r(ee),r(Z),u(()=>_e(k,"aria-invalid",d().department?"true":void 0)),ra(k,()=>l().department,e=>j(w,p(l).department=e,p(l))),i(he,Q),Xe(),xe()}export{lt as component};
