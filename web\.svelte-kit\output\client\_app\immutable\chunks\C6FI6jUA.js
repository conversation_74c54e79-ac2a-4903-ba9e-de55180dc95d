import{f as Mt,a as Pt}from"./BasJTneF.js";import{aE as yn,aC as bn,aD as xn,p as wt,v as vn,i as At,a as Ft,c as St,au as Nt,r as yt}from"./CGmarHxI.js";import{s as kt,c as zt}from"./ncUU1dSD.js";import{e as Bt}from"./B-Xjo-Yt.js";import{b as Vt}from"./5V1tIHTN.js";import{p as tt,r as jt}from"./Btcx8l8F.js";import{e as En}from"./CmxjS0TN.js";import{a as Ln}from"./DDUgF6Ik.js";const Ct=Symbol("EMBLA_CAROUSEL_CONTEXT");function In(t){return yn(Ct,t),t}function on(t="This component"){if(!bn(Ct))throw new Error(`${t} must be used within a <Carousel.Root> component`);return xn(Ct)}var Tn=Mt("<div><!></div>");function he(t,n){wt(n,!0);let r=tt(n,"ref",15,null),i=tt(n,"opts",19,()=>({})),c=tt(n,"plugins",19,()=>[]),s=tt(n,"setApi",3,()=>{}),e=tt(n,"orientation",3,"horizontal"),a=jt(n,["$$slots","$$events","$$legacy","ref","opts","plugins","setApi","orientation","class","children"]),o=vn({api:void 0,scrollPrev:l,scrollNext:g,orientation:e(),canScrollNext:!1,canScrollPrev:!1,handleKeyDown:b,options:i(),plugins:c(),onInit:f,scrollSnaps:[],selectedIndex:0,scrollTo:d});In(o);function l(){var u;(u=o.api)==null||u.scrollPrev()}function g(){var u;(u=o.api)==null||u.scrollNext()}function d(u,S){var x;(x=o.api)==null||x.scrollTo(u,S)}function p(u){u&&(o.canScrollPrev=u.canScrollPrev(),o.canScrollNext=u.canScrollNext(),o.selectedIndex=u.selectedScrollSnap())}At(()=>{o.api&&(p(o.api),o.api.on("select",p),o.api.on("reInit",p))});function b(u){u.key==="ArrowLeft"?(u.preventDefault(),l()):u.key==="ArrowRight"&&(u.preventDefault(),g())}At(()=>{s()(o.api)});function f(u){o.api=u.detail,o.scrollSnaps=o.api.scrollSnapList()}At(()=>()=>{var u;(u=o.api)==null||u.off("select",p)});var m=Tn();Bt(m,u=>({"data-slot":"carousel",class:u,role:"region","aria-roledescription":"carousel",...a}),[()=>zt("relative",n.class)]);var h=St(m);kt(h,()=>n.children??Nt),yt(m),Vt(m,u=>r(u),()=>r()),Pt(t,m),Ft()}function An(t){return Object.prototype.toString.call(t)==="[object Object]"}function Wt(t){return An(t)||Array.isArray(t)}function Cn(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function _t(t,n){const r=Object.keys(t),i=Object.keys(n);if(r.length!==i.length)return!1;const c=JSON.stringify(Object.keys(t.breakpoints||{})),s=JSON.stringify(Object.keys(n.breakpoints||{}));return c!==s?!1:r.every(e=>{const a=t[e],o=n[e];return typeof a=="function"?`${a}`==`${o}`:!Wt(a)||!Wt(o)?a===o:_t(a,o)})}function tn(t){return t.concat().sort((n,r)=>n.name>r.name?1:-1).map(n=>n.options)}function On(t,n){if(t.length!==n.length)return!1;const r=tn(t),i=tn(n);return r.every((c,s)=>{const e=i[s];return _t(c,e)})}function Gt(t){return typeof t=="number"}function Ot(t){return typeof t=="string"}function xt(t){return typeof t=="boolean"}function nn(t){return Object.prototype.toString.call(t)==="[object Object]"}function M(t){return Math.abs(t)}function Ht(t){return Math.sign(t)}function lt(t,n){return M(t-n)}function Dn(t,n){if(t===0||n===0||M(t)<=M(n))return 0;const r=lt(M(t),M(n));return M(r/t)}function Mn(t){return Math.round(t*100)/100}function dt(t){return pt(t).map(Number)}function V(t){return t[gt(t)]}function gt(t){return Math.max(0,t.length-1)}function Rt(t,n){return n===gt(t)}function en(t,n=0){return Array.from(Array(t),(r,i)=>n+i)}function pt(t){return Object.keys(t)}function rn(t,n){return[t,n].reduce((r,i)=>(pt(i).forEach(c=>{const s=r[c],e=i[c],a=nn(s)&&nn(e);r[c]=a?rn(s,e):e}),r),{})}function Dt(t,n){return typeof n.MouseEvent<"u"&&t instanceof n.MouseEvent}function Pn(t,n){const r={start:i,center:c,end:s};function i(){return 0}function c(o){return s(o)/2}function s(o){return n-o}function e(o,l){return Ot(t)?r[t](o):t(n,o,l)}return{measure:e}}function mt(){let t=[];function n(c,s,e,a={passive:!0}){let o;if("addEventListener"in c)c.addEventListener(s,e,a),o=()=>c.removeEventListener(s,e,a);else{const l=c;l.addListener(e),o=()=>l.removeListener(e)}return t.push(o),i}function r(){t=t.filter(c=>c())}const i={add:n,clear:r};return i}function wn(t,n,r,i){const c=mt(),s=1e3/60;let e=null,a=0,o=0;function l(){c.add(t,"visibilitychange",()=>{t.hidden&&f()})}function g(){b(),c.clear()}function d(h){if(!o)return;e||(e=h,r(),r());const u=h-e;for(e=h,a+=u;a>=s;)r(),a-=s;const S=a/s;i(S),o&&(o=n.requestAnimationFrame(d))}function p(){o||(o=n.requestAnimationFrame(d))}function b(){n.cancelAnimationFrame(o),e=null,a=0,o=0}function f(){e=null,a=0}return{init:l,destroy:g,start:p,stop:b,update:r,render:i}}function Fn(t,n){const r=n==="rtl",i=t==="y",c=i?"y":"x",s=i?"x":"y",e=!i&&r?-1:1,a=g(),o=d();function l(f){const{height:m,width:h}=f;return i?m:h}function g(){return i?"top":r?"right":"left"}function d(){return i?"bottom":r?"left":"right"}function p(f){return f*e}return{scroll:c,cross:s,startEdge:a,endEdge:o,measureSize:l,direction:p}}function nt(t=0,n=0){const r=M(t-n);function i(l){return l<t}function c(l){return l>n}function s(l){return i(l)||c(l)}function e(l){return s(l)?i(l)?t:n:l}function a(l){return r?l-r*Math.ceil((l-n)/r):l}return{length:r,max:n,min:t,constrain:e,reachedAny:s,reachedMax:c,reachedMin:i,removeOffset:a}}function sn(t,n,r){const{constrain:i}=nt(0,t),c=t+1;let s=e(n);function e(p){return r?M((c+p)%c):i(p)}function a(){return s}function o(p){return s=e(p),d}function l(p){return g().set(a()+p)}function g(){return sn(t,a(),r)}const d={get:a,set:o,add:l,clone:g};return d}function Nn(t,n,r,i,c,s,e,a,o,l,g,d,p,b,f,m,h,u,S){const{cross:x,direction:L}=t,O=["INPUT","SELECT","TEXTAREA"],I={passive:!1},v=mt(),E=mt(),T=nt(50,225).constrain(b.measure(20)),P={mouse:300,touch:400},A={mouse:500,touch:600},k=f?43:25;let j=!1,_=0,G=0,Z=!1,J=!1,U=!1,q=!1;function st(y){if(!S)return;function C(N){(xt(S)||S(y,N))&&ct(N)}const w=n;v.add(w,"dragstart",N=>N.preventDefault(),I).add(w,"touchmove",()=>{},I).add(w,"touchend",()=>{}).add(w,"touchstart",C).add(w,"mousedown",C).add(w,"touchcancel",F).add(w,"contextmenu",F).add(w,"click",X,!0)}function H(){v.clear(),E.clear()}function et(){const y=q?r:n;E.add(y,"touchmove",z,I).add(y,"touchend",F).add(y,"mousemove",z,I).add(y,"mouseup",F)}function ot(y){const C=y.nodeName||"";return O.includes(C)}function K(){return(f?A:P)[q?"mouse":"touch"]}function it(y,C){const w=d.add(Ht(y)*-1),N=g.byDistance(y,!f).distance;return f||M(y)<T?N:h&&C?N*.5:g.byIndex(w.get(),0).distance}function ct(y){const C=Dt(y,i);q=C,U=f&&C&&!y.buttons&&j,j=lt(c.get(),e.get())>=2,!(C&&y.button!==0)&&(ot(y.target)||(Z=!0,s.pointerDown(y),l.useFriction(0).useDuration(0),c.set(e),et(),_=s.readPoint(y),G=s.readPoint(y,x),p.emit("pointerDown")))}function z(y){if(!Dt(y,i)&&y.touches.length>=2)return F(y);const w=s.readPoint(y),N=s.readPoint(y,x),R=lt(w,_),Q=lt(N,G);if(!J&&!q&&(!y.cancelable||(J=R>Q,!J)))return F(y);const $=s.pointerMove(y);R>m&&(U=!0),l.useFriction(.3).useDuration(.75),a.start(),c.add(L($)),y.preventDefault()}function F(y){const w=g.byDistance(0,!1).index!==d.get(),N=s.pointerUp(y)*K(),R=it(L(N),w),Q=Dn(N,R),$=k-10*Q,Y=u+Q/50;J=!1,Z=!1,E.clear(),l.useDuration($).useFriction(Y),o.distance(R,!f),q=!1,p.emit("pointerUp")}function X(y){U&&(y.stopPropagation(),y.preventDefault(),U=!1)}function B(){return Z}return{init:st,destroy:H,pointerDown:B}}function kn(t,n){let i,c;function s(d){return d.timeStamp}function e(d,p){const f=`client${(p||t.scroll)==="x"?"X":"Y"}`;return(Dt(d,n)?d:d.touches[0])[f]}function a(d){return i=d,c=d,e(d)}function o(d){const p=e(d)-e(c),b=s(d)-s(i)>170;return c=d,b&&(i=d),p}function l(d){if(!i||!c)return 0;const p=e(c)-e(i),b=s(d)-s(i),f=s(d)-s(c)>170,m=p/b;return b&&!f&&M(m)>.1?m:0}return{pointerDown:a,pointerMove:o,pointerUp:l,readPoint:e}}function zn(){function t(r){const{offsetTop:i,offsetLeft:c,offsetWidth:s,offsetHeight:e}=r;return{top:i,right:c+s,bottom:i+e,left:c,width:s,height:e}}return{measure:t}}function Bn(t){function n(i){return t*(i/100)}return{measure:n}}function Vn(t,n,r,i,c,s,e){const a=[t].concat(i);let o,l,g=[],d=!1;function p(h){return c.measureSize(e.measure(h))}function b(h){if(!s)return;l=p(t),g=i.map(p);function u(S){for(const x of S){if(d)return;const L=x.target===t,O=i.indexOf(x.target),I=L?l:g[O],v=p(L?t:i[O]);if(M(v-I)>=.5){h.reInit(),n.emit("resize");break}}}o=new ResizeObserver(S=>{(xt(s)||s(h,S))&&u(S)}),r.requestAnimationFrame(()=>{a.forEach(S=>o.observe(S))})}function f(){d=!0,o&&o.disconnect()}return{init:b,destroy:f}}function jn(t,n,r,i,c,s){let e=0,a=0,o=c,l=s,g=t.get(),d=0;function p(){const I=i.get()-t.get(),v=!o;let E=0;return v?(e=0,r.set(i),t.set(i),E=I):(r.set(t),e+=I/o,e*=l,g+=e,t.add(e),E=g-d),a=Ht(E),d=g,O}function b(){const I=i.get()-n.get();return M(I)<.001}function f(){return o}function m(){return a}function h(){return e}function u(){return x(c)}function S(){return L(s)}function x(I){return o=I,O}function L(I){return l=I,O}const O={direction:m,duration:f,velocity:h,seek:p,settled:b,useBaseFriction:S,useBaseDuration:u,useFriction:L,useDuration:x};return O}function _n(t,n,r,i,c){const s=c.measure(10),e=c.measure(50),a=nt(.1,.99);let o=!1;function l(){return!(o||!t.reachedAny(r.get())||!t.reachedAny(n.get()))}function g(b){if(!l())return;const f=t.reachedMin(n.get())?"min":"max",m=M(t[f]-n.get()),h=r.get()-n.get(),u=a.constrain(m/e);r.subtract(h*u),!b&&M(h)<s&&(r.set(t.constrain(r.get())),i.useDuration(25).useBaseFriction())}function d(b){o=!b}return{shouldConstrain:l,constrain:g,toggleActive:d}}function Gn(t,n,r,i,c){const s=nt(-n+t,0),e=d(),a=g(),o=p();function l(f,m){return lt(f,m)<=1}function g(){const f=e[0],m=V(e),h=e.lastIndexOf(f),u=e.indexOf(m)+1;return nt(h,u)}function d(){return r.map((f,m)=>{const{min:h,max:u}=s,S=s.constrain(f),x=!m,L=Rt(r,m);return x?u:L||l(h,S)?h:l(u,S)?u:S}).map(f=>parseFloat(f.toFixed(3)))}function p(){if(n<=t+c)return[s.max];if(i==="keepSnaps")return e;const{min:f,max:m}=a;return e.slice(f,m)}return{snapsContained:o,scrollContainLimit:a}}function Hn(t,n,r){const i=n[0],c=r?i-t:V(n);return{limit:nt(c,i)}}function Rn(t,n,r,i){const s=n.min+.1,e=n.max+.1,{reachedMin:a,reachedMax:o}=nt(s,e);function l(p){return p===1?o(r.get()):p===-1?a(r.get()):!1}function g(p){if(!l(p))return;const b=t*(p*-1);i.forEach(f=>f.add(b))}return{loop:g}}function Un(t){const{max:n,length:r}=t;function i(s){const e=s-n;return r?e/-r:0}return{get:i}}function qn(t,n,r,i,c){const{startEdge:s,endEdge:e}=t,{groupSlides:a}=c,o=d().map(n.measure),l=p(),g=b();function d(){return a(i).map(m=>V(m)[e]-m[0][s]).map(M)}function p(){return i.map(m=>r[s]-m[s]).map(m=>-M(m))}function b(){return a(l).map(m=>m[0]).map((m,h)=>m+o[h])}return{snaps:l,snapsAligned:g}}function Kn(t,n,r,i,c,s){const{groupSlides:e}=c,{min:a,max:o}=i,l=g();function g(){const p=e(s),b=!t||n==="keepSnaps";return r.length===1?[s]:b?p:p.slice(a,o).map((f,m,h)=>{const u=!m,S=Rt(h,m);if(u){const x=V(h[0])+1;return en(x)}if(S){const x=gt(s)-V(h)[0]+1;return en(x,V(h)[0])}return f})}return{slideRegistry:l}}function Xn(t,n,r,i,c){const{reachedAny:s,removeOffset:e,constrain:a}=i;function o(f){return f.concat().sort((m,h)=>M(m)-M(h))[0]}function l(f){const m=t?e(f):a(f),h=n.map((S,x)=>({diff:g(S-m,0),index:x})).sort((S,x)=>M(S.diff)-M(x.diff)),{index:u}=h[0];return{index:u,distance:m}}function g(f,m){const h=[f,f+r,f-r];if(!t)return f;if(!m)return o(h);const u=h.filter(S=>Ht(S)===m);return u.length?o(u):V(h)-r}function d(f,m){const h=n[f]-c.get(),u=g(h,m);return{index:f,distance:u}}function p(f,m){const h=c.get()+f,{index:u,distance:S}=l(h),x=!t&&s(h);if(!m||x)return{index:u,distance:f};const L=n[u]-S,O=f+g(L,0);return{index:u,distance:O}}return{byDistance:p,byIndex:d,shortcut:g}}function Qn(t,n,r,i,c,s,e){function a(d){const p=d.distance,b=d.index!==n.get();s.add(p),p&&(i.duration()?t.start():(t.update(),t.render(1),t.update())),b&&(r.set(n.get()),n.set(d.index),e.emit("select"))}function o(d,p){const b=c.byDistance(d,p);a(b)}function l(d,p){const b=n.clone().set(d),f=c.byIndex(b.get(),p);a(f)}return{distance:o,index:l}}function Jn(t,n,r,i,c,s,e,a){const o={passive:!0,capture:!0};let l=0;function g(b){if(!a)return;function f(m){if(new Date().getTime()-l>10)return;e.emit("slideFocusStart"),t.scrollLeft=0;const S=r.findIndex(x=>x.includes(m));Gt(S)&&(c.useDuration(0),i.index(S,0),e.emit("slideFocus"))}s.add(document,"keydown",d,!1),n.forEach((m,h)=>{s.add(m,"focus",u=>{(xt(a)||a(b,u))&&f(h)},o)})}function d(b){b.code==="Tab"&&(l=new Date().getTime())}return{init:g}}function at(t){let n=t;function r(){return n}function i(o){n=e(o)}function c(o){n+=e(o)}function s(o){n-=e(o)}function e(o){return Gt(o)?o:o.get()}return{get:r,set:i,add:c,subtract:s}}function cn(t,n){const r=t.scroll==="x"?e:a,i=n.style;let c=null,s=!1;function e(p){return`translate3d(${p}px,0px,0px)`}function a(p){return`translate3d(0px,${p}px,0px)`}function o(p){if(s)return;const b=Mn(t.direction(p));b!==c&&(i.transform=r(b),c=b)}function l(p){s=!p}function g(){s||(i.transform="",n.getAttribute("style")||n.removeAttribute("style"))}return{clear:g,to:o,toggleActive:l}}function Yn(t,n,r,i,c,s,e,a,o){const g=dt(c),d=dt(c).reverse(),p=u().concat(S());function b(v,E){return v.reduce((T,P)=>T-c[P],E)}function f(v,E){return v.reduce((T,P)=>b(T,E)>0?T.concat([P]):T,[])}function m(v){return s.map((E,T)=>({start:E-i[T]+.5+v,end:E+n-.5+v}))}function h(v,E,T){const P=m(E);return v.map(A=>{const k=T?0:-r,j=T?r:0,_=T?"end":"start",G=P[A][_];return{index:A,loopPoint:G,slideLocation:at(-1),translate:cn(t,o[A]),target:()=>a.get()>G?k:j}})}function u(){const v=e[0],E=f(d,v);return h(E,r,!1)}function S(){const v=n-e[0]-1,E=f(g,v);return h(E,-r,!0)}function x(){return p.every(({index:v})=>{const E=g.filter(T=>T!==v);return b(E,n)<=.1})}function L(){p.forEach(v=>{const{target:E,translate:T,slideLocation:P}=v,A=E();A!==P.get()&&(T.to(A),P.set(A))})}function O(){p.forEach(v=>v.translate.clear())}return{canLoop:x,clear:O,loop:L,loopPoints:p}}function Zn(t,n,r){let i,c=!1;function s(o){if(!r)return;function l(g){for(const d of g)if(d.type==="childList"){o.reInit(),n.emit("slidesChanged");break}}i=new MutationObserver(g=>{c||(xt(r)||r(o,g))&&l(g)}),i.observe(t,{childList:!0})}function e(){i&&i.disconnect(),c=!0}return{init:s,destroy:e}}function $n(t,n,r,i){const c={};let s=null,e=null,a,o=!1;function l(){a=new IntersectionObserver(f=>{o||(f.forEach(m=>{const h=n.indexOf(m.target);c[h]=m}),s=null,e=null,r.emit("slidesInView"))},{root:t.parentElement,threshold:i}),n.forEach(f=>a.observe(f))}function g(){a&&a.disconnect(),o=!0}function d(f){return pt(c).reduce((m,h)=>{const u=parseInt(h),{isIntersecting:S}=c[u];return(f&&S||!f&&!S)&&m.push(u),m},[])}function p(f=!0){if(f&&s)return s;if(!f&&e)return e;const m=d(f);return f&&(s=m),f||(e=m),m}return{init:l,destroy:g,get:p}}function Wn(t,n,r,i,c,s){const{measureSize:e,startEdge:a,endEdge:o}=t,l=r[0]&&c,g=f(),d=m(),p=r.map(e),b=h();function f(){if(!l)return 0;const S=r[0];return M(n[a]-S[a])}function m(){if(!l)return 0;const S=s.getComputedStyle(V(i));return parseFloat(S.getPropertyValue(`margin-${o}`))}function h(){return r.map((S,x,L)=>{const O=!x,I=Rt(L,x);return O?p[x]+g:I?p[x]+d:L[x+1][a]-S[a]}).map(M)}return{slideSizes:p,slideSizesWithGaps:b,startGap:g,endGap:d}}function te(t,n,r,i,c,s,e,a,o){const{startEdge:l,endEdge:g,direction:d}=t,p=Gt(r);function b(u,S){return dt(u).filter(x=>x%S===0).map(x=>u.slice(x,x+S))}function f(u){return u.length?dt(u).reduce((S,x,L)=>{const O=V(S)||0,I=O===0,v=x===gt(u),E=c[l]-s[O][l],T=c[l]-s[x][g],P=!i&&I?d(e):0,A=!i&&v?d(a):0,k=M(T-A-(E+P));return L&&k>n+o&&S.push(x),v&&S.push(u.length),S},[]).map((S,x,L)=>{const O=Math.max(L[x-1]||0);return u.slice(O,S)}):[]}function m(u){return p?b(u,r):f(u)}return{groupSlides:m}}function ne(t,n,r,i,c,s,e){const{align:a,axis:o,direction:l,startIndex:g,loop:d,duration:p,dragFree:b,dragThreshold:f,inViewThreshold:m,slidesToScroll:h,skipSnaps:u,containScroll:S,watchResize:x,watchSlides:L,watchDrag:O,watchFocus:I}=s,v=2,E=zn(),T=E.measure(n),P=r.map(E.measure),A=Fn(o,l),k=A.measureSize(T),j=Bn(k),_=Pn(a,k),G=!d&&!!S,Z=d||!!S,{slideSizes:J,slideSizesWithGaps:U,startGap:q,endGap:st}=Wn(A,T,P,r,Z,c),H=te(A,k,h,d,T,P,q,st,v),{snaps:et,snapsAligned:ot}=qn(A,_,T,P,H),K=-V(et)+V(U),{snapsContained:it,scrollContainLimit:ct}=Gn(k,K,ot,S,v),z=G?it:ot,{limit:F}=Hn(K,z,d),X=sn(gt(z),g,d),B=X.clone(),D=dt(r),y=({dragHandler:rt,scrollBody:It,scrollBounds:Tt,options:{loop:ht}})=>{ht||Tt.constrain(rt.pointerDown()),It.seek()},C=({scrollBody:rt,translate:It,location:Tt,offsetLocation:ht,previousLocation:ln,scrollLooper:fn,slideLooper:dn,dragHandler:pn,animation:mn,eventHandler:Xt,scrollBounds:gn,options:{loop:Qt}},Jt)=>{const Yt=rt.settled(),hn=!gn.shouldConstrain(),Zt=Qt?Yt:Yt&&hn,$t=Zt&&!pn.pointerDown();$t&&mn.stop();const Sn=Tt.get()*Jt+ln.get()*(1-Jt);ht.set(Sn),Qt&&(fn.loop(rt.direction()),dn.loop()),It.to(ht.get()),$t&&Xt.emit("settle"),Zt||Xt.emit("scroll")},w=wn(i,c,()=>y(Lt),rt=>C(Lt,rt)),N=.68,R=z[X.get()],Q=at(R),$=at(R),Y=at(R),W=at(R),ut=jn(Q,Y,$,W,p,N),vt=Xn(d,z,K,F,W),Et=Qn(w,X,B,ut,vt,W,e),Ut=Un(F),qt=mt(),un=$n(n,r,e,m),{slideRegistry:Kt}=Kn(G,S,z,ct,H,D),an=Jn(t,r,Kt,Et,ut,qt,e,I),Lt={ownerDocument:i,ownerWindow:c,eventHandler:e,containerRect:T,slideRects:P,animation:w,axis:A,dragHandler:Nn(A,t,i,c,W,kn(A,c),Q,w,Et,ut,vt,X,e,j,b,f,u,N,O),eventStore:qt,percentOfView:j,index:X,indexPrevious:B,limit:F,location:Q,offsetLocation:Y,previousLocation:$,options:s,resizeHandler:Vn(n,e,c,r,A,x,E),scrollBody:ut,scrollBounds:_n(F,Y,W,ut,j),scrollLooper:Rn(K,F,Y,[Q,Y,$,W]),scrollProgress:Ut,scrollSnapList:z.map(Ut.get),scrollSnaps:z,scrollTarget:vt,scrollTo:Et,slideLooper:Yn(A,k,K,J,U,et,z,Y,r),slideFocus:an,slidesHandler:Zn(n,e,L),slidesInView:un,slideIndexes:D,slideRegistry:Kt,slidesToScroll:H,target:W,translate:cn(A,n)};return Lt}function ee(){let t={},n;function r(l){n=l}function i(l){return t[l]||[]}function c(l){return i(l).forEach(g=>g(n,l)),o}function s(l,g){return t[l]=i(l).concat([g]),o}function e(l,g){return t[l]=i(l).filter(d=>d!==g),o}function a(){t={}}const o={init:r,emit:c,off:e,on:s,clear:a};return o}const oe={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function re(t){function n(s,e){return rn(s,e||{})}function r(s){const e=s.breakpoints||{},a=pt(e).filter(o=>t.matchMedia(o).matches).map(o=>e[o]).reduce((o,l)=>n(o,l),{});return n(s,a)}function i(s){return s.map(e=>pt(e.breakpoints||{})).reduce((e,a)=>e.concat(a),[]).map(t.matchMedia)}return{mergeOptions:n,optionsAtMedia:r,optionsMediaQueries:i}}function se(t){let n=[];function r(s,e){return n=e.filter(({options:a})=>t.optionsAtMedia(a).active!==!1),n.forEach(a=>a.init(s,t)),e.reduce((a,o)=>Object.assign(a,{[o.name]:o}),{})}function i(){n=n.filter(s=>s.destroy())}return{init:r,destroy:i}}function bt(t,n,r){const i=t.ownerDocument,c=i.defaultView,s=re(c),e=se(s),a=mt(),o=ee(),{mergeOptions:l,optionsAtMedia:g,optionsMediaQueries:d}=s,{on:p,off:b,emit:f}=o,m=A;let h=!1,u,S=l(oe,bt.globalOptions),x=l(S),L=[],O,I,v;function E(){const{container:D,slides:y}=x;I=(Ot(D)?t.querySelector(D):D)||t.children[0];const w=Ot(y)?I.querySelectorAll(y):y;v=[].slice.call(w||I.children)}function T(D){const y=ne(t,I,v,i,c,D,o);if(D.loop&&!y.slideLooper.canLoop()){const C=Object.assign({},D,{loop:!1});return T(C)}return y}function P(D,y){h||(S=l(S,D),x=g(S),L=y||L,E(),u=T(x),d([S,...L.map(({options:C})=>C)]).forEach(C=>a.add(C,"change",A)),x.active&&(u.translate.to(u.location.get()),u.animation.init(),u.slidesInView.init(),u.slideFocus.init(B),u.eventHandler.init(B),u.resizeHandler.init(B),u.slidesHandler.init(B),u.options.loop&&u.slideLooper.loop(),I.offsetParent&&v.length&&u.dragHandler.init(B),O=e.init(B,L)))}function A(D,y){const C=H();k(),P(l({startIndex:C},D),y),o.emit("reInit")}function k(){u.dragHandler.destroy(),u.eventStore.clear(),u.translate.clear(),u.slideLooper.clear(),u.resizeHandler.destroy(),u.slidesHandler.destroy(),u.slidesInView.destroy(),u.animation.destroy(),e.destroy(),a.clear()}function j(){h||(h=!0,a.clear(),k(),o.emit("destroy"),o.clear())}function _(D,y,C){!x.active||h||(u.scrollBody.useBaseFriction().useDuration(y===!0?0:x.duration),u.scrollTo.index(D,C||0))}function G(D){const y=u.index.add(1).get();_(y,D,-1)}function Z(D){const y=u.index.add(-1).get();_(y,D,1)}function J(){return u.index.add(1).get()!==H()}function U(){return u.index.add(-1).get()!==H()}function q(){return u.scrollSnapList}function st(){return u.scrollProgress.get(u.offsetLocation.get())}function H(){return u.index.get()}function et(){return u.indexPrevious.get()}function ot(){return u.slidesInView.get()}function K(){return u.slidesInView.get(!1)}function it(){return O}function ct(){return u}function z(){return t}function F(){return I}function X(){return v}const B={canScrollNext:J,canScrollPrev:U,containerNode:F,internalEngine:ct,destroy:j,off:b,on:p,emit:f,plugins:it,previousScrollSnap:et,reInit:m,rootNode:z,scrollNext:G,scrollPrev:Z,scrollProgress:st,scrollSnapList:q,scrollTo:_,selectedScrollSnap:H,slideNodes:X,slidesInView:ot,slidesNotInView:K};return P(n,r),setTimeout(()=>o.emit("init"),0),B}bt.globalOptions=void 0;function ft(t,n={options:{},plugins:[]}){let r=n,i;return Cn()&&(bt.globalOptions=ft.globalOptions,i=bt(t,r.options,r.plugins),i.on("init",()=>t.dispatchEvent(new CustomEvent("emblaInit",{detail:i})))),{destroy:()=>{i&&i.destroy()},update:c=>{const s=!_t(r.options,c.options),e=!On(r.plugins,c.plugins);!s&&!e||(r=c,i&&i.reInit(r.options,r.plugins))}}}ft.globalOptions=void 0;var ie=Mt('<div data-slot="carousel-content" class="overflow-hidden"><div><!></div></div>');function Se(t,n){wt(n,!0);let r=tt(n,"ref",15,null),i=jt(n,["$$slots","$$events","$$legacy","ref","class","children"]);const c=on("<Carousel.Content/>");var s=ie(),e=St(s);Bt(e,o=>({class:o,"data-embla-container":"",...i}),[()=>zt("flex",c.orientation==="horizontal"?"-ml-4":"-mt-4 flex-col",n.class)]);var a=St(e);kt(a,()=>n.children??Nt),yt(e),Vt(e,o=>r(o),()=>r()),yt(s),Ln(s,(o,l)=>ft==null?void 0:ft(o,l),()=>({options:{container:"[data-embla-container]",slides:"[data-embla-slide]",...c.options,axis:c.orientation==="horizontal"?"x":"y"},plugins:c.plugins})),En("emblaInit",s,function(...o){var l;(l=c.onInit)==null||l.apply(this,o)}),Pt(t,s),Ft()}var ce=Mt("<div><!></div>");function ye(t,n){wt(n,!0);let r=tt(n,"ref",15,null),i=jt(n,["$$slots","$$events","$$legacy","ref","class","children"]);const c=on("<Carousel.Item/>");var s=ce();Bt(s,a=>({"data-slot":"carousel-item",role:"group","aria-roledescription":"slide",class:a,"data-embla-slide":"",...i}),[()=>zt("min-w-0 shrink-0 grow-0 basis-full",c.orientation==="horizontal"?"pl-4":"pt-4",n.class)]);var e=St(s);kt(e,()=>n.children??Nt),yt(s),Vt(s,a=>r(a),()=>r()),Pt(t,s),Ft()}export{he as C,Se as a,ye as b};
