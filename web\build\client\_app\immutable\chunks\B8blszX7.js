import{j as $e,bc as ze,aJ as me,w as G,bd as Ne}from"./CGmarHxI.js";import{p as pe,n as it}from"./Buv24VCh.js";import{a as _e}from"./nZgk9enP.js";import{U as mt,N as Ct,P as Vt,c as $t,d as pt,e as at,H as Ut,p as Ht,f as Bt,i as yt,h as Me,j as qt,g as Yt}from"./BiJhC7W5.js";class Re extends Error{constructor(i,a){super(i),this.name="DevalueError",this.path=a.join("")}}function st(n){return Object(n)!==n}const zt=Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function Gt(n){const i=Object.getPrototypeOf(n);return i===Object.prototype||i===null||Object.getOwnPropertyNames(i).sort().join("\0")===zt}function Jt(n){return Object.prototype.toString.call(n).slice(8,-1)}function Wt(n){switch(n){case'"':return'\\"';case"<":return"\\u003C";case"\\":return"\\\\";case`
`:return"\\n";case"\r":return"\\r";case"	":return"\\t";case"\b":return"\\b";case"\f":return"\\f";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";default:return n<" "?`\\u${n.charCodeAt(0).toString(16).padStart(4,"0")}`:""}}function ce(n){let i="",a=0;const t=n.length;for(let c=0;c<t;c+=1){const u=n[c],o=Wt(u);o&&(i+=n.slice(a,c)+o,a=c+1)}return`"${a===0?n:i+n.slice(a)}"`}function Zt(n){return Object.getOwnPropertySymbols(n).filter(i=>Object.getOwnPropertyDescriptor(n,i).enumerable)}const Xt=/^[a-zA-Z_$][a-zA-Z_$0-9]*$/;function ot(n){return Xt.test(n)?"."+n:"["+JSON.stringify(n)+"]"}function Kt(n,i){const a=[],t=new Map,c=[];if(i)for(const d of Object.getOwnPropertyNames(i))c.push({key:d,fn:i[d]});const u=[];let o=0;function l(d){if(typeof d=="function")throw new Re("Cannot stringify a function",u);if(t.has(d))return t.get(d);if(d===void 0)return mt;if(Number.isNaN(d))return Ct;if(d===1/0)return Vt;if(d===-1/0)return $t;if(d===0&&1/d<0)return pt;const A=o++;t.set(d,A);for(const{key:R,fn:V}of c){const S=V(d);if(S)return a[A]=`["${R}",${l(S)}]`,A}let y="";if(st(d))y=Ce(d);else{const R=Jt(d);switch(R){case"Number":case"String":case"Boolean":y=`["Object",${Ce(d)}]`;break;case"BigInt":y=`["BigInt",${d}]`;break;case"Date":y=`["Date","${!isNaN(d.getDate())?d.toISOString():""}"]`;break;case"RegExp":const{source:S,flags:J}=d;y=J?`["RegExp",${ce(S)},"${J}"]`:`["RegExp",${ce(S)}]`;break;case"Array":y="[";for(let h=0;h<d.length;h+=1)h>0&&(y+=","),h in d?(u.push(`[${h}]`),y+=l(d[h]),u.pop()):y+=Ut;y+="]";break;case"Set":y='["Set"';for(const h of d)y+=`,${l(h)}`;y+="]";break;case"Map":y='["Map"';for(const[h,M]of d)u.push(`.get(${st(h)?Ce(h):"..."})`),y+=`,${l(h)},${l(M)}`,u.pop();y+="]";break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const M=at(d.buffer);y='["'+R+'","'+M+'"]';break}case"ArrayBuffer":{y=`["ArrayBuffer","${at(d)}"]`;break}default:if(!Gt(d))throw new Re("Cannot stringify arbitrary non-POJOs",u);if(Zt(d).length>0)throw new Re("Cannot stringify POJOs with symbolic keys",u);if(Object.getPrototypeOf(d)===null){y='["null"';for(const h in d)u.push(ot(h)),y+=`,${ce(h)},${l(d[h])}`,u.pop();y+="]"}else{y="{";let h=!1;for(const M in d)h&&(y+=","),h=!0,u.push(ot(M)),y+=`${ce(M)}:${l(d[M])}`,u.pop();y+="}"}}}return a[A]=y,A}const p=l(n);return p<0?`${p}`:`[${a.join(",")}]`}function Ce(n){const i=typeof n;return i==="string"?ce(n):n instanceof String?ce(n.toString()):n===void 0?mt.toString():n===0&&1/n<0?pt.toString():i==="bigint"?`["BigInt","${n}"]`:String(n)}function ye(n){const i={}.toString.call(n).slice(8,-1);if(i=="Set")return new Set([...n].map(a=>ye(a)));if(i=="Map")return new Map([...n].map(a=>[ye(a[0]),ye(a[1])]));if(i=="Date")return new Date(n.getTime());if(i=="RegExp")return RegExp(n.source,n.flags);if(i=="Array"||i=="Object"){const a=i=="Object"?Object.create(Object.getPrototypeOf(n)):[];for(const t in n)a[t]=ye(n[t]);return a}return n}function Ue(n,i,a){return n[i]=a,"skip"}function Qt(n,i){return i.value!==void 0&&typeof i.value!="object"&&i.path.length<n.length}function ee(n,i,a={}){a.modifier||(a.modifier=c=>Qt(i,c)?void 0:c.value);const t=Z(n,i,a.modifier);if(t)return a.value===void 0||a.value(t.value)?t:void 0}function Z(n,i,a){if(!i.length)return;const t=[i[0]];let c=n;for(;c&&t.length<i.length;){const o=t[t.length-1],l=a?a({parent:c,key:String(o),value:c[o],path:t.map(p=>String(p)),isLeaf:!1,set:p=>Ue(c,o,p)}):c[o];if(l===void 0)return;c=l,t.push(i[t.length])}if(!c)return;const u=i[i.length-1];return{parent:c,key:String(u),value:c[u],path:i.map(o=>String(o)),isLeaf:!0,set:o=>Ue(c,u,o)}}function K(n,i,a=[]){for(const t in n){const c=n[t],u=c===null||typeof c!="object",o={parent:n,key:t,value:c,path:a.concat([t]),isLeaf:u,set:p=>Ue(n,t,p)},l=i(o);if(l==="abort")return l;if(l==="skip")continue;if(!u){const p=K(c,i,o.path);if(p==="abort")return p}}}function en(n,i){return n===i||n.size===i.size&&[...n].every(a=>i.has(a))}function ut(n,i){const a=new Map;function t(l,p){return l instanceof Date&&p instanceof Date&&l.getTime()!==p.getTime()||l instanceof Set&&p instanceof Set&&!en(l,p)||l instanceof File&&p instanceof File&&l!==p}function c(l){return l instanceof Date||l instanceof Set||l instanceof File}function u(l,p){const d=p?Z(p,l.path):void 0;function A(){return a.set(l.path.join(" "),l.path),"skip"}if(c(l.value)&&(!c(d==null?void 0:d.value)||t(l.value,d.value)))return A();l.isLeaf&&(!d||l.value!==d.value)&&A()}K(n,l=>u(l,i)),K(i,l=>u(l,n));const o=Array.from(a.values());return o.sort((l,p)=>l.length-p.length),o}function W(n,i,a){const t=typeof a=="function";for(const c of i){const u=Z(n,c,({parent:o,key:l,value:p})=>((p===void 0||typeof p!="object")&&(o[l]={}),o[l]));u&&(u.parent[u.key]=t?a(c,u):a)}}function fe(n){return n.toString().split(/[[\].]+/).filter(i=>i)}function he(n){return n.reduce((i,a)=>{const t=String(a);return typeof a=="number"||/^\d+$/.test(t)?i+=`[${t}]`:i?i+=`.${t}`:i+=t,i},"")}class D extends Error{constructor(i){super(i),Object.setPrototypeOf(this,D.prototype)}}function tn(n,i){var c;const a={};function t(u){if("_errors"in a||(a._errors=[]),!Array.isArray(a._errors))if(typeof a._errors=="string")a._errors=[a._errors];else throw new D("Form-level error was not an array.");a._errors.push(u.message)}for(const u of n){if(!u.path||u.path.length==1&&!u.path[0]){t(u);continue}const l=!/^\d$/.test(String(u.path[u.path.length-1]))&&((c=ee(i,u.path.filter(y=>/\D/.test(String(y)))))==null?void 0:c.value),p=Z(a,u.path,({value:y,parent:R,key:V})=>(y===void 0&&(R[V]={}),R[V]));if(!p){t(u);continue}const{parent:d,key:A}=p;l?(A in d||(d[A]={}),"_errors"in d[A]?d[A]._errors.push(u.message):d[A]._errors=[u.message]):A in d?d[A].push(u.message):d[A]=[u.message]}return a}function ct(n,i,a){return a?n:(K(i,t=>{Array.isArray(t.value)&&t.set(void 0)}),K(n,t=>{!Array.isArray(t.value)&&t.value!==void 0||W(i,[t.path],t.value)}),i)}function nn(n){return ht(n,[])}function ht(n,i){return Object.entries(n).filter(([,t])=>t!==void 0).flatMap(([t,c])=>{if(Array.isArray(c)&&c.length>0){const u=i.concat([t]);return{path:he(u),messages:c}}else return ht(n[t],i.concat([t]))})}function q(n){return n&&typeof n=="object"?ye(n):n}function ft(n){n.flashMessage&&He(n)&&(document.cookie=`flash=; Max-Age=0; Path=${n.flashMessage.cookiePath??"/"};`)}function He(n){return n.flashMessage?n.syncFlashMessage:!1}function Be(n){const i=JSON.parse(n);return i.data&&(i.data=Ht(i.data,Bt.decoders)),i}function Ve(n){return HTMLElement.prototype.cloneNode.call(n)}function rn(n,i=()=>{}){const a=async({action:c,result:u,reset:o=!0,invalidateAll:l=!0})=>{u.type==="success"&&(o&&HTMLFormElement.prototype.reset.call(n),l&&await yt()),(location.origin+location.pathname===c.origin+c.pathname||u.type==="redirect"||u.type==="error")&&await Me(u)};async function t(c){var J,h,M,be,ge;if(((J=c.submitter)!=null&&J.hasAttribute("formmethod")?c.submitter.formMethod:Ve(n).method)!=="post")return;c.preventDefault();const o=new URL((h=c.submitter)!=null&&h.hasAttribute("formaction")?c.submitter.formAction:Ve(n).action),l=(M=c.submitter)!=null&&M.hasAttribute("formenctype")?c.submitter.formEnctype:Ve(n).enctype,p=new FormData(n),d=(be=c.submitter)==null?void 0:be.getAttribute("name");d&&p.append(d,((ge=c.submitter)==null?void 0:ge.getAttribute("value"))??"");const A=new AbortController;let y=!1;const V=await i({action:o,cancel:()=>y=!0,controller:A,formData:p,formElement:n,submitter:c.submitter})??a;if(y)return;let S;try{const F=new Headers({accept:"application/json","x-sveltekit-action":"true"});l!=="multipart/form-data"&&F.set("Content-Type",/^(:?application\/x-www-form-urlencoded|text\/plain)$/.test(l)?l:"application/x-www-form-urlencoded");const ke=l==="multipart/form-data"?p:new URLSearchParams(p),le=await fetch(o,{method:"POST",headers:F,cache:"no-store",body:ke,signal:A.signal});S=Be(await le.text()),S.type==="error"&&(S.status=le.status)}catch(F){if((F==null?void 0:F.name)==="AbortError")return;S={type:"error",error:F}}await V({action:o,formData:p,formElement:n,update:F=>a({action:o,result:S,reset:F==null?void 0:F.reset,invalidateAll:F==null?void 0:F.invalidateAll}),result:S})}return HTMLFormElement.prototype.addEventListener.call(n,"submit",t),{destroy(){HTMLFormElement.prototype.removeEventListener.call(n,"submit",t)}}}const bt="noCustomValidity";async function lt(n,i){"setCustomValidity"in n&&n.setCustomValidity(""),!(bt in n.dataset)&&gt(n,i)}function an(n,i){for(const a of n.querySelectorAll("input,select,textarea,button")){if("dataset"in a&&bt in a.dataset||!a.name)continue;const t=Z(i,fe(a.name)),c=t&&typeof t.value=="object"&&"_errors"in t.value?t.value._errors:t==null?void 0:t.value;if(gt(a,c),c)return}}function gt(n,i){if(!("setCustomValidity"in n))return;const a=i&&i.length?i.join(`
`):"";n.setCustomValidity(a),a&&n.reportValidity()}const sn=(n,i=0)=>{const a=n.getBoundingClientRect();return a.top>=i&&a.left>=0&&a.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&a.right<=(window.innerWidth||document.documentElement.clientWidth)},on=(n,i=1.125,a="smooth")=>{const u=n.getBoundingClientRect().top+window.pageYOffset-window.innerHeight/(2*i);window.scrollTo({left:0,top:u,behavior:a})},un=["checkbox","radio","range","file"];function dt(n){const i=!!n&&(n instanceof HTMLSelectElement||n instanceof HTMLInputElement&&un.includes(n.type)),a=!!n&&n instanceof HTMLSelectElement&&n.multiple,t=!!n&&n instanceof HTMLInputElement&&n.type=="file";return{immediate:i,multiple:a,file:t}}var N;(function(n){n[n.Idle=0]="Idle",n[n.Submitting=1]="Submitting",n[n.Delayed=2]="Delayed",n[n.Timeout=3]="Timeout"})(N||(N={}));const cn=new Set;function fn(n,i,a){let t=N.Idle,c,u;const o=cn;function l(){p(),A(t!=N.Delayed?N.Submitting:N.Delayed),c=window.setTimeout(()=>{c&&t==N.Submitting&&A(N.Delayed)},a.delayMs),u=window.setTimeout(()=>{u&&t==N.Delayed&&A(N.Timeout)},a.timeoutMs),o.add(p)}function p(){clearTimeout(c),clearTimeout(u),c=u=0,o.delete(p),A(N.Idle)}function d(){o.forEach(h=>h()),o.clear()}function A(h){t=h,i.submitting.set(t>=N.Submitting),i.delayed.set(t>=N.Delayed),i.timeout.set(t>=N.Timeout)}const y=n;function R(h){const M=h.target;a.selectErrorText&&M.select()}function V(){a.selectErrorText&&y.querySelectorAll("input").forEach(h=>{h.addEventListener("invalid",R)})}function S(){a.selectErrorText&&y.querySelectorAll("input").forEach(h=>h.removeEventListener("invalid",R))}const J=n;{V();const h=M=>{M.clearAll?d():p(),M.cancelled||setTimeout(()=>qe(J,a),1)};return _e(()=>{S(),h({cancelled:!0})}),{submitting(){l()},completed:h,scrollToFirstError(){setTimeout(()=>qe(J,a),1)},isSubmitting:()=>t===N.Submitting||t===N.Delayed}}}const qe=async(n,i)=>{if(i.scrollToError=="off")return;const a=i.errorSelector;if(!a)return;await $e();let t;if(t=n.querySelector(a),!t)return;t=t.querySelector(a)??t;const c=i.stickyNavbar?document.querySelector(i.stickyNavbar):null;typeof i.scrollToError!="string"?t.scrollIntoView(i.scrollToError):sn(t,(c==null?void 0:c.offsetHeight)??0)||on(t,void 0,i.scrollToError);function u(l){return typeof i.autoFocusOnError=="boolean"?i.autoFocusOnError:!/iPhone|iPad|iPod|Android/i.test(l)}if(!u(navigator.userAgent))return;let o;if(o=t,["INPUT","SELECT","BUTTON","TEXTAREA"].includes(o.tagName)||(o=o.querySelector('input:not([type="hidden"]):not(.flatpickr-input), select, textarea')),o)try{o.focus({preventScroll:!0}),i.selectErrorText&&o.tagName=="INPUT"&&o.select()}catch{}};function Fe(n,i,a){const t=Z(n,i,({parent:c,key:u,value:o})=>(o===void 0&&(c[u]=/\D/.test(u)?{}:[]),c[u]));if(t){const c=a(t.value);t.parent[t.key]=c}return n}function ln(n,i,a){const t=n.form,c=fe(i),u=ze(t,o=>{const l=Z(o,c);return l==null?void 0:l.value});return{subscribe(...o){const l=u.subscribe(...o);return()=>l()},update(o,l){t.update(p=>Fe(p,c,o),l??a)},set(o,l){t.update(p=>Fe(p,c,()=>o),l??a)}}}function dn(n,i){const a="form"in n;if(!a&&(i==null?void 0:i.taint)!==void 0)throw new D("If options.taint is set, the whole superForm object must be used as a proxy.");return a}function Se(n,i,a){const t=fe(i);if(dn(n,a))return ln(n,i,a);const c=ze(n,u=>{const o=Z(u,t);return o==null?void 0:o.value});return{subscribe(...u){const o=c.subscribe(...u);return()=>o()},update(u){n.update(o=>Fe(o,t,u))},set(u){n.update(o=>Fe(o,t,()=>u))}}}function Ye(n){let i={};const a=Array.isArray(n);for(const[t,c]of Object.entries(n))!c||typeof c!="object"||(a?i={...i,...Ye(c)}:i[t]=Ye(c));return i}const Te=new WeakMap,oe=new WeakMap,wt=n=>{throw n.result.error},mn={applyAction:!0,invalidateAll:!0,resetForm:!0,autoFocusOnError:"detect",scrollToError:"smooth",errorSelector:'[aria-invalid="true"],[data-invalid]',selectErrorText:!1,stickyNavbar:void 0,taintedMessage:!1,onSubmit:void 0,onResult:void 0,onUpdate:void 0,onUpdated:void 0,onError:wt,dataType:"form",validators:void 0,customValidity:!1,clearOnSubmit:"message",delayMs:500,timeoutMs:8e3,multipleSubmits:"prevent",SPA:void 0,validationMethod:"auto"};function pn(n){return`Duplicate form id's found: "${n}". Multiple forms will receive the same data. Use the id option to differentiate between them, or if this is intended, set the warnings.duplicateId option to false in superForm to disable this warning. More information: https://superforms.rocks/concepts/multiple-forms`}let Et=!1;try{SUPERFORMS_LEGACY&&(Et=!0)}catch{}let ue=!1;try{globalThis.STORIES&&(ue=!0)}catch{}function En(n,i){var rt;let a,t=i??{},c;{if((t.legacy??Et)&&(t.resetForm===void 0&&(t.resetForm=!1),t.taintedMessage===void 0&&(t.taintedMessage=!0)),ue&&t.applyAction===void 0&&(t.applyAction=!1),typeof t.SPA=="string"&&(t.invalidateAll===void 0&&(t.invalidateAll=!1),t.applyAction===void 0&&(t.applyAction=!1)),c=t.validators,t={...mn,...t},(t.SPA===!0||typeof t.SPA=="object")&&t.validators===void 0&&console.warn("No validators set for superForm in SPA mode. Add a validation adapter to the validators option, or set it to false to disable this warning."),!n)throw new D("No form data sent to superForm. Make sure the output from superValidate is used (usually data.form) and that it's not null or undefined. Alternatively, an object with default values for the form can also be used, but then constraints won't be available.");d(n)===!1&&(n={id:t.id??Math.random().toString(36).slice(2,10),valid:!1,posted:!1,errors:{},data:n,shape:Ye(n)}),n=n;const e=n.id=t.id??n.id,r=me(pe)??(ue?{}:void 0);if(((rt=t.warnings)==null?void 0:rt.duplicateId)!==!1)if(!Te.has(r))Te.set(r,new Set([e]));else{const s=Te.get(r);s!=null&&s.has(e)?console.warn(pn(e)):s==null||s.add(e)}if(oe.has(n)||oe.set(n,n),a=oe.get(n),n=q(a),_e(()=>{var s;jt(),Tt(),Dt();for(const f of Object.values(U))f.length=0;(s=Te.get(r))==null||s.delete(e)}),t.dataType!=="json"){const s=(f,m)=>{if(!(!m||typeof m!="object")){if(Array.isArray(m))m.length>0&&s(f,m[0]);else if(!(m instanceof Date)&&!(m instanceof File)&&!(m instanceof FileList))throw new D(`Object found in form field "${f}". Set the dataType option to "json" and add use:enhance to use nested data structures. More information: https://superforms.rocks/concepts/nested-data`)}};for(const[f,m]of Object.entries(n.data))s(f,m)}}const u={formId:n.id,form:q(n.data),constraints:n.constraints??{},posted:n.posted,errors:q(n.errors),message:q(n.message),tainted:void 0,valid:n.valid,submitting:!1,shape:n.shape},o=u,l=G(t.id??n.id);function p(e){return Object.values(e).filter(s=>d(s)!==!1)}function d(e){return!e||typeof e!="object"||!("valid"in e&&"errors"in e&&typeof e.valid=="boolean")?!1:"id"in e&&typeof e.id=="string"?e.id:!1}const A=G(n.data),y={subscribe:A.subscribe,set:(e,r={})=>{const s=q(e);return Ke(s,r.taint??!0),A.set(s)},update:(e,r={})=>A.update(s=>{const f=e(s);return Ke(f,r.taint??!0),f})};function R(){return t.SPA===!0||typeof t.SPA=="object"}function V(e){var r;return e>400?e:(typeof t.SPA=="boolean"||typeof t.SPA=="string"||(r=t.SPA)==null?void 0:r.failStatus)||e}async function S(e={}){const r=e.formData??o.form;let s={},f;const m=e.adapter??t.validators;if(typeof m=="object"){if(m!=c&&!("jsonSchema"in m))throw new D('Client validation adapter found in options.validators. A full adapter must be used when changing validators dynamically, for example "zod" instead of "zodClient".');if(f=await m.validate(r),!f.success)s=tn(f.issues,m.shape??o.shape??{});else if(e.recheckValidData!==!1)return S({...e,recheckValidData:!1})}else f={success:!0,data:{}};const g={...o.form,...r,...f.success?f.data:{}};return{valid:f.success,posted:!1,errors:s,data:g,constraints:o.constraints,message:void 0,id:o.formId,shape:o.shape}}function J(e){if(!t.onChange||!e.paths.length||e.type=="blur")return;let r;const s=e.paths.map(he);e.type&&e.paths.length==1&&e.formElement&&e.target instanceof Element?r={path:s[0],paths:s,formElement:e.formElement,target:e.target,set(f,m,g){Se({form:y},f,g).set(m)},get(f){return me(Se(y,f))}}:r={paths:s,target:void 0,set(f,m,g){Se({form:y},f,g).set(m)},get(f){return me(Se(y,f))}},t.onChange(r)}async function h(e,r=!1,s){e&&(t.validators=="clear"&&z.update(g=>(W(g,e.paths,void 0),g)),setTimeout(()=>J(e)));let f=!1;if(r||(t.validationMethod=="onsubmit"||t.validationMethod=="submit-only"||t.validationMethod=="onblur"&&(e==null?void 0:e.type)=="input"||t.validationMethod=="oninput"&&(e==null?void 0:e.type)=="blur")&&(f=!0),f||!e||!t.validators||t.validators=="clear"){if(e!=null&&e.paths){const g=(e==null?void 0:e.formElement)??de();g&&M(g)}return}const m=await S({adapter:s});return m.valid&&(e.immediate||e.type!="input")&&y.set(m.data,{taint:"ignore"}),await $e(),be(m.errors,e,r),m}function M(e){const r=new Map;if(t.customValidity&&e)for(const s of e.querySelectorAll("[name]")){if(typeof s.name!="string"||!s.name.length)continue;const f="validationMessage"in s?String(s.validationMessage):"";r.set(s.name,{el:s,message:f}),lt(s,void 0)}return r}async function be(e,r,s){const{type:f,immediate:m,multiple:g,paths:H}=r,Q=o.errors,te={};let x=new Map;const I=r.formElement??de();I&&(x=M(I)),K(e,T=>{if(!Array.isArray(T.value))return;const j=[...T.path];j[j.length-1]=="_errors"&&j.pop();const se=j.join(".");function B(){if(W(te,[T.path],T.value),t.customValidity&&ne&&x.has(se)){const{el:C,message:re}=x.get(se);re!=T.value&&(setTimeout(()=>lt(C,T.value)),x.clear())}}if(s)return B();const ve=T.path[T.path.length-1]=="_errors",ne=T.value&&H.some(C=>ve?j&&C&&j.length>0&&j[0]==C[0]:se==C.join("."));if(ne&&t.validationMethod=="oninput"||m&&!g&&ne)return B();if(g){const C=ee(me(z),T.path.slice(0,-1));if(C!=null&&C.value&&typeof(C==null?void 0:C.value)=="object"){for(const re of Object.values(C.value))if(Array.isArray(re))return B()}}const X=ee(Q,T.path);if(X&&X.key in X.parent)return B();if(ve){if(t.validationMethod=="oninput"||f=="blur"&&kt(he(T.path.slice(0,-1))))return B()}else if(f=="blur"&&ne)return B()}),z.set(te)}function ge(e,r={}){return r.keepFiles&&K(o.form,s=>{if(!(s.parent instanceof FileList)&&(s.value instanceof File||s.value instanceof FileList)){const f=ee(e,s.path);(!f||!(f.key in f.parent))&&W(e,[s.path],s.value)}}),y.set(e,r)}function F(e,r){return e&&r&&t.resetForm&&(t.resetForm===!0||t.resetForm())}function ke(e=!0){let r=o.form,s=o.tainted;if(e){const f=Nt(o.form);r=f.data;const m=f.paths;m.length&&(s=q(s)??{},W(s,m,!1))}return{valid:o.valid,posted:o.posted,errors:o.errors,data:r,constraints:o.constraints,message:o.message,id:o.formId,tainted:s,shape:o.shape}}async function le(e,r){e.valid&&r&&F(e.valid,r)?Oe({message:e.message,posted:!0}):Ee({form:e,untaint:r,keepFiles:!0,pessimisticUpdate:t.invalidateAll=="force"||t.invalidateAll=="pessimistic"}),U.onUpdated.length&&await $e();for(const s of U.onUpdated)s({form:e})}function Oe(e={}){e.newState&&(a.data={...a.data,...e.newState});const r=q(a);r.data={...r.data,...e.data},e.id!==void 0&&(r.id=e.id),Ee({form:r,untaint:!0,message:e.message,keepFiles:!1,posted:e.posted,resetted:!0})}async function vt(e){if(e.type=="error")throw new D(`ActionResult of type "${e.type}" cannot be passed to update function.`);if(e.type=="redirect"){F(!0,!0)&&Oe({posted:!0});return}if(typeof e.data!="object")throw new D("Non-object validation data returned from ActionResult.");const r=p(e.data);if(!r.length)throw new D("No form data returned from ActionResult. Make sure you return { form } in the form actions.");for(const s of r)s.id===o.formId&&await le(s,e.status>=200&&e.status<300)}const ae=G(u.message),Ie=G(u.constraints),je=G(u.posted),Ge=G(u.shape),Pe=G(n.errors),z={subscribe:Pe.subscribe,set(e,r){return Pe.set(ct(e,o.errors,r==null?void 0:r.force))},update(e,r){return Pe.update(s=>ct(e(s),o.errors,r==null?void 0:r.force))},clear:()=>z.set({})};let O=null;function At(e){var r;O&&e&&Object.keys(e).length==1&&((r=e.paths)!=null&&r.length)&&O.target&&O.target instanceof HTMLInputElement&&O.target.type.toLowerCase()=="file"?O.paths=e.paths:O=e,setTimeout(()=>{h(O)},0)}function St(e,r,s,f,m){O===null&&(O={paths:[]}),O.type=e,O.immediate=r,O.multiple=s,O.formElement=f,O.target=m}function Je(){return(O==null?void 0:O.paths)??[]}function Tt(){O=null}const L={defaultMessage:"Leave page? Changes that you made may not be saved.",state:G(),message:t.taintedMessage,clean:q(n.data),forceRedirection:!1};function We(){return t.taintedMessage&&!o.submitting&&!L.forceRedirection&&Xe()}function Ze(e){if(!We())return;e.preventDefault(),e.returnValue="";const{taintedMessage:r}=t,f=typeof r=="function"||r===!0?L.defaultMessage:r;return(e||window.event).returnValue=f||L.defaultMessage,f}async function _t(e){if(!We())return;const{taintedMessage:r}=t,s=typeof r=="function";if(s&&e.cancel(),e.type==="leave")return;const f=s||r===!0?L.defaultMessage:r;let m;try{m=s?await r():window.confirm(f||L.defaultMessage)}catch{m=!1}if(m&&e.to)try{L.forceRedirection=!0,await Yt(e.to.url,{...e.to.params});return}finally{L.forceRedirection=!1}else!m&&!s&&e.cancel()}function Mt(){t.taintedMessage=L.message}function Ft(){return L.state}function kt(e){if(!o.tainted)return!1;if(!e)return!!o.tainted;const r=ee(o.tainted,fe(e));return!!r&&r.key in r.parent}function Xe(e){if(!arguments.length)return we(o.tainted);if(typeof e=="boolean")return e;if(typeof e=="object")return we(e);if(!o.tainted||e===void 0)return!1;const r=ee(o.tainted,fe(e));return we(r==null?void 0:r.value)}function we(e){if(!e)return!1;if(typeof e=="object"){for(const r of Object.values(e))if(we(r))return!0}return e===!0}function Ke(e,r){if(r=="ignore")return;const s=ut(e,o.form),f=ut(e,L.clean).map(m=>m.join());s.length&&(r=="untaint-all"||r=="untaint-form"?L.state.set(void 0):L.state.update(m=>(m||(m={}),W(m,s,(g,H)=>{if(!f.includes(g.join()))return;const Q=Z(e,g),te=Z(L.clean,g);return Q&&te&&Q.value===te.value?void 0:r===!0?!0:r==="untaint"?void 0:H.value}),m)),At({paths:s}))}function Ot(e,r){L.state.set(e),r&&(L.clean=r)}const xe=G(!1),Qe=G(!1),et=G(!1),tt=[L.state.subscribe(e=>u.tainted=q(e)),y.subscribe(e=>u.form=q(e)),z.subscribe(e=>u.errors=q(e)),l.subscribe(e=>u.formId=e),Ie.subscribe(e=>u.constraints=e),je.subscribe(e=>u.posted=e),ae.subscribe(e=>u.message=e),xe.subscribe(e=>u.submitting=e),Ge.subscribe(e=>u.shape=e)];function It(e){tt.push(e)}function jt(){tt.forEach(e=>e())}let $;function de(){return $}function Pt(e){$=document.createElement("form"),$.method="POST",$.action=e,nt($),document.body.appendChild($)}function xt(e){$&&($.action=e)}function Dt(){$!=null&&$.parentElement&&$.remove(),$=void 0}const Lt=ze(z,e=>e?nn(e):[]);t.taintedMessage=void 0;function Ee(e){const r=e.form,s=e.message??r.message;if((e.untaint||e.resetted)&&Ot(typeof e.untaint=="boolean"?void 0:e.untaint,r.data),e.pessimisticUpdate||ge(r.data,{taint:"ignore",keepFiles:e.keepFiles}),ae.set(s),e.resetted?z.update(()=>({}),{force:!0}):z.set(r.errors),l.set(r.id),je.set(e.posted??r.posted),r.constraints&&Ie.set(r.constraints),r.shape&&Ge.set(r.shape),u.valid=r.valid,t.flashMessage&&He(t)){const f=t.flashMessage.module.getFlash(pe);s&&me(f)===void 0&&f.set(s)}}const U={onSubmit:t.onSubmit?[t.onSubmit]:[],onResult:t.onResult?[t.onResult]:[],onUpdate:t.onUpdate?[t.onUpdate]:[],onUpdated:t.onUpdated?[t.onUpdated]:[],onError:t.onError?[t.onError]:[]};window.addEventListener("beforeunload",Ze),_e(()=>{window.removeEventListener("beforeunload",Ze)}),qt(_t),It(pe.subscribe(async e=>{ue&&e===void 0&&(e={status:200});const r=e.status>=200&&e.status<300;if(t.applyAction&&e.form&&typeof e.form=="object"){const s=e.form;if(s.type==="error")return;for(const f of p(s)){const m=oe.has(f);f.id!==o.formId||m||(oe.set(f,f),await le(f,r))}}else if(t.applyAction!=="never"&&e.data&&typeof e.data=="object")for(const s of p(e.data)){const f=oe.has(s);if(s.id!==o.formId||f)continue;(t.invalidateAll==="force"||t.invalidateAll==="pessimistic")&&(a.data=s.data);const m=F(s.valid,!0);Ee({form:s,untaint:r,keepFiles:!m,resetted:m})}})),typeof t.SPA=="string"&&Pt(t.SPA);function nt(e,r){if(t.SPA!==void 0&&e.method=="get"&&(e.method="post"),typeof t.SPA=="string"?t.SPA.length&&e.action==document.location.href&&(e.action=t.SPA):$=e,r){if(r.onError){if(t.onError==="apply")throw new D('options.onError is set to "apply", cannot add any onError events.');if(r.onError==="apply")throw new D('Cannot add "apply" as onError event in use:enhance.');U.onError.push(r.onError)}r.onResult&&U.onResult.push(r.onResult),r.onSubmit&&U.onSubmit.push(r.onSubmit),r.onUpdate&&U.onUpdate.push(r.onUpdate),r.onUpdated&&U.onUpdated.push(r.onUpdated)}Mt();let s;async function f(x){const I=dt(x.target);I.immediate&&!I.file&&await new Promise(T=>setTimeout(T,0)),s=Je(),St("input",I.immediate,I.multiple,e,x.target??void 0)}async function m(x){if(o.submitting||!s||Je()!=s)return;const I=dt(x.target);I.immediate&&!I.file&&await new Promise(T=>setTimeout(T,0)),h({paths:s,immediate:I.multiple,multiple:I.multiple,type:"blur",formElement:e,target:x.target??void 0}),s=void 0}e.addEventListener("focusout",m),e.addEventListener("input",f),_e(()=>{e.removeEventListener("focusout",m),e.removeEventListener("input",f)});const g=fn(e,{submitting:xe,delayed:Qe,timeout:et},t);let H,Q;const te=rn(e,async x=>{let I,T=t.validators;const j={...x,jsonData(w){if(t.dataType!=="json")throw new D("options.dataType must be set to 'json' to use jsonData.");I=w},validators(w){T=w},customRequest(w){Q=w}},se=j.cancel;let B=!1;function De(w){const b={...w,posted:!0},E=b.valid?200:V(400),P={form:b},_=b.valid?{type:"success",status:E,data:P}:{type:"failure",status:E,data:P};setTimeout(()=>re({result:_}),0)}function ve(){switch(t.clearOnSubmit){case"errors-and-message":z.clear(),ae.set(void 0);break;case"errors":z.clear();break;case"message":ae.set(void 0);break}}async function ne(w,b){var E;if(w.status=b,t.onError!=="apply"){const P={result:w,message:ae,form:n};for(const _ of U.onError)_!=="apply"&&(_!=wt||!((E=t.flashMessage)!=null&&E.onError))&&await _(P)}t.flashMessage&&t.flashMessage.onError&&await t.flashMessage.onError({result:w,flashMessage:t.flashMessage.module.getFlash(pe)}),t.applyAction&&(t.onError=="apply"?await Me(w):await Me({type:"failure",status:V(w.status),data:w}))}function X(w={resetTimers:!0}){return B=!0,w.resetTimers&&g.isSubmitting()&&g.completed({cancelled:B}),se()}if(j.cancel=X,g.isSubmitting()&&t.multipleSubmits=="prevent")X({resetTimers:!1});else{g.isSubmitting()&&t.multipleSubmits=="abort"&&H&&H.abort(),g.submitting(),H=j.controller;for(const w of U.onSubmit)try{await w(j)}catch(b){X(),ne({type:"error",error:b},500)}}if(B&&t.flashMessage&&ft(t),!B){const w=!R()&&(e.noValidate||(j.submitter instanceof HTMLButtonElement||j.submitter instanceof HTMLInputElement)&&j.submitter.formNoValidate);let b;const E=async()=>await S({adapter:T});if(ve(),w||(b=await E(),b.valid||(X({resetTimers:!1}),De(b))),!B){t.flashMessage&&(t.clearOnSubmit=="errors-and-message"||t.clearOnSubmit=="message")&&He(t)&&t.flashMessage.module.getFlash(pe).set(void 0);const P="formData"in j?j.formData:j.data;if(s=void 0,R())b||(b=await E()),X({resetTimers:!1}),De(b);else if(t.dataType==="json"){b||(b=await E());const _=q(I??b.data);K(_,v=>{if(v.value instanceof File){const k="__superform_file_"+he(v.path);return P.append(k,v.value),v.set(void 0)}else if(Array.isArray(v.value)&&v.value.length&&v.value.every(k=>k instanceof File)){const k="__superform_files_"+he(v.path);for(const Y of v.value)P.append(k,Y);return v.set(void 0)}}),Object.keys(_).forEach(v=>{typeof P.get(v)=="string"&&P.delete(v)});const ie=t.transport?Object.fromEntries(Object.entries(t.transport).map(([v,k])=>[v,k.encode])):void 0,Ae=C(Kt(_,ie),t.jsonChunkSize??5e5);for(const v of Ae)P.append("__superform_json",v)}if(!P.has("__superform_id")){const _=o.formId;_!==void 0&&P.set("__superform_id",_)}typeof t.SPA=="string"&&xt(t.SPA)}}function C(w,b){const E=Math.ceil(w.length/b),P=new Array(E);for(let _=0,ie=0;_<E;++_,ie+=b)P[_]=w.substring(ie,ie+b);return P}async function re(w){let b=!1;H=null;let E="type"in w.result&&"status"in w.result?w.result:{type:"error",status:V(parseInt(String(w.result.status))||500),error:w.result.error instanceof Error?w.result.error:w.result};const P=()=>b=!0,_={result:E,formEl:e,formElement:e,cancel:P},ie=ue||!R()?()=>{}:it.subscribe(v=>{var k,Y;!v||((k=v.from)==null?void 0:k.route.id)===((Y=v.to)==null?void 0:Y.route.id)||P()});function Ae(v,k,Y){k.result={type:"error",error:v,status:V(Y)}}for(const v of U.onResult)try{await v(_)}catch(k){Ae(k,_,Math.max(E.status??500,400))}if(E=_.result,!b){if((E.type==="success"||E.type==="failure")&&E.data){const v=p(E.data);if(!v.length)throw new D("No form data returned from ActionResult. Make sure you return { form } in the form actions.");for(const k of v){if(k.id!==o.formId)continue;const Y={form:k,formEl:e,formElement:e,cancel:()=>b=!0,result:E};for(const Le of U.onUpdate)try{await Le(Y)}catch(Rt){Ae(Rt,Y,Math.max(E.status??500,400))}E=Y.result,b||(t.customValidity&&an(e,Y.form.errors),F(Y.form.valid,E.type=="success")&&Y.formElement.querySelectorAll('input[type="file"]').forEach(Le=>Le.value=""))}}b||(E.type!=="error"?(E.type==="success"&&t.invalidateAll&&await yt(),t.applyAction?await Me(E):await vt(E)):await ne(E,Math.max(E.status??500,400)))}if(b&&t.flashMessage&&ft(t),b||E.type!="redirect")g.completed({cancelled:b});else if(ue)g.completed({cancelled:b,clearAll:!0});else{const v=it.subscribe(k=>{k||(setTimeout(()=>{try{v&&v()}catch{}}),g.isSubmitting()&&g.completed({cancelled:b,clearAll:!0}))})}ie()}if(!B&&Q){se();const w=await Q(x);let b;w instanceof Response?b=Be(await w.text()):w instanceof XMLHttpRequest?b=Be(w.responseText):b=w,b.type==="error"&&(b.status=w.status),re({result:b})}return re});return{destroy:()=>{for(const[x,I]of Object.entries(U))U[x]=I.filter(T=>T===t[x]);te.destroy()}}}function Nt(e){const r=[];if(K(e,f=>{if(f.value instanceof File)return r.push(f.path),"skip";if(Array.isArray(f.value)&&f.value.length&&f.value.every(m=>m instanceof File))return r.push(f.path),"skip"}),!r.length)return{data:e,paths:r};const s=q(e);return W(s,r,f=>{var m;return(m=ee(a.data,f))==null?void 0:m.value}),{data:s,paths:r}}return{form:y,formId:l,errors:z,message:ae,constraints:Ie,tainted:Ft(),submitting:Ne(xe),delayed:Ne(Qe),timeout:Ne(et),options:t,capture:ke,restore:e=>{Ee({form:e,untaint:e.tainted??!0})},async validate(e,r={}){if(!t.validators)throw new D("options.validators must be set to use the validate method.");r.update===void 0&&(r.update=!0),r.taint===void 0&&(r.taint=!1),typeof r.errors=="string"&&(r.errors=[r.errors]);let s;const f=fe(e);"value"in r?r.update===!0||r.update==="value"?(y.update(H=>(W(H,[f],r.value),H),{taint:r.taint}),s=o.form):(s=q(o.form),W(s,[f],r.value)):s=o.form;const m=await S({formData:s}),g=ee(m.errors,f);return g&&g.value&&r.errors&&(g.value=r.errors),(r.update===!0||r.update=="errors")&&z.update(H=>(W(H,[f],g==null?void 0:g.value),H)),g==null?void 0:g.value},async validateForm(e={}){if(!t.validators&&!e.schema)throw new D("options.validators or the schema option must be set to use the validateForm method.");const r=e.update?await h({paths:[]},!0,e.schema):S({adapter:e.schema}),s=de();return e.update&&s&&setTimeout(()=>{s&&qe(s,{...t,scrollToError:e.focusOnError===!1?"off":t.scrollToError})},1),r||S({adapter:e.schema})},allErrors:Lt,posted:je,reset(e){return Oe({message:e!=null&&e.keepMessage?o.message:void 0,data:e==null?void 0:e.data,id:e==null?void 0:e.id,newState:e==null?void 0:e.newState})},submit(e){const r=de()?de():e&&e instanceof HTMLElement?e.closest("form"):void 0;if(!r)throw new D("use:enhance must be added to the form to use submit, or pass a HTMLElement inside the form (or the form itself) as an argument.");if(!r.requestSubmit)return r.submit();const s=e&&(e instanceof HTMLButtonElement&&e.type=="submit"||e instanceof HTMLInputElement&&["submit","image"].includes(e.type));r.requestSubmit(s?e:void 0)},isTainted:Xe,enhance:nt}}new TextEncoder;let yn=!1;try{SUPERFORMS_LEGACY&&(yn=!0)}catch{}export{rn as e,En as s};
