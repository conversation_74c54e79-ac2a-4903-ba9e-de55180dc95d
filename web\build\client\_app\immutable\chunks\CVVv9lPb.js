import{c,a as s,f as y}from"./BasJTneF.js";import{p as h,f as d,a as S,g as v,x as k,c as G,au as I,r as j}from"./CGmarHxI.js";import{c as q}from"./BvdI7LR8.js";import{p as n,r as b,s as z}from"./Btcx8l8F.js";import{s as g}from"./ncUU1dSD.js";import{i as A}from"./u21ee2wt.js";import{e as B}from"./B-Xjo-Yt.js";import{b as _,m as C}from"./BfX7a-t9.js";import{u as D}from"./CGK0g3x_.js";import{u as E}from"./CnMg5bH0.js";var F=y("<div><!></div>");function H(i,r){h(r,!0);let m=n(r,"id",19,E),a=n(r,"ref",15,null),f=b(r,["$$slots","$$events","$$legacy","id","ref","children","child"]);const l=D({id:_.with(()=>m()),ref:_.with(()=>a(),e=>a(e))}),o=k(()=>C(f,l.props));var u=c(),P=d(u);{var x=e=>{var t=c(),p=d(t);g(p,()=>r.child,()=>({props:v(o)})),s(e,t)},w=e=>{var t=F();B(t,()=>({...v(o)}));var p=G(t);g(p,()=>r.children??I),j(t),s(e,t)};A(P,e=>{r.child?e(x):e(w,!1)})}s(i,u),S()}function V(i,r){h(r,!0),n(r,"ref",11,null);let m=b(r,["$$slots","$$events","$$legacy","ref"]);var a=c(),f=d(a);q(f,()=>H,(l,o)=>{o(l,z({"data-slot":"select-group"},()=>m))}),s(i,a),S()}export{V as S};
