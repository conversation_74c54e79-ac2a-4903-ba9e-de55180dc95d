import{c as n,a as m}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as f}from"./BBa424ah.js";import{l as i,s as l}from"./Btcx8l8F.js";import{I as c}from"./D4f2twK-.js";function M(a,o){const r=i(o,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143"}],["path",{d:"m2 2 20 20"}]];c(a,l({name:"eye-off"},()=>r,{get iconNode(){return s},children:(e,$)=>{var t=n(),p=d(t);f(p,o,"default",{},null),m(e,t)},$$slots:{default:!0}}))}export{M as E};
