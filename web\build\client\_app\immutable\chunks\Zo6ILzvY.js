import{f as C,a as c,e as Oe,c as K}from"./BasJTneF.js";import"./CgXBgsce.js";import{P as $,E as Ue,aF as Xe,S as Qe,aW as ke,V as Ke,W as $e,D as er,C as Ne,Z as Se,am as de,a7 as Ie,m as z,O as ee,a0 as rr,F as sr,aS as Ee,aR as De,aX as Ce,Q as or,X as ne,Y as ie,aY as tr,p as ar,l as Te,d as re,h as ce,aZ as Ae,b as dr,f as W,g as h,s as H,a as nr,c as _,r as y,t as le,e as ue,aJ as ir}from"./CGmarHxI.js";import{s as je}from"./CIt1g2O9.js";import{i as L}from"./u21ee2wt.js";import{h as fe}from"./DYwWIJ9y.js";import{s as cr}from"./BBa424ah.js";import{a as Me,m as lr,s as ur,e as Fe}from"./CmxjS0TN.js";import{s as G,b as fr}from"./B-Xjo-Yt.js";import{b as pr}from"./5V1tIHTN.js";import{p as br}from"./CWmzcjye.js";import{i as gr}from"./BIEMS98f.js";import{p}from"./Btcx8l8F.js";import{p as vr}from"./Buv24VCh.js";const pe=0,se=1,be=2;function mr(g,t,a,k,i){$&&Ue();var l=g,B=Xe(),U=or,b=Se,u,w,x,N=(B?Ie:z)(void 0),v=(B?Ie:z)(void 0),T=!1;function A(f,m){T=!0,m&&(Ee(S),De(S),Ce(U));try{f===pe&&a&&(u?ne(u):u=ee(()=>a(l))),f===se&&k&&(w?ne(w):w=ee(()=>k(l,N))),f===be&&i&&(x?ne(x):x=ee(()=>i(l,v))),f!==pe&&u&&ie(u,()=>u=null),f!==se&&w&&ie(w,()=>w=null),f!==be&&x&&ie(x,()=>x=null)}finally{m&&(Ce(null),De(null),Ee(null),tr())}}var S=Qe(()=>{if(b===(b=t()))return;let f=$&&ke(b)===(l.data===Ke);if(f&&(l=$e(),er(l),Ne(!1),f=!0),ke(b)){var m=b;T=!1,m.then(I=>{m===b&&(de(N,I),A(se,!0))},I=>{if(m===b&&(de(v,I),A(be,!0),!i))throw v.v}),$?a&&(u=ee(()=>a(l))):rr(()=>{T||A(pe,!0)})}else de(N,b),A(se,!1);return f&&Ne(!0),()=>b=Se});$&&(l=sr)}/*! clipboard-copy. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */function ge(){return new DOMException("The request is not allowed","NotAllowedError")}async function hr(g){if(!navigator.clipboard)throw ge();return navigator.clipboard.writeText(g)}async function _r(g){const t=document.createElement("span");t.textContent=g,t.style.whiteSpace="pre",t.style.webkitUserSelect="auto",t.style.userSelect="all",document.body.appendChild(t);const a=window.getSelection(),k=window.document.createRange();a==null||a.removeAllRanges(),k.selectNode(t),a==null||a.addRange(k);let i=!1;try{i=window.document.execCommand("copy")}finally{a==null||a.removeAllRanges(),window.document.body.removeChild(t)}if(!i)throw ge()}async function yr(g){try{await hr(g)}catch(t){try{await _r(g)}catch(a){throw a||t||ge()}}}var wr=C(`<style>.super-debug--absolute {
			position: absolute;
		}

		.super-debug--top-0 {
			top: 0;
		}

		.super-debug--inset-x-0 {
			left: 0px;
			right: 0px;
		}

		.super-debug--hidden {
			height: 0;
			overflow: hidden;
		}

		.super-debug--hidden:not(.super-debug--with-label) {
			height: 1.5em;
		}

		.super-debug--rotated {
			transform: rotate(180deg);
		}

		.super-debug {
			--_sd-bg-color: var(--sd-bg-color, var(--sd-vscode-bg-color, rgb(30, 41, 59)));
			position: relative;
			background-color: var(--_sd-bg-color);
			border-radius: 0.5rem;
			overflow: hidden;
		}

		.super-debug--pre {
			overflow-x: auto;
		}

		.super-debug--collapse {
			display: block;
			width: 100%;
			color: rgba(255, 255, 255, 0.25);
			background-color: rgba(255, 255, 255, 0.15);
			padding: 5px 0;
			display: flex;
			justify-content: center;
			border-color: transparent;
			margin: 0;
			padding: 3px 0;
		}

		.super-debug--collapse:focus {
			color: #fafafa;
			background-color: rgba(255, 255, 255, 0.25);
		}

		.super-debug--collapse:is(:hover) {
			color: rgba(255, 255, 255, 0.35);
			background-color: rgba(255, 255, 255, 0.25);
		}

		.super-debug--status {
			display: flex;
			padding: 1em;
			padding-bottom: 0;
			justify-content: space-between;
			font-family:
				Inconsolata, Monaco, Consolas, 'Lucida Console', 'Courier New', Courier, monospace;
		}

		.super-debug--right-status {
			display: flex;
			gap: 0.55em;
		}

		.super-debug--copy {
			margin: 0;
			padding: 0;
			padding-top: 2px;
			background-color: transparent;
			border: 0;
			color: #666;
			cursor: pointer;
		}

		.super-debug--copy:hover {
			background-color: transparent;
			color: #666;
		}

		.super-debug--copy:focus {
			background-color: transparent;
			color: #666;
		}

		.super-debug--label {
			color: var(--sd-label-color, var(--sd-vscode-label-color, white));
		}

		.super-debug--promise-loading {
			color: var(--sd-promise-loading-color, var(--sd-vscode-promise-loading-color, #999));
		}

		.super-debug--promise-rejected {
			color: var(--sd-promise-rejected-color, var(--sd-vscode-promise-rejected-color, #ff475d));
		}

		.super-debug pre {
			color: var(--sd-code-default, var(--sd-vscode-code-default, #999));
			background-color: var(--_sd-bg-color);
			font-size: 1em;
			margin-bottom: 0;
			padding: 1em 0 1em 1em;
		}

		.super-debug--info {
			color: var(--sd-info, var(--sd-vscode-info, rgb(85, 85, 255)));
		}

		.super-debug--success {
			color: var(--sd-success, var(--sd-vscode-success, #2cd212));
		}

		.super-debug--redirect {
			color: var(--sd-redirect, var(--sd-vscode-redirect, #03cae5));
		}

		.super-debug--error {
			color: var(--sd-error, var(--sd-vscode-error, #ff475d));
		}

		.super-debug--code .key {
			color: var(--sd-code-key, var(--sd-vscode-code-key, #eab308));
		}

		.super-debug--code .string {
			color: var(--sd-code-string, var(--sd-vscode-code-string, #6ec687));
		}

		.super-debug--code .date {
			color: var(--sd-code-date, var(--sd-vscode-code-date, #f06962));
		}

		.super-debug--code .boolean {
			color: var(--sd-code-boolean, var(--sd-vscode-code-boolean, #79b8ff));
		}

		.super-debug--code .number {
			color: var(--sd-code-number, var(--sd-vscode-code-number, #af77e9));
		}

		.super-debug--code .bigint {
			color: var(--sd-code-bigint, var(--sd-vscode-code-bigint, #af77e9));
		}

		.super-debug--code .null {
			color: var(--sd-code-null, var(--sd-vscode-code-null, #238afe));
		}

		.super-debug--code .nan {
			color: var(--sd-code-nan, var(--sd-vscode-code-nan, #af77e9));
		}

		.super-debug--code .undefined {
			color: var(--sd-code-undefined, var(--sd-vscode-code-undefined, #238afe));
		}

		.super-debug--code .function {
			color: var(--sd-code-function, var(--sd-vscode-code-function, #f06962));
		}

		.super-debug--code .symbol {
			color: var(--sd-code-symbol, var(--sd-vscode-code-symbol, #4de0c5));
		}

		.super-debug--code .error {
			color: var(--sd-code-error, var(--sd-vscode-code-error, #ff475d));
		}

		.super-debug pre::-webkit-scrollbar {
			width: var(--sd-sb-width, var(--sd-vscode-sb-width, 1rem));
			height: var(--sd-sb-height, var(--sd-vscode-sb-height, 1rem));
		}

		.super-debug pre::-webkit-scrollbar-track {
			border-radius: 12px;
			background-color: var(
				--sd-sb-track-color,
				var(--sd-vscode-sb-track-color, hsl(0, 0%, 40%, 0.2))
			);
		}
		.super-debug:is(:focus-within, :hover) pre::-webkit-scrollbar-track {
			border-radius: 12px;
			background-color: var(
				--sd-sb-track-color-focus,
				var(--sd-vscode-sb-track-color-focus, hsl(0, 0%, 50%, 0.2))
			);
		}

		.super-debug pre::-webkit-scrollbar-thumb {
			border-radius: 12px;
			background-color: var(
				--sd-sb-thumb-color,
				var(--sd-vscode-sb-thumb-color, hsl(217, 50%, 50%, 0.5))
			);
		}
		.super-debug:is(:focus-within, :hover) pre::-webkit-scrollbar-thumb {
			border-radius: 12px;
			background-color: var(
				--sd-sb-thumb-color-focus,
				var(--sd-vscode-sb-thumb-color-focus, hsl(217, 50%, 50%))
			);
		}</style>`),xr=Oe('<svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M7 9.667A2.667 2.667 0 0 1 9.667 7h8.666A2.667 2.667 0 0 1 21 9.667v8.666A2.667 2.667 0 0 1 18.333 21H9.667A2.667 2.667 0 0 1 7 18.333z"></path><path d="M4.012 16.737A2.005 2.005 0 0 1 3 15V5c0-1.1.9-2 2-2h10c.75 0 1.158.385 1.5 1"></path></g></svg>'),kr=Oe('<svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M15 12v6m-3-3h6"></path><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></g></svg>'),Nr=C("<div> </div>"),Sr=C('<span class="super-debug--promise-rejected">Rejected:</span> <!>',1),Ir=C('<div class="super-debug--promise-loading">Loading data...</div>'),Er=C('<button type="button" class="super-debug--collapse" aria-label="Collapse"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M4.08 11.92L12 4l7.92 7.92l-1.42 1.41l-5.5-5.5V22h-2V7.83l-5.5 5.5l-1.42-1.41M12 4h10V2H2v2h10Z"></path></svg></button>'),Dr=C('<div dir="ltr"><div><div class="super-debug--label"> </div> <div class="super-debug--right-status"><button type="button" class="super-debug--copy"><!></button> <!></div></div> <pre><code class="super-debug--code"><!></code></pre> <!></div>'),Cr=C("<!> <!>",1);function Yr(g,t){ar(t,!1);const[a,k]=ur(),i=()=>Me(vr,"$page",a),l=()=>Me(h(U),"$debugData",a),B=z(),U=z();let b=z(!1),u=p(t,"data",8),w=p(t,"display",8,!0),x=p(t,"status",8,!0),N=p(t,"label",8,""),v=p(t,"stringTruncate",8,120),T=p(t,"ref",12,void 0),A=p(t,"promise",8,!1),S=p(t,"raw",8,!1),f=p(t,"functions",8,!1),m=p(t,"theme",8,"default"),I=p(t,"collapsible",8,!1),j=p(t,"collapsed",12,!1);I()&&ve();function ve(s=void 0){let o;const e=i().route.id??"";try{sessionStorage.SuperDebug&&(o=JSON.parse(sessionStorage.SuperDebug)),o={collapsed:o&&o.collapsed?o.collapsed:{}},o.collapsed[e]=s===void 0?o.collapsed[e]??j():s}catch{o={collapsed:{[e]:j()}}}s!==void 0&&(sessionStorage.SuperDebug=JSON.stringify(o)),j(o.collapsed[e])}let X=z();async function Re(s){if(!s.target)return;const o=s.target.closest(".super-debug");if(!o)return;const e=o.querySelector(".super-debug--code");e&&(clearTimeout(h(X)),await yr(e.innerText),re(X,setTimeout(()=>re(X,void 0),900)))}function me(s){return{name:s.name,size:s.size,type:s.type,lastModified:new Date(s.lastModified)}}function oe(s){switch(typeof s){case"function":return`<span class="function">[function ${s.name??"unnamed"}]</span>`;case"symbol":return`<span class="symbol">${s.toString()}</span>`}return JSON.stringify(s,function(e,r){if(r===void 0)return"#}#undefined";if(typeof this=="object"&&this[e]instanceof Date)return"#}D#"+(isNaN(this[e])?"Invalid Date":r);if(typeof r=="number"){if(r==Number.POSITIVE_INFINITY)return"#}#Inf";if(r==Number.NEGATIVE_INFINITY)return"#}#-Inf";if(isNaN(r))return"#}#NaN"}if(typeof r=="bigint")return"#}BI#"+r;if(typeof r=="function"&&f())return`#}F#[function ${r.name}]`;if(r instanceof Error)return`#}E#${r.name}: ${r.message||r.cause||"(No error message)"}`;if(r instanceof Set)return Array.from(r);if(r instanceof Map)return Array.from(r.entries());if(typeof this=="object"&&typeof this[e]=="object"&&this[e]&&"toExponential"in this[e])return"#}DE#"+this[e].toString();if(typeof this=="object"&&this[e]instanceof File)return me(this[e]);if(typeof this=="object"&&this[e]instanceof FileList){const M=this[e],Q=[];for(let F=0;F<M.length;F++){const E=M.item(F);E&&Q.push(me(E))}return Q}return r},2).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+-]?\d+)?)/g,function(e){let r="number";return/^"/.test(e)?/:$/.test(e)?(r="key",e=e.slice(1,-2)+":"):(r="string",e=v()>0&&e.length>v()?e.slice(0,v()/2)+`[..${e.length-v()}/${e.length}..]`+e.slice(-v()/2):e,e=='"#}#undefined"'?(r="undefined",e="undefined"):e.startsWith('"#}D#')?(r="date",e=e.slice(5,-1)):e=='"#}#NaN"'?(r="nan",e="NaN"):e=='"#}#Inf"'?(r="nan",e="Infinity"):e=='"#}#-Inf"'?(r="nan",e="-Infinity"):e.startsWith('"#}BI#')?(r="bigint",e=e.slice(6,-1)+"n"):e.startsWith('"#}F#')?(r="function",e=e.slice(5,-1)):e.startsWith('"#}E#')?(r="error",e=e.slice(5,-1)):e.startsWith('"#}DE#')&&(r="number",e=e.slice(6,-1))):/true|false/.test(e)?r="boolean":/null/.test(e)&&(r="null"),'<span class="'+r+'">'+e+"</span>"})}function Ve(s,o,e){return o?!1:e||typeof s=="object"&&s!==null&&"then"in s&&typeof s.then=="function"}function he(s,o){return o?!1:typeof s=="object"&&s!==null&&"subscribe"in s&&typeof s.subscribe=="function"}Te(()=>ce(m()),()=>{re(B,m()==="vscode"?`
      --sd-vscode-bg-color: #1f1f1f;
      --sd-vscode-label-color: #cccccc;
      --sd-vscode-code-default: #8c8a89;
      --sd-vscode-code-key: #9cdcfe;
      --sd-vscode-code-string: #ce9171;
      --sd-vscode-code-number: #b5c180;
      --sd-vscode-code-boolean: #4a9cd6;
      --sd-vscode-code-null: #4a9cd6;
      --sd-vscode-code-undefined: #4a9cd6;
      --sd-vscode-code-nan: #4a9cd6;
      --sd-vscode-code-symbol: #4de0c5;
      --sd-vscode-sb-thumb-color: #35373a;
      --sd-vscode-sb-thumb-color-focus: #4b4d50;
    `:void 0)}),Te(()=>(ce(u()),ce(S()),Ae),()=>{lr(re(U,he(u(),S())?u():Ae(u())),"$debugData",a)}),dr(),gr();var _e=Cr(),ye=W(_e);{var We=s=>{var o=wr();c(s,o)};L(ye,s=>{h(b)||s(We)})}var He=H(ye,2);{var Le=s=>{var o=Dr();let e;var r=_(o),M=_(r),Q=_(M,!0);y(M);var F=H(M,2),E=_(F),ze=_(E);{var Be=d=>{var n=xr();c(d,n)},Je=d=>{var n=kr();c(d,n)};L(ze,d=>{h(X)?d(Je,!1):d(Be)})}y(E);var Ye=H(E,2);{var Ze=d=>{var n=Nr();let O;var R=_(n,!0);y(n),le(Y=>{O=G(n,1,"",null,O,Y),je(R,i().status)},[()=>({"super-debug--info":i().status<200,"super-debug--success":i().status>=200&&i().status<300,"super-debug--redirect":i().status>=300&&i().status<400,"super-debug--error":i().status>=400})],ue),c(d,n)};L(Ye,d=>{x()&&d(Ze)})}y(F),y(r);var J=H(r,2);let we;var xe=_(J),qe=_(xe);cr(qe,t,"default",{},d=>{var n=K(),O=W(n);{var R=V=>{var Z=K(),te=W(Z);mr(te,l,q=>{var D=Ir();c(q,D)},(q,D)=>{var P=K(),ae=W(P);fe(ae,()=>oe(he(h(D),S())?ir(h(D)):h(D))),c(q,P)},(q,D)=>{var P=Sr(),ae=H(W(P),2);fe(ae,()=>oe(h(D))),c(q,P)}),c(V,Z)},Y=V=>{var Z=K(),te=W(Z);fe(te,()=>oe(l())),c(V,Z)};L(O,V=>{Ve(l(),S(),A())?V(R):V(Y,!1)})}c(d,n)}),y(xe),y(J),pr(J,d=>T(d),()=>T());var Pe=H(J,2);{var Ge=d=>{var n=Er(),O=_(n);let R;y(n),le(Y=>R=G(O,0,"",null,R,Y),[()=>({"super-debug--rotated":j()})],ue),Fe("click",n,br(()=>ve(!j()))),c(d,n)};L(Pe,d=>{I()&&d(Ge)})}y(o),le((d,n)=>{e=G(o,1,"super-debug",null,e,d),fr(o,h(B)),G(r,1,`super-debug--status ${N()===""?"super-debug--absolute super-debug--inset-x-0 super-debug--top-0":""}`),je(Q,N()),we=G(J,1,"super-debug--pre",null,we,n),o.dir=o.dir},[()=>({"super-debug--collapsible":I()}),()=>({"super-debug--with-label":N(),"super-debug--hidden":j()})],ue),Fe("click",E,Re),c(s,o)};L(He,s=>{w()&&s(Le)})}c(g,_e),nr(),k()}export{Yr as S};
