function E(e){try{return e?typeof e=="string"?JSON.parse(e):e.data&&typeof e.data=="string"?JSON.parse(e.data):e.data&&typeof e.data=="object"?e.data:e:{}}catch(s){return console.error("Error parsing profile data:",s),{}}}function P(e){const s={...e};if(s.personalInfo||(s.personalInfo={fullName:e.fullName,email:e.email,phone:e.phone,location:e.location,website:e.website,summary:e.summary,jobTitle:e.jobType}),!s.skillsData&&e.skills){let r=[];Array.isArray(e.skills)?r=e.skills:typeof e.skills=="string"&&(r=e.skills.split(",").map(i=>i.trim()).filter(Boolean)),s.skillsData={list:r,technical:r}}return s.workExperience||(s.workExperience=[]),s.education||(s.education=[]),s.projects||(s.projects=[]),s.certifications||(s.certifications=[]),s.languages||(s.languages=[]),s.achievements||(s.achievements=[]),s.jobPreferences||(s.jobPreferences={valueInRole:[],interestedRoles:[],roleSpecializations:[],preferredLocations:[],experienceLevel:"",companySize:"",desiredIndustries:[],avoidIndustries:[],preferredSkills:[],avoidSkills:[],minimumSalary:"",securityClearance:"",jobSearchStatus:""}),s}function b(e){var n,l,o,m,f,h,g,k,p;let s=0;const r=7;return(((n=e.personalInfo)==null?void 0:n.fullName)||e.fullName||((l=e.header)==null?void 0:l.fullName))&&s++,(((o=e.personalInfo)==null?void 0:o.summary)||e.summary||((m=e.personalInfo)==null?void 0:m.headline)||((f=e.header)==null?void 0:f.headline))&&s++,(e.workExperience&&e.workExperience.length>0||e.workExperiences&&e.workExperiences.length>0)&&s++,(e.education&&e.education.length>0||e.educations&&e.educations.length>0)&&s++,(((h=e.skillsData)==null?void 0:h.list)&&e.skillsData.list.length>0||Array.isArray(e.skills)&&e.skills.length>0||((g=e.skills)==null?void 0:g.skills)&&e.skills.skills.length>0)&&s++,e.projects&&e.projects.length>0&&s++,(((k=e.jobPreferences)==null?void 0:k.preferredLocations)&&e.jobPreferences.preferredLocations.length>0||((p=e.jobPreferences)==null?void 0:p.interestedRoles)&&e.jobPreferences.interestedRoles.length>0)&&s++,Math.round(s/r*100)}function j(e,s=70){var a,u,n,l;const r=[];if(!((a=e.data)!=null&&a.data))return{isEligible:!1,completionPercentage:0,missingRequirements:["Profile data is missing"],hasResume:!1};let i=e.data.data;if(typeof i=="string")try{i=JSON.parse(i)}catch(o){return console.error("Error parsing profile data JSON:",o),{isEligible:!1,completionPercentage:0,missingRequirements:["Profile data is corrupted"],hasResume:!1}}const t=b(i);t<s&&r.push(`Profile completion is ${t}%, minimum required is ${s}%`);const c=e.documents&&e.documents.length>0;return c||r.push("At least one resume is required"),!((u=i.personalInfo)!=null&&u.fullName)&&!i.fullName&&r.push("Full name is required"),!((n=i.personalInfo)!=null&&n.email)&&!i.email&&r.push("Email address is required"),((l=i.skillsData)==null?void 0:l.list)&&i.skillsData.list.length>0||Array.isArray(i.skills)&&i.skills.length>0||r.push("Skills are required for job matching"),(!i.workExperience||i.workExperience.length===0)&&r.push("Work experience is required for job matching"),{isEligible:r.length===0,completionPercentage:t,missingRequirements:r,hasResume:c}}export{j as a,b as c,P as m,E as p};
