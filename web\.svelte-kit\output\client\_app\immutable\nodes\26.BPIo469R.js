import{c as je,a as s,f as l,t as he}from"../chunks/BasJTneF.js";import{o as lt,a as Rt}from"../chunks/nZgk9enP.js";import{p as Ze,d as j,m as ct,f as v,a as et,c as t,s as r,g as a,r as e,t as ve,e as At,k as Re,i as yt,n as m,x as Xe,ab as ht,aM as Dt}from"../chunks/CGmarHxI.js";import{s as u}from"../chunks/CIt1g2O9.js";import{i as le}from"../chunks/u21ee2wt.js";import{e as qe,i as Ge}from"../chunks/C3w0v0gR.js";import{c as d}from"../chunks/BvdI7LR8.js";import{r as $t,s as vt,f as Ft,b as dt}from"../chunks/B-Xjo-Yt.js";import{S as Tt}from"../chunks/C6g8ubaU.js";import"../chunks/CgXBgsce.js";import{g as Lt}from"../chunks/CmxjS0TN.js";import{t as bt}from"../chunks/BYB878do.js";import{b as Ut}from"../chunks/CzsE_FAw.js";import{i as wt}from"../chunks/BIEMS98f.js";import{p as Je}from"../chunks/Btcx8l8F.js";import{f as It,a as Yt}from"../chunks/Dq03aqGn.js";import{B as Oe}from"../chunks/B1K98fMG.js";import{C as tt}from"../chunks/DuGukytH.js";import{C as at}from"../chunks/Cdn-N1RY.js";import{C as rt}from"../chunks/BkJY4La4.js";import{C as st}from"../chunks/GwmmX_iF.js";import{C as ot}from"../chunks/D50jIuLr.js";import{t as Ke}from"../chunks/DjPYYl4Z.js";import{R as Pt}from"../chunks/BMRJMPdn.js";import{P as Bt,D as Mt,a as St,R as kt}from"../chunks/tdzGgazS.js";import{C as Et,a as zt,b as Ht}from"../chunks/C6FI6jUA.js";import{C as Nt}from"../chunks/T7uRAIbG.js";import{C as Ot}from"../chunks/BBNNmnYR.js";import{C as Wt}from"../chunks/DW7T7T22.js";import{C as Jt}from"../chunks/DkmCSZhC.js";import{S as Qt}from"../chunks/yW0TxTga.js";import{F as qt}from"../chunks/ChqRiddM.js";import{B as ut}from"../chunks/CDnvByek.js";import{T as Gt}from"../chunks/CZ8wIJN8.js";import{B as ft}from"../chunks/DaBofrVv.js";import{S as Vt}from"../chunks/0ykhD7u6.js";import{D as Kt,a as Xt,b as Zt,c as ea}from"../chunks/CKh8VGVX.js";import{S as mt}from"../chunks/BAawoUIy.js";import{E as Ct}from"../chunks/zNKWipEG.js";import{C as jt}from"../chunks/Dt_Sfkn6.js";import{U as pt}from"../chunks/BSHZ37s_.js";import{C as ta}from"../chunks/DETxXRrJ.js";import{R as aa}from"../chunks/C4zOxlM4.js";import{A as ra}from"../chunks/B-l1ubNa.js";import{C as sa}from"../chunks/CxmsTEaf.js";var oa=l('<div class="mb-6"><label for="seatCount" class="mb-1 block font-medium">Number of Seats</label> <input id="seatCount" type="number" min="1" class="w-32 rounded border px-3 py-2"/></div>'),la=l('<div class="flex h-40 w-full items-center justify-center"><div class="h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div></div>'),ia=(Ie,o,p)=>o(a(p).id,"monthly"),na=(Ie,o,p)=>o(a(p).id,"annual"),da=l('<div class="flex flex-col justify-between rounded-md border p-4 shadow-sm"><div><h3 class="text-lg font-semibold"> </h3> <p class="text-sm text-gray-500"> </p> <p class="mt-2 font-bold text-blue-600"> </p></div> <div class="mt-4 flex flex-col space-y-2"><button class="rounded bg-blue-600 px-3 py-2 text-white">Monthly Billing</button> <button class="rounded bg-gray-200 px-3 py-2 text-gray-800">Annual Billing (20% off)</button></div></div>'),va=l('<div class="grid gap-4 md:grid-cols-2"></div>'),ca=l(`<div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"><div class="relative max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-white p-6 shadow-xl"><button class="absolute right-2 top-2 text-gray-500">✕</button> <h2 class="mb-2 text-2xl font-bold">Upgrade your plan</h2> <p class="mb-6 text-gray-600">You’ve hit the free plan limit. Choose a plan to continue applying to jobs.</p> <!> <!> <div class="mt-8"><h4 class="mb-2 text-lg font-semibold">Frequently Asked Questions</h4> <ul class="space-y-2 text-sm text-gray-600"><li><strong>Q:</strong> Can I cancel anytime?<br/><strong>A:</strong> Yes! You can manage your
            subscription from your dashboard.</li> <li><strong>Q:</strong> What happens after I upgrade?<br/><strong>A:</strong> Your limits and
            access will be updated immediately.</li> <li><strong>Q:</strong> Can I switch between monthly and yearly later?<br/><strong>A:</strong> Yes, you can change your billing cycle in your account.</li></ul></div> <div class="mt-6 flex justify-end"><button class="text-gray-500 underline">Close</button></div></div></div>`);function ua(Ie,o){Ze(o,!1);let p=Je(o,"open",8,!1),be=Je(o,"onClose",8,()=>{}),f=Je(o,"section",8,"pro"),h=ct([]),ce=ct(!0),J=ct(3);lt(async()=>{try{const c=await fetch("/api/admin/plans");if(c.ok){const F=await c.json();j(h,F.filter(E=>E.section===f()&&E.id!=="free"))}else console.error("Error loading plans from API"),j(h,[{id:"casual",name:"Casual",description:"For occasional job seekers",section:"pro",monthlyPrice:999,annualPrice:9990,features:[]},{id:"active",name:"Active",description:"For active job seekers",section:"pro",monthlyPrice:1999,annualPrice:19990,features:[]}].filter(F=>F.section===f()))}catch(c){console.error("Error loading plans:",c),j(h,[{id:"casual",name:"Casual",description:"For occasional job seekers",section:"pro",monthlyPrice:999,annualPrice:9990,features:[]},{id:"active",name:"Active",description:"For active job seekers",section:"pro",monthlyPrice:1999,annualPrice:19990,features:[]}].filter(F=>F.section===f()))}finally{j(ce,!1)}});function ie(c,F="monthly"){fetch("/api/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:c,billingCycle:F,seatCount:a(J)})}).then(E=>E.json()).then(E=>{E.url&&(window.location.href=E.url)})}wt();var pe=je(),Z=v(pe);{var I=c=>{var F=ca(),E=t(F),Ye=t(E);Ye.__click=[function(...b){var $;($=be())==null||$.apply(this,b)}];var Ae=r(Ye,6);{var ze=b=>{var $=oa(),ee=r(t($),2);$t(ee),e($),Ut(ee,()=>a(J),te=>j(J,te)),s(b,$)};le(Ae,b=>{f()==="teams"&&b(ze)})}var De=r(Ae,2);{var ye=b=>{var $=la();s(b,$)},ue=b=>{var $=va();qe($,5,()=>a(h),Ge,(ee,te)=>{var ae=da(),ne=t(ae),Y=t(ne),z=t(Y,!0);e(Y);var P=r(Y,2),q=t(P,!0);e(P);var A=r(P,2),B=t(A);e(A),e(ne);var G=r(ne,2),H=t(G);H.__click=[ia,ie,te];var fe=r(H,2);fe.__click=[na,ie,te],e(G),e(ae),ve((V,_e)=>{u(z,a(te).name),u(q,a(te).description),u(B,`$${V??""}/mo or $${_e??""}/yr`)},[()=>(a(te).monthlyPrice/100).toFixed(2),()=>(a(te).annualPrice/100).toFixed(2)],At),s(ee,ae)}),e($),s(b,$)};le(De,b=>{a(ce)?b(ye):b(ue,!1)})}var Be=r(De,4),Fe=t(Be);Fe.__click=[function(...b){var $;($=be())==null||$.apply(this,b)}],e(Be),e(E),e(F),bt(3,E,()=>It,()=>({y:20,duration:250})),bt(3,F,()=>Yt),s(c,F)};le(Z,c=>{p()&&c(I)})}s(Ie,pe),et()}Lt(["click"]);function fa(Ie,o){Ze(o,!1);let p=Je(o,"userData",8,null);lt(()=>{if(p()){const be=localStorage.getItem("welcomeToastShown"),f=localStorage.getItem("onboardingCompleted");!be&&!f&&setTimeout(()=>{Ke.success("Welcome to Hirli!",{description:`Hi ${p().name||"there"}, we're excited to have you on board!`,duration:5e3,icon:Pt}),localStorage.setItem("welcomeToastShown","true")},1e3)}}),wt(),et()}var ma=l("<div></div>"),pa=l("<!> Previous",1),_a=l(" <!>",1),ga=l(`<div class="flex flex-col items-center p-6 text-center"><div class="bg-primary/10 mb-4 flex h-16 w-16 items-center justify-center rounded-full"><!></div> <h2 class="mb-2 text-2xl font-bold"> </h2> <p class="text-muted-foreground mb-6 max-w-md"> </p> <div class="bg-muted mb-6 flex h-48 w-full items-center justify-center rounded-lg"><!></div> <div class="mb-6 flex gap-2"></div> <div class="mb-6 flex items-center space-x-2"><!> <label for="dont-show-again" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Don't show this again</label></div> <div class="flex w-full justify-between"><!> <!></div></div>`),xa=l('<div class="relative"><!></div>'),ha=l("<!> <!>",1);function ba(Ie,o){Ze(o,!0);let p=Je(o,"open",7,!1),be=Je(o,"onClose",3,()=>{}),f=Je(o,"userData",3,null),h=Re(0);const ce=5;let J=Re(null),ie=Re(!1),pe=Re(!1),Z=Re(!1);const I=[{title:"Welcome to Hirli!",description:"We're excited to have you on board. Let's take a quick tour to help you get started.",icon:Pt,color:"text-blue-500"},{title:"Find Your Perfect Job",description:"Use our powerful search to find jobs that match your skills and experience.",icon:Qt,color:"text-purple-500"},{title:"Upload Your Resume",description:"Upload your resume to get personalized job matches and make applying easier.",icon:qt,color:"text-green-500"},{title:"Track Your Applications",description:"Keep track of all your job applications in one place.",icon:ut,color:"text-amber-500"},{title:"Get Matched to Jobs",description:"Our AI will match you with jobs that fit your skills and experience.",icon:Gt,color:"text-red-500"}];function c(){a(h)<ce-1?(ht(h),a(J)&&a(pe)&&a(J).scrollTo(a(h))):E()}function F(){a(h)>0&&(ht(h,-1),a(J)&&a(pe)&&a(J).scrollTo(a(h)))}function E(){typeof localStorage<"u"&&(a(ie)&&localStorage.setItem("onboardingCompleted","true"),localStorage.setItem("welcomeToastShown","true")),be()()}function Ye(De){j(J,De,!0),a(J)&&(j(pe,!0),a(J).on("select",()=>{j(h,a(J).selectedScrollSnap(),!0)}),a(h)===0&&a(J).scrollTo(0))}lt(()=>{typeof localStorage<"u"&&!a(Z)&&(j(Z,!0),!localStorage.getItem("onboardingCompleted")&&f()&&p(!0))}),Rt(()=>{a(J)&&j(J,null),j(pe,!1)}),yt(()=>{p()||j(h,0)});var Ae=je(),ze=v(Ae);d(ze,()=>kt,(De,ye)=>{ye(De,{get open(){return p()},set open(ue){p(ue)},children:(ue,Be)=>{var Fe=je(),b=v(Fe);d(b,()=>Bt,($,ee)=>{ee($,{children:(te,ae)=>{var ne=ha(),Y=v(ne);d(Y,()=>Mt,(P,q)=>{q(P,{})});var z=r(Y,2);d(z,()=>St,(P,q)=>{q(P,{class:"gap-0 p-0 sm:w-[600px]",children:(A,B)=>{var G=xa(),H=t(G);d(H,()=>Et,(fe,V)=>{V(fe,{opts:{loop:!1,dragFree:!1},setApi:Ye,class:"w-[510px]",children:(_e,ke)=>{var Me=je(),Qe=v(Me);d(Qe,()=>zt,(He,We)=>{We(He,{children:(Ne,me)=>{var ge=je(),re=v(ge);qe(re,17,()=>I,Ge,(Ee,Te)=>{var Le=je(),$e=v(Le);d($e,()=>Ht,(T,_)=>{_(T,{class:"basis-full",children:(M,se)=>{var xe=ga(),y=t(xe),g=t(y);{var N=S=>{var K=je(),R=v(K);d(R,()=>a(Te).icon,(W,X)=>{X(W,{class:"text-primary h-8 w-8"})}),s(S,K)};le(g,S=>{a(Te).icon&&S(N)})}e(y);var k=r(y,2),L=t(k,!0);e(k);var U=r(k,2),we=t(U,!0);e(U);var w=r(U,2),D=t(w);{var Q=S=>{var K=je(),R=v(K);d(R,()=>a(Te).icon,(W,X)=>{X(W,{get class(){return`h-24 w-24 ${a(Te).color??""}`}})}),s(S,K)};le(D,S=>{a(Te).icon&&S(Q)})}e(w);var x=r(w,2);qe(x,21,()=>Array(ce),Ge,(S,K,R)=>{var W=ma();let X;ve(Ue=>X=vt(W,1,"h-2 w-2 rounded-full transition-colors duration-200",null,X,Ue),[()=>({"bg-primary":R===a(h),"bg-muted":R!==a(h)})]),s(S,W)}),e(x);var n=r(x,2),i=t(n);d(i,()=>Nt,(S,K)=>{K(S,{get checked(){return a(ie)},onCheckedChange:R=>j(ie,R,!0),id:"dont-show-again"})}),m(2),e(n);var de=r(n,2),C=t(de);const O=Xe(()=>a(h)===0);Oe(C,{variant:"outline",onclick:()=>F(),get disabled(){return a(O)},children:(S,K)=>{var R=pa(),W=v(R);Ot(W,{class:"mr-2 h-4 w-4"}),m(),s(S,R)},$$slots:{default:!0}});var Pe=r(C,2);const oe=Xe(()=>a(h)===ce-1?"default":"outline");Oe(Pe,{get variant(){return a(oe)},onclick:()=>a(h)===ce-1?E():c(),children:(S,K)=>{m();var R=_a(),W=v(R),X=r(W);{var Ue=Se=>{Wt(Se,{class:"ml-2 h-4 w-4"})},Ce=Se=>{Jt(Se,{class:"ml-2 h-4 w-4"})};le(X,Se=>{a(h)===ce-1?Se(Ue):Se(Ce,!1)})}ve(()=>u(W,`${a(h)===ce-1?"Get Started":"Next"} `)),s(S,R)},$$slots:{default:!0}}),e(de),e(xe),ve(()=>{u(L,a(Te).title),u(we,a(Te).description)}),s(M,xe)},$$slots:{default:!0}})}),s(Ee,Le)}),s(Ne,ge)},$$slots:{default:!0}})}),s(_e,Me)},$$slots:{default:!0}})}),e(G),s(A,G)},$$slots:{default:!0}})}),s(te,ne)},$$slots:{default:!0}})}),s(ue,Fe)},$$slots:{default:!0}})}),s(Ie,Ae),et()}var ya=l("<!> Referral Program",1),$a=l("<!> <!>",1),wa=l('<div class="flex items-center justify-center py-8"><div class="text-muted-foreground">Loading referral data...</div></div>'),Pa=l('<div class="flex items-center justify-between rounded border p-2"><div><p class="text-sm font-medium"> </p> <p class="text-muted-foreground text-xs"> </p></div> <!></div>'),Sa=l('<p class="text-muted-foreground text-center text-sm"> </p>'),ka=l('<div class="space-y-3"><h3 class="font-semibold">Recent Referrals</h3> <div class="max-h-40 space-y-2 overflow-y-auto"></div> <!></div>'),Ca=l('<div class="py-6 text-center"><!> <p class="text-muted-foreground">No referrals yet</p> <p class="text-muted-foreground text-sm">Start sharing your link to earn rewards!</p></div>'),ja=l('<div class="space-y-2"><h3 class="text-sm font-semibold">You Were Referred By</h3> <div class="flex items-center gap-2 rounded border p-2"><p class="text-sm font-medium"> </p> <!></div></div>'),Ra=l('<div class="grid grid-cols-3 gap-4"><div class="text-center"><div class="text-2xl font-bold"> </div> <div class="text-muted-foreground text-sm">Total Referrals</div></div> <div class="text-center"><div class="font-mono text-2xl font-bold"> </div> <div class="text-muted-foreground text-sm">Your Code</div></div> <div class="text-center"><div class="text-2xl font-bold">$0</div> <div class="text-muted-foreground text-sm">Rewards (Soon)</div></div></div> <!> <div class="space-y-3"><h3 class="font-semibold">Your Referral Link</h3> <div class="flex gap-2"><input readonly="" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 font-mono text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/> <!> <!></div> <p class="text-muted-foreground text-sm">Share this link with friends to earn referral rewards when they sign up.</p></div> <!> <!>',1),Aa=l('<div class="flex items-center justify-center py-8"><div class="text-muted-foreground">Failed to load referral data</div></div>'),Da=l("<!> Full Settings",1),Fa=l("<!> <!>",1),Ta=l('<!> <div class="space-y-6"><!></div> <!>',1);function La(Ie,o){Ze(o,!0);let p=Je(o,"open",15,!1),be=Je(o,"onClose",3,()=>{}),f=Re(null),h=Re(!0),ce=Re(!1);const J=async()=>{try{const c=await fetch("/api/referrals");c.ok?j(f,await c.json(),!0):Ke.error("Failed to load referral data")}catch(c){console.error("Error loading referral data:",c),Ke.error("Failed to load referral data")}finally{j(h,!1)}},ie=async()=>{var c;if((c=a(f))!=null&&c.referralLink){j(ce,!0);try{await navigator.clipboard.writeText(a(f).referralLink),Ke.success("Referral link copied to clipboard!")}catch(F){console.error("Error copying to clipboard:",F),Ke.error("Failed to copy referral link")}finally{j(ce,!1)}}},pe=async()=>{var c;if((c=a(f))!=null&&c.referralLink)if(navigator.share)try{await navigator.share({title:"Join Hirli with my referral link",text:"Sign up for Hirli using my referral link and get started with job automation!",url:a(f).referralLink})}catch(F){console.error("Error sharing:",F),ie()}else ie()};yt(()=>{p()&&J()});var Z=je(),I=v(Z);d(I,()=>kt,(c,F)=>{F(c,{get open(){return p()},set open(E){p(E)},children:(E,Ye)=>{var Ae=je(),ze=v(Ae);d(ze,()=>St,(De,ye)=>{ye(De,{class:"max-w-2xl",children:(ue,Be)=>{var Fe=Ta(),b=v(Fe);d(b,()=>Kt,(Y,z)=>{z(Y,{children:(P,q)=>{var A=$a(),B=v(A);d(B,()=>Xt,(H,fe)=>{fe(H,{class:"flex items-center gap-2",children:(V,_e)=>{var ke=ya(),Me=v(ke);mt(Me,{class:"h-5 w-5"}),m(),s(V,ke)},$$slots:{default:!0}})});var G=r(B,2);d(G,()=>Zt,(H,fe)=>{fe(H,{children:(V,_e)=>{m();var ke=he("Share Hirli with friends and earn rewards for successful referrals.");s(V,ke)},$$slots:{default:!0}})}),s(P,A)},$$slots:{default:!0}})});var $=r(b,2),ee=t($);{var te=Y=>{var z=wa();s(Y,z)},ae=(Y,z)=>{{var P=A=>{var B=Ra(),G=v(B),H=t(G),fe=t(H),V=t(fe,!0);e(fe),m(2),e(H);var _e=r(H,2),ke=t(_e),Me=t(ke,!0);e(ke),m(2),e(_e),m(2),e(G);var Qe=r(G,2);Vt(Qe,{});var He=r(Qe,2),We=r(t(He),2),Ne=t(We);$t(Ne);var me=r(Ne,2);Oe(me,{variant:"outline",size:"sm",onclick:ie,get disabled(){return a(ce)},children:(T,_)=>{jt(T,{class:"h-4 w-4"})},$$slots:{default:!0}});var ge=r(me,2);Oe(ge,{variant:"outline",size:"sm",onclick:pe,children:(T,_)=>{mt(T,{class:"h-4 w-4"})},$$slots:{default:!0}}),e(We),m(2),e(He);var re=r(He,2);{var Ee=T=>{var _=ka(),M=r(t(_),2);qe(M,21,()=>a(f).referrals.slice(0,3),Ge,(y,g)=>{var N=Pa(),k=t(N),L=t(k),U=t(L,!0);e(L);var we=r(L,2),w=t(we);e(we),e(k);var D=r(k,2);const Q=Xe(()=>a(g).status==="completed"?"default":"secondary");ft(D,{get variant(){return a(Q)},class:"text-xs",children:(x,n)=>{m();var i=he();ve(()=>u(i,a(g).status||"pending")),s(x,i)},$$slots:{default:!0}}),e(N),ve(x=>{var n,i;u(U,((n=a(g).referred)==null?void 0:n.name)||((i=a(g).referred)==null?void 0:i.email)||"Unknown User"),u(w,`Joined ${x??""}`)},[()=>new Date(a(g).createdAt).toLocaleDateString()]),s(y,N)}),e(M);var se=r(M,2);{var xe=y=>{var g=Sa(),N=t(g);e(g),ve(()=>u(N,`And ${a(f).referrals.length-3} more...`)),s(y,g)};le(se,y=>{a(f).referrals.length>3&&y(xe)})}e(_),s(T,_)},Te=T=>{var _=Ca(),M=t(_);pt(M,{class:"text-muted-foreground mx-auto mb-2 h-12 w-12"}),m(4),e(_),s(T,_)};le(re,T=>{a(f).referrals&&a(f).referrals.length>0?T(Ee):T(Te,!1)})}var Le=r(re,2);{var $e=T=>{var _=ja(),M=r(t(_),2),se=t(M),xe=t(se,!0);e(se);var y=r(se,2);ft(y,{variant:"outline",class:"text-xs",children:(g,N)=>{m();var k=he("Referrer");s(g,k)},$$slots:{default:!0}}),e(M),e(_),ve(()=>u(xe,a(f).referredBy.name||a(f).referredBy.email)),s(T,_)};le(Le,T=>{a(f).referredBy&&T($e)})}ve(()=>{u(V,a(f).referralCount||0),u(Me,a(f).referralCode),Ft(Ne,a(f).referralLink)}),s(A,B)},q=A=>{var B=Aa();s(A,B)};le(Y,A=>{a(f)?A(P):A(q,!1)},z)}};le(ee,Y=>{a(h)?Y(te):Y(ae,!1)})}e($);var ne=r($,2);d(ne,()=>ea,(Y,z)=>{z(Y,{class:"flex justify-between",children:(P,q)=>{var A=Fa(),B=v(A);Oe(B,{variant:"outline",href:"/dashboard/settings/referrals",children:(H,fe)=>{var V=Da(),_e=v(V);Ct(_e,{class:"mr-2 h-4 w-4"}),m(),s(H,V)},$$slots:{default:!0}});var G=r(B,2);Oe(G,{variant:"outline",onclick:be(),children:(H,fe)=>{m();var V=he("Close");s(H,V)},$$slots:{default:!0}}),s(P,A)},$$slots:{default:!0}})}),s(ue,Fe)},$$slots:{default:!0}})}),s(E,Ae)},$$slots:{default:!0}})}),s(Ie,Z),et()}var Ua=l('<div class="flex items-center justify-between"><div class="flex items-center gap-2"><!> <!></div> <!></div> <!>',1),Ia=l('<div class="flex items-center justify-center py-4"><div class="text-muted-foreground text-sm">Loading...</div></div>'),Ya=l("<!> Copy Link",1),Ba=l("<!> View All",1),Ma=l('<div class="flex items-center justify-between text-sm"><span class="truncate"> </span> <!></div>'),Ea=l('<div class="text-muted-foreground text-center text-xs"> </div>'),za=l('<div class="space-y-2"><div class="text-sm font-medium">Recent Referrals</div> <!> <!></div>'),Ha=l('<div class="py-4 text-center"><!> <p class="text-muted-foreground text-sm">No referrals yet</p> <p class="text-muted-foreground text-xs">Start sharing to earn rewards!</p></div>'),Na=l('<div class="grid grid-cols-2 gap-4"><div class="bg-muted/50 rounded-lg p-3 text-center"><div class="text-xl font-bold"> </div> <div class="text-muted-foreground text-xs">Referrals</div></div> <div class="bg-muted/50 rounded-lg p-3 text-center"><div class="font-mono text-lg font-bold"> </div> <div class="text-muted-foreground text-xs">Your Code</div></div></div> <div class="flex gap-2"><!> <!></div> <!>',1),Oa=l('<div class="py-4 text-center"><div class="text-muted-foreground text-sm">Failed to load referral data</div></div>'),Wa=l("<!> <!> <!>",1),Ja=l("<!> <!>",1);function Qa(Ie,o){Ze(o,!0);let p=Re(null),be=Re(!0),f=Re(!1),h=Re(!1);const ce=async()=>{try{const I=await fetch("/api/referrals");I.ok?j(p,await I.json(),!0):console.error("Failed to load referral data")}catch(I){console.error("Error loading referral data:",I)}finally{j(be,!1)}},J=async()=>{var I;if((I=a(p))!=null&&I.referralLink){j(h,!0);try{await navigator.clipboard.writeText(a(p).referralLink),Ke.success("Referral link copied!")}catch(c){console.error("Error copying to clipboard:",c),Ke.error("Failed to copy referral link")}finally{j(h,!1)}}};lt(()=>{ce()});var ie=Ja(),pe=v(ie);d(pe,()=>tt,(I,c)=>{c(I,{class:"relative overflow-hidden",children:(F,E)=>{var Ye=Wa(),Ae=v(Ye);d(Ae,()=>st,(ye,ue)=>{ue(ye,{class:"pb-3",children:(Be,Fe)=>{var b=Ua(),$=v(b),ee=t($),te=t(ee);mt(te,{class:"text-primary h-5 w-5"});var ae=r(te,2);d(ae,()=>ot,(z,P)=>{P(z,{class:"text-lg",children:(q,A)=>{m();var B=he("Referral Program");s(q,B)},$$slots:{default:!0}})}),e(ee);var ne=r(ee,2);Oe(ne,{variant:"ghost",size:"sm",onclick:()=>j(f,!0),class:"h-8 w-8 p-0",children:(z,P)=>{Ct(z,{class:"h-4 w-4"})},$$slots:{default:!0}}),e($);var Y=r($,2);d(Y,()=>rt,(z,P)=>{P(z,{children:(q,A)=>{m();var B=he("Share Hirli with friends and earn rewards");s(q,B)},$$slots:{default:!0}})}),s(Be,b)},$$slots:{default:!0}})});var ze=r(Ae,2);d(ze,()=>at,(ye,ue)=>{ue(ye,{class:"space-y-4",children:(Be,Fe)=>{var b=je(),$=v(b);{var ee=ae=>{var ne=Ia();s(ae,ne)},te=(ae,ne)=>{{var Y=P=>{var q=Na(),A=v(q),B=t(A),G=t(B),H=t(G,!0);e(G),m(2),e(B);var fe=r(B,2),V=t(fe),_e=t(V,!0);e(V),m(2),e(fe),e(A);var ke=r(A,2),Me=t(ke);Oe(Me,{variant:"outline",size:"sm",class:"flex-1",onclick:J,get disabled(){return a(h)},children:(me,ge)=>{var re=Ya(),Ee=v(re);jt(Ee,{class:"mr-2 h-3 w-3"}),m(),s(me,re)},$$slots:{default:!0}});var Qe=r(Me,2);Oe(Qe,{variant:"outline",size:"sm",class:"flex-1",onclick:()=>j(f,!0),children:(me,ge)=>{var re=Ba(),Ee=v(re);pt(Ee,{class:"mr-2 h-3 w-3"}),m(),s(me,re)},$$slots:{default:!0}}),e(ke);var He=r(ke,2);{var We=me=>{var ge=za(),re=r(t(ge),2);qe(re,17,()=>a(p).referrals.slice(0,2),Ge,(Le,$e)=>{var T=Ma(),_=t(T),M=t(_,!0);e(_);var se=r(_,2);const xe=Xe(()=>a($e).status==="completed"?"default":"secondary");ft(se,{get variant(){return a(xe)},class:"text-xs",children:(y,g)=>{m();var N=he();ve(()=>u(N,a($e).status||"pending")),s(y,N)},$$slots:{default:!0}}),e(T),ve(()=>{var y,g;return u(M,((y=a($e).referred)==null?void 0:y.name)||((g=a($e).referred)==null?void 0:g.email)||"Unknown User")}),s(Le,T)});var Ee=r(re,2);{var Te=Le=>{var $e=Ea(),T=t($e);e($e),ve(()=>u(T,`+${a(p).referrals.length-2} more`)),s(Le,$e)};le(Ee,Le=>{a(p).referrals.length>2&&Le(Te)})}e(ge),s(me,ge)},Ne=me=>{var ge=Ha(),re=t(ge);pt(re,{class:"text-muted-foreground mx-auto mb-2 h-8 w-8"}),m(4),e(ge),s(me,ge)};le(He,me=>{a(p).referrals&&a(p).referrals.length>0?me(We):me(Ne,!1)})}ve(()=>{u(H,a(p).referralCount||0),u(_e,a(p).referralCode)}),s(P,q)},z=P=>{var q=Oa();s(P,q)};le(ae,P=>{a(p)?P(Y):P(z,!1)},ne)}};le($,ae=>{a(be)?ae(ee):ae(te,!1)})}s(Be,b)},$$slots:{default:!0}})});var De=r(ze,2);d(De,()=>ta,(ye,ue)=>{ue(ye,{class:"pt-3",children:(Be,Fe)=>{Oe(Be,{variant:"default",size:"sm",class:"w-full",href:"/dashboard/settings/referrals",children:(b,$)=>{m();var ee=he("Manage Referrals");s(b,ee)},$$slots:{default:!0}})},$$slots:{default:!0}})}),s(F,Ye)},$$slots:{default:!0}})});var Z=r(pe,2);La(Z,{onClose:()=>j(f,!1),get open(){return a(f)},set open(I){j(f,I,!0)}}),s(Ie,ie),et()}var qa=l("<!> <!>",1),Ga=l('<div class="space-y-4"><div class="space-y-2"><div class="flex items-center justify-between"><span class="text-sm font-medium">Completed</span> <span class="text-muted-foreground text-sm"> </span></div> <div class="bg-muted h-2 w-full overflow-hidden rounded-full"><div class="bg-success h-full rounded-full"></div></div></div> <div class="space-y-2"><div class="flex items-center justify-between"><span class="text-sm font-medium">Running</span> <span class="text-muted-foreground text-sm"> </span></div> <div class="bg-muted h-2 w-full overflow-hidden rounded-full"><div class="bg-primary h-full rounded-full"></div></div></div> <div class="space-y-2"><div class="flex items-center justify-between"><span class="text-sm font-medium">Pending</span> <span class="text-muted-foreground text-sm"> </span></div> <div class="bg-muted h-2 w-full overflow-hidden rounded-full"><div class="bg-warning h-full rounded-full"></div></div></div></div>'),Va=l("<!> <!>",1),Ka=l("<!> <!>",1),Xa=l('<div class="text-muted-foreground flex flex-col items-center justify-center py-6 text-center"><p>No automation runs yet</p> <p class="mt-1 text-sm">Create your first automation to get started</p></div>'),Za=l('<div class="flex items-center justify-between"><div class="space-y-1"><p class="text-sm font-medium leading-none"> </p> <p class="text-muted-foreground text-sm"><!></p></div> <div class="ml-auto"><span> </span></div></div>'),er=l('<div class="space-y-4"><!> <div class="pt-2 text-center"><a href="/dashboard/automation" class="text-primary text-sm hover:underline">View all automation runs</a></div></div>'),tr=l("<!> <!>",1),ar=l("<!> <!>",1),rr=l('<div class="flex items-center justify-between"><div class="flex items-center"><div></div> <span class="text-sm font-medium"> </span></div> <div class="flex items-center"><span class="text-muted-foreground text-sm"> </span></div></div>'),sr=l('<div class="space-y-4"><!> <div class="pt-2 text-center"><a href="/dashboard/tracker" class="text-primary text-sm hover:underline">View application tracker</a></div></div>'),or=l('<div class="text-muted-foreground flex flex-col items-center justify-center py-6 text-center"><p>No application data available</p></div>'),lr=l("<!> <!>",1),ir=l("<!> <!>",1),nr=l('<div class="space-y-2"><div class="flex items-center justify-between"><span class="text-sm font-medium"> </span> <span class="text-muted-foreground text-sm"> </span></div> <div class="bg-muted h-2 w-full overflow-hidden rounded-full"><div></div></div> <div class="text-muted-foreground text-xs"> </div></div>'),dr=l('<div class="space-y-4"><!> <div class="pt-2 text-center"><a href="/dashboard/settings/profile" class="text-primary text-sm hover:underline">Manage profiles</a></div></div>'),vr=l('<div class="text-muted-foreground flex flex-col items-center justify-center py-6 text-center"><p>No profile data available</p> <p class="mt-1 text-sm">Create your first profile to get started</p></div>'),cr=l("<!> <!>",1),ur=l('<div class="border-border grid divide-x border-b md:grid-cols-2 lg:grid-cols-4"><div class="p-4"><div class="flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="text-sm font-medium">Automation Runs</h3> <!></div> <div><div class="text-2xl font-bold"> </div> <p class="text-muted-foreground text-xs"> </p></div></div> <div class="p-4"><div class="flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="text-sm font-medium">Applications</h3> <!></div> <div><div class="text-2xl font-bold"> </div> <p class="text-muted-foreground text-xs"> </p></div></div> <div class="p-4"><div class="flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="text-sm font-medium">Jobs Found</h3> <!></div> <div><div class="text-2xl font-bold"> </div> <p class="text-muted-foreground text-xs"> </p></div></div> <div class="p-4"><div class="flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="text-sm font-medium">Subscription Usage</h3> <!></div> <div><div class="text-2xl font-bold"> </div> <p class="text-muted-foreground text-xs"> </p></div></div></div> <div class="grid gap-4 p-4 md:grid-cols-2 lg:grid-cols-7"><!> <!> <!> <!></div> <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"><div class="lg:col-span-1"><!></div></div> <!> <!> <!>',1),fr=l("<!> <!>",1);function os(Ie,o){Ze(o,!0);let p=Re(!1),be=Re(!1);lt(async()=>{if(new URLSearchParams(window.location.search).get("checkout")==="success")try{const Z=await fetch("/api/auth/refresh-session",{method:"POST",headers:{"Content-Type":"application/json"}});if(Z.ok){const I=await Z.json();console.log("Session refreshed with updated role:",I.user.role);const c=new URL(window.location.href);c.searchParams.delete("checkout"),window.history.replaceState({},"",c),window.location.reload()}}catch(Z){console.error("Failed to refresh session:",Z)}typeof localStorage<"u"&&!localStorage.getItem("onboardingCompleted")&&o.data.user&&setTimeout(()=>{j(be,!0)},1500)});var f=fr(),h=v(f);Tt(h,{title:"Dashboard | Hirli",description:"Manage your job applications, track your progress, and get insights on your job search with Hirli's intelligent dashboard.",keywords:"job dashboard, job applications, job tracking, career management, application analytics, job search progress"});var ce=r(h,2);{var J=ie=>{var pe=ur(),Z=v(pe),I=t(Z),c=t(I),F=r(t(c),2);ra(F,{class:"text-muted-foreground h-4 w-4"}),e(c);var E=r(c,2),Ye=t(E),Ae=t(Ye,!0);e(Ye);var ze=r(Ye,2),De=t(ze);e(ze),e(E),e(I);var ye=r(I,2),ue=t(ye),Be=r(t(ue),2);ut(Be,{class:"text-muted-foreground h-4 w-4"}),e(ue);var Fe=r(ue,2),b=t(Fe),$=t(b,!0);e(b);var ee=r(b,2),te=t(ee);e(ee),e(Fe),e(ye);var ae=r(ye,2),ne=t(ae),Y=r(t(ne),2);ut(Y,{class:"text-muted-foreground h-4 w-4"}),e(ne);var z=r(ne,2),P=t(z),q=t(P,!0);e(P);var A=r(P,2),B=t(A);e(A),e(z),e(ae);var G=r(ae,2),H=t(G),fe=r(t(H),2);sa(fe,{class:"text-muted-foreground h-4 w-4"}),e(H);var V=r(H,2),_e=t(V),ke=t(_e,!0);e(_e);var Me=r(_e,2),Qe=t(Me,!0);e(Me),e(V),e(G),e(Z);var He=r(Z,2),We=t(He);d(We,()=>tt,(_,M)=>{M(_,{class:"col-span-4",children:(se,xe)=>{var y=Va(),g=v(y);d(g,()=>st,(k,L)=>{L(k,{children:(U,we)=>{var w=qa(),D=v(w);d(D,()=>ot,(x,n)=>{n(x,{children:(i,de)=>{m();var C=he("Automation Status");s(i,C)},$$slots:{default:!0}})});var Q=r(D,2);d(Q,()=>rt,(x,n)=>{n(x,{children:(i,de)=>{m();var C=he("Distribution of automation runs by status");s(i,C)},$$slots:{default:!0}})}),s(U,w)},$$slots:{default:!0}})});var N=r(g,2);d(N,()=>at,(k,L)=>{L(k,{children:(U,we)=>{var w=Ga(),D=t(w),Q=t(D),x=r(t(Q),2),n=t(x);e(x),e(Q);var i=r(Q,2),de=t(i);e(i),e(D);var C=r(D,2),O=t(C),Pe=r(t(O),2),oe=t(Pe);e(Pe),e(O);var S=r(O,2),K=t(S);e(S),e(C);var R=r(C,2),W=t(R),X=r(t(W),2),Ue=t(X);e(X),e(W);var Ce=r(W,2),Se=t(Ce);e(Ce),e(R),e(w),ve(()=>{var Ve,it,nt,_t,gt,xt;u(n,`${(((Ve=o.data.automationStats)==null?void 0:Ve.completedRuns)||0)??""} runs`),dt(de,`width: ${(it=o.data.automationStats)!=null&&it.totalRuns?o.data.automationStats.completedRuns/o.data.automationStats.totalRuns*100:0}%`),u(oe,`${(((nt=o.data.automationStats)==null?void 0:nt.runningRuns)||0)??""} runs`),dt(K,`width: ${(_t=o.data.automationStats)!=null&&_t.totalRuns?o.data.automationStats.runningRuns/o.data.automationStats.totalRuns*100:0}%`),u(Ue,`${(((gt=o.data.automationStats)==null?void 0:gt.pendingRuns)||0)??""} runs`),dt(Se,`width: ${(xt=o.data.automationStats)!=null&&xt.totalRuns?o.data.automationStats.pendingRuns/o.data.automationStats.totalRuns*100:0}%`)}),s(U,w)},$$slots:{default:!0}})}),s(se,y)},$$slots:{default:!0}})});var Ne=r(We,2);d(Ne,()=>tt,(_,M)=>{M(_,{class:"col-span-3",children:(se,xe)=>{var y=tr(),g=v(y);d(g,()=>st,(k,L)=>{L(k,{children:(U,we)=>{var w=Ka(),D=v(w);d(D,()=>ot,(x,n)=>{n(x,{children:(i,de)=>{m();var C=he("Recent Automation Runs");s(i,C)},$$slots:{default:!0}})});var Q=r(D,2);d(Q,()=>rt,(x,n)=>{n(x,{children:(i,de)=>{m();var C=he("Your most recent automation runs");s(i,C)},$$slots:{default:!0}})}),s(U,w)},$$slots:{default:!0}})});var N=r(g,2);d(N,()=>at,(k,L)=>{L(k,{children:(U,we)=>{var w=je(),D=v(w);{var Q=n=>{var i=Xa();s(n,i)},x=n=>{var i=er(),de=t(i);qe(de,17,()=>o.data.recentAutomationRuns.slice(0,3),Ge,(C,O)=>{var Pe=Za(),oe=t(Pe),S=t(oe),K=t(S,!0);e(S);var R=r(S,2),W=t(R);const X=Xe(()=>a(O).keywords||"");aa(W,{get keywordIds(){return a(X)},fallback:"No keywords"}),e(R),e(oe);var Ue=r(oe,2),Ce=t(Ue),Se=t(Ce,!0);e(Ce),e(Ue),e(Pe),ve(()=>{var Ve;u(K,((Ve=a(O).profile)==null?void 0:Ve.name)||"Unknown Profile"),vt(Ce,1,`rounded-full px-2 py-1 text-xs ${a(O).status==="completed"?"bg-green-100 text-green-800":a(O).status==="running"?"bg-blue-100 text-blue-800":a(O).status==="failed"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`),u(Se,a(O).status)}),s(C,Pe)}),m(2),e(i),s(n,i)};le(D,n=>{var i;(i=o.data.recentAutomationRuns)!=null&&i.length?n(x,!1):n(Q)})}s(U,w)},$$slots:{default:!0}})}),s(se,y)},$$slots:{default:!0}})});var me=r(Ne,2);d(me,()=>tt,(_,M)=>{M(_,{class:"col-span-4",children:(se,xe)=>{var y=lr(),g=v(y);d(g,()=>st,(k,L)=>{L(k,{children:(U,we)=>{var w=ar(),D=v(w);d(D,()=>ot,(x,n)=>{n(x,{children:(i,de)=>{m();var C=he("Application Status");s(i,C)},$$slots:{default:!0}})});var Q=r(D,2);d(Q,()=>rt,(x,n)=>{n(x,{children:(i,de)=>{m();var C=he("Distribution of applications by status");s(i,C)},$$slots:{default:!0}})}),s(U,w)},$$slots:{default:!0}})});var N=r(g,2);d(N,()=>at,(k,L)=>{L(k,{children:(U,we)=>{var w=je(),D=v(w);{var Q=n=>{var i=sr(),de=t(i);qe(de,17,()=>Object.entries(o.data.applicationStats.byStatus),Ge,(C,O)=>{var Pe=Xe(()=>Dt(a(O),2));let oe=()=>a(Pe)[0],S=()=>a(Pe)[1];var K=rr(),R=t(K),W=t(R),X=r(W,2),Ue=t(X,!0);e(X),e(R);var Ce=r(R,2),Se=t(Ce),Ve=t(Se);e(Se),e(Ce),e(K),ve((it,nt)=>{vt(W,1,`mr-2 h-3 w-3 rounded-full ${oe()==="applied"?"bg-primary":oe()==="interview"?"bg-purple-500":oe()==="assessment"?"bg-warning":oe()==="offer"?"bg-success":oe()==="rejected"?"bg-destructive":"bg-muted"}`),u(Ue,it),u(Ve,`${S()??""} (${nt??""}%)`)},[()=>oe().charAt(0).toUpperCase()+oe().slice(1),()=>Math.round(S()/o.data.applicationStats.total*100)]),s(C,K)}),m(2),e(i),s(n,i)},x=n=>{var i=or();s(n,i)};le(D,n=>{var i;(i=o.data.applicationStats)!=null&&i.byStatus&&Object.keys(o.data.applicationStats.byStatus).length>0?n(Q):n(x,!1)})}s(U,w)},$$slots:{default:!0}})}),s(se,y)},$$slots:{default:!0}})});var ge=r(me,2);d(ge,()=>tt,(_,M)=>{M(_,{class:"col-span-3",children:(se,xe)=>{var y=cr(),g=v(y);d(g,()=>st,(k,L)=>{L(k,{children:(U,we)=>{var w=ir(),D=v(w);d(D,()=>ot,(x,n)=>{n(x,{children:(i,de)=>{m();var C=he("Profile Completion");s(i,C)},$$slots:{default:!0}})});var Q=r(D,2);d(Q,()=>rt,(x,n)=>{n(x,{children:(i,de)=>{m();var C=he("Completion status of your profiles");s(i,C)},$$slots:{default:!0}})}),s(U,w)},$$slots:{default:!0}})});var N=r(g,2);d(N,()=>at,(k,L)=>{L(k,{children:(U,we)=>{var w=je(),D=v(w);{var Q=n=>{var i=dr(),de=t(i);qe(de,17,()=>o.data.profileStats,Ge,(C,O)=>{var Pe=nr(),oe=t(Pe),S=t(oe),K=t(S,!0);e(S);var R=r(S,2),W=t(R);e(R),e(oe);var X=r(oe,2),Ue=t(X);e(X);var Ce=r(X,2),Se=t(Ce);e(Ce),e(Pe),ve(()=>{u(K,a(O).name),u(W,`${a(O).completionPercentage??""}%`),vt(Ue,1,`h-full rounded-full ${a(O).completionPercentage>=80?"bg-green-500":a(O).completionPercentage>=50?"bg-yellow-500":"bg-red-500"}`),dt(Ue,`width: ${a(O).completionPercentage??""}%`),u(Se,`${a(O).documentCount??""} document${a(O).documentCount!==1?"s":""}`)}),s(C,Pe)}),m(2),e(i),s(n,i)},x=n=>{var i=vr();s(n,i)};le(D,n=>{var i;(i=o.data.profileStats)!=null&&i.length?n(Q):n(x,!1)})}s(U,w)},$$slots:{default:!0}})}),s(se,y)},$$slots:{default:!0}})}),e(He);var re=r(He,2),Ee=t(re),Te=t(Ee);Qa(Te,{}),e(Ee),e(re);var Le=r(re,2);ua(Le,{get open(){return a(p)},onClose:()=>j(p,!1)});var $e=r(Le,2);fa($e,{get userData(){return o.data.user}});var T=r($e,2);ba(T,{get open(){return a(be)},onClose:()=>j(be,!1),get userData(){return o.data.user}}),ve(_=>{var M,se,xe,y,g,N,k,L,U,we;u(Ae,((M=o.data.automationStats)==null?void 0:M.totalRuns)||0),u(De,`${(((se=o.data.automationStats)==null?void 0:se.completedRuns)||0)??""} completed, ${(((xe=o.data.automationStats)==null?void 0:xe.runningRuns)||0)??""} running`),u($,((y=o.data.applicationStats)==null?void 0:y.total)||0),u(te,`${(((N=(g=o.data.applicationStats)==null?void 0:g.byStatus)==null?void 0:N.interview)||0)??""} interviews`),u(q,((k=o.data.automationStats)==null?void 0:k.totalJobsFound)||0),u(B,`${_??""}% avg progress`),u(ke,((L=o.data.usage)==null?void 0:L.used)||0),u(Qe,((U=o.data.usage)==null?void 0:U.limit)!==null?`${((we=o.data.usage)==null?void 0:we.remaining)||0} remaining`:"Unlimited")},[()=>{var _;return Math.round(((_=o.data.automationStats)==null?void 0:_.avgProgress)||0)}]),s(ie,pe)};le(ce,ie=>{ie(J,!1)})}s(Ie,f),et()}export{os as component};
