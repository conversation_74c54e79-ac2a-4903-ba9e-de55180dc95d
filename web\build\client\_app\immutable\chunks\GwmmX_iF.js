import{f as d,a as c}from"./BasJTneF.js";import{p,a as m,c as n,au as f,r as l}from"./CGmarHxI.js";import{c as h,s as u}from"./ncUU1dSD.js";import{e as v}from"./B-Xjo-Yt.js";import{b as _}from"./5V1tIHTN.js";import{p as b,r as g}from"./Btcx8l8F.js";var w=d("<div><!></div>");function q(o,a){p(a,!0);let t=b(a,"ref",15,null),e=g(a,["$$slots","$$events","$$legacy","ref","class","children"]);var r=w();v(r,s=>({"data-slot":"card-header",class:s,...e}),[()=>h("@container/card-header has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6",a.class)]);var i=n(r);u(i,()=>a.children??f),l(r),_(r,s=>t(s),()=>t()),c(o,r),m()}export{q as C};
