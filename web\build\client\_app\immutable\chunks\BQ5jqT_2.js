var m=s=>{throw TypeError(s)};var p=(s,e,t)=>e.has(s)||m("Cannot "+t);var c=(s,e,t)=>(p(s,e,"read from private field"),t?t.call(s):e.get(s)),i=(s,e,t)=>e.has(s)?m("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(s):e.set(s,t),l=(s,e,t,a)=>(p(s,e,"write to private field"),a?a.call(s,t):e.set(s,t),t);import{o as d}from"./CmxjS0TN.js";import{c as u}from"./BfX7a-t9.js";var r,n;class b{constructor(e,t){i(this,r);i(this,n);l(this,r,e),l(this,n,u(t))}get current(){return c(this,n).call(this),c(this,r).call(this)}}r=new WeakMap,n=new WeakMap;const f=/\(.+\)/,w=new Set(["all","print","screen","and","or","not","only"]);class M extends b{constructor(e,t){let a=f.test(e)||e.split(/[\s,]+/).some(o=>w.has(o.trim()))?e:`(${e})`;const h=window.matchMedia(a);super(()=>h.matches,o=>d(h,"change",o))}}export{M};
