import{f as d,a as t,t as F,c as L}from"./BasJTneF.js";import{p as _r,c as a,r,s as o,t as U,a as yr,e as pr,n as _,l as oa,g as e,m as Pr,d as s,h as Rr,b as sa,f as v,k as E,v as at,a_ as la,x as Ye,i as Ir}from"./CGmarHxI.js";import{s as B}from"./CIt1g2O9.js";import{i as S}from"./u21ee2wt.js";import{e as $t,i as At}from"./C3w0v0gR.js";import{c as i}from"./BvdI7LR8.js";import{p as re}from"./Btcx8l8F.js";import{I as Hr}from"./DMTMHyMa.js";import{S as zt,a as Ht,R as Wt,b as Qt}from"./CGK0g3x_.js";import{M as ur}from"./Ci8yIwIB.js";import{S as Or}from"./CfcZq63z.js";import{b as gr}from"./Cf6rS4LV.js";import{g as Mr,C as na,a as ia}from"./9r-6KH_O.js";import{d as da}from"./ncUU1dSD.js";import{B as jt}from"./B1K98fMG.js";import{P as ca,S as va,a as ua,b as pa,c as fa,d as ma,R as ga}from"./CTn0v-X8.js";import{P as Br,D as Ar,a as jr,R as Tr}from"./tdzGgazS.js";import{S as fr}from"./D9yI7a4E.js";import"./CgXBgsce.js";import{t as nr}from"./DjPYYl4Z.js";import{B as Yr,S as ha,F as _a}from"./eW6QhNR3.js";import{S as Gt}from"./B2lQHLf_.js";import{S as hr}from"./CVVv9lPb.js";import{S as ya}from"./KVutzy_p.js";import{D as Lr,a as Er,b as Dr,c as Wr}from"./CKh8VGVX.js";import{s as ba,a as mr,b as xa,d as $a}from"./B-Xjo-Yt.js";import{e as Tt}from"./CmxjS0TN.js";import{b as Qr}from"./VYoCKyli.js";import{i as Gr}from"./BIEMS98f.js";import{B as qr}from"./DaBofrVv.js";import{B as Nr}from"./CIPPbbaT.js";import{M as wa}from"./CwgkX8t9.js";import{D as Sa}from"./6BxQgNmX.js";import{B as Pa}from"./CDnvByek.js";import{C as Kr}from"./-SpbofVw.js";import{h as Ca}from"./DYwWIJ9y.js";import{S as ka}from"./BAawoUIy.js";import{F as Aa,C as Fr,B as zr}from"./BM9SsHQg.js";import{S as Cr}from"./FAbXdqfL.js";import{S as ja}from"./C2MdR6K0.js";import{L as kr}from"./BhzFx1Wy.js";var Ta=d('<img class="h-full w-full object-cover"/>'),La=d('<div class="bg-muted text-muted-foreground flex h-full w-full items-center justify-center"> </div>'),Ea=d('<div class="text-muted-foreground flex items-center text-sm"><!> <span class="truncate"> </span> <!></div>'),Da=d('<div class="text-muted-foreground flex items-center text-sm"><!> <span> </span></div>'),Na=d('<div class="text-muted-foreground flex items-center text-sm"><!> <span> </span></div>'),Ua=d('<div class="text-muted-foreground mt-1 flex items-center text-xs"><!> <span> </span></div>'),Va=d('<div class="flex items-center"><span class="text-muted-foreground text-xs"> </span> <div class="bg-muted ml-2 h-1.5 w-16 rounded-full"><div class="bg-primary h-1.5 rounded-full"></div></div></div>'),Ja=d('<div tabindex="0" role="button"><div class="flex items-start gap-4"><div class="border-border h-12 w-12 flex-shrink-0 overflow-hidden rounded border"><!></div> <div class="flex flex-1 flex-col gap-1 overflow-hidden"><div class="flex items-start justify-between"><h3 class="text-foreground truncate text-base font-medium"> </h3> <!></div> <div class="text-foreground/80 text-sm"><span class="truncate"> </span></div> <!> <!> <!> <!></div></div> <div class="mt-2 flex items-center justify-between"><!> <!></div></div>');function Ra(Kt,x){_r(x,!1);let c=re(x,"job",8),Ee=re(x,"onClick",8,()=>{});const V=()=>{};let It=re(x,"isSelected",8,!1),ot=re(x,"isSaved",8,!1);Gr();var Pe=Ja(),ne=a(Pe),ve=a(ne),De=a(ve);{var wt=h=>{var $=Ta();U(()=>{mr($,"src",c().companyLogo),mr($,"alt",c().company)}),t(h,$)},Lt=h=>{var $=La(),C=a($,!0);r($),U(A=>B(C,A),[()=>{var A;return((A=c().company)==null?void 0:A.charAt(0))||"J"}],pr),t(h,$)};S(De,h=>{c().companyLogo?h(wt):h(Lt,!1)})}r(ve);var Ve=o(ve,2),ae=a(Ve),Z=a(ae),W=a(Z,!0);r(Z);var J=o(Z,2);{var T=h=>{Nr(h,{class:"fill-primary text-primary h-5 w-5"})};S(J,h=>{ot()&&h(T)})}r(ae);var Y=o(ae,2),P=a(Y),z=a(P,!0);r(P),r(Y);var Q=o(Y,2);{var oe=h=>{var $=Ea(),C=a($);wa(C,{class:"mr-1 h-4 w-4"});var A=o(C,2),pe=a(A,!0);r(A);var k=o(A,2);{var g=O=>{qr(O,{variant:"outline",class:"ml-2 text-xs",children:(fe,Ce)=>{_();var Fe=F();U(()=>B(Fe,c().workplaceType==="remote"?"Remote":c().workplaceType==="hybrid"?"Hybrid":c().workplaceType==="onsite"?"On-site":c().workplaceType)),t(fe,Fe)},$$slots:{default:!0}})};S(k,O=>{c().workplaceType&&O(g)})}r($),U(()=>B(pe,c().location)),t(h,$)};S(Q,h=>{c().location&&h(oe)})}var ct=o(Q,2);{var St=h=>{var $=Da(),C=a($);Sa(C,{class:"mr-1 h-4 w-4"});var A=o(C,2),pe=a(A,!0);r(A),r($),U(()=>B(pe,c().salary)),t(h,$)};S(ct,h=>{c().salary&&h(St)})}var vt=o(ct,2);{var qe=h=>{var $=Na(),C=a($);Pa(C,{class:"mr-1 h-4 w-4"});var A=o(C,2),pe=a(A,!0);r(A),r($),U(()=>B(pe,c().employmentType)),t(h,$)};S(vt,h=>{c().employmentType&&h(qe)})}var ft=o(vt,2);{var Ot=h=>{var $=Ua(),C=a($);Kr(C,{class:"mr-1 h-3.5 w-3.5"});var A=o(C,2),pe=a(A);r(A),r($),U(k=>B(pe,`Posted ${k??""}`),[()=>new Date(c().postedDate).toLocaleDateString()],pr),t(h,$)};S(ft,h=>{c().postedDate&&h(Ot)})}r(Ve),r(ne);var ue=o(ne,2),st=a(ue);{var mt=h=>{qr(h,{variant:"secondary",class:"text-xs",children:($,C)=>{_();var A=F("Easy Apply");t($,A)},$$slots:{default:!0}})};S(st,h=>{c().easyApply&&h(mt)})}var Et=o(st,2);{var Dt=h=>{var $=Va(),C=a($),A=a(C);r(C);var pe=o(C,2),k=a(pe);r(pe),r($),U((g,O)=>{B(A,`Match Score: ${g??""}%`),xa(k,`width: ${O??""}%`)},[()=>Math.round(c().matchScore*100),()=>Math.round(c().matchScore*100)],pr),t(h,$)};S(Et,h=>{c().matchScore&&h(Dt)})}return r(ue),r(Pe),U(()=>{ba(Pe,1,`relative cursor-pointer border-b p-4 ${It()?"border-l-primary bg-primary/5 border-l-2":"hover:bg-muted/50"}`),B(W,c().title),B(z,c().company)}),Tt("click",Pe,()=>Ee()(c())),Tt("keydown",Pe,h=>{h.key==="Enter"&&Ee()(c())}),t(Kt,Pe),Qr(x,"onClose",V),yr({onClose:V})}var Ia=d('<img class="h-full w-full object-cover"/>'),Oa=d('<div class="bg-muted text-muted-foreground flex h-full w-full items-center justify-center"> </div>'),Ma=d('<span class="bg-muted ml-1 rounded-full px-2 py-0.5 text-xs"> </span>'),Ba=d('<div class="text-muted-foreground mt-1 flex items-center text-sm"><span> </span> <!></div>'),Ya=d('<div class="text-muted-foreground mt-1 flex items-center text-sm"><!> <span> </span></div>'),qa=d("<!> View Application",1),Fa=d("<!> Apply Now",1),za=d("<!> Saved",1),Ha=d("<!> Saving...",1),Wa=d("<!> Save",1),Qa=d('<button class="bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium focus-visible:outline-none focus-visible:ring-1" type="button"><!></button> <button class="border-input bg-background text-foreground hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex items-center justify-center rounded-md border px-4 py-2 text-sm font-medium focus-visible:outline-none focus-visible:ring-1" type="button"><!></button>',1),Ga=d('<button class="bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium focus-visible:outline-none focus-visible:ring-1" type="button">Log in to Apply</button>'),Ka=d('<div class="flex items-start"><div class="text-muted-foreground mr-2 mt-0.5"><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg></div> <div><div class="font-medium">Salary</div> <div class="text-muted-foreground text-sm"> </div></div></div>'),Xa=d('<div class="flex items-start"><div class="text-muted-foreground mr-2 mt-0.5"><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path></svg></div> <div><div class="font-medium">Job Type</div> <div class="text-muted-foreground text-sm"> </div></div></div>'),Za=d('<div><h3 class="mb-3 font-medium">About the job</h3> <div class="prose prose-sm prose-headings:text-foreground prose-p:text-foreground/90 max-w-none"><!></div></div>'),eo=d("<li> </li>"),to=d('<div><h3 class="mb-3 font-medium">Requirements</h3> <ul class="text-foreground/80 list-disc space-y-1 pl-5"></ul></div>'),ro=d("<li> </li>"),ao=d('<div><h3 class="mb-3 font-medium">Benefits</h3> <ul class="text-foreground/80 list-disc space-y-1 pl-5"></ul></div>'),oo=d("<!> View Application",1),so=d("<!> Apply Now",1),lo=d("<!> Saved",1),no=d("<!> Saving...",1),io=d("<!> Save",1),co=d('<div class="flex flex-wrap gap-2"><button class="bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex flex-1 items-center justify-center rounded-md px-4 py-2 text-sm font-medium focus-visible:outline-none focus-visible:ring-1" type="button"><!></button> <button class="border-input bg-background text-foreground hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex items-center justify-center rounded-md border px-4 py-2 text-sm font-medium focus-visible:outline-none focus-visible:ring-1" type="button"><!></button></div>'),vo=d('<button class="bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex w-full items-center justify-center rounded-md px-4 py-2 text-sm font-medium focus-visible:outline-none focus-visible:ring-1" type="button"><!> Log in to Apply</button>'),uo=d('<div class="relative"><div class="border-border border-b p-5"><div class="flex items-start"><div class="border-border mr-4 h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border"><!></div> <div class="flex-1"><a class="hover:underline"><h2 class="text-primary text-xl font-semibold"> </h2></a> <div class="mt-1 flex items-center"><span class="text-foreground font-medium"> </span></div> <!> <!></div> <div class="flex space-x-2"><button type="button" class="text-muted-foreground hover:bg-muted hover:text-foreground h-8 w-8 rounded-full p-0"><!> <span class="sr-only">Share</span></button> <button type="button" class="text-muted-foreground hover:bg-muted hover:text-foreground h-8 w-8 rounded-full p-0"><!> <span class="sr-only">Report</span></button></div></div> <div class="mt-4 flex flex-wrap gap-2"><!></div></div> <div class="space-y-6 p-5"><div class="bg-muted rounded-lg p-4"><h3 class="mb-3 font-medium">Job details</h3> <div class="grid grid-cols-1 gap-3 md:grid-cols-2"><!> <!></div></div> <!> <!> <!> <div class="border-border border-t pt-4"><!></div></div></div>'),po=d('<div class="flex h-full items-center justify-center"><p class="text-muted-foreground text-center">Select a job to view details</p></div>'),fo=d('<div class="h-full overflow-y-auto"><!></div>');function mo(Kt,x){_r(x,!1);let c=re(x,"job",8),Ee=re(x,"isAuthenticated",8,!1),V=re(x,"onApply",8,()=>{}),It=re(x,"onSave",8,()=>{}),ot=re(x,"onSignInRequired",8,()=>{});const Pe=()=>{};let ne=re(x,"isApplied",12,!1),ve=Pr(!1),De=Pr(!1),wt=Pr("");async function Lt(){var T;if(!(!Ee()||!((T=c())!=null&&T.id)))try{const Y=await fetch(`/api/jobs/${c().id}/is-saved`);if(Y.ok){const P=await Y.json();s(ve,P.isSaved)}}catch(Y){console.error("Error checking if job is saved:",Y)}}async function Ve(){if(!Ee()){ot()("save");return}if(!(e(ve)||e(De)||ne())){s(De,!0);try{const T=await fetch(`/api/jobs/${c().id}/save`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({notes:""})});if(!T.ok){const Y=await T.json();throw Y.isApplied?(ne(!0),new Error("Cannot save job that is already applied to")):new Error(Y.error||"Failed to save job")}It()&&It()(c()),s(ve,!0),s(wt,c().id)}catch(T){console.error("Error saving job:",T)}finally{s(De,!1)}}}oa(()=>(Rr(c()),Rr(Ee()),e(wt)),()=>{var T;(T=c())!=null&&T.id&&Ee()&&c().id!==e(wt)&&(s(wt,c().id),Lt())}),sa(),Gr();var ae=fo(),Z=a(ae);{var W=T=>{var Y=uo(),P=a(Y),z=a(P),Q=a(z),oe=a(Q);{var ct=f=>{var p=Ia();U(()=>{mr(p,"src",c().companyLogo),mr(p,"alt",c().company)}),t(f,p)},St=f=>{var p=Oa(),y=a(p,!0);r(p),U(R=>B(y,R),[()=>{var R;return((R=c().company)==null?void 0:R.charAt(0))||"J"}],pr),t(f,p)};S(oe,f=>{c().companyLogo?f(ct):f(St,!1)})}r(Q);var vt=o(Q,2),qe=a(vt),ft=a(qe),Ot=a(ft,!0);r(ft),r(qe);var ue=o(qe,2),st=a(ue),mt=a(st,!0);r(st),r(ue);var Et=o(ue,2);{var Dt=f=>{var p=Ba(),y=a(p),R=a(y,!0);r(y);var Se=o(y,2);{var M=ie=>{var He=Ma(),Ut=a(He,!0);r(He),U(()=>B(Ut,c().workplaceType==="remote"?"Remote":c().workplaceType==="hybrid"?"Hybrid":c().workplaceType==="onsite"?"On-site":c().workplaceType)),t(ie,He)};S(Se,ie=>{c().workplaceType&&ie(M)})}r(p),U(()=>B(R,c().location)),t(f,p)};S(Et,f=>{c().location&&f(Dt)})}var h=o(Et,2);{var $=f=>{var p=Ya(),y=a(p);Kr(y,{class:"mr-1 h-4 w-4"});var R=o(y,2),Se=a(R);r(R),r(p),U(M=>B(Se,`Posted ${M??""}`),[()=>new Date(c().postedDate).toLocaleDateString()],pr),t(f,p)};S(h,f=>{c().postedDate&&f($)})}r(vt);var C=o(vt,2),A=a(C),pe=a(A);ka(pe,{class:"m-auto h-5 w-5"}),_(2),r(A);var k=o(A,2),g=a(k);Aa(g,{class:"m-auto h-5 w-5"}),_(2),r(k),r(C),r(z);var O=o(z,2),fe=a(O);{var Ce=f=>{var p=Qa(),y=v(p),R=a(y);{var Se=j=>{var G=qa(),le=v(G);Fr(le,{class:"mr-2 h-4 w-4"}),_(),t(j,G)},M=j=>{var G=Fa(),le=v(G);Cr(le,{class:"mr-2 h-4 w-4"}),_(),t(j,G)};S(R,j=>{ne()?j(Se):j(M,!1)})}r(y);var ie=o(y,2),He=a(ie);{var Ut=j=>{var G=za(),le=v(G);zr(le,{class:"text-primary mr-2 h-4 w-4"}),_(),t(j,G)},it=(j,G)=>{{var le=ge=>{var be=Ha(),Xe=v(be);Yr(Xe,{class:"mr-2 h-4 w-4 animate-pulse"}),_(),t(ge,be)},Ke=ge=>{var be=Wa(),Xe=v(be);Nr(Xe,{class:"mr-2 h-4 w-4"}),_(),t(ge,be)};S(j,ge=>{e(De)?ge(le):ge(Ke,!1)},G)}};S(He,j=>{e(ve)?j(Ut):j(it,!1)})}r(ie),U(()=>ie.disabled=e(ve)||e(De)||ne()),Tt("click",y,()=>V()(c())),Tt("click",ie,Ve),t(f,p)},Fe=f=>{var p=Ga();Tt("click",p,()=>ot()("apply")),t(f,p)};S(fe,f=>{Ee()?f(Ce):f(Fe,!1)})}r(O),r(P);var lt=o(P,2),ze=a(lt),nt=o(a(ze),2),gt=a(nt);{var me=f=>{var p=Ka(),y=o(a(p),2),R=o(a(y),2),Se=a(R,!0);r(R),r(y),r(p),U(()=>B(Se,c().salary)),t(f,p)};S(gt,f=>{c().salary&&f(me)})}var Ge=o(gt,2);{var ee=f=>{var p=Xa(),y=o(a(p),2),R=o(a(y),2),Se=a(R,!0);r(R),r(y),r(p),U(()=>B(Se,c().employmentType)),t(f,p)};S(Ge,f=>{c().employmentType&&f(ee)})}r(nt),r(ze);var Pt=o(ze,2);{var Nt=f=>{var p=Za(),y=o(a(p),2),R=a(y);Ca(R,()=>c().description),r(y),r(p),t(f,p)};S(Pt,f=>{c().description&&f(Nt)})}var Mt=o(Pt,2);{var $e=f=>{var p=to(),y=o(a(p),2);$t(y,5,()=>c().requirements,At,(R,Se)=>{var M=eo(),ie=a(M,!0);r(M),U(()=>B(ie,e(Se))),t(R,M)}),r(y),r(p),t(f,p)};S(Mt,f=>{c().requirements&&c().requirements.length>0&&f($e)})}var we=o(Mt,2);{var Je=f=>{var p=ao(),y=o(a(p),2);$t(y,5,()=>c().benefits,At,(R,Se)=>{var M=ro(),ie=a(M,!0);r(M),U(()=>B(ie,e(Se))),t(R,M)}),r(y),r(p),t(f,p)};S(we,f=>{c().benefits&&c().benefits.length>0&&f(Je)})}var Re=o(we,2),Ct=a(Re);{var Bt=f=>{var p=co(),y=a(p),R=a(y);{var Se=j=>{var G=oo(),le=v(G);Fr(le,{class:"mr-2 h-4 w-4"}),_(),t(j,G)},M=j=>{var G=so(),le=v(G);Cr(le,{class:"mr-2 h-4 w-4"}),_(),t(j,G)};S(R,j=>{ne()?j(Se):j(M,!1)})}r(y);var ie=o(y,2),He=a(ie);{var Ut=j=>{var G=lo(),le=v(G);zr(le,{class:"text-primary mr-2 h-4 w-4"}),_(),t(j,G)},it=(j,G)=>{{var le=ge=>{var be=no(),Xe=v(be);Yr(Xe,{class:"mr-2 h-4 w-4 animate-pulse"}),_(),t(ge,be)},Ke=ge=>{var be=io(),Xe=v(be);Nr(Xe,{class:"mr-2 h-4 w-4"}),_(),t(ge,be)};S(j,ge=>{e(De)?ge(le):ge(Ke,!1)},G)}};S(He,j=>{e(ve)?j(Ut):j(it,!1)})}r(ie),r(p),U(()=>ie.disabled=e(ve)||e(De)||ne()),Tt("click",y,()=>V()(c())),Tt("click",ie,Ve),t(f,p)},lr=f=>{var p=vo(),y=a(p);Cr(y,{class:"mr-2 h-4 w-4"}),_(),r(p),Tt("click",p,()=>ot()("apply")),t(f,p)};S(Ct,f=>{Ee()?f(Bt):f(lr,!1)})}r(Re),r(lt),r(Y),U(()=>{mr(qe,"href",`/dashboard/jobs/${c().id??""}`),B(Ot,c().title),B(mt,c().company)}),Tt("click",A,()=>{navigator.clipboard&&navigator.clipboard.writeText(window.location.href)}),Tt("click",k,()=>{}),t(T,Y)},J=T=>{var Y=po();t(T,Y)};S(Z,T=>{c()?T(W):T(J,!1)})}return r(ae),t(Kt,ae),Qr(x,"onClose",Pe),yr({onClose:Pe})}var go=d("<!> <!>",1),ho=d("<!> <!>",1),_o=d("<!> <!>",1),yo=d("<!> <!>",1),bo=d('<span class="bg-primary text-primary-foreground ml-2 rounded-full border px-2 py-0.5 text-xs"> </span>'),xo=d("<!> All Filters <!>",1),$o=d("<!> <!>",1),wo=d("<!> <!>",1),So=d("<!> <!>",1),Po=d("<!> <!>",1),Co=d("<!> <!>",1),ko=d("<!> <!>",1),Ao=d('<!> <div class="grid gap-4 py-4"><div class="grid grid-cols-4 items-center gap-4"><label for="job-title-filter" class="text-right">Job Title</label> <div class="col-span-3"><!></div></div> <div class="grid grid-cols-4 items-center gap-4"><label for="locations-filter" class="text-right">Locations</label> <div class="col-span-3"><!></div></div> <div class="grid grid-cols-4 items-center gap-4"><label for="date-posted" class="text-right">Date Posted</label> <!></div> <div class="grid grid-cols-4 items-center gap-4"><label for="companies" class="text-right">Companies</label> <div class="col-span-3"><!></div></div> <div class="grid grid-cols-4 items-center gap-4"><label for="easy-apply-sheet" class="text-right">Easy Apply Only</label> <div class="col-span-3 flex items-center"><!></div></div> <div class="grid grid-cols-4 items-center gap-4"><label for="job-type" class="text-right">Job Type</label> <!></div> <div class="grid grid-cols-4 items-center gap-4"><label for="experience-level" class="text-right">Experience Level</label> <!></div> <div class="grid grid-cols-4 items-center gap-4"><label for="salary-range" class="text-right">Salary Range</label> <!></div> <div class="grid grid-cols-4 items-center gap-4"><label for="companies-filter" class="text-right">Companies</label> <div class="col-span-3"><!></div></div> <div class="grid grid-cols-4 items-center gap-4"><label for="easy-apply-toggle" class="text-right">Easy Apply</label> <div class="col-span-3"><!></div></div></div> <!>',1),jo=d("<!> <!>",1),To=d("<!> <!>",1),Lo=d("<!> <!>",1),Eo=d('<!> <div class="grid gap-4 py-4"><div class="grid grid-cols-4 items-center gap-4"><label for="search-name" class="text-right">Search Name</label> <!></div> <div class="grid grid-cols-4 items-center gap-4"><label for="notifications" class="text-right">Email Notifications</label> <div class="col-span-3 flex items-center space-x-2"><!> <span class="text-muted-foreground text-sm">Receive daily emails with new job matches</span></div></div></div> <!>',1),Do=d("<!> <!>",1),No=d("<!> <!>",1),Uo=d('<!> <div class="flex justify-end gap-4"><!> <!></div>',1),Vo=d("<!> <!>",1),Jo=d('<div class="m-0 flex flex-row gap-2 border-b"><div class="relative m-0 w-full border-r md:w-1/4"><!></div> <div class="flex w-full flex-row py-2 pr-6 md:flex-row md:space-x-2 md:space-y-0"><div class="relative w-full sm:w-auto"><!></div> <div class="relative w-full sm:w-auto"><!></div> <div class="relative w-full sm:w-auto"><!></div> <div class="relative w-full sm:w-auto"><!></div> <div class="relative w-full sm:w-auto"><!></div> <div class="relative w-full sm:w-auto"><!></div> <!> <div class="relative ml-auto w-full sm:w-auto"><!></div></div></div> <!> <!> <!>',1);function Rs(Kt,x){_r(x,!0);let c=re(x,"isSearching",3,!1),Ee=re(x,"user",3,null),V=re(x,"initialParams",19,()=>({}));const It=Ye(()=>!!Ee()),ot=Ye(c);let Pe=E(!1),ne=E(!1),ve=E(""),De=E(!1),wt=!1,Lt=E(!1),Ve=E(""),ae=E(at(V().title||"")),Z=E(at(Array.isArray(V().locations)?V().locations:V().locations?V().locations.split(","):[])),W=E(at(Array.isArray(V().locationType)?V().locationType:V().locationType?V().locationType.split(","):[])),J=E(at(Array.isArray(V().experience)?V().experience:V().experience?V().experience.split(","):[])),T=at(V().category||[]),Y=at(V().education||[]),P=E(at(V().salary||"")),z=E(at(V().datePosted||"")),Q=E(at(Array.isArray(V().companies)?V().companies:V().companies?V().companies.split(","):[])),oe=E(at(V().easyApply==="true"||!1)),ct=E(null),St=E(0);const vt=10*60*1e3;let qe=E(null),ft=E(0);const Ot=10*60*1e3;let ue=E(at([])),st=E(at([])),mt=E(!1);const Et=["Remote","Hybrid","Onsite"],Dt=["Internship","Entry Level","Junior","Mid Level","Senior","Executive"],h=[{value:"any",label:"Any Time"},{value:"today",label:"Today"},{value:"week",label:"Past Week"},{value:"month",label:"Past Month"},{value:"3months",label:"Past 3 Months"}],$=[{value:"",label:"Any Salary"},{value:"0-50000",label:"$0 - $50,000"},{value:"50000-75000",label:"$50,000 - $75,000"},{value:"75000-100000",label:"$75,000 - $100,000"},{value:"100000-150000",label:"$100,000 - $150,000"},{value:"150000+",label:"$150,000+"}];let C=E(at([])),A=E(at([])),pe=E(!1),k=E(""),g=E(!1);function O(){const l=new URL(window.location.href),u=l.searchParams.get("title");u&&s(ae,u,!0);const n=l.searchParams.get("locations");n&&s(Z,n.split(",").filter(Boolean),!0);const w=l.searchParams.get("locationType");w&&s(W,w.split(",").filter(Boolean),!0);const m=l.searchParams.get("experience");m&&s(J,m.split(",").filter(Boolean),!0);const b=l.searchParams.get("salary");b&&s(P,b,!0);const I=l.searchParams.get("datePosted");I&&s(z,I,!0);const H=l.searchParams.get("companies");H&&s(Q,H.split(",").filter(Boolean),!0);const te=l.searchParams.get("easyApply");te&&s(oe,te==="true")}let fe=!1;function Ce(){if(fe||e(g)){console.log("Component already initializing or initialized, skipping duplicate initialization");return}fe=!0,console.log("Initializing JobSearch component"),O(),console.log("Initializing cities cache:",e(ct)),console.log("Initializing companies cache:",e(qe));try{ze()}catch(l){console.error("Error fetching cities during initialization:",l)}try{nt()}catch(l){console.error("Error fetching companies during initialization:",l)}setTimeout(()=>{(e(ae)||e(Z).length>0||e(W).length>0||e(J).length>0||e(P))&&ee(),s(g,!0),fe=!1,console.log("JobSearch component initialization complete")},300)}Ce(),window.addEventListener("urlparamsupdated",gt),la(()=>()=>{window.removeEventListener("urlparamsupdated",gt),s(g,!1)});function Fe(l){switch(l){case"0-50000":return"$0 - $50,000";case"50000-75000":return"$50,000 - $75,000";case"75000-100000":return"$75,000 - $100,000";case"100000-150000":return"$100,000 - $150,000";case"150000+":return"$150,000+";default:return l}}function lt(){s(ae,""),s(Z,[],!0),s(W,[],!0),s(J,[],!0),s(P,""),s(z,""),s(Q,[],!0),s(k,""),s(oe,!1);{const l=new URL(window.location.href),u=new URLSearchParams;for(const[w,m]of new URLSearchParams(l.search).entries())["title","locations","locationType","experience","salary","datePosted","companies","easyApply"].includes(w)||u.set(w,m);const n=`${l.pathname}?${u.toString()}`;window.history.replaceState({},"",n)}x.onSearch({title:e(ae),locations:e(Z),locationType:e(W),experience:e(J),category:T,education:Y,salary:e(P),datePosted:e(z),companies:[],easyApply:e(oe)})}async function ze(l=""){var u;try{if(e(mt)){console.log("Cities already loading, skipping duplicate request");return}const n=Date.now();if(!l&&e(ct)&&n-e(St)<vt){console.log("Using cached cities data"),s(ue,e(ct),!0),s(st,[...e(ue)],!0);return}}catch(n){console.error("Error in fetchCities initial checks:",n)}s(mt,!0),console.log("Fetching cities with search:",l);try{const w=((u=(await Mr(na,{search:l,country:"US",limit:100})).data)==null?void 0:u.locations)||[];if(w&&w.length>0?(s(ue,w.map(m=>{const b=typeof m.state=="object"?m.state.code:m.stateCode||"";return{value:`${m.id}|${m.name}|${b}|${m.country||"US"}`,label:`${m.name}, ${b}`}}),!0),e(ue).length===0&&s(ue,[{value:"test1|New York|NY|US",label:"New York, NY"},{value:"test2|Los Angeles|CA|US",label:"Los Angeles, CA"},{value:"test3|Chicago|IL|US",label:"Chicago, IL"}],!0)):s(ue,[{value:"test1|New York|NY|US",label:"New York, NY"},{value:"test2|Los Angeles|CA|US",label:"Los Angeles, CA"},{value:"test3|Chicago|IL|US",label:"Chicago, IL"}],!0),s(st,[...e(ue)],!0),l||(s(ct,e(ue),!0),s(St,Date.now(),!0),console.log("Cached cities data")),e(Z).length>0&&e(ue).length>0&&!e(g)){const m=e(ue).filter(b=>e(Z).some(I=>I.includes("|")?b.value===I:b.value.startsWith(I)||b.label.includes(I))).map(b=>b.value);m.length>0&&s(Z,m,!0)}}catch(n){console.error("Error fetching cities:",n),s(ue,[{value:"test1|New York|NY|US",label:"New York, NY"},{value:"test2|Los Angeles|CA|US",label:"Los Angeles, CA"},{value:"test3|Chicago|IL|US",label:"Chicago, IL"}],!0),s(st,[...e(ue)],!0)}finally{s(mt,!1)}}async function nt(l=""){var u;try{if(e(pe)){console.log("Companies already loading, skipping duplicate request");return}const n=Date.now();if(!l&&e(qe)&&n-e(ft)<Ot){console.log("Using cached companies data"),s(C,e(qe),!0),s(A,[...e(C)],!0);return}}catch(n){console.error("Error in fetchCompanies initial checks:",n)}s(pe,!0),console.log("Fetching companies with search:",l);try{const w=((u=(await Mr(ia,{search:l,limit:100})).data)==null?void 0:u.companies)||[];if(w&&w.length>0?(s(C,w.map(m=>({value:m.id,label:m.name})),!0),e(C).length===0&&s(C,[{value:"test1",label:"Test Company 1"},{value:"test2",label:"Test Company 2"},{value:"test3",label:"Test Company 3"}],!0)):s(C,[{value:"test1",label:"Test Company 1"},{value:"test2",label:"Test Company 2"},{value:"test3",label:"Test Company 3"}],!0),s(A,[...e(C)],!0),l||(s(qe,e(C),!0),s(ft,Date.now(),!0),console.log("Cached companies data")),e(Q).length>0&&e(C).length>0&&!e(g)){const m=e(C).filter(b=>e(Q).some(I=>b.value===I||b.label.includes(I))).map(b=>b.value);m.length>0&&s(Q,m,!0)}}catch(n){console.error("Error fetching companies:",n),s(C,[{value:"test1",label:"Test Company 1"},{value:"test2",label:"Test Company 2"},{value:"test3",label:"Test Company 3"}],!0),s(A,[...e(C)],!0)}finally{s(pe,!1)}}function gt(l){if(e(g)&&l.detail&&l.detail.params){const u=l.detail.params;if(u.title!==void 0&&s(ae,u.title||"",!0),u.locations)try{const n=u.locations.split(",").filter(Boolean);n.length>0&&s(Z,n,!0)}catch(n){console.error("Error parsing locations from URL event:",n)}if(u.locationType)try{const n=u.locationType.split(",").filter(Boolean);n.length>0&&s(W,n,!0)}catch(n){console.error("Error parsing locationType from URL event:",n)}if(u.experience)try{const n=u.experience.split(",").filter(Boolean);n.length>0&&s(J,n,!0)}catch(n){console.error("Error parsing experience from URL event:",n)}if(u.salary!==void 0&&s(P,u.salary||"",!0),u.datePosted!==void 0&&s(z,u.datePosted||"",!0),u.companies)try{const n=u.companies.split(",").filter(Boolean);n.length>0&&s(Q,n,!0)}catch(n){console.error("Error parsing companies from URL event:",n)}u.easyApply!==void 0&&s(oe,u.easyApply==="true")}}function me(l,u){if(console.log(`updateUrlParam called with name: ${l}, value:`,u),(l==="locations"||l==="companies")&&document.querySelector(`[data-param-name="${l}"]`)){console.log(`JobSearch: Skipping URL param update for ${l} - handled by multi-combobox`);return}const n={title:e(ae),locations:e(Z),locationType:e(W),experience:e(J),category:T,education:Y,salary:e(P),datePosted:e(z),companies:e(Q),easyApply:e(oe)};n[l]=u,console.log(`JobSearch: Calling onSearch with updated params after ${l} change:`,n);try{const w=new URL(window.location.href);console.log("Current URL:",w.toString());const m=new URLSearchParams(w.search);if(console.log("Current URL params:",Object.fromEntries(m.entries())),Array.isArray(u))if(console.log(`Value is array with length ${u.length}`),u.length>0){const I=u.join(",");console.log(`Setting param ${l} to joined value: ${I}`),m.set(l,I)}else console.log(`Deleting param ${l} because array is empty`),m.delete(l);else u?(console.log(`Setting param ${l} to string value: ${u}`),m.set(l,u)):(console.log(`Deleting param ${l} because value is empty`),m.delete(l));const b=`${w.pathname}?${m.toString()}`;console.log("New URL will be:",b),window.history.pushState({},"",b),console.log("URL updated successfully"),console.log("Dispatching urlparamsupdated event"),window.dispatchEvent(new CustomEvent("urlparamsupdated",{detail:{params:Object.fromEntries(m.entries())}}))}catch(w){console.error("Error updating URL:",w)}console.log("Calling onSearch with searchParams:",n),x.onSearch(n)}let Ge=E(0);function ee(){if(!(e(ae)||e(Z).length>0||e(W).length>0||e(J).length>0||e(P)||e(z)||e(k)||e(oe))&&!e(g))return;const u=e(Q).length>0?e(Q):e(k)?[e(k)]:[],n={title:e(ae),locations:e(Z),locationType:e(W),experience:e(J),category:T,education:Y,salary:e(P),datePosted:e(z),companies:u,easyApply:e(oe),saveSearch:wt};Pt(n)}const Pt=da(l=>{const u=Date.now();u-e(Ge)<500||(s(Ge,u,!0),x.onSearch(l))},500);function Nt(l){return e(It)?!0:(window.location.href="/auth/sign-in",!1)}async function Mt(){if(!Nt())return;if(!e(ve).trim()){nr.error("Please enter a name for your search");return}const l={name:e(ve).trim(),filters:{title:e(ae),locations:e(Z),locationType:e(W),experience:e(J),category:T,education:Y,salary:e(P),datePosted:e(z),companies:e(k)?[e(k)]:[],easyApply:e(oe)},notifications:e(De),createdAt:new Date().toISOString()};console.log("Saving search:",l);try{await new Promise(u=>setTimeout(u,500)),s(ne,!1),s(ve,""),s(De,!1),nr.success("Your search has been saved")}catch{nr.error("Issue saving your search")}}var $e=Jo(),we=v($e),Je=a(we),Re=a(Je);Or(Re,{placeholder:"Job title (e.g. Software Engineer)",className:"border-none shadow-none drop-shadow-none h-13 rounded-none rounded-tl-lg",paramName:"title",get disabled(){return e(ot)},autofocus:!0,onSearch:l=>{e(g)&&(l.length>2||l.length===0)&&ee()},get value(){return e(ae)},set value(l){s(ae,l,!0)}}),r(Je);var Ct=o(Je,2),Bt=a(Ct),lr=a(Bt);const f=Ye(()=>e(mt)?"Loading...":"No locations found");ur(lr,{get options(){return e(st)},placeholder:"Select locations",searchPlaceholder:"Search locations...",get emptyMessage(){return e(f)},width:"w-full",get disabled(){return e(ot)},paramName:"locations",onSelectedValuesChange:l=>{console.log("Location values changed:",l),e(g)&&ee()},get selectedValues(){return e(Z)},set selectedValues(l){s(Z,l,!0)}}),r(Bt);var p=o(Bt,2),y=a(p);i(y,()=>Wt,(l,u)=>{u(l,{type:"multiple",get value(){return e(W)},onValueChange:n=>{if(n)try{s(W,n,!0),gr&&e(g)&&(me("locationType",e(W)),ee())}catch(w){console.error("Error in onValueChange:",w)}},children:(n,w)=>{var m=go(),b=v(m);i(b,()=>zt,(H,te)=>{te(H,{class:"border-md px-4 py-2 font-light",children:(ke,Ie)=>{var D=L(),he=v(D);const K=Ye(()=>e(W).length>0?e(W).length===1?e(W)[0]:`${e(W).length} work types selected`:"Work Type");i(he,()=>Gt,(N,se)=>{se(N,{get placeholder(){return e(K)},onSelect:()=>{console.log("Work Type Select.Value clicked")}})}),t(ke,D)},$$slots:{default:!0}})});var I=o(b,2);i(I,()=>Ht,(H,te)=>{te(H,{class:"!w-[150px] rounded-none",align:"start",sideOffset:3,children:(ke,Ie)=>{var D=L(),he=v(D);i(he,()=>hr,(K,N)=>{N(K,{children:(se,Yt)=>{var Ae=L(),Oe=v(Ae);$t(Oe,17,()=>Et,At,(je,Te)=>{var Le=L(),Ze=v(Le);i(Ze,()=>Qt,(et,tt)=>{tt(et,{get value(){return e(Te)},class:"capitalize",children:(qt,rt)=>{_();var Me=F();U(()=>B(Me,e(Te))),t(qt,Me)},$$slots:{default:!0}})}),t(je,Le)}),t(se,Ae)},$$slots:{default:!0}})}),t(ke,D)},$$slots:{default:!0}})}),t(n,m)},$$slots:{default:!0}})}),r(p);var R=o(p,2),Se=a(R);i(Se,()=>Wt,(l,u)=>{u(l,{type:"multiple",get value(){return e(J)},onValueChange:n=>{if(n)try{s(J,n,!0),gr&&e(g)&&(me("experience",e(J)),ee())}catch(w){console.error("Error in onValueChange:",w)}},children:(n,w)=>{var m=ho(),b=v(m);i(b,()=>zt,(H,te)=>{te(H,{class:"border-md px-4 py-2 font-light",children:(ke,Ie)=>{var D=L(),he=v(D);const K=Ye(()=>e(J).length>0?e(J).length===1?e(J)[0]:`${e(J).length} experience levels selected`:"Experience");i(he,()=>Gt,(N,se)=>{se(N,{get placeholder(){return e(K)},onSelect:()=>{console.log("Experience Select.Value clicked")}})}),t(ke,D)},$$slots:{default:!0}})});var I=o(b,2);i(I,()=>Ht,(H,te)=>{te(H,{class:"!w-[150px] rounded-none",align:"start",sideOffset:3,children:(ke,Ie)=>{var D=L(),he=v(D);i(he,()=>hr,(K,N)=>{N(K,{children:(se,Yt)=>{var Ae=L(),Oe=v(Ae);$t(Oe,17,()=>Dt,At,(je,Te)=>{var Le=L(),Ze=v(Le);i(Ze,()=>Qt,(et,tt)=>{tt(et,{get value(){return e(Te)},class:"capitalize",children:(qt,rt)=>{_();var Me=F();U(()=>B(Me,e(Te))),t(qt,Me)},$$slots:{default:!0}})}),t(je,Le)}),t(se,Ae)},$$slots:{default:!0}})}),t(ke,D)},$$slots:{default:!0}})}),t(n,m)},$$slots:{default:!0}})}),r(R);var M=o(R,2),ie=a(M);i(ie,()=>Wt,(l,u)=>{u(l,{type:"single",get value(){return e(P)},onValueChange:n=>{try{s(P,n||"",!0),gr&&e(g)&&(me("salary",e(P)),ee())}catch(w){console.error("Error in onValueChange:",w)}},children:(n,w)=>{var m=_o(),b=v(m);i(b,()=>zt,(H,te)=>{te(H,{class:"border-md px-4 py-2 font-light",children:(ke,Ie)=>{var D=L(),he=v(D);const K=Ye(()=>e(P)?Fe(e(P)):"Salary");i(he,()=>Gt,(N,se)=>{se(N,{get placeholder(){return e(K)}})}),t(ke,D)},$$slots:{default:!0}})});var I=o(b,2);i(I,()=>Ht,(H,te)=>{te(H,{class:"!w-[200px] rounded-none",align:"start",sideOffset:3,children:(ke,Ie)=>{var D=L(),he=v(D);$t(he,17,()=>$,At,(K,N)=>{var se=L(),Yt=v(se);i(Yt,()=>Qt,(Ae,Oe)=>{Oe(Ae,{get value(){return e(N).value},children:(je,Te)=>{_();var Le=F();U(()=>B(Le,e(N).label)),t(je,Le)},$$slots:{default:!0}})}),t(K,se)}),t(ke,D)},$$slots:{default:!0}})}),t(n,m)},$$slots:{default:!0}})}),r(M);var He=o(M,2),Ut=a(He);i(Ut,()=>Wt,(l,u)=>{u(l,{type:"single",get value(){return e(z)},onValueChange:n=>{try{s(z,n||"",!0),gr&&e(g)&&(me("datePosted",e(z)),ee())}catch(w){console.error("Error in onValueChange:",w)}},children:(n,w)=>{var m=yo(),b=v(m);i(b,()=>zt,(H,te)=>{te(H,{class:"border-md px-4 py-2 font-light",children:(ke,Ie)=>{var D=L(),he=v(D);const K=Ye(()=>{var N;return e(z)&&((N=h.find(se=>se.value===e(z)))==null?void 0:N.label)||"Date Posted"});i(he,()=>Gt,(N,se)=>{se(N,{get placeholder(){return e(K)}})}),t(ke,D)},$$slots:{default:!0}})});var I=o(b,2);i(I,()=>Ht,(H,te)=>{te(H,{class:"!w-[150px] rounded-none",align:"start",sideOffset:3,children:(ke,Ie)=>{var D=L(),he=v(D);$t(he,17,()=>h,At,(K,N)=>{var se=L(),Yt=v(se);i(Yt,()=>Qt,(Ae,Oe)=>{Oe(Ae,{get value(){return e(N).value},children:(je,Te)=>{_();var Le=F();U(()=>B(Le,e(N).label)),t(je,Le)},$$slots:{default:!0}})}),t(K,se)}),t(ke,D)},$$slots:{default:!0}})}),t(n,m)},$$slots:{default:!0}})}),r(He);var it=o(He,2),j=a(it);const G=Ye(()=>e(pe)?"Loading...":"No companies found");ur(j,{get options(){return e(A)},placeholder:"Select companies",searchPlaceholder:"Search companies...",get emptyMessage(){return e(G)},width:"w-[250px]",get disabled(){return e(ot)},paramName:"companies",onSelectedValuesChange:l=>{console.log("Companies changed:",l),e(g)&&(console.log("Calling handleSearch with companies:",e(Q)),ee())},get selectedValues(){return e(Q)},set selectedValues(l){s(Q,l,!0)}}),r(it);var le=o(it,2);jt(le,{class:"border-md border p-4",id:"easy-apply",onclick:()=>{s(oe,!e(oe)),e(g)&&(me("easyApply",e(oe)?"true":""),ee())},get disabled(){return e(ot)},children:(l,u)=>{_();var n=F("Easy Apply");t(l,n)},$$slots:{default:!0}});var Ke=o(le,2),ge=a(Ke);jt(ge,{variant:"outline",class:"border-md px-4 py-2 font-light",get disabled(){return e(ot)},onclick:()=>s(Pe,!0),children:(l,u)=>{var n=xo(),w=v(n);ha(w,{class:"mr-2 h-4 w-4"});var m=o(w,2);{var b=I=>{var H=bo(),te=a(H,!0);r(H),U(()=>B(te,(e(ae)?1:0)+(e(Z).length>0?1:0)+(e(W).length>0?1:0)+(e(J).length>0?1:0)+(e(P)?1:0)+(e(z)?1:0)+(e(Q).length>0?1:0)+(e(oe)?1:0))),t(I,H)};S(m,I=>{(e(ae)||e(Z).length>0||e(W).length>0||e(J).length>0||e(P)||e(z)||e(Q).length>0||e(oe))&&I(b)})}t(l,n)},$$slots:{default:!0}}),r(Ke),r(Ct),r(we);var be=o(we,2);i(be,()=>ga,(l,u)=>{u(l,{get open(){return e(Pe)},set open(n){s(Pe,n,!0)},children:(n,w)=>{var m=L(),b=v(m);i(b,()=>ca,(I,H)=>{H(I,{children:(te,ke)=>{var Ie=jo(),D=v(Ie);i(D,()=>va,(K,N)=>{N(K,{})});var he=o(D,2);i(he,()=>ua,(K,N)=>{N(K,{side:"right",class:"w-full sm:max-w-lg",children:(se,Yt)=>{var Ae=Ao(),Oe=v(Ae);i(Oe,()=>pa,(q,yt)=>{yt(q,{children:(_e,dr)=>{var Ne=$o(),Ue=v(Ne);i(Ue,()=>fa,(de,ye)=>{ye(de,{children:(ce,Jt)=>{_();var X=F("All Filters");t(ce,X)},$$slots:{default:!0}})});var bt=o(Ue,2);i(bt,()=>ma,(de,ye)=>{ye(de,{children:(ce,Jt)=>{_();var X=F("Apply additional filters to refine your job search.");t(ce,X)},$$slots:{default:!0}})}),t(_e,Ne)},$$slots:{default:!0}})});var je=o(Oe,2),Te=a(je),Le=o(a(Te),2),Ze=a(Le);Or(Ze,{placeholder:"Enter job title",paramName:"title",onSearch:()=>{},get value(){return e(ae)},set value(q){s(ae,q,!0)}}),r(Le),r(Te);var et=o(Te,2),tt=o(a(et),2),qt=a(tt);const rt=Ye(()=>e(mt)?"Loading...":"No locations found");ur(qt,{get options(){return e(st)},placeholder:"Select locations",searchPlaceholder:"Search locations...",get emptyMessage(){return e(rt)},width:"w-full",paramName:"locations",onSelectedValuesChange:q=>{console.log("Locations changed in filter sheet:",q)},get selectedValues(){return e(Z)},set selectedValues(q){s(Z,q,!0)}}),r(tt),r(et);var Me=o(et,2),Xt=o(a(Me),2);i(Xt,()=>Wt,(q,yt)=>{yt(q,{type:"single",get value(){return e(z)},onValueChange:_e=>{s(z,_e||"",!0),e(g)&&(me("datePosted",e(z)),ee())},children:(_e,dr)=>{var Ne=wo(),Ue=v(Ne);i(Ue,()=>zt,(de,ye)=>{ye(de,{id:"date-posted",class:"col-span-3",children:(ce,Jt)=>{var X=L(),We=v(X);const Qe=Ye(()=>{var xe;return e(z)&&((xe=h.find(dt=>dt.value===e(z)))==null?void 0:xe.label)||"Select date range"});i(We,()=>Gt,(xe,dt)=>{dt(xe,{get placeholder(){return e(Qe)}})}),t(ce,X)},$$slots:{default:!0}})});var bt=o(Ue,2);i(bt,()=>Ht,(de,ye)=>{ye(de,{children:(ce,Jt)=>{var X=L(),We=v(X);$t(We,17,()=>h,At,(Qe,xe)=>{var dt=L(),cr=v(dt);i(cr,()=>Qt,(Rt,ar)=>{ar(Rt,{get value(){return e(xe).value},children:(or,sr)=>{_();var xt=F();U(()=>B(xt,e(xe).label)),t(or,xt)},$$slots:{default:!0}})}),t(Qe,dt)}),t(ce,X)},$$slots:{default:!0}})}),t(_e,Ne)},$$slots:{default:!0}})}),r(Me);var Vt=o(Me,2),ut=o(a(Vt),2),ht=a(ut);const Zt=Ye(()=>e(pe)?"Loading...":"No companies found");ur(ht,{get options(){return e(A)},placeholder:"Select companies",searchPlaceholder:"Search companies...",get emptyMessage(){return e(Zt)},width:"w-full",paramName:"companies",onSelectedValuesChange:q=>{console.log("Companies changed in filter sheet:",q)},get selectedValues(){return e(Q)},set selectedValues(q){s(Q,q,!0)}}),r(ut),r(Vt);var Be=o(Vt,2),kt=o(a(Be),2),_t=a(kt);fr(_t,{id:"easy-apply-sheet",get checked(){return e(oe)},onCheckedChange:q=>{s(oe,q,!0),e(g)&&(me("easyApply",e(oe)?"true":""),ee())}}),r(kt),r(Be);var er=o(Be,2),pt=o(a(er),2);i(pt,()=>Wt,(q,yt)=>{yt(q,{type:"multiple",get value(){return e(W)},onValueChange:_e=>{_e&&(s(W,_e,!0),e(g)&&(me("locationType",e(W)),ee()))},children:(_e,dr)=>{var Ne=So(),Ue=v(Ne);i(Ue,()=>zt,(de,ye)=>{ye(de,{id:"job-type",class:"col-span-3",children:(ce,Jt)=>{var X=L(),We=v(X);i(We,()=>Gt,(Qe,xe)=>{xe(Qe,{placeholder:"Select job types"})}),t(ce,X)},$$slots:{default:!0}})});var bt=o(Ue,2);i(bt,()=>Ht,(de,ye)=>{ye(de,{children:(ce,Jt)=>{var X=L(),We=v(X);i(We,()=>hr,(Qe,xe)=>{xe(Qe,{children:(dt,cr)=>{var Rt=L(),ar=v(Rt);$t(ar,17,()=>Et,At,(or,sr)=>{var xt=L(),xr=v(xt);i(xr,()=>Qt,($r,wr)=>{wr($r,{get value(){return e(sr)},class:"capitalize",children:(Sr,aa)=>{_();var vr=F();U(()=>B(vr,e(sr))),t(Sr,vr)},$$slots:{default:!0}})}),t(or,xt)}),t(dt,Rt)},$$slots:{default:!0}})}),t(ce,X)},$$slots:{default:!0}})}),t(_e,Ne)},$$slots:{default:!0}})}),r(er);var tr=o(er,2),br=o(a(tr),2);i(br,()=>Wt,(q,yt)=>{yt(q,{type:"multiple",get value(){return e(J)},onValueChange:_e=>{_e&&(s(J,_e,!0),e(g)&&(me("experience",e(J)),ee()))},children:(_e,dr)=>{var Ne=Po(),Ue=v(Ne);i(Ue,()=>zt,(de,ye)=>{ye(de,{id:"experience-level",class:"col-span-3",children:(ce,Jt)=>{var X=L(),We=v(X);i(We,()=>Gt,(Qe,xe)=>{xe(Qe,{placeholder:"Select experience levels"})}),t(ce,X)},$$slots:{default:!0}})});var bt=o(Ue,2);i(bt,()=>Ht,(de,ye)=>{ye(de,{children:(ce,Jt)=>{var X=L(),We=v(X);i(We,()=>hr,(Qe,xe)=>{xe(Qe,{children:(dt,cr)=>{var Rt=L(),ar=v(Rt);$t(ar,17,()=>Dt,At,(or,sr)=>{var xt=L(),xr=v(xt);i(xr,()=>Qt,($r,wr)=>{wr($r,{get value(){return e(sr)},class:"capitalize",children:(Sr,aa)=>{_();var vr=F();U(()=>B(vr,e(sr))),t(Sr,vr)},$$slots:{default:!0}})}),t(or,xt)}),t(dt,Rt)},$$slots:{default:!0}})}),t(ce,X)},$$slots:{default:!0}})}),t(_e,Ne)},$$slots:{default:!0}})}),r(tr);var ir=o(tr,2),Ft=o(a(ir),2);i(Ft,()=>Wt,(q,yt)=>{yt(q,{type:"single",get value(){return e(P)},onValueChange:_e=>{s(P,_e||"",!0),e(g)&&(me("salary",e(P)),ee())},children:(_e,dr)=>{var Ne=Co(),Ue=v(Ne);i(Ue,()=>zt,(de,ye)=>{ye(de,{id:"salary-range",class:"col-span-3",children:(ce,Jt)=>{var X=L(),We=v(X);i(We,()=>Gt,(Qe,xe)=>{xe(Qe,{placeholder:"Select salary range"})}),t(ce,X)},$$slots:{default:!0}})});var bt=o(Ue,2);i(bt,()=>Ht,(de,ye)=>{ye(de,{children:(ce,Jt)=>{var X=L(),We=v(X);$t(We,17,()=>$,At,(Qe,xe)=>{var dt=L(),cr=v(dt);i(cr,()=>Qt,(Rt,ar)=>{ar(Rt,{get value(){return e(xe).value},children:(or,sr)=>{_();var xt=F();U(()=>B(xt,e(xe).label)),t(or,xt)},$$slots:{default:!0}})}),t(Qe,dt)}),t(ce,X)},$$slots:{default:!0}})}),t(_e,Ne)},$$slots:{default:!0}})}),r(ir);var rr=o(ir,2),Ur=o(a(rr),2),Zr=a(Ur);const ea=Ye(()=>e(pe)?"Loading...":"No companies found");ur(Zr,{get options(){return e(A)},placeholder:"Select companies",searchPlaceholder:"Search companies...",get emptyMessage(){return e(ea)},width:"w-full",paramName:"companies",onSelectedValuesChange:q=>{console.log("Companies changed in filter sheet:",q)},get selectedValues(){return e(Q)},set selectedValues(q){s(Q,q,!0)}}),r(Ur),r(rr);var Vr=o(rr,2),Jr=o(a(Vr),2),ta=a(Jr);fr(ta,{id:"easy-apply-toggle",get checked(){return e(oe)},onCheckedChange:q=>{s(oe,q,!0),e(g)&&(me("easyApply",e(oe)?"true":""),ee())}}),r(Jr),r(Vr),r(je);var ra=o(je,2);i(ra,()=>ya,(q,yt)=>{yt(q,{class:"flex justify-between",children:(_e,dr)=>{var Ne=ko(),Ue=v(Ne);jt(Ue,{variant:"outline",onclick:()=>{lt(),s(Pe,!1)},children:(de,ye)=>{_();var ce=F("Clear Filters");t(de,ce)},$$slots:{default:!0}});var bt=o(Ue,2);jt(bt,{variant:"default",onclick:()=>{s(Pe,!1),e(g)&&ee()},children:(de,ye)=>{_();var ce=F("Apply Filters");t(de,ce)},$$slots:{default:!0}}),t(_e,Ne)},$$slots:{default:!0}})}),t(se,Ae)},$$slots:{default:!0}})}),t(te,Ie)},$$slots:{default:!0}})}),t(n,m)},$$slots:{default:!0}})});var Xe=o(be,2);i(Xe,()=>Tr,(l,u)=>{u(l,{get open(){return e(ne)},set open(n){s(ne,n,!0)},children:(n,w)=>{var m=L(),b=v(m);i(b,()=>Br,(I,H)=>{H(I,{children:(te,ke)=>{var Ie=Do(),D=v(Ie);i(D,()=>Ar,(K,N)=>{N(K,{})});var he=o(D,2);i(he,()=>jr,(K,N)=>{N(K,{class:"sm:max-w-md",children:(se,Yt)=>{var Ae=Eo(),Oe=v(Ae);i(Oe,()=>Lr,(rt,Me)=>{Me(rt,{children:(Xt,Vt)=>{var ut=To(),ht=v(ut);i(ht,()=>Er,(Be,kt)=>{kt(Be,{children:(_t,er)=>{_();var pt=F("Save Search");t(_t,pt)},$$slots:{default:!0}})});var Zt=o(ht,2);i(Zt,()=>Dr,(Be,kt)=>{kt(Be,{children:(_t,er)=>{_();var pt=F("Save your current search criteria to quickly access it later.");t(_t,pt)},$$slots:{default:!0}})}),t(Xt,ut)},$$slots:{default:!0}})});var je=o(Oe,2),Te=a(je),Le=o(a(Te),2);Hr(Le,{id:"search-name",placeholder:"e.g., Software Engineer in NYC",class:"col-span-3",get value(){return e(ve)},set value(rt){s(ve,rt,!0)}}),r(Te);var Ze=o(Te,2),et=o(a(Ze),2),tt=a(et);fr(tt,{id:"notifications",get checked(){return e(De)},onCheckedChange:rt=>{s(De,rt,!0)}}),_(2),r(et),r(Ze),r(je);var qt=o(je,2);i(qt,()=>Wr,(rt,Me)=>{Me(rt,{class:"sm:justify-between",children:(Xt,Vt)=>{var ut=Lo(),ht=v(ut);jt(ht,{variant:"outline",onclick:()=>{s(ne,!1),s(ve,""),s(De,!1)},children:(Be,kt)=>{_();var _t=F("Cancel");t(Be,_t)},$$slots:{default:!0}});var Zt=o(ht,2);jt(Zt,{variant:"default",onclick:Mt,children:(Be,kt)=>{_();var _t=F("Save");t(Be,_t)},$$slots:{default:!0}}),t(Xt,ut)},$$slots:{default:!0}})}),t(se,Ae)},$$slots:{default:!0}})}),t(te,Ie)},$$slots:{default:!0}})}),t(n,m)},$$slots:{default:!0}})});var Xr=o(Xe,2);i(Xr,()=>Tr,(l,u)=>{u(l,{get open(){return e(Lt)},set open(n){s(Lt,n,!0)},children:(n,w)=>{var m=L(),b=v(m);i(b,()=>Br,(I,H)=>{H(I,{children:(te,ke)=>{var Ie=Vo(),D=v(Ie);i(D,()=>Ar,(K,N)=>{N(K,{})});var he=o(D,2);i(he,()=>jr,(K,N)=>{N(K,{class:"sm:max-w-md",children:(se,Yt)=>{var Ae=Uo(),Oe=v(Ae);i(Oe,()=>Lr,(Ze,et)=>{et(Ze,{children:(tt,qt)=>{var rt=No(),Me=v(rt);i(Me,()=>Er,(Vt,ut)=>{ut(Vt,{children:(ht,Zt)=>{_();var Be=F("Sign in required");t(ht,Be)},$$slots:{default:!0}})});var Xt=o(Me,2);i(Xt,()=>Dr,(Vt,ut)=>{ut(Vt,{children:(ht,Zt)=>{var Be=L(),kt=v(Be);{var _t=pt=>{var tr=F("You need to sign in to save job searches and receive alerts.");t(pt,tr)},er=(pt,tr)=>{{var br=Ft=>{var rr=F("You need to sign in to apply for jobs and track your applications.");t(Ft,rr)},ir=Ft=>{var rr=F("You need to sign in to access this feature.");t(Ft,rr)};S(pt,Ft=>{e(Ve)==="apply"?Ft(br):Ft(ir,!1)},tr)}};S(kt,pt=>{e(Ve)==="save"?pt(_t):pt(er,!1)})}t(ht,Be)},$$slots:{default:!0}})}),t(tt,rt)},$$slots:{default:!0}})});var je=o(Oe,2),Te=a(je);jt(Te,{variant:"outline",onclick:()=>{s(Lt,!1)},children:(Ze,et)=>{_();var tt=F("Cancel");t(Ze,tt)},$$slots:{default:!0}});var Le=o(Te,2);jt(Le,{variant:"default",onclick:()=>{window.location.href="/auth/sign-in"},children:(Ze,et)=>{_();var tt=F("Sign In");t(Ze,tt)},$$slots:{default:!0}}),r(je),t(se,Ae)},$$slots:{default:!0}})}),t(te,Ie)},$$slots:{default:!0}})}),t(n,m)},$$slots:{default:!0}})}),t(Kt,$e),yr()}var Ro=d('<div class="flex items-center gap-2"><span class="text-sm text-gray-600">Job Alert</span> <div class="flex items-center gap-1"><!></div></div>'),Io=d('<div class="flex h-40 items-center justify-center"><div class="text-center"><!> <p class="text-muted-foreground text-sm">Loading jobs...</p></div></div>'),Oo=d('<div class="flex h-40 flex-col items-center justify-center p-6 text-center"><!> <h3 class="mb-2 text-lg font-medium">No jobs found</h3> <p class="text-muted-foreground text-sm">Try adjusting your search criteria</p></div>'),Mo=d('<div class="flex justify-center py-4"><div class="text-center"><!> <p class="text-muted-foreground text-xs">Loading more jobs...</p></div></div>'),Bo=d('<div class="py-4 text-center"><div class="bg-muted text-muted-foreground inline-block rounded-full px-3 py-1 text-xs">End of results</div></div>'),Yo=d("<!> <!>",1),qo=d('<div class="flex h-full items-center justify-center p-10"><div class="text-center"><!> <p class="text-muted-foreground">Loading job details...</p></div></div>'),Fo=d('<div class="flex h-full items-center justify-center"><p class="text-muted-foreground text-center">Select a job to view details</p></div>'),zo=d("<!> <!>",1),Ho=d("<!> <!>",1),Wo=d('<!> <div class="grid gap-4 py-4"><div class="grid grid-cols-4 items-center gap-4"><label for="alert-name" class="text-right">Alert Name</label> <!></div> <div class="grid grid-cols-4 items-center gap-4"><label for="alert-frequency" class="text-right">Frequency</label> <div class="col-span-3"><select id="alert-frequency" class="border-input bg-background w-full rounded-md border px-3 py-2"><option>Daily</option><option>Weekly</option><option>Instant</option></select></div></div> <div class="grid grid-cols-4 items-center gap-4"><label for="alert-enabled" class="text-right">Enabled</label> <div class="col-span-3 flex items-center space-x-2"><!> <span class="text-muted-foreground text-sm"> </span></div></div></div> <!>',1),Qo=d("<!> <!>",1),Go=d('<div class="grid h-[calc(100vh-113px)] grid-cols-1 lg:grid-cols-3"><div class="border-border border-r lg:col-span-1"><div class="border-border flex items-center justify-between border-b px-4 py-3"><div><h3 class="text-base font-medium">Job Listings</h3> <p class="text-sm text-gray-500"> </p></div> <!></div> <!></div> <div class="lg:col-span-2"><div class="h-full overflow-hidden"><!></div></div></div> <!>',1);function Is(Kt,x){_r(x,!0);const c=re(x,"isAuthenticated",3,!1),Ee=re(x,"jobs",19,()=>[]),V=re(x,"isLoading",3,!1);re(x,"onLoadMore",3,async()=>[]);const It=re(x,"onApply",3,()=>{}),ot=re(x,"onSave",3,()=>{}),Pe=re(x,"onSignInRequired",3,()=>{}),ne=re(x,"selectedJob",3,null),ve=re(x,"onSelectJob",3,()=>{}),De=re(x,"totalJobCount",3,0),wt=re(x,"savedJobs",19,()=>[]),Lt=re(x,"appliedJobs",19,()=>[]),Ve=re(x,"searchParams",19,()=>({})),ae=()=>{};let Z=E(!0),W=E(!1),J=E(!1),T=E(""),Y=E("daily"),P=E(!0);function z(k){ve()(k)}function Q(){ve()(null)}async function oe(){if(!e(T).trim()){nr.error("Please enter a name for your alert");return}const k={name:e(T).trim(),searchParams:Ve(),frequency:e(Y),enabled:e(P)};try{const g=await fetch("/api/job-alerts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(k)}),O=await g.json();if(!g.ok)throw new Error(O.error||"Failed to save job alert");s(J,!1),s(T,""),s(Y,"daily"),s(P,!0);const fe=k.enabled?"Your job alert has been saved and is active":"Your job alert has been saved but notifications are disabled";nr.success(fe)}catch(g){console.error("Error saving job alert:",g),nr.error(g.message||"Failed to save job alert")}}Ir(()=>{Ee().length>0&&!ne()&&ve()(Ee()[0])}),Ir(()=>{});var ct=Go(),St=v(ct),vt=a(St),qe=a(vt),ft=a(qe),Ot=o(a(ft),2),ue=a(Ot);r(Ot),r(ft);var st=o(ft,2);{var mt=k=>{var g=Ro(),O=o(a(g),2),fe=a(O);fr(fe,{checked:!1,onCheckedChange:Ce=>{Ce&&(c()?(s(T,""),s(Y,"daily"),s(P,!0),s(J,!0)):Pe()("alert"))}}),r(O),r(g),t(k,g)};S(st,k=>{var g,O,fe;Ve()&&(Ve().title||((g=Ve().locations)==null?void 0:g.length)>0||((O=Ve().locationType)==null?void 0:O.length)>0||((fe=Ve().experience)==null?void 0:fe.length)>0||Ve().salary)&&k(mt)})}r(qe);var Et=o(qe,2);ja(Et,{orientation:"vertical",class:"h-[calc(100vh-190px)] w-auto",children:(k,g)=>{var O=L(),fe=v(O);{var Ce=lt=>{var ze=Io(),nt=a(ze),gt=a(nt);kr(gt,{class:"text-primary mx-auto mb-3 h-8 w-8 animate-spin"}),_(2),r(nt),r(ze),t(lt,ze)},Fe=(lt,ze)=>{{var nt=me=>{var Ge=Oo(),ee=a(Ge);_a(ee,{class:"text-muted mb-6 h-24 w-24"}),_(4),r(Ge),t(me,Ge)},gt=me=>{var Ge=Yo(),ee=v(Ge);$t(ee,17,Ee,$e=>$e.id,($e,we)=>{const Je=Ye(()=>ne()&&ne().id===e(we).id),Re=Ye(()=>wt().includes(e(we).id));Ra($e,{get job(){return e(we)},onClick:z,get isSelected(){return e(Je)},get isSaved(){return e(Re)}})});var Pt=o(ee,2);{var Nt=$e=>{var we=Mo(),Je=a(we),Re=a(Je);kr(Re,{class:"text-primary mx-auto mb-2 h-6 w-6 animate-spin"}),_(2),r(Je),r(we),t($e,we)},Mt=($e,we)=>{{var Je=Re=>{var Ct=Bo();t(Re,Ct)};S($e,Re=>{e(Z)||Re(Je)},we)}};S(Pt,$e=>{e(W)?$e(Nt):$e(Mt,!1)})}t(me,Ge)};S(lt,me=>{Ee().length===0?me(nt):me(gt,!1)},ze)}};S(fe,lt=>{V()&&Ee().length===0?lt(Ce):lt(Fe,!1)})}t(k,O)},$$slots:{default:!0}}),r(vt);var Dt=o(vt,2),h=a(Dt),$=a(h);{var C=k=>{var g=qo(),O=a(g),fe=a(O);kr(fe,{class:"text-primary mx-auto mb-3 h-8 w-8 animate-spin"}),_(2),r(O),r(g),t(k,g)},A=(k,g)=>{{var O=Ce=>{const Fe=Ye(()=>Lt().includes(ne().id));mo(Ce,{get job(){return ne()},get isAuthenticated(){return c()},onApply:It(),onSave:ot(),onSignInRequired:Pe(),onClose:Q,get isApplied(){return e(Fe)}})},fe=Ce=>{var Fe=Fo();t(Ce,Fe)};S(k,Ce=>{ne()?Ce(O):Ce(fe,!1)},g)}};S($,k=>{V()&&!ne()?k(C):k(A,!1)})}r(h),r(Dt),r(St);var pe=o(St,2);return i(pe,()=>Tr,(k,g)=>{g(k,{get open(){return e(J)},set open(O){s(J,O,!0)},children:(O,fe)=>{var Ce=Qo(),Fe=v(Ce);i(Fe,()=>Ar,(ze,nt)=>{nt(ze,{})});var lt=o(Fe,2);i(lt,()=>jr,(ze,nt)=>{nt(ze,{class:"sm:max-w-md",children:(gt,me)=>{var Ge=Wo(),ee=v(Ge);i(ee,()=>Lr,(M,ie)=>{ie(M,{children:(He,Ut)=>{var it=zo(),j=v(it);i(j,()=>Er,(le,Ke)=>{Ke(le,{children:(ge,be)=>{_();var Xe=F("Set Job Alert");t(ge,Xe)},$$slots:{default:!0}})});var G=o(j,2);i(G,()=>Dr,(le,Ke)=>{Ke(le,{children:(ge,be)=>{_();var Xe=F("Get notified when new jobs matching your search criteria are posted.");t(ge,Xe)},$$slots:{default:!0}})}),t(He,it)},$$slots:{default:!0}})});var Pt=o(ee,2),Nt=a(Pt),Mt=o(a(Nt),2);Hr(Mt,{id:"alert-name",placeholder:"e.g., Software Engineer in NYC",class:"col-span-3",get value(){return e(T)},set value(M){s(T,M,!0)}}),r(Nt);var $e=o(Nt,2),we=o(a($e),2),Je=a(we),Re=a(Je);Re.value=Re.__value="daily";var Ct=o(Re);Ct.value=Ct.__value="weekly";var Bt=o(Ct);Bt.value=Bt.__value="instant",r(Je),r(we),r($e);var lr=o($e,2),f=o(a(lr),2),p=a(f);fr(p,{id:"alert-enabled",get checked(){return e(P)},onCheckedChange:M=>{console.log("Alert enabled changed to:",M),s(P,M,!0)}});var y=o(p,2),R=a(y,!0);r(y),r(f),r(lr),r(Pt);var Se=o(Pt,2);i(Se,()=>Wr,(M,ie)=>{ie(M,{class:"sm:justify-between",children:(He,Ut)=>{var it=Ho(),j=v(it);jt(j,{variant:"outline",onclick:()=>{console.log("Canceling job alert creation"),s(J,!1),s(T,""),s(Y,"daily"),s(P,!0)},children:(Ke,ge)=>{_();var be=F("Cancel");t(Ke,be)},$$slots:{default:!0}});var G=o(j,2);const le=Ye(()=>!e(T).trim());jt(G,{variant:"default",onclick:oe,get disabled(){return e(le)},children:(Ke,ge)=>{_();var be=F("Save Alert");t(Ke,be)},$$slots:{default:!0}}),t(He,it)},$$slots:{default:!0}})}),U(()=>B(R,e(P)?"You will receive emails with new job matches":"Email notifications are disabled")),$a(Je,()=>e(Y),M=>s(Y,M)),t(gt,Ge)},$$slots:{default:!0}})}),t(O,Ce)},$$slots:{default:!0}})}),U(()=>B(ue,`${De()??""} jobs found`)),t(Kt,ct),yr({onFilterChange:ae})}export{Rs as J,Is as a};
