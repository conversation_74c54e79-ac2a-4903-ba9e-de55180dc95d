import{f,a as p}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as U}from"../chunks/nZgk9enP.js";import{p as j,f as x,a as C,s as I,c as d,d as t,m as _,g as u,r as m,n as O,t as N}from"../chunks/CGmarHxI.js";import{s as T}from"../chunks/CIt1g2O9.js";import{i as L}from"../chunks/u21ee2wt.js";import{i as q}from"../chunks/BIEMS98f.js";import{g as z}from"../chunks/BiJhC7W5.js";import{t as D}from"../chunks/DjPYYl4Z.js";import{S as E}from"../chunks/C6g8ubaU.js";var J=f('<div class="flex flex-col items-center justify-center space-y-4"><div class="border-primary h-8 w-8 animate-spin rounded-full border-2 border-t-transparent"></div> <p class="text-muted-foreground text-sm">Completing your authentication...</p></div>'),M=f('<div class="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800"><p> </p></div> <a href="/auth/sign-up" class="text-sm text-blue-600 hover:underline">Return to sign up</a>',1),Y=f('<!> <div class="flex min-h-[calc(100vh-4rem)] flex-col items-center justify-center"><div class="mx-auto w-full max-w-md space-y-6 p-6"><div class="flex flex-col space-y-2 text-center"><h1 class="text-2xl font-semibold tracking-tight">LinkedIn Authentication</h1> <!></div></div></div>',1);function $(w,y){j(y,!1);let n=_(!0),o=_("");U(async()=>{console.log("LinkedIn callback - URL:",window.location.href),console.log("LinkedIn callback - Search params:",window.location.search);try{const e=new URLSearchParams(window.location.search),a=e.get("code"),i=e.get("state");console.log("LinkedIn callback - Code:",a),console.log("LinkedIn callback - State:",i);const s=localStorage.getItem("linkedin_oauth_state");if(localStorage.removeItem("linkedin_oauth_state"),!i||i!==s){t(o,"Invalid state parameter. Authentication failed."),t(n,!1);return}if(!a){t(o,"No authorization code received from LinkedIn."),t(n,!1);return}const c=`${window.location.origin}/auth/linkedin-callback`;console.log("LinkedIn callback - Redirect URI:",c),console.log("LinkedIn callback - Sending API request to /api/auth/linkedin");const r=await fetch("/api/auth/linkedin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({code:a,redirectUri:c})});if(console.log("LinkedIn callback - API response status:",r.status),!r.ok){const l=await r.json();console.log("LinkedIn callback - API error response:",l),t(o,l.error||"Authentication failed."),t(n,!1);return}D.success("Sign Up Successful",{description:"Your account has been created with LinkedIn."}),z("/dashboard")}catch(e){console.error("LinkedIn callback error:",e),t(o,"An error occurred during authentication."),t(n,!1)}}),q();var v=Y(),h=x(v);E(h,{title:"LinkedIn Authentication | Auto Apply",description:"Completing your LinkedIn authentication"});var g=I(h,2),k=d(g),b=d(k),S=I(d(b),2);{var A=e=>{var a=J();p(e,a)},P=(e,a)=>{{var i=s=>{var c=M(),r=x(c),l=d(r),R=d(l,!0);m(l),m(r),O(2),N(()=>T(R,u(o))),p(s,c)};L(e,s=>{u(o)&&s(i)},a)}};L(S,e=>{u(n)?e(A):e(P,!1)})}m(b),m(k),m(g),p(w,v),C()}export{$ as component};
