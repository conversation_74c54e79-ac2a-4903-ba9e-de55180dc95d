import{c as n,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as d,s as c}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function L(r,a){const s=d(a,["children","$$slots","$$events","$$legacy"]),t=[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17"}]];f(r,c({name:"layers"},()=>s,{get iconNode(){return t},children:(e,$)=>{var o=n(),l=i(o);m(l,a,"default",{},null),p(e,o)},$$slots:{default:!0}}))}export{L};
