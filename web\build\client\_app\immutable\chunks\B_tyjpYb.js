import{c as n,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as p,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function w(a,r){const s=p(r,["children","$$slots","$$events","$$legacy"]),t=[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"}],["circle",{cx:"12",cy:"8",r:"6"}]];f(a,d({name:"award"},()=>s,{get iconNode(){return t},children:(e,$)=>{var o=n(),l=i(o);m(l,r,"default",{},null),c(e,o)},$$slots:{default:!0}}))}export{w as A};
