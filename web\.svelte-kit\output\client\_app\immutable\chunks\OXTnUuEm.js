import{b as E}from"./BfX7a-t9.js";import{A as s,a as y,b as m,c as f,E as O,H as S}from"./CIOgxH3l.js";import{b as T}from"./Bpi49Nrf.js";function C(t){return window.getComputedStyle(t).getPropertyValue("direction")}function D(t="ltr",n="horizontal"){return{horizontal:t==="rtl"?m:y,vertical:f}[n]}function F(t="ltr",n="horizontal"){return{horizontal:t==="rtl"?y:m,vertical:s}[n]}function W(t="ltr",n="horizontal"){return["ltr","rtl"].includes(t)||(t="ltr"),["horizontal","vertical"].includes(n)||(n="horizontal"),{nextKey:D(t,n),prevKey:F(t,n)}}function B(t){const n=E(null);function u(){if(!T)return[];const e=document.getElementById(t.rootNodeId.current);return e?t.candidateSelector?Array.from(e.querySelectorAll(t.candidateSelector)):t.candidateAttr?Array.from(e.querySelectorAll(`[${t.candidateAttr}]:not([data-disabled])`)):[]:[]}function v(){var r;const e=u();e.length&&((r=e[0])==null||r.focus())}function K(e,r,a=!1){var I;const g=document.getElementById(t.rootNodeId.current);if(!g||!e)return;const o=u();if(!o.length)return;const l=o.indexOf(e),b=C(g),{nextKey:h,prevKey:A}=W(b,t.orientation.current),x=t.loop.current,d={[h]:l+1,[A]:l-1,[S]:0,[O]:o.length-1};if(a){const N=h===f?y:f,z=A===s?m:s;d[N]=l+1,d[z]=l-1}let c=d[r.key];if(c===void 0)return;r.preventDefault(),c<0&&x?c=o.length-1:c===o.length&&x&&(c=0);const i=o[c];if(i)return i.focus(),n.current=i.id,(I=t.onCandidateFocus)==null||I.call(t,i),i}function R(e){const r=u(),a=n.current!==null;return e&&!a&&r[0]===e?(n.current=e.id,0):(e==null?void 0:e.id)===n.current?0:-1}return{setCurrentTabStopId(e){n.current=e},getTabIndex:R,handleKeydown:K,focusFirstCandidate:v,currentTabStopId:n}}export{B as u};
