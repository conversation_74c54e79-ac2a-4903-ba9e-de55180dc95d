import{f as C,a as T}from"./BasJTneF.js";import{p as B,s as I,c as b,t as _,g as s,x as l,a as O,r as g}from"./CGmarHxI.js";import{s as j}from"./CIt1g2O9.js";import{c as p}from"./BvdI7LR8.js";import{c as D,s as N,b as z}from"./B-Xjo-Yt.js";import{p as S}from"./Btcx8l8F.js";import{c as P}from"./ncUU1dSD.js";import{C as k}from"./CKg8MWp_.js";import{C as E}from"./BAIxhb6t.js";import{C as F}from"./-SpbofVw.js";import{T as A}from"./CTO_B1Jk.js";import{S as G}from"./yW0TxTga.js";import{P as H}from"./DvO_AOqy.js";import{C as J}from"./DW7T7T22.js";import{I as K}from"./BuYRPDDz.js";import{i as Q}from"./u21ee2wt.js";import{e as R,i as W}from"./C3w0v0gR.js";var X=C("<div><!> </div>");function we(v,e){B(e,!0);const m=S(e,"className",3,"");function x(t,u){if(u)switch(u){case"critical":return"bg-red-100 text-red-800 border-red-200";case"major":return"bg-orange-100 text-orange-800 border-orange-200";case"minor":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"maintenance":return"bg-blue-100 text-blue-800 border-blue-200";case"info":return"bg-gray-100 text-gray-800 border-gray-200"}switch(t){case"resolved":case"completed":return"bg-green-100 text-green-800 border-green-200";case"monitoring":case"in-progress":return"bg-blue-100 text-blue-800 border-blue-200";case"investigating":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"identified":return"bg-orange-100 text-orange-800 border-orange-200";case"scheduled":return"bg-purple-100 text-purple-800 border-purple-200";case"cancelled":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}}function w(t){switch(t){case"resolved":case"completed":return J;case"monitoring":return k;case"in-progress":return H;case"investigating":return G;case"identified":return A;case"scheduled":return F;case"cancelled":return E;default:return k}}function h(t){switch(t){case"in-progress":return"In Progress";default:return t.charAt(0).toUpperCase()+t.slice(1)}}const f=l(()=>x(e.status,e.severity)),c=l(()=>w(e.status)),y=l(()=>h(e.status));var a=X(),o=b(a);p(o,()=>s(c),(t,u)=>{u(t,{class:"mr-1 h-3 w-3"})});var d=I(o);g(a),_(t=>{N(a,1,t),j(d,` ${s(y)??""}`)},[()=>D(P("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold",s(f),m()))]),T(v,a),O()}var Y=C("<div><!> </div>");function he(v,e){B(e,!0);const m=S(e,"className",3,"");function x(t){switch(t){case"critical":return"bg-red-100 text-red-800 border-red-200";case"major":return"bg-orange-100 text-orange-800 border-orange-200";case"minor":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"maintenance":return"bg-blue-100 text-blue-800 border-blue-200";case"info":default:return"bg-gray-100 text-gray-800 border-gray-200"}}function w(t){switch(t){case"critical":return k;case"major":case"minor":return A;case"maintenance":case"info":default:return K}}function h(t){switch(t){case"critical":return"Critical Outage";case"major":return"Major Outage";case"minor":return"Minor Outage";case"maintenance":return"Maintenance";case"info":default:return"Information"}}const f=l(()=>x(e.severity)),c=l(()=>w(e.severity)),y=l(()=>h(e.severity));var a=Y(),o=b(a);p(o,()=>s(c),(t,u)=>{u(t,{class:"mr-1 h-3 w-3"})});var d=I(o);g(a),_(t=>{N(a,1,t),j(d,` ${s(y)??""}`)},[()=>D(P("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold",s(f),m()))]),T(v,a),O()}var Z=C("<div></div>"),$=C('<div class="flex justify-between text-xs text-gray-500"><span> </span> <span> </span></div>'),ee=C('<div><div class="flex h-2 w-full overflow-hidden rounded-full bg-gray-200"></div> <!></div>');function ye(v,e){B(e,!0);const m=S(e,"progress",3,0),x=S(e,"showTimes",3,!0),w=S(e,"className",3,"");let h=l(()=>()=>{if(m()>0)return m();if(e.status==="completed"||e.status==="resolved"||e.status==="cancelled")return 100;if(e.status==="scheduled"&&new Date(e.startTime)>new Date)return 0;const r=new Date(e.startTime).getTime(),i=new Date(e.endTime).getTime(),n=Date.now();return n<=r?0:n>=i?e.status!=="completed"&&e.status!=="resolved"?90:100:Math.round((n-r)/(i-r)*100)});function f(r){return new Date(r).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0})}function c(r){switch(r){case"resolved":case"completed":return"bg-green-500";case"monitoring":case"in-progress":return"bg-blue-500";case"investigating":case"scheduled":return"bg-yellow-500";case"identified":return"bg-orange-500";case"cancelled":return"bg-red-500";default:return"bg-gray-500"}}function y(){const r=s(h);return e.status==="completed"||e.status==="cancelled"?[{color:c(e.status),width:"100%"}]:e.status==="in-progress"||e.status==="monitoring"?[{color:c(e.status),width:`${r}%`},{color:"bg-gray-200",width:`${100-r}%`}]:e.status==="scheduled"?[{color:"bg-gray-200",width:"100%"},{color:c(e.status),width:"10%",pulse:!0}]:[{color:c(e.status),width:`${r}%`},{color:"bg-gray-200",width:`${100-r}%`}]}const a=l(()=>()=>y());var o=ee(),d=b(o);R(d,21,()=>s(a),W,(r,i)=>{var n=Z();_(M=>{N(n,1,M),z(n,`width: ${s(i).width??""}`)},[()=>D(P(s(i).color,"h-full transition-all duration-500 ease-in-out",s(i).pulse&&"animate-pulse"))]),T(r,n)}),g(d);var t=I(d,2);{var u=r=>{var i=$(),n=b(i),M=b(n,!0);g(n);var U=I(n,2),L=b(U,!0);g(U),g(i),_((V,q)=>{j(M,V),j(L,q)},[()=>f(e.startTime),()=>f(e.endTime)]),T(r,i)};Q(t,r=>{x()&&r(u)})}g(o),_(r=>N(o,1,r),[()=>D(P("w-full space-y-1",w()))]),T(v,o),O()}export{we as S,he as a,ye as b};
