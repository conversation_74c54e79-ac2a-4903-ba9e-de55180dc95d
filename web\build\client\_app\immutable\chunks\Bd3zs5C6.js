function t(e){return e?"open":"closed"}function r(e){return e?"checked":"unchecked"}function a(e){return e?"true":"false"}function u(e){return e?"true":"false"}function i(e){return e?"true":"false"}function d(e){return e?"":void 0}function f(e){return e?"true":"false"}function s(e){return e?"true":"false"}function c(e,n){return n?"mixed":e?"true":"false"}function o(e){return e}function g(e){return e?"true":void 0}function l(e){return e}function D(e){return e?"":void 0}function A(e){return e?"":void 0}function b(e){return e?"":void 0}function h(e){return e?"":void 0}function k(e){return e?"":void 0}function p(e){return e?!0:void 0}function R(e){return e?!0:void 0}function q(e){return e?!0:void 0}export{g as a,o as b,t as c,i as d,A as e,r as f,l as g,d as h,f as i,c as j,R as k,p as l,s as m,a as n,h as o,q as p,b as q,D as r,u as s,k as t};
