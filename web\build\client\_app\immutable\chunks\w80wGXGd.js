import{S as C,P as a,E as c,F as i,T as N,O as S,az as T,z as A,N as F,C as m,D as u,R as P,Y as R,X as k,as as w}from"./CGmarHxI.js";import{a as d}from"./CIt1g2O9.js";import{s as p,c as v}from"./C3w0v0gR.js";import{b as z}from"./BasJTneF.js";import{f as D}from"./CmxjS0TN.js";function q(h,g,y,_,G,M){let E=a;a&&c();var r,n,e=null;a&&i.nodeType===1&&(e=i,c());var o=a?i:h,s,b=v;C(()=>{const t=g()||null;var l=y||t==="svg"?T:null;if(t!==r){var x=v;p(b),s&&(t===null?R(s,()=>{s=null,n=null}):t===n?k(s):(w(s),d(!1))),t&&t!==n&&(s=S(()=>{if(e=a?e:l?document.createElementNS(l,t):document.createElement(t),z(e,e),_){a&&D(t)&&e.append(document.createComment(""));var f=a?A(e):e.appendChild(F());a&&(f===null?m(!1):u(f)),_(e,f)}P.nodes_end=e,o.before(e)})),r=t,r&&(n=r),d(!0),p(x)}},N),E&&(m(!0),u(o))}export{q as e};
