const c={botId:"94f9362a-6e6e-4d4f-bb1b-7eef90c7655c",clientId:"df7939d8-55e5-46c3-991b-f0502408e646",configuration:{website:{},email:{},phone:{},termsOfService:{},privacyPolicy:{},variant:"soft",themeMode:"light",fontFamily:"inter"}};function r(t,e=!1){if(typeof window>"u")return;if(!e&&typeof window.location<"u"&&!(window.location.pathname==="/contact")){console.log("Not on contact page, skipping Botpress initialization");return}const n={...c};if(document.getElementById("botpress-script")){if(window.botpress)try{window.botpress.init(n),window.dispatchEvent(new CustomEvent("botpress-ready"))}catch(s){console.log("Error reinitializing Botpress:",s);const i=document.getElementById("botpress-script");i&&i.remove(),window.botpress=void 0,setTimeout(()=>r(t,e),100)}return}const o=document.createElement("script");o.id="botpress-script",o.src="https://cdn.botpress.cloud/webchat/v2.4/inject.js",o.async=!0,document.body.appendChild(o),o.onload=()=>{var s;try{(s=window.botpress)==null||s.init(n),console.log("Botpress initialized successfully"),window.dispatchEvent(new CustomEvent("botpress-ready"))}catch(i){console.log("Error initializing Botpress:",i)}}}function a(t=!1){if(!(typeof window>"u")){if(!t&&typeof window.location<"u"&&!(window.location.pathname==="/contact")){console.log("Not on contact page, cannot show chat");return}if(!window.botpress){console.log("Botpress not loaded yet, initializing..."),r(void 0,!0),setTimeout(()=>{if(window.botpress)try{window.botpress.open(),console.log("Botpress chat opened after initialization")}catch(e){console.log("Error opening Botpress chat after initialization:",e)}},1e3);return}try{window.botpress.open(),console.log("Botpress chat opened successfully")}catch(e){console.log("Error opening Botpress chat:",e),r(void 0,!0),setTimeout(()=>{if(window.botpress)try{window.botpress.open()}catch(n){console.log("Failed to open Botpress chat after retry:",n)}},1e3)}}}function p(){if(typeof window>"u")return;if(window.botpress)try{window.botpress.close()}catch(o){console.log("Error closing Botpress chat:",o)}const t=document.getElementById("botpress-script");if(t&&t.remove(),document.querySelectorAll('[id^="bp-"]').forEach(o=>{o.remove()}),document.querySelectorAll('iframe[src*="botpress"]').forEach(o=>{o.remove()}),window.botpress)try{window.botpress=void 0}catch(o){console.log("Error resetting Botpress object:",o)}console.log("Botpress cleanup completed")}export{p as c,r as i,a as s};
