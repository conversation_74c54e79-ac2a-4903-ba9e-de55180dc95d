const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Uxihc9oX.js","./BosuxZz1.js"])))=>i.map(i=>d[i]);
var on=Object.defineProperty;var Et=t=>{throw TypeError(t)};var an=(t,e,r)=>e in t?on(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var m=(t,e,r)=>an(t,typeof e!="symbol"?e+"":e,r),Ct=(t,e,r)=>e.has(t)||Et("Cannot "+r);var l=(t,e,r)=>(Ct(t,e,"read from private field"),r?r.call(t):e.get(t)),C=(t,e,r)=>e.has(t)?Et("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,r),E=(t,e,r,n)=>(Ct(t,e,"write to private field"),n?n.call(t,r):e.set(t,r),r);import{_ as Qt}from"./C1FmrZbK.js";const Jt=!(typeof navigator>"u")&&navigator.product==="ReactNative",Yt={timeout:Jt?6e4:12e4},un=function(t){const e={...Yt,...typeof t=="string"?{url:t}:t};if(e.timeout=Xt(e.timeout),e.query){const{url:r,searchParams:n}=function(s){const o=s.indexOf("?");if(o===-1)return{url:s,searchParams:new URLSearchParams};const i=s.slice(0,o),a=s.slice(o+1);if(!Jt)return{url:i,searchParams:new URLSearchParams(a)};if(typeof decodeURIComponent!="function")throw new Error("Broken `URLSearchParams` implementation, and `decodeURIComponent` is not defined");const u=new URLSearchParams;for(const c of a.split("&")){const[f,d]=c.split("=");f&&u.append(St(f),St(d||""))}return{url:i,searchParams:u}}(e.url);for(const[s,o]of Object.entries(e.query)){if(o!==void 0)if(Array.isArray(o))for(const a of o)n.append(s,a);else n.append(s,o);const i=n.toString();i&&(e.url=`${r}?${i}`)}}return e.method=e.body&&!e.method?"POST":(e.method||"GET").toUpperCase(),e};function St(t){return decodeURIComponent(t.replace(/\+/g," "))}function Xt(t){if(t===!1||t===0)return!1;if(t.connect||t.socket)return t;const e=Number(t);return isNaN(e)?Xt(Yt.timeout):{connect:e,socket:e}}const cn=/^https?:\/\//i,ln=function(t){if(!cn.test(t.url))throw new Error(`"${t.url}" is not a valid URL`)};function Zt(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}const fn=["request","response","progress","error","abort"],xt=["processOptions","validateOptions","interceptRequest","finalizeOptions","onRequest","onResponse","onError","onReturn","onHeaders"];function Kt(t,e){const r=[],n=xt.reduce((o,i)=>(o[i]=o[i]||[],o),{processOptions:[un],validateOptions:[ln]});function s(o){const i=fn.reduce((h,y)=>(h[y]=function(){const p=Object.create(null);let g=0;return{publish:function(w){for(const b in p)p[b](w)},subscribe:function(w){const b=g++;return p[b]=w,function(){delete p[b]}}}}(),h),{}),a=(h=>function(y,p,...g){const w=y==="onError";let b=p;for(let S=0;S<h[y].length&&(b=(0,h[y][S])(b,...g),!w||b);S++);return b})(n),u=a("processOptions",o);a("validateOptions",u);const c={options:u,channels:i,applyMiddleware:a};let f;const d=i.request.subscribe(h=>{f=e(h,(y,p)=>((g,w,b)=>{let S=g,A=w;if(!S)try{A=a("onResponse",w,b)}catch(T){A=null,S=T}S=S&&a("onError",S,b),S?i.error.publish(S):A&&i.response.publish(A)})(y,p,h))});i.abort.subscribe(()=>{d(),f&&f.abort()});const v=a("onReturn",i,c);return v===i&&i.request.publish(c),v}return s.use=function(o){if(!o)throw new Error("Tried to add middleware that resolved to falsey value");if(typeof o=="function")throw new Error("Tried to add middleware that was a function. It probably expects you to pass options to it.");if(o.onReturn&&n.onReturn.length>0)throw new Error("Tried to add new middleware with `onReturn` handler, but another handler has already been registered for this event");return xt.forEach(i=>{o[i]&&n[i].push(o[i])}),r.push(o),s},s.clone=()=>Kt(r,e),t.forEach(s.use),s}var At,Ot,hn=Zt(function(){if(Ot)return At;Ot=1;var t=function(e){return e.replace(/^\s+|\s+$/g,"")};return At=function(e){if(!e)return{};for(var r={},n=t(e).split(`
`),s=0;s<n.length;s++){var o=n[s],i=o.indexOf(":"),a=t(o.slice(0,i)).toLowerCase(),u=t(o.slice(i+1));typeof r[a]>"u"?r[a]=u:(c=r[a],Object.prototype.toString.call(c)==="[object Array]"?r[a].push(u):r[a]=[r[a],u])}var c;return r}}()),be,ge,ue,we,D,Ee,Ce,Gt;let Ke=(Gt=class{constructor(){m(this,"onabort");m(this,"onerror");m(this,"onreadystatechange");m(this,"ontimeout");m(this,"readyState",0);m(this,"response");m(this,"responseText","");m(this,"responseType","");m(this,"status");m(this,"statusText");m(this,"withCredentials");C(this,be);C(this,ge);C(this,ue);C(this,we,{});C(this,D);C(this,Ee,{});C(this,Ce)}open(e,r,n){var s;E(this,be,e),E(this,ge,r),E(this,ue,""),this.readyState=1,(s=this.onreadystatechange)==null||s.call(this),E(this,D,void 0)}abort(){l(this,D)&&l(this,D).abort()}getAllResponseHeaders(){return l(this,ue)}setRequestHeader(e,r){l(this,we)[e]=r}setInit(e,r=!0){E(this,Ee,e),E(this,Ce,r)}send(e){const r=this.responseType!=="arraybuffer",n={...l(this,Ee),method:l(this,be),headers:l(this,we),body:e};typeof AbortController=="function"&&l(this,Ce)&&(E(this,D,new AbortController),typeof EventTarget<"u"&&l(this,D).signal instanceof EventTarget&&(n.signal=l(this,D).signal)),typeof document<"u"&&(n.credentials=this.withCredentials?"include":"omit"),fetch(l(this,ge),n).then(s=>{var o;return s.headers.forEach((i,a)=>{E(this,ue,l(this,ue)+`${a}: ${i}\r
`)}),this.status=s.status,this.statusText=s.statusText,this.readyState=3,(o=this.onreadystatechange)==null||o.call(this),r?s.text():s.arrayBuffer()}).then(s=>{var o;typeof s=="string"?this.responseText=s:this.response=s,this.readyState=4,(o=this.onreadystatechange)==null||o.call(this)}).catch(s=>{var o,i;s.name!=="AbortError"?(o=this.onerror)==null||o.call(this,s):(i=this.onabort)==null||i.call(this)})}},be=new WeakMap,ge=new WeakMap,ue=new WeakMap,we=new WeakMap,D=new WeakMap,Ee=new WeakMap,Ce=new WeakMap,Gt);const et=typeof XMLHttpRequest=="function"?"xhr":"fetch",dn=et==="xhr"?XMLHttpRequest:Ke,pn=(t,e)=>{const r=t.options,n=t.applyMiddleware("finalizeOptions",r),s={},o=t.applyMiddleware("interceptRequest",void 0,{adapter:et,context:t});if(o){const p=setTimeout(e,0,null,o);return{abort:()=>clearTimeout(p)}}let i=new dn;i instanceof Ke&&typeof n.fetch=="object"&&i.setInit(n.fetch,n.useAbortSignal??!0);const a=n.headers,u=n.timeout;let c=!1,f=!1,d=!1;if(i.onerror=p=>{y(i instanceof Ke?p instanceof Error?p:new Error(`Request error while attempting to reach is ${n.url}`,{cause:p}):new Error(`Request error while attempting to reach is ${n.url}${p.lengthComputable?`(${p.loaded} of ${p.total} bytes transferred)`:""}`))},i.ontimeout=p=>{y(new Error(`Request timeout while attempting to reach ${n.url}${p.lengthComputable?`(${p.loaded} of ${p.total} bytes transferred)`:""}`))},i.onabort=()=>{h(!0),c=!0},i.onreadystatechange=function(){u&&(h(),s.socket=setTimeout(()=>v("ESOCKETTIMEDOUT"),u.socket)),!c&&i&&i.readyState===4&&i.status!==0&&function(){if(!(c||f||d)){if(i.status===0)return void y(new Error("Unknown XHR error"));h(),f=!0,e(null,{body:i.response||(i.responseType===""||i.responseType==="text"?i.responseText:""),url:n.url,method:n.method,headers:hn(i.getAllResponseHeaders()),statusCode:i.status,statusMessage:i.statusText})}}()},i.open(n.method,n.url,!0),i.withCredentials=!!n.withCredentials,a&&i.setRequestHeader)for(const p in a)a.hasOwnProperty(p)&&i.setRequestHeader(p,a[p]);return n.rawBody&&(i.responseType="arraybuffer"),t.applyMiddleware("onRequest",{options:n,adapter:et,request:i,context:t}),i.send(n.body||null),u&&(s.connect=setTimeout(()=>v("ETIMEDOUT"),u.connect)),{abort:function(){c=!0,i&&i.abort()}};function v(p){d=!0,i.abort();const g=new Error(p==="ESOCKETTIMEDOUT"?`Socket timed out on request to ${n.url}`:`Connection timed out on request to ${n.url}`);g.code=p,t.channels.error.publish(g)}function h(p){(p||c||i&&i.readyState>=2&&s.connect)&&clearTimeout(s.connect),s.socket&&clearTimeout(s.socket)}function y(p){if(f)return;h(!0),f=!0,i=null;const g=p||new Error(`Network error while attempting to reach ${n.url}`);g.isNetworkError=!0,g.request=n,e(g)}},yn=(t=[],e=pn)=>Kt(t,e);var vn={},It,$t,_t,jt,Tt,Je={exports:{}};Tt||(Tt=1,function(t,e){e.formatArgs=function(n){if(n[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+n[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;const s="color: "+this.color;n.splice(1,0,s,"color: inherit");let o=0,i=0;n[0].replace(/%[a-zA-Z%]/g,a=>{a!=="%%"&&(o++,a==="%c"&&(i=o))}),n.splice(i,0,s)},e.save=function(n){try{n?e.storage.setItem("debug",n):e.storage.removeItem("debug")}catch{}},e.load=function(){let n;try{n=e.storage.getItem("debug")}catch{}return!n&&typeof process<"u"&&"env"in process&&(n=vn.DEBUG),n},e.useColors=function(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let n;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(n=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(n[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},e.storage=function(){try{return localStorage}catch{}}(),e.destroy=(()=>{let n=!1;return()=>{n||(n=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.log=console.debug||console.log||(()=>{}),t.exports=(jt?_t:(jt=1,_t=function(n){function s(a){let u,c,f,d=null;function v(...h){if(!v.enabled)return;const y=v,p=Number(new Date),g=p-(u||p);y.diff=g,y.prev=u,y.curr=p,u=p,h[0]=s.coerce(h[0]),typeof h[0]!="string"&&h.unshift("%O");let w=0;h[0]=h[0].replace(/%([a-zA-Z%])/g,(b,S)=>{if(b==="%%")return"%";w++;const A=s.formatters[S];if(typeof A=="function"){const T=h[w];b=A.call(y,T),h.splice(w,1),w--}return b}),s.formatArgs.call(y,h),(y.log||s.log).apply(y,h)}return v.namespace=a,v.useColors=s.useColors(),v.color=s.selectColor(a),v.extend=o,v.destroy=s.destroy,Object.defineProperty(v,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(c!==s.namespaces&&(c=s.namespaces,f=s.enabled(a)),f),set:h=>{d=h}}),typeof s.init=="function"&&s.init(v),v}function o(a,u){const c=s(this.namespace+(typeof u>"u"?":":u)+a);return c.log=this.log,c}function i(a,u){let c=0,f=0,d=-1,v=0;for(;c<a.length;)if(f<u.length&&(u[f]===a[c]||u[f]==="*"))u[f]==="*"?(d=f,v=c,f++):(c++,f++);else{if(d===-1)return!1;f=d+1,v++,c=v}for(;f<u.length&&u[f]==="*";)f++;return f===u.length}return s.debug=s,s.default=s,s.coerce=function(a){return a instanceof Error?a.stack||a.message:a},s.disable=function(){const a=[...s.names,...s.skips.map(u=>"-"+u)].join(",");return s.enable(""),a},s.enable=function(a){s.save(a),s.namespaces=a,s.names=[],s.skips=[];const u=(typeof a=="string"?a:"").trim().replace(" ",",").split(",").filter(Boolean);for(const c of u)c[0]==="-"?s.skips.push(c.slice(1)):s.names.push(c)},s.enabled=function(a){for(const u of s.skips)if(i(a,u))return!1;for(const u of s.names)if(i(a,u))return!0;return!1},s.humanize=function(){if($t)return It;$t=1;var a=1e3,u=60*a,c=60*u,f=24*c,d=7*f;function v(h,y,p,g){var w=y>=1.5*p;return Math.round(h/p)+" "+g+(w?"s":"")}return It=function(h,y){y=y||{};var p,g,w=typeof h;if(w==="string"&&h.length>0)return function(b){if(!((b=String(b)).length>100)){var S=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(b);if(S){var A=parseFloat(S[1]);switch((S[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*A;case"weeks":case"week":case"w":return A*d;case"days":case"day":case"d":return A*f;case"hours":case"hour":case"hrs":case"hr":case"h":return A*c;case"minutes":case"minute":case"mins":case"min":case"m":return A*u;case"seconds":case"second":case"secs":case"sec":case"s":return A*a;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return A;default:return}}}}(h);if(w==="number"&&isFinite(h))return y.long?(p=h,(g=Math.abs(p))>=f?v(p,g,f,"day"):g>=c?v(p,g,c,"hour"):g>=u?v(p,g,u,"minute"):g>=a?v(p,g,a,"second"):p+" ms"):function(b){var S=Math.abs(b);return S>=f?Math.round(b/f)+"d":S>=c?Math.round(b/c)+"h":S>=u?Math.round(b/u)+"m":S>=a?Math.round(b/a)+"s":b+"ms"}(h);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(h))}}(),s.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(n).forEach(a=>{s[a]=n[a]}),s.names=[],s.skips=[],s.formatters={},s.selectColor=function(a){let u=0;for(let c=0;c<a.length;c++)u=(u<<5)-u+a.charCodeAt(c),u|=0;return s.colors[Math.abs(u)%s.colors.length]},s.enable(s.load()),s}))(e);const{formatters:r}=t.exports;r.j=function(n){try{return JSON.stringify(n)}catch(s){return"[UnexpectedJSONParseError]: "+s.message}}}(Je,Je.exports)),Je.exports;const mn=typeof Buffer>"u"?()=>!1:t=>Buffer.isBuffer(t);function Rt(t){return Object.prototype.toString.call(t)==="[object Object]"}function bn(t){if(Rt(t)===!1)return!1;const e=t.constructor;if(e===void 0)return!0;const r=e.prototype;return!(Rt(r)===!1||r.hasOwnProperty("isPrototypeOf")===!1)}const gn=["boolean","string","number"];function wn(){return{processOptions:t=>{const e=t.body;return!e||typeof e.pipe=="function"||mn(e)||gn.indexOf(typeof e)===-1&&!Array.isArray(e)&&!bn(e)?t:Object.assign({},t,{body:JSON.stringify(t.body),headers:Object.assign({},t.headers,{"Content-Type":"application/json"})})}}}function En(t){return{onResponse:r=>{const n=r.headers["content-type"]||"",s=t&&t.force||n.indexOf("application/json")!==-1;return r.body&&n&&s?Object.assign({},r,{body:e(r.body)}):r},processOptions:r=>Object.assign({},r,{headers:Object.assign({Accept:"application/json"},r.headers)})};function e(r){try{return JSON.parse(r)}catch(n){throw n.message=`Failed to parsed response body as JSON: ${n.message}`,n}}}let pe={};typeof globalThis<"u"?pe=globalThis:typeof window<"u"?pe=window:typeof global<"u"?pe=global:typeof self<"u"&&(pe=self);var Cn=pe;function Sn(t={}){const e=t.implementation||Cn.Observable;if(!e)throw new Error("`Observable` is not available in global scope, and no implementation was passed");return{onReturn:(r,n)=>new e(s=>(r.error.subscribe(o=>s.error(o)),r.progress.subscribe(o=>s.next(Object.assign({type:"progress"},o))),r.response.subscribe(o=>{s.next(Object.assign({type:"response"},o)),s.complete()}),r.request.publish(n),()=>r.abort.publish()))}}function xn(){return{onRequest:t=>{if(t.adapter!=="xhr")return;const e=t.request,r=t.context;function n(s){return o=>{const i=o.lengthComputable?o.loaded/o.total*100:-1;r.channels.progress.publish({stage:s,percent:i,total:o.total,loaded:o.loaded,lengthComputable:o.lengthComputable})}}"upload"in e&&"onprogress"in e.upload&&(e.upload.onprogress=n("upload")),"onprogress"in e&&(e.onprogress=n("download"))}}}var er=(t,e,r)=>(r.method==="GET"||r.method==="HEAD")&&(t.isNetworkError||!1);function An(t){return 100*Math.pow(2,t)+100*Math.random()}const st=(t={})=>(e=>{const r=e.maxRetries||5,n=e.retryDelay||An,s=e.shouldRetry;return{onError:(o,i)=>{const a=i.options,u=a.maxRetries||r,c=a.retryDelay||n,f=a.shouldRetry||s,d=a.attemptNumber||0;if((v=a.body)!==null&&typeof v=="object"&&typeof v.pipe=="function"||!f(o,d,a)||d>=u)return o;var v;const h=Object.assign({},i,{options:Object.assign({},a,{attemptNumber:d+1})});return setTimeout(()=>i.channels.request.publish(h),c(d)),null}}})({shouldRetry:er,...t});st.shouldRetry=er;var tt=function(t,e){return tt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(r[s]=n[s])},tt(t,e)};function V(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");tt(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}function On(t,e,r,n){function s(o){return o instanceof r?o:new r(function(i){i(o)})}return new(r||(r=Promise))(function(o,i){function a(f){try{c(n.next(f))}catch(d){i(d)}}function u(f){try{c(n.throw(f))}catch(d){i(d)}}function c(f){f.done?o(f.value):s(f.value).then(a,u)}c((n=n.apply(t,e||[])).next())})}function tr(t,e){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},n,s,o,i=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return i.next=a(0),i.throw=a(1),i.return=a(2),typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function a(c){return function(f){return u([c,f])}}function u(c){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(r=0)),r;)try{if(n=1,s&&(o=c[0]&2?s.return:c[0]?s.throw||((o=s.return)&&o.call(s),0):s.next)&&!(o=o.call(s,c[1])).done)return o;switch(s=0,o&&(c=[c[0]&2,o.value]),c[0]){case 0:case 1:o=c;break;case 4:return r.label++,{value:c[1],done:!1};case 5:r.label++,s=c[1],c=[0];continue;case 7:c=r.ops.pop(),r.trys.pop();continue;default:if(o=r.trys,!(o=o.length>0&&o[o.length-1])&&(c[0]===6||c[0]===2)){r=0;continue}if(c[0]===3&&(!o||c[1]>o[0]&&c[1]<o[3])){r.label=c[1];break}if(c[0]===6&&r.label<o[1]){r.label=o[1],o=c;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(c);break}o[2]&&r.ops.pop(),r.trys.pop();continue}c=e.call(t,r)}catch(f){c=[6,f],s=0}finally{n=o=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function fe(t){var e=typeof Symbol=="function"&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function B(t,e){var r=typeof Symbol=="function"&&t[Symbol.iterator];if(!r)return t;var n=r.call(t),s,o=[],i;try{for(;(e===void 0||e-- >0)&&!(s=n.next()).done;)o.push(s.value)}catch(a){i={error:a}}finally{try{s&&!s.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return o}function H(t,e,r){if(r||arguments.length===2)for(var n=0,s=e.length,o;n<s;n++)(o||!(n in e))&&(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))}function ie(t){return this instanceof ie?(this.v=t,this):new ie(t)}function In(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(t,e||[]),s,o=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",i),s[Symbol.asyncIterator]=function(){return this},s;function i(h){return function(y){return Promise.resolve(y).then(h,d)}}function a(h,y){n[h]&&(s[h]=function(p){return new Promise(function(g,w){o.push([h,p,g,w])>1||u(h,p)})},y&&(s[h]=y(s[h])))}function u(h,y){try{c(n[h](y))}catch(p){v(o[0][3],p)}}function c(h){h.value instanceof ie?Promise.resolve(h.value.v).then(f,d):v(o[0][2],h)}function f(h){u("next",h)}function d(h){u("throw",h)}function v(h,y){h(y),o.shift(),o.length&&u(o[0][0],o[0][1])}}function $n(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],r;return e?e.call(t):(t=typeof fe=="function"?fe(t):t[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(o){r[o]=t[o]&&function(i){return new Promise(function(a,u){i=t[o](i),s(a,u,i.done,i.value)})}}function s(o,i,a,u){Promise.resolve(u).then(function(c){o({value:c,done:a})},i)}}function x(t){return typeof t=="function"}function ot(t){var e=function(n){Error.call(n),n.stack=new Error().stack},r=t(e);return r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r}var Ye=ot(function(t){return function(r){t(this),this.message=r?r.length+` errors occurred during unsubscription:
`+r.map(function(n,s){return s+1+") "+n.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=r}});function Ue(t,e){if(t){var r=t.indexOf(e);0<=r&&t.splice(r,1)}}var Te=function(){function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){var e,r,n,s,o;if(!this.closed){this.closed=!0;var i=this._parentage;if(i)if(this._parentage=null,Array.isArray(i))try{for(var a=fe(i),u=a.next();!u.done;u=a.next()){var c=u.value;c.remove(this)}}catch(p){e={error:p}}finally{try{u&&!u.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}else i.remove(this);var f=this.initialTeardown;if(x(f))try{f()}catch(p){o=p instanceof Ye?p.errors:[p]}var d=this._finalizers;if(d){this._finalizers=null;try{for(var v=fe(d),h=v.next();!h.done;h=v.next()){var y=h.value;try{Pt(y)}catch(p){o=o??[],p instanceof Ye?o=H(H([],B(o)),B(p.errors)):o.push(p)}}}catch(p){n={error:p}}finally{try{h&&!h.done&&(s=v.return)&&s.call(v)}finally{if(n)throw n.error}}}if(o)throw new Ye(o)}},t.prototype.add=function(e){var r;if(e&&e!==this)if(this.closed)Pt(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=(r=this._finalizers)!==null&&r!==void 0?r:[]).push(e)}},t.prototype._hasParent=function(e){var r=this._parentage;return r===e||Array.isArray(r)&&r.includes(e)},t.prototype._addParent=function(e){var r=this._parentage;this._parentage=Array.isArray(r)?(r.push(e),r):r?[r,e]:e},t.prototype._removeParent=function(e){var r=this._parentage;r===e?this._parentage=null:Array.isArray(r)&&Ue(r,e)},t.prototype.remove=function(e){var r=this._finalizers;r&&Ue(r,e),e instanceof t&&e._removeParent(this)},t.EMPTY=function(){var e=new t;return e.closed=!0,e}(),t}(),rr=Te.EMPTY;function nr(t){return t instanceof Te||t&&"closed"in t&&x(t.remove)&&x(t.add)&&x(t.unsubscribe)}function Pt(t){x(t)?t():t.unsubscribe()}var _n={Promise:void 0},jn={setTimeout:function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return setTimeout.apply(void 0,H([t,e],B(r)))},clearTimeout:function(t){return clearTimeout(t)},delegate:void 0};function sr(t){jn.setTimeout(function(){throw t})}function Ft(){}function Me(t){t()}var it=function(t){V(e,t);function e(r){var n=t.call(this)||this;return n.isStopped=!1,r?(n.destination=r,nr(r)&&r.add(n)):n.destination=Pn,n}return e.create=function(r,n,s){return new me(r,n,s)},e.prototype.next=function(r){this.isStopped||this._next(r)},e.prototype.error=function(r){this.isStopped||(this.isStopped=!0,this._error(r))},e.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this),this.destination=null)},e.prototype._next=function(r){this.destination.next(r)},e.prototype._error=function(r){try{this.destination.error(r)}finally{this.unsubscribe()}},e.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},e}(Te),Tn=function(){function t(e){this.partialObserver=e}return t.prototype.next=function(e){var r=this.partialObserver;if(r.next)try{r.next(e)}catch(n){ke(n)}},t.prototype.error=function(e){var r=this.partialObserver;if(r.error)try{r.error(e)}catch(n){ke(n)}else ke(e)},t.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(r){ke(r)}},t}(),me=function(t){V(e,t);function e(r,n,s){var o=t.call(this)||this,i;return x(r)||!r?i={next:r??void 0,error:n??void 0,complete:s??void 0}:i=r,o.destination=new Tn(i),o}return e}(it);function ke(t){sr(t)}function Rn(t){throw t}var Pn={closed:!0,next:Ft,error:Rn,complete:Ft},at=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function Ve(t){return t}function Fn(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return or(t)}function or(t){return t.length===0?Ve:t.length===1?t[0]:function(r){return t.reduce(function(n,s){return s(n)},r)}}var O=function(){function t(e){e&&(this._subscribe=e)}return t.prototype.lift=function(e){var r=new t;return r.source=this,r.operator=e,r},t.prototype.subscribe=function(e,r,n){var s=this,o=kn(e)?e:new me(e,r,n);return Me(function(){var i=s,a=i.operator,u=i.source;o.add(a?a.call(o,u):u?s._subscribe(o):s._trySubscribe(o))}),o},t.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(r){e.error(r)}},t.prototype.forEach=function(e,r){var n=this;return r=qt(r),new r(function(s,o){var i=new me({next:function(a){try{e(a)}catch(u){o(u),i.unsubscribe()}},error:o,complete:s});n.subscribe(i)})},t.prototype._subscribe=function(e){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(e)},t.prototype[at]=function(){return this},t.prototype.pipe=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return or(e)(this)},t.prototype.toPromise=function(e){var r=this;return e=qt(e),new e(function(n,s){var o;r.subscribe(function(i){return o=i},function(i){return s(i)},function(){return n(o)})})},t.create=function(e){return new t(e)},t}();function qt(t){var e;return(e=t??_n.Promise)!==null&&e!==void 0?e:Promise}function qn(t){return t&&x(t.next)&&x(t.error)&&x(t.complete)}function kn(t){return t&&t instanceof it||qn(t)&&nr(t)}function Mn(t){return x(t==null?void 0:t.lift)}function M(t){return function(e){if(Mn(e))return e.lift(function(r){try{return t(r,this)}catch(n){this.error(n)}});throw new TypeError("Unable to lift unknown Observable type")}}function ee(t,e,r,n,s){return new Un(t,e,r,n,s)}var Un=function(t){V(e,t);function e(r,n,s,o,i,a){var u=t.call(this,r)||this;return u.onFinalize=i,u.shouldUnsubscribe=a,u._next=n?function(c){try{n(c)}catch(f){r.error(f)}}:t.prototype._next,u._error=o?function(c){try{o(c)}catch(f){r.error(f)}finally{this.unsubscribe()}}:t.prototype._error,u._complete=s?function(){try{s()}catch(c){r.error(c)}finally{this.unsubscribe()}}:t.prototype._complete,u}return e.prototype.unsubscribe=function(){var r;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var n=this.closed;t.prototype.unsubscribe.call(this),!n&&((r=this.onFinalize)===null||r===void 0||r.call(this))}},e}(it),Dn=ot(function(t){return function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),ut=function(t){V(e,t);function e(){var r=t.call(this)||this;return r.closed=!1,r.currentObservers=null,r.observers=[],r.isStopped=!1,r.hasError=!1,r.thrownError=null,r}return e.prototype.lift=function(r){var n=new kt(this,this);return n.operator=r,n},e.prototype._throwIfClosed=function(){if(this.closed)throw new Dn},e.prototype.next=function(r){var n=this;Me(function(){var s,o;if(n._throwIfClosed(),!n.isStopped){n.currentObservers||(n.currentObservers=Array.from(n.observers));try{for(var i=fe(n.currentObservers),a=i.next();!a.done;a=i.next()){var u=a.value;u.next(r)}}catch(c){s={error:c}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(s)throw s.error}}}})},e.prototype.error=function(r){var n=this;Me(function(){if(n._throwIfClosed(),!n.isStopped){n.hasError=n.isStopped=!0,n.thrownError=r;for(var s=n.observers;s.length;)s.shift().error(r)}})},e.prototype.complete=function(){var r=this;Me(function(){if(r._throwIfClosed(),!r.isStopped){r.isStopped=!0;for(var n=r.observers;n.length;)n.shift().complete()}})},e.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(e.prototype,"observed",{get:function(){var r;return((r=this.observers)===null||r===void 0?void 0:r.length)>0},enumerable:!1,configurable:!0}),e.prototype._trySubscribe=function(r){return this._throwIfClosed(),t.prototype._trySubscribe.call(this,r)},e.prototype._subscribe=function(r){return this._throwIfClosed(),this._checkFinalizedStatuses(r),this._innerSubscribe(r)},e.prototype._innerSubscribe=function(r){var n=this,s=this,o=s.hasError,i=s.isStopped,a=s.observers;return o||i?rr:(this.currentObservers=null,a.push(r),new Te(function(){n.currentObservers=null,Ue(a,r)}))},e.prototype._checkFinalizedStatuses=function(r){var n=this,s=n.hasError,o=n.thrownError,i=n.isStopped;s?r.error(o):i&&r.complete()},e.prototype.asObservable=function(){var r=new O;return r.source=this,r},e.create=function(r,n){return new kt(r,n)},e}(O),kt=function(t){V(e,t);function e(r,n){var s=t.call(this)||this;return s.destination=r,s.source=n,s}return e.prototype.next=function(r){var n,s;(s=(n=this.destination)===null||n===void 0?void 0:n.next)===null||s===void 0||s.call(n,r)},e.prototype.error=function(r){var n,s;(s=(n=this.destination)===null||n===void 0?void 0:n.error)===null||s===void 0||s.call(n,r)},e.prototype.complete=function(){var r,n;(n=(r=this.destination)===null||r===void 0?void 0:r.complete)===null||n===void 0||n.call(r)},e.prototype._subscribe=function(r){var n,s;return(s=(n=this.source)===null||n===void 0?void 0:n.subscribe(r))!==null&&s!==void 0?s:rr},e}(ut),ct={now:function(){return(ct.delegate||Date).now()},delegate:void 0},Ln=function(t){V(e,t);function e(r,n,s){r===void 0&&(r=1/0),n===void 0&&(n=1/0),s===void 0&&(s=ct);var o=t.call(this)||this;return o._bufferSize=r,o._windowTime=n,o._timestampProvider=s,o._buffer=[],o._infiniteTimeWindow=!0,o._infiniteTimeWindow=n===1/0,o._bufferSize=Math.max(1,r),o._windowTime=Math.max(1,n),o}return e.prototype.next=function(r){var n=this,s=n.isStopped,o=n._buffer,i=n._infiniteTimeWindow,a=n._timestampProvider,u=n._windowTime;s||(o.push(r),!i&&o.push(a.now()+u)),this._trimBuffer(),t.prototype.next.call(this,r)},e.prototype._subscribe=function(r){this._throwIfClosed(),this._trimBuffer();for(var n=this._innerSubscribe(r),s=this,o=s._infiniteTimeWindow,i=s._buffer,a=i.slice(),u=0;u<a.length&&!r.closed;u+=o?1:2)r.next(a[u]);return this._checkFinalizedStatuses(r),n},e.prototype._trimBuffer=function(){var r=this,n=r._bufferSize,s=r._timestampProvider,o=r._buffer,i=r._infiniteTimeWindow,a=(i?1:2)*n;if(n<1/0&&a<o.length&&o.splice(0,o.length-a),!i){for(var u=s.now(),c=0,f=1;f<o.length&&o[f]<=u;f+=2)c=f;c&&o.splice(0,c+1)}},e}(ut),Nn=function(t){V(e,t);function e(r,n){return t.call(this)||this}return e.prototype.schedule=function(r,n){return this},e}(Te),Mt={setInterval:function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return setInterval.apply(void 0,H([t,e],B(r)))},clearInterval:function(t){return clearInterval(t)},delegate:void 0},zn=function(t){V(e,t);function e(r,n){var s=t.call(this,r,n)||this;return s.scheduler=r,s.work=n,s.pending=!1,s}return e.prototype.schedule=function(r,n){var s;if(n===void 0&&(n=0),this.closed)return this;this.state=r;var o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(s=this.id)!==null&&s!==void 0?s:this.requestAsyncId(i,this.id,n),this},e.prototype.requestAsyncId=function(r,n,s){return s===void 0&&(s=0),Mt.setInterval(r.flush.bind(r,this),s)},e.prototype.recycleAsyncId=function(r,n,s){if(s===void 0&&(s=0),s!=null&&this.delay===s&&this.pending===!1)return n;n!=null&&Mt.clearInterval(n)},e.prototype.execute=function(r,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var s=this._execute(r,n);if(s)return s;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},e.prototype._execute=function(r,n){var s=!1,o;try{this.work(r)}catch(i){s=!0,o=i||new Error("Scheduled action threw falsy error")}if(s)return this.unsubscribe(),o},e.prototype.unsubscribe=function(){if(!this.closed){var r=this,n=r.id,s=r.scheduler,o=s.actions;this.work=this.state=this.scheduler=null,this.pending=!1,Ue(o,this),n!=null&&(this.id=this.recycleAsyncId(s,n,null)),this.delay=null,t.prototype.unsubscribe.call(this)}},e}(Nn),Ut=function(){function t(e,r){r===void 0&&(r=t.now),this.schedulerActionCtor=e,this.now=r}return t.prototype.schedule=function(e,r,n){return r===void 0&&(r=0),new this.schedulerActionCtor(this,e).schedule(n,r)},t.now=ct.now,t}(),Bn=function(t){V(e,t);function e(r,n){n===void 0&&(n=Ut.now);var s=t.call(this,r,n)||this;return s.actions=[],s._active=!1,s}return e.prototype.flush=function(r){var n=this.actions;if(this._active){n.push(r);return}var s;this._active=!0;do if(s=r.execute(r.state,r.delay))break;while(r=n.shift());if(this._active=!1,s){for(;r=n.shift();)r.unsubscribe();throw s}},e}(Ut),Hn=new Bn(zn),Vn=Hn,ir=new O(function(t){return t.complete()});function Wn(t){return t&&x(t.schedule)}function lt(t){return t[t.length-1]}function Gn(t){return x(lt(t))?t.pop():void 0}function ft(t){return Wn(lt(t))?t.pop():void 0}function Qn(t,e){return typeof lt(t)=="number"?t.pop():e}var ar=function(t){return t&&typeof t.length=="number"&&typeof t!="function"};function ur(t){return x(t==null?void 0:t.then)}function cr(t){return x(t[at])}function lr(t){return Symbol.asyncIterator&&x(t==null?void 0:t[Symbol.asyncIterator])}function fr(t){return new TypeError("You provided "+(t!==null&&typeof t=="object"?"an invalid object":"'"+t+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function Jn(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var hr=Jn();function dr(t){return x(t==null?void 0:t[hr])}function pr(t){return In(this,arguments,function(){var r,n,s,o;return tr(this,function(i){switch(i.label){case 0:r=t.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,ie(r.read())];case 3:return n=i.sent(),s=n.value,o=n.done,o?[4,ie(void 0)]:[3,5];case 4:return[2,i.sent()];case 5:return[4,ie(s)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return r.releaseLock(),[7];case 10:return[2]}})})}function yr(t){return x(t==null?void 0:t.getReader)}function U(t){if(t instanceof O)return t;if(t!=null){if(cr(t))return Yn(t);if(ar(t))return Xn(t);if(ur(t))return Zn(t);if(lr(t))return vr(t);if(dr(t))return Kn(t);if(yr(t))return es(t)}throw fr(t)}function Yn(t){return new O(function(e){var r=t[at]();if(x(r.subscribe))return r.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Xn(t){return new O(function(e){for(var r=0;r<t.length&&!e.closed;r++)e.next(t[r]);e.complete()})}function Zn(t){return new O(function(e){t.then(function(r){e.closed||(e.next(r),e.complete())},function(r){return e.error(r)}).then(null,sr)})}function Kn(t){return new O(function(e){var r,n;try{for(var s=fe(t),o=s.next();!o.done;o=s.next()){var i=o.value;if(e.next(i),e.closed)return}}catch(a){r={error:a}}finally{try{o&&!o.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}e.complete()})}function vr(t){return new O(function(e){ts(t,e).catch(function(r){return e.error(r)})})}function es(t){return vr(pr(t))}function ts(t,e){var r,n,s,o;return On(this,void 0,void 0,function(){var i,a;return tr(this,function(u){switch(u.label){case 0:u.trys.push([0,5,6,11]),r=$n(t),u.label=1;case 1:return[4,r.next()];case 2:if(n=u.sent(),!!n.done)return[3,4];if(i=n.value,e.next(i),e.closed)return[2];u.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return a=u.sent(),s={error:a},[3,11];case 6:return u.trys.push([6,,9,10]),n&&!n.done&&(o=r.return)?[4,o.call(r)]:[3,8];case 7:u.sent(),u.label=8;case 8:return[3,10];case 9:if(s)throw s.error;return[7];case 10:return[7];case 11:return e.complete(),[2]}})})}function ne(t,e,r,n,s){n===void 0&&(n=0),s===void 0&&(s=!1);var o=e.schedule(function(){r(),s?t.add(this.schedule(null,n)):this.unsubscribe()},n);if(t.add(o),!s)return o}function mr(t,e){return e===void 0&&(e=0),M(function(r,n){r.subscribe(ee(n,function(s){return ne(n,t,function(){return n.next(s)},e)},function(){return ne(n,t,function(){return n.complete()},e)},function(s){return ne(n,t,function(){return n.error(s)},e)}))})}function br(t,e){return e===void 0&&(e=0),M(function(r,n){n.add(t.schedule(function(){return r.subscribe(n)},e))})}function rs(t,e){return U(t).pipe(br(e),mr(e))}function ns(t,e){return U(t).pipe(br(e),mr(e))}function ss(t,e){return new O(function(r){var n=0;return e.schedule(function(){n===t.length?r.complete():(r.next(t[n++]),r.closed||this.schedule())})})}function os(t,e){return new O(function(r){var n;return ne(r,e,function(){n=t[hr](),ne(r,e,function(){var s,o,i;try{s=n.next(),o=s.value,i=s.done}catch(a){r.error(a);return}i?r.complete():r.next(o)},0,!0)}),function(){return x(n==null?void 0:n.return)&&n.return()}})}function gr(t,e){if(!t)throw new Error("Iterable cannot be null");return new O(function(r){ne(r,e,function(){var n=t[Symbol.asyncIterator]();ne(r,e,function(){n.next().then(function(s){s.done?r.complete():r.next(s.value)})},0,!0)})})}function is(t,e){return gr(pr(t),e)}function as(t,e){if(t!=null){if(cr(t))return rs(t,e);if(ar(t))return ss(t,e);if(ur(t))return ns(t,e);if(lr(t))return gr(t,e);if(dr(t))return os(t,e);if(yr(t))return is(t,e)}throw fr(t)}function Re(t,e){return e?as(t,e):U(t)}function We(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=ft(t);return Re(t,r)}function wr(t,e){var r=x(t)?t:function(){return t},n=function(s){return s.error(r())};return new O(n)}function us(t){return!!t&&(t instanceof O||x(t.lift)&&x(t.subscribe))}var cs=ot(function(t){return function(){t(this),this.name="EmptyError",this.message="no elements in sequence"}});function _(t,e){return new Promise(function(r,n){var s=!1,o;t.subscribe({next:function(i){o=i,s=!0},error:n,complete:function(){s?r(o):n(new cs)}})})}function ls(t){return t instanceof Date&&!isNaN(t)}function j(t,e){return M(function(r,n){var s=0;r.subscribe(ee(n,function(o){n.next(t.call(e,o,s++))}))})}var fs=Array.isArray;function hs(t,e){return fs(e)?t.apply(void 0,H([],B(e))):t(e)}function ds(t){return j(function(e){return hs(t,e)})}function ps(t,e,r){return r===void 0&&(r=Ve),function(n){Dt(e,function(){for(var s=t.length,o=new Array(s),i=s,a=s,u=function(f){Dt(e,function(){var d=Re(t[f],e),v=!1;d.subscribe(ee(n,function(h){o[f]=h,v||(v=!0,a--),a||n.next(r(o.slice()))},function(){--i||n.complete()}))},n)},c=0;c<s;c++)u(c)})}}function Dt(t,e,r){e()}function ys(t,e,r,n,s,o,i,a){var u=[],c=0,f=0,d=!1,v=function(){d&&!u.length&&!c&&e.complete()},h=function(p){return c<n?y(p):u.push(p)},y=function(p){c++;var g=!1;U(r(p,f++)).subscribe(ee(e,function(w){e.next(w)},function(){g=!0},void 0,function(){if(g)try{c--;for(var w=function(){var b=u.shift();i||y(b)};u.length&&c<n;)w();v()}catch(b){e.error(b)}}))};return t.subscribe(ee(e,h,function(){d=!0,v()})),function(){}}function Pe(t,e,r){return r===void 0&&(r=1/0),x(e)?Pe(function(n,s){return j(function(o,i){return e(n,o,s,i)})(U(t(n,s)))},r):(typeof e=="number"&&(r=e),M(function(n,s){return ys(n,s,t,r)}))}function Er(t){return t===void 0&&(t=1/0),Pe(Ve,t)}function vs(){return Er(1)}function Cr(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return vs()(Re(t,ft(t)))}function Sr(t){return new O(function(e){U(t()).subscribe(e)})}function ms(t,e,r){return r===void 0&&(r=Vn),new O(function(n){var s=ls(t)?1e3-r.now():t;s<0&&(s=0);var o=0;return r.schedule(function(){n.closed||(n.next(o++),n.complete())},s)})}function bs(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=ft(t),n=Qn(t,1/0),s=t;return s.length?s.length===1?U(s[0]):Er(n)(Re(s,r)):ir}var gs=Array.isArray;function ws(t){return t.length===1&&gs(t[0])?t[0]:t}function he(t,e){return M(function(r,n){var s=0;r.subscribe(ee(n,function(o){return t.call(e,o,s++)&&n.next(o)}))})}function ht(t){return M(function(e,r){var n=null,s=!1,o;n=e.subscribe(ee(r,void 0,void 0,function(i){o=U(t(i,ht(t)(e))),n?(n.unsubscribe(),n=null,o.subscribe(r)):s=!0})),s&&(n.unsubscribe(),n=null,o.subscribe(r))})}function xr(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=Gn(t);return r?Fn(xr.apply(void 0,H([],B(t))),ds(r)):M(function(n,s){ps(H([n],B(ws(t))))(s)})}function Es(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return xr.apply(void 0,H([],B(t)))}function Ar(t){return M(function(e,r){try{e.subscribe(r)}finally{r.add(t)}})}function Or(t){t===void 0&&(t={});var e=t.connector,r=e===void 0?function(){return new ut}:e,n=t.resetOnError,s=n===void 0?!0:n,o=t.resetOnComplete,i=o===void 0?!0:o,a=t.resetOnRefCountZero,u=a===void 0?!0:a;return function(c){var f,d,v,h=0,y=!1,p=!1,g=function(){d==null||d.unsubscribe(),d=void 0},w=function(){g(),f=v=void 0,y=p=!1},b=function(){var S=f;w(),S==null||S.unsubscribe()};return M(function(S,A){h++,!p&&!y&&g();var T=v=v??r();A.add(function(){h--,h===0&&!p&&!y&&(d=Xe(b,u))}),T.subscribe(A),!f&&h>0&&(f=new me({next:function(R){return T.next(R)},error:function(R){p=!0,g(),d=Xe(w,s,R),T.error(R)},complete:function(){y=!0,g(),d=Xe(w,i),T.complete()}}),U(S).subscribe(f))})(c)}}function Xe(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];if(e===!0){t();return}if(e!==!1){var s=new me({next:function(){s.unsubscribe(),t()}});return U(e.apply(void 0,H([],B(r)))).subscribe(s)}}function Cs(t,e,r){var n,s=!1;return n=t,Or({connector:function(){return new Ln(n,e,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:s})}function Ss(t,e,r){var n=x(t)||e||r?{next:t,error:e,complete:r}:t;return n?M(function(s,o){var i;(i=n.subscribe)===null||i===void 0||i.call(n);var a=!0;s.subscribe(ee(o,function(u){var c;(c=n.next)===null||c===void 0||c.call(n,u),o.next(u)},function(){var u;a=!1,(u=n.complete)===null||u===void 0||u.call(n),o.complete()},function(u){var c;a=!1,(c=n.error)===null||c===void 0||c.call(n,u),o.error(u)},function(){var u,c;a&&((u=n.unsubscribe)===null||u===void 0||u.call(n)),(c=n.finalize)===null||c===void 0||c.call(n)}))}):Ve}var Ir={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},dt={0:8203,1:8204,2:8205,3:65279},xs=new Array(4).fill(String.fromCodePoint(dt[0])).join("");function As(t){let e=JSON.stringify(t);return`${xs}${Array.from(e).map(r=>{let n=r.charCodeAt(0);if(n>255)throw new Error(`Only ASCII edit info can be encoded. Error attempting to encode ${e} on character ${r} (${n})`);return Array.from(n.toString(4).padStart(4,"0")).map(s=>String.fromCodePoint(dt[s])).join("")}).join("")}`}function Os(t){return!Number.isNaN(Number(t))||/[a-z]/i.test(t)&&!/\d+(?:[-:\/]\d+){2}(?:T\d+(?:[-:\/]\d+){1,2}(\.\d+)?Z?)?/.test(t)?!1:!!Date.parse(t)}function Is(t){try{new URL(t,t.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}function Zo(t,e,r="auto"){return r===!0||r==="auto"&&(Os(t)||Is(t))?t:`${t}${As(e)}`}Object.fromEntries(Object.entries(dt).map(t=>t.reverse()));Object.fromEntries(Object.entries(Ir).map(t=>t.reverse()));var $s=`${Object.values(Ir).map(t=>`\\u{${t.toString(16)}}`).join("")}`,Lt=new RegExp(`[${$s}]{4,}`,"gu");function _s(t){var e;return{cleaned:t.replace(Lt,""),encoded:((e=t.match(Lt))==null?void 0:e[0])||""}}function js(t){return t&&JSON.parse(_s(JSON.stringify(t)).cleaned)}function Ts(t){return js(t)}class Rs extends Error{constructor(r){const n=$r(r);super(n.message);m(this,"response");m(this,"statusCode",400);m(this,"responseBody");m(this,"details");Object.assign(this,n)}}class Ps extends Error{constructor(r){const n=$r(r);super(n.message);m(this,"response");m(this,"statusCode",500);m(this,"responseBody");m(this,"details");Object.assign(this,n)}}function $r(t){const e=t.body,r={response:t,statusCode:t.statusCode,responseBody:Ms(e,t),message:"",details:void 0};if(e.error&&e.message)return r.message=`${e.error} - ${e.message}`,r;if(Fs(e)||qs(e)){const n=e.error.items||[],s=n.slice(0,5).map(i=>{var a;return(a=i.error)==null?void 0:a.description}).filter(Boolean);let o=s.length?`:
- ${s.join(`
- `)}`:"";return n.length>5&&(o+=`
...and ${n.length-5} more`),r.message=`${e.error.description}${o}`,r.details=e.error,r}return e.error&&e.error.description?(r.message=e.error.description,r.details=e.error,r):(r.message=e.error||e.message||ks(t),r)}function Fs(t){return De(t)&&De(t.error)&&t.error.type==="mutationError"&&typeof t.error.description=="string"}function qs(t){return De(t)&&De(t.error)&&t.error.type==="actionError"&&typeof t.error.description=="string"}function De(t){return typeof t=="object"&&t!==null&&!Array.isArray(t)}function ks(t){const e=t.statusMessage?` ${t.statusMessage}`:"";return`${t.method}-request to ${t.url} resulted in HTTP ${t.statusCode}${e}`}function Ms(t,e){return(e.headers["content-type"]||"").toLowerCase().indexOf("application/json")!==-1?JSON.stringify(t,null,2):t}class Us extends Error{constructor({projectId:r}){super("CorsOriginError");m(this,"projectId");m(this,"addOriginUrl");this.name="CorsOriginError",this.projectId=r;const n=new URL(`https://sanity.io/manage/project/${r}/api`);if(typeof location<"u"){const{origin:s}=location;n.searchParams.set("cors","add"),n.searchParams.set("origin",s),this.addOriginUrl=n,this.message=`The current origin is not allowed to connect to the Live Content API. Add it here: ${n}`}else this.message=`The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${n}`}}const Ds={onResponse:t=>{if(t.statusCode>=500)throw new Ps(t);if(t.statusCode>=400)throw new Rs(t);return t}};function Ls(){const t={};return{onResponse:e=>{const r=e.headers["x-sanity-warning"],n=Array.isArray(r)?r:[r];for(const s of n)!s||t[s]||(t[s]=!0,console.warn(s));return e}}}function Nt(t){return yn([st({shouldRetry:Ns}),...t,Ls(),wn(),En(),xn(),Ds,Sn({implementation:O})])}function Ns(t,e,r){if(r.maxRetries===0)return!1;const n=r.method==="GET"||r.method==="HEAD",s=(r.uri||r.url).startsWith("/data/query"),o=t.response&&(t.response.statusCode===429||t.response.statusCode===502||t.response.statusCode===503);return(n||s)&&o?!0:st.shouldRetry(t,e,r)}const zs="https://www.sanity.io/help/";function pt(t){return zs+t}const zt=["image","file"],Bt=["before","after","replace"],_r=t=>{if(!/^(~[a-z0-9]{1}[-\w]{0,63}|[a-z0-9]{1}[-\w]{0,63})$/.test(t))throw new Error("Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters")},Bs=t=>{if(!/^[-a-z0-9]+$/i.test(t))throw new Error("`projectId` can only contain only a-z, 0-9 and dashes")},Hs=t=>{if(zt.indexOf(t)===-1)throw new Error(`Invalid asset type: ${t}. Must be one of ${zt.join(", ")}`)},ye=(t,e)=>{if(e===null||typeof e!="object"||Array.isArray(e))throw new Error(`${t}() takes an object of properties`)},jr=(t,e)=>{if(typeof e!="string"||!/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(e)||e.includes(".."))throw new Error(`${t}(): "${e}" is not a valid document ID`)},Le=(t,e)=>{if(!e._id)throw new Error(`${t}() requires that the document contains an ID ("_id" property)`);jr(t,e._id)},Vs=(t,e,r)=>{const n="insert(at, selector, items)";if(Bt.indexOf(t)===-1){const s=Bt.map(o=>`"${o}"`).join(", ");throw new Error(`${n} takes an "at"-argument which is one of: ${s}`)}if(typeof e!="string")throw new Error(`${n} takes a "selector"-argument which must be a string`);if(!Array.isArray(r))throw new Error(`${n} takes an "items"-argument which must be an array`)},Tr=t=>{if(!t.dataset)throw new Error("`dataset` must be provided to perform queries");return t.dataset||""},Rr=t=>{if(typeof t!="string"||!/^[a-z0-9._-]{1,75}$/i.test(t))throw new Error("Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.");return t},Pr=t=>{if(!t["~experimental_resource"])throw new Error("`resource` must be provided to perform resource queries");const{type:e,id:r}=t["~experimental_resource"];switch(e){case"dataset":{if(r.split(".").length!==2)throw new Error('Dataset resource ID must be in the format "project.dataset"');return}case"dashboard":case"media-library":case"canvas":return;default:throw new Error(`Unsupported resource type: ${e.toString()}`)}},k=(t,e)=>{if(e["~experimental_resource"])throw new Error(`\`${t}\` does not support resource-based operations`)};function Ws(t){let e=!1,r;return(...n)=>(e||(r=t(...n),e=!0),r)}const oe=t=>Ws((...e)=>console.warn(t.join(" "),...e)),Gs=oe(["Because you set `withCredentials` to true, we will override your `useCdn`","setting to be false since (cookie-based) credentials are never set on the CDN"]),Qs=oe(["Since you haven't set a value for `useCdn`, we will deliver content using our","global, edge-cached API-CDN. If you wish to have content delivered faster, set","`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API."]),Js=oe(["The Sanity client is configured with the `perspective` set to `drafts` or `previewDrafts`, which doesn't support the API-CDN.","The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning."]),Ys=oe(["The `previewDrafts` perspective has been renamed to  `drafts` and will be removed in a future API version"]),Xs=oe(["You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.",`See ${pt("js-client-browser-token")} for more information and how to hide this warning.`]),Zs=oe(["You have configured Sanity client to use a token, but also provided `withCredentials: true`.","This is no longer supported - only token will be used - remove `withCredentials: true`."]),Ks=oe(["Using the Sanity client without specifying an API version is deprecated.",`See ${pt("js-client-api-version")}`]),eo="apicdn.sanity.io",ve={apiHost:"https://api.sanity.io",apiVersion:"1",useProjectHostname:!0,stega:{enabled:!1}},to=["localhost","127.0.0.1","0.0.0.0"],ro=t=>to.indexOf(t)!==-1;function no(t){if(t==="1"||t==="X")return;const e=new Date(t);if(!(/^\d{4}-\d{2}-\d{2}$/.test(t)&&e instanceof Date&&e.getTime()>0))throw new Error("Invalid API version string, expected `1` or date in format `YYYY-MM-DD`")}function Fr(t){if(Array.isArray(t)&&t.length>1&&t.includes("raw"))throw new TypeError('Invalid API perspective value: "raw". The raw-perspective can not be combined with other perspectives')}const qr=(t,e)=>{const r={...e,...t,stega:{...typeof e.stega=="boolean"?{enabled:e.stega}:e.stega||ve.stega,...typeof t.stega=="boolean"?{enabled:t.stega}:t.stega||{}}};r.apiVersion||Ks();const n={...ve,...r},s=n.useProjectHostname&&!n["~experimental_resource"];if(typeof Promise>"u"){const v=pt("js-client-promise-polyfill");throw new Error(`No native Promise-implementation found, polyfill needed - see ${v}`)}if(s&&!n.projectId)throw new Error("Configuration must contain `projectId`");if(n["~experimental_resource"]&&Pr(n),typeof n.perspective<"u"&&Fr(n.perspective),"encodeSourceMap"in n)throw new Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?");if("encodeSourceMapAtPath"in n)throw new Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?");if(typeof n.stega.enabled!="boolean")throw new Error(`stega.enabled must be a boolean, received ${n.stega.enabled}`);if(n.stega.enabled&&n.stega.studioUrl===void 0)throw new Error("stega.studioUrl must be defined when stega.enabled is true");if(n.stega.enabled&&typeof n.stega.studioUrl!="string"&&typeof n.stega.studioUrl!="function")throw new Error(`stega.studioUrl must be a string or a function, received ${n.stega.studioUrl}`);const o=typeof window<"u"&&window.location&&window.location.hostname,i=o&&ro(window.location.hostname),a=!!n.token;n.withCredentials&&a&&(Zs(),n.withCredentials=!1),o&&i&&a&&n.ignoreBrowserTokenWarning!==!0?Xs():typeof n.useCdn>"u"&&Qs(),s&&Bs(n.projectId),n.dataset&&_r(n.dataset),"requestTagPrefix"in n&&(n.requestTagPrefix=n.requestTagPrefix?Rr(n.requestTagPrefix).replace(/\.+$/,""):void 0),n.apiVersion=`${n.apiVersion}`.replace(/^v/,""),n.isDefaultApi=n.apiHost===ve.apiHost,n.useCdn===!0&&n.withCredentials&&Gs(),n.useCdn=n.useCdn!==!1&&!n.withCredentials,no(n.apiVersion);const u=n.apiHost.split("://",2),c=u[0],f=u[1],d=n.isDefaultApi?eo:f;return s?(n.url=`${c}://${n.projectId}.${f}/v${n.apiVersion}`,n.cdnUrl=`${c}://${n.projectId}.${d}/v${n.apiVersion}`):(n.url=`${n.apiHost}/v${n.apiVersion}`,n.cdnUrl=n.url),n};class kr extends Error{constructor(){super(...arguments);m(this,"name","ConnectionFailedError")}}class so extends Error{constructor(r,n,s={}){super(r,s);m(this,"name","DisconnectError");m(this,"reason");this.reason=n}}class oo extends Error{constructor(r,n){super(r);m(this,"name","ChannelError");m(this,"data");this.data=n}}class io extends Error{constructor(r,n,s={}){super(r,s);m(this,"name","MessageError");m(this,"data");this.data=n}}class Ht extends Error{constructor(){super(...arguments);m(this,"name","MessageParseError")}}const ao=["channelError","disconnect"];function Mr(t,e){return Sr(()=>{const r=t();return us(r)?r:We(r)}).pipe(Pe(r=>uo(r,e)))}function uo(t,e){return new O(r=>{const n=e.includes("open"),s=e.includes("reconnect");function o(c){if("data"in c){const[f,d]=Vt(c);r.error(f?new Ht("Unable to parse EventSource error message",{cause:d}):new io((d==null?void 0:d.data).message,d));return}t.readyState===t.CLOSED?r.error(new kr("EventSource connection failed")):s&&r.next({type:"reconnect"})}function i(){r.next({type:"open"})}function a(c){var v;const[f,d]=Vt(c);if(f){r.error(new Ht("Unable to parse EventSource message",{cause:f}));return}if(c.type==="channelError"){r.error(new oo(co(d==null?void 0:d.data),d.data));return}if(c.type==="disconnect"){r.error(new so(`Server disconnected client: ${((v=d.data)==null?void 0:v.reason)||"unknown error"}`));return}r.next({type:c.type,id:c.lastEventId,...d.data?{data:d.data}:{}})}t.addEventListener("error",o),n&&t.addEventListener("open",i);const u=[...new Set([...ao,...e])].filter(c=>c!=="error"&&c!=="open"&&c!=="reconnect");return u.forEach(c=>t.addEventListener(c,a)),()=>{t.removeEventListener("error",o),n&&t.removeEventListener("open",i),u.forEach(c=>t.removeEventListener(c,a)),t.close()}})}function Vt(t){try{const e=typeof t.data=="string"&&JSON.parse(t.data);return[null,{type:t.type,id:t.lastEventId,...lo(e)?{}:{data:e}}]}catch(e){return[e,null]}}function co(t){return t.error?t.error.description?t.error.description:typeof t.error=="string"?t.error:JSON.stringify(t.error,null,2):t.message||"Unknown listener error"}function lo(t){for(const e in t)return!1;return!0}function Ur(t){if(typeof t=="string")return{id:t};if(Array.isArray(t))return{query:"*[_id in $ids]",params:{ids:t}};if(typeof t=="object"&&t!==null&&"query"in t&&typeof t.query=="string")return"params"in t&&typeof t.params=="object"&&t.params!==null?{query:t.query,params:t.params}:{query:t.query};const e=["* Document ID (<docId>)","* Array of document IDs","* Object containing `query`"].join(`
`);throw new Error(`Unknown selection - must be one of:

${e}`)}class Dr{constructor(e,r={}){m(this,"selection");m(this,"operations");this.selection=e,this.operations=r}set(e){return this._assign("set",e)}setIfMissing(e){return this._assign("setIfMissing",e)}diffMatchPatch(e){return ye("diffMatchPatch",e),this._assign("diffMatchPatch",e)}unset(e){if(!Array.isArray(e))throw new Error("unset(attrs) takes an array of attributes to unset, non-array given");return this.operations=Object.assign({},this.operations,{unset:e}),this}inc(e){return this._assign("inc",e)}dec(e){return this._assign("dec",e)}insert(e,r,n){return Vs(e,r,n),this._assign("insert",{[e]:r,items:n})}append(e,r){return this.insert("after",`${e}[-1]`,r)}prepend(e,r){return this.insert("before",`${e}[0]`,r)}splice(e,r,n,s){const o=typeof n>"u"||n===-1,i=r<0?r-1:r,a=o?-1:Math.max(0,r+n),u=i<0&&a>=0?"":a,c=`${e}[${i}:${u}]`;return this.insert("replace",c,s||[])}ifRevisionId(e){return this.operations.ifRevisionID=e,this}serialize(){return{...Ur(this.selection),...this.operations}}toJSON(){return this.serialize()}reset(){return this.operations={},this}_assign(e,r,n=!0){return ye(e,r),this.operations=Object.assign({},this.operations,{[e]:Object.assign({},n&&this.operations[e]||{},r)}),this}_set(e,r){return this._assign(e,r,!1)}}var te;const mt=class mt extends Dr{constructor(r,n,s){super(r,n);C(this,te);E(this,te,s)}clone(){return new mt(this.selection,{...this.operations},l(this,te))}commit(r){if(!l(this,te))throw new Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");const n=typeof this.selection=="string",s=Object.assign({returnFirst:n,returnDocuments:!0},r);return l(this,te).mutate({patch:this.serialize()},s)}};te=new WeakMap;let se=mt;var re;const bt=class bt extends Dr{constructor(r,n,s){super(r,n);C(this,re);E(this,re,s)}clone(){return new bt(this.selection,{...this.operations},l(this,re))}commit(r){if(!l(this,re))throw new Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");const n=typeof this.selection=="string",s=Object.assign({returnFirst:n,returnDocuments:!0},r);return l(this,re).mutate({patch:this.serialize()},s)}};re=new WeakMap;let K=bt;const Lr={returnDocuments:!1};class Nr{constructor(e=[],r){m(this,"operations");m(this,"trxId");this.operations=e,this.trxId=r}create(e){return ye("create",e),this._add({create:e})}createIfNotExists(e){const r="createIfNotExists";return ye(r,e),Le(r,e),this._add({[r]:e})}createOrReplace(e){const r="createOrReplace";return ye(r,e),Le(r,e),this._add({[r]:e})}delete(e){return jr("delete",e),this._add({delete:{id:e}})}transactionId(e){return e?(this.trxId=e,this):this.trxId}serialize(){return[...this.operations]}toJSON(){return this.serialize()}reset(){return this.operations=[],this}_add(e){return this.operations.push(e),this}}var L;const gt=class gt extends Nr{constructor(r,n,s){super(r,s);C(this,L);E(this,L,n)}clone(){return new gt([...this.operations],l(this,L),this.trxId)}commit(r){if(!l(this,L))throw new Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return l(this,L).mutate(this.serialize(),Object.assign({transactionId:this.trxId},Lr,r||{}))}patch(r,n){const s=typeof n=="function",o=typeof r!="string"&&r instanceof K,i=typeof r=="object"&&("query"in r||"id"in r);if(o)return this._add({patch:r.serialize()});if(s){const a=n(new K(r,{},l(this,L)));if(!(a instanceof K))throw new Error("function passed to `patch()` must return the patch");return this._add({patch:a.serialize()})}if(i){const a=new K(r,n||{},l(this,L));return this._add({patch:a.serialize()})}return this._add({patch:{id:r,...n}})}};L=new WeakMap;let Ne=gt;var W;const wt=class wt extends Nr{constructor(r,n,s){super(r,s);C(this,W);E(this,W,n)}clone(){return new wt([...this.operations],l(this,W),this.trxId)}commit(r){if(!l(this,W))throw new Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return l(this,W).mutate(this.serialize(),Object.assign({transactionId:this.trxId},Lr,r||{}))}patch(r,n){const s=typeof n=="function";if(typeof r!="string"&&r instanceof se)return this._add({patch:r.serialize()});if(s){const o=n(new se(r,{},l(this,W)));if(!(o instanceof se))throw new Error("function passed to `patch()` must return the patch");return this._add({patch:o.serialize()})}return this._add({patch:{id:r,...n}})}};W=new WeakMap;let ze=wt;const fo="X-Sanity-Project-ID";function ho(t,e={}){const r={},n=e.token||t.token;n&&(r.Authorization=`Bearer ${n}`),!e.useGlobalApi&&!t.useProjectHostname&&t.projectId&&(r[fo]=t.projectId);const s=!!(typeof e.withCredentials>"u"?t.withCredentials:e.withCredentials),o=typeof e.timeout>"u"?t.timeout:e.timeout;return Object.assign({},e,{headers:Object.assign({},r,e.headers||{}),timeout:typeof o>"u"?5*60*1e3:o,proxy:e.proxy||t.proxy,json:!0,withCredentials:s,fetch:typeof e.fetch=="object"&&typeof t.fetch=="object"?{...t.fetch,...e.fetch}:e.fetch||t.fetch})}const zr=({query:t,params:e={},options:r={}})=>{const n=new URLSearchParams,{tag:s,includeMutations:o,returnQuery:i,...a}=r;s&&n.append("tag",s),n.append("query",t);for(const[u,c]of Object.entries(e))n.append(`$${u}`,JSON.stringify(c));for(const[u,c]of Object.entries(a))c&&n.append(u,`${c}`);return i===!1&&n.append("returnQuery","false"),o===!1&&n.append("includeMutations","false"),`?${n}`},po=(t,e)=>t===!1?void 0:typeof t>"u"?e:t,yo=(t={})=>({dryRun:t.dryRun,returnIds:!0,returnDocuments:po(t.returnDocuments,!0),visibility:t.visibility||"sync",autoGenerateArrayKeys:t.autoGenerateArrayKeys,skipCrossDatasetReferenceValidation:t.skipCrossDatasetReferenceValidation}),yt=t=>t.type==="response",vo=t=>t.body,mo=(t,e)=>t.reduce((r,n)=>(r[e(n)]=n,r),Object.create(null)),bo=11264;function Br(t,e,r,n,s={},o={}){const i="stega"in o?{...r||{},...typeof o.stega=="boolean"?{enabled:o.stega}:o.stega||{}}:r,a=i.enabled?Ts(s):s,u=o.filterResponse===!1?y=>y:y=>y.result,{cache:c,next:f,...d}={useAbortSignal:typeof o.signal<"u",resultSourceMap:i.enabled?"withKeyArraySelector":o.resultSourceMap,...o,returnQuery:o.filterResponse===!1&&o.returnQuery!==!1},v=typeof c<"u"||typeof f<"u"?{...d,fetch:{cache:c,next:f}}:d,h=de(t,e,"query",{query:n,params:a},v);return i.enabled?h.pipe(Es(Re(Qt(()=>import("./QP0wKkFK.js"),[],import.meta.url).then(function(y){return y.stegaEncodeSourceMap$1}).then(({stegaEncodeSourceMap:y})=>y))),j(([y,p])=>{const g=p(y.result,y.resultSourceMap,i);return u({...y,result:g})})):h.pipe(j(u))}function Hr(t,e,r,n={}){const s={uri:F(t,"doc",r),json:!0,tag:n.tag,signal:n.signal};return qe(t,e,s).pipe(he(yt),j(o=>o.body.documents&&o.body.documents[0]))}function Vr(t,e,r,n={}){const s={uri:F(t,"doc",r.join(",")),json:!0,tag:n.tag,signal:n.signal};return qe(t,e,s).pipe(he(yt),j(o=>{const i=mo(o.body.documents||[],a=>a._id);return r.map(a=>i[a]||null)}))}function Wr(t,e,r,n){return Le("createIfNotExists",r),Ge(t,e,r,"createIfNotExists",n)}function Gr(t,e,r,n){return Le("createOrReplace",r),Ge(t,e,r,"createOrReplace",n)}function Qr(t,e,r,n){return de(t,e,"mutate",{mutations:[{delete:Ur(r)}]},n)}function Jr(t,e,r,n){let s;r instanceof K||r instanceof se?s={patch:r.serialize()}:r instanceof Ne||r instanceof ze?s=r.serialize():s=r;const o=Array.isArray(s)?s:[s],i=n&&n.transactionId||void 0;return de(t,e,"mutate",{mutations:o,transactionId:i},n)}function Yr(t,e,r,n){const s=Array.isArray(r)?r:[r],o=n&&n.transactionId||void 0,i=n&&n.skipCrossDatasetReferenceValidation||void 0,a=n&&n.dryRun||void 0;return de(t,e,"actions",{actions:s,transactionId:o,skipCrossDatasetReferenceValidation:i,dryRun:a},n)}function de(t,e,r,n,s={}){const o=r==="mutate",i=r==="actions",a=r==="query",u=o||i?"":zr(n),c=!o&&!i&&u.length<bo,f=c?u:"",d=s.returnFirst,{timeout:v,token:h,tag:y,headers:p,returnQuery:g,lastLiveEventId:w,cacheMode:b}=s,S=F(t,r,f),A={method:c?"GET":"POST",uri:S,json:!0,body:c?void 0:n,query:o&&yo(s),timeout:v,headers:p,token:h,tag:y,returnQuery:g,perspective:s.perspective,resultSourceMap:s.resultSourceMap,lastLiveEventId:Array.isArray(w)?w[0]:w,cacheMode:b,canUseCdn:a,signal:s.signal,fetch:s.fetch,useAbortSignal:s.useAbortSignal,useCdn:s.useCdn};return qe(t,e,A).pipe(he(yt),j(vo),j(T=>{if(!o)return T;const R=T.results||[];if(s.returnDocuments)return d?R[0]&&R[0].document:R.map(Qe=>Qe.document);const nn=d?"documentId":"documentIds",sn=d?R[0]&&R[0].id:R.map(Qe=>Qe.id);return{transactionId:T.transactionId,results:R,[nn]:sn}}))}function Ge(t,e,r,n,s={}){const o={[n]:r},i=Object.assign({returnFirst:!0,returnDocuments:!0},s);return de(t,e,"mutate",{mutations:[o]},i)}const Fe=t=>t.config().dataset!==void 0&&t.config().projectId!==void 0||t.config()["~experimental_resource"]!==void 0,Xr=(t,e)=>Fe(t)&&e.startsWith(F(t,"query")),go=(t,e)=>Fe(t)&&e.startsWith(F(t,"mutate")),wo=(t,e)=>Fe(t)&&e.startsWith(F(t,"doc","")),Eo=(t,e)=>Fe(t)&&e.startsWith(F(t,"listen")),Co=(t,e)=>Fe(t)&&e.startsWith(F(t,"history","")),So=(t,e)=>e.startsWith("/data/")||Xr(t,e)||go(t,e)||wo(t,e)||Eo(t,e)||Co(t,e);function qe(t,e,r){const n=r.url||r.uri,s=t.config(),o=typeof r.canUseCdn>"u"?["GET","HEAD"].indexOf(r.method||"GET")>=0&&So(t,n):r.canUseCdn;let i=(r.useCdn??s.useCdn)&&o;const a=r.tag&&s.requestTagPrefix?[s.requestTagPrefix,r.tag].join("."):r.tag||s.requestTagPrefix;if(a&&r.tag!==null&&(r.query={tag:Rr(a),...r.query}),["GET","HEAD","POST"].indexOf(r.method||"GET")>=0&&Xr(t,n)){const f=r.resultSourceMap??s.resultSourceMap;f!==void 0&&f!==!1&&(r.query={resultSourceMap:f,...r.query});const d=r.perspective||s.perspective;typeof d<"u"&&(d==="previewDrafts"&&Ys(),Fr(d),r.query={perspective:Array.isArray(d)?d.join(","):d,...r.query},(Array.isArray(d)&&d.length>0||d==="previewDrafts"||d==="drafts")&&i&&(i=!1,Js())),r.lastLiveEventId&&(r.query={...r.query,lastLiveEventId:r.lastLiveEventId}),r.returnQuery===!1&&(r.query={returnQuery:"false",...r.query}),i&&r.cacheMode=="noStale"&&(r.query={cacheMode:"noStale",...r.query})}const u=ho(s,Object.assign({},r,{url:vt(t,n,i)})),c=new O(f=>e(u,s.requester).subscribe(f));return r.signal?c.pipe(xo(r.signal)):c}function q(t,e,r){return qe(t,e,r).pipe(he(n=>n.type==="response"),j(n=>n.body))}function F(t,e,r){const n=t.config();if(n["~experimental_resource"]){Pr(n);const i=Io(n),a=r!==void 0?`${e}/${r}`:e;return`${i}/${a}`.replace(/\/($|\?)/,"$1")}const s=Tr(n),o=`/${e}/${s}`;return`/data${r!==void 0?`${o}/${r}`:o}`.replace(/\/($|\?)/,"$1")}function vt(t,e,r=!1){const{url:n,cdnUrl:s}=t.config();return`${r?s:n}/${e.replace(/^\//,"")}`}function xo(t){return e=>new O(r=>{const n=()=>r.error(Oo(t));if(t&&t.aborted){n();return}const s=e.subscribe(r);return t.addEventListener("abort",n),()=>{t.removeEventListener("abort",n),s.unsubscribe()}})}const Ao=!!globalThis.DOMException;function Oo(t){if(Ao)return new DOMException((t==null?void 0:t.reason)??"The operation was aborted.","AbortError");const e=new Error((t==null?void 0:t.reason)??"The operation was aborted.");return e.name="AbortError",e}const Io=t=>{if(!t["~experimental_resource"])throw new Error("`resource` must be provided to perform resource queries");const{type:e,id:r}=t["~experimental_resource"];switch(e){case"dataset":{const n=r.split(".");if(n.length!==2)throw new Error('Dataset ID must be in the format "project.dataset"');return`/projects/${n[0]}/datasets/${n[1]}`}case"canvas":return`/canvases/${r}`;case"media-library":return`/media-libraries/${r}`;case"dashboard":return`/dashboards/${r}`;default:throw new Error(`Unsupported resource type: ${e.toString()}`)}};var Se,xe;class $o{constructor(e,r){C(this,Se);C(this,xe);E(this,Se,e),E(this,xe,r)}upload(e,r,n){return Zr(l(this,Se),l(this,xe),e,r,n)}}Se=new WeakMap,xe=new WeakMap;var Ae,Oe;class _o{constructor(e,r){C(this,Ae);C(this,Oe);E(this,Ae,e),E(this,Oe,r)}upload(e,r,n){const s=Zr(l(this,Ae),l(this,Oe),e,r,n);return _(s.pipe(he(o=>o.type==="response"),j(o=>o.body.document)))}}Ae=new WeakMap,Oe=new WeakMap;function Zr(t,e,r,n,s={}){Hs(r);let o=s.extract||void 0;o&&!o.length&&(o=["none"]);const i=t.config(),a=To(s,n),{tag:u,label:c,title:f,description:d,creditLine:v,filename:h,source:y}=a,p={label:c,title:f,description:d,filename:h,meta:o,creditLine:v};return y&&(p.sourceId=y.id,p.sourceName=y.name,p.sourceUrl=y.url),qe(t,e,{tag:u,method:"POST",timeout:a.timeout||0,uri:jo(i,r),headers:a.contentType?{"Content-Type":a.contentType}:{},query:p,body:n})}function jo(t,e){const r=e==="image"?"images":"files";if(t["~experimental_resource"]){const{type:s,id:o}=t["~experimental_resource"];switch(s){case"dataset":throw new Error("Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.");case"canvas":return`/canvases/${o}/assets/${r}`;case"media-library":return`/media-libraries/${o}/upload`;case"dashboard":return`/dashboards/${o}/assets/${r}`;default:throw new Error(`Unsupported resource type: ${s.toString()}`)}}const n=Tr(t);return`assets/${r}/${n}`}function To(t,e){return typeof File>"u"||!(e instanceof File)?t:Object.assign({filename:t.preserveFilename===!1?void 0:e.name,contentType:e.type},t)}var Ro=(t,e)=>Object.keys(e).concat(Object.keys(t)).reduce((r,n)=>(r[n]=typeof t[n]>"u"?e[n]:t[n],r),{});const Po=(t,e)=>e.reduce((r,n)=>(typeof t[n]>"u"||(r[n]=t[n]),r),{}),Kr=Sr(()=>Qt(()=>import("./Uxihc9oX.js").then(t=>t.b),__vite__mapDeps([0,1]),import.meta.url)).pipe(j(({default:t})=>t),Cs(1));function en(){return function(t){return t.pipe(ht((e,r)=>e instanceof kr?Cr(We({type:"reconnect"}),ms(1e3).pipe(Pe(()=>r))):wr(()=>e)))}}const Fo=14800,qo=["includePreviousRevision","includeResult","includeMutations","includeAllVersions","visibility","effectFormat","tag"],ko={includeResult:!0};function tn(t,e,r={}){const{url:n,token:s,withCredentials:o,requestTagPrefix:i}=this.config(),a=r.tag&&i?[i,r.tag].join("."):r.tag,u={...Ro(r,ko),tag:a},c=Po(u,qo),f=zr({query:t,params:e,options:{tag:a,...c}}),d=`${n}${F(this,"listen",f)}`;if(d.length>Fo)return wr(()=>new Error("Query too large for listener"));const v=u.events?u.events:["mutation"],h={};return o&&(h.withCredentials=!0),s&&(h.headers={Authorization:`Bearer ${s}`}),Mr(()=>(typeof EventSource>"u"||h.headers?Kr:We(EventSource)).pipe(j(y=>new y(d,h))),v).pipe(en(),he(y=>v.includes(y.type)),j(y=>({type:y.type,..."data"in y?y.data:{}})))}function Mo(t,e){return Uo(typeof t=="function"?{predicate:t,...e}:t)}function Uo(t){return e=>{let r,n=!1;const{predicate:s,...o}=t,i=e.pipe(Ss(u=>{t.predicate(u)&&(n=!0,r=u)}),Ar(()=>{n=!1,r=void 0}),Or(o)),a=new O(u=>{n&&u.next(r),u.complete()});return bs(i,a)}}const Wt="2021-03-25";var G;class rn{constructor(e){C(this,G);E(this,G,e)}events({includeDrafts:e=!1,tag:r}={}){k("live",l(this,G).config());const{projectId:n,apiVersion:s,token:o,withCredentials:i,requestTagPrefix:a}=l(this,G).config(),u=s.replace(/^v/,"");if(u!=="X"&&u<Wt)throw new Error(`The live events API requires API version ${Wt} or later. The current API version is ${u}. Please update your API version to use this feature.`);if(e&&!o&&!i)throw new Error("The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.");const c=F(l(this,G),"live/events"),f=new URL(l(this,G).getUrl(c,!1)),d=r&&a?[a,r].join("."):r;d&&f.searchParams.set("tag",d),e&&f.searchParams.set("includeDrafts","true");const v={};e&&o&&(v.headers={Authorization:`Bearer ${o}`}),e&&i&&(v.withCredentials=!0);const h=`${f.href}::${JSON.stringify(v)}`,y=Ze.get(h);if(y)return y;const p=Mr(()=>(typeof EventSource>"u"||v.headers?Kr:We(EventSource)).pipe(j(b=>new b(f.href,v))),["message","restart","welcome","reconnect","goaway"]).pipe(en(),j(b=>{if(b.type==="message"){const{data:S,...A}=b;return{...A,tags:S.tags}}return b})),g=Do(f,{method:"OPTIONS",mode:"cors",credentials:v.withCredentials?"include":"omit",headers:v.headers}).pipe(Pe(()=>ir),ht(()=>{throw new Us({projectId:n})})),w=Cr(g,p).pipe(Ar(()=>Ze.delete(h)),Mo({predicate:b=>b.type==="welcome"}));return Ze.set(h,w),w}}G=new WeakMap;function Do(t,e){return new O(r=>{const n=new AbortController,s=n.signal;return fetch(t,{...e,signal:n.signal}).then(o=>{r.next(o),r.complete()},o=>{s.aborted||r.error(o)}),()=>n.abort()})}const Ze=new Map;var Q,J;class Lo{constructor(e,r){C(this,Q);C(this,J);E(this,Q,e),E(this,J,r)}create(e,r){return ae(l(this,Q),l(this,J),"PUT",e,r)}edit(e,r){return ae(l(this,Q),l(this,J),"PATCH",e,r)}delete(e){return ae(l(this,Q),l(this,J),"DELETE",e)}list(){return q(l(this,Q),l(this,J),{uri:"/datasets",tag:null})}}Q=new WeakMap,J=new WeakMap;var P,Y;class No{constructor(e,r){C(this,P);C(this,Y);E(this,P,e),E(this,Y,r)}create(e,r){return k("dataset",l(this,P).config()),_(ae(l(this,P),l(this,Y),"PUT",e,r))}edit(e,r){return k("dataset",l(this,P).config()),_(ae(l(this,P),l(this,Y),"PATCH",e,r))}delete(e){return k("dataset",l(this,P).config()),_(ae(l(this,P),l(this,Y),"DELETE",e))}list(){return k("dataset",l(this,P).config()),_(q(l(this,P),l(this,Y),{uri:"/datasets",tag:null}))}}P=new WeakMap,Y=new WeakMap;function ae(t,e,r,n,s){return k("dataset",t.config()),_r(n),q(t,e,{method:r,uri:`/datasets/${n}`,body:s,tag:null})}var X,ce;class zo{constructor(e,r){C(this,X);C(this,ce);E(this,X,e),E(this,ce,r)}list(e){k("projects",l(this,X).config());const r=(e==null?void 0:e.includeMembers)===!1?"/projects?includeMembers=false":"/projects";return q(l(this,X),l(this,ce),{uri:r})}getById(e){return k("projects",l(this,X).config()),q(l(this,X),l(this,ce),{uri:`/projects/${e}`})}}X=new WeakMap,ce=new WeakMap;var Z,le;class Bo{constructor(e,r){C(this,Z);C(this,le);E(this,Z,e),E(this,le,r)}list(e){k("projects",l(this,Z).config());const r=(e==null?void 0:e.includeMembers)===!1?"/projects?includeMembers=false":"/projects";return _(q(l(this,Z),l(this,le),{uri:r}))}getById(e){return k("projects",l(this,Z).config()),_(q(l(this,Z),l(this,le),{uri:`/projects/${e}`}))}}Z=new WeakMap,le=new WeakMap;var Ie,$e;class Ho{constructor(e,r){C(this,Ie);C(this,$e);E(this,Ie,e),E(this,$e,r)}getById(e){return q(l(this,Ie),l(this,$e),{uri:`/users/${e}`})}}Ie=new WeakMap,$e=new WeakMap;var _e,je;class Vo{constructor(e,r){C(this,_e);C(this,je);E(this,_e,e),E(this,je,r)}getById(e){return _(q(l(this,_e),l(this,je),{uri:`/users/${e}`}))}}_e=new WeakMap,je=new WeakMap;var N,$;const Be=class Be{constructor(e,r=ve){m(this,"assets");m(this,"datasets");m(this,"live");m(this,"projects");m(this,"users");C(this,N);C(this,$);m(this,"listen",tn);this.config(r),E(this,$,e),this.assets=new $o(this,l(this,$)),this.datasets=new Lo(this,l(this,$)),this.live=new rn(this),this.projects=new zo(this,l(this,$)),this.users=new Ho(this,l(this,$))}clone(){return new Be(l(this,$),this.config())}config(e){if(e===void 0)return{...l(this,N)};if(l(this,N)&&l(this,N).allowReconfigure===!1)throw new Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return E(this,N,qr(e,l(this,N)||{})),this}withConfig(e){const r=this.config();return new Be(l(this,$),{...r,...e,stega:{...r.stega||{},...typeof(e==null?void 0:e.stega)=="boolean"?{enabled:e.stega}:(e==null?void 0:e.stega)||{}}})}fetch(e,r,n){return Br(this,l(this,$),l(this,N).stega,e,r,n)}getDocument(e,r){return Hr(this,l(this,$),e,r)}getDocuments(e,r){return Vr(this,l(this,$),e,r)}create(e,r){return Ge(this,l(this,$),e,"create",r)}createIfNotExists(e,r){return Wr(this,l(this,$),e,r)}createOrReplace(e,r){return Gr(this,l(this,$),e,r)}delete(e,r){return Qr(this,l(this,$),e,r)}mutate(e,r){return Jr(this,l(this,$),e,r)}patch(e,r){return new se(e,r,this)}transaction(e){return new ze(e,this)}action(e,r){return Yr(this,l(this,$),e,r)}request(e){return q(this,l(this,$),e)}getUrl(e,r){return vt(this,e,r)}getDataUrl(e,r){return F(this,e,r)}};N=new WeakMap,$=new WeakMap;let rt=Be;var z,I;const He=class He{constructor(e,r=ve){m(this,"assets");m(this,"datasets");m(this,"live");m(this,"projects");m(this,"users");m(this,"observable");C(this,z);C(this,I);m(this,"listen",tn);this.config(r),E(this,I,e),this.assets=new _o(this,l(this,I)),this.datasets=new No(this,l(this,I)),this.live=new rn(this),this.projects=new Bo(this,l(this,I)),this.users=new Vo(this,l(this,I)),this.observable=new rt(e,r)}clone(){return new He(l(this,I),this.config())}config(e){if(e===void 0)return{...l(this,z)};if(l(this,z)&&l(this,z).allowReconfigure===!1)throw new Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.observable&&this.observable.config(e),E(this,z,qr(e,l(this,z)||{})),this}withConfig(e){const r=this.config();return new He(l(this,I),{...r,...e,stega:{...r.stega||{},...typeof(e==null?void 0:e.stega)=="boolean"?{enabled:e.stega}:(e==null?void 0:e.stega)||{}}})}fetch(e,r,n){return _(Br(this,l(this,I),l(this,z).stega,e,r,n))}getDocument(e,r){return _(Hr(this,l(this,I),e,r))}getDocuments(e,r){return _(Vr(this,l(this,I),e,r))}create(e,r){return _(Ge(this,l(this,I),e,"create",r))}createIfNotExists(e,r){return _(Wr(this,l(this,I),e,r))}createOrReplace(e,r){return _(Gr(this,l(this,I),e,r))}delete(e,r){return _(Qr(this,l(this,I),e,r))}mutate(e,r){return _(Jr(this,l(this,I),e,r))}patch(e,r){return new K(e,r,this)}transaction(e){return new Ne(e,this)}action(e,r){return _(Yr(this,l(this,I),e,r))}request(e){return _(q(this,l(this,I),e))}dataRequest(e,r,n){return _(de(this,l(this,I),e,r,n))}getUrl(e,r){return vt(this,e,r)}getDataUrl(e,r){return F(this,e,r)}};z=new WeakMap,I=new WeakMap;let nt=He;function Wo(t,e){return{requester:Nt(t),createClient:r=>{const n=Nt(t);return new e((s,o)=>(o||n)({maxRedirects:0,maxRetries:r.maxRetries,retryDelay:r.retryDelay,...s}),r)}}}var Go=[];const Qo=Wo(Go,nt),Ko=Qo.createClient;export{Zo as C,Ko as c};
