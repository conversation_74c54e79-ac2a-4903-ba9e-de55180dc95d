import{c as n,a as d}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as h}from"./BBa424ah.js";import{l,s as m}from"./Btcx8l8F.js";import{I as c}from"./D4f2twK-.js";function y(s,t){const a=l(t,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M3 12h.01"}],["path",{d:"M3 18h.01"}],["path",{d:"M3 6h.01"}],["path",{d:"M8 12h13"}],["path",{d:"M8 18h13"}],["path",{d:"M8 6h13"}]];c(s,m({name:"list"},()=>a,{get iconNode(){return r},children:(e,f)=>{var o=n(),p=i(o);h(p,t,"default",{},null),d(e,o)},$$slots:{default:!0}}))}export{y as L};
