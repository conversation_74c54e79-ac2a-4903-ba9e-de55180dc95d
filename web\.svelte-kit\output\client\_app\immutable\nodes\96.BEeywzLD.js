import{f as b,a as l,c as w}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as S,t as P,a as C,c as a,s as i,n as V,r as n,f as y,g as q}from"../chunks/CGmarHxI.js";import{i as z}from"../chunks/BIEMS98f.js";import{p as G}from"../chunks/Btcx8l8F.js";import{S as J}from"../chunks/C6g8ubaU.js";import{s as N}from"../chunks/CIt1g2O9.js";import{i as K}from"../chunks/u21ee2wt.js";import{e as L,i as Q}from"../chunks/C3w0v0gR.js";import{c as R}from"../chunks/BvdI7LR8.js";import{e as Y}from"../chunks/CmxjS0TN.js";import{g as k}from"../chunks/BiJhC7W5.js";import{A as W}from"../chunks/BPr9JIwg.js";import{M as X}from"../chunks/CcFQTcQh.js";import{C as Z}from"../chunks/BBNNmnYR.js";import{C as $}from"../chunks/DkmCSZhC.js";var tt=b('<div class="mt-8 rounded-lg border p-8 text-center"><p class="text-muted-foreground"> </p></div>'),et=b('<div class="flex flex-col gap-4"><div class="flex items-center justify-between px-4"><button class="flex items-center gap-1 rounded-md p-2 hover:bg-gray-100 dark:hover:bg-gray-800" aria-label="Previous month"><!> <span>Previous</span></button> <h2 class="text-xl font-semibold"> </h2> <button class="flex items-center gap-1 rounded-md p-2 hover:bg-gray-100 dark:hover:bg-gray-800" aria-label="Next month"><span>Next</span> <!></button></div> <div class="px-4 pb-8"><!></div></div>');function rt(h,e){S(e,!0);function o(t,r){return new Intl.DateTimeFormat("en-US",{month:"long",year:"numeric"}).format(new Date(r,t-1,1))}function d(){let t=e.currentMonth-1,r=e.currentYear;t<1&&(t=12,r--),k(`/system-status/history?month=${t}&year=${r}`)}function v(){let t=e.currentMonth+1,r=e.currentYear;t>12&&(t=1,r++),k(`/system-status/history?month=${t}&year=${r}`)}var s=et(),c=a(s),m=a(c),H=a(m);Z(H,{class:"h-4 w-4"}),V(2),n(m);var g=i(m,2),A=a(g,!0);n(g);var f=i(g,2),T=i(a(f),2);$(T,{class:"h-4 w-4"}),n(f),n(c);var _=i(c,2),j=a(_);{var D=t=>{var r=w(),u=y(r);R(u,()=>W,(x,p)=>{p(x,{type:"multiple",class:"w-full space-y-4",children:(E,nt)=>{var M=w(),F=y(M);L(F,17,()=>e.maintenance,Q,(I,O,U)=>{X(I,{get incident(){return q(O)},index:U})}),l(E,M)},$$slots:{default:!0}})}),l(t,r)},B=t=>{var r=tt(),u=a(r),x=a(u);n(u),n(r),P(p=>N(x,`No notices or maintenance events for ${p??""}`),[()=>o(e.currentMonth,e.currentYear)]),l(t,r)};K(j,t=>{e.maintenance&&e.maintenance.length>0?t(D):t(B,!1)})}n(_),n(s),P(t=>{m.disabled=!e.hasPrevMonth,N(A,t),f.disabled=!e.hasNextMonth},[()=>o(e.currentMonth,e.currentYear)]),Y("click",m,d),Y("click",f,v),l(h,s),C()}var at=b('<!> <div class="flex flex-col gap-4"><div class="border-border flex items-center justify-between border-b p-6"><div class="flex flex-col"><h1 class="text-3xl font-bold">Notice History</h1> <p class="text-muted-foreground">Past system notices and maintenance events</p></div> <div><a href="/system-status" class="text-primary hover:underline">Back to Status Page</a></div></div> <!></div>',1);function _t(h,e){S(e,!1);let o=G(e,"data",8);z();var d=at(),v=y(d);J(v,{title:"System Status History | Hirli",description:"View the history of system status notices and maintenance events.",keywords:"system status, maintenance history, incident history, Hirli status"});var s=i(v,2),c=i(a(s),2);rt(c,{get maintenance(){return o().maintenance},get currentMonth(){return o().currentMonth},get currentYear(){return o().currentYear},get hasNextMonth(){return o().hasNextMonth},get hasPrevMonth(){return o().hasPrevMonth}}),n(s),l(h,d),C()}export{_t as component};
