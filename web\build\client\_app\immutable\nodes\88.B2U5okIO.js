import{f as u,c as Et,a,t as m}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as mt,c as l,s as n,r as o,f as A,g as e,e as I,n as L,t as F,a as ut,m as H,w as Ft,l as it,b as Bt,d as h}from"../chunks/CGmarHxI.js";import{e as pt,i as Ot}from"../chunks/C3w0v0gR.js";import{s as gt,a as Jt}from"../chunks/B-Xjo-Yt.js";import{i as _t}from"../chunks/BIEMS98f.js";import{p as P}from"../chunks/Btcx8l8F.js";import{a as Mt,s as Nt}from"../chunks/CmxjS0TN.js";import{S as Gt}from"../chunks/C6g8ubaU.js";import{s as E}from"../chunks/CIt1g2O9.js";import{i as v}from"../chunks/u21ee2wt.js";import{B as nt}from"../chunks/B1K98fMG.js";import{S as lt}from"../chunks/0ykhD7u6.js";import{L as qt}from"../chunks/BhzFx1Wy.js";import{C as zt}from"../chunks/BNEH2jqx.js";import{I as Ht}from"../chunks/BuYRPDDz.js";import{X as Kt}from"../chunks/CnpHcmx3.js";import{S as Vt}from"../chunks/D9yI7a4E.js";import{R as Wt,P as Xt,D as Yt,a as Qt}from"../chunks/tdzGgazS.js";import{S as Zt}from"../chunks/B0MU434M.js";import{D as $t}from"../chunks/CodWuqwu.js";import{D as er,a as tr,b as rr,c as ar}from"../chunks/CKh8VGVX.js";var sr=u('<div class="absolute inset-x-0 top-0 -translate-y-1/2 transform"><div class="bg-primary text-primary-foreground inline-block rounded-full px-4 py-1 text-xs font-semibold uppercase tracking-wider">Most Popular</div></div>'),or=u('<span class="mr-1 align-super text-sm">$</span> <span class="text-lg">/ month</span>',1),ir=u("<span>/ seat</span>"),lr=u('<span class="text-muted-foreground mt-2 text-sm"> </span>'),nr=u("<!> Processing...",1),dr=u('10<span class="ml-1 text-xs font-normal">/mo</span>',1),cr=u(' <span class="ml-1 text-xs font-normal">/mo</span>',1),vr=u('<span class="text-lg">∞</span>'),fr=u('<span class="text-lg">∞</span>'),mr=u('5<span class="ml-1 text-xs font-normal">/mo</span>',1),ur=u('<span class="text-lg">∞</span>'),pr=u('<div class="flex items-center"><span class="mr-1 text-xs text-amber-500">Limited</span> <div class="tooltip-wrapper svelte-12270ft"><!> <div class="tooltip svelte-12270ft"><p class="text-xs">This feature has usage limits based on your plan</p></div></div></div>'),gr=u('<tr class="border-border border-t"><td class="text-foreground py-3 pr-4"><!></td><td class="py-3 pl-4 text-right"><!></td></tr>'),_r=u('<div><!> <div class="p-7"><h3 class="text-foreground text-3xl font-semibold"> </h3> <p class="text-muted-foreground mt-4 h-12"> </p></div> <!> <div class="align-center mt-4 flex min-h-20 flex-col justify-center text-center"><div><span class="font-semi-bold text-5xl"><!></span> <!></div> <!> <div class="mt-6 px-8"><!></div></div> <!> <div class="mt-6 px-8"><div class="min-h-[300px]"><div class="mb-4"><h4 class="text-muted-foreground mb-2 text-xs font-medium uppercase">Plan Limits</h4> <div class="grid grid-cols-1 gap-2 sm:grid-cols-3"><div class="rounded-md border p-3 text-center"><p class="text-muted-foreground mb-1 text-xs">Resume Scans</p> <p class="flex items-center justify-center text-lg font-semibold"><!></p></div> <div class="rounded-md border p-3 text-center"><p class="text-muted-foreground mb-1 text-xs">Job Profiles</p> <p class="flex items-center justify-center text-lg font-semibold"><!></p></div> <div class="rounded-md border p-3 text-center"><p class="text-muted-foreground mb-1 text-xs"><!></p> <p class="flex items-center justify-center text-lg font-semibold"><!></p></div></div></div> <div><h4 class="text-muted-foreground mb-2 text-xs font-medium uppercase">Key Features</h4> <table class="w-full table-auto border-collapse text-sm"><tbody></tbody></table></div></div></div> <!></div>');function xr(Ne,g){mt(g,!1);let T=P(g,"title",8),Ge=P(g,"price",8),ne=P(g,"description",8),U=P(g,"isPopular",8,!1),O=P(g,"ctaText",8),j=P(g,"billingCycle",8),_=P(g,"limits",24,()=>({})),qe=P(g,"features",24,()=>[]),J=P(g,"onCtaClick",8),_e=P(g,"disabled",8,!1),K=P(g,"activePlan",8,!1),M=P(g,"loading",8,!1);_t();var w=_r(),D=l(w);{var de=t=>{var r=sr();a(t,r)};v(D,t=>{U()&&t(de)})}var C=n(D,2),ce=l(C),xe=l(ce,!0);o(ce);var be=n(ce,2),ze=l(be,!0);o(be),o(C);var he=n(C,2);lt(he,{class:"bg-border my-4"});var V=n(he,2),W=l(V),N=l(W),ye=l(N);{var ve=t=>{var r=m("Free");a(t,r)},Pe=t=>{var r=or(),x=n(A(r));L(),F(()=>E(x,`${Ge()??""} `)),a(t,r)};v(ye,t=>{T().toLowerCase()==="free"?t(ve):t(Pe,!1)})}o(N);var fe=n(N,2);{var we=t=>{var r=ir();a(t,r)};v(fe,t=>{T().toLowerCase()==="custom"&&t(we)})}o(W);var Ce=n(W,2);{var He=t=>{var r=lr(),x=l(r);o(r),F(()=>E(x,`Billed ${j()??""}`)),a(t,r)};v(Ce,t=>{T().toLowerCase()!=="free"&&t(He)})}var Se=n(Ce,2),ke=l(Se);const Ke=I(()=>U()?"default":"outline"),Ie=I(()=>_e()||M());nt(ke,{class:"w-full",get variant(){return e(Ke)},get disabled(){return e(Ie)},get onclick(){return J()},children:(t,r)=>{var x=Et(),k=A(x);{var f=R=>{var q=nr(),pe=A(q);qt(pe,{class:"mr-2 h-4 w-4 animate-spin"}),L(),a(R,q)},d=(R,q)=>{{var pe=B=>{var p=m("Current Plan");a(B,p)},We=B=>{var p=m();F(()=>E(p,O())),a(B,p)};v(R,B=>{K()?B(pe):B(We,!1)},q)}};v(k,R=>{M()?R(f):R(d,!1)})}a(t,x)},$$slots:{default:!0}}),o(Se),o(V);var Le=n(V,2);lt(Le,{class:"bg-border mt-4"});var s=n(Le,2),i=l(s),c=l(i),b=n(l(c),2),S=l(b),X=n(l(S),2),Te=l(X);{var Y=t=>{var r=dr();L(),a(t,r)},Q=(t,r)=>{{var x=f=>{var d=cr(),R=A(d);L(),F(()=>E(R,`${_().resumesPerMonth??""} `)),a(f,d)},k=f=>{var d=vr();a(f,d)};v(t,f=>{var d;((d=_())==null?void 0:d.resumesPerMonth)!==void 0&&_().resumesPerMonth!==null&&_().resumesPerMonth!=="U"?f(x):f(k,!1)},r)}};v(Te,t=>{T().toLowerCase()==="free"?t(Y):t(Q,!1)})}o(X),o(S);var G=n(S,2),je=n(l(G),2),dt=l(je);{var De=t=>{var r=m("1");a(t,r)},Re=(t,r)=>{{var x=f=>{var d=m();F(()=>E(d,_().profiles)),a(f,d)},k=f=>{var d=fr();a(f,d)};v(t,f=>{var d;((d=_())==null?void 0:d.profiles)!==void 0&&_().profiles!==null&&_().profiles!=="U"?f(x):f(k,!1)},r)}};v(dt,t=>{T().toLowerCase()==="free"?t(De):t(Re,!1)})}o(je),o(G);var Ae=n(G,2),me=l(Ae),ct=l(me);{var Ue=t=>{var r=m("AI Credits");a(t,r)},Ee=t=>{var r=m("Team Seats");a(t,r)};v(ct,t=>{T().toLowerCase()==="free"?t(Ue):t(Ee,!1)})}o(me);var Fe=n(me,2),ue=l(Fe);{var Ve=t=>{var r=mr();L(),a(t,r)},Z=(t,r)=>{{var x=f=>{var d=m();F(()=>E(d,_().seats)),a(f,d)},k=f=>{var d=ur();a(f,d)};v(t,f=>{var d;((d=_())==null?void 0:d.seats)!==void 0&&_().seats!==null&&_().seats!=="U"?f(x):f(k,!1)},r)}};v(ue,t=>{T().toLowerCase()==="free"?t(Ve):t(Z,!1)})}o(Fe),o(Ae),o(b),o(c);var Be=n(c,2),vt=n(l(Be),2),ft=l(vt);pt(ft,5,qe,Ot,(t,r)=>{var x=gr(),k=l(x),f=l(k);{var d=p=>{var ge=m("Resume Scanner");a(p,ge)},R=(p,ge)=>{{var Xe=y=>{var z=m("Resume Builder");a(y,z)},Ye=(y,z)=>{{var Oe=$=>{var Ze=m("Resume AI");a($,Ze)},Qe=($,Ze)=>{{var bt=ee=>{var $e=m("Job Search Profiles");a(ee,$e)},ht=(ee,$e)=>{{var yt=te=>{var et=m("Save Jobs");a(te,et)},Pt=(te,et)=>{{var wt=re=>{var tt=m("Job Alerts");a(re,tt)},Ct=(re,tt)=>{{var St=ae=>{var rt=m("Application Tracker");a(ae,rt)},kt=(ae,rt)=>{{var It=se=>{var at=m("Cover Letter Generator");a(se,at)},Lt=(se,at)=>{{var Tt=oe=>{var st=m("Dashboard Access");a(oe,st)},jt=(oe,st)=>{{var Dt=ie=>{var ot=m("User Profile");a(ie,ot)},Rt=(ie,ot)=>{{var At=le=>{var Je=m("Document Storage");a(le,Je)},Ut=le=>{var Je=m();F(Me=>E(Je,Me),[()=>e(r).featureId.split("_").map(Me=>Me.charAt(0).toUpperCase()+Me.slice(1)).join(" ")],I),a(le,Je)};v(ie,le=>{e(r).featureId==="documents"?le(At):le(Ut,!1)},ot)}};v(oe,ie=>{e(r).featureId==="profile"?ie(Dt):ie(Rt,!1)},st)}};v(se,oe=>{e(r).featureId==="dashboard"?oe(Tt):oe(jt,!1)},at)}};v(ae,se=>{e(r).featureId==="cover_letter_generator"?se(It):se(Lt,!1)},rt)}};v(re,ae=>{e(r).featureId==="tracker"||e(r).featureId==="application_tracker"?ae(St):ae(kt,!1)},tt)}};v(te,re=>{e(r).featureId==="job_alerts"?re(wt):re(Ct,!1)},et)}};v(ee,te=>{e(r).featureId==="job_save"?te(yt):te(Pt,!1)},$e)}};v($,ee=>{e(r).featureId==="job_search_profiles"?ee(bt):ee(ht,!1)},Ze)}};v(y,$=>{e(r).featureId==="resume_ai"?$(Oe):$(Qe,!1)},z)}};v(p,y=>{e(r).featureId==="resume_builder"?y(Xe):y(Ye,!1)},ge)}};v(f,p=>{e(r).featureId==="resume_scanner"?p(d):p(R,!1)})}o(k);var q=n(k),pe=l(q);{var We=p=>{zt(p,{class:"text-success ml-auto h-4 w-4"})},B=(p,ge)=>{{var Xe=y=>{var z=pr(),Oe=n(l(z),2),Qe=l(Oe);Ht(Qe,{class:"text-muted-foreground h-3 w-3 cursor-help"}),L(2),o(Oe),o(z),a(y,z)},Ye=y=>{Kt(y,{class:"text-destructive ml-auto h-4 w-4"})};v(p,y=>{e(r).accessLevel==="limited"?y(Xe):y(Ye,!1)},ge)}};v(pe,p=>{e(r).accessLevel==="included"?p(We):p(B,!1)})}o(q),o(x),a(t,x)}),o(ft),o(vt),o(Be),o(i),o(s);var xt=n(s,2);lt(xt,{class:"bg-border mt-4"}),o(w),F(()=>{gt(w,1,`border-border rounded-lg border shadow-sm ${U()?"border-primary shadow-primary/20 relative":""}`),E(xe,T()),E(ze,ne())}),a(Ne,w),ut()}var br=u("<div><!></div>"),hr=u('<p class="text-muted-foreground text-md mb-6">You need to sign in to update your account plan.</p> <!>',1),yr=u("<!> <!>",1),Pr=u("<!> <!>",1),wr=u("<!> <!>",1),Cr=u("<!> <!>",1),Sr=u(`<!> <section><div class="container mx-auto"><div class="py-14 text-center"><h2 class="mb-4 text-3xl font-semibold">Simple, Transparent Pricing</h2> <p class="text-muted-foreground font-lg mx-auto mt-2 max-w-2xl">Start with a free account to speed up your job hunt or boost your entire team to scale
        hiring process with resume automation.</p> <div class="mt-8 flex flex-col items-center justify-between gap-4 px-4 md:flex-row"><div class="flex justify-start space-x-4 md:justify-center"><!> <!></div> <div class="text-muted-foreground flex items-center gap-3 text-sm"><!> <span>Annual Billing</span></div></div></div> <div class="mt-10 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"></div> <div class="border-border bg-card text-card-foreground mt-16 rounded-lg border p-6 text-center shadow-sm"><h3 class="mb-2 text-xl font-semibold">Need a custom solution?</h3> <p class="text-muted-foreground mb-4">Get in touch for enterprise or partner solutions tailored to your needs.</p> <a href="/contact" class="text-primary font-medium hover:underline">Talk to Sales →</a></div></div></section> <!>`,1);function Wr(Ne,g){mt(g,!1);const[T,Ge]=Nt(),ne=()=>Mt(_e,"$billingCycleStore",T),U=H();let O=P(g,"data",8),j=H(O().preselectedPlanId||null);const _=typeof window<"u"?new URLSearchParams(window.location.search):null,qe=_==null?void 0:_.get("section");let J=H(qe==="teams"||O().preselectedSection==="teams"?"teams":"pro"),_e=Ft(O().preselectedBillingCycle==="annual"?"annual":"monthly"),K=H(ne()==="annual"),M=H(null);const w=O().user;let D=O().plans;console.log("All plans:",D),console.log("Individual plans:",D.filter(s=>s.section==="pro")),console.log("Team plans:",D.filter(s=>s.section==="teams")),console.log("Plans from server:",D),console.log("Individual plans:",D.filter(s=>s.section==="pro").map(s=>s.id)),console.log("Team plans:",D.filter(s=>s.section==="teams").map(s=>s.id));let de=H(!1),C=H(!1);function ce(s){return`${(s/100).toFixed(0)}`}async function xe(s,i){h(C,!0);try{console.log("Creating checkout session",{planId:s,billingCycle:i});const c=await fetch("/api/billing/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:s,billingCycle:i})});if(!c.ok){const S=await c.text();console.error("Failed to create Stripe session",{status:c.status,statusText:c.statusText,errorText:S}),alert(`Error: ${S||"Failed to create checkout session"}`),h(C,!1);return}const b=await c.json();if(!b.url){console.error("No URL returned from checkout session",b),alert("Error: No checkout URL returned"),h(C,!1);return}console.log("Redirecting to Stripe",{url:b.url}),window.location.href=b.url}catch(c){console.error("Failed to create Stripe session",c),alert(`Error: ${c.message||"Failed to create checkout session"}`),h(C,!1)}}async function be(s,i){if((await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:s,password:i})})).ok)if(h(C,!1),e(M)){const{planId:b,billingCycle:S}=e(M);h(M,null),h(j,b),xe(b,S)}else window.location.href="/dashboard"}function ze(){h(de,!0)}function he(){h(de,!1)}it(()=>ne(),()=>{h(U,ne())}),it(()=>e(K),()=>{_e.set(e(K)?"annual":"monthly")}),it(()=>e(j),()=>{e(j)&&setTimeout(()=>{const s=document.getElementById(`plan-${e(j)}`);s&&s.scrollIntoView({behavior:"smooth",block:"center"})},500)}),Bt(),_t();var V=Sr(),W=A(V);Gt(W,{title:"Pricing | Hirli",description:"Simple, transparent pricing plans for all your job application automation needs. Choose the plan that fits your career goals.",keywords:"pricing, subscription plans, job application tools, career services, job search automation, resume optimization"});var N=n(W,2),ye=l(N),ve=l(ye),Pe=n(l(ve),4),fe=l(Pe),we=l(fe);const Ce=I(()=>e(J)==="pro"?"border-primary bg-primary text-primary-foreground":"bg-background text-foreground hover:bg-muted");nt(we,{onclick:()=>h(J,"pro"),get class(){return`border-border rounded-full border px-4 py-2 text-sm font-medium transition ${e(Ce)??""}`},children:(s,i)=>{L();var c=m("Individual");a(s,c)},$$slots:{default:!0}});var He=n(we,2);const Se=I(()=>e(J)==="teams"?"border-primary bg-primary text-primary-foreground":"bg-background text-foreground hover:bg-muted");nt(He,{onclick:()=>h(J,"teams"),get class(){return`border-border rounded-full border px-4 py-2 text-sm font-medium transition ${e(Se)??""}`},children:(s,i)=>{L();var c=m("Teams");a(s,c)},$$slots:{default:!0}}),o(fe);var ke=n(fe,2),Ke=l(ke);Vt(Ke,{get checked(){return e(K)},set checked(s){h(K,s)},$$legacy:!0}),L(2),o(ke),o(Pe),o(ve);var Ie=n(ve,2);pt(Ie,5,()=>D.filter(s=>s.section===e(J)).sort((s,i)=>s.id==="free"?-1:i.id==="free"?1:s.monthlyPrice-i.monthlyPrice),s=>s.id,(s,i)=>{var c=br(),b=l(c);const S=I(()=>ce(e(U)==="monthly"?e(i).monthlyPrice:e(i).annualPrice/12)),X=I(()=>e(i).id==="pro"||e(i).id==="startup"||e(i).popular||e(j)===e(i).id),Te=I(()=>w&&w.role===e(i).id),Y=I(()=>e(j)===e(i).id&&e(C)),Q=I(()=>w?w.role===e(i).id?"Current Plan":"Upgrade":e(i).id==="free"?"Start Free":"Choose Plan"),G=I(()=>e(C)||w&&w.role===e(i).id);xr(b,{get title(){return e(i).name},get price(){return e(S)},get description(){return e(i).description},get limits(){return e(i).limits},get features(){return e(i).features},get billingCycle(){return e(U)},get isPopular(){return e(X)},get activePlan(){return e(Te)},get loading(){return e(Y)},get ctaText(){return e(Q)},get disabled(){return e(G)},onCtaClick:()=>{if(e(i).id==="free"){window.location.href="/auth/sign-up";return}if(!w){h(M,{planId:e(i).id,billingCycle:e(U)}),ze();return}h(j,e(i).id),xe(e(i).id,e(U))}}),o(c),F(()=>{Jt(c,"id",`plan-${e(i).id??""}`),gt(c,1,`relative ${e(j)===e(i).id?"ring-primary ring-2 ring-offset-2":""}`)}),a(s,c)}),o(Ie),L(2),o(ye),o(N);var Le=n(N,2);Wt(Le,{get open(){return e(de)},onOpenChange:he,children:(s,i)=>{var c=Cr(),b=A(c);$t(b,{});var S=n(b,2);Xt(S,{children:(X,Te)=>{var Y=wr(),Q=A(Y);Yt(Q,{});var G=n(Q,2);Qt(G,{class:"md:w-[375px]",children:(je,dt)=>{var De=Pr(),Re=A(De);er(Re,{children:(me,ct)=>{var Ue=yr(),Ee=A(Ue);tr(Ee,{class:"mb-2 text-2xl",children:(ue,Ve)=>{L();var Z=m("Sign In");a(ue,Z)},$$slots:{default:!0}});var Fe=n(Ee,2);rr(Fe,{children:(ue,Ve)=>{var Z=hr(),Be=n(A(Z),2);Zt(Be,{get isLoading(){return e(C)},onEmailPasswordLogin:be}),a(ue,Z)},$$slots:{default:!0}}),a(me,Ue)},$$slots:{default:!0}});var Ae=n(Re,2);ar(Ae,{}),a(je,De)},$$slots:{default:!0}}),a(X,Y)},$$slots:{default:!0}}),a(s,c)},$$slots:{default:!0}}),a(Ne,V),ut(),Ge()}export{Wr as component};
