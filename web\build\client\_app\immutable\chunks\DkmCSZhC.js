import{c as i,a as m}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function C(t,o){const s=l(o,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"m9 18 6-6-6-6"}]];f(t,d({name:"chevron-right"},()=>s,{get iconNode(){return e},children:(a,$)=>{var r=i(),n=p(r);c(n,o,"default",{},null),m(a,r)},$$slots:{default:!0}}))}export{C};
