import{c as Qe,f as u,t as p,a as r,w as Xr}from"../chunks/BasJTneF.js";import{a as jr,o as Zr}from"../chunks/nZgk9enP.js";import{p as nt,k as me,v as at,i as kt,d as n,f as _,n as v,s as t,c as s,g as e,r as a,t as ne,a as dt,x as rt}from"../chunks/CGmarHxI.js";import{s as C}from"../chunks/CIt1g2O9.js";import{i as De}from"../chunks/u21ee2wt.js";import{c as g}from"../chunks/BvdI7LR8.js";import{e as Rr,g as Lt}from"../chunks/CmxjS0TN.js";import{B as de}from"../chunks/B1K98fMG.js";import{D as ea,a as ta,R as ra}from"../chunks/WD4kvFhR.js";import{S as aa}from"../chunks/C6g8ubaU.js";import{g as tr}from"../chunks/BiJhC7W5.js";import"../chunks/CgXBgsce.js";import{t as Oe}from"../chunks/DjPYYl4Z.js";import{m as Mt,p as sa}from"../chunks/Dqigtbi1.js";import{p as vt}from"../chunks/Btcx8l8F.js";import{T as oa,R as la}from"../chunks/I7hvcB12.js";import{s as rr,r as Ut,c as ia,a as Et,f as na}from"../chunks/B-Xjo-Yt.js";import{e as lt,i as it}from"../chunks/C3w0v0gR.js";import{a as xt,R as bt,P as Ot,D as Ft}from"../chunks/tdzGgazS.js";import{L as Re}from"../chunks/BvvicRXk.js";import{I as et}from"../chunks/DMTMHyMa.js";import{R as da,S as ua,a as va,b as ca}from"../chunks/CGK0g3x_.js";import{D as yt,a as $t,b as Pt,c as wt}from"../chunks/CKh8VGVX.js";import{S as fa}from"../chunks/B2lQHLf_.js";import{S as ma}from"../chunks/CVVv9lPb.js";import{S as Ct}from"../chunks/BHzYYMdu.js";import{S as ht}from"../chunks/DumgozFE.js";import{S as Rt}from"../chunks/D9yI7a4E.js";import{b as Ht}from"../chunks/CzsE_FAw.js";import{C as pa,a as _a,b as ga}from"../chunks/Dmwghw4a.js";import{C as ha}from"../chunks/CDeW2UsS.js";import{C as xa}from"../chunks/DW5gea7N.js";import{C as ba}from"../chunks/B5tu6DNS.js";import{R as ya,P as $a,a as Pa}from"../chunks/3WmhYGjL.js";import{c as wr}from"../chunks/ncUU1dSD.js";import{C as wa,F as kr,L as Lr,G as Nr}from"../chunks/-vfp2Q9I.js";import{C as Ar}from"../chunks/BNEH2jqx.js";import{B as Sr}from"../chunks/DaBofrVv.js";import{C as Jt}from"../chunks/T7uRAIbG.js";import{u as Zt,r as Ur}from"../chunks/CWA2dVWH.js";import{U as Tr}from"../chunks/G5Oo-PmU.js";import{F as er}from"../chunks/ChqRiddM.js";import{D as ka}from"../chunks/tr-scC-m.js";import{T as ar}from"../chunks/VNuMAkuB.js";import{A as sr,a as or,b as lr,c as ir,d as nr,e as dr,f as ur,R as vr}from"../chunks/BnikQ10_.js";import{P as St}from"../chunks/DR5zc253.js";import{B as qt}from"../chunks/CDnvByek.js";import{T as Tt}from"../chunks/C33xR25f.js";import{G as Dr,L as Cr}from"../chunks/DQB68x0Z.js";import{L as cr}from"../chunks/DRGimm5x.js";import{G as Or}from"../chunks/D1zde6Ej.js";import{X as Fr}from"../chunks/CnpHcmx3.js";import{M as Sa}from"../chunks/CwgkX8t9.js";import{B as Da}from"../chunks/C2AK_5VT.js";import{D as Ca}from"../chunks/6BxQgNmX.js";import{T as Xt}from"../chunks/C88uNE8B.js";import{U as Ia}from"../chunks/B_6ivTD3.js";import{E as Ea}from"../chunks/8b74MdfD.js";import{D as ja}from"../chunks/BgDjIxoO.js";import{D as Ir}from"../chunks/Dz4exfp3.js";import{E as Ra}from"../chunks/6UJoWgvL.js";import{E as La}from"../chunks/7AwcL9ec.js";import{A as Na}from"../chunks/Ce6y1v79.js";var Aa=u("<!> <!>",1),Ua=u('<p class="text-destructive text-sm"> </p>'),Ta=u('<p class="text-destructive text-sm"> </p>'),Oa=u("<!> <!>",1),Fa=u('<span class="mr-2">Saving...</span>'),za=u("<!> Save Changes",1),Ma=u("<!> <!>",1),Ha=u('<!> <div class="grid gap-4 py-4"><div class="grid gap-2"><!> <!> <!></div> <div class="grid gap-2"><!> <!> <!></div> <div class="grid gap-2"><!> <!></div> <div class="grid gap-2"><!> <!></div></div> <!>',1);function qa(Me,o){nt(o,!0);let l=me(at({profileName:o.data.profileName||"",fullName:o.data.fullName||"",jobTitle:o.data.jobTitle||"",jobSearchStatus:o.data.jobSearchStatus||"actively_looking"})),d=me(!1),x=me(at({}));kt(()=>{o.open&&(n(l,{profileName:o.data.profileName||"",fullName:o.data.fullName||"",jobTitle:o.data.jobTitle||"",jobSearchStatus:o.data.jobSearchStatus||"actively_looking"},!0),n(x,{},!0))});async function k(){if(n(x,{},!0),e(l).profileName||(e(x).profileName="Profile name is required"),e(l).fullName||(e(x).fullName="Full name is required"),!(Object.keys(e(x)).length>0)){n(d,!0);try{await o.onSave(e(l))&&(Oe.success("Profile header updated successfully"),o.onClose())}catch(te){console.error("Error saving profile header:",te),Oe.error("Failed to save profile header")}finally{n(d,!1)}}}const O=[{value:"actively_looking",label:"Actively Looking"},{value:"open_to_opportunities",label:"Open to Opportunities"},{value:"not_looking",label:"Not Looking"}];var ee=Qe(),B=_(ee);g(B,()=>bt,(te,F)=>{F(te,{get open(){return o.open},onOpenChange:ge=>!ge&&o.onClose(),children:(ge,Ee)=>{var ue=Qe(),xe=_(ue);g(xe,()=>xt,(Ne,he)=>{he(Ne,{class:"sm:max-w-[500px]",children:(X,se)=>{var H=Ha(),G=_(H);g(G,()=>yt,(I,pe)=>{pe(I,{children:(we,i)=>{var y=Aa(),w=_(y);g(w,()=>$t,(c,j)=>{j(c,{children:(z,re)=>{v();var S=p("Edit Profile");r(z,S)},$$slots:{default:!0}})});var E=t(w,2);g(E,()=>Pt,(c,j)=>{j(c,{children:(z,re)=>{v();var S=p("Update your profile information. Click save when you're done.");r(z,S)},$$slots:{default:!0}})}),r(we,y)},$$slots:{default:!0}})});var L=t(G,2),oe=s(L),Ce=s(oe);Re(Ce,{for:"profileName",children:(I,pe)=>{v();var we=p("Profile Name");r(I,we)},$$slots:{default:!0}});var Ue=t(Ce,2);et(Ue,{id:"profileName",placeholder:"My Professional Profile",get value(){return e(l).profileName},set value(I){e(l).profileName=I}});var Z=t(Ue,2);{var N=I=>{var pe=Ua(),we=s(pe,!0);a(pe),ne(()=>C(we,e(x).profileName)),r(I,pe)};De(Z,I=>{e(x).profileName&&I(N)})}a(oe);var A=t(oe,2),W=s(A);Re(W,{for:"fullName",children:(I,pe)=>{v();var we=p("Full Name");r(I,we)},$$slots:{default:!0}});var f=t(W,2);et(f,{id:"fullName",placeholder:"John Doe",get value(){return e(l).fullName},set value(I){e(l).fullName=I}});var h=t(f,2);{var q=I=>{var pe=Ta(),we=s(pe,!0);a(pe),ne(()=>C(we,e(x).fullName)),r(I,pe)};De(h,I=>{e(x).fullName&&I(q)})}a(A);var b=t(A,2),V=s(b);Re(V,{for:"jobTitle",children:(I,pe)=>{v();var we=p("Job Title");r(I,we)},$$slots:{default:!0}});var be=t(V,2);et(be,{id:"jobTitle",placeholder:"Software Engineer",get value(){return e(l).jobTitle},set value(I){e(l).jobTitle=I}}),a(b);var le=t(b,2),$e=s(le);Re($e,{for:"jobSearchStatus",children:(I,pe)=>{v();var we=p("Job Search Status");r(I,we)},$$slots:{default:!0}});var ze=t($e,2);g(ze,()=>da,(I,pe)=>{pe(I,{get value(){return e(l).jobSearchStatus},onValueChange:we=>e(l).jobSearchStatus=we,children:(we,i)=>{var y=Oa(),w=_(y);g(w,()=>ua,(c,j)=>{j(c,{id:"jobSearchStatus",class:"w-full",children:(z,re)=>{var S=Qe(),m=_(S);g(m,()=>fa,(P,R)=>{R(P,{placeholder:"Select job search status"})}),r(z,S)},$$slots:{default:!0}})});var E=t(w,2);g(E,()=>va,(c,j)=>{j(c,{children:(z,re)=>{var S=Qe(),m=_(S);g(m,()=>ma,(P,R)=>{R(P,{children:(Q,fe)=>{var U=Qe(),ve=_(U);lt(ve,17,()=>O,it,(Se,je)=>{var ce=Qe(),ie=_(ce);g(ie,()=>ca,(Le,ae)=>{ae(Le,{get value(){return e(je).value},children:(qe,Je)=>{v();var Ve=p();ne(()=>C(Ve,e(je).label)),r(qe,Ve)},$$slots:{default:!0}})}),r(Se,ce)}),r(Q,U)},$$slots:{default:!0}})}),r(z,S)},$$slots:{default:!0}})}),r(we,y)},$$slots:{default:!0}})}),a(le),a(L);var Ae=t(L,2);g(Ae,()=>wt,(I,pe)=>{pe(I,{children:(we,i)=>{var y=Ma(),w=_(y);de(w,{variant:"outline",get onclick(){return o.onClose},children:(c,j)=>{v();var z=p("Cancel");r(c,z)},$$slots:{default:!0}});var E=t(w,2);de(E,{onclick:k,get disabled(){return e(d)},children:(c,j)=>{var z=Qe(),re=_(z);{var S=P=>{var R=Fa();r(P,R)},m=P=>{var R=za(),Q=_(R);Ct(Q,{class:"mr-2 h-4 w-4"}),v(),r(P,R)};De(re,P=>{e(d)?P(S):P(m,!1)})}r(c,z)},$$slots:{default:!0}}),r(we,y)},$$slots:{default:!0}})}),r(X,H)},$$slots:{default:!0}})}),r(ge,ue)},$$slots:{default:!0}})}),r(Me,ee),dt()}var Ja=u("<!> Edit",1),Wa=u('<div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Profile</h2> <!></div> <div class="mt-4 space-y-4"><div class="grid grid-cols-1 gap-4 md:grid-cols-2"><div><h3 class="text-muted-foreground text-sm font-medium">Profile Name</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Full Name</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Job Title</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Job Search Status</h3> <div class="flex items-center"><span></span> <p> </p></div></div></div></div></div> <!>',1);function Va(Me,o){nt(o,!0);let l=me(!1);function d(){n(l,!0)}async function x(W){try{return await o.onSave(W)}catch(f){return console.error("Error saving profile header:",f),Oe.error("Failed to save profile header"),!1}}const k=[{value:"actively_looking",label:"Actively Looking"},{value:"open_to_opportunities",label:"Open to Opportunities"},{value:"not_looking",label:"Not Looking"}];function O(W){const f=k.find(h=>h.value===W);return f?f.label:"Not specified"}var ee=Wa(),B=_(ee),te=s(B),F=t(s(te),2);de(F,{variant:"ghost",size:"sm",onclick:d,children:(W,f)=>{var h=Ja(),q=_(h);ht(q,{class:"mr-2 h-4 w-4"}),v(),r(W,h)},$$slots:{default:!0}}),a(te);var ge=t(te,2),Ee=s(ge),ue=s(Ee),xe=t(s(ue),2),Ne=s(xe,!0);a(xe),a(ue);var he=t(ue,2),X=t(s(he),2),se=s(X,!0);a(X),a(he);var H=t(he,2),G=t(s(H),2),L=s(G,!0);a(G),a(H);var oe=t(H,2),Ce=t(s(oe),2),Ue=s(Ce),Z=t(Ue,2),N=s(Z,!0);a(Z),a(Ce),a(oe),a(Ee),a(ge),a(B);var A=t(B,2);qa(A,{get open(){return e(l)},get data(){return o.data},onClose:()=>n(l,!1),onSave:x}),ne(W=>{C(Ne,o.data.profileName||"Not specified"),C(se,o.data.fullName||"Not specified"),C(L,o.data.jobTitle||"Not specified"),rr(Ue,1,`mr-2 h-2 w-2 rounded-full ${o.data.jobSearchStatus==="actively_looking"?"bg-green-500":o.data.jobSearchStatus==="open_to_opportunities"?"bg-yellow-500":"bg-gray-500"}`),C(N,W)},[()=>O(o.data.jobSearchStatus)]),r(Me,ee),dt()}var Ba=u("<!> Edit",1),Ya=u("<!> <!>",1),Ka=u("<!> ",1),Ga=u("<!> <!>",1),Qa=u('<!> <form class="space-y-4 py-4"><div class="flex items-center justify-between space-x-2"><div><!> <p class="text-muted-foreground text-sm">Allow recruiters to find and contact you</p></div> <!></div> <div class="flex items-center justify-between space-x-2"><div><!> <p class="text-muted-foreground text-sm">Allow companies to find your profile</p></div> <!></div> <div class="flex items-center justify-between space-x-2"><div><!> <p class="text-muted-foreground text-sm">Keep your job search private from current employer</p></div> <!></div> <!></form>',1),Xa=u("<!> <!>",1),Za=u('<div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Profile Visibility</h2> <!></div> <p class="text-muted-foreground mt-2 text-sm">Control who can see your profile and job search status</p> <div class="mt-4 space-y-4"><div class="flex items-center justify-between space-x-2"><div><p class="font-medium">Recruiter Visibility</p> <p class="text-muted-foreground text-sm"> </p></div> <!></div> <div class="flex items-center justify-between space-x-2"><div><p class="font-medium">Discovery Status</p> <p class="text-muted-foreground text-sm"> </p></div> <!></div> <div class="flex items-center justify-between space-x-2"><div><p class="font-medium">Current Employer</p> <p class="text-muted-foreground text-sm"> </p></div> <!></div></div></div> <!>',1);function es(Me,o){nt(o,!0);let l=me(at({showToRecruiters:o.data.showToRecruiters||!1,getDiscovered:o.data.getDiscovered||!0,hideFromCurrentEmployer:o.data.hideFromCurrentEmployer||!1})),d=me(!1),x=me(!1);function k(){n(l,{showToRecruiters:o.data.showToRecruiters||!1,getDiscovered:o.data.getDiscovered||!0,hideFromCurrentEmployer:o.data.hideFromCurrentEmployer||!1},!0),n(x,!0)}async function O(W){W.preventDefault(),n(d,!0);try{await o.onSave(e(l))&&(Oe.success("Profile visibility updated successfully"),n(x,!1))}catch(f){console.error("Error saving profile visibility:",f),Oe.error("Failed to save profile visibility")}finally{n(d,!1)}}var ee=Za(),B=_(ee),te=s(B),F=t(s(te),2);de(F,{variant:"ghost",size:"sm",onclick:k,children:(W,f)=>{var h=Ba(),q=_(h);ht(q,{class:"mr-2 h-4 w-4"}),v(),r(W,h)},$$slots:{default:!0}}),a(te);var ge=t(te,4),Ee=s(ge),ue=s(Ee),xe=t(s(ue),2),Ne=s(xe,!0);a(xe),a(ue);var he=t(ue,2);g(he,()=>Rt,(W,f)=>{f(W,{get checked(){return o.data.showToRecruiters},disabled:!0})}),a(Ee);var X=t(Ee,2),se=s(X),H=t(s(se),2),G=s(H,!0);a(H),a(se);var L=t(se,2);g(L,()=>Rt,(W,f)=>{f(W,{get checked(){return o.data.getDiscovered},disabled:!0})}),a(X);var oe=t(X,2),Ce=s(oe),Ue=t(s(Ce),2),Z=s(Ue,!0);a(Ue),a(Ce);var N=t(Ce,2);g(N,()=>Rt,(W,f)=>{f(W,{get checked(){return o.data.hideFromCurrentEmployer},disabled:!0})}),a(oe),a(ge),a(B);var A=t(B,2);g(A,()=>bt,(W,f)=>{f(W,{get open(){return e(x)},set open(h){n(x,h,!0)},children:(h,q)=>{var b=Qe(),V=_(b);g(V,()=>Ot,(be,le)=>{le(be,{children:($e,ze)=>{var Ae=Xa(),I=_(Ae);g(I,()=>Ft,(we,i)=>{i(we,{})});var pe=t(I,2);g(pe,()=>xt,(we,i)=>{i(we,{class:"sm:max-w-[500px]",children:(y,w)=>{var E=Qa(),c=_(E);g(c,()=>yt,(ie,Le)=>{Le(ie,{children:(ae,qe)=>{var Je=Ya(),Ve=_(Je);g(Ve,()=>$t,(tt,D)=>{D(tt,{children:(Y,ye)=>{v();var J=p("Edit Profile Visibility");r(Y,J)},$$slots:{default:!0}})});var Be=t(Ve,2);g(Be,()=>Pt,(tt,D)=>{D(tt,{children:(Y,ye)=>{v();var J=p("Control who can see your profile and job search status");r(Y,J)},$$slots:{default:!0}})}),r(ae,Je)},$$slots:{default:!0}})});var j=t(c,2),z=s(j),re=s(z),S=s(re);Re(S,{for:"showToRecruiters",class:"font-medium",children:(ie,Le)=>{v();var ae=p("Show Profile to Recruiters");r(ie,ae)},$$slots:{default:!0}}),v(2),a(re);var m=t(re,2);g(m,()=>Rt,(ie,Le)=>{Le(ie,{id:"showToRecruiters",get checked(){return e(l).showToRecruiters},onCheckedChange:ae=>e(l).showToRecruiters=ae})}),a(z);var P=t(z,2),R=s(P),Q=s(R);Re(Q,{for:"getDiscovered",class:"font-medium",children:(ie,Le)=>{v();var ae=p("Get Discovered by Companies");r(ie,ae)},$$slots:{default:!0}}),v(2),a(R);var fe=t(R,2);g(fe,()=>Rt,(ie,Le)=>{Le(ie,{id:"getDiscovered",get checked(){return e(l).getDiscovered},onCheckedChange:ae=>e(l).getDiscovered=ae})}),a(P);var U=t(P,2),ve=s(U),Se=s(ve);Re(Se,{for:"hideFromCurrentEmployer",class:"font-medium",children:(ie,Le)=>{v();var ae=p("Hide from Current Employer");r(ie,ae)},$$slots:{default:!0}}),v(2),a(ve);var je=t(ve,2);g(je,()=>Rt,(ie,Le)=>{Le(ie,{id:"hideFromCurrentEmployer",get checked(){return e(l).hideFromCurrentEmployer},onCheckedChange:ae=>e(l).hideFromCurrentEmployer=ae})}),a(U);var ce=t(U,2);g(ce,()=>wt,(ie,Le)=>{Le(ie,{children:(ae,qe)=>{var Je=Ga(),Ve=_(Je);de(Ve,{variant:"outline",type:"button",onclick:()=>n(x,!1),children:(tt,D)=>{v();var Y=p("Cancel");r(tt,Y)},$$slots:{default:!0}});var Be=t(Ve,2);de(Be,{type:"submit",get disabled(){return e(d)},class:"ml-2",children:(tt,D)=>{var Y=Ka(),ye=_(Y);Ct(ye,{class:"mr-2 h-4 w-4"});var J=t(ye);ne(()=>C(J,` ${e(d)?"Saving...":"Save Changes"}`)),r(tt,Y)},$$slots:{default:!0}}),r(ae,Je)},$$slots:{default:!0}})}),a(j),Rr("submit",j,O),r(y,E)},$$slots:{default:!0}})}),r($e,Ae)},$$slots:{default:!0}})}),r(h,b)},$$slots:{default:!0}})}),ne(()=>{C(Ne,o.data.showToRecruiters?"Visible to recruiters":"Hidden from recruiters"),C(G,o.data.getDiscovered?"Companies can find you":"Not discoverable by companies"),C(Z,o.data.hideFromCurrentEmployer?"Hidden from current employer":"Visible to current employer")}),r(Me,ee),dt()}var ts=u(" <!>",1),rs=u('<div class="py-6 text-center text-sm">No results found.</div>'),as=u("<!> ",1),ss=u("<!> <!>",1),os=u("<!> <!>",1),ls=u('<div style="min-width: 300px;"><!></div>'),is=u("<!> <!>",1),ns=u('<div><!> <input type="hidden"/></div>');function Er(Me,o){nt(o,!0);const l=vt(o,"value",3,""),d=vt(o,"options",19,()=>[]),x=vt(o,"placeholder",3,"Select..."),k=vt(o,"searchPlaceholder",3,"Search..."),O=vt(o,"disabled",3,!1),ee=vt(o,"required",3,!1),B=vt(o,"name",3,""),te=vt(o,"id",3,""),F=vt(o,"className",3,""),ge=vt(o,"onChange",3,void 0);let Ee=me(at(l())),ue=me(!1),xe=me("");const Ne=rt(()=>e(xe)?d().filter(L=>L.label.toLowerCase().includes(e(xe).toLowerCase())):d()),he=rt(()=>d().find(L=>L.value===e(Ee)));function X(L){n(Ee,L.value,!0),ge()&&ge()(L.value),n(ue,!1),n(xe,"")}var se=ns(),H=s(se);g(H,()=>ya,(L,oe)=>{oe(L,{get open(){return e(ue)},set open(Ce){n(ue,Ce,!0)},children:(Ce,Ue)=>{var Z=is(),N=_(Z);g(N,()=>$a,(W,f)=>{f(W,{children:(h,q)=>{de(h,{variant:"outline",role:"combobox",get"aria-expanded"(){return e(ue)},class:"w-full justify-between",get disabled(){return O()},children:(b,V)=>{v();var be=ts(),le=_(be),$e=t(le);wa($e,{class:"ml-2 h-4 w-4 shrink-0 opacity-50"}),ne(()=>C(le,`${(e(he)?e(he).label:x())??""} `)),r(b,be)},$$slots:{default:!0}})},$$slots:{default:!0}})});var A=t(N,2);g(A,()=>Pa,(W,f)=>{f(W,{class:"w-full p-0",children:(h,q)=>{var b=ls(),V=s(b);g(V,()=>pa,(be,le)=>{le(be,{children:($e,ze)=>{var Ae=os(),I=_(Ae);g(I,()=>ba,(y,w)=>{w(y,{get placeholder(){return k()},class:"h-9",get value(){return e(xe)},set value(E){n(xe,E,!0)}})});var pe=t(I,2);{var we=y=>{var w=rs();r(y,w)},i=y=>{var w=Qe(),E=_(w);g(E,()=>_a,(c,j)=>{j(c,{children:(z,re)=>{var S=ss(),m=_(S);g(m,()=>ga,(R,Q)=>{Q(R,{children:(fe,U)=>{v();var ve=p("No results found.");r(fe,ve)},$$slots:{default:!0}})});var P=t(m,2);g(P,()=>ha,(R,Q)=>{Q(R,{children:(fe,U)=>{var ve=Qe(),Se=_(ve);lt(Se,17,()=>e(Ne),je=>je.value,(je,ce)=>{var ie=Qe(),Le=_(ie);g(Le,()=>xa,(ae,qe)=>{qe(ae,{get value(){return e(ce).label},onSelect:()=>X(e(ce)),class:"flex items-center",children:(Je,Ve)=>{var Be=as(),tt=_(Be);const D=rt(()=>wr("mr-2 h-4 w-4",e(Ee)===e(ce).value?"opacity-100":"opacity-0"));Ar(tt,{get class(){return e(D)}});var Y=t(tt);ne(()=>C(Y,` ${e(ce).label??""}`)),r(Je,Be)},$$slots:{default:!0}})}),r(je,ie)}),r(fe,ve)},$$slots:{default:!0}})}),r(z,S)},$$slots:{default:!0}})}),r(y,w)};De(pe,y=>{e(Ne).length===0?y(we):y(i,!1)})}r($e,Ae)},$$slots:{default:!0}})}),a(b),r(h,b)},$$slots:{default:!0}})}),r(Ce,Z)},$$slots:{default:!0}})});var G=t(H,2);Ut(G),a(se),ne(L=>{rr(se,1,L),Et(G,"name",B()),Et(G,"id",te()),na(G,e(Ee)),G.required=ee()},[()=>ia(wr("w-full",F()))]),r(Me,se),dt()}function ds(Me,o,l){const d=Me.target.value;o.phone=l(d)}var us=u("<!> <!>",1),vs=u('<p class="text-destructive text-sm"> </p>'),cs=u('<p class="text-destructive text-sm"> </p>'),fs=u("<!> ",1),ms=u("<!> <!>",1),ps=u('<!> <div class="py-4"><form class="space-y-6"><div class="space-y-4"><h3 class="text-base font-medium">Contact Information</h3> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="space-y-2"><!> <input id="email" type="email" class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"/> <!></div> <div class="space-y-2"><!> <input id="phone" type="tel" class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" placeholder="(*************"/> <!></div></div></div> <div class="space-y-4"><h3 class="text-base font-medium">Location Information</h3> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="space-y-2"><!> <input id="address" type="text" class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <input id="zip" type="text" class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="space-y-2"><!> <input id="country" value="USA" disabled class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"/></div></div></div></form></div> <!>',1),_s=u("<!> <!>",1);function gs(Me,o){var Ee,ue,xe,Ne,he,X,se;nt(o,!0);const l=[{value:"New York",label:"New York"},{value:"Los Angeles",label:"Los Angeles"},{value:"Chicago",label:"Chicago"},{value:"Houston",label:"Houston"},{value:"Phoenix",label:"Phoenix"},{value:"Philadelphia",label:"Philadelphia"},{value:"San Antonio",label:"San Antonio"},{value:"San Diego",label:"San Diego"},{value:"Dallas",label:"Dallas"},{value:"San Jose",label:"San Jose"},{value:"Austin",label:"Austin"},{value:"Jacksonville",label:"Jacksonville"},{value:"Fort Worth",label:"Fort Worth"},{value:"Columbus",label:"Columbus"},{value:"Indianapolis",label:"Indianapolis"}],d=[{value:"AL",label:"Alabama"},{value:"AK",label:"Alaska"},{value:"AZ",label:"Arizona"},{value:"AR",label:"Arkansas"},{value:"CA",label:"California"},{value:"CO",label:"Colorado"},{value:"CT",label:"Connecticut"},{value:"DE",label:"Delaware"},{value:"FL",label:"Florida"},{value:"GA",label:"Georgia"},{value:"HI",label:"Hawaii"},{value:"ID",label:"Idaho"},{value:"IL",label:"Illinois"},{value:"IN",label:"Indiana"},{value:"IA",label:"Iowa"},{value:"KS",label:"Kansas"},{value:"KY",label:"Kentucky"},{value:"LA",label:"Louisiana"},{value:"ME",label:"Maine"},{value:"MD",label:"Maryland"},{value:"MA",label:"Massachusetts"},{value:"MI",label:"Michigan"},{value:"MN",label:"Minnesota"},{value:"MS",label:"Mississippi"},{value:"MO",label:"Missouri"},{value:"MT",label:"Montana"},{value:"NE",label:"Nebraska"},{value:"NV",label:"Nevada"},{value:"NH",label:"New Hampshire"},{value:"NJ",label:"New Jersey"},{value:"NM",label:"New Mexico"},{value:"NY",label:"New York"},{value:"NC",label:"North Carolina"},{value:"ND",label:"North Dakota"},{value:"OH",label:"Ohio"},{value:"OK",label:"Oklahoma"},{value:"OR",label:"Oregon"},{value:"PA",label:"Pennsylvania"},{value:"RI",label:"Rhode Island"},{value:"SC",label:"South Carolina"},{value:"SD",label:"South Dakota"},{value:"TN",label:"Tennessee"},{value:"TX",label:"Texas"},{value:"UT",label:"Utah"},{value:"VT",label:"Vermont"},{value:"VA",label:"Virginia"},{value:"WA",label:"Washington"},{value:"WV",label:"West Virginia"},{value:"WI",label:"Wisconsin"},{value:"WY",label:"Wyoming"},{value:"DC",label:"District of Columbia"}];let x=at({email:((Ee=o.data)==null?void 0:Ee.email)||"",phone:((ue=o.data)==null?void 0:ue.phone)||"",address:((xe=o.data)==null?void 0:xe.address)||"",city:((Ne=o.data)==null?void 0:Ne.city)||"",state:((he=o.data)==null?void 0:he.state)||"",zip:((X=o.data)==null?void 0:X.zip)||"",country:((se=o.data)==null?void 0:se.country)||"USA"}),k=me(!1),O=me(at({}));function ee(){n(O,{},!0);let H=!0;return x.email&&(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(x.email)||(e(O).email="Please enter a valid email address",H=!1)),x.phone&&x.phone.replace(/\D/g,"").length<10&&(e(O).phone="Phone number must have at least 10 digits",H=!1),H}function B(H){const G=H.replace(/\D/g,"");return G.length<=3?G:G.length<=6?`(${G.slice(0,3)}) ${G.slice(3)}`:`(${G.slice(0,3)}) ${G.slice(3,6)}-${G.slice(6,10)}`}async function te(){if(ee()){n(k,!0);try{await o.onSave(x)&&(Oe.success("Personal information updated successfully"),o.onClose())}catch(H){console.error("Error saving personal information:",H),Oe.error("Failed to save personal information")}finally{n(k,!1)}}}var F=Qe(),ge=_(F);g(ge,()=>bt,(H,G)=>{G(H,{get open(){return o.open},onOpenChange:L=>!L&&o.onClose(),children:(L,oe)=>{var Ce=Qe(),Ue=_(Ce);g(Ue,()=>Ot,(Z,N)=>{N(Z,{children:(A,W)=>{var f=_s(),h=_(f);g(h,()=>Ft,(b,V)=>{V(b,{})});var q=t(h,2);g(q,()=>xt,(b,V)=>{V(b,{class:"sm:max-w-[600px]",children:(be,le)=>{var $e=ps(),ze=_($e);g(ze,()=>yt,(D,Y)=>{Y(D,{children:(ye,J)=>{var T=us(),K=_(T);g(K,()=>$t,(Ie,$)=>{$(Ie,{children:(ke,Pe)=>{v();var He=p("Edit Personal Information");r(ke,He)},$$slots:{default:!0}})});var M=t(K,2);g(M,()=>Pt,(Ie,$)=>{$(Ie,{children:(ke,Pe)=>{v();var He=p("Update your personal information to help fill out job applications faster.");r(ke,He)},$$slots:{default:!0}})}),r(ye,T)},$$slots:{default:!0}})});var Ae=t(ze,2),I=s(Ae),pe=s(I),we=t(s(pe),2),i=s(we),y=s(i);Re(y,{for:"email",children:(D,Y)=>{v();var ye=p("Email Address");r(D,ye)},$$slots:{default:!0}});var w=t(y,2);Ut(w);var E=t(w,2);{var c=D=>{var Y=vs(),ye=s(Y,!0);a(Y),ne(()=>C(ye,e(O).email)),r(D,Y)};De(E,D=>{e(O).email&&D(c)})}a(i);var j=t(i,2),z=s(j);Re(z,{for:"phone",children:(D,Y)=>{v();var ye=p("Phone Number");r(D,ye)},$$slots:{default:!0}});var re=t(z,2);Ut(re),re.__input=[ds,x,B];var S=t(re,2);{var m=D=>{var Y=cs(),ye=s(Y,!0);a(Y),ne(()=>C(ye,e(O).phone)),r(D,Y)};De(S,D=>{e(O).phone&&D(m)})}a(j),a(we),a(pe);var P=t(pe,2),R=t(s(P),2),Q=s(R),fe=s(Q);Re(fe,{for:"address",children:(D,Y)=>{v();var ye=p("Street Address");r(D,ye)},$$slots:{default:!0}});var U=t(fe,2);Ut(U),a(Q);var ve=t(Q,2),Se=s(ve);Re(Se,{for:"city",children:(D,Y)=>{v();var ye=p("City");r(D,ye)},$$slots:{default:!0}});var je=t(Se,2);Er(je,{id:"city",name:"city",get options(){return l},placeholder:"Select city...",searchPlaceholder:"Search cities...",onChange:D=>x.city=D,get value(){return x.city},set value(D){x.city=D}}),a(ve);var ce=t(ve,2),ie=s(ce);Re(ie,{for:"state",children:(D,Y)=>{v();var ye=p("State");r(D,ye)},$$slots:{default:!0}});var Le=t(ie,2);Er(Le,{id:"state",name:"state",get options(){return d},placeholder:"Select state...",searchPlaceholder:"Search states...",onChange:D=>x.state=D,get value(){return x.state},set value(D){x.state=D}}),a(ce);var ae=t(ce,2),qe=s(ae);Re(qe,{for:"zip",children:(D,Y)=>{v();var ye=p("ZIP/Postal Code");r(D,ye)},$$slots:{default:!0}});var Je=t(qe,2);Ut(Je),a(ae);var Ve=t(ae,2),Be=s(Ve);Re(Be,{for:"country",children:(D,Y)=>{v();var ye=p("Country");r(D,ye)},$$slots:{default:!0}}),t(Be,2),a(Ve),a(R),a(P),a(I),a(Ae);var tt=t(Ae,2);g(tt,()=>wt,(D,Y)=>{Y(D,{children:(ye,J)=>{var T=ms(),K=_(T);de(K,{variant:"outline",get onclick(){return o.onClose},children:(Ie,$)=>{v();var ke=p("Cancel");r(Ie,ke)},$$slots:{default:!0}});var M=t(K,2);de(M,{onclick:te,get disabled(){return e(k)},class:"ml-2",children:(Ie,$)=>{var ke=fs(),Pe=_(ke);Ct(Pe,{class:"mr-2 h-4 w-4"});var He=t(Pe);ne(()=>C(He,` ${e(k)?"Saving...":"Save Changes"}`)),r(Ie,ke)},$$slots:{default:!0}}),r(ye,T)},$$slots:{default:!0}})}),Rr("submit",I,D=>{D.preventDefault(),te()}),Ht(w,()=>x.email,D=>x.email=D),Ht(re,()=>x.phone,D=>x.phone=D),Ht(U,()=>x.address,D=>x.address=D),Ht(Je,()=>x.zip,D=>x.zip=D),r(be,$e)},$$slots:{default:!0}})}),r(A,f)},$$slots:{default:!0}})}),r(L,Ce)},$$slots:{default:!0}})}),r(Me,F),dt()}Lt(["input"]);var hs=u("<!> Edit",1),xs=u('<div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Personal Information</h2> <!></div> <p class="text-muted-foreground mt-2 text-sm">This information helps you fill out job applications faster and more accurately.</p> <div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2"><div><h3 class="text-muted-foreground text-sm font-medium">Email</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Phone</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Address</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">City</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">State</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">ZIP</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Country</h3> <p> </p></div></div></div> <!>',1);function bs(Me,o){nt(o,!0);let l=me(!1);function d(){n(l,!0)}async function x(b){try{return await o.onSave(b)}catch(V){return console.error("Error saving personal information:",V),Oe.error("Failed to save personal information"),!1}}var k=xs(),O=_(k),ee=s(O),B=t(s(ee),2);de(B,{variant:"ghost",size:"sm",onclick:d,children:(b,V)=>{var be=hs(),le=_(be);ht(le,{class:"mr-2 h-4 w-4"}),v(),r(b,be)},$$slots:{default:!0}}),a(ee);var te=t(ee,4),F=s(te),ge=t(s(F),2),Ee=s(ge,!0);a(ge),a(F);var ue=t(F,2),xe=t(s(ue),2),Ne=s(xe,!0);a(xe),a(ue);var he=t(ue,2),X=t(s(he),2),se=s(X,!0);a(X),a(he);var H=t(he,2),G=t(s(H),2),L=s(G,!0);a(G),a(H);var oe=t(H,2),Ce=t(s(oe),2),Ue=s(Ce,!0);a(Ce),a(oe);var Z=t(oe,2),N=t(s(Z),2),A=s(N,!0);a(N),a(Z);var W=t(Z,2),f=t(s(W),2),h=s(f,!0);a(f),a(W),a(te),a(O);var q=t(O,2);gs(q,{get open(){return e(l)},get data(){return o.data},onClose:()=>n(l,!1),onSave:x}),ne(()=>{C(Ee,o.data.email||"Not specified"),C(Ne,o.data.phone||"Not specified"),C(se,o.data.address||"Not specified"),C(L,o.data.city||"Not specified"),C(Ue,o.data.state||"Not specified"),C(A,o.data.zip||"Not specified"),C(h,o.data.country||"USA")}),r(Me,k),dt()}async function ys(Me,o,l,d,x,k){var te,F;const O=Me.target;if(!O.files||O.files.length===0)return;const ee=O.files[0];if(ee.size>5*1024*1024){Oe.error("File size exceeds 5MB limit");return}const B=(te=ee.name.split(".").pop())==null?void 0:te.toLowerCase();if(!["pdf","doc","docx"].includes(B||"")){Oe.error("Only PDF, DOC, and DOCX files are supported");return}n(o,!0);try{const ge=new FormData;ge.append("file",ee);const Ee=window.location.pathname.split("/").pop();Ee&&ge.append("profileId",Ee),ge.append("label",ee.name),ge.append("type","resume"),ge.append("parseIntoProfile",e(l).parseIntoProfile.toString());try{const ue=await fetch("/api/resume/upload",{method:"POST",body:ge});if(!ue.ok){const he=await ue.json();throw ue.status===403&&(he.limitReached||(F=he.message)!=null&&F.includes("limit"))?(n(d,!0),new Error(he.message||"Document limit reached. Please upgrade your plan to upload more documents.")):new Error(he.message||"Failed to upload resume")}const xe=await ue.json();console.log("Resume upload response:",xe);const Ne=xe.resume.id;n(l,{...e(l),resumeId:Ne,fileName:xe.document.label,uploadedAt:xe.document.createdAt||new Date().toISOString(),isDefault:!0},!0),xe.alreadyParsed?(n(x,!0),e(l).parseIntoProfile=!1,Oe.success("Resume uploaded successfully. It was already parsed previously.")):(Ne&&await k(Ne),Oe.success(xe.message||"Resume uploaded successfully"))}catch(ue){throw console.error("Error in fetch operation:",ue),ue}}catch(ge){console.error("Error uploading resume:",ge),Oe.error(ge instanceof Error?ge.message:"Failed to upload resume")}finally{n(o,!1)}}var $s=u("<!> <!>",1),Ps=u(`<div class="rounded-md border border-yellow-300 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-900/20"><h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Document Limit Reached</h3> <p class="text-muted-foreground mt-2 text-xs">You have reached your document upload limit. Please upgrade your plan to upload more
              documents.</p> <!></div>`),ws=u('<div class="flex items-center rounded-md bg-amber-50 p-2 text-sm text-amber-700 dark:bg-amber-900/20 dark:text-amber-200"><span>Checking resume parse status...</span></div>'),ks=u('<div class="flex items-center rounded-md bg-amber-50 p-2 text-amber-700 dark:bg-amber-900/20 dark:text-amber-200"><span class="mr-2 animate-pulse">●</span> <span class="text-sm">Resume parsing in progress...</span></div>'),Ss=u('<div class="flex items-center rounded-md bg-green-50 p-2 text-green-700 dark:bg-green-900/20 dark:text-green-200"><!> <span class="text-sm">This resume has already been parsed.</span></div>'),Ds=u('<div class="rounded-md border p-4"><div class="flex items-center justify-between"><div class="flex items-center"><!> <div><p class="font-medium"> </p> <p class="text-muted-foreground text-xs"> </p></div></div> <!></div></div>'),Cs=u("<!> Choose File",1),Is=(Me,o,l)=>o(e(l)),Es=u('<button type="button"><!> <div><p class="text-sm font-medium"> </p> <p class="text-muted-foreground text-xs"> </p></div> <!></button>'),js=u('<div><h4 class="text-muted-foreground mb-2 text-center text-xs font-medium">Or Select Existing Resume</h4> <div class="max-h-40 overflow-y-auto rounded-md border"></div></div>'),Rs=u('<div class="text-muted-foreground text-center text-sm">Loading your resumes...</div>'),Ls=u('<span class="text-muted-foreground">(parsing in progress)</span>'),Ns=u('<span class="text-muted-foreground">(already parsed)</span>'),As=u("Parse resume data into profile <!>",1),Us=u('<span class="mr-2">Saving...</span>'),Ts=u('<span class="mr-2">Parsing in progress...</span>'),Os=u("<!> Save Changes",1),Fs=u("<!> <!>",1),zs=u('<!> <div class="grid gap-4 py-4"><!> <!> <!> <div class="rounded-md border p-4"><h3 class="mb-3 text-sm font-medium">Resume Options</h3> <div class="mb-4"><h4 class="text-muted-foreground mb-2 text-xs font-medium">Upload New Resume</h4> <div class="flex items-center space-x-2"><input type="file" id="resumeUpload" accept=".pdf,.doc,.docx" class="hidden"/> <!></div> <p class="text-muted-foreground mt-1 text-xs">Accepted file formats: PDF, DOC, DOCX. Maximum file size: 5MB.</p></div> <!></div> <div class="space-y-2"><div class="flex items-center space-x-2"><!> <!></div> <p class="text-muted-foreground ml-6 text-xs"><!></p></div></div> <!>',1),Ms=u("<!> <!>",1);function Hs(Me,o){var L,oe,Ce,Ue;nt(o,!0);const l=vt(o,"disabled",3,!1);let d=me(at({resumeId:((L=o.data)==null?void 0:L.resumeId)||"",fileName:((oe=o.data)==null?void 0:oe.fileName)||"",uploadedAt:((Ce=o.data)==null?void 0:Ce.uploadedAt)||"",isDefault:((Ue=o.data)==null?void 0:Ue.isDefault)||!1,parseIntoProfile:!0})),x=me(!1),k=me(at([])),O=me(!1),ee=me(!1),B=me(!1),te=me(!1),F=me(!1);kt(()=>{var Z,N,A,W,f;o.open&&(n(B,!1),n(te,!1),n(d,{resumeId:((Z=o.data)==null?void 0:Z.resumeId)||"",fileName:((N=o.data)==null?void 0:N.fileName)||"",uploadedAt:((A=o.data)==null?void 0:A.uploadedAt)||"",isDefault:((W=o.data)==null?void 0:W.isDefault)||!1,parseIntoProfile:!0},!0),ge(),(f=o.data)!=null&&f.resumeId&&Ee(o.data.resumeId))});async function ge(){n(O,!0);try{const Z=await fetch("/api/documents?type=resume");if(!Z.ok)throw new Error("Failed to load existing resumes");const N=await Z.json();n(k,N.documents||[],!0)}catch(Z){console.error("Error loading existing resumes:",Z)}finally{n(O,!1)}}async function Ee(Z){if(Z){n(te,!0),n(B,!1);try{const N=await fetch(`/api/resume/${Z}/status`);if(!N.ok)throw new Error("Failed to check resume parse status");const A=await N.json();console.log("Resume parse status check (one-time):",A),A.isParsed&&A.parsedData?(n(B,!0),e(d).parseIntoProfile=!1):(n(B,!1),e(d).parseIntoProfile=!0),e(B)||await ue(Z)}catch(N){console.error("Error checking resume parse status:",N)}finally{n(te,!1)}}}async function ue(Z){try{const N=await fetch(`/api/resume/${Z}/parsing-status`);if(!N.ok)throw new Error("Failed to check parsing status");return(await N.json()).isParsing?(n(F,!0),xe(Z),!0):!1}catch(N){return console.error("Error checking parsing status:",N),!1}}function xe(Z){Zt(Z),Ur(Z,(A,W)=>{console.log(`Resume parsing completed for ${A}:`,W),n(F,!1),n(B,!0),e(d).parseIntoProfile=!1,Oe.success("Resume parsing completed successfully",{description:"Your resume has been parsed and the data has been added to your profile.",duration:5e3}),o.onClose()},(A,W)=>{console.error(`Resume parsing failed for ${A}:`,W),n(F,!1),Oe.error("Resume parsing failed",{description:W?`Error: ${W}`:"Please try again later.",duration:5e3})});const N=A=>{var W;((W=A.detail)==null?void 0:W.resumeId)===Z&&(console.log(`Received resume-parsing-completed event for ${Z} in modal`),n(F,!1),n(B,!0),e(d).parseIntoProfile=!1,o.onClose())};window.addEventListener("resume-parsing-completed",N),Ne=N}let Ne=null;jr(()=>{e(d).resumeId&&Zt(e(d).resumeId),Ne&&(window.removeEventListener("resume-parsing-completed",Ne),Ne=null)});async function he(Z){var A;console.log("Selected document:",Z);const N=((A=Z.resume)==null?void 0:A.id)||"";n(d,{...e(d),resumeId:N,fileName:Z.label||Z.fileName||"",uploadedAt:Z.createdAt||new Date().toISOString(),isDefault:!0},!0),N&&(await Ee(N),e(B)&&(console.log("Resume is already parsed, disabling parse checkbox"),e(d).parseIntoProfile=!1))}function X(Z){return Z?new Date(Z).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Not available"}async function se(){if(!e(d).fileName){Oe.error("Please upload a resume file");return}e(d).parseIntoProfile&&n(F,!0),n(x,!0);try{await o.onSave(e(d))?(Oe.success("Resume updated successfully"),o.onClose()):n(F,!1)}catch(Z){console.error("Error saving resume:",Z),Oe.error("Failed to save resume"),n(F,!1)}finally{n(x,!1)}}var H=Qe(),G=_(H);g(G,()=>bt,(Z,N)=>{N(Z,{get open(){return o.open},onOpenChange:A=>!A&&o.onClose(),children:(A,W)=>{var f=Qe(),h=_(f);g(h,()=>Ot,(q,b)=>{b(q,{children:(V,be)=>{var le=Ms(),$e=_(le);g($e,()=>Ft,(Ae,I)=>{I(Ae,{})});var ze=t($e,2);g(ze,()=>xt,(Ae,I)=>{I(Ae,{class:"sm:max-w-[500px]",children:(pe,we)=>{var i=zs(),y=_(i);g(y,()=>yt,(Y,ye)=>{ye(Y,{children:(J,T)=>{var K=$s(),M=_(K);g(M,()=>$t,($,ke)=>{ke($,{children:(Pe,He)=>{v();var We=p();ne(()=>{var Ke;return C(We,(Ke=o.data)!=null&&Ke.resumeId?"Replace Resume":"Upload Resume")}),r(Pe,We)},$$slots:{default:!0}})});var Ie=t(M,2);g(Ie,()=>Pt,($,ke)=>{ke($,{children:(Pe,He)=>{v();var We=p("Upload your resume to use with job applications.");r(Pe,We)},$$slots:{default:!0}})}),r(J,K)},$$slots:{default:!0}})});var w=t(y,2),E=s(w);{var c=Y=>{var ye=Ps(),J=t(s(ye),4);de(J,{variant:"outline",class:"mt-2",size:"sm",onclick:()=>{tr("/pricing?plan=pro&billing=monthly"),o.onClose()},children:(T,K)=>{v();var M=p("Upgrade Plan");r(T,M)},$$slots:{default:!0}}),a(ye),r(Y,ye)};De(E,Y=>{e(ee)&&Y(c)})}var j=t(E,2);{var z=Y=>{var ye=ws();r(Y,ye)},re=(Y,ye)=>{{var J=K=>{var M=ks();r(K,M)},T=(K,M)=>{{var Ie=$=>{var ke=Ss(),Pe=s(ke);Ar(Pe,{class:"mr-2 h-4 w-4"}),v(2),a(ke),r($,ke)};De(K,$=>{e(B)&&$(Ie)},M)}};De(Y,K=>{e(F)?K(J):K(T,!1)},ye)}};De(j,Y=>{e(te)?Y(z):Y(re,!1)})}var S=t(j,2);{var m=Y=>{var ye=Ds(),J=s(ye),T=s(J),K=s(T);er(K,{class:"mr-2 h-5 w-5 text-blue-500"});var M=t(K,2),Ie=s(M),$=s(Ie,!0);a(Ie);var ke=t(Ie,2),Pe=s(ke);a(ke),a(M),a(T);var He=t(T,2);g(He,()=>Sr,(We,Ke)=>{Ke(We,{variant:"outline",children:(Xe,Ye)=>{v();var Ze=p("Selected");r(Xe,Ze)},$$slots:{default:!0}})}),a(J),a(ye),ne(We=>{C($,e(d).fileName),C(Pe,`Uploaded: ${We??""}`)},[()=>X(e(d).uploadedAt)]),r(Y,ye)};De(S,Y=>{e(d).fileName&&Y(m)})}var P=t(S,2),R=t(s(P),2),Q=t(s(R),2),fe=s(Q);fe.__change=[ys,x,d,ee,B,Ee];var U=t(fe,2);const ve=rt(()=>l()||e(x));de(U,{type:"button",variant:"outline",class:"w-full",get disabled(){return e(ve)},onclick:()=>{var Y;return(Y=document.getElementById("resumeUpload"))==null?void 0:Y.click()},children:(Y,ye)=>{var J=Cs(),T=_(J);Tr(T,{class:"mr-2 h-4 w-4"}),v(),r(Y,J)},$$slots:{default:!0}}),a(Q),v(2),a(R);var Se=t(R,2);{var je=Y=>{var ye=js(),J=t(s(ye),2);lt(J,21,()=>e(k),it,(T,K)=>{var M=Es();M.__click=[Is,he,K];var Ie=s(M);er(Ie,{class:"mr-2 h-4 w-4 text-blue-500"});var $=t(Ie,2),ke=s($),Pe=s(ke,!0);a(ke);var He=t(ke,2),We=s(He);a(He),a($);var Ke=t($,2);{var Xe=Ye=>{var Ze=Qe(),st=_(Ze);g(st,()=>Sr,(_t,ut)=>{ut(_t,{variant:"outline",class:"ml-auto",children:(ft,gt)=>{v();var Dt=p("Selected");r(ft,Dt)},$$slots:{default:!0}})}),r(Ye,Ze)};De(Ke,Ye=>{var Ze;e(d).resumeId===((Ze=e(K).resume)==null?void 0:Ze.id)&&Ye(Xe)})}a(M),ne(Ye=>{var Ze;rr(M,1,`hover:bg-muted/50 flex w-full items-center border-b p-3 text-left last:border-0 ${e(d).resumeId===((Ze=e(K).resume)==null?void 0:Ze.id)?"bg-muted/30":""}`),C(Pe,e(K).label||"Unnamed Resume"),C(We,`Uploaded: ${Ye??""}`)},[()=>X(e(K).createdAt)]),r(T,M)}),a(J),a(ye),r(Y,ye)},ce=(Y,ye)=>{{var J=T=>{var K=Rs();r(T,K)};De(Y,T=>{e(O)&&T(J)},ye)}};De(Se,Y=>{e(k).length>0?Y(je):Y(ce,!1)})}a(P);var ie=t(P,2),Le=s(ie),ae=s(Le);g(ae,()=>Jt,(Y,ye)=>{ye(Y,{id:"parseIntoProfile",get checked(){return e(d).parseIntoProfile},get disabled(){return e(F)},onCheckedChange:J=>{e(d).parseIntoProfile=J===!0,J&&e(B)&&e(d).resumeId&&ue(e(d).resumeId)}})});var qe=t(ae,2);Re(qe,{for:"parseIntoProfile",class:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:(Y,ye)=>{v();var J=As(),T=t(_(J));{var K=Ie=>{var $=Ls();r(Ie,$)},M=(Ie,$)=>{{var ke=Pe=>{var He=Ns();r(Pe,He)};De(Ie,Pe=>{e(B)&&Pe(ke)},$)}};De(T,Ie=>{e(F)?Ie(K):Ie(M,!1)})}r(Y,J)},$$slots:{default:!0}}),a(Le);var Je=t(Le,2),Ve=s(Je);{var Be=Y=>{var ye=p("Resume parsing is currently in progress. Please wait until it completes.");r(Y,ye)},tt=(Y,ye)=>{{var J=K=>{var M=p(`This resume has already been parsed. You can use it as is, or check the box to
              re-parse it if you need to update your profile data.`);r(K,M)},T=K=>{var M=p(`When enabled, we'll extract information from your resume and update your profile with
              it.`);r(K,M)};De(Y,K=>{e(B)?K(J):K(T,!1)},ye)}};De(Ve,Y=>{e(F)?Y(Be):Y(tt,!1)})}a(Je),a(ie),a(w);var D=t(w,2);g(D,()=>wt,(Y,ye)=>{ye(Y,{children:(J,T)=>{var K=Fs(),M=_(K);const Ie=rt(()=>e(x)||e(F));de(M,{variant:"outline",get onclick(){return o.onClose},get disabled(){return e(Ie)},children:(Pe,He)=>{v();var We=p("Cancel");r(Pe,We)},$$slots:{default:!0}});var $=t(M,2);const ke=rt(()=>l()||e(x)||!e(d).fileName||e(F));de($,{onclick:se,get disabled(){return e(ke)},children:(Pe,He)=>{var We=Qe(),Ke=_(We);{var Xe=Ze=>{var st=Us();r(Ze,st)},Ye=(Ze,st)=>{{var _t=ft=>{var gt=Ts();r(ft,gt)},ut=ft=>{var gt=Os(),Dt=_(gt);Ct(Dt,{class:"mr-2 h-4 w-4"}),v(),r(ft,gt)};De(Ze,ft=>{e(F)?ft(_t):ft(ut,!1)},st)}};De(Ke,Ze=>{e(x)?Ze(Xe):Ze(Ye,!1)})}r(Pe,We)},$$slots:{default:!0}}),r(J,K)},$$slots:{default:!0}})}),r(pe,i)},$$slots:{default:!0}})}),r(V,le)},$$slots:{default:!0}})}),r(A,f)},$$slots:{default:!0}})}),r(Me,H),dt()}Lt(["change","click"]);var qs=u('<div class="flex items-center rounded-md bg-amber-50 px-2 py-1 text-xs text-amber-600 dark:bg-amber-900/20 dark:text-amber-200"><span class="mr-1 animate-pulse">●</span> Parsing...</div>'),Js=u("<!> Download",1),Ws=u("<!> ",1),Vs=u('<div class="grid grid-cols-1 gap-4 rounded-md border p-4 md:grid-cols-2"><div><p class="text-sm font-medium">File Name</p> <p class="text-sm"> </p></div> <div><p class="text-sm font-medium">Uploaded</p> <p class="text-sm"> </p></div></div>'),Bs=u("<!> Upload Resume",1),Ys=u('<div class="flex flex-col items-center justify-center rounded-md border p-8"><p class="text-muted-foreground mb-4">No resume uploaded yet</p> <!></div>'),Ks=u('<div class="border-border space-y-4 rounded-md border p-6"><div class="flex items-center justify-between"><div class="flex items-center gap-2"><h3 class="text-lg font-medium">Resume</h3> <!></div> <div class="flex space-x-2"><!> <!></div></div> <!></div> <!>',1);function Gs(Me,o){nt(o,!0);const l=vt(o,"data",3,null),d=vt(o,"disabled",3,!1);let x=me(!1),k=me(!1);function O(){n(x,!0)}async function ee(){var N;if((N=l())!=null&&N.resumeId)try{const A=await fetch(`/api/resume/${l().resumeId}/download`);if(!A.ok)throw new Error("Failed to download resume");const W=await A.blob(),f=window.URL.createObjectURL(W),h=document.createElement("a");h.href=f,h.download=l().fileName||"resume.pdf",document.body.appendChild(h),h.click(),window.URL.revokeObjectURL(f),document.body.removeChild(h)}catch(A){console.error("Error downloading resume:",A),Oe.error("Failed to download resume")}}async function B(N){try{const A=await o.onSave(N);return N.parseIntoProfile&&(console.log("Resume parsing requested, updating UI state"),n(k,!0),o.onParsingStatusChange&&o.onParsingStatusChange(!0)),A}catch(A){return console.error("Error saving resume:",A),Oe.error("Failed to save resume"),!1}}function te(N){return N?new Date(N).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Not available"}kt(()=>{var N;if((N=l())!=null&&N.resumeId){Ur(l().resumeId,(f,h)=>{console.log(`Resume parsing completed for ${f}:`,h),n(k,!1),o.onParsingStatusChange&&o.onParsingStatusChange(!1),Oe.success("Resume parsing completed successfully",{description:"Your resume has been parsed and the data has been added to your profile.",duration:5e3})},(f,h)=>{console.error(`Resume parsing failed for ${f}:`,h),n(k,!1),o.onParsingStatusChange&&o.onParsingStatusChange(!1),Oe.error("Resume parsing failed",{description:h?`Error: ${h}`:"Please try again later.",duration:5e3})}),F(l().resumeId).then(f=>{console.log(`Resume ${l().resumeId} parsing status check: ${f}`),n(k,f,!0),o.onParsingStatusChange&&o.onParsingStatusChange(f)});const A=f=>{var h;((h=f.detail)==null?void 0:h.resumeId)===l().resumeId&&(console.log(`Received resume-parsing-completed event for ${l().resumeId}`),n(k,!1),o.onParsingStatusChange&&o.onParsingStatusChange(!1))},W=f=>{var h,q;((h=f.detail)==null?void 0:h.resumeId)===l().resumeId&&((q=f.detail)!=null&&q.isParsing)&&(console.log(`Received resume-parsing-status event for ${l().resumeId} with isParsing=true`),n(k,!0),o.onParsingStatusChange&&o.onParsingStatusChange(!0))};return window.addEventListener("resume-parsing-completed",A),window.addEventListener("resume-parsing-status",W),()=>{window.removeEventListener("resume-parsing-completed",A),window.removeEventListener("resume-parsing-status",W)}}}),jr(()=>{var N;(N=l())!=null&&N.resumeId&&Zt(l().resumeId)});async function F(N){try{const A=await fetch(`/api/resume/${N}/parsing-status`);if(!A.ok)throw new Error("Failed to check parsing status");return(await A.json()).isParsing?(n(k,!0),o.onParsingStatusChange&&o.onParsingStatusChange(!0),!0):(n(k,!1),o.onParsingStatusChange&&o.onParsingStatusChange(!1),!1)}catch(A){return console.error("Error checking parsing status:",A),!1}}var ge=Ks(),Ee=_(ge),ue=s(Ee),xe=s(ue),Ne=t(s(xe),2);{var he=N=>{var A=qs();r(N,A)};De(Ne,N=>{e(k)&&N(he)})}a(xe);var X=t(xe,2),se=s(X);{var H=N=>{const A=rt(()=>d()||e(k));de(N,{variant:"outline",size:"sm",onclick:ee,get disabled(){return e(A)},children:(W,f)=>{var h=Js(),q=_(h);ka(q,{class:"mr-2 h-4 w-4"}),v(),r(W,h)},$$slots:{default:!0}})};De(se,N=>{var A;(A=l())!=null&&A.resumeId&&N(H)})}var G=t(se,2);const L=rt(()=>d()||e(k));de(G,{variant:"ghost",size:"sm",onclick:O,get disabled(){return e(L)},children:(N,A)=>{var W=Ws(),f=_(W);ht(f,{class:"mr-2 h-4 w-4"});var h=t(f);ne(()=>{var q;return C(h,` ${(q=l())!=null&&q.resumeId?"Replace":"Upload"}`)}),r(N,W)},$$slots:{default:!0}}),a(X),a(ue);var oe=t(ue,2);{var Ce=N=>{var A=Vs(),W=s(A),f=t(s(W),2),h=s(f,!0);a(f),a(W);var q=t(W,2),b=t(s(q),2),V=s(b,!0);a(b),a(q),a(A),ne(be=>{C(h,l().fileName||"Not available"),C(V,be)},[()=>te(l().uploadedAt)]),r(N,A)},Ue=N=>{var A=Ys(),W=t(s(A),2);de(W,{variant:"outline",onclick:O,get disabled(){return d()},children:(f,h)=>{var q=Bs(),b=_(q);Tr(b,{class:"mr-2 h-4 w-4"}),v(),r(f,q)},$$slots:{default:!0}}),a(A),r(N,A)};De(oe,N=>{var A;(A=l())!=null&&A.resumeId?N(Ce):N(Ue,!1)})}a(Ee);var Z=t(Ee,2);Hs(Z,{get open(){return e(x)},get data(){return l()},onClose:()=>n(x,!1),onSave:B,get disabled(){return d()}}),r(Me,ge),dt()}var Qs=u("<!> Add Experience",1),Xs=u('<p class="text-muted-foreground text-sm"> </p>'),Zs=u('<p class="mt-2 text-sm"> </p>'),eo=u('<div class="flex items-start justify-between rounded-md border p-4"><div class="flex-1"><div class="flex items-center"><!> <h3 class="font-medium"> </h3></div> <p class="text-muted-foreground"> </p> <p class="text-muted-foreground text-sm"> </p> <!> <!></div> <div class="flex space-x-2"><!> <!></div></div>'),to=u('<div class="space-y-4"></div>'),ro=u("<!> Add Experience",1),ao=u('<div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8"><!> <p class="text-muted-foreground text-center">No work experience added yet</p> <!></div>'),so=u("<!> <!>",1),oo=u('<p class="text-destructive text-sm"> </p>'),lo=u('<p class="text-destructive text-sm"> </p>'),io=u('<p class="text-destructive text-sm"> </p>'),no=u("<!> <!>",1),uo=u('<!> <form method="POST" class="space-y-4"><div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="space-y-2"><!> <!> <!></div> <div class="space-y-2"><!> <!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!> <!></div> <div class="space-y-2"><div class="flex items-center space-x-2"><!> <!></div></div> <div class="space-y-2"><!> <!></div></div> <div class="space-y-2"><!> <!></div> <!></form>',1),vo=u("<!> <!>",1),co=u("<!> <!>",1),fo=u("<!> <!>",1),mo=u('<div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Work Experience</h2> <!></div> <div class="mt-4"><!></div></div> <!> <!>',1);function po(Me,o){nt(o,!0);const l=vt(o,"disabled",3,!1);let d=me(at({company:"",title:"",location:"",startDate:"",endDate:"",current:!1,description:""})),x=at({}),k=!1,O=me(at(o.data||[])),ee=me(!1),B=me(!1),te=me(null),F=me(null);function ge(){n(d,{company:"",title:"",location:"",startDate:"",endDate:"",current:!1,description:""},!0),n(te,null),n(ee,!0)}function Ee(f){const h=e(O)[f];n(d,{company:h.company||"",title:h.title||"",location:h.location||"",startDate:h.startDate||"",endDate:h.endDate||"",current:h.current||!1,description:h.description||""},!0),n(te,f,!0),n(ee,!0)}function ue(f){n(F,f,!0),n(B,!0)}function xe(){e(F)!==null&&(n(O,e(O).filter((f,h)=>h!==e(F)),!0),he(),n(B,!1),n(F,null))}async function Ne(){if(!e(d).company||!e(d).title||!e(d).startDate){Oe.error("Please fill in all required fields");return}const f={...e(d)};e(te)!==null?e(O)[e(te)]=f:n(O,[...e(O),f],!0),await he(),n(ee,!1)}async function he(){const f=await o.onSave(e(O));return f&&Oe.success("Work experience updated successfully"),f}function X(f){return f?new Date(f).toLocaleDateString("en-US",{year:"numeric",month:"short"}):""}function se(f,h,q){const b=X(f),V=q?"Present":X(h);return`${b} - ${V}`}var H=mo(),G=_(H),L=s(G),oe=t(s(L),2);de(oe,{variant:"outline",size:"sm",onclick:ge,get disabled(){return l()},children:(f,h)=>{var q=Qs(),b=_(q);St(b,{class:"mr-2 h-4 w-4"}),v(),r(f,q)},$$slots:{default:!0}}),a(L);var Ce=t(L,2),Ue=s(Ce);{var Z=f=>{var h=to();lt(h,21,()=>e(O),it,(q,b,V)=>{var be=eo(),le=s(be),$e=s(le),ze=s($e);qt(ze,{class:"mr-2 h-5 w-5 text-blue-500"});var Ae=t(ze,2),I=s(Ae,!0);a(Ae),a($e);var pe=t($e,2),we=s(pe,!0);a(pe);var i=t(pe,2),y=s(i,!0);a(i);var w=t(i,2);{var E=m=>{var P=Xs(),R=s(P,!0);a(P),ne(()=>C(R,e(b).location)),r(m,P)};De(w,m=>{e(b).location&&m(E)})}var c=t(w,2);{var j=m=>{var P=Zs(),R=s(P,!0);a(P),ne(()=>C(R,e(b).description)),r(m,P)};De(c,m=>{e(b).description&&m(j)})}a(le);var z=t(le,2),re=s(z);de(re,{variant:"ghost",size:"icon",onclick:()=>Ee(V),get disabled(){return l()},children:(m,P)=>{ht(m,{class:"h-4 w-4"})},$$slots:{default:!0}});var S=t(re,2);de(S,{variant:"ghost",size:"icon",onclick:()=>ue(V),get disabled(){return l()},children:(m,P)=>{Tt(m,{class:"h-4 w-4"})},$$slots:{default:!0}}),a(z),a(be),ne(m=>{C(I,e(b).title),C(we,e(b).company),C(y,m)},[()=>se(e(b).startDate,e(b).endDate,e(b).current)]),r(q,be)}),a(h),r(f,h)},N=f=>{var h=ao(),q=s(h);qt(q,{class:"text-muted-foreground mb-2 h-10 w-10"});var b=t(q,4);de(b,{variant:"outline",class:"mt-4",onclick:ge,get disabled(){return l()},children:(V,be)=>{var le=ro(),$e=_(le);St($e,{class:"mr-2 h-4 w-4"}),v(),r(V,le)},$$slots:{default:!0}}),a(h),r(f,h)};De(Ue,f=>{e(O).length>0?f(Z):f(N,!1)})}a(Ce),a(G);var A=t(G,2);g(A,()=>bt,(f,h)=>{h(f,{get open(){return e(ee)},set open(q){n(ee,q,!0)},children:(q,b)=>{var V=Qe(),be=_(V);g(be,()=>xt,(le,$e)=>{$e(le,{class:"sm:max-w-[600px]",children:(ze,Ae)=>{var I=uo(),pe=_(I);g(pe,()=>yt,(J,T)=>{T(J,{children:(K,M)=>{var Ie=so(),$=_(Ie);g($,()=>$t,(Pe,He)=>{He(Pe,{children:(We,Ke)=>{v();var Xe=p();ne(()=>C(Xe,e(te)!==null?"Edit Experience":"Add Experience")),r(We,Xe)},$$slots:{default:!0}})});var ke=t($,2);g(ke,()=>Pt,(Pe,He)=>{He(Pe,{children:(We,Ke)=>{v();var Xe=p();ne(()=>C(Xe,e(te)!==null?"Update your work experience details.":"Add a new work experience to your profile.")),r(We,Xe)},$$slots:{default:!0}})}),r(K,Ie)},$$slots:{default:!0}})});var we=t(pe,2),i=s(we),y=s(i),w=s(y);Re(w,{for:"company",children:(J,T)=>{v();var K=p("Company *");r(J,K)},$$slots:{default:!0}});var E=t(w,2);et(E,{id:"company",get value(){return e(d).company},set value(J){e(d).company=J}});var c=t(E,2);{var j=J=>{var T=oo(),K=s(T,!0);a(T),ne(()=>C(K,x.company)),r(J,T)};De(c,J=>{x.company&&J(j)})}a(y);var z=t(y,2),re=s(z);Re(re,{for:"title",children:(J,T)=>{v();var K=p("Job Title *");r(J,K)},$$slots:{default:!0}});var S=t(re,2);et(S,{id:"title",get value(){return e(d).title},set value(J){e(d).title=J}});var m=t(S,2);{var P=J=>{var T=lo(),K=s(T,!0);a(T),ne(()=>C(K,x.title)),r(J,T)};De(m,J=>{x.title&&J(P)})}a(z);var R=t(z,2),Q=s(R);Re(Q,{for:"location",children:(J,T)=>{v();var K=p("Location");r(J,K)},$$slots:{default:!0}});var fe=t(Q,2);et(fe,{id:"location",get value(){return e(d).location},set value(J){e(d).location=J}}),a(R);var U=t(R,2),ve=s(U);Re(ve,{for:"startDate",children:(J,T)=>{v();var K=p("Start Date *");r(J,K)},$$slots:{default:!0}});var Se=t(ve,2);et(Se,{id:"startDate",type:"month",get value(){return e(d).startDate},set value(J){e(d).startDate=J}});var je=t(Se,2);{var ce=J=>{var T=io(),K=s(T,!0);a(T),ne(()=>C(K,x.startDate)),r(J,T)};De(je,J=>{x.startDate&&J(ce)})}a(U);var ie=t(U,2),Le=s(ie),ae=s(Le);g(ae,()=>Jt,(J,T)=>{T(J,{id:"current",get checked(){return e(d).current},onCheckedChange:K=>e(d).current=K})});var qe=t(ae,2);Re(qe,{for:"current",children:(J,T)=>{v();var K=p("I currently work here");r(J,K)},$$slots:{default:!0}}),a(Le),a(ie);var Je=t(ie,2),Ve=s(Je);Re(Ve,{for:"endDate",children:(J,T)=>{v();var K=p("End Date");r(J,K)},$$slots:{default:!0}});var Be=t(Ve,2);et(Be,{id:"endDate",type:"month",get disabled(){return e(d).current},get value(){return e(d).endDate},set value(J){e(d).endDate=J}}),a(Je),a(i);var tt=t(i,2),D=s(tt);Re(D,{for:"description",children:(J,T)=>{v();var K=p("Description");r(J,K)},$$slots:{default:!0}});var Y=t(D,2);ar(Y,{id:"description",rows:4,placeholder:"Describe your responsibilities and achievements...",get value(){return e(d).description},set value(J){e(d).description=J}}),a(tt);var ye=t(tt,2);g(ye,()=>wt,(J,T)=>{T(J,{children:(K,M)=>{var Ie=no(),$=_(Ie);de($,{variant:"outline",type:"button",onclick:()=>n(ee,!1),children:(Pe,He)=>{v();var We=p("Cancel");r(Pe,We)},$$slots:{default:!0}});var ke=t($,2);de(ke,{type:"button",onclick:Ne,disabled:k,children:(Pe,He)=>{var We=Qe(),Ke=_(We);{var Xe=Ye=>{var Ze=p("Save");r(Ye,Ze)};De(Ke,Ye=>{Ye(Xe,!1)})}r(Pe,We)},$$slots:{default:!0}}),r(K,Ie)},$$slots:{default:!0}})}),a(we),r(ze,I)},$$slots:{default:!0}})}),r(q,V)},$$slots:{default:!0}})});var W=t(A,2);g(W,()=>vr,(f,h)=>{h(f,{get open(){return e(B)},set open(q){n(B,q,!0)},children:(q,b)=>{var V=Qe(),be=_(V);g(be,()=>sr,(le,$e)=>{$e(le,{children:(ze,Ae)=>{var I=fo(),pe=_(I);g(pe,()=>or,(i,y)=>{y(i,{children:(w,E)=>{var c=vo(),j=_(c);g(j,()=>lr,(re,S)=>{S(re,{children:(m,P)=>{v();var R=p("Delete Work Experience");r(m,R)},$$slots:{default:!0}})});var z=t(j,2);g(z,()=>ir,(re,S)=>{S(re,{children:(m,P)=>{v();var R=p("Are you sure you want to delete this work experience? This action cannot be undone.");r(m,R)},$$slots:{default:!0}})}),r(w,c)},$$slots:{default:!0}})});var we=t(pe,2);g(we,()=>nr,(i,y)=>{y(i,{children:(w,E)=>{var c=co(),j=_(c);g(j,()=>dr,(re,S)=>{S(re,{children:(m,P)=>{v();var R=p("Cancel");r(m,R)},$$slots:{default:!0}})});var z=t(j,2);g(z,()=>ur,(re,S)=>{S(re,{onclick:xe,children:(m,P)=>{v();var R=p("Delete");r(m,R)},$$slots:{default:!0}})}),r(w,c)},$$slots:{default:!0}})}),r(ze,I)},$$slots:{default:!0}})}),r(q,V)},$$slots:{default:!0}})}),r(Me,H),dt()}var _o=u("<!> Add Education",1),go=u('<p class="text-muted-foreground"> </p>'),ho=u('<p class="text-muted-foreground text-sm"> </p>'),xo=u('<p class="mt-2 text-sm"> </p>'),bo=u('<div class="flex items-start justify-between rounded-md border p-4"><div class="flex-1"><div class="flex items-center"><!> <h3 class="font-medium"> </h3></div> <!> <p class="text-muted-foreground text-sm"> </p> <!> <!></div> <div class="flex space-x-2"><!> <!></div></div>'),yo=u('<div class="space-y-4"></div>'),$o=u("<!> Add Education",1),Po=u('<div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8"><!> <p class="text-muted-foreground text-center">No education added yet</p> <!></div>'),wo=u("<!> <!>",1),ko=u('<p class="text-destructive text-sm"> </p>'),So=u('<p class="text-destructive text-sm"> </p>'),Do=u("<!> <!>",1),Co=u('<!> <form method="POST" class="space-y-4"><div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="space-y-2 sm:col-span-2"><!> <!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!> <!></div> <div class="space-y-2"><div class="flex items-center space-x-2"><!> <!></div></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div></div> <div class="space-y-2"><!> <!></div> <!></form>',1),Io=u("<!> <!>",1),Eo=u("<!> <!>",1),jo=u("<!> <!>",1),Ro=u('<div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Education</h2> <!></div> <div class="mt-4"><!></div></div> <!> <!>',1);function Lo(Me,o){nt(o,!0);const l=vt(o,"disabled",3,!1);let d=me(at({institution:"",degree:"",fieldOfStudy:"",startDate:"",endDate:"",current:!1,gpa:"",description:""})),x=me(!1),k=me(at({})),O=me(at(o.data||[])),ee=me(!1),B=me(!1),te=me(null),F=me(null);function ge(){n(d,{institution:"",degree:"",fieldOfStudy:"",startDate:"",endDate:"",current:!1,gpa:"",description:""},!0),n(te,null),n(ee,!0)}function Ee(f){const h=e(O)[f];n(d,{...h},!0),n(te,f,!0),n(ee,!0)}function ue(f){n(F,f,!0),n(B,!0)}function xe(){e(F)!==null&&(n(O,e(O).filter((f,h)=>h!==e(F)),!0),he(),n(B,!1),n(F,null))}async function Ne(){n(k,{},!0);let f=!0;if(e(d).institution||(e(k).institution="Institution is required",f=!1),e(d).startDate||(e(k).startDate="Start date is required",f=!1),!f){Oe.error("Please fill in all required fields");return}n(x,!0);try{const h={...e(d)};e(te)!==null?e(O)[e(te)]=h:n(O,[...e(O),h],!0),await he(),n(ee,!1)}catch(h){console.error("Error saving education:",h),Oe.error("Failed to save education")}finally{n(x,!1)}}async function he(){const f=await o.onSave(e(O));return f&&Oe.success("Education updated successfully"),f}function X(f){return f?new Date(f).toLocaleDateString("en-US",{year:"numeric",month:"short"}):""}function se(f,h,q){const b=X(f),V=q?"Present":X(h);return`${b} - ${V}`}var H=Ro(),G=_(H),L=s(G),oe=t(s(L),2);de(oe,{variant:"outline",size:"sm",onclick:ge,get disabled(){return l()},children:(f,h)=>{var q=_o(),b=_(q);St(b,{class:"mr-2 h-4 w-4"}),v(),r(f,q)},$$slots:{default:!0}}),a(L);var Ce=t(L,2),Ue=s(Ce);{var Z=f=>{var h=yo();lt(h,21,()=>e(O),it,(q,b,V)=>{var be=bo(),le=s(be),$e=s(le),ze=s($e);Dr(ze,{class:"mr-2 h-5 w-5 text-blue-500"});var Ae=t(ze,2),I=s(Ae,!0);a(Ae),a($e);var pe=t($e,2);{var we=m=>{var P=go(),R=s(P);a(P),ne(()=>C(R,`${e(b).degree??""}${e(b).degree&&e(b).fieldOfStudy?", ":""}
                  ${e(b).fieldOfStudy??""}`)),r(m,P)};De(pe,m=>{(e(b).degree||e(b).fieldOfStudy)&&m(we)})}var i=t(pe,2),y=s(i,!0);a(i);var w=t(i,2);{var E=m=>{var P=ho(),R=s(P);a(P),ne(()=>C(R,`GPA: ${e(b).gpa??""}`)),r(m,P)};De(w,m=>{e(b).gpa&&m(E)})}var c=t(w,2);{var j=m=>{var P=xo(),R=s(P,!0);a(P),ne(()=>C(R,e(b).description)),r(m,P)};De(c,m=>{e(b).description&&m(j)})}a(le);var z=t(le,2),re=s(z);de(re,{variant:"ghost",size:"icon",onclick:()=>Ee(V),get disabled(){return l()},children:(m,P)=>{ht(m,{class:"h-4 w-4"})},$$slots:{default:!0}});var S=t(re,2);de(S,{variant:"ghost",size:"icon",onclick:()=>ue(V),get disabled(){return l()},children:(m,P)=>{Tt(m,{class:"h-4 w-4"})},$$slots:{default:!0}}),a(z),a(be),ne(m=>{C(I,e(b).institution),C(y,m)},[()=>se(e(b).startDate,e(b).endDate,e(b).current)]),r(q,be)}),a(h),r(f,h)},N=f=>{var h=Po(),q=s(h);Dr(q,{class:"text-muted-foreground mb-2 h-10 w-10"});var b=t(q,4);de(b,{variant:"outline",class:"mt-4",onclick:ge,get disabled(){return l()},children:(V,be)=>{var le=$o(),$e=_(le);St($e,{class:"mr-2 h-4 w-4"}),v(),r(V,le)},$$slots:{default:!0}}),a(h),r(f,h)};De(Ue,f=>{e(O).length>0?f(Z):f(N,!1)})}a(Ce),a(G);var A=t(G,2);g(A,()=>bt,(f,h)=>{h(f,{get open(){return e(ee)},set open(q){n(ee,q,!0)},children:(q,b)=>{var V=Qe(),be=_(V);g(be,()=>xt,(le,$e)=>{$e(le,{class:"sm:max-w-[600px]",children:(ze,Ae)=>{var I=Co(),pe=_(I);g(pe,()=>yt,(T,K)=>{K(T,{children:(M,Ie)=>{var $=wo(),ke=_($);g(ke,()=>$t,(He,We)=>{We(He,{children:(Ke,Xe)=>{v();var Ye=p();ne(()=>C(Ye,e(te)!==null?"Edit Education":"Add Education")),r(Ke,Ye)},$$slots:{default:!0}})});var Pe=t(ke,2);g(Pe,()=>Pt,(He,We)=>{We(He,{children:(Ke,Xe)=>{v();var Ye=p();ne(()=>C(Ye,e(te)!==null?"Update your education details.":"Add a new education to your profile.")),r(Ke,Ye)},$$slots:{default:!0}})}),r(M,$)},$$slots:{default:!0}})});var we=t(pe,2),i=s(we),y=s(i),w=s(y);Re(w,{for:"institution",children:(T,K)=>{v();var M=p("Institution *");r(T,M)},$$slots:{default:!0}});var E=t(w,2);et(E,{id:"institution",get value(){return e(d).institution},set value(T){e(d).institution=T}});var c=t(E,2);{var j=T=>{var K=ko(),M=s(K,!0);a(K),ne(()=>C(M,e(k).institution)),r(T,K)};De(c,T=>{e(k).institution&&T(j)})}a(y);var z=t(y,2),re=s(z);Re(re,{for:"degree",children:(T,K)=>{v();var M=p("Degree");r(T,M)},$$slots:{default:!0}});var S=t(re,2);et(S,{id:"degree",get value(){return e(d).degree},set value(T){e(d).degree=T}}),a(z);var m=t(z,2),P=s(m);Re(P,{for:"fieldOfStudy",children:(T,K)=>{v();var M=p("Field of Study");r(T,M)},$$slots:{default:!0}});var R=t(P,2);et(R,{id:"fieldOfStudy",get value(){return e(d).fieldOfStudy},set value(T){e(d).fieldOfStudy=T}}),a(m);var Q=t(m,2),fe=s(Q);Re(fe,{for:"startDate",children:(T,K)=>{v();var M=p("Start Date *");r(T,M)},$$slots:{default:!0}});var U=t(fe,2);et(U,{id:"startDate",type:"month",get value(){return e(d).startDate},set value(T){e(d).startDate=T}});var ve=t(U,2);{var Se=T=>{var K=So(),M=s(K,!0);a(K),ne(()=>C(M,e(k).startDate)),r(T,K)};De(ve,T=>{e(k).startDate&&T(Se)})}a(Q);var je=t(Q,2),ce=s(je),ie=s(ce);g(ie,()=>Jt,(T,K)=>{K(T,{id:"current",get checked(){return e(d).current},onCheckedChange:M=>e(d).current=M})});var Le=t(ie,2);Re(Le,{for:"current",children:(T,K)=>{v();var M=p("I am currently studying here");r(T,M)},$$slots:{default:!0}}),a(ce),a(je);var ae=t(je,2),qe=s(ae);Re(qe,{for:"endDate",children:(T,K)=>{v();var M=p("End Date");r(T,M)},$$slots:{default:!0}});var Je=t(qe,2);et(Je,{id:"endDate",type:"month",get disabled(){return e(d).current},get value(){return e(d).endDate},set value(T){e(d).endDate=T}}),a(ae);var Ve=t(ae,2),Be=s(Ve);Re(Be,{for:"gpa",children:(T,K)=>{v();var M=p("GPA");r(T,M)},$$slots:{default:!0}});var tt=t(Be,2);et(tt,{id:"gpa",get value(){return e(d).gpa},set value(T){e(d).gpa=T}}),a(Ve),a(i);var D=t(i,2),Y=s(D);Re(Y,{for:"description",children:(T,K)=>{v();var M=p("Description");r(T,M)},$$slots:{default:!0}});var ye=t(Y,2);ar(ye,{id:"description",rows:4,placeholder:"Describe your studies, achievements, or activities...",get value(){return e(d).description},set value(T){e(d).description=T}}),a(D);var J=t(D,2);g(J,()=>wt,(T,K)=>{K(T,{children:(M,Ie)=>{var $=Do(),ke=_($);de(ke,{variant:"outline",type:"button",onclick:()=>n(ee,!1),children:(He,We)=>{v();var Ke=p("Cancel");r(He,Ke)},$$slots:{default:!0}});var Pe=t(ke,2);de(Pe,{type:"button",onclick:Ne,get disabled(){return e(x)},children:(He,We)=>{var Ke=Qe(),Xe=_(Ke);{var Ye=st=>{var _t=p("Saving...");r(st,_t)},Ze=st=>{var _t=p("Save");r(st,_t)};De(Xe,st=>{e(x)?st(Ye):st(Ze,!1)})}r(He,Ke)},$$slots:{default:!0}}),r(M,$)},$$slots:{default:!0}})}),a(we),r(ze,I)},$$slots:{default:!0}})}),r(q,V)},$$slots:{default:!0}})});var W=t(A,2);g(W,()=>vr,(f,h)=>{h(f,{get open(){return e(B)},set open(q){n(B,q,!0)},children:(q,b)=>{var V=Qe(),be=_(V);g(be,()=>sr,(le,$e)=>{$e(le,{children:(ze,Ae)=>{var I=jo(),pe=_(I);g(pe,()=>or,(i,y)=>{y(i,{children:(w,E)=>{var c=Io(),j=_(c);g(j,()=>lr,(re,S)=>{S(re,{children:(m,P)=>{v();var R=p("Delete Education");r(m,R)},$$slots:{default:!0}})});var z=t(j,2);g(z,()=>ir,(re,S)=>{S(re,{children:(m,P)=>{v();var R=p("Are you sure you want to delete this education? This action cannot be undone.");r(m,R)},$$slots:{default:!0}})}),r(w,c)},$$slots:{default:!0}})});var we=t(pe,2);g(we,()=>nr,(i,y)=>{y(i,{children:(w,E)=>{var c=Eo(),j=_(c);g(j,()=>dr,(re,S)=>{S(re,{children:(m,P)=>{v();var R=p("Cancel");r(m,R)},$$slots:{default:!0}})});var z=t(j,2);g(z,()=>ur,(re,S)=>{S(re,{onclick:xe,children:(m,P)=>{v();var R=p("Delete");r(m,R)},$$slots:{default:!0}})}),r(w,c)},$$slots:{default:!0}})}),r(ze,I)},$$slots:{default:!0}})}),r(q,V)},$$slots:{default:!0}})}),r(Me,H),dt()}var No=u("<!> Add Project",1),Ao=u('<p class="text-muted-foreground text-sm"> </p>'),Uo=u('<a target="_blank" rel="noopener noreferrer" class="text-primary flex items-center text-sm hover:underline"><!> Project Link</a>'),To=u('<p class="mt-2 text-sm"> </p>'),Oo=u('<span class="bg-primary/10 text-primary rounded-full px-2 py-0.5 text-xs"> </span>'),Fo=u('<div class="mt-2 flex flex-wrap gap-1"></div>'),zo=u('<div class="flex items-start justify-between rounded-md border p-4"><div class="flex-1"><div class="flex items-center"><!> <h3 class="font-medium"> </h3></div> <!> <!> <!> <!></div> <div class="flex space-x-2"><!> <!></div></div>'),Mo=u('<div class="space-y-4"></div>'),Ho=u("<!> Add Project",1),qo=u('<div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8"><!> <p class="text-muted-foreground text-center">No projects added yet</p> <!></div>'),Jo=u("<!> <!>",1),Wo=u('<p class="text-destructive text-sm"> </p>'),Vo=u('<p class="text-destructive text-sm"> </p>'),Bo=(Me,o,l)=>o(e(l)),Yo=u('<span class="bg-primary/10 text-primary flex items-center rounded-full px-3 py-1 text-sm"> <button type="button" class="hover:bg-primary/20 ml-1 rounded-full p-0.5"><!></button></span>'),Ko=u('<div class="mt-2 flex flex-wrap gap-2"></div>'),Go=u("<!> <!>",1),Qo=Xr(u(`<!> <form method="POST" class="space-y-4"><div class="space-y-2"><!> <!> <!></div> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="space-y-2"><!> <!></div> <div class="space-y-2"><div class="flex items-center space-x-2"><!> <!></div></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!> <!></div></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <div class="flex items-center space-x-2"><!> <script>
            const handleKeydown = (e: KeyboardEvent) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addSkill();
              }
            };

            document.addEventListener('keydown', handleKeydown);
          <\/script> <!></div> <!></div> <!></form>`,1)),Xo=u("<!> <!>",1),Zo=u("<!> <!>",1),el=u("<!> <!>",1),tl=u('<div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Projects & Outside Experience</h2> <!></div> <div class="mt-4"><!></div></div> <!> <!>',1);function rl(Me,o){nt(o,!0);const l=vt(o,"disabled",3,!1);let d=me(at({title:"",description:"",startDate:"",endDate:"",current:!1,url:"",skills:[]})),x=me(!1),k=me(at({})),O=me(at(o.data||[])),ee=me(!1),B=me(!1),te=me(null),F=me(null),ge=me("");function Ee(){n(d,{title:"",description:"",startDate:"",endDate:"",current:!1,url:"",skills:[]},!0),n(te,null),n(ee,!0)}function ue(b){const V=e(O)[b];n(d,{...V},!0),n(te,b,!0),n(ee,!0)}function xe(b){n(F,b,!0),n(B,!0)}function Ne(){e(F)!==null&&(n(O,e(O).filter((b,V)=>V!==e(F)),!0),H(),n(B,!1),n(F,null))}function he(){var b;e(ge)&&!((b=e(d).skills)!=null&&b.includes(e(ge)))&&(e(d).skills=[...e(d).skills||[],e(ge)],n(ge,""))}function X(b){var V;e(d).skills=((V=e(d).skills)==null?void 0:V.filter(be=>be!==b))||[]}async function se(){n(k,{},!0);let b=!0;if(e(d).title||(e(k).title="Title is required",b=!1),!b){Oe.error("Please fill in all required fields");return}n(x,!0);try{const V={...e(d)};e(te)!==null?e(O)[e(te)]=V:n(O,[...e(O),V],!0),await H(),n(ee,!1)}catch(V){console.error("Error saving project:",V),Oe.error("Failed to save project")}finally{n(x,!1)}}async function H(){const b=await o.onSave(e(O));return b&&Oe.success("Projects updated successfully"),b}function G(b){return b?new Date(b).toLocaleDateString("en-US",{year:"numeric",month:"short"}):""}function L(b,V,be){if(!b)return"";const le=G(b),$e=be?"Present":G(V);return le&&$e?`${le} - ${$e}`:le||$e}var oe=tl(),Ce=_(oe),Ue=s(Ce),Z=t(s(Ue),2);de(Z,{variant:"outline",size:"sm",onclick:Ee,get disabled(){return l()},children:(b,V)=>{var be=No(),le=_(be);St(le,{class:"mr-2 h-4 w-4"}),v(),r(b,be)},$$slots:{default:!0}}),a(Ue);var N=t(Ue,2),A=s(N);{var W=b=>{var V=Mo();lt(V,21,()=>e(O),it,(be,le,$e)=>{var ze=zo(),Ae=s(ze),I=s(Ae),pe=s(I);kr(pe,{class:"mr-2 h-5 w-5 text-blue-500"});var we=t(pe,2),i=s(we,!0);a(we),a(I);var y=t(I,2);{var w=Q=>{var fe=Ao(),U=s(fe,!0);a(fe),ne(ve=>C(U,ve),[()=>L(e(le).startDate,e(le).endDate,e(le).current)]),r(Q,fe)};De(y,Q=>{e(le).startDate&&Q(w)})}var E=t(y,2);{var c=Q=>{var fe=Uo(),U=s(fe);cr(U,{class:"mr-1 h-3 w-3"}),v(),a(fe),ne(()=>Et(fe,"href",e(le).url)),r(Q,fe)};De(E,Q=>{e(le).url&&Q(c)})}var j=t(E,2);{var z=Q=>{var fe=To(),U=s(fe,!0);a(fe),ne(()=>C(U,e(le).description)),r(Q,fe)};De(j,Q=>{e(le).description&&Q(z)})}var re=t(j,2);{var S=Q=>{var fe=Fo();lt(fe,21,()=>e(le).skills,it,(U,ve)=>{var Se=Oo(),je=s(Se,!0);a(Se),ne(()=>C(je,e(ve))),r(U,Se)}),a(fe),r(Q,fe)};De(re,Q=>{e(le).skills&&e(le).skills.length>0&&Q(S)})}a(Ae);var m=t(Ae,2),P=s(m);de(P,{variant:"ghost",size:"icon",onclick:()=>ue($e),get disabled(){return l()},children:(Q,fe)=>{ht(Q,{class:"h-4 w-4"})},$$slots:{default:!0}});var R=t(P,2);de(R,{variant:"ghost",size:"icon",onclick:()=>xe($e),get disabled(){return l()},children:(Q,fe)=>{Tt(Q,{class:"h-4 w-4"})},$$slots:{default:!0}}),a(m),a(ze),ne(()=>C(i,e(le).title)),r(be,ze)}),a(V),r(b,V)},f=b=>{var V=qo(),be=s(V);kr(be,{class:"text-muted-foreground mb-2 h-10 w-10"});var le=t(be,4);de(le,{variant:"outline",class:"mt-4",onclick:Ee,get disabled(){return l()},children:($e,ze)=>{var Ae=Ho(),I=_(Ae);St(I,{class:"mr-2 h-4 w-4"}),v(),r($e,Ae)},$$slots:{default:!0}}),a(V),r(b,V)};De(A,b=>{e(O).length>0?b(W):b(f,!1)})}a(N),a(Ce);var h=t(Ce,2);g(h,()=>bt,(b,V)=>{V(b,{get open(){return e(ee)},set open(be){n(ee,be,!0)},children:(be,le)=>{var $e=Qe(),ze=_($e);g(ze,()=>xt,(Ae,I)=>{I(Ae,{class:"sm:max-w-[600px]",children:(pe,we)=>{var i=Qo(),y=_(i);g(y,()=>yt,($,ke)=>{ke($,{children:(Pe,He)=>{var We=Jo(),Ke=_(We);g(Ke,()=>$t,(Ye,Ze)=>{Ze(Ye,{children:(st,_t)=>{v();var ut=p();ne(()=>C(ut,e(te)!==null?"Edit Project":"Add Project")),r(st,ut)},$$slots:{default:!0}})});var Xe=t(Ke,2);g(Xe,()=>Pt,(Ye,Ze)=>{Ze(Ye,{children:(st,_t)=>{v();var ut=p();ne(()=>C(ut,e(te)!==null?"Update your project details.":"Add a new project to your profile.")),r(st,ut)},$$slots:{default:!0}})}),r(Pe,We)},$$slots:{default:!0}})});var w=t(y,2),E=s(w),c=s(E);Re(c,{for:"title",children:($,ke)=>{v();var Pe=p("Project Title *");r($,Pe)},$$slots:{default:!0}});var j=t(c,2);et(j,{id:"title",get value(){return e(d).title},set value($){e(d).title=$}});var z=t(j,2);{var re=$=>{var ke=Wo(),Pe=s(ke,!0);a(ke),ne(()=>C(Pe,e(k).title)),r($,ke)};De(z,$=>{e(k).title&&$(re)})}a(E);var S=t(E,2),m=s(S),P=s(m);Re(P,{for:"startDate",children:($,ke)=>{v();var Pe=p("Start Date");r($,Pe)},$$slots:{default:!0}});var R=t(P,2);et(R,{id:"startDate",type:"month",get value(){return e(d).startDate},set value($){e(d).startDate=$}}),a(m);var Q=t(m,2),fe=s(Q),U=s(fe);g(U,()=>Jt,($,ke)=>{ke($,{id:"current",get checked(){return e(d).current},onCheckedChange:Pe=>e(d).current=Pe})});var ve=t(U,2);Re(ve,{for:"current",children:($,ke)=>{v();var Pe=p("This is an ongoing project");r($,Pe)},$$slots:{default:!0}}),a(fe),a(Q);var Se=t(Q,2),je=s(Se);Re(je,{for:"endDate",children:($,ke)=>{v();var Pe=p("End Date");r($,Pe)},$$slots:{default:!0}});var ce=t(je,2);et(ce,{id:"endDate",type:"month",get disabled(){return e(d).current},get value(){return e(d).endDate},set value($){e(d).endDate=$}}),a(Se);var ie=t(Se,2),Le=s(ie);Re(Le,{for:"url",children:($,ke)=>{v();var Pe=p("Project URL");r($,Pe)},$$slots:{default:!0}});var ae=t(Le,2);et(ae,{id:"url",placeholder:"https://",get value(){return e(d).url},set value($){e(d).url=$}});var qe=t(ae,2);{var Je=$=>{var ke=Vo(),Pe=s(ke,!0);a(ke),ne(()=>C(Pe,e(k).url)),r($,ke)};De(qe,$=>{e(k).url&&$(Je)})}a(ie),a(S);var Ve=t(S,2),Be=s(Ve);Re(Be,{for:"description",children:($,ke)=>{v();var Pe=p("Description");r($,Pe)},$$slots:{default:!0}});var tt=t(Be,2);ar(tt,{id:"description",rows:4,placeholder:"Describe your project, its purpose, and your role...",get value(){return e(d).description},set value($){e(d).description=$}}),a(Ve);var D=t(Ve,2),Y=s(D);Re(Y,{children:($,ke)=>{v();var Pe=p("Skills Used");r($,Pe)},$$slots:{default:!0}});var ye=t(Y,2),J=s(ye);et(J,{placeholder:"Add a skill",get value(){return e(ge)},set value($){n(ge,$,!0)}});var T=t(J,4);de(T,{type:"button",variant:"outline",onclick:he,get disabled(){return l()},children:($,ke)=>{v();var Pe=p("Add");r($,Pe)},$$slots:{default:!0}}),a(ye);var K=t(ye,2);{var M=$=>{var ke=Ko();lt(ke,21,()=>e(d).skills,it,(Pe,He)=>{var We=Yo(),Ke=s(We),Xe=t(Ke);Xe.__click=[Bo,X,He];var Ye=s(Xe);Tt(Ye,{class:"h-3 w-3"}),a(Xe),a(We),ne(()=>{C(Ke,`${e(He)??""} `),Xe.disabled=l()}),r(Pe,We)}),a(ke),r($,ke)};De(K,$=>{e(d).skills&&e(d).skills.length>0&&$(M)})}a(D);var Ie=t(D,2);g(Ie,()=>wt,($,ke)=>{ke($,{children:(Pe,He)=>{var We=Go(),Ke=_(We);de(Ke,{variant:"outline",type:"button",onclick:()=>n(ee,!1),children:(Ye,Ze)=>{v();var st=p("Cancel");r(Ye,st)},$$slots:{default:!0}});var Xe=t(Ke,2);de(Xe,{type:"button",onclick:se,get disabled(){return e(x)},children:(Ye,Ze)=>{var st=Qe(),_t=_(st);{var ut=gt=>{var Dt=p("Saving...");r(gt,Dt)},ft=gt=>{var Dt=p("Save");r(gt,Dt)};De(_t,gt=>{e(x)?gt(ut):gt(ft,!1)})}r(Ye,st)},$$slots:{default:!0}}),r(Pe,We)},$$slots:{default:!0}})}),a(w),r(pe,i)},$$slots:{default:!0}})}),r(be,$e)},$$slots:{default:!0}})});var q=t(h,2);g(q,()=>vr,(b,V)=>{V(b,{get open(){return e(B)},set open(be){n(B,be,!0)},children:(be,le)=>{var $e=Qe(),ze=_($e);g(ze,()=>sr,(Ae,I)=>{I(Ae,{children:(pe,we)=>{var i=el(),y=_(i);g(y,()=>or,(E,c)=>{c(E,{children:(j,z)=>{var re=Xo(),S=_(re);g(S,()=>lr,(P,R)=>{R(P,{children:(Q,fe)=>{v();var U=p("Delete Project");r(Q,U)},$$slots:{default:!0}})});var m=t(S,2);g(m,()=>ir,(P,R)=>{R(P,{children:(Q,fe)=>{v();var U=p("Are you sure you want to delete this project? This action cannot be undone.");r(Q,U)},$$slots:{default:!0}})}),r(j,re)},$$slots:{default:!0}})});var w=t(y,2);g(w,()=>nr,(E,c)=>{c(E,{children:(j,z)=>{var re=Zo(),S=_(re);g(S,()=>dr,(P,R)=>{R(P,{children:(Q,fe)=>{v();var U=p("Cancel");r(Q,U)},$$slots:{default:!0}})});var m=t(S,2);g(m,()=>ur,(P,R)=>{R(P,{onclick:Ne,children:(Q,fe)=>{v();var U=p("Delete");r(Q,U)},$$slots:{default:!0}})}),r(j,re)},$$slots:{default:!0}})}),r(pe,i)},$$slots:{default:!0}})}),r(be,$e)},$$slots:{default:!0}})}),r(Me,oe),dt()}Lt(["click"]);var al=u("<!> <!>",1),sl=u('<p class="text-destructive text-sm"> </p>'),ol=u('<p class="text-destructive text-sm"> </p>'),ll=u('<p class="text-destructive text-sm"> </p>'),il=u('<p class="text-destructive text-sm"> </p>'),nl=u('<span class="mr-2">Saving...</span>'),dl=u("<!> Save Changes",1),ul=u("<!> <!>",1),vl=u('<!> <div class="grid gap-4 py-4"><div class="grid gap-2"><!> <div class="relative"><!> <!></div> <!></div> <div class="grid gap-2"><!> <div class="relative"><!> <!></div> <!></div> <div class="grid gap-2"><!> <div class="relative"><!> <!></div> <!></div> <div class="grid gap-2"><!> <div class="relative"><!> <!></div> <!></div></div> <!>',1);function cl(Me,o){nt(o,!0);let l=me(at(o.data||{linkedinUrl:"",githubUrl:"",portfolioUrl:"",otherUrl:""})),d=me(!1),x=me(at({}));kt(()=>{o.open&&(n(l,{linkedinUrl:o.data.linkedinUrl||"",githubUrl:o.data.githubUrl||"",portfolioUrl:o.data.portfolioUrl||"",otherUrl:o.data.otherUrl||""},!0),n(x,{},!0))});async function k(){n(x,{},!0),n(d,!0);try{await o.onSave(e(l))&&(Oe.success("Portfolio links updated successfully"),o.onClose())}catch(B){console.error("Error saving portfolio links:",B),Oe.error("Failed to save portfolio links")}finally{n(d,!1)}}var O=Qe(),ee=_(O);g(ee,()=>bt,(B,te)=>{te(B,{get open(){return o.open},onOpenChange:F=>!F&&o.onClose(),children:(F,ge)=>{var Ee=Qe(),ue=_(Ee);g(ue,()=>xt,(xe,Ne)=>{Ne(xe,{class:"sm:max-w-[500px]",children:(he,X)=>{var se=vl(),H=_(se);g(H,()=>yt,(S,m)=>{m(S,{children:(P,R)=>{var Q=al(),fe=_(Q);g(fe,()=>$t,(ve,Se)=>{Se(ve,{children:(je,ce)=>{v();var ie=p("Edit Portfolio & Links");r(je,ie)},$$slots:{default:!0}})});var U=t(fe,2);g(U,()=>Pt,(ve,Se)=>{Se(ve,{children:(je,ce)=>{v();var ie=p("Update your professional links. Click save when you're done.");r(je,ie)},$$slots:{default:!0}})}),r(P,Q)},$$slots:{default:!0}})});var G=t(H,2),L=s(G),oe=s(L);Re(oe,{for:"linkedinUrl",children:(S,m)=>{v();var P=p("LinkedIn URL");r(S,P)},$$slots:{default:!0}});var Ce=t(oe,2),Ue=s(Ce);Lr(Ue,{class:"text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"});var Z=t(Ue,2);et(Z,{id:"linkedinUrl",placeholder:"https://linkedin.com/in/yourprofile",class:"pl-10",get value(){return e(l).linkedinUrl},set value(S){e(l).linkedinUrl=S}}),a(Ce);var N=t(Ce,2);{var A=S=>{var m=sl(),P=s(m,!0);a(m),ne(()=>C(P,e(x).linkedinUrl)),r(S,m)};De(N,S=>{e(x).linkedinUrl&&S(A)})}a(L);var W=t(L,2),f=s(W);Re(f,{for:"githubUrl",children:(S,m)=>{v();var P=p("GitHub URL");r(S,P)},$$slots:{default:!0}});var h=t(f,2),q=s(h);Nr(q,{class:"text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"});var b=t(q,2);et(b,{id:"githubUrl",placeholder:"https://github.com/yourusername",class:"pl-10",get value(){return e(l).githubUrl},set value(S){e(l).githubUrl=S}}),a(h);var V=t(h,2);{var be=S=>{var m=ol(),P=s(m,!0);a(m),ne(()=>C(P,e(x).githubUrl)),r(S,m)};De(V,S=>{e(x).githubUrl&&S(be)})}a(W);var le=t(W,2),$e=s(le);Re($e,{for:"portfolioUrl",children:(S,m)=>{v();var P=p("Portfolio URL");r(S,P)},$$slots:{default:!0}});var ze=t($e,2),Ae=s(ze);Or(Ae,{class:"text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"});var I=t(Ae,2);et(I,{id:"portfolioUrl",placeholder:"https://yourportfolio.com",class:"pl-10",get value(){return e(l).portfolioUrl},set value(S){e(l).portfolioUrl=S}}),a(ze);var pe=t(ze,2);{var we=S=>{var m=ll(),P=s(m,!0);a(m),ne(()=>C(P,e(x).portfolioUrl)),r(S,m)};De(pe,S=>{e(x).portfolioUrl&&S(we)})}a(le);var i=t(le,2),y=s(i);Re(y,{for:"otherUrl",children:(S,m)=>{v();var P=p("Other URL");r(S,P)},$$slots:{default:!0}});var w=t(y,2),E=s(w);cr(E,{class:"text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"});var c=t(E,2);et(c,{id:"otherUrl",placeholder:"https://example.com",class:"pl-10",get value(){return e(l).otherUrl},set value(S){e(l).otherUrl=S}}),a(w);var j=t(w,2);{var z=S=>{var m=il(),P=s(m,!0);a(m),ne(()=>C(P,e(x).otherUrl)),r(S,m)};De(j,S=>{e(x).otherUrl&&S(z)})}a(i),a(G);var re=t(G,2);g(re,()=>wt,(S,m)=>{m(S,{children:(P,R)=>{var Q=ul(),fe=_(Q);de(fe,{variant:"outline",get onclick(){return o.onClose},children:(ve,Se)=>{v();var je=p("Cancel");r(ve,je)},$$slots:{default:!0}});var U=t(fe,2);de(U,{onclick:k,get disabled(){return e(d)},children:(ve,Se)=>{var je=Qe(),ce=_(je);{var ie=ae=>{var qe=nl();r(ae,qe)},Le=ae=>{var qe=dl(),Je=_(qe);Ct(Je,{class:"mr-2 h-4 w-4"}),v(),r(ae,qe)};De(ce,ae=>{e(d)?ae(ie):ae(Le,!1)})}r(ve,je)},$$slots:{default:!0}}),r(P,Q)},$$slots:{default:!0}})}),r(he,se)},$$slots:{default:!0}})}),r(F,Ee)},$$slots:{default:!0}})}),r(Me,O),dt()}var fl=u("<!> Edit",1),ml=u('<a target="_blank" rel="noopener noreferrer" class="text-primary text-sm hover:underline"> </a>'),pl=u('<p class="text-muted-foreground text-sm">Not specified</p>'),_l=u('<a target="_blank" rel="noopener noreferrer" class="text-primary text-sm hover:underline"> </a>'),gl=u('<p class="text-muted-foreground text-sm">Not specified</p>'),hl=u('<a target="_blank" rel="noopener noreferrer" class="text-primary text-sm hover:underline"> </a>'),xl=u('<p class="text-muted-foreground text-sm">Not specified</p>'),bl=u('<a target="_blank" rel="noopener noreferrer" class="text-primary text-sm hover:underline"> </a>'),yl=u('<p class="text-muted-foreground text-sm">Not specified</p>'),$l=u('<div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Portfolio & Links</h2> <!></div> <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="rounded-md border p-4"><div class="flex items-center"><div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="font-medium">LinkedIn URL</h3> <!></div></div></div> <div class="rounded-md border p-4"><div class="flex items-center"><div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="font-medium">GitHub URL</h3> <!></div></div></div> <div class="rounded-md border p-4"><div class="flex items-center"><div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="font-medium">Portfolio URL</h3> <!></div></div></div> <div class="rounded-md border p-4"><div class="flex items-center"><div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="font-medium">Other URL</h3> <!></div></div></div></div></div> <!>',1);function Pl(Me,o){nt(o,!0);const l=vt(o,"disabled",3,!1);let d=me(!1);function x(){n(d,!0)}async function k(E){try{return await o.onSave(E)}catch(c){return console.error("Error saving portfolio links:",c),Oe.error("Failed to save portfolio links"),!1}}function O(E){return E?E.replace(/^https?:\/\/(www\.)?/,""):""}var ee=$l(),B=_(ee),te=s(B),F=t(s(te),2);de(F,{variant:"ghost",size:"sm",onclick:x,get disabled(){return l()},children:(E,c)=>{var j=fl(),z=_(j);ht(z,{class:"mr-2 h-4 w-4"}),v(),r(E,j)},$$slots:{default:!0}}),a(te);var ge=t(te,2),Ee=s(ge),ue=s(Ee),xe=s(ue),Ne=s(xe);Lr(Ne,{class:"text-primary h-5 w-5"}),a(xe);var he=t(xe,2),X=t(s(he),2);{var se=E=>{var c=ml(),j=s(c,!0);a(c),ne(z=>{Et(c,"href",o.data.linkedinUrl),C(j,z)},[()=>O(o.data.linkedinUrl)]),r(E,c)},H=E=>{var c=pl();r(E,c)};De(X,E=>{var c;(c=o.data)!=null&&c.linkedinUrl?E(se):E(H,!1)})}a(he),a(ue),a(Ee);var G=t(Ee,2),L=s(G),oe=s(L),Ce=s(oe);Nr(Ce,{class:"text-primary h-5 w-5"}),a(oe);var Ue=t(oe,2),Z=t(s(Ue),2);{var N=E=>{var c=_l(),j=s(c,!0);a(c),ne(z=>{Et(c,"href",o.data.githubUrl),C(j,z)},[()=>O(o.data.githubUrl)]),r(E,c)},A=E=>{var c=gl();r(E,c)};De(Z,E=>{var c;(c=o.data)!=null&&c.githubUrl?E(N):E(A,!1)})}a(Ue),a(L),a(G);var W=t(G,2),f=s(W),h=s(f),q=s(h);Or(q,{class:"text-primary h-5 w-5"}),a(h);var b=t(h,2),V=t(s(b),2);{var be=E=>{var c=hl(),j=s(c,!0);a(c),ne(z=>{Et(c,"href",o.data.portfolioUrl),C(j,z)},[()=>O(o.data.portfolioUrl)]),r(E,c)},le=E=>{var c=xl();r(E,c)};De(V,E=>{var c;(c=o.data)!=null&&c.portfolioUrl?E(be):E(le,!1)})}a(b),a(f),a(W);var $e=t(W,2),ze=s($e),Ae=s(ze),I=s(Ae);cr(I,{class:"text-primary h-5 w-5"}),a(Ae);var pe=t(Ae,2),we=t(s(pe),2);{var i=E=>{var c=bl(),j=s(c,!0);a(c),ne(z=>{Et(c,"href",o.data.otherUrl),C(j,z)},[()=>O(o.data.otherUrl)]),r(E,c)},y=E=>{var c=yl();r(E,c)};De(we,E=>{var c;(c=o.data)!=null&&c.otherUrl?E(i):E(y,!1)})}a(pe),a(ze),a($e),a(ge),a(B);var w=t(B,2);cl(w,{get open(){return e(d)},get data(){return o.data},onClose:()=>n(d,!1),onSave:k,get disabled(){return l()}}),r(Me,ee),dt()}var wl=u("<!> <!>",1),kl=u("<!> Add",1),Sl=(Me,o,l)=>o(e(l)),Dl=u(' <button type="button" class="hover:bg-primary/20 ml-1 rounded-full p-0.5"><!></button>',1),Cl=u('<div class="space-y-2"><!> <div class="flex flex-wrap gap-2"></div></div>'),Il=u('<p class="text-muted-foreground text-sm">No skills added yet</p>'),El=u("<!> ",1),jl=u("<!> <!>",1),Rl=u('<!> <div class="py-4"><div class="space-y-4"><div class="space-y-2"><!> <div class="flex items-center space-x-2"><!> <!></div></div> <!> <div class="space-y-2"><!> <!> <div class="flex max-h-40 flex-wrap gap-2 overflow-y-auto"></div></div></div></div> <!>',1),Ll=u("<!> <!>",1);function Nl(Me,o){nt(o,!0);const l=vt(o,"disabled",3,!1);let d=me(at([])),x=me(""),k=me(!1),O=me(at([])),ee=me(at([])),B=me("");kt(()=>{o.open&&(n(d,[...o.skills],!0),n(x,""),n(B,""),te())});async function te(){try{const X=await fetch("/api/skills");if(X.ok){const se=await X.json();n(O,se,!0),F()}}catch(X){console.error("Error fetching skills:",X)}}function F(){e(B)?n(ee,e(O).filter(X=>X.name.toLowerCase().includes(e(B).toLowerCase())).slice(0,20),!0):n(ee,e(O).slice(0,20),!0)}kt(()=>{F()});function ge(){e(x)&&!e(d).includes(e(x))&&(n(d,[...e(d),e(x)],!0),n(x,""))}function Ee(X){e(d).includes(X)||n(d,[...e(d),X],!0)}function ue(X){n(d,e(d).filter(se=>se!==X),!0)}async function xe(){n(k,!0);try{await o.onSave(e(d))&&(Oe.success("Skills updated successfully"),o.onClose())}catch(X){console.error("Error saving skills:",X),Oe.error("Failed to save skills")}finally{n(k,!1)}}var Ne=Qe(),he=_(Ne);g(he,()=>bt,(X,se)=>{se(X,{get open(){return o.open},get onOpenChange(){return o.onClose},children:(H,G)=>{var L=Qe(),oe=_(L);g(oe,()=>Ot,(Ce,Ue)=>{Ue(Ce,{children:(Z,N)=>{var A=Ll(),W=_(A);g(W,()=>Ft,(h,q)=>{q(h,{})});var f=t(W,2);g(f,()=>xt,(h,q)=>{q(h,{class:"sm:max-w-[500px]",children:(b,V)=>{var be=Rl(),le=_(be);g(le,()=>yt,(m,P)=>{P(m,{children:(R,Q)=>{var fe=wl(),U=_(fe);g(U,()=>$t,(Se,je)=>{je(Se,{children:(ce,ie)=>{v();var Le=p("Edit Skills");r(ce,Le)},$$slots:{default:!0}})});var ve=t(U,2);g(ve,()=>Pt,(Se,je)=>{je(Se,{children:(ce,ie)=>{v();var Le=p("Add or remove skills from your profile.");r(ce,Le)},$$slots:{default:!0}})}),r(R,fe)},$$slots:{default:!0}})});var $e=t(le,2),ze=s($e),Ae=s(ze),I=s(Ae);Re(I,{children:(m,P)=>{v();var R=p("Add Skills");r(m,R)},$$slots:{default:!0}});var pe=t(I,2),we=s(pe);et(we,{placeholder:"Enter a skill",get disabled(){return l()},onkeydown:m=>{m.key==="Enter"&&(m.preventDefault(),ge())},get value(){return e(x)},set value(m){n(x,m,!0)}});var i=t(we,2);de(i,{type:"button",variant:"outline",onclick:ge,get disabled(){return l()},children:(m,P)=>{var R=kl(),Q=_(R);St(Q,{class:"mr-2 h-4 w-4"}),v(),r(m,R)},$$slots:{default:!0}}),a(pe),a(Ae);var y=t(Ae,2);{var w=m=>{var P=Cl(),R=s(P);Re(R,{children:(fe,U)=>{v();var ve=p("Your Skills");r(fe,ve)},$$slots:{default:!0}});var Q=t(R,2);lt(Q,21,()=>e(d),it,(fe,U)=>{Badge(fe,{variant:"secondary",class:"flex items-center gap-1",children:(ve,Se)=>{v();var je=Dl(),ce=_(je),ie=t(ce);ie.__click=[Sl,ue,U];var Le=s(ie);Fr(Le,{class:"h-3 w-3"}),a(ie),ne(()=>C(ce,`${e(U)??""} `)),r(ve,je)},$$slots:{default:!0}})}),a(Q),a(P),r(m,P)},E=m=>{var P=Il();r(m,P)};De(y,m=>{e(d).length>0?m(w):m(E,!1)})}var c=t(y,2),j=s(c);Re(j,{children:(m,P)=>{v();var R=p("Suggested Skills");r(m,R)},$$slots:{default:!0}});var z=t(j,2);et(z,{placeholder:"Search for skills",get disabled(){return l()},get value(){return e(B)},set value(m){n(B,m,!0)}});var re=t(z,2);lt(re,21,()=>e(ee),it,(m,P)=>{const R=rt(()=>e(d).includes(e(P).name)?"default":"outline");de(m,{type:"button",get variant(){return e(R)},size:"sm",onclick:()=>Ee(e(P).name),get disabled(){return l()},children:(Q,fe)=>{v();var U=p();ne(()=>C(U,e(P).name)),r(Q,U)},$$slots:{default:!0}})}),a(re),a(c),a(ze),a($e);var S=t($e,2);g(S,()=>wt,(m,P)=>{P(m,{children:(R,Q)=>{var fe=jl(),U=_(fe);de(U,{variant:"outline",get onclick(){return o.onClose},get disabled(){return l()},children:(je,ce)=>{v();var ie=p("Cancel");r(je,ie)},$$slots:{default:!0}});var ve=t(U,2);const Se=rt(()=>e(k)||l());de(ve,{onclick:xe,get disabled(){return e(Se)},class:"ml-2",children:(je,ce)=>{var ie=El(),Le=_(ie);Ct(Le,{class:"mr-2 h-4 w-4"});var ae=t(Le);ne(()=>C(ae,` ${e(k)?"Saving...":"Save Changes"}`)),r(je,ie)},$$slots:{default:!0}}),r(R,fe)},$$slots:{default:!0}})}),r(b,be)},$$slots:{default:!0}})}),r(Z,A)},$$slots:{default:!0}})}),r(H,L)},$$slots:{default:!0}})}),r(Me,Ne),dt()}Lt(["click"]);var Al=u("<!> Edit",1),Ul=u('<span class="bg-primary/10 text-primary rounded-full px-3 py-1 text-sm"> </span>'),Tl=u('<div class="flex flex-wrap gap-2"></div>'),Ol=u("<!> Add Skills",1),Fl=u('<div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8"><p class="text-muted-foreground text-center">No skills added yet</p> <!></div>'),zl=u('<div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Skills</h2> <!></div> <div class="mt-4"><!></div></div> <!>',1);function Ml(Me,o){nt(o,!0);const l=vt(o,"disabled",3,!1);let d=me(!1);function x(){n(d,!0)}async function k(he){try{const X={skills:he};return await o.onSave(X)}catch(X){return console.error("Error saving skills:",X),Oe.error("Failed to save skills"),!1}}var O=zl(),ee=_(O),B=s(ee),te=t(s(B),2);de(te,{variant:"ghost",size:"sm",onclick:x,get disabled(){return l()},children:(he,X)=>{var se=Al(),H=_(se);ht(H,{class:"mr-2 h-4 w-4"}),v(),r(he,se)},$$slots:{default:!0}}),a(B);var F=t(B,2),ge=s(F);{var Ee=he=>{var X=Tl();lt(X,21,()=>o.data.skills,it,(se,H)=>{var G=Ul(),L=s(G,!0);a(G),ne(()=>C(L,e(H))),r(se,G)}),a(X),r(he,X)},ue=he=>{var X=Fl(),se=t(s(X),2);de(se,{variant:"outline",class:"mt-4",onclick:x,get disabled(){return l()},children:(H,G)=>{var L=Ol(),oe=_(L);St(oe,{class:"mr-2 h-4 w-4"}),v(),r(H,L)},$$slots:{default:!0}}),a(X),r(he,X)};De(ge,he=>{var X;(X=o.data)!=null&&X.skills&&o.data.skills.length>0?he(Ee):he(ue,!1)})}a(F),a(ee);var xe=t(ee,2);const Ne=rt(()=>{var he;return((he=o.data)==null?void 0:he.skills)||[]});Nl(xe,{get open(){return e(d)},get skills(){return e(Ne)},onClose:()=>n(d,!1),onSave:k,get disabled(){return l()}}),r(Me,O),dt()}var Hl=u("<!> <!>",1),ql=u('<div class="flex items-center justify-between rounded-md border p-2"><div><p class="font-medium"> </p> <p class="text-muted-foreground text-sm"> </p></div> <div class="flex space-x-1"><!> <!></div></div>'),Jl=u('<div class="space-y-2"><!> <div class="space-y-2"></div></div>'),Wl=u('<p class="text-muted-foreground text-sm">No languages added yet</p>'),Vl=u("<!> ",1),Bl=u("<!> <!>",1),Yl=u('<!> <div class="py-4"><div class="space-y-4"><div class="space-y-2"><!> <div class="flex flex-col space-y-2"><!> <div class="grid grid-cols-2 gap-2 sm:grid-cols-4"></div> <!></div></div> <!> <div class="space-y-2"><!> <!> <div class="flex max-h-40 flex-wrap gap-2 overflow-y-auto"></div></div></div></div> <!>',1),Kl=u("<!> <!>",1);function Gl(Me,o){nt(o,!0);let l=me(at([])),d=me(at({language:"",proficiency:"intermediate"})),x=me(!1),k=me(at([])),O=me(at([])),ee=me(""),B=me(null);const te=[{value:"beginner",label:"Beginner"},{value:"intermediate",label:"Intermediate"},{value:"advanced",label:"Advanced"},{value:"native",label:"Native"}];kt(()=>{o.open&&(n(l,[...o.languages],!0),F(),n(ee,""),n(B,null),ge())});function F(){n(d,{language:"",proficiency:"intermediate"},!0)}async function ge(){try{const L=await fetch("/api/languages");if(L.ok){const oe=await L.json();n(k,oe,!0),ue()}else n(k,Ee.map(oe=>({name:oe,code:oe.toLowerCase()})),!0),ue()}catch(L){console.error("Error fetching languages:",L),n(k,Ee.map(oe=>({name:oe,code:oe.toLowerCase()})),!0),ue()}}const Ee=["English","Spanish","French","German","Chinese","Japanese","Korean","Russian","Arabic","Portuguese","Italian","Hindi"];function ue(){e(ee)?n(O,e(k).filter(L=>L.name.toLowerCase().includes(e(ee).toLowerCase())).slice(0,20),!0):n(O,e(k).slice(0,20),!0)}kt(()=>{ue()});function xe(){if(!e(d).language){Oe.error("Please enter a language");return}if(e(B)!==null)e(l)[e(B)]={...e(d)},n(B,null);else{if(e(l).some(oe=>oe.language.toLowerCase()===e(d).language.toLowerCase())){Oe.error("This language is already added");return}n(l,[...e(l),{...e(d)}],!0)}F()}function Ne(L){const oe=e(l)[L];n(d,{...oe},!0),n(B,L,!0)}function he(L){n(l,e(l).filter((oe,Ce)=>Ce!==L),!0),e(B)===L&&(n(B,null),F())}function X(L){e(d).language=L}async function se(){n(x,!0);try{await o.onSave(e(l))&&(Oe.success("Languages updated successfully"),o.onClose())}catch(L){console.error("Error saving languages:",L),Oe.error("Failed to save languages")}finally{n(x,!1)}}var H=Qe(),G=_(H);g(G,()=>bt,(L,oe)=>{oe(L,{get open(){return o.open},get onOpenChange(){return o.onClose},children:(Ce,Ue)=>{var Z=Qe(),N=_(Z);g(N,()=>Ot,(A,W)=>{W(A,{children:(f,h)=>{var q=Kl(),b=_(q);g(b,()=>Ft,(be,le)=>{le(be,{})});var V=t(b,2);g(V,()=>xt,(be,le)=>{le(be,{class:"sm:max-w-[500px]",children:($e,ze)=>{var Ae=Yl(),I=_(Ae);g(I,()=>yt,(U,ve)=>{ve(U,{children:(Se,je)=>{var ce=Hl(),ie=_(ce);g(ie,()=>$t,(ae,qe)=>{qe(ae,{children:(Je,Ve)=>{v();var Be=p("Edit Languages");r(Je,Be)},$$slots:{default:!0}})});var Le=t(ie,2);g(Le,()=>Pt,(ae,qe)=>{qe(ae,{children:(Je,Ve)=>{v();var Be=p("Add or remove languages from your profile.");r(Je,Be)},$$slots:{default:!0}})}),r(Se,ce)},$$slots:{default:!0}})});var pe=t(I,2),we=s(pe),i=s(we),y=s(i);Re(y,{children:(U,ve)=>{v();var Se=p("Add Language");r(U,Se)},$$slots:{default:!0}});var w=t(y,2),E=s(w);et(E,{placeholder:"Enter a language",get value(){return e(d).language},set value(U){e(d).language=U}});var c=t(E,2);lt(c,21,()=>te,it,(U,ve)=>{const Se=rt(()=>e(d).proficiency===e(ve).value?"default":"outline");de(U,{type:"button",get variant(){return e(Se)},onclick:()=>e(d).proficiency=e(ve).value,children:(je,ce)=>{v();var ie=p();ne(()=>C(ie,e(ve).label)),r(je,ie)},$$slots:{default:!0}})}),a(c);var j=t(c,2);de(j,{type:"button",variant:"outline",onclick:xe,children:(U,ve)=>{v();var Se=p();ne(()=>C(Se,`${e(B)!==null?"Update":"Add"} Language`)),r(U,Se)},$$slots:{default:!0}}),a(w),a(i);var z=t(i,2);{var re=U=>{var ve=Jl(),Se=s(ve);Re(Se,{children:(ce,ie)=>{v();var Le=p("Your Languages");r(ce,Le)},$$slots:{default:!0}});var je=t(Se,2);lt(je,21,()=>e(l),it,(ce,ie,Le)=>{var ae=ql(),qe=s(ae),Je=s(qe),Ve=s(Je,!0);a(Je);var Be=t(Je,2),tt=s(Be,!0);a(Be),a(qe);var D=t(qe,2),Y=s(D);de(Y,{variant:"ghost",size:"icon",onclick:()=>Ne(Le),children:(J,T)=>{ht(J,{class:"h-4 w-4"})},$$slots:{default:!0}});var ye=t(Y,2);de(ye,{variant:"ghost",size:"icon",onclick:()=>he(Le),children:(J,T)=>{Fr(J,{class:"h-4 w-4"})},$$slots:{default:!0}}),a(D),a(ae),ne(J=>{C(Ve,e(ie).language),C(tt,J)},[()=>{var J;return((J=te.find(T=>T.value===e(ie).proficiency))==null?void 0:J.label)||e(ie).proficiency}]),r(ce,ae)}),a(je),a(ve),r(U,ve)},S=U=>{var ve=Wl();r(U,ve)};De(z,U=>{e(l).length>0?U(re):U(S,!1)})}var m=t(z,2),P=s(m);Re(P,{children:(U,ve)=>{v();var Se=p("Suggested Languages");r(U,Se)},$$slots:{default:!0}});var R=t(P,2);et(R,{placeholder:"Search for languages",get value(){return e(ee)},set value(U){n(ee,U,!0)}});var Q=t(R,2);lt(Q,21,()=>e(O),it,(U,ve)=>{de(U,{type:"button",variant:"outline",size:"sm",onclick:()=>X(e(ve).name),children:(Se,je)=>{v();var ce=p();ne(()=>C(ce,e(ve).name)),r(Se,ce)},$$slots:{default:!0}})}),a(Q),a(m),a(we),a(pe);var fe=t(pe,2);g(fe,()=>wt,(U,ve)=>{ve(U,{children:(Se,je)=>{var ce=Bl(),ie=_(ce);de(ie,{variant:"outline",get onclick(){return o.onClose},children:(ae,qe)=>{v();var Je=p("Cancel");r(ae,Je)},$$slots:{default:!0}});var Le=t(ie,2);de(Le,{onclick:se,get disabled(){return e(x)},class:"ml-2",children:(ae,qe)=>{var Je=Vl(),Ve=_(Je);Ct(Ve,{class:"mr-2 h-4 w-4"});var Be=t(Ve);ne(()=>C(Be,` ${e(x)?"Saving...":"Save Changes"}`)),r(ae,Je)},$$slots:{default:!0}}),r(Se,ce)},$$slots:{default:!0}})}),r($e,Ae)},$$slots:{default:!0}})}),r(f,q)},$$slots:{default:!0}})}),r(Ce,Z)},$$slots:{default:!0}})}),r(Me,H),dt()}var Ql=u("<!> Add Language",1),Xl=u('<div class="flex items-start justify-between rounded-md border p-4"><div class="flex-1"><div class="flex items-center"><!> <h3 class="font-medium"> </h3></div> <p class="text-muted-foreground text-sm"> </p></div> <!></div>'),Zl=u('<div class="space-y-4"></div>'),ei=u("<!> Add Language",1),ti=u('<div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8"><!> <p class="text-muted-foreground text-center">No languages added yet</p> <!></div>'),ri=u('<div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Languages</h2> <!></div> <div class="mt-4"><!></div></div> <!>',1);function ai(Me,o){nt(o,!0);const l=vt(o,"disabled",3,!1);let d=me(!1);const x=[{value:"beginner",label:"Beginner"},{value:"intermediate",label:"Intermediate"},{value:"advanced",label:"Advanced"},{value:"native",label:"Native"}];function k(se){const H=x.find(G=>G.value===se);return H?H.label:"Not specified"}function O(){n(d,!0)}async function ee(se){try{return await o.onSave(se)}catch(H){return console.error("Error saving languages:",H),Oe.error("Failed to save languages"),!1}}var B=ri(),te=_(B),F=s(te),ge=t(s(F),2);de(ge,{variant:"outline",size:"sm",onclick:O,get disabled(){return l()},children:(se,H)=>{var G=Ql(),L=_(G);St(L,{class:"mr-2 h-4 w-4"}),v(),r(se,G)},$$slots:{default:!0}}),a(F);var Ee=t(F,2),ue=s(Ee);{var xe=se=>{var H=Zl();lt(H,21,()=>o.data,it,(G,L)=>{var oe=Xl(),Ce=s(oe),Ue=s(Ce),Z=s(Ue);Cr(Z,{class:"mr-2 h-5 w-5 text-blue-500"});var N=t(Z,2),A=s(N,!0);a(N),a(Ue);var W=t(Ue,2),f=s(W,!0);a(W),a(Ce);var h=t(Ce,2);de(h,{variant:"ghost",size:"icon",onclick:O,get disabled(){return l()},children:(q,b)=>{ht(q,{class:"h-4 w-4"})},$$slots:{default:!0}}),a(oe),ne(q=>{C(A,e(L).language),C(f,q)},[()=>k(e(L).proficiency)]),r(G,oe)}),a(H),r(se,H)},Ne=se=>{var H=ti(),G=s(H);Cr(G,{class:"text-muted-foreground mb-2 h-10 w-10"});var L=t(G,4);de(L,{variant:"outline",class:"mt-4",onclick:O,get disabled(){return l()},children:(oe,Ce)=>{var Ue=ei(),Z=_(Ue);St(Z,{class:"mr-2 h-4 w-4"}),v(),r(oe,Ue)},$$slots:{default:!0}}),a(H),r(se,H)};De(ue,se=>{o.data&&o.data.length>0?se(xe):se(Ne,!1)})}a(Ee),a(te);var he=t(te,2);const X=rt(()=>o.data||[]);Gl(he,{get open(){return e(d)},get languages(){return e(X)},onClose:()=>n(d,!1),onSave:ee,get disabled(){return l()}}),r(Me,B),dt()}var si=u("<!> <!>",1),oi=u('<div class="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-full px-3 py-1 text-sm"> <button type="button" class="text-secondary-foreground/70 hover:text-secondary-foreground ml-1 rounded-full p-1">&times;</button></div>'),li=u('<div class="mt-2 flex flex-wrap gap-2"></div>'),ii=u('<div class="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-full px-3 py-1 text-sm"> <button type="button" class="text-secondary-foreground/70 hover:text-secondary-foreground ml-1 rounded-full p-1">&times;</button></div>'),ni=u('<div class="mt-2 flex flex-wrap gap-2"></div>'),di=u('<div class="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-full px-3 py-1 text-sm"> <button type="button" class="text-secondary-foreground/70 hover:text-secondary-foreground ml-1 rounded-full p-1">&times;</button></div>'),ui=u('<div class="mt-2 flex flex-wrap gap-2"></div>'),vi=u('<div class="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-full px-3 py-1 text-sm"> <button type="button" class="text-secondary-foreground/70 hover:text-secondary-foreground ml-1 rounded-full p-1">&times;</button></div>'),ci=u('<div class="mt-2 flex flex-wrap gap-2"></div>'),fi=u('<div class="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-full px-3 py-1 text-sm"> <button type="button" class="text-secondary-foreground/70 hover:text-secondary-foreground ml-1 rounded-full p-1">&times;</button></div>'),mi=u('<div class="mt-2 flex flex-wrap gap-2"></div>'),pi=u('<span class="mr-2">Saving...</span>'),_i=u("<!> Save Changes",1),gi=u("<!> <!>",1),hi=u('<!> <div class="max-h-[60vh] overflow-y-auto"><div class="grid gap-4 py-4"><div class="grid gap-2"><!> <div class="flex gap-2"><!> <!></div> <!></div> <div class="grid gap-2"><!> <div class="flex gap-2"><!> <!></div> <!></div> <div class="grid gap-2"><!> <div class="flex flex-wrap gap-2"></div></div> <div class="grid gap-2"><!> <div class="flex flex-wrap gap-2"></div></div> <div class="grid gap-2"><!> <div class="flex flex-wrap gap-2"></div></div> <div class="grid gap-2"><!> <!></div> <div class="grid gap-2"><!> <div class="flex gap-2"><!> <!></div> <!></div> <div class="grid gap-2"><!> <div class="flex flex-wrap gap-2"></div></div> <div class="grid gap-2"><!> <div class="flex gap-2"><!> <!></div> <!></div> <div class="grid gap-2"><!> <div class="flex gap-2"><!> <!></div> <!></div> <div class="grid gap-2"><!> <div class="flex items-center space-x-2"><!> <span class="text-muted-foreground text-sm">Would you like to see roles that require top security clearance?</span></div></div> <div class="grid gap-2"><!> <div class="flex flex-wrap gap-2"></div></div></div></div> <!>',1);function xi(Me,o){var V,be,le,$e,ze,Ae,I,pe,we,i,y,w,E;nt(o,!0);let l=me(at({interestedRoles:((V=o.data)==null?void 0:V.interestedRoles)||[],preferredLocations:((be=o.data)==null?void 0:be.preferredLocations)||[],remotePreference:((le=o.data)==null?void 0:le.remotePreference)||"hybrid",salary:(($e=o.data)==null?void 0:$e.salary)||"",industries:((ze=o.data)==null?void 0:ze.industries)||[],jobTypes:((Ae=o.data)==null?void 0:Ae.jobTypes)||[],experienceLevels:((I=o.data)==null?void 0:I.experienceLevels)||[],valueInRole:((pe=o.data)==null?void 0:pe.valueInRole)||[],idealCompanySize:((we=o.data)==null?void 0:we.idealCompanySize)||"medium",avoidIndustries:((i=o.data)==null?void 0:i.avoidIndustries)||[],avoidSkills:((y=o.data)==null?void 0:y.avoidSkills)||[],securityClearance:((w=o.data)==null?void 0:w.securityClearance)||!1,jobSearchStatus:((E=o.data)==null?void 0:E.jobSearchStatus)||"actively_looking"})),d=me(!1),x=me(at({}));kt(()=>{var c,j,z,re,S,m,P,R,Q,fe,U,ve,Se;o.open&&(n(l,{interestedRoles:((c=o.data)==null?void 0:c.interestedRoles)||[],preferredLocations:((j=o.data)==null?void 0:j.preferredLocations)||[],remotePreference:((z=o.data)==null?void 0:z.remotePreference)||"hybrid",salary:((re=o.data)==null?void 0:re.salary)||"",industries:((S=o.data)==null?void 0:S.industries)||[],jobTypes:((m=o.data)==null?void 0:m.jobTypes)||[],experienceLevels:((P=o.data)==null?void 0:P.experienceLevels)||[],valueInRole:((R=o.data)==null?void 0:R.valueInRole)||[],idealCompanySize:((Q=o.data)==null?void 0:Q.idealCompanySize)||"medium",avoidIndustries:((fe=o.data)==null?void 0:fe.avoidIndustries)||[],avoidSkills:((U=o.data)==null?void 0:U.avoidSkills)||[],securityClearance:((ve=o.data)==null?void 0:ve.securityClearance)||!1,jobSearchStatus:((Se=o.data)==null?void 0:Se.jobSearchStatus)||"actively_looking"},!0),n(x,{},!0))});const k=[{value:"remote",label:"Remote Only"},{value:"hybrid",label:"Hybrid"},{value:"onsite",label:"On-site Only"},{value:"flexible",label:"Flexible"}],O=[{value:"full_time",label:"Full-time"},{value:"part_time",label:"Part-time"},{value:"contract",label:"Contract"},{value:"temporary",label:"Temporary"},{value:"internship",label:"Internship"}],ee=[{value:"entry",label:"Entry Level"},{value:"mid",label:"Mid Level"},{value:"senior",label:"Senior Level"},{value:"executive",label:"Executive"}],B=[{value:"startup",label:"Startup (1-10 employees)"},{value:"small",label:"Small (11-50 employees)"},{value:"medium",label:"Medium (51-200 employees)"},{value:"large",label:"Large (201-1000 employees)"},{value:"enterprise",label:"Enterprise (1000+ employees)"}],te=[{value:"actively_looking",label:"Actively Looking"},{value:"open_to_opportunities",label:"Open to Opportunities"},{value:"not_looking",label:"Not Looking"}];async function F(){n(d,!0);try{await o.onSave(e(l))&&(Oe.success("Job preferences updated successfully"),o.onClose())}catch(c){console.error("Error saving job preferences:",c),Oe.error("Failed to save job preferences")}finally{n(d,!1)}}let ge=me("");function Ee(){e(ge).trim()&&(e(l).interestedRoles=[...e(l).interestedRoles||[],e(ge).trim()],n(ge,""))}function ue(c){e(l).interestedRoles&&(e(l).interestedRoles=e(l).interestedRoles.filter((j,z)=>z!==c))}let xe=me("");function Ne(){e(xe).trim()&&(e(l).preferredLocations=[...e(l).preferredLocations||[],e(xe).trim()],n(xe,""))}function he(c){e(l).preferredLocations&&(e(l).preferredLocations=e(l).preferredLocations.filter((j,z)=>z!==c))}function X(c){e(l).jobTypes||(e(l).jobTypes=[]),e(l).jobTypes.includes(c)?e(l).jobTypes=e(l).jobTypes.filter(j=>j!==c):e(l).jobTypes=[...e(l).jobTypes,c]}function se(c){e(l).experienceLevels||(e(l).experienceLevels=[]),e(l).experienceLevels.includes(c)?e(l).experienceLevels=e(l).experienceLevels.filter(j=>j!==c):e(l).experienceLevels=[...e(l).experienceLevels,c]}function H(c){e(l).idealCompanySize=c}function G(c){e(l).jobSearchStatus=c}function L(){e(l).securityClearance=!e(l).securityClearance}let oe=me("");function Ce(){e(oe).trim()&&(e(l).valueInRole=[...e(l).valueInRole||[],e(oe).trim()],n(oe,""))}function Ue(c){e(l).valueInRole&&(e(l).valueInRole=e(l).valueInRole.filter((j,z)=>z!==c))}let Z=me("");function N(){e(Z).trim()&&(e(l).avoidIndustries=[...e(l).avoidIndustries||[],e(Z).trim()],n(Z,""))}function A(c){e(l).avoidIndustries&&(e(l).avoidIndustries=e(l).avoidIndustries.filter((j,z)=>z!==c))}let W=me("");function f(){e(W).trim()&&(e(l).avoidSkills=[...e(l).avoidSkills||[],e(W).trim()],n(W,""))}function h(c){e(l).avoidSkills&&(e(l).avoidSkills=e(l).avoidSkills.filter((j,z)=>z!==c))}var q=Qe(),b=_(q);g(b,()=>bt,(c,j)=>{j(c,{get open(){return o.open},onOpenChange:z=>!z&&o.onClose(),children:(z,re)=>{var S=Qe(),m=_(S);g(m,()=>xt,(P,R)=>{R(P,{class:"sm:max-w-[600px]",children:(Q,fe)=>{var U=hi(),ve=_(U);g(ve,()=>yt,(_e,Te)=>{Te(_e,{children:(Fe,ot)=>{var ct=si(),Ge=_(ct);g(Ge,()=>$t,(pt,Nt)=>{Nt(pt,{children:(It,Qt)=>{v();var At=p("Edit Job Preferences");r(It,At)},$$slots:{default:!0}})});var mt=t(Ge,2);g(mt,()=>Pt,(pt,Nt)=>{Nt(pt,{children:(It,Qt)=>{v();var At=p("Update your job preferences to help us find the right opportunities for you.");r(It,At)},$$slots:{default:!0}})}),r(Fe,ct)},$$slots:{default:!0}})});var Se=t(ve,2),je=s(Se),ce=s(je),ie=s(ce);Re(ie,{children:(_e,Te)=>{v();var Fe=p("Desired Roles");r(_e,Fe)},$$slots:{default:!0}});var Le=t(ie,2),ae=s(Le);et(ae,{placeholder:"Add a role",class:"flex-1",get value(){return e(ge)},set value(_e){n(ge,_e,!0)}});var qe=t(ae,2);de(qe,{type:"button",onclick:Ee,children:(_e,Te)=>{v();var Fe=p("Add");r(_e,Fe)},$$slots:{default:!0}}),a(Le);var Je=t(Le,2);{var Ve=_e=>{var Te=li();lt(Te,21,()=>e(l).interestedRoles,it,(Fe,ot,ct)=>{var Ge=oi(),mt=s(Ge),pt=t(mt);pt.__click=()=>ue(ct),a(Ge),ne(()=>C(mt,`${e(ot)??""} `)),r(Fe,Ge)}),a(Te),r(_e,Te)};De(Je,_e=>{e(l).interestedRoles&&e(l).interestedRoles.length>0&&_e(Ve)})}a(ce);var Be=t(ce,2),tt=s(Be);Re(tt,{children:(_e,Te)=>{v();var Fe=p("Preferred Locations");r(_e,Fe)},$$slots:{default:!0}});var D=t(tt,2),Y=s(D);et(Y,{placeholder:"Add a location",class:"flex-1",get value(){return e(xe)},set value(_e){n(xe,_e,!0)}});var ye=t(Y,2);de(ye,{type:"button",onclick:Ne,children:(_e,Te)=>{v();var Fe=p("Add");r(_e,Fe)},$$slots:{default:!0}}),a(D);var J=t(D,2);{var T=_e=>{var Te=ni();lt(Te,21,()=>e(l).preferredLocations,it,(Fe,ot,ct)=>{var Ge=ii(),mt=s(Ge),pt=t(mt);pt.__click=()=>he(ct),a(Ge),ne(()=>C(mt,`${e(ot)??""} `)),r(Fe,Ge)}),a(Te),r(_e,Te)};De(J,_e=>{e(l).preferredLocations&&e(l).preferredLocations.length>0&&_e(T)})}a(Be);var K=t(Be,2),M=s(K);Re(M,{children:(_e,Te)=>{v();var Fe=p("Remote Preference");r(_e,Fe)},$$slots:{default:!0}});var Ie=t(M,2);lt(Ie,21,()=>k,it,(_e,Te)=>{const Fe=rt(()=>e(l).remotePreference===e(Te).value?"default":"outline");de(_e,{type:"button",get variant(){return e(Fe)},class:"flex-grow-0",onclick:()=>e(l).remotePreference=e(Te).value,children:(ot,ct)=>{v();var Ge=p();ne(()=>C(Ge,e(Te).label)),r(ot,Ge)},$$slots:{default:!0}})}),a(Ie),a(K);var $=t(K,2),ke=s($);Re(ke,{children:(_e,Te)=>{v();var Fe=p("Job Types");r(_e,Fe)},$$slots:{default:!0}});var Pe=t(ke,2);lt(Pe,21,()=>O,it,(_e,Te)=>{const Fe=rt(()=>{var ot;return(ot=e(l).jobTypes)!=null&&ot.includes(e(Te).value)?"default":"outline"});de(_e,{type:"button",get variant(){return e(Fe)},class:"flex-grow-0",onclick:()=>X(e(Te).value),children:(ot,ct)=>{v();var Ge=p();ne(()=>C(Ge,e(Te).label)),r(ot,Ge)},$$slots:{default:!0}})}),a(Pe),a($);var He=t($,2),We=s(He);Re(We,{children:(_e,Te)=>{v();var Fe=p("Experience Level");r(_e,Fe)},$$slots:{default:!0}});var Ke=t(We,2);lt(Ke,21,()=>ee,it,(_e,Te)=>{const Fe=rt(()=>{var ot;return(ot=e(l).experienceLevels)!=null&&ot.includes(e(Te).value)?"default":"outline"});de(_e,{type:"button",get variant(){return e(Fe)},class:"flex-grow-0",onclick:()=>se(e(Te).value),children:(ot,ct)=>{v();var Ge=p();ne(()=>C(Ge,e(Te).label)),r(ot,Ge)},$$slots:{default:!0}})}),a(Ke),a(He);var Xe=t(He,2),Ye=s(Xe);Re(Ye,{for:"salary",children:(_e,Te)=>{v();var Fe=p("Desired Salary");r(_e,Fe)},$$slots:{default:!0}});var Ze=t(Ye,2);et(Ze,{id:"salary",placeholder:"e.g. $80,000 - $100,000",get value(){return e(l).salary},set value(_e){e(l).salary=_e}}),a(Xe);var st=t(Xe,2),_t=s(st);Re(_t,{children:(_e,Te)=>{v();var Fe=p("What do you value in a new role?");r(_e,Fe)},$$slots:{default:!0}});var ut=t(_t,2),ft=s(ut);et(ft,{placeholder:"Add a value",class:"flex-1",get value(){return e(oe)},set value(_e){n(oe,_e,!0)}});var gt=t(ft,2);de(gt,{type:"button",onclick:Ce,children:(_e,Te)=>{v();var Fe=p("Add");r(_e,Fe)},$$slots:{default:!0}}),a(ut);var Dt=t(ut,2);{var zr=_e=>{var Te=ui();lt(Te,21,()=>e(l).valueInRole,it,(Fe,ot,ct)=>{var Ge=di(),mt=s(Ge),pt=t(mt);pt.__click=()=>Ue(ct),a(Ge),ne(()=>C(mt,`${e(ot)??""} `)),r(Fe,Ge)}),a(Te),r(_e,Te)};De(Dt,_e=>{e(l).valueInRole&&e(l).valueInRole.length>0&&_e(zr)})}a(st);var Wt=t(st,2),fr=s(Wt);Re(fr,{children:(_e,Te)=>{v();var Fe=p("Ideal Company Size");r(_e,Fe)},$$slots:{default:!0}});var mr=t(fr,2);lt(mr,21,()=>B,it,(_e,Te)=>{const Fe=rt(()=>e(l).idealCompanySize===e(Te).value?"default":"outline");de(_e,{type:"button",get variant(){return e(Fe)},class:"flex-grow-0",onclick:()=>H(e(Te).value),children:(ot,ct)=>{v();var Ge=p();ne(()=>C(Ge,e(Te).label)),r(ot,Ge)},$$slots:{default:!0}})}),a(mr),a(Wt);var Vt=t(Wt,2),pr=s(Vt);Re(pr,{children:(_e,Te)=>{v();var Fe=p("Industries to avoid");r(_e,Fe)},$$slots:{default:!0}});var Bt=t(pr,2),_r=s(Bt);et(_r,{placeholder:"Add an industry",class:"flex-1",get value(){return e(Z)},set value(_e){n(Z,_e,!0)}});var Mr=t(_r,2);de(Mr,{type:"button",onclick:N,children:(_e,Te)=>{v();var Fe=p("Add");r(_e,Fe)},$$slots:{default:!0}}),a(Bt);var Hr=t(Bt,2);{var qr=_e=>{var Te=ci();lt(Te,21,()=>e(l).avoidIndustries,it,(Fe,ot,ct)=>{var Ge=vi(),mt=s(Ge),pt=t(mt);pt.__click=()=>A(ct),a(Ge),ne(()=>C(mt,`${e(ot)??""} `)),r(Fe,Ge)}),a(Te),r(_e,Te)};De(Hr,_e=>{e(l).avoidIndustries&&e(l).avoidIndustries.length>0&&_e(qr)})}a(Vt);var Yt=t(Vt,2),gr=s(Yt);Re(gr,{children:(_e,Te)=>{v();var Fe=p("Skills to avoid");r(_e,Fe)},$$slots:{default:!0}});var Kt=t(gr,2),hr=s(Kt);et(hr,{placeholder:"Add a skill",class:"flex-1",get value(){return e(W)},set value(_e){n(W,_e,!0)}});var Jr=t(hr,2);de(Jr,{type:"button",onclick:f,children:(_e,Te)=>{v();var Fe=p("Add");r(_e,Fe)},$$slots:{default:!0}}),a(Kt);var Wr=t(Kt,2);{var Vr=_e=>{var Te=mi();lt(Te,21,()=>e(l).avoidSkills,it,(Fe,ot,ct)=>{var Ge=fi(),mt=s(Ge),pt=t(mt);pt.__click=()=>h(ct),a(Ge),ne(()=>C(mt,`${e(ot)??""} `)),r(Fe,Ge)}),a(Te),r(_e,Te)};De(Wr,_e=>{e(l).avoidSkills&&e(l).avoidSkills.length>0&&_e(Vr)})}a(Yt);var Gt=t(Yt,2),xr=s(Gt);Re(xr,{children:(_e,Te)=>{v();var Fe=p("Security Clearance");r(_e,Fe)},$$slots:{default:!0}});var br=t(xr,2),Br=s(br);const Yr=rt(()=>e(l).securityClearance?"default":"outline");de(Br,{type:"button",get variant(){return e(Yr)},onclick:L,children:(_e,Te)=>{v();var Fe=p();ne(()=>C(Fe,e(l).securityClearance?"Yes":"No")),r(_e,Fe)},$$slots:{default:!0}}),v(2),a(br),a(Gt);var yr=t(Gt,2),$r=s(yr);Re($r,{children:(_e,Te)=>{v();var Fe=p("Job Search Status");r(_e,Fe)},$$slots:{default:!0}});var Pr=t($r,2);lt(Pr,21,()=>te,it,(_e,Te)=>{const Fe=rt(()=>e(l).jobSearchStatus===e(Te).value?"default":"outline");de(_e,{type:"button",get variant(){return e(Fe)},class:"flex-grow-0",onclick:()=>G(e(Te).value),children:(ot,ct)=>{v();var Ge=p();ne(()=>C(Ge,e(Te).label)),r(ot,Ge)},$$slots:{default:!0}})}),a(Pr),a(yr),a(je),a(Se);var Kr=t(Se,2);g(Kr,()=>wt,(_e,Te)=>{Te(_e,{children:(Fe,ot)=>{var ct=gi(),Ge=_(ct);de(Ge,{variant:"outline",get onclick(){return o.onClose},children:(pt,Nt)=>{v();var It=p("Cancel");r(pt,It)},$$slots:{default:!0}});var mt=t(Ge,2);de(mt,{onclick:F,get disabled(){return e(d)},children:(pt,Nt)=>{var It=Qe(),Qt=_(It);{var At=jt=>{var zt=pi();r(jt,zt)},Gr=jt=>{var zt=_i(),Qr=_(zt);Ct(Qr,{class:"mr-2 h-4 w-4"}),v(),r(jt,zt)};De(Qt,jt=>{e(d)?jt(At):jt(Gr,!1)})}r(pt,It)},$$slots:{default:!0}}),r(Fe,ct)},$$slots:{default:!0}})}),r(Q,U)},$$slots:{default:!0}})}),r(z,S)},$$slots:{default:!0}})}),r(Me,q),dt()}Lt(["click"]);var bi=u("<!> Edit",1),yi=u("<!> Edit",1),$i=u(`<div class="space-y-6"><div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Job Preferences</h2> <!></div> <p class="text-muted-foreground mt-2 text-sm">Set your job preferences to help us find the right opportunities for you.</p> <div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2"><div class="rounded-md border p-4"><div class="flex items-center"><div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="font-medium">Desired Roles</h3> <p class="text-muted-foreground text-sm"> </p></div></div></div> <div class="rounded-md border p-4"><div class="flex items-center"><div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="font-medium">Preferred Locations</h3> <p class="text-muted-foreground text-sm"> </p></div></div></div> <div class="rounded-md border p-4"><div class="flex items-center"><div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="font-medium">Remote Preference</h3> <p class="text-muted-foreground text-sm"> </p></div></div></div> <div class="rounded-md border p-4"><div class="flex items-center"><div class="bg-primary/10 mr-3 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="font-medium">Desired Salary</h3> <p class="text-muted-foreground text-sm"> </p></div></div></div></div></div> <div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Additional Job Preferences</h2> <!></div> <p class="text-muted-foreground mt-2 text-sm">Provide more details about your job preferences to help us find the right opportunities for
      you.</p> <div class="mt-4 space-y-4"><div><h3 class="font-medium">What do you value in a new role?</h3> <p class="text-muted-foreground text-sm"> </p></div> <div><h3 class="font-medium">What is your ideal company size?</h3> <p class="text-muted-foreground text-sm"> </p></div> <div><h3 class="font-medium">What industries are exciting to you?</h3> <p class="text-muted-foreground text-sm"> </p></div> <div><h3 class="font-medium">Are there any industries you don't want to work in?</h3> <p class="text-muted-foreground text-sm"> </p></div> <div><h3 class="font-medium">Are there any skills you don't want to work with?</h3> <p class="text-muted-foreground text-sm"> </p></div> <div><h3 class="font-medium">Would you like to see roles that require top security clearance?</h3> <p class="text-muted-foreground text-sm"> </p></div> <div><h3 class="font-medium">What's the status of your job search?</h3> <p class="text-muted-foreground text-sm"> </p></div></div></div></div> <!>`,1);function Pi(Me,o){nt(o,!0);let l=me(!1);function d(){n(l,!0)}async function x(D){try{return await o.onSave(D)}catch(Y){return console.error("Error saving job preferences:",Y),Oe.error("Failed to save job preferences"),!1}}function k(D){return D&&{remote:"Remote Only",hybrid:"Hybrid",onsite:"On-site Only",flexible:"Flexible"}[D]||"Not specified"}function O(D){return!D||D.length===0?"Not specified":D.join(", ")}function ee(D){return D&&{startup:"Startup (1-10 employees)",small:"Small (11-50 employees)",medium:"Medium (51-200 employees)",large:"Large (201-1000 employees)",enterprise:"Enterprise (1000+ employees)"}[D]||"Not specified"}function B(D){return D&&{actively_looking:"Actively Looking",open_to_opportunities:"Open to Opportunities",not_looking:"Not Looking"}[D]||"Not specified"}var te=$i(),F=_(te),ge=s(F),Ee=s(ge),ue=t(s(Ee),2);de(ue,{variant:"ghost",size:"sm",onclick:d,children:(D,Y)=>{var ye=bi(),J=_(ye);ht(J,{class:"mr-2 h-4 w-4"}),v(),r(D,ye)},$$slots:{default:!0}}),a(Ee);var xe=t(Ee,4),Ne=s(xe),he=s(Ne),X=s(he),se=s(X);qt(se,{class:"text-primary h-5 w-5"}),a(X);var H=t(X,2),G=t(s(H),2),L=s(G,!0);a(G),a(H),a(he),a(Ne);var oe=t(Ne,2),Ce=s(oe),Ue=s(Ce),Z=s(Ue);Sa(Z,{class:"text-primary h-5 w-5"}),a(Ue);var N=t(Ue,2),A=t(s(N),2),W=s(A,!0);a(A),a(N),a(Ce),a(oe);var f=t(oe,2),h=s(f),q=s(h),b=s(q);Da(b,{class:"text-primary h-5 w-5"}),a(q);var V=t(q,2),be=t(s(V),2),le=s(be,!0);a(be),a(V),a(h),a(f);var $e=t(f,2),ze=s($e),Ae=s(ze),I=s(Ae);Ca(I,{class:"text-primary h-5 w-5"}),a(Ae);var pe=t(Ae,2),we=t(s(pe),2),i=s(we,!0);a(we),a(pe),a(ze),a($e),a(xe),a(ge);var y=t(ge,2),w=s(y),E=t(s(w),2);de(E,{variant:"ghost",size:"sm",onclick:d,children:(D,Y)=>{var ye=yi(),J=_(ye);ht(J,{class:"mr-2 h-4 w-4"}),v(),r(D,ye)},$$slots:{default:!0}}),a(w);var c=t(w,4),j=s(c),z=t(s(j),2),re=s(z,!0);a(z),a(j);var S=t(j,2),m=t(s(S),2),P=s(m,!0);a(m),a(S);var R=t(S,2),Q=t(s(R),2),fe=s(Q,!0);a(Q),a(R);var U=t(R,2),ve=t(s(U),2),Se=s(ve,!0);a(ve),a(U);var je=t(U,2),ce=t(s(je),2),ie=s(ce,!0);a(ce),a(je);var Le=t(je,2),ae=t(s(Le),2),qe=s(ae,!0);a(ae),a(Le);var Je=t(Le,2),Ve=t(s(Je),2),Be=s(Ve,!0);a(Ve),a(Je),a(c),a(y),a(F);var tt=t(F,2);xi(tt,{get open(){return e(l)},get data(){return o.data},onClose:()=>n(l,!1),onSave:x}),ne((D,Y,ye,J,T,K,M,Ie,$)=>{var ke,Pe,He;C(L,D),C(W,Y),C(le,ye),C(i,((ke=o.data)==null?void 0:ke.salary)||"Not specified"),C(re,J),C(P,T),C(fe,K),C(Se,M),C(ie,Ie),C(qe,((Pe=o.data)==null?void 0:Pe.securityClearance)===!0?"Yes":((He=o.data)==null?void 0:He.securityClearance)===!1?"No":"Not specified"),C(Be,$)},[()=>{var D;return O((D=o.data)==null?void 0:D.interestedRoles)},()=>{var D;return O((D=o.data)==null?void 0:D.preferredLocations)},()=>{var D;return k((D=o.data)==null?void 0:D.remotePreference)},()=>{var D;return O((D=o.data)==null?void 0:D.valueInRole)},()=>{var D;return(D=o.data)!=null&&D.idealCompanySize?ee(o.data.idealCompanySize):"Not specified"},()=>{var D;return O((D=o.data)==null?void 0:D.industries)},()=>{var D;return O((D=o.data)==null?void 0:D.avoidIndustries)},()=>{var D;return O((D=o.data)==null?void 0:D.avoidSkills)},()=>{var D;return(D=o.data)!=null&&D.jobSearchStatus?B(o.data.jobSearchStatus):"Not specified"}]),r(Me,te),dt()}var wi=u("<!> <!>",1),ki=u('<span class="mr-2">Saving...</span>'),Si=u("<!> Save Changes",1),Di=u("<!> <!>",1),Ci=u('<!> <div class="max-h-[60vh] overflow-y-auto"><div class="grid gap-4 py-4"><div class="grid gap-2"><!> <div class="flex flex-wrap gap-2"></div></div> <div class="grid gap-2"><!> <div class="flex items-center space-x-2"><!></div></div> <div class="grid gap-2"><!> <div class="flex items-center space-x-2"><!></div></div> <div class="grid gap-2"><!> <div class="flex items-center space-x-2"><!></div></div> <div class="grid gap-2"><!> <div class="flex items-center space-x-2"><!></div></div> <div class="grid gap-2"><!> <div class="flex items-center space-x-2"><!></div></div> <div class="grid gap-2"><!> <div class="flex items-center space-x-2"><!></div></div> <div class="grid gap-2"><!> <div class="flex flex-wrap gap-2"></div></div> <div class="grid gap-2"><!> <div class="flex items-center space-x-2"><!></div></div></div></div> <!>',1);function Ii(Me,o){var F,ge,Ee,ue,xe,Ne,he,X,se;nt(o,!0);let l=me(at({ethnicity:((F=o.data)==null?void 0:F.ethnicity)||"",authorizedUS:((ge=o.data)==null?void 0:ge.authorizedUS)||!1,authorizedCanada:((Ee=o.data)==null?void 0:Ee.authorizedCanada)||!1,authorizedUK:((ue=o.data)==null?void 0:ue.authorizedUK)||!1,requireSponsorship:((xe=o.data)==null?void 0:xe.requireSponsorship)||!1,disability:((Ne=o.data)==null?void 0:Ne.disability)||!1,lgbtq:((he=o.data)==null?void 0:he.lgbtq)||!1,gender:((X=o.data)==null?void 0:X.gender)||"",veteran:((se=o.data)==null?void 0:se.veteran)||!1})),d=me(!1);kt(()=>{var H,G,L,oe,Ce,Ue,Z,N,A;o.open&&n(l,{ethnicity:((H=o.data)==null?void 0:H.ethnicity)||"",authorizedUS:((G=o.data)==null?void 0:G.authorizedUS)||!1,authorizedCanada:((L=o.data)==null?void 0:L.authorizedCanada)||!1,authorizedUK:((oe=o.data)==null?void 0:oe.authorizedUK)||!1,requireSponsorship:((Ce=o.data)==null?void 0:Ce.requireSponsorship)||!1,disability:((Ue=o.data)==null?void 0:Ue.disability)||!1,lgbtq:((Z=o.data)==null?void 0:Z.lgbtq)||!1,gender:((N=o.data)==null?void 0:N.gender)||"",veteran:((A=o.data)==null?void 0:A.veteran)||!1},!0)});const x=[{value:"american_indian",label:"American Indian or Alaska Native"},{value:"asian",label:"Asian"},{value:"black",label:"Black or African American"},{value:"hispanic",label:"Hispanic or Latino"},{value:"native_hawaiian",label:"Native Hawaiian or Other Pacific Islander"},{value:"white",label:"White"},{value:"two_or_more",label:"Two or More Races"},{value:"prefer_not_to_say",label:"Prefer not to say"}],k=[{value:"male",label:"Male"},{value:"female",label:"Female"},{value:"non_binary",label:"Non-binary"},{value:"other",label:"Other"},{value:"prefer_not_to_say",label:"Prefer not to say"}];async function O(){n(d,!0);try{await o.onSave(e(l))&&(Oe.success("Employment information updated successfully"),o.onClose())}catch(H){console.error("Error saving employment information:",H),Oe.error("Failed to save employment information")}finally{n(d,!1)}}function ee(H){e(l)[H]=!e(l)[H]}var B=Qe(),te=_(B);g(te,()=>bt,(H,G)=>{G(H,{get open(){return o.open},onOpenChange:L=>!L&&o.onClose(),children:(L,oe)=>{var Ce=Qe(),Ue=_(Ce);g(Ue,()=>xt,(Z,N)=>{N(Z,{class:"sm:max-w-[600px]",children:(A,W)=>{var f=Ci(),h=_(f);g(h,()=>yt,(M,Ie)=>{Ie(M,{children:($,ke)=>{var Pe=wi(),He=_(Pe);g(He,()=>$t,(Ke,Xe)=>{Xe(Ke,{children:(Ye,Ze)=>{v();var st=p("Edit Employment Information");r(Ye,st)},$$slots:{default:!0}})});var We=t(He,2);g(We,()=>Pt,(Ke,Xe)=>{Xe(Ke,{children:(Ye,Ze)=>{v();var st=p("Update your employment information to help with job applications.");r(Ye,st)},$$slots:{default:!0}})}),r($,Pe)},$$slots:{default:!0}})});var q=t(h,2),b=s(q),V=s(b),be=s(V);Re(be,{children:(M,Ie)=>{v();var $=p("What is your ethnicity?");r(M,$)},$$slots:{default:!0}});var le=t(be,2);lt(le,21,()=>x,it,(M,Ie)=>{const $=rt(()=>e(l).ethnicity===e(Ie).value?"default":"outline");de(M,{type:"button",get variant(){return e($)},class:"flex-grow-0",onclick:()=>e(l).ethnicity=e(Ie).value,children:(ke,Pe)=>{v();var He=p();ne(()=>C(He,e(Ie).label)),r(ke,He)},$$slots:{default:!0}})}),a(le),a(V);var $e=t(V,2),ze=s($e);Re(ze,{children:(M,Ie)=>{v();var $=p("Are you authorized to work in the US?");r(M,$)},$$slots:{default:!0}});var Ae=t(ze,2),I=s(Ae);const pe=rt(()=>e(l).authorizedUS?"default":"outline");de(I,{type:"button",get variant(){return e(pe)},onclick:()=>ee("authorizedUS"),children:(M,Ie)=>{v();var $=p();ne(()=>C($,e(l).authorizedUS?"Yes":"No")),r(M,$)},$$slots:{default:!0}}),a(Ae),a($e);var we=t($e,2),i=s(we);Re(i,{children:(M,Ie)=>{v();var $=p("Are you authorized to work in Canada?");r(M,$)},$$slots:{default:!0}});var y=t(i,2),w=s(y);const E=rt(()=>e(l).authorizedCanada?"default":"outline");de(w,{type:"button",get variant(){return e(E)},onclick:()=>ee("authorizedCanada"),children:(M,Ie)=>{v();var $=p();ne(()=>C($,e(l).authorizedCanada?"Yes":"No")),r(M,$)},$$slots:{default:!0}}),a(y),a(we);var c=t(we,2),j=s(c);Re(j,{children:(M,Ie)=>{v();var $=p("Are you authorized to work in the United Kingdom?");r(M,$)},$$slots:{default:!0}});var z=t(j,2),re=s(z);const S=rt(()=>e(l).authorizedUK?"default":"outline");de(re,{type:"button",get variant(){return e(S)},onclick:()=>ee("authorizedUK"),children:(M,Ie)=>{v();var $=p();ne(()=>C($,e(l).authorizedUK?"Yes":"No")),r(M,$)},$$slots:{default:!0}}),a(z),a(c);var m=t(c,2),P=s(m);Re(P,{children:(M,Ie)=>{v();var $=p("Will you now or in the future require sponsorship for employment visa status?");r(M,$)},$$slots:{default:!0}});var R=t(P,2),Q=s(R);const fe=rt(()=>e(l).requireSponsorship?"default":"outline");de(Q,{type:"button",get variant(){return e(fe)},onclick:()=>ee("requireSponsorship"),children:(M,Ie)=>{v();var $=p();ne(()=>C($,e(l).requireSponsorship?"Yes":"No")),r(M,$)},$$slots:{default:!0}}),a(R),a(m);var U=t(m,2),ve=s(U);Re(ve,{children:(M,Ie)=>{v();var $=p("Do you have a disability?");r(M,$)},$$slots:{default:!0}});var Se=t(ve,2),je=s(Se);const ce=rt(()=>e(l).disability?"default":"outline");de(je,{type:"button",get variant(){return e(ce)},onclick:()=>ee("disability"),children:(M,Ie)=>{v();var $=p();ne(()=>C($,e(l).disability?"Yes":"No")),r(M,$)},$$slots:{default:!0}}),a(Se),a(U);var ie=t(U,2),Le=s(ie);Re(Le,{children:(M,Ie)=>{v();var $=p("Do you identify as LGBTQ+?");r(M,$)},$$slots:{default:!0}});var ae=t(Le,2),qe=s(ae);const Je=rt(()=>e(l).lgbtq?"default":"outline");de(qe,{type:"button",get variant(){return e(Je)},onclick:()=>ee("lgbtq"),children:(M,Ie)=>{v();var $=p();ne(()=>C($,e(l).lgbtq?"Yes":"No")),r(M,$)},$$slots:{default:!0}}),a(ae),a(ie);var Ve=t(ie,2),Be=s(Ve);Re(Be,{children:(M,Ie)=>{v();var $=p("What is your gender?");r(M,$)},$$slots:{default:!0}});var tt=t(Be,2);lt(tt,21,()=>k,it,(M,Ie)=>{const $=rt(()=>e(l).gender===e(Ie).value?"default":"outline");de(M,{type:"button",get variant(){return e($)},class:"flex-grow-0",onclick:()=>e(l).gender=e(Ie).value,children:(ke,Pe)=>{v();var He=p();ne(()=>C(He,e(Ie).label)),r(ke,He)},$$slots:{default:!0}})}),a(tt),a(Ve);var D=t(Ve,2),Y=s(D);Re(Y,{children:(M,Ie)=>{v();var $=p("Are you a veteran?");r(M,$)},$$slots:{default:!0}});var ye=t(Y,2),J=s(ye);const T=rt(()=>e(l).veteran?"default":"outline");de(J,{type:"button",get variant(){return e(T)},onclick:()=>ee("veteran"),children:(M,Ie)=>{v();var $=p();ne(()=>C($,e(l).veteran?"Yes":"No")),r(M,$)},$$slots:{default:!0}}),a(ye),a(D),a(b),a(q);var K=t(q,2);g(K,()=>wt,(M,Ie)=>{Ie(M,{children:($,ke)=>{var Pe=Di(),He=_(Pe);de(He,{variant:"outline",get onclick(){return o.onClose},children:(Ke,Xe)=>{v();var Ye=p("Cancel");r(Ke,Ye)},$$slots:{default:!0}});var We=t(He,2);de(We,{onclick:O,get disabled(){return e(d)},children:(Ke,Xe)=>{var Ye=Qe(),Ze=_(Ye);{var st=ut=>{var ft=ki();r(ut,ft)},_t=ut=>{var ft=Si(),gt=_(ft);Ct(gt,{class:"mr-2 h-4 w-4"}),v(),r(ut,ft)};De(Ze,ut=>{e(d)?ut(st):ut(_t,!1)})}r(Ke,Ye)},$$slots:{default:!0}}),r($,Pe)},$$slots:{default:!0}})}),r(A,f)},$$slots:{default:!0}})}),r(L,Ce)},$$slots:{default:!0}})}),r(Me,B),dt()}var Ei=u("<!> Edit",1),ji=u('<div class="rounded-lg border p-6"><div class="flex items-center justify-between"><h2 class="text-xl font-semibold">Employment Information</h2> <!></div> <p class="text-muted-foreground mt-2 text-sm">Add your employment information to help with job applications.</p> <div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2"><div><h3 class="text-muted-foreground text-sm font-medium">Ethnicity</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Authorized to work in US</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Authorized to work in Canada</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Authorized to work in UK</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Require sponsorship</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Disability</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">LGBTQ+</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Gender</h3> <p> </p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Veteran</h3> <p> </p></div></div></div> <!>',1);function Ri(Me,o){nt(o,!0);let l=me(!1);function d(){n(l,!0)}async function x(I){try{return await o.onSave(I)}catch(pe){return console.error("Error saving employment information:",pe),Oe.error("Failed to save employment information"),!1}}function k(I){return I==null?"Not specified":I?"Yes":"No"}var O=ji(),ee=_(O),B=s(ee),te=t(s(B),2);de(te,{variant:"ghost",size:"sm",onclick:d,children:(I,pe)=>{var we=Ei(),i=_(we);ht(i,{class:"mr-2 h-4 w-4"}),v(),r(I,we)},$$slots:{default:!0}}),a(B);var F=t(B,4),ge=s(F),Ee=t(s(ge),2),ue=s(Ee,!0);a(Ee),a(ge);var xe=t(ge,2),Ne=t(s(xe),2),he=s(Ne,!0);a(Ne),a(xe);var X=t(xe,2),se=t(s(X),2),H=s(se,!0);a(se),a(X);var G=t(X,2),L=t(s(G),2),oe=s(L,!0);a(L),a(G);var Ce=t(G,2),Ue=t(s(Ce),2),Z=s(Ue,!0);a(Ue),a(Ce);var N=t(Ce,2),A=t(s(N),2),W=s(A,!0);a(A),a(N);var f=t(N,2),h=t(s(f),2),q=s(h,!0);a(h),a(f);var b=t(f,2),V=t(s(b),2),be=s(V,!0);a(V),a(b);var le=t(b,2),$e=t(s(le),2),ze=s($e,!0);a($e),a(le),a(F),a(ee);var Ae=t(ee,2);Ii(Ae,{get open(){return e(l)},get data(){return o.data},onClose:()=>n(l,!1),onSave:x}),ne((I,pe,we,i,y,w,E)=>{var c,j;C(ue,((c=o.data)==null?void 0:c.ethnicity)||"Not specified"),C(he,I),C(H,pe),C(oe,we),C(Z,i),C(W,y),C(q,w),C(be,((j=o.data)==null?void 0:j.gender)||"Not specified"),C(ze,E)},[()=>{var I;return k((I=o.data)==null?void 0:I.authorizedUS)},()=>{var I;return k((I=o.data)==null?void 0:I.authorizedCanada)},()=>{var I;return k((I=o.data)==null?void 0:I.authorizedUK)},()=>{var I;return k((I=o.data)==null?void 0:I.requireSponsorship)},()=>{var I;return k((I=o.data)==null?void 0:I.disability)},()=>{var I;return k((I=o.data)==null?void 0:I.lgbtq)},()=>{var I;return k((I=o.data)==null?void 0:I.veteran)}]),r(Me,O),dt()}var Li=u("<!> <span>Profile</span>",1),Ni=u("<!> <span>Personal Info</span>",1),Ai=u("<!> <span>Job Preferences</span>",1),Ui=u("<!> <!> <!>",1),Ti=u("<!> <!> <!> <!> <!> <!> <!>",1),Oi=u("<!> <!>",1),Fi=u('<div class="flex flex-col space-y-6"><div class="grid grid-cols-1 gap-6 md:grid-cols-4"><div class="flex flex-col gap-4 md:col-span-1"><!> <!> <!></div> <div class="space-y-6 md:col-span-3"><!></div></div></div>');function zi(Me,o){nt(o,!0);const l=vt(o,"isResumeBeingParsed",3,!1);let d=me("profile");var x=Fi(),k=s(x),O=s(k),ee=s(O);Va(ee,{get data(){return o.completeProfile.header},get onSave(){return o.saveHandlers.saveProfileHeader}});var B=t(ee,2);g(B,()=>la,(xe,Ne)=>{Ne(xe,{get value(){return e(d)},onValueChange:he=>n(d,he,!0),class:"w-full",orientation:"horizontal",children:(he,X)=>{var se=Qe(),H=_(se);g(H,()=>oa,(G,L)=>{L(G,{class:"flex flex-col space-y-1 rounded-md border p-2",children:(oe,Ce)=>{var Ue=Ui(),Z=_(Ue);g(Z,()=>Xt,(W,f)=>{f(W,{value:"profile",class:"data-[state=active]:bg-muted flex items-center justify-start gap-2 rounded-md px-3 py-2 text-sm font-medium",children:(h,q)=>{var b=Li(),V=_(b);Ia(V,{class:"h-4 w-4"}),v(2),r(h,b)},$$slots:{default:!0}})});var N=t(Z,2);g(N,()=>Xt,(W,f)=>{f(W,{value:"personal",class:"data-[state=active]:bg-muted flex items-center justify-start gap-2 rounded-md px-3 py-2 text-sm font-medium",children:(h,q)=>{var b=Ni(),V=_(b);er(V,{class:"h-4 w-4"}),v(2),r(h,b)},$$slots:{default:!0}})});var A=t(N,2);g(A,()=>Xt,(W,f)=>{f(W,{value:"job",class:"data-[state=active]:bg-muted flex items-center justify-start gap-2 rounded-md px-3 py-2 text-sm font-medium",children:(h,q)=>{var b=Ai(),V=_(b);qt(V,{class:"h-4 w-4"}),v(2),r(h,b)},$$slots:{default:!0}})}),r(oe,Ue)},$$slots:{default:!0}})}),r(he,se)},$$slots:{default:!0}})});var te=t(B,2);es(te,{get data(){return o.completeProfile.visibility},get onSave(){return o.saveHandlers.saveProfileVisibility}}),a(O);var F=t(O,2),ge=s(F);{var Ee=xe=>{var Ne=Ti(),he=_(Ne);Gs(he,{get data(){return o.completeProfile.resume},get onSave(){return o.saveHandlers.saveResume},get disabled(){return l()},onParsingStatusChange:Ce=>{o.onResumeParsingChange&&o.onResumeParsingChange(Ce)}});var X=t(he,2);po(X,{get data(){return o.completeProfile.workExperiences},get onSave(){return o.saveHandlers.saveWorkExperiences},get disabled(){return l()}});var se=t(X,2);Lo(se,{get data(){return o.completeProfile.educations},get onSave(){return o.saveHandlers.saveEducations},get disabled(){return l()}});var H=t(se,2);rl(H,{get data(){return o.completeProfile.projects},get onSave(){return o.saveHandlers.saveProjects},get disabled(){return l()}});var G=t(H,2);Pl(G,{get data(){return o.completeProfile.portfolioLinks},get onSave(){return o.saveHandlers.savePortfolioLinks},get disabled(){return l()}});var L=t(G,2);Ml(L,{get data(){return o.completeProfile.skills},get onSave(){return o.saveHandlers.saveSkills},get disabled(){return l()}});var oe=t(L,2);ai(oe,{get data(){return o.completeProfile.languages},get onSave(){return o.saveHandlers.saveLanguages},get disabled(){return l()}}),r(xe,Ne)},ue=(xe,Ne)=>{{var he=se=>{var H=Oi(),G=_(H);bs(G,{get data(){return o.completeProfile.personalInfo},get onSave(){return o.saveHandlers.savePersonalInfo},get disabled(){return l()}});var L=t(G,2);const oe=rt(()=>o.profileData.employmentInfo||{});Ri(L,{get data(){return e(oe)},get onSave(){return o.saveHandlers.saveEmploymentInfo},get disabled(){return l()}}),r(se,H)},X=(se,H)=>{{var G=L=>{const oe=rt(()=>o.profileData.jobPreferences||{});Pi(L,{get data(){return e(oe)},get onSave(){return o.saveHandlers.saveJobPreferences},get disabled(){return l()}})};De(se,L=>{e(d)==="job"&&L(G)},H)}};De(xe,se=>{e(d)==="personal"?se(he):se(X,!1)},Ne)}};De(ge,xe=>{e(d)==="profile"?xe(Ee):xe(ue,!1)})}a(F),a(k),a(x),r(Me,x),dt()}var Mi=u('<!> <span class="sr-only">Open menu</span>',1),Hi=async(Me,o,l)=>{const d=e(o).visibility.showToRecruiters,x={...e(o).visibility,showToRecruiters:!d};await l(x)&&Oe.success(`Profile is now ${d?"hidden":"visible"} to recruiters`)},qi=u("<!> <span>Hide from Recruiters</span>",1),Ji=u("<!> <span>Show to Recruiters</span>",1),Wi=(Me,o)=>{confirm("Are you sure you want to delete this profile? This action cannot be undone.")&&fetch(`/api/profile/${o.id}`,{method:"DELETE"}).then(async l=>{if(l.ok)Oe.success("Profile deleted successfully"),tr("/dashboard/settings/profile");else{const d=await l.json();Oe.error(d.error||"Failed to delete profile")}}).catch(l=>{console.error("Error deleting profile:",l),Oe.error("Failed to delete profile")})},Vi=u('<!> <!> <button class="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50"><!></button> <!> <button class="text-destructive hover:bg-destructive hover:text-destructive-foreground focus:bg-destructive focus:text-destructive-foreground relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors"><!> <span>Delete Profile</span></button>',1),Bi=u("<!> <!>",1),Yi=u('<div class="flex flex-col items-center justify-center py-12"><div class="border-primary h-12 w-12 animate-spin rounded-full border-4 border-t-transparent"></div> <p class="mt-4 text-lg">Loading profile...</p></div>'),Ki=u("<!> Back to Profiles",1),Gi=u('<div class="flex items-center rounded-md bg-amber-50 px-3 py-1 text-sm text-amber-600"><span class="mr-2 animate-pulse">●</span> Resume parsing in progress...</div>'),Qi=u('<div class="grid grid-cols-1 gap-6"><div class="mb-4 flex items-center justify-between"><!> <!></div> <!></div>'),Xi=u('<!> <div class="border-border flex items-center justify-between border-b p-6"><div><h1 class="text-xl font-semibold"> </h1> <p class="text-muted-foreground text-sm"> </p></div> <div><!></div></div> <div class="container mx-auto p-6"><!></div>',1);function dd(Me,o){nt(o,!0);let l=me(!0),d=me(!1),x=at({...o.data.profile,published:!1,publishedAt:null}),k=me(at({}));console.log("Profile data:",o.data.profile);async function O(){var i;try{const w=await(await fetch(`/api/profile/${o.data.profile.id}/data`)).json();if(console.log("Profile data from API:",w),w&&w.data){const j=sa(w.data);n(k,Mt(j),!0)}else w&&typeof w=="object"?n(k,Mt(w),!0):n(k,Mt({}),!0);const E=w,c=E.resumeId||E.resumeData&&E.resumeData.id||((i=E.resume)==null?void 0:i.resumeId);if(c)try{const j=await fetch(`/api/resume/${c}/parsing-status`);if(j.ok){const z=await j.json();n(d,z.isParsing,!0),z.isParsing&&setTimeout(B,5e3)}}catch(j){console.error("Error checking resume parsing status:",j)}else w&&"isParsing"in w&&(n(d,w.isParsing,!0),w.isParsing&&setTimeout(B,5e3));return w&&w.parsedResumeData&&(console.log("Parsed resume data available:",w.parsedResumeData),ee(w.parsedResumeData)),n(F,te(),!0),w}catch(y){return console.error("Error fetching profile data:",y),null}}async function ee(i){var y,w,E,c,j,z,re,S,m,P,R,Q,fe,U,ve,Se,je;try{console.log("Updating profile with parsed resume data");const ce=i.legacy?i.data:i,ie={...e(k),personalInfo:{...e(k).personalInfo,fullName:((y=e(k).personalInfo)==null?void 0:y.fullName)||((w=ce.profile)==null?void 0:w.name),email:((E=e(k).personalInfo)==null?void 0:E.email)||((c=ce.profile)==null?void 0:c.email),phone:((j=e(k).personalInfo)==null?void 0:j.phone)||((z=ce.profile)==null?void 0:z.phone),address:((re=e(k).personalInfo)==null?void 0:re.address)||((S=ce.profile)==null?void 0:S.location),jobTitle:((m=e(k).personalInfo)==null?void 0:m.jobTitle)||((P=ce.profile)==null?void 0:P.title)},workExperience:((R=e(k).workExperience)==null?void 0:R.length)>0?e(k).workExperience:ce.experience||ce.workExperiences||[],education:((Q=e(k).education)==null?void 0:Q.length)>0?e(k).education:ce.education||ce.educations||[],skills:((fe=e(k).skills)==null?void 0:fe.length)>0?e(k).skills:Array.isArray(ce.skills)?ce.skills.map(ae=>typeof ae=="string"?ae:ae.name||ae.skill):[],projects:((U=e(k).projects)==null?void 0:U.length)>0?e(k).projects:ce.projects||[],languages:((ve=e(k).languages)==null?void 0:ve.length)>0?e(k).languages:ce.languages||[],certifications:((Se=e(k).certifications)==null?void 0:Se.length)>0?e(k).certifications:ce.certifications||[],summary:e(k).summary||((je=ce.profile)==null?void 0:je.summary),resumeId:ce.resumeId,parsedResumeApplied:!0};if(!e(k).parsedResumeApplied||Object.keys(e(k)).length<5){if(!(await fetch(`/api/profile/${x.id}/data`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(ie)})).ok)throw new Error("Failed to save profile data with parsed resume data");n(k,ie,!0),console.log("Profile data updated with parsed resume data:",e(k)),n(F,te(),!0),Oe.success("Profile updated with resume data")}else console.log("Parsed resume data already applied to this profile, skipping update")}catch(ce){console.error("Error updating profile with parsed resume data:",ce),Oe.error("Failed to update profile with parsed resume data")}}async function B(){var i;try{const y=e(k),w=y.resumeId||y.resumeData&&y.resumeData.id||((i=y.resume)==null?void 0:i.resumeId);if(!w){console.log("No resume ID found in profile data, skipping parsing status check"),n(d,!1);return}const E=await fetch(`/api/resume/${w}/parsing-status`);if(!E.ok)throw new Error("Failed to check parsing status");const c=await E.json();n(d,c.isParsing,!0),c.isParsing?setTimeout(B,5e3):await O()&&n(F,te(),!0)}catch(y){console.error("Error checking parsing status:",y),n(d,!1)}}Zr(async()=>{try{await O(),n(l,!1)}catch(i){console.error("Error initializing profile data:",i),n(k,Mt({}),!0),n(l,!1)}});function te(){var E,c,j,z,re,S,m,P,R,Q,fe,U,ve,Se,je,ce,ie,Le,ae,qe,Je,Ve;const i=e(k),y=i.resumeData||{},w=((E=i.jobPreferences)==null?void 0:E.jobSearchStatus)||"actively_looking";return{header:{profileName:x.name||"",fullName:i.fullName||((c=i.personalInfo)==null?void 0:c.fullName)||((j=i.header)==null?void 0:j.fullName)||"",jobTitle:i.jobType||((z=i.personalInfo)==null?void 0:z.jobTitle)||"",jobSearchStatus:w},visibility:{showToRecruiters:((re=i.visibility)==null?void 0:re.showToRecruiters)||!1,getDiscovered:((S=i.visibility)==null?void 0:S.getDiscovered)||!0,hideFromCurrentEmployer:((m=i.visibility)==null?void 0:m.hideFromCurrentEmployer)||!1},personalInfo:{email:i.email||((P=i.personalInfo)==null?void 0:P.email)||"",phone:i.phone||((R=i.personalInfo)==null?void 0:R.phone)||"",address:((Q=i.personalInfo)==null?void 0:Q.address)||"",city:((fe=i.personalInfo)==null?void 0:fe.city)||"",state:((U=i.personalInfo)==null?void 0:U.state)||"",zip:((ve=i.personalInfo)==null?void 0:ve.zip)||"",country:((Se=i.personalInfo)==null?void 0:Se.country)||"USA"},resume:y.id||i.resumeId?{resumeId:y.id||i.resumeId,fileName:y.name||i.resumeName||"resume.pdf",uploadedAt:y.updatedAt||i.resumeUpdatedAt,isDefault:!0}:null,workExperiences:i.workExperience||i.workExperiences||[],educations:i.education||i.educations||[],projects:i.projects||[],portfolioLinks:{linkedinUrl:((je=i.portfolioLinks)==null?void 0:je.linkedin)||((ce=i.personalInfo)==null?void 0:ce.linkedin)||"",githubUrl:((ie=i.portfolioLinks)==null?void 0:ie.github)||((Le=i.personalInfo)==null?void 0:Le.github)||"",portfolioUrl:i.website||((ae=i.personalInfo)==null?void 0:ae.website)||"",otherUrl:((qe=i.portfolioLinks)==null?void 0:qe.other)||""},skills:{skills:Array.isArray(i.skills)?i.skills:((Je=i.skillsData)==null?void 0:Je.list)||((Ve=i.skillsData)==null?void 0:Ve.technical)||[]},languages:i.languages?i.languages.map(Be=>({...Be,proficiency:Be.proficiency||"intermediate"})):[]}}let F=me(at(te()));function ge(i){return new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}const Ee=x!=null&&x.updatedAt?ge(x.updatedAt):"Recently";async function ue(i){try{const y={...e(k),...i};if(console.log("Saving profile data:",y),!(await fetch(`/api/profile/${x.id}/data`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(y)})).ok)throw new Error("Failed to save profile data");return n(k,y,!0),n(F,te(),!0),Oe.success("Profile updated successfully"),!0}catch(y){return console.error("Error saving profile data:",y),Oe.error("Failed to save profile data"),!1}}async function xe(i){try{if(!(await fetch(`/api/profile/${x.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.profileName})})).ok)throw new Error("Failed to update profile name");x.name=i.profileName}catch(w){return console.error("Error updating profile name:",w),Oe.error("Failed to update profile name"),!1}const y={fullName:i.fullName,jobType:i.jobTitle,personalInfo:{...e(k).personalInfo,fullName:i.fullName,jobTitle:i.jobTitle},jobPreferences:{...e(k).jobPreferences,jobSearchStatus:i.jobSearchStatus}};return e(F).header=i,ue(y)}async function Ne(i){const y={visibility:i};return e(F).visibility=i,ue(y)}async function he(i){if(!i.resumeId)return console.error("Cannot save resume: resumeId is missing"),Oe.error("Cannot save resume: Resume ID is missing"),!1;console.log("[snapshot] Saving resume data:",i),console.log("Saving resume data:",i);const y={resumeId:i.resumeId,resumeData:{id:i.resumeId,name:i.fileName,updatedAt:i.uploadedAt},parsedResumeApplied:!1};if(i.parseIntoProfile&&i.resumeId)try{console.log("Triggering resume parsing for resumeId:",i.resumeId),n(d,!0);const w=await fetch(`/api/resume/${i.resumeId}/parse`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({profileId:x.id})});if(w.ok)Oe.success("Resume parsing started"),setTimeout(B,5e3);else{const E=await w.json();console.error("Resume parsing error:",E),Oe.error(`Failed to start resume parsing: ${E.error||"Unknown error"}`),n(d,!1)}}catch(w){console.error("Error triggering resume parsing:",w),Oe.error("Failed to start resume parsing"),n(d,!1)}else try{console.log("Checking if resume is already parsed:",i.resumeId);const w=await O();w&&w.data&&console.log("Profile data already includes parsed resume data")}catch(w){console.error("Error checking if resume is already parsed:",w)}e(F).resume=i;try{const w=await ue(y);return w&&n(F,te(),!0),w}catch(w){return console.error("Error saving profile data:",w),Oe.error("Failed to save profile data"),i.parseIntoProfile&&n(d,!1),!1}}async function X(i){const y={workExperience:i};return e(F).workExperiences=i,ue(y)}async function se(i){const y={education:i};return e(F).educations=i,ue(y)}async function H(i){const y={projects:i};return e(F).projects=i,ue(y)}async function G(i){const y={website:i.portfolioUrl,personalInfo:{...e(k).personalInfo,website:i.portfolioUrl},portfolioLinks:{linkedin:i.linkedinUrl,github:i.githubUrl,other:i.otherUrl}};return e(F).portfolioLinks=i,ue(y)}async function L(i){const y={skills:i.skills,skillsData:{...e(k).skillsData,list:i.skills}};return e(F).skills=i,ue(y)}async function oe(i){const y={languages:i};return e(F).languages=i,ue(y)}async function Ce(i){console.log("Saving job preferences:",i);const y={jobPreferences:{...e(k).jobPreferences,valueInRole:i.valueInRole||[],interestedRoles:i.interestedRoles||[],preferredLocations:i.preferredLocations||[],remotePreference:i.remotePreference||"hybrid",minimumSalary:i.salary||"",desiredIndustries:i.industries||[],avoidIndustries:i.avoidIndustries||[],avoidSkills:i.avoidSkills||[],companySize:i.idealCompanySize||"medium",jobSearchStatus:i.jobSearchStatus||"actively_looking"}};i.jobTypes&&(y.jobPreferences.jobTypes=i.jobTypes),i.experienceLevels&&(y.jobPreferences.experienceLevels=i.experienceLevels),i.securityClearance!==void 0&&(y.jobPreferences.securityClearance=i.securityClearance);const w=await ue(y);return w&&n(F,te(),!0),w}async function Ue(i){const y={employmentInfo:{...e(k).employmentInfo,...i}};return ue(y)}async function Z(i){console.log("Saving personal info:",i);const y={email:i.email,phone:i.phone,personalInfo:{...e(k).personalInfo,email:i.email,phone:i.phone,address:i.address,city:i.city,state:i.state,zip:i.zip,country:i.country||"USA"}},w=await ue(y);return w&&(e(F).personalInfo=i,n(F,te(),!0)),w}var N=Xi(),A=_(N);const W=rt(()=>`${(x==null?void 0:x.name)||"Profile"} - Hirli`),f=rt(()=>`https://hirli.com/dashboard/settings/profile/${x==null?void 0:x.id}`);aa(A,{get title(){return e(W)},description:"Edit your professional profile for job applications.",keywords:"profile, resume, job search, career profile, professional information",get url(){return e(f)}});var h=t(A,2),q=s(h),b=s(q),V=s(b,!0);a(b);var be=t(b,2),le=s(be);a(be),a(q);var $e=t(q,2),ze=s($e);g(ze,()=>ra,(i,y)=>{y(i,{children:(w,E)=>{var c=Bi(),j=_(c);g(j,()=>ea,(re,S)=>{S(re,{children:(m,P)=>{de(m,{variant:"ghost",size:"icon",class:"h-8 w-8 rounded-full",children:(R,Q)=>{var fe=Mi(),U=_(fe);Ea(U,{class:"h-4 w-4"}),v(2),r(R,fe)},$$slots:{default:!0}})},$$slots:{default:!0}})});var z=t(j,2);g(z,()=>ta,(re,S)=>{S(re,{align:"end",class:"w-48",children:(m,P)=>{var R=Vi(),Q=_(R);g(Q,()=>ja,(ae,qe)=>{qe(ae,{children:(Je,Ve)=>{v();var Be=p("Profile Actions");r(Je,Be)},$$slots:{default:!0}})});var fe=t(Q,2);g(fe,()=>Ir,(ae,qe)=>{qe(ae,{})});var U=t(fe,2);U.__click=[Hi,F,Ne];var ve=s(U);{var Se=ae=>{var qe=qi(),Je=_(qe);Ra(Je,{class:"mr-2 h-4 w-4"}),v(2),r(ae,qe)},je=ae=>{var qe=Ji(),Je=_(qe);La(Je,{class:"mr-2 h-4 w-4"}),v(2),r(ae,qe)};De(ve,ae=>{e(F).visibility.showToRecruiters?ae(Se):ae(je,!1)})}a(U);var ce=t(U,2);g(ce,()=>Ir,(ae,qe)=>{qe(ae,{})});var ie=t(ce,2);ie.__click=[Wi,x];var Le=s(ie);Tt(Le,{class:"mr-2 h-4 w-4"}),v(2),a(ie),r(m,R)},$$slots:{default:!0}})}),r(w,c)},$$slots:{default:!0}})}),a($e),a(h);var Ae=t(h,2),I=s(Ae);{var pe=i=>{var y=Yi();r(i,y)},we=i=>{var y=Qi(),w=s(y),E=s(w);de(E,{variant:"outline",size:"sm",onclick:()=>tr("/dashboard/settings/profile"),class:"flex items-center gap-2",children:(re,S)=>{var m=Ki(),P=_(m);Na(P,{class:"h-4 w-4"}),v(),r(re,m)},$$slots:{default:!0}});var c=t(E,2);{var j=re=>{var S=Gi();r(re,S)};De(c,re=>{e(d)&&re(j)})}a(w);var z=t(w,2);zi(z,{get completeProfile(){return e(F)},get profileData(){return e(k)},get isResumeBeingParsed(){return e(d)},onResumeParsingChange:re=>{n(d,re,!0)},saveHandlers:{saveProfileHeader:xe,saveProfileVisibility:Ne,savePersonalInfo:Z,saveResume:he,saveWorkExperiences:X,saveEducations:se,saveProjects:H,savePortfolioLinks:G,saveSkills:L,saveLanguages:oe,saveJobPreferences:Ce,saveEmploymentInfo:Ue}}),a(y),r(i,y)};De(I,i=>{e(l)?i(pe):i(we,!1)})}a(Ae),ne(()=>{C(V,(x==null?void 0:x.name)||"Profile"),C(le,`Last updated: ${Ee??""}`)}),r(Me,N),dt()}Lt(["click"]);export{dd as component};
