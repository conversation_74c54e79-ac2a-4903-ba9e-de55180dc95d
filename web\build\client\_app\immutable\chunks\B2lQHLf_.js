import{f as n,a as p}from"./BasJTneF.js";import{p as f,t as m,a as i,c as u,r as d}from"./CGmarHxI.js";import{s as h}from"./CIt1g2O9.js";import{e as S}from"./B-Xjo-Yt.js";import{p as _,r as v}from"./Btcx8l8F.js";import{c as x}from"./ncUU1dSD.js";var b=n("<span> </span>");function w(s,e){f(e,!0);let a=_(e,"onSelect",3,void 0),r=v(e,["$$slots","$$events","$$legacy","class","placeholder","onSelect"]);function o(){a()&&a()()}var t=b();S(t,l=>({class:l,onclick:o,...r}),[()=>x("text-sm",e.class)]);var c=u(t,!0);d(t),m(()=>h(c,e.placeholder)),p(s,t),i()}export{w as S};
