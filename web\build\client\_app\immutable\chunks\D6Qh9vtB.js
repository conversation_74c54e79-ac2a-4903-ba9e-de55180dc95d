import{c as i,a as n}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as p}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function v(t,r){const s=m(r,["children","$$slots","$$events","$$legacy"]),e=[["circle",{cx:"12",cy:"12",r:"10"}],["rect",{x:"9",y:"9",width:"6",height:"6",rx:"1"}]];f(t,d({name:"circle-stop"},()=>s,{get iconNode(){return e},children:(c,$)=>{var o=i(),a=l(o);p(a,r,"default",{},null),n(c,o)},$$slots:{default:!0}}))}export{v as C};
