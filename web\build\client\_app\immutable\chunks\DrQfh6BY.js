import{c as n,a as c}from"./BasJTneF.js";import{p as m,f as i,au as d,a as f}from"./CGmarHxI.js";import{s as l}from"./ncUU1dSD.js";import{s as h,r as u}from"./Btcx8l8F.js";import{I as $}from"./DxW95yuQ.js";function N(r,o){m(o,!0);let e=u(o,["$$slots","$$events","$$legacy"]);const t=[["path",{d:"M20 6 9 17l-5-5"}]];$(r,h({name:"check"},()=>e,{get iconNode(){return t},children:(a,_)=>{var s=n(),p=i(s);l(p,()=>o.children??d),c(a,s)},$$slots:{default:!0}})),f()}export{N as C};
