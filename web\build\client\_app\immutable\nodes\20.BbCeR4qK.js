import{f as k,a as i,t as w}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as ge}from"../chunks/nZgk9enP.js";import{p as he,f as S,a as _e,s,d as l,m as P,c as r,g as n,r as t,n as c,t as re}from"../chunks/CGmarHxI.js";import{s as N}from"../chunks/CIt1g2O9.js";import{i as Y}from"../chunks/u21ee2wt.js";import{i as xe}from"../chunks/BIEMS98f.js";import{s as $e,a as ye}from"../chunks/CmxjS0TN.js";import{p as be}from"../chunks/Buv24VCh.js";import{S as we}from"../chunks/C6g8ubaU.js";import{B as A}from"../chunks/B1K98fMG.js";import{C as O}from"../chunks/DuGukytH.js";import{C as q}from"../chunks/Cdn-N1RY.js";import{B as Pe}from"../chunks/DaBofrVv.js";import{L as ke}from"../chunks/BhzFx1Wy.js";import{C as Ce}from"../chunks/BAIxhb6t.js";import{C as je}from"../chunks/DW7T7T22.js";import{G as Be}from"../chunks/BEVim9wJ.js";import{U as Ve}from"../chunks/BSHZ37s_.js";var Ee=k('<div class="mb-4 flex justify-center"><!></div> <h1 class="mb-2 text-2xl font-bold">Verifying Your Email</h1> <p class="text-muted-foreground">Please wait while we verify your email address...</p>',1),Ue=k('<div class="mb-4 flex justify-center"><div class="bg-destructive/10 rounded-full p-3"><!></div></div> <h1 class="mb-2 text-2xl font-bold">Verification Failed</h1> <p class="text-muted-foreground mb-6"> </p> <div class="flex flex-col gap-3 sm:flex-row"><!> <!></div>',1),Ge=k('<div class="mb-6 rounded-lg border border-green-200 bg-green-50 p-4"><div class="mb-2 flex items-center justify-center gap-2"><!> <span class="font-semibold text-green-800">Referral Bonus!</span></div> <p class="text-sm text-green-700">Thanks for joining through <strong> </strong> </p> <div class="mt-2 flex items-center justify-center gap-1"><!> <!></div></div>'),Re=k(`<div class="mb-4 flex justify-center"><div class="rounded-full bg-green-100 p-3"><!></div></div> <h1 class="mb-2 text-2xl font-bold">Email Verified!</h1> <p class="text-muted-foreground mb-4">Your email has been successfully verified. You can now access all features of your
            account.</p> <!> <div class="space-y-3"><!> <!></div> <div class="text-muted-foreground mt-4 text-sm">Redirecting to sign in page in a few seconds...</div>`,1),Se=k('<!> <div class="flex min-h-[calc(100vh-200px)] flex-col items-center justify-center p-4"><div class="w-full max-w-md"><!></div></div>',1);function rr(te,se){he(se,!1);const[ae,oe]=$e(),ie=()=>ye(be,"$page",ae);let C=P(!0),h=P(""),L=P(!1),z=P(!1),j=P(""),B="";ge(async()=>{const o=ie().url.searchParams.get("token");if(!o){l(h,"Verification token is missing."),l(C,!1);return}try{const e=await fetch(`/api/auth/verify?token=${o}`);if(e.redirected){B=new URL(e.url).searchParams.get("email")||"",B&&await D(B),l(L,!0),l(C,!1),setTimeout(()=>{window.location.href="/auth/sign-in?verified=true"},3e3);return}const a=await e.json();e.ok?(l(L,!0),a.email&&(B=a.email,await D(a.email)),setTimeout(()=>{window.location.href="/auth/sign-in?verified=true"},3e3)):l(h,a.error||"Failed to verify email.")}catch(e){console.error("Verification error:",e),l(h,"An error occurred during verification.")}finally{l(C,!1)}});async function D(o){try{const e=await fetch("/api/referrals");if(e.ok){const a=await e.json();a.referredBy&&(l(z,!0),l(j,a.referredBy.name||a.referredBy.email))}else e.status===401&&console.log("User not authenticated, skipping referral check")}catch(e){console.error("Error checking referral status:",e)}}xe();var J=Se(),K=S(J);we(K,{title:"Verify Email | Auto Apply",description:"Verify your email address"});var Q=s(K,2),W=r(Q),le=r(W);{var ne=o=>{O(o,{class:"text-center",children:(e,a)=>{q(e,{class:"pt-6",children:(V,d)=>{var p=Ee(),_=S(p),f=r(_);ke(f,{class:"text-primary h-12 w-12 animate-spin"}),t(_),c(4),i(V,p)},$$slots:{default:!0}})},$$slots:{default:!0}})},ce=(o,e)=>{{var a=d=>{O(d,{class:"text-center",children:(p,_)=>{q(p,{class:"pt-6",children:(f,X)=>{var T=Ue(),x=S(T),F=r(x),E=r(F);Ce(E,{class:"text-destructive h-10 w-10"}),t(F),t(x);var m=s(x,4),U=r(m,!0);t(m);var G=s(m,2),$=r(G);A($,{href:"/auth/sign-in",class:"flex-1",children:(g,R)=>{c();var y=w("Go to Sign In");i(g,y)},$$slots:{default:!0}});var H=s($,2);A(H,{href:"/",variant:"outline",class:"flex-1",children:(g,R)=>{c();var y=w("Go to Homepage");i(g,y)},$$slots:{default:!0}}),t(G),re(()=>N(U,n(h))),i(f,T)},$$slots:{default:!0}})},$$slots:{default:!0}})},V=(d,p)=>{{var _=f=>{O(f,{class:"text-center",children:(X,T)=>{q(X,{class:"pt-6",children:(x,F)=>{var E=Re(),m=S(E),U=r(m),G=r(U);je(G,{class:"h-10 w-10 text-green-600"}),t(U),t(m);var $=s(m,6);{var H=v=>{var b=Ge(),u=r(b),de=r(u);Be(de,{class:"h-5 w-5 text-green-600"}),c(2),t(u);var I=s(u,2),M=s(r(I)),fe=r(M,!0);t(M);var me=s(M);t(I);var Z=s(I,2),ee=r(Z);Ve(ee,{class:"h-4 w-4 text-green-600"});var ve=s(ee,2);Pe(ve,{variant:"secondary",class:"text-xs",children:(ue,Ye)=>{c();var pe=w("Referral Completed");i(ue,pe)},$$slots:{default:!0}}),t(Z),t(b),re(()=>{N(fe,n(j)),N(me,`'s referral! Your referral
                has been completed and both you and ${n(j)??""} may be eligible for rewards.`)}),i(v,b)};Y($,v=>{n(z)&&n(j)&&v(H)})}var g=s($,2),R=r(g);A(R,{href:"/auth/sign-in",class:"w-full",children:(v,b)=>{c();var u=w("Sign In to Your Account");i(v,u)},$$slots:{default:!0}});var y=s(R,2);A(y,{href:"/",variant:"outline",class:"w-full",children:(v,b)=>{c();var u=w("Go to Homepage");i(v,u)},$$slots:{default:!0}}),t(g),c(2),i(x,E)},$$slots:{default:!0}})},$$slots:{default:!0}})};Y(d,f=>{n(L)&&f(_)},p)}};Y(o,d=>{n(h)?d(a):d(V,!1)},e)}};Y(le,o=>{n(C)?o(ne):o(ce,!1)})}t(W),t(Q),i(te,J),_e(),oe()}export{rr as component};
