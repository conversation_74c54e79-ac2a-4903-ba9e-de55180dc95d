import{c as ae,a as r,f as o,t as xe}from"../chunks/BasJTneF.js";import{p as ur,f as i,a as fr,i as Sr,d as v,k as O,s as a,c as s,r as t,n as h,g as e,t as M,x as _e,ab as mr,v as pr}from"../chunks/CGmarHxI.js";import{s as A}from"../chunks/CIt1g2O9.js";import{i as de}from"../chunks/u21ee2wt.js";import{e as dr,i as vr}from"../chunks/C3w0v0gR.js";import{c as l}from"../chunks/BvdI7LR8.js";import{j as Ar,g as Fr}from"../chunks/CmxjS0TN.js";import{S as qr}from"../chunks/C6g8ubaU.js";import{C as He}from"../chunks/DuGukytH.js";import{C as Je}from"../chunks/Cdn-N1RY.js";import{C as Rr}from"../chunks/BkJY4La4.js";import{C as Nr}from"../chunks/GwmmX_iF.js";import{C as Qr}from"../chunks/D50jIuLr.js";import{T as Lr,R as Er}from"../chunks/I7hvcB12.js";import{b as Br,P as jr,D as kr,a as Ir,R as Dr}from"../chunks/tdzGgazS.js";import{B as gr}from"../chunks/DaBofrVv.js";import{I as hr}from"../chunks/DMTMHyMa.js";import{S as $r}from"../chunks/0ykhD7u6.js";import{B as pe}from"../chunks/B1K98fMG.js";import"../chunks/CgXBgsce.js";import{t as fe}from"../chunks/DjPYYl4Z.js";import{a as zr,b as Mr}from"../chunks/B-Xjo-Yt.js";import{b as Or}from"../chunks/CzsE_FAw.js";import{c as Vr,u as xr,s as Hr}from"../chunks/CPe_16wQ.js";import{p as Jr,s as Xr,r as Gr}from"../chunks/Btcx8l8F.js";import{X as Kr}from"../chunks/CnpHcmx3.js";import{M as tr}from"../chunks/QtAhPN2H.js";import{C as Ur}from"../chunks/BBNNmnYR.js";import{S as sr}from"../chunks/FAbXdqfL.js";import{C as Wr}from"../chunks/DkmCSZhC.js";import{S as Yr}from"../chunks/D871oxnv.js";import{f as br}from"../chunks/ncUU1dSD.js";import{T as wr}from"../chunks/C88uNE8B.js";import{C as yr}from"../chunks/-SpbofVw.js";import{P as cr}from"../chunks/DR5zc253.js";import{T as Pr}from"../chunks/DmZyh-PW.js";import{S as Cr}from"../chunks/yW0TxTga.js";import{D as Zr,a as et,b as rt,c as tt}from"../chunks/CKh8VGVX.js";import{B as st}from"../chunks/C2AK_5VT.js";function at(be,p){ur(p,!0);let Q=Jr(p,"ref",15,null),g=Gr(p,["$$slots","$$events","$$legacy","ref"]);var x=ae(),L=i(x);l(L,()=>Br,(me,oe)=>{oe(me,Xr({"data-slot":"dialog-close"},()=>g,{get ref(){return Q()},set ref(ge){Q(ge)}}))}),r(be,x),fr()}var ot=o('<!> <span class="sr-only">Close</span>',1),it=o('<div class="flex h-96 items-center justify-center"><div class="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div> <span class="ml-2">Preparing your interview questions...</span></div>'),lt=o('<div class="flex items-start"><!> <p> </p></div>'),nt=o("<!> Previous",1),dt=o(" <!>",1),vt=o(" <!>",1),ct=o('<div class="prose prose-sm max-w-none"><p> </p></div>'),ut=o("<!> Show Feedback",1),ft=o('<div class="flex h-full items-center justify-center"><!></div>'),_t=o('<div class="flex h-full flex-col items-center justify-center text-center"><!> <p class="text-gray-500">Submit your response to receive AI feedback on your interview answer.</p></div>'),mt=o('<div class="grid h-[600px] grid-cols-1 md:grid-cols-2"><div class="flex flex-col border-r"><div class="flex-1 p-6"><div class="mb-4 flex items-center"><div class="mr-2 flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-700"><span> </span></div> <div class="text-sm text-gray-500"> </div></div> <!> <div class="mb-4"><textarea class="w-full rounded-md border border-gray-300 p-2" placeholder="Type your response here..."></textarea></div> <div class="flex justify-between"><!> <!></div></div> <div class="border-t p-4"><div class="h-2 w-full rounded-full bg-gray-200"><div class="h-2 rounded-full bg-blue-500"></div></div></div></div> <div class="flex flex-col bg-gray-50"><div class="flex-1 p-6"><div class="mb-4 flex items-center"><!> <h3 class="text-lg font-medium">AI Feedback</h3></div> <!></div></div></div>'),pt=o('<div class="flex items-center justify-between border-b p-4"><div><h2 class="text-xl font-bold">AI Interview Coach</h2> <p class="text-sm text-gray-500"> </p></div> <!></div> <!>',1),gt=o("<!> <!>",1);function ht(be,p){ur(p,!0);let Q=O(!0),g=O(null),x=O(0),L=O(""),me=O(!0),oe=O(!1),ge=O(null),we=O(!1);function ar(u){u||ir()}Sr(()=>{Qe()});const Re=_e(()=>{var u,n,k;return((k=(n=(u=e(g))==null?void 0:u.questions)==null?void 0:n[e(x)])==null?void 0:k.question)||"Loading question..."}),Ae=_e(()=>{var u,n;return((n=(u=e(g))==null?void 0:u.responses)==null?void 0:n.some(k=>k.questionIndex===e(x)))||!1}),Xe=_e(()=>{var u,n,k;return((k=(n=(u=e(g))==null?void 0:u.feedback)==null?void 0:n.find(V=>V.questionIndex===e(x)))==null?void 0:k.feedback)||null}),Ge=_e(()=>e(g)?e(x)===e(g).questions.length-1:!1),Ne=_e(()=>e(x)===0),Ke=_e(()=>e(g)?Math.round((e(x)+1)/e(g).questions.length*100):0);async function Qe(){try{v(me,!0);const u=await Vr(p.jobTitle,p.applicationId,p.company,p.jobDescription);if(!u){fe.error("Failed to create interview coaching session"),p.onClose();return}v(g,u,!0)}catch(u){console.error("Error initializing session:",u),fe.error("Failed to initialize interview coaching session"),p.onClose()}finally{v(me,!1)}}async function Le(){if(!(!e(g)||!e(L).trim()))try{v(oe,!0);const u=await Hr(e(g).id,e(x),e(L));if(!u){fe.error("Failed to submit response");return}v(g,u.session,!0),v(ge,u.feedback,!0),v(we,!0),fe.success("Response submitted")}catch(u){console.error("Error submitting response:",u),fe.error("Failed to submit response")}finally{v(oe,!1)}}function Ue(){if(e(Ge)){We();return}v(we,!1),v(ge,null),v(L,""),mr(x)}function or(){e(Ne)||(v(we,!1),v(ge,null),v(L,""),mr(x,-1))}async function We(){if(e(g))try{await xr(e(g).id,"completed"),fe.success("Interview coaching session completed"),v(Q,!1),p.onClose()}catch(u){console.error("Error completing session:",u),fe.error("Failed to complete session")}}async function ir(){if(!e(g)){p.onClose();return}try{await xr(e(g).id,"cancelled"),fe.success("Interview coaching session cancelled"),v(Q,!1),p.onClose()}catch(u){console.error("Error cancelling session:",u),fe.error("Failed to cancel session"),v(Q,!1),p.onClose()}}var Ee=ae(),lr=i(Ee);l(lr,()=>Dr,(u,n)=>{n(u,{onOpenChange:ar,get open(){return e(Q)},set open(k){v(Q,k,!0)},children:(k,V)=>{var Ye=ae(),Se=i(Ye);l(Se,()=>jr,(je,Fe)=>{Fe(je,{children:(Be,ze)=>{var Me=gt(),Y=i(Me);l(Y,()=>kr,(H,ie)=>{ie(H,{})});var ve=a(Y,2);l(ve,()=>Ir,(H,ie)=>{ie(H,{class:"max-w-4xl p-0",children:(J,Z)=>{var ce=pt(),q=i(ce),X=s(q),ee=a(s(X),2),he=s(ee);t(ee),t(X);var re=a(X,2);l(re,()=>at,(I,c)=>{c(I,{class:"rounded-full p-1 hover:bg-gray-100",children:(d,b)=>{var _=ot(),$=i(_);Kr($,{class:"h-5 w-5"}),h(2),r(d,_)},$$slots:{default:!0}})}),t(q);var m=a(q,2);{var w=I=>{var c=it();r(I,c)},E=I=>{var c=mt(),d=s(c),b=s(d),_=s(b),$=s(_),y=s($),B=s(y,!0);t(y),t($);var D=a($,2),F=s(D);t(D),t(_);var te=a(_,2);l(te,()=>He,(f,S)=>{S(f,{class:"mb-4",children:(N,ne)=>{var T=ae(),j=i(T);l(j,()=>Je,(Ce,qe)=>{qe(Ce,{class:"p-4",children:(er,Oe)=>{var Te=lt(),rr=s(Te);tr(rr,{class:"mr-2 mt-1 h-5 w-5 text-blue-500"});var nr=a(rr,2),Ve=s(nr,!0);t(nr),t(Te),M(()=>A(Ve,e(Re))),r(er,Te)},$$slots:{default:!0}})}),r(N,T)},$$slots:{default:!0}})});var G=a(te,2),K=s(G);Ar(K),zr(K,"rows",8),t(G);var ue=a(G,2),R=s(ue);const z=_e(()=>e(Ne)||e(oe));pe(R,{variant:"outline",get disabled(){return e(z)},onclick:or,children:(f,S)=>{var N=nt(),ne=i(N);Ur(ne,{class:"mr-1 h-4 w-4"}),h(),r(f,N)},$$slots:{default:!0}});var le=a(R,2);{var $e=f=>{pe(f,{onclick:Ue,children:(S,N)=>{h();var ne=dt(),T=i(ne),j=a(T);Wr(j,{class:"ml-1 h-4 w-4"}),M(()=>A(T,`${e(Ge)?"Complete":"Next"} `)),r(S,ne)},$$slots:{default:!0}})},U=f=>{const S=_e(()=>!e(L).trim()||e(oe));pe(f,{get disabled(){return e(S)},onclick:Le,children:(N,ne)=>{h();var T=vt(),j=i(T),Ce=a(j);Yr(Ce,{class:"ml-1 h-4 w-4"}),M(()=>A(j,`${e(oe)?"Submitting...":"Submit"} `)),r(N,T)},$$slots:{default:!0}})};de(le,f=>{e(Ae)?f($e):f(U,!1)})}t(ue),t(b);var P=a(b,2),C=s(P),W=s(C);t(C),t(P),t(d);var ke=a(d,2),Ie=s(ke),ye=s(Ie),Pe=s(ye);sr(Pe,{class:"mr-2 h-5 w-5 text-blue-500"}),h(2),t(ye);var Ze=a(ye,2);{var se=f=>{var S=ae(),N=i(S);l(N,()=>He,(ne,T)=>{T(ne,{children:(j,Ce)=>{var qe=ae(),er=i(qe);l(er,()=>Je,(Oe,Te)=>{Te(Oe,{class:"p-4",children:(rr,nr)=>{var Ve=ct(),_r=s(Ve),Tr=s(_r,!0);t(_r),t(Ve),M(()=>A(Tr,e(Xe))),r(rr,Ve)},$$slots:{default:!0}})}),r(j,qe)},$$slots:{default:!0}})}),r(f,S)},De=(f,S)=>{{var N=T=>{var j=ft(),Ce=s(j);pe(Ce,{variant:"outline",class:"border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100",onclick:()=>v(we,!0),children:(qe,er)=>{var Oe=ut(),Te=i(Oe);sr(Te,{class:"mr-2 h-4 w-4"}),h(),r(qe,Oe)},$$slots:{default:!0}}),t(j),r(T,j)},ne=T=>{var j=_t(),Ce=s(j);sr(Ce,{class:"mb-2 h-8 w-8 text-blue-300"}),h(2),t(j),r(T,j)};de(f,T=>{e(Ae)?T(N):T(ne,!1)},S)}};de(Ze,f=>{e(we)&&e(Xe)?f(se):f(De,!1)})}t(Ie),t(ke),t(c),M(()=>{var f,S;A(B,e(x)+1),A(F,`Question ${e(x)+1} of ${(((S=(f=e(g))==null?void 0:f.questions)==null?void 0:S.length)||0)??""}`),K.disabled=e(oe)||e(Ae),Mr(W,`width: ${e(Ke)??""}%`)}),Or(K,()=>e(L),f=>v(L,f)),r(I,c)};de(m,I=>{e(me)?I(w):I(E,!1)})}M(()=>A(he,`${p.jobTitle??""}
            ${p.company?`at ${p.company}`:""}`)),r(J,ce)},$$slots:{default:!0}})}),r(Be,Me)},$$slots:{default:!0}})}),r(k,Ye)},$$slots:{default:!0}})}),r(be,Ee),fr()}var $t=o("<!> <span>Coaching Sessions</span>",1),xt=o("<!> <span>History</span>",1),bt=o("<!> <!>",1),wt=o("<!> New Session",1),yt=o('<div class="flex h-64 items-center justify-center"><p class="text-gray-500">Loading sessions...</p></div>'),Pt=o("<!> New Session",1),Ct=o('<div class="bg-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-full"><!></div> <h3 class="mb-2 text-lg font-medium">No coaching sessions found</h3> <p class="text-muted-foreground mb-4 text-center">Start a new coaching session to practice for your interviews.</p> <!>',1),St=o('<div class="flex items-center justify-between"><!> <span class="text-muted-foreground text-xs"> </span></div> <!> <!>',1),jt=o('<div class="flex items-center justify-between"><div class="flex items-center"><!> <span class="text-sm"> </span></div> <div><!></div></div>'),kt=o("<!> <!>",1),It=o('<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"></div>'),Dt=o('<div class="mb-4"><div class="relative"><!> <!></div></div> <!>',1),Tt=o('<div class="flex h-64 items-center justify-center"><p class="text-gray-500">Loading history...</p></div>'),At=o("<!> New Session",1),Ft=o('<div class="bg-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-full"><!></div> <h3 class="mb-2 text-lg font-medium">No completed sessions</h3> <p class="text-muted-foreground mb-4 text-center">Complete a coaching session to see it in your history.</p> <!>',1),qt=o('<div class="text-muted-foreground text-sm"> </div>'),Rt=o('<div class="grid grid-cols-[1fr_auto_auto] items-center gap-4 p-4"><div><div class="font-medium"> </div> <!></div> <div class="text-muted-foreground text-sm"> </div> <div><!></div></div> <!>',1),Nt=o('<div class="rounded-md border"><div class="grid grid-cols-[1fr_auto_auto] gap-4 p-4 font-medium"><div>Job Title</div> <div>Date</div> <div>Actions</div></div> <!> <!></div>'),Qt=o('<div class="mb-4"><div class="relative"><!> <!></div></div> <!>',1),Lt=o('<div class="flex items-center justify-between"><!> <!></div> <!> <!>',1),Et=o("<!> <!>",1),Bt=o('<div class="mb-4 rounded-md bg-blue-50 p-4 text-sm text-blue-700"><p>No job applications found. Add a job application first.</p></div>'),zt=(be,p,Q)=>p(e(Q)),Mt=(be,p,Q)=>be.key==="Enter"&&p(e(Q)),Ot=o('<div class="flex cursor-pointer items-center justify-between border-b p-3 hover:bg-gray-50" role="button" tabindex="0"><div><div class="font-medium"> </div> <div class="flex items-center text-sm text-gray-500"><!> </div></div> <!></div>'),Vt=o('<div class="mb-4 max-h-64 overflow-y-auto rounded-md border"></div>'),Ht=o("<!> <!> <!>",1),Jt=o("<!> <!>",1),Xt=o('<!> <div class="flex h-full flex-col"><div class="border-border flex flex-col justify-between border-b p-6"><div class="flex items-center"><!> <h2 class="text-lg font-semibold">AI Interview Coach</h2></div> <p class="text-muted-foreground">Practice for your interviews with AI-powered coaching.</p></div> <div class="flex-1 p-6"><!></div></div> <!> <!>',1);function Fs(be,p){ur(p,!0);let Q=O("sessions"),g=O(!1),x=O(null),L=O(""),me=O(pr([])),oe=O(pr([])),ge=O(!0);Sr(()=>{ar()});const we=_e(()=>e(me).filter(n=>n.jobTitle.toLowerCase().includes(e(L).toLowerCase())));async function ar(){try{const n=await fetch("/api/ai/interview/sessions");if(n.ok){const V=await n.json();v(me,V.sessions||[],!0)}const k=await fetch("/api/applications?status=applied,interview");if(k.ok){const V=await k.json();v(oe,V.applications||[],!0)}}catch(n){console.error("Error loading data:",n),fe.error("Failed to load data")}finally{v(ge,!1)}}function Re(){v(g,!0)}function Ae(n){v(x,n,!0),v(g,!1),fe.success("Starting new coaching session")}function Xe(n){switch(n){case"in_progress":return"In Progress";case"completed":return"Completed";case"cancelled":return"Cancelled";default:return n}}function Ge(n){switch(n){case"in_progress":return"bg-blue-100 text-blue-800";case"completed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}var Ne=Xt(),Ke=i(Ne);qr(Ke,{title:"AI Interview Coach | Auto Apply",description:"Practice for your interviews with AI-powered coaching"});var Qe=a(Ke,2),Le=s(Qe),Ue=s(Le),or=s(Ue);sr(or,{class:"mr-2 h-5 w-5 text-blue-500"}),h(2),t(Ue),h(2),t(Le);var We=a(Le,2),ir=s(We);l(ir,()=>Er,(n,k)=>{k(n,{get value(){return e(Q)},onValueChange:V=>v(Q,V,!0),children:(V,Ye)=>{var Se=Lt(),je=i(Se),Fe=s(je);l(Fe,()=>Lr,(Y,ve)=>{ve(Y,{children:(H,ie)=>{var J=bt(),Z=i(J);l(Z,()=>wr,(q,X)=>{X(q,{value:"sessions",class:"flex items-center gap-2",children:(ee,he)=>{var re=$t(),m=i(re);tr(m,{class:"h-4 w-4"}),h(2),r(ee,re)},$$slots:{default:!0}})});var ce=a(Z,2);l(ce,()=>wr,(q,X)=>{X(q,{value:"history",class:"flex items-center gap-2",children:(ee,he)=>{var re=xt(),m=i(re);yr(m,{class:"h-4 w-4"}),h(2),r(ee,re)},$$slots:{default:!0}})}),r(H,J)},$$slots:{default:!0}})});var Be=a(Fe,2);pe(Be,{onclick:Re,children:(Y,ve)=>{var H=wt(),ie=i(H);cr(ie,{class:"mr-2 h-4 w-4"}),h(),r(Y,H)},$$slots:{default:!0}}),t(je);var ze=a(je,2);l(ze,()=>Pr,(Y,ve)=>{ve(Y,{value:"sessions",class:"mt-6",children:(H,ie)=>{var J=Dt(),Z=i(J),ce=s(Z),q=s(ce);Cr(q,{class:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"});var X=a(q,2);hr(X,{type:"search",placeholder:"Search sessions...",class:"pl-8",get value(){return e(L)},set value(m){v(L,m,!0)}}),t(ce),t(Z);var ee=a(Z,2);{var he=m=>{var w=yt();r(m,w)},re=(m,w)=>{{var E=c=>{var d=ae(),b=i(d);l(b,()=>He,(_,$)=>{$(_,{children:(y,B)=>{var D=ae(),F=i(D);l(F,()=>Je,(te,G)=>{G(te,{class:"flex flex-col items-center justify-center p-6",children:(K,ue)=>{var R=Ct(),z=i(R),le=s(z);tr(le,{class:"text-primary h-6 w-6"}),t(z);var $e=a(z,6);pe($e,{onclick:Re,children:(U,P)=>{var C=Pt(),W=i(C);cr(W,{class:"mr-2 h-4 w-4"}),h(),r(U,C)},$$slots:{default:!0}}),r(K,R)},$$slots:{default:!0}})}),r(y,D)},$$slots:{default:!0}})}),r(c,d)},I=c=>{var d=It();dr(d,21,()=>e(we),vr,(b,_)=>{var $=ae(),y=i($);l(y,()=>He,(B,D)=>{D(B,{class:"overflow-hidden",children:(F,te)=>{var G=kt(),K=i(G);l(K,()=>Nr,(R,z)=>{z(R,{class:"pb-3",children:(le,$e)=>{var U=St(),P=i(U),C=s(P);const W=_e(()=>Ge(e(_).status));gr(C,{get class(){return e(W)},children:(se,De)=>{h();var f=xe();M(S=>A(f,S),[()=>Xe(e(_).status)]),r(se,f)},$$slots:{default:!0}});var ke=a(C,2),Ie=s(ke,!0);t(ke),t(P);var ye=a(P,2);l(ye,()=>Qr,(se,De)=>{De(se,{class:"line-clamp-1",children:(f,S)=>{h();var N=xe();M(()=>A(N,e(_).jobTitle)),r(f,N)},$$slots:{default:!0}})});var Pe=a(ye,2);{var Ze=se=>{var De=ae(),f=i(De);l(f,()=>Rr,(S,N)=>{N(S,{children:(ne,T)=>{h();var j=xe();M(()=>A(j,e(_).industry)),r(ne,j)},$$slots:{default:!0}})}),r(se,De)};de(Pe,se=>{e(_).industry&&se(Ze)})}M(se=>A(Ie,se),[()=>br(e(_).sessionDate)]),r(le,U)},$$slots:{default:!0}})});var ue=a(K,2);l(ue,()=>Je,(R,z)=>{z(R,{class:"pb-3",children:(le,$e)=>{var U=jt(),P=s(U),C=s(P);tr(C,{class:"mr-2 h-4 w-4 text-gray-500"});var W=a(C,2),ke=s(W);t(W),t(P);var Ie=a(P,2),ye=s(Ie);pe(ye,{variant:"outline",size:"sm",onclick:()=>{},children:(Pe,Ze)=>{h();var se=xe("Continue");r(Pe,se)},$$slots:{default:!0}}),t(Ie),t(U),M(()=>{var Pe;return A(ke,`${(((Pe=e(_).questions)==null?void 0:Pe.length)||0)??""} Questions`)}),r(le,U)},$$slots:{default:!0}})}),r(F,G)},$$slots:{default:!0}})}),r(b,$)}),t(d),r(c,d)};de(m,c=>{e(we).length===0?c(E):c(I,!1)},w)}};de(ee,m=>{e(ge)?m(he):m(re,!1)})}r(H,J)},$$slots:{default:!0}})});var Me=a(ze,2);l(Me,()=>Pr,(Y,ve)=>{ve(Y,{value:"history",class:"mt-6",children:(H,ie)=>{var J=Qt(),Z=i(J),ce=s(Z),q=s(ce);Cr(q,{class:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"});var X=a(q,2);hr(X,{type:"search",placeholder:"Search history...",class:"pl-8",get value(){return e(L)},set value(m){v(L,m,!0)}}),t(ce),t(Z);var ee=a(Z,2);{var he=m=>{var w=Tt();r(m,w)},re=(m,w)=>{{var E=c=>{var d=ae(),b=i(d);l(b,()=>He,(_,$)=>{$(_,{children:(y,B)=>{var D=ae(),F=i(D);l(F,()=>Je,(te,G)=>{G(te,{class:"flex flex-col items-center justify-center p-6",children:(K,ue)=>{var R=Ft(),z=i(R),le=s(z);yr(le,{class:"text-primary h-6 w-6"}),t(z);var $e=a(z,6);pe($e,{onclick:Re,children:(U,P)=>{var C=At(),W=i(C);cr(W,{class:"mr-2 h-4 w-4"}),h(),r(U,C)},$$slots:{default:!0}}),r(K,R)},$$slots:{default:!0}})}),r(y,D)},$$slots:{default:!0}})}),r(c,d)},I=c=>{var d=Nt(),b=a(s(d),2);$r(b,{});var _=a(b,2);dr(_,17,()=>e(me).filter($=>$.status==="completed"),vr,($,y)=>{var B=Rt(),D=i(B),F=s(D),te=s(F),G=s(te,!0);t(te);var K=a(te,2);{var ue=P=>{var C=qt(),W=s(C,!0);t(C),M(()=>A(W,e(y).industry)),r(P,C)};de(K,P=>{e(y).industry&&P(ue)})}t(F);var R=a(F,2),z=s(R,!0);t(R);var le=a(R,2),$e=s(le);pe($e,{variant:"outline",size:"sm",children:(P,C)=>{h();var W=xe("View");r(P,W)},$$slots:{default:!0}}),t(le),t(D);var U=a(D,2);$r(U,{}),M(P=>{A(G,e(y).jobTitle),A(z,P)},[()=>br(e(y).sessionDate)]),r($,B)}),t(d),r(c,d)};de(m,c=>{e(me).filter(d=>d.status==="completed").length===0?c(E):c(I,!1)},w)}};de(ee,m=>{e(ge)?m(he):m(re,!1)})}r(H,J)},$$slots:{default:!0}})}),r(V,Se)},$$slots:{default:!0}})}),t(We),t(Qe);var Ee=a(Qe,2);l(Ee,()=>Dr,(n,k)=>{k(n,{get open(){return e(g)},set open(V){v(g,V,!0)},children:(V,Ye)=>{var Se=ae(),je=i(Se);l(je,()=>jr,(Fe,Be)=>{Be(Fe,{children:(ze,Me)=>{var Y=Jt(),ve=i(Y);l(ve,()=>kr,(ie,J)=>{J(ie,{})});var H=a(ve,2);l(H,()=>Ir,(ie,J)=>{J(ie,{class:"sm:max-w-md",children:(Z,ce)=>{var q=Ht(),X=i(q);l(X,()=>Zr,(w,E)=>{E(w,{children:(I,c)=>{var d=Et(),b=i(d);l(b,()=>et,($,y)=>{y($,{children:(B,D)=>{h();var F=xe("Select an Application");r(B,F)},$$slots:{default:!0}})});var _=a(b,2);l(_,()=>rt,($,y)=>{y($,{children:(B,D)=>{h();var F=xe("Choose a job application to practice for an interview.");r(B,F)},$$slots:{default:!0}})}),r(I,d)},$$slots:{default:!0}})});var ee=a(X,2);{var he=w=>{var E=Bt();r(w,E)},re=w=>{var E=Vt();dr(E,21,()=>e(oe),vr,(I,c)=>{var d=Ot();d.__click=[zt,Ae,c],d.__keydown=[Mt,Ae,c];var b=s(d),_=s(b),$=s(_,!0);t(_);var y=a(_,2),B=s(y);st(B,{class:"mr-1 h-3 w-3"});var D=a(B);t(y),t(b);var F=a(b,2);const te=_e(()=>e(c).status==="interview"?"default":"outline");gr(F,{get variant(){return e(te)},children:(G,K)=>{h();var ue=xe();M(()=>A(ue,e(c).status)),r(G,ue)},$$slots:{default:!0}}),t(d),M(()=>{A($,e(c).jobTitle),A(D,` ${e(c).company??""}`)}),r(I,d)}),t(E),r(w,E)};de(ee,w=>{e(oe).length===0?w(he):w(re,!1)})}var m=a(ee,2);l(m,()=>tt,(w,E)=>{E(w,{children:(I,c)=>{pe(I,{variant:"outline",onclick:()=>v(g,!1),children:(d,b)=>{h();var _=xe("Cancel");r(d,_)},$$slots:{default:!0}})},$$slots:{default:!0}})}),r(Z,q)},$$slots:{default:!0}})}),r(ze,Y)},$$slots:{default:!0}})}),r(V,Se)},$$slots:{default:!0}})});var lr=a(Ee,2);{var u=n=>{ht(n,{get applicationId(){return e(x).id},get jobTitle(){return e(x).jobTitle},get company(){return e(x).company},onClose:()=>v(x,null)})};de(lr,n=>{e(x)&&n(u)})}r(be,Ne),fr()}Fr(["click","keydown"]);export{Fs as component};
