import{f as p,a as r,t as c}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as f,f as u,a as h,s,c as v,n as x,r as g}from"../chunks/CGmarHxI.js";import{i as y}from"../chunks/BIEMS98f.js";import{g as b}from"../chunks/BiJhC7W5.js";import{S as k}from"../chunks/C6g8ubaU.js";import{B as $}from"../chunks/B1K98fMG.js";var _=p('<!> <section class="flex h-screen flex-col items-center justify-center"><div class="relative"><div class="animate-pulse text-9xl font-bold">404</div></div> <p class="mt-4 text-xl">Looks like you’re lost in space…</p> <!></section>',1);function F(a,i){f(i,!1);function n(){b("/")}y();var e=_(),o=u(e);k(o,{title:"404 - Page Not Found | Hirli",description:"The page you are looking for does not exist. You may have mistyped the address or the page may have moved.",keywords:"404, page not found, error, Hirli"});var t=s(o,2),l=s(v(t),4);$(l,{onclick:n,class:"border-border mt-6 inline-flex h-10 items-center justify-center rounded-full border px-6 py-3 text-sm font-semibold ",children:(m,H)=>{x();var d=c("Take me home");r(m,d)},$$slots:{default:!0}}),g(t),r(a,e),h()}export{F as component};
