import{c as n,a as i}from"./BasJTneF.js";import{p as m,f as c,au as d,a as f}from"./CGmarHxI.js";import{s as h}from"./ncUU1dSD.js";import{s as l,r as u}from"./Btcx8l8F.js";import{I as $}from"./DxW95yuQ.js";function x(s,o){m(o,!0);let t=u(o,["$$slots","$$events","$$legacy"]);const e=[["path",{d:"m9 18 6-6-6-6"}]];$(s,l({name:"chevron-right"},()=>t,{get iconNode(){return e},children:(a,g)=>{var r=n(),p=c(r);h(p,()=>o.children??d),i(a,r)},$$slots:{default:!0}})),f()}export{x as C};
