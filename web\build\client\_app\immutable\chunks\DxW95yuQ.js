import{e as x,a as l,c as N}from"./BasJTneF.js";import{p as y,c as z,s as S,au as C,r as I,a as j,f as A,g as s,x as B,aM as M}from"./CGmarHxI.js";import{s as q}from"./ncUU1dSD.js";import{e as D,i as E}from"./C3w0v0gR.js";import{e as F}from"./w80wGXGd.js";import{e as m}from"./B-Xjo-Yt.js";import{p as t,r as G}from"./Btcx8l8F.js";/**
 * @license @lucide/svelte v0.482.0 - ISC
 *
 * ISC License
 * 
 * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
 * 
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 * 
 */const H={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};var J=x("<svg><!><!></svg>");function V(h,e){y(e,!0);const u=t(e,"color",3,"currentColor"),r=t(e,"size",3,24),i=t(e,"strokeWidth",3,2),f=t(e,"absoluteStrokeWidth",3,!1),v=t(e,"iconNode",19,()=>[]),g=G(e,["$$slots","$$events","$$legacy","name","color","size","strokeWidth","absoluteStrokeWidth","iconNode","children"]);var o=J();m(o,a=>({...H,...g,width:r(),height:r(),stroke:u(),"stroke-width":a,class:["lucide-icon lucide",e.name&&`lucide-${e.name}`,e.class]}),[()=>f()?Number(i())*24/Number(r()):i()]);var n=z(o);D(n,17,v,E,(a,w)=>{var c=B(()=>M(s(w),2));let b=()=>s(c)[0],_=()=>s(c)[1];var d=N(),p=A(d);F(p,b,!0,(W,K)=>{m(W,()=>({..._()}))}),l(a,d)});var k=S(n);q(k,()=>e.children??C),I(o),l(h,o),j()}export{V as I};
