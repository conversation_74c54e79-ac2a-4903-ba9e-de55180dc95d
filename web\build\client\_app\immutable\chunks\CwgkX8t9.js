import{c as p,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function M(s,o){const t=l(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["circle",{cx:"12",cy:"10",r:"3"}]];f(s,d({name:"map-pin"},()=>t,{get iconNode(){return a},children:(e,$)=>{var r=p(),n=i(r);m(n,o,"default",{},null),c(e,r)},$$slots:{default:!0}}))}export{M};
