import{f as l,a as c,t as we}from"./BasJTneF.js";import{p as ye,c as a,f as Te,r as e,s as r,t as y,g as s,x as te,a as pe,n as ae}from"./CGmarHxI.js";import{s as o}from"./CIt1g2O9.js";import{i as T}from"./u21ee2wt.js";import{e as J,i as K}from"./C3w0v0gR.js";import{c as O}from"./BvdI7LR8.js";import{a as Se,b as De,c as je}from"./BPr9JIwg.js";import{B as ke}from"./DaBofrVv.js";import{S as Q,a as Ae,b as Me}from"./D0KcwhQz.js";import{C as Pe}from"./CKg8MWp_.js";import{M as Be}from"./QtAhPN2H.js";var Ce=l('<div class="flex w-full items-center gap-4 pr-4"><div class="flex h-8 w-8 items-center justify-center rounded-full bg-red-100 dark:bg-red-900"><!></div> <div class="flex w-full flex-col gap-1"><h3 class="text-lg font-medium"> </h3> <div class="mt-1 flex items-center justify-between gap-2"><div class="flex flex-wrap gap-2"><!> <!></div> <span class="text-muted-foreground text-xs"> </span></div></div></div>'),Ue=l('<div class="mb-4"><div class="mb-2 flex items-center justify-between"><div class="text-xs text-gray-500"> </div> <!></div> <!></div>'),Fe=l('<div class="mt-4"><h4 class="mb-2 text-sm font-medium">Affected Services</h4> <div class="flex flex-wrap gap-2"></div></div>'),Ie=l('<div class="bg-muted rounded-sm p-2 text-xs"><div class="mb-1 flex items-center justify-between"><span class="font-medium"> </span> <span class="text-muted-foreground text-[10px]"> </span></div> <p> </p></div>'),qe=l('<div class="mt-3 border-t pt-2"><h5 class="mb-2 flex items-center gap-1 text-xs font-medium"><!> Comments</h5> <div class="space-y-2"></div></div>'),Le=l('<div class="relative pl-6 before:absolute before:left-0 before:top-0 before:h-full before:w-0.5 before:bg-gray-200 dark:before:bg-gray-800"><div class="absolute left-[-4px] top-1 h-2 w-2 rounded-full bg-gray-300 dark:bg-gray-700"></div> <div class="mb-1 flex items-center justify-between"><div class="text-muted-foreground text-xs"> </div> <!></div> <p class="text-sm"> </p> <!></div>'),Ne=l('<div class="mt-6"><h4 class="mb-3 text-sm font-medium">Updates</h4> <div class="space-y-4"></div></div>'),We=l('<div class="border-t pt-4"><!> <p class="whitespace-pre-line text-sm"> </p> <!> <!></div>'),Ye=l('<div class="bg-gray-50 dark:bg-gray-900"><!></div> <!>',1),ze=l('<div class="overflow-hidden rounded-lg border"><!></div>');function et(re,t){ye(t,!0);function R(m){return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(m))}function V(m){return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",year:"numeric"}).format(new Date(m))}function ie(){if(!t.incident.startTime||!t.incident.endTime)return 0;const m=new Date(t.incident.startTime).getTime(),j=new Date(t.incident.endTime).getTime(),p=Date.now();return p<=m?0:p>=j?100:Math.round((p-m)/(j-m)*100)}var P=ze(),se=a(P);const ne=te(()=>`incident-${t.index}`);O(se,()=>Se,(m,j)=>{j(m,{get value(){return s(ne)},class:"border-0",children:(p,Ee)=>{var X=Ye(),B=Te(X),de=a(B);O(de,()=>De,(C,U)=>{U(C,{class:"flex w-full items-center justify-between p-4 text-left",children:(F,ce)=>{var x=Ce(),b=a(x),I=a(b);Pe(I,{class:"h-4 w-4 text-red-500 dark:text-red-400"}),e(b);var h=r(b,2),S=a(h),k=a(S,!0);e(S);var A=r(S,2),D=a(A),M=a(D);Q(M,{get status(){return t.incident.status}});var n=r(M,2);{var d=i=>{Ae(i,{get severity(){return t.incident.severity}})};T(n,i=>{t.incident.severity&&i(d)})}e(D);var v=r(D,2),f=a(v);e(v),e(A),e(h),e(x),y(i=>{o(k,t.incident.title),o(f,`Started ${i??""}`)},[()=>R(t.incident.date||t.incident.startTime)]),c(F,x)},$$slots:{default:!0}})}),e(B);var ve=r(B,2);O(ve,()=>je,(C,U)=>{U(C,{class:"p-4 pt-0",children:(F,ce)=>{var x=We(),b=a(x);{var I=n=>{var d=Ue(),v=a(d),f=a(v),i=a(f);e(f);var g=r(f,2);Q(g,{get status(){return t.incident.status}}),e(v);var w=r(v,2);const _=te(()=>t.incident.progress||ie());Me(w,{get startTime(){return t.incident.startTime},get endTime(){return t.incident.endTime},get status(){return t.incident.status},get progress(){return s(_)},showTimes:!1}),e(d),y((q,L)=>o(i,`${q??""} → ${L??""}`),[()=>V(t.incident.startTime),()=>V(t.incident.endTime)]),c(n,d)};T(b,n=>{t.incident.startTime&&t.incident.endTime&&n(I)})}var h=r(b,2),S=a(h,!0);e(h);var k=r(h,2);{var A=n=>{var d=Fe(),v=r(a(d),2);J(v,21,()=>t.incident.affectedServices,K,(f,i)=>{ke(f,{variant:"outline",children:(g,w)=>{ae();var _=we();y(()=>o(_,s(i))),c(g,_)},$$slots:{default:!0}})}),e(v),e(d),c(n,d)};T(k,n=>{t.incident.affectedServices&&t.incident.affectedServices.length>0&&n(A)})}var D=r(k,2);{var M=n=>{var d=Ne(),v=r(a(d),2);J(v,21,()=>t.incident.updates,K,(f,i)=>{var g=Le(),w=r(a(g),2),_=a(w),q=a(_,!0);e(_);var L=r(_,2);{var oe=u=>{Q(u,{get status(){return s(i).status},className:"ml-2"})};T(L,u=>{s(i).status&&u(oe)})}e(w);var N=r(w,2),le=a(N,!0);e(N);var me=r(N,2);{var fe=u=>{var W=qe(),Y=a(W),ue=a(Y);Be(ue,{class:"h-3 w-3"}),ae(),e(Y);var Z=r(Y,2);J(Z,21,()=>s(i).comments,K,(_e,z)=>{var E=Ie(),G=a(E),H=a(G),xe=a(H,!0);e(H);var $=r(H,2),ge=a($,!0);e($),e(G);var ee=r(G,2),be=a(ee,!0);e(ee),e(E),y(he=>{o(xe,s(z).author||"System"),o(ge,he),o(be,s(z).text)},[()=>new Date(s(z).timestamp).toLocaleString()]),c(_e,E)}),e(Z),e(W),c(u,W)};T(me,u=>{s(i).comments&&s(i).comments.length>0&&u(fe)})}e(g),y(u=>{o(q,u),o(le,s(i).message)},[()=>R(s(i).date||s(i).timestamp)]),c(f,g)}),e(v),e(d),c(n,d)};T(D,n=>{t.incident.updates&&t.incident.updates.length>0&&n(M)})}e(x),y(()=>o(S,t.incident.description)),c(F,x)},$$slots:{default:!0}})}),c(p,X)},$$slots:{default:!0}})}),e(P),c(re,P),pe()}export{et as M};
