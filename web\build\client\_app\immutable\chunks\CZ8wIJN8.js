import{c as i,a as n}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as p,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function v(t,r){const e=p(r,["children","$$slots","$$events","$$legacy"]),s=[["circle",{cx:"12",cy:"12",r:"10"}],["circle",{cx:"12",cy:"12",r:"6"}],["circle",{cx:"12",cy:"12",r:"2"}]];f(t,d({name:"target"},()=>e,{get iconNode(){return s},children:(c,$)=>{var o=i(),a=l(o);m(a,r,"default",{},null),n(c,o)},$$slots:{default:!0}}))}export{v as T};
