import{f,t as h,a as s,c as Be}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as je,f as x,c as n,s as t,n as c,r as l,g as o,e as C,a as Ee,t as ye,d as ve,m as qe,_ as Ae}from"../chunks/CGmarHxI.js";import{s as xe}from"../chunks/CIt1g2O9.js";import{i as M}from"../chunks/u21ee2wt.js";import{e as Te,i as Me}from"../chunks/C3w0v0gR.js";import{c as Je}from"../chunks/BvdI7LR8.js";import{a as Ie}from"../chunks/DDUgF6Ik.js";import{a as Se,s as De,e as Ue}from"../chunks/CmxjS0TN.js";import{i as Re}from"../chunks/BIEMS98f.js";import{p as Fe}from"../chunks/Btcx8l8F.js";import{g as We}from"../chunks/BiJhC7W5.js";import{s as He}from"../chunks/B8blszX7.js";import{R as Ve,T as ze}from"../chunks/I7hvcB12.js";import{R as Oe,A as Le,a as Ye,b as Ge,c as Ke,d as Qe,e as Xe,f as Ze}from"../chunks/BnikQ10_.js";import{B as Pe}from"../chunks/B1K98fMG.js";import{t as T}from"../chunks/DjPYYl4Z.js";import{S as et}from"../chunks/C6g8ubaU.js";import{S as L}from"../chunks/D9yI7a4E.js";import{R as Ne,S as ke,a as Ce,b as X}from"../chunks/CGK0g3x_.js";import{o as tt}from"../chunks/nZgk9enP.js";import{M as st}from"../chunks/yPulTJ2h.js";import{B as ot}from"../chunks/hA0h0kTo.js";import{M as it}from"../chunks/DSDNnczY.js";import{M as at}from"../chunks/2KCyzleV.js";import{T as rt}from"../chunks/C88uNE8B.js";import{T as nt}from"../chunks/DmZyh-PW.js";var lt=f("<!> <!> <!>",1),dt=f("<!> <!>",1),ct=f("<!> <!>",1),ut=f("<!> <!>",1),vt=f('<div class="space-y-2"><div class="font-medium">Email Digest Frequency</div> <!> <div class="text-muted-foreground text-sm">How often you want to receive email digests summarizing your notifications</div></div> <div class="space-y-2"><div class="font-medium">Email Format</div> <!> <div class="text-muted-foreground text-sm">Choose how you want your emails to be formatted</div></div>',1),ft=f('<div class="border-border border-b px-6 py-4"><div class="flex items-center justify-between"><div><h4 class="text-md font-normal">Email Notifications</h4> <p class="text-muted-foreground text-sm">Configure how you receive email notifications.</p></div> <!></div></div> <div class="grid grid-cols-2 items-center justify-between gap-4 p-4"><div class="space-y-0.5"><div class="font-medium">Email Notifications</div> <div class="text-muted-foreground text-sm">Receive notifications via email</div></div> <!> <!></div>',1);function mt(me,Y){je(Y,!1);const[pe,ge]=De(),i=()=>Se(d,"$form",pe);let he=Fe(Y,"formData",8);const{form:d}=he();function m(){const j=document.getElementById("notification-form");j&&j.dispatchEvent(new Event("change",{bubbles:!0}))}Re();var p=ft(),R=x(p),V=n(R),ne=t(n(V),2);Pe(ne,{variant:"outline",onclick:()=>We("/dashboard/settings/account"),children:(j,E)=>{c();var K=h("Account Settings");s(j,K)},$$slots:{default:!0}}),l(V),l(R);var G=t(R,2),Z=t(n(G),2);const le=C(()=>!!i().emailNotifications);L(Z,{get checked(){return o(le)},onCheckedChange:j=>{d.update(E=>({...E,emailNotifications:j})),m()}});var oe=t(Z,2);{var de=j=>{var E=vt(),K=x(E),fe=t(n(K),2);const ie=C(()=>i().emailDigest||"daily");Ne(fe,{type:"single",get value(){return o(ie)},onValueChange:z=>{d.update(ee=>({...ee,emailDigest:z})),m()},children:(z,ee)=>{var ce=dt(),g=x(ce);ke(g,{class:"w-full",children:(W,ue)=>{c();var P=h();ye(()=>xe(P,i().emailDigest==="daily"?"Daily":i().emailDigest==="weekly"?"Weekly":i().emailDigest==="never"?"Never":"Select frequency")),s(W,P)},$$slots:{default:!0}});var U=t(g,2);Ce(U,{class:"max-h-60",children:(W,ue)=>{var P=lt(),H=x(P);X(H,{value:"daily",children:(D,N)=>{c();var b=h("Daily");s(D,b)},$$slots:{default:!0}});var F=t(H,2);X(F,{value:"weekly",children:(D,N)=>{c();var b=h("Weekly");s(D,b)},$$slots:{default:!0}});var S=t(F,2);X(S,{value:"never",children:(D,N)=>{c();var b=h("Never");s(D,b)},$$slots:{default:!0}}),s(W,P)},$$slots:{default:!0}}),s(z,ce)},$$slots:{default:!0}}),c(2),l(K);var $=t(K,2),w=t(n($),2);const ae=C(()=>i().emailFormat||"html");Ne(w,{type:"single",get value(){return o(ae)},onValueChange:z=>{d.update(ee=>({...ee,emailFormat:z})),m()},children:(z,ee)=>{var ce=ut(),g=x(ce);ke(g,{class:"w-full",children:(W,ue)=>{c();var P=h();ye(()=>xe(P,i().emailFormat==="html"?"HTML (Rich formatting)":i().emailFormat==="text"?"Plain Text":"Select format")),s(W,P)},$$slots:{default:!0}});var U=t(g,2);Ce(U,{class:"max-h-60",children:(W,ue)=>{var P=ct(),H=x(P);X(H,{value:"html",children:(S,D)=>{c();var N=h("HTML (Rich formatting)");s(S,N)},$$slots:{default:!0}});var F=t(H,2);X(F,{value:"text",children:(S,D)=>{c();var N=h("Plain Text");s(S,N)},$$slots:{default:!0}}),s(W,P)},$$slots:{default:!0}}),s(z,ce)},$$slots:{default:!0}}),c(2),l($),s(j,E)};M(oe,j=>{i().emailNotifications&&j(de)})}l(G),s(me,p),Ee(),ge()}var pt=f("<!> <!> <!>",1),gt=f("<!> <!>",1),ht=f('<div class="border-muted mb-6 mt-4 border-l-2 pl-6"><div class="space-y-2"><div class="font-medium">Notification Frequency</div> <!> <div class="text-muted-foreground text-sm">How often you want to receive job match notifications</div></div></div>'),_t=f("<!> <!> <!>",1),$t=f("<!> <!>",1),bt=f('<div class="border-muted mb-6 mt-4 border-l-2 pl-6"><div class="space-y-2"><div class="font-medium">Notification Frequency</div> <!> <div class="text-muted-foreground text-sm">How often you want to receive new job notifications</div></div></div>'),yt=f("<!> <!> <!>",1),xt=f("<!> <!>",1),wt=f('<div class="border-muted mb-6 mt-4 border-l-2 pl-6"><div class="space-y-2"><div class="font-medium">Notification Frequency</div> <!> <div class="text-muted-foreground text-sm">How often you want to receive automation notifications</div></div></div>'),Pt=f(`<div class="border-border flex flex-col border-b px-6 py-4"><h4 class="text-md font-normal">Job Alert Notifications</h4> <p class="text-muted-foreground text-sm">Configure notifications for job-related activities.</p></div> <div class="grid grid-cols-2 gap-4 p-4"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Job Match Notifications</div> <div class="text-muted-foreground text-sm">Receive notifications when new job matches are found</div></div> <!></div> <!> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Application Status Updates</div> <div class="text-muted-foreground text-sm">Receive notifications when your application status changes</div></div> <!></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">New Jobs Notifications</div> <div class="text-muted-foreground text-sm">Receive notifications when new jobs matching your criteria are posted</div></div> <!></div> <!> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Interview Reminders</div> <div class="text-muted-foreground text-sm">Receive reminders about upcoming interviews and follow-ups</div></div> <!></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Saved Jobs Updates</div> <div class="text-muted-foreground text-sm">Receive updates about jobs you've saved (closing dates, changes, etc.)</div></div> <!></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Automation Notifications</div> <div class="text-muted-foreground text-sm">Receive notifications about automation runs, status updates, and results</div></div> <!></div> <!> <div class="mt-6 border-t pt-6"><div class="mb-4 font-medium">Notification Channels</div> <div class="space-y-4"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Email Notifications</div> <div class="text-muted-foreground text-sm">Receive job alerts via email</div></div> <!></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Browser Notifications</div> <div class="text-muted-foreground text-sm">Receive job alerts in your browser</div></div> <!></div></div></div></div>`,1);function Nt(me,Y){je(Y,!1);const[pe,ge]=De(),i=()=>Se(d,"$form",pe);let he=Fe(Y,"formData",8);const{form:d}=he();function m(){const a=document.getElementById("notification-form");a&&a.dispatchEvent(new Event("change",{bubbles:!0}))}Re();var p=Pt(),R=t(x(p),2),V=n(R),ne=t(n(V),2);const G=C(()=>!!i().jobMatchNotifications);L(ne,{get checked(){return o(G)},onCheckedChange:a=>{d.update(u=>({...u,jobMatchNotifications:a})),m()}}),l(V);var Z=t(V,2);{var le=a=>{var u=ht(),Q=n(u),e=t(n(Q),2);const r=C(()=>i().jobMatchFrequency||"daily");Ne(e,{type:"single",get value(){return o(r)},onValueChange:v=>{d.update(B=>({...B,jobMatchFrequency:v})),m()},children:(v,B)=>{var _=gt(),y=x(_);ke(y,{class:"w-full",children:(A,re)=>{c();var k=h();ye(()=>xe(k,i().jobMatchFrequency==="realtime"?"Real-time":i().jobMatchFrequency==="daily"?"Daily Digest":i().jobMatchFrequency==="weekly"?"Weekly Digest":"Select frequency")),s(A,k)},$$slots:{default:!0}});var se=t(y,2);Ce(se,{class:"max-h-60",children:(A,re)=>{var k=pt(),_e=x(k);X(_e,{value:"realtime",children:(J,be)=>{c();var I=h("Real-time");s(J,I)},$$slots:{default:!0}});var $e=t(_e,2);X($e,{value:"daily",children:(J,be)=>{c();var I=h("Daily Digest");s(J,I)},$$slots:{default:!0}});var we=t($e,2);X(we,{value:"weekly",children:(J,be)=>{c();var I=h("Weekly Digest");s(J,I)},$$slots:{default:!0}}),s(A,k)},$$slots:{default:!0}}),s(v,_)},$$slots:{default:!0}}),c(2),l(Q),l(u),s(a,u)};M(Z,a=>{i().jobMatchNotifications&&a(le)})}var oe=t(Z,2),de=t(n(oe),2);const j=C(()=>!!i().applicationStatusNotifications);L(de,{get checked(){return o(j)},onCheckedChange:a=>{d.update(u=>({...u,applicationStatusNotifications:a})),m()}}),l(oe);var E=t(oe,2),K=t(n(E),2);const fe=C(()=>!!i().newJobsNotifications);L(K,{get checked(){return o(fe)},onCheckedChange:a=>{d.update(u=>({...u,newJobsNotifications:a})),m()}}),l(E);var ie=t(E,2);{var $=a=>{var u=bt(),Q=n(u),e=t(n(Q),2);const r=C(()=>i().newJobsFrequency||"daily");Ne(e,{type:"single",get value(){return o(r)},onValueChange:v=>{d.update(B=>({...B,newJobsFrequency:v})),m()},children:(v,B)=>{var _=$t(),y=x(_);ke(y,{class:"w-full",children:(A,re)=>{c();var k=h();ye(()=>xe(k,i().newJobsFrequency==="realtime"?"Real-time":i().newJobsFrequency==="daily"?"Daily Digest":i().newJobsFrequency==="weekly"?"Weekly Digest":"Select frequency")),s(A,k)},$$slots:{default:!0}});var se=t(y,2);Ce(se,{class:"max-h-60",children:(A,re)=>{var k=_t(),_e=x(k);X(_e,{value:"realtime",children:(J,be)=>{c();var I=h("Real-time");s(J,I)},$$slots:{default:!0}});var $e=t(_e,2);X($e,{value:"daily",children:(J,be)=>{c();var I=h("Daily Digest");s(J,I)},$$slots:{default:!0}});var we=t($e,2);X(we,{value:"weekly",children:(J,be)=>{c();var I=h("Weekly Digest");s(J,I)},$$slots:{default:!0}}),s(A,k)},$$slots:{default:!0}}),s(v,_)},$$slots:{default:!0}}),c(2),l(Q),l(u),s(a,u)};M(ie,a=>{i().newJobsNotifications&&a($)})}var w=t(ie,2),ae=t(n(w),2);const z=C(()=>!!i().interviewReminders);L(ae,{get checked(){return o(z)},onCheckedChange:a=>{d.update(u=>({...u,interviewReminders:a})),m()}}),l(w);var ee=t(w,2),ce=t(n(ee),2);const g=C(()=>!!i().savedJobsUpdates);L(ce,{get checked(){return o(g)},onCheckedChange:a=>{d.update(u=>({...u,savedJobsUpdates:a})),m()}}),l(ee);var U=t(ee,2),W=t(n(U),2);const ue=C(()=>!!i().automationNotifications);L(W,{get checked(){return o(ue)},onCheckedChange:a=>{d.update(u=>({...u,automationNotifications:a})),m()}}),l(U);var P=t(U,2);{var H=a=>{var u=wt(),Q=n(u),e=t(n(Q),2);const r=C(()=>i().automationFrequency||"realtime");Ne(e,{type:"single",get value(){return o(r)},onValueChange:v=>{d.update(B=>({...B,automationFrequency:v})),m()},children:(v,B)=>{var _=xt(),y=x(_);ke(y,{class:"w-full",children:(A,re)=>{c();var k=h();ye(()=>xe(k,i().automationFrequency==="realtime"?"Real-time":i().automationFrequency==="daily"?"Daily Digest":i().automationFrequency==="weekly"?"Weekly Digest":"Select frequency")),s(A,k)},$$slots:{default:!0}});var se=t(y,2);Ce(se,{class:"max-h-60",children:(A,re)=>{var k=yt(),_e=x(k);X(_e,{value:"realtime",children:(J,be)=>{c();var I=h("Real-time");s(J,I)},$$slots:{default:!0}});var $e=t(_e,2);X($e,{value:"daily",children:(J,be)=>{c();var I=h("Daily Digest");s(J,I)},$$slots:{default:!0}});var we=t($e,2);X(we,{value:"weekly",children:(J,be)=>{c();var I=h("Weekly Digest");s(J,I)},$$slots:{default:!0}}),s(A,k)},$$slots:{default:!0}}),s(v,_)},$$slots:{default:!0}}),c(2),l(Q),l(u),s(a,u)};M(P,a=>{i().automationNotifications&&a(H)})}var F=t(P,2),S=t(n(F),2),D=n(S),N=t(n(D),2);const b=C(()=>!!(i().jobEmailNotifications??!0));L(N,{get checked(){return o(b)},onCheckedChange:a=>{d.update(u=>({...u,jobEmailNotifications:a})),m()}}),l(D);var te=t(D,2),O=t(n(te),2);const q=C(()=>!!(i().jobBrowserNotifications??!0));L(O,{get checked(){return o(q)},onCheckedChange:a=>{d.update(u=>({...u,jobBrowserNotifications:a})),m()}}),l(te),l(S),l(F),l(R),s(me,p),Ee(),ge()}var kt=f('<div class="border-border flex flex-col border-b px-6 py-4"><h4 class="text-md font-normal">Marketing Communications</h4> <p class="text-muted-foreground text-sm">Configure marketing and promotional communications.</p></div> <div class="grid grid-cols-2 gap-4 p-4"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Marketing Emails</div> <div class="text-muted-foreground text-sm">Receive marketing and promotional emails</div></div> <!></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Product Updates</div> <div class="text-muted-foreground text-sm">Receive notifications about new features and product updates</div></div> <!></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Newsletter Subscription</div> <div class="text-muted-foreground text-sm">Receive our monthly newsletter with job search tips and industry insights</div></div> <!></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Event Invitations</div> <div class="text-muted-foreground text-sm">Receive invitations to webinars, career fairs, and other events</div></div> <!></div></div>',1);function Ct(me,Y){je(Y,!1);const[pe,ge]=De(),i=()=>Se(d,"$form",pe);let he=Fe(Y,"formData",8);const{form:d}=he();function m(){const $=document.getElementById("notification-form");$&&$.dispatchEvent(new Event("change",{bubbles:!0}))}Re();var p=kt(),R=t(x(p),2),V=n(R),ne=t(n(V),2);const G=C(()=>!!i().marketingEmails);L(ne,{get checked(){return o(G)},onCheckedChange:$=>{d.update(w=>({...w,marketingEmails:$})),m()}}),l(V);var Z=t(V,2),le=t(n(Z),2);const oe=C(()=>!!i().productUpdates);L(le,{get checked(){return o(oe)},onCheckedChange:$=>{d.update(w=>({...w,productUpdates:$})),m()}}),l(Z);var de=t(Z,2),j=t(n(de),2);const E=C(()=>!!i().newsletterSubscription);L(j,{get checked(){return o(E)},onCheckedChange:$=>{d.update(w=>({...w,newsletterSubscription:$})),m()}}),l(de);var K=t(de,2),fe=t(n(K),2);const ie=C(()=>!!i().eventInvitations);L(fe,{get checked(){return o(ie)},onCheckedChange:$=>{d.update(w=>({...w,eventInvitations:$})),m()}}),l(K),l(R),s(me,p),Ee(),ge()}var jt=f('<span class="text-destructive">❌ Not supported in this browser</span>'),Et=f('<span class="text-green-600">✅ Permission granted</span>'),St=f('<span class="text-destructive">❌ Permission blocked</span>'),Dt=f('<span class="text-yellow-600">⚠️ Permission not requested</span>'),Rt=f('<span class="text-green-600">✅ Active</span>'),Ft=f('<span class="text-muted-foreground">❌ None</span>'),qt=f('<div class="flex items-center gap-2"><span class="font-medium">Subscription:</span> <!></div>'),Bt=f('<div class="text-destructive bg-destructive/10 rounded p-2 text-xs"><strong>Notifications are blocked.</strong> To enable: <br/>1. Click the lock icon (🔒) in your address bar <br/>2. Change "Notifications" to "Allow" <br/>3. Refresh the page and try again</div>'),Tt=f('<div class="flex justify-start gap-2"><!></div>'),Mt=f('<div class="text-muted-foreground text-xs">Processing push notification settings...</div>'),At=f(`<div class="border-border flex flex-col border-b px-6 py-4"><h4 class="text-md font-normal">Platform Notifications</h4> <p class="text-muted-foreground text-sm">Configure how you receive notifications on the platform.</p></div> <div class="grid grid-cols-2 gap-4 p-4"><div class="space-y-2"><div class="flex items-center justify-between"><div class="font-medium">Browser Notifications</div> <!></div> <div class="text-muted-foreground text-sm">Receive notifications in your browser when you're on the site</div></div> <div class="space-y-2"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Push Notifications</div> <div class="text-muted-foreground text-sm">Receive push notifications for important updates</div> <div class="space-y-1 text-xs"><div class="flex items-center gap-2"><span class="font-medium">Status:</span> <!></div> <!></div> <!></div> <!></div> <!> <div class="flex flex-wrap gap-2"><!> <!> <!></div> <!></div></div>`,1);function Jt(me,Y){je(Y,!1);const[pe,ge]=De(),i=()=>Se(d,"$form",pe);let he=Fe(Y,"formData",8);const{form:d}=he();let m=qe(!1),p=qe({supported:!1,permission:"default",hasSubscription:!1,serviceWorkerRegistered:!1});function R(){const e=document.getElementById("notification-form");e&&e.dispatchEvent(new Event("change",{bubbles:!0}))}tt(async()=>{ve(p,await getPushNotificationStatus()),o(p).permission==="granted"&&o(p).hasSubscription?i().pushNotifications||d.update(e=>({...e,pushNotifications:!0})):i().pushNotifications&&d.update(e=>({...e,pushNotifications:!1}))});async function V(e){if(console.log("🔄 Push notification toggle clicked:",e),o(m)){console.log("⏳ Already loading, ignoring click");return}ve(m,!0);try{if(e){console.log("🔔 Enabling push notifications...");const r=await requestPushNotificationPermission();console.log("🔔 Permission request result:",r),r.success?(console.log("✅ Permission granted, updating form..."),d.update(v=>({...v,pushNotifications:!0})),R(),T.success("Push notifications enabled successfully!"),ve(p,await getPushNotificationStatus())):(console.log("❌ Permission failed, keeping switch off"),d.update(v=>({...v,pushNotifications:!1})),T.error(r.error||"Failed to enable push notifications"))}else{console.log("🔕 Disabling push notifications...");const r=await unregisterPushNotifications();r.success?(d.update(v=>({...v,pushNotifications:!1})),R(),T.success("Push notifications disabled successfully"),ve(p,await getPushNotificationStatus())):(d.update(v=>({...v,pushNotifications:!0})),T.error(r.error||"Failed to disable push notifications"))}}catch(r){console.error("❌ Error handling push notification toggle:",r),T.error("An unexpected error occurred"),d.update(v=>({...v,pushNotifications:!e}))}finally{ve(m,!1)}}async function ne(){try{const e=await testRequestPermission();T.success(`Permission result: ${e}`),ve(p,await getPushNotificationStatus())}catch(e){console.error("Error testing permission request:",e),T.error("Error testing permission request")}}async function G(){try{const e=await forceRequestPermission();T.success(`Permission result: ${e}`),ve(p,await getPushNotificationStatus())}catch(e){console.error("Error forcing permission request:",e),T.error("Error requesting permission")}}async function Z(){try{const e=await resetPushNotifications();e.success?(T.success("Push notifications reset successfully! You can now enable them again."),d.update(r=>({...r,pushNotifications:!1})),ve(p,await getPushNotificationStatus()),R()):T.error(e.error||"Failed to reset push notifications")}catch(e){console.error("Error resetting push notifications:",e),T.error("Error resetting push notifications")}}async function le(){try{const r=await(await fetch("/api/push/test",{method:"POST",headers:{"Content-Type":"application/json"}})).json();r.success?T.success("Test push notification sent! Check your browser notifications."):T.error(r.message||"Failed to send test notification")}catch(e){console.error("Error testing push notification:",e),T.error("Error testing push notification. Please try again.")}}Re();var oe=At(),de=t(x(oe),2),j=n(de),E=n(j),K=t(n(E),2);const fe=C(()=>!!i().browserNotifications);L(K,{get checked(){return o(fe)},onCheckedChange:e=>{d.update(r=>({...r,browserNotifications:e})),R()}}),l(E),c(2),l(j);var ie=t(j,2),$=n(ie),w=n($),ae=t(n(w),4),z=n(ae),ee=t(n(z),2);{var ce=e=>{var r=jt();s(e,r)},g=(e,r)=>{{var v=_=>{var y=Et();s(_,y)},B=(_,y)=>{{var se=re=>{var k=St();s(re,k)},A=re=>{var k=Dt();s(re,k)};M(_,re=>{o(p).permission==="denied"?re(se):re(A,!1)},y)}};M(e,_=>{o(p).permission==="granted"?_(v):_(B,!1)},r)}};M(ee,e=>{o(p).supported?e(g,!1):e(ce)})}l(z);var U=t(z,2);{var W=e=>{var r=qt(),v=t(n(r),2);{var B=y=>{var se=Rt();s(y,se)},_=y=>{var se=Ft();s(y,se)};M(v,y=>{o(p).hasSubscription?y(B):y(_,!1)})}l(r),s(e,r)};M(U,e=>{o(p).supported&&e(W)})}l(ae);var ue=t(ae,2);{var P=e=>{var r=Bt();s(e,r)};M(ue,e=>{o(p).permission==="denied"&&e(P)})}l(w);var H=t(w,2);const F=C(()=>!!i().pushNotifications&&o(p).permission==="granted"&&o(p).hasSubscription),S=C(()=>o(m)||!o(p).supported||o(p).permission==="denied");L(H,{get checked(){return o(F)},get disabled(){return o(S)},onCheckedChange:V}),l($);var D=t($,2);{var N=e=>{var r=Tt(),v=n(r);Pe(v,{type:"button",variant:"outline",size:"sm",onclick:le,get disabled(){return o(m)},children:(B,_)=>{c();var y=h("Test Push Notification");s(B,y)},$$slots:{default:!0}}),l(r),s(e,r)};M(D,e=>{i().pushNotifications&&o(p).hasSubscription&&e(N)})}var b=t(D,2),te=n(b);Pe(te,{type:"button",variant:"secondary",size:"sm",onclick:ne,get disabled(){return o(m)},children:(e,r)=>{c();var v=h("🔧 Test Permission");s(e,v)},$$slots:{default:!0}});var O=t(te,2);Pe(O,{type:"button",variant:"secondary",size:"sm",onclick:G,get disabled(){return o(m)},children:(e,r)=>{c();var v=h("🔔 Force Permission Dialog");s(e,v)},$$slots:{default:!0}});var q=t(O,2);{var a=e=>{Pe(e,{type:"button",variant:"destructive",size:"sm",onclick:Z,get disabled(){return o(m)},children:(r,v)=>{c();var B=h("🔄 Reset & Clear All");s(r,B)},$$slots:{default:!0}})};M(q,e=>{(o(p).hasSubscription||o(p).permission==="granted")&&e(a)})}l(b);var u=t(b,2);{var Q=e=>{var r=Mt();s(e,r)};M(u,e=>{o(m)&&e(Q)})}l(ie),l(de),s(me,oe),Ee(),ge()}var It=f('<div class="flex items-center gap-2"><!> <span> </span></div>'),Ut=f("<!> <!>",1),Wt=f("<!> <!>",1),Ht=f("<!> <!>",1),Vt=f("<!> <!>",1),zt=f(`<!> <div class="flex flex-col justify-between p-6"><div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between"><div><div class="flex items-center gap-2"><h2 class="text-lg font-semibold">Notification Settings</h2></div> <p class="text-muted-foreground text-sm">Manage your notification preferences for emails, job alerts, marketing communications, and
        platform notifications.</p></div></div></div> <div class="grid grid-cols-1 gap-6"><div><form id="notification-form" method="POST" class="space-y-8"><!> <button id="submit-button" type="submit" class="hidden" aria-label="Save settings"></button></form></div> <!></div> <!>`,1);function $s(me,Y){je(Y,!1);const[pe,ge]=De(),i=()=>Se(oe,"$submitting",pe);let he=Fe(Y,"data",8);const d=[{id:"email",label:"Email",icon:st},{id:"jobs",label:"Job Alerts",icon:ot},{id:"marketing",label:"Marketing",icon:it},{id:"platform",label:"Platform",icon:at}];let m=qe("email"),p=null,R=!1,V=null,ne=qe(!1);const G=He(he().form,{dataType:"json",validationMethod:"auto",taintedMessage:!1,onUpdated:({form:g})=>{g.valid&&(E("saved"),T.success("Notification settings auto-saved successfully"))},onError:()=>{E("error"),T.error("Failed to update notification settings")}}),{form:Z,enhance:le,submitting:oe,delayed:de}=G;function j(){console.log("Form changed, triggering auto-save"),E("saving"),clearTimeout(p),p=setTimeout(async()=>{if(!i()&&!R){R=!0,console.log("Auto-saving form...");try{const g=document.getElementById("notification-form");g?(g.requestSubmit(),console.log("Form auto-saved via form element")):console.error("Form element not found")}catch(g){console.error("Error auto-saving form:",g),E("error")}setTimeout(()=>{R=!1},1e3)}},1e3)}function E(g,U=3e3){clearTimeout(V),g==="error"&&(V=setTimeout(()=>{},U))}function K(){const g={emailNotifications:!0,emailDigest:"daily",emailFormat:"html",jobMatchNotifications:!0,jobMatchFrequency:"daily",applicationStatusNotifications:!0,newJobsNotifications:!0,newJobsFrequency:"daily",interviewReminders:!0,savedJobsUpdates:!0,jobEmailNotifications:!0,jobBrowserNotifications:!0,jobMobileNotifications:!1,marketingEmails:!0,productUpdates:!0,newsletterSubscription:!1,eventInvitations:!1,browserNotifications:!0,desktopNotifications:!1,mobileNotifications:!1,pushNotifications:!0};Z.update(()=>g),T.success("Notification settings reset to defaults"),ve(ne,!1);const U=document.getElementById("notification-form");U&&U.dispatchEvent(new Event("change",{bubbles:!0}))}Re();var fe=zt(),ie=x(fe);et(ie,{title:"Notification Settings - Hirli",description:"Manage your notification preferences for emails, job alerts, marketing communications, and platform notifications.",keywords:"notification settings, email preferences, job alerts, marketing preferences, push notifications",url:"https://hirli.com/dashboard/settings/notifications"});var $=t(ie,4),w=n($),ae=n(w),z=n(ae);Ve(z,{get value(){return o(m)},onValueChange:g=>ve(m,g),children:(g,U)=>{var W=Ut(),ue=x(W);ze(ue,{class:"w-full",children:(H,F)=>{var S=Be(),D=x(S);Te(D,1,()=>d,Me,(N,b)=>{rt(N,{get value(){return o(b).id},class:"flex-1",children:(te,O)=>{var q=It(),a=n(q);Je(a,()=>o(b).icon,(e,r)=>{r(e,{class:"h-4 w-4"})});var u=t(a,2),Q=n(u,!0);l(u),l(q),ye(()=>xe(Q,o(b).label)),s(te,q)},$$slots:{default:!0}})}),s(H,S)},$$slots:{default:!0}});var P=t(ue,2);Te(P,1,()=>d,Me,(H,F)=>{nt(H,{get value(){return o(F).id},children:(S,D)=>{var N=Be(),b=x(N);{var te=q=>{mt(q,{get formData(){return G}})},O=(q,a)=>{{var u=e=>{Nt(e,{get formData(){return G}})},Q=(e,r)=>{{var v=_=>{Ct(_,{get formData(){return G}})},B=(_,y)=>{{var se=A=>{Jt(A,{get formData(){return G}})};M(_,A=>{o(F).id==="platform"&&A(se)},y)}};M(e,_=>{o(F).id==="marketing"?_(v):_(B,!1)},r)}};M(q,e=>{o(F).id==="jobs"?e(u):e(Q,!1)},a)}};M(b,q=>{o(F).id==="email"?q(te):q(O,!1)})}s(S,N)},$$slots:{default:!0}})}),s(g,W)},$$slots:{default:!0}}),c(2),l(ae),Ie(ae,g=>le==null?void 0:le(g)),Ae(()=>Ue("change",ae,j)),l(w);var ee=t(w,2);M(ee,g=>{}),l($);var ce=t($,2);Oe(ce,{get open(){return o(ne)},onOpenChange:g=>ve(ne,g),children:(g,U)=>{Le(g,{children:(W,ue)=>{var P=Vt(),H=x(P);Ye(H,{children:(S,D)=>{var N=Wt(),b=x(N);Ge(b,{children:(O,q)=>{c();var a=h("Reset Notification Settings");s(O,a)},$$slots:{default:!0}});var te=t(b,2);Ke(te,{children:(O,q)=>{c();var a=h(`This will reset all notification settings to their default values. This action cannot be
        undone.`);s(O,a)},$$slots:{default:!0}}),s(S,N)},$$slots:{default:!0}});var F=t(H,2);Qe(F,{children:(S,D)=>{var N=Ht(),b=x(N);Xe(b,{children:(O,q)=>{c();var a=h("Cancel");s(O,a)},$$slots:{default:!0}});var te=t(b,2);Ze(te,{onclick:K,children:(O,q)=>{c();var a=h("Reset");s(O,a)},$$slots:{default:!0}}),s(S,N)},$$slots:{default:!0}}),s(W,P)},$$slots:{default:!0}})},$$slots:{default:!0}}),s(me,fe),Ee(),ge()}export{$s as component};
