import{c as n,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as c,s as m}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(o,t){const r=c(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"}],["path",{d:"M12 8v4"}],["path",{d:"M12 16h.01"}]];f(o,m({name:"shield-alert"},()=>r,{get iconNode(){return s},children:(e,$)=>{var a=n(),l=i(a);d(l,t,"default",{},null),p(e,a)},$$slots:{default:!0}}))}export{y as S};
