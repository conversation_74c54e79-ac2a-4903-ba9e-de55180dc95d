import{c as i,a as d}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as h,s as p}from"./Btcx8l8F.js";import{I as $}from"./D4f2twK-.js";function y(r,t){const o=h(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16"}],["path",{d:"m19 9-5 5-4-4-3 3"}]];$(r,p({name:"chart-line"},()=>o,{get iconNode(){return a},children:(s,m)=>{var e=i(),n=l(e);c(n,t,"default",{},null),d(s,e)},$$slots:{default:!0}}))}function N(r,t){const o=h(t,["children","$$slots","$$events","$$legacy"]),a=[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3"}],["path",{d:"M12 12V8"}]];$(r,p({name:"network"},()=>o,{get iconNode(){return a},children:(s,m)=>{var e=i(),n=l(e);c(n,t,"default",{},null),d(s,e)},$$slots:{default:!0}}))}export{y as C,N};
