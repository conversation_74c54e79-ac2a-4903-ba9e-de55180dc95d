var j=Object.defineProperty;var S=e=>{throw TypeError(e)};var q=(e,t,r)=>t in e?j(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var T=(e,t,r)=>q(e,typeof t!="symbol"?t+"":t,r),z=(e,t,r)=>t.has(e)||S("Cannot "+r);var P=(e,t,r)=>(z(e,t,"read from private field"),r?r.call(e):t.get(e)),I=(e,t,r)=>t.has(e)?S("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r);import{c as w,a as l,f as k}from"./BasJTneF.js";import{x as y,g as p,d as C,p as A,f as b,a as B,c as E,au as F,r as G,t as H}from"./CGmarHxI.js";import{c as J}from"./BvdI7LR8.js";import{e as K,b as L}from"./B-Xjo-Yt.js";import{p as o,r as D,s as M}from"./Btcx8l8F.js";import{s as O,c as N}from"./ncUU1dSD.js";import{i as Q}from"./u21ee2wt.js";import{u as U,b as n,m as V}from"./BfX7a-t9.js";import{u as W}from"./CnMg5bH0.js";const Y="data-progress-root";var u;class Z{constructor(t){T(this,"opts");I(this,u,y(()=>({role:"progressbar",value:this.opts.value.current,"aria-valuemin":this.opts.min.current,"aria-valuemax":this.opts.max.current,"aria-valuenow":this.opts.value.current===null?void 0:this.opts.value.current,"data-value":this.opts.value.current===null?void 0:this.opts.value.current,"data-state":$(this.opts.value.current,this.opts.max.current),"data-max":this.opts.max.current,"data-min":this.opts.min.current,"data-indeterminate":this.opts.value.current===null?"":void 0,[Y]:""})));this.opts=t,U(t)}get props(){return p(P(this,u))}set props(t){C(P(this,u),t)}}u=new WeakMap;function $(e,t){return e===null?"indeterminate":e===t?"loaded":"loading"}function tt(e){return new Z(e)}var et=k("<div><!></div>");function rt(e,t){A(t,!0);let r=o(t,"value",3,0),m=o(t,"max",3,100),h=o(t,"min",3,0),d=o(t,"id",19,W),f=o(t,"ref",15,null),g=D(t,["$$slots","$$events","$$legacy","child","children","value","max","min","id","ref"]);const x=tt({value:n.with(()=>r()),max:n.with(()=>m()),min:n.with(()=>h()),id:n.with(()=>d()),ref:n.with(()=>f(),a=>f(a))}),c=y(()=>V(g,x.props));var i=w(),R=b(i);{var v=a=>{var s=w(),_=b(s);O(_,()=>t.child,()=>({props:p(c)})),l(a,s)},X=a=>{var s=et();K(s,()=>({...p(c)}));var _=E(s);O(_,()=>t.children??F),G(s),l(a,s)};Q(R,a=>{t.child?a(v):a(X,!1)})}l(e,i),B()}var at=k('<div data-slot="progress-indicator" class="bg-primary h-full w-full flex-1 transition-all"></div>');function vt(e,t){A(t,!0);let r=o(t,"ref",15,null),m=o(t,"max",3,100),h=D(t,["$$slots","$$events","$$legacy","ref","class","max","value"]);var d=w(),f=b(d);const g=y(()=>N("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t.class));J(f,()=>rt,(x,c)=>{c(x,M({"data-slot":"progress",get class(){return p(g)},get value(){return t.value},get max(){return m()}},()=>h,{get ref(){return r()},set ref(i){r(i)},children:(i,R)=>{var v=at();H(()=>L(v,`transform: translateX(-${100-100*(t.value??0)/(m()??1)}%)`)),l(i,v)},$$slots:{default:!0}}))}),l(e,d),B()}export{vt as P};
