const e={"action.add-document":"Add document","action.archive":"Archive release","action.archive.tooltip":"Unschedule this release to archive it","action.archived":"Archived","action.create-revert-release":"Stage in new release","action.delete-release":"Delete release","action.duplicate-release":"Duplicate release","action.edit":"Edit release","action.open":"Active","action.schedule":"Schedule release...","action.unpublish":"Unpublish","action.unpublish-doc-actions":"Unpublish when releasing","action.unschedule":"Unschedule release","action.publish-all-documents":"Run release","action.review":"Review changes","action.revert":"Revert release","actions.summary":"Summary","action.immediate-revert-release":"Revert now","action.unarchive":"Unarchive release","activity.event.add-document":"added a document version","activity.event.archive":"archived the <strong>{{releaseTitle}}</strong> release","activity.event.create":"created the <strong>{{releaseTitle}}</strong> release <ScheduleTarget>targeting </ScheduleTarget>","activity.event.discard-document":"discarded a document version","activity.event.edit":"set release time to <ScheduleTarget></ScheduleTarget>","activity.event.edit-time-asap":"immediately","activity.event.edit-time-undecided":"never","activity.event.publish":"published the <strong>{{releaseTitle}}</strong> release","activity.event.schedule":"marked as scheduled","activity.event.unarchive":"unarchived the <strong>{{releaseTitle}}</strong> release","activity.event.unschedule":"marked as unscheduled","activity.panel.loading":"Loading release activity","activity.panel.error":"An error occurred getting the release activity","activity.panel.title":"Activity","archive-dialog.confirm-archive-header":"Are you sure you want to archive this release?","archive-dialog.confirm-archive-title":"Are you sure you want to archive the <strong>'{{title}}'</strong> release?","archive-dialog.confirm-archive-description_one":"This will archive 1 document version.","archive-dialog.confirm-archive-description_other":"This will archive {{count}} document versions.","archive-dialog.confirm-archive-button":"Yes, archive release","archive-info.title":"This release is archived","archive-info.description":"Your plan supports a {{retentionDays}}-day retention period. After this period this release will be removed.","changes-published-docs.title":"Changes to published documents",created:"Created <RelativeTime/>","copy-suffix":"Copy","dashboard.details.published-asap":"Published","dashboard.details.published-on":"Published on {{date}}","dashboard.details.pin-release":"Pin release","dashboard.details.unpin-release":"Unpin release","dashboard.details.activity":"Activity","delete-dialog.confirm-delete.header":"Are you sure you want to delete this release?","delete-dialog.confirm-delete-description_one":"This will delete 1 document version.","delete-dialog.confirm-delete-description_other":"This will delete {{count}} document versions.","delete-dialog.confirm-delete-button":"Yes, delete release","diff.no-changes":"No changes","diff.list-empty":"Changes list is empty, see document","discard-version-dialog.description-draft":"This will permanently remove all changes made to this document. This action cannot be undone.","discard-version-dialog.description-release":"This will permanently remove all changes made to this document within the '<strong>{{releaseTitle}}</strong>' release. This action cannot be undone.","discard-version-dialog.header-draft":"Discard draft?","discard-version-dialog.header-release":"Remove document from the '<strong>{{releaseTitle}}</strong>' release?","discard-version-dialog.title-draft":"Discard draft","discard-version-dialog.title-release":"Remove from release","document-validation.error_other":"{{count}} validation errors","document-validation.error_one":"{{count}} validation error","deleted-release":"The '<strong>{{title}}</strong>' release has been deleted","duplicate-dialog.confirm-duplicate-header":"Are you sure you want to duplicate this release?","duplicate-dialog.confirm-duplicate-description_one":"This will duplicate the release and the 1 document version.","duplicate-dialog.confirm-duplicate-description_other":"This will duplicate the release and the {{count}} document versions.","duplicate-dialog.confirm-duplicate-button":"Yes, duplicate release","error-details-title":"Error details","failed-edit-title":"Failed to save changes","failed-publish-title":"Failed to publish","failed-schedule-title":"Failed to schedule","footer.status.archived":"Archived","footer.status.created":"Created","footer.status.edited":"Edited","footer.status.published":"Published","footer.status.unarchived":"Unarchived","loading-release":"Loading release","loading-release-documents":"Loading documents","loading-release-documents.error.title":"Something went wrong","loading-release-documents.error.description":"We're unable to load the documents for this release. Please try again later.","menu.label":"Release menu","menu.tooltip":"Actions","menu.group.when-releasing":"When releasing","no-archived-release":"No archived releases","no-releases":"No Releases","not-found":"Release not found: {{releaseId}}","overview.action.documentation":"Documentation","overview.calendar.tooltip":"View calendar","overview.description":"Releases are collections of document changes which can be managed, scheduled, and rolled back together.","overview.search-releases-placeholder":"Search releases","overview.title":"Releases","permissions.error.discard-version":"You do not have permission to discard this version","permissions.error.unpublish":"You do not have permission to unpublish this document","permission-missing-title":"Limited access","permission-missing-description":"Your role currently limits what you can see in this release. You may not publish nor schedule this release.","permissions.error.archive":"You do not have permission to archive this release","permissions.error.delete":"You do not have permission to delete this release","permissions.error.duplicate":"You do not have permission to duplicate this release","permissions.error.unarchive":"You do not have permission to unarchive this release","presence.tooltip.one":'{{displayName}} is editing this document in the "{{releaseTitle}}" release right now',"presence.tooltip.other":"{{count}} people are editing this document right now","publish-action.validation.no-documents":"There are no documents to publish","publish-dialog.confirm-publish.title":"Are you sure you want to publish the release and all document versions?","publish-dialog.confirm-publish-description_one":"The '<strong>{{title}}</strong>' release and its document will be published.","publish-dialog.confirm-publish-description_other":"The '<strong>{{title}}</strong>' release and its {{releaseDocumentsLength}} documents will be published.","publish-dialog.validation.no-permission":"You do not have permission to publish","publish-dialog.validation.loading":"Validating documents...","publish-dialog.validation.error":"Some documents have validation errors","publish-info.title":"This release is published","release-placeholder.title":"Untitled","review.description":"Add documents to this release to review changes","review.edited":"Edited <RelativeTime/>","revert-dialog.confirm-revert-description_one":"This will revert {{releaseDocumentsLength}} document version.","revert-dialog.confirm-revert-description_other":"This will revert {{releaseDocumentsLength}} document versions.","revert-dialog.confirm-revert.title":"Are you sure you want to revert the '{{title}}' release?","revert-dialog.confirm-revert.stage-revert-checkbox-label":"Stage revert actions in a new release","revert-dialog.confirm-revert.warning-card":"Changes were made to documents in this release after they were published. Reverting will overwrite these changes.","revert-release.title":'Reverting "{{title}}"',"revert-release.description":'Revert changes to document versions in "{{title}}".',"schedule-button.tooltip":"Are you sure you want to unschedule the release?","schedule-action.validation.no-documents":"There are no documents to schedule","schedule-button-tooltip.validation.no-permission":"You do not have permission to schedule","schedule-button-tooltip.validation.loading":"Validating documents...","schedule-button-tooltip.validation.error":"Some documents have validation errors","schedule-button-tooltip.already-scheduled":"This release is already scheduled","schedule-dialog.confirm-title":"Schedule the release","schedule-dialog.confirm-description_one":"The '<strong>{{title}}</strong>' release and its document will be published on the selected date.","schedule-dialog.confirm-description_other":"The <strong>{{title}}</strong> release and its {{count}} document versions will be scheduled.","schedule-dialog.confirm-button":"Yes, schedule","schedule-dialog.select-publish-date-label":"Schedule on","unschedule-dialog.confirm-title":"Are you sure you want to unschedule the release?","unschedule-dialog.confirm-description":"The release will no longer be published on the scheduled date","schedule-dialog.publish-date-in-past-warning":"Schedule this release for a future time and date.","search-documents-placeholder":"Search documents","summary.created":"Created <RelativeTime/>","summary.published":"Published <RelativeTime/>","summary.not-published":"Not published","summary.no-documents":"No documents","summary.document-count_one":"{{count}} document","summary.document-count_other":"{{count}} documents","table-body.action.add":"Add","table-body.action.change":"Change","table-body.action.unpublish":"Unpublish","table-header.archivedAt":"Archived","table-header.contributors":"Contributors","table-header.type":"Type","table-header.title":"Release","table-header.action":"Action","table-header.documents":"Documents","table-header.edited":"Edited","table-header.publishedAt":"Published","table-header.time":"Time","toast.archive.error":"Failed to archive '<strong>{{title}}</strong>': {{error}}","toast.create-version.error":"Failed to add document to release: {{error}}","toast.delete.error":"Failed to delete '<strong>{{title}}</strong>': {{error}}","toast.delete.success":"The '<strong>{{title}}</strong>' release was successfully deleted","toast.duplicate.error":"Failed to duplicate '<strong>{{title}}</strong>': {{error}}","toast.duplicate.success":"The '<strong>{{title}}</strong>' release was duplicated. <Link/>","toast.duplicate.success-link":"View duplicated release","toast.publish.error":"Failed to publish '<strong>{{title}}</strong>': {{error}}","toast.schedule.error":"Failed to schedule '<strong>{{title}}</strong>': {{error}}","toast.schedule.success":"The '<strong>{{title}}</strong>' release was scheduled.","toast.unschedule.error":"Failed to unscheduled '<strong>{{title}}</strong>': {{error}}","toast.unarchive.error":"Failed to unarchive '<strong>{{title}}</strong>': {{error}}","type-picker.tooltip.scheduled":"The release is scheduled, unschedule it to change type","toast.revert.error":"Failed to revert release: {{error}}","toast.immediate-revert.success":"The '{{title}}' release was successfully reverted","toast.revert-stage.success":"Revert release for '{{title}}' was successfully created. <Link/>","toast.revert-stage.success-link":"View revert release","unpublish.already-unpublished":"This document is already unpublished.","unpublish.no-published-version":"There is no published version of this document.","unpublish-dialog.header":"Are you sure you want to unpublish this document when releasing?","unpublish-dialog.action.cancel":"Cancel","unpublish-dialog.action.unpublish":"Yes, unpublish when releasing","unpublish-dialog.description.to-draft":"This will unpublish the document as part of the <Label>{{title}}</Label> release, and create a draft if no draft exists at the time of release.","unpublish-dialog.description.lost-changes":"Any changes made to this document version will be lost."};export{e as default};
