import{c as d,a as e}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as n}from"./BBa424ah.js";import{l,s as m}from"./Btcx8l8F.js";import{I as c}from"./D4f2twK-.js";function y(o,t){const r=l(t,["children","$$slots","$$events","$$legacy"]),h=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2"}],["path",{d:"M9 22v-4h6v4"}],["path",{d:"M8 6h.01"}],["path",{d:"M16 6h.01"}],["path",{d:"M12 6h.01"}],["path",{d:"M12 10h.01"}],["path",{d:"M12 14h.01"}],["path",{d:"M16 10h.01"}],["path",{d:"M16 14h.01"}],["path",{d:"M8 10h.01"}],["path",{d:"M8 14h.01"}]];c(o,m({name:"building"},()=>r,{get iconNode(){return h},children:(s,f)=>{var a=d(),p=i(a);n(p,t,"default",{},null),e(s,a)},$$slots:{default:!0}}))}export{y as B};
