import{c as p,a as f}from"./BasJTneF.js";import{p as g,f as l,a as u,g as m,x as b}from"./CGmarHxI.js";import{c as v}from"./BvdI7LR8.js";import{p as x,s as _,r as h}from"./Btcx8l8F.js";import{c as k}from"./ncUU1dSD.js";import{a as y}from"./I7hvcB12.js";function q(s,e){g(e,!0);let r=x(e,"ref",15,null),a=h(e,["$$slots","$$events","$$legacy","ref","class"]);var t=p(),o=l(t);const i=b(()=>k("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/50 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 whitespace-nowrap rounded-md border border-transparent px-2 py-1 text-sm font-medium transition-[color,box-shadow] focus-visible:outline-1 focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",e.class));v(o,()=>y,(n,d)=>{d(n,_({"data-slot":"tabs-trigger",get class(){return m(i)}},()=>a,{get ref(){return r()},set ref(c){r(c)}}))}),f(s,t),u()}export{q as T};
