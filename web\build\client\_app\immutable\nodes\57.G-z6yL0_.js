import{f,c as q,a as r,t as g}from"../chunks/BasJTneF.js";import{o as ir}from"../chunks/nZgk9enP.js";import{p as tr,c as l,r as i,s as a,f as v,g as e,x as z,n as m,t as Y,a as ar,k as se,v as er,d as x}from"../chunks/CGmarHxI.js";import{s as O}from"../chunks/CIt1g2O9.js";import{i as de}from"../chunks/u21ee2wt.js";import{e as rr,i as dr}from"../chunks/C3w0v0gR.js";import{c as n}from"../chunks/BvdI7LR8.js";import{r as cr,f as vr}from"../chunks/B-Xjo-Yt.js";import{g as ur}from"../chunks/CmxjS0TN.js";import{B as Ae}from"../chunks/B1K98fMG.js";import{a as fr,b as We,c as Te,d as pr,e as $e,T as _r}from"../chunks/LESefvxV.js";import{B as Ve}from"../chunks/DaBofrVv.js";import{S as X}from"../chunks/BPvdPoic.js";import"../chunks/CgXBgsce.js";import{t as Ue}from"../chunks/DjPYYl4Z.js";import{S as gr}from"../chunks/C6g8ubaU.js";import{p as qe}from"../chunks/Btcx8l8F.js";import{S as mr,a as hr,b as ke,R as $r}from"../chunks/CGK0g3x_.js";import{S as xr}from"../chunks/B2lQHLf_.js";import{C as Pr}from"../chunks/BBNNmnYR.js";import{C as br}from"../chunks/DkmCSZhC.js";import{S as wr}from"../chunks/yW0TxTga.js";import{C as Cr}from"../chunks/CKg8MWp_.js";var yr=f("<!> <!> <!> <!> <!>",1),Sr=f("<!> <!>",1),Lr=f('<span class="sr-only">Go to first page</span> <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.85355 3.85355C7.04882 3.65829 7.04882 3.34171 6.85355 3.14645C6.65829 2.95118 6.34171 2.95118 6.14645 3.14645L2.14645 7.14645C1.95118 7.34171 1.95118 7.65829 2.14645 7.85355L6.14645 11.8536C6.34171 12.0488 6.65829 12.0488 6.85355 11.8536C7.04882 11.6583 7.04882 11.3417 6.85355 11.1464L3.20711 7.5L6.85355 3.85355ZM12.8536 3.85355C13.0488 3.65829 13.0488 3.34171 12.8536 3.14645C12.6583 2.95118 12.3417 2.95118 12.1464 3.14645L8.14645 7.14645C7.95118 7.34171 7.95118 7.65829 8.14645 7.85355L12.1464 11.8536C12.3417 12.0488 12.6583 12.0488 12.8536 11.8536C13.0488 11.6583 13.0488 11.3417 12.8536 11.1464L9.20711 7.5L12.8536 3.85355Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>',1),Nr=f('<span class="sr-only">Go to previous page</span> <!>',1),Tr=f('<span class="sr-only">Go to next page</span> <!>',1),Ur=f('<span class="sr-only">Go to last page</span> <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.14645 11.1464C1.95118 11.3417 1.95118 11.6583 2.14645 11.8536C2.34171 12.0488 2.65829 12.0488 2.85355 11.8536L6.85355 7.85355C7.04882 7.65829 7.04882 7.34171 6.85355 7.14645L2.85355 3.14645C2.65829 2.95118 2.34171 2.95118 2.14645 3.14645C1.95118 3.34171 1.95118 3.65829 2.14645 3.85355L5.79289 7.5L2.14645 11.1464ZM8.14645 11.1464C7.95118 11.3417 7.95118 11.6583 8.14645 11.8536C8.34171 12.0488 8.65829 12.0488 8.85355 11.8536L12.8536 7.85355C13.0488 7.65829 13.0488 7.34171 12.8536 7.14645L8.85355 3.14645C8.65829 2.95118 8.34171 2.95118 8.14645 3.14645C7.95118 3.34171 7.95118 3.65829 8.14645 3.85355L11.7929 7.5L8.14645 11.1464Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>',1),kr=f('<div class="flex items-center justify-between px-2"><div class="text-muted-foreground flex-1 text-sm"> </div> <div class="flex items-center space-x-6 lg:space-x-8"><div class="flex items-center space-x-2"><p class="text-sm font-medium">Rows per page</p> <!></div> <div class="flex w-[100px] items-center justify-center text-sm font-medium"> </div> <div class="flex items-center space-x-2"><!> <!> <!> <!></div></div></div>');function Ar(xe,P){tr(P,!0);const M=qe(P,"currentPage",3,1),ce=qe(P,"pageSize",3,10),Pe=qe(P,"totalItems",3,0),U=qe(P,"totalPages",3,1);function ve(){return U()<=0?1:Math.min(M(),U())}let D=z(ve),F=z(()=>e(D)>1&&U()>0),oe=z(()=>e(D)<U()&&U()>1);function ue(){e(F)&&P.onPageChange(1)}function Oe(){e(F)&&P.onPageChange(M()-1)}function Ee(){e(oe)&&P.onPageChange(M()+1)}function ze(){e(oe)&&P.onPageChange(U())}var be=kr(),we=l(be),Me=l(we);i(we);var De=a(we,2),fe=l(De),Fe=a(l(fe),2);const Ce=z(()=>ce().toString());n(Fe,()=>$r,(o,u)=>{u(o,{type:"single",get value(){return e(Ce)},onValueChange:c=>P.onPageSizeChange(Number(c)),children:(c,I)=>{var w=Sr(),ee=v(w);n(ee,()=>mr,(ge,k)=>{k(ge,{class:"h-8 w-[70px]",children:(G,me)=>{var A=q(),j=v(A);const Z=z(()=>ce().toString());n(j,()=>xr,(Q,C)=>{C(Q,{get placeholder(){return e(Z)}})}),r(G,A)},$$slots:{default:!0}})});var ne=a(ee,2);n(ne,()=>hr,(ge,k)=>{k(ge,{class:"min-w-[70px]",children:(G,me)=>{var A=yr(),j=v(A);n(j,()=>ke,($,y)=>{y($,{value:"5",children:(p,B)=>{m();var h=g("5");r(p,h)},$$slots:{default:!0}})});var Z=a(j,2);n(Z,()=>ke,($,y)=>{y($,{value:"10",children:(p,B)=>{m();var h=g("10");r(p,h)},$$slots:{default:!0}})});var Q=a(Z,2);n(Q,()=>ke,($,y)=>{y($,{value:"20",children:(p,B)=>{m();var h=g("20");r(p,h)},$$slots:{default:!0}})});var C=a(Q,2);n(C,()=>ke,($,y)=>{y($,{value:"30",children:(p,B)=>{m();var h=g("30");r(p,h)},$$slots:{default:!0}})});var re=a(C,2);n(re,()=>ke,($,y)=>{y($,{value:"50",children:(p,B)=>{m();var h=g("50");r(p,h)},$$slots:{default:!0}})}),r(G,A)},$$slots:{default:!0}})}),r(c,w)},$$slots:{default:!0}})}),i(fe);var pe=a(fe,2),Ie=l(pe);i(pe);var ye=a(pe,2),_e=l(ye);const je=z(()=>!e(F));Ae(_e,{variant:"outline",class:"hidden h-8 w-8 p-0 lg:flex",onclick:ue,get disabled(){return e(je)},children:(o,u)=>{var c=Lr();m(2),r(o,c)},$$slots:{default:!0}});var Be=a(_e,2);const Qe=z(()=>!e(F));Ae(Be,{variant:"outline",class:"h-8 w-8 p-0",onclick:Oe,get disabled(){return e(Qe)},children:(o,u)=>{var c=Nr(),I=a(v(c),2);Pr(I,{size:"15"}),r(o,c)},$$slots:{default:!0}});var Ge=a(Be,2);const He=z(()=>!e(oe));Ae(Ge,{variant:"outline",class:"h-8 w-8 p-0",onclick:Ee,get disabled(){return e(He)},children:(o,u)=>{var c=Tr(),I=a(v(c),2);br(I,{size:"15"}),r(o,c)},$$slots:{default:!0}});var s=a(Ge,2);const t=z(()=>!e(oe));Ae(s,{variant:"outline",class:"hidden h-8 w-8 p-0 lg:flex",onclick:ze,get disabled(){return e(t)},children:(o,u)=>{var c=Ur();m(2),r(o,c)},$$slots:{default:!0}}),i(ye),i(De),i(be),Y(o=>{O(Me,`${Pe()??""} total items`),O(Ie,`Page ${e(D)??""} of ${o??""}`)},[()=>Math.max(1,U())]),r(xe,be),ar()}var Er=(xe,P)=>P(xe),zr=f('<div class="border-destructive/20 bg-destructive/10 text-destructive mb-4 rounded-lg border p-4 text-sm"><div class="flex items-center"><!> <p> </p></div> <div class="mt-2"><!></div></div>'),Mr=f('<div class="flex items-center justify-between p-4"><div class="flex items-center space-x-4"><!> <div class="space-y-2"><!> <!></div></div> <div class="flex space-x-2"><!> <!> <!></div></div>'),Dr=f('<div class="space-y-4"><div class="flex items-center space-x-4 p-4"><!> <div class="space-y-2"><!> <!></div></div> <!></div>'),Fr=f("<!> <!> <!> <!> <!>",1),Ir=f('<div class="font-medium"> </div> <div class="text-muted-foreground text-sm"> </div>',1),jr=f("<!> <!>",1),Br=f('<span class="text-muted-foreground">N/A</span>'),Gr=f('<span class="text-muted-foreground">N/A</span>'),Zr=f("<!> <!> <!> <!> <!>",1),Rr=f("<!> <!>",1),Vr=f('<div class="p-4"><!> <div class="mt-4"><!></div></div>'),qr=f('<!> <div class="border-border flex items-center justify-between border-b px-4 py-2"><h2 class="text-lg font-semibold">User Subscriptions</h2> <div class="flex gap-2"><div class="relative"><!> <input type="search" placeholder="Search users..." class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-[250px] rounded-md border px-3 py-2 pl-8 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"/></div></div></div> <!> <!>',1);function _t(xe,P){tr(P,!0);let M=se(er([])),ce=se(er([])),Pe=se(!0),U=se(null),ve=se(""),D=se(1),F=se(10),oe=se(null);function ue(){if(!e(ve).trim())return[...e(M)];const s=e(ve).toLowerCase();return e(M).filter(t=>{var o,u;return t.email.toLowerCase().includes(s)||((o=t.name)==null?void 0:o.toLowerCase().includes(s))||((u=t.role)==null?void 0:u.toLowerCase().includes(s))})}function Oe(){const s=ue(),t=Math.ceil(s.length/e(F));e(D)>t&&t>0&&x(D,t,!0);const o=(e(D)-1)*e(F),u=o+e(F);return s.slice(o,u)}let Ee=z(Oe);async function ze(){try{x(Pe,!0),x(U,null);const s=await fetch("/api/admin/users");if(!s.ok)throw new Error("Failed to load users");const t=await s.json();console.log("Users data:",t),Array.isArray(t)?(t.length>0&&t[0].subscription&&console.log("First user subscription:",t[0].subscription),x(M,t,!0)):(console.error("Unexpected users data format:",t),x(M,[],!0)),await be(),e(M).length>0?Ue.success("Users loaded successfully",{description:`${e(M).length} users loaded`}):Ue.warning("No users found",{description:"No user data was returned from the API"})}catch(s){console.error("Error loading users:",s),x(U,s.message,!0),Ue.error("Failed to load users",{description:s.message})}finally{x(Pe,!1)}}async function be(){try{const s=await fetch("/api/admin/plans");if(!s.ok)throw new Error("Failed to load plans");const t=await s.json();console.log("Plans data:",t),Array.isArray(t)?x(ce,t,!0):(console.error("Unexpected plans data format:",t),x(ce,[],!0)),e(ce).length===0&&Ue.warning("No plans found",{description:"No plan data was returned from the API"})}catch(s){console.error("Error loading plans:",s),Ue.error("Failed to load plans",{description:s.message})}}function we(s){const t=s.target;clearTimeout(e(oe)),x(oe,setTimeout(()=>{x(ve,t.value,!0),x(D,1)},300),!0)}function Me(s){if(!s)return"N/A";try{const t=new Date(s);return isNaN(t.getTime())?(console.error("Invalid date:",s),"Invalid date"):t.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch(t){return console.error("Error formatting date:",t),"Error"}}function De(s){switch(s){case"active":return"success";case"trialing":return"warning";case"canceled":case"incomplete_expired":return"destructive";case"incomplete":case"past_due":return"destructive";case"paused":return"outline";default:return"secondary"}}ir(ze);var fe=qr(),Fe=v(fe);gr(Fe,{title:"User Subscriptions"});var Ce=a(Fe,2),pe=a(l(Ce),2),Ie=l(pe),ye=l(Ie);wr(ye,{class:"text-muted-foreground absolute left-2.5 top-2.5 h-4 w-4"});var _e=a(ye,2);cr(_e),_e.__input=[Er,we],i(Ie),i(pe),i(Ce);var je=a(Ce,2);{var Be=s=>{var t=zr(),o=l(t),u=l(o);Cr(u,{class:"mr-2 h-4 w-4"});var c=a(u,2),I=l(c);i(c),i(o);var w=a(o,2),ee=l(w);Ae(ee,{variant:"outline",size:"sm",onclick:ze,children:(ne,ge)=>{m();var k=g("Try Again");r(ne,k)},$$slots:{default:!0}}),i(w),i(t),Y(()=>O(I,`Error loading users: ${e(U)??""}`)),r(s,t)};de(je,s=>{e(U)&&s(Be)})}var Qe=a(je,2);{var Ge=s=>{var t=Dr(),o=l(t),u=l(o);X(u,{class:"h-12 w-12 rounded-full"});var c=a(u,2),I=l(c);X(I,{class:"h-4 w-[250px]"});var w=a(I,2);X(w,{class:"h-4 w-[200px]"}),i(c),i(o);var ee=a(o,2);rr(ee,16,()=>Array(5),dr,(ne,ge)=>{var k=Mr(),G=l(k),me=l(G);X(me,{class:"h-12 w-12 rounded-full"});var A=a(me,2),j=l(A);X(j,{class:"h-4 w-[250px]"});var Z=a(j,2);X(Z,{class:"h-4 w-[200px]"}),i(A),i(G);var Q=a(G,2),C=l(Q);X(C,{class:"h-8 w-8 rounded-full"});var re=a(C,2);X(re,{class:"h-8 w-8 rounded-full"});var $=a(re,2);X($,{class:"h-8 w-8 rounded-full"}),i(Q),i(k),r(ne,k)}),i(t),r(s,t)},He=s=>{var t=Vr(),o=l(t);n(o,()=>_r,(w,ee)=>{ee(w,{class:"border-border border",children:(ne,ge)=>{var k=Rr(),G=v(k);n(G,()=>fr,(A,j)=>{j(A,{children:(Z,Q)=>{var C=q(),re=v(C);n(re,()=>We,($,y)=>{y($,{children:(p,B)=>{var h=Fr(),te=v(h);n(te,()=>Te,(S,L)=>{L(S,{children:(E,H)=>{m();var N=g("User");r(E,N)},$$slots:{default:!0}})});var _=a(te,2);n(_,()=>Te,(S,L)=>{L(S,{children:(E,H)=>{m();var N=g("Plan");r(E,N)},$$slots:{default:!0}})});var le=a(_,2);n(le,()=>Te,(S,L)=>{L(S,{children:(E,H)=>{m();var N=g("Status");r(E,N)},$$slots:{default:!0}})});var Se=a(le,2);n(Se,()=>Te,(S,L)=>{L(S,{children:(E,H)=>{m();var N=g("Start Date");r(E,N)},$$slots:{default:!0}})});var he=a(Se,2);n(he,()=>Te,(S,L)=>{L(S,{children:(E,H)=>{m();var N=g("End Date");r(E,N)},$$slots:{default:!0}})}),r(p,h)},$$slots:{default:!0}})}),r(Z,C)},$$slots:{default:!0}})});var me=a(G,2);n(me,()=>pr,(A,j)=>{j(A,{children:(Z,Q)=>{var C=q(),re=v(C);{var $=p=>{var B=q(),h=v(B);n(h,()=>We,(te,_)=>{_(te,{children:(le,Se)=>{var he=q(),S=v(he);n(S,()=>$e,(L,E)=>{E(L,{class:"h-24 text-center",colspan:5,children:(H,N)=>{m();var Le=g();Y(Ze=>O(Le,Ze),[()=>ue().length===0?"No users found.":"No users match your search criteria."]),r(H,Le)},$$slots:{default:!0}})}),r(le,he)},$$slots:{default:!0}})}),r(p,B)},y=p=>{var B=q(),h=v(B);rr(h,17,()=>e(Ee),te=>te.id,(te,_)=>{var le=q(),Se=v(le);n(Se,()=>We,(he,S)=>{S(he,{children:(L,E)=>{var H=Zr(),N=v(H);n(N,()=>$e,(J,K)=>{K(J,{children:(W,Re)=>{var T=Ir(),R=v(T),V=l(R,!0);i(R);var ae=a(R,2),d=l(ae,!0);i(ae),Y(()=>{O(V,e(_).email),O(d,e(_).name||"No name")}),r(W,T)},$$slots:{default:!0}})});var Le=a(N,2);n(Le,()=>$e,(J,K)=>{K(J,{children:(W,Re)=>{Ve(W,{variant:"outline",children:(T,R)=>{m();var V=g();Y(()=>O(V,e(_).role||"free")),r(T,V)},$$slots:{default:!0}})},$$slots:{default:!0}})});var Ze=a(Le,2);n(Ze,()=>$e,(J,K)=>{K(J,{children:(W,Re)=>{var T=q(),R=v(T);{var V=d=>{var b=jr(),ie=v(b);const Je=z(()=>De(e(_).subscription.status));Ve(ie,{get variant(){return e(Je)},children:(Ne,Ye)=>{m();var Ke=g();Y(()=>O(Ke,e(_).subscription.status)),r(Ne,Ke)},$$slots:{default:!0}});var or=a(ie,2);{var nr=Ne=>{Ve(Ne,{variant:"outline",class:"ml-1",children:(Ye,Ke)=>{m();var lr=g("Canceling");r(Ye,lr)},$$slots:{default:!0}})};de(or,Ne=>{e(_).subscription.cancelAtPeriodEnd&&Ne(nr)})}r(d,b)},ae=d=>{Ve(d,{variant:"outline",children:(b,ie)=>{m();var Je=g("No subscription");r(b,Je)},$$slots:{default:!0}})};de(R,d=>{e(_).subscription?d(V):d(ae,!1)})}r(W,T)},$$slots:{default:!0}})});var Xe=a(Ze,2);n(Xe,()=>$e,(J,K)=>{K(J,{children:(W,Re)=>{var T=q(),R=v(T);{var V=d=>{var b=g();Y(ie=>O(b,ie),[()=>Me(e(_).subscription.currentPeriodStart)]),r(d,b)},ae=d=>{var b=Br();r(d,b)};de(R,d=>{e(_).subscription&&e(_).subscription.currentPeriodStart?d(V):d(ae,!1)})}r(W,T)},$$slots:{default:!0}})});var sr=a(Xe,2);n(sr,()=>$e,(J,K)=>{K(J,{children:(W,Re)=>{var T=q(),R=v(T);{var V=d=>{var b=g();Y(ie=>O(b,ie),[()=>Me(e(_).subscription.currentPeriodEnd)]),r(d,b)},ae=d=>{var b=Gr();r(d,b)};de(R,d=>{e(_).subscription&&e(_).subscription.currentPeriodEnd?d(V):d(ae,!1)})}r(W,T)},$$slots:{default:!0}})}),r(L,H)},$$slots:{default:!0}})}),r(te,le)}),r(p,B)};de(re,p=>{e(Ee).length===0?p($):p(y,!1)})}r(Z,C)},$$slots:{default:!0}})}),r(ne,k)},$$slots:{default:!0}})});var u=a(o,2),c=l(u);const I=z(()=>Math.ceil(ue().length/e(F)));Ar(c,{get currentPage(){return e(D)},get pageSize(){return e(F)},get totalItems(){return ue().length},get totalPages(){return e(I)},onPageChange:w=>x(D,w,!0),onPageSizeChange:w=>{x(F,w,!0),x(D,1)}}),i(u),i(t),r(s,t)};de(Qe,s=>{e(Pe)&&e(M).length===0?s(Ge):s(He,!1)})}Y(()=>vr(_e,e(ve))),r(xe,fe),ar()}ur(["input"]);export{_t as component};
