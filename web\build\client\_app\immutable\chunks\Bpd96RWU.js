import{c as p,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as d,s as l}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(r,o){const s=d(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M3 6h18"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"}]];f(r,l({name:"trash"},()=>s,{get iconNode(){return a},children:(e,h)=>{var t=p(),n=i(t);m(n,o,"default",{},null),c(e,t)},$$slots:{default:!0}}))}export{y as T};
