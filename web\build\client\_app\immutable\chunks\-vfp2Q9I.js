import{c as d,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as c}from"./CGmarHxI.js";import{s as i}from"./BBa424ah.js";import{l as $,s as h}from"./Btcx8l8F.js";import{I as p}from"./D4f2twK-.js";function y(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"m7 15 5 5 5-5"}],["path",{d:"m7 9 5-5 5 5"}]];p(n,h({name:"chevrons-up-down"},()=>o,{get iconNode(){return a},children:(s,u)=>{var e=d(),r=c(e);i(r,t,"default",{},null),l(s,e)},$$slots:{default:!0}}))}function M(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"}],["path",{d:"M8 10v4"}],["path",{d:"M12 10v2"}],["path",{d:"M16 10v6"}]];p(n,h({name:"folder-kanban"},()=>o,{get iconNode(){return a},children:(s,u)=>{var e=d(),r=c(e);i(r,t,"default",{},null),l(s,e)},$$slots:{default:!0}}))}function z(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"}],["path",{d:"M9 18c-4.51 2-5-2-7-2"}]];p(n,h({name:"github"},()=>o,{get iconNode(){return a},children:(s,u)=>{var e=d(),r=c(e);i(r,t,"default",{},null),l(s,e)},$$slots:{default:!0}}))}function b(n,t){const o=$(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"}],["rect",{width:"4",height:"12",x:"2",y:"9"}],["circle",{cx:"4",cy:"4",r:"2"}]];p(n,h({name:"linkedin"},()=>o,{get iconNode(){return a},children:(s,u)=>{var e=d(),r=c(e);i(r,t,"default",{},null),l(s,e)},$$slots:{default:!0}}))}export{y as C,M as F,z as G,b as L};
