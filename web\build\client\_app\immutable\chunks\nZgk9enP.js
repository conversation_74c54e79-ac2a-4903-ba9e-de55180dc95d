import{bh as s,i as _,Q as n,ao as m,o as l,ae as p}from"./CGmarHxI.js";function v(e){n===null&&s(),p&&n.l!==null?d(n).m.push(e):_(()=>{const t=l(e);if(typeof t=="function")return t})}function g(e){n===null&&s(),v(()=>()=>l(e))}function b(e,t,{bubbles:c=!1,cancelable:a=!1}={}){return new CustomEvent(e,{detail:t,bubbles:c,cancelable:a})}function x(){const e=n;return e===null&&s(),(t,c,a)=>{var r;const o=(r=e.s.$$events)==null?void 0:r[t];if(o){const f=m(o)?o.slice():[o],u=b(t,c,a);for(const i of f)i.call(e.x,u);return!u.defaultPrevented}return!0}}function d(e){var t=e.l;return t.u??(t.u={a:[],b:[],m:[]})}const h=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{h as _,g as a,x as c,v as o};
