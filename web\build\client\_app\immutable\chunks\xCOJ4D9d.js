import{w,g as S}from"./Dc4vaUpe.js";import{w as $}from"./26EXiO5K.js";const u=50,y="user_notifications",{subscribe:I,update:g,set:f}=w([]),d=w(0);try{const e=localStorage.getItem(y);if(e){const i=JSON.parse(e).map(n=>({...n,timestamp:new Date(n.timestamp)}));f(i)}}catch(e){console.error("Failed to load notifications from local storage:",e)}function l(e){try{localStorage.setItem(y,JSON.stringify(e))}catch(o){console.error("Failed to save notifications to local storage:",o)}}function b(e){const o=e.id||crypto.randomUUID(),i=e.timestamp||new Date,n=e.requestId;return console.log("Adding notification to store:",{id:o,title:e.title,message:e.message,type:e.type,read:e.read,global:e.global,timestamp:i,requestId:n}),n&&console.log(`Notification has request ID: ${n}`),g(a=>{const s=a.findIndex(r=>r.id===o);if(s>=0){console.log(`Updating existing notification with ID ${o}`);const r=[...a],m=r[s].read,p=e.read||!1;return r[s]={...r[s],...e,id:o,timestamp:i,read:m||p},!m&&p&&d.update(N=>Math.max(0,N-1)),l(r),r}console.log(`Creating new notification with ID ${o}`);const c={id:o,title:e.title||"",message:e.message||"",url:e.url,type:e.type||"info",read:e.read||!1,global:e.global||!1,timestamp:i,requestId:e.requestId};c.read||d.update(r=>r+1);const t=[c,...a];if(console.log(`Total notifications after adding: ${t.length}`),t.length>u){console.log(`Trimming notifications to ${u} items`);const r=t.slice(0,u);return l(r),r}return l(t),t}),o}async function h(e){try{const o=await fetch("/api/notifications",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"markAsRead",id:e})});o.ok?console.log("Notification marked as read on server"):console.error("Failed to mark notification as read on server:",o.statusText)}catch(o){console.error("Error marking notification as read:",o)}g(o=>{let i=!1;const n=o.map(a=>a.id===e&&!a.read?(i=!0,{...a,read:!0}):a);return i&&d.update(a=>Math.max(0,a-1)),l(n),n})}async function k(){try{const e=await fetch("/api/notifications",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"markAllAsRead"})});e.ok?console.log("All notifications marked as read on server"):console.error("Failed to mark all notifications as read on server:",e.statusText)}catch(e){console.error("Error marking all notifications as read:",e)}d.set(0),g(e=>e.map(o=>({...o,read:!0})));try{const o=S({subscribe:I});l(o)}catch(e){console.error("Error saving to local storage:",e)}}async function v(e){try{console.log(`Fetching notifications from server${e?` with requestId ${e}`:""}...`);const o=Date.now();let i="/api/notifications";e&&(i+=`?requestId=${encodeURIComponent(e)}`);const n=await fetch(i),a=Date.now()-o;if(console.log(`Fetch completed in ${a}ms`),!n.ok)throw new Error(`Failed to fetch notifications: ${n.statusText}`);const s=await n.json();if(console.log("Notifications API response:",s),s.success&&s.notifications){console.log(`Processing ${s.notifications.length} notifications from server`),s.notifications.length>0&&console.log("Notification IDs:",s.notifications.map(t=>t.id).join(", ")),s.unreadCount!==void 0&&(console.log(`Updating unread count to ${s.unreadCount}`),d.set(s.unreadCount));const c=s.notifications.map(t=>(console.log(`Processing notification from server: ${t.id}`,{title:t.title,message:t.message,type:t.type,read:t.read,global:t.global,timestamp:t.timestamp||t.createdAt,requestId:t.requestId}),{id:t.id,title:t.title,message:t.message,url:t.url,type:t.type||"info",read:t.read||!1,global:t.global||!1,timestamp:new Date(t.timestamp||t.createdAt||Date.now()),requestId:t.requestId}));if(e){const t=c.some(r=>r.requestId===e);console.log(`Notification with request ID ${e} ${t?"found":"not found"} in server response`)}console.log(`Setting ${c.length} notifications in store`),f(c),l(c),console.log("Notifications processed successfully")}else console.warn("No notifications found in API response or response was not successful");return s}catch(o){throw console.error("Error fetching notifications:",o),o}}function T(e,o){return console.log(`Sending notification via WebSocket to user ${o}:`,e),$.send({type:"notification",data:{...e,userId:o}})}function A(e){console.log("Dispatching resume parsing event:",e);const o=new CustomEvent("resume-parsing-completed",{detail:{resumeId:e.resumeId,profileId:e.profileId,userId:e.userId,message:e.message||"Resume parsing completed successfully",timestamp:new Date().toISOString()}});window.dispatchEvent(o)}async function D(){console.log("Refreshing notifications from server");try{await v(),console.log("Successfully refreshed notifications from server")}catch(e){console.error("Error refreshing notifications from server:",e)}}const R={subscribe:I,set:f,update:g,addNotification:b,removeNotification:h,markAsRead:h,clearAll:k,fetchFromServer:v,sendNotificationViaWebSocket:T,dispatchResumeParsingEvent:A,refreshNotifications:D};export{R as n,d as u};
