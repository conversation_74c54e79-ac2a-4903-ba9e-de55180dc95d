var re=Object.defineProperty;var qt=r=>{throw TypeError(r)};var ie=(r,t,o)=>t in r?re(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o;var c=(r,t,o)=>ie(r,typeof t!="symbol"?t+"":t,o),jt=(r,t,o)=>t.has(r)||qt("Cannot "+o);var e=(r,t,o)=>(jt(r,t,"read from private field"),o?o.call(r):t.get(r)),l=(r,t,o)=>t.has(r)?qt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,o),Dt=(r,t,o,s)=>(jt(r,t,"write to private field"),s?s.call(r,o):t.set(r,o),o);import{c as C,a as h,f as rt,e as se}from"./BasJTneF.js";import{p as S,f as _,a as H,g as i,x as m,c as X,r as Y,au as j,t as ne,k as Z,d as g,e as Jt,s as ae}from"./CGmarHxI.js";import{c as kt}from"./BvdI7LR8.js";import{p as n,r as J,s as ot}from"./Btcx8l8F.js";import{s as N,c as Qt}from"./ncUU1dSD.js";import{i as U}from"./u21ee2wt.js";import{e as V,a as Xt}from"./B-Xjo-Yt.js";import{m as $,b as d,o as le,w as Yt,u as $t}from"./BfX7a-t9.js";import{u as Mt}from"./CnMg5bH0.js";import{u as de,b as ue,P as ce,a as he,g as Zt,F as pe}from"./D-o7ybA5.js";import{P as fe}from"./BaVT73bJ.js";import{n as Gt}from"./DX6rZLP_.js";import{o as ge}from"./CmxjS0TN.js";import{o as ve}from"./Ntteq2n_.js";import{C as te}from"./DuoUhxYL.js";import{b as me,d as be,i as ye}from"./Bpi49Nrf.js";import{u as _e}from"./hrXlVaSN.js";import{h as ee}from"./Bd3zs5C6.js";var Ce=se('<svg viewBox="0 0 30 10" preserveAspectRatio="none" data-arrow=""><polygon points="0,0 30,0 15,10" fill="currentColor"></polygon></svg>'),we=rt("<span><!></span>");function Pe(r,t){S(t,!0);let o=n(t,"id",19,Mt),s=n(t,"width",3,10),a=n(t,"height",3,5),p=J(t,["$$slots","$$events","$$legacy","id","children","child","width","height"]);const f=m(()=>$(p,{id:o()}));var v=C(),b=_(v);{var T=O=>{var w=C(),A=_(w);N(A,()=>t.child,()=>({props:i(f)})),h(O,w)},Q=O=>{var w=we();V(w,()=>({...i(f)}));var A=X(w);{var M=u=>{var y=C(),it=_(y);N(it,()=>t.children??j),h(u,y)},D=u=>{var y=Ce();ne(()=>{Xt(y,"width",s()),Xt(y,"height",a())}),h(u,y)};U(A,u=>{t.children?u(M):u(D,!1)})}Y(w),h(O,w)};U(b,O=>{t.child?O(T):O(Q,!1)})}h(r,v),H()}function Te(r,t){S(t,!0);let o=n(t,"id",19,Mt),s=n(t,"ref",15,null),a=J(t,["$$slots","$$events","$$legacy","id","ref"]);const p=de({id:d.with(()=>o()),ref:d.with(()=>s(),v=>s(v))}),f=m(()=>$(a,p.props));Pe(r,ot(()=>i(f))),H()}function Ut(r,t,o={}){const{immediate:s=!0}=o,a=d(!1);let p;function f(){p&&(clearTimeout(p),p=null)}function v(){a.current=!1,f()}function b(...T){f(),a.current=!0,p=setTimeout(()=>{a.current=!1,p=null,r(...T)},t)}return s&&(a.current=!0,me&&b()),le(()=>{v()}),{isPending:d.readonly(a),start:b,stop:v}}const Oe="data-tooltip-content",De="data-tooltip-trigger";var lt,tt,E,Ft,Nt;class ke{constructor(t){c(this,"opts");l(this,lt,Z(!0));c(this,"isPointerInTransit",d(!1));l(this,tt);l(this,E,Z(null));l(this,Ft,()=>{this.opts.skipDelayDuration.current!==0&&e(this,tt).start()});l(this,Nt,()=>{e(this,tt).stop()});c(this,"onOpen",t=>{i(e(this,E))&&i(e(this,E))!==t&&i(e(this,E)).handleClose(),e(this,Nt).call(this),this.isOpenDelayed=!1,g(e(this,E),t,!0)});c(this,"onClose",t=>{i(e(this,E))===t&&g(e(this,E),null),e(this,Ft).call(this)});c(this,"isTooltipOpen",t=>i(e(this,E))===t);this.opts=t,Dt(this,tt,Ut(()=>{this.isOpenDelayed=!0},this.opts.skipDelayDuration.current,{immediate:!1}))}get isOpenDelayed(){return i(e(this,lt))}set isOpenDelayed(t){g(e(this,lt),t,!0)}}lt=new WeakMap,tt=new WeakMap,E=new WeakMap,Ft=new WeakMap,Nt=new WeakMap;var dt,ut,ct,ht,pt,ft,gt,L,I,vt,xt;class Fe{constructor(t,o){c(this,"opts");c(this,"provider");l(this,dt,m(()=>this.opts.delayDuration.current??this.provider.opts.delayDuration.current));l(this,ut,m(()=>this.opts.disableHoverableContent.current??this.provider.opts.disableHoverableContent.current));l(this,ct,m(()=>this.opts.disableCloseOnTriggerClick.current??this.provider.opts.disableCloseOnTriggerClick.current));l(this,ht,m(()=>this.opts.disabled.current??this.provider.opts.disabled.current));l(this,pt,m(()=>this.opts.ignoreNonKeyboardFocus.current??this.provider.opts.ignoreNonKeyboardFocus.current));l(this,ft,Z(null));l(this,gt,Z(null));l(this,L,Z(!1));l(this,I);l(this,vt,m(()=>this.opts.open.current?i(e(this,L))?"delayed-open":"instant-open":"closed"));c(this,"handleOpen",()=>{e(this,I).stop(),g(e(this,L),!1),this.opts.open.current=!0});c(this,"handleClose",()=>{e(this,I).stop(),this.opts.open.current=!1});l(this,xt,()=>{e(this,I).stop();const t=!this.provider.isOpenDelayed,o=this.delayDuration??0;t||o===0?(g(e(this,L),o>0&&t,!0),this.opts.open.current=!0):e(this,I).start()});c(this,"onTriggerEnter",()=>{e(this,xt).call(this)});c(this,"onTriggerLeave",()=>{this.disableHoverableContent?this.handleClose():e(this,I).stop()});this.opts=t,this.provider=o,Dt(this,I,Ut(()=>{g(e(this,L),!0),this.opts.open.current=!0},this.delayDuration??0,{immediate:!1})),Yt(()=>this.delayDuration,()=>{this.delayDuration!==void 0&&Dt(this,I,Ut(()=>{g(e(this,L),!0),this.opts.open.current=!0},this.delayDuration,{immediate:!1}))}),Yt(()=>this.opts.open.current,s=>{s?this.provider.onOpen(this):this.provider.onClose(this)})}get delayDuration(){return i(e(this,dt))}set delayDuration(t){g(e(this,dt),t)}get disableHoverableContent(){return i(e(this,ut))}set disableHoverableContent(t){g(e(this,ut),t)}get disableCloseOnTriggerClick(){return i(e(this,ct))}set disableCloseOnTriggerClick(t){g(e(this,ct),t)}get disabled(){return i(e(this,ht))}set disabled(t){g(e(this,ht),t)}get ignoreNonKeyboardFocus(){return i(e(this,pt))}set ignoreNonKeyboardFocus(t){g(e(this,pt),t)}get contentNode(){return i(e(this,ft))}set contentNode(t){g(e(this,ft),t,!0)}get triggerNode(){return i(e(this,gt))}set triggerNode(t){g(e(this,gt),t,!0)}get stateAttr(){return i(e(this,vt))}set stateAttr(t){g(e(this,vt),t)}}dt=new WeakMap,ut=new WeakMap,ct=new WeakMap,ht=new WeakMap,pt=new WeakMap,ft=new WeakMap,gt=new WeakMap,L=new WeakMap,I=new WeakMap,vt=new WeakMap,xt=new WeakMap;var q,et,k,Kt,Et,It,At,Rt,St,Ht,mt;class Ne{constructor(t,o){c(this,"opts");c(this,"root");l(this,q,d(!1));l(this,et,Z(!1));l(this,k,m(()=>this.opts.disabled.current||this.root.disabled));c(this,"handlePointerUp",()=>{e(this,q).current=!1});l(this,Kt,()=>{i(e(this,k))||(e(this,q).current=!1)});l(this,Et,()=>{i(e(this,k))||(e(this,q).current=!0,document.addEventListener("pointerup",()=>{this.handlePointerUp()},{once:!0}))});l(this,It,t=>{i(e(this,k))||t.pointerType!=="touch"&&(i(e(this,et))||this.root.provider.isPointerInTransit.current||(this.root.onTriggerEnter(),g(e(this,et),!0)))});l(this,At,()=>{i(e(this,k))||(this.root.onTriggerLeave(),g(e(this,et),!1))});l(this,Rt,t=>{e(this,q).current||i(e(this,k))||this.root.ignoreNonKeyboardFocus&&!be(t.currentTarget)||this.root.handleOpen()});l(this,St,()=>{i(e(this,k))||this.root.handleClose()});l(this,Ht,()=>{this.root.disableCloseOnTriggerClick||i(e(this,k))||this.root.handleClose()});l(this,mt,m(()=>{var t;return{id:this.opts.id.current,"aria-describedby":this.root.opts.open.current?(t=this.root.contentNode)==null?void 0:t.id:void 0,"data-state":this.root.stateAttr,"data-disabled":ee(i(e(this,k))),"data-delay-duration":`${this.root.delayDuration}`,[De]:"",tabindex:i(e(this,k))?void 0:0,disabled:this.opts.disabled.current,onpointerup:e(this,Kt),onpointerdown:e(this,Et),onpointermove:e(this,It),onpointerleave:e(this,At),onfocus:e(this,Rt),onblur:e(this,St),onclick:e(this,Ht)}}));this.opts=t,this.root=o,$t({...t,onRefChange:s=>{this.root.triggerNode=s}})}get props(){return i(e(this,mt))}set props(t){g(e(this,mt),t)}}q=new WeakMap,et=new WeakMap,k=new WeakMap,Kt=new WeakMap,Et=new WeakMap,It=new WeakMap,At=new WeakMap,Rt=new WeakMap,St=new WeakMap,Ht=new WeakMap,mt=new WeakMap;var bt,yt;class xe{constructor(t,o){c(this,"opts");c(this,"root");c(this,"onInteractOutside",t=>{var o;if(ye(t.target)&&((o=this.root.triggerNode)!=null&&o.contains(t.target))&&this.root.disableCloseOnTriggerClick){t.preventDefault();return}this.opts.onInteractOutside.current(t),!t.defaultPrevented&&this.root.handleClose()});c(this,"onEscapeKeydown",t=>{var o,s;(s=(o=this.opts.onEscapeKeydown).current)==null||s.call(o,t),!t.defaultPrevented&&this.root.handleClose()});c(this,"onOpenAutoFocus",t=>{t.preventDefault()});c(this,"onCloseAutoFocus",t=>{t.preventDefault()});l(this,bt,m(()=>({open:this.root.opts.open.current})));l(this,yt,m(()=>({id:this.opts.id.current,"data-state":this.root.stateAttr,"data-disabled":ee(this.root.disabled),style:{pointerEvents:"auto",outline:"none"},[Oe]:""})));c(this,"popperProps",{onInteractOutside:this.onInteractOutside,onEscapeKeydown:this.onEscapeKeydown,onOpenAutoFocus:this.onOpenAutoFocus,onCloseAutoFocus:this.onCloseAutoFocus});this.opts=t,this.root=o,$t({...t,onRefChange:s=>{this.root.contentNode=s},deps:()=>this.root.opts.open.current}),_e({triggerNode:()=>this.root.triggerNode,contentNode:()=>this.root.contentNode,enabled:()=>this.root.opts.open.current&&!this.root.disableHoverableContent,onPointerExit:()=>{this.root.provider.isTooltipOpen(this.root)&&this.root.handleClose()},setIsPointerInTransit:s=>{this.root.provider.isPointerInTransit.current=s},transitTimeout:this.root.provider.opts.skipDelayDuration.current}),ve(()=>ge(window,"scroll",s=>{const a=s.target;a&&a.contains(this.root.triggerNode)&&this.root.handleClose()}))}get snippetProps(){return i(e(this,bt))}set snippetProps(t){g(e(this,bt),t)}get props(){return i(e(this,yt))}set props(t){g(e(this,yt),t)}}bt=new WeakMap,yt=new WeakMap;const oe=new te("Tooltip.Provider"),Vt=new te("Tooltip.Root");function Ke(r){return oe.set(new ke(r))}function Ee(r){return Vt.set(new Fe(r,oe.get()))}function Ie(r){return new Ne(r,Vt.get())}function Ae(r){return new xe(r,Vt.get())}function Re(r,t){S(t,!0);let o=n(t,"open",15,!1),s=n(t,"onOpenChange",3,Gt);Ee({open:d.with(()=>o(),a=>{o(a),s()(a)}),delayDuration:d.with(()=>t.delayDuration),disableCloseOnTriggerClick:d.with(()=>t.disableCloseOnTriggerClick),disableHoverableContent:d.with(()=>t.disableHoverableContent),ignoreNonKeyboardFocus:d.with(()=>t.ignoreNonKeyboardFocus),disabled:d.with(()=>t.disabled)}),ue(r,{children:(a,p)=>{var f=C(),v=_(f);N(v,()=>t.children??j),h(a,f)},$$slots:{default:!0}}),H()}var Se=rt("<div><div><!></div></div>"),He=rt("<div><div><!></div></div>");function Me(r,t){S(t,!0);let o=n(t,"id",19,Mt),s=n(t,"ref",15,null),a=n(t,"side",3,"top"),p=n(t,"sideOffset",3,0),f=n(t,"align",3,"center"),v=n(t,"avoidCollisions",3,!0),b=n(t,"arrowPadding",3,0),T=n(t,"sticky",3,"partial"),Q=n(t,"hideWhenDetached",3,!1),O=n(t,"collisionPadding",3,0),w=n(t,"onInteractOutside",3,Gt),A=n(t,"onEscapeKeydown",3,Gt),M=n(t,"forceMount",3,!1),D=J(t,["$$slots","$$events","$$legacy","children","child","id","ref","side","sideOffset","align","avoidCollisions","arrowPadding","sticky","hideWhenDetached","collisionPadding","onInteractOutside","onEscapeKeydown","forceMount"]);const u=Ae({id:d.with(()=>o()),ref:d.with(()=>s(),x=>s(x)),onInteractOutside:d.with(()=>w()),onEscapeKeydown:d.with(()=>A())}),y=m(()=>({side:a(),sideOffset:p(),align:f(),avoidCollisions:v(),arrowPadding:b(),sticky:T(),hideWhenDetached:Q(),collisionPadding:O()})),it=m(()=>$(D,i(y),u.props));var st=C(),_t=_(st);{var zt=x=>{ce(x,ot(()=>i(it),()=>u.popperProps,{get enabled(){return u.root.opts.open.current},get id(){return o()},trapFocus:!1,loop:!1,preventScroll:!1,forceMount:!0,popper:(nt,P)=>{let Ct=()=>P==null?void 0:P().props,W=()=>P==null?void 0:P().wrapperProps;var F=C();const wt=m(()=>$(Ct(),{style:Zt("tooltip")}));var Pt=_(F);{var Tt=z=>{var R=C(),B=_(R),K=Jt(()=>({props:i(wt),wrapperProps:W(),...u.snippetProps}));N(B,()=>t.child,()=>i(K)),h(z,R)},Ot=z=>{var R=Se();V(R,()=>({...W()}));var B=X(R);V(B,()=>({...i(wt)}));var K=X(B);N(K,()=>t.children??j),Y(B),Y(R),h(z,R)};U(Pt,z=>{t.child?z(Tt):z(Ot,!1)})}h(nt,F)},$$slots:{popper:!0}}))},Lt=(x,Wt)=>{{var nt=P=>{he(P,ot(()=>i(it),()=>u.popperProps,{get present(){return u.root.opts.open.current},get id(){return o()},trapFocus:!1,loop:!1,preventScroll:!1,forceMount:!1,popper:(W,F)=>{let wt=()=>F==null?void 0:F().props,Pt=()=>F==null?void 0:F().wrapperProps;var Tt=C();const Ot=m(()=>$(wt(),{style:Zt("tooltip")}));var z=_(Tt);{var R=K=>{var G=C(),at=_(G),Bt=Jt(()=>({props:i(Ot),wrapperProps:Pt(),...u.snippetProps}));N(at,()=>t.child,()=>i(Bt)),h(K,G)},B=K=>{var G=He();V(G,()=>({...Pt()}));var at=X(G);V(at,()=>({...i(Ot)}));var Bt=X(at);N(Bt,()=>t.children??j),Y(at),Y(G),h(K,G)};U(z,K=>{t.child?K(R):K(B,!1)})}h(W,Tt)},$$slots:{popper:!0}}))};U(x,P=>{M()||P(nt)},Wt)}};U(_t,x=>{M()?x(zt):x(Lt,!1)})}h(r,st),H()}var ze=rt("<button><!></button>");function Le(r,t){S(t,!0);let o=n(t,"id",19,Mt),s=n(t,"disabled",3,!1),a=n(t,"type",3,"button"),p=n(t,"ref",15,null),f=J(t,["$$slots","$$events","$$legacy","children","child","id","disabled","type","ref"]);const v=Ie({id:d.with(()=>o()),disabled:d.with(()=>s()??!1),ref:d.with(()=>p(),T=>p(T))}),b=m(()=>$(f,v.props,{type:a()}));pe(r,{get id(){return o()},children:(T,Q)=>{var O=C(),w=_(O);{var A=D=>{var u=C(),y=_(u);N(y,()=>t.child,()=>({props:i(b)})),h(D,u)},M=D=>{var u=ze();V(u,()=>({...i(b)}));var y=X(u);N(y,()=>t.children??j),Y(u),h(D,u)};U(w,D=>{t.child?D(A):D(M,!1)})}h(T,O)},$$slots:{default:!0}}),H()}function We(r,t){S(t,!0);let o=n(t,"ref",15,null),s=J(t,["$$slots","$$events","$$legacy","ref"]);Te(r,ot(()=>s,{get ref(){return o()},set ref(a){o(a)}})),H()}function Be(r,t){S(t,!0);let o=n(t,"delayDuration",3,700),s=n(t,"disableCloseOnTriggerClick",3,!1),a=n(t,"disableHoverableContent",3,!1),p=n(t,"disabled",3,!1),f=n(t,"ignoreNonKeyboardFocus",3,!1),v=n(t,"skipDelayDuration",3,300);Ke({delayDuration:d.with(()=>o()),disableCloseOnTriggerClick:d.with(()=>s()),disableHoverableContent:d.with(()=>a()),disabled:d.with(()=>p()),ignoreNonKeyboardFocus:d.with(()=>f()),skipDelayDuration:d.with(()=>v())});var b=C(),T=_(b);N(T,()=>t.children??j),h(r,b),H()}function co(r,t){S(t,!0);let o=n(t,"ref",15,null),s=J(t,["$$slots","$$events","$$legacy","ref"]);var a=C(),p=_(a);kt(p,()=>Le,(f,v)=>{v(f,ot({"data-slot":"tooltip-trigger"},()=>s,{get ref(){return o()},set ref(b){o(b)}}))}),h(r,a),H()}var Ge=rt("<div></div>"),Ue=rt("<!> <!>",1);function ho(r,t){S(t,!0);let o=n(t,"ref",15,null),s=n(t,"sideOffset",3,0),a=n(t,"side",3,"top"),p=J(t,["$$slots","$$events","$$legacy","ref","class","sideOffset","side","children","arrowClasses"]);var f=C(),v=_(f);kt(v,()=>fe,(b,T)=>{T(b,{children:(Q,O)=>{var w=C(),A=_(w);const M=m(()=>Qt("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-(--bits-tooltip-content-transform-origin) z-50 w-fit text-balance rounded-md px-3 py-1.5 text-xs",t.class));kt(A,()=>Me,(D,u)=>{u(D,ot({"data-slot":"tooltip-content",get sideOffset(){return s()},get side(){return a()},get class(){return i(M)}},()=>p,{get ref(){return o()},set ref(y){o(y)},children:(y,it)=>{var st=Ue(),_t=_(st);N(_t,()=>t.children??j);var zt=ae(_t,2);kt(zt,()=>We,(Lt,x)=>{x(Lt,{child:(nt,P)=>{let Ct=()=>P==null?void 0:P().props;var W=Ge();V(W,F=>({class:F,...Ct()}),[()=>Qt("bg-primary z-50 size-2.5 rotate-45 rounded-[2px]","data-[side=top]:translate-x-1/2 data-[side=top]:translate-y-[calc(-50%_+_2px)]","data-[side=bottom]:-translate-y-[calc(-50%_+_1px)] data-[side=bottom]:translate-x-1/2","data-[side=right]:translate-x-[calc(50%_+_2px)] data-[side=right]:translate-y-1/2","data-[side=left]:translate-y-[calc(50%_-_3px)]",t.arrowClasses)]),h(nt,W)},$$slots:{child:!0}})}),h(y,st)},$$slots:{default:!0}}))}),h(Q,w)},$$slots:{default:!0}})}),h(r,f),H()}const po=Re,fo=Be;export{fo as P,po as R,co as T,ho as a};
