import{d as e}from"./sanity-DV0NwVOn.js";const o=e("comments",{"close-pane-button-text":"Close comments","close-pane-button-text-aria-label":"Close comments","compose.add-comment-input-placeholder":"Add comment to <strong>{{field}}</strong>","compose.add-comment-input-placeholder-upsell":"Upgrade to add comment","compose.create-comment-placeholder":"Create a new comment","compose.mention-user-aria-label":"Mention user","compose.mention-user-tooltip":"Mention user","compose.reply-placeholder":"Reply","compose.reply-placeholder-upsell":"Upgrade to reply","compose.send-comment-aria-label":"Send comment","compose.send-comment-tooltip":"Send comment","copy-link-error-message":"Unable to copy link to clipboard","delete-comment.body":"Once deleted, a comment cannot be recovered.","delete-comment.confirm":"Delete comment","delete-comment.title":"Delete this comment?","delete-dialog.error":"An error occurred while deleting the comment. Please try again.","delete-thread.body":"This comment and its replies will be deleted, and once deleted cannot be recovered.","delete-thread.confirm":"Delete thread","delete-thread.title":"Delete this comment thread?","discard.button-confirm":"Discard","discard.header":"Discard comment?","discard.text":"Do you want to discard the comment?","feature-feedback.link":"Share your feedback","feature-feedback.title":"Help improve ","feature-name":"Comments","field-button.aria-label-add":"Add comment","field-button.aria-label-open":"Open comments","field-button.content_one":"View comment","field-button.content_other":"View comments","field-button.title":"Add comment","inline-add-comment-button.disabled-overlap-title":"Comments cannot overlap","inline-add-comment-button.title":"Add comment","list-item.breadcrumb-button-go-to-field-aria-label":"Go to {{field}} field","list-item.context-menu-add-reaction":"Add reaction","list-item.context-menu-add-reaction-aria-label":"Add reaction","list-item.context-menu-add-reaction-upsell":"Upgrade to add reaction","list-item.copy-link":"Copy link to comment","list-item.delete-comment":"Delete comment","list-item.edit-comment":"Edit comment","list-item.edit-comment-upsell":"Upgrade to edit comment","list-item.go-to-field-button.aria-label":"Go to field","list-item.layout-context":"on <IntentLink>{{title}}</IntentLink>","list-item.layout-edited":"edited","list-item.layout-failed-sent":"Failed to send.","list-item.layout-posting":"Posting...","list-item.layout-retry":"Retry","list-item.missing-referenced-value-tooltip-content":"The commented text has been deleted","list-item.open-menu-aria-label":"Open comment actions menu","list-item.re-open-resolved":"Re-open","list-item.re-open-resolved-aria-label":"Re-open","list-item.resolved-tooltip-aria-label":"Mark comment as resolved","list-item.resolved-tooltip-content":"Mark as resolved","list-status.empty-state-open-text":"Open comments on this document will be shown here.","list-status.empty-state-open-title":"No open comments yet","list-status.empty-state-resolved-text":"Resolved comments on this document will be shown here.","list-status.empty-state-resolved-title":"No resolved comments yet","list-status.error":"Something went wrong","list-status.loading":"Loading comments","mentions.no-users-found":"No users found","mentions.unauthorized-user":"Unauthorized","mentions.user-list-aria-label":"List of users to mention","onboarding.body":"You can add comments to any field in a document. They'll show up here, grouped by field.","onboarding.dismiss":"Got it","onboarding.header":"Document fields now have comments","reactions.add-reaction-tooltip":"Add reaction","reactions.react-with-aria-label":"React with {{reactionName}}","reactions.user-list.unknown-user-fallback-name":"Unknown user","reactions.user-list.you":"you","reactions.user-list.you_leading":"You","reactions.users-reacted-with-reaction":"<UserList/> <Text>reacted with</Text> <ReactionName/>","status-filter.status-open":"Open","status-filter.status-open-full":"Open comments","status-filter.status-resolved":"Resolved","status-filter.status-resolved-full":"Resolved comments","status-filter.status-resolved-full-upsell":"Upgrade to see resolved comments"});export{o as default};
