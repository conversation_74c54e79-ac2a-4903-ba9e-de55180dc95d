import{e as C,a as h,c as S}from"./BasJTneF.js";import"./CgXBgsce.js";import{p as j,a as B,c as I,s as A,r as M,f as O,g as l,x as P,aM as q}from"./CGmarHxI.js";import{e as D,i as E}from"./C3w0v0gR.js";import{s as F}from"./BBa424ah.js";import{e as G}from"./w80wGXGd.js";import{e as g}from"./B-Xjo-Yt.js";import{i as H}from"./BIEMS98f.js";import{l as v,p as o}from"./Btcx8l8F.js";/**
 * @license lucide-svelte v0.486.0 - ISC
 *
 * ISC License
 * 
 * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
 * 
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 * 
 */const J={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};var K=C("<svg><!><!></svg>");function ee(k,e){const d=v(e,["children","$$slots","$$events","$$legacy"]),p=v(d,["name","color","size","strokeWidth","absoluteStrokeWidth","iconNode"]);j(e,!1);let c=o(e,"name",8,void 0),_=o(e,"color",8,"currentColor"),i=o(e,"size",8,24),m=o(e,"strokeWidth",8,2),w=o(e,"absoluteStrokeWidth",8,!1),b=o(e,"iconNode",24,()=>[]);const x=(...r)=>r.filter((t,a,n)=>!!t&&n.indexOf(t)===a).join(" ");H();var s=K();g(s,(r,t)=>({...J,...p,width:i(),height:i(),stroke:_(),"stroke-width":r,class:t}),[()=>w()?Number(m())*24/Number(i()):m(),()=>x("lucide-icon","lucide",c()?`lucide-${c()}`:"",d.class)]);var f=I(s);D(f,1,b,E,(r,t)=>{var a=P(()=>q(l(t),2));let n=()=>l(a)[0],y=()=>l(a)[1];var u=S(),z=O(u);G(z,n,!0,(N,L)=>{g(N,()=>({...y()}))}),h(r,u)});var W=A(f);F(W,e,"default",{},null),M(s),h(k,s),B()}export{ee as I,J as d};
