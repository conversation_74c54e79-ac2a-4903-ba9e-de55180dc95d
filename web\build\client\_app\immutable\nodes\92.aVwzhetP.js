import{f as d,a as i,t as er}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as yr,f as E,t as x,a as wr,g as k,e as T,s as a,c as e,n as F,r}from"../chunks/CGmarHxI.js";import{s as m}from"../chunks/CIt1g2O9.js";import{i as w}from"../chunks/u21ee2wt.js";import{e as tr,i as ar}from"../chunks/C3w0v0gR.js";import{a as j}from"../chunks/B-Xjo-Yt.js";import{e as br}from"../chunks/CmxjS0TN.js";import{i as Cr}from"../chunks/BIEMS98f.js";import{p as Pr}from"../chunks/Btcx8l8F.js";import{S as Rr}from"../chunks/C6g8ubaU.js";import{C as sr}from"../chunks/DuGukytH.js";import{C as or}from"../chunks/Cdn-N1RY.js";import{C as ir}from"../chunks/GwmmX_iF.js";import{C as dr}from"../chunks/D50jIuLr.js";import{u as kr}from"../chunks/jRvHGFcG.js";import{P as Ar}from"../chunks/BMgaXnEE.js";import{A as Tr}from"../chunks/Ce6y1v79.js";import{A as jr}from"../chunks/Cs0qIT7f.js";var Dr=d('<div class="mb-8 overflow-hidden rounded-lg"><img class="w-full"/></div>'),Sr=d('<div class="prose prose-lg max-w-none"><!></div>'),Ir=d('<div class="mt-8"><a target="_blank" rel="noopener noreferrer" class="bg-primary hover:bg-primary/90 inline-flex items-center rounded-md px-4 py-2 text-white"><svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg> Download Resource</a></div>'),Ur=d('<span class="bg-primary/10 text-primary rounded-full px-3 py-1 text-xs"> </span>'),Br=d('<div><dt class="text-sm font-medium text-gray-500">Tags</dt> <dd class="text-gray-700"><div class="mt-1 flex flex-wrap gap-2"></div></dd></div>'),Hr=d('<div><dt class="text-sm font-medium text-gray-500">Published</dt> <dd class="text-gray-700"> </dd></div>'),Lr=d('<dl class="space-y-4"><div><dt class="text-sm font-medium text-gray-500">Category</dt> <dd class="text-gray-700"> </dd></div> <div><dt class="text-sm font-medium text-gray-500">Type</dt> <dd class="capitalize text-gray-700"> </dd></div> <!> <!></dl>'),qr=d("<!> <!>",1),zr=d('<div class="rounded-lg border p-4"><h3 class="mb-1 font-medium"> </h3> <p class="mb-2 text-sm text-gray-600"> </p> <a class="text-primary inline-flex items-center text-sm font-medium hover:underline">View Resource <!></a></div>'),Er=d('<div class="space-y-4"></div>'),Fr=d("<!> <!>",1),Mr=d('<!> <div class="container mx-auto px-4 py-12"><div class="mb-8"><a href="/resources" class="text-primary inline-flex items-center text-sm hover:underline"><!> Back to Resources</a></div> <div class="grid gap-8 lg:grid-cols-3"><div class="lg:col-span-2"><div class="mb-8"><h1 class="mb-4 text-3xl font-bold"> </h1> <p class="text-lg text-gray-600"> </p></div> <!> <!> <!></div> <div><div class="space-y-6"><!> <!></div></div></div></div>',1);function ve(lr,M){yr(M,!1);const t=Pr(M,"data",8)().resource,O=t.relatedResources||[];Cr();var V=Mr(),G=E(V);const vr=T(()=>t.tags?`career resources, job search, ${t.tags.join(", ")}`:`career resources, job search, ${t.title.toLowerCase()}, ${t.resourceType||t.type||"resource"}`);Rr(G,{get title(){return`${t.title??""} | Hirli Resources`},get description(){return t.description},get keywords(){return k(vr)}});var J=a(G,2),D=e(J),K=e(D),nr=e(K);Tr(nr,{class:"mr-1 h-4 w-4"}),F(),r(K),r(D);var N=a(D,2),S=e(N),I=e(S),U=e(I),cr=e(U,!0);r(U);var Q=a(U,2),mr=e(Q,!0);r(Q),r(I);var W=a(I,2);{var pr=s=>{var o=Dr(),l=e(o);r(o),x(n=>{j(l,"src",n),j(l,"alt",t.mainImage.alt||t.title)},[()=>kr(t.mainImage,{width:800})],T),br("error",l,n=>{const h=n.currentTarget;h.src="https://placehold.co/800x400?text=Resource"}),i(s,o)};w(W,s=>{t.mainImage&&s(pr)})}var X=a(W,2);{var ur=s=>{var o=Sr(),l=e(o);Ar(l,{get value(){return t.content}}),r(o),i(s,o)};w(X,s=>{t.content&&s(ur)})}var _r=a(X,2);{var fr=s=>{var o=Ir(),l=e(o);r(o),x(()=>j(l,"href",t.downloadUrl)),i(s,o)};w(_r,s=>{t.downloadUrl&&s(fr)})}r(S);var Y=a(S,2),Z=e(Y),rr=e(Z);sr(rr,{children:(s,o)=>{var l=qr(),n=E(l);ir(n,{children:(b,C)=>{dr(b,{children:(p,v)=>{F();var u=er("Resource Details");i(p,u)},$$slots:{default:!0}})},$$slots:{default:!0}});var h=a(n,2);or(h,{children:(b,C)=>{var p=Lr(),v=e(p),u=a(e(v),2),_=e(u,!0);r(u),r(v);var f=a(v,2),$=a(e(f),2),B=e($,!0);r($),r(f);var y=a(f,2);{var H=c=>{var g=Br(),R=a(e(g),2),A=e(R);tr(A,5,()=>t.tags,ar,(q,hr)=>{var z=Ur(),$r=e(z,!0);r(z),x(()=>m($r,k(hr))),i(q,z)}),r(A),r(R),r(g),i(c,g)};w(y,c=>{t.tags&&t.tags.length>0&&c(H)})}var P=a(y,2);{var L=c=>{var g=Hr(),R=a(e(g),2),A=e(R,!0);r(R),r(g),x(q=>m(A,q),[()=>new Date(t.publishedAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})],T),i(c,g)};w(P,c=>{t.publishedAt&&c(L)})}r(p),x(()=>{m(_,t.category),m(B,t.resourceType||t.type||"Resource")}),i(b,p)},$$slots:{default:!0}}),i(s,l)},$$slots:{default:!0}});var gr=a(rr,2);{var xr=s=>{sr(s,{children:(o,l)=>{var n=Fr(),h=E(n);ir(h,{children:(C,p)=>{dr(C,{children:(v,u)=>{F();var _=er("Related Resources");i(v,_)},$$slots:{default:!0}})},$$slots:{default:!0}});var b=a(h,2);or(b,{children:(C,p)=>{var v=Er();tr(v,5,()=>O,ar,(u,_)=>{var f=zr(),$=e(f),B=e($,!0);r($);var y=a($,2),H=e(y);r(y);var P=a(y,2),L=a(e(P));jr(L,{class:"ml-1 h-3 w-3"}),r(P),r(f),x(c=>{m(B,k(_).title),m(H,`${c??""}...`),j(P,"href",`/resources/${k(_).slug.current}`)},[()=>k(_).description.substring(0,100)],T),i(u,f)}),r(v),i(C,v)},$$slots:{default:!0}}),i(o,n)},$$slots:{default:!0}})};w(gr,s=>{O.length>0&&s(xr)})}r(Z),r(Y),r(N),r(J),x(()=>{m(cr,t.title),m(mr,t.description)}),i(lr,V),wr()}export{ve as component};
