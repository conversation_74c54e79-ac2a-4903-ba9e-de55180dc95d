var F=Object.getPrototypeOf;var G=Reflect.get;var b=i=>{throw TypeError(i)};var S=(i,u,e)=>u.has(i)||b("Cannot "+e);var s=(i,u,e)=>(S(i,u,"read from private field"),e?e.call(i):u.get(i)),d=(i,u,e)=>u.has(i)?b("Cannot add the same private member more than once"):u instanceof WeakSet?u.add(i):u.set(i,e);var z=(i,u,e)=>(S(i,u,"access private method"),e);var j=(i,u,e)=>G(F(i),e,u);import{a7 as v,g as h,d as c}from"./CGmarHxI.js";import{i as l}from"./BfX7a-t9.js";var a,n,o,p,m;const w=class w extends Map{constructor(e){super();d(this,p);d(this,a,new Map);d(this,n,v(0));d(this,o,v(0));if(e){for(var[r,t]of e)super.set(r,t);s(this,o).v=super.size}}has(e){var r=s(this,a),t=r.get(e);if(t===void 0){var f=super.get(e);if(f!==void 0)t=v(0),r.set(e,t);else return h(s(this,n)),!1}return h(t),!0}forEach(e,r){z(this,p,m).call(this),super.forEach(e,r)}get(e){var r=s(this,a),t=r.get(e);if(t===void 0){var f=super.get(e);if(f!==void 0)t=v(0),r.set(e,t);else{h(s(this,n));return}}return h(t),super.get(e)}set(e,r){var E;var t=s(this,a),f=t.get(e),A=super.get(e),B=super.set(e,r),g=s(this,n);if(f===void 0)t.set(e,v(0)),c(s(this,o),super.size),l(g);else if(A!==r){l(f);var x=g.reactions===null?null:new Set(g.reactions),C=x===null||!((E=f.reactions)!=null&&E.every(D=>x.has(D)));C&&l(g)}return B}delete(e){var r=s(this,a),t=r.get(e),f=super.delete(e);return t!==void 0&&(r.delete(e),c(s(this,o),super.size),c(t,-1),l(s(this,n))),f}clear(){if(super.size!==0){super.clear();var e=s(this,a);c(s(this,o),0);for(var r of e.values())c(r,-1);l(s(this,n)),e.clear()}}keys(){return h(s(this,n)),super.keys()}values(){return z(this,p,m).call(this),super.values()}entries(){return z(this,p,m).call(this),super.entries()}[Symbol.iterator](){return this.entries()}get size(){return h(s(this,o)),super.size}};a=new WeakMap,n=new WeakMap,o=new WeakMap,p=new WeakSet,m=function(){h(s(this,n));var e=s(this,a);if(s(this,o).v!==e.size)for(var r of j(w.prototype,this,"keys").call(this))e.has(r)||e.set(r,v(0));for(var[,t]of s(this,a))h(t)};let q=w;export{q as S};
