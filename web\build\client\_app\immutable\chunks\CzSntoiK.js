import{c as l,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function h(o,r){const e=m(r,["children","$$slots","$$events","$$legacy"]),t=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["circle",{cx:"9",cy:"7",r:"4"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11"}]];f(o,d({name:"user-plus"},()=>e,{get iconNode(){return t},children:(a,$)=>{var s=l(),n=p(s);c(n,r,"default",{},null),i(a,s)},$$slots:{default:!0}}))}export{h as U};
