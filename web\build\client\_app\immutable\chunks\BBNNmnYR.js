import{c as l,a as m}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as i}from"./BBa424ah.js";import{l as c,s as f}from"./Btcx8l8F.js";import{I as d}from"./D4f2twK-.js";function C(t,o){const e=c(o,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"m15 18-6-6 6-6"}]];d(t,f({name:"chevron-left"},()=>e,{get iconNode(){return s},children:(a,$)=>{var r=l(),n=p(r);i(n,o,"default",{},null),m(a,r)},$$slots:{default:!0}}))}export{C};
