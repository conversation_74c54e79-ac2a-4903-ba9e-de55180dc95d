import{c,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(t,o){const s=m(o,["children","$$slots","$$events","$$legacy"]),e=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"M12 16v-4"}],["path",{d:"M12 8h.01"}]];f(t,d({name:"info"},()=>s,{get iconNode(){return e},children:(a,$)=>{var r=c(),n=p(r);l(n,o,"default",{},null),i(a,r)},$$slots:{default:!0}}))}export{y as I};
