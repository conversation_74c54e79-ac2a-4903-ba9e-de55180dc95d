import{c as n,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as d,s as m}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(t,o){const a=d(o,["children","$$slots","$$events","$$legacy"]),s=[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}]];f(t,m({name:"clipboard"},()=>a,{get iconNode(){return s},children:(e,$)=>{var r=n(),i=l(r);c(i,o,"default",{},null),p(e,r)},$$slots:{default:!0}}))}export{x as C};
