import{f as B,a as n,t as u}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as C,s as a,f as I,a as N,c,t as O,g as t,e as T,n as v,d,m as w,r as g}from"../chunks/CGmarHxI.js";import{s as D}from"../chunks/CIt1g2O9.js";import{e as F}from"../chunks/CmxjS0TN.js";import{p as J}from"../chunks/CWmzcjye.js";import{i as z}from"../chunks/BIEMS98f.js";import{p as A}from"../chunks/Btcx8l8F.js";import{I as P}from"../chunks/DMTMHyMa.js";import{B as G}from"../chunks/B1K98fMG.js";import{L as b}from"../chunks/BvvicRXk.js";import{t as h}from"../chunks/DjPYYl4Z.js";import{g as H}from"../chunks/BiJhC7W5.js";var K=B('<h1 class="mb-6 text-2xl font-semibold">Reset Password</h1> <form class="max-w-md space-y-4"><div><!> <!></div> <div><!> <!></div> <!></form>',1);function ar(E,_){C(_,!1);let R=A(_,"data",8),o=w(""),p=w(""),i=w(!1);async function S(){if(t(o)!==t(p)){h.error("Error",{description:"Passwords do not match"});return}d(i,!0);try{const r=await fetch("/api/auth/reset-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:R().token,password:t(o)})});if(!r.ok){const e=await r.json();throw new Error((e==null?void 0:e.error)||"Failed to reset password")}h.success("Success",{description:"Password updated!"}),H("/auth/sign-in")}catch(r){h.error("Error",{description:r instanceof Error?r.message:"Something went wrong"})}finally{d(i,!1)}}z();var $=K(),m=a(I($),2),f=c(m),y=c(f);b(y,{for:"password",children:(r,e)=>{v();var s=u("New Password");n(r,s)},$$slots:{default:!0}});var k=a(y,2);P(k,{id:"password",type:"password",required:!0,get value(){return t(o)},set value(r){d(o,r)},$$legacy:!0}),g(f);var l=a(f,2),x=c(l);b(x,{for:"confirm",children:(r,e)=>{v();var s=u("Confirm Password");n(r,s)},$$slots:{default:!0}});var q=a(x,2);P(q,{id:"confirm",type:"password",required:!0,get value(){return t(p)},set value(r){d(p,r)},$$legacy:!0}),g(l);var L=a(l,2);const j=T(()=>t(i)||t(o).length<6);G(L,{type:"submit",get disabled(){return t(j)},children:(r,e)=>{v();var s=u();O(()=>D(s,t(i)?"Resetting...":"Reset Password")),n(r,s)},$$slots:{default:!0}}),g(m),F("submit",m,J(S)),n(E,$),N()}export{ar as component};
