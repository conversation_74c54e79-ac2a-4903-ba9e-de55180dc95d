import{f as ke,a as n,t as d}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{f as Te,s,c as e,n as l,r as t}from"../chunks/CGmarHxI.js";import{e as m}from"../chunks/CmxjS0TN.js";import{B as c}from"../chunks/B1K98fMG.js";import{S as rt}from"../chunks/C6g8ubaU.js";import{S as it}from"../chunks/DumgozFE.js";import{C as o}from"../chunks/BNEH2jqx.js";import{C as ot}from"../chunks/DW7T7T22.js";import{D as nt}from"../chunks/tr-scC-m.js";import{A as ct}from"../chunks/Cs0qIT7f.js";var dt=ke("View All Templates <!>",1),mt=ke(`<!> <section class="py-32 md:py-40"><div class="container mx-auto px-4"><div class="mx-auto max-w-3xl"><h1 class="mb-8 text-5xl font-light md:text-6xl lg:text-7xl">Build a resume that <span class="text-blue-600">gets noticed</span></h1> <p class="mb-12 text-xl text-gray-600">Our professional resume builder helps you create a standout resume in minutes. Designed to
        pass ATS systems and impress recruiters.</p> <div class="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0"><!> <!></div></div></div></section> <section class="bg-gray-50 py-16"><div class="container mx-auto px-4"><img src="/images/resume-template.png" alt="Resume Template" class="h-auto w-full shadow-lg"/></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto max-w-5xl"><div class="grid grid-cols-1 gap-16 md:grid-cols-3"><div><div class="mb-4 text-5xl font-light text-black">3x</div> <p class="text-xl text-gray-600">More interview callbacks with our professionally designed templates</p></div> <div><div class="mb-4 text-5xl font-light text-black">90%</div> <p class="text-xl text-gray-600">ATS pass rate ensuring your resume gets seen by recruiters</p></div> <div><div class="mb-4 text-5xl font-light text-black">15<span class="text-3xl">min</span></div> <p class="text-xl text-gray-600">Average time to create a professional, tailored resume</p></div></div></div></div></section> <section class="border-t border-gray-100 bg-gray-50 py-24"><div class="container mx-auto px-4"><div class="mx-auto mb-20 max-w-3xl text-center"><h2 class="mb-6 text-4xl font-light">Powerful Resume Building Features</h2> <p class="text-xl text-gray-600">Our resume builder is designed to help you create professional, ATS-optimized resumes in
        minutes.</p></div> <div class="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3"><div><div class="mb-6"><!></div> <h3 class="mb-4 text-2xl font-light">Easy to Use</h3> <p class="mb-6 text-lg text-gray-600">Our intuitive interface makes building a professional resume simple and fast. No design
          skills required. Just fill in your information and our builder does the rest.</p> <ul class="space-y-3"><li class="flex items-start"><!> <span class="text-gray-600">Drag-and-drop interface</span></li> <li class="flex items-start"><!> <span class="text-gray-600">Pre-written content suggestions</span></li> <li class="flex items-start"><!> <span class="text-gray-600">Real-time preview</span></li></ul></div> <div><div class="mb-6"><!></div> <h3 class="mb-4 text-2xl font-light">ATS Optimized</h3> <p class="mb-6 text-lg text-gray-600">Our resumes are designed to pass through Applicant Tracking Systems with flying colors,
          ensuring your application gets seen by recruiters.</p> <ul class="space-y-3"><li class="flex items-start"><!> <span class="text-gray-600">Keyword optimization</span></li> <li class="flex items-start"><!> <span class="text-gray-600">ATS-friendly formatting</span></li> <li class="flex items-start"><!> <span class="text-gray-600">90% pass rate</span></li></ul></div> <div><div class="mb-6"><!></div> <h3 class="mb-4 text-2xl font-light">Multiple Formats</h3> <p class="mb-6 text-lg text-gray-600">Download your resume in PDF, Word, or plain text formats to suit any application
          requirements.</p> <ul class="space-y-3"><li class="flex items-start"><!> <span class="text-gray-600">PDF for professional look</span></li> <li class="flex items-start"><!> <span class="text-gray-600">DOCX for editability</span></li> <li class="flex items-start"><!> <span class="text-gray-600">TXT for ATS submission</span></li></ul></div></div></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto mb-20 max-w-3xl text-center"><h2 class="mb-6 text-4xl font-light">Professional Resume Templates</h2> <p class="text-xl text-gray-600">Choose from our collection of professionally designed templates that are both visually
        appealing and ATS-friendly.</p></div> <div class="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3"><div><img src="/images/resume-template-1.jpg" alt="Professional Template" class="mb-4 h-auto w-full"/> <h3 class="mb-2 text-2xl font-light">Professional</h3> <p class="mb-4 text-gray-600">Clean and modern design for corporate roles</p> <!></div> <div><img src="/images/resume-template-2.jpg" alt="Creative Template" class="mb-4 h-auto w-full"/> <h3 class="mb-2 text-2xl font-light">Creative</h3> <p class="mb-4 text-gray-600">Bold design for creative industries</p> <!></div> <div><img src="/images/resume-template-3.jpg" alt="Executive Template" class="mb-4 h-auto w-full"/> <h3 class="mb-2 text-2xl font-light">Executive</h3> <p class="mb-4 text-gray-600">Sophisticated design for senior positions</p> <!></div></div> <div class="mt-12 text-center"><!></div></div></section> <section class="border-t border-gray-100 bg-gray-50 py-24"><div class="container mx-auto px-4"><div class="mx-auto mb-20 max-w-3xl text-center"><h2 class="mb-6 text-4xl font-light">How It Works</h2> <p class="text-xl text-gray-600">Create a professional resume in just three simple steps.</p></div> <div class="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3"><div><div class="mb-6 inline-flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-xl font-medium text-blue-600">1</div> <h3 class="mb-4 text-2xl font-light">Choose a Template</h3> <p class="mb-6 text-gray-600">Select from our collection of professional, ATS-friendly resume templates.</p> <img src="/images/step-1-template.jpg" alt="Choose Template" class="h-auto w-full"/></div> <div><div class="mb-6 inline-flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-xl font-medium text-blue-600">2</div> <h3 class="mb-4 text-2xl font-light">Add Your Content</h3> <p class="mb-6 text-gray-600">Fill in your details with our guided form, or import from LinkedIn to save time.</p> <img src="/images/step-2-content.jpg" alt="Add Content" class="h-auto w-full"/></div> <div><div class="mb-6 inline-flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-xl font-medium text-blue-600">3</div> <h3 class="mb-4 text-2xl font-light">Download & Apply</h3> <p class="mb-6 text-gray-600">Export your polished resume in your preferred format and start applying with confidence.</p> <img src="/images/step-3-download.jpg" alt="Download Resume" class="h-auto w-full"/></div></div></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto max-w-4xl text-center"><div class="mb-8"><img src="https://randomuser.me/api/portraits/women/67.jpg" alt="User" class="mx-auto h-20 w-20 rounded-full"/></div> <blockquote class="mb-8 text-3xl font-light italic">"The templates helped me create a resume that stands out. I landed a job within 3 weeks of
        using this builder!"</blockquote> <div><p class="text-xl font-medium">Alex R.</p> <p class="text-gray-600">Marketing Specialist at Facebook</p></div></div></div></section> <section class="border-t border-gray-100 bg-gray-50 py-24"><div class="container mx-auto px-4"><div class="mx-auto mb-20 max-w-3xl text-center"><h2 class="mb-6 text-4xl font-light">Simple, Transparent Pricing</h2> <p class="text-xl text-gray-600">Choose the plan that fits your needs. All plans include our core resume builder features.</p></div> <div class="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2"><div class="bg-white p-12"><h3 class="mb-2 text-2xl font-light">Basic</h3> <p class="mb-6 text-5xl font-light">$0<span class="text-lg font-normal text-gray-500">/month</span></p> <p class="mb-6 border-b border-gray-100 pb-6 text-gray-600">Perfect for creating a simple, professional resume.</p> <ul class="mb-8 space-y-4"><li class="flex items-start"><!> <span>1 resume template</span></li> <li class="flex items-start"><!> <span>PDF downloads</span></li> <li class="flex items-start"><!> <span>Basic ATS optimization</span></li></ul> <!></div> <div class="bg-white p-12"><h3 class="mb-2 text-2xl font-light">Pro</h3> <p class="mb-6 text-5xl font-light">$12<span class="text-lg font-normal text-gray-500">/month</span></p> <p class="mb-6 border-b border-gray-100 pb-6 text-gray-600">For job seekers who want to stand out from the crowd.</p> <ul class="mb-8 space-y-4"><li class="flex items-start"><!> <span>All templates</span></li> <li class="flex items-start"><!> <span>Multiple formats (PDF, DOCX, TXT)</span></li> <li class="flex items-start"><!> <span>Advanced ATS optimization</span></li> <li class="flex items-start"><!> <span>Content suggestions</span></li> <li class="flex items-start"><!> <span>Unlimited resume versions</span></li></ul> <!></div></div></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto max-w-3xl"><h2 class="mb-16 text-center text-4xl font-light">Frequently Asked Questions</h2> <div class="space-y-12"><div><h3 class="mb-4 text-2xl font-light">How does the resume builder work?</h3> <p class="text-lg text-gray-600">Our resume builder guides you through the process of creating a professional resume.
            Simply choose a template, fill in your information, and download your completed resume
            in your preferred format.</p></div> <div><h3 class="mb-4 text-2xl font-light">Are the templates ATS-friendly?</h3> <p class="text-lg text-gray-600">Yes, all of our templates are designed to be ATS-friendly. They use clean, simple
            formatting that can be easily read by Applicant Tracking Systems, ensuring your resume
            gets past the initial screening.</p></div> <div><h3 class="mb-4 text-2xl font-light">Can I create multiple resumes?</h3> <p class="text-lg text-gray-600">With our Pro plan, you can create unlimited resume versions. This allows you to tailor
            your resume for different job applications, maximizing your chances of success.</p></div> <div><h3 class="mb-4 text-2xl font-light">Can I cancel my subscription anytime?</h3> <p class="text-lg text-gray-600">Yes, you can cancel your subscription at any time. If you cancel, you'll continue to
            have access until the end of your billing period.</p></div></div></div></div></section> <section class="bg-black py-24 text-white"><div class="container mx-auto px-4 text-center"><h2 class="mb-8 text-4xl font-light">Ready to Build Your Professional Resume?</h2> <p class="mx-auto mb-12 max-w-3xl text-xl text-white/80">Join thousands of job seekers who have successfully landed interviews with our resume builder.</p> <div class="flex flex-col justify-center gap-4 sm:flex-row"><!> <!></div> <p class="mt-8 text-white/60">No credit card required. Start for free.</p></div></section>`,1);function Tt($e){var X=mt(),H=Te(X);rt(H,{title:"Hirli Resume Builder - Create Professional Resumes",description:"Create a professional resume that stands out with our easy-to-use builder. Customize your design and content to match your career goals.",keywords:"resume builder, professional resume, ATS optimized, resume templates",url:"https://hirli.com/resume-builder",image:"/assets/og-image-resume-builder.jpg"});var x=s(H,2),N=e(x),V=e(N),W=s(e(V),4),J=e(W);c(J,{class:"bg-black px-8 py-4 text-lg text-white hover:bg-gray-800",children:(a,r)=>{l();var i=d("Create Your Resume");n(a,i)},$$slots:{default:!0}});var Ae=s(J,2);c(Ae,{variant:"outline",class:"border-gray-300 px-8 py-4 text-lg",children:(a,r)=>{l();var i=d("View Templates");n(a,i)},$$slots:{default:!0}}),t(W),t(V),t(N),t(x);var p=s(x,2),G=e(p),Ce=e(G);t(G),t(p);var u=s(p,4),K=e(u),L=s(e(K),2),v=e(L),g=e(v),Se=e(g);it(Se,{class:"h-8 w-8 text-blue-600"}),t(g);var Q=s(g,6),h=e(Q),Pe=e(h);o(Pe,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(h);var f=s(h,2),je=e(f);o(je,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(f);var Z=s(f,2),De=e(Z);o(De,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(Z),t(Q),t(v);var b=s(v,2),y=e(b),Re=e(y);ot(Re,{class:"h-8 w-8 text-blue-600"}),t(y);var ee=s(y,6),_=e(ee),Fe=e(_);o(Fe,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(_);var w=s(_,2),Be=e(w);o(Be,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(w);var te=s(w,2),Oe=e(te);o(Oe,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(te),t(ee),t(b);var se=s(b,2),T=e(se),ze=e(T);nt(ze,{class:"h-8 w-8 text-blue-600"}),t(T);var ae=s(T,6),k=e(ae),qe=e(k);o(qe,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(k);var $=s(k,2),Ee=e($);o(Ee,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t($);var le=s($,2),Ie=e(le);o(Ie,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(le),t(ae),t(se),t(L),t(K),t(u);var A=s(u,2),re=e(A),C=s(e(re),2),S=e(C),ie=e(S),Ue=s(ie,6);c(Ue,{class:"w-full bg-black text-white hover:bg-gray-800",children:(a,r)=>{l();var i=d("Use This Template");n(a,i)},$$slots:{default:!0}}),t(S);var P=s(S,2),oe=e(P),Ye=s(oe,6);c(Ye,{class:"w-full bg-black text-white hover:bg-gray-800",children:(a,r)=>{l();var i=d("Use This Template");n(a,i)},$$slots:{default:!0}}),t(P);var ne=s(P,2),ce=e(ne),Me=s(ce,6);c(Me,{class:"w-full bg-black text-white hover:bg-gray-800",children:(a,r)=>{l();var i=d("Use This Template");n(a,i)},$$slots:{default:!0}}),t(ne),t(C);var de=s(C,2),Xe=e(de);c(Xe,{variant:"outline",class:"border-gray-300 px-8 py-4 text-lg",children:(a,r)=>{l();var i=dt(),lt=s(Te(i));ct(lt,{class:"ml-2 h-4 w-4"}),n(a,i)},$$slots:{default:!0}}),t(de),t(re),t(A);var j=s(A,2),me=e(j),xe=s(e(me),2),D=e(xe),He=s(e(D),6);t(D);var R=s(D,2),Ne=s(e(R),6);t(R);var pe=s(R,2),Ve=s(e(pe),6);t(pe),t(xe),t(me),t(j);var F=s(j,4),ue=e(F),ve=s(e(ue),2),B=e(ve),O=s(e(B),6),z=e(O),We=e(z);o(We,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(z);var q=s(z,2),Je=e(q);o(Je,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(q);var ge=s(q,2),Ge=e(ge);o(Ge,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(ge),t(O);var Ke=s(O,2);c(Ke,{variant:"outline",class:"w-full border-gray-300 p-4 text-lg font-medium",children:(a,r)=>{l();var i=d("Get Started");n(a,i)},$$slots:{default:!0}}),t(B);var he=s(B,2),E=s(e(he),6),I=e(E),Le=e(I);o(Le,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(I);var U=s(I,2),Qe=e(U);o(Qe,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(U);var Y=s(U,2),Ze=e(Y);o(Ze,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(Y);var M=s(Y,2),et=e(M);o(et,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(M);var fe=s(M,2),tt=e(fe);o(tt,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"}),l(2),t(fe),t(E);var st=s(E,2);c(st,{class:"w-full bg-black p-4 text-lg font-medium text-white hover:bg-gray-800",children:(a,r)=>{l();var i=d("Start 7-Day Free Trial");n(a,i)},$$slots:{default:!0}}),t(he),t(ve),t(ue),t(F);var be=s(F,4),ye=e(be),_e=s(e(ye),4),we=e(_e);c(we,{class:"bg-white px-10 py-5 text-lg font-medium text-black hover:bg-gray-100",children:(a,r)=>{l();var i=d("Create Your Resume Now");n(a,i)},$$slots:{default:!0}});var at=s(we,2);c(at,{variant:"outline",class:"border-white px-10 py-5 text-lg font-medium text-white hover:bg-white/10",children:(a,r)=>{l();var i=d("View Templates");n(a,i)},$$slots:{default:!0}}),t(_e),l(2),t(ye),t(be),m("error",Ce,a=>{const r=a.currentTarget;r.src="https://placehold.co/1200x600?text=Resume+Template"}),m("error",ie,a=>{const r=a.currentTarget;r.src="https://placehold.co/400x600?text=Professional"}),m("error",oe,a=>{const r=a.currentTarget;r.src="https://placehold.co/400x600?text=Creative"}),m("error",ce,a=>{const r=a.currentTarget;r.src="https://placehold.co/400x600?text=Executive"}),m("error",He,a=>{const r=a.currentTarget;r.src="https://placehold.co/400x300?text=Choose+Template"}),m("error",Ne,a=>{const r=a.currentTarget;r.src="https://placehold.co/400x300?text=Add+Content"}),m("error",Ve,a=>{const r=a.currentTarget;r.src="https://placehold.co/400x300?text=Download+Resume"}),n($e,X)}export{Tt as component};
