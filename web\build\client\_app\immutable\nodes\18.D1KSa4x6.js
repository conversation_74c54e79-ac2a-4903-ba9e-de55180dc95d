import{f as m,a as c,t as xe}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as Ke,l as Qe,b as er,f as we,a as rr,s,c as i,g as e,m as n,d as t,r as l,n as P,x as ye,e as L,t as I}from"../chunks/CGmarHxI.js";import{s as te}from"../chunks/CIt1g2O9.js";import{i as U}from"../chunks/u21ee2wt.js";import{a as _e,s as T,c as ae}from"../chunks/B-Xjo-Yt.js";import{e as se}from"../chunks/CmxjS0TN.js";import{p as tr}from"../chunks/CWmzcjye.js";import{i as ar}from"../chunks/BIEMS98f.js";import{b as sr}from"../chunks/BiJhC7W5.js";import{B as M}from"../chunks/B1K98fMG.js";import{I as N}from"../chunks/DMTMHyMa.js";import{C as or}from"../chunks/T7uRAIbG.js";import{t as E}from"../chunks/DjPYYl4Z.js";import{S as ir}from"../chunks/C6g8ubaU.js";async function $e(V,J,b){const{callbackUrl:g,...w}=J??{},{redirect:h=!0,redirectTo:y=g??window.location.href,...S}=w,C=`${sr??""}/auth/${V==="credentials"?"callback":"signin"}/${V}`,_=await fetch(`${C}?${new URLSearchParams(b)}`,{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({...S,callbackUrl:y})}),f=await _.json();if(h){const x=f.url??y;window.location.href=x,x.includes("#")&&window.location.reload();return}const $=new URL(f.url).searchParams.get("error")??void 0,k=new URL(f.url).searchParams.get("code")??void 0;return{error:$,code:k,status:_.status,ok:_.ok,url:$?null:f.url}}var nr=m('<img alt="Google" src="/assets/svg/google.svg" class="mr-2"/> Sign up with Google',1),lr=m('<img alt="LinkedIn" src="/assets/svg/linkedin.svg" class="mr-2"/> Sign up with LinkedIn',1),dr=m('<p class="text-destructive text-sm">Please enter a valid email address.</p>'),cr=m('<div class="mt-2 rounded-md border p-2"><ul class="space-y-2 text-sm"><li>&#8226; Minimum 8 characters</li> <li>&#8226; At least one uppercase letter</li> <li>&#8226; At least one special character (!, @, #, $, etc.)</li></ul></div>'),ur=m('<p class="text-destructive text-sm">Passwords do not match.</p>'),fr=m('<p class="text-sm text-green-600"> </p>'),pr=m('<p class="text-muted-foreground text-sm">Validating referral code...</p>'),vr=m('<div class="grid gap-6"><div class="flex flex-col items-center justify-center gap-4"><!> <!></div> <div class="relative"><div class="absolute inset-0 flex items-center"><span class="border-border w-full border-2 border-t"></span></div> <div class="relative z-10 flex justify-center text-xs uppercase"><span class="text-muted-foreground bg-background px-2">Or</span></div></div></div> <form class="grid gap-6"><div class="grid gap-2"><!> <!></div> <div class="relative"><!> <button type="button" class="absolute right-0 top-0.5 p-2"><i></i></button></div> <!> <div class="relative"><!> <button type="button" class="absolute right-0 top-0.5 p-2"><i></i></button></div> <!> <div class="grid gap-2"><label for="referralCode" class="text-sm font-medium">Referral Code <span class="text-muted-foreground">(Optional)</span></label> <!> <!> <!></div> <div class="flex items-center space-x-4"><!> <label for="terms" class="text-sm">Agree to our <a href="/legal/terms" class="hover:text-primary underline underline-offset-4">Terms of Service</a> and <a href="/legal/privacy" class="hover:text-primary underline underline-offset-4">Privacy Policy</a>.</label></div> <!></form>',1),mr=m(`<div class="grid gap-6"><div class="border-border bg-primary/10 text-primary rounded-lg border p-4 text-sm"><p>We've sent a verification link to <strong> </strong>. Please check your email and
          click the link to verify your account.</p></div> <div class="mt-4 flex flex-col items-center justify-center gap-4"><p class="text-muted-foreground text-center text-sm">Didn't receive the email? Check your spam folder or try again.</p> <!></div></div>`),gr=m('<!> <div class="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]"><div class="mb-8 flex flex-col space-y-2 text-left"><h1 class="text-3xl font-semibold tracking-tight">Sign up</h1> <p class="text-muted-foreground text-md font-light">Already have an account? <a class="underline" href="/auth/sign-in">Sign in</a></p></div> <!></div>',1);function Lr(V,J){Ke(J,!1);let b=n(""),g=n(""),w=n(""),h=n(!1),y=n(!1),S=n(!1),u=n(""),C=n(""),_=n(!1),f=n(!1),$=n(!1),k=n(!1),x=n(!1),B=n(!1),F=n(!1),H=n(!1),O=n(!1),oe=n(!1),W=n(!1);function ke(){t(oe,/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e(b)))}function Pe(){t(W,!0)}function Ce(){t(W,!0)}const ie=async d=>{if(d){t(_,!0);try{const v=await(await fetch(`/api/referrals/validate?code=${encodeURIComponent(d)}`)).json();v.valid?(t(C,v.referrer.name||"Someone"),E.success("Referral Code Valid",{description:`You were referred by ${e(C)}!`})):(t(u,""),E.error("Invalid Referral Code",{description:"The referral code you entered is not valid."}))}catch(o){console.error("Error validating referral code:",o),t(u,""),E.error("Error",{description:"Failed to validate referral code."})}finally{t(_,!1)}}},Ue=async()=>{t(h,!0);const d=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e(b),password:e(g),confirmPassword:e(w),termsAccepted:e(y),referralCode:e(u)})});if(t(h,!1),d.ok){const o=await d.json();E.success("Account Created",{description:"Please check your email for a verification link to complete your registration.",duration:1e4}),t(S,!0),console.log("Account created successfully!"),console.log("Verification Token:",o.verificationToken),console.log("Verification URL:",o.verificationUrl)}else{const{message:o}=await d.json();E.error("Error",{description:o||"An error occurred while creating the account."}),console.error(o)}};function ne(){t(B,/[A-Z]/.test(e(g))),t(F,/[!@#$%^&*]/.test(e(g))),t(H,e(g).length>=8),t($,e(g)===e(w)),t(f,e(B)&&e(F)&&e(H)),e(f)&&t(O,!1)}function Se(){t(O,!0)}function Re(){e(f)&&t(O,!1)}function je(){t(k,!e(k))}function Ae(){t(x,!e(x))}Qe(()=>e(u),()=>{if(typeof window<"u"){const o=new URLSearchParams(window.location.search).get("ref");o&&!e(u)&&(t(u,o),ie(o))}}),er(),ar();var le=gr(),de=we(le);ir(de,{title:"Sign Up | Hirli",description:"Create your Hirli account to automate your job applications and streamline your job search process.",keywords:"sign up, create account, job application, career, automation, Hirli account"});var ce=s(de,2),Le=s(i(ce),2);{var Ie=d=>{var o=vr(),v=we(o),R=i(v),j=i(R),X=ye(()=>$e("google",{callbackUrl:"/dashboard"}));M(j,{type:"button",class:"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50",$$events:{click(...r){var a;(a=e(X))==null||a.apply(this,r)}},children:(r,a)=>{var p=nr();P(),c(r,p)},$$slots:{default:!0}});var z=s(j,2),Y=ye(()=>$e("linkedin",{callbackUrl:"/dashboard"}));M(z,{type:"button",class:"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50",$$events:{click(...r){var a;(a=e(Y))==null||a.apply(this,r)}},children:(r,a)=>{var p=lr();P(),c(r,p)},$$slots:{default:!0}}),l(R),P(2),l(v);var A=s(v,2),Z=i(A),q=i(Z);N(q,{id:"email",type:"email",required:!0,placeholder:"<EMAIL>",class:"border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm",get value(){return e(b)},set value(r){t(b,r)},$$events:{input:ke,focus:Pe,blur:Ce},$$legacy:!0});var Ee=s(q,2);{var Ve=r=>{var a=dr();c(r,a)};U(Ee,r=>{e(W)&&!e(oe)&&r(Ve)})}l(Z);var K=s(Z,2),ue=i(K);const Be=L(()=>e(k)?"text":"password"),Fe=L(()=>e(f)?"border-green-500":"border-destructive");N(ue,{id:"password",get type(){return e(Be)},placeholder:"Password",get disabled(){return e(h)},get class(){return`border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border ${e(Fe)??""} bg-transparent px-3 py-1 text-sm shadow-sm`},get value(){return e(g)},set value(r){t(g,r)},$$events:{input:ne,focus:Se,blur:Re},$$legacy:!0});var D=s(ue,2),He=i(D);l(D),l(K);var fe=s(K,2);{var Oe=r=>{var a=cr(),p=i(a),be=i(p),he=s(be,2),Ye=s(he,2);l(p),l(a),I(()=>{T(be,1,ae(e(H)?"text-green-500":"text-destructive")),T(he,1,ae(e(B)?"text-green-500":"text-destructive")),T(Ye,1,ae(e(F)?"text-green-500":"text-destructive"))}),c(r,a)};U(fe,r=>{e(O)&&!e(f)&&r(Oe)})}var Q=s(fe,2),pe=i(Q);const ze=L(()=>e(x)?"text":"password"),Ze=L(()=>e($)?"border-green-500":"border-destructive");N(pe,{id:"confirm-password",get type(){return e(ze)},placeholder:"Confirm Password",get disabled(){return e(h)},get class(){return`border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border ${e(Ze)??""} bg-transparent px-3 py-1 text-sm shadow-sm`},get value(){return e(w)},set value(r){t(w,r)},$$events:{input:ne},$$legacy:!0});var G=s(pe,2),qe=i(G);l(G),l(Q);var ve=s(Q,2);{var De=r=>{var a=ur();c(r,a)};U(ve,r=>{!e($)&&e(w)!==""&&r(De)})}var ee=s(ve,2),me=s(i(ee),2);N(me,{id:"referralCode",type:"text",placeholder:"Enter referral code",class:"border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm",get value(){return e(u)},set value(r){t(u,r)},$$events:{input:()=>{e(u)&&ie(e(u))}},$$legacy:!0});var ge=s(me,2);{var Ge=r=>{var a=fr(),p=i(a);l(a),I(()=>te(p,`✓ Referred by ${e(C)??""}`)),c(r,a)};U(ge,r=>{e(C)&&r(Ge)})}var Me=s(ge,2);{var Ne=r=>{var a=pr();c(r,a)};U(Me,r=>{e(_)&&r(Ne)})}l(ee);var re=s(ee,2),Je=i(re);or(Je,{get checked(){return e(y)},set checked(r){t(y,r)},$$legacy:!0}),P(2),l(re);var We=s(re,2);const Xe=L(()=>e(h)||!e(f)||!e($)||!e(H)||!e(B)||!e(F)||!e(y)||!e(b));M(We,{class:"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50",type:"submit",get disabled(){return e(Xe)},children:(r,a)=>{P();var p=xe();I(()=>te(p,e(h)?"Creating...":"Sign Up")),c(r,p)},$$slots:{default:!0}}),l(A),I(()=>{_e(D,"aria-label",e(k)?"Hide password":"Show password"),T(He,1,`fa-solid ${e(k)?"fa-eye-slash":"fa-eye"}`),_e(G,"aria-label",e(x)?"Hide confirm password":"Show confirm password"),T(qe,1,`fa-solid ${e(x)?"fa-eye-slash":"fa-eye"}`)}),se("click",D,je),se("click",G,Ae),se("submit",A,tr(Ue)),c(d,o)},Te=d=>{var o=mr(),v=i(o),R=i(v),j=s(i(R)),X=i(j,!0);l(j),P(),l(R),l(v);var z=s(v,2),Y=s(i(z),2);M(Y,{class:"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50",type:"button",onclick:()=>{t(S,!1)},children:(A,Z)=>{P();var q=xe("Back to Sign Up");c(A,q)},$$slots:{default:!0}}),l(z),l(o),I(()=>te(X,e(b))),c(d,o)};U(Le,d=>{e(S)?d(Te,!1):d(Ie)})}l(ce),c(V,le),rr()}export{Lr as component};
