var a=e=>{throw TypeError(e)};var x=(e,t,n)=>t.has(e)||a("Cannot "+n);var s=(e,t,n)=>(x(e,t,"read from private field"),n?n.call(e):t.get(e)),i=(e,t,n)=>t.has(e)?a("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),h=(e,t,n,u)=>(x(e,t,"write to private field"),u?u.call(e,n):t.set(e,n),n);import{aC as C,aD as c,aE as f}from"./CGmarHxI.js";var o,r;class m{constructor(t){i(this,o);i(this,r);h(this,o,t),h(this,r,Symbol(t))}get key(){return s(this,r)}exists(){return C(s(this,r))}get(){const t=c(s(this,r));if(t===void 0)throw new Error(`Context "${s(this,o)}" not found`);return t}getOr(t){const n=c(s(this,r));return n===void 0?t:n}set(t){return f(s(this,r),t)}}o=new WeakMap,r=new WeakMap;export{m as C};
