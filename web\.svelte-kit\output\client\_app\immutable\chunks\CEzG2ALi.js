const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./CfBTQ9Yv.js","./BGYDhraB.js","./BUfAIGxh.js","./foSyZIKW.js","./BLAa_HGQ.js","./BMpib84U.js","./Sg8aqnBg.js"])))=>i.map(i=>d[i]);
import{f as u,a as d,c as Fe,t as Le}from"./BasJTneF.js";import"./CgXBgsce.js";import{o as Gt,a as pa}from"./nZgk9enP.js";import{p as ut,l as qe,b as Tt,s as l,c as n,a as ft,h as yt,r as o,k as St,v as ga,i as Rr,d as _e,f as ge,t as b,n as Te,g as e,x as Ma,m as ot,q as Ue,ab as _a,e as $t,o as Ve,_ as Jr,w as ha,aL as dt}from"./CGmarHxI.js";import{s as D}from"./CIt1g2O9.js";import{i as O}from"./u21ee2wt.js";import{e as Se,i as ze}from"./C3w0v0gR.js";import{c as Jt}from"./BvdI7LR8.js";import{a as ba}from"./DDUgF6Ik.js";import{g as yr,e as it,b as wt,a as tr,s as xr,x as Ba}from"./CmxjS0TN.js";import{i as xt}from"./BIEMS98f.js";import{p as Ke}from"./Btcx8l8F.js";import{B as at}from"./B1K98fMG.js";import{I as et}from"./DMTMHyMa.js";import{_ as kt}from"./C1FmrZbK.js";import{a as je,s as vt,b as he,d as Pt}from"./B-Xjo-Yt.js";import{b as Ha}from"./5V1tIHTN.js";import{C as Ga}from"./DuGukytH.js";import{C as Ua}from"./Cdn-N1RY.js";import{C as Wa}from"./GwmmX_iF.js";import{C as Va}from"./D50jIuLr.js";import{B as Za}from"./DaBofrVv.js";import{t as ur}from"./DjPYYl4Z.js";import{b as Ya,d as qa}from"./CPe_16wQ.js";import{S as ya}from"./FAbXdqfL.js";import{R as Ka}from"./qwsZpUIl.js";import{I as Xa}from"./BuYRPDDz.js";import{C as Ja}from"./BNEH2jqx.js";import{R as Qa,B as eo,I as to,U as ro,L as ao,a as oo,b as no,G as io,P as so}from"./C3y1xd2Y.js";import{S as lo}from"./BoNCRmBc.js";import{L as co}from"./DRGimm5x.js";import{L as vo}from"./CrpvsheG.js";import{C as uo}from"./lZwfPN85.js";import{T as Wr}from"./VNuMAkuB.js";import"./BiJhC7W5.js";import{s as xa}from"./B8blszX7.js";import{z as wa}from"./CrHU05dq.js";import{s as Lr}from"./BBa424ah.js";import{R as fo,a as mo}from"./tdzGgazS.js";import{C as po}from"./BwkAotBa.js";import{D as go,a as _o,b as ho,c as bo}from"./CKh8VGVX.js";import{E as yo}from"./7AwcL9ec.js";import{E as xo}from"./6UJoWgvL.js";import{h as qt}from"./DYwWIJ9y.js";import{w as Da}from"./Dc4vaUpe.js";import{S as wo}from"./Zo6ILzvY.js";import{S as Do}from"./C2MdR6K0.js";import{L as Rt}from"./BvvicRXk.js";import{S as Qr}from"./0ykhD7u6.js";import{F as Lt,C as Nt,a as jt}from"./FeejBSkx.js";import{o as Et,s as Pe,a as Kt}from"./C8B1VUaq.js";const $o=Et({alignment:Pe({required_error:"Please select an alignment"}),margin:Pe({required_error:"Please select a margin size"}),pageSize:Pe({required_error:"Please select a page size"}),font:Pe({required_error:"Please select a font"}),fontSize:Pe({required_error:"Please select a font size"}),lineHeight:Pe({required_error:"Please select a line height"}),bulletIcon:Pe({required_error:"Please select a bullet icon"}),layout:Pe().optional(),primaryColor:Pe().optional(),accentColor:Pe().optional(),textColor:Pe().optional(),backgroundColor:Pe().optional(),headerStyle:Pe().optional(),sectionStyle:Pe().optional(),paperSize:Pe().optional()}),Eo={alignment:"Left",margin:"Medium",pageSize:"Letter (8.5 x 11 in)",font:"Roboto",fontSize:"12px",lineHeight:"1.5",bulletIcon:"•",layout:"classic",primaryColor:"#2563eb",accentColor:"#4b5563",textColor:"#111827",backgroundColor:"#ffffff",headerStyle:"centered",sectionStyle:"bordered",paperSize:"letter"},So=Et({header:Et({name:Pe().default(""),email:Pe().email().default(""),phone:Pe().default("")}),summary:Et({content:Pe().default("")}).default({}),education:Kt(Et({school:Pe().default(""),degree:Pe().default(""),major:Pe().default(""),gpa:Pe().default(""),startDate:Pe().default(""),endDate:Pe().default("")})).default([]),certifications:Kt(Et({description:Pe().default("")})).default([]),experience:Kt(Et({jobTitle:Pe().default(""),company:Pe().default(""),startDate:Pe().default(""),endDate:Pe().default(""),description:Pe().default("")})).default([]),projects:Kt(Et({name:Pe().default(""),description:Pe().default("")})).default([]),skills:Kt(Et({name:Pe().default(""),years:Pe().default("")})).default([])});var To=u('<div class="space-y-6"><div class="space-y-2"><label for="name-input" class="text-sm font-medium">Full Name</label> <!></div> <div class="space-y-2"><label for="email-input" class="text-sm font-medium">Email</label> <!></div> <div class="space-y-2"><label for="phone-input" class="text-sm font-medium">Phone</label> <!></div></div>');function Co(t,a){ut(a,!1);let r=Ke(a,"data",12);Gt(()=>{console.log("ResumeHeaderForm mounted with data:",r())}),qe(()=>yt(r()),()=>{r()&&(r().name||r(r().name="",!0),r().email||r(r().email="",!0),r().phone||r(r().phone="",!0))}),Tt(),xt();var s=To(),c=n(s),f=l(n(c),2);et(f,{id:"name-input",type:"text",get value(){return r().name},set value(i){r(r().name=i,!0)},$$legacy:!0}),o(c);var y=l(c,2),m=l(n(y),2);et(m,{id:"email-input",type:"email",get value(){return r().email},set value(i){r(r().email=i,!0)},$$legacy:!0}),o(y);var v=l(y,2),U=l(n(v),2);et(U,{id:"phone-input",type:"text",get value(){return r().phone},set value(i){r(r().phone=i,!0)},$$legacy:!0}),o(v),o(s),d(t,s),ft()}async function ea(t,a){await a(),ur.success("Suggestions refreshed")}var Ao=u('<div class="flex items-center"><!> </div>'),Oo=u('<!> <button class="inline-flex h-8 w-8 items-center justify-center rounded-md p-0 text-gray-700 hover:bg-gray-100"><!> <span class="sr-only">Refresh</span></button>',1),zo=u('<div class="flex items-center justify-center py-6"><div class="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div> <span class="ml-2">Generating suggestions...</span></div>'),Io=u('<div class="rounded-md bg-red-50 p-4 text-sm text-red-500"><p> </p> <button class="mt-2 inline-flex h-9 items-center justify-center rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100">Try Again</button></div>'),ko=u('<div class="py-4 text-center text-sm text-gray-500"><p>No suggestions available for this section.</p> <button class="mt-2 inline-flex h-9 items-center justify-center rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100">Generate Suggestions</button></div>'),Po=(t,a,r)=>a(e(r)),Ro=u('<button class="inline-flex h-7 w-7 items-center justify-center rounded-md p-0 text-green-600 hover:bg-gray-100" title="Apply this suggestion"><!> <span class="sr-only">Apply</span></button>'),Lo=u('<div class="rounded-md border p-3"><div class="mb-2 flex items-center justify-between"><!> <div class="flex space-x-2"><!> <button class="inline-flex h-7 w-7 items-center justify-center rounded-md p-0 text-blue-600 hover:bg-gray-100"><!> <span class="sr-only">Info</span></button></div></div> <div class="mt-2"><p class="text-sm"> </p></div></div>'),No=u('<div class="space-y-4"></div>'),jo=u("<!> <!>",1);function Fo(t,a){ut(a,!0);let r=St(ga([])),s=St(!1),c=St(null);Rr(()=>{a.resumeId&&a.section&&a.content&&f()});async function f(){_e(s,!0),_e(c,null);try{const i=await Ya(a.resumeId,a.section,a.content);_e(r,i,!0)}catch(i){_e(c,i instanceof Error?i.message:"Failed to load suggestions",!0),console.error("Error loading suggestions:",i)}finally{_e(s,!1)}}async function y(i){try{await qa(i.id)&&(_e(r,e(r).map(I=>I.id===i.id?{...I,applied:!0}:I),!0),a.onApplySuggestion(i.suggestion))}catch(K){console.error("Error applying suggestion:",K),ur.error("Failed to apply suggestion")}}function m(i){return i.split("_").map(K=>K.charAt(0).toUpperCase()+K.slice(1)).join(" ")}var v=Fe(),U=ge(v);Jt(U,()=>Ga,(i,K)=>{K(i,{class:"w-full",children:(I,z)=>{var E=jo(),G=ge(E);Jt(G,()=>Wa,(ae,S)=>{S(ae,{class:"flex flex-row items-center justify-between space-y-0 pb-2",children:(B,le)=>{var ce=Oo(),pe=ge(ce);Jt(pe,()=>Va,(Y,Q)=>{Q(Y,{class:"text-md font-medium",children:(fe,xe)=>{var ke=Ao(),ve=n(ke);ya(ve,{class:"mr-2 h-4 w-4 text-blue-500"});var ee=l(ve);o(ke),b(X=>D(ee,` AI Suggestions for ${X??""}`),[()=>m(a.section)]),d(fe,ke)},$$slots:{default:!0}})});var be=l(pe,2);be.__click=[ea,f];var Ae=n(be);Ka(Ae,{class:"h-4 w-4"}),Te(2),o(be),d(B,ce)},$$slots:{default:!0}})});var V=l(G,2);Jt(V,()=>Ua,(ae,S)=>{S(ae,{children:(B,le)=>{var ce=Fe(),pe=ge(ce);{var be=Y=>{var Q=zo();d(Y,Q)},Ae=(Y,Q)=>{{var fe=ke=>{var ve=Io(),ee=n(ve),X=n(ee);o(ee);var ie=l(ee,2);ie.__click=f,o(ve),b(()=>D(X,`Error: ${e(c)??""}`)),d(ke,ve)},xe=(ke,ve)=>{{var ee=ie=>{var we=ko(),Ie=l(n(we),2);Ie.__click=[ea,f],o(we),d(ie,we)},X=ie=>{var we=No();Se(we,21,()=>e(r),Ie=>Ie.id,(Ie,$e)=>{var ue=Lo(),W=n(ue),me=n(W);const ye=Ma(()=>e($e).applied?"outline":"default");Za(me,{get variant(){return e(ye)},class:"text-xs",children:(L,x)=>{Te();var N=Le();b(()=>D(N,e($e).applied?"Applied":"Suggestion")),d(L,N)},$$slots:{default:!0}});var Ee=l(me,2),De=n(Ee);{var oe=L=>{var x=Ro();x.__click=[Po,y,$e];var N=n(x);Ja(N,{class:"h-4 w-4"}),Te(2),o(x),d(L,x)};O(De,L=>{e($e).applied||L(oe)})}var k=l(De,2),re=n(k);Xa(re,{class:"h-4 w-4"}),Te(2),o(k),o(Ee),o(W);var te=l(W,2),de=n(te),j=n(de,!0);o(de),o(te),o(ue),b(()=>{je(k,"title",e($e).reasoning||"No reasoning provided"),D(j,e($e).suggestion)}),d(Ie,ue)}),o(we),d(ie,we)};O(ke,ie=>{e(r).length===0?ie(ee):ie(X,!1)},ve)}};O(Y,ke=>{e(c)?ke(fe):ke(xe,!1)},Q)}};O(pe,Y=>{e(s)?Y(be):Y(Ae,!1)})}d(B,ce)},$$slots:{default:!0}})}),d(I,E)},$$slots:{default:!0}})}),d(t,v),ft()}yr(["click"]);function Mo(t,a){e(a)&&e(a).chain().focus().toggleBold().run()}function Bo(t,a){e(a)&&e(a).chain().focus().toggleItalic().run()}function Ho(t,a){if(e(a)){const{from:r,to:s}=e(a).state.selection,c=e(a).state.doc.textBetween(r,s);c&&e(a).chain().focus().deleteSelection().insertContent(`<u>${c}</u>`).run()}}function Go(t,a){if(e(a)&&typeof window<"u"){const r=window.prompt("URL","https://");if(r){const{from:s,to:c}=e(a).state.selection,f=e(a).state.doc.textBetween(s,c);f&&e(a).chain().focus().deleteSelection().insertContent(`<a href="${r}" target="_blank">${f}</a>`).run()}}}function Uo(t,a){e(a)&&e(a).chain().focus().toggleBulletList().run()}function Wo(t,a){e(a)&&e(a).chain().focus().toggleOrderedList().run()}function Vo(t,a){e(a)&&e(a).chain().focus().toggleCodeBlock().run()}function Zo(t,a){e(a)&&e(a).chain().focus().undo().run()}function Yo(t,a){e(a)&&e(a).chain().focus().redo().run()}function qo(t,a){_e(a,!e(a))}function Ko(){typeof window<"u"&&window.alert("AI Settings feature would be implemented here")}var Xo=(t,a,r,s)=>{typeof window<"u"&&e(a)&&window.confirm("Reset the professional summary? This will clear all content.")&&(e(a).commands.setContent(""),_e(r,""),_e(s,0),console.log("Professional summary content reset"))},Jo=u('<div class="mb-2 rounded-md border border-zinc-700 bg-zinc-800 p-1"><div class="flex flex-wrap space-x-1"><button type="button" title="Bold" aria-label="Bold"><!></button> <button type="button" title="Italic" aria-label="Italic"><!></button> <button type="button" title="Underline" aria-label="Underline"><!></button> <button type="button" title="Link" aria-label="Link"><!></button> <div class="mx-1 h-6 border-r border-zinc-600"></div> <button type="button" title="Bullet List" aria-label="Bullet List"><!></button> <button type="button" title="Numbered List" aria-label="Numbered List"><!></button> <button type="button" title="Code Block" aria-label="Code Block"><!></button> <div class="mx-1 h-6 border-r border-zinc-600"></div> <button type="button" class="rounded p-1 hover:bg-zinc-700" title="Undo" aria-label="Undo"><!></button> <button type="button" class="rounded p-1 hover:bg-zinc-700" title="Redo" aria-label="Redo"><!></button></div></div>'),Qo=u('<div class="mt-4"><!></div>'),en=u(`<div class="space-y-2"><div class="flex items-center justify-between"><button class="rounded p-1 text-zinc-400 hover:text-zinc-200" title="Reset" aria-label="Reset"><!></button> <span class="text-xs text-zinc-400"> </span></div> <!> <div id="summary-editor" class="min-h-[200px] w-full rounded-md border border-zinc-700 bg-white p-4 text-black focus:outline-none dark:bg-zinc-900 dark:text-white"><!></div> <div class="mt-2 flex justify-between"><p class="text-xs text-zinc-400">Write a concise professional summary highlighting your key qualifications, experience, and
      career goals. Use bullet points for better readability.</p> <div class="flex space-x-2"><button type="button" class="flex items-center rounded bg-zinc-800 px-2 py-1 text-xs text-zinc-300 hover:bg-zinc-700" aria-label="AI Settings"><!> <span>AI Settings</span></button> <button type="button" class="flex items-center rounded bg-blue-600 px-2 py-1 text-xs text-white hover:bg-blue-700" aria-label="Rewrite with AI Suggestions"><!> <span>Rewrite with AI Suggestions</span></button></div></div> <!></div>`);function tn(t,a){var ve;ut(a,!0);const r=Ke(a,"data",7);let s=St(ga(((ve=r())==null?void 0:ve.content)||""));Rr(()=>{r()&&e(s)&&(console.log("SummaryForm: content changed to",e(s)),r().content=e(s))});let c=St(null),f,y=St(0),m=St(!1),v=St(!1),U=St("");Rr(()=>{if(typeof window<"u"){const X=window.location.pathname.match(/\/dashboard\/builder\/([^\/]+)/);X&&X[1]&&_e(U,X[1],!0)}});function i(ee){e(c)&&(e(c).commands.setContent(ee),_e(s,ee,!0),_e(v,!1))}Gt(async()=>{if(_e(m,typeof window<"u"),e(m))try{const{Editor:ee}=await kt(async()=>{const{Editor:W}=await import("./BGYDhraB.js").then(me=>me.o);return{Editor:W}},[],import.meta.url),{default:X}=await kt(async()=>{const{default:W}=await import("./CfBTQ9Yv.js");return{default:W}},__vite__mapDeps([0,1,2,3,4,5,6]),import.meta.url),{default:ie}=await kt(async()=>{const{default:W}=await import("./foSyZIKW.js");return{default:W}},__vite__mapDeps([3,1]),import.meta.url),{default:we}=await kt(async()=>{const{default:W}=await import("./Sg8aqnBg.js");return{default:W}},__vite__mapDeps([6,1]),import.meta.url),{default:Ie}=await kt(async()=>{const{default:W}=await import("./BMpib84U.js");return{default:W}},__vite__mapDeps([5,1]),import.meta.url),{default:$e}=await kt(async()=>{const{default:W}=await import("./BUfAIGxh.js");return{default:W}},__vite__mapDeps([2,1]),import.meta.url),{default:ue}=await kt(async()=>{const{default:W}=await import("./BLAa_HGQ.js");return{default:W}},__vite__mapDeps([4,1]),import.meta.url);_e(c,new ee({element:f,extensions:[X.configure({heading:{levels:[1,2]},bulletList:!1,orderedList:!1}),ie,we,Ie,$e,ue],content:e(s),onTransaction:()=>{_e(c,e(c),!0)},onUpdate:({editor:W})=>{_e(s,W.getHTML(),!0);try{const me=W.getText();_e(y,me?me.length:0,!0)}catch(me){console.error("Error calculating character count:",me),_e(y,e(s).length,!0)}r()&&(console.log("TipTap editor update: content changed to",e(s)),r().content=e(s))}}),!0)}catch(ee){console.error("Error initializing editor:",ee),_e(y,e(s).length,!0)}else _e(y,e(s).length,!0)}),pa(()=>{e(c)&&e(c).destroy()});var K=en(),I=n(K),z=n(I);z.__click=[Xo,c,s,y];var E=n(z);Qa(E,{size:16,"aria-hidden":"true"}),o(z);var G=l(z,2),V=n(G);o(G),o(I);var ae=l(I,2);{var S=ee=>{var X=Jo(),ie=n(X),we=n(ie);we.__click=[Mo,c];var Ie=n(we);eo(Ie,{size:16,"aria-hidden":"true"}),o(we);var $e=l(we,2);$e.__click=[Bo,c];var ue=n($e);to(ue,{size:16,"aria-hidden":"true"}),o($e);var W=l($e,2);W.__click=[Ho,c];var me=n(W);ro(me,{size:16,"aria-hidden":"true"}),o(W);var ye=l(W,2);ye.__click=[Go,c];var Ee=n(ye);co(Ee,{size:16,"aria-hidden":"true"}),o(ye);var De=l(ye,4);De.__click=[Uo,c];var oe=n(De);vo(oe,{size:16,"aria-hidden":"true"}),o(De);var k=l(De,2);k.__click=[Wo,c];var re=n(k);ao(re,{size:16,"aria-hidden":"true"}),o(k);var te=l(k,2);te.__click=[Vo,c];var de=n(te);uo(de,{size:16,"aria-hidden":"true"}),o(te);var j=l(te,4);j.__click=[Zo,c];var L=n(j);oo(L,{size:16,"aria-hidden":"true"}),o(j);var x=l(j,2);x.__click=[Yo,c];var N=n(x);no(N,{size:16,"aria-hidden":"true"}),o(x),o(ie),o(X),b((C,R,P,F,w,_,h)=>{vt(we,1,`rounded p-1 hover:bg-zinc-700 ${C??""}`),vt($e,1,`rounded p-1 hover:bg-zinc-700 ${R??""}`),vt(W,1,`rounded p-1 hover:bg-zinc-700 ${P??""}`),vt(ye,1,`rounded p-1 hover:bg-zinc-700 ${F??""}`),vt(De,1,`rounded p-1 hover:bg-zinc-700 ${w??""}`),vt(k,1,`rounded p-1 hover:bg-zinc-700 ${_??""}`),vt(te,1,`rounded p-1 hover:bg-zinc-700 ${h??""}`)},[()=>{var C;return(C=e(c))!=null&&C.isActive("bold")?"bg-zinc-700":""},()=>{var C;return(C=e(c))!=null&&C.isActive("italic")?"bg-zinc-700":""},()=>{var C;return(C=e(c))!=null&&C.isActive("underline")?"bg-zinc-700":""},()=>{var C;return(C=e(c))!=null&&C.isActive("link")?"bg-zinc-700":""},()=>{var C;return(C=e(c))!=null&&C.isActive("bulletList")?"bg-zinc-700":""},()=>{var C;return(C=e(c))!=null&&C.isActive("orderedList")?"bg-zinc-700":""},()=>{var C;return(C=e(c))!=null&&C.isActive("codeBlock")?"bg-zinc-700":""}]),d(ee,X)};O(ae,ee=>{e(m)&&ee(S)})}var B=l(ae,2),le=n(B);{var ce=ee=>{var X=Le();b(()=>D(X,e(s))),d(ee,X)};O(le,ee=>{e(m)||ee(ce)})}o(B),Ha(B,ee=>f=ee,()=>f);var pe=l(B,2),be=l(n(pe),2),Ae=n(be);Ae.__click=[Ko];var Y=n(Ae);lo(Y,{size:14,class:"mr-1","aria-hidden":"true"}),Te(2),o(Ae);var Q=l(Ae,2);Q.__click=[qo,v];var fe=n(Q);ya(fe,{size:14,class:"mr-1","aria-hidden":"true"}),Te(2),o(Q),o(be),o(pe);var xe=l(pe,2);{var ke=ee=>{var X=Qo(),ie=n(X);Fo(ie,{get resumeId(){return e(U)},section:"summary",get content(){return e(s)},onApplySuggestion:i}),o(X),d(ee,X)};O(xe,ee=>{e(v)&&e(U)&&ee(ke)})}o(K),b(()=>{D(V,`${e(y)??""}/1000 characters`),je(B,"contenteditable",e(m))}),d(t,K),ft()}yr(["click"]);var rn=u('<div class="grid grid-cols-1 gap-4 md:grid-cols-2"><div class="space-y-2"><label class="text-sm font-medium">School</label> <!></div> <div class="space-y-2"><label class="text-sm font-medium">Degree</label> <!></div> <div class="space-y-2"><label class="text-sm font-medium">Major</label> <!></div> <div class="space-y-2"><label class="text-sm font-medium">GPA</label> <!></div> <div class="space-y-2"><label class="text-sm font-medium">Start Date</label> <!></div> <div class="space-y-2"><label class="text-sm font-medium">End Date</label> <!></div></div>'),an=u('<div><span class="font-medium">School:</span> </div>'),on=u('<div><span class="font-medium">Degree:</span> </div>'),nn=u('<div><span class="font-medium">Major:</span> </div>'),sn=u('<div><span class="font-medium">GPA:</span> </div>'),ln=u('<div><span class="font-medium">Start Date:</span> </div>'),dn=u('<div><span class="font-medium">End Date:</span> </div>'),cn=u('<div class="grid grid-cols-1 gap-2 text-sm md:grid-cols-2"><!> <!> <!> <!> <!> <!></div>'),vn=u('<div class="education-entry rounded border border-zinc-700 bg-zinc-800 p-3"><div class="mb-3 flex items-center justify-between"><h4 class="font-medium"> </h4> <div class="flex gap-2"><!> <!></div></div> <!></div>'),un=u('<div class="rounded border border-dashed border-zinc-700 p-6 text-center text-zinc-500"><p>No education entries added yet. Click "Add Education" to get started.</p></div>'),fn=u('<div class="space-y-4"><div class="flex items-center justify-between"><div class="flex items-center"><div class="mr-2 cursor-move p-1 text-zinc-400 hover:text-zinc-200" title="Drag to reorder" aria-label="Drag to reorder" role="button" tabindex="0" draggable="true"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="9" cy="5" r="1"></circle><circle cx="9" cy="12" r="1"></circle><circle cx="9" cy="19" r="1"></circle><circle cx="15" cy="5" r="1"></circle><circle cx="15" cy="12" r="1"></circle><circle cx="15" cy="19" r="1"></circle></svg></div> <h3 class="text-lg font-semibold">Education</h3></div> <!></div> <!></div>');function mn(t,a){ut(a,!1);let r=Ke(a,"data",28,()=>({education:[]})),s=ot(null);function c(){const E={school:"",degree:"",major:"",gpa:"",startDate:"",endDate:""};r(r().education=[...r().education,E],!0),_e(s,r().education.length-1)}function f(E){r(r().education=r().education.filter((G,V)=>V!==E),!0),e(s)===E?_e(s,null):e(s)!==null&&e(s)>E&&_a(s,-1)}Gt(()=>{(!r().education||r().education.length===0)&&c()}),qe(()=>yt(r()),()=>{r().education||r(r().education=[],!0)}),Tt(),xt();var y=fn(),m=n(y),v=n(m),U=n(v);Te(2),o(v);var i=l(v,2);at(i,{variant:"outline",size:"sm",onclick:c,class:"text-sm",children:(E,G)=>{Te();var V=Le("Add Education");d(E,V)},$$slots:{default:!0}}),o(m);var K=l(m,2);{var I=E=>{var G=Fe(),V=ge(G);Se(V,1,()=>r().education,ze,(ae,S,B)=>{var le=vn(),ce=n(le),pe=n(ce),be=n(pe,!0);o(pe);var Ae=l(pe,2),Y=n(Ae);at(Y,{variant:"ghost",size:"sm",onclick:()=>_e(s,e(s)===B?null:B),class:"h-7 text-xs",children:(ve,ee)=>{Te();var X=Le();b(()=>D(X,e(s)===B?"Done":"Edit")),d(ve,X)},$$slots:{default:!0}});var Q=l(Y,2);at(Q,{variant:"ghost",size:"sm",onclick:()=>f(B),class:"h-7 text-xs text-red-400 hover:text-red-300",children:(ve,ee)=>{Te();var X=Le("Remove");d(ve,X)},$$slots:{default:!0}}),o(Ae),o(ce);var fe=l(ce,2);{var xe=ve=>{var ee=rn(),X=n(ee),ie=n(X);je(ie,"for",`school-${B}`);var we=l(ie,2);et(we,{id:`school-${B}`,type:"text",placeholder:"e.g., Harvard University",get value(){return e(S).school},set value(x){e(S).school=x,Ue(()=>r())},$$legacy:!0}),o(X);var Ie=l(X,2),$e=n(Ie);je($e,"for",`degree-${B}`);var ue=l($e,2);et(ue,{id:`degree-${B}`,type:"text",placeholder:"e.g., Bachelor of Science",get value(){return e(S).degree},set value(x){e(S).degree=x,Ue(()=>r())},$$legacy:!0}),o(Ie);var W=l(Ie,2),me=n(W);je(me,"for",`major-${B}`);var ye=l(me,2);et(ye,{id:`major-${B}`,type:"text",placeholder:"e.g., Computer Science",get value(){return e(S).major},set value(x){e(S).major=x,Ue(()=>r())},$$legacy:!0}),o(W);var Ee=l(W,2),De=n(Ee);je(De,"for",`gpa-${B}`);var oe=l(De,2);et(oe,{id:`gpa-${B}`,type:"text",placeholder:"e.g., 3.8",get value(){return e(S).gpa},set value(x){e(S).gpa=x,Ue(()=>r())},$$legacy:!0}),o(Ee);var k=l(Ee,2),re=n(k);je(re,"for",`start-date-${B}`);var te=l(re,2);et(te,{id:`start-date-${B}`,type:"text",placeholder:"e.g., Sep 2018",get value(){return e(S).startDate},set value(x){e(S).startDate=x,Ue(()=>r())},$$legacy:!0}),o(k);var de=l(k,2),j=n(de);je(j,"for",`end-date-${B}`);var L=l(j,2);et(L,{id:`end-date-${B}`,type:"text",placeholder:"e.g., May 2022 or Present",get value(){return e(S).endDate},set value(x){e(S).endDate=x,Ue(()=>r())},$$legacy:!0}),o(de),o(ee),d(ve,ee)},ke=ve=>{var ee=cn(),X=n(ee);{var ie=k=>{var re=an(),te=l(n(re));o(re),b(()=>D(te,` ${e(S).school??""}`)),d(k,re)};O(X,k=>{e(S).school&&k(ie)})}var we=l(X,2);{var Ie=k=>{var re=on(),te=l(n(re));o(re),b(()=>D(te,` ${e(S).degree??""}`)),d(k,re)};O(we,k=>{e(S).degree&&k(Ie)})}var $e=l(we,2);{var ue=k=>{var re=nn(),te=l(n(re));o(re),b(()=>D(te,` ${e(S).major??""}`)),d(k,re)};O($e,k=>{e(S).major&&k(ue)})}var W=l($e,2);{var me=k=>{var re=sn(),te=l(n(re));o(re),b(()=>D(te,` ${e(S).gpa??""}`)),d(k,re)};O(W,k=>{e(S).gpa&&k(me)})}var ye=l(W,2);{var Ee=k=>{var re=ln(),te=l(n(re));o(re),b(()=>D(te,` ${e(S).startDate??""}`)),d(k,re)};O(ye,k=>{e(S).startDate&&k(Ee)})}var De=l(ye,2);{var oe=k=>{var re=dn(),te=l(n(re));o(re),b(()=>D(te,` ${e(S).endDate??""}`)),d(k,re)};O(De,k=>{e(S).endDate&&k(oe)})}o(ee),d(ve,ee)};O(fe,ve=>{e(s)===B?ve(xe):ve(ke,!1)})}o(le),b(()=>D(be,e(S).school?e(S).school:"New Education Entry")),d(ae,le)}),d(E,G)},z=E=>{var G=un();d(E,G)};O(K,E=>{r().education&&r().education.length>0?E(I):E(z,!1)})}o(y),it("dragstart",U,E=>{typeof window<"u"&&(E.dataTransfer.setData("text/plain","education-section"),E.dataTransfer.effectAllowed="move",console.log("Drag started for education section"))}),d(t,y),ft()}var pn=u('<div class="certification-entry space-y-2 rounded border border-zinc-700 bg-zinc-800 p-3"><div class="flex items-center justify-between"><label class="text-sm font-medium">Certification Details</label> <!></div> <!></div>'),gn=u('<div class="rounded border border-dashed border-zinc-700 p-6 text-center text-zinc-500"><p>No certifications added yet. Click "Add Certification" to get started.</p></div>'),_n=u('<div class="space-y-4"><div class="flex items-center justify-between"><div class="flex items-center"><div class="mr-2 cursor-move p-1 text-zinc-400 hover:text-zinc-200" title="Drag to reorder" aria-label="Drag to reorder" role="button" tabindex="0" draggable="true"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="9" cy="5" r="1"></circle><circle cx="9" cy="12" r="1"></circle><circle cx="9" cy="19" r="1"></circle><circle cx="15" cy="5" r="1"></circle><circle cx="15" cy="12" r="1"></circle><circle cx="15" cy="19" r="1"></circle></svg></div> <h3 class="text-lg font-semibold">Certifications & Licenses</h3></div> <!></div> <!></div>');function hn(t,a){ut(a,!1);let r=Ke(a,"data",28,()=>({certifications:[]}));function s(){const E={description:""},G=[...r().certifications,E];if(r(r().certifications=G,!0),typeof window<"u"){const V=new CustomEvent("form:update",{detail:{field:"certifications",value:G},bubbles:!0});window.dispatchEvent(V)}}function c(E){const G=r().certifications.filter((V,ae)=>ae!==E);if(r(r().certifications=G,!0),typeof window<"u"){const V=new CustomEvent("form:update",{detail:{field:"certifications",value:G},bubbles:!0});window.dispatchEvent(V)}}function f(E,G){if(r(r().certifications[E].description=G,!0),typeof window<"u"){const V=new CustomEvent("form:update",{detail:{field:"certifications",value:r().certifications},bubbles:!0});window.dispatchEvent(V)}}qe(()=>yt(r()),()=>{r().certifications||r(r().certifications=[],!0)}),Tt(),xt();var y=_n(),m=n(y),v=n(m),U=n(v);Te(2),o(v);var i=l(v,2);at(i,{variant:"outline",size:"sm",onclick:s,class:"text-sm",children:(E,G)=>{Te();var V=Le("Add Certification");d(E,V)},$$slots:{default:!0}}),o(m);var K=l(m,2);{var I=E=>{var G=Fe(),V=ge(G);Se(V,1,()=>r().certifications,ze,(ae,S,B)=>{var le=pn(),ce=n(le),pe=n(ce);je(pe,"for",`certification-${B}`);var be=l(pe,2);at(be,{variant:"ghost",size:"sm",onclick:()=>c(B),class:"h-7 text-xs text-red-400 hover:text-red-300",children:(Y,Q)=>{Te();var fe=Le("Remove");d(Y,fe)},$$slots:{default:!0}}),o(ce);var Ae=l(ce,2);Wr(Ae,{id:`certification-${B}`,get value(){return e(S).description},placeholder:"e.g., AWS Certified Solutions Architect, 2023",class:"min-h-[80px] w-full",$$events:{input:Y=>f(B,Y.target.value)}}),o(le),d(ae,le)}),d(E,G)},z=E=>{var G=gn();d(E,G)};O(K,E=>{r().certifications&&r().certifications.length>0?E(I):E(z,!1)})}o(y),it("dragstart",U,E=>{typeof window<"u"&&(E.dataTransfer.setData("text/plain","certifications-section"),E.dataTransfer.effectAllowed="move",console.log("Drag started for certifications section"))}),d(t,y),ft()}var bn=u('<div class="grid grid-cols-1 gap-4 md:grid-cols-2"><div class="space-y-2"><label class="text-sm font-medium">Company</label> <!></div> <div class="space-y-2"><label class="text-sm font-medium">Job Title</label> <!></div> <div class="space-y-2"><label class="text-sm font-medium">Start Date</label> <!></div> <div class="space-y-2"><label class="text-sm font-medium">End Date</label> <!></div> <div class="space-y-2 md:col-span-2"><label class="text-sm font-medium">Description</label> <!></div></div>'),yn=u('<div><span class="font-medium">Company:</span> </div>'),xn=u('<div><span class="font-medium">Job Title:</span> </div>'),wn=u('<div><span class="font-medium">Duration:</span> </div>'),Dn=u('<div class="mt-1"><span class="font-medium">Description:</span> <p class="mt-1 whitespace-pre-line"> </p></div>'),$n=u('<div class="grid grid-cols-1 gap-2 text-sm"><!> <!> <!> <!></div>'),En=u('<div class="experience-entry rounded border border-zinc-700 bg-zinc-800 p-3"><div class="mb-3 flex items-center justify-between"><h4 class="font-medium"> </h4> <div class="flex gap-2"><!> <!></div></div> <!></div>'),Sn=u('<div class="rounded border border-dashed border-zinc-700 p-6 text-center text-zinc-500"><p>No experience entries added yet. Click "Add Experience" to get started.</p></div>'),Tn=u('<div class="space-y-4"><div class="flex items-center justify-between"><div class="flex items-center"><div class="mr-2 cursor-move p-1 text-zinc-400 hover:text-zinc-200" title="Drag to reorder" aria-label="Drag to reorder" role="button" tabindex="0" draggable="true"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="9" cy="5" r="1"></circle><circle cx="9" cy="12" r="1"></circle><circle cx="9" cy="19" r="1"></circle><circle cx="15" cy="5" r="1"></circle><circle cx="15" cy="12" r="1"></circle><circle cx="15" cy="19" r="1"></circle></svg></div> <h3 class="text-lg font-semibold">Professional Experience</h3></div> <!></div> <!></div>');function Cn(t,a){ut(a,!1);let r=Ke(a,"data",28,()=>({experience:[]})),s=ot(null);function c(){const E={company:"",jobTitle:"",startDate:"",endDate:"",description:""};r(r().experience=[...r().experience,E],!0),_e(s,r().experience.length-1)}function f(E){r(r().experience=r().experience.filter((G,V)=>V!==E),!0),e(s)===E?_e(s,null):e(s)!==null&&e(s)>E&&_a(s,-1)}Gt(()=>{(!r().experience||r().experience.length===0)&&c()}),qe(()=>yt(r()),()=>{r().experience||r(r().experience=[],!0)}),Tt(),xt();var y=Tn(),m=n(y),v=n(m),U=n(v);Te(2),o(v);var i=l(v,2);at(i,{variant:"outline",size:"sm",onclick:c,class:"text-sm",children:(E,G)=>{Te();var V=Le("Add Experience");d(E,V)},$$slots:{default:!0}}),o(m);var K=l(m,2);{var I=E=>{var G=Fe(),V=ge(G);Se(V,1,()=>r().experience,ze,(ae,S,B)=>{var le=En(),ce=n(le),pe=n(ce),be=n(pe,!0);o(pe);var Ae=l(pe,2),Y=n(Ae);at(Y,{variant:"ghost",size:"sm",onclick:()=>_e(s,e(s)===B?null:B),class:"h-7 text-xs",children:(ve,ee)=>{Te();var X=Le();b(()=>D(X,e(s)===B?"Done":"Edit")),d(ve,X)},$$slots:{default:!0}});var Q=l(Y,2);at(Q,{variant:"ghost",size:"sm",onclick:()=>f(B),class:"h-7 text-xs text-red-400 hover:text-red-300",children:(ve,ee)=>{Te();var X=Le("Remove");d(ve,X)},$$slots:{default:!0}}),o(Ae),o(ce);var fe=l(ce,2);{var xe=ve=>{var ee=bn(),X=n(ee),ie=n(X);je(ie,"for",`company-${B}`);var we=l(ie,2);et(we,{id:`company-${B}`,type:"text",placeholder:"e.g., Google Inc.",get value(){return e(S).company},set value(de){e(S).company=de,Ue(()=>r())},$$legacy:!0}),o(X);var Ie=l(X,2),$e=n(Ie);je($e,"for",`job-title-${B}`);var ue=l($e,2);et(ue,{id:`job-title-${B}`,type:"text",placeholder:"e.g., Software Engineer",get value(){return e(S).jobTitle},set value(de){e(S).jobTitle=de,Ue(()=>r())},$$legacy:!0}),o(Ie);var W=l(Ie,2),me=n(W);je(me,"for",`start-date-${B}`);var ye=l(me,2);et(ye,{id:`start-date-${B}`,type:"text",placeholder:"e.g., Jan 2020",get value(){return e(S).startDate},set value(de){e(S).startDate=de,Ue(()=>r())},$$legacy:!0}),o(W);var Ee=l(W,2),De=n(Ee);je(De,"for",`end-date-${B}`);var oe=l(De,2);et(oe,{id:`end-date-${B}`,type:"text",placeholder:"e.g., Present",get value(){return e(S).endDate},set value(de){e(S).endDate=de,Ue(()=>r())},$$legacy:!0}),o(Ee);var k=l(Ee,2),re=n(k);je(re,"for",`description-${B}`);var te=l(re,2);Wr(te,{id:`description-${B}`,placeholder:"Describe your responsibilities and achievements",class:"min-h-[120px]",get value(){return e(S).description},set value(de){e(S).description=de,Ue(()=>r())},$$legacy:!0}),o(k),o(ee),d(ve,ee)},ke=ve=>{var ee=$n(),X=n(ee);{var ie=ye=>{var Ee=yn(),De=l(n(Ee));o(Ee),b(()=>D(De,` ${e(S).company??""}`)),d(ye,Ee)};O(X,ye=>{e(S).company&&ye(ie)})}var we=l(X,2);{var Ie=ye=>{var Ee=xn(),De=l(n(Ee));o(Ee),b(()=>D(De,` ${e(S).jobTitle??""}`)),d(ye,Ee)};O(we,ye=>{e(S).jobTitle&&ye(Ie)})}var $e=l(we,2);{var ue=ye=>{var Ee=wn(),De=l(n(Ee));o(Ee),b(()=>D(De,` ${(e(S).startDate||"")??""}${e(S).startDate&&e(S).endDate?" - ":""}${(e(S).endDate||"")??""}`)),d(ye,Ee)};O($e,ye=>{(e(S).startDate||e(S).endDate)&&ye(ue)})}var W=l($e,2);{var me=ye=>{var Ee=Dn(),De=l(n(Ee),2),oe=n(De,!0);o(De),o(Ee),b(()=>D(oe,e(S).description)),d(ye,Ee)};O(W,ye=>{e(S).description&&ye(me)})}o(ee),d(ve,ee)};O(fe,ve=>{e(s)===B?ve(xe):ve(ke,!1)})}o(le),b(()=>D(be,e(S).company?`${e(S).company}${e(S).jobTitle?` - ${e(S).jobTitle}`:""}`:"New Experience Entry")),d(ae,le)}),d(E,G)},z=E=>{var G=Sn();d(E,G)};O(K,E=>{r().experience&&r().experience.length>0?E(I):E(z,!1)})}o(y),it("dragstart",U,E=>{typeof window<"u"&&(E.dataTransfer.setData("text/plain","experience-section"),E.dataTransfer.effectAllowed="move",console.log("Drag started for experience section"))}),d(t,y),ft()}var An=u('<div class="project-entry space-y-2 rounded border border-zinc-700 bg-zinc-800 p-3"><div class="flex items-center justify-between"><label class="text-sm font-medium">Project Name</label> <!></div> <!> <label class="text-sm font-medium">Description</label> <!></div>'),On=u('<div class="rounded border border-dashed border-zinc-700 p-6 text-center text-zinc-500"><p>No projects added yet. Click "Add Project" to get started.</p></div>'),zn=u('<div class="space-y-4"><div class="flex items-center justify-between"><div class="flex items-center"><div class="mr-2 cursor-move p-1 text-zinc-400 hover:text-zinc-200" title="Drag to reorder" aria-label="Drag to reorder" role="button" tabindex="0" draggable="true"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="9" cy="5" r="1"></circle><circle cx="9" cy="12" r="1"></circle><circle cx="9" cy="19" r="1"></circle><circle cx="15" cy="5" r="1"></circle><circle cx="15" cy="12" r="1"></circle><circle cx="15" cy="19" r="1"></circle></svg></div> <h3 class="text-lg font-semibold">Projects & Outside Experience</h3></div> <!></div> <!></div>');function In(t,a){ut(a,!1);let r=Ke(a,"data",28,()=>({projects:[]}));function s(){const z={name:"",description:""};r(r().projects=[...r().projects,z],!0)}function c(z){r(r().projects=r().projects.filter((E,G)=>G!==z),!0)}qe(()=>yt(r()),()=>{r().projects||r(r().projects=[],!0)}),Tt(),xt();var f=zn(),y=n(f),m=n(y),v=n(m);Te(2),o(m);var U=l(m,2);at(U,{variant:"outline",size:"sm",onclick:s,class:"text-sm",children:(z,E)=>{Te();var G=Le("Add Project");d(z,G)},$$slots:{default:!0}}),o(y);var i=l(y,2);{var K=z=>{var E=Fe(),G=ge(E);Se(G,1,()=>r().projects,ze,(V,ae,S)=>{var B=An(),le=n(B),ce=n(le);je(ce,"for",`project-name-${S}`);var pe=l(ce,2);at(pe,{variant:"ghost",size:"sm",onclick:()=>c(S),class:"h-7 text-xs text-red-400 hover:text-red-300",children:(Q,fe)=>{Te();var xe=Le("Remove");d(Q,xe)},$$slots:{default:!0}}),o(le);var be=l(le,2);et(be,{id:`project-name-${S}`,type:"text",placeholder:"e.g., Personal Website, Open Source Contribution, etc.",class:"w-full",get value(){return e(ae).name},set value(Q){e(ae).name=Q,Ue(()=>r())},$$legacy:!0});var Ae=l(be,2);je(Ae,"for",`project-description-${S}`);var Y=l(Ae,2);Wr(Y,{id:`project-description-${S}`,placeholder:"Describe the project, your role, technologies used, and outcomes.",class:"min-h-[100px] w-full",get value(){return e(ae).description},set value(Q){e(ae).description=Q,Ue(()=>r())},$$legacy:!0}),o(B),d(V,B)}),d(z,E)},I=z=>{var E=On();d(z,E)};O(i,z=>{r().projects&&r().projects.length>0?z(K):z(I,!1)})}o(f),it("dragstart",v,z=>{typeof window<"u"&&(z.dataTransfer.setData("text/plain","projects-section"),z.dataTransfer.effectAllowed="move",console.log("Drag started for projects section"))}),d(t,f),ft()}var kn=u('<div class="skill-entry space-y-2 rounded border border-zinc-700 bg-zinc-800 p-3"><div class="flex items-center justify-between"><label class="text-sm font-medium">Skill Name</label> <!></div> <!> <label class="text-sm font-medium">Years of Experience</label> <!></div>'),Pn=u('<div class="rounded border border-dashed border-zinc-700 p-6 text-center text-zinc-500"><p>No skills added yet. Click "Add Skill" to get started.</p></div>'),Rn=u('<div class="space-y-4"><div class="flex items-center justify-between"><div class="flex items-center"><div class="mr-2 cursor-move p-1 text-zinc-400 hover:text-zinc-200" title="Drag to reorder" aria-label="Drag to reorder" role="button" tabindex="0" draggable="true"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><circle cx="9" cy="5" r="1"></circle><circle cx="9" cy="12" r="1"></circle><circle cx="9" cy="19" r="1"></circle><circle cx="15" cy="5" r="1"></circle><circle cx="15" cy="12" r="1"></circle><circle cx="15" cy="19" r="1"></circle></svg></div> <h3 class="text-lg font-semibold">Skills & Interests</h3></div> <!></div> <!></div>');function Ln(t,a){ut(a,!1);let r=Ke(a,"data",28,()=>({skills:[]}));function s(){r(r().skills=[...r().skills,{name:"",years:""}],!0),console.log("Added skill, updated data:",r())}function c(z){r(r().skills=r().skills.filter((E,G)=>G!==z),!0),console.log("Removed skill, updated data:",r())}qe(()=>yt(r()),()=>{r().skills||r(r().skills=[],!0)}),qe(()=>yt(r()),()=>{r()&&r().skills&&console.log("Skills data updated:",r().skills)}),Tt(),xt();var f=Rn(),y=n(f),m=n(y),v=n(m);Te(2),o(m);var U=l(m,2);at(U,{variant:"outline",size:"sm",onclick:s,class:"text-sm",children:(z,E)=>{Te();var G=Le("Add Skill");d(z,G)},$$slots:{default:!0}}),o(y);var i=l(y,2);{var K=z=>{var E=Fe(),G=ge(E);Se(G,1,()=>r().skills,ze,(V,ae,S)=>{var B=kn(),le=n(B),ce=n(le);je(ce,"for",`skill-name-${S}`);var pe=l(ce,2);at(pe,{variant:"ghost",size:"sm",onclick:()=>c(S),class:"h-7 text-xs text-red-400 hover:text-red-300",children:(Q,fe)=>{Te();var xe=Le("Remove");d(Q,xe)},$$slots:{default:!0}}),o(le);var be=l(le,2);et(be,{id:`skill-name-${S}`,type:"text",placeholder:"e.g., JavaScript, Project Management, etc.",class:"w-full",get value(){return e(ae).name},set value(Q){e(ae).name=Q,Ue(()=>r())},$$legacy:!0});var Ae=l(be,2);je(Ae,"for",`skill-years-${S}`);var Y=l(Ae,2);et(Y,{id:`skill-years-${S}`,type:"text",placeholder:"e.g., 3, 5+, etc.",class:"w-full",get value(){return e(ae).years},set value(Q){e(ae).years=Q,Ue(()=>r())},$$legacy:!0}),o(B),d(V,B)}),d(z,E)},I=z=>{var E=Pn();d(z,E)};O(i,z=>{r().skills&&r().skills.length>0?z(K):z(I,!1)})}o(f),it("dragstart",v,z=>{typeof window<"u"&&(z.dataTransfer.setData("text/plain","skills-section"),z.dataTransfer.effectAllowed="move",console.log("Drag started for skills section"))}),d(t,f),ft()}function ta(t,a){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);a&&(s=s.filter(function(c){return Object.getOwnPropertyDescriptor(t,c).enumerable})),r.push.apply(r,s)}return r}function fr(t){for(var a=1;a<arguments.length;a++){var r=arguments[a]!=null?arguments[a]:{};a%2?ta(Object(r),!0).forEach(function(s){Vt(t,s,r[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ta(Object(r)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(r,s))})}return t}function Qt(t){"@babel/helpers - typeof";return Qt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},Qt(t)}function Vt(t,a,r){return a in t?Object.defineProperty(t,a,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[a]=r,t}function Nn(t,a){if(t==null)return{};var r={},s=Object.keys(t),c,f;for(f=0;f<s.length;f++)c=s[f],!(a.indexOf(c)>=0)&&(r[c]=t[c]);return r}function jn(t,a){if(t==null)return{};var r=Nn(t,a),s,c;if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(t);for(c=0;c<f.length;c++)s=f[c],!(a.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(t,s)&&(r[s]=t[s])}return r}function Fn(t,a){return Bn(t)||Gn(t,a)||Vr(t,a)||Wn()}function pt(t){return Mn(t)||Hn(t)||Vr(t)||Un()}function Mn(t){if(Array.isArray(t))return Nr(t)}function Bn(t){if(Array.isArray(t))return t}function Hn(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Gn(t,a){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var s=[],c=!0,f=!1,y,m;try{for(r=r.call(t);!(c=(y=r.next()).done)&&(s.push(y.value),!(a&&s.length===a));c=!0);}catch(v){f=!0,m=v}finally{try{!c&&r.return!=null&&r.return()}finally{if(f)throw m}}return s}}function Vr(t,a){if(t){if(typeof t=="string")return Nr(t,a);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Nr(t,a)}}function Nr(t,a){(a==null||a>t.length)&&(a=t.length);for(var r=0,s=new Array(a);r<a;r++)s[r]=t[r];return s}function Un(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Wn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nr(t,a){var r=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=Vr(t))||a){r&&(t=r);var s=0,c=function(){};return{s:c,n:function(){return s>=t.length?{done:!0}:{done:!1,value:t[s++]}},e:function(v){throw v},f:c}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var f=!0,y=!1,m;return{s:function(){r=r.call(t)},n:function(){var v=r.next();return f=v.done,v},e:function(v){y=!0,m=v},f:function(){try{!f&&r.return!=null&&r.return()}finally{if(y)throw m}}}}var Vn="finalize",Zn="consider";function Zt(t,a,r){t.dispatchEvent(new CustomEvent(Vn,{detail:{items:a,info:r}}))}function It(t,a,r){t.dispatchEvent(new CustomEvent(Zn,{detail:{items:a,info:r}}))}var wr="draggedEntered",ir="draggedLeft",Dr="draggedOverIndex",Zr="draggedLeftDocument",mr={LEFT_FOR_ANOTHER:"leftForAnother",OUTSIDE_OF_ANY:"outsideOfAny"};function Yn(t,a,r){t.dispatchEvent(new CustomEvent(wr,{detail:{indexObj:a,draggedEl:r}}))}function qn(t,a,r){t.dispatchEvent(new CustomEvent(ir,{detail:{draggedEl:a,type:mr.LEFT_FOR_ANOTHER,theOtherDz:r}}))}function Kn(t,a){t.dispatchEvent(new CustomEvent(ir,{detail:{draggedEl:a,type:mr.OUTSIDE_OF_ANY}}))}function Xn(t,a,r){t.dispatchEvent(new CustomEvent(Dr,{detail:{indexObj:a,draggedEl:r}}))}function Jn(t){window.dispatchEvent(new CustomEvent(Zr,{detail:{draggedEl:t}}))}var nt={DRAG_STARTED:"dragStarted",DRAGGED_ENTERED:wr,DRAGGED_ENTERED_ANOTHER:"dragEnteredAnother",DRAGGED_OVER_INDEX:Dr,DRAGGED_LEFT:ir,DRAGGED_LEFT_ALL:"draggedLeftAll",DROPPED_INTO_ZONE:"droppedIntoZone",DROPPED_INTO_ANOTHER:"droppedIntoAnother",DROPPED_OUTSIDE_OF_ANY:"droppedOutsideOfAny",DRAG_STOPPED:"dragStopped"},lt={POINTER:"pointer",KEYBOARD:"keyboard"},$r="isDndShadowItem",Er="data-is-dnd-shadow-item-internal",Qn="data-is-dnd-shadow-item-hint",ei="id:dnd-shadow-placeholder-0000",ti="dnd-action-dragged-el",Xe="id",jr=0;function $a(){jr++}function Ea(){if(jr===0)throw new Error("Bug! trying to decrement when there are no dropzones");jr--}var Yr=typeof window>"u";function Fr(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,r,s=a?ni(t):t.getBoundingClientRect(),c=getComputedStyle(t),f=c.transform;if(f){var y,m,v,U;if(f.startsWith("matrix3d("))r=f.slice(9,-1).split(/, /),y=+r[0],m=+r[5],v=+r[12],U=+r[13];else if(f.startsWith("matrix("))r=f.slice(7,-1).split(/, /),y=+r[0],m=+r[3],v=+r[4],U=+r[5];else return s;var i=c.transformOrigin,K=s.x-v-(1-y)*parseFloat(i),I=s.y-U-(1-m)*parseFloat(i.slice(i.indexOf(" ")+1)),z=y?s.width/y:t.offsetWidth,E=m?s.height/m:t.offsetHeight;return{x:K,y:I,width:z,height:E,top:I,right:K+z,bottom:I+E,left:K}}else return s}function Sa(t){var a=Fr(t);return{top:a.top+window.scrollY,bottom:a.bottom+window.scrollY,left:a.left+window.scrollX,right:a.right+window.scrollX}}function Ta(t){var a=t.getBoundingClientRect();return{top:a.top+window.scrollY,bottom:a.bottom+window.scrollY,left:a.left+window.scrollX,right:a.right+window.scrollX}}function Ca(t){return{x:(t.left+t.right)/2,y:(t.top+t.bottom)/2}}function ri(t,a){return Math.sqrt(Math.pow(t.x-a.x,2)+Math.pow(t.y-a.y,2))}function Sr(t,a){return t.y<=a.bottom&&t.y>=a.top&&t.x>=a.left&&t.x<=a.right}function rr(t){return Ca(Ta(t))}function ra(t,a){var r=rr(t),s=Sa(a);return Sr(r,s)}function ai(t,a){var r=rr(t),s=rr(a);return ri(r,s)}function oi(t){var a=Ta(t);return a.right<0||a.left>document.documentElement.scrollWidth||a.bottom<0||a.top>document.documentElement.scrollHeight}function ni(t){for(var a=t.getBoundingClientRect(),r={top:a.top,bottom:a.bottom,left:a.left,right:a.right},s=t.parentElement;s&&s!==document.body;){var c=s.getBoundingClientRect(),f=window.getComputedStyle(s).overflowY,y=window.getComputedStyle(s).overflowX,m=f==="scroll"||f==="auto",v=y==="scroll"||y==="auto";m&&(r.top=Math.max(r.top,c.top),r.bottom=Math.min(r.bottom,c.bottom)),v&&(r.left=Math.max(r.left,c.left),r.right=Math.min(r.right,c.right)),s=s.parentElement}return r.top=Math.max(r.top,0),r.bottom=Math.min(r.bottom,window.innerHeight),r.left=Math.max(r.left,0),r.right=Math.min(r.right,window.innerWidth),{top:r.top,bottom:r.bottom,left:r.left,right:r.right,width:Math.max(0,r.right-r.left),height:Math.max(0,r.bottom-r.top)}}var Ut;function qr(){Ut=new Map}qr();function ii(t){var a=Array.from(t.children).findIndex(function(r){return r.getAttribute(Er)});if(a>=0)return Ut.has(t)||Ut.set(t,new Map),Ut.get(t).set(a,Sa(t.children[a])),a}function si(t,a){if(!ra(t,a))return null;var r=a.children;if(r.length===0)return{index:0,isProximityBased:!0};for(var s=ii(a),c=0;c<r.length;c++)if(ra(t,r[c])){var f=Ut.has(a)&&Ut.get(a).get(c);return f&&!Sr(rr(t),f)?{index:s,isProximityBased:!1}:{index:c,isProximityBased:!1}}for(var y=Number.MAX_VALUE,m=void 0,v=0;v<r.length;v++){var U=ai(t,r[v]);U<y&&(y=U,m=v)}return{index:m,isProximityBased:!0}}function lr(t){return JSON.stringify(t,null,2)}function pr(t){if(!t)throw new Error("cannot get depth of a falsy node");return Aa(t,0)}function Aa(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return t.parentElement?Aa(t.parentElement,a+1):a-1}function li(t,a){if(Object.keys(t).length!==Object.keys(a).length)return!1;for(var r in t)if(!{}.hasOwnProperty.call(a,r)||a[r]!==t[r])return!1;return!0}function di(t,a){if(t.length!==a.length)return!1;for(var r=0;r<t.length;r++)if(t[r]!==a[r])return!1;return!0}var ci=200,aa=10,Mr;function vi(t,a){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:ci,s=arguments.length>3?arguments[3]:void 0,c,f,y=!1,m,v=Array.from(a).sort(function(i,K){return pr(K)-pr(i)});function U(){var i=rr(t),K=s.multiScrollIfNeeded();if(!K&&m&&Math.abs(m.x-i.x)<aa&&Math.abs(m.y-i.y)<aa){Mr=window.setTimeout(U,r);return}if(oi(t)){Jn(t);return}m=i;var I=!1,z=nr(v),E;try{for(z.s();!(E=z.n()).done;){var G=E.value;K&&qr();var V=si(t,G);if(V!==null){var ae=V.index;I=!0,G!==c?(c&&qn(c,t,G),Yn(G,V,t),c=G):ae!==f&&(Xn(G,V,t),f=ae);break}}}catch(S){z.e(S)}finally{z.f()}!I&&y&&c?(Kn(c,t),c=void 0,f=void 0,y=!1):y=!0,Mr=window.setTimeout(U,r)}U()}function ui(){clearTimeout(Mr),qr()}var Xt=30;function fi(){var t;function a(){t={directionObj:void 0,stepPx:0}}a();function r(f){var y=t,m=y.directionObj,v=y.stepPx;m&&(f.scrollBy(m.x*v,m.y*v),window.requestAnimationFrame(function(){return r(f)}))}function s(f){return Xt-f}function c(f,y){if(!y)return!1;var m=mi(f,y),v=!!t.directionObj;if(m===null)return v&&a(),!1;var U=!1,i=!1;return y.scrollHeight>y.clientHeight&&(m.bottom<Xt?(U=!0,t.directionObj={x:0,y:1},t.stepPx=s(m.bottom)):m.top<Xt&&(U=!0,t.directionObj={x:0,y:-1},t.stepPx=s(m.top)),!v&&U)||y.scrollWidth>y.clientWidth&&(m.right<Xt?(i=!0,t.directionObj={x:1,y:0},t.stepPx=s(m.right)):m.left<Xt&&(i=!0,t.directionObj={x:-1,y:0},t.stepPx=s(m.left)),!v&&i)?(r(y),!0):(a(),!1)}return{scrollIfNeeded:c,resetScrolling:a}}function mi(t,a){var r=a===document.scrollingElement?{top:0,bottom:window.innerHeight,left:0,right:window.innerWidth}:a.getBoundingClientRect();return Sr(t,r)?{top:t.y-r.top,bottom:r.bottom-t.y,left:t.x-r.left,right:r.right-t.x}:null}function pi(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],a=arguments.length>1?arguments[1]:void 0,r=_i(t),s=Array.from(r).sort(function(v,U){return pr(U)-pr(v)}),c=fi(),f=c.scrollIfNeeded,y=c.resetScrolling;function m(){var v=a();if(!v||!s)return!1;for(var U=s.filter(function(I){return Sr(v,I.getBoundingClientRect())||I===document.scrollingElement}),i=0;i<U.length;i++){var K=f(v,U[i]);if(K)return!0}return!1}return{multiScrollIfNeeded:r.size>0?m:function(){return!1},destroy:function(){return y()}}}function gi(t){if(!t)return[];for(var a=[],r=t;r;){var s=window.getComputedStyle(r),c=s.overflow;c.split(" ").some(function(f){return f.includes("auto")||f.includes("scroll")})&&a.push(r),r=r.parentElement}return a}function _i(t){var a=new Set,r=nr(t),s;try{for(r.s();!(s=r.n()).done;){var c=s.value;gi(c).forEach(function(f){return a.add(f)})}}catch(f){r.e(f)}finally{r.f()}return(document.scrollingElement.scrollHeight>document.scrollingElement.clientHeight||document.scrollingElement.scrollWidth>document.scrollingElement.clientHeight)&&a.add(document.scrollingElement),a}function hi(t){var a=t.cloneNode(!0),r=[],s=t.tagName==="SELECT",c=s?[t]:pt(t.querySelectorAll("select")),f=nr(c),y;try{for(f.s();!(y=f.n()).done;){var m=y.value;r.push(m.value)}}catch(B){f.e(B)}finally{f.f()}if(c.length>0)for(var v=s?[a]:pt(a.querySelectorAll("select")),U=0;U<v.length;U++){var i=v[U],K=r[U],I=i.querySelector('option[value="'.concat(K,'"'));I&&I.setAttribute("selected",!0)}var z=t.tagName==="CANVAS",E=z?[t]:pt(t.querySelectorAll("canvas"));if(E.length>0)for(var G=z?[a]:pt(a.querySelectorAll("canvas")),V=0;V<G.length;V++){var ae=E[V],S=G[V];S.width=ae.width,S.height=ae.height,ae.width>0&&ae.height>0&&S.getContext("2d").drawImage(ae,0,0)}return a}var ar=Object.freeze({USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT:"USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT"}),bi=Vt({},ar.USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT,!1);function Oa(t){if(!ar[t])throw new Error("Can't get non existing feature flag ".concat(t,"! Supported flags: ").concat(Object.keys(ar)));return bi[t]}var yi=.2;function Ft(t){return"".concat(t," ").concat(yi,"s ease")}function xi(t,a){var r=t.getBoundingClientRect(),s=hi(t);za(t,s),s.id=ti,s.style.position="fixed";var c=r.top,f=r.left;if(s.style.top="".concat(c,"px"),s.style.left="".concat(f,"px"),a){var y=Ca(r);c-=y.y-a.y,f-=y.x-a.x,window.setTimeout(function(){s.style.top="".concat(c,"px"),s.style.left="".concat(f,"px")},0)}return s.style.margin="0",s.style.boxSizing="border-box",s.style.height="".concat(r.height,"px"),s.style.width="".concat(r.width,"px"),s.style.transition="".concat(Ft("top"),", ").concat(Ft("left"),", ").concat(Ft("background-color"),", ").concat(Ft("opacity"),", ").concat(Ft("color")," "),window.setTimeout(function(){return s.style.transition+=", ".concat(Ft("width"),", ").concat(Ft("height"))},0),s.style.zIndex="9999",s.style.cursor="grabbing",s}function wi(t){t.style.cursor="grab"}function Di(t,a,r,s){za(a,t);var c=a.getBoundingClientRect(),f=t.getBoundingClientRect(),y=c.width-f.width,m=c.height-f.height;if(y||m){var v={left:(r-f.left)/f.width,top:(s-f.top)/f.height};Oa(ar.USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT)||(t.style.height="".concat(c.height,"px"),t.style.width="".concat(c.width,"px")),t.style.left="".concat(parseFloat(t.style.left)-v.left*y,"px"),t.style.top="".concat(parseFloat(t.style.top)-v.top*m,"px")}}function za(t,a){var r=window.getComputedStyle(t);Array.from(r).filter(function(s){return s.startsWith("background")||s.startsWith("padding")||s.startsWith("font")||s.startsWith("text")||s.startsWith("align")||s.startsWith("justify")||s.startsWith("display")||s.startsWith("flex")||s.startsWith("border")||s==="opacity"||s==="color"||s==="list-style-type"||Oa(ar.USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT)&&(s==="width"||s==="height")}).forEach(function(s){return a.style.setProperty(s,r.getPropertyValue(s),r.getPropertyPriority(s))})}function $i(t,a){t.draggable=!1,t.ondragstart=function(){return!1},a?(t.style.userSelect="",t.style.WebkitUserSelect="",t.style.cursor=""):(t.style.userSelect="none",t.style.WebkitUserSelect="none",t.style.cursor="grab")}function Ia(t){t.style.display="none",t.style.position="fixed",t.style.zIndex="-5"}function Ei(t){t.style.visibility="hidden",t.setAttribute(Er,"true")}function Si(t){t.style.visibility="",t.removeAttribute(Er)}function cr(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:function(){},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(){return[]};t.forEach(function(s){var c=a(s);Object.keys(c).forEach(function(f){s.style[f]=c[f]}),r(s).forEach(function(f){return s.classList.add(f)})})}function gr(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:function(){},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(){return[]};t.forEach(function(s){var c=a(s);Object.keys(c).forEach(function(f){s.style[f]=""}),r(s).forEach(function(f){return s.classList.contains(f)&&s.classList.remove(f)})})}function Ti(t){var a=t.style.minHeight;t.style.minHeight=window.getComputedStyle(t).getPropertyValue("height");var r=t.style.minWidth;return t.style.minWidth=window.getComputedStyle(t).getPropertyValue("width"),function(){t.style.minHeight=a,t.style.minWidth=r}}var Ci="--any--",Ai=100,Oi=20,oa=3,na={outline:"rgba(255, 255, 102, 0.7) solid 2px"},ia="data-is-dnd-original-dragged-item",_t,tt,st,Tr,Me,Cr,zt,Qe,At,ct,Ct=!1,Kr=!1,Xr,sr=!1,vr=[],er,bt=new Map,We=new Map,zr=new WeakMap;function zi(t,a){bt.has(a)||bt.set(a,new Set),bt.get(a).has(t)||(bt.get(a).add(t),$a())}function sa(t,a){bt.get(a).delete(t),Ea(),bt.get(a).size===0&&bt.delete(a)}function Ii(){var t=bt.get(Tr),a=nr(t),r;try{for(a.s();!(r=a.n()).done;){var s=r.value;s.addEventListener(wr,ka),s.addEventListener(ir,Pa),s.addEventListener(Dr,Ra)}}catch(y){a.e(y)}finally{a.f()}window.addEventListener(Zr,Yt);var c=Math.max.apply(Math,pt(Array.from(t.keys()).map(function(y){return We.get(y).dropAnimationDurationMs}))),f=c===0?Oi:Math.max(c,Ai);er=pi(t,function(){return ct}),vi(tt,t,f*1.07,er)}function ki(){var t=bt.get(Tr),a=nr(t),r;try{for(a.s();!(r=a.n()).done;){var s=r.value;s.removeEventListener(wr,ka),s.removeEventListener(ir,Pa),s.removeEventListener(Dr,Ra)}}catch(c){a.e(c)}finally{a.f()}window.removeEventListener(Zr,Yt),er&&(er.destroy(),er=void 0),ui()}function Ar(t){return t.findIndex(function(a){return!!a[$r]})}function Pi(t){var a;return fr(fr({},t),{},(a={},Vt(a,$r,!0),Vt(a,Xe,ei),a))}function ka(t){var a=We.get(t.currentTarget),r=a.items,s=a.dropFromOthersDisabled;if(!(s&&t.currentTarget!==Me)){if(sr=!1,r=r.filter(function(i){return i[Xe]!==zt[Xe]}),Me!==t.currentTarget){var c=We.get(Me).items,f=c.filter(function(i){return!i[$r]});It(Me,f,{trigger:nt.DRAGGED_ENTERED_ANOTHER,id:st[Xe],source:lt.POINTER})}var y=t.detail.indexObj,m=y.index,v=y.isProximityBased,U=v&&m===t.currentTarget.children.length-1?m+1:m;Qe=t.currentTarget,r.splice(U,0,zt),It(t.currentTarget,r,{trigger:nt.DRAGGED_ENTERED,id:st[Xe],source:lt.POINTER})}}function Pa(t){if(Ct){var a=We.get(t.currentTarget),r=a.items,s=a.dropFromOthersDisabled;if(!(s&&t.currentTarget!==Me&&t.currentTarget!==Qe)){var c=pt(r),f=Ar(c);f!==-1&&c.splice(f,1);var y=Qe;Qe=void 0;var m=t.detail,v=m.type,U=m.theOtherDz;if(v===mr.OUTSIDE_OF_ANY||v===mr.LEFT_FOR_ANOTHER&&U!==Me&&We.get(U).dropFromOthersDisabled){sr=!0,Qe=Me;var i=y===Me?c:pt(We.get(Me).items);i.splice(Cr,0,zt),It(Me,i,{trigger:nt.DRAGGED_LEFT_ALL,id:st[Xe],source:lt.POINTER})}It(t.currentTarget,c,{trigger:nt.DRAGGED_LEFT,id:st[Xe],source:lt.POINTER})}}}function Ra(t){var a=We.get(t.currentTarget),r=a.items,s=a.dropFromOthersDisabled;if(!(s&&t.currentTarget!==Me)){var c=pt(r);sr=!1;var f=t.detail.indexObj.index,y=Ar(c);y!==-1&&c.splice(y,1),c.splice(f,0,zt),It(t.currentTarget,c,{trigger:nt.DRAGGED_OVER_INDEX,id:st[Xe],source:lt.POINTER})}}function _r(t){t.preventDefault();var a=t.touches?t.touches[0]:t;ct={x:a.clientX,y:a.clientY},tt.style.transform="translate3d(".concat(ct.x-At.x,"px, ").concat(ct.y-At.y,"px, 0)")}function Yt(){Kr=!0,window.removeEventListener("mousemove",_r),window.removeEventListener("touchmove",_r),window.removeEventListener("mouseup",Yt),window.removeEventListener("touchend",Yt),ki(),wi(tt),Qe||(Qe=Me);var t=We.get(Qe),a=t.items,r=t.type;gr(bt.get(r),function(f){return We.get(f).dropTargetStyle},function(f){return We.get(f).dropTargetClasses});var s=Ar(a);s===-1&&Qe===Me&&(s=Cr),a=a.map(function(f){return f[$r]?st:f});function c(){Xr(),Zt(Qe,a,{trigger:sr?nt.DROPPED_OUTSIDE_OF_ANY:nt.DROPPED_INTO_ZONE,id:st[Xe],source:lt.POINTER}),Qe!==Me&&Zt(Me,We.get(Me).items,{trigger:nt.DROPPED_INTO_ANOTHER,id:st[Xe],source:lt.POINTER});var f=Array.from(Qe.children).find(function(y){return y.getAttribute(Er)});f&&Si(f),Ni()}We.get(Qe).dropAnimationDisabled?c():Ri(s,c)}function Ri(t,a){var r=t>-1?Fr(Qe.children[t],!1):Fr(Qe,!1),s={x:r.left-parseFloat(tt.style.left),y:r.top-parseFloat(tt.style.top)},c=We.get(Qe),f=c.dropAnimationDurationMs,y="transform ".concat(f,"ms ease");tt.style.transition=tt.style.transition?tt.style.transition+","+y:y,tt.style.transform="translate3d(".concat(s.x,"px, ").concat(s.y,"px, 0)"),window.setTimeout(a,f)}function Li(t,a){vr.push({dz:t,destroy:a}),window.requestAnimationFrame(function(){Ia(t),document.body.appendChild(t)})}function Ni(){tt.remove(),_t.remove(),vr.length&&(vr.forEach(function(t){var a=t.dz,r=t.destroy;r(),a.remove()}),vr=[]),tt=void 0,_t=void 0,st=void 0,Tr=void 0,Me=void 0,Cr=void 0,zt=void 0,Qe=void 0,At=void 0,ct=void 0,Ct=!1,Kr=!1,Xr=void 0,sr=!1}function ji(t,a){var r=!1,s={items:void 0,type:void 0,flipDurationMs:0,dragDisabled:!1,morphDisabled:!1,dropFromOthersDisabled:!1,dropTargetStyle:na,dropTargetClasses:[],transformDraggedElement:function(){},centreDraggedOnCursor:!1,dropAnimationDisabled:!1},c=new Map;function f(){window.addEventListener("mousemove",v,{passive:!1}),window.addEventListener("touchmove",v,{passive:!1,capture:!1}),window.addEventListener("mouseup",m,{passive:!1}),window.addEventListener("touchend",m,{passive:!1})}function y(){window.removeEventListener("mousemove",v),window.removeEventListener("touchmove",v),window.removeEventListener("mouseup",m),window.removeEventListener("touchend",m)}function m(I){if(y(),_t=void 0,At=void 0,ct=void 0,I.type==="touchend"){var z=new Event("click",{bubbles:!0,cancelable:!0});I.target.dispatchEvent(z)}}function v(I){I.preventDefault();var z=I.touches?I.touches[0]:I;ct={x:z.clientX,y:z.clientY},(Math.abs(ct.x-At.x)>=oa||Math.abs(ct.y-At.y)>=oa)&&(y(),i())}function U(I){if(!(I.target!==I.currentTarget&&(I.target.value!==void 0||I.target.isContentEditable))&&!I.button&&!Ct){I.preventDefault(),I.stopPropagation();var z=I.touches?I.touches[0]:I;At={x:z.clientX,y:z.clientY},ct=fr({},At),_t=I.currentTarget,f()}}function i(){Ct=!0;var I=c.get(_t);Cr=I,Me=_t.parentElement;var z=Me.closest("dialog")||Me.closest("[popover]")||Me.getRootNode(),E=z.body||z,G=s.items,V=s.type,ae=s.centreDraggedOnCursor,S=pt(G);st=S[I],Tr=V,zt=Pi(st),tt=xi(_t,ae&&ct),E.appendChild(tt);function B(){_t.parentElement?window.requestAnimationFrame(B):(_t.setAttribute(ia,!0),E.appendChild(_t),Ii(),Ia(_t),zt[Xe]=st[Xe],tt.focus())}window.requestAnimationFrame(B),cr(Array.from(bt.get(s.type)).filter(function(le){return le===Me||!We.get(le).dropFromOthersDisabled}),function(le){return We.get(le).dropTargetStyle},function(le){return We.get(le).dropTargetClasses}),S.splice(I,1,zt),Xr=Ti(Me),It(Me,S,{trigger:nt.DRAG_STARTED,id:st[Xe],source:lt.POINTER}),window.addEventListener("mousemove",_r,{passive:!1}),window.addEventListener("touchmove",_r,{passive:!1,capture:!1}),window.addEventListener("mouseup",Yt,{passive:!1}),window.addEventListener("touchend",Yt,{passive:!1})}function K(I){var z=I.items,E=z===void 0?void 0:z,G=I.flipDurationMs,V=G===void 0?0:G,ae=I.type,S=ae===void 0?Ci:ae,B=I.dragDisabled,le=B===void 0?!1:B,ce=I.morphDisabled,pe=ce===void 0?!1:ce,be=I.dropFromOthersDisabled,Ae=be===void 0?!1:be,Y=I.dropTargetStyle,Q=Y===void 0?na:Y,fe=I.dropTargetClasses,xe=fe===void 0?[]:fe,ke=I.transformDraggedElement,ve=ke===void 0?function(){}:ke,ee=I.centreDraggedOnCursor,X=ee===void 0?!1:ee,ie=I.dropAnimationDisabled,we=ie===void 0?!1:ie;s.dropAnimationDurationMs=V,s.type&&S!==s.type&&sa(t,s.type),s.type=S,s.items=pt(E),s.dragDisabled=le,s.morphDisabled=pe,s.transformDraggedElement=ve,s.centreDraggedOnCursor=X,s.dropAnimationDisabled=we,r&&Ct&&!Kr&&(!li(Q,s.dropTargetStyle)||!di(xe,s.dropTargetClasses))&&(gr([t],function(){return s.dropTargetStyle},function(){return xe}),cr([t],function(){return Q},function(){return xe})),s.dropTargetStyle=Q,s.dropTargetClasses=pt(xe);function Ie(me,ye){return We.get(me)?We.get(me)[ye]:s[ye]}r&&Ct&&s.dropFromOthersDisabled!==Ae&&(Ae?gr([t],function(me){return Ie(me,"dropTargetStyle")},function(me){return Ie(me,"dropTargetClasses")}):cr([t],function(me){return Ie(me,"dropTargetStyle")},function(me){return Ie(me,"dropTargetClasses")})),s.dropFromOthersDisabled=Ae,We.set(t,s),zi(t,S);for(var $e=Ct?Ar(s.items):-1,ue=0;ue<t.children.length;ue++){var W=t.children[ue];if($i(W,le),ue===$e){pe||Di(tt,W,ct.x,ct.y),s.transformDraggedElement(tt,st,ue),Ei(W);continue}W.removeEventListener("mousedown",zr.get(W)),W.removeEventListener("touchstart",zr.get(W)),le||(W.addEventListener("mousedown",U),W.addEventListener("touchstart",U),zr.set(W,U)),c.set(W,ue),r||(r=!0)}}return K(a),{update:function(z){K(z)},destroy:function(){function z(){sa(t,We.get(t).type),We.delete(t)}Ct&&!t.closest("[".concat(ia,"]"))?Li(t,z):z()}}}var dr,Br={DND_ZONE_ACTIVE:"dnd-zone-active",DND_ZONE_DRAG_DISABLED:"dnd-zone-drag-disabled"},La=(dr={},Vt(dr,Br.DND_ZONE_ACTIVE,"Tab to one the items and press space-bar or enter to start dragging it"),Vt(dr,Br.DND_ZONE_DRAG_DISABLED,"This is a disabled drag and drop list"),dr),Fi="dnd-action-aria-alert",Ze;function Hr(){Ze||(Ze=document.createElement("div"),function(){Ze.id=Fi,Ze.style.position="fixed",Ze.style.bottom="0",Ze.style.left="0",Ze.style.zIndex="-5",Ze.style.opacity="0",Ze.style.height="0",Ze.style.width="0",Ze.setAttribute("role","alert")}(),document.body.prepend(Ze),Object.entries(La).forEach(function(t){var a=Fn(t,2),r=a[0],s=a[1];return document.body.prepend(Hi(r,s))}))}function Mi(){return Yr?null:(document.readyState==="complete"?Hr():window.addEventListener("DOMContentLoaded",Hr),fr({},Br))}function Bi(){Yr||!Ze||(Object.keys(La).forEach(function(t){var a;return(a=document.getElementById(t))===null||a===void 0?void 0:a.remove()}),Ze.remove(),Ze=void 0)}function Hi(t,a){var r=document.createElement("div");return r.id=t,r.innerHTML="<p>".concat(a,"</p>"),r.style.display="none",r.style.position="fixed",r.style.zIndex="-5",r}function Wt(t){if(!Yr){Ze||Hr(),Ze.innerHTML="";var a=document.createTextNode(t);Ze.appendChild(a),Ze.style.display="none",Ze.style.display="inline"}}var Gi="--any--",la={outline:"rgba(255, 255, 102, 0.7) solid 2px"},mt=!1,Gr,rt,Bt="",Mt,Dt,Ot="",hr=new WeakSet,da=new WeakMap,ca=new WeakMap,Ur=new Map,Je=new Map,ht=new Map,br;function Ui(t,a){ht.size===0&&(br=Mi(),window.addEventListener("keydown",Na),window.addEventListener("click",ja)),ht.has(a)||ht.set(a,new Set),ht.get(a).has(t)||(ht.get(a).add(t),$a())}function va(t,a){rt===t&&or(),ht.get(a).delete(t),Ea(),ht.get(a).size===0&&ht.delete(a),ht.size===0&&(window.removeEventListener("keydown",Na),window.removeEventListener("click",ja),br=void 0,Bi())}function Na(t){if(mt)switch(t.key){case"Escape":{or();break}}}function ja(){mt&&(hr.has(document.activeElement)||or())}function Wi(t){if(mt){var a=t.currentTarget;if(a!==rt){Bt=a.getAttribute("aria-label")||"";var r=Je.get(rt),s=r.items,c=s.find(function(K){return K[Xe]===Dt}),f=s.indexOf(c),y=s.splice(f,1)[0],m=Je.get(a),v=m.items,U=m.autoAriaDisabled;a.getBoundingClientRect().top<rt.getBoundingClientRect().top||a.getBoundingClientRect().left<rt.getBoundingClientRect().left?(v.push(y),U||Wt("Moved item ".concat(Ot," to the end of the list ").concat(Bt))):(v.unshift(y),U||Wt("Moved item ".concat(Ot," to the beginning of the list ").concat(Bt)));var i=rt;Zt(i,s,{trigger:nt.DROPPED_INTO_ANOTHER,id:Dt,source:lt.KEYBOARD}),Zt(a,v,{trigger:nt.DROPPED_INTO_ZONE,id:Dt,source:lt.KEYBOARD}),rt=a}}}function Fa(){Ur.forEach(function(t,a){var r=t.update;return r(Je.get(a))})}function or(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;Je.get(rt).autoAriaDisabled||Wt("Stopped dragging item ".concat(Ot)),hr.has(document.activeElement)&&document.activeElement.blur(),t&&It(rt,Je.get(rt).items,{trigger:nt.DRAG_STOPPED,id:Dt,source:lt.KEYBOARD}),gr(ht.get(Gr),function(a){return Je.get(a).dropTargetStyle},function(a){return Je.get(a).dropTargetClasses}),Mt=null,Dt=null,Ot="",Gr=null,rt=null,Bt="",mt=!1,Fa()}function Vi(t,a){var r={items:void 0,type:void 0,dragDisabled:!1,zoneTabIndex:0,zoneItemTabIndex:0,dropFromOthersDisabled:!1,dropTargetStyle:la,dropTargetClasses:[],autoAriaDisabled:!1};function s(i,K,I){i.length<=1||i.splice(I,1,i.splice(K,1,i[I])[0])}function c(i){switch(i.key){case"Enter":case" ":{if((i.target.disabled!==void 0||i.target.href||i.target.isContentEditable)&&!hr.has(i.target))return;i.preventDefault(),i.stopPropagation(),mt?or():f(i);break}case"ArrowDown":case"ArrowRight":{if(!mt)return;i.preventDefault(),i.stopPropagation();var K=Je.get(t),I=K.items,z=Array.from(t.children),E=z.indexOf(i.currentTarget);E<z.length-1&&(r.autoAriaDisabled||Wt("Moved item ".concat(Ot," to position ").concat(E+2," in the list ").concat(Bt)),s(I,E,E+1),Zt(t,I,{trigger:nt.DROPPED_INTO_ZONE,id:Dt,source:lt.KEYBOARD}));break}case"ArrowUp":case"ArrowLeft":{if(!mt)return;i.preventDefault(),i.stopPropagation();var G=Je.get(t),V=G.items,ae=Array.from(t.children),S=ae.indexOf(i.currentTarget);S>0&&(r.autoAriaDisabled||Wt("Moved item ".concat(Ot," to position ").concat(S," in the list ").concat(Bt)),s(V,S,S-1),Zt(t,V,{trigger:nt.DROPPED_INTO_ZONE,id:Dt,source:lt.KEYBOARD}));break}}}function f(i){m(i.currentTarget),rt=t,Gr=r.type,mt=!0;var K=Array.from(ht.get(r.type)).filter(function(z){return z===rt||!Je.get(z).dropFromOthersDisabled});if(cr(K,function(z){return Je.get(z).dropTargetStyle},function(z){return Je.get(z).dropTargetClasses}),!r.autoAriaDisabled){var I="Started dragging item ".concat(Ot,". Use the arrow keys to move it within its list ").concat(Bt);K.length>1&&(I+=", or tab to another list in order to move the item into it"),Wt(I)}It(t,Je.get(t).items,{trigger:nt.DRAG_STARTED,id:Dt,source:lt.KEYBOARD}),Fa()}function y(i){mt&&i.currentTarget!==Mt&&(i.stopPropagation(),or(!1),f(i))}function m(i){var K=Je.get(t),I=K.items,z=Array.from(t.children),E=z.indexOf(i);Mt=i,Mt.tabIndex=r.zoneItemTabIndex,Dt=I[E][Xe],Ot=z[E].getAttribute("aria-label")||""}function v(i){var K=i.items,I=K===void 0?[]:K,z=i.type,E=z===void 0?Gi:z,G=i.dragDisabled,V=G===void 0?!1:G,ae=i.zoneTabIndex,S=ae===void 0?0:ae,B=i.zoneItemTabIndex,le=B===void 0?0:B,ce=i.dropFromOthersDisabled,pe=ce===void 0?!1:ce,be=i.dropTargetStyle,Ae=be===void 0?la:be,Y=i.dropTargetClasses,Q=Y===void 0?[]:Y,fe=i.autoAriaDisabled,xe=fe===void 0?!1:fe;r.items=pt(I),r.dragDisabled=V,r.dropFromOthersDisabled=pe,r.zoneTabIndex=S,r.zoneItemTabIndex=le,r.dropTargetStyle=Ae,r.dropTargetClasses=Q,r.autoAriaDisabled=xe,r.type&&E!==r.type&&va(t,r.type),r.type=E,Ui(t,E),xe||(t.setAttribute("aria-disabled",V),t.setAttribute("role","list"),t.setAttribute("aria-describedby",V?br.DND_ZONE_DRAG_DISABLED:br.DND_ZONE_ACTIVE)),Je.set(t,r),mt?t.tabIndex=t===rt||Mt.contains(t)||r.dropFromOthersDisabled||rt&&r.type!==Je.get(rt).type?-1:0:t.tabIndex=r.zoneTabIndex,t.addEventListener("focus",Wi);for(var ke=function(X){var ie=t.children[X];hr.add(ie),ie.tabIndex=mt?-1:r.zoneItemTabIndex,xe||ie.setAttribute("role","listitem"),ie.removeEventListener("keydown",da.get(ie)),ie.removeEventListener("click",ca.get(ie)),V||(ie.addEventListener("keydown",c),da.set(ie,c),ie.addEventListener("click",y),ca.set(ie,y)),mt&&r.items[X][Xe]===Dt&&(Mt=ie,Mt.tabIndex=r.zoneItemTabIndex,ie.focus())},ve=0;ve<t.children.length;ve++)ke(ve)}v(a);var U={update:function(K){v(K)},destroy:function(){va(t,r.type),Je.delete(t),Ur.delete(t)}};return Ur.set(t,U),U}var Zi=["items","flipDurationMs","type","dragDisabled","morphDisabled","dropFromOthersDisabled","zoneTabIndex","zoneItemTabIndex","dropTargetStyle","dropTargetClasses","transformDraggedElement","autoAriaDisabled","centreDraggedOnCursor","dropAnimationDisabled"];function Ir(t,a){if(Yi(t))return{update:function(){},destroy:function(){}};ua(a);var r=ji(t,a),s=Vi(t,a);return{update:function(f){ua(f),r.update(f),s.update(f)},destroy:function(){r.destroy(),s.destroy()}}}function Yi(t){return!!t.closest("[".concat(Qn,'="true"]'))}function ua(t){var a=t.items;t.flipDurationMs,t.type,t.dragDisabled,t.morphDisabled,t.dropFromOthersDisabled;var r=t.zoneTabIndex,s=t.zoneItemTabIndex;t.dropTargetStyle;var c=t.dropTargetClasses;t.transformDraggedElement,t.autoAriaDisabled,t.centreDraggedOnCursor,t.dropAnimationDisabled;var f=jn(t,Zi);if(Object.keys(f).length>0&&console.warn("dndzone will ignore unknown options",f),!a)throw new Error("no 'items' key provided to dndzone");var y=a.find(function(m){return!{}.hasOwnProperty.call(m,Xe)});if(y)throw new Error("missing '".concat(Xe,"' property for item ").concat(lr(y)));if(c&&!Array.isArray(c))throw new Error("dropTargetClasses should be an array but instead it is a ".concat(Qt(c),", ").concat(lr(c)));if(r&&!fa(r))throw new Error("zoneTabIndex should be a number but instead it is a ".concat(Qt(r),", ").concat(lr(r)));if(s&&!fa(s))throw new Error("zoneItemTabIndex should be a number but instead it is a ".concat(Qt(s),", ").concat(lr(s)))}function fa(t){return!isNaN(t)&&function(a){return(a|0)===a}(parseFloat(t))}var qi=u('<div class="drag-handle cursor-move p-1 text-zinc-400 hover:text-zinc-200" title="Drag to reorder" aria-label="Drag to reorder" role="button" tabindex="0"><!></div>'),Ki=u('<button type="button" class="mr-2 rounded p-1 text-zinc-400 hover:text-zinc-200" title="Edit section title" aria-label="Edit section title"><!></button>'),Xi=u('<button type="button" class="mr-2 rounded p-1 text-zinc-400 hover:text-zinc-200"><!></button>'),Ji=u('<div class="border-b py-4 transition-all"><!></div>'),Qi=u("<!> <!>",1),es=u("<!> <!>",1),ts=u('<!> <div class="grid gap-4 py-4"><div class="grid grid-cols-4 items-center gap-4"><label for="section-title" class="text-right text-sm font-medium">Title</label> <!></div></div> <!>',1),rs=u('<div><div class="w-full"><div class="border-b"><div class="flex w-full items-center justify-between border-b border-zinc-700 px-4 py-3 text-white"><div class="flex flex-1 cursor-pointer items-center gap-4" role="button" tabindex="0"><!> <h3 class="text-xl"> </h3></div> <div class="flex items-center"><!> <!> <div class="cursor-pointer p-1" role="button" tabindex="0"><!></div></div></div></div> <!></div></div> <!>',1);function kr(t,a){ut(a,!1);let r=Ke(a,"title",12),s=Ke(a,"drag",8,!1),c=Ke(a,"className",8,""),f=Ke(a,"allowEdit",8,!0),y=Ke(a,"allowHide",8,!0),m=Ke(a,"isResumeHeader",8,!1),v=Ke(a,"isHidden",8,!1),U=ot(!1),i=ot(!1),K=ot("");function I(){_e(K,r()),_e(i,!0)}function z(){e(K)&&e(K).trim()&&(console.log("Section title updated to:",e(K)),r(e(K))),_e(i,!1)}function E(){_e(U,!e(U))}function G(ue){(ue.key==="Enter"||ue.key===" ")&&(ue.preventDefault(),E())}xt();var V=rs(),ae=ge(V),S=n(ae),B=n(S),le=n(B),ce=n(le),pe=n(ce);{var be=ue=>{var W=qi(),me=n(W);io(me,{size:16,"aria-hidden":"true"}),o(W),d(ue,W)};O(pe,ue=>{s()&&!m()&&ue(be)})}var Ae=l(pe,2),Y=n(Ae,!0);o(Ae),o(ce);var Q=l(ce,2),fe=n(Q);{var xe=ue=>{var W=Ki(),me=n(W);so(me,{size:16,"aria-hidden":"true"}),o(W),it("click",W,ye=>{ye.stopPropagation(),I()}),d(ue,W)};O(fe,ue=>{e(U)&&f()&&!m()&&ue(xe)})}var ke=l(fe,2);{var ve=ue=>{var W=Xi(),me=n(W);{var ye=De=>{yo(De,{size:16,"aria-hidden":"true"})},Ee=De=>{xo(De,{size:16,"aria-hidden":"true"})};O(me,De=>{v()?De(ye):De(Ee,!1)})}o(W),b(()=>{je(W,"title",v()?"Show section in resume":"Hide section in resume"),je(W,"aria-label",v()?"Show section in resume":"Hide section in resume")}),it("click",W,De=>{De.stopPropagation();const oe=new CustomEvent("toggle-visibility",{detail:{title:r()},bubbles:!0});typeof window<"u"&&window.dispatchEvent(oe)}),d(ue,W)};O(ke,ue=>{e(U)&&y()&&!m()&&ue(ve)})}var ee=l(ke,2),X=n(ee);const ie=$t(()=>e(U)?"rotate-180":"");po(X,{size:20,get class(){return`transition-transform duration-200 ${e(ie)??""}`},"aria-hidden":"true"}),o(ee),o(Q),o(le),o(B);var we=l(B,2);{var Ie=ue=>{var W=Ji(),me=n(W);Lr(me,a,"default",{},null),o(W),d(ue,W)};O(we,ue=>{e(U)&&ue(Ie)})}o(S),o(ae);var $e=l(ae,2);fo($e,{get open(){return e(i)},set open(ue){_e(i,ue)},children:(ue,W)=>{mo(ue,{class:"sm:max-w-[425px]",children:(me,ye)=>{var Ee=ts(),De=ge(Ee);go(De,{children:(de,j)=>{var L=Qi(),x=ge(L);_o(x,{children:(C,R)=>{Te();var P=Le("Edit Section Title");d(C,P)},$$slots:{default:!0}});var N=l(x,2);ho(N,{children:(C,R)=>{Te();var P=Le("Change the title of this section. Click save when you're done.");d(C,P)},$$slots:{default:!0}}),d(de,L)},$$slots:{default:!0}});var oe=l(De,2),k=n(oe),re=l(n(k),2);et(re,{id:"section-title",class:"col-span-3",placeholder:"Enter section title",get value(){return e(K)},set value(de){_e(K,de)},$$legacy:!0}),o(k),o(oe);var te=l(oe,2);bo(te,{children:(de,j)=>{var L=es(),x=ge(L);at(x,{type:"button",variant:"outline",$$events:{click:()=>_e(i,!1)},children:(C,R)=>{Te();var P=Le("Cancel");d(C,P)},$$slots:{default:!0}});var N=l(x,2);at(N,{type:"button",$$events:{click:z},children:(C,R)=>{Te();var P=Le("Save changes");d(C,P)},$$slots:{default:!0}}),d(de,L)},$$slots:{default:!0}}),d(me,Ee)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),b(()=>{vt(ae,1,`group relative flex flex-row gap-2 rounded-md border border-zinc-700 p-5 ${c()}`),je(ce,"aria-expanded",e(U)),D(Y,r())}),it("click",ce,E),it("keydown",ce,G),it("click",ee,E),it("keydown",ee,G),d(t,V),ft()}var as=u('<div class="ml-4"><details class="text-sm"><summary class="cursor-pointer font-medium"> </summary> <div class="mt-2 flex flex-wrap gap-2"></div></details></div>'),os=u('<div class="resume-form my-6 space-y-4"><!> <!> <div class="space-y-4 outline-none"></div> <div class="mt-4 flex flex-wrap gap-2"><!> <!></div> <details class="mt-8 rounded-md border border-zinc-700 bg-zinc-800 p-4"><summary class="cursor-pointer font-medium">Form Data Debug</summary> <div class="mt-4 overflow-auto"><pre class="text-xs"> </pre></div></details></div>');function yc(t,a){ut(a,!1);const[r,s]=xr(),c=()=>tr(m,"$formData",r);let f=Ke(a,"data",8);const y=xa(f(),{validators:wa(So),dataType:"json",onUpdated:({form:Y})=>{if(Y.valid){console.log("Form updated with valid data:",Y.data);try{const Q=new CustomEvent("form-data-changed",{detail:{data:Y.data},bubbles:!0});window.dispatchEvent(Q)}catch(Q){console.error("Error dispatching event:",Q)}}else console.error("Form validation errors:",Y.errors)},delayMs:100,resetForm:!1,applyAction:!1,taintedMessage:null}),{form:m}=y;let v=ot([{id:"education",title:"Education",component:mn,visible:!0},{id:"certifications",title:"Certifications",component:hn,visible:!0},{id:"experience",title:"Professional Experience",component:Cn,visible:!0},{id:"projects",title:"Projects & Outside Experience",component:In,visible:!0},{id:"skills",title:"Skills & Interests",component:Ln,visible:!0}]);function U({detail:Y}){_e(v,Y.items)}let i=ot([]);function K(Y){const Q=e(v).find(fe=>fe.title===Y)||{id:"summary"};if(e(i).includes(Q.id)?(_e(i,e(i).filter(fe=>fe!==Q.id)),console.log(`Section "${Y}" is now visible in resume`)):(_e(i,[...e(i),Q.id]),console.log(`Section "${Y}" is now hidden in resume`)),typeof window<"u"){const fe=new CustomEvent("resume-sections-visibility-changed",{detail:{hiddenSections:e(i)},bubbles:!0});window.dispatchEvent(fe)}}Gt(()=>{if(typeof window<"u"){const Y=fe=>{const{title:xe}=fe.detail;K(xe)},Q=fe=>{if(fe.detail&&fe.detail.field&&fe.detail.value!==void 0){const{field:xe,value:ke}=fe.detail;console.log(`Received form:update event for field "${xe}"`,ke);const ve={...c()};ve[xe]=ke,Ba(m,ve),console.log(`Updated form data for field "${xe}"`,c())}};return window.addEventListener("toggle-visibility",Y),window.addEventListener("form:update",Q),()=>{window.removeEventListener("toggle-visibility",Y),window.removeEventListener("form:update",Q)}}}),qe(()=>c(),()=>{if(c()&&(console.log("Form data in ResumeForm:",c()),c().header&&console.log("Header data in ResumeForm:",c().header),c().skills&&console.log("Skills data in ResumeForm:",c().skills),c().projects&&console.log("Projects data in ResumeForm:",c().projects),typeof window<"u")){const Y=new CustomEvent("form-data-changed",{detail:{data:c()},bubbles:!0});window.dispatchEvent(Y)}}),qe(()=>yt(f()),()=>{console.log("Data prop in ResumeForm:",f())}),Tt(),xt();var I=os(),z=n(I);kr(z,{title:"Resume Header",drag:!1,isResumeHeader:!0,allowEdit:!1,allowHide:!1,children:(Y,Q)=>{Co(Y,{get data(){return c().header},set data(fe){wt(m,Ve(c).header=fe,Ve(c))},$$legacy:!0})},$$slots:{default:!0}});var E=l(z,2);const G=$t(()=>e(i).includes("summary"));kr(E,{title:"Professional Summary",drag:!0,allowEdit:!0,allowHide:!0,isResumeHeader:!1,get isHidden(){return e(G)},children:(Y,Q)=>{tn(Y,{get data(){return c().summary},set data(fe){wt(m,Ve(c).summary=fe,Ve(c))},$$legacy:!0})},$$slots:{default:!0}});var V=l(E,2);Se(V,5,()=>e(v),Y=>Y.id,(Y,Q)=>{const fe=$t(()=>e(i).includes(e(Q).id));kr(Y,{get title(){return e(Q).title},drag:!0,allowEdit:!0,allowHide:!0,isResumeHeader:!1,get isHidden(){return e(fe)},children:(xe,ke)=>{var ve=Fe(),ee=ge(ve);Jt(ee,()=>e(Q).component,(X,ie)=>{ie(X,{get data(){return c()[e(Q).id]},set data(we){wt(m,Ve(c)[e(Q).id]=we,Ve(c))},$$legacy:!0})}),d(xe,ve)},$$slots:{default:!0}})}),o(V),ba(V,(Y,Q)=>Ir==null?void 0:Ir(Y,Q),()=>({items:e(v),flipDurationMs:200})),Jr(()=>it("consider",V,U)),Jr(()=>it("finalize",V,U));var ae=l(V,2),S=n(ae);at(S,{children:(Y,Q)=>{Te();var fe=Le("Custom Section");d(Y,fe)},$$slots:{default:!0}});var B=l(S,2);{var le=Y=>{var Q=as(),fe=n(Q),xe=n(fe),ke=n(xe);o(xe);var ve=l(xe,2);Se(ve,5,()=>e(i),ze,(ee,X)=>{at(ee,{variant:"outline",size:"sm",$$events:{click:()=>{var ie;return K(((ie=e(v).find(we=>we.id===e(X)))==null?void 0:ie.title)||(e(X)==="summary"?"Professional Summary":e(X)))}},children:(ie,we)=>{Te();var Ie=Le();b($e=>D(Ie,$e),[()=>{var $e;return e(X)==="summary"?"Professional Summary":(($e=e(v).find(ue=>ue.id===e(X)))==null?void 0:$e.title)||e(X)}],$t),d(ie,Ie)},$$slots:{default:!0}})}),o(ve),o(fe),o(Q),b(()=>D(ke,`Hidden in Resume (${e(i).length??""})`)),d(Y,Q)};O(B,Y=>{e(i).length>0&&Y(le)})}o(ae);var ce=l(ae,2),pe=l(n(ce),2),be=n(pe),Ae=n(be,!0);o(be),o(pe),o(ce),o(I),b(Y=>D(Ae,Y),[()=>JSON.stringify(c(),null,2)],$t),d(t,I),ft(),s()}const ns={...Eo,layout:"",primaryColor:"",accentColor:"",textColor:"",backgroundColor:"",headerStyle:"",sectionStyle:"",paperSize:""},Ht=Da(ns),ma=t=>{Ht.update(a=>({...a,...t}))},Pr={classic:{primaryColor:"#2563eb",accentColor:"#4b5563",textColor:"#111827",backgroundColor:"#ffffff"},modern:{primaryColor:"#10b981",accentColor:"#6b7280",textColor:"#1f2937",backgroundColor:"#f9fafb"},elegant:{primaryColor:"#7c3aed",accentColor:"#4b5563",textColor:"#111827",backgroundColor:"#f3f4f6"},professional:{primaryColor:"#1e40af",accentColor:"#374151",textColor:"#111827",backgroundColor:"#ffffff"},executive:{primaryColor:"#1f2937",accentColor:"#4b5563",textColor:"#111827",backgroundColor:"#ffffff"},creative:{primaryColor:"#ec4899",accentColor:"#6b7280",textColor:"#1f2937",backgroundColor:"#f9fafb"}},is={classic:{headerStyle:"centered",sectionStyle:"bordered",font:"Times New Roman",fontSize:"12px",lineHeight:"1.5"},modern:{headerStyle:"left-aligned",sectionStyle:"clean",font:"Arial",fontSize:"11px",lineHeight:"1.2"},minimalist:{headerStyle:"compact",sectionStyle:"minimal",font:"Roboto",fontSize:"11px",lineHeight:"1.2"},executive:{headerStyle:"bold",sectionStyle:"divided",font:"Georgia",fontSize:"12px",lineHeight:"1.5"},professional:{headerStyle:"dark-header",sectionStyle:"two-column",font:"Arial",fontSize:"11px",lineHeight:"1.4"}},ss={header:{name:"",email:"",phone:""},summary:{content:""},experience:[],education:[],skills:[],projects:[],certifications:[]},ls=ha(ss);var ds=u("<span> </span>"),cs=u("<span> </span>"),vs=u('<div class="mt-1 flex justify-center gap-4 text-sm"><!> <!></div>'),us=u('<div class="mb-6"><div class="bg-gray-800 p-4 text-white"><h1 class="text-2xl font-bold"> </h1> <!></div></div>'),fs=u("<span> </span>"),ms=u("<span> </span>"),ps=u('<div class="flex gap-4 text-sm"><!> <!></div>'),gs=u('<div class="mb-6"><h1 class="text-2xl font-bold"> </h1> <!> <div class="mt-2 border-b-2"></div></div>'),_s=u("<span> </span>"),hs=u("<span> </span>"),bs=u('<div class="flex gap-4 text-sm"><!> <!></div>'),ys=u('<div class="mb-6"><div class="flex flex-wrap items-baseline justify-between"><h1 class="text-2xl font-bold"> </h1> <!></div> <div class="mt-2 border-b border-gray-200"></div></div>'),xs=u("<span> </span>"),ws=u("<span> </span>"),Ds=u('<div class="flex justify-center gap-4 text-sm"><!> <!></div>'),$s=u('<div class="mb-6"><h1 class="text-center text-3xl font-bold"> </h1> <!> <div class="mt-2 border-b-4"></div></div>'),Es=u("<span> </span>"),Ss=u("<span> </span>"),Ts=u('<div class="flex justify-center gap-4 text-sm"><!> <!></div>'),Cs=u('<div class="mb-6 text-center"><h1 class="text-2xl font-bold"> </h1> <!> <div class="mt-2 border-b border-gray-300"></div></div>'),As=u('<div class="prose prose-sm max-w-none text-sm"><!></div>'),Os=u('<p class="mb-2 text-sm"> </p>'),zs=u('<p class="text-sm text-gray-500">No summary provided</p>'),Is=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold uppercase">Professional Summary</h2> <!></div>'),ks=u('<div class="prose max-w-none"><!></div>'),Ps=u('<p class="mb-2"> </p>'),Rs=u('<p class="text-gray-500">No summary provided</p>'),Ls=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold">Professional Summary</h2> <!></div>'),Ns=u('<div class="prose prose-sm max-w-none text-sm"><!></div>'),js=u('<p class="mb-1 text-sm"> </p>'),Fs=u('<p class="text-xs text-gray-500">No summary provided</p>'),Ms=u('<div class="mb-4"><h2 class="text-md mb-2 font-semibold">Summary</h2> <!></div>'),Bs=u('<div class="prose max-w-none"><!></div>'),Hs=u('<p class="mb-2 font-medium"> </p>'),Gs=u('<p class="text-gray-500">No summary provided</p>'),Us=u('<div class="mb-4"><h2 class="mb-2 text-xl font-bold uppercase">Professional Summary</h2> <!></div>'),Ws=u('<div class="prose max-w-none"><!></div>'),Vs=u('<p class="mb-2"> </p>'),Zs=u('<p class="text-gray-500">No summary provided</p>'),Ys=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold uppercase">Professional Summary</h2> <!></div>'),qs=u("<li> </li>"),Ks=u('<ul class="mt-1 list-disc pl-5 text-sm"></ul>'),Xs=u('<li><div class="flex justify-between"><strong> </strong> <span class="text-sm font-semibold"> </span></div> <div class="flex justify-between"><em class="font-semibold"> </em> <span class="text-sm"></span></div> <!></li>'),Js=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold uppercase">Professional Experience</h2> <ul class="list-none space-y-3 pl-0"></ul></div>'),Qs=u('<p class="mt-1 text-sm"> </p>'),el=u('<li><div class="flex justify-between"><strong class="text-lg"> </strong> <span class="text-sm"> </span></div> <div><em> </em></div> <!></li>'),tl=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold">Experience</h2> <ul class="list-none space-y-4 pl-0"></ul></div>'),rl=u('<p class="mt-1 text-xs"> </p>'),al=u('<li><div class="flex justify-between"><span> <strong> </strong></span> <span class="text-xs"> </span></div> <!></li>'),ol=u('<div class="mb-4"><h2 class="text-md mb-2 font-semibold">Experience</h2> <ul class="list-none space-y-2 pl-0"></ul></div>'),nl=u('<p class="mt-1"> </p>'),il=u('<li><div class="flex justify-between"><strong class="text-lg"> </strong> <span class="font-semibold"> </span></div> <div class="font-semibold"><em> </em></div> <!></li>'),sl=u('<div class="mb-4"><h2 class="mb-2 text-xl font-bold uppercase">Experience</h2> <ul class="list-none space-y-4 pl-0"></ul></div>'),ll=u('<p class="mt-1 text-sm"> </p>'),dl=u('<li><div class="flex justify-between"><strong> </strong> <span class="text-sm"> </span></div> <div><em> </em></div> <!></li>'),cl=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold uppercase">Experience</h2> <ul class="list-none space-y-3 pl-0"></ul></div>'),vl=u('<div class="text-sm"> </div>'),ul=u('<li><div class="flex justify-between"><strong> </strong> <span class="text-sm font-semibold"> </span></div> <div><span class="font-semibold"> </span></div> <!></li>'),fl=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold uppercase">Education</h2> <ul class="list-none space-y-3 pl-0"></ul></div>'),ml=u('<div class="text-sm"> </div>'),pl=u('<li><div class="flex justify-between"><strong class="text-lg"> </strong> <span class="text-sm"> </span></div> <div> </div> <!></li>'),gl=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold">Education</h2> <ul class="list-none space-y-3 pl-0"></ul></div>'),_l=u('<div class="text-xs"> <!></div>'),hl=u('<li><div class="flex justify-between"><span> <strong> </strong></span> <span class="text-xs"> </span></div> <!></li>'),bl=u('<div class="mb-4"><h2 class="text-md mb-2 font-semibold">Education</h2> <ul class="list-none space-y-2 pl-0"></ul></div>'),yl=u("<div> </div>"),xl=u('<li><div class="flex justify-between"><strong class="text-lg"> </strong> <span class="font-semibold"> </span></div> <div class="font-semibold"> </div> <!></li>'),wl=u('<div class="mb-4"><h2 class="mb-2 text-xl font-bold uppercase">Education</h2> <ul class="list-none space-y-3 pl-0"></ul></div>'),Dl=u('<div class="text-sm"> </div>'),$l=u('<li><div class="flex justify-between"><strong> </strong> <span class="text-sm"> </span></div> <div> </div> <!></li>'),El=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold uppercase">Education</h2> <ul class="list-none space-y-3 pl-0"></ul></div>'),Sl=u('<span class="text-xs"> </span>'),Tl=u('<span class="inline-block rounded px-2 py-1 text-sm"> <!></span>'),Cl=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold uppercase">Skills</h2> <div class="grid grid-cols-3 gap-2"></div></div>'),Al=u('<span class="text-xs"> </span>'),Ol=u('<span class="inline-block rounded-full px-3 py-1 text-sm"> <!></span>'),zl=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold">Skills</h2> <div class="flex flex-wrap gap-2"></div></div>'),Il=u("<span> <!></span>"),kl=u('<div class="mb-4"><h2 class="text-md mb-2 font-semibold">Skills</h2> <div class="text-xs"></div></div>'),Pl=u('<span class="text-sm font-semibold"> </span>'),Rl=u('<div class="flex items-center"><span> </span> <span class="ml-2"> <!></span></div>'),Ll=u('<div class="mb-4"><h2 class="mb-2 text-xl font-bold uppercase">Skills</h2> <div class="grid grid-cols-2 gap-2"></div></div>'),Nl=u('<span class="text-xs"> </span>'),jl=u('<span class="inline-block rounded px-2 py-1 text-sm"> <!></span>'),Fl=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold uppercase">Skills</h2> <div class="flex flex-wrap gap-2"></div></div>'),Ml=u('<p class="mt-1 text-sm"> </p>'),Bl=u("<li><div><strong> </strong></div> <!></li>"),Hl=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold uppercase">Projects</h2> <ul class="list-none space-y-3 pl-0"></ul> <div class="mt-4 hidden"><pre class="text-xs"> </pre></div></div>'),Gl=u("<li><span> </span> </li>"),Ul=u('<div class="mb-4"><h2 class="mb-2 text-lg font-bold uppercase">Certifications</h2> <ul class="list-none space-y-2 pl-0"></ul> <div class="mt-4 hidden"><pre class="text-xs"> </pre></div></div>'),Wl=u('<div class="mt-2 rounded-md bg-zinc-900 p-4 text-white"><!></div>'),Vl=u('<div class="resume-preview max-h-min w-full"><div class="resume-document mx-auto overflow-hidden rounded bg-white shadow-lg"><div><!> <!> <!> <!> <!> <!> <!></div></div>  <div class="mt-4 text-xs text-gray-500"><p> </p> <p> </p></div> <div class="mt-4"><button type="button" class="flex w-full items-center justify-between rounded-md bg-zinc-800 p-3 text-left text-sm font-medium text-zinc-300 hover:bg-zinc-700"><span>Superform Debug Panel</span> <svg fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button> <!></div></div>');function xc(t,a){ut(a,!1);const[r,s]=xr(),c=()=>tr(Ht,"$designStore",r),f=ot(),y=ot(),m=ot();let v=Ke(a,"formData",8),U=Ke(a,"designData",8,void 0),i=ot(c());const K={letter:{width:816,height:1056},a4:{width:794,height:1123},legal:{width:816,height:1344}},I=Da(null);let z=ot(!1);const E=ls.subscribe(oe=>{oe&&(console.log("Resume data from store:",oe),I.set(oe))});Gt(()=>{if(typeof window<"u"){const oe=te=>{console.log("Resume data updated event received:",te.detail),te.detail&&te.detail.data&&I.set(te.detail.data)},k=te=>{console.log("Header data updated event received:",te.detail),te.detail&&te.detail.header&&I.update(de=>(de||(de={}),{...de,header:te.detail.header}))},re=te=>{console.log("Skills data updated event received:",te.detail),te.detail&&te.detail.skills&&I.update(de=>(de||(de={}),{...de,skills:te.detail.skills}))};return window.addEventListener("resume-data-updated",oe),window.addEventListener("header-data-updated",k),window.addEventListener("skills-data-updated",re),()=>{window.removeEventListener("resume-data-updated",oe),window.removeEventListener("header-data-updated",k),window.removeEventListener("skills-data-updated",re)}}}),pa(()=>{E()}),Gt(()=>{console.log("Resume component mounted with data:",e(m))}),qe(()=>(e(i),c(),yt(U()),Ht),()=>{_e(i,c()),U()&&U().data&&(Ht.set(U().data),_e(i,U().data),console.log("Design updated:",e(i)),e(i).layout&&(console.log("Layout template selected:",e(i).layout),e(i).layout==="professional"?(dt(i,e(i).headerStyle="dark-header"),dt(i,e(i).sectionStyle="two-column")):e(i).layout==="modern"?(dt(i,e(i).headerStyle="left-aligned"),dt(i,e(i).sectionStyle="clean")):e(i).layout==="minimalist"?(dt(i,e(i).headerStyle="compact"),dt(i,e(i).sectionStyle="minimal")):e(i).layout==="executive"?(dt(i,e(i).headerStyle="bold"),dt(i,e(i).sectionStyle="divided")):e(i).layout==="classic"&&(dt(i,e(i).headerStyle="centered"),dt(i,e(i).sectionStyle="bordered"))))}),qe(()=>e(i),()=>{e(i).pageSize&&(e(i).pageSize.includes("Letter")?dt(i,e(i).paperSize="letter"):e(i).pageSize.includes("A4")?dt(i,e(i).paperSize="a4"):e(i).pageSize.includes("Legal")&&dt(i,e(i).paperSize="legal"))}),qe(()=>e(i),()=>{_e(f,K[e(i).paperSize]||K.letter)}),qe(()=>e(i),()=>{_e(y,e(i).margin==="Small"?"0.5in":e(i).margin==="Large"?"1in":"0.75in")}),qe(()=>yt(v()),()=>{v()&&(console.log("Form data received in Resume component:",v()),I.set(v()))}),qe(()=>yt(v()),()=>{_e(m,v()||{header:{name:"",email:"",phone:""},summary:{content:""},experience:[],education:[],skills:[],projects:[],certifications:[]})}),qe(()=>e(m),()=>{console.log("Current resumeData:",e(m))}),qe(()=>e(m),()=>{console.log("Resume data updated:",e(m))}),Tt(),xt();var G=Vl(),V=n(G),ae=n(V),S=n(ae);{var B=oe=>{var k=us(),re=n(k),te=n(re),de=n(te,!0);o(te);var j=l(te,2);{var L=x=>{var N=vs(),C=n(N);{var R=w=>{var _=ds(),h=n(_,!0);o(_),b(()=>D(h,v().header.email)),d(w,_)};O(C,w=>{var _,h;(h=(_=v())==null?void 0:_.header)!=null&&h.email&&w(R)})}var P=l(C,2);{var F=w=>{var _=cs(),h=n(_,!0);o(_),b(()=>D(h,v().header.phone)),d(w,_)};O(P,w=>{var _,h;(h=(_=v())==null?void 0:_.header)!=null&&h.phone&&w(F)})}o(N),d(x,N)};O(j,x=>{var N,C,R,P;((C=(N=v())==null?void 0:N.header)!=null&&C.email||(P=(R=v())==null?void 0:R.header)!=null&&P.phone)&&x(L)})}o(re),o(k),b(()=>{var x,N;return D(de,((N=(x=v())==null?void 0:x.header)==null?void 0:N.name)||"")}),d(oe,k)},le=(oe,k)=>{{var re=de=>{var j=gs(),L=n(j),x=n(L,!0);o(L);var N=l(L,2);{var C=P=>{var F=ps(),w=n(F);{var _=g=>{var p=fs(),$=n(p,!0);o(p),b(()=>D($,v().header.email)),d(g,p)};O(w,g=>{var p,$;($=(p=v())==null?void 0:p.header)!=null&&$.email&&g(_)})}var h=l(w,2);{var A=g=>{var p=ms(),$=n(p,!0);o(p),b(()=>D($,v().header.phone)),d(g,p)};O(h,g=>{var p,$;($=(p=v())==null?void 0:p.header)!=null&&$.phone&&g(A)})}o(F),d(P,F)};O(N,P=>{var F,w,_,h;((w=(F=v())==null?void 0:F.header)!=null&&w.email||(h=(_=v())==null?void 0:_.header)!=null&&h.phone)&&P(C)})}var R=l(N,2);o(j),b(()=>{var P,F;he(L,`color: ${e(i).primaryColor??""}`),D(x,((F=(P=v())==null?void 0:P.header)==null?void 0:F.name)||""),he(R,`border-color: ${e(i).primaryColor??""}`)}),d(de,j)},te=(de,j)=>{{var L=N=>{var C=ys(),R=n(C),P=n(R),F=n(P,!0);o(P);var w=l(P,2);{var _=h=>{var A=bs(),g=n(A);{var p=J=>{var T=_s(),M=n(T,!0);o(T),b(()=>D(M,v().header.email)),d(J,T)};O(g,J=>{var T,M;(M=(T=v())==null?void 0:T.header)!=null&&M.email&&J(p)})}var $=l(g,2);{var H=J=>{var T=hs(),M=n(T,!0);o(T),b(()=>D(M,v().header.phone)),d(J,T)};O($,J=>{var T,M;(M=(T=v())==null?void 0:T.header)!=null&&M.phone&&J(H)})}o(A),d(h,A)};O(w,h=>{var A,g,p,$;((g=(A=v())==null?void 0:A.header)!=null&&g.email||($=(p=v())==null?void 0:p.header)!=null&&$.phone)&&h(_)})}o(R),Te(2),o(C),b(()=>{var h,A;he(P,`color: ${e(i).primaryColor??""}`),D(F,((A=(h=v())==null?void 0:h.header)==null?void 0:A.name)||"")}),d(N,C)},x=(N,C)=>{{var R=F=>{var w=$s(),_=n(w),h=n(_,!0);o(_);var A=l(_,2);{var g=$=>{var H=Ds(),J=n(H);{var T=Z=>{var se=xs(),ne=n(se,!0);o(se),b(()=>D(ne,v().header.email)),d(Z,se)};O(J,Z=>{var se,ne;(ne=(se=v())==null?void 0:se.header)!=null&&ne.email&&Z(T)})}var M=l(J,2);{var q=Z=>{var se=ws(),ne=n(se,!0);o(se),b(()=>D(ne,v().header.phone)),d(Z,se)};O(M,Z=>{var se,ne;(ne=(se=v())==null?void 0:se.header)!=null&&ne.phone&&Z(q)})}o(H),d($,H)};O(A,$=>{var H,J,T,M;((J=(H=v())==null?void 0:H.header)!=null&&J.email||(M=(T=v())==null?void 0:T.header)!=null&&M.phone)&&$(g)})}var p=l(A,2);o(w),b(()=>{var $,H;he(_,`color: ${e(i).primaryColor??""}`),D(h,((H=($=v())==null?void 0:$.header)==null?void 0:H.name)||""),he(p,`border-color: ${e(i).primaryColor??""}`)}),d(F,w)},P=F=>{var w=Cs(),_=n(w),h=n(_,!0);o(_);var A=l(_,2);{var g=p=>{var $=Ts(),H=n($);{var J=q=>{var Z=Es(),se=n(Z,!0);o(Z),b(()=>D(se,v().header.email)),d(q,Z)};O(H,q=>{var Z,se;(se=(Z=v())==null?void 0:Z.header)!=null&&se.email&&q(J)})}var T=l(H,2);{var M=q=>{var Z=Ss(),se=n(Z,!0);o(Z),b(()=>D(se,v().header.phone)),d(q,Z)};O(T,q=>{var Z,se;(se=(Z=v())==null?void 0:Z.header)!=null&&se.phone&&q(M)})}o($),d(p,$)};O(A,p=>{var $,H,J,T;((H=($=v())==null?void 0:$.header)!=null&&H.email||(T=(J=v())==null?void 0:J.header)!=null&&T.phone)&&p(g)})}Te(2),o(w),b(()=>{var p,$;he(_,`color: ${e(i).primaryColor??""}`),D(h,(($=(p=v())==null?void 0:p.header)==null?void 0:$.name)||"")}),d(F,w)};O(N,F=>{e(i).headerStyle==="bold"||e(i).layout==="executive"?F(R):F(P,!1)},C)}};O(de,N=>{e(i).headerStyle==="compact"||e(i).layout==="minimalist"?N(L):N(x,!1)},j)}};O(oe,de=>{e(i).headerStyle==="left-aligned"||e(i).layout==="modern"?de(re):de(te,!1)},k)}};O(S,oe=>{e(i).headerStyle==="dark-header"||e(i).layout==="professional"?oe(B):oe(le,!1)})}var ce=l(S,2);{var pe=oe=>{var k=Fe(),re=ge(k);{var te=j=>{var L=Is(),x=n(L),N=l(x,2);{var C=P=>{var F=As(),w=n(F);qt(w,()=>v().summary.content),o(F),d(P,F)},R=(P,F)=>{{var w=h=>{var A=Fe(),g=ge(A);Se(g,1,()=>v().summary.content.split(`

`),ze,(p,$)=>{var H=Fe(),J=ge(H);{var T=M=>{var q=Os(),Z=n(q,!0);o(q),b(()=>D(Z,e($))),d(M,q)};O(J,M=>{e($).trim()&&M(T)})}d(p,H)}),d(h,A)},_=h=>{var A=zs();d(h,A)};O(P,h=>{v().summary&&v().summary.content&&typeof v().summary.content=="string"?h(w):h(_,!1)},F)}};O(N,P=>{v().summary&&v().summary.content&&typeof v().summary.content=="string"&&v().summary.content.includes("<")?P(C):P(R,!1)})}o(L),b(()=>he(x,`color: ${e(i).primaryColor??""}`)),d(j,L)},de=(j,L)=>{{var x=C=>{var R=Ls(),P=n(R),F=l(P,2);{var w=h=>{var A=ks(),g=n(A);qt(g,()=>v().summary.content),o(A),d(h,A)},_=(h,A)=>{{var g=$=>{var H=Fe(),J=ge(H);Se(J,1,()=>v().summary.content.split(`

`),ze,(T,M)=>{var q=Fe(),Z=ge(q);{var se=ne=>{var Oe=Ps(),Ce=n(Oe,!0);o(Oe),b(()=>D(Ce,e(M))),d(ne,Oe)};O(Z,ne=>{e(M).trim()&&ne(se)})}d(T,q)}),d($,H)},p=$=>{var H=Rs();d($,H)};O(h,$=>{v().summary&&v().summary.content&&typeof v().summary.content=="string"?$(g):$(p,!1)},A)}};O(F,h=>{v().summary&&v().summary.content&&typeof v().summary.content=="string"&&v().summary.content.includes("<")?h(w):h(_,!1)})}o(R),b(()=>he(P,`color: ${e(i).primaryColor??""}`)),d(C,R)},N=(C,R)=>{{var P=w=>{var _=Ms(),h=n(_),A=l(h,2);{var g=$=>{var H=Ns(),J=n(H);qt(J,()=>v().summary.content),o(H),d($,H)},p=($,H)=>{{var J=M=>{var q=Fe(),Z=ge(q);Se(Z,1,()=>v().summary.content.split(`

`),ze,(se,ne)=>{var Oe=Fe(),Ce=ge(Oe);{var Re=Ne=>{var Be=js(),Ye=n(Be,!0);o(Be),b(()=>D(Ye,e(ne))),d(Ne,Be)};O(Ce,Ne=>{e(ne).trim()&&Ne(Re)})}d(se,Oe)}),d(M,q)},T=M=>{var q=Fs();d(M,q)};O($,M=>{v().summary&&v().summary.content&&typeof v().summary.content=="string"?M(J):M(T,!1)},H)}};O(A,$=>{v().summary&&v().summary.content&&typeof v().summary.content=="string"&&v().summary.content.includes("<")?$(g):$(p,!1)})}o(_),b(()=>he(h,`color: ${e(i).primaryColor??""}`)),d(w,_)},F=(w,_)=>{{var h=g=>{var p=Us(),$=n(p),H=l($,2);{var J=M=>{var q=Bs(),Z=n(q);qt(Z,()=>v().summary.content),o(q),d(M,q)},T=(M,q)=>{{var Z=ne=>{var Oe=Fe(),Ce=ge(Oe);Se(Ce,1,()=>v().summary.content.split(`

`),ze,(Re,Ne)=>{var Be=Fe(),Ye=ge(Be);{var He=Ge=>{var gt=Hs(),Or=n(gt,!0);o(gt),b(()=>D(Or,e(Ne))),d(Ge,gt)};O(Ye,Ge=>{e(Ne).trim()&&Ge(He)})}d(Re,Be)}),d(ne,Oe)},se=ne=>{var Oe=Gs();d(ne,Oe)};O(M,ne=>{v().summary&&v().summary.content&&typeof v().summary.content=="string"?ne(Z):ne(se,!1)},q)}};O(H,M=>{v().summary&&v().summary.content&&typeof v().summary.content=="string"&&v().summary.content.includes("<")?M(J):M(T,!1)})}o(p),b(()=>he($,`color: ${e(i).primaryColor??""}; border-bottom: 2px solid ${e(i).primaryColor??""};`)),d(g,p)},A=g=>{var p=Ys(),$=n(p),H=l($,2);{var J=M=>{var q=Ws(),Z=n(q);qt(Z,()=>e(m).summary.content),o(q),d(M,q)},T=(M,q)=>{{var Z=ne=>{var Oe=Fe(),Ce=ge(Oe);Se(Ce,1,()=>e(m).summary.content.split(`

`),ze,(Re,Ne)=>{var Be=Fe(),Ye=ge(Be);{var He=Ge=>{var gt=Vs(),Or=n(gt,!0);o(gt),b(()=>D(Or,e(Ne))),d(Ge,gt)};O(Ye,Ge=>{e(Ne).trim()&&Ge(He)})}d(Re,Be)}),d(ne,Oe)},se=ne=>{var Oe=Zs();d(ne,Oe)};O(M,ne=>{e(m).summary&&e(m).summary.content&&typeof e(m).summary.content=="string"?ne(Z):ne(se,!1)},q)}};O(H,M=>{e(m).summary&&e(m).summary.content&&typeof e(m).summary.content=="string"&&e(m).summary.content.includes("<")?M(J):M(T,!1)})}o(p),b(()=>he($,`color: ${e(i).primaryColor??""}`)),d(g,p)};O(w,g=>{e(i).sectionStyle==="divided"||e(i).layout==="executive"?g(h):g(A,!1)},_)}};O(C,w=>{e(i).sectionStyle==="minimal"||e(i).layout==="minimalist"?w(P):w(F,!1)},R)}};O(j,C=>{e(i).sectionStyle==="clean"||e(i).layout==="modern"?C(x):C(N,!1)},L)}};O(re,j=>{e(i).sectionStyle==="two-column"||e(i).layout==="professional"?j(te):j(de,!1)})}d(oe,k)};O(ce,oe=>{var k,re;(re=(k=v())==null?void 0:k.summary)!=null&&re.content&&oe(pe)})}var be=l(ce,2);{var Ae=oe=>{var k=Fe(),re=ge(k);{var te=j=>{var L=Js(),x=n(L),N=l(x,2);Se(N,5,()=>e(m).experience,ze,(C,R)=>{var P=Xs(),F=n(P),w=n(F),_=n(w,!0);o(w);var h=l(w,2),A=n(h);o(h),o(F);var g=l(F,2),p=n(g),$=n(p,!0);o(p),Te(2),o(g);var H=l(g,2);{var J=T=>{var M=Ks();Se(M,5,()=>e(R).description.split(`
`),ze,(q,Z)=>{var se=Fe(),ne=ge(se);{var Oe=Ce=>{var Re=qs(),Ne=n(Re,!0);o(Re),b(Be=>D(Ne,Be),[()=>e(Z).trim()],$t),d(Ce,Re)};O(ne,Ce=>{e(Z).trim()&&Ce(Oe)})}d(q,se)}),o(M),d(T,M)};O(H,T=>{e(R).description&&T(J)})}o(P),b(()=>{he(w,`color: ${e(i).accentColor??""}`),D(_,e(R).company||""),D(A,`${(e(R).startDate||"")??""}${e(R).startDate&&e(R).endDate?" - ":""}${(e(R).endDate||"")??""}`),D($,e(R).jobTitle||"")}),d(C,P)}),o(N),o(L),b(()=>he(x,`color: ${e(i).primaryColor??""}`)),d(j,L)},de=(j,L)=>{{var x=C=>{var R=tl(),P=n(R),F=l(P,2);Se(F,5,()=>e(m).experience,ze,(w,_)=>{var h=el(),A=n(h),g=n(A),p=n(g,!0);o(g);var $=l(g,2),H=n($);o($),o(A);var J=l(A,2),T=n(J),M=n(T,!0);o(T),o(J);var q=l(J,2);{var Z=se=>{var ne=Qs(),Oe=n(ne,!0);o(ne),b(()=>D(Oe,e(_).description)),d(se,ne)};O(q,se=>{e(_).description&&se(Z)})}o(h),b(()=>{he(g,`color: ${e(i).accentColor??""}`),D(p,e(_).company||""),D(H,`${(e(_).startDate||"")??""}${e(_).startDate&&e(_).endDate?" - ":""}${(e(_).endDate||"")??""}`),D(M,e(_).jobTitle||"")}),d(w,h)}),o(F),o(R),b(()=>he(P,`color: ${e(i).primaryColor??""}`)),d(C,R)},N=(C,R)=>{{var P=w=>{var _=ol(),h=n(_),A=l(h,2);Se(A,5,()=>e(m).experience,ze,(g,p)=>{var $=al(),H=n($),J=n(H),T=n(J),M=l(T),q=n(M,!0);o(M),o(J);var Z=l(J,2),se=n(Z);o(Z),o(H);var ne=l(H,2);{var Oe=Ce=>{var Re=rl(),Ne=n(Re,!0);o(Re),b(()=>D(Ne,e(p).description)),d(Ce,Re)};O(ne,Ce=>{e(p).description&&Ce(Oe)})}o($),b(()=>{D(T,`${(e(p).jobTitle||"")??""} @ `),D(q,e(p).company||""),D(se,`${(e(p).startDate||"")??""}${e(p).startDate&&e(p).endDate?" - ":""}${(e(p).endDate||"")??""}`)}),d(g,$)}),o(A),o(_),b(()=>he(h,`color: ${e(i).primaryColor??""}`)),d(w,_)},F=(w,_)=>{{var h=g=>{var p=sl(),$=n(p),H=l($,2);Se(H,5,()=>e(m).experience,ze,(J,T)=>{var M=il(),q=n(M),Z=n(q),se=n(Z,!0);o(Z);var ne=l(Z,2),Oe=n(ne);o(ne),o(q);var Ce=l(q,2),Re=n(Ce),Ne=n(Re,!0);o(Re),o(Ce);var Be=l(Ce,2);{var Ye=He=>{var Ge=nl(),gt=n(Ge,!0);o(Ge),b(()=>D(gt,e(T).description)),d(He,Ge)};O(Be,He=>{e(T).description&&He(Ye)})}o(M),b(()=>{he(Z,`color: ${e(i).accentColor??""}`),D(se,e(T).company||""),D(Oe,`${(e(T).startDate||"")??""}${e(T).startDate&&e(T).endDate?" - ":""}${(e(T).endDate||"")??""}`),D(Ne,e(T).jobTitle||"")}),d(J,M)}),o(H),o(p),b(()=>he($,`color: ${e(i).primaryColor??""}; border-bottom: 2px solid ${e(i).primaryColor??""};`)),d(g,p)},A=g=>{var p=cl(),$=n(p),H=l($,2);Se(H,5,()=>e(m).experience,ze,(J,T)=>{var M=dl(),q=n(M),Z=n(q),se=n(Z,!0);o(Z);var ne=l(Z,2),Oe=n(ne);o(ne),o(q);var Ce=l(q,2),Re=n(Ce),Ne=n(Re,!0);o(Re),o(Ce);var Be=l(Ce,2);{var Ye=He=>{var Ge=ll(),gt=n(Ge,!0);o(Ge),b(()=>D(gt,e(T).description)),d(He,Ge)};O(Be,He=>{e(T).description&&He(Ye)})}o(M),b(()=>{he(Z,`color: ${e(i).accentColor??""}`),D(se,e(T).company||""),D(Oe,`${(e(T).startDate||"")??""}${e(T).startDate&&e(T).endDate?" - ":""}${(e(T).endDate||"")??""}`),D(Ne,e(T).jobTitle||"")}),d(J,M)}),o(H),o(p),b(()=>he($,`color: ${e(i).primaryColor??""}`)),d(g,p)};O(w,g=>{e(i).sectionStyle==="divided"||e(i).layout==="executive"?g(h):g(A,!1)},_)}};O(C,w=>{e(i).sectionStyle==="minimal"||e(i).layout==="minimalist"?w(P):w(F,!1)},R)}};O(j,C=>{e(i).sectionStyle==="clean"||e(i).layout==="modern"?C(x):C(N,!1)},L)}};O(re,j=>{e(i).sectionStyle==="two-column"||e(i).layout==="professional"?j(te):j(de,!1)})}d(oe,k)};O(be,oe=>{var k;(k=e(m))!=null&&k.experience&&e(m).experience.length>0&&oe(Ae)})}var Y=l(be,2);{var Q=oe=>{var k=Fe(),re=ge(k);{var te=j=>{var L=fl(),x=n(L),N=l(x,2);Se(N,5,()=>e(m).education,ze,(C,R)=>{var P=ul(),F=n(P),w=n(F),_=n(w,!0);o(w);var h=l(w,2),A=n(h);o(h),o(F);var g=l(F,2),p=n(g),$=n(p);o(p),o(g);var H=l(g,2);{var J=T=>{var M=vl(),q=n(M);o(M),b(()=>D(q,`GPA: ${e(R).gpa??""}`)),d(T,M)};O(H,T=>{e(R).gpa&&T(J)})}o(P),b(()=>{he(w,`color: ${e(i).accentColor??""}`),D(_,e(R).school||""),D(A,`${(e(R).startDate||"")??""}${e(R).startDate&&e(R).endDate?" - ":""}${(e(R).endDate||"")??""}`),D($,`${(e(R).degree||"")??""}${e(R).degree&&e(R).major?" in ":""}${(e(R).major||"")??""}`)}),d(C,P)}),o(N),o(L),b(()=>he(x,`color: ${e(i).primaryColor??""}`)),d(j,L)},de=(j,L)=>{{var x=C=>{var R=gl(),P=n(R),F=l(P,2);Se(F,5,()=>e(m).education,ze,(w,_)=>{var h=pl(),A=n(h),g=n(A),p=n(g,!0);o(g);var $=l(g,2),H=n($);o($),o(A);var J=l(A,2),T=n(J);o(J);var M=l(J,2);{var q=Z=>{var se=ml(),ne=n(se);o(se),b(()=>D(ne,`GPA: ${e(_).gpa??""}`)),d(Z,se)};O(M,Z=>{e(_).gpa&&Z(q)})}o(h),b(()=>{he(g,`color: ${e(i).accentColor??""}`),D(p,e(_).school||""),D(H,`${(e(_).startDate||"")??""}${e(_).startDate&&e(_).endDate?" - ":""}${(e(_).endDate||"")??""}`),D(T,`${(e(_).degree||"")??""}${e(_).degree&&e(_).major?" in ":""}${(e(_).major||"")??""}`)}),d(w,h)}),o(F),o(R),b(()=>he(P,`color: ${e(i).primaryColor??""}`)),d(C,R)},N=(C,R)=>{{var P=w=>{var _=bl(),h=n(_),A=l(h,2);Se(A,5,()=>e(m).education,ze,(g,p)=>{var $=hl(),H=n($),J=n(H),T=n(J),M=l(T),q=n(M,!0);o(M),o(J);var Z=l(J,2),se=n(Z);o(Z),o(H);var ne=l(H,2);{var Oe=Ce=>{var Re=_l(),Ne=n(Re),Be=l(Ne);{var Ye=He=>{var Ge=Le();b(()=>D(Ge,`${e(p).major?" | ":""}GPA: ${e(p).gpa??""}`)),d(He,Ge)};O(Be,He=>{e(p).gpa&&He(Ye)})}o(Re),b(()=>D(Ne,`${(e(p).major||"")??""} `)),d(Ce,Re)};O(ne,Ce=>{(e(p).major||e(p).gpa)&&Ce(Oe)})}o($),b(()=>{D(T,`${(e(p).degree||"")??""} @ `),D(q,e(p).school||""),D(se,`${(e(p).startDate||"")??""}${e(p).startDate&&e(p).endDate?" - ":""}${(e(p).endDate||"")??""}`)}),d(g,$)}),o(A),o(_),b(()=>he(h,`color: ${e(i).primaryColor??""}`)),d(w,_)},F=(w,_)=>{{var h=g=>{var p=wl(),$=n(p),H=l($,2);Se(H,5,()=>e(m).education,ze,(J,T)=>{var M=xl(),q=n(M),Z=n(q),se=n(Z,!0);o(Z);var ne=l(Z,2),Oe=n(ne);o(ne),o(q);var Ce=l(q,2),Re=n(Ce);o(Ce);var Ne=l(Ce,2);{var Be=Ye=>{var He=yl(),Ge=n(He);o(He),b(()=>D(Ge,`GPA: ${e(T).gpa??""}`)),d(Ye,He)};O(Ne,Ye=>{e(T).gpa&&Ye(Be)})}o(M),b(()=>{he(Z,`color: ${e(i).accentColor??""}`),D(se,e(T).school||""),D(Oe,`${(e(T).startDate||"")??""}${e(T).startDate&&e(T).endDate?" - ":""}${(e(T).endDate||"")??""}`),D(Re,`${(e(T).degree||"")??""}${e(T).degree&&e(T).major?" in ":""}${(e(T).major||"")??""}`)}),d(J,M)}),o(H),o(p),b(()=>he($,`color: ${e(i).primaryColor??""}; border-bottom: 2px solid ${e(i).primaryColor??""};`)),d(g,p)},A=g=>{var p=El(),$=n(p),H=l($,2);Se(H,5,()=>e(m).education,ze,(J,T)=>{var M=$l(),q=n(M),Z=n(q),se=n(Z,!0);o(Z);var ne=l(Z,2),Oe=n(ne);o(ne),o(q);var Ce=l(q,2),Re=n(Ce);o(Ce);var Ne=l(Ce,2);{var Be=Ye=>{var He=Dl(),Ge=n(He);o(He),b(()=>D(Ge,`GPA: ${e(T).gpa??""}`)),d(Ye,He)};O(Ne,Ye=>{e(T).gpa&&Ye(Be)})}o(M),b(()=>{he(Z,`color: ${e(i).accentColor??""}`),D(se,e(T).school||""),D(Oe,`${(e(T).startDate||"")??""}${e(T).startDate&&e(T).endDate?" - ":""}${(e(T).endDate||"")??""}`),D(Re,`${(e(T).degree||"")??""}${e(T).degree&&e(T).major?" in ":""}${(e(T).major||"")??""}`)}),d(J,M)}),o(H),o(p),b(()=>he($,`color: ${e(i).primaryColor??""}`)),d(g,p)};O(w,g=>{e(i).sectionStyle==="divided"||e(i).layout==="executive"?g(h):g(A,!1)},_)}};O(C,w=>{e(i).sectionStyle==="minimal"||e(i).layout==="minimalist"?w(P):w(F,!1)},R)}};O(j,C=>{e(i).sectionStyle==="clean"||e(i).layout==="modern"?C(x):C(N,!1)},L)}};O(re,j=>{e(i).sectionStyle==="two-column"||e(i).layout==="professional"?j(te):j(de,!1)})}d(oe,k)};O(Y,oe=>{var k;(k=e(m))!=null&&k.education&&e(m).education.length>0&&oe(Q)})}var fe=l(Y,2);{var xe=oe=>{var k=Fe(),re=ge(k);{var te=j=>{var L=Cl(),x=n(L),N=l(x,2);Se(N,5,()=>e(m).skills,ze,(C,R)=>{var P=Tl(),F=n(P),w=l(F);{var _=h=>{var A=Sl(),g=n(A);o(A),b(()=>D(g,`(${e(R).years??""} yrs)`)),d(h,A)};O(w,h=>{typeof e(R)=="object"&&e(R).years&&h(_)})}o(P),b(()=>{he(P,`
                  background-color: ${(e(i).backgroundColor==="#ffffff"?"#f3f4f6":e(i).backgroundColor)??""};
                  border: 1px solid ${e(i).accentColor??""};
                  color: ${e(i).textColor??""};
                `),D(F,`${(typeof e(R)=="string"?e(R):e(R).name||"")??""} `)}),d(C,P)}),o(N),o(L),b(()=>he(x,`color: ${e(i).primaryColor??""}`)),d(j,L)},de=(j,L)=>{{var x=C=>{var R=zl(),P=n(R),F=l(P,2);Se(F,5,()=>e(m).skills,ze,(w,_)=>{var h=Ol(),A=n(h),g=l(A);{var p=$=>{var H=Al(),J=n(H);o(H),b(()=>D(J,`(${e(_).years??""} yrs)`)),d($,H)};O(g,$=>{typeof e(_)=="object"&&e(_).years&&$(p)})}o(h),b(()=>{he(h,`
                  background-color: ${e(i).primaryColor??""};
                  color: white;
                `),D(A,`${(typeof e(_)=="string"?e(_):e(_).name||"")??""} `)}),d(w,h)}),o(F),o(R),b(()=>he(P,`color: ${e(i).primaryColor??""}`)),d(C,R)},N=(C,R)=>{{var P=w=>{var _=kl(),h=n(_),A=l(h,2);Se(A,5,()=>e(m).skills,ze,(g,p,$)=>{var H=Il(),J=n(H),T=l(J);{var M=q=>{var Z=Le("•");d(q,Z)};O(T,q=>{$<e(m).skills.length-1&&q(M)})}o(H),b(()=>D(J,`${(typeof e(p)=="string"?e(p):e(p).name||"")??""} `)),d(g,H)}),o(A),o(_),b(()=>he(h,`color: ${e(i).primaryColor??""}`)),d(w,_)},F=(w,_)=>{{var h=g=>{var p=Ll(),$=n(p),H=l($,2);Se(H,5,()=>e(m).skills,ze,(J,T)=>{var M=Rl(),q=n(M),Z=n(q,!0);o(q);var se=l(q,2),ne=n(se),Oe=l(ne);{var Ce=Re=>{var Ne=Pl(),Be=n(Ne);o(Ne),b(()=>D(Be,`(${e(T).years??""} yrs)`)),d(Re,Ne)};O(Oe,Re=>{typeof e(T)=="object"&&e(T).years&&Re(Ce)})}o(se),o(M),b(()=>{he(q,`color: ${e(i).accentColor??""}`),D(Z,e(i).bulletIcon||"•"),D(ne,`${(typeof e(T)=="string"?e(T):e(T).name||"")??""} `)}),d(J,M)}),o(H),o(p),b(()=>he($,`color: ${e(i).primaryColor??""}; border-bottom: 2px solid ${e(i).primaryColor??""};`)),d(g,p)},A=g=>{var p=Fl(),$=n(p),H=l($,2);Se(H,5,()=>e(m).skills,ze,(J,T)=>{var M=jl(),q=n(M),Z=l(q);{var se=ne=>{var Oe=Nl(),Ce=n(Oe);o(Oe),b(()=>D(Ce,`(${e(T).years??""} yrs)`)),d(ne,Oe)};O(Z,ne=>{typeof e(T)=="object"&&e(T).years&&ne(se)})}o(M),b(()=>{he(M,`
                  background-color: ${(e(i).backgroundColor==="#ffffff"?"#f3f4f6":e(i).backgroundColor)??""};
                  border: 1px solid ${e(i).accentColor??""};
                  color: ${e(i).textColor??""};
                `),D(q,`${(typeof e(T)=="string"?e(T):e(T).name||"")??""} `)}),d(J,M)}),o(H),o(p),b(()=>he($,`color: ${e(i).primaryColor??""}`)),d(g,p)};O(w,g=>{e(i).sectionStyle==="divided"||e(i).layout==="executive"?g(h):g(A,!1)},_)}};O(C,w=>{e(i).sectionStyle==="minimal"||e(i).layout==="minimalist"?w(P):w(F,!1)},R)}};O(j,C=>{e(i).sectionStyle==="clean"||e(i).layout==="modern"?C(x):C(N,!1)},L)}};O(re,j=>{e(i).sectionStyle==="two-column"||e(i).layout==="professional"?j(te):j(de,!1)})}d(oe,k)};O(fe,oe=>{var k;(k=e(m))!=null&&k.skills&&e(m).skills.length>0&&oe(xe)})}var ke=l(fe,2);{var ve=oe=>{var k=Hl(),re=n(k),te=l(re,2);Se(te,5,()=>e(m).projects,ze,(x,N)=>{var C=Bl(),R=n(C),P=n(R),F=n(P,!0);o(P),o(R);var w=l(R,2);{var _=h=>{var A=Ml(),g=n(A,!0);o(A),b(()=>D(g,e(N).description)),d(h,A)};O(w,h=>{e(N).description&&h(_)})}o(C),b(()=>{he(P,`color: ${e(i).accentColor??""}`),D(F,e(N).name||"")}),d(x,C)}),o(te);var de=l(te,2),j=n(de),L=n(j,!0);o(j),o(de),o(k),b(x=>{he(re,`color: ${e(i).primaryColor??""}`),D(L,x)},[()=>{var x;return JSON.stringify(((x=e(m))==null?void 0:x.projects)||[],null,2)}],$t),d(oe,k)};O(ke,oe=>{var k;(k=e(m))!=null&&k.projects&&e(m).projects.length>0&&oe(ve)})}var ee=l(ke,2);{var X=oe=>{var k=Ul(),re=n(k),te=l(re,2);Se(te,5,()=>e(m).certifications,ze,(x,N)=>{var C=Gl(),R=n(C),P=n(R,!0);o(R);var F=l(R);o(C),b(()=>{he(R,`color: ${e(i).accentColor??""}`),D(P,e(i).bulletIcon||"•"),D(F,` ${(e(N).description||"")??""}`)}),d(x,C)}),o(te);var de=l(te,2),j=n(de),L=n(j,!0);o(j),o(de),o(k),b(x=>{he(re,`color: ${e(i).primaryColor??""}`),D(L,x)},[()=>{var x;return JSON.stringify(((x=e(m))==null?void 0:x.certifications)||[],null,2)}],$t),d(oe,k)};O(ee,oe=>{var k;(k=e(m))!=null&&k.certifications&&e(m).certifications.length>0&&oe(X)})}o(ae),o(V);var ie=l(V,2),we=n(ie),Ie=n(we);o(we);var $e=l(we,2),ue=n($e);o($e),o(ie);var W=l(ie,2),me=n(W),ye=l(n(me),2);o(me);var Ee=l(me,2);{var De=oe=>{var k=Wl(),re=n(k);wo(re,{get data(){return v()},display:!0,status:!0,label:"Form Data",collapsible:!1,collapsed:!1,stringTruncate:120,raw:!1,functions:!1,theme:"default"}),o(k),d(oe,k)};O(Ee,oe=>{e(z)&&oe(De)})}o(W),o(G),b(()=>{he(V,`
      width: ${e(f).width/2}px;
      height: ${e(f).height/2}px;
      transform: scale(0.9);
      transform-origin: top center;
      color: ${e(i).textColor??""};
      background-color: ${e(i).backgroundColor??""};
    `),he(ae,`
        padding: ${e(y)??""};
        font-family: ${e(i).font??""};
        font-size: ${e(i).fontSize??""};
        line-height: ${e(i).lineHeight??""};
      `),D(Ie,`Font: ${e(i).font??""}, ${e(i).fontSize??""}, ${e(i).lineHeight??""} line height`),D(ue,`Colors: Primary ${e(i).primaryColor??""}, Accent ${e(i).accentColor??""}`),je(me,"aria-expanded",e(z)),vt(ye,0,`h-5 w-5 transform transition-transform duration-200 ${e(z)?"rotate-180":""}`)}),it("click",me,()=>_e(z,!e(z))),d(t,G),ft(),s()}var Zl=(t,a)=>a("content"),Yl=(t,a)=>a("design"),ql=u('<div class="border-b-nuetral-400 flex items-center justify-between border border-l border-r border-t"><div class="flex gap-2 rounded-none border border-b border-l border-t border-zinc-800 bg-zinc-900"><button>Edit Content</button> <button>Edit Design</button></div> <div class="flex flex-row items-center justify-center"><button class="bg-blue-600 p-3 text-sm font-medium text-white transition hover:bg-blue-700">Fit Resume to Page</button> <i class="fa fa-arrow-rotate-left border-l border-zinc-800 px-4 py-3 text-sm font-medium text-white transition hover:border-zinc-700 hover:bg-zinc-800"></i></div></div> <!>',1);function wc(t,a){ut(a,!1);const[r,s]=xr(),c=()=>tr(f,"$activeTab",r),f=ha("content");function y(G){f.set(G)}xt();var m=ql(),v=ge(m),U=n(v),i=n(U);i.__click=[Zl,y];let K;var I=l(i,2);I.__click=[Yl,y];let z;o(U),Te(2),o(v);var E=l(v,2);Do(E,{class:"h-[calc(100vh-11rem)] px-6",children:(G,V)=>{var ae=Fe(),S=ge(ae);{var B=ce=>{var pe=Fe(),be=ge(pe);Lr(be,a,"content",{},null),d(ce,pe)},le=ce=>{var pe=Fe(),be=ge(pe);Lr(be,a,"design",{},null),d(ce,pe)};O(S,ce=>{c()==="content"?ce(B):ce(le,!1)})}d(G,ae)},$$slots:{default:!0}}),b((G,V)=>{K=vt(i,1,"border border-b border-l border-t border-zinc-800 px-4 py-3 text-sm font-medium transition",null,K,G),z=vt(I,1,"px-4 py-3 text-sm font-medium transition",null,z,V)},[()=>({selected:c()==="content"?"bg-blue-600 text-white border border-zinc-800":"bg-zinc-800 text-zinc-300"}),()=>({selected:c()==="design"?"bg-blue-600 text-white":"bg-zinc-800 text-zinc-300"})],$t),d(t,m),ft(),s()}yr(["click"]);var Kl=u('<span class="absolute -bottom-2 -right-2 rounded-full bg-blue-600 px-1.5 text-white"><i class="fa fa-check text-sm font-bold"></i></span>'),Xl=u('<button type="button" class="relative cursor-pointer text-left transition-all"><p class="mb-2 text-sm font-semibold text-zinc-300"> </p> <img/> <!></button>'),Jl=u('<button type="button" class="flex flex-col items-center"><div class="h-10 w-10 rounded-full border-2 transition-all"></div> <span class="mt-1 text-xs text-zinc-400"> </span></button>'),Ql=u("<option> </option>"),ed=u('<!> <div class="w-1/2"><select class="w-full rounded border border-zinc-600 bg-zinc-800 p-2 text-white"></select></div>',1),td=u("<!> <!>",1),rd=u("<option> </option>"),ad=u('<!> <div class="w-1/2"><select class="w-full rounded border border-zinc-600 bg-zinc-800 p-2 text-white"></select></div>',1),od=u("<!> <!>",1),nd=u("<option> </option>"),id=u('<!> <div class="w-1/2"><select class="w-full rounded border border-zinc-600 bg-zinc-800 p-2 text-white"></select></div>',1),sd=u("<!> <!>",1),ld=u("<option> </option>"),dd=u('<!> <select class="w-full rounded border border-zinc-600 bg-zinc-800 p-2 text-white"></select>',1),cd=u("<!> <!>",1),vd=u("<option> </option>"),ud=u('<!> <select class="w-full rounded border border-zinc-600 bg-zinc-800 p-2 text-white"></select>',1),fd=u("<!> <!>",1),md=u("<option> </option>"),pd=u('<!> <select class="w-full rounded border border-zinc-600 bg-zinc-800 p-2 text-white"></select>',1),gd=u("<!> <!>",1),_d=u("<option> </option>"),hd=u('<!> <select class="w-full rounded border border-zinc-600 bg-zinc-800 p-2 text-white"></select>',1),bd=u("<!> <!>",1),yd=(t,a)=>_e(a,!e(a)),xd=u('<div class="mt-2 rounded-md bg-zinc-900 p-4"><h4 class="mb-2 text-sm font-semibold text-zinc-300">Current Design Settings</h4> <div class="max-h-96 overflow-auto rounded bg-zinc-950 p-2 text-xs text-zinc-300"><pre> </pre></div> <h4 class="mb-2 mt-4 text-sm font-semibold text-zinc-300">Design Store</h4> <div class="max-h-96 overflow-auto rounded bg-zinc-950 p-2 text-xs text-zinc-300"><pre> </pre></div></div>'),wd=u('<form method="POST" class="space-y-8 py-6"><div><h3 class="mb-4 text-lg font-bold text-zinc-300">Layout Templates</h3> <div class="grid grid-cols-2 gap-5 sm:grid-cols-4"></div></div> <div class="mt-6"><h3 class="mb-4 text-lg font-bold text-zinc-300">Color Themes</h3> <div class="grid grid-cols-5 gap-3"></div></div> <!> <h3 class="text-secondary-400 mb-4 text-lg font-bold">Font & Text Formatting</h3> <div class="flex flex-col gap-4"><!> <!> <!></div> <!> <div class="flex flex-col gap-4"><h3 class="mb-2 text-lg font-semibold text-zinc-300">Content Format</h3> <div class="flex flex-col gap-4"><div class="flex flex-row gap-4"><!></div> <div class="flex flex-row gap-4"><!></div> <div class="justify-apart flex flex-row gap-4"><!></div> <div class="justify-apart flex flex-row gap-4"><!></div></div></div> <div class="mt-6"><button type="button" class="flex w-full items-center justify-between rounded-md bg-zinc-800 p-3 text-left text-sm font-medium text-zinc-300 hover:bg-zinc-700"><span>Template Debug Panel</span> <svg fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button> <!></div></form>');function Dc(t,a){ut(a,!1);const[r,s]=xr(),c=()=>tr(le,"$formData",r),f=()=>tr(Ht,"$designStore",r);let y=Ke(a,"data",8);const m=[{name:"Classic",image:"/assets/webp/classic.webp",template:"classic"},{name:"Modern",image:"/assets/webp/modern.webp",template:"modern"},{name:"Minimalist",image:"/assets/webp/minimalist.webp",template:"minimalist"},{name:"Executive",image:"/assets/webp/accent.webp",template:"executive"}],v=[{name:"Classic Blue",theme:"classic",color:"#2563eb"},{name:"Modern Green",theme:"modern",color:"#10b981"},{name:"Elegant Purple",theme:"elegant",color:"#7c3aed"},{name:"Professional Navy",theme:"professional",color:"#1e40af"},{name:"Executive Black",theme:"executive",color:"#1f2937"},{name:"Creative Pink",theme:"creative",color:"#ec4899"}],U=["Times New Roman","Arial","Roboto","Georgia"],i=["10px","11px","12px","14px"],K=["1","1.2","1.5","2"],I=["Small","Medium","Large"],z=["Left","Center","Right"],E=["Letter (8.5 x 11 in)","A4 (210 x 297 mm)","Legal (8.5 x 14 in)"],G=["•","◦","▸","▹","✓","✦","✧","✪"];let V=ot("Classic"),ae=ot("classic"),S=ot(!1);const B=xa(y(),{validators:wa($o),onUpdated:({form:j})=>{j.valid?(ma(j.data),ur.success("Design updated")):ur.error("Please fix the errors in the form.")}}),{form:le,enhance:ce}=B;qe(()=>(c(),f(),Pr),()=>{if(c()){console.log("Form data changed, updating design store:",c()),ma(c());const L=new CustomEvent("design-data-changed",{detail:{data:c()},bubbles:!0});typeof window<"u"&&window.dispatchEvent(L)}const j=f();if(j&&Object.keys(j).length>0){let L=!1;for(const x in j)if(j[x]&&c()[x]!==j[x]){L=!0;break}if(L){console.log("Design store changed, updating form:",j),le.update(N=>({...N,...j}));const x=new CustomEvent("design-data-changed",{detail:{data:j},bubbles:!0});typeof window<"u"&&window.dispatchEvent(x)}}if(f().layout){const L=m.find(x=>x.template===f().layout);L&&_e(V,L.name)}if(f().primaryColor){const L=Object.entries(Pr).find(([x,N])=>N.primaryColor===f().primaryColor);L&&_e(ae,L[0])}}),Tt(),xt();var pe=wd(),be=n(pe),Ae=l(n(be),2);Se(Ae,5,()=>m,ze,(j,L)=>{var x=Xl();x.__click=()=>{_e(V,e(L).name);const w=is[e(L).template];w&&le.update(_=>{const h={..._,layout:e(L).template,headerStyle:w.headerStyle,sectionStyle:w.sectionStyle,font:w.font,fontSize:w.fontSize,lineHeight:w.lineHeight};console.log("Layout template selected:",e(L).template),console.log("Updated design data:",h),Ht.set(h);const A=new CustomEvent("design-data-changed",{detail:{data:h},bubbles:!0});return typeof window<"u"&&window.dispatchEvent(A),h})};var N=n(x),C=n(N,!0);o(N);var R=l(N,2),P=l(R,2);{var F=w=>{var _=Kl();d(w,_)};O(P,w=>{e(V)===e(L).name&&w(F)})}o(x),b(()=>{je(x,"aria-pressed",e(V)===e(L).name),je(x,"aria-label",`Select ${e(L).name} layout`),D(C,e(L).name),je(R,"src",e(L).image),je(R,"alt",e(L).name),vt(R,1,`w-full rounded-xl border-4 object-cover ${e(V)===e(L).name?"border-blue-500 ring-2 ring-blue-500":"border-zinc-600 hover:border-blue-500"}`)}),d(j,x)}),o(Ae),o(be);var Y=l(be,2),Q=l(n(Y),2);Se(Q,5,()=>v,ze,(j,L)=>{var x=Jl();x.__click=()=>{_e(ae,e(L).theme);const P=Pr[e(L).theme];P&&le.update(F=>{const w={...F,primaryColor:P.primaryColor,accentColor:P.accentColor,textColor:P.textColor,backgroundColor:P.backgroundColor};console.log("Color theme selected:",e(L).theme),console.log("Updated design data:",w),Ht.set(w);const _=new CustomEvent("design-data-changed",{detail:{data:w},bubbles:!0});return typeof window<"u"&&window.dispatchEvent(_),w})};var N=n(x),C=l(N,2),R=n(C,!0);o(C),o(x),b(()=>{he(N,`
              background-color: ${e(L).color??""};
              border-color: ${e(ae)===e(L).theme?"white":"transparent"};
            `),D(R,e(L).name)}),d(j,x)}),o(Q),o(Y);var fe=l(Y,2);Qr(fe,{class:"bg-zinc-700"});var xe=l(fe,4),ke=n(xe);Lt(ke,{class:"flex flex-row items-center gap-2",get form(){return B},name:"alignment",children:(j,L)=>{var x=td(),N=ge(x);Nt(N,{children:P=>{var F=ed(),w=ge(F);Rt(w,{class:"mb-0 w-1/2 text-sm font-bold text-zinc-400 opacity-100",children:(A,g)=>{Te();var p=Le("Header Alignment");d(A,p)},$$slots:{default:!0}});var _=l(w,2),h=n(_);b(()=>{c(),Ue(()=>{})}),Se(h,5,()=>z,ze,(A,g)=>{var p=Ql(),$={},H=n(p,!0);o(p),b(()=>{$!==($=e(g))&&(p.value=(p.__value=e(g))??""),D(H,e(g))}),d(A,p)}),o(h),o(_),Pt(h,()=>c().alignment,A=>wt(le,Ve(c).alignment=A,Ve(c))),d(P,F)},$$slots:{default:!0}});var C=l(N,2);jt(C,{}),d(j,x)},$$slots:{default:!0}});var ve=l(ke,2);Lt(ve,{class:"flex flex-row items-center gap-2",get form(){return B},name:"margin",children:(j,L)=>{var x=od(),N=ge(x);Nt(N,{children:P=>{var F=ad(),w=ge(F);Rt(w,{class:"mb-0 w-1/2 text-sm font-bold text-zinc-400",children:(A,g)=>{Te();var p=Le("Margin Size");d(A,p)},$$slots:{default:!0}});var _=l(w,2),h=n(_);b(()=>{c(),Ue(()=>{})}),Se(h,5,()=>I,ze,(A,g)=>{var p=rd(),$={},H=n(p,!0);o(p),b(()=>{$!==($=e(g))&&(p.value=(p.__value=e(g))??""),D(H,e(g))}),d(A,p)}),o(h),o(_),Pt(h,()=>c().margin,A=>wt(le,Ve(c).margin=A,Ve(c))),d(P,F)},$$slots:{default:!0}});var C=l(N,2);jt(C,{}),d(j,x)},$$slots:{default:!0}});var ee=l(ve,2);Lt(ee,{class:"flex flex-row items-center gap-2",get form(){return B},name:"pageSize",children:(j,L)=>{var x=sd(),N=ge(x);Nt(N,{children:P=>{var F=id(),w=ge(F);Rt(w,{class:"mb-0 w-1/2 text-sm font-bold text-zinc-400",children:(A,g)=>{Te();var p=Le("Page Size");d(A,p)},$$slots:{default:!0}});var _=l(w,2),h=n(_);b(()=>{c(),Ue(()=>{})}),Se(h,5,()=>E,ze,(A,g)=>{var p=nd(),$={},H=n(p,!0);o(p),b(()=>{$!==($=e(g))&&(p.value=(p.__value=e(g))??""),D(H,e(g))}),d(A,p)}),o(h),o(_),Pt(h,()=>c().pageSize,A=>wt(le,Ve(c).pageSize=A,Ve(c))),d(P,F)},$$slots:{default:!0}});var C=l(N,2);jt(C,{}),d(j,x)},$$slots:{default:!0}}),o(xe);var X=l(xe,2);Qr(X,{class:"bg-zinc-700"});var ie=l(X,2),we=l(n(ie),2),Ie=n(we),$e=n(Ie);Lt($e,{get form(){return B},name:"font",children:(j,L)=>{var x=cd(),N=ge(x);Nt(N,{children:P=>{var F=dd(),w=ge(F);Rt(w,{class:"text-sm font-bold text-zinc-400",children:(h,A)=>{Te();var g=Le("Font Family");d(h,g)},$$slots:{default:!0}});var _=l(w,2);b(()=>{c(),Ue(()=>{})}),Se(_,5,()=>U,ze,(h,A)=>{var g=ld(),p={},$=n(g,!0);o(g),b(()=>{p!==(p=e(A))&&(g.value=(g.__value=e(A))??""),D($,e(A))}),d(h,g)}),o(_),Pt(_,()=>c().font,h=>wt(le,Ve(c).font=h,Ve(c))),d(P,F)},$$slots:{default:!0}});var C=l(N,2);jt(C,{}),d(j,x)},$$slots:{default:!0}}),o(Ie);var ue=l(Ie,2),W=n(ue);Lt(W,{get form(){return B},name:"fontSize",children:(j,L)=>{var x=fd(),N=ge(x);Nt(N,{children:P=>{var F=ud(),w=ge(F);Rt(w,{class:"text-sm font-bold text-zinc-400",children:(h,A)=>{Te();var g=Le("Font Size");d(h,g)},$$slots:{default:!0}});var _=l(w,2);b(()=>{c(),Ue(()=>{})}),Se(_,5,()=>i,ze,(h,A)=>{var g=vd(),p={},$=n(g,!0);o(g),b(()=>{p!==(p=e(A))&&(g.value=(g.__value=e(A))??""),D($,e(A))}),d(h,g)}),o(_),Pt(_,()=>c().fontSize,h=>wt(le,Ve(c).fontSize=h,Ve(c))),d(P,F)},$$slots:{default:!0}});var C=l(N,2);jt(C,{}),d(j,x)},$$slots:{default:!0}}),o(ue);var me=l(ue,2),ye=n(me);Lt(ye,{get form(){return B},name:"lineHeight",children:(j,L)=>{var x=gd(),N=ge(x);Nt(N,{children:P=>{var F=pd(),w=ge(F);Rt(w,{class:"text-sm font-bold text-zinc-400",children:(h,A)=>{Te();var g=Le("Line Height");d(h,g)},$$slots:{default:!0}});var _=l(w,2);b(()=>{c(),Ue(()=>{})}),Se(_,5,()=>K,ze,(h,A)=>{var g=md(),p={},$=n(g,!0);o(g),b(()=>{p!==(p=e(A))&&(g.value=(g.__value=e(A))??""),D($,e(A))}),d(h,g)}),o(_),Pt(_,()=>c().lineHeight,h=>wt(le,Ve(c).lineHeight=h,Ve(c))),d(P,F)},$$slots:{default:!0}});var C=l(N,2);jt(C,{}),d(j,x)},$$slots:{default:!0}}),o(me);var Ee=l(me,2),De=n(Ee);Lt(De,{get form(){return B},name:"bulletIcon",children:(j,L)=>{var x=bd(),N=ge(x);Nt(N,{children:P=>{var F=hd(),w=ge(F);Rt(w,{class:"text-sm font-bold text-zinc-400",children:(h,A)=>{Te();var g=Le("Bullet Icon");d(h,g)},$$slots:{default:!0}});var _=l(w,2);b(()=>{c(),Ue(()=>{})}),Se(_,5,()=>G,ze,(h,A)=>{var g=_d(),p={},$=n(g,!0);o(g),b(()=>{p!==(p=e(A))&&(g.value=(g.__value=e(A))??""),D($,e(A))}),d(h,g)}),o(_),Pt(_,()=>c().bulletIcon,h=>wt(le,Ve(c).bulletIcon=h,Ve(c))),d(P,F)},$$slots:{default:!0}});var C=l(N,2);jt(C,{}),d(j,x)},$$slots:{default:!0}}),o(Ee),o(we),o(ie);var oe=l(ie,2),k=n(oe);k.__click=[yd,S];var re=l(n(k),2);o(k);var te=l(k,2);{var de=j=>{var L=xd(),x=l(n(L),2),N=n(x),C=n(N,!0);o(N),o(x);var R=l(x,4),P=n(R),F=n(P,!0);o(P),o(R),o(L),b((w,_)=>{D(C,w),D(F,_)},[()=>JSON.stringify(c(),null,2),()=>JSON.stringify(f(),null,2)],$t),d(j,L)};O(te,j=>{e(S)&&j(de)})}o(oe),o(pe),ba(pe,j=>ce==null?void 0:ce(j)),b(()=>{je(k,"aria-expanded",e(S)),vt(re,0,`h-5 w-5 transform transition-transform duration-200 ${e(S)?"rotate-180":""}`)}),d(t,pe),ft(),s()}yr(["click"]);export{Dc as E,wc as R,xc as a,yc as b};
