import{c as p,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function M(o,r){const t=m(r,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["circle",{cx:"9",cy:"7",r:"4"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75"}]];f(o,d({name:"users"},()=>t,{get iconNode(){return a},children:(e,$)=>{var s=p(),n=i(s);l(n,r,"default",{},null),c(e,s)},$$slots:{default:!0}}))}export{M as U};
