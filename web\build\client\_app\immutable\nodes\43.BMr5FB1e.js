import{f as I,a as m,t as J}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as K,l as u,b as L,f as Q,a as W,s as a,c as r,d as g,m as h,g as e,r as t,n as X,t as Y}from"../chunks/CGmarHxI.js";import{s as k}from"../chunks/CIt1g2O9.js";import{e as Z,i as ee}from"../chunks/C3w0v0gR.js";import{c as te}from"../chunks/BvdI7LR8.js";import{i as re}from"../chunks/BIEMS98f.js";import{s as se,a as ae}from"../chunks/CmxjS0TN.js";import{B as ie}from"../chunks/B1K98fMG.js";import{g as oe}from"../chunks/BiJhC7W5.js";import{S as ne}from"../chunks/C6g8ubaU.js";import{g as ce}from"../chunks/Buv24VCh.js";import{U as de}from"../chunks/B_6ivTD3.js";import{B as le}from"../chunks/hA0h0kTo.js";import{S as fe}from"../chunks/rNI1Perp.js";import{S as pe}from"../chunks/FAbXdqfL.js";import{C as me}from"../chunks/CxmsTEaf.js";import{A as ue}from"../chunks/B-l1ubNa.js";import{B as ge}from"../chunks/1gTNXEeM.js";import{S as he}from"../chunks/BAawoUIy.js";import{U as ve}from"../chunks/BSHZ37s_.js";import{C as ye}from"../chunks/DkmCSZhC.js";var be=I('<div class="hover:bg-muted/50 flex items-center justify-between p-6 transition-colors"><div class="flex items-center gap-4"><div class="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div class="space-y-1"><h3 class="font-medium"> </h3> <p class="text-muted-foreground text-sm"> </p></div></div> <div class="flex items-center gap-2"><!> <!></div></div>'),_e=I('<!> <div class="flex h-full flex-col"><div class="border-border flex flex-col justify-between border-b p-6"><h2 class="text-lg font-semibold">General Settings</h2> <p class="text-muted-foreground">Manage your account settings and preferences.</p></div> <div class="divide-y"></div></div>',1);function Re($,j){K(j,!1);const[T,H]=se(),v=()=>ae(P,"$page",T),i=h(),n=h(),y=h(),{page:P}=ce(),b=[{title:"Profile",description:"Manage your personal information",content:"Update your name, email, profile picture and other personal details.",icon:de,href:"/dashboard/settings/profile"},{title:"Account",description:"Manage your account preferences",content:"Update your notification preferences, language settings, and accessibility options.",icon:le,href:"/dashboard/settings/account"},{title:"Security",description:"Manage your security settings",content:"Update your password, enable two-factor authentication, and manage your sessions.",icon:fe,href:"/dashboard/settings/security"},{title:"AI Coach",description:"Practice for your interviews",content:"Use AI to practice for your interviews with personalized feedback and suggestions.",icon:pe,href:"/dashboard/settings/interview-coach"},{title:"Billing",description:"Manage your subscription and payments",content:"View your current subscription, payment methods, and billing history.",icon:me,href:"/dashboard/settings/billing"},{title:"Usage",description:"Monitor your feature usage",content:"Track your feature usage and subscription limits across the platform.",icon:ue,href:"/dashboard/settings/usage"},{title:"Notifications",description:"Manage your notification preferences",content:"Control how and when you receive notifications across email, browser, and more.",icon:ge,href:"/dashboard/settings/notifications"},{title:"Referrals",description:"Share and earn rewards",content:"Invite friends to join Hirli and earn rewards for successful referrals.",icon:he,href:"/dashboard/settings/referrals"}],z={title:"Team",description:"Manage your team members",content:"Invite team members, manage permissions, and organize your team.",icon:ve,href:"/dashboard/settings/team"};u(()=>v(),()=>{g(i,v().data.user)}),u(()=>e(i),()=>{var o,s;g(n,((o=e(i))==null?void 0:o.teamId)||((s=e(i))==null?void 0:s.hasTeamFeature)||!1)}),u(()=>e(n),()=>{g(y,e(n)?[...b,z]:b)}),L(),re();var _=_e(),x=Q(_);ne(x,{title:"Account Settings | Hirli",description:"Manage your Hirli account settings, including profile information, security preferences, and notification settings.",keywords:"account settings, profile settings, security settings, notification preferences, account management"});var w=a(x,2),S=a(r(w),2);Z(S,5,()=>e(y),ee,(o,s)=>{let D=()=>e(s).title,E=()=>e(s).description,F=()=>e(s).icon,G=()=>e(s).href;var c=be(),d=r(c),l=r(d),N=r(l);te(N,F,(p,B)=>{B(p,{class:"text-primary h-5 w-5"})}),t(l);var M=a(l,2),f=r(M),O=r(f,!0);t(f);var C=a(f,2),R=r(C,!0);t(C),t(M),t(d);var U=a(d,2),A=r(U);ie(A,{variant:"outline",onclick:()=>oe(G()),children:(p,B)=>{X();var q=J("Manage");m(p,q)},$$slots:{default:!0}});var V=a(A,2);ye(V,{class:"text-muted-foreground h-4 w-4"}),t(U),t(c),Y(()=>{k(O,D()),k(R,E())}),m(o,c)}),t(S),t(w),m($,_),W(),H()}export{Re as component};
