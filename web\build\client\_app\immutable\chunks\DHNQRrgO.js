import{c as i,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(t,o){const s=m(o,["children","$$slots","$$events","$$legacy"]),e=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4"}]];f(t,d({name:"lock"},()=>s,{get iconNode(){return e},children:(a,$)=>{var r=i(),n=p(r);l(n,o,"default",{},null),c(a,r)},$$slots:{default:!0}}))}export{x as L};
