import{f as n,a as v}from"../chunks/BasJTneF.js";import{p as g,a as p,s as _,c as e,r as a}from"../chunks/CGmarHxI.js";import{R as b,a as h,E as R,b as w}from"../chunks/CEzG2ALi.js";var x=n('<div slot="content"><!></div>'),y=n('<div slot="design"><!></div>'),P=n('<div class="grid h-screen grid-cols-1 md:grid-cols-[2.5fr_3fr]"><div class=" overflow-y-auto border border-b-0 border-l-0 border-t-0 border-zinc-900 p-6 text-white"><!></div> <div class="flex items-center justify-center overflow-y-auto bg-neutral-950 p-4"><!></div></div>');function z(m,o){g(o,!0);var s=P(),t=e(s),c=e(t);b(c,{$$slots:{content:(d,u)=>{var r=x(),i=e(r);w(i,{get data(){return o.data.form.resume}}),a(r),v(d,r)},design:(d,u)=>{var r=y(),i=e(r);R(i,{get data(){return o.data.form.design}}),a(r),v(d,r)}}}),a(t);var l=_(t,2),f=e(l);h(f,{}),a(l),a(s),v(m,s),p()}export{z as component};
