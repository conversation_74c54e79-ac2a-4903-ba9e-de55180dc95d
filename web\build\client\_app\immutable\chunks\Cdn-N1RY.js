import{f as n,a as c}from"./BasJTneF.js";import{p,a as f,c as m,au as l,r as d}from"./CGmarHxI.js";import{c as v,s as h}from"./ncUU1dSD.js";import{e as u}from"./B-Xjo-Yt.js";import{b as _}from"./5V1tIHTN.js";import{p as b,r as x}from"./Btcx8l8F.js";var C=n("<div><!></div>");function w(e,r){p(r,!0);let t=b(r,"ref",15,null),o=x(r,["$$slots","$$events","$$legacy","ref","class","children"]);var a=C();u(a,s=>({"data-slot":"card-content",class:s,...o}),[()=>v("px-6",r.class)]);var i=m(a);h(i,()=>r.children??l),d(a),_(a,s=>t(s),()=>t()),c(e,a),f()}export{w as C};
