import{c as m,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function q(a,s){const e=c(s,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"}]];f(a,d({name:"message-square"},()=>e,{get iconNode(){return r},children:(t,$)=>{var o=m(),n=i(o);l(n,s,"default",{},null),p(t,o)},$$slots:{default:!0}}))}export{q as M};
