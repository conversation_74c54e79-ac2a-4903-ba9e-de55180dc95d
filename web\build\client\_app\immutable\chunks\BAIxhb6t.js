import{c as i,a as n}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as p,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(t,r){const s=p(r,["children","$$slots","$$events","$$legacy"]),e=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"m15 9-6 6"}],["path",{d:"m9 9 6 6"}]];f(t,d({name:"circle-x"},()=>s,{get iconNode(){return e},children:(a,$)=>{var o=i(),c=l(o);m(c,r,"default",{},null),n(a,o)},$$slots:{default:!0}}))}export{y as C};
