var ae=Object.defineProperty;var ee=t=>{throw TypeError(t)};var ce=(t,e,r)=>e in t?ae(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var z=(t,e,r)=>ce(t,typeof e!="symbol"?e+"":e,r),te=(t,e,r)=>e.has(t)||ee("Cannot "+r);var n=(t,e,r)=>(te(t,e,"read from private field"),r?r.call(t):e.get(t)),f=(t,e,r)=>e.has(t)?ee("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,r);var J=(t,e,r)=>(te(t,e,"access private method"),r);import{c as H,a as g,f as W}from"./BasJTneF.js";import{x as m,g as d,d as v,p as j,f as w,a as G,s as ue,e as de,c as re,au as ie,r as se}from"./CGmarHxI.js";import{i as B}from"./u21ee2wt.js";import{c as he}from"./BvdI7LR8.js";import{s as Y,p as u,r as Z}from"./Btcx8l8F.js";import{s as L,c as le}from"./ncUU1dSD.js";import{e as pe}from"./B-Xjo-Yt.js";import{u as fe,w as Q,b,m as me}from"./BfX7a-t9.js";import{s as ke}from"./BniYvUIG.js";import{C as ne}from"./DuoUhxYL.js";import{h as be,i as ve,j as ge}from"./Bd3zs5C6.js";import{d as Ce,S as xe}from"./CIOgxH3l.js";import"./CgXBgsce.js";import{i as ye}from"./BIEMS98f.js";import{H as we}from"./BjCTmJLi.js";import{u as _e}from"./CnMg5bH0.js";import{C as Re}from"./DrQfh6BY.js";import{I as qe}from"./DxW95yuQ.js";const De="data-checkbox-root";var _,R,q,D,U,P,I;class Pe{constructor(e,r=null){f(this,D);z(this,"opts");z(this,"group");f(this,_,m(()=>this.group&&this.group.opts.name.current?this.group.opts.name.current:this.opts.name.current));f(this,R,m(()=>this.group&&this.group.opts.required.current?!0:this.opts.required.current));f(this,q,m(()=>this.group&&this.group.opts.disabled.current?!0:this.opts.disabled.current));f(this,P,m(()=>({checked:this.opts.checked.current,indeterminate:this.opts.indeterminate.current})));f(this,I,m(()=>({id:this.opts.id.current,role:"checkbox",type:this.opts.type.current,disabled:this.trueDisabled,"aria-checked":ge(this.opts.checked.current,this.opts.indeterminate.current),"aria-required":ve(this.trueRequired),"data-disabled":be(this.trueDisabled),"data-state":Ne(this.opts.checked.current,this.opts.indeterminate.current),[De]:"",onclick:this.onclick,onkeydown:this.onkeydown})));this.opts=e,this.group=r,this.onkeydown=this.onkeydown.bind(this),this.onclick=this.onclick.bind(this),fe(e),Q.pre([()=>{var s;return ke((s=this.group)==null?void 0:s.opts.value.current)},()=>this.opts.value.current],([s,a])=>{!s||!a||(this.opts.checked.current=s.includes(a))}),Q.pre(()=>this.opts.checked.current,s=>{var a,k;this.group&&(s?(a=this.group)==null||a.addValue(this.opts.value.current):(k=this.group)==null||k.removeValue(this.opts.value.current))})}get trueName(){return d(n(this,_))}set trueName(e){v(n(this,_),e)}get trueRequired(){return d(n(this,R))}set trueRequired(e){v(n(this,R),e)}get trueDisabled(){return d(n(this,q))}set trueDisabled(e){v(n(this,q),e)}onkeydown(e){this.opts.disabled.current||(e.key===Ce&&e.preventDefault(),e.key===xe&&(e.preventDefault(),J(this,D,U).call(this)))}onclick(e){this.opts.disabled.current||J(this,D,U).call(this)}get snippetProps(){return d(n(this,P))}set snippetProps(e){v(n(this,P),e)}get props(){return d(n(this,I))}set props(e){v(n(this,I),e)}}_=new WeakMap,R=new WeakMap,q=new WeakMap,D=new WeakSet,U=function(){this.opts.indeterminate.current?(this.opts.indeterminate.current=!1,this.opts.checked.current=!0):this.opts.checked.current=!this.opts.checked.current},P=new WeakMap,I=new WeakMap;var N,S,A;class Ie{constructor(e){z(this,"root");f(this,N,m(()=>this.root.group?!!(this.root.opts.value.current!==void 0&&this.root.group.opts.value.current.includes(this.root.opts.value.current)):this.root.opts.checked.current));f(this,S,m(()=>!!this.root.trueName));f(this,A,m(()=>({type:"checkbox",checked:this.root.opts.checked.current===!0,disabled:this.root.trueDisabled,required:this.root.trueRequired,name:this.root.trueName,value:this.root.opts.value.current})));this.root=e}get trueChecked(){return d(n(this,N))}set trueChecked(e){v(n(this,N),e)}get shouldRender(){return d(n(this,S))}set shouldRender(e){v(n(this,S),e)}get props(){return d(n(this,A))}set props(e){v(n(this,A),e)}}N=new WeakMap,S=new WeakMap,A=new WeakMap;function Ne(t,e){return e?"indeterminate":t?"checked":"unchecked"}const Se=new ne("Checkbox.Group"),oe=new ne("Checkbox.Root");function Ae(t,e){return oe.set(new Pe(t,e))}function Ee(){return new Ie(oe.get())}function Oe(t,e){j(e,!1);const r=Ee();ye();var s=H(),a=w(s);{var k=h=>{we(h,Y(()=>r.props))};B(a,h=>{r.shouldRender&&h(k)})}g(t,s),G()}var Te=W("<button><!></button>"),ze=W("<!> <!>",1);function Be(t,e){j(e,!0);let r=u(e,"checked",15,!1),s=u(e,"ref",15,null),a=u(e,"disabled",3,!1),k=u(e,"required",3,!1),h=u(e,"name",3,void 0),l=u(e,"value",3,"on"),M=u(e,"id",19,_e),E=u(e,"indeterminate",15,!1),K=u(e,"type",3,"button"),$=Z(e,["$$slots","$$events","$$legacy","checked","ref","onCheckedChange","children","disabled","required","name","value","id","indeterminate","onIndeterminateChange","child","type"]);const c=Se.getOr(null);c&&l()&&(c.opts.value.current.includes(l())?r(!0):r(!1)),Q.pre(()=>l(),()=>{c&&l()&&(c.opts.value.current.includes(l())?r(!0):r(!1))});const p=Ae({checked:b.with(()=>r(),i=>{var o;r(i),(o=e.onCheckedChange)==null||o.call(e,i)}),disabled:b.with(()=>a()??!1),required:b.with(()=>k()),name:b.with(()=>h()),value:b.with(()=>l()),id:b.with(()=>M()),ref:b.with(()=>s(),i=>s(i)),indeterminate:b.with(()=>E(),i=>{var o;E(i),(o=e.onIndeterminateChange)==null||o.call(e,i)}),type:b.with(()=>K())},c),O=m(()=>me({...$},p.props));var T=ze(),C=w(T);{var V=i=>{var o=H(),x=w(o),y=de(()=>({props:d(O),...p.snippetProps}));L(x,()=>e.child,()=>d(y)),g(i,o)},X=i=>{var o=Te();pe(o,()=>({...d(O)}));var x=re(o);L(x,()=>e.children??ie,()=>p.snippetProps),se(o),g(i,o)};B(C,i=>{e.child?i(V):i(X,!1)})}var F=ue(C,2);Oe(F,{}),g(t,T),G()}function He(t,e){j(e,!0);let r=Z(e,["$$slots","$$events","$$legacy"]);const s=[["path",{d:"M5 12h14"}]];qe(t,Y({name:"minus"},()=>r,{get iconNode(){return s},children:(a,k)=>{var h=H(),l=w(h);L(l,()=>e.children??ie),g(a,h)},$$slots:{default:!0}})),G()}var je=W('<div data-slot="checkbox-indicator" class="text-current transition-none"><!></div>');function nt(t,e){j(e,!0);let r=u(e,"ref",15,null),s=u(e,"checked",15,!1),a=u(e,"indeterminate",15,!1),k=Z(e,["$$slots","$$events","$$legacy","ref","checked","indeterminate","class"]);var h=H(),l=w(h);const M=m(()=>le("border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs peer flex size-4 shrink-0 items-center justify-center rounded-[4px] border outline-none transition-shadow focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e.class));he(l,()=>Be,(E,K)=>{K(E,Y({"data-slot":"checkbox",get class(){return d(M)}},()=>k,{get ref(){return r()},set ref(c){r(c)},get checked(){return s()},set checked(c){s(c)},get indeterminate(){return a()},set indeterminate(c){a(c)},children:(c,p)=>{let O=()=>p==null?void 0:p().checked,T=()=>p==null?void 0:p().indeterminate;var C=je(),V=re(C);{var X=i=>{Re(i,{class:"size-3.5"})},F=(i,o)=>{{var x=y=>{He(y,{class:"size-3.5"})};B(i,y=>{T()&&y(x)},o)}};B(V,i=>{O()?i(X):i(F,!1)})}se(C),g(c,C)},$$slots:{default:!0}}))}),g(t,h),G()}export{nt as C};
