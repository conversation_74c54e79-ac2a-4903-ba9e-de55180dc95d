import{c as p,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(t,o){const a=m(o,["children","$$slots","$$events","$$legacy"]),r=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5"}],["path",{d:"M3 12A9 3 0 0 0 21 12"}]];f(t,d({name:"database"},()=>a,{get iconNode(){return r},children:(e,$)=>{var s=p(),n=l(s);c(n,o,"default",{},null),i(e,s)},$$slots:{default:!0}}))}export{x as D};
