import{f as x,a as s,t as p}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{f as h,s as l,c as e,r,n as _}from"../chunks/CGmarHxI.js";import{B as $}from"../chunks/B1K98fMG.js";import{C as A}from"../chunks/DuGukytH.js";import{C as E}from"../chunks/Cdn-N1RY.js";import{S}from"../chunks/C6g8ubaU.js";import{C as j}from"../chunks/DW7T7T22.js";var B=x(`<div class="mb-4 flex justify-center"><div class="rounded-full bg-green-100 p-3"><!></div></div> <h1 class="mb-2 text-3xl font-bold">Email Verified!</h1> <p class="text-muted-foreground mb-6 max-w-md">Your email address has been successfully verified. You can now access all features of your
          account.</p> <div class="space-y-3"><!> <!></div>`,1),V=x('<!> <div class="flex min-h-[calc(100vh-200px)] flex-col items-center justify-center p-4"><div class="w-full max-w-md"><!></div></div>',1);function K(g){var i=V(),d=h(i);S(d,{title:"Email Verified | Auto Apply",description:"Your email has been verified successfully"});var c=l(d,2),n=e(c),b=e(n);A(b,{class:"text-center",children:(w,k)=>{E(w,{class:"pt-6",children:(y,G)=>{var f=B(),t=h(f),m=e(t),C=e(m);j(C,{class:"h-10 w-10 text-green-600"}),r(m),r(t);var v=l(t,6),u=e(v);$(u,{href:"/auth/sign-in",class:"w-full",children:(a,Y)=>{_();var o=p("Sign In to Your Account");s(a,o)},$$slots:{default:!0}});var P=l(u,2);$(P,{href:"/",variant:"outline",class:"w-full",children:(a,Y)=>{_();var o=p("Go to Homepage");s(a,o)},$$slots:{default:!0}}),r(v),s(y,f)},$$slots:{default:!0}})},$$slots:{default:!0}}),r(n),r(c),s(g,i)}export{K as component};
