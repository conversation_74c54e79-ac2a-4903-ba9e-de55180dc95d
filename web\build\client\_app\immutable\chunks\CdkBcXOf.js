import{f as d,a as u}from"./BasJTneF.js";import{o as ae}from"./nZgk9enP.js";import{p as se,k as q,v as re,d as _,i as z,g as a,c as f,r as i,a as te,s as I,x as ne,t as y}from"./CGmarHxI.js";import{s as P}from"./CIt1g2O9.js";import{s as F}from"./BniYvUIG.js";import{i as g}from"./u21ee2wt.js";import{e as oe,i as le}from"./C3w0v0gR.js";import{p as E}from"./Btcx8l8F.js";import{C as ue}from"./BNEH2jqx.js";var ie=d('<p class="text-muted-foreground text-sm">Loading features...</p>'),de=d('<span class="text-muted-foreground ml-2 text-xs"> </span>'),ce=d('<span class="text-muted-foreground text-xs font-medium"> </span>'),fe=d('<span class="text-muted-foreground text-xs font-medium"> </span>'),pe=d('<span class="text-muted-foreground text-xs font-medium"> </span>'),me=d('<li class="flex items-center justify-between text-sm"><div class="flex items-center"><!> <span> </span> <!></div> <!> <!> <!></li>'),ve=d('<ul class="space-y-2"></ul>'),_e=d('<p class="text-muted-foreground text-sm">No features available</p>'),ge=d('<div class="space-y-2"><!></div>');function ke(B,j){se(j,!0);const s=E(j,"plan",3,null),G=E(j,"compact",3,!1),k=E(j,"showCount",3,!0);let l=q(re([])),S=q(!0);ae(()=>{_(S,!1),D()}),z(()=>{s()&&(console.log("[snapshot] Updating enabled features from plan:",F(s())),D())});function D(){if(!s()){console.warn("Plan is null or undefined"),_(l,[],!0);return}if(console.log("Full plan object in PlanFeaturesList:",JSON.stringify(s())),!s().features||!Array.isArray(s().features)){console.warn("Plan features are missing or not an array:",s()),s().id?(console.log("Creating default features for plan ID:",s().id),_(l,[{featureId:"dashboard",accessLevel:"included"},{featureId:"profile",accessLevel:"included"},{featureId:"resume_scanner",accessLevel:"included"},{featureId:"resume_builder",accessLevel:"included"},{featureId:"resume_ai",accessLevel:"included"},{featureId:"job_search_profiles",accessLevel:"included"},{featureId:"application_tracker",accessLevel:"included"}].map(o=>{const b=w(o.featureId);return{...o,name:b,description:"",id:o.featureId,icon:null}}),!0)):_(l,[],!0);return}console.log("[snapshot] Updating enabled features from plan object:",F(s())),console.log("Updating enabled features from plan object:",s());const e=s().features.filter(r=>r.accessLevel?r.accessLevel==="included"||r.accessLevel==="limited"||r.accessLevel==="unlimited":(console.warn("Feature missing accessLevel:",r),!1));if(console.log("[snapshot] Filtered enabled features:",F(e)),console.log("Filtered enabled features:",e),e.length===0&&s().id){console.log("No enabled features found, creating defaults for plan:",s().id),_(l,[{featureId:"dashboard",accessLevel:"included"},{featureId:"profile",accessLevel:"included"},{featureId:"resume_scanner",accessLevel:"included"},{featureId:"resume_builder",accessLevel:"included"},{featureId:"resume_ai",accessLevel:"included"},{featureId:"job_search_profiles",accessLevel:"included"},{featureId:"application_tracker",accessLevel:"included"}].map(o=>{const b=w(o.featureId);return{...o,name:b,description:"",id:o.featureId,icon:null}}),!0);return}_(l,e.map(r=>{const o=w(r.featureId);return{...r,name:o,description:"",id:r.featureId,icon:null}}),!0)}function w(e){return e?e.split("_").map(r=>r.charAt(0).toUpperCase()+r.slice(1)).join(" "):"Unknown Feature"}z(()=>{s()&&(console.log("[snapshot] Plan features:",F(s().features)),console.log("Plan features:",s().features),console.log("[snapshot] Enabled features:",F(a(l))),console.log("Enabled features:",a(l)))});const c=ne(()=>{var e;return((e=s())==null?void 0:e.limits)||{resumesPerMonth:void 0,seats:void 0,profiles:void 0}});function x(e){return e?typeof e=="string"?e:typeof e=="number"?e.toString():typeof e=="object"&&e.value!==void 0?e.value.toString():"":""}var N=ge(),H=f(N);{var K=e=>{var r=ie();u(e,r)},Q=(e,r)=>{{var o=m=>{var h=ve();oe(h,21,()=>a(l),le,(R,v)=>{var C=me(),U=f(C),J=f(U);ue(J,{class:"mr-2 h-4 w-4 text-green-500"});var M=I(J,2),T=f(M,!0);i(M);var W=I(M,2);{var X=t=>{var n=de(),p=f(n,!0);i(n),y(()=>P(p,a(v).description)),u(t,n)};g(W,t=>{!G()&&a(v).description&&t(X)})}i(U);var O=I(U,2);{var Y=t=>{var n=ce(),p=f(n,!0);i(n),y(L=>P(p,L),[()=>x(a(c).resumesPerMonth)]),u(t,n)};g(O,t=>{k()&&a(v).id==="resume_scanner"&&a(c).resumesPerMonth&&t(Y)})}var V=I(O,2);{var Z=t=>{var n=fe(),p=f(n);i(n),y((L,A)=>P(p,`${L??""}
              ${A??""}`),[()=>x(a(c).seats),()=>x(a(c).seats)==="1"?"seat":"seats"]),u(t,n)};g(V,t=>{k()&&a(v).id==="team_collaboration"&&a(c).seats&&t(Z)})}var $=I(V,2);{var ee=t=>{var n=pe(),p=f(n);i(n),y((L,A)=>P(p,`${L??""}
              ${A??""}`),[()=>x(a(c).profiles),()=>x(a(c).profiles)==="1"?"profile":"profiles"]),u(t,n)};g($,t=>{k()&&a(v).id==="application_tracker"&&a(c).profiles&&t(ee)})}i(C),y(()=>P(T,a(v).name)),u(R,C)}),i(h),u(m,h)},b=m=>{var h=_e();u(m,h)};g(e,m=>{a(l).length>0?m(o):m(b,!1)},r)}};g(H,e=>{a(S)?e(K):e(Q,!1)})}i(N),u(B,N),te()}export{ke as P};
