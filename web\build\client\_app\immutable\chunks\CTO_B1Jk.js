import{c as l,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as d,s as c}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function I(a,t){const o=d(t,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"}],["path",{d:"M12 9v4"}],["path",{d:"M12 17h.01"}]];f(a,c({name:"triangle-alert"},()=>o,{get iconNode(){return e},children:(s,$)=>{var r=l(),n=i(r);m(n,t,"default",{},null),p(s,r)},$$slots:{default:!0}}))}export{I as T};
