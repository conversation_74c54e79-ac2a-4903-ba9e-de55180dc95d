import * as server from '../entries/pages/dashboard/settings/admin/email/_layout.server.ts.js';

export const index = 6;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/admin/email/_layout.svelte.js')).default;
export { server };
export const server_id = "src/routes/dashboard/settings/admin/email/+layout.server.ts";
export const imports = ["_app/immutable/nodes/6.B0Pov2Hz.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/BiJhC7W5.js","_app/immutable/chunks/D8pQCLOH.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/B-Xjo-Yt.js"];
export const stylesheets = [];
export const fonts = [];
