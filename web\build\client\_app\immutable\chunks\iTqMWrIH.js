import{c as n,a as d}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as l}from"./Btcx8l8F.js";import{I as h}from"./D4f2twK-.js";function x(r,t){const s=c(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M12 8V4H8"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2"}],["path",{d:"M2 14h2"}],["path",{d:"M20 14h2"}],["path",{d:"M15 13v2"}],["path",{d:"M9 13v2"}]];h(r,l({name:"bot"},()=>s,{get iconNode(){return a},children:(e,f)=>{var o=n(),p=i(o);m(p,t,"default",{},null),d(e,o)},$$slots:{default:!0}}))}export{x as B};
