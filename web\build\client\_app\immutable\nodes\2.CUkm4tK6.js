import{f as k,a as M}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as _}from"../chunks/nZgk9enP.js";import{p as I,t as j,a as A,c as e,s as r,r as t}from"../chunks/CGmarHxI.js";import{s as p}from"../chunks/CIt1g2O9.js";import{s as J}from"../chunks/BBa424ah.js";import{i as L}from"../chunks/BIEMS98f.js";import{s as $,a as q}from"../chunks/CmxjS0TN.js";import{g as z}from"../chunks/BiJhC7W5.js";import{p as B}from"../chunks/Buv24VCh.js";var C=k('<div class="flex min-h-screen flex-row items-center justify-center"><div class="flex h-full w-full items-center justify-center"><div class="bg-muted text-primary-foreground border-border relative hidden min-h-screen w-full flex-col border-r p-10 lg:flex"><div class="absolute inset-0 bg-cover" style="background-image:url(https://images.unsplash.com/photo-1590069261209-f8e9b8642343?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=crop&amp;w=1376&amp;q=80);"></div> <div class="z-index-20 relative flex items-center gap-2"><svg class="text-primary-foreground h-7 w-7" fill="none" height="32" viewBox="0 0 256  256" width="32" xmlns="http://www.w3.org/2000/svg"><rect fill="none" height="224" rx="32" stroke="currentColor" stroke-width="16" width="224" x="16" y="16"></rect><path d="M80 130 L110 160 L180 90" fill="none" stroke="currentColor" stroke-dasharray="300" stroke-dashoffset="300" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"><animate attributeName="stroke-dashoffset" begin="0.2s" dur="0.6s" fill="freeze" from="300" to="0"></animate></path></svg> <a href="/" class="font-inter text-foreground hover:text-foreground/80 text-2xl font-normal transition-colors duration-200">hirli</a></div> <div class="relative z-20 mt-auto"><blockquote class="space-y-2"><p class="text-lg"> </p> <footer class="text-sm"> </footer></blockquote></div></div></div> <div class="flex w-2/3 flex-col items-center justify-center lg:p-8"><!></div></div>');function O(h,i){I(i,!1);const[v,x]=$(),g=()=>q(B,"$page",v);_(()=>{g().data.user&&z("/dashboard")});const n=[{text:"I've submitted resumes automatically for countless hours, and I'm not stopping!",name:"Sofia Davis"},{text:"Just another batch of resumes sent while I sip my coffee.",name:"John Smith"},{text:"Automating my way through job applications, one resume at a time!",name:"Emma Brown"},{text:"Resumes flying off to potential employers faster than I can count.",name:"Michael Johnson"},{text:"I’ve sent 100s of resumes today, and it’s only 10 AM!",name:"Olivia Wilson"},{text:"You blink, I submit another resume!",name:"James Taylor"},{text:"Job applications? Done. Resumes submitted automatically with precision!",name:"Charlotte Lee"},{text:"I’m on a roll! Resumes are flowing like never before.",name:"Liam Harris"},{text:"The auto-submit system never sleeps. Resumes are being sent constantly!",name:"Amelia Clark"},{text:"Another day, another batch of resumes automatically submitted!",name:"Lucas Martinez"}],l=n[Math.floor(Math.random()*n.length)];L();var a=C(),o=e(a),m=e(o),f=r(e(m),4),u=e(f),s=e(u),b=e(s);t(s);var c=r(s,2),y=e(c,!0);t(c),t(u),t(f),t(m),t(o);var d=r(o,2),w=e(d);J(w,i,"default",{},null),t(d),t(a),j(()=>{p(b,`“${l.text??""}”`),p(y,l.name)}),M(h,a),A(),x()}export{O as component};
