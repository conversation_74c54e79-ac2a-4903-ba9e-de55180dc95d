import{c as p,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as d,s as c}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(t,a){const s=d(a,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7"}]];f(t,c({name:"save"},()=>s,{get iconNode(){return r},children:(e,$)=>{var o=p(),n=l(o);m(n,a,"default",{},null),i(e,o)},$$slots:{default:!0}}))}export{y as S};
