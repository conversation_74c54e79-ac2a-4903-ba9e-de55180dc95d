import{f as a,a as t,t as f}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as zt,l as ct,b as Bt,f as o,a as Gt,s as r,c as z,o as x,t as Nt,d as _t,m as pt,g as K,n as s,r as B}from"../chunks/CGmarHxI.js";import{s as jt}from"../chunks/CIt1g2O9.js";import{i as It}from"../chunks/u21ee2wt.js";import{a as Yt}from"../chunks/DDUgF6Ik.js";import{i as At}from"../chunks/BIEMS98f.js";import{p as Ot}from"../chunks/Btcx8l8F.js";import{s as Ut,b as j,a as X}from"../chunks/CmxjS0TN.js";import{C as gt}from"../chunks/DuGukytH.js";import{C as ht}from"../chunks/Cdn-N1RY.js";import{C as Pt}from"../chunks/BkJY4La4.js";import{C as xt}from"../chunks/GwmmX_iF.js";import{C as yt}from"../chunks/D50jIuLr.js";import{F as I,C as Y,a as A}from"../chunks/FeejBSkx.js";import{B as R}from"../chunks/B1K98fMG.js";import{I as H}from"../chunks/DMTMHyMa.js";import{T as Ht}from"../chunks/VNuMAkuB.js";import{p as Lt}from"../chunks/Buv24VCh.js";import{s as Rt}from"../chunks/B8blszX7.js";import{t as bt}from"../chunks/DjPYYl4Z.js";import{S as qt}from"../chunks/C6g8ubaU.js";import{S as Jt}from"../chunks/BoNCRmBc.js";import{F as O}from"../chunks/CXvW3J0s.js";import{F as U}from"../chunks/ByFxH6T3.js";import{G as Kt}from"../chunks/D1zde6Ej.js";import{S as Mt}from"../chunks/rNI1Perp.js";import{B as Qt}from"../chunks/hA0h0kTo.js";import{U as Vt}from"../chunks/BSHZ37s_.js";var Wt=a("<!> <!>",1),Xt=a("<!> <!>",1),Zt=a("<!> <!> <!>",1),tr=a("<!> <!>",1),rr=a("<!> <!> <!>",1),er=a("<!> <!>",1),ar=a("<!> <!> <!>",1),or=a("<!> <!> <!>",1),sr=a("<!> <!>",1),nr=a("<!> <!>",1),lr=a("<!> <!>",1),ir=a("<!> <!> <!>",1),dr=a("<!> <!>",1),$r=a("<!> <!> <!>",1),ur=a("<!> <!>",1),vr=a("<!> <!> <!>",1),mr=a("<!> <!>",1),fr=a("<!> <!> <!>",1),cr=a('<div class="grid grid-cols-1 gap-6 sm:grid-cols-2"><!> <!> <!> <!></div>'),_r=a("<!> <!>",1),pr=a("<!> Account",1),gr=a("<!> Security",1),hr=a("<!> Team",1),Pr=a("<!> Notifications",1),xr=a('<!> <div class="space-y-6"><div class="border-border flex flex-row justify-between border-b p-6"><h2 class="text-lg font-semibold">General Settings</h2> <p class="text-muted-foreground text-foreground/80">Configure general application settings and preferences.</p></div> <form method="POST" class="space-y-8"><!> <!> <div class="flex justify-end"><!></div></form> <div class="flex items-center justify-between rounded-lg border p-4"><div class="flex items-center gap-4"><div class="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="font-medium">Other Settings</h3> <p class="text-muted-foreground text-sm">Configure additional settings in other sections</p></div></div> <div class="flex gap-2"><!> <!> <!> <!></div></div></div>',1);function Wr(wt,Z){zt(Z,!1);const[M,Ct]=Ut(),tt=()=>X(Lt,"$page",M),i=()=>X(E,"$formData",M),rt=()=>X(St,"$submitting",M),q=pt(),et=pt();let Ft=Ot(Z,"data",8);const k=Rt({id:"general-settings",data:Ft().form.data,onUpdated:({form:n})=>{n.valid&&bt.success("General settings updated successfully")},onError:()=>{bt.error("Failed to update general settings")}}),{form:E,enhance:Q,submitting:St}=k;ct(()=>tt(),()=>{_t(q,tt().data.user)}),ct(()=>K(q),()=>{var n,S;_t(et,((n=K(q))==null?void 0:n.teamId)||((S=K(q))==null?void 0:S.hasTeamFeature)||!1)}),Bt(),At();var at=xr(),ot=o(at);qt(ot,{title:"General Settings - Hirli",description:"Configure general application settings including site name, description, contact information, and regional preferences.",keywords:"general settings, site configuration, application settings, regional settings, language settings",url:"https://hirli.com/dashboard/settings/general"});var st=r(ot,2),J=r(z(st),2),nt=z(J);gt(nt,{children:(n,S)=>{var c=sr(),P=o(c);xt(P,{class:"p-6",children:(G,W)=>{var y=Wt(),C=o(y);yt(C,{children:(b,F)=>{s();var _=f("Site Information");t(b,_)},$$slots:{default:!0}});var T=r(C,2);Pt(T,{children:(b,F)=>{s();var _=f("Basic information about your site.");t(b,_)},$$slots:{default:!0}}),t(G,y)},$$slots:{default:!0}});var L=r(P,2);ht(L,{class:"space-y-6 p-6 pt-0",children:(G,W)=>{var y=or(),C=o(y);I(C,{get form(){return k},name:"siteName",children:(F,_)=>{var w=Zt(),$=o(w);Y($,{children:(v,d)=>{var l=Xt(),e=o(l);O(e,{children:(m,p)=>{s();var D=f("Site Name");t(m,D)},$$slots:{default:!0}});var h=r(e,2);H(h,{type:"text",get value(){return i().siteName},set value(m){j(E,x(i).siteName=m,x(i))},$$legacy:!0}),t(v,l)},$$slots:{default:!0}});var u=r($,2);U(u,{children:(v,d)=>{s();var l=f("The name of your site");t(v,l)},$$slots:{default:!0}});var g=r(u,2);A(g,{}),t(F,w)},$$slots:{default:!0}});var T=r(C,2);I(T,{get form(){return k},name:"siteDescription",children:(F,_)=>{var w=rr(),$=o(w);Y($,{children:(v,d)=>{var l=tr(),e=o(l);O(e,{children:(m,p)=>{s();var D=f("Site Description");t(m,D)},$$slots:{default:!0}});var h=r(e,2);Ht(h,{rows:3,get value(){return i().siteDescription},set value(m){j(E,x(i).siteDescription=m,x(i))},$$legacy:!0}),t(v,l)},$$slots:{default:!0}});var u=r($,2);U(u,{children:(v,d)=>{s();var l=f("A brief description of your site");t(v,l)},$$slots:{default:!0}});var g=r(u,2);A(g,{}),t(F,w)},$$slots:{default:!0}});var b=r(T,2);I(b,{get form(){return k},name:"contactEmail",children:(F,_)=>{var w=ar(),$=o(w);Y($,{children:(v,d)=>{var l=er(),e=o(l);O(e,{children:(m,p)=>{s();var D=f("Contact Email");t(m,D)},$$slots:{default:!0}});var h=r(e,2);H(h,{type:"email",get value(){return i().contactEmail},set value(m){j(E,x(i).contactEmail=m,x(i))},$$legacy:!0}),t(v,l)},$$slots:{default:!0}});var u=r($,2);U(u,{children:(v,d)=>{s();var l=f("The primary contact email for your site");t(v,l)},$$slots:{default:!0}});var g=r(u,2);A(g,{}),t(F,w)},$$slots:{default:!0}}),t(G,y)},$$slots:{default:!0}}),t(n,c)},$$slots:{default:!0}});var lt=r(nt,2);gt(lt,{children:(n,S)=>{var c=_r(),P=o(c);xt(P,{class:"p-6",children:(G,W)=>{var y=nr(),C=o(y);yt(C,{children:(b,F)=>{s();var _=f("Regional Settings");t(b,_)},$$slots:{default:!0}});var T=r(C,2);Pt(T,{children:(b,F)=>{s();var _=f("Configure regional preferences.");t(b,_)},$$slots:{default:!0}}),t(G,y)},$$slots:{default:!0}});var L=r(P,2);ht(L,{class:"space-y-6 p-6 pt-0",children:(G,W)=>{var y=cr(),C=z(y);I(C,{get form(){return k},name:"timezone",children:(_,w)=>{var $=ir(),u=o($);Y(u,{children:(d,l)=>{var e=lr(),h=o(e);O(h,{children:(p,D)=>{s();var N=f("Timezone");t(p,N)},$$slots:{default:!0}});var m=r(h,2);H(m,{type:"text",get value(){return i().timezone},set value(p){j(E,x(i).timezone=p,x(i))},$$legacy:!0}),t(d,e)},$$slots:{default:!0}});var g=r(u,2);U(g,{children:(d,l)=>{s();var e=f("Your default timezone");t(d,e)},$$slots:{default:!0}});var v=r(g,2);A(v,{}),t(_,$)},$$slots:{default:!0}});var T=r(C,2);I(T,{get form(){return k},name:"language",children:(_,w)=>{var $=$r(),u=o($);Y(u,{children:(d,l)=>{var e=dr(),h=o(e);O(h,{children:(p,D)=>{s();var N=f("Language");t(p,N)},$$slots:{default:!0}});var m=r(h,2);H(m,{type:"text",get value(){return i().language},set value(p){j(E,x(i).language=p,x(i))},$$legacy:!0}),t(d,e)},$$slots:{default:!0}});var g=r(u,2);U(g,{children:(d,l)=>{s();var e=f("Your preferred language");t(d,e)},$$slots:{default:!0}});var v=r(g,2);A(v,{}),t(_,$)},$$slots:{default:!0}});var b=r(T,2);I(b,{get form(){return k},name:"dateFormat",children:(_,w)=>{var $=vr(),u=o($);Y(u,{children:(d,l)=>{var e=ur(),h=o(e);O(h,{children:(p,D)=>{s();var N=f("Date Format");t(p,N)},$$slots:{default:!0}});var m=r(h,2);H(m,{type:"text",get value(){return i().dateFormat},set value(p){j(E,x(i).dateFormat=p,x(i))},$$legacy:!0}),t(d,e)},$$slots:{default:!0}});var g=r(u,2);U(g,{children:(d,l)=>{s();var e=f("Your preferred date format");t(d,e)},$$slots:{default:!0}});var v=r(g,2);A(v,{}),t(_,$)},$$slots:{default:!0}});var F=r(b,2);I(F,{get form(){return k},name:"timeFormat",children:(_,w)=>{var $=fr(),u=o($);Y(u,{children:(d,l)=>{var e=mr(),h=o(e);O(h,{children:(p,D)=>{s();var N=f("Time Format");t(p,N)},$$slots:{default:!0}});var m=r(h,2);H(m,{type:"text",get value(){return i().timeFormat},set value(p){j(E,x(i).timeFormat=p,x(i))},$$legacy:!0}),t(d,e)},$$slots:{default:!0}});var g=r(u,2);U(g,{children:(d,l)=>{s();var e=f("Your preferred time format");t(d,e)},$$slots:{default:!0}});var v=r(g,2);A(v,{}),t(_,$)},$$slots:{default:!0}}),B(y),t(G,y)},$$slots:{default:!0}}),t(n,c)},$$slots:{default:!0}});var it=r(lt,2),Tt=z(it);R(Tt,{type:"submit",get disabled(){return rt()},children:(n,S)=>{s();var c=f();Nt(()=>jt(c,rt()?"Saving...":"Save Changes")),t(n,c)},$$slots:{default:!0}}),B(it),B(J),Yt(J,n=>Q==null?void 0:Q(n));var dt=r(J,2),V=z(dt),$t=z(V),Dt=z($t);Jt(Dt,{class:"text-primary h-5 w-5"}),B($t),s(2),B(V);var ut=r(V,2),vt=z(ut);R(vt,{variant:"outline",onclick:()=>window.location.href="/dashboard/settings/account",children:(n,S)=>{var c=pr(),P=o(c);Kt(P,{class:"mr-2 h-4 w-4"}),s(),t(n,c)},$$slots:{default:!0}});var mt=r(vt,2);R(mt,{variant:"outline",onclick:()=>window.location.href="/dashboard/settings/security",children:(n,S)=>{var c=gr(),P=o(c);Mt(P,{class:"mr-2 h-4 w-4"}),s(),t(n,c)},$$slots:{default:!0}});var ft=r(mt,2);{var kt=n=>{R(n,{variant:"outline",onclick:()=>window.location.href="/dashboard/settings/team",children:(S,c)=>{var P=hr(),L=o(P);Vt(L,{class:"mr-2 h-4 w-4"}),s(),t(S,P)},$$slots:{default:!0}})};It(ft,n=>{K(et)&&n(kt)})}var Et=r(ft,2);R(Et,{variant:"outline",onclick:()=>window.location.href="/dashboard/settings/notifications",children:(n,S)=>{var c=Pr(),P=o(c);Qt(P,{class:"mr-2 h-4 w-4"}),s(),t(n,c)},$$slots:{default:!0}}),B(ut),B(dt),B(st),t(wt,at),Gt(),Ct()}export{Wr as component};
