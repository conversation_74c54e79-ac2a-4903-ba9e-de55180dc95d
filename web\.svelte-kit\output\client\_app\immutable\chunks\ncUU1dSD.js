import{S as ct,T as lt,O as dt,au as ut,as as mt,P as ft,F as ht}from"./CGmarHxI.js";import{h as gt}from"./B-Xjo-Yt.js";function mn(e,t,...r){var n=e,a=ut,o;ct(()=>{a!==(a=t())&&(o&&(mt(o),o=null),o=dt(()=>a(n,...r)))},lt),ft&&(n=ht)}const De="-",bt=e=>{const t=wt(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:s=>{const i=s.split(De);return i[0]===""&&i.length!==1&&i.shift(),je(i,t)||pt(s)},getConflictingClassGroupIds:(s,i)=>{const d=r[s]||[];return i&&n[s]?[...d,...n[s]]:d}}},je=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),a=n?je(e.slice(1),n):void 0;if(a)return a;if(t.validators.length===0)return;const o=e.join(De);return(s=t.validators.find(({validator:i})=>i(o)))==null?void 0:s.classGroupId},Ge=/^\[(.+)\]$/,pt=e=>{if(Ge.test(e)){const t=Ge.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},wt=e=>{const{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(const a in r)ke(r[a],n,a,t);return n},ke=(e,t,r,n)=>{e.forEach(a=>{if(typeof a=="string"){const o=a===""?t:Ae(t,a);o.classGroupId=r;return}if(typeof a=="function"){if(yt(a)){ke(a(n),t,r,n);return}t.validators.push({validator:a,classGroupId:r});return}Object.entries(a).forEach(([o,s])=>{ke(s,Ae(t,o),r,n)})})},Ae=(e,t)=>{let r=e;return t.split(De).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},yt=e=>e.isThemeGetter,xt=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const a=(o,s)=>{r.set(o,s),t++,t>e&&(t=0,n=r,r=new Map)};return{get(o){let s=r.get(o);if(s!==void 0)return s;if((s=n.get(o))!==void 0)return a(o,s),s},set(o,s){r.has(o)?r.set(o,s):a(o,s)}}},ve="!",Me=":",kt=Me.length,vt=e=>{const{prefix:t,experimentalParseClassName:r}=e;let n=a=>{const o=[];let s=0,i=0,d=0,h;for(let b=0;b<a.length;b++){let p=a[b];if(s===0&&i===0){if(p===Me){o.push(a.slice(d,b)),d=b+kt;continue}if(p==="/"){h=b;continue}}p==="["?s++:p==="]"?s--:p==="("?i++:p===")"&&i--}const g=o.length===0?a:a.substring(d),x=Mt(g),y=x!==g,v=h&&h>d?h-d:void 0;return{modifiers:o,hasImportantModifier:y,baseClassName:x,maybePostfixModifierPosition:v}};if(t){const a=t+Me,o=n;n=s=>s.startsWith(a)?o(s.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:s,maybePostfixModifierPosition:void 0}}if(r){const a=n;n=o=>r({className:o,parseClassName:a})}return n},Mt=e=>e.endsWith(ve)?e.substring(0,e.length-1):e.startsWith(ve)?e.substring(1):e,Pt=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const a=[];let o=[];return n.forEach(s=>{s[0]==="["||t[s]?(a.push(...o.sort(),s),o=[]):o.push(s)}),a.push(...o.sort()),a}},St=e=>({cache:xt(e.cacheSize),parseClassName:vt(e),sortModifiers:Pt(e),...bt(e)}),Dt=/\s+/,Ot=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:a,sortModifiers:o}=t,s=[],i=e.trim().split(Dt);let d="";for(let h=i.length-1;h>=0;h-=1){const g=i[h],{isExternal:x,modifiers:y,hasImportantModifier:v,baseClassName:b,maybePostfixModifierPosition:p}=r(g);if(x){d=g+(d.length>0?" "+d:d);continue}let O=!!p,F=n(O?b.substring(0,p):b);if(!F){if(!O){d=g+(d.length>0?" "+d:d);continue}if(F=n(b),!F){d=g+(d.length>0?" "+d:d);continue}O=!1}const ee=o(y).join(":"),j=v?ee+ve:ee,R=j+F;if(s.includes(R))continue;s.push(R);const _=a(F,O);for(let G=0;G<_.length;++G){const B=_[G];s.push(j+B)}d=g+(d.length>0?" "+d:d)}return d};function Tt(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=Be(t))&&(n&&(n+=" "),n+=r);return n}const Be=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=Be(e[n]))&&(r&&(r+=" "),r+=t);return r};function Pe(e,...t){let r,n,a,o=s;function s(d){const h=t.reduce((g,x)=>x(g),e());return r=St(h),n=r.cache.get,a=r.cache.set,o=i,i(d)}function i(d){const h=n(d);if(h)return h;const g=Ot(d,r);return a(d,g),g}return function(){return o(Tt.apply(null,arguments))}}const k=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},Qe=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,$e=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Wt=/^\d+\/\d+$/,Ct=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Yt=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,zt=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Et=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Ft=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,q=e=>Wt.test(e),f=e=>!!e&&!Number.isNaN(Number(e)),Y=e=>!!e&&Number.isInteger(Number(e)),we=e=>e.endsWith("%")&&f(e.slice(0,-1)),C=e=>Ct.test(e),Gt=()=>!0,At=e=>Yt.test(e)&&!zt.test(e),Je=()=>!1,It=e=>Et.test(e),Nt=e=>Ft.test(e),Rt=e=>!c(e)&&!l(e),_t=e=>H(e,Ze,Je),c=e=>Qe.test(e),I=e=>H(e,et,At),ye=e=>H(e,Vt,f),Ie=e=>H(e,Ue,Je),Lt=e=>H(e,Ke,Nt),se=e=>H(e,tt,It),l=e=>$e.test(e),Q=e=>V(e,et),qt=e=>V(e,jt),Ne=e=>V(e,Ue),Xt=e=>V(e,Ze),Ht=e=>V(e,Ke),ie=e=>V(e,tt,!0),H=(e,t,r)=>{const n=Qe.exec(e);return n?n[1]?t(n[1]):r(n[2]):!1},V=(e,t,r=!1)=>{const n=$e.exec(e);return n?n[1]?t(n[1]):r:!1},Ue=e=>e==="position"||e==="percentage",Ke=e=>e==="image"||e==="url",Ze=e=>e==="length"||e==="size"||e==="bg-size",et=e=>e==="length",Vt=e=>e==="number",jt=e=>e==="family-name",tt=e=>e==="shadow",Se=()=>{const e=k("color"),t=k("font"),r=k("text"),n=k("font-weight"),a=k("tracking"),o=k("leading"),s=k("breakpoint"),i=k("container"),d=k("spacing"),h=k("radius"),g=k("shadow"),x=k("inset-shadow"),y=k("text-shadow"),v=k("drop-shadow"),b=k("blur"),p=k("perspective"),O=k("aspect"),F=k("ease"),ee=k("animate"),j=()=>["auto","avoid","all","avoid-page","page","left","right","column"],R=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],_=()=>[...R(),l,c],G=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto","contain","none"],m=()=>[l,c,d],T=()=>[q,"full","auto",...m()],Oe=()=>[Y,"none","subgrid",l,c],Te=()=>["auto",{span:["full",Y,l,c]},Y,l,c],te=()=>[Y,"auto",l,c],We=()=>["auto","min","max","fr",l,c],ge=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],L=()=>["start","end","center","stretch","center-safe","end-safe"],W=()=>["auto",...m()],A=()=>[q,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...m()],u=()=>[e,l,c],Ce=()=>[...R(),Ne,Ie,{position:[l,c]}],Ye=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ze=()=>["auto","cover","contain",Xt,_t,{size:[l,c]}],be=()=>[we,Q,I],S=()=>["","none","full",h,l,c],D=()=>["",f,Q,I],re=()=>["solid","dashed","dotted","double"],Ee=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],M=()=>[f,we,Ne,Ie],Fe=()=>["","none",b,l,c],ne=()=>["none",f,l,c],ae=()=>["none",f,l,c],pe=()=>[f,l,c],oe=()=>[q,"full",...m()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[C],breakpoint:[C],color:[Gt],container:[C],"drop-shadow":[C],ease:["in","out","in-out"],font:[Rt],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[C],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[C],shadow:[C],spacing:["px",f],text:[C],"text-shadow":[C],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",q,c,l,O]}],container:["container"],columns:[{columns:[f,c,l,i]}],"break-after":[{"break-after":j()}],"break-before":[{"break-before":j()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:_()}],overflow:[{overflow:G()}],"overflow-x":[{"overflow-x":G()}],"overflow-y":[{"overflow-y":G()}],overscroll:[{overscroll:B()}],"overscroll-x":[{"overscroll-x":B()}],"overscroll-y":[{"overscroll-y":B()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:T()}],"inset-x":[{"inset-x":T()}],"inset-y":[{"inset-y":T()}],start:[{start:T()}],end:[{end:T()}],top:[{top:T()}],right:[{right:T()}],bottom:[{bottom:T()}],left:[{left:T()}],visibility:["visible","invisible","collapse"],z:[{z:[Y,"auto",l,c]}],basis:[{basis:[q,"full","auto",i,...m()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[f,q,"auto","initial","none",c]}],grow:[{grow:["",f,l,c]}],shrink:[{shrink:["",f,l,c]}],order:[{order:[Y,"first","last","none",l,c]}],"grid-cols":[{"grid-cols":Oe()}],"col-start-end":[{col:Te()}],"col-start":[{"col-start":te()}],"col-end":[{"col-end":te()}],"grid-rows":[{"grid-rows":Oe()}],"row-start-end":[{row:Te()}],"row-start":[{"row-start":te()}],"row-end":[{"row-end":te()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":We()}],"auto-rows":[{"auto-rows":We()}],gap:[{gap:m()}],"gap-x":[{"gap-x":m()}],"gap-y":[{"gap-y":m()}],"justify-content":[{justify:[...ge(),"normal"]}],"justify-items":[{"justify-items":[...L(),"normal"]}],"justify-self":[{"justify-self":["auto",...L()]}],"align-content":[{content:["normal",...ge()]}],"align-items":[{items:[...L(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...L(),{baseline:["","last"]}]}],"place-content":[{"place-content":ge()}],"place-items":[{"place-items":[...L(),"baseline"]}],"place-self":[{"place-self":["auto",...L()]}],p:[{p:m()}],px:[{px:m()}],py:[{py:m()}],ps:[{ps:m()}],pe:[{pe:m()}],pt:[{pt:m()}],pr:[{pr:m()}],pb:[{pb:m()}],pl:[{pl:m()}],m:[{m:W()}],mx:[{mx:W()}],my:[{my:W()}],ms:[{ms:W()}],me:[{me:W()}],mt:[{mt:W()}],mr:[{mr:W()}],mb:[{mb:W()}],ml:[{ml:W()}],"space-x":[{"space-x":m()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":m()}],"space-y-reverse":["space-y-reverse"],size:[{size:A()}],w:[{w:[i,"screen",...A()]}],"min-w":[{"min-w":[i,"screen","none",...A()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[s]},...A()]}],h:[{h:["screen","lh",...A()]}],"min-h":[{"min-h":["screen","lh","none",...A()]}],"max-h":[{"max-h":["screen","lh",...A()]}],"font-size":[{text:["base",r,Q,I]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,l,ye]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",we,c]}],"font-family":[{font:[qt,c,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,l,c]}],"line-clamp":[{"line-clamp":[f,"none",l,ye]}],leading:[{leading:[o,...m()]}],"list-image":[{"list-image":["none",l,c]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",l,c]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:u()}],"text-color":[{text:u()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...re(),"wavy"]}],"text-decoration-thickness":[{decoration:[f,"from-font","auto",l,I]}],"text-decoration-color":[{decoration:u()}],"underline-offset":[{"underline-offset":[f,"auto",l,c]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:m()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",l,c]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",l,c]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Ce()}],"bg-repeat":[{bg:Ye()}],"bg-size":[{bg:ze()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Y,l,c],radial:["",l,c],conic:[Y,l,c]},Ht,Lt]}],"bg-color":[{bg:u()}],"gradient-from-pos":[{from:be()}],"gradient-via-pos":[{via:be()}],"gradient-to-pos":[{to:be()}],"gradient-from":[{from:u()}],"gradient-via":[{via:u()}],"gradient-to":[{to:u()}],rounded:[{rounded:S()}],"rounded-s":[{"rounded-s":S()}],"rounded-e":[{"rounded-e":S()}],"rounded-t":[{"rounded-t":S()}],"rounded-r":[{"rounded-r":S()}],"rounded-b":[{"rounded-b":S()}],"rounded-l":[{"rounded-l":S()}],"rounded-ss":[{"rounded-ss":S()}],"rounded-se":[{"rounded-se":S()}],"rounded-ee":[{"rounded-ee":S()}],"rounded-es":[{"rounded-es":S()}],"rounded-tl":[{"rounded-tl":S()}],"rounded-tr":[{"rounded-tr":S()}],"rounded-br":[{"rounded-br":S()}],"rounded-bl":[{"rounded-bl":S()}],"border-w":[{border:D()}],"border-w-x":[{"border-x":D()}],"border-w-y":[{"border-y":D()}],"border-w-s":[{"border-s":D()}],"border-w-e":[{"border-e":D()}],"border-w-t":[{"border-t":D()}],"border-w-r":[{"border-r":D()}],"border-w-b":[{"border-b":D()}],"border-w-l":[{"border-l":D()}],"divide-x":[{"divide-x":D()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":D()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...re(),"hidden","none"]}],"divide-style":[{divide:[...re(),"hidden","none"]}],"border-color":[{border:u()}],"border-color-x":[{"border-x":u()}],"border-color-y":[{"border-y":u()}],"border-color-s":[{"border-s":u()}],"border-color-e":[{"border-e":u()}],"border-color-t":[{"border-t":u()}],"border-color-r":[{"border-r":u()}],"border-color-b":[{"border-b":u()}],"border-color-l":[{"border-l":u()}],"divide-color":[{divide:u()}],"outline-style":[{outline:[...re(),"none","hidden"]}],"outline-offset":[{"outline-offset":[f,l,c]}],"outline-w":[{outline:["",f,Q,I]}],"outline-color":[{outline:u()}],shadow:[{shadow:["","none",g,ie,se]}],"shadow-color":[{shadow:u()}],"inset-shadow":[{"inset-shadow":["none",x,ie,se]}],"inset-shadow-color":[{"inset-shadow":u()}],"ring-w":[{ring:D()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:u()}],"ring-offset-w":[{"ring-offset":[f,I]}],"ring-offset-color":[{"ring-offset":u()}],"inset-ring-w":[{"inset-ring":D()}],"inset-ring-color":[{"inset-ring":u()}],"text-shadow":[{"text-shadow":["none",y,ie,se]}],"text-shadow-color":[{"text-shadow":u()}],opacity:[{opacity:[f,l,c]}],"mix-blend":[{"mix-blend":[...Ee(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Ee()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[f]}],"mask-image-linear-from-pos":[{"mask-linear-from":M()}],"mask-image-linear-to-pos":[{"mask-linear-to":M()}],"mask-image-linear-from-color":[{"mask-linear-from":u()}],"mask-image-linear-to-color":[{"mask-linear-to":u()}],"mask-image-t-from-pos":[{"mask-t-from":M()}],"mask-image-t-to-pos":[{"mask-t-to":M()}],"mask-image-t-from-color":[{"mask-t-from":u()}],"mask-image-t-to-color":[{"mask-t-to":u()}],"mask-image-r-from-pos":[{"mask-r-from":M()}],"mask-image-r-to-pos":[{"mask-r-to":M()}],"mask-image-r-from-color":[{"mask-r-from":u()}],"mask-image-r-to-color":[{"mask-r-to":u()}],"mask-image-b-from-pos":[{"mask-b-from":M()}],"mask-image-b-to-pos":[{"mask-b-to":M()}],"mask-image-b-from-color":[{"mask-b-from":u()}],"mask-image-b-to-color":[{"mask-b-to":u()}],"mask-image-l-from-pos":[{"mask-l-from":M()}],"mask-image-l-to-pos":[{"mask-l-to":M()}],"mask-image-l-from-color":[{"mask-l-from":u()}],"mask-image-l-to-color":[{"mask-l-to":u()}],"mask-image-x-from-pos":[{"mask-x-from":M()}],"mask-image-x-to-pos":[{"mask-x-to":M()}],"mask-image-x-from-color":[{"mask-x-from":u()}],"mask-image-x-to-color":[{"mask-x-to":u()}],"mask-image-y-from-pos":[{"mask-y-from":M()}],"mask-image-y-to-pos":[{"mask-y-to":M()}],"mask-image-y-from-color":[{"mask-y-from":u()}],"mask-image-y-to-color":[{"mask-y-to":u()}],"mask-image-radial":[{"mask-radial":[l,c]}],"mask-image-radial-from-pos":[{"mask-radial-from":M()}],"mask-image-radial-to-pos":[{"mask-radial-to":M()}],"mask-image-radial-from-color":[{"mask-radial-from":u()}],"mask-image-radial-to-color":[{"mask-radial-to":u()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":R()}],"mask-image-conic-pos":[{"mask-conic":[f]}],"mask-image-conic-from-pos":[{"mask-conic-from":M()}],"mask-image-conic-to-pos":[{"mask-conic-to":M()}],"mask-image-conic-from-color":[{"mask-conic-from":u()}],"mask-image-conic-to-color":[{"mask-conic-to":u()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Ce()}],"mask-repeat":[{mask:Ye()}],"mask-size":[{mask:ze()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",l,c]}],filter:[{filter:["","none",l,c]}],blur:[{blur:Fe()}],brightness:[{brightness:[f,l,c]}],contrast:[{contrast:[f,l,c]}],"drop-shadow":[{"drop-shadow":["","none",v,ie,se]}],"drop-shadow-color":[{"drop-shadow":u()}],grayscale:[{grayscale:["",f,l,c]}],"hue-rotate":[{"hue-rotate":[f,l,c]}],invert:[{invert:["",f,l,c]}],saturate:[{saturate:[f,l,c]}],sepia:[{sepia:["",f,l,c]}],"backdrop-filter":[{"backdrop-filter":["","none",l,c]}],"backdrop-blur":[{"backdrop-blur":Fe()}],"backdrop-brightness":[{"backdrop-brightness":[f,l,c]}],"backdrop-contrast":[{"backdrop-contrast":[f,l,c]}],"backdrop-grayscale":[{"backdrop-grayscale":["",f,l,c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f,l,c]}],"backdrop-invert":[{"backdrop-invert":["",f,l,c]}],"backdrop-opacity":[{"backdrop-opacity":[f,l,c]}],"backdrop-saturate":[{"backdrop-saturate":[f,l,c]}],"backdrop-sepia":[{"backdrop-sepia":["",f,l,c]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":m()}],"border-spacing-x":[{"border-spacing-x":m()}],"border-spacing-y":[{"border-spacing-y":m()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",l,c]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[f,"initial",l,c]}],ease:[{ease:["linear","initial",F,l,c]}],delay:[{delay:[f,l,c]}],animate:[{animate:["none",ee,l,c]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,l,c]}],"perspective-origin":[{"perspective-origin":_()}],rotate:[{rotate:ne()}],"rotate-x":[{"rotate-x":ne()}],"rotate-y":[{"rotate-y":ne()}],"rotate-z":[{"rotate-z":ne()}],scale:[{scale:ae()}],"scale-x":[{"scale-x":ae()}],"scale-y":[{"scale-y":ae()}],"scale-z":[{"scale-z":ae()}],"scale-3d":["scale-3d"],skew:[{skew:pe()}],"skew-x":[{"skew-x":pe()}],"skew-y":[{"skew-y":pe()}],transform:[{transform:[l,c,"","none","gpu","cpu"]}],"transform-origin":[{origin:_()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:oe()}],"translate-x":[{"translate-x":oe()}],"translate-y":[{"translate-y":oe()}],"translate-z":[{"translate-z":oe()}],"translate-none":["translate-none"],accent:[{accent:u()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:u()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",l,c]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":m()}],"scroll-mx":[{"scroll-mx":m()}],"scroll-my":[{"scroll-my":m()}],"scroll-ms":[{"scroll-ms":m()}],"scroll-me":[{"scroll-me":m()}],"scroll-mt":[{"scroll-mt":m()}],"scroll-mr":[{"scroll-mr":m()}],"scroll-mb":[{"scroll-mb":m()}],"scroll-ml":[{"scroll-ml":m()}],"scroll-p":[{"scroll-p":m()}],"scroll-px":[{"scroll-px":m()}],"scroll-py":[{"scroll-py":m()}],"scroll-ps":[{"scroll-ps":m()}],"scroll-pe":[{"scroll-pe":m()}],"scroll-pt":[{"scroll-pt":m()}],"scroll-pr":[{"scroll-pr":m()}],"scroll-pb":[{"scroll-pb":m()}],"scroll-pl":[{"scroll-pl":m()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",l,c]}],fill:[{fill:["none",...u()]}],"stroke-w":[{stroke:[f,Q,I,ye]}],stroke:[{stroke:["none",...u()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Bt=(e,{cacheSize:t,prefix:r,experimentalParseClassName:n,extend:a={},override:o={}})=>(U(e,"cacheSize",t),U(e,"prefix",r),U(e,"experimentalParseClassName",n),ce(e.theme,o.theme),ce(e.classGroups,o.classGroups),ce(e.conflictingClassGroups,o.conflictingClassGroups),ce(e.conflictingClassGroupModifiers,o.conflictingClassGroupModifiers),U(e,"orderSensitiveModifiers",o.orderSensitiveModifiers),le(e.theme,a.theme),le(e.classGroups,a.classGroups),le(e.conflictingClassGroups,a.conflictingClassGroups),le(e.conflictingClassGroupModifiers,a.conflictingClassGroupModifiers),rt(e,a,"orderSensitiveModifiers"),e),U=(e,t,r)=>{r!==void 0&&(e[t]=r)},ce=(e,t)=>{if(t)for(const r in t)U(e,r,t[r])},le=(e,t)=>{if(t)for(const r in t)rt(e,t,r)},rt=(e,t,r)=>{const n=t[r];n!==void 0&&(e[r]=e[r]?e[r].concat(n):n)},fn=(e,...t)=>typeof e=="function"?Pe(Se,e,...t):Pe(()=>Bt(Se(),e),...t),Qt=Pe(Se),nt=6048e5,$t=864e5,hn=6e4,gn=36e5,de=43200,Re=1440,_e=Symbol.for("constructDateFrom");function E(e,t){return typeof e=="function"?e(t):e&&typeof e=="object"&&_e in e?e[_e](t):e instanceof Date?new e.constructor(t):new Date(t)}function P(e,t){return E(t||e,e)}let Jt={};function Z(){return Jt}function K(e,t){var i,d,h,g;const r=Z(),n=(t==null?void 0:t.weekStartsOn)??((d=(i=t==null?void 0:t.locale)==null?void 0:i.options)==null?void 0:d.weekStartsOn)??r.weekStartsOn??((g=(h=r.locale)==null?void 0:h.options)==null?void 0:g.weekStartsOn)??0,a=P(e,t==null?void 0:t.in),o=a.getDay(),s=(o<n?7:0)+o-n;return a.setDate(a.getDate()-s),a.setHours(0,0,0,0),a}function me(e,t){return K(e,{...t,weekStartsOn:1})}function at(e,t){const r=P(e,t==null?void 0:t.in),n=r.getFullYear(),a=E(r,0);a.setFullYear(n+1,0,4),a.setHours(0,0,0,0);const o=me(a),s=E(r,0);s.setFullYear(n,0,4),s.setHours(0,0,0,0);const i=me(s);return r.getTime()>=o.getTime()?n+1:r.getTime()>=i.getTime()?n:n-1}function fe(e){const t=P(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),+e-+r}function he(e,...t){const r=E.bind(null,e||t.find(n=>typeof n=="object"));return t.map(r)}function Le(e,t){const r=P(e,t==null?void 0:t.in);return r.setHours(0,0,0,0),r}function Ut(e,t,r){const[n,a]=he(r==null?void 0:r.in,e,t),o=Le(n),s=Le(a),i=+o-fe(o),d=+s-fe(s);return Math.round((i-d)/$t)}function Kt(e,t){const r=at(e,t),n=E(e,0);return n.setFullYear(r,0,4),n.setHours(0,0,0,0),me(n)}function ue(e,t){const r=+P(e)-+P(t);return r<0?-1:r>0?1:r}function Zt(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function er(e){return!(!Zt(e)&&typeof e!="number"||isNaN(+P(e)))}function tr(e,t,r){const[n,a]=he(r==null?void 0:r.in,e,t),o=n.getFullYear()-a.getFullYear(),s=n.getMonth()-a.getMonth();return o*12+s}function rr(e){return t=>{const n=(e?Math[e]:Math.trunc)(t);return n===0?0:n}}function nr(e,t){return+P(e)-+P(t)}function ar(e,t){const r=P(e,t==null?void 0:t.in);return r.setHours(23,59,59,999),r}function or(e,t){const r=P(e,t==null?void 0:t.in),n=r.getMonth();return r.setFullYear(r.getFullYear(),n+1,0),r.setHours(23,59,59,999),r}function sr(e,t){const r=P(e,t==null?void 0:t.in);return+ar(r,t)==+or(r,t)}function ir(e,t,r){const[n,a,o]=he(r==null?void 0:r.in,e,e,t),s=ue(a,o),i=Math.abs(tr(a,o));if(i<1)return 0;a.getMonth()===1&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-s*i);let d=ue(a,o)===-s;sr(n)&&i===1&&ue(n,o)===1&&(d=!1);const h=s*(i-+d);return h===0?0:h}function cr(e,t,r){const n=nr(e,t)/1e3;return rr(r==null?void 0:r.roundingMethod)(n)}function lr(e,t){const r=P(e,t==null?void 0:t.in);return r.setFullYear(r.getFullYear(),0,1),r.setHours(0,0,0,0),r}const dr={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},ur=(e,t,r)=>{let n;const a=dr[e];return typeof a=="string"?n=a:t===1?n=a.one:n=a.other.replace("{{count}}",t.toString()),r!=null&&r.addSuffix?r.comparison&&r.comparison>0?"in "+n:n+" ago":n};function xe(e){return(t={})=>{const r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}const mr={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},fr={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},hr={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},gr={date:xe({formats:mr,defaultWidth:"full"}),time:xe({formats:fr,defaultWidth:"full"}),dateTime:xe({formats:hr,defaultWidth:"full"})},br={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},pr=(e,t,r,n)=>br[e];function $(e){return(t,r)=>{const n=r!=null&&r.context?String(r.context):"standalone";let a;if(n==="formatting"&&e.formattingValues){const s=e.defaultFormattingWidth||e.defaultWidth,i=r!=null&&r.width?String(r.width):s;a=e.formattingValues[i]||e.formattingValues[s]}else{const s=e.defaultWidth,i=r!=null&&r.width?String(r.width):e.defaultWidth;a=e.values[i]||e.values[s]}const o=e.argumentCallback?e.argumentCallback(t):t;return a[o]}}const wr={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},yr={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},xr={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},kr={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},vr={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Mr={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Pr=(e,t)=>{const r=Number(e),n=r%100;if(n>20||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},Sr={ordinalNumber:Pr,era:$({values:wr,defaultWidth:"wide"}),quarter:$({values:yr,defaultWidth:"wide",argumentCallback:e=>e-1}),month:$({values:xr,defaultWidth:"wide"}),day:$({values:kr,defaultWidth:"wide"}),dayPeriod:$({values:vr,defaultWidth:"wide",formattingValues:Mr,defaultFormattingWidth:"wide"})};function J(e){return(t,r={})=>{const n=r.width,a=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],o=t.match(a);if(!o)return null;const s=o[0],i=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(i)?Or(i,x=>x.test(s)):Dr(i,x=>x.test(s));let h;h=e.valueCallback?e.valueCallback(d):d,h=r.valueCallback?r.valueCallback(h):h;const g=t.slice(s.length);return{value:h,rest:g}}}function Dr(e,t){for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}function Or(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}function Tr(e){return(t,r={})=>{const n=t.match(e.matchPattern);if(!n)return null;const a=n[0],o=t.match(e.parsePattern);if(!o)return null;let s=e.valueCallback?e.valueCallback(o[0]):o[0];s=r.valueCallback?r.valueCallback(s):s;const i=t.slice(a.length);return{value:s,rest:i}}}const Wr=/^(\d+)(th|st|nd|rd)?/i,Cr=/\d+/i,Yr={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},zr={any:[/^b/i,/^(a|c)/i]},Er={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Fr={any:[/1/i,/2/i,/3/i,/4/i]},Gr={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Ar={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Ir={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Nr={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Rr={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},_r={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Lr={ordinalNumber:Tr({matchPattern:Wr,parsePattern:Cr,valueCallback:e=>parseInt(e,10)}),era:J({matchPatterns:Yr,defaultMatchWidth:"wide",parsePatterns:zr,defaultParseWidth:"any"}),quarter:J({matchPatterns:Er,defaultMatchWidth:"wide",parsePatterns:Fr,defaultParseWidth:"any",valueCallback:e=>e+1}),month:J({matchPatterns:Gr,defaultMatchWidth:"wide",parsePatterns:Ar,defaultParseWidth:"any"}),day:J({matchPatterns:Ir,defaultMatchWidth:"wide",parsePatterns:Nr,defaultParseWidth:"any"}),dayPeriod:J({matchPatterns:Rr,defaultMatchWidth:"any",parsePatterns:_r,defaultParseWidth:"any"})},ot={code:"en-US",formatDistance:ur,formatLong:gr,formatRelative:pr,localize:Sr,match:Lr,options:{weekStartsOn:0,firstWeekContainsDate:1}};function qr(e,t){const r=P(e,t==null?void 0:t.in);return Ut(r,lr(r))+1}function Xr(e,t){const r=P(e,t==null?void 0:t.in),n=+me(r)-+Kt(r);return Math.round(n/nt)+1}function st(e,t){var g,x,y,v;const r=P(e,t==null?void 0:t.in),n=r.getFullYear(),a=Z(),o=(t==null?void 0:t.firstWeekContainsDate)??((x=(g=t==null?void 0:t.locale)==null?void 0:g.options)==null?void 0:x.firstWeekContainsDate)??a.firstWeekContainsDate??((v=(y=a.locale)==null?void 0:y.options)==null?void 0:v.firstWeekContainsDate)??1,s=E((t==null?void 0:t.in)||e,0);s.setFullYear(n+1,0,o),s.setHours(0,0,0,0);const i=K(s,t),d=E((t==null?void 0:t.in)||e,0);d.setFullYear(n,0,o),d.setHours(0,0,0,0);const h=K(d,t);return+r>=+i?n+1:+r>=+h?n:n-1}function Hr(e,t){var i,d,h,g;const r=Z(),n=(t==null?void 0:t.firstWeekContainsDate)??((d=(i=t==null?void 0:t.locale)==null?void 0:i.options)==null?void 0:d.firstWeekContainsDate)??r.firstWeekContainsDate??((g=(h=r.locale)==null?void 0:h.options)==null?void 0:g.firstWeekContainsDate)??1,a=st(e,t),o=E((t==null?void 0:t.in)||e,0);return o.setFullYear(a,0,n),o.setHours(0,0,0,0),K(o,t)}function Vr(e,t){const r=P(e,t==null?void 0:t.in),n=+K(r,t)-+Hr(r,t);return Math.round(n/nt)+1}function w(e,t){const r=e<0?"-":"",n=Math.abs(e).toString().padStart(t,"0");return r+n}const z={y(e,t){const r=e.getFullYear(),n=r>0?r:1-r;return w(t==="yy"?n%100:n,t.length)},M(e,t){const r=e.getMonth();return t==="M"?String(r+1):w(r+1,2)},d(e,t){return w(e.getDate(),t.length)},a(e,t){const r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h(e,t){return w(e.getHours()%12||12,t.length)},H(e,t){return w(e.getHours(),t.length)},m(e,t){return w(e.getMinutes(),t.length)},s(e,t){return w(e.getSeconds(),t.length)},S(e,t){const r=t.length,n=e.getMilliseconds(),a=Math.trunc(n*Math.pow(10,r-3));return w(a,t.length)}},X={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},qe={G:function(e,t,r){const n=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return r.era(n,{width:"abbreviated"});case"GGGGG":return r.era(n,{width:"narrow"});case"GGGG":default:return r.era(n,{width:"wide"})}},y:function(e,t,r){if(t==="yo"){const n=e.getFullYear(),a=n>0?n:1-n;return r.ordinalNumber(a,{unit:"year"})}return z.y(e,t)},Y:function(e,t,r,n){const a=st(e,n),o=a>0?a:1-a;if(t==="YY"){const s=o%100;return w(s,2)}return t==="Yo"?r.ordinalNumber(o,{unit:"year"}):w(o,t.length)},R:function(e,t){const r=at(e);return w(r,t.length)},u:function(e,t){const r=e.getFullYear();return w(r,t.length)},Q:function(e,t,r){const n=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return w(n,2);case"Qo":return r.ordinalNumber(n,{unit:"quarter"});case"QQQ":return r.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,t,r){const n=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return w(n,2);case"qo":return r.ordinalNumber(n,{unit:"quarter"});case"qqq":return r.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,t,r){const n=e.getMonth();switch(t){case"M":case"MM":return z.M(e,t);case"Mo":return r.ordinalNumber(n+1,{unit:"month"});case"MMM":return r.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(n,{width:"wide",context:"formatting"})}},L:function(e,t,r){const n=e.getMonth();switch(t){case"L":return String(n+1);case"LL":return w(n+1,2);case"Lo":return r.ordinalNumber(n+1,{unit:"month"});case"LLL":return r.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(n,{width:"wide",context:"standalone"})}},w:function(e,t,r,n){const a=Vr(e,n);return t==="wo"?r.ordinalNumber(a,{unit:"week"}):w(a,t.length)},I:function(e,t,r){const n=Xr(e);return t==="Io"?r.ordinalNumber(n,{unit:"week"}):w(n,t.length)},d:function(e,t,r){return t==="do"?r.ordinalNumber(e.getDate(),{unit:"date"}):z.d(e,t)},D:function(e,t,r){const n=qr(e);return t==="Do"?r.ordinalNumber(n,{unit:"dayOfYear"}):w(n,t.length)},E:function(e,t,r){const n=e.getDay();switch(t){case"E":case"EE":case"EEE":return r.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(n,{width:"short",context:"formatting"});case"EEEE":default:return r.day(n,{width:"wide",context:"formatting"})}},e:function(e,t,r,n){const a=e.getDay(),o=(a-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return w(o,2);case"eo":return r.ordinalNumber(o,{unit:"day"});case"eee":return r.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(a,{width:"short",context:"formatting"});case"eeee":default:return r.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,r,n){const a=e.getDay(),o=(a-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return w(o,t.length);case"co":return r.ordinalNumber(o,{unit:"day"});case"ccc":return r.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(a,{width:"narrow",context:"standalone"});case"cccccc":return r.day(a,{width:"short",context:"standalone"});case"cccc":default:return r.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,r){const n=e.getDay(),a=n===0?7:n;switch(t){case"i":return String(a);case"ii":return w(a,t.length);case"io":return r.ordinalNumber(a,{unit:"day"});case"iii":return r.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(n,{width:"short",context:"formatting"});case"iiii":default:return r.day(n,{width:"wide",context:"formatting"})}},a:function(e,t,r){const a=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,r){const n=e.getHours();let a;switch(n===12?a=X.noon:n===0?a=X.midnight:a=n/12>=1?"pm":"am",t){case"b":case"bb":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,r){const n=e.getHours();let a;switch(n>=17?a=X.evening:n>=12?a=X.afternoon:n>=4?a=X.morning:a=X.night,t){case"B":case"BB":case"BBB":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,r){if(t==="ho"){let n=e.getHours()%12;return n===0&&(n=12),r.ordinalNumber(n,{unit:"hour"})}return z.h(e,t)},H:function(e,t,r){return t==="Ho"?r.ordinalNumber(e.getHours(),{unit:"hour"}):z.H(e,t)},K:function(e,t,r){const n=e.getHours()%12;return t==="Ko"?r.ordinalNumber(n,{unit:"hour"}):w(n,t.length)},k:function(e,t,r){let n=e.getHours();return n===0&&(n=24),t==="ko"?r.ordinalNumber(n,{unit:"hour"}):w(n,t.length)},m:function(e,t,r){return t==="mo"?r.ordinalNumber(e.getMinutes(),{unit:"minute"}):z.m(e,t)},s:function(e,t,r){return t==="so"?r.ordinalNumber(e.getSeconds(),{unit:"second"}):z.s(e,t)},S:function(e,t){return z.S(e,t)},X:function(e,t,r){const n=e.getTimezoneOffset();if(n===0)return"Z";switch(t){case"X":return He(n);case"XXXX":case"XX":return N(n);case"XXXXX":case"XXX":default:return N(n,":")}},x:function(e,t,r){const n=e.getTimezoneOffset();switch(t){case"x":return He(n);case"xxxx":case"xx":return N(n);case"xxxxx":case"xxx":default:return N(n,":")}},O:function(e,t,r){const n=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Xe(n,":");case"OOOO":default:return"GMT"+N(n,":")}},z:function(e,t,r){const n=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Xe(n,":");case"zzzz":default:return"GMT"+N(n,":")}},t:function(e,t,r){const n=Math.trunc(+e/1e3);return w(n,t.length)},T:function(e,t,r){return w(+e,t.length)}};function Xe(e,t=""){const r=e>0?"-":"+",n=Math.abs(e),a=Math.trunc(n/60),o=n%60;return o===0?r+String(a):r+String(a)+t+w(o,2)}function He(e,t){return e%60===0?(e>0?"-":"+")+w(Math.abs(e)/60,2):N(e,t)}function N(e,t=""){const r=e>0?"-":"+",n=Math.abs(e),a=w(Math.trunc(n/60),2),o=w(n%60,2);return r+a+t+o}const Ve=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},it=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},jr=(e,t)=>{const r=e.match(/(P+)(p+)?/)||[],n=r[1],a=r[2];if(!a)return Ve(e,t);let o;switch(n){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;case"PPPP":default:o=t.dateTime({width:"full"});break}return o.replace("{{date}}",Ve(n,t)).replace("{{time}}",it(a,t))},Br={p:it,P:jr},Qr=/^D+$/,$r=/^Y+$/,Jr=["D","DD","YY","YYYY"];function Ur(e){return Qr.test(e)}function Kr(e){return $r.test(e)}function Zr(e,t,r){const n=en(e,t,r);if(console.warn(n),Jr.includes(e))throw new RangeError(n)}function en(e,t,r){const n=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${n} to the input \`${r}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const tn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,rn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,nn=/^'([^]*?)'?$/,an=/''/g,on=/[a-zA-Z]/;function sn(e,t,r){var g,x,y,v;const n=Z(),a=n.locale??ot,o=n.firstWeekContainsDate??((x=(g=n.locale)==null?void 0:g.options)==null?void 0:x.firstWeekContainsDate)??1,s=n.weekStartsOn??((v=(y=n.locale)==null?void 0:y.options)==null?void 0:v.weekStartsOn)??0,i=P(e,r==null?void 0:r.in);if(!er(i))throw new RangeError("Invalid time value");let d=t.match(rn).map(b=>{const p=b[0];if(p==="p"||p==="P"){const O=Br[p];return O(b,a.formatLong)}return b}).join("").match(tn).map(b=>{if(b==="''")return{isToken:!1,value:"'"};const p=b[0];if(p==="'")return{isToken:!1,value:cn(b)};if(qe[p])return{isToken:!0,value:b};if(p.match(on))throw new RangeError("Format string contains an unescaped latin alphabet character `"+p+"`");return{isToken:!1,value:b}});a.localize.preprocessor&&(d=a.localize.preprocessor(i,d));const h={firstWeekContainsDate:o,weekStartsOn:s,locale:a};return d.map(b=>{if(!b.isToken)return b.value;const p=b.value;(Kr(p)||Ur(p))&&Zr(p,t,String(e));const O=qe[p[0]];return O(i,p,a.localize,h)}).join("")}function cn(e){const t=e.match(nn);return t?t[1].replace(an,"'"):e}function ln(e,t,r){const n=Z(),a=(r==null?void 0:r.locale)??n.locale??ot,o=2520,s=ue(e,t);if(isNaN(s))throw new RangeError("Invalid time value");const i=Object.assign({},r,{addSuffix:r==null?void 0:r.addSuffix,comparison:s}),[d,h]=he(r==null?void 0:r.in,...s>0?[t,e]:[e,t]),g=cr(h,d),x=(fe(h)-fe(d))/1e3,y=Math.round((g-x)/60);let v;if(y<2)return r!=null&&r.includeSeconds?g<5?a.formatDistance("lessThanXSeconds",5,i):g<10?a.formatDistance("lessThanXSeconds",10,i):g<20?a.formatDistance("lessThanXSeconds",20,i):g<40?a.formatDistance("halfAMinute",0,i):g<60?a.formatDistance("lessThanXMinutes",1,i):a.formatDistance("xMinutes",1,i):y===0?a.formatDistance("lessThanXMinutes",1,i):a.formatDistance("xMinutes",y,i);if(y<45)return a.formatDistance("xMinutes",y,i);if(y<90)return a.formatDistance("aboutXHours",1,i);if(y<Re){const b=Math.round(y/60);return a.formatDistance("aboutXHours",b,i)}else{if(y<o)return a.formatDistance("xDays",1,i);if(y<de){const b=Math.round(y/Re);return a.formatDistance("xDays",b,i)}else if(y<de*2)return v=Math.round(y/de),a.formatDistance("aboutXMonths",v,i)}if(v=ir(h,d),v<12){const b=Math.round(y/de);return a.formatDistance("xMonths",b,i)}else{const b=v%12,p=Math.trunc(v/12);return b<3?a.formatDistance("aboutXYears",p,i):b<9?a.formatDistance("overXYears",p,i):a.formatDistance("almostXYears",p+1,i)}}function bn(...e){return Qt(gt(e))}function pn(e){return e?ln(new Date(e),new Date,{addSuffix:!1}):""}function wn(e,t){let r=null;return function(...n){const a=()=>{r=null,e(...n)};r!==null&&clearTimeout(r),r=setTimeout(a,t)}}function yn(e){return e?sn(new Date(e),"MMM d, yyyy"):""}export{pn as a,E as b,bn as c,wn as d,ln as e,yn as f,Ut as g,Z as h,w as i,hn as j,K as k,fn as l,gn as m,he as n,Le as o,ar as p,mn as s,P as t};
