import{c as d,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as i}from"./BBa424ah.js";import{l as c,s as m}from"./Btcx8l8F.js";import{I as $}from"./D4f2twK-.js";function L(o,a){const e=c(a,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z"}],["path",{d:"M22 10v6"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5"}]];$(o,m({name:"graduation-cap"},()=>e,{get iconNode(){return s},children:(n,h)=>{var t=d(),r=l(t);i(r,a,"default",{},null),p(n,t)},$$slots:{default:!0}}))}function N(o,a){const e=c(a,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"m5 8 6 6"}],["path",{d:"m4 14 6-6 2-3"}],["path",{d:"M2 5h12"}],["path",{d:"M7 2h1"}],["path",{d:"m22 22-5-10-5 10"}],["path",{d:"M14 18h6"}]];$(o,m({name:"languages"},()=>e,{get iconNode(){return s},children:(n,h)=>{var t=d(),r=l(t);i(r,a,"default",{},null),p(n,t)},$$slots:{default:!0}}))}export{L as G,N as L};
