var _r=Object.defineProperty;var lr=o=>{throw TypeError(o)};var Tr=(o,t,r)=>t in o?_r(o,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[t]=r;var b=(o,t,r)=>Tr(o,typeof t!="symbol"?t+"":t,r),ar=(o,t,r)=>t.has(o)||lr("Cannot "+r);var s=(o,t,r)=>(ar(o,t,"read from private field"),r?r.call(o):t.get(o)),c=(o,t,r)=>t.has(o)?lr("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(o):t.set(o,r),cr=(o,t,r,e)=>(ar(o,t,"write to private field"),e?e.call(o,r):t.set(o,r),r);import{c as x,a as P,f as I}from"./BasJTneF.js";import{k as w,i as L,o as Ft,d as h,g as i,x as m,u as er,p as R,f as N,a as A,c as j,au as k,r as G,s as Bt}from"./CGmarHxI.js";import{s as H,c as br}from"./ncUU1dSD.js";import{i as E}from"./u21ee2wt.js";import{c as Z}from"./BvdI7LR8.js";import{p as v,r as C,s as y}from"./Btcx8l8F.js";import{o as hr}from"./CmxjS0TN.js";import{u as U,e as yr,m as O,b as T}from"./BfX7a-t9.js";import{u as Jt,a as J}from"./hQ6uUXJy.js";import{C as Mt}from"./DuoUhxYL.js";import{a as or}from"./Cb-3cdbh.js";import{u as Nr,P as Kt}from"./XESq6qWN.js";import{u as Q}from"./CnMg5bH0.js";import{e as K}from"./B-Xjo-Yt.js";var B;class sr{constructor(){c(this,B,w(!1));L(()=>(Ft(()=>h(s(this,B),!0)),()=>{h(s(this,B),!1)}))}get current(){return i(s(this,B))}}B=new WeakMap;function Rr(o,t,r){return Math.min(r,Math.max(t,o))}const Ar="data-scroll-area-root",Cr="data-scroll-area-viewport",zr="data-scroll-area-corner",xr="data-scroll-area-thumb",Vr="data-scroll-area-scrollbar";var $,tt,rt,et,ot,st,it,nt,lt,at;class Er{constructor(t){b(this,"opts");c(this,$,w(null));c(this,tt,w(null));c(this,rt,w(null));c(this,et,w(null));c(this,ot,w(null));c(this,st,w(0));c(this,it,w(0));c(this,nt,w(!1));c(this,lt,w(!1));c(this,at,m(()=>({id:this.opts.id.current,dir:this.opts.dir.current,style:{position:"relative","--bits-scroll-area-corner-height":`${this.cornerHeight}px`,"--bits-scroll-area-corner-width":`${this.cornerWidth}px`},[Ar]:""})));this.opts=t,U({...t,onRefChange:r=>{this.scrollAreaNode=r}})}get scrollAreaNode(){return i(s(this,$))}set scrollAreaNode(t){h(s(this,$),t,!0)}get viewportNode(){return i(s(this,tt))}set viewportNode(t){h(s(this,tt),t,!0)}get contentNode(){return i(s(this,rt))}set contentNode(t){h(s(this,rt),t,!0)}get scrollbarXNode(){return i(s(this,et))}set scrollbarXNode(t){h(s(this,et),t,!0)}get scrollbarYNode(){return i(s(this,ot))}set scrollbarYNode(t){h(s(this,ot),t,!0)}get cornerWidth(){return i(s(this,st))}set cornerWidth(t){h(s(this,st),t,!0)}get cornerHeight(){return i(s(this,it))}set cornerHeight(t){h(s(this,it),t,!0)}get scrollbarXEnabled(){return i(s(this,nt))}set scrollbarXEnabled(t){h(s(this,nt),t,!0)}get scrollbarYEnabled(){return i(s(this,lt))}set scrollbarYEnabled(t){h(s(this,lt),t,!0)}get props(){return i(s(this,at))}set props(t){h(s(this,at),t)}}$=new WeakMap,tt=new WeakMap,rt=new WeakMap,et=new WeakMap,ot=new WeakMap,st=new WeakMap,it=new WeakMap,nt=new WeakMap,lt=new WeakMap,at=new WeakMap;var ct,jt,ht,dt;class Lr{constructor(t,r){b(this,"opts");b(this,"root");c(this,ct,T(Q()));c(this,jt,T(null));c(this,ht,m(()=>({id:this.opts.id.current,style:{overflowX:this.root.scrollbarXEnabled?"scroll":"hidden",overflowY:this.root.scrollbarYEnabled?"scroll":"hidden"},[Cr]:""})));c(this,dt,m(()=>({id:s(this,ct).current,"data-scroll-area-content":"",style:{minWidth:this.root.scrollbarXEnabled?"fit-content":void 0}})));this.opts=t,this.root=r,U({...t,onRefChange:e=>{this.root.viewportNode=e}}),U({id:s(this,ct),ref:s(this,jt),onRefChange:e=>{this.root.contentNode=e}})}get props(){return i(s(this,ht))}set props(t){h(s(this,ht),t)}get contentProps(){return i(s(this,dt))}set contentProps(t){h(s(this,dt),t)}}ct=new WeakMap,jt=new WeakMap,ht=new WeakMap,dt=new WeakMap;var ut,bt;class Or{constructor(t,r){b(this,"opts");b(this,"root");c(this,ut,m(()=>this.opts.orientation.current==="horizontal"));c(this,bt,w(!1));this.opts=t,this.root=r,L(()=>(this.isHorizontal?this.root.scrollbarXEnabled=!0:this.root.scrollbarYEnabled=!0,()=>{this.isHorizontal?this.root.scrollbarXEnabled=!1:this.root.scrollbarYEnabled=!1}))}get isHorizontal(){return i(s(this,ut))}set isHorizontal(t){h(s(this,ut),t)}get hasThumb(){return i(s(this,bt))}set hasThumb(t){h(s(this,bt),t,!0)}}ut=new WeakMap,bt=new WeakMap;var pt,ft;class Dr{constructor(t){b(this,"scrollbar");b(this,"root");c(this,pt,w(!1));c(this,ft,m(()=>({"data-state":this.isVisible?"visible":"hidden"})));this.scrollbar=t,this.root=t.root,L(()=>{const r=this.root.scrollAreaNode,e=this.root.opts.scrollHideDelay.current;let n=0;if(!r)return;const l=()=>{window.clearTimeout(n),Ft(()=>this.isVisible=!0)},a=()=>{n&&window.clearTimeout(n),n=window.setTimeout(()=>{Ft(()=>{this.scrollbar.hasThumb=!1,this.isVisible=!1})},e)},f=yr(hr(r,"pointerenter",l),hr(r,"pointerleave",a));return()=>{window.clearTimeout(n),f()}})}get isVisible(){return i(s(this,pt))}set isVisible(t){h(s(this,pt),t,!0)}get props(){return i(s(this,ft))}set props(t){h(s(this,ft),t)}}pt=new WeakMap,ft=new WeakMap;var mt,St;class Hr{constructor(t){b(this,"scrollbar");b(this,"root");b(this,"machine",Nr("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}}));c(this,mt,m(()=>this.machine.state.current==="hidden"));c(this,St,m(()=>({"data-state":this.machine.state.current==="hidden"?"hidden":"visible",onpointerenter:this.onpointerenter,onpointerleave:this.onpointerleave})));this.scrollbar=t,this.root=t.root;const r=Jt(()=>this.machine.dispatch("SCROLL_END"),100);L(()=>{const e=this.machine.state.current,n=this.root.opts.scrollHideDelay.current;if(e==="idle"){const l=window.setTimeout(()=>this.machine.dispatch("HIDE"),n);return()=>window.clearTimeout(l)}}),L(()=>{const e=this.root.viewportNode;if(!e)return;const n=this.scrollbar.isHorizontal?"scrollLeft":"scrollTop";let l=e[n];return or(e,"scroll",()=>{const u=e[n];l!==u&&(this.machine.dispatch("SCROLL"),r()),l=u})}),this.onpointerenter=this.onpointerenter.bind(this),this.onpointerleave=this.onpointerleave.bind(this)}get isHidden(){return i(s(this,mt))}set isHidden(t){h(s(this,mt),t)}onpointerenter(t){this.machine.dispatch("POINTER_ENTER")}onpointerleave(t){this.machine.dispatch("POINTER_LEAVE")}get props(){return i(s(this,St))}set props(t){h(s(this,St),t)}}mt=new WeakMap,St=new WeakMap;var gt,vt;class Wr{constructor(t){b(this,"scrollbar");b(this,"root");c(this,gt,w(!1));c(this,vt,m(()=>({"data-state":this.isVisible?"visible":"hidden"})));this.scrollbar=t,this.root=t.root;const r=Jt(()=>{const e=this.root.viewportNode;if(!e)return;const n=e.offsetWidth<e.scrollWidth,l=e.offsetHeight<e.scrollHeight;this.isVisible=this.scrollbar.isHorizontal?n:l},10);J(()=>this.root.viewportNode,r),J(()=>this.root.contentNode,r)}get isVisible(){return i(s(this,gt))}set isVisible(t){h(s(this,gt),t,!0)}get props(){return i(s(this,vt))}set props(t){h(s(this,vt),t)}}gt=new WeakMap,vt=new WeakMap;var wt,Pt,_t,Tt,yt,Nt;class Xr{constructor(t){b(this,"scrollbar");b(this,"root");c(this,wt,w(null));c(this,Pt,w(0));c(this,_t,w({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}));c(this,Tt,m(()=>mr(this.sizes.viewport,this.sizes.content)));c(this,yt,m(()=>this.thumbRatio>0&&this.thumbRatio<1));c(this,Nt,w(""));this.scrollbar=t,this.root=t.root,L(()=>{this.scrollbar.hasThumb=this.hasThumb}),er(()=>{!this.scrollbar.hasThumb&&this.thumbNode&&(this.prevTransformStyle=this.thumbNode.style.transform)})}get thumbNode(){return i(s(this,wt))}set thumbNode(t){h(s(this,wt),t,!0)}get pointerOffset(){return i(s(this,Pt))}set pointerOffset(t){h(s(this,Pt),t,!0)}get sizes(){return i(s(this,_t))}set sizes(t){h(s(this,_t),t)}get thumbRatio(){return i(s(this,Tt))}set thumbRatio(t){h(s(this,Tt),t)}get hasThumb(){return i(s(this,yt))}set hasThumb(t){h(s(this,yt),t)}get prevTransformStyle(){return i(s(this,Nt))}set prevTransformStyle(t){h(s(this,Nt),t,!0)}setSizes(t){this.sizes=t}getScrollPosition(t,r){return re({pointerPos:t,pointerOffset:this.pointerOffset,sizes:this.sizes,dir:r})}onThumbPointerUp(){this.pointerOffset=0}onThumbPointerDown(t){this.pointerOffset=t}xOnThumbPositionChange(){if(!(this.root.viewportNode&&this.thumbNode))return;const t=this.root.viewportNode.scrollLeft,e=`translate3d(${dr({scrollPos:t,sizes:this.sizes,dir:this.root.opts.dir.current})}px, 0, 0)`;this.thumbNode.style.transform=e,this.prevTransformStyle=e}xOnWheelScroll(t){this.root.viewportNode&&(this.root.viewportNode.scrollLeft=t)}xOnDragScroll(t){this.root.viewportNode&&(this.root.viewportNode.scrollLeft=this.getScrollPosition(t,this.root.opts.dir.current))}yOnThumbPositionChange(){if(!(this.root.viewportNode&&this.thumbNode))return;const t=this.root.viewportNode.scrollTop,e=`translate3d(0, ${dr({scrollPos:t,sizes:this.sizes})}px, 0)`;this.thumbNode.style.transform=e,this.prevTransformStyle=e}yOnWheelScroll(t){this.root.viewportNode&&(this.root.viewportNode.scrollTop=t)}yOnDragScroll(t){this.root.viewportNode&&(this.root.viewportNode.scrollTop=this.getScrollPosition(t,this.root.opts.dir.current))}}wt=new WeakMap,Pt=new WeakMap,_t=new WeakMap,Tt=new WeakMap,yt=new WeakMap,Nt=new WeakMap;var Rt,At,Ct;class Yr{constructor(t,r){b(this,"opts");b(this,"scrollbarVis");b(this,"root");c(this,Rt,w());b(this,"scrollbar");b(this,"onThumbPointerDown",t=>{this.scrollbarVis.onThumbPointerDown(t.x)});b(this,"onDragScroll",t=>{this.scrollbarVis.xOnDragScroll(t.x)});b(this,"onThumbPointerUp",()=>{this.scrollbarVis.onThumbPointerUp()});b(this,"onThumbPositionChange",()=>{this.scrollbarVis.xOnThumbPositionChange()});b(this,"onWheelScroll",(t,r)=>{if(!this.root.viewportNode)return;const e=this.root.viewportNode.scrollLeft+t.deltaX;this.scrollbarVis.xOnWheelScroll(e),gr(e,r)&&t.preventDefault()});b(this,"onResize",()=>{this.scrollbar.opts.ref.current&&this.root.viewportNode&&this.computedStyle&&this.scrollbarVis.setSizes({content:this.root.viewportNode.scrollWidth,viewport:this.root.viewportNode.offsetWidth,scrollbar:{size:this.scrollbar.opts.ref.current.clientWidth,paddingStart:qt(this.computedStyle.paddingLeft),paddingEnd:qt(this.computedStyle.paddingRight)}})});c(this,At,m(()=>Zt(this.scrollbarVis.sizes)));c(this,Ct,m(()=>({id:this.scrollbar.opts.id.current,"data-orientation":"horizontal",style:{bottom:0,left:this.root.opts.dir.current==="rtl"?"var(--bits-scroll-area-corner-width)":0,right:this.root.opts.dir.current==="ltr"?"var(--bits-scroll-area-corner-width)":0,"--bits-scroll-area-thumb-width":`${this.thumbSize}px`}})));this.opts=t,this.scrollbarVis=r,this.root=r.root,this.scrollbar=r.scrollbar,U({...this.scrollbar.opts,onRefChange:e=>{this.root.scrollbarXNode=e},deps:()=>this.opts.mounted.current}),L(()=>{this.scrollbar.opts.ref.current&&this.opts.mounted.current&&(this.computedStyle=getComputedStyle(this.scrollbar.opts.ref.current))}),L(()=>{this.onResize()})}get computedStyle(){return i(s(this,Rt))}set computedStyle(t){h(s(this,Rt),t,!0)}get thumbSize(){return i(s(this,At))}set thumbSize(t){h(s(this,At),t)}get props(){return i(s(this,Ct))}set props(t){h(s(this,Ct),t)}}Rt=new WeakMap,At=new WeakMap,Ct=new WeakMap;var zt,xt,Vt;class Mr{constructor(t,r){b(this,"opts");b(this,"scrollbarVis");b(this,"root");b(this,"scrollbar");c(this,zt,w());c(this,xt,m(()=>Zt(this.scrollbarVis.sizes)));c(this,Vt,m(()=>({id:this.scrollbar.opts.id.current,"data-orientation":"vertical",style:{top:0,right:this.root.opts.dir.current==="ltr"?0:void 0,left:this.root.opts.dir.current==="rtl"?0:void 0,bottom:"var(--bits-scroll-area-corner-height)","--bits-scroll-area-thumb-height":`${this.thumbSize}px`}})));this.opts=t,this.scrollbarVis=r,this.root=r.root,this.scrollbar=r.scrollbar,U({...this.scrollbar.opts,onRefChange:e=>{this.root.scrollbarYNode=e},deps:()=>this.opts.mounted.current}),L(()=>{this.scrollbar.opts.ref.current&&this.opts.mounted.current&&(this.computedStyle=getComputedStyle(this.scrollbar.opts.ref.current))}),L(()=>{this.onResize()}),this.onThumbPointerDown=this.onThumbPointerDown.bind(this),this.onDragScroll=this.onDragScroll.bind(this),this.onThumbPointerUp=this.onThumbPointerUp.bind(this),this.onThumbPositionChange=this.onThumbPositionChange.bind(this),this.onWheelScroll=this.onWheelScroll.bind(this),this.onResize=this.onResize.bind(this)}get computedStyle(){return i(s(this,zt))}set computedStyle(t){h(s(this,zt),t,!0)}onThumbPointerDown(t){this.scrollbarVis.onThumbPointerDown(t.y)}onDragScroll(t){this.scrollbarVis.yOnDragScroll(t.y)}onThumbPointerUp(){this.scrollbarVis.onThumbPointerUp()}onThumbPositionChange(){this.scrollbarVis.yOnThumbPositionChange()}onWheelScroll(t,r){if(!this.root.viewportNode)return;const e=this.root.viewportNode.scrollTop+t.deltaY;this.scrollbarVis.yOnWheelScroll(e),gr(e,r)&&t.preventDefault()}onResize(){this.scrollbar.opts.ref.current&&this.root.viewportNode&&this.computedStyle&&this.scrollbarVis.setSizes({content:this.root.viewportNode.scrollHeight,viewport:this.root.viewportNode.offsetHeight,scrollbar:{size:this.scrollbar.opts.ref.current.clientHeight,paddingStart:qt(this.computedStyle.paddingTop),paddingEnd:qt(this.computedStyle.paddingBottom)}})}get thumbSize(){return i(s(this,xt))}set thumbSize(t){h(s(this,xt),t)}get props(){return i(s(this,Vt))}set props(t){h(s(this,Vt),t)}}zt=new WeakMap,xt=new WeakMap,Vt=new WeakMap;var Et,Lt,Ot,Dt;class Ur{constructor(t){b(this,"scrollbarState");b(this,"root");b(this,"scrollbarVis");b(this,"scrollbar");c(this,Et,w(null));c(this,Lt,w(""));b(this,"handleResize");b(this,"handleThumbPositionChange");b(this,"handleWheelScroll");b(this,"handleThumbPointerDown");b(this,"handleThumbPointerUp");c(this,Ot,m(()=>this.scrollbarVis.sizes.content-this.scrollbarVis.sizes.viewport));c(this,Dt,m(()=>O({...this.scrollbarState.props,style:{position:"absolute",...this.scrollbarState.props.style},[Vr]:"",onpointerdown:this.onpointerdown,onpointermove:this.onpointermove,onpointerup:this.onpointerup})));this.scrollbarState=t,this.root=t.root,this.scrollbarVis=t.scrollbarVis,this.scrollbar=t.scrollbarVis.scrollbar,this.handleResize=Jt(()=>this.scrollbarState.onResize(),10),this.handleThumbPositionChange=this.scrollbarState.onThumbPositionChange,this.handleWheelScroll=this.scrollbarState.onWheelScroll,this.handleThumbPointerDown=this.scrollbarState.onThumbPointerDown,this.handleThumbPointerUp=this.scrollbarState.onThumbPointerUp,L(()=>{const r=this.maxScrollPos,e=this.scrollbar.opts.ref.current;return this.root.viewportNode,or(document,"wheel",a=>{const f=a.target;(e==null?void 0:e.contains(f))&&this.handleWheelScroll(a,r)},{passive:!1})}),er(()=>{this.scrollbarVis.sizes,Ft(()=>this.handleThumbPositionChange())}),er(()=>{this.handleThumbPositionChange()}),J(()=>this.scrollbar.opts.ref.current,this.handleResize),J(()=>this.root.contentNode,this.handleResize),this.onpointerdown=this.onpointerdown.bind(this),this.onpointermove=this.onpointermove.bind(this),this.onpointerup=this.onpointerup.bind(this)}get rect(){return i(s(this,Et))}set rect(t){h(s(this,Et),t)}get prevWebkitUserSelect(){return i(s(this,Lt))}set prevWebkitUserSelect(t){h(s(this,Lt),t,!0)}get maxScrollPos(){return i(s(this,Ot))}set maxScrollPos(t){h(s(this,Ot),t)}handleDragScroll(t){if(!this.rect)return;const r=t.clientX-this.rect.left,e=t.clientY-this.rect.top;this.scrollbarState.onDragScroll({x:r,y:e})}onpointerdown(t){var e;if(t.button!==0)return;t.target.setPointerCapture(t.pointerId),this.rect=((e=this.scrollbar.opts.ref.current)==null?void 0:e.getBoundingClientRect())??null,this.prevWebkitUserSelect=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",this.root.viewportNode&&(this.root.viewportNode.style.scrollBehavior="auto"),this.handleDragScroll(t)}onpointermove(t){this.handleDragScroll(t)}onpointerup(t){const r=t.target;r.hasPointerCapture(t.pointerId)&&r.releasePointerCapture(t.pointerId),document.body.style.webkitUserSelect=this.prevWebkitUserSelect,this.root.viewportNode&&(this.root.viewportNode.style.scrollBehavior=""),this.rect=null}get props(){return i(s(this,Dt))}set props(t){h(s(this,Dt),t)}}Et=new WeakMap,Lt=new WeakMap,Ot=new WeakMap,Dt=new WeakMap;var Ht,Y,Gt,Wt;class Ir{constructor(t,r){b(this,"opts");b(this,"scrollbarState");c(this,Ht);c(this,Y,w());c(this,Gt,Jt(()=>{i(s(this,Y))&&(i(s(this,Y))(),h(s(this,Y),void 0))},100));c(this,Wt,m(()=>({id:this.opts.id.current,"data-state":this.scrollbarState.scrollbarVis.hasThumb?"visible":"hidden",style:{width:"var(--bits-scroll-area-thumb-width)",height:"var(--bits-scroll-area-thumb-height)",transform:this.scrollbarState.scrollbarVis.prevTransformStyle},onpointerdowncapture:this.onpointerdowncapture,onpointerup:this.onpointerup,[xr]:""})));this.opts=t,this.scrollbarState=r,cr(this,Ht,r.root),U({...t,onRefChange:e=>{this.scrollbarState.scrollbarVis.thumbNode=e},deps:()=>this.opts.mounted.current}),L(()=>{const e=s(this,Ht).viewportNode;if(!e)return;const n=()=>{if(s(this,Gt).call(this),!i(s(this,Y))){const a=ee(e,this.scrollbarState.handleThumbPositionChange);h(s(this,Y),a,!0),this.scrollbarState.handleThumbPositionChange()}};return this.scrollbarState.handleThumbPositionChange(),or(e,"scroll",n)}),this.onpointerdowncapture=this.onpointerdowncapture.bind(this),this.onpointerup=this.onpointerup.bind(this)}onpointerdowncapture(t){const r=t.target;if(!r)return;const e=r.getBoundingClientRect(),n=t.clientX-e.left,l=t.clientY-e.top;this.scrollbarState.handleThumbPointerDown({x:n,y:l})}onpointerup(t){this.scrollbarState.handleThumbPointerUp()}get props(){return i(s(this,Wt))}set props(t){h(s(this,Wt),t)}}Ht=new WeakMap,Y=new WeakMap,Gt=new WeakMap,Wt=new WeakMap;var F,q,Xt,Yt;class kr{constructor(t,r){b(this,"opts");b(this,"root");c(this,F,w(0));c(this,q,w(0));c(this,Xt,m(()=>!!(i(s(this,F))&&i(s(this,q)))));c(this,Yt,m(()=>({id:this.opts.id.current,style:{width:i(s(this,F)),height:i(s(this,q)),position:"absolute",right:this.root.opts.dir.current==="ltr"?0:void 0,left:this.root.opts.dir.current==="rtl"?0:void 0,bottom:0},[zr]:""})));this.opts=t,this.root=r,J(()=>this.root.scrollbarXNode,()=>{var n;const e=((n=this.root.scrollbarXNode)==null?void 0:n.offsetHeight)||0;this.root.cornerHeight=e,h(s(this,q),e,!0)}),J(()=>this.root.scrollbarYNode,()=>{var n;const e=((n=this.root.scrollbarYNode)==null?void 0:n.offsetWidth)||0;this.root.cornerWidth=e,h(s(this,F),e,!0)}),U(t)}get hasSize(){return i(s(this,Xt))}set hasSize(t){h(s(this,Xt),t)}get props(){return i(s(this,Yt))}set props(t){h(s(this,Yt),t)}}F=new WeakMap,q=new WeakMap,Xt=new WeakMap,Yt=new WeakMap;const Ut=new Mt("ScrollArea.Root"),It=new Mt("ScrollArea.Scrollbar"),Qt=new Mt("ScrollArea.ScrollbarVisible"),ir=new Mt("ScrollArea.ScrollbarAxis"),pr=new Mt("ScrollArea.ScrollbarShared");function Br(o){return Ut.set(new Er(o))}function Fr(o){return new Lr(o,Ut.get())}function qr(o){return It.set(new Or(o,Ut.get()))}function jr(){return Qt.set(new Xr(It.get()))}function fr(){return new Wr(It.get())}function Gr(){return new Hr(It.get())}function Jr(){return new Dr(It.get())}function Kr(o){return ir.set(new Yr(o,Qt.get()))}function Qr(o){return ir.set(new Mr(o,Qt.get()))}function Zr(){return pr.set(new Ur(ir.get()))}function $r(o){return new Ir(o,pr.get())}function te(o){return new kr(o,Ut.get())}function qt(o){return o?Number.parseInt(o,10):0}function mr(o,t){const r=o/t;return Number.isNaN(r)?0:r}function Zt(o){const t=mr(o.viewport,o.content),r=o.scrollbar.paddingStart+o.scrollbar.paddingEnd,e=(o.scrollbar.size-r)*t;return Math.max(e,18)}function re({pointerPos:o,pointerOffset:t,sizes:r,dir:e="ltr"}){const n=Zt(r),l=n/2,a=t||l,f=n-a,u=r.scrollbar.paddingStart+a,S=r.scrollbar.size-r.scrollbar.paddingEnd-f,p=r.content-r.viewport,d=e==="ltr"?[0,p]:[p*-1,0];return Sr([u,S],d)(o)}function dr({scrollPos:o,sizes:t,dir:r="ltr"}){const e=Zt(t),n=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-n,a=t.content-t.viewport,f=l-e,u=r==="ltr"?[0,a]:[a*-1,0],S=Rr(o,u[0],u[1]);return Sr([0,a],[0,f])(S)}function Sr(o,t){return r=>{if(o[0]===o[1]||t[0]===t[1])return t[0];const e=(t[1]-t[0])/(o[1]-o[0]);return t[0]+e*(r-o[0])}}function gr(o,t){return o>0&&o<t}function ee(o,t){let r={left:o.scrollLeft,top:o.scrollTop},e=0;return function n(){const l={left:o.scrollLeft,top:o.scrollTop},a=r.left!==l.left,f=r.top!==l.top;(a||f)&&t(),r=l,e=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(e)}var oe=I("<div><!></div>");function se(o,t){R(t,!0);let r=v(t,"ref",15,null),e=v(t,"id",19,Q),n=v(t,"type",3,"hover"),l=v(t,"dir",3,"ltr"),a=v(t,"scrollHideDelay",3,600),f=C(t,["$$slots","$$events","$$legacy","ref","id","type","dir","scrollHideDelay","children","child"]);const u=Br({type:T.with(()=>n()),dir:T.with(()=>l()),scrollHideDelay:T.with(()=>a()),id:T.with(()=>e()),ref:T.with(()=>r(),z=>r(z))}),S=m(()=>O(f,u.props));var p=x(),d=N(p);{var g=z=>{var _=x(),W=N(_);H(W,()=>t.child,()=>({props:i(S)})),P(z,_)},D=z=>{var _=oe();K(_,()=>({...i(S)}));var W=j(_);H(W,()=>t.children??k),G(_),P(z,_)};E(d,z=>{t.child?z(g):z(D,!1)})}P(o,p),A()}var ie=I("<div><div><!></div></div>");function ne(o,t){R(t,!0);let r=v(t,"ref",15,null),e=v(t,"id",19,Q),n=C(t,["$$slots","$$events","$$legacy","ref","id","children"]);const l=Fr({id:T.with(()=>e()),ref:T.with(()=>r(),d=>r(d))}),a=m(()=>O(n,l.props)),f=m(()=>O({},l.contentProps));var u=ie();K(u,()=>({...i(a)}));var S=j(u);K(S,()=>({...i(f)}));var p=j(S);H(p,()=>t.children??k),G(S),G(u),P(o,u),A()}var le=I("<div><!></div>");function vr(o,t){R(t,!0);let r=C(t,["$$slots","$$events","$$legacy","child","children"]);const e=Zr(),n=m(()=>O(r,e.props));var l=x(),a=N(l);{var f=S=>{var p=x(),d=N(p);H(d,()=>t.child,()=>({props:i(n)})),P(S,p)},u=S=>{var p=le();K(p,()=>({...i(n)}));var d=j(p);H(d,()=>t.children??k),G(p),P(S,p)};E(a,S=>{t.child?S(f):S(u,!1)})}P(o,l),A()}function ae(o,t){R(t,!0);let r=C(t,["$$slots","$$events","$$legacy"]);const e=new sr,n=Kr({mounted:T.with(()=>e.current)}),l=m(()=>O(r,n.props));vr(o,y(()=>i(l))),A()}function ce(o,t){R(t,!0);let r=C(t,["$$slots","$$events","$$legacy"]);const e=new sr,n=Qr({mounted:T.with(()=>e.current)}),l=m(()=>O(r,n.props));vr(o,y(()=>i(l))),A()}function $t(o,t){R(t,!0);let r=C(t,["$$slots","$$events","$$legacy"]);const e=jr();var n=x(),l=N(n);{var a=u=>{ae(u,y(()=>r))},f=u=>{ce(u,y(()=>r))};E(l,u=>{e.scrollbar.opts.orientation.current==="horizontal"?u(a):u(f,!1)})}P(o,n),A()}function he(o,t){R(t,!0);let r=v(t,"forceMount",3,!1),e=C(t,["$$slots","$$events","$$legacy","forceMount"]);const n=fr(),l=m(()=>O(e,n.props)),a=m(()=>r()||n.isVisible);Kt(o,y({get present(){return i(a)}},()=>i(l),{presence:u=>{$t(u,y(()=>i(l)))},$$slots:{presence:!0}})),A()}function de(o,t){R(t,!0);let r=v(t,"forceMount",3,!1),e=C(t,["$$slots","$$events","$$legacy","forceMount"]);const n=Gr(),l=m(()=>O(e,n.props)),a=m(()=>r()||!n.isHidden);Kt(o,y(()=>i(l),{get present(){return i(a)},presence:u=>{$t(u,y(()=>i(l)))},$$slots:{presence:!0}})),A()}function ue(o,t){R(t,!0);let r=v(t,"forceMount",3,!1),e=C(t,["$$slots","$$events","$$legacy","forceMount"]);const n=Jr(),l=fr(),a=m(()=>O(e,n.props,l.props,{"data-state":n.isVisible?"visible":"hidden"})),f=m(()=>r()||n.isVisible&&l.isVisible);Kt(o,y(()=>i(a),{get present(){return i(f)},presence:S=>{$t(S,y(()=>i(a)))},$$slots:{presence:!0}})),A()}function be(o,t){R(t,!0);let r=v(t,"ref",15,null),e=v(t,"id",19,Q),n=C(t,["$$slots","$$events","$$legacy","ref","id","orientation"]);const l=qr({orientation:T.with(()=>t.orientation),id:T.with(()=>e()),ref:T.with(()=>r(),d=>r(d))}),a=m(()=>l.root.opts.type.current);var f=x(),u=N(f);{var S=d=>{ue(d,y(()=>n,{get id(){return e()}}))},p=(d,g)=>{{var D=_=>{de(_,y(()=>n,{get id(){return e()}}))},z=(_,W)=>{{var tr=X=>{he(X,y(()=>n,{get id(){return e()}}))},kt=(X,rr)=>{{var V=M=>{$t(M,y(()=>n,{get id(){return e()}}))};E(X,M=>{i(a)==="always"&&M(V)},rr)}};E(_,X=>{i(a)==="auto"?X(tr):X(kt,!1)},W)}};E(d,_=>{i(a)==="scroll"?_(D):_(z,!1)},g)}};E(u,d=>{i(a)==="hover"?d(S):d(p,!1)})}P(o,f),A()}var pe=I("<div><!></div>");function fe(o,t){R(t,!0);let r=v(t,"ref",15,null),e=C(t,["$$slots","$$events","$$legacy","ref","id","child","children","present"]);const n=new sr,l=$r({id:T.with(()=>t.id),ref:T.with(()=>r(),d=>r(d)),mounted:T.with(()=>n.current)}),a=m(()=>O(e,l.props,{style:{hidden:!t.present}}));var f=x(),u=N(f);{var S=d=>{var g=x(),D=N(g);H(D,()=>t.child,()=>({props:i(a)})),P(d,g)},p=d=>{var g=pe();K(g,()=>({...i(a)}));var D=j(g);H(D,()=>t.children??k),G(g),P(d,g)};E(u,d=>{t.child?d(S):d(p,!1)})}P(o,f),A()}function me(o,t){R(t,!0);let r=v(t,"id",19,Q),e=v(t,"ref",15,null),n=v(t,"forceMount",3,!1),l=C(t,["$$slots","$$events","$$legacy","id","ref","forceMount"]);const a=Qt.get(),f=m(()=>n()||a.hasThumb);Kt(o,y({get present(){return i(f)}},()=>l,{get id(){return r()},presence:(S,p)=>{let d=()=>p==null?void 0:p().present;fe(S,y(()=>l,{get id(){return r()},get present(){return d().current},get ref(){return e()},set ref(g){e(g)}}))},$$slots:{presence:!0}})),A()}var Se=I("<div><!></div>");function ge(o,t){R(t,!0);let r=v(t,"ref",15,null),e=C(t,["$$slots","$$events","$$legacy","ref","id","children","child"]);const n=te({id:T.with(()=>t.id),ref:T.with(()=>r(),p=>r(p))}),l=m(()=>O(e,n.props));var a=x(),f=N(a);{var u=p=>{var d=x(),g=N(d);H(g,()=>t.child,()=>({props:i(l)})),P(p,d)},S=p=>{var d=Se();K(d,()=>({...i(l)}));var g=j(d);H(g,()=>t.children??k),G(d),P(p,d)};E(f,p=>{t.child?p(u):p(S,!1)})}P(o,a),A()}function ve(o,t){R(t,!0);let r=v(t,"ref",15,null),e=v(t,"id",19,Q),n=C(t,["$$slots","$$events","$$legacy","ref","id"]);const l=Ut.get(),a=m(()=>!!(l.scrollbarXNode&&l.scrollbarYNode)),f=m(()=>l.opts.type.current!=="scroll"&&i(a));var u=x(),S=N(u);{var p=d=>{ge(d,y(()=>n,{get id(){return e()},get ref(){return r()},set ref(g){r(g)}}))};E(S,d=>{i(f)&&d(p)})}P(o,u),A()}var we=I("<!> <!>",1);function ur(o,t){R(t,!0);let r=v(t,"ref",15,null),e=v(t,"orientation",3,"vertical"),n=C(t,["$$slots","$$events","$$legacy","ref","class","orientation","children"]);var l=x(),a=N(l);const f=m(()=>br("flex touch-none select-none p-px transition-colors",e()==="vertical"&&"h-full w-2.5 border-l border-l-transparent",e()==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent",t.class));Z(a,()=>be,(u,S)=>{S(u,y({"data-slot":"scroll-area-scrollbar",get orientation(){return e()},get class(){return i(f)}},()=>n,{get ref(){return r()},set ref(p){r(p)},children:(p,d)=>{var g=we(),D=N(g);H(D,()=>t.children??k);var z=Bt(D,2);Z(z,()=>me,(_,W)=>{W(_,{"data-slot":"scroll-area-thumb",class:"bg-border relative flex-1 rounded-full"})}),P(p,g)},$$slots:{default:!0}}))}),P(o,l),A()}var Pe=I("<!> <!> <!> <!>",1);function Xe(o,t){R(t,!0);let r=v(t,"ref",15,null),e=v(t,"orientation",3,"vertical"),n=v(t,"scrollbarXClasses",3,""),l=v(t,"scrollbarYClasses",3,""),a=C(t,["$$slots","$$events","$$legacy","ref","class","orientation","scrollbarXClasses","scrollbarYClasses","children"]);var f=x(),u=N(f);const S=m(()=>br("relative",t.class));Z(u,()=>se,(p,d)=>{d(p,y({"data-slot":"scroll-area",get class(){return i(S)}},()=>a,{get ref(){return r()},set ref(g){r(g)},children:(g,D)=>{var z=Pe(),_=N(z);Z(_,()=>ne,(V,M)=>{M(V,{"data-slot":"scroll-area-viewport",class:"ring-ring/10 dark:ring-ring/20 dark:outline-ring/40 outline-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] focus-visible:outline-1 focus-visible:ring-4",children:(wr,_e)=>{var nr=x(),Pr=N(nr);H(Pr,()=>t.children??k),P(wr,nr)},$$slots:{default:!0}})});var W=Bt(_,2);{var tr=V=>{ur(V,{orientation:"vertical",get class(){return l()}})};E(W,V=>{(e()==="vertical"||e()==="both")&&V(tr)})}var kt=Bt(W,2);{var X=V=>{ur(V,{orientation:"horizontal",get class(){return n()}})};E(kt,V=>{(e()==="horizontal"||e()==="both")&&V(X)})}var rr=Bt(kt,2);Z(rr,()=>ve,(V,M)=>{M(V,{})}),P(g,z)},$$slots:{default:!0}}))}),P(o,f),A()}export{Xe as S,ur as a};
