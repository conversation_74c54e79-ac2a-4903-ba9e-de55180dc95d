import{d as t}from"./sanity-DV0NwVOn.js";const s=t("tasks",{"actions.create.text":"Create new task","actions.open.text":"Tasks","buttons.create.text":"Create Task","buttons.discard.text":"Discard","buttons.draft.text":"Draft","buttons.new.text":"New task","buttons.new.upsell-tooltip":"Upgrade to create tasks","buttons.next.tooltip":"Go to next task","buttons.previous.tooltip":"Go to previous task","dialog.remove-task.body":"Once deleted, a task cannot be recovered.","dialog.remove-task.buttons.cancel.text":"Cancel","dialog.remove-task.buttons.confirm.text":"Delete","dialog.remove-task.title":"Delete this task?","document.footer.open-tasks.placeholder_one":"Open task","document.footer.open-tasks.placeholder_other":"Open tasks","document.footer.open-tasks.text_one":"{{count}} open task","document.footer.open-tasks.text_other":"{{count}} open tasks","empty-state.list.assigned.heading":"You haven't been assigned any tasks","empty-state.list.assigned.text":"Once you're assigned tasks they'll show up here","empty-state.list.create-new":"Create new task","empty-state.list.document.heading":"This document doesn't have any tasks yet","empty-state.list.document.text":"Once a document has connected tasks, they will be shown here.","empty-state.list.no-active-document.heading":"Open a document to see its task","empty-state.list.no-active-document.text":"Tasks on your active document will be shown here.","empty-state.list.subscribed.heading":"You haven't subscribed to any tasks","empty-state.list.subscribed.text":"When you create, modify, or comment on a task you will be subscribed automatically","empty-state.status.list.closed.assigned.heading":"No completed tasks","empty-state.status.list.closed.assigned.text":"Your tasks marked done will show up here","empty-state.status.list.closed.document.heading":"No completed tasks","empty-state.status.list.closed.subscribed.heading":"No completed tasks","empty-state.status.list.closed.subscribed.text":"Tasks you subscribe to marked done will show up here","empty-state.status.list.open.assigned.heading":"You're all caught up","empty-state.status.list.open.assigned.text":"New tasks assigned to you will show up here","empty-state.status.list.open.document.heading":"No tasks on this document","empty-state.status.list.open.subscribed.heading":"No subscribed tasks","empty-state.status.list.open.subscribed.text":"Tasks you subscribe to will show up here","form.input.assignee.no-user-assigned.text":"Unassigned","form.input.assignee.no-user-assigned.tooltip":"Set assignee","form.input.assignee.search.no-users.text":"No users found","form.input.assignee.search.placeholder":"Select assignee","form.input.assignee.unauthorized.text":"Unauthorized","form.input.assignee.user-assigned.tooltip":"Change assignee","form.input.assignee.user-not-found.text":"User not found","form.input.create-more.text":"Create more","form.input.date.buttons.empty.tooltip":"Set due date","form.input.date.buttons.remove.text":"Remove","form.input.date.buttons.tooltip":"Change due date","form.input.description.placeholder":"Add description","form.input.status.button.tooltip":"Change status","form.input.target.buttons.remove.text":"Remove target content","form.input.target.error.schema-not-found":"Schema not found","form.input.target.search.placeholder":"Select target document","form.input.title.placeholder":"Task title","form.status.error.title-required":"Title is required","form.status.success":"Task created","list.empty.text":"No tasks","list.feedback.text":"Help us improve, <Link>share feedback on Tasks</Link> ","menuitem.copylink.text":"Copy link to task","menuitem.delete.text":"Delete task","menuitem.duplicate.text":"Duplicate task","menuitem.duplicate.upsell-tooltip":"Upgrade to duplicate tasks","panel.activity.created-fragment":"created this task","panel.activity.title":"Activity","panel.activity.unknown-user":"Unknown user","panel.close.tooltip":"Close sidebar","panel.comment.placeholder":"Add a comment...","panel.comment.placeholder.upsell":"Upgrade to comment on tasks","panel.create.title":"Create","panel.drafts.title":"Drafts","panel.navigation.tooltip":"Open tasks","panel.title":"Tasks","tab.assigned.label":"Assigned","tab.document.label":"Active Document","tab.subscribed.label":"Subscribed","toolbar.tooltip":"Tasks"});export{s as default};
