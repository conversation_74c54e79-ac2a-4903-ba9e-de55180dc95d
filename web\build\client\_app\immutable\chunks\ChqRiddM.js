import{c as n,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as m,s as c}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function H(o,t){const e=m(t,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4"}],["path",{d:"M10 9H8"}],["path",{d:"M16 13H8"}],["path",{d:"M16 17H8"}]];f(o,c({name:"file-text"},()=>e,{get iconNode(){return r},children:(s,h)=>{var a=n(),p=d(a);l(p,t,"default",{},null),i(s,a)},$$slots:{default:!0}}))}export{H as F};
