import{c as p,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as d,s as m}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(r,o){const a=d(o,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0"}],["path",{d:"M22 8c0-2.3-.8-4.3-2-6"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"}],["path",{d:"M4 2C2.8 3.7 2 5.7 2 8"}]];f(r,m({name:"bell-ring"},()=>a,{get iconNode(){return s},children:(e,$)=>{var t=p(),n=i(t);c(n,o,"default",{},null),l(e,t)},$$slots:{default:!0}}))}export{y as B};
