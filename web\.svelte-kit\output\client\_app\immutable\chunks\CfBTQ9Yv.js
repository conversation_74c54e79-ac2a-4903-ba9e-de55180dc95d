import{N as w,w as Y,b,M as F,m as K,a as W,P as E,c as D,T as x,t as R,S as T,d as J,E as H,k as U,F as V,e as _,f as B,D as Z,g as Q,h as ee,i as te,j as ne,n as re,l as se}from"./BGYDhraB.js";import{Bold as ie}from"./BUfAIGxh.js";import{BulletList as oe}from"./foSyZIKW.js";import{Italic as ae}from"./BLAa_HGQ.js";import{ListItem as le}from"./BMpib84U.js";import{OrderedList as ue}from"./Sg8aqnBg.js";const pe=/^\s*>\s$/,de=w.create({name:"blockquote",addOptions(){return{HTMLAttributes:{}}},content:"block+",group:"block",defining:!0,parseHTML(){return[{tag:"blockquote"}]},renderHTML({HTMLAttributes:t}){return["blockquote",b(this.options.HTMLAttributes,t),0]},addCommands(){return{setBlockquote:()=>({commands:t})=>t.wrapIn(this.name),toggleBlockquote:()=>({commands:t})=>t.toggleWrap(this.name),unsetBlockquote:()=>({commands:t})=>t.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[Y({find:pe,type:this.type})]}}),ce=/(^|[^`])`([^`]+)`(?!`)/,fe=/(^|[^`])`([^`]+)`(?!`)/g,he=F.create({name:"code",addOptions(){return{HTMLAttributes:{}}},excludes:"_",code:!0,exitable:!0,parseHTML(){return[{tag:"code"}]},renderHTML({HTMLAttributes:t}){return["code",b(this.options.HTMLAttributes,t),0]},addCommands(){return{setCode:()=>({commands:t})=>t.setMark(this.name),toggleCode:()=>({commands:t})=>t.toggleMark(this.name),unsetCode:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[W({find:ce,type:this.type})]},addPasteRules(){return[K({find:fe,type:this.type})]}}),me=/^```([a-z]+)?[\s\n]$/,ge=/^~~~([a-z]+)?[\s\n]$/,ve=w.create({name:"codeBlock",addOptions(){return{languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:t=>{var e;const{languageClassPrefix:n}=this.options,i=[...((e=t.firstElementChild)===null||e===void 0?void 0:e.classList)||[]].filter(o=>o.startsWith(n)).map(o=>o.replace(n,""))[0];return i||null},rendered:!1}}},parseHTML(){return[{tag:"pre",preserveWhitespace:"full"}]},renderHTML({node:t,HTMLAttributes:e}){return["pre",b(this.options.HTMLAttributes,e),["code",{class:t.attrs.language?this.options.languageClassPrefix+t.attrs.language:null},0]]},addCommands(){return{setCodeBlock:t=>({commands:e})=>e.setNode(this.name,t),toggleCodeBlock:t=>({commands:e})=>e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:t,$anchor:e}=this.editor.state.selection,n=e.pos===1;return!t||e.parent.type.name!==this.name?!1:n||!e.parent.textContent.length?this.editor.commands.clearNodes():!1},Enter:({editor:t})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:e}=t,{selection:n}=e,{$from:r,empty:s}=n;if(!s||r.parent.type!==this.type)return!1;const i=r.parentOffset===r.parent.nodeSize-2,o=r.parent.textContent.endsWith(`

`);return!i||!o?!1:t.chain().command(({tr:a})=>(a.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:t})=>{if(!this.options.exitOnArrowDown)return!1;const{state:e}=t,{selection:n,doc:r}=e,{$from:s,empty:i}=n;if(!i||s.parent.type!==this.type||!(s.parentOffset===s.parent.nodeSize-2))return!1;const a=s.after();return a===void 0?!1:r.nodeAt(a)?t.commands.command(({tr:p})=>(p.setSelection(T.near(r.resolve(a))),!0)):t.commands.exitCode()}}},addInputRules(){return[R({find:me,type:this.type,getAttributes:t=>({language:t[1]})}),R({find:ge,type:this.type,getAttributes:t=>({language:t[1]})})]},addProseMirrorPlugins(){return[new E({key:new D("codeBlockVSCodeHandler"),props:{handlePaste:(t,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;const n=e.clipboardData.getData("text/plain"),r=e.clipboardData.getData("vscode-editor-data"),s=r?JSON.parse(r):void 0,i=s==null?void 0:s.mode;if(!n||!i)return!1;const{tr:o,schema:a}=t.state,l=a.text(n.replace(/\r\n?/g,`
`));return o.replaceSelectionWith(this.type.create({language:i},l)),o.selection.$from.parent.type!==this.type&&o.setSelection(x.near(o.doc.resolve(Math.max(0,o.selection.from-2)))),o.setMeta("paste",!0),t.dispatch(o),!0}}})]}}),ye=w.create({name:"doc",topNode:!0,content:"block+"});function Me(t={}){return new E({view(e){return new we(e,t)}})}class we{constructor(e,n){var r;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=(r=n.width)!==null&&r!==void 0?r:1,this.color=n.color===!1?void 0:n.color||"black",this.class=n.class,this.handlers=["dragover","dragend","drop","dragleave"].map(s=>{let i=o=>{this[s](o)};return e.dom.addEventListener(s,i),{name:s,handler:i}})}destroy(){this.handlers.forEach(({name:e,handler:n})=>this.editorView.dom.removeEventListener(e,n))}update(e,n){this.cursorPos!=null&&n.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,e==null?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e=this.editorView.state.doc.resolve(this.cursorPos),n=!e.parent.inlineContent,r,s=this.editorView.dom,i=s.getBoundingClientRect(),o=i.width/s.offsetWidth,a=i.height/s.offsetHeight;if(n){let u=e.nodeBefore,d=e.nodeAfter;if(u||d){let h=this.editorView.nodeDOM(this.cursorPos-(u?u.nodeSize:0));if(h){let g=h.getBoundingClientRect(),k=u?g.bottom:g.top;u&&d&&(k=(k+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let A=this.width/2*a;r={left:g.left,right:g.right,top:k-A,bottom:k+A}}}}if(!r){let u=this.editorView.coordsAtPos(this.cursorPos),d=this.width/2*o;r={left:u.left-d,right:u.left+d,top:u.top,bottom:u.bottom}}let l=this.editorView.dom.offsetParent;this.element||(this.element=l.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",n),this.element.classList.toggle("prosemirror-dropcursor-inline",!n);let p,c;if(!l||l==document.body&&getComputedStyle(l).position=="static")p=-pageXOffset,c=-pageYOffset;else{let u=l.getBoundingClientRect(),d=u.width/l.offsetWidth,h=u.height/l.offsetHeight;p=u.left-l.scrollLeft*d,c=u.top-l.scrollTop*h}this.element.style.left=(r.left-p)/o+"px",this.element.style.top=(r.top-c)/a+"px",this.element.style.width=(r.right-r.left)/o+"px",this.element.style.height=(r.bottom-r.top)/a+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let n=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),r=n&&n.inside>=0&&this.editorView.state.doc.nodeAt(n.inside),s=r&&r.type.spec.disableDropCursor,i=typeof s=="function"?s(this.editorView,n,e):s;if(n&&!i){let o=n.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let a=J(this.editorView.state.doc,o,this.editorView.dragging.slice);a!=null&&(o=a)}this.setCursor(o),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}const be=H.create({name:"dropCursor",addOptions(){return{color:"currentColor",width:1,class:void 0}},addProseMirrorPlugins(){return[Me(this.options)]}});class f extends T{constructor(e){super(e,e)}map(e,n){let r=e.resolve(n.map(this.head));return f.valid(r)?new f(r):T.near(r)}content(){return _.empty}eq(e){return e instanceof f&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,n){if(typeof n.pos!="number")throw new RangeError("Invalid input for GapCursor.fromJSON");return new f(e.resolve(n.pos))}getBookmark(){return new N(this.anchor)}static valid(e){let n=e.parent;if(n.isTextblock||!Ce(e)||!ke(e))return!1;let r=n.type.spec.allowGapCursor;if(r!=null)return r;let s=n.contentMatchAt(e.index()).defaultType;return s&&s.isTextblock}static findGapCursorFrom(e,n,r=!1){e:for(;;){if(!r&&f.valid(e))return e;let s=e.pos,i=null;for(let o=e.depth;;o--){let a=e.node(o);if(n>0?e.indexAfter(o)<a.childCount:e.index(o)>0){i=a.child(n>0?e.indexAfter(o):e.index(o)-1);break}else if(o==0)return null;s+=n;let l=e.doc.resolve(s);if(f.valid(l))return l}for(;;){let o=n>0?i.firstChild:i.lastChild;if(!o){if(i.isAtom&&!i.isText&&!B.isSelectable(i)){e=e.doc.resolve(s+i.nodeSize*n),r=!1;continue e}break}i=o,s+=n;let a=e.doc.resolve(s);if(f.valid(a))return a}return null}}}f.prototype.visible=!1;f.findFrom=f.findGapCursorFrom;T.jsonID("gapcursor",f);class N{constructor(e){this.pos=e}map(e){return new N(e.map(this.pos))}resolve(e){let n=e.resolve(this.pos);return f.valid(n)?new f(n):T.near(n)}}function Ce(t){for(let e=t.depth;e>=0;e--){let n=t.index(e),r=t.node(e);if(n==0){if(r.type.spec.isolating)return!0;continue}for(let s=r.child(n-1);;s=s.lastChild){if(s.childCount==0&&!s.inlineContent||s.isAtom||s.type.spec.isolating)return!0;if(s.inlineContent)return!1}}return!0}function ke(t){for(let e=t.depth;e>=0;e--){let n=t.indexAfter(e),r=t.node(e);if(n==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let s=r.child(n);;s=s.firstChild){if(s.childCount==0&&!s.inlineContent||s.isAtom||s.type.spec.isolating)return!0;if(s.inlineContent)return!1}}return!0}function xe(){return new E({props:{decorations:Le,createSelectionBetween(t,e,n){return e.pos==n.pos&&f.valid(n)?new f(n):null},handleClick:Te,handleKeyDown:Ae,handleDOMEvents:{beforeinput:Ie}}})}const Ae=U({ArrowLeft:I("horiz",-1),ArrowRight:I("horiz",1),ArrowUp:I("vert",-1),ArrowDown:I("vert",1)});function I(t,e){const n=t=="vert"?e>0?"down":"up":e>0?"right":"left";return function(r,s,i){let o=r.selection,a=e>0?o.$to:o.$from,l=o.empty;if(o instanceof x){if(!i.endOfTextblock(n)||a.depth==0)return!1;l=!1,a=r.doc.resolve(e>0?a.after():a.before())}let p=f.findGapCursorFrom(a,e,l);return p?(s&&s(r.tr.setSelection(new f(p))),!0):!1}}function Te(t,e,n){if(!t||!t.editable)return!1;let r=t.state.doc.resolve(e);if(!f.valid(r))return!1;let s=t.posAtCoords({left:n.clientX,top:n.clientY});return s&&s.inside>-1&&B.isSelectable(t.state.doc.nodeAt(s.inside))?!1:(t.dispatch(t.state.tr.setSelection(new f(r))),!0)}function Ie(t,e){if(e.inputType!="insertCompositionText"||!(t.state.selection instanceof f))return!1;let{$from:n}=t.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(t.state.schema.nodes.text);if(!r)return!1;let s=V.empty;for(let o=r.length-1;o>=0;o--)s=V.from(r[o].createAndFill(null,s));let i=t.state.tr.replace(n.pos,n.pos,new _(s,0,0));return i.setSelection(x.near(i.doc.resolve(n.pos+1))),t.dispatch(i),!1}function Le(t){if(!(t.selection instanceof f))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",Z.create(t.doc,[Q.widget(t.selection.head,e,{key:"gapcursor"})])}const Se=H.create({name:"gapCursor",addProseMirrorPlugins(){return[xe()]},extendNodeSchema(t){var e;const n={name:t.name,options:t.options,storage:t.storage};return{allowGapCursor:(e=ee(te(t,"allowGapCursor",n)))!==null&&e!==void 0?e:null}}}),Ee=w.create({name:"hardBreak",addOptions(){return{keepMarks:!0,HTMLAttributes:{}}},inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML(){return[{tag:"br"}]},renderHTML({HTMLAttributes:t}){return["br",b(this.options.HTMLAttributes,t)]},renderText(){return`
`},addCommands(){return{setHardBreak:()=>({commands:t,chain:e,state:n,editor:r})=>t.first([()=>t.exitCode(),()=>t.command(()=>{const{selection:s,storedMarks:i}=n;if(s.$from.parent.type.spec.isolating)return!1;const{keepMarks:o}=this.options,{splittableMarks:a}=r.extensionManager,l=i||s.$to.parentOffset&&s.$from.marks();return e().insertContent({type:this.name}).command(({tr:p,dispatch:c})=>{if(c&&l&&o){const u=l.filter(d=>a.includes(d.type.name));p.ensureMarks(u)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),He=w.create({name:"heading",addOptions(){return{levels:[1,2,3,4,5,6],HTMLAttributes:{}}},content:"inline*",group:"block",defining:!0,addAttributes(){return{level:{default:1,rendered:!1}}},parseHTML(){return this.options.levels.map(t=>({tag:`h${t}`,attrs:{level:t}}))},renderHTML({node:t,HTMLAttributes:e}){return[`h${this.options.levels.includes(t.attrs.level)?t.attrs.level:this.options.levels[0]}`,b(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:t=>({commands:e})=>this.options.levels.includes(t.level)?e.setNode(this.name,t):!1,toggleHeading:t=>({commands:e})=>this.options.levels.includes(t.level)?e.toggleNode(this.name,"paragraph",t):!1}},addKeyboardShortcuts(){return this.options.levels.reduce((t,e)=>({...t,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}),{})},addInputRules(){return this.options.levels.map(t=>R({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${t}})\\s$`),type:this.type,getAttributes:{level:t}}))}});var S=200,m=function(){};m.prototype.append=function(e){return e.length?(e=m.from(e),!this.length&&e||e.length<S&&this.leafAppend(e)||this.length<S&&e.leafPrepend(this)||this.appendInner(e)):this};m.prototype.prepend=function(e){return e.length?m.from(e).append(this):this};m.prototype.appendInner=function(e){return new Pe(this,e)};m.prototype.slice=function(e,n){return e===void 0&&(e=0),n===void 0&&(n=this.length),e>=n?m.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,n))};m.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)};m.prototype.forEach=function(e,n,r){n===void 0&&(n=0),r===void 0&&(r=this.length),n<=r?this.forEachInner(e,n,r,0):this.forEachInvertedInner(e,n,r,0)};m.prototype.map=function(e,n,r){n===void 0&&(n=0),r===void 0&&(r=this.length);var s=[];return this.forEach(function(i,o){return s.push(e(i,o))},n,r),s};m.from=function(e){return e instanceof m?e:e&&e.length?new G(e):m.empty};var G=function(t){function e(r){t.call(this),this.values=r}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var n={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(s,i){return s==0&&i==this.length?this:new e(this.values.slice(s,i))},e.prototype.getInner=function(s){return this.values[s]},e.prototype.forEachInner=function(s,i,o,a){for(var l=i;l<o;l++)if(s(this.values[l],a+l)===!1)return!1},e.prototype.forEachInvertedInner=function(s,i,o,a){for(var l=i-1;l>=o;l--)if(s(this.values[l],a+l)===!1)return!1},e.prototype.leafAppend=function(s){if(this.length+s.length<=S)return new e(this.values.concat(s.flatten()))},e.prototype.leafPrepend=function(s){if(this.length+s.length<=S)return new e(s.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(e.prototype,n),e}(m);m.empty=new G([]);var Pe=function(t){function e(n,r){t.call(this),this.left=n,this.right=r,this.length=n.length+r.length,this.depth=Math.max(n.depth,r.depth)+1}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(r){return r<this.left.length?this.left.get(r):this.right.get(r-this.left.length)},e.prototype.forEachInner=function(r,s,i,o){var a=this.left.length;if(s<a&&this.left.forEachInner(r,s,Math.min(i,a),o)===!1||i>a&&this.right.forEachInner(r,Math.max(s-a,0),Math.min(this.length,i)-a,o+a)===!1)return!1},e.prototype.forEachInvertedInner=function(r,s,i,o){var a=this.left.length;if(s>a&&this.right.forEachInvertedInner(r,s-a,Math.max(i,a)-a,o+a)===!1||i<a&&this.left.forEachInvertedInner(r,Math.min(s,a),i,o)===!1)return!1},e.prototype.sliceInner=function(r,s){if(r==0&&s==this.length)return this;var i=this.left.length;return s<=i?this.left.slice(r,s):r>=i?this.right.slice(r-i,s-i):this.left.slice(r,i).append(this.right.slice(0,s-i))},e.prototype.leafAppend=function(r){var s=this.right.leafAppend(r);if(s)return new e(this.left,s)},e.prototype.leafPrepend=function(r){var s=this.left.leafPrepend(r);if(s)return new e(s,this.right)},e.prototype.appendInner=function(r){return this.left.depth>=Math.max(this.right.depth,r.depth)+1?new e(this.left,new e(this.right,r)):new e(this,r)},e}(m);const Oe=500;class v{constructor(e,n){this.items=e,this.eventCount=n}popEvent(e,n){if(this.eventCount==0)return null;let r=this.items.length;for(;;r--)if(this.items.get(r-1).selection){--r;break}let s,i;n&&(s=this.remapping(r,this.items.length),i=s.maps.length);let o=e.tr,a,l,p=[],c=[];return this.items.forEach((u,d)=>{if(!u.step){s||(s=this.remapping(r,d+1),i=s.maps.length),i--,c.push(u);return}if(s){c.push(new y(u.map));let h=u.step.map(s.slice(i)),g;h&&o.maybeStep(h).doc&&(g=o.mapping.maps[o.mapping.maps.length-1],p.push(new y(g,void 0,void 0,p.length+c.length))),i--,g&&s.appendMap(g,i)}else o.maybeStep(u.step);if(u.selection)return a=s?u.selection.map(s.slice(i)):u.selection,l=new v(this.items.slice(0,r).append(c.reverse().concat(p)),this.eventCount-1),!1},this.items.length,0),{remaining:l,transform:o,selection:a}}addTransform(e,n,r,s){let i=[],o=this.eventCount,a=this.items,l=!s&&a.length?a.get(a.length-1):null;for(let c=0;c<e.steps.length;c++){let u=e.steps[c].invert(e.docs[c]),d=new y(e.mapping.maps[c],u,n),h;(h=l&&l.merge(d))&&(d=h,c?i.pop():a=a.slice(0,a.length-1)),i.push(d),n&&(o++,n=void 0),s||(l=d)}let p=o-r.depth;return p>De&&(a=Re(a,p),o-=p),new v(a.append(i),o)}remapping(e,n){let r=new ne;return this.items.forEach((s,i)=>{let o=s.mirrorOffset!=null&&i-s.mirrorOffset>=e?r.maps.length-s.mirrorOffset:void 0;r.appendMap(s.map,o)},e,n),r}addMaps(e){return this.eventCount==0?this:new v(this.items.append(e.map(n=>new y(n))),this.eventCount)}rebased(e,n){if(!this.eventCount)return this;let r=[],s=Math.max(0,this.items.length-n),i=e.mapping,o=e.steps.length,a=this.eventCount;this.items.forEach(d=>{d.selection&&a--},s);let l=n;this.items.forEach(d=>{let h=i.getMirror(--l);if(h==null)return;o=Math.min(o,h);let g=i.maps[h];if(d.step){let k=e.steps[h].invert(e.docs[h]),A=d.selection&&d.selection.map(i.slice(l+1,h));A&&a++,r.push(new y(g,k,A))}else r.push(new y(g))},s);let p=[];for(let d=n;d<o;d++)p.push(new y(i.maps[d]));let c=this.items.slice(0,s).append(p).append(r),u=new v(c,a);return u.emptyItemCount()>Oe&&(u=u.compress(this.items.length-r.length)),u}emptyItemCount(){let e=0;return this.items.forEach(n=>{n.step||e++}),e}compress(e=this.items.length){let n=this.remapping(0,e),r=n.maps.length,s=[],i=0;return this.items.forEach((o,a)=>{if(a>=e)s.push(o),o.selection&&i++;else if(o.step){let l=o.step.map(n.slice(r)),p=l&&l.getMap();if(r--,p&&n.appendMap(p,r),l){let c=o.selection&&o.selection.map(n.slice(r));c&&i++;let u=new y(p.invert(),l,c),d,h=s.length-1;(d=s.length&&s[h].merge(u))?s[h]=d:s.push(u)}}else o.map&&r--},this.items.length,0),new v(m.from(s.reverse()),i)}}v.empty=new v(m.empty,0);function Re(t,e){let n;return t.forEach((r,s)=>{if(r.selection&&e--==0)return n=s,!1}),t.slice(n)}class y{constructor(e,n,r,s){this.map=e,this.step=n,this.selection=r,this.mirrorOffset=s}merge(e){if(this.step&&e.step&&!e.selection){let n=e.step.merge(this.step);if(n)return new y(n.getMap().invert(),n,this.selection)}}}class M{constructor(e,n,r,s,i){this.done=e,this.undone=n,this.prevRanges=r,this.prevTime=s,this.prevComposition=i}}const De=20;function Be(t,e,n,r){let s=n.getMeta(C),i;if(s)return s.historyState;n.getMeta(ze)&&(t=new M(t.done,t.undone,null,0,-1));let o=n.getMeta("appendedTransaction");if(n.steps.length==0)return t;if(o&&o.getMeta(C))return o.getMeta(C).redo?new M(t.done.addTransform(n,void 0,r,L(e)),t.undone,z(n.mapping.maps),t.prevTime,t.prevComposition):new M(t.done,t.undone.addTransform(n,void 0,r,L(e)),null,t.prevTime,t.prevComposition);if(n.getMeta("addToHistory")!==!1&&!(o&&o.getMeta("addToHistory")===!1)){let a=n.getMeta("composition"),l=t.prevTime==0||!o&&t.prevComposition!=a&&(t.prevTime<(n.time||0)-r.newGroupDelay||!Ne(n,t.prevRanges)),p=o?P(t.prevRanges,n.mapping):z(n.mapping.maps);return new M(t.done.addTransform(n,l?e.selection.getBookmark():void 0,r,L(e)),v.empty,p,n.time,a??t.prevComposition)}else return(i=n.getMeta("rebased"))?new M(t.done.rebased(n,i),t.undone.rebased(n,i),P(t.prevRanges,n.mapping),t.prevTime,t.prevComposition):new M(t.done.addMaps(n.mapping.maps),t.undone.addMaps(n.mapping.maps),P(t.prevRanges,n.mapping),t.prevTime,t.prevComposition)}function Ne(t,e){if(!e)return!1;if(!t.docChanged)return!0;let n=!1;return t.mapping.maps[0].forEach((r,s)=>{for(let i=0;i<e.length;i+=2)r<=e[i+1]&&s>=e[i]&&(n=!0)}),n}function z(t){let e=[];for(let n=t.length-1;n>=0&&e.length==0;n--)t[n].forEach((r,s,i,o)=>e.push(i,o));return e}function P(t,e){if(!t)return null;let n=[];for(let r=0;r<t.length;r+=2){let s=e.map(t[r],1),i=e.map(t[r+1],-1);s<=i&&n.push(s,i)}return n}function Ve(t,e,n){let r=L(e),s=C.get(e).spec.config,i=(n?t.undone:t.done).popEvent(e,r);if(!i)return null;let o=i.selection.resolve(i.transform.doc),a=(n?t.done:t.undone).addTransform(i.transform,e.selection.getBookmark(),s,r),l=new M(n?a:i.remaining,n?i.remaining:a,null,0,-1);return i.transform.setSelection(o).setMeta(C,{redo:n,historyState:l})}let O=!1,$=null;function L(t){let e=t.plugins;if($!=e){O=!1,$=e;for(let n=0;n<e.length;n++)if(e[n].spec.historyPreserveItems){O=!0;break}}return O}const C=new D("history"),ze=new D("closeHistory");function $e(t={}){return t={depth:t.depth||100,newGroupDelay:t.newGroupDelay||500},new E({key:C,state:{init(){return new M(v.empty,v.empty,null,0,-1)},apply(e,n,r){return Be(n,r,e,t)}},config:t,props:{handleDOMEvents:{beforeinput(e,n){let r=n.inputType,s=r=="historyUndo"?q:r=="historyRedo"?X:null;return s?(n.preventDefault(),s(e.state,e.dispatch)):!1}}}})}function j(t,e){return(n,r)=>{let s=C.getState(n);if(!s||(t?s.undone:s.done).eventCount==0)return!1;if(r){let i=Ve(s,n,t);i&&r(e?i.scrollIntoView():i)}return!0}}const q=j(!1,!0),X=j(!0,!0),Fe=H.create({name:"history",addOptions(){return{depth:100,newGroupDelay:500}},addCommands(){return{undo:()=>({state:t,dispatch:e})=>q(t,e),redo:()=>({state:t,dispatch:e})=>X(t,e)}},addProseMirrorPlugins(){return[$e(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),Ke=w.create({name:"horizontalRule",addOptions(){return{HTMLAttributes:{}}},group:"block",parseHTML(){return[{tag:"hr"}]},renderHTML({HTMLAttributes:t}){return["hr",b(this.options.HTMLAttributes,t)]},addCommands(){return{setHorizontalRule:()=>({chain:t,state:e})=>{const{selection:n}=e,{$from:r,$to:s}=n,i=t();return r.parentOffset===0?i.insertContentAt({from:Math.max(r.pos-1,0),to:s.pos},{type:this.name}):se(n)?i.insertContentAt(s.pos,{type:this.name}):i.insertContent({type:this.name}),i.command(({tr:o,dispatch:a})=>{var l;if(a){const{$to:p}=o.selection,c=p.end();if(p.nodeAfter)p.nodeAfter.isTextblock?o.setSelection(x.create(o.doc,p.pos+1)):p.nodeAfter.isBlock?o.setSelection(B.create(o.doc,p.pos)):o.setSelection(x.create(o.doc,p.pos));else{const u=(l=p.parent.type.contentMatch.defaultType)===null||l===void 0?void 0:l.create();u&&(o.insert(c,u),o.setSelection(x.create(o.doc,c+1)))}o.scrollIntoView()}return!0}).run()}}},addInputRules(){return[re({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),We=w.create({name:"paragraph",priority:1e3,addOptions(){return{HTMLAttributes:{}}},group:"block",content:"inline*",parseHTML(){return[{tag:"p"}]},renderHTML({HTMLAttributes:t}){return["p",b(this.options.HTMLAttributes,t),0]},addCommands(){return{setParagraph:()=>({commands:t})=>t.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),_e=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,Ge=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,je=F.create({name:"strike",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:t=>t.includes("line-through")?{}:!1}]},renderHTML({HTMLAttributes:t}){return["s",b(this.options.HTMLAttributes,t),0]},addCommands(){return{setStrike:()=>({commands:t})=>t.setMark(this.name),toggleStrike:()=>({commands:t})=>t.toggleMark(this.name),unsetStrike:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[W({find:_e,type:this.type})]},addPasteRules(){return[K({find:Ge,type:this.type})]}}),qe=w.create({name:"text",group:"inline"}),et=H.create({name:"starterKit",addExtensions(){const t=[];return this.options.bold!==!1&&t.push(ie.configure(this.options.bold)),this.options.blockquote!==!1&&t.push(de.configure(this.options.blockquote)),this.options.bulletList!==!1&&t.push(oe.configure(this.options.bulletList)),this.options.code!==!1&&t.push(he.configure(this.options.code)),this.options.codeBlock!==!1&&t.push(ve.configure(this.options.codeBlock)),this.options.document!==!1&&t.push(ye.configure(this.options.document)),this.options.dropcursor!==!1&&t.push(be.configure(this.options.dropcursor)),this.options.gapcursor!==!1&&t.push(Se.configure(this.options.gapcursor)),this.options.hardBreak!==!1&&t.push(Ee.configure(this.options.hardBreak)),this.options.heading!==!1&&t.push(He.configure(this.options.heading)),this.options.history!==!1&&t.push(Fe.configure(this.options.history)),this.options.horizontalRule!==!1&&t.push(Ke.configure(this.options.horizontalRule)),this.options.italic!==!1&&t.push(ae.configure(this.options.italic)),this.options.listItem!==!1&&t.push(le.configure(this.options.listItem)),this.options.orderedList!==!1&&t.push(ue.configure(this.options.orderedList)),this.options.paragraph!==!1&&t.push(We.configure(this.options.paragraph)),this.options.strike!==!1&&t.push(je.configure(this.options.strike)),this.options.text!==!1&&t.push(qe.configure(this.options.text)),t}});export{et as StarterKit,et as default};
