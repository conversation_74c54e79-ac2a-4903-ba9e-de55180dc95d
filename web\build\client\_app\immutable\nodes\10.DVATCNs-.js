import{f as h,a as n,t as L}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as Ye,l as P,b as Ke,f as A,t as S,a as Ne,g as o,e as U,c as t,s as r,d as O,m as B,r as e,n as y}from"../chunks/CGmarHxI.js";import{s as x}from"../chunks/CIt1g2O9.js";import{i as je}from"../chunks/u21ee2wt.js";import{e as z,i as E}from"../chunks/C3w0v0gR.js";import{c as ke}from"../chunks/BvdI7LR8.js";import{b as Qe}from"../chunks/B-Xjo-Yt.js";import{i as Xe}from"../chunks/BIEMS98f.js";import{p as et}from"../chunks/Btcx8l8F.js";import{S as tt}from"../chunks/C6g8ubaU.js";import{C as ee}from"../chunks/DuGukytH.js";import{C as te}from"../chunks/Cdn-N1RY.js";import{R as rt,T as st}from"../chunks/I7hvcB12.js";import{B as re}from"../chunks/B1K98fMG.js";import{P as at}from"../chunks/BMgaXnEE.js";import{A as Ae}from"../chunks/Cs0qIT7f.js";import{M as Ce}from"../chunks/yPulTJ2h.js";import{T as se}from"../chunks/C88uNE8B.js";import{T as ae}from"../chunks/DmZyh-PW.js";import{C as J}from"../chunks/DW7T7T22.js";import{C as ot}from"../chunks/-SpbofVw.js";import{B as it}from"../chunks/CDnvByek.js";import{G as nt}from"../chunks/D1zde6Ej.js";import{S as lt}from"../chunks/rNI1Perp.js";import{Z as dt}from"../chunks/1zwBog76.js";import{T as ct}from"../chunks/CZ8wIJN8.js";import{A as mt}from"../chunks/B_tyjpYb.js";import{U as pt}from"../chunks/BSHZ37s_.js";import{R as Ie}from"../chunks/BMRJMPdn.js";var vt=h('<div class="text-muted-foreground mx-auto mt-6 max-w-3xl"><!></div>'),ut=h('<div class="text-center"><p class="text-primary text-3xl font-bold md:text-4xl"> </p> <p class="text-muted-foreground text-sm md:text-base"> </p></div>'),ft=h("<!> <!> <!>",1),ht=h(`<div class="grid gap-12 md:grid-cols-2"><div><h2 class="mb-4 text-3xl font-bold">Our Mission</h2> <p class="text-muted-foreground mb-6">At Hirli, our mission is to help job seekers find and apply to relevant opportunities
              more efficiently, saving them time and increasing their chances of landing the right
              job.</p> <p class="text-muted-foreground mb-6">We believe that the traditional job application process is broken. It's
              time-consuming, repetitive, and often discouraging. By leveraging AI and automation,
              we're transforming this experience into something faster, smarter, and more effective.</p> <p class="text-muted-foreground">Our goal is to give job seekers back their most valuable resource—time—while helping
              them discover and secure opportunities that truly match their skills, experience, and
              career aspirations.</p></div> <div class="bg-muted rounded-lg p-8"><h3 class="mb-4 text-xl font-semibold">What We're Building</h3> <ul class="space-y-4"><li class="flex items-start"><!> <span>An AI-powered platform that automates the job application process</span></li> <li class="flex items-start"><!> <span>Intelligent matching algorithms that connect candidates with the right
                  opportunities</span></li> <li class="flex items-start"><!> <span>Tools that help job seekers present their skills and experience effectively</span></li> <li class="flex items-start"><!> <span>A streamlined application process that saves hours of repetitive work</span></li> <li class="flex items-start"><!> <span>A comprehensive dashboard to track and manage all job applications in one place</span></li></ul></div></div>`),xt=h('<div class="bg-muted-foreground/20 h-full w-0.5"></div>'),gt=h('<div class="flex"><div class="mr-6 flex flex-col items-center"><div class="bg-primary text-primary-foreground flex h-10 w-10 items-center justify-center rounded-full text-sm font-bold"> </div> <!></div> <div class="pb-8"><h3 class="text-xl font-semibold"> </h3> <p class="text-muted-foreground"> </p></div></div>'),_t=h(`<div><h2 class="mb-4 text-3xl font-bold">Our Story</h2> <p class="text-muted-foreground mb-8">Hirli was founded in 2022 by Jane Smith and Michael Chen, who experienced firsthand the
            frustrations of the modern job search process. After spending countless hours submitting
            applications and filling out the same information repeatedly, they knew there had to be
            a better way.</p> <div class="mb-12 space-y-8"></div> <p class="text-muted-foreground">Today, Hirli continues to grow and evolve, driven by our commitment to making job
            searching less painful and more productive. We're constantly innovating and expanding
            our capabilities to better serve job seekers around the world.</p></div>`),bt=h('<div class="bg-primary/10 text-primary mb-4 flex h-12 w-12 items-center justify-center rounded-full"><!></div> <div class="mb-2 flex items-center"><span class="bg-primary text-primary-foreground mr-2 flex h-6 w-6 items-center justify-center rounded-full text-xs font-bold"></span> <h3 class="text-lg font-semibold"> </h3></div> <p class="text-muted-foreground"> </p>',1),yt=h(`<div><h2 class="mb-4 text-3xl font-bold">How Hirli Works</h2> <p class="text-muted-foreground mb-8">Hirli uses advanced AI and automation to streamline the job application process, saving
            you time and helping you find the right opportunities faster.</p> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3"></div> <div class="mt-8 text-center"><a href="/sign-up"><!></a></div></div>`),wt=h("<!> <!> <!> <!>",1),$t=h('<div class="bg-primary/10 text-primary mb-4 flex h-12 w-12 items-center justify-center rounded-full"><!></div> <h3 class="mb-2 text-xl font-semibold"> </h3> <p class="text-muted-foreground"> </p>',1),jt=h('<h3 class="mb-1 text-xl font-semibold"> </h3> <p class="text-muted-foreground mb-3 text-sm"> </p> <p class="text-muted-foreground"> </p>',1),kt=h('<div class="bg-muted aspect-square w-full overflow-hidden"><div class="h-full w-full bg-cover bg-center"></div></div> <!>',1),At=h("<span>View Open Positions</span> <!>",1),Ct=h("<!> <span>Contact Recruiting</span>",1),It=h(`<!> <div class="container mx-auto px-4 py-16"><div class="mb-20 text-center"><h1 class="mb-6 text-4xl font-bold md:text-5xl"> </h1> <p class="text-muted-foreground mx-auto max-w-3xl text-xl"> </p> <!></div> <div class="bg-muted/50 mb-20 rounded-lg p-8"><div class="grid grid-cols-2 gap-8 md:grid-cols-4"></div></div> <div class="mb-20"><!></div> <div class="mb-20"><h2 class="mb-8 text-center text-3xl font-bold">Our Values</h2> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"></div></div> <div class="mb-20"><h2 class="mb-8 text-center text-3xl font-bold">Our Team</h2> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3"></div></div> <div class="border-border rounded-lg border p-8"><div class="flex flex-col items-center text-center md:flex-row md:items-center md:justify-between md:text-left"><div class="mb-6 md:mb-0 md:mr-8"><h2 class="mb-2 text-2xl font-semibold">Join Our Team</h2> <p class="text-muted-foreground">We're always looking for talented individuals who are passionate about transforming the
          job search experience.</p></div> <div class="flex flex-wrap gap-4"><a href="/careers"><!></a> <a href="mailto:<EMAIL>"><!></a></div></div></div></div>`,1);function ir(Se,oe){Ye(oe,!1);const ie=B(),ne=B(),G=B(),le=B(),de=B(),s=et(oe,"data",8)().aboutPage,Te=[{icon:"Rocket",title:"Innovation",description:"We constantly push the boundaries of what's possible in job search technology, leveraging AI and automation to create better experiences."},{icon:"Users",title:"User-Centric",description:"Every feature we build starts with our users' needs. We obsess over creating intuitive, helpful tools that make job searching less stressful."},{icon:"Shield",title:"Trust & Security",description:"We handle sensitive personal information with the utmost care, maintaining rigorous security standards and transparent data practices."},{icon:"Target",title:"Efficiency",description:"We believe in working smarter, not harder—both in how we build our platform and how we help job seekers optimize their search process."},{icon:"Globe",title:"Accessibility",description:"We strive to make career opportunities accessible to everyone, regardless of background, location, or circumstance."},{icon:"Award",title:"Excellence",description:"We hold ourselves to the highest standards in everything we do, from code quality to customer support to user experience design."}],We=[{name:"Jane Smith",title:"CEO & Co-Founder",bio:"Jane has over 15 years of experience in HR tech and previously founded two successful startups. She holds an MBA from Stanford and is passionate about using technology to improve the job search process.",image:"/images/team/jane-smith.jpg"},{name:"Michael Chen",title:"CTO & Co-Founder",bio:"Michael is an AI expert with a Ph.D. in Computer Science from MIT. Before Hirli, he led engineering teams at Google and developed machine learning systems for talent acquisition.",image:"/images/team/michael-chen.jpg"},{name:"Sarah Johnson",title:"Chief Product Officer",bio:"Sarah brings 10+ years of product management experience from LinkedIn and Indeed. She specializes in creating intuitive user experiences that solve complex problems.",image:"/images/team/sarah-johnson.jpg"}],Me=[{year:"2022",title:"Founded",description:"Hirli was founded with a mission to revolutionize the job application process through automation and AI."},{year:"2022",title:"Platform Launch",description:"Launched our core platform with automated job application capabilities for major job boards."},{year:"2023",title:"100,000 Users",description:"Celebrated reaching 100,000 users on our platform."}],He=[{title:"Create Your Profile",description:"Build your professional profile with your skills, experience, and job preferences.",icon:"Briefcase"},{title:"Set Job Preferences",description:"Specify the types of jobs you`re interested in, preferred locations, and salary expectations.",icon:"Target"},{title:"AI Matches You With Jobs",description:"Our AI analyzes thousands of job listings to find the best matches for your profile.",icon:"Zap"}],Oe=[{value:"100,000+",label:"Active Users"},{value:"2.5M+",label:"Applications Submitted"},{value:"85%",label:"Time Saved"},{value:"40+",label:"Job Platforms Supported"}];function ce(a){return{Rocket:Ie,Users:pt,Award:mt,Target:ct,Zap:dt,Shield:lt,Globe:nt,Briefcase:it,Clock:ot,CheckCircle:J,ArrowRight:Ae,Mail:Ce}[a]||Ie}let me=B("mission");P(()=>{},()=>{O(ie,(s==null?void 0:s.values)||Te)}),P(()=>{},()=>{O(ne,(s==null?void 0:s.teamMembers)||We)}),P(()=>{},()=>{O(G,(s==null?void 0:s.milestones)||Me)}),P(()=>{},()=>{O(le,(s==null?void 0:s.howItWorks)||He)}),P(()=>{},()=>{O(de,(s==null?void 0:s.stats)||Oe)}),Ke(),Xe();var pe=It(),ve=A(pe);const Be=U(()=>{var a;return((a=s==null?void 0:s.seo)==null?void 0:a.metaTitle)||"About Hirli | AI-Powered Job Application Platform"}),Je=U(()=>{var a;return((a=s==null?void 0:s.seo)==null?void 0:a.metaDescription)||"Learn about Hirli's mission to revolutionize the job search process through AI and automation. Meet our team and discover our story."}),Re=U(()=>{var a,i;return((i=(a=s==null?void 0:s.seo)==null?void 0:a.keywords)==null?void 0:i.join(", "))||"Hirli, about us, job application platform, AI job search, company mission, job search automation"});tt(ve,{get title(){return o(Be)},get description(){return o(Je)},get keywords(){return o(Re)}});var ue=r(ve,2),V=t(ue),F=t(V),Pe=t(F,!0);e(F);var Z=r(F,2),Ue=t(Z,!0);e(Z);var ze=r(Z,2);{var Ee=a=>{var i=vt(),m=t(i);at(m,{get value(){return s.content}}),e(i),n(a,i)};je(ze,a=>{s!=null&&s.content&&a(Ee)})}e(V);var D=r(V,2),fe=t(D);z(fe,5,()=>o(de),E,(a,i)=>{var m=ut(),_=t(m),$=t(_,!0);e(_);var w=r(_,2),C=t(w,!0);e(w),e(m),S(()=>{x($,o(i).value),x(C,o(i).label)}),n(a,m)}),e(fe),e(D);var Y=r(D,2),Ge=t(Y);rt(Ge,{get value(){return o(me)},onValueChange:a=>O(me,a),class:"w-full",children:(a,i)=>{var m=wt(),_=A(m);st(_,{class:"border-border w-full border-b",children:(g,j)=>{var l=ft(),v=A(l);se(v,{value:"mission",class:"px-4 py-2 text-sm font-medium",children:(d,p)=>{y();var c=L("Our Mission");n(d,c)},$$slots:{default:!0}});var u=r(v,2);se(u,{value:"story",class:"px-4 py-2 text-sm font-medium",children:(d,p)=>{y();var c=L("Our Story");n(d,c)},$$slots:{default:!0}});var f=r(u,2);se(f,{value:"how",class:"px-4 py-2 text-sm font-medium",children:(d,p)=>{y();var c=L("How It Works");n(d,c)},$$slots:{default:!0}}),n(g,l)},$$slots:{default:!0}});var $=r(_,2);ae($,{value:"mission",class:"pt-8",children:(g,j)=>{var l=ht(),v=r(t(l),2),u=r(t(v),2),f=t(u),d=t(f);J(d,{class:"text-primary mr-2 mt-1 h-5 w-5 flex-shrink-0"}),y(2),e(f);var p=r(f,2),c=t(p);J(c,{class:"text-primary mr-2 mt-1 h-5 w-5 flex-shrink-0"}),y(2),e(p);var b=r(p,2),R=t(b);J(R,{class:"text-primary mr-2 mt-1 h-5 w-5 flex-shrink-0"}),y(2),e(b);var T=r(b,2),W=t(T);J(W,{class:"text-primary mr-2 mt-1 h-5 w-5 flex-shrink-0"}),y(2),e(T);var I=r(T,2),M=t(I);J(M,{class:"text-primary mr-2 mt-1 h-5 w-5 flex-shrink-0"}),y(2),e(I),e(u),e(v),e(l),n(g,l)},$$slots:{default:!0}});var w=r($,2);ae(w,{value:"story",class:"pt-8",children:(g,j)=>{var l=_t(),v=r(t(l),4);z(v,5,()=>o(G),E,(u,f)=>{var d=gt(),p=t(d),c=t(p),b=t(c,!0);e(c);var R=r(c,2);{var T=k=>{var q=xt();n(k,q)};je(R,k=>{o(f)!==o(G)[o(G).length-1]&&k(T)})}e(p);var W=r(p,2),I=t(W),M=t(I,!0);e(I);var H=r(I,2),X=t(H,!0);e(H),e(W),e(d),S(k=>{x(b,k),x(M,o(f).title),x(X,o(f).description)},[()=>o(f).year.slice(-2)],U),n(u,d)}),e(v),y(2),e(l),n(g,l)},$$slots:{default:!0}});var C=r(w,2);ae(C,{value:"how",class:"pt-8",children:(g,j)=>{var l=yt(),v=r(t(l),4);z(v,5,()=>o(le),E,(p,c,b)=>{ee(p,{children:(R,T)=>{te(R,{class:"p-6",children:(W,I)=>{var M=bt(),H=A(M),X=t(H);ke(X,()=>ce(o(c).icon),(Ze,De)=>{De(Ze,{class:"h-6 w-6"})}),e(H);var k=r(H,2),q=t(k);q.textContent=b+1;var we=r(q,2),Ve=t(we,!0);e(we),e(k);var $e=r(k,2),Fe=t($e,!0);e($e),S(()=>{x(Ve,o(c).title),x(Fe,o(c).description)}),n(W,M)},$$slots:{default:!0}})},$$slots:{default:!0}})}),e(v);var u=r(v,2),f=t(u),d=t(f);re(d,{class:"px-8",children:(p,c)=>{y();var b=L("Get Started");n(p,b)},$$slots:{default:!0}}),e(f),e(u),e(l),n(g,l)},$$slots:{default:!0}}),n(a,m)},$$slots:{default:!0}}),e(Y);var K=r(Y,2),he=r(t(K),2);z(he,5,()=>o(ie),E,(a,i)=>{ee(a,{children:(m,_)=>{te(m,{class:"p-6",children:($,w)=>{var C=$t(),g=A(C),j=t(g);ke(j,()=>ce(o(i).icon),(d,p)=>{p(d,{class:"h-6 w-6"})}),e(g);var l=r(g,2),v=t(l,!0);e(l);var u=r(l,2),f=t(u,!0);e(u),S(()=>{x(v,o(i).title),x(f,o(i).description)}),n($,C)},$$slots:{default:!0}})},$$slots:{default:!0}})}),e(he),e(K);var N=r(K,2),xe=r(t(N),2);z(xe,5,()=>o(ne),E,(a,i)=>{ee(a,{children:(m,_)=>{var $=kt(),w=A($),C=t(w);e(w);var g=r(w,2);te(g,{class:"p-6",children:(j,l)=>{var v=jt(),u=A(v),f=t(u,!0);e(u);var d=r(u,2),p=t(d,!0);e(d);var c=r(d,2),b=t(c,!0);e(c),S(()=>{x(f,o(i).name),x(p,o(i).title),x(b,o(i).bio)}),n(j,v)},$$slots:{default:!0}}),S(j=>Qe(C,`background-image: url(${j??""})`),[()=>o(i).image||"https://via.placeholder.com/300x300?text="+encodeURIComponent(o(i).name)],U),n(m,$)},$$slots:{default:!0}})}),e(xe),e(N);var ge=r(N,2),_e=t(ge),be=r(t(_e),2),Q=t(be),qe=t(Q);re(qe,{variant:"default",class:"flex items-center",children:(a,i)=>{var m=At(),_=r(A(m),2);Ae(_,{class:"ml-2 h-4 w-4"}),n(a,m)},$$slots:{default:!0}}),e(Q);var ye=r(Q,2),Le=t(ye);re(Le,{variant:"outline",class:"flex items-center",children:(a,i)=>{var m=Ct(),_=A(m);Ce(_,{class:"mr-2 h-4 w-4"}),y(2),n(a,m)},$$slots:{default:!0}}),e(ye),e(be),e(_e),e(ge),e(ue),S(()=>{x(Pe,(s==null?void 0:s.title)||"About Hirli"),x(Ue,(s==null?void 0:s.description)||"We're on a mission to revolutionize the job search process through AI and automation, helping job seekers find and apply to relevant opportunities more efficiently.")}),n(Se,pe),Ne()}export{ir as component};
