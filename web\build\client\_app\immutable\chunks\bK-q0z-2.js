async function n(a,i,e=1,t=!0){try{const r=await fetch("/api/feature-usage",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({featureId:a,limitId:i,amount:e,checkLimit:t})}),s=await r.json();return r.ok?{success:!0,message:s.message||"Feature usage tracked successfully"}:{success:!1,error:s.error||"Failed to track feature usage",limitReached:s.limitReached,used:s.used,limit:s.limit}}catch(r){return console.error("Error tracking feature usage:",r),{success:!1,error:r.message||"Failed to track feature usage"}}}async function l(a,i){try{const e=await fetch(`/api/feature-usage?featureId=${a}&limitId=${i}`);if(!e.ok)return{limitReached:!1,error:(await e.json()).error||"Failed to check feature limit"};const t=await e.json(),r=Array.isArray(t)&&t.length>0?t.find(o=>o.featureId===a&&o.limitId===i):null;if(!r)return{limitReached:!1,used:0};const s=r.used||0,c=parseInt(r.limit,10);return{limitReached:c!==1/0&&s>=c,used:s,limit:c}}catch(e){return console.error("Error checking feature limit:",e),{limitReached:!1,error:e.message||"Failed to check feature limit"}}}async function f(a,i){try{const e=await fetch("/api/feature-usage/reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({featureId:a,limitId:i})}),t=await e.json();return e.ok?{success:!0,message:t.message||"Feature usage reset successfully"}:{success:!1,error:t.error||"Failed to reset feature usage"}}catch(e){return console.error("Error resetting feature usage:",e),{success:!1,error:e.message||"Failed to reset feature usage"}}}export{l as c,f as r,n as t};
