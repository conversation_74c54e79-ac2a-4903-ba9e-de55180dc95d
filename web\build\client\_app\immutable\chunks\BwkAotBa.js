import{c as m,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as d,s as l}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(s,o){const t=d(o,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"m6 9 6 6 6-6"}]];f(s,l({name:"chevron-down"},()=>t,{get iconNode(){return e},children:(n,$)=>{var r=m(),a=i(r);c(a,o,"default",{},null),p(n,r)},$$slots:{default:!0}}))}export{y as C};
