import{c as m,a as g}from"./BasJTneF.js";import{p as n,f as l,a as c}from"./CGmarHxI.js";import{c as d}from"./BvdI7LR8.js";import{p as u,s as _,r as D}from"./Btcx8l8F.js";import{D as v}from"./CQeqUgF6.js";function b(a,r){n(r,!0);let e=u(r,"ref",15,null),o=D(r,["$$slots","$$events","$$legacy","ref"]);var t=m(),s=l(t);d(s,()=>v,(p,f)=>{f(p,_({"data-slot":"dialog-trigger"},()=>o,{get ref(){return e()},set ref(i){e(i)}}))}),g(a,t),c()}export{b as D};
