import{a as xo,c as ne,g as Eo}from"./BosuxZz1.js";var mt={},$r={},te={},Gt={exports:{}};const Co={},Ao=Object.freeze(Object.defineProperty({__proto__:null,default:Co},Symbol.toStringTag,{value:"Module"})),Pe=xo(Ao);var Yt={exports:{}},an;function xr(){if(an)return Yt.exports;an=1,typeof process>"u"||!process.version||process.version.indexOf("v0.")===0||process.version.indexOf("v1.")===0&&process.version.indexOf("v1.8.")!==0?Yt.exports={nextTick:e}:Yt.exports=process;function e(r,t,a,n){if(typeof r!="function")throw new TypeError('"callback" argument must be a function');var i=arguments.length,s,o;switch(i){case 0:case 1:return process.nextTick(r);case 2:return process.nextTick(function(){r.call(null,t)});case 3:return process.nextTick(function(){r.call(null,t,a)});case 4:return process.nextTick(function(){r.call(null,t,a,n)});default:for(s=new Array(i-1),o=0;o<s.length;)s[o++]=arguments[o];return process.nextTick(function(){r.apply(null,s)})}}return Yt.exports}var Ur,nn;function Po(){if(nn)return Ur;nn=1;var e={}.toString;return Ur=Array.isArray||function(r){return e.call(r)=="[object Array]"},Ur}var jr,sn;function $i(){return sn||(sn=1,jr=Pe),jr}var Kt={exports:{}},on;function Oa(){return on||(on=1,function(e,r){var t=Pe,a=t.Buffer;function n(s,o){for(var f in s)o[f]=s[f]}a.from&&a.alloc&&a.allocUnsafe&&a.allocUnsafeSlow?e.exports=t:(n(t,r),r.Buffer=i);function i(s,o,f){return a(s,o,f)}n(a,i),i.from=function(s,o,f){if(typeof s=="number")throw new TypeError("Argument must not be a number");return a(s,o,f)},i.alloc=function(s,o,f){if(typeof s!="number")throw new TypeError("Argument must be a number");var l=a(s);return o!==void 0?typeof f=="string"?l.fill(o,f):l.fill(o):l.fill(0),l},i.allocUnsafe=function(s){if(typeof s!="number")throw new TypeError("Argument must be a number");return a(s)},i.allocUnsafeSlow=function(s){if(typeof s!="number")throw new TypeError("Argument must be a number");return t.SlowBuffer(s)}}(Kt,Kt.exports)),Kt.exports}var ae={},ln;function $t(){if(ln)return ae;ln=1;function e(g){return Array.isArray?Array.isArray(g):b(g)==="[object Array]"}ae.isArray=e;function r(g){return typeof g=="boolean"}ae.isBoolean=r;function t(g){return g===null}ae.isNull=t;function a(g){return g==null}ae.isNullOrUndefined=a;function n(g){return typeof g=="number"}ae.isNumber=n;function i(g){return typeof g=="string"}ae.isString=i;function s(g){return typeof g=="symbol"}ae.isSymbol=s;function o(g){return g===void 0}ae.isUndefined=o;function f(g){return b(g)==="[object RegExp]"}ae.isRegExp=f;function l(g){return typeof g=="object"&&g!==null}ae.isObject=l;function u(g){return b(g)==="[object Date]"}ae.isDate=u;function y(g){return b(g)==="[object Error]"||g instanceof Error}ae.isError=y;function c(g){return typeof g=="function"}ae.isFunction=c;function p(g){return g===null||typeof g=="boolean"||typeof g=="number"||typeof g=="string"||typeof g=="symbol"||typeof g>"u"}ae.isPrimitive=p,ae.isBuffer=Pe.Buffer.isBuffer;function b(g){return Object.prototype.toString.call(g)}return ae}var Jt={exports:{}},Qt={exports:{}},fn;function No(){return fn||(fn=1,typeof Object.create=="function"?Qt.exports=function(r,t){t&&(r.super_=t,r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}))}:Qt.exports=function(r,t){if(t){r.super_=t;var a=function(){};a.prototype=t.prototype,r.prototype=new a,r.prototype.constructor=r}}),Qt.exports}var un;function Ut(){if(un)return Jt.exports;un=1;try{var e=Pe;if(typeof e.inherits!="function")throw"";Jt.exports=e.inherits}catch{Jt.exports=No()}return Jt.exports}var Wr={exports:{}},hn;function To(){return hn||(hn=1,function(e){function r(i,s){if(!(i instanceof s))throw new TypeError("Cannot call a class as a function")}var t=Oa().Buffer,a=Pe;function n(i,s,o){i.copy(s,o)}e.exports=function(){function i(){r(this,i),this.head=null,this.tail=null,this.length=0}return i.prototype.push=function(o){var f={data:o,next:null};this.length>0?this.tail.next=f:this.head=f,this.tail=f,++this.length},i.prototype.unshift=function(o){var f={data:o,next:this.head};this.length===0&&(this.tail=f),this.head=f,++this.length},i.prototype.shift=function(){if(this.length!==0){var o=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,o}},i.prototype.clear=function(){this.head=this.tail=null,this.length=0},i.prototype.join=function(o){if(this.length===0)return"";for(var f=this.head,l=""+f.data;f=f.next;)l+=o+f.data;return l},i.prototype.concat=function(o){if(this.length===0)return t.alloc(0);for(var f=t.allocUnsafe(o>>>0),l=this.head,u=0;l;)n(l.data,f,u),u+=l.data.length,l=l.next;return f},i}(),a&&a.inspect&&a.inspect.custom&&(e.exports.prototype[a.inspect.custom]=function(){var i=a.inspect({length:this.length});return this.constructor.name+" "+i})}(Wr)),Wr.exports}var Hr,cn;function Ui(){if(cn)return Hr;cn=1;var e=xr();function r(n,i){var s=this,o=this._readableState&&this._readableState.destroyed,f=this._writableState&&this._writableState.destroyed;return o||f?(i?i(n):n&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,e.nextTick(a,this,n)):e.nextTick(a,this,n)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(n||null,function(l){!i&&l?s._writableState?s._writableState.errorEmitted||(s._writableState.errorEmitted=!0,e.nextTick(a,s,l)):e.nextTick(a,s,l):i&&i(l)}),this)}function t(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function a(n,i){n.emit("error",i)}return Hr={destroy:r,undestroy:t},Hr}var Zr,dn;function Ro(){return dn||(dn=1,Zr=Pe.deprecate),Zr}var qr,pn;function ji(){if(pn)return qr;pn=1;var e=xr();qr=g;function r(S){var x=this;this.next=null,this.entry=null,this.finish=function(){it(x,S)}}var t=!process.browser&&["v0.10","v0.9."].indexOf(process.version.slice(0,5))>-1?setImmediate:e.nextTick,a;g.WritableState=p;var n=Object.create($t());n.inherits=Ut();var i={deprecate:Ro()},s=$i(),o=Oa().Buffer,f=(typeof ne<"u"?ne:typeof window<"u"?window:typeof self<"u"?self:{}).Uint8Array||function(){};function l(S){return o.from(S)}function u(S){return o.isBuffer(S)||S instanceof f}var y=Ui();n.inherits(g,s);function c(){}function p(S,x){a=a||pt(),S=S||{};var N=x instanceof a;this.objectMode=!!S.objectMode,N&&(this.objectMode=this.objectMode||!!S.writableObjectMode);var F=S.highWaterMark,j=S.writableHighWaterMark,Z=this.objectMode?16:16*1024;F||F===0?this.highWaterMark=F:N&&(j||j===0)?this.highWaterMark=j:this.highWaterMark=Z,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var be=S.decodeStrings===!1;this.decodeStrings=!be,this.defaultEncoding=S.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(_e){B(x,_e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new r(this)}p.prototype.getBuffer=function(){for(var x=this.bufferedRequest,N=[];x;)N.push(x),x=x.next;return N},function(){try{Object.defineProperty(p.prototype,"buffer",{get:i.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch{}}();var b;typeof Symbol=="function"&&Symbol.hasInstance&&typeof Function.prototype[Symbol.hasInstance]=="function"?(b=Function.prototype[Symbol.hasInstance],Object.defineProperty(g,Symbol.hasInstance,{value:function(S){return b.call(this,S)?!0:this!==g?!1:S&&S._writableState instanceof p}})):b=function(S){return S instanceof this};function g(S){if(a=a||pt(),!b.call(g,this)&&!(this instanceof a))return new g(S);this._writableState=new p(S,this),this.writable=!0,S&&(typeof S.write=="function"&&(this._write=S.write),typeof S.writev=="function"&&(this._writev=S.writev),typeof S.destroy=="function"&&(this._destroy=S.destroy),typeof S.final=="function"&&(this._final=S.final)),s.call(this)}g.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))};function C(S,x){var N=new Error("write after end");S.emit("error",N),e.nextTick(x,N)}function h(S,x,N,F){var j=!0,Z=!1;return N===null?Z=new TypeError("May not write null values to stream"):typeof N!="string"&&N!==void 0&&!x.objectMode&&(Z=new TypeError("Invalid non-string/buffer chunk")),Z&&(S.emit("error",Z),e.nextTick(F,Z),j=!1),j}g.prototype.write=function(S,x,N){var F=this._writableState,j=!1,Z=!F.objectMode&&u(S);return Z&&!o.isBuffer(S)&&(S=l(S)),typeof x=="function"&&(N=x,x=null),Z?x="buffer":x||(x=F.defaultEncoding),typeof N!="function"&&(N=c),F.ended?C(this,N):(Z||h(this,F,S,N))&&(F.pendingcb++,j=w(this,F,Z,S,x,N)),j},g.prototype.cork=function(){var S=this._writableState;S.corked++},g.prototype.uncork=function(){var S=this._writableState;S.corked&&(S.corked--,!S.writing&&!S.corked&&!S.bufferProcessing&&S.bufferedRequest&&U(this,S))},g.prototype.setDefaultEncoding=function(x){if(typeof x=="string"&&(x=x.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((x+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+x);return this._writableState.defaultEncoding=x,this};function v(S,x,N){return!S.objectMode&&S.decodeStrings!==!1&&typeof x=="string"&&(x=o.from(x,N)),x}Object.defineProperty(g.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});function w(S,x,N,F,j,Z){if(!N){var be=v(x,F,j);F!==be&&(N=!0,j="buffer",F=be)}var _e=x.objectMode?1:F.length;x.length+=_e;var He=x.length<x.highWaterMark;if(He||(x.needDrain=!0),x.writing||x.corked){var Ze=x.lastBufferedRequest;x.lastBufferedRequest={chunk:F,encoding:j,isBuf:N,callback:Z,next:null},Ze?Ze.next=x.lastBufferedRequest:x.bufferedRequest=x.lastBufferedRequest,x.bufferedRequestCount+=1}else E(S,x,!1,_e,F,j,Z);return He}function E(S,x,N,F,j,Z,be){x.writelen=F,x.writecb=be,x.writing=!0,x.sync=!0,N?S._writev(j,x.onwrite):S._write(j,Z,x.onwrite),x.sync=!1}function A(S,x,N,F,j){--x.pendingcb,N?(e.nextTick(j,F),e.nextTick(he,S,x),S._writableState.errorEmitted=!0,S.emit("error",F)):(j(F),S._writableState.errorEmitted=!0,S.emit("error",F),he(S,x))}function M(S){S.writing=!1,S.writecb=null,S.length-=S.writelen,S.writelen=0}function B(S,x){var N=S._writableState,F=N.sync,j=N.writecb;if(M(N),x)A(S,N,F,x,j);else{var Z=R(N);!Z&&!N.corked&&!N.bufferProcessing&&N.bufferedRequest&&U(S,N),F?t(O,S,N,Z,j):O(S,N,Z,j)}}function O(S,x,N,F){N||D(S,x),x.pendingcb--,F(),he(S,x)}function D(S,x){x.length===0&&x.needDrain&&(x.needDrain=!1,S.emit("drain"))}function U(S,x){x.bufferProcessing=!0;var N=x.bufferedRequest;if(S._writev&&N&&N.next){var F=x.bufferedRequestCount,j=new Array(F),Z=x.corkedRequestsFree;Z.entry=N;for(var be=0,_e=!0;N;)j[be]=N,N.isBuf||(_e=!1),N=N.next,be+=1;j.allBuffers=_e,E(S,x,!0,x.length,j,"",Z.finish),x.pendingcb++,x.lastBufferedRequest=null,Z.next?(x.corkedRequestsFree=Z.next,Z.next=null):x.corkedRequestsFree=new r(x),x.bufferedRequestCount=0}else{for(;N;){var He=N.chunk,Ze=N.encoding,d=N.callback,m=x.objectMode?1:He.length;if(E(S,x,!1,m,He,Ze,d),N=N.next,x.bufferedRequestCount--,x.writing)break}N===null&&(x.lastBufferedRequest=null)}x.bufferedRequest=N,x.bufferProcessing=!1}g.prototype._write=function(S,x,N){N(new Error("_write() is not implemented"))},g.prototype._writev=null,g.prototype.end=function(S,x,N){var F=this._writableState;typeof S=="function"?(N=S,S=null,x=null):typeof x=="function"&&(N=x,x=null),S!=null&&this.write(S,x),F.corked&&(F.corked=1,this.uncork()),F.ending||nt(this,F,N)};function R(S){return S.ending&&S.length===0&&S.bufferedRequest===null&&!S.finished&&!S.writing}function ee(S,x){S._final(function(N){x.pendingcb--,N&&S.emit("error",N),x.prefinished=!0,S.emit("prefinish"),he(S,x)})}function se(S,x){!x.prefinished&&!x.finalCalled&&(typeof S._final=="function"?(x.pendingcb++,x.finalCalled=!0,e.nextTick(ee,S,x)):(x.prefinished=!0,S.emit("prefinish")))}function he(S,x){var N=R(x);return N&&(se(S,x),x.pendingcb===0&&(x.finished=!0,S.emit("finish"))),N}function nt(S,x,N){x.ending=!0,he(S,x),N&&(x.finished?e.nextTick(N):S.once("finish",N)),x.ended=!0,S.writable=!1}function it(S,x,N){var F=S.entry;for(S.entry=null;F;){var j=F.callback;x.pendingcb--,j(N),F=F.next}x.corkedRequestsFree.next=S}return Object.defineProperty(g.prototype,"destroyed",{get:function(){return this._writableState===void 0?!1:this._writableState.destroyed},set:function(S){this._writableState&&(this._writableState.destroyed=S)}}),g.prototype.destroy=y.destroy,g.prototype._undestroy=y.undestroy,g.prototype._destroy=function(S,x){this.end(),x(S)},qr}var Vr,mn;function pt(){if(mn)return Vr;mn=1;var e=xr(),r=Object.keys||function(y){var c=[];for(var p in y)c.push(p);return c};Vr=f;var t=Object.create($t());t.inherits=Ut();var a=Wi(),n=ji();t.inherits(f,a);for(var i=r(n.prototype),s=0;s<i.length;s++){var o=i[s];f.prototype[o]||(f.prototype[o]=n.prototype[o])}function f(y){if(!(this instanceof f))return new f(y);a.call(this,y),n.call(this,y),y&&y.readable===!1&&(this.readable=!1),y&&y.writable===!1&&(this.writable=!1),this.allowHalfOpen=!0,y&&y.allowHalfOpen===!1&&(this.allowHalfOpen=!1),this.once("end",l)}Object.defineProperty(f.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});function l(){this.allowHalfOpen||this._writableState.ended||e.nextTick(u,this)}function u(y){y.end()}return Object.defineProperty(f.prototype,"destroyed",{get:function(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set:function(y){this._readableState===void 0||this._writableState===void 0||(this._readableState.destroyed=y,this._writableState.destroyed=y)}}),f.prototype._destroy=function(y,c){this.push(null),this.end(),e.nextTick(c,y)},Vr}var Xr={},er={exports:{}},vn;function Mo(){return vn||(vn=1,function(e,r){var t=Pe,a=t.Buffer;function n(s,o){for(var f in s)o[f]=s[f]}a.from&&a.alloc&&a.allocUnsafe&&a.allocUnsafeSlow?e.exports=t:(n(t,r),r.Buffer=i);function i(s,o,f){return a(s,o,f)}n(a,i),i.from=function(s,o,f){if(typeof s=="number")throw new TypeError("Argument must not be a number");return a(s,o,f)},i.alloc=function(s,o,f){if(typeof s!="number")throw new TypeError("Argument must be a number");var l=a(s);return o!==void 0?typeof f=="string"?l.fill(o,f):l.fill(o):l.fill(0),l},i.allocUnsafe=function(s){if(typeof s!="number")throw new TypeError("Argument must be a number");return a(s)},i.allocUnsafeSlow=function(s){if(typeof s!="number")throw new TypeError("Argument must be a number");return t.SlowBuffer(s)}}(er,er.exports)),er.exports}var gn;function bn(){if(gn)return Xr;gn=1;var e=Mo().Buffer,r=e.isEncoding||function(h){switch(h=""+h,h&&h.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function t(h){if(!h)return"utf8";for(var v;;)switch(h){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return h;default:if(v)return;h=(""+h).toLowerCase(),v=!0}}function a(h){var v=t(h);if(typeof v!="string"&&(e.isEncoding===r||!r(h)))throw new Error("Unknown encoding: "+h);return v||h}Xr.StringDecoder=n;function n(h){this.encoding=a(h);var v;switch(this.encoding){case"utf16le":this.text=y,this.end=c,v=4;break;case"utf8":this.fillLast=f,v=4;break;case"base64":this.text=p,this.end=b,v=3;break;default:this.write=g,this.end=C;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=e.allocUnsafe(v)}n.prototype.write=function(h){if(h.length===0)return"";var v,w;if(this.lastNeed){if(v=this.fillLast(h),v===void 0)return"";w=this.lastNeed,this.lastNeed=0}else w=0;return w<h.length?v?v+this.text(h,w):this.text(h,w):v||""},n.prototype.end=u,n.prototype.text=l,n.prototype.fillLast=function(h){if(this.lastNeed<=h.length)return h.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);h.copy(this.lastChar,this.lastTotal-this.lastNeed,0,h.length),this.lastNeed-=h.length};function i(h){return h<=127?0:h>>5===6?2:h>>4===14?3:h>>3===30?4:h>>6===2?-1:-2}function s(h,v,w){var E=v.length-1;if(E<w)return 0;var A=i(v[E]);return A>=0?(A>0&&(h.lastNeed=A-1),A):--E<w||A===-2?0:(A=i(v[E]),A>=0?(A>0&&(h.lastNeed=A-2),A):--E<w||A===-2?0:(A=i(v[E]),A>=0?(A>0&&(A===2?A=0:h.lastNeed=A-3),A):0))}function o(h,v,w){if((v[0]&192)!==128)return h.lastNeed=0,"�";if(h.lastNeed>1&&v.length>1){if((v[1]&192)!==128)return h.lastNeed=1,"�";if(h.lastNeed>2&&v.length>2&&(v[2]&192)!==128)return h.lastNeed=2,"�"}}function f(h){var v=this.lastTotal-this.lastNeed,w=o(this,h);if(w!==void 0)return w;if(this.lastNeed<=h.length)return h.copy(this.lastChar,v,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);h.copy(this.lastChar,v,0,h.length),this.lastNeed-=h.length}function l(h,v){var w=s(this,h,v);if(!this.lastNeed)return h.toString("utf8",v);this.lastTotal=w;var E=h.length-(w-this.lastNeed);return h.copy(this.lastChar,0,E),h.toString("utf8",v,E)}function u(h){var v=h&&h.length?this.write(h):"";return this.lastNeed?v+"�":v}function y(h,v){if((h.length-v)%2===0){var w=h.toString("utf16le",v);if(w){var E=w.charCodeAt(w.length-1);if(E>=55296&&E<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=h[h.length-2],this.lastChar[1]=h[h.length-1],w.slice(0,-1)}return w}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=h[h.length-1],h.toString("utf16le",v,h.length-1)}function c(h){var v=h&&h.length?this.write(h):"";if(this.lastNeed){var w=this.lastTotal-this.lastNeed;return v+this.lastChar.toString("utf16le",0,w)}return v}function p(h,v){var w=(h.length-v)%3;return w===0?h.toString("base64",v):(this.lastNeed=3-w,this.lastTotal=3,w===1?this.lastChar[0]=h[h.length-1]:(this.lastChar[0]=h[h.length-2],this.lastChar[1]=h[h.length-1]),h.toString("base64",v,h.length-w))}function b(h){var v=h&&h.length?this.write(h):"";return this.lastNeed?v+this.lastChar.toString("base64",0,3-this.lastNeed):v}function g(h){return h.toString(this.encoding)}function C(h){return h&&h.length?this.write(h):""}return Xr}var Gr,_n;function Wi(){if(_n)return Gr;_n=1;var e=xr();Gr=v;var r=Po(),t;v.ReadableState=h,Pe.EventEmitter;var a=function(d,m){return d.listeners(m).length},n=$i(),i=Oa().Buffer,s=(typeof ne<"u"?ne:typeof window<"u"?window:typeof self<"u"?self:{}).Uint8Array||function(){};function o(d){return i.from(d)}function f(d){return i.isBuffer(d)||d instanceof s}var l=Object.create($t());l.inherits=Ut();var u=Pe,y=void 0;u&&u.debuglog?y=u.debuglog("stream"):y=function(){};var c=To(),p=Ui(),b;l.inherits(v,n);var g=["error","close","destroy","pause","resume"];function C(d,m,P){if(typeof d.prependListener=="function")return d.prependListener(m,P);!d._events||!d._events[m]?d.on(m,P):r(d._events[m])?d._events[m].unshift(P):d._events[m]=[P,d._events[m]]}function h(d,m){t=t||pt(),d=d||{};var P=m instanceof t;this.objectMode=!!d.objectMode,P&&(this.objectMode=this.objectMode||!!d.readableObjectMode);var T=d.highWaterMark,H=d.readableHighWaterMark,I=this.objectMode?16:16*1024;T||T===0?this.highWaterMark=T:P&&(H||H===0)?this.highWaterMark=H:this.highWaterMark=I,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new c,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=d.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,d.encoding&&(b||(b=bn().StringDecoder),this.decoder=new b(d.encoding),this.encoding=d.encoding)}function v(d){if(t=t||pt(),!(this instanceof v))return new v(d);this._readableState=new h(d,this),this.readable=!0,d&&(typeof d.read=="function"&&(this._read=d.read),typeof d.destroy=="function"&&(this._destroy=d.destroy)),n.call(this)}Object.defineProperty(v.prototype,"destroyed",{get:function(){return this._readableState===void 0?!1:this._readableState.destroyed},set:function(d){this._readableState&&(this._readableState.destroyed=d)}}),v.prototype.destroy=p.destroy,v.prototype._undestroy=p.undestroy,v.prototype._destroy=function(d,m){this.push(null),m(d)},v.prototype.push=function(d,m){var P=this._readableState,T;return P.objectMode?T=!0:typeof d=="string"&&(m=m||P.defaultEncoding,m!==P.encoding&&(d=i.from(d,m),m=""),T=!0),w(this,d,m,!1,T)},v.prototype.unshift=function(d){return w(this,d,null,!0,!1)};function w(d,m,P,T,H){var I=d._readableState;if(m===null)I.reading=!1,U(d,I);else{var z;H||(z=A(I,m)),z?d.emit("error",z):I.objectMode||m&&m.length>0?(typeof m!="string"&&!I.objectMode&&Object.getPrototypeOf(m)!==i.prototype&&(m=o(m)),T?I.endEmitted?d.emit("error",new Error("stream.unshift() after end event")):E(d,I,m,!0):I.ended?d.emit("error",new Error("stream.push() after EOF")):(I.reading=!1,I.decoder&&!P?(m=I.decoder.write(m),I.objectMode||m.length!==0?E(d,I,m,!1):se(d,I)):E(d,I,m,!1))):T||(I.reading=!1)}return M(I)}function E(d,m,P,T){m.flowing&&m.length===0&&!m.sync?(d.emit("data",P),d.read(0)):(m.length+=m.objectMode?1:P.length,T?m.buffer.unshift(P):m.buffer.push(P),m.needReadable&&R(d)),se(d,m)}function A(d,m){var P;return!f(m)&&typeof m!="string"&&m!==void 0&&!d.objectMode&&(P=new TypeError("Invalid non-string/buffer chunk")),P}function M(d){return!d.ended&&(d.needReadable||d.length<d.highWaterMark||d.length===0)}v.prototype.isPaused=function(){return this._readableState.flowing===!1},v.prototype.setEncoding=function(d){return b||(b=bn().StringDecoder),this._readableState.decoder=new b(d),this._readableState.encoding=d,this};var B=8388608;function O(d){return d>=B?d=B:(d--,d|=d>>>1,d|=d>>>2,d|=d>>>4,d|=d>>>8,d|=d>>>16,d++),d}function D(d,m){return d<=0||m.length===0&&m.ended?0:m.objectMode?1:d!==d?m.flowing&&m.length?m.buffer.head.data.length:m.length:(d>m.highWaterMark&&(m.highWaterMark=O(d)),d<=m.length?d:m.ended?m.length:(m.needReadable=!0,0))}v.prototype.read=function(d){y("read",d),d=parseInt(d,10);var m=this._readableState,P=d;if(d!==0&&(m.emittedReadable=!1),d===0&&m.needReadable&&(m.length>=m.highWaterMark||m.ended))return y("read: emitReadable",m.length,m.ended),m.length===0&&m.ended?_e(this):R(this),null;if(d=D(d,m),d===0&&m.ended)return m.length===0&&_e(this),null;var T=m.needReadable;y("need readable",T),(m.length===0||m.length-d<m.highWaterMark)&&(T=!0,y("length less than watermark",T)),m.ended||m.reading?(T=!1,y("reading or ended",T)):T&&(y("do read"),m.reading=!0,m.sync=!0,m.length===0&&(m.needReadable=!0),this._read(m.highWaterMark),m.sync=!1,m.reading||(d=D(P,m)));var H;return d>0?H=F(d,m):H=null,H===null?(m.needReadable=!0,d=0):m.length-=d,m.length===0&&(m.ended||(m.needReadable=!0),P!==d&&m.ended&&_e(this)),H!==null&&this.emit("data",H),H};function U(d,m){if(!m.ended){if(m.decoder){var P=m.decoder.end();P&&P.length&&(m.buffer.push(P),m.length+=m.objectMode?1:P.length)}m.ended=!0,R(d)}}function R(d){var m=d._readableState;m.needReadable=!1,m.emittedReadable||(y("emitReadable",m.flowing),m.emittedReadable=!0,m.sync?e.nextTick(ee,d):ee(d))}function ee(d){y("emit readable"),d.emit("readable"),N(d)}function se(d,m){m.readingMore||(m.readingMore=!0,e.nextTick(he,d,m))}function he(d,m){for(var P=m.length;!m.reading&&!m.flowing&&!m.ended&&m.length<m.highWaterMark&&(y("maybeReadMore read 0"),d.read(0),P!==m.length);)P=m.length;m.readingMore=!1}v.prototype._read=function(d){this.emit("error",new Error("_read() is not implemented"))},v.prototype.pipe=function(d,m){var P=this,T=this._readableState;switch(T.pipesCount){case 0:T.pipes=d;break;case 1:T.pipes=[T.pipes,d];break;default:T.pipes.push(d);break}T.pipesCount+=1,y("pipe count=%d opts=%j",T.pipesCount,m);var H=(!m||m.end!==!1)&&d!==process.stdout&&d!==process.stderr,I=H?Xt:St;T.endEmitted?e.nextTick(I):P.once("end",I),d.on("unpipe",z);function z(st,xt){y("onunpipe"),st===P&&xt&&xt.hasUnpiped===!1&&(xt.hasUnpiped=!0,So())}function Xt(){y("onend"),d.end()}var Dr=nt(P);d.on("drain",Dr);var tn=!1;function So(){y("cleanup"),d.removeListener("close",Lr),d.removeListener("finish",zr),d.removeListener("drain",Dr),d.removeListener("error",Ir),d.removeListener("unpipe",z),P.removeListener("end",Xt),P.removeListener("end",St),P.removeListener("data",rn),tn=!0,T.awaitDrain&&(!d._writableState||d._writableState.needDrain)&&Dr()}var Fr=!1;P.on("data",rn);function rn(st){y("ondata"),Fr=!1;var xt=d.write(st);xt===!1&&!Fr&&((T.pipesCount===1&&T.pipes===d||T.pipesCount>1&&Ze(T.pipes,d)!==-1)&&!tn&&(y("false write response, pause",T.awaitDrain),T.awaitDrain++,Fr=!0),P.pause())}function Ir(st){y("onerror",st),St(),d.removeListener("error",Ir),a(d,"error")===0&&d.emit("error",st)}C(d,"error",Ir);function Lr(){d.removeListener("finish",zr),St()}d.once("close",Lr);function zr(){y("onfinish"),d.removeListener("close",Lr),St()}d.once("finish",zr);function St(){y("unpipe"),P.unpipe(d)}return d.emit("pipe",P),T.flowing||(y("pipe resume"),P.resume()),d};function nt(d){return function(){var m=d._readableState;y("pipeOnDrain",m.awaitDrain),m.awaitDrain&&m.awaitDrain--,m.awaitDrain===0&&a(d,"data")&&(m.flowing=!0,N(d))}}v.prototype.unpipe=function(d){var m=this._readableState,P={hasUnpiped:!1};if(m.pipesCount===0)return this;if(m.pipesCount===1)return d&&d!==m.pipes?this:(d||(d=m.pipes),m.pipes=null,m.pipesCount=0,m.flowing=!1,d&&d.emit("unpipe",this,P),this);if(!d){var T=m.pipes,H=m.pipesCount;m.pipes=null,m.pipesCount=0,m.flowing=!1;for(var I=0;I<H;I++)T[I].emit("unpipe",this,{hasUnpiped:!1});return this}var z=Ze(m.pipes,d);return z===-1?this:(m.pipes.splice(z,1),m.pipesCount-=1,m.pipesCount===1&&(m.pipes=m.pipes[0]),d.emit("unpipe",this,P),this)},v.prototype.on=function(d,m){var P=n.prototype.on.call(this,d,m);if(d==="data")this._readableState.flowing!==!1&&this.resume();else if(d==="readable"){var T=this._readableState;!T.endEmitted&&!T.readableListening&&(T.readableListening=T.needReadable=!0,T.emittedReadable=!1,T.reading?T.length&&R(this):e.nextTick(it,this))}return P},v.prototype.addListener=v.prototype.on;function it(d){y("readable nexttick read 0"),d.read(0)}v.prototype.resume=function(){var d=this._readableState;return d.flowing||(y("resume"),d.flowing=!0,S(this,d)),this};function S(d,m){m.resumeScheduled||(m.resumeScheduled=!0,e.nextTick(x,d,m))}function x(d,m){m.reading||(y("resume read 0"),d.read(0)),m.resumeScheduled=!1,m.awaitDrain=0,d.emit("resume"),N(d),m.flowing&&!m.reading&&d.read(0)}v.prototype.pause=function(){return y("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&(y("pause"),this._readableState.flowing=!1,this.emit("pause")),this};function N(d){var m=d._readableState;for(y("flow",m.flowing);m.flowing&&d.read()!==null;);}v.prototype.wrap=function(d){var m=this,P=this._readableState,T=!1;d.on("end",function(){if(y("wrapped end"),P.decoder&&!P.ended){var z=P.decoder.end();z&&z.length&&m.push(z)}m.push(null)}),d.on("data",function(z){if(y("wrapped data"),P.decoder&&(z=P.decoder.write(z)),!(P.objectMode&&z==null)&&!(!P.objectMode&&(!z||!z.length))){var Xt=m.push(z);Xt||(T=!0,d.pause())}});for(var H in d)this[H]===void 0&&typeof d[H]=="function"&&(this[H]=function(z){return function(){return d[z].apply(d,arguments)}}(H));for(var I=0;I<g.length;I++)d.on(g[I],this.emit.bind(this,g[I]));return this._read=function(z){y("wrapped _read",z),T&&(T=!1,d.resume())},this},Object.defineProperty(v.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),v._fromList=F;function F(d,m){if(m.length===0)return null;var P;return m.objectMode?P=m.buffer.shift():!d||d>=m.length?(m.decoder?P=m.buffer.join(""):m.buffer.length===1?P=m.buffer.head.data:P=m.buffer.concat(m.length),m.buffer.clear()):P=j(d,m.buffer,m.decoder),P}function j(d,m,P){var T;return d<m.head.data.length?(T=m.head.data.slice(0,d),m.head.data=m.head.data.slice(d)):d===m.head.data.length?T=m.shift():T=P?Z(d,m):be(d,m),T}function Z(d,m){var P=m.head,T=1,H=P.data;for(d-=H.length;P=P.next;){var I=P.data,z=d>I.length?I.length:d;if(z===I.length?H+=I:H+=I.slice(0,d),d-=z,d===0){z===I.length?(++T,P.next?m.head=P.next:m.head=m.tail=null):(m.head=P,P.data=I.slice(z));break}++T}return m.length-=T,H}function be(d,m){var P=i.allocUnsafe(d),T=m.head,H=1;for(T.data.copy(P),d-=T.data.length;T=T.next;){var I=T.data,z=d>I.length?I.length:d;if(I.copy(P,P.length-d,0,z),d-=z,d===0){z===I.length?(++H,T.next?m.head=T.next:m.head=m.tail=null):(m.head=T,T.data=I.slice(z));break}++H}return m.length-=H,P}function _e(d){var m=d._readableState;if(m.length>0)throw new Error('"endReadable()" called on non-empty stream');m.endEmitted||(m.ended=!0,e.nextTick(He,m,d))}function He(d,m){!d.endEmitted&&d.length===0&&(d.endEmitted=!0,m.readable=!1,m.emit("end"))}function Ze(d,m){for(var P=0,T=d.length;P<T;P++)if(d[P]===m)return P;return-1}return Gr}var Yr,yn;function Hi(){if(yn)return Yr;yn=1,Yr=a;var e=pt(),r=Object.create($t());r.inherits=Ut(),r.inherits(a,e);function t(s,o){var f=this._transformState;f.transforming=!1;var l=f.writecb;if(!l)return this.emit("error",new Error("write callback called multiple times"));f.writechunk=null,f.writecb=null,o!=null&&this.push(o),l(s);var u=this._readableState;u.reading=!1,(u.needReadable||u.length<u.highWaterMark)&&this._read(u.highWaterMark)}function a(s){if(!(this instanceof a))return new a(s);e.call(this,s),this._transformState={afterTransform:t.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,s&&(typeof s.transform=="function"&&(this._transform=s.transform),typeof s.flush=="function"&&(this._flush=s.flush)),this.on("prefinish",n)}function n(){var s=this;typeof this._flush=="function"?this._flush(function(o,f){i(s,o,f)}):i(this,null,null)}a.prototype.push=function(s,o){return this._transformState.needTransform=!1,e.prototype.push.call(this,s,o)},a.prototype._transform=function(s,o,f){throw new Error("_transform() is not implemented")},a.prototype._write=function(s,o,f){var l=this._transformState;if(l.writecb=f,l.writechunk=s,l.writeencoding=o,!l.transforming){var u=this._readableState;(l.needTransform||u.needReadable||u.length<u.highWaterMark)&&this._read(u.highWaterMark)}},a.prototype._read=function(s){var o=this._transformState;o.writechunk!==null&&o.writecb&&!o.transforming?(o.transforming=!0,this._transform(o.writechunk,o.writeencoding,o.afterTransform)):o.needTransform=!0},a.prototype._destroy=function(s,o){var f=this;e.prototype._destroy.call(this,s,function(l){o(l),f.emit("close")})};function i(s,o,f){if(o)return s.emit("error",o);if(f!=null&&s.push(f),s._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(s._transformState.transforming)throw new Error("Calling transform done when still transforming");return s.push(null)}return Yr}var Kr,wn;function Bo(){if(wn)return Kr;wn=1,Kr=t;var e=Hi(),r=Object.create($t());r.inherits=Ut(),r.inherits(t,e);function t(a){if(!(this instanceof t))return new t(a);e.call(this,a)}return t.prototype._transform=function(a,n,i){i(null,a)},Kr}var kn;function Zi(){return kn||(kn=1,function(e,r){var t={},a=Pe;t.READABLE_STREAM==="disable"&&a?(e.exports=a,r=e.exports=a.Readable,r.Readable=a.Readable,r.Writable=a.Writable,r.Duplex=a.Duplex,r.Transform=a.Transform,r.PassThrough=a.PassThrough,r.Stream=a):(r=e.exports=Wi(),r.Stream=a||r,r.Readable=r,r.Writable=ji(),r.Duplex=pt(),r.Transform=Hi(),r.PassThrough=Bo())}(Gt,Gt.exports)),Gt.exports}var Sn,tr;te.base64=!0;te.array=!0;te.string=!0;te.arraybuffer=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u";te.nodebuffer=typeof Buffer<"u";te.uint8array=typeof Uint8Array<"u";if(typeof ArrayBuffer>"u")tr=te.blob=!1;else{var xn=new ArrayBuffer(0);try{tr=te.blob=new Blob([xn],{type:"application/zip"}).size===0}catch{try{var Oo=self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder,En=new Oo;En.append(xn),tr=te.blob=En.getBlob("application/zip").size===0}catch{tr=te.blob=!1}}}try{Sn=te.nodestream=!!Zi().Readable}catch{Sn=te.nodestream=!1}var rr={},Cn;function qi(){if(Cn)return rr;Cn=1;var e=Y(),r=te,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return rr.encode=function(a){for(var n=[],i,s,o,f,l,u,y,c=0,p=a.length,b=p,g=e.getTypeOf(a)!=="string";c<a.length;)b=p-c,g?(i=a[c++],s=c<p?a[c++]:0,o=c<p?a[c++]:0):(i=a.charCodeAt(c++),s=c<p?a.charCodeAt(c++):0,o=c<p?a.charCodeAt(c++):0),f=i>>2,l=(i&3)<<4|s>>4,u=b>1?(s&15)<<2|o>>6:64,y=b>2?o&63:64,n.push(t.charAt(f)+t.charAt(l)+t.charAt(u)+t.charAt(y));return n.join("")},rr.decode=function(a){var n,i,s,o,f,l,u,y=0,c=0,p="data:";if(a.substr(0,p.length)===p)throw new Error("Invalid base64 input, it looks like a data url.");a=a.replace(/[^A-Za-z0-9+/=]/g,"");var b=a.length*3/4;if(a.charAt(a.length-1)===t.charAt(64)&&b--,a.charAt(a.length-2)===t.charAt(64)&&b--,b%1!==0)throw new Error("Invalid base64 input, bad content length.");var g;for(r.uint8array?g=new Uint8Array(b|0):g=new Array(b|0);y<a.length;)o=t.indexOf(a.charAt(y++)),f=t.indexOf(a.charAt(y++)),l=t.indexOf(a.charAt(y++)),u=t.indexOf(a.charAt(y++)),n=o<<2|f>>4,i=(f&15)<<4|l>>2,s=(l&3)<<6|u,g[c++]=n,l!==64&&(g[c++]=i),u!==64&&(g[c++]=s);return g},rr}var Er={isNode:typeof Buffer<"u",newBufferFrom:function(e,r){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(e,r);if(typeof e=="number")throw new Error('The "data" argument must not be a number');return new Buffer(e,r)},allocBuffer:function(e){if(Buffer.alloc)return Buffer.alloc(e);var r=new Buffer(e);return r.fill(0),r},isBuffer:function(e){return Buffer.isBuffer(e)},isStream:function(e){return e&&typeof e.on=="function"&&typeof e.pause=="function"&&typeof e.resume=="function"}},Jr,An;function Do(){if(An)return Jr;An=1;var e=ne.MutationObserver||ne.WebKitMutationObserver,r;if(process.browser)if(e){var t=0,a=new e(f),n=ne.document.createTextNode("");a.observe(n,{characterData:!0}),r=function(){n.data=t=++t%2}}else if(!ne.setImmediate&&typeof ne.MessageChannel<"u"){var i=new ne.MessageChannel;i.port1.onmessage=f,r=function(){i.port2.postMessage(0)}}else"document"in ne&&"onreadystatechange"in ne.document.createElement("script")?r=function(){var u=ne.document.createElement("script");u.onreadystatechange=function(){f(),u.onreadystatechange=null,u.parentNode.removeChild(u),u=null},ne.document.documentElement.appendChild(u)}:r=function(){setTimeout(f,0)};else r=function(){process.nextTick(f)};var s,o=[];function f(){s=!0;for(var u,y,c=o.length;c;){for(y=o,o=[],u=-1;++u<c;)y[u]();c=o.length}s=!1}Jr=l;function l(u){o.push(u)===1&&!s&&r()}return Jr}var Qr,Pn;function Fo(){if(Pn)return Qr;Pn=1;var e=Do();function r(){}var t={},a=["REJECTED"],n=["FULFILLED"],i=["PENDING"];if(!process.browser)var s=["UNHANDLED"];Qr=o;function o(h){if(typeof h!="function")throw new TypeError("resolver must be a function");this.state=i,this.queue=[],this.outcome=void 0,process.browser||(this.handled=s),h!==r&&y(this,h)}o.prototype.finally=function(h){if(typeof h!="function")return this;var v=this.constructor;return this.then(w,E);function w(A){function M(){return A}return v.resolve(h()).then(M)}function E(A){function M(){throw A}return v.resolve(h()).then(M)}},o.prototype.catch=function(h){return this.then(null,h)},o.prototype.then=function(h,v){if(typeof h!="function"&&this.state===n||typeof v!="function"&&this.state===a)return this;var w=new this.constructor(r);if(process.browser||this.handled===s&&(this.handled=null),this.state!==i){var E=this.state===n?h:v;l(w,E,this.outcome)}else this.queue.push(new f(w,h,v));return w};function f(h,v,w){this.promise=h,typeof v=="function"&&(this.onFulfilled=v,this.callFulfilled=this.otherCallFulfilled),typeof w=="function"&&(this.onRejected=w,this.callRejected=this.otherCallRejected)}f.prototype.callFulfilled=function(h){t.resolve(this.promise,h)},f.prototype.otherCallFulfilled=function(h){l(this.promise,this.onFulfilled,h)},f.prototype.callRejected=function(h){t.reject(this.promise,h)},f.prototype.otherCallRejected=function(h){l(this.promise,this.onRejected,h)};function l(h,v,w){e(function(){var E;try{E=v(w)}catch(A){return t.reject(h,A)}E===h?t.reject(h,new TypeError("Cannot resolve promise with itself")):t.resolve(h,E)})}t.resolve=function(h,v){var w=c(u,v);if(w.status==="error")return t.reject(h,w.value);var E=w.value;if(E)y(h,E);else{h.state=n,h.outcome=v;for(var A=-1,M=h.queue.length;++A<M;)h.queue[A].callFulfilled(v)}return h},t.reject=function(h,v){h.state=a,h.outcome=v,process.browser||h.handled===s&&e(function(){h.handled===s&&process.emit("unhandledRejection",v,h)});for(var w=-1,E=h.queue.length;++w<E;)h.queue[w].callRejected(v);return h};function u(h){var v=h&&h.then;if(h&&(typeof h=="object"||typeof h=="function")&&typeof v=="function")return function(){v.apply(h,arguments)}}function y(h,v){var w=!1;function E(O){w||(w=!0,t.reject(h,O))}function A(O){w||(w=!0,t.resolve(h,O))}function M(){v(A,E)}var B=c(M);B.status==="error"&&E(B.value)}function c(h,v){var w={};try{w.value=h(v),w.status="success"}catch(E){w.status="error",w.value=E}return w}o.resolve=p;function p(h){return h instanceof this?h:t.resolve(new this(r),h)}o.reject=b;function b(h){var v=new this(r);return t.reject(v,h)}o.all=g;function g(h){var v=this;if(Object.prototype.toString.call(h)!=="[object Array]")return this.reject(new TypeError("must be an array"));var w=h.length,E=!1;if(!w)return this.resolve([]);for(var A=new Array(w),M=0,B=-1,O=new this(r);++B<w;)D(h[B],B);return O;function D(U,R){v.resolve(U).then(ee,function(se){E||(E=!0,t.reject(O,se))});function ee(se){A[R]=se,++M===w&&!E&&(E=!0,t.resolve(O,A))}}}o.race=C;function C(h){var v=this;if(Object.prototype.toString.call(h)!=="[object Array]")return this.reject(new TypeError("must be an array"));var w=h.length,E=!1;if(!w)return this.resolve([]);for(var A=-1,M=new this(r);++A<w;)B(h[A]);return M;function B(O){v.resolve(O).then(function(D){E||(E=!0,t.resolve(M,D))},function(D){E||(E=!0,t.reject(M,D))})}}return Qr}var Sa=null;typeof Promise<"u"?Sa=Promise:Sa=Fo();var jt={Promise:Sa};(function(e,r){if(e.setImmediate)return;var t=1,a={},n=!1,i=e.document,s;function o(v){typeof v!="function"&&(v=new Function(""+v));for(var w=new Array(arguments.length-1),E=0;E<w.length;E++)w[E]=arguments[E+1];var A={callback:v,args:w};return a[t]=A,s(t),t++}function f(v){delete a[v]}function l(v){var w=v.callback,E=v.args;switch(E.length){case 0:w();break;case 1:w(E[0]);break;case 2:w(E[0],E[1]);break;case 3:w(E[0],E[1],E[2]);break;default:w.apply(r,E);break}}function u(v){if(n)setTimeout(u,0,v);else{var w=a[v];if(w){n=!0;try{l(w)}finally{f(v),n=!1}}}}function y(){s=function(v){process.nextTick(function(){u(v)})}}function c(){if(e.postMessage&&!e.importScripts){var v=!0,w=e.onmessage;return e.onmessage=function(){v=!1},e.postMessage("","*"),e.onmessage=w,v}}function p(){var v="setImmediate$"+Math.random()+"$",w=function(E){E.source===e&&typeof E.data=="string"&&E.data.indexOf(v)===0&&u(+E.data.slice(v.length))};e.addEventListener?e.addEventListener("message",w,!1):e.attachEvent("onmessage",w),s=function(E){e.postMessage(v+E,"*")}}function b(){var v=new MessageChannel;v.port1.onmessage=function(w){var E=w.data;u(E)},s=function(w){v.port2.postMessage(w)}}function g(){var v=i.documentElement;s=function(w){var E=i.createElement("script");E.onreadystatechange=function(){u(w),E.onreadystatechange=null,v.removeChild(E),E=null},v.appendChild(E)}}function C(){s=function(v){setTimeout(u,0,v)}}var h=Object.getPrototypeOf&&Object.getPrototypeOf(e);h=h&&h.setTimeout?h:e,{}.toString.call(e.process)==="[object process]"?y():c()?p():e.MessageChannel?b():i&&"onreadystatechange"in i.createElement("script")?g():C(),h.setImmediate=o,h.clearImmediate=f})(typeof self>"u"?typeof ne>"u"?ne:ne:self);var Nn;function Y(){return Nn||(Nn=1,function(e){var r=te,t=qi(),a=Er,n=jt;function i(c){var p=null;return r.uint8array?p=new Uint8Array(c.length):p=new Array(c.length),o(c,p)}e.newBlob=function(c,p){e.checkSupport("blob");try{return new Blob([c],{type:p})}catch{try{var b=self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder,g=new b;return g.append(c),g.getBlob(p)}catch{throw new Error("Bug : can't construct the Blob.")}}};function s(c){return c}function o(c,p){for(var b=0;b<c.length;++b)p[b]=c.charCodeAt(b)&255;return p}var f={stringifyByChunk:function(c,p,b){var g=[],C=0,h=c.length;if(h<=b)return String.fromCharCode.apply(null,c);for(;C<h;)p==="array"||p==="nodebuffer"?g.push(String.fromCharCode.apply(null,c.slice(C,Math.min(C+b,h)))):g.push(String.fromCharCode.apply(null,c.subarray(C,Math.min(C+b,h)))),C+=b;return g.join("")},stringifyByChar:function(c){for(var p="",b=0;b<c.length;b++)p+=String.fromCharCode(c[b]);return p},applyCanBeUsed:{uint8array:function(){try{return r.uint8array&&String.fromCharCode.apply(null,new Uint8Array(1)).length===1}catch{return!1}}(),nodebuffer:function(){try{return r.nodebuffer&&String.fromCharCode.apply(null,a.allocBuffer(1)).length===1}catch{return!1}}()}};function l(c){var p=65536,b=e.getTypeOf(c),g=!0;if(b==="uint8array"?g=f.applyCanBeUsed.uint8array:b==="nodebuffer"&&(g=f.applyCanBeUsed.nodebuffer),g)for(;p>1;)try{return f.stringifyByChunk(c,b,p)}catch{p=Math.floor(p/2)}return f.stringifyByChar(c)}e.applyFromCharCode=l;function u(c,p){for(var b=0;b<c.length;b++)p[b]=c[b];return p}var y={};y.string={string:s,array:function(c){return o(c,new Array(c.length))},arraybuffer:function(c){return y.string.uint8array(c).buffer},uint8array:function(c){return o(c,new Uint8Array(c.length))},nodebuffer:function(c){return o(c,a.allocBuffer(c.length))}},y.array={string:l,array:s,arraybuffer:function(c){return new Uint8Array(c).buffer},uint8array:function(c){return new Uint8Array(c)},nodebuffer:function(c){return a.newBufferFrom(c)}},y.arraybuffer={string:function(c){return l(new Uint8Array(c))},array:function(c){return u(new Uint8Array(c),new Array(c.byteLength))},arraybuffer:s,uint8array:function(c){return new Uint8Array(c)},nodebuffer:function(c){return a.newBufferFrom(new Uint8Array(c))}},y.uint8array={string:l,array:function(c){return u(c,new Array(c.length))},arraybuffer:function(c){return c.buffer},uint8array:s,nodebuffer:function(c){return a.newBufferFrom(c)}},y.nodebuffer={string:l,array:function(c){return u(c,new Array(c.length))},arraybuffer:function(c){return y.nodebuffer.uint8array(c).buffer},uint8array:function(c){return u(c,new Uint8Array(c.length))},nodebuffer:s},e.transformTo=function(c,p){if(p||(p=""),!c)return p;e.checkSupport(c);var b=e.getTypeOf(p),g=y[b][c](p);return g},e.resolve=function(c){for(var p=c.split("/"),b=[],g=0;g<p.length;g++){var C=p[g];C==="."||C===""&&g!==0&&g!==p.length-1||(C===".."?b.pop():b.push(C))}return b.join("/")},e.getTypeOf=function(c){if(typeof c=="string")return"string";if(Object.prototype.toString.call(c)==="[object Array]")return"array";if(r.nodebuffer&&a.isBuffer(c))return"nodebuffer";if(r.uint8array&&c instanceof Uint8Array)return"uint8array";if(r.arraybuffer&&c instanceof ArrayBuffer)return"arraybuffer"},e.checkSupport=function(c){var p=r[c.toLowerCase()];if(!p)throw new Error(c+" is not supported by this platform")},e.MAX_VALUE_16BITS=65535,e.MAX_VALUE_32BITS=-1,e.pretty=function(c){var p="",b,g;for(g=0;g<(c||"").length;g++)b=c.charCodeAt(g),p+="\\x"+(b<16?"0":"")+b.toString(16).toUpperCase();return p},e.delay=function(c,p,b){setImmediate(function(){c.apply(b||null,p||[])})},e.inherits=function(c,p){var b=function(){};b.prototype=p.prototype,c.prototype=new b},e.extend=function(){var c={},p,b;for(p=0;p<arguments.length;p++)for(b in arguments[p])Object.prototype.hasOwnProperty.call(arguments[p],b)&&typeof c[b]>"u"&&(c[b]=arguments[p][b]);return c},e.prepareContent=function(c,p,b,g,C){var h=n.Promise.resolve(p).then(function(v){var w=r.blob&&(v instanceof Blob||["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(v))!==-1);return w&&typeof FileReader<"u"?new n.Promise(function(E,A){var M=new FileReader;M.onload=function(B){E(B.target.result)},M.onerror=function(B){A(B.target.error)},M.readAsArrayBuffer(v)}):v});return h.then(function(v){var w=e.getTypeOf(v);return w?(w==="arraybuffer"?v=e.transformTo("uint8array",v):w==="string"&&(C?v=t.decode(v):b&&g!==!0&&(v=i(v))),v):n.Promise.reject(new Error("Can't read the data of '"+c+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))})}}($r)),$r}function Vi(e){this.name=e||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}Vi.prototype={push:function(e){this.emit("data",e)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(e){this.emit("error",e)}return!0},error:function(e){return this.isFinished?!1:(this.isPaused?this.generatedError=e:(this.isFinished=!0,this.emit("error",e),this.previous&&this.previous.error(e),this.cleanUp()),!0)},on:function(e,r){return this._listeners[e].push(r),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(e,r){if(this._listeners[e])for(var t=0;t<this._listeners[e].length;t++)this._listeners[e][t].call(this,r)},pipe:function(e){return e.registerPrevious(this)},registerPrevious:function(e){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=e.streamInfo,this.mergeStreamInfo(),this.previous=e;var r=this;return e.on("data",function(t){r.processChunk(t)}),e.on("end",function(){r.end()}),e.on("error",function(t){r.error(t)}),this},pause:function(){return this.isPaused||this.isFinished?!1:(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;this.isPaused=!1;var e=!1;return this.generatedError&&(this.error(this.generatedError),e=!0),this.previous&&this.previous.resume(),!e},flush:function(){},processChunk:function(e){this.push(e)},withStreamInfo:function(e,r){return this.extraStreamInfo[e]=r,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var e in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,e)&&(this.streamInfo[e]=this.extraStreamInfo[e])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var e="Worker "+this.name;return this.previous?this.previous+" -> "+e:e}};var ve=Vi;(function(e){for(var r=Y(),t=te,a=Er,n=ve,i=new Array(256),s=0;s<256;s++)i[s]=s>=252?6:s>=248?5:s>=240?4:s>=224?3:s>=192?2:1;i[254]=i[254]=1;var o=function(c){var p,b,g,C,h,v=c.length,w=0;for(C=0;C<v;C++)b=c.charCodeAt(C),(b&64512)===55296&&C+1<v&&(g=c.charCodeAt(C+1),(g&64512)===56320&&(b=65536+(b-55296<<10)+(g-56320),C++)),w+=b<128?1:b<2048?2:b<65536?3:4;for(t.uint8array?p=new Uint8Array(w):p=new Array(w),h=0,C=0;h<w;C++)b=c.charCodeAt(C),(b&64512)===55296&&C+1<v&&(g=c.charCodeAt(C+1),(g&64512)===56320&&(b=65536+(b-55296<<10)+(g-56320),C++)),b<128?p[h++]=b:b<2048?(p[h++]=192|b>>>6,p[h++]=128|b&63):b<65536?(p[h++]=224|b>>>12,p[h++]=128|b>>>6&63,p[h++]=128|b&63):(p[h++]=240|b>>>18,p[h++]=128|b>>>12&63,p[h++]=128|b>>>6&63,p[h++]=128|b&63);return p},f=function(c,p){var b;for(p=p||c.length,p>c.length&&(p=c.length),b=p-1;b>=0&&(c[b]&192)===128;)b--;return b<0||b===0?p:b+i[c[b]]>p?b:p},l=function(c){var p,b,g,C,h=c.length,v=new Array(h*2);for(b=0,p=0;p<h;){if(g=c[p++],g<128){v[b++]=g;continue}if(C=i[g],C>4){v[b++]=65533,p+=C-1;continue}for(g&=C===2?31:C===3?15:7;C>1&&p<h;)g=g<<6|c[p++]&63,C--;if(C>1){v[b++]=65533;continue}g<65536?v[b++]=g:(g-=65536,v[b++]=55296|g>>10&1023,v[b++]=56320|g&1023)}return v.length!==b&&(v.subarray?v=v.subarray(0,b):v.length=b),r.applyFromCharCode(v)};e.utf8encode=function(p){return t.nodebuffer?a.newBufferFrom(p,"utf-8"):o(p)},e.utf8decode=function(p){return t.nodebuffer?r.transformTo("nodebuffer",p).toString("utf-8"):(p=r.transformTo(t.uint8array?"uint8array":"array",p),l(p))};function u(){n.call(this,"utf-8 decode"),this.leftOver=null}r.inherits(u,n),u.prototype.processChunk=function(c){var p=r.transformTo(t.uint8array?"uint8array":"array",c.data);if(this.leftOver&&this.leftOver.length){if(t.uint8array){var b=p;p=new Uint8Array(b.length+this.leftOver.length),p.set(this.leftOver,0),p.set(b,this.leftOver.length)}else p=this.leftOver.concat(p);this.leftOver=null}var g=f(p),C=p;g!==p.length&&(t.uint8array?(C=p.subarray(0,g),this.leftOver=p.subarray(g,p.length)):(C=p.slice(0,g),this.leftOver=p.slice(g,p.length))),this.push({data:e.utf8decode(C),meta:c.meta})},u.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:e.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},e.Utf8DecodeWorker=u;function y(){n.call(this,"utf-8 encode")}r.inherits(y,n),y.prototype.processChunk=function(c){this.push({data:e.utf8encode(c.data),meta:c.meta})},e.Utf8EncodeWorker=y})(mt);var Xi=ve,Gi=Y();function Da(e){Xi.call(this,"ConvertWorker to "+e),this.destType=e}Gi.inherits(Da,Xi);Da.prototype.processChunk=function(e){this.push({data:Gi.transformTo(this.destType,e.data),meta:e.meta})};var Io=Da,ea,Tn;function Lo(){if(Tn)return ea;Tn=1;var e=Zi().Readable,r=Y();r.inherits(t,e);function t(a,n,i){e.call(this,n),this._helper=a;var s=this;a.on("data",function(o,f){s.push(o)||s._helper.pause(),i&&i(f)}).on("error",function(o){s.emit("error",o)}).on("end",function(){s.push(null)})}return t.prototype._read=function(){this._helper.resume()},ea=t,ea}var Xe=Y(),zo=Io,$o=ve,Uo=qi(),jo=te,Wo=jt,Yi=null;if(jo.nodestream)try{Yi=Lo()}catch{}function Ho(e,r,t){switch(e){case"blob":return Xe.newBlob(Xe.transformTo("arraybuffer",r),t);case"base64":return Uo.encode(r);default:return Xe.transformTo(e,r)}}function Zo(e,r){var t,a=0,n=null,i=0;for(t=0;t<r.length;t++)i+=r[t].length;switch(e){case"string":return r.join("");case"array":return Array.prototype.concat.apply([],r);case"uint8array":for(n=new Uint8Array(i),t=0;t<r.length;t++)n.set(r[t],a),a+=r[t].length;return n;case"nodebuffer":return Buffer.concat(r);default:throw new Error("concat : unsupported type '"+e+"'")}}function qo(e,r){return new Wo.Promise(function(t,a){var n=[],i=e._internalType,s=e._outputType,o=e._mimeType;e.on("data",function(f,l){n.push(f),r&&r(l)}).on("error",function(f){n=[],a(f)}).on("end",function(){try{var f=Ho(s,Zo(i,n),o);t(f)}catch(l){a(l)}n=[]}).resume()})}function Ki(e,r,t){var a=r;switch(r){case"blob":case"arraybuffer":a="uint8array";break;case"base64":a="string";break}try{this._internalType=a,this._outputType=r,this._mimeType=t,Xe.checkSupport(a),this._worker=e.pipe(new zo(a)),e.lock()}catch(n){this._worker=new $o("error"),this._worker.error(n)}}Ki.prototype={accumulate:function(e){return qo(this,e)},on:function(e,r){var t=this;return e==="data"?this._worker.on(e,function(a){r.call(t,a.data,a.meta)}):this._worker.on(e,function(){Xe.delay(r,arguments,t)}),this},resume:function(){return Xe.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(e){if(Xe.checkSupport("nodestream"),this._outputType!=="nodebuffer")throw new Error(this._outputType+" is not supported by this method");return new Yi(this,{objectMode:this._outputType!=="nodebuffer"},e)}};var Ji=Ki,ge={};ge.base64=!1;ge.binary=!1;ge.dir=!1;ge.createFolders=!0;ge.date=null;ge.compression=null;ge.compressionOptions=null;ge.comment=null;ge.unixPermissions=null;ge.dosPermissions=null;var Cr=Y(),Ar=ve,Vo=16*1024;function vt(e){Ar.call(this,"DataWorker");var r=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,e.then(function(t){r.dataIsReady=!0,r.data=t,r.max=t&&t.length||0,r.type=Cr.getTypeOf(t),r.isPaused||r._tickAndRepeat()},function(t){r.error(t)})}Cr.inherits(vt,Ar);vt.prototype.cleanUp=function(){Ar.prototype.cleanUp.call(this),this.data=null};vt.prototype.resume=function(){return Ar.prototype.resume.call(this)?(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,Cr.delay(this._tickAndRepeat,[],this)),!0):!1};vt.prototype._tickAndRepeat=function(){this._tickScheduled=!1,!(this.isPaused||this.isFinished)&&(this._tick(),this.isFinished||(Cr.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))};vt.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var e=Vo,r=null,t=Math.min(this.max,this.index+e);if(this.index>=this.max)return this.end();switch(this.type){case"string":r=this.data.substring(this.index,t);break;case"uint8array":r=this.data.subarray(this.index,t);break;case"array":case"nodebuffer":r=this.data.slice(this.index,t);break}return this.index=t,this.push({data:r,meta:{percent:this.max?this.index/this.max*100:0}})};var Qi=vt,Xo=Y();function Go(){for(var e,r=[],t=0;t<256;t++){e=t;for(var a=0;a<8;a++)e=e&1?3988292384^e>>>1:e>>>1;r[t]=e}return r}var es=Go();function Yo(e,r,t,a){var n=es,i=a+t;e=e^-1;for(var s=a;s<i;s++)e=e>>>8^n[(e^r[s])&255];return e^-1}function Ko(e,r,t,a){var n=es,i=a+t;e=e^-1;for(var s=a;s<i;s++)e=e>>>8^n[(e^r.charCodeAt(s))&255];return e^-1}var Fa=function(r,t){if(typeof r>"u"||!r.length)return 0;var a=Xo.getTypeOf(r)!=="string";return a?Yo(t|0,r,r.length,0):Ko(t|0,r,r.length,0)},ts=ve,Jo=Fa,Qo=Y();function Ia(){ts.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}Qo.inherits(Ia,ts);Ia.prototype.processChunk=function(e){this.streamInfo.crc32=Jo(e.data,this.streamInfo.crc32||0),this.push(e)};var rs=Ia,el=Y(),La=ve;function za(e){La.call(this,"DataLengthProbe for "+e),this.propName=e,this.withStreamInfo(e,0)}el.inherits(za,La);za.prototype.processChunk=function(e){if(e){var r=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=r+e.data.length}La.prototype.processChunk.call(this,e)};var tl=za,Rn=jt,Mn=Qi,rl=rs,xa=tl;function $a(e,r,t,a,n){this.compressedSize=e,this.uncompressedSize=r,this.crc32=t,this.compression=a,this.compressedContent=n}$a.prototype={getContentWorker:function(){var e=new Mn(Rn.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new xa("data_length")),r=this;return e.on("end",function(){if(this.streamInfo.data_length!==r.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")}),e},getCompressedWorker:function(){return new Mn(Rn.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}};$a.createWorkerFrom=function(e,r,t){return e.pipe(new rl).pipe(new xa("uncompressedSize")).pipe(r.compressWorker(t)).pipe(new xa("compressedSize")).withStreamInfo("compression",r)};var Ua=$a,al=Ji,nl=Qi,ta=mt,ra=Ua,Bn=ve,ja=function(e,r,t){this.name=e,this.dir=t.dir,this.date=t.date,this.comment=t.comment,this.unixPermissions=t.unixPermissions,this.dosPermissions=t.dosPermissions,this._data=r,this._dataBinary=t.binary,this.options={compression:t.compression,compressionOptions:t.compressionOptions}};ja.prototype={internalStream:function(e){var r=null,t="string";try{if(!e)throw new Error("No output type specified.");t=e.toLowerCase();var a=t==="string"||t==="text";(t==="binarystring"||t==="text")&&(t="string"),r=this._decompressWorker();var n=!this._dataBinary;n&&!a&&(r=r.pipe(new ta.Utf8EncodeWorker)),!n&&a&&(r=r.pipe(new ta.Utf8DecodeWorker))}catch(i){r=new Bn("error"),r.error(i)}return new al(r,t,"")},async:function(e,r){return this.internalStream(e).accumulate(r)},nodeStream:function(e,r){return this.internalStream(e||"nodebuffer").toNodejsStream(r)},_compressWorker:function(e,r){if(this._data instanceof ra&&this._data.compression.magic===e.magic)return this._data.getCompressedWorker();var t=this._decompressWorker();return this._dataBinary||(t=t.pipe(new ta.Utf8EncodeWorker)),ra.createWorkerFrom(t,e,r)},_decompressWorker:function(){return this._data instanceof ra?this._data.getContentWorker():this._data instanceof Bn?this._data:new nl(this._data)}};var On=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],il=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")};for(var aa=0;aa<On.length;aa++)ja.prototype[On[aa]]=il;var sl=ja,as={},Pr={},Nr={},Be={};(function(e){var r=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Int32Array<"u";function t(i,s){return Object.prototype.hasOwnProperty.call(i,s)}e.assign=function(i){for(var s=Array.prototype.slice.call(arguments,1);s.length;){var o=s.shift();if(o){if(typeof o!="object")throw new TypeError(o+"must be non-object");for(var f in o)t(o,f)&&(i[f]=o[f])}}return i},e.shrinkBuf=function(i,s){return i.length===s?i:i.subarray?i.subarray(0,s):(i.length=s,i)};var a={arraySet:function(i,s,o,f,l){if(s.subarray&&i.subarray){i.set(s.subarray(o,o+f),l);return}for(var u=0;u<f;u++)i[l+u]=s[o+u]},flattenChunks:function(i){var s,o,f,l,u,y;for(f=0,s=0,o=i.length;s<o;s++)f+=i[s].length;for(y=new Uint8Array(f),l=0,s=0,o=i.length;s<o;s++)u=i[s],y.set(u,l),l+=u.length;return y}},n={arraySet:function(i,s,o,f,l){for(var u=0;u<f;u++)i[l+u]=s[o+u]},flattenChunks:function(i){return[].concat.apply([],i)}};e.setTyped=function(i){i?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,a)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,n))},e.setTyped(r)})(Be);var Wt={},Ne={},gt={},ol=Be,ll=4,Dn=0,Fn=1,fl=2;function bt(e){for(var r=e.length;--r>=0;)e[r]=0}var ul=0,ns=1,hl=2,cl=3,dl=258,Wa=29,Ht=256,Dt=Ht+1+Wa,ht=30,Ha=19,is=2*Dt+1,qe=15,na=16,pl=7,Za=256,ss=16,os=17,ls=18,Ea=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],cr=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],ml=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],fs=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],vl=512,Me=new Array((Dt+2)*2);bt(Me);var Nt=new Array(ht*2);bt(Nt);var Ft=new Array(vl);bt(Ft);var It=new Array(dl-cl+1);bt(It);var qa=new Array(Wa);bt(qa);var yr=new Array(ht);bt(yr);function ia(e,r,t,a,n){this.static_tree=e,this.extra_bits=r,this.extra_base=t,this.elems=a,this.max_length=n,this.has_stree=e&&e.length}var us,hs,cs;function sa(e,r){this.dyn_tree=e,this.max_code=0,this.stat_desc=r}function ds(e){return e<256?Ft[e]:Ft[256+(e>>>7)]}function Lt(e,r){e.pending_buf[e.pending++]=r&255,e.pending_buf[e.pending++]=r>>>8&255}function le(e,r,t){e.bi_valid>na-t?(e.bi_buf|=r<<e.bi_valid&65535,Lt(e,e.bi_buf),e.bi_buf=r>>na-e.bi_valid,e.bi_valid+=t-na):(e.bi_buf|=r<<e.bi_valid&65535,e.bi_valid+=t)}function Ce(e,r,t){le(e,t[r*2],t[r*2+1])}function ps(e,r){var t=0;do t|=e&1,e>>>=1,t<<=1;while(--r>0);return t>>>1}function gl(e){e.bi_valid===16?(Lt(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=e.bi_buf&255,e.bi_buf>>=8,e.bi_valid-=8)}function bl(e,r){var t=r.dyn_tree,a=r.max_code,n=r.stat_desc.static_tree,i=r.stat_desc.has_stree,s=r.stat_desc.extra_bits,o=r.stat_desc.extra_base,f=r.stat_desc.max_length,l,u,y,c,p,b,g=0;for(c=0;c<=qe;c++)e.bl_count[c]=0;for(t[e.heap[e.heap_max]*2+1]=0,l=e.heap_max+1;l<is;l++)u=e.heap[l],c=t[t[u*2+1]*2+1]+1,c>f&&(c=f,g++),t[u*2+1]=c,!(u>a)&&(e.bl_count[c]++,p=0,u>=o&&(p=s[u-o]),b=t[u*2],e.opt_len+=b*(c+p),i&&(e.static_len+=b*(n[u*2+1]+p)));if(g!==0){do{for(c=f-1;e.bl_count[c]===0;)c--;e.bl_count[c]--,e.bl_count[c+1]+=2,e.bl_count[f]--,g-=2}while(g>0);for(c=f;c!==0;c--)for(u=e.bl_count[c];u!==0;)y=e.heap[--l],!(y>a)&&(t[y*2+1]!==c&&(e.opt_len+=(c-t[y*2+1])*t[y*2],t[y*2+1]=c),u--)}}function ms(e,r,t){var a=new Array(qe+1),n=0,i,s;for(i=1;i<=qe;i++)a[i]=n=n+t[i-1]<<1;for(s=0;s<=r;s++){var o=e[s*2+1];o!==0&&(e[s*2]=ps(a[o]++,o))}}function _l(){var e,r,t,a,n,i=new Array(qe+1);for(t=0,a=0;a<Wa-1;a++)for(qa[a]=t,e=0;e<1<<Ea[a];e++)It[t++]=a;for(It[t-1]=a,n=0,a=0;a<16;a++)for(yr[a]=n,e=0;e<1<<cr[a];e++)Ft[n++]=a;for(n>>=7;a<ht;a++)for(yr[a]=n<<7,e=0;e<1<<cr[a]-7;e++)Ft[256+n++]=a;for(r=0;r<=qe;r++)i[r]=0;for(e=0;e<=143;)Me[e*2+1]=8,e++,i[8]++;for(;e<=255;)Me[e*2+1]=9,e++,i[9]++;for(;e<=279;)Me[e*2+1]=7,e++,i[7]++;for(;e<=287;)Me[e*2+1]=8,e++,i[8]++;for(ms(Me,Dt+1,i),e=0;e<ht;e++)Nt[e*2+1]=5,Nt[e*2]=ps(e,5);us=new ia(Me,Ea,Ht+1,Dt,qe),hs=new ia(Nt,cr,0,ht,qe),cs=new ia(new Array(0),ml,0,Ha,pl)}function vs(e){var r;for(r=0;r<Dt;r++)e.dyn_ltree[r*2]=0;for(r=0;r<ht;r++)e.dyn_dtree[r*2]=0;for(r=0;r<Ha;r++)e.bl_tree[r*2]=0;e.dyn_ltree[Za*2]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function gs(e){e.bi_valid>8?Lt(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function yl(e,r,t,a){gs(e),Lt(e,t),Lt(e,~t),ol.arraySet(e.pending_buf,e.window,r,t,e.pending),e.pending+=t}function In(e,r,t,a){var n=r*2,i=t*2;return e[n]<e[i]||e[n]===e[i]&&a[r]<=a[t]}function oa(e,r,t){for(var a=e.heap[t],n=t<<1;n<=e.heap_len&&(n<e.heap_len&&In(r,e.heap[n+1],e.heap[n],e.depth)&&n++,!In(r,a,e.heap[n],e.depth));)e.heap[t]=e.heap[n],t=n,n<<=1;e.heap[t]=a}function Ln(e,r,t){var a,n,i=0,s,o;if(e.last_lit!==0)do a=e.pending_buf[e.d_buf+i*2]<<8|e.pending_buf[e.d_buf+i*2+1],n=e.pending_buf[e.l_buf+i],i++,a===0?Ce(e,n,r):(s=It[n],Ce(e,s+Ht+1,r),o=Ea[s],o!==0&&(n-=qa[s],le(e,n,o)),a--,s=ds(a),Ce(e,s,t),o=cr[s],o!==0&&(a-=yr[s],le(e,a,o)));while(i<e.last_lit);Ce(e,Za,r)}function Ca(e,r){var t=r.dyn_tree,a=r.stat_desc.static_tree,n=r.stat_desc.has_stree,i=r.stat_desc.elems,s,o,f=-1,l;for(e.heap_len=0,e.heap_max=is,s=0;s<i;s++)t[s*2]!==0?(e.heap[++e.heap_len]=f=s,e.depth[s]=0):t[s*2+1]=0;for(;e.heap_len<2;)l=e.heap[++e.heap_len]=f<2?++f:0,t[l*2]=1,e.depth[l]=0,e.opt_len--,n&&(e.static_len-=a[l*2+1]);for(r.max_code=f,s=e.heap_len>>1;s>=1;s--)oa(e,t,s);l=i;do s=e.heap[1],e.heap[1]=e.heap[e.heap_len--],oa(e,t,1),o=e.heap[1],e.heap[--e.heap_max]=s,e.heap[--e.heap_max]=o,t[l*2]=t[s*2]+t[o*2],e.depth[l]=(e.depth[s]>=e.depth[o]?e.depth[s]:e.depth[o])+1,t[s*2+1]=t[o*2+1]=l,e.heap[1]=l++,oa(e,t,1);while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],bl(e,r),ms(t,f,e.bl_count)}function zn(e,r,t){var a,n=-1,i,s=r[0*2+1],o=0,f=7,l=4;for(s===0&&(f=138,l=3),r[(t+1)*2+1]=65535,a=0;a<=t;a++)i=s,s=r[(a+1)*2+1],!(++o<f&&i===s)&&(o<l?e.bl_tree[i*2]+=o:i!==0?(i!==n&&e.bl_tree[i*2]++,e.bl_tree[ss*2]++):o<=10?e.bl_tree[os*2]++:e.bl_tree[ls*2]++,o=0,n=i,s===0?(f=138,l=3):i===s?(f=6,l=3):(f=7,l=4))}function $n(e,r,t){var a,n=-1,i,s=r[0*2+1],o=0,f=7,l=4;for(s===0&&(f=138,l=3),a=0;a<=t;a++)if(i=s,s=r[(a+1)*2+1],!(++o<f&&i===s)){if(o<l)do Ce(e,i,e.bl_tree);while(--o!==0);else i!==0?(i!==n&&(Ce(e,i,e.bl_tree),o--),Ce(e,ss,e.bl_tree),le(e,o-3,2)):o<=10?(Ce(e,os,e.bl_tree),le(e,o-3,3)):(Ce(e,ls,e.bl_tree),le(e,o-11,7));o=0,n=i,s===0?(f=138,l=3):i===s?(f=6,l=3):(f=7,l=4)}}function wl(e){var r;for(zn(e,e.dyn_ltree,e.l_desc.max_code),zn(e,e.dyn_dtree,e.d_desc.max_code),Ca(e,e.bl_desc),r=Ha-1;r>=3&&e.bl_tree[fs[r]*2+1]===0;r--);return e.opt_len+=3*(r+1)+5+5+4,r}function kl(e,r,t,a){var n;for(le(e,r-257,5),le(e,t-1,5),le(e,a-4,4),n=0;n<a;n++)le(e,e.bl_tree[fs[n]*2+1],3);$n(e,e.dyn_ltree,r-1),$n(e,e.dyn_dtree,t-1)}function Sl(e){var r=4093624447,t;for(t=0;t<=31;t++,r>>>=1)if(r&1&&e.dyn_ltree[t*2]!==0)return Dn;if(e.dyn_ltree[9*2]!==0||e.dyn_ltree[10*2]!==0||e.dyn_ltree[13*2]!==0)return Fn;for(t=32;t<Ht;t++)if(e.dyn_ltree[t*2]!==0)return Fn;return Dn}var Un=!1;function xl(e){Un||(_l(),Un=!0),e.l_desc=new sa(e.dyn_ltree,us),e.d_desc=new sa(e.dyn_dtree,hs),e.bl_desc=new sa(e.bl_tree,cs),e.bi_buf=0,e.bi_valid=0,vs(e)}function bs(e,r,t,a){le(e,(ul<<1)+(a?1:0),3),yl(e,r,t)}function El(e){le(e,ns<<1,3),Ce(e,Za,Me),gl(e)}function Cl(e,r,t,a){var n,i,s=0;e.level>0?(e.strm.data_type===fl&&(e.strm.data_type=Sl(e)),Ca(e,e.l_desc),Ca(e,e.d_desc),s=wl(e),n=e.opt_len+3+7>>>3,i=e.static_len+3+7>>>3,i<=n&&(n=i)):n=i=t+5,t+4<=n&&r!==-1?bs(e,r,t,a):e.strategy===ll||i===n?(le(e,(ns<<1)+(a?1:0),3),Ln(e,Me,Nt)):(le(e,(hl<<1)+(a?1:0),3),kl(e,e.l_desc.max_code+1,e.d_desc.max_code+1,s+1),Ln(e,e.dyn_ltree,e.dyn_dtree)),vs(e),a&&gs(e)}function Al(e,r,t){return e.pending_buf[e.d_buf+e.last_lit*2]=r>>>8&255,e.pending_buf[e.d_buf+e.last_lit*2+1]=r&255,e.pending_buf[e.l_buf+e.last_lit]=t&255,e.last_lit++,r===0?e.dyn_ltree[t*2]++:(e.matches++,r--,e.dyn_ltree[(It[t]+Ht+1)*2]++,e.dyn_dtree[ds(r)*2]++),e.last_lit===e.lit_bufsize-1}gt._tr_init=xl;gt._tr_stored_block=bs;gt._tr_flush_block=Cl;gt._tr_tally=Al;gt._tr_align=El;function Pl(e,r,t,a){for(var n=e&65535|0,i=e>>>16&65535|0,s=0;t!==0;){s=t>2e3?2e3:t,t-=s;do n=n+r[a++]|0,i=i+n|0;while(--s);n%=65521,i%=65521}return n|i<<16|0}var _s=Pl;function Nl(){for(var e,r=[],t=0;t<256;t++){e=t;for(var a=0;a<8;a++)e=e&1?3988292384^e>>>1:e>>>1;r[t]=e}return r}var Tl=Nl();function Rl(e,r,t,a){var n=Tl,i=a+t;e^=-1;for(var s=a;s<i;s++)e=e>>>8^n[(e^r[s])&255];return e^-1}var ys=Rl,Va={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},oe=Be,ce=gt,ws=_s,Fe=ys,Ml=Va,et=0,Bl=1,Ol=3,je=4,jn=5,Ae=0,Wn=1,de=-2,Dl=-3,la=-5,Fl=-1,Il=1,ar=2,Ll=3,zl=4,$l=0,Ul=2,Tr=8,jl=9,Wl=15,Hl=8,Zl=29,ql=256,Aa=ql+1+Zl,Vl=30,Xl=19,Gl=2*Aa+1,Yl=15,$=3,$e=258,we=$e+$+1,Kl=32,Rr=42,Pa=69,dr=73,pr=91,mr=103,Ve=113,Pt=666,re=1,Zt=2,Ge=3,_t=4,Jl=3;function Ue(e,r){return e.msg=Ml[r],r}function Hn(e){return(e<<1)-(e>4?9:0)}function ze(e){for(var r=e.length;--r>=0;)e[r]=0}function Ie(e){var r=e.state,t=r.pending;t>e.avail_out&&(t=e.avail_out),t!==0&&(oe.arraySet(e.output,r.pending_buf,r.pending_out,t,e.next_out),e.next_out+=t,r.pending_out+=t,e.total_out+=t,e.avail_out-=t,r.pending-=t,r.pending===0&&(r.pending_out=0))}function ie(e,r){ce._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,r),e.block_start=e.strstart,Ie(e.strm)}function W(e,r){e.pending_buf[e.pending++]=r}function Et(e,r){e.pending_buf[e.pending++]=r>>>8&255,e.pending_buf[e.pending++]=r&255}function Ql(e,r,t,a){var n=e.avail_in;return n>a&&(n=a),n===0?0:(e.avail_in-=n,oe.arraySet(r,e.input,e.next_in,n,t),e.state.wrap===1?e.adler=ws(e.adler,r,n,t):e.state.wrap===2&&(e.adler=Fe(e.adler,r,n,t)),e.next_in+=n,e.total_in+=n,n)}function ks(e,r){var t=e.max_chain_length,a=e.strstart,n,i,s=e.prev_length,o=e.nice_match,f=e.strstart>e.w_size-we?e.strstart-(e.w_size-we):0,l=e.window,u=e.w_mask,y=e.prev,c=e.strstart+$e,p=l[a+s-1],b=l[a+s];e.prev_length>=e.good_match&&(t>>=2),o>e.lookahead&&(o=e.lookahead);do if(n=r,!(l[n+s]!==b||l[n+s-1]!==p||l[n]!==l[a]||l[++n]!==l[a+1])){a+=2,n++;do;while(l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&a<c);if(i=$e-(c-a),a=c-$e,i>s){if(e.match_start=r,s=i,i>=o)break;p=l[a+s-1],b=l[a+s]}}while((r=y[r&u])>f&&--t!==0);return s<=e.lookahead?s:e.lookahead}function Ye(e){var r=e.w_size,t,a,n,i,s;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=r+(r-we)){oe.arraySet(e.window,e.window,r,r,0),e.match_start-=r,e.strstart-=r,e.block_start-=r,a=e.hash_size,t=a;do n=e.head[--t],e.head[t]=n>=r?n-r:0;while(--a);a=r,t=a;do n=e.prev[--t],e.prev[t]=n>=r?n-r:0;while(--a);i+=r}if(e.strm.avail_in===0)break;if(a=Ql(e.strm,e.window,e.strstart+e.lookahead,i),e.lookahead+=a,e.lookahead+e.insert>=$)for(s=e.strstart-e.insert,e.ins_h=e.window[s],e.ins_h=(e.ins_h<<e.hash_shift^e.window[s+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[s+$-1])&e.hash_mask,e.prev[s&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=s,s++,e.insert--,!(e.lookahead+e.insert<$)););}while(e.lookahead<we&&e.strm.avail_in!==0)}function ef(e,r){var t=65535;for(t>e.pending_buf_size-5&&(t=e.pending_buf_size-5);;){if(e.lookahead<=1){if(Ye(e),e.lookahead===0&&r===et)return re;if(e.lookahead===0)break}e.strstart+=e.lookahead,e.lookahead=0;var a=e.block_start+t;if((e.strstart===0||e.strstart>=a)&&(e.lookahead=e.strstart-a,e.strstart=a,ie(e,!1),e.strm.avail_out===0)||e.strstart-e.block_start>=e.w_size-we&&(ie(e,!1),e.strm.avail_out===0))return re}return e.insert=0,r===je?(ie(e,!0),e.strm.avail_out===0?Ge:_t):(e.strstart>e.block_start&&(ie(e,!1),e.strm.avail_out===0),re)}function fa(e,r){for(var t,a;;){if(e.lookahead<we){if(Ye(e),e.lookahead<we&&r===et)return re;if(e.lookahead===0)break}if(t=0,e.lookahead>=$&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+$-1])&e.hash_mask,t=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),t!==0&&e.strstart-t<=e.w_size-we&&(e.match_length=ks(e,t)),e.match_length>=$)if(a=ce._tr_tally(e,e.strstart-e.match_start,e.match_length-$),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=$){e.match_length--;do e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+$-1])&e.hash_mask,t=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart;while(--e.match_length!==0);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else a=ce._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(a&&(ie(e,!1),e.strm.avail_out===0))return re}return e.insert=e.strstart<$-1?e.strstart:$-1,r===je?(ie(e,!0),e.strm.avail_out===0?Ge:_t):e.last_lit&&(ie(e,!1),e.strm.avail_out===0)?re:Zt}function ot(e,r){for(var t,a,n;;){if(e.lookahead<we){if(Ye(e),e.lookahead<we&&r===et)return re;if(e.lookahead===0)break}if(t=0,e.lookahead>=$&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+$-1])&e.hash_mask,t=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=$-1,t!==0&&e.prev_length<e.max_lazy_match&&e.strstart-t<=e.w_size-we&&(e.match_length=ks(e,t),e.match_length<=5&&(e.strategy===Il||e.match_length===$&&e.strstart-e.match_start>4096)&&(e.match_length=$-1)),e.prev_length>=$&&e.match_length<=e.prev_length){n=e.strstart+e.lookahead-$,a=ce._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-$),e.lookahead-=e.prev_length-1,e.prev_length-=2;do++e.strstart<=n&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+$-1])&e.hash_mask,t=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart);while(--e.prev_length!==0);if(e.match_available=0,e.match_length=$-1,e.strstart++,a&&(ie(e,!1),e.strm.avail_out===0))return re}else if(e.match_available){if(a=ce._tr_tally(e,0,e.window[e.strstart-1]),a&&ie(e,!1),e.strstart++,e.lookahead--,e.strm.avail_out===0)return re}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(a=ce._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<$-1?e.strstart:$-1,r===je?(ie(e,!0),e.strm.avail_out===0?Ge:_t):e.last_lit&&(ie(e,!1),e.strm.avail_out===0)?re:Zt}function tf(e,r){for(var t,a,n,i,s=e.window;;){if(e.lookahead<=$e){if(Ye(e),e.lookahead<=$e&&r===et)return re;if(e.lookahead===0)break}if(e.match_length=0,e.lookahead>=$&&e.strstart>0&&(n=e.strstart-1,a=s[n],a===s[++n]&&a===s[++n]&&a===s[++n])){i=e.strstart+$e;do;while(a===s[++n]&&a===s[++n]&&a===s[++n]&&a===s[++n]&&a===s[++n]&&a===s[++n]&&a===s[++n]&&a===s[++n]&&n<i);e.match_length=$e-(i-n),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=$?(t=ce._tr_tally(e,1,e.match_length-$),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(t=ce._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),t&&(ie(e,!1),e.strm.avail_out===0))return re}return e.insert=0,r===je?(ie(e,!0),e.strm.avail_out===0?Ge:_t):e.last_lit&&(ie(e,!1),e.strm.avail_out===0)?re:Zt}function rf(e,r){for(var t;;){if(e.lookahead===0&&(Ye(e),e.lookahead===0)){if(r===et)return re;break}if(e.match_length=0,t=ce._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,t&&(ie(e,!1),e.strm.avail_out===0))return re}return e.insert=0,r===je?(ie(e,!0),e.strm.avail_out===0?Ge:_t):e.last_lit&&(ie(e,!1),e.strm.avail_out===0)?re:Zt}function xe(e,r,t,a,n){this.good_length=e,this.max_lazy=r,this.nice_length=t,this.max_chain=a,this.func=n}var ft;ft=[new xe(0,0,0,0,ef),new xe(4,4,8,4,fa),new xe(4,5,16,8,fa),new xe(4,6,32,32,fa),new xe(4,4,16,16,ot),new xe(8,16,32,32,ot),new xe(8,16,128,128,ot),new xe(8,32,128,256,ot),new xe(32,128,258,1024,ot),new xe(32,258,258,4096,ot)];function af(e){e.window_size=2*e.w_size,ze(e.head),e.max_lazy_match=ft[e.level].max_lazy,e.good_match=ft[e.level].good_length,e.nice_match=ft[e.level].nice_length,e.max_chain_length=ft[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=$-1,e.match_available=0,e.ins_h=0}function nf(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=Tr,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new oe.Buf16(Gl*2),this.dyn_dtree=new oe.Buf16((2*Vl+1)*2),this.bl_tree=new oe.Buf16((2*Xl+1)*2),ze(this.dyn_ltree),ze(this.dyn_dtree),ze(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new oe.Buf16(Yl+1),this.heap=new oe.Buf16(2*Aa+1),ze(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new oe.Buf16(2*Aa+1),ze(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function Ss(e){var r;return!e||!e.state?Ue(e,de):(e.total_in=e.total_out=0,e.data_type=Ul,r=e.state,r.pending=0,r.pending_out=0,r.wrap<0&&(r.wrap=-r.wrap),r.status=r.wrap?Rr:Ve,e.adler=r.wrap===2?0:1,r.last_flush=et,ce._tr_init(r),Ae)}function xs(e){var r=Ss(e);return r===Ae&&af(e.state),r}function sf(e,r){return!e||!e.state||e.state.wrap!==2?de:(e.state.gzhead=r,Ae)}function Es(e,r,t,a,n,i){if(!e)return de;var s=1;if(r===Fl&&(r=6),a<0?(s=0,a=-a):a>15&&(s=2,a-=16),n<1||n>jl||t!==Tr||a<8||a>15||r<0||r>9||i<0||i>zl)return Ue(e,de);a===8&&(a=9);var o=new nf;return e.state=o,o.strm=e,o.wrap=s,o.gzhead=null,o.w_bits=a,o.w_size=1<<o.w_bits,o.w_mask=o.w_size-1,o.hash_bits=n+7,o.hash_size=1<<o.hash_bits,o.hash_mask=o.hash_size-1,o.hash_shift=~~((o.hash_bits+$-1)/$),o.window=new oe.Buf8(o.w_size*2),o.head=new oe.Buf16(o.hash_size),o.prev=new oe.Buf16(o.w_size),o.lit_bufsize=1<<n+6,o.pending_buf_size=o.lit_bufsize*4,o.pending_buf=new oe.Buf8(o.pending_buf_size),o.d_buf=1*o.lit_bufsize,o.l_buf=3*o.lit_bufsize,o.level=r,o.strategy=i,o.method=t,xs(e)}function of(e,r){return Es(e,r,Tr,Wl,Hl,$l)}function lf(e,r){var t,a,n,i;if(!e||!e.state||r>jn||r<0)return e?Ue(e,de):de;if(a=e.state,!e.output||!e.input&&e.avail_in!==0||a.status===Pt&&r!==je)return Ue(e,e.avail_out===0?la:de);if(a.strm=e,t=a.last_flush,a.last_flush=r,a.status===Rr)if(a.wrap===2)e.adler=0,W(a,31),W(a,139),W(a,8),a.gzhead?(W(a,(a.gzhead.text?1:0)+(a.gzhead.hcrc?2:0)+(a.gzhead.extra?4:0)+(a.gzhead.name?8:0)+(a.gzhead.comment?16:0)),W(a,a.gzhead.time&255),W(a,a.gzhead.time>>8&255),W(a,a.gzhead.time>>16&255),W(a,a.gzhead.time>>24&255),W(a,a.level===9?2:a.strategy>=ar||a.level<2?4:0),W(a,a.gzhead.os&255),a.gzhead.extra&&a.gzhead.extra.length&&(W(a,a.gzhead.extra.length&255),W(a,a.gzhead.extra.length>>8&255)),a.gzhead.hcrc&&(e.adler=Fe(e.adler,a.pending_buf,a.pending,0)),a.gzindex=0,a.status=Pa):(W(a,0),W(a,0),W(a,0),W(a,0),W(a,0),W(a,a.level===9?2:a.strategy>=ar||a.level<2?4:0),W(a,Jl),a.status=Ve);else{var s=Tr+(a.w_bits-8<<4)<<8,o=-1;a.strategy>=ar||a.level<2?o=0:a.level<6?o=1:a.level===6?o=2:o=3,s|=o<<6,a.strstart!==0&&(s|=Kl),s+=31-s%31,a.status=Ve,Et(a,s),a.strstart!==0&&(Et(a,e.adler>>>16),Et(a,e.adler&65535)),e.adler=1}if(a.status===Pa)if(a.gzhead.extra){for(n=a.pending;a.gzindex<(a.gzhead.extra.length&65535)&&!(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>n&&(e.adler=Fe(e.adler,a.pending_buf,a.pending-n,n)),Ie(e),n=a.pending,a.pending===a.pending_buf_size));)W(a,a.gzhead.extra[a.gzindex]&255),a.gzindex++;a.gzhead.hcrc&&a.pending>n&&(e.adler=Fe(e.adler,a.pending_buf,a.pending-n,n)),a.gzindex===a.gzhead.extra.length&&(a.gzindex=0,a.status=dr)}else a.status=dr;if(a.status===dr)if(a.gzhead.name){n=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>n&&(e.adler=Fe(e.adler,a.pending_buf,a.pending-n,n)),Ie(e),n=a.pending,a.pending===a.pending_buf_size)){i=1;break}a.gzindex<a.gzhead.name.length?i=a.gzhead.name.charCodeAt(a.gzindex++)&255:i=0,W(a,i)}while(i!==0);a.gzhead.hcrc&&a.pending>n&&(e.adler=Fe(e.adler,a.pending_buf,a.pending-n,n)),i===0&&(a.gzindex=0,a.status=pr)}else a.status=pr;if(a.status===pr)if(a.gzhead.comment){n=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>n&&(e.adler=Fe(e.adler,a.pending_buf,a.pending-n,n)),Ie(e),n=a.pending,a.pending===a.pending_buf_size)){i=1;break}a.gzindex<a.gzhead.comment.length?i=a.gzhead.comment.charCodeAt(a.gzindex++)&255:i=0,W(a,i)}while(i!==0);a.gzhead.hcrc&&a.pending>n&&(e.adler=Fe(e.adler,a.pending_buf,a.pending-n,n)),i===0&&(a.status=mr)}else a.status=mr;if(a.status===mr&&(a.gzhead.hcrc?(a.pending+2>a.pending_buf_size&&Ie(e),a.pending+2<=a.pending_buf_size&&(W(a,e.adler&255),W(a,e.adler>>8&255),e.adler=0,a.status=Ve)):a.status=Ve),a.pending!==0){if(Ie(e),e.avail_out===0)return a.last_flush=-1,Ae}else if(e.avail_in===0&&Hn(r)<=Hn(t)&&r!==je)return Ue(e,la);if(a.status===Pt&&e.avail_in!==0)return Ue(e,la);if(e.avail_in!==0||a.lookahead!==0||r!==et&&a.status!==Pt){var f=a.strategy===ar?rf(a,r):a.strategy===Ll?tf(a,r):ft[a.level].func(a,r);if((f===Ge||f===_t)&&(a.status=Pt),f===re||f===Ge)return e.avail_out===0&&(a.last_flush=-1),Ae;if(f===Zt&&(r===Bl?ce._tr_align(a):r!==jn&&(ce._tr_stored_block(a,0,0,!1),r===Ol&&(ze(a.head),a.lookahead===0&&(a.strstart=0,a.block_start=0,a.insert=0))),Ie(e),e.avail_out===0))return a.last_flush=-1,Ae}return r!==je?Ae:a.wrap<=0?Wn:(a.wrap===2?(W(a,e.adler&255),W(a,e.adler>>8&255),W(a,e.adler>>16&255),W(a,e.adler>>24&255),W(a,e.total_in&255),W(a,e.total_in>>8&255),W(a,e.total_in>>16&255),W(a,e.total_in>>24&255)):(Et(a,e.adler>>>16),Et(a,e.adler&65535)),Ie(e),a.wrap>0&&(a.wrap=-a.wrap),a.pending!==0?Ae:Wn)}function ff(e){var r;return!e||!e.state?de:(r=e.state.status,r!==Rr&&r!==Pa&&r!==dr&&r!==pr&&r!==mr&&r!==Ve&&r!==Pt?Ue(e,de):(e.state=null,r===Ve?Ue(e,Dl):Ae))}function uf(e,r){var t=r.length,a,n,i,s,o,f,l,u;if(!e||!e.state||(a=e.state,s=a.wrap,s===2||s===1&&a.status!==Rr||a.lookahead))return de;for(s===1&&(e.adler=ws(e.adler,r,t,0)),a.wrap=0,t>=a.w_size&&(s===0&&(ze(a.head),a.strstart=0,a.block_start=0,a.insert=0),u=new oe.Buf8(a.w_size),oe.arraySet(u,r,t-a.w_size,a.w_size,0),r=u,t=a.w_size),o=e.avail_in,f=e.next_in,l=e.input,e.avail_in=t,e.next_in=0,e.input=r,Ye(a);a.lookahead>=$;){n=a.strstart,i=a.lookahead-($-1);do a.ins_h=(a.ins_h<<a.hash_shift^a.window[n+$-1])&a.hash_mask,a.prev[n&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=n,n++;while(--i);a.strstart=n,a.lookahead=$-1,Ye(a)}return a.strstart+=a.lookahead,a.block_start=a.strstart,a.insert=a.lookahead,a.lookahead=0,a.match_length=a.prev_length=$-1,a.match_available=0,e.next_in=f,e.input=l,e.avail_in=o,a.wrap=s,Ae}Ne.deflateInit=of;Ne.deflateInit2=Es;Ne.deflateReset=xs;Ne.deflateResetKeep=Ss;Ne.deflateSetHeader=sf;Ne.deflate=lf;Ne.deflateEnd=ff;Ne.deflateSetDictionary=uf;Ne.deflateInfo="pako deflate (from Nodeca project)";var tt={},Mr=Be,Cs=!0,As=!0;try{String.fromCharCode.apply(null,[0])}catch{Cs=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch{As=!1}var zt=new Mr.Buf8(256);for(var Oe=0;Oe<256;Oe++)zt[Oe]=Oe>=252?6:Oe>=248?5:Oe>=240?4:Oe>=224?3:Oe>=192?2:1;zt[254]=zt[254]=1;tt.string2buf=function(e){var r,t,a,n,i,s=e.length,o=0;for(n=0;n<s;n++)t=e.charCodeAt(n),(t&64512)===55296&&n+1<s&&(a=e.charCodeAt(n+1),(a&64512)===56320&&(t=65536+(t-55296<<10)+(a-56320),n++)),o+=t<128?1:t<2048?2:t<65536?3:4;for(r=new Mr.Buf8(o),i=0,n=0;i<o;n++)t=e.charCodeAt(n),(t&64512)===55296&&n+1<s&&(a=e.charCodeAt(n+1),(a&64512)===56320&&(t=65536+(t-55296<<10)+(a-56320),n++)),t<128?r[i++]=t:t<2048?(r[i++]=192|t>>>6,r[i++]=128|t&63):t<65536?(r[i++]=224|t>>>12,r[i++]=128|t>>>6&63,r[i++]=128|t&63):(r[i++]=240|t>>>18,r[i++]=128|t>>>12&63,r[i++]=128|t>>>6&63,r[i++]=128|t&63);return r};function Ps(e,r){if(r<65534&&(e.subarray&&As||!e.subarray&&Cs))return String.fromCharCode.apply(null,Mr.shrinkBuf(e,r));for(var t="",a=0;a<r;a++)t+=String.fromCharCode(e[a]);return t}tt.buf2binstring=function(e){return Ps(e,e.length)};tt.binstring2buf=function(e){for(var r=new Mr.Buf8(e.length),t=0,a=r.length;t<a;t++)r[t]=e.charCodeAt(t);return r};tt.buf2string=function(e,r){var t,a,n,i,s=r||e.length,o=new Array(s*2);for(a=0,t=0;t<s;){if(n=e[t++],n<128){o[a++]=n;continue}if(i=zt[n],i>4){o[a++]=65533,t+=i-1;continue}for(n&=i===2?31:i===3?15:7;i>1&&t<s;)n=n<<6|e[t++]&63,i--;if(i>1){o[a++]=65533;continue}n<65536?o[a++]=n:(n-=65536,o[a++]=55296|n>>10&1023,o[a++]=56320|n&1023)}return Ps(o,a)};tt.utf8border=function(e,r){var t;for(r=r||e.length,r>e.length&&(r=e.length),t=r-1;t>=0&&(e[t]&192)===128;)t--;return t<0||t===0?r:t+zt[e[t]]>r?t:r};function hf(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}var Ns=hf,Tt=Ne,Rt=Be,Na=tt,Ta=Va,cf=Ns,Ts=Object.prototype.toString,df=0,ua=4,ct=0,Zn=1,qn=2,pf=-1,mf=0,vf=8;function Ke(e){if(!(this instanceof Ke))return new Ke(e);this.options=Rt.assign({level:pf,method:vf,chunkSize:16384,windowBits:15,memLevel:8,strategy:mf,to:""},e||{});var r=this.options;r.raw&&r.windowBits>0?r.windowBits=-r.windowBits:r.gzip&&r.windowBits>0&&r.windowBits<16&&(r.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new cf,this.strm.avail_out=0;var t=Tt.deflateInit2(this.strm,r.level,r.method,r.windowBits,r.memLevel,r.strategy);if(t!==ct)throw new Error(Ta[t]);if(r.header&&Tt.deflateSetHeader(this.strm,r.header),r.dictionary){var a;if(typeof r.dictionary=="string"?a=Na.string2buf(r.dictionary):Ts.call(r.dictionary)==="[object ArrayBuffer]"?a=new Uint8Array(r.dictionary):a=r.dictionary,t=Tt.deflateSetDictionary(this.strm,a),t!==ct)throw new Error(Ta[t]);this._dict_set=!0}}Ke.prototype.push=function(e,r){var t=this.strm,a=this.options.chunkSize,n,i;if(this.ended)return!1;i=r===~~r?r:r===!0?ua:df,typeof e=="string"?t.input=Na.string2buf(e):Ts.call(e)==="[object ArrayBuffer]"?t.input=new Uint8Array(e):t.input=e,t.next_in=0,t.avail_in=t.input.length;do{if(t.avail_out===0&&(t.output=new Rt.Buf8(a),t.next_out=0,t.avail_out=a),n=Tt.deflate(t,i),n!==Zn&&n!==ct)return this.onEnd(n),this.ended=!0,!1;(t.avail_out===0||t.avail_in===0&&(i===ua||i===qn))&&(this.options.to==="string"?this.onData(Na.buf2binstring(Rt.shrinkBuf(t.output,t.next_out))):this.onData(Rt.shrinkBuf(t.output,t.next_out)))}while((t.avail_in>0||t.avail_out===0)&&n!==Zn);return i===ua?(n=Tt.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===ct):(i===qn&&(this.onEnd(ct),t.avail_out=0),!0)};Ke.prototype.onData=function(e){this.chunks.push(e)};Ke.prototype.onEnd=function(e){e===ct&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=Rt.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};function Xa(e,r){var t=new Ke(r);if(t.push(e,!0),t.err)throw t.msg||Ta[t.err];return t.result}function gf(e,r){return r=r||{},r.raw=!0,Xa(e,r)}function bf(e,r){return r=r||{},r.gzip=!0,Xa(e,r)}Wt.Deflate=Ke;Wt.deflate=Xa;Wt.deflateRaw=gf;Wt.gzip=bf;var qt={},ke={},nr=30,_f=12,yf=function(r,t){var a,n,i,s,o,f,l,u,y,c,p,b,g,C,h,v,w,E,A,M,B,O,D,U,R;a=r.state,n=r.next_in,U=r.input,i=n+(r.avail_in-5),s=r.next_out,R=r.output,o=s-(t-r.avail_out),f=s+(r.avail_out-257),l=a.dmax,u=a.wsize,y=a.whave,c=a.wnext,p=a.window,b=a.hold,g=a.bits,C=a.lencode,h=a.distcode,v=(1<<a.lenbits)-1,w=(1<<a.distbits)-1;e:do{g<15&&(b+=U[n++]<<g,g+=8,b+=U[n++]<<g,g+=8),E=C[b&v];t:for(;;){if(A=E>>>24,b>>>=A,g-=A,A=E>>>16&255,A===0)R[s++]=E&65535;else if(A&16){M=E&65535,A&=15,A&&(g<A&&(b+=U[n++]<<g,g+=8),M+=b&(1<<A)-1,b>>>=A,g-=A),g<15&&(b+=U[n++]<<g,g+=8,b+=U[n++]<<g,g+=8),E=h[b&w];r:for(;;){if(A=E>>>24,b>>>=A,g-=A,A=E>>>16&255,A&16){if(B=E&65535,A&=15,g<A&&(b+=U[n++]<<g,g+=8,g<A&&(b+=U[n++]<<g,g+=8)),B+=b&(1<<A)-1,B>l){r.msg="invalid distance too far back",a.mode=nr;break e}if(b>>>=A,g-=A,A=s-o,B>A){if(A=B-A,A>y&&a.sane){r.msg="invalid distance too far back",a.mode=nr;break e}if(O=0,D=p,c===0){if(O+=u-A,A<M){M-=A;do R[s++]=p[O++];while(--A);O=s-B,D=R}}else if(c<A){if(O+=u+c-A,A-=c,A<M){M-=A;do R[s++]=p[O++];while(--A);if(O=0,c<M){A=c,M-=A;do R[s++]=p[O++];while(--A);O=s-B,D=R}}}else if(O+=c-A,A<M){M-=A;do R[s++]=p[O++];while(--A);O=s-B,D=R}for(;M>2;)R[s++]=D[O++],R[s++]=D[O++],R[s++]=D[O++],M-=3;M&&(R[s++]=D[O++],M>1&&(R[s++]=D[O++]))}else{O=s-B;do R[s++]=R[O++],R[s++]=R[O++],R[s++]=R[O++],M-=3;while(M>2);M&&(R[s++]=R[O++],M>1&&(R[s++]=R[O++]))}}else if(A&64){r.msg="invalid distance code",a.mode=nr;break e}else{E=h[(E&65535)+(b&(1<<A)-1)];continue r}break}}else if(A&64)if(A&32){a.mode=_f;break e}else{r.msg="invalid literal/length code",a.mode=nr;break e}else{E=C[(E&65535)+(b&(1<<A)-1)];continue t}break}}while(n<i&&s<f);M=g>>3,n-=M,g-=M<<3,b&=(1<<g)-1,r.next_in=n,r.next_out=s,r.avail_in=n<i?5+(i-n):5-(n-i),r.avail_out=s<f?257+(f-s):257-(s-f),a.hold=b,a.bits=g},Vn=Be,lt=15,Xn=852,Gn=592,Yn=0,ha=1,Kn=2,wf=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],kf=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],Sf=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],xf=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64],Ef=function(r,t,a,n,i,s,o,f){var l=f.bits,u=0,y=0,c=0,p=0,b=0,g=0,C=0,h=0,v=0,w=0,E,A,M,B,O,D=null,U=0,R,ee=new Vn.Buf16(lt+1),se=new Vn.Buf16(lt+1),he=null,nt=0,it,S,x;for(u=0;u<=lt;u++)ee[u]=0;for(y=0;y<n;y++)ee[t[a+y]]++;for(b=l,p=lt;p>=1&&ee[p]===0;p--);if(b>p&&(b=p),p===0)return i[s++]=1<<24|64<<16|0,i[s++]=1<<24|64<<16|0,f.bits=1,0;for(c=1;c<p&&ee[c]===0;c++);for(b<c&&(b=c),h=1,u=1;u<=lt;u++)if(h<<=1,h-=ee[u],h<0)return-1;if(h>0&&(r===Yn||p!==1))return-1;for(se[1]=0,u=1;u<lt;u++)se[u+1]=se[u]+ee[u];for(y=0;y<n;y++)t[a+y]!==0&&(o[se[t[a+y]]++]=y);if(r===Yn?(D=he=o,R=19):r===ha?(D=wf,U-=257,he=kf,nt-=257,R=256):(D=Sf,he=xf,R=-1),w=0,y=0,u=c,O=s,g=b,C=0,M=-1,v=1<<b,B=v-1,r===ha&&v>Xn||r===Kn&&v>Gn)return 1;for(;;){it=u-C,o[y]<R?(S=0,x=o[y]):o[y]>R?(S=he[nt+o[y]],x=D[U+o[y]]):(S=96,x=0),E=1<<u-C,A=1<<g,c=A;do A-=E,i[O+(w>>C)+A]=it<<24|S<<16|x|0;while(A!==0);for(E=1<<u-1;w&E;)E>>=1;if(E!==0?(w&=E-1,w+=E):w=0,y++,--ee[u]===0){if(u===p)break;u=t[a+o[y]]}if(u>b&&(w&B)!==M){for(C===0&&(C=b),O+=c,g=u-C,h=1<<g;g+C<p&&(h-=ee[g+C],!(h<=0));)g++,h<<=1;if(v+=1<<g,r===ha&&v>Xn||r===Kn&&v>Gn)return 1;M=w&B,i[M]=b<<24|g<<16|O-s|0}}return w!==0&&(i[O+w]=u-C<<24|64<<16|0),f.bits=b,0},fe=Be,Ra=_s,Ee=ys,Cf=yf,Mt=Ef,Af=0,Rs=1,Ms=2,Jn=4,Pf=5,ir=6,Je=0,Nf=1,Tf=2,me=-2,Bs=-3,Os=-4,Rf=-5,Qn=8,Ds=1,ei=2,ti=3,ri=4,ai=5,ni=6,ii=7,si=8,oi=9,li=10,wr=11,Te=12,ca=13,fi=14,da=15,ui=16,hi=17,ci=18,di=19,sr=20,or=21,pi=22,mi=23,vi=24,gi=25,bi=26,pa=27,_i=28,yi=29,G=30,Fs=31,Mf=32,Bf=852,Of=592,Df=15,Ff=Df;function wi(e){return(e>>>24&255)+(e>>>8&65280)+((e&65280)<<8)+((e&255)<<24)}function If(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new fe.Buf16(320),this.work=new fe.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function Is(e){var r;return!e||!e.state?me:(r=e.state,e.total_in=e.total_out=r.total=0,e.msg="",r.wrap&&(e.adler=r.wrap&1),r.mode=Ds,r.last=0,r.havedict=0,r.dmax=32768,r.head=null,r.hold=0,r.bits=0,r.lencode=r.lendyn=new fe.Buf32(Bf),r.distcode=r.distdyn=new fe.Buf32(Of),r.sane=1,r.back=-1,Je)}function Ls(e){var r;return!e||!e.state?me:(r=e.state,r.wsize=0,r.whave=0,r.wnext=0,Is(e))}function zs(e,r){var t,a;return!e||!e.state||(a=e.state,r<0?(t=0,r=-r):(t=(r>>4)+1,r<48&&(r&=15)),r&&(r<8||r>15))?me:(a.window!==null&&a.wbits!==r&&(a.window=null),a.wrap=t,a.wbits=r,Ls(e))}function $s(e,r){var t,a;return e?(a=new If,e.state=a,a.window=null,t=zs(e,r),t!==Je&&(e.state=null),t):me}function Lf(e){return $s(e,Ff)}var ki=!0,ma,va;function zf(e){if(ki){var r;for(ma=new fe.Buf32(512),va=new fe.Buf32(32),r=0;r<144;)e.lens[r++]=8;for(;r<256;)e.lens[r++]=9;for(;r<280;)e.lens[r++]=7;for(;r<288;)e.lens[r++]=8;for(Mt(Rs,e.lens,0,288,ma,0,e.work,{bits:9}),r=0;r<32;)e.lens[r++]=5;Mt(Ms,e.lens,0,32,va,0,e.work,{bits:5}),ki=!1}e.lencode=ma,e.lenbits=9,e.distcode=va,e.distbits=5}function Us(e,r,t,a){var n,i=e.state;return i.window===null&&(i.wsize=1<<i.wbits,i.wnext=0,i.whave=0,i.window=new fe.Buf8(i.wsize)),a>=i.wsize?(fe.arraySet(i.window,r,t-i.wsize,i.wsize,0),i.wnext=0,i.whave=i.wsize):(n=i.wsize-i.wnext,n>a&&(n=a),fe.arraySet(i.window,r,t-a,n,i.wnext),a-=n,a?(fe.arraySet(i.window,r,t-a,a,0),i.wnext=a,i.whave=i.wsize):(i.wnext+=n,i.wnext===i.wsize&&(i.wnext=0),i.whave<i.wsize&&(i.whave+=n))),0}function $f(e,r){var t,a,n,i,s,o,f,l,u,y,c,p,b,g,C=0,h,v,w,E,A,M,B,O,D=new fe.Buf8(4),U,R,ee=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&e.avail_in!==0)return me;t=e.state,t.mode===Te&&(t.mode=ca),s=e.next_out,n=e.output,f=e.avail_out,i=e.next_in,a=e.input,o=e.avail_in,l=t.hold,u=t.bits,y=o,c=f,O=Je;e:for(;;)switch(t.mode){case Ds:if(t.wrap===0){t.mode=ca;break}for(;u<16;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}if(t.wrap&2&&l===35615){t.check=0,D[0]=l&255,D[1]=l>>>8&255,t.check=Ee(t.check,D,2,0),l=0,u=0,t.mode=ei;break}if(t.flags=0,t.head&&(t.head.done=!1),!(t.wrap&1)||(((l&255)<<8)+(l>>8))%31){e.msg="incorrect header check",t.mode=G;break}if((l&15)!==Qn){e.msg="unknown compression method",t.mode=G;break}if(l>>>=4,u-=4,B=(l&15)+8,t.wbits===0)t.wbits=B;else if(B>t.wbits){e.msg="invalid window size",t.mode=G;break}t.dmax=1<<B,e.adler=t.check=1,t.mode=l&512?li:Te,l=0,u=0;break;case ei:for(;u<16;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}if(t.flags=l,(t.flags&255)!==Qn){e.msg="unknown compression method",t.mode=G;break}if(t.flags&57344){e.msg="unknown header flags set",t.mode=G;break}t.head&&(t.head.text=l>>8&1),t.flags&512&&(D[0]=l&255,D[1]=l>>>8&255,t.check=Ee(t.check,D,2,0)),l=0,u=0,t.mode=ti;case ti:for(;u<32;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}t.head&&(t.head.time=l),t.flags&512&&(D[0]=l&255,D[1]=l>>>8&255,D[2]=l>>>16&255,D[3]=l>>>24&255,t.check=Ee(t.check,D,4,0)),l=0,u=0,t.mode=ri;case ri:for(;u<16;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}t.head&&(t.head.xflags=l&255,t.head.os=l>>8),t.flags&512&&(D[0]=l&255,D[1]=l>>>8&255,t.check=Ee(t.check,D,2,0)),l=0,u=0,t.mode=ai;case ai:if(t.flags&1024){for(;u<16;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}t.length=l,t.head&&(t.head.extra_len=l),t.flags&512&&(D[0]=l&255,D[1]=l>>>8&255,t.check=Ee(t.check,D,2,0)),l=0,u=0}else t.head&&(t.head.extra=null);t.mode=ni;case ni:if(t.flags&1024&&(p=t.length,p>o&&(p=o),p&&(t.head&&(B=t.head.extra_len-t.length,t.head.extra||(t.head.extra=new Array(t.head.extra_len)),fe.arraySet(t.head.extra,a,i,p,B)),t.flags&512&&(t.check=Ee(t.check,a,p,i)),o-=p,i+=p,t.length-=p),t.length))break e;t.length=0,t.mode=ii;case ii:if(t.flags&2048){if(o===0)break e;p=0;do B=a[i+p++],t.head&&B&&t.length<65536&&(t.head.name+=String.fromCharCode(B));while(B&&p<o);if(t.flags&512&&(t.check=Ee(t.check,a,p,i)),o-=p,i+=p,B)break e}else t.head&&(t.head.name=null);t.length=0,t.mode=si;case si:if(t.flags&4096){if(o===0)break e;p=0;do B=a[i+p++],t.head&&B&&t.length<65536&&(t.head.comment+=String.fromCharCode(B));while(B&&p<o);if(t.flags&512&&(t.check=Ee(t.check,a,p,i)),o-=p,i+=p,B)break e}else t.head&&(t.head.comment=null);t.mode=oi;case oi:if(t.flags&512){for(;u<16;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}if(l!==(t.check&65535)){e.msg="header crc mismatch",t.mode=G;break}l=0,u=0}t.head&&(t.head.hcrc=t.flags>>9&1,t.head.done=!0),e.adler=t.check=0,t.mode=Te;break;case li:for(;u<32;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}e.adler=t.check=wi(l),l=0,u=0,t.mode=wr;case wr:if(t.havedict===0)return e.next_out=s,e.avail_out=f,e.next_in=i,e.avail_in=o,t.hold=l,t.bits=u,Tf;e.adler=t.check=1,t.mode=Te;case Te:if(r===Pf||r===ir)break e;case ca:if(t.last){l>>>=u&7,u-=u&7,t.mode=pa;break}for(;u<3;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}switch(t.last=l&1,l>>>=1,u-=1,l&3){case 0:t.mode=fi;break;case 1:if(zf(t),t.mode=sr,r===ir){l>>>=2,u-=2;break e}break;case 2:t.mode=hi;break;case 3:e.msg="invalid block type",t.mode=G}l>>>=2,u-=2;break;case fi:for(l>>>=u&7,u-=u&7;u<32;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}if((l&65535)!==(l>>>16^65535)){e.msg="invalid stored block lengths",t.mode=G;break}if(t.length=l&65535,l=0,u=0,t.mode=da,r===ir)break e;case da:t.mode=ui;case ui:if(p=t.length,p){if(p>o&&(p=o),p>f&&(p=f),p===0)break e;fe.arraySet(n,a,i,p,s),o-=p,i+=p,f-=p,s+=p,t.length-=p;break}t.mode=Te;break;case hi:for(;u<14;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}if(t.nlen=(l&31)+257,l>>>=5,u-=5,t.ndist=(l&31)+1,l>>>=5,u-=5,t.ncode=(l&15)+4,l>>>=4,u-=4,t.nlen>286||t.ndist>30){e.msg="too many length or distance symbols",t.mode=G;break}t.have=0,t.mode=ci;case ci:for(;t.have<t.ncode;){for(;u<3;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}t.lens[ee[t.have++]]=l&7,l>>>=3,u-=3}for(;t.have<19;)t.lens[ee[t.have++]]=0;if(t.lencode=t.lendyn,t.lenbits=7,U={bits:t.lenbits},O=Mt(Af,t.lens,0,19,t.lencode,0,t.work,U),t.lenbits=U.bits,O){e.msg="invalid code lengths set",t.mode=G;break}t.have=0,t.mode=di;case di:for(;t.have<t.nlen+t.ndist;){for(;C=t.lencode[l&(1<<t.lenbits)-1],h=C>>>24,v=C>>>16&255,w=C&65535,!(h<=u);){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}if(w<16)l>>>=h,u-=h,t.lens[t.have++]=w;else{if(w===16){for(R=h+2;u<R;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}if(l>>>=h,u-=h,t.have===0){e.msg="invalid bit length repeat",t.mode=G;break}B=t.lens[t.have-1],p=3+(l&3),l>>>=2,u-=2}else if(w===17){for(R=h+3;u<R;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}l>>>=h,u-=h,B=0,p=3+(l&7),l>>>=3,u-=3}else{for(R=h+7;u<R;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}l>>>=h,u-=h,B=0,p=11+(l&127),l>>>=7,u-=7}if(t.have+p>t.nlen+t.ndist){e.msg="invalid bit length repeat",t.mode=G;break}for(;p--;)t.lens[t.have++]=B}}if(t.mode===G)break;if(t.lens[256]===0){e.msg="invalid code -- missing end-of-block",t.mode=G;break}if(t.lenbits=9,U={bits:t.lenbits},O=Mt(Rs,t.lens,0,t.nlen,t.lencode,0,t.work,U),t.lenbits=U.bits,O){e.msg="invalid literal/lengths set",t.mode=G;break}if(t.distbits=6,t.distcode=t.distdyn,U={bits:t.distbits},O=Mt(Ms,t.lens,t.nlen,t.ndist,t.distcode,0,t.work,U),t.distbits=U.bits,O){e.msg="invalid distances set",t.mode=G;break}if(t.mode=sr,r===ir)break e;case sr:t.mode=or;case or:if(o>=6&&f>=258){e.next_out=s,e.avail_out=f,e.next_in=i,e.avail_in=o,t.hold=l,t.bits=u,Cf(e,c),s=e.next_out,n=e.output,f=e.avail_out,i=e.next_in,a=e.input,o=e.avail_in,l=t.hold,u=t.bits,t.mode===Te&&(t.back=-1);break}for(t.back=0;C=t.lencode[l&(1<<t.lenbits)-1],h=C>>>24,v=C>>>16&255,w=C&65535,!(h<=u);){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}if(v&&!(v&240)){for(E=h,A=v,M=w;C=t.lencode[M+((l&(1<<E+A)-1)>>E)],h=C>>>24,v=C>>>16&255,w=C&65535,!(E+h<=u);){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}l>>>=E,u-=E,t.back+=E}if(l>>>=h,u-=h,t.back+=h,t.length=w,v===0){t.mode=bi;break}if(v&32){t.back=-1,t.mode=Te;break}if(v&64){e.msg="invalid literal/length code",t.mode=G;break}t.extra=v&15,t.mode=pi;case pi:if(t.extra){for(R=t.extra;u<R;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}t.length+=l&(1<<t.extra)-1,l>>>=t.extra,u-=t.extra,t.back+=t.extra}t.was=t.length,t.mode=mi;case mi:for(;C=t.distcode[l&(1<<t.distbits)-1],h=C>>>24,v=C>>>16&255,w=C&65535,!(h<=u);){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}if(!(v&240)){for(E=h,A=v,M=w;C=t.distcode[M+((l&(1<<E+A)-1)>>E)],h=C>>>24,v=C>>>16&255,w=C&65535,!(E+h<=u);){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}l>>>=E,u-=E,t.back+=E}if(l>>>=h,u-=h,t.back+=h,v&64){e.msg="invalid distance code",t.mode=G;break}t.offset=w,t.extra=v&15,t.mode=vi;case vi:if(t.extra){for(R=t.extra;u<R;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}t.offset+=l&(1<<t.extra)-1,l>>>=t.extra,u-=t.extra,t.back+=t.extra}if(t.offset>t.dmax){e.msg="invalid distance too far back",t.mode=G;break}t.mode=gi;case gi:if(f===0)break e;if(p=c-f,t.offset>p){if(p=t.offset-p,p>t.whave&&t.sane){e.msg="invalid distance too far back",t.mode=G;break}p>t.wnext?(p-=t.wnext,b=t.wsize-p):b=t.wnext-p,p>t.length&&(p=t.length),g=t.window}else g=n,b=s-t.offset,p=t.length;p>f&&(p=f),f-=p,t.length-=p;do n[s++]=g[b++];while(--p);t.length===0&&(t.mode=or);break;case bi:if(f===0)break e;n[s++]=t.length,f--,t.mode=or;break;case pa:if(t.wrap){for(;u<32;){if(o===0)break e;o--,l|=a[i++]<<u,u+=8}if(c-=f,e.total_out+=c,t.total+=c,c&&(e.adler=t.check=t.flags?Ee(t.check,n,c,s-c):Ra(t.check,n,c,s-c)),c=f,(t.flags?l:wi(l))!==t.check){e.msg="incorrect data check",t.mode=G;break}l=0,u=0}t.mode=_i;case _i:if(t.wrap&&t.flags){for(;u<32;){if(o===0)break e;o--,l+=a[i++]<<u,u+=8}if(l!==(t.total&4294967295)){e.msg="incorrect length check",t.mode=G;break}l=0,u=0}t.mode=yi;case yi:O=Nf;break e;case G:O=Bs;break e;case Fs:return Os;case Mf:default:return me}return e.next_out=s,e.avail_out=f,e.next_in=i,e.avail_in=o,t.hold=l,t.bits=u,(t.wsize||c!==e.avail_out&&t.mode<G&&(t.mode<pa||r!==Jn))&&Us(e,e.output,e.next_out,c-e.avail_out),y-=e.avail_in,c-=e.avail_out,e.total_in+=y,e.total_out+=c,t.total+=c,t.wrap&&c&&(e.adler=t.check=t.flags?Ee(t.check,n,c,e.next_out-c):Ra(t.check,n,c,e.next_out-c)),e.data_type=t.bits+(t.last?64:0)+(t.mode===Te?128:0)+(t.mode===sr||t.mode===da?256:0),(y===0&&c===0||r===Jn)&&O===Je&&(O=Rf),O}function Uf(e){if(!e||!e.state)return me;var r=e.state;return r.window&&(r.window=null),e.state=null,Je}function jf(e,r){var t;return!e||!e.state||(t=e.state,!(t.wrap&2))?me:(t.head=r,r.done=!1,Je)}function Wf(e,r){var t=r.length,a,n,i;return!e||!e.state||(a=e.state,a.wrap!==0&&a.mode!==wr)?me:a.mode===wr&&(n=1,n=Ra(n,r,t,0),n!==a.check)?Bs:(i=Us(e,r,t,t),i?(a.mode=Fs,Os):(a.havedict=1,Je))}ke.inflateReset=Ls;ke.inflateReset2=zs;ke.inflateResetKeep=Is;ke.inflateInit=Lf;ke.inflateInit2=$s;ke.inflate=$f;ke.inflateEnd=Uf;ke.inflateGetHeader=jf;ke.inflateSetDictionary=Wf;ke.inflateInfo="pako inflate (from Nodeca project)";var js={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};function Hf(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}var Zf=Hf,dt=ke,Bt=Be,vr=tt,K=js,Ma=Va,qf=Ns,Vf=Zf,Ws=Object.prototype.toString;function Qe(e){if(!(this instanceof Qe))return new Qe(e);this.options=Bt.assign({chunkSize:16384,windowBits:0,to:""},e||{});var r=this.options;r.raw&&r.windowBits>=0&&r.windowBits<16&&(r.windowBits=-r.windowBits,r.windowBits===0&&(r.windowBits=-15)),r.windowBits>=0&&r.windowBits<16&&!(e&&e.windowBits)&&(r.windowBits+=32),r.windowBits>15&&r.windowBits<48&&(r.windowBits&15||(r.windowBits|=15)),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new qf,this.strm.avail_out=0;var t=dt.inflateInit2(this.strm,r.windowBits);if(t!==K.Z_OK)throw new Error(Ma[t]);if(this.header=new Vf,dt.inflateGetHeader(this.strm,this.header),r.dictionary&&(typeof r.dictionary=="string"?r.dictionary=vr.string2buf(r.dictionary):Ws.call(r.dictionary)==="[object ArrayBuffer]"&&(r.dictionary=new Uint8Array(r.dictionary)),r.raw&&(t=dt.inflateSetDictionary(this.strm,r.dictionary),t!==K.Z_OK)))throw new Error(Ma[t])}Qe.prototype.push=function(e,r){var t=this.strm,a=this.options.chunkSize,n=this.options.dictionary,i,s,o,f,l,u=!1;if(this.ended)return!1;s=r===~~r?r:r===!0?K.Z_FINISH:K.Z_NO_FLUSH,typeof e=="string"?t.input=vr.binstring2buf(e):Ws.call(e)==="[object ArrayBuffer]"?t.input=new Uint8Array(e):t.input=e,t.next_in=0,t.avail_in=t.input.length;do{if(t.avail_out===0&&(t.output=new Bt.Buf8(a),t.next_out=0,t.avail_out=a),i=dt.inflate(t,K.Z_NO_FLUSH),i===K.Z_NEED_DICT&&n&&(i=dt.inflateSetDictionary(this.strm,n)),i===K.Z_BUF_ERROR&&u===!0&&(i=K.Z_OK,u=!1),i!==K.Z_STREAM_END&&i!==K.Z_OK)return this.onEnd(i),this.ended=!0,!1;t.next_out&&(t.avail_out===0||i===K.Z_STREAM_END||t.avail_in===0&&(s===K.Z_FINISH||s===K.Z_SYNC_FLUSH))&&(this.options.to==="string"?(o=vr.utf8border(t.output,t.next_out),f=t.next_out-o,l=vr.buf2string(t.output,o),t.next_out=f,t.avail_out=a-f,f&&Bt.arraySet(t.output,t.output,o,f,0),this.onData(l)):this.onData(Bt.shrinkBuf(t.output,t.next_out))),t.avail_in===0&&t.avail_out===0&&(u=!0)}while((t.avail_in>0||t.avail_out===0)&&i!==K.Z_STREAM_END);return i===K.Z_STREAM_END&&(s=K.Z_FINISH),s===K.Z_FINISH?(i=dt.inflateEnd(this.strm),this.onEnd(i),this.ended=!0,i===K.Z_OK):(s===K.Z_SYNC_FLUSH&&(this.onEnd(K.Z_OK),t.avail_out=0),!0)};Qe.prototype.onData=function(e){this.chunks.push(e)};Qe.prototype.onEnd=function(e){e===K.Z_OK&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=Bt.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};function Ga(e,r){var t=new Qe(r);if(t.push(e,!0),t.err)throw t.msg||Ma[t.err];return t.result}function Xf(e,r){return r=r||{},r.raw=!0,Ga(e,r)}qt.Inflate=Qe;qt.inflate=Ga;qt.inflateRaw=Xf;qt.ungzip=Ga;var Gf=Be.assign,Yf=Wt,Kf=qt,Jf=js,Hs={};Gf(Hs,Yf,Kf,Jf);var Qf=Hs,eu=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Uint32Array<"u",tu=Qf,Zs=Y(),Br=ve,ru=eu?"uint8array":"array";Nr.magic="\b\0";function rt(e,r){Br.call(this,"FlateWorker/"+e),this._pako=null,this._pakoAction=e,this._pakoOptions=r,this.meta={}}Zs.inherits(rt,Br);rt.prototype.processChunk=function(e){this.meta=e.meta,this._pako===null&&this._createPako(),this._pako.push(Zs.transformTo(ru,e.data),!1)};rt.prototype.flush=function(){Br.prototype.flush.call(this),this._pako===null&&this._createPako(),this._pako.push([],!0)};rt.prototype.cleanUp=function(){Br.prototype.cleanUp.call(this),this._pako=null};rt.prototype._createPako=function(){this._pako=new tu[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var e=this;this._pako.onData=function(r){e.push({data:r,meta:e.meta})}};Nr.compressWorker=function(e){return new rt("Deflate",e)};Nr.uncompressWorker=function(){return new rt("Inflate",{})};var Si=ve;Pr.STORE={magic:"\0\0",compressWorker:function(){return new Si("STORE compression")},uncompressWorker:function(){return new Si("STORE decompression")}};Pr.DEFLATE=Nr;var We={};We.LOCAL_FILE_HEADER="PK";We.CENTRAL_FILE_HEADER="PK";We.CENTRAL_DIRECTORY_END="PK";We.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK\x07";We.ZIP64_CENTRAL_DIRECTORY_END="PK";We.DATA_DESCRIPTOR="PK\x07\b";var ut=Y(),yt=ve,ga=mt,xi=Fa,kr=We,q=function(e,r){var t="",a;for(a=0;a<r;a++)t+=String.fromCharCode(e&255),e=e>>>8;return t},au=function(e,r){var t=e;return e||(t=r?16893:33204),(t&65535)<<16},nu=function(e){return(e||0)&63},qs=function(e,r,t,a,n,i){var s=e.file,o=e.compression,f=i!==ga.utf8encode,l=ut.transformTo("string",i(s.name)),u=ut.transformTo("string",ga.utf8encode(s.name)),y=s.comment,c=ut.transformTo("string",i(y)),p=ut.transformTo("string",ga.utf8encode(y)),b=u.length!==s.name.length,g=p.length!==y.length,C,h,v="",w="",E="",A=s.dir,M=s.date,B={crc32:0,compressedSize:0,uncompressedSize:0};(!r||t)&&(B.crc32=e.crc32,B.compressedSize=e.compressedSize,B.uncompressedSize=e.uncompressedSize);var O=0;r&&(O|=8),!f&&(b||g)&&(O|=2048);var D=0,U=0;A&&(D|=16),n==="UNIX"?(U=798,D|=au(s.unixPermissions,A)):(U=20,D|=nu(s.dosPermissions)),C=M.getUTCHours(),C=C<<6,C=C|M.getUTCMinutes(),C=C<<5,C=C|M.getUTCSeconds()/2,h=M.getUTCFullYear()-1980,h=h<<4,h=h|M.getUTCMonth()+1,h=h<<5,h=h|M.getUTCDate(),b&&(w=q(1,1)+q(xi(l),4)+u,v+="up"+q(w.length,2)+w),g&&(E=q(1,1)+q(xi(c),4)+p,v+="uc"+q(E.length,2)+E);var R="";R+=`
\0`,R+=q(O,2),R+=o.magic,R+=q(C,2),R+=q(h,2),R+=q(B.crc32,4),R+=q(B.compressedSize,4),R+=q(B.uncompressedSize,4),R+=q(l.length,2),R+=q(v.length,2);var ee=kr.LOCAL_FILE_HEADER+R+l+v,se=kr.CENTRAL_FILE_HEADER+q(U,2)+R+q(c.length,2)+"\0\0\0\0"+q(D,4)+q(a,4)+l+v+c;return{fileRecord:ee,dirRecord:se}},iu=function(e,r,t,a,n){var i="",s=ut.transformTo("string",n(a));return i=kr.CENTRAL_DIRECTORY_END+"\0\0\0\0"+q(e,2)+q(e,2)+q(r,4)+q(t,4)+q(s.length,2)+s,i},su=function(e){var r="";return r=kr.DATA_DESCRIPTOR+q(e.crc32,4)+q(e.compressedSize,4)+q(e.uncompressedSize,4),r};function Se(e,r,t,a){yt.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=r,this.zipPlatform=t,this.encodeFileName=a,this.streamFiles=e,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}ut.inherits(Se,yt);Se.prototype.push=function(e){var r=e.meta.percent||0,t=this.entriesCount,a=this._sources.length;this.accumulate?this.contentBuffer.push(e):(this.bytesWritten+=e.data.length,yt.prototype.push.call(this,{data:e.data,meta:{currentFile:this.currentFile,percent:t?(r+100*(t-a-1))/t:100}}))};Se.prototype.openedSource=function(e){this.currentSourceOffset=this.bytesWritten,this.currentFile=e.file.name;var r=this.streamFiles&&!e.file.dir;if(r){var t=qs(e,r,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:t.fileRecord,meta:{percent:0}})}else this.accumulate=!0};Se.prototype.closedSource=function(e){this.accumulate=!1;var r=this.streamFiles&&!e.file.dir,t=qs(e,r,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(t.dirRecord),r)this.push({data:su(e),meta:{percent:100}});else for(this.push({data:t.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null};Se.prototype.flush=function(){for(var e=this.bytesWritten,r=0;r<this.dirRecords.length;r++)this.push({data:this.dirRecords[r],meta:{percent:100}});var t=this.bytesWritten-e,a=iu(this.dirRecords.length,t,e,this.zipComment,this.encodeFileName);this.push({data:a,meta:{percent:100}})};Se.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()};Se.prototype.registerPrevious=function(e){this._sources.push(e);var r=this;return e.on("data",function(t){r.processChunk(t)}),e.on("end",function(){r.closedSource(r.previous.streamInfo),r._sources.length?r.prepareNextSource():r.end()}),e.on("error",function(t){r.error(t)}),this};Se.prototype.resume=function(){if(!yt.prototype.resume.call(this))return!1;if(!this.previous&&this._sources.length)return this.prepareNextSource(),!0;if(!this.previous&&!this._sources.length&&!this.generatedError)return this.end(),!0};Se.prototype.error=function(e){var r=this._sources;if(!yt.prototype.error.call(this,e))return!1;for(var t=0;t<r.length;t++)try{r[t].error(e)}catch{}return!0};Se.prototype.lock=function(){yt.prototype.lock.call(this);for(var e=this._sources,r=0;r<e.length;r++)e[r].lock()};var ou=Se,lu=Pr,fu=ou,uu=function(e,r){var t=e||r,a=lu[t];if(!a)throw new Error(t+" is not a valid compression method !");return a};as.generateWorker=function(e,r,t){var a=new fu(r.streamFiles,t,r.platform,r.encodeFileName),n=0;try{e.forEach(function(i,s){n++;var o=uu(s.options.compression,r.compression),f=s.options.compressionOptions||r.compressionOptions||{},l=s.dir,u=s.date;s._compressWorker(o,f).withStreamInfo("file",{name:i,dir:l,date:u,comment:s.comment||"",unixPermissions:s.unixPermissions,dosPermissions:s.dosPermissions}).pipe(a)}),a.entriesCount=n}catch(i){a.error(i)}return a};var hu=Y(),Or=ve;function Vt(e,r){Or.call(this,"Nodejs stream input adapter for "+e),this._upstreamEnded=!1,this._bindStream(r)}hu.inherits(Vt,Or);Vt.prototype._bindStream=function(e){var r=this;this._stream=e,e.pause(),e.on("data",function(t){r.push({data:t,meta:{percent:0}})}).on("error",function(t){r.isPaused?this.generatedError=t:r.error(t)}).on("end",function(){r.isPaused?r._upstreamEnded=!0:r.end()})};Vt.prototype.pause=function(){return Or.prototype.pause.call(this)?(this._stream.pause(),!0):!1};Vt.prototype.resume=function(){return Or.prototype.resume.call(this)?(this._upstreamEnded?this.end():this._stream.resume(),!0):!1};var cu=Vt,du=mt,Ot=Y(),Vs=ve,pu=Ji,Xs=ge,Ei=Ua,mu=sl,vu=as,Ci=Er,gu=cu,Gs=function(e,r,t){var a=Ot.getTypeOf(r),n,i=Ot.extend(t||{},Xs);i.date=i.date||new Date,i.compression!==null&&(i.compression=i.compression.toUpperCase()),typeof i.unixPermissions=="string"&&(i.unixPermissions=parseInt(i.unixPermissions,8)),i.unixPermissions&&i.unixPermissions&16384&&(i.dir=!0),i.dosPermissions&&i.dosPermissions&16&&(i.dir=!0),i.dir&&(e=Ys(e)),i.createFolders&&(n=bu(e))&&Ks.call(this,n,!0);var s=a==="string"&&i.binary===!1&&i.base64===!1;(!t||typeof t.binary>"u")&&(i.binary=!s);var o=r instanceof Ei&&r.uncompressedSize===0;(o||i.dir||!r||r.length===0)&&(i.base64=!1,i.binary=!0,r="",i.compression="STORE",a="string");var f=null;r instanceof Ei||r instanceof Vs?f=r:Ci.isNode&&Ci.isStream(r)?f=new gu(e,r):f=Ot.prepareContent(e,r,i.binary,i.optimizedBinaryString,i.base64);var l=new mu(e,f,i);this.files[e]=l},bu=function(e){e.slice(-1)==="/"&&(e=e.substring(0,e.length-1));var r=e.lastIndexOf("/");return r>0?e.substring(0,r):""},Ys=function(e){return e.slice(-1)!=="/"&&(e+="/"),e},Ks=function(e,r){return r=typeof r<"u"?r:Xs.createFolders,e=Ys(e),this.files[e]||Gs.call(this,e,null,{dir:!0,createFolders:r}),this.files[e]};function Ai(e){return Object.prototype.toString.call(e)==="[object RegExp]"}var _u={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(e){var r,t,a;for(r in this.files)a=this.files[r],t=r.slice(this.root.length,r.length),t&&r.slice(0,this.root.length)===this.root&&e(t,a)},filter:function(e){var r=[];return this.forEach(function(t,a){e(t,a)&&r.push(a)}),r},file:function(e,r,t){if(arguments.length===1)if(Ai(e)){var a=e;return this.filter(function(i,s){return!s.dir&&a.test(i)})}else{var n=this.files[this.root+e];return n&&!n.dir?n:null}else e=this.root+e,Gs.call(this,e,r,t);return this},folder:function(e){if(!e)return this;if(Ai(e))return this.filter(function(n,i){return i.dir&&e.test(n)});var r=this.root+e,t=Ks.call(this,r),a=this.clone();return a.root=t.name,a},remove:function(e){e=this.root+e;var r=this.files[e];if(r||(e.slice(-1)!=="/"&&(e+="/"),r=this.files[e]),r&&!r.dir)delete this.files[e];else for(var t=this.filter(function(n,i){return i.name.slice(0,e.length)===e}),a=0;a<t.length;a++)delete this.files[t[a].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(e){var r,t={};try{if(t=Ot.extend(e||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:du.utf8encode}),t.type=t.type.toLowerCase(),t.compression=t.compression.toUpperCase(),t.type==="binarystring"&&(t.type="string"),!t.type)throw new Error("No output type specified.");Ot.checkSupport(t.type),(t.platform==="darwin"||t.platform==="freebsd"||t.platform==="linux"||t.platform==="sunos")&&(t.platform="UNIX"),t.platform==="win32"&&(t.platform="DOS");var a=t.comment||this.comment||"";r=vu.generateWorker(this,t,a)}catch(n){r=new Vs("error"),r.error(n)}return new pu(r,t.type||"string",t.mimeType)},generateAsync:function(e,r){return this.generateInternalStream(e).accumulate(r)},generateNodeStream:function(e,r){return e=e||{},e.type||(e.type="nodebuffer"),this.generateInternalStream(e).toNodejsStream(r)}},yu=_u,wu=Y();function Js(e){this.data=e,this.length=e.length,this.index=0,this.zero=0}Js.prototype={checkOffset:function(e){this.checkIndex(this.index+e)},checkIndex:function(e){if(this.length<this.zero+e||e<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+e+"). Corrupted zip ?")},setIndex:function(e){this.checkIndex(e),this.index=e},skip:function(e){this.setIndex(this.index+e)},byteAt:function(){},readInt:function(e){var r=0,t;for(this.checkOffset(e),t=this.index+e-1;t>=this.index;t--)r=(r<<8)+this.byteAt(t);return this.index+=e,r},readString:function(e){return wu.transformTo("string",this.readData(e))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var e=this.readInt(4);return new Date(Date.UTC((e>>25&127)+1980,(e>>21&15)-1,e>>16&31,e>>11&31,e>>5&63,(e&31)<<1))}};var Qs=Js,eo=Qs,ku=Y();function wt(e){eo.call(this,e);for(var r=0;r<this.data.length;r++)e[r]=e[r]&255}ku.inherits(wt,eo);wt.prototype.byteAt=function(e){return this.data[this.zero+e]};wt.prototype.lastIndexOfSignature=function(e){for(var r=e.charCodeAt(0),t=e.charCodeAt(1),a=e.charCodeAt(2),n=e.charCodeAt(3),i=this.length-4;i>=0;--i)if(this.data[i]===r&&this.data[i+1]===t&&this.data[i+2]===a&&this.data[i+3]===n)return i-this.zero;return-1};wt.prototype.readAndCheckSignature=function(e){var r=e.charCodeAt(0),t=e.charCodeAt(1),a=e.charCodeAt(2),n=e.charCodeAt(3),i=this.readData(4);return r===i[0]&&t===i[1]&&a===i[2]&&n===i[3]};wt.prototype.readData=function(e){if(this.checkOffset(e),e===0)return[];var r=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,r};var to=wt,ro=Qs,Su=Y();function kt(e){ro.call(this,e)}Su.inherits(kt,ro);kt.prototype.byteAt=function(e){return this.data.charCodeAt(this.zero+e)};kt.prototype.lastIndexOfSignature=function(e){return this.data.lastIndexOf(e)-this.zero};kt.prototype.readAndCheckSignature=function(e){var r=this.readData(4);return e===r};kt.prototype.readData=function(e){this.checkOffset(e);var r=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,r};var xu=kt,ao=to,Eu=Y();function Ya(e){ao.call(this,e)}Eu.inherits(Ya,ao);Ya.prototype.readData=function(e){if(this.checkOffset(e),e===0)return new Uint8Array(0);var r=this.data.subarray(this.zero+this.index,this.zero+this.index+e);return this.index+=e,r};var no=Ya,io=no,Cu=Y();function Ka(e){io.call(this,e)}Cu.inherits(Ka,io);Ka.prototype.readData=function(e){this.checkOffset(e);var r=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,r};var Au=Ka,lr=Y(),Pi=te,Pu=to,Nu=xu,Tu=Au,Ru=no,so=function(e){var r=lr.getTypeOf(e);return lr.checkSupport(r),r==="string"&&!Pi.uint8array?new Nu(e):r==="nodebuffer"?new Tu(e):Pi.uint8array?new Ru(lr.transformTo("uint8array",e)):new Pu(lr.transformTo("array",e))},ba=so,De=Y(),Mu=Ua,Ni=Fa,fr=mt,ur=Pr,Bu=te,Ou=0,Du=3,Fu=function(e){for(var r in ur)if(Object.prototype.hasOwnProperty.call(ur,r)&&ur[r].magic===e)return ur[r];return null};function oo(e,r){this.options=e,this.loadOptions=r}oo.prototype={isEncrypted:function(){return(this.bitFlag&1)===1},useUTF8:function(){return(this.bitFlag&2048)===2048},readLocalPart:function(e){var r,t;if(e.skip(22),this.fileNameLength=e.readInt(2),t=e.readInt(2),this.fileName=e.readData(this.fileNameLength),e.skip(t),this.compressedSize===-1||this.uncompressedSize===-1)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(r=Fu(this.compressionMethod),r===null)throw new Error("Corrupted zip : compression "+De.pretty(this.compressionMethod)+" unknown (inner file : "+De.transformTo("string",this.fileName)+")");this.decompressed=new Mu(this.compressedSize,this.uncompressedSize,this.crc32,r,e.readData(this.compressedSize))},readCentralPart:function(e){this.versionMadeBy=e.readInt(2),e.skip(2),this.bitFlag=e.readInt(2),this.compressionMethod=e.readString(2),this.date=e.readDate(),this.crc32=e.readInt(4),this.compressedSize=e.readInt(4),this.uncompressedSize=e.readInt(4);var r=e.readInt(2);if(this.extraFieldsLength=e.readInt(2),this.fileCommentLength=e.readInt(2),this.diskNumberStart=e.readInt(2),this.internalFileAttributes=e.readInt(2),this.externalFileAttributes=e.readInt(4),this.localHeaderOffset=e.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");e.skip(r),this.readExtraFields(e),this.parseZIP64ExtraField(e),this.fileComment=e.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var e=this.versionMadeBy>>8;this.dir=!!(this.externalFileAttributes&16),e===Ou&&(this.dosPermissions=this.externalFileAttributes&63),e===Du&&(this.unixPermissions=this.externalFileAttributes>>16&65535),!this.dir&&this.fileNameStr.slice(-1)==="/"&&(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var e=ba(this.extraFields[1].value);this.uncompressedSize===De.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===De.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===De.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===De.MAX_VALUE_32BITS&&(this.diskNumberStart=e.readInt(4))}},readExtraFields:function(e){var r=e.index+this.extraFieldsLength,t,a,n;for(this.extraFields||(this.extraFields={});e.index+4<r;)t=e.readInt(2),a=e.readInt(2),n=e.readData(a),this.extraFields[t]={id:t,length:a,value:n};e.setIndex(r)},handleUTF8:function(){var e=Bu.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=fr.utf8decode(this.fileName),this.fileCommentStr=fr.utf8decode(this.fileComment);else{var r=this.findExtraFieldUnicodePath();if(r!==null)this.fileNameStr=r;else{var t=De.transformTo(e,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(t)}var a=this.findExtraFieldUnicodeComment();if(a!==null)this.fileCommentStr=a;else{var n=De.transformTo(e,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(n)}}},findExtraFieldUnicodePath:function(){var e=this.extraFields[28789];if(e){var r=ba(e.value);return r.readInt(1)!==1||Ni(this.fileName)!==r.readInt(4)?null:fr.utf8decode(r.readData(e.length-5))}return null},findExtraFieldUnicodeComment:function(){var e=this.extraFields[25461];if(e){var r=ba(e.value);return r.readInt(1)!==1||Ni(this.fileComment)!==r.readInt(4)?null:fr.utf8decode(r.readData(e.length-5))}return null}};var Iu=oo,Lu=so,Re=Y(),ye=We,zu=Iu,$u=te;function lo(e){this.files=[],this.loadOptions=e}lo.prototype={checkSignature:function(e){if(!this.reader.readAndCheckSignature(e)){this.reader.index-=4;var r=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+Re.pretty(r)+", expected "+Re.pretty(e)+")")}},isSignature:function(e,r){var t=this.reader.index;this.reader.setIndex(e);var a=this.reader.readString(4),n=a===r;return this.reader.setIndex(t),n},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var e=this.reader.readData(this.zipCommentLength),r=$u.uint8array?"uint8array":"array",t=Re.transformTo(r,e);this.zipComment=this.loadOptions.decodeFileName(t)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var e=this.zip64EndOfCentralSize-44,r=0,t,a,n;r<e;)t=this.reader.readInt(2),a=this.reader.readInt(4),n=this.reader.readData(a),this.zip64ExtensibleData[t]={id:t,length:a,value:n}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),this.disksCount>1)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var e,r;for(e=0;e<this.files.length;e++)r=this.files[e],this.reader.setIndex(r.localHeaderOffset),this.checkSignature(ye.LOCAL_FILE_HEADER),r.readLocalPart(this.reader),r.handleUTF8(),r.processAttributes()},readCentralDir:function(){var e;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(ye.CENTRAL_FILE_HEADER);)e=new zu({zip64:this.zip64},this.loadOptions),e.readCentralPart(this.reader),this.files.push(e);if(this.centralDirRecords!==this.files.length&&this.centralDirRecords!==0&&this.files.length===0)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var e=this.reader.lastIndexOfSignature(ye.CENTRAL_DIRECTORY_END);if(e<0){var r=!this.isSignature(0,ye.LOCAL_FILE_HEADER);throw r?new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html"):new Error("Corrupted zip: can't find end of central directory")}this.reader.setIndex(e);var t=e;if(this.checkSignature(ye.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===Re.MAX_VALUE_16BITS||this.diskWithCentralDirStart===Re.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===Re.MAX_VALUE_16BITS||this.centralDirRecords===Re.MAX_VALUE_16BITS||this.centralDirSize===Re.MAX_VALUE_32BITS||this.centralDirOffset===Re.MAX_VALUE_32BITS){if(this.zip64=!0,e=this.reader.lastIndexOfSignature(ye.ZIP64_CENTRAL_DIRECTORY_LOCATOR),e<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(e),this.checkSignature(ye.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,ye.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(ye.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(ye.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var a=this.centralDirOffset+this.centralDirSize;this.zip64&&(a+=20,a+=12+this.zip64EndOfCentralSize);var n=t-a;if(n>0)this.isSignature(t,ye.CENTRAL_FILE_HEADER)||(this.reader.zero=n);else if(n<0)throw new Error("Corrupted zip: missing "+Math.abs(n)+" bytes.")},prepareReader:function(e){this.reader=Lu(e)},load:function(e){this.prepareReader(e),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}};var Uu=lo,_a=Y(),gr=jt,ju=mt,Wu=Uu,Hu=rs,Ti=Er;function Zu(e){return new gr.Promise(function(r,t){var a=e.decompressed.getContentWorker().pipe(new Hu);a.on("error",function(n){t(n)}).on("end",function(){a.streamInfo.crc32!==e.decompressed.crc32?t(new Error("Corrupted zip : CRC32 mismatch")):r()}).resume()})}var qu=function(e,r){var t=this;return r=_a.extend(r||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:ju.utf8decode}),Ti.isNode&&Ti.isStream(e)?gr.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):_a.prepareContent("the loaded zip file",e,!0,r.optimizedBinaryString,r.base64).then(function(a){var n=new Wu(r);return n.load(a),n}).then(function(n){var i=[gr.Promise.resolve(n)],s=n.files;if(r.checkCRC32)for(var o=0;o<s.length;o++)i.push(Zu(s[o]));return gr.Promise.all(i)}).then(function(n){for(var i=n.shift(),s=i.files,o=0;o<s.length;o++){var f=s[o],l=f.fileNameStr,u=_a.resolve(f.fileNameStr);t.file(u,f.decompressed,{binary:!0,optimizedBinaryString:!0,date:f.date,dir:f.dir,comment:f.fileCommentStr.length?f.fileCommentStr:null,unixPermissions:f.unixPermissions,dosPermissions:f.dosPermissions,createFolders:r.createFolders}),f.dir||(t.file(u).unsafeOriginalName=l)}return i.zipComment.length&&(t.comment=i.zipComment),t})};function pe(){if(!(this instanceof pe))return new pe;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var e=new pe;for(var r in this)typeof this[r]!="function"&&(e[r]=this[r]);return e}}pe.prototype=yu;pe.prototype.loadAsync=qu;pe.support=te;pe.defaults=ge;pe.version="3.10.1";pe.loadAsync=function(e,r){return new pe().loadAsync(e,r)};pe.external=jt;var Vu=pe;const Xu=Eo(Vu);var J;(function(e){e.OfficeDocument="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",e.FontTable="http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable",e.Image="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",e.Numbering="http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering",e.Styles="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",e.StylesWithEffects="http://schemas.microsoft.com/office/2007/relationships/stylesWithEffects",e.Theme="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",e.Settings="http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings",e.WebSettings="http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings",e.Hyperlink="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",e.Footnotes="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes",e.Endnotes="http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes",e.Footer="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer",e.Header="http://schemas.openxmlformats.org/officeDocument/2006/relationships/header",e.ExtendedProperties="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",e.CoreProperties="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",e.CustomProperties="http://schemas.openxmlformats.org/package/2006/relationships/metadata/custom-properties",e.Comments="http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",e.CommentsExtended="http://schemas.microsoft.com/office/2011/relationships/commentsExtended",e.AltChunk="http://schemas.openxmlformats.org/officeDocument/2006/relationships/aFChunk"})(J||(J={}));function Gu(e,r){return r.elements(e).map(t=>({id:r.attr(t,"Id"),type:r.attr(t,"Type"),target:r.attr(t,"Target"),targetMode:r.attr(t,"TargetMode")}))}function Yu(e){return e==null?void 0:e.replace(/[ .]+/g,"-").replace(/[&]+/g,"and").toLowerCase()}function Ba(e){return/^[^"'].*\s.*[^"']$/.test(e)?`'${e}'`:e}function br(e){let r=e.lastIndexOf("/")+1,t=r==0?"":e.substring(0,r),a=r==0?e:e.substring(r);return[t,a]}function ya(e,r){try{const t="http://docx/";return new URL(e,t+r).toString().substring(t.length)}catch{return`${r}${e}`}}function Le(e,r){return e.reduce((t,a)=>(t[r(a)]=a,t),{})}function Ku(e){return new Promise((r,t)=>{const a=new FileReader;a.onloadend=()=>r(a.result),a.onerror=()=>t(),a.readAsDataURL(e)})}function wa(e){return e&&typeof e=="object"&&!Array.isArray(e)}function Ju(e){return typeof e=="string"||e instanceof String}function Sr(e,...r){if(!r.length)return e;const t=r.shift();if(wa(e)&&wa(t))for(const a in t)if(wa(t[a])){const n=e[a]??(e[a]={});Sr(n,t[a])}else e[a]=t[a];return Sr(e,...r)}function Ct(e){return Array.isArray(e)?e:[e]}function Qu(e,r,t){return r>e?r:t<e?t:e}const fo={wordml:"http://schemas.openxmlformats.org/wordprocessingml/2006/main"},Q={Dxa:{mul:.05,unit:"pt"},Emu:{mul:1/12700,unit:"pt"},FontSize:{mul:.5,unit:"pt"},Border:{mul:.125,unit:"pt",min:.25,max:12},Point:{mul:1,unit:"pt"},Percent:{mul:.02,unit:"%"}};function uo(e,r=Q.Dxa){if(e==null||/.+(p[xt]|[%])$/.test(e))return e;var t=parseInt(e)*r.mul;return r.min&&r.max&&(t=Qu(t,r.min,r.max)),`${t.toFixed(2)}${r.unit}`}function eh(e,r=!1){switch(e){case"1":return!0;case"0":return!1;case"on":return!0;case"off":return!1;case"true":return!0;case"false":return!1;default:return r}}function ho(e,r,t){if(e.namespaceURI!=fo.wordml)return!1;switch(e.localName){case"color":r.color=t.attr(e,"val");break;case"sz":r.fontSize=t.lengthAttr(e,"val",Q.FontSize);break;default:return!1}return!0}function th(e,r=!1){r&&(e=e.replace(/<[?].*[?]>/,"")),e=ah(e);const t=new DOMParser().parseFromString(e,"application/xml"),a=rh(t);if(a)throw new Error(a);return t}function rh(e){var r;return(r=e.getElementsByTagName("parsererror")[0])==null?void 0:r.textContent}function ah(e){return e.charCodeAt(0)===65279?e.substring(1):e}function nh(e){return new XMLSerializer().serializeToString(e)}class co{elements(r,t=null){const a=[];for(let n=0,i=r.childNodes.length;n<i;n++){let s=r.childNodes.item(n);s.nodeType==1&&(t==null||s.localName==t)&&a.push(s)}return a}element(r,t){for(let a=0,n=r.childNodes.length;a<n;a++){let i=r.childNodes.item(a);if(i.nodeType==1&&i.localName==t)return i}return null}elementAttr(r,t,a){var n=this.element(r,t);return n?this.attr(n,a):void 0}attrs(r){return Array.from(r.attributes)}attr(r,t){for(let a=0,n=r.attributes.length;a<n;a++){let i=r.attributes.item(a);if(i.localName==t)return i.value}return null}intAttr(r,t,a=null){var n=this.attr(r,t);return n?parseInt(n):a}hexAttr(r,t,a=null){var n=this.attr(r,t);return n?parseInt(n,16):a}floatAttr(r,t,a=null){var n=this.attr(r,t);return n?parseFloat(n):a}boolAttr(r,t,a=null){return eh(this.attr(r,t),a)}lengthAttr(r,t,a=Q.Dxa){return uo(this.attr(r,t),a)}}const _=new co;class ue{constructor(r,t){this._package=r,this.path=t}async load(){this.rels=await this._package.loadRelationships(this.path);const r=await this._package.load(this.path),t=this._package.parseXmlDocument(r);this._package.options.keepOrigin&&(this._xmlDocument=t),this.parseXml(t.firstElementChild)}save(){this._package.update(this.path,nh(this._xmlDocument))}parseXml(r){}}const ih={embedRegular:"regular",embedBold:"bold",embedItalic:"italic",embedBoldItalic:"boldItalic"};function sh(e,r){return r.elements(e).map(t=>oh(t,r))}function oh(e,r){let t={name:r.attr(e,"name"),embedFontRefs:[]};for(let a of r.elements(e))switch(a.localName){case"family":t.family=r.attr(a,"val");break;case"altName":t.altName=r.attr(a,"val");break;case"embedRegular":case"embedBold":case"embedItalic":case"embedBoldItalic":t.embedFontRefs.push(lh(a,r));break}return t}function lh(e,r){return{id:r.attr(e,"id"),key:r.attr(e,"fontKey"),type:ih[e.localName]}}class fh extends ue{parseXml(r){this.fonts=sh(r,this._package.xmlParser)}}class Ja{constructor(r,t){this._zip=r,this.options=t,this.xmlParser=new co}get(r){const t=uh(r);return this._zip.files[t]??this._zip.files[t.replace(/\//g,"\\")]}update(r,t){this._zip.file(r,t)}static async load(r,t){const a=await Xu.loadAsync(r);return new Ja(a,t)}save(r="blob"){return this._zip.generateAsync({type:r})}load(r,t="string"){var a;return((a=this.get(r))==null?void 0:a.async(t))??Promise.resolve(null)}async loadRelationships(r=null){let t="_rels/.rels";if(r!=null){const[n,i]=br(r);t=`${n}_rels/${i}.rels`}const a=await this.load(t);return a?Gu(this.parseXmlDocument(a).firstElementChild,this.xmlParser):null}parseXmlDocument(r){return th(r,this.options.trimXmlDeclaration)}}function uh(e){return e.startsWith("/")?e.substr(1):e}class hh extends ue{constructor(r,t,a){super(r,t),this._documentParser=a}parseXml(r){this.body=this._documentParser.parseDocumentFile(r)}}function hr(e,r){return{type:r.attr(e,"val"),color:r.attr(e,"color"),size:r.lengthAttr(e,"sz",Q.Border),offset:r.lengthAttr(e,"space",Q.Point),frame:r.boolAttr(e,"frame"),shadow:r.boolAttr(e,"shadow")}}function ch(e,r){var t={};for(let a of r.elements(e))switch(a.localName){case"left":t.left=hr(a,r);break;case"top":t.top=hr(a,r);break;case"right":t.right=hr(a,r);break;case"bottom":t.bottom=hr(a,r);break}return t}var Ri;(function(e){e.Continuous="continuous",e.NextPage="nextPage",e.NextColumn="nextColumn",e.EvenPage="evenPage",e.OddPage="oddPage"})(Ri||(Ri={}));function po(e,r=_){var t={};for(let a of r.elements(e))switch(a.localName){case"pgSz":t.pageSize={width:r.lengthAttr(a,"w"),height:r.lengthAttr(a,"h"),orientation:r.attr(a,"orient")};break;case"type":t.type=r.attr(a,"val");break;case"pgMar":t.pageMargins={left:r.lengthAttr(a,"left"),right:r.lengthAttr(a,"right"),top:r.lengthAttr(a,"top"),bottom:r.lengthAttr(a,"bottom"),header:r.lengthAttr(a,"header"),footer:r.lengthAttr(a,"footer"),gutter:r.lengthAttr(a,"gutter")};break;case"cols":t.columns=dh(a,r);break;case"headerReference":(t.headerRefs??(t.headerRefs=[])).push(Mi(a,r));break;case"footerReference":(t.footerRefs??(t.footerRefs=[])).push(Mi(a,r));break;case"titlePg":t.titlePage=r.boolAttr(a,"val",!0);break;case"pgBorders":t.pageBorders=ch(a,r);break;case"pgNumType":t.pageNumber=ph(a,r);break}return t}function dh(e,r){return{numberOfColumns:r.intAttr(e,"num"),space:r.lengthAttr(e,"space"),separator:r.boolAttr(e,"sep"),equalWidth:r.boolAttr(e,"equalWidth",!0),columns:r.elements(e,"col").map(t=>({width:r.lengthAttr(t,"w"),space:r.lengthAttr(t,"space")}))}}function ph(e,r){return{chapSep:r.attr(e,"chapSep"),chapStyle:r.attr(e,"chapStyle"),format:r.attr(e,"fmt"),start:r.intAttr(e,"start")}}function Mi(e,r){return{id:r.attr(e,"id"),type:r.attr(e,"type")}}function mh(e,r){return{before:r.lengthAttr(e,"before"),after:r.lengthAttr(e,"after"),line:r.intAttr(e,"line"),lineRule:r.attr(e,"lineRule")}}function Qa(e,r){let t={};for(let a of r.elements(e))vh(a,t,r);return t}function vh(e,r,t){return!!ho(e,r,t)}function mo(e,r){let t={};for(let a of r.elements(e))vo(a,t,r);return t}function vo(e,r,t){if(e.namespaceURI!=fo.wordml)return!1;if(ho(e,r,t))return!0;switch(e.localName){case"tabs":r.tabs=gh(e,t);break;case"sectPr":r.sectionProps=po(e,t);break;case"numPr":r.numbering=bh(e,t);break;case"spacing":return r.lineSpacing=mh(e,t),!1;case"textAlignment":return r.textAlignment=t.attr(e,"val"),!1;case"keepLines":r.keepLines=t.boolAttr(e,"val",!0);break;case"keepNext":r.keepNext=t.boolAttr(e,"val",!0);break;case"pageBreakBefore":r.pageBreakBefore=t.boolAttr(e,"val",!0);break;case"outlineLvl":r.outlineLevel=t.intAttr(e,"val");break;case"pStyle":r.styleName=t.attr(e,"val");break;case"rPr":r.runProps=Qa(e,t);break;default:return!1}return!0}function gh(e,r){return r.elements(e,"tab").map(t=>({position:r.lengthAttr(t,"pos"),leader:r.attr(t,"leader"),style:r.attr(t,"val")}))}function bh(e,r){var t={};for(let a of r.elements(e))switch(a.localName){case"numId":t.id=r.attr(a,"val");break;case"ilvl":t.level=r.intAttr(a,"val");break}return t}function _h(e,r){let t={numberings:[],abstractNumberings:[],bulletPictures:[]};for(let a of r.elements(e))switch(a.localName){case"num":t.numberings.push(yh(a,r));break;case"abstractNum":t.abstractNumberings.push(wh(a,r));break;case"numPicBullet":t.bulletPictures.push(Sh(a,r));break}return t}function yh(e,r){let t={id:r.attr(e,"numId"),overrides:[]};for(let a of r.elements(e))switch(a.localName){case"abstractNumId":t.abstractId=r.attr(a,"val");break;case"lvlOverride":t.overrides.push(kh(a,r));break}return t}function wh(e,r){let t={id:r.attr(e,"abstractNumId"),levels:[]};for(let a of r.elements(e))switch(a.localName){case"name":t.name=r.attr(a,"val");break;case"multiLevelType":t.multiLevelType=r.attr(a,"val");break;case"numStyleLink":t.numberingStyleLink=r.attr(a,"val");break;case"styleLink":t.styleLink=r.attr(a,"val");break;case"lvl":t.levels.push(go(a,r));break}return t}function go(e,r){let t={level:r.intAttr(e,"ilvl")};for(let a of r.elements(e))switch(a.localName){case"start":t.start=r.attr(a,"val");break;case"lvlRestart":t.restart=r.intAttr(a,"val");break;case"numFmt":t.format=r.attr(a,"val");break;case"lvlText":t.text=r.attr(a,"val");break;case"lvlJc":t.justification=r.attr(a,"val");break;case"lvlPicBulletId":t.bulletPictureId=r.attr(a,"val");break;case"pStyle":t.paragraphStyle=r.attr(a,"val");break;case"pPr":t.paragraphProps=mo(a,r);break;case"rPr":t.runProps=Qa(a,r);break}return t}function kh(e,r){let t={level:r.intAttr(e,"ilvl")};for(let a of r.elements(e))switch(a.localName){case"startOverride":t.start=r.intAttr(a,"val");break;case"lvl":t.numberingLevel=go(a,r);break}return t}function Sh(e,r){var t=r.element(e,"pict"),a=t&&r.element(t,"shape"),n=a&&r.element(a,"imagedata");return n?{id:r.attr(e,"numPicBulletId"),referenceId:r.attr(n,"id"),style:r.attr(a,"style")}:null}class xh extends ue{constructor(r,t,a){super(r,t),this._documentParser=a}parseXml(r){Object.assign(this,_h(r,this._package.xmlParser)),this.domNumberings=this._documentParser.parseNumberingFile(r)}}class Eh extends ue{constructor(r,t,a){super(r,t),this._documentParser=a}parseXml(r){this.styles=this._documentParser.parseStylesFile(r)}}var k;(function(e){e.Document="document",e.Paragraph="paragraph",e.Run="run",e.Break="break",e.NoBreakHyphen="noBreakHyphen",e.Table="table",e.Row="row",e.Cell="cell",e.Hyperlink="hyperlink",e.SmartTag="smartTag",e.Drawing="drawing",e.Image="image",e.Text="text",e.Tab="tab",e.Symbol="symbol",e.BookmarkStart="bookmarkStart",e.BookmarkEnd="bookmarkEnd",e.Footer="footer",e.Header="header",e.FootnoteReference="footnoteReference",e.EndnoteReference="endnoteReference",e.Footnote="footnote",e.Endnote="endnote",e.SimpleField="simpleField",e.ComplexField="complexField",e.Instruction="instruction",e.VmlPicture="vmlPicture",e.MmlMath="mmlMath",e.MmlMathParagraph="mmlMathParagraph",e.MmlFraction="mmlFraction",e.MmlFunction="mmlFunction",e.MmlFunctionName="mmlFunctionName",e.MmlNumerator="mmlNumerator",e.MmlDenominator="mmlDenominator",e.MmlRadical="mmlRadical",e.MmlBase="mmlBase",e.MmlDegree="mmlDegree",e.MmlSuperscript="mmlSuperscript",e.MmlSubscript="mmlSubscript",e.MmlPreSubSuper="mmlPreSubSuper",e.MmlSubArgument="mmlSubArgument",e.MmlSuperArgument="mmlSuperArgument",e.MmlNary="mmlNary",e.MmlDelimiter="mmlDelimiter",e.MmlRun="mmlRun",e.MmlEquationArray="mmlEquationArray",e.MmlLimit="mmlLimit",e.MmlLimitLower="mmlLimitLower",e.MmlMatrix="mmlMatrix",e.MmlMatrixRow="mmlMatrixRow",e.MmlBox="mmlBox",e.MmlBar="mmlBar",e.MmlGroupChar="mmlGroupChar",e.VmlElement="vmlElement",e.Inserted="inserted",e.Deleted="deleted",e.DeletedText="deletedText",e.Comment="comment",e.CommentReference="commentReference",e.CommentRangeStart="commentRangeStart",e.CommentRangeEnd="commentRangeEnd",e.AltChunk="altChunk"})(k||(k={}));class at{constructor(){this.children=[],this.cssStyle={}}}class Ch extends at{constructor(){super(...arguments),this.type=k.Header}}class Ah extends at{constructor(){super(...arguments),this.type=k.Footer}}class bo extends ue{constructor(r,t,a){super(r,t),this._documentParser=a}parseXml(r){this.rootElement=this.createRootElement(),this.rootElement.children=this._documentParser.parseBodyElements(r)}}class Ph extends bo{createRootElement(){return new Ch}}class Nh extends bo{createRootElement(){return new Ah}}function Th(e,r){const t={};for(let a of r.elements(e))switch(a.localName){case"Template":t.template=a.textContent;break;case"Pages":t.pages=At(a.textContent);break;case"Words":t.words=At(a.textContent);break;case"Characters":t.characters=At(a.textContent);break;case"Application":t.application=a.textContent;break;case"Lines":t.lines=At(a.textContent);break;case"Paragraphs":t.paragraphs=At(a.textContent);break;case"Company":t.company=a.textContent;break;case"AppVersion":t.appVersion=a.textContent;break}return t}function At(e){if(!(typeof e>"u"))return parseInt(e)}class Rh extends ue{parseXml(r){this.props=Th(r,this._package.xmlParser)}}function Mh(e,r){const t={};for(let a of r.elements(e))switch(a.localName){case"title":t.title=a.textContent;break;case"description":t.description=a.textContent;break;case"subject":t.subject=a.textContent;break;case"creator":t.creator=a.textContent;break;case"keywords":t.keywords=a.textContent;break;case"language":t.language=a.textContent;break;case"lastModifiedBy":t.lastModifiedBy=a.textContent;break;case"revision":a.textContent&&(t.revision=parseInt(a.textContent));break}return t}class Bh extends ue{parseXml(r){this.props=Mh(r,this._package.xmlParser)}}class Oh{}function Dh(e,r){var t=new Oh,a=r.element(e,"themeElements");for(let n of r.elements(a))switch(n.localName){case"clrScheme":t.colorScheme=Fh(n,r);break;case"fontScheme":t.fontScheme=Ih(n,r);break}return t}function Fh(e,r){var t={name:r.attr(e,"name"),colors:{}};for(let i of r.elements(e)){var a=r.element(i,"srgbClr"),n=r.element(i,"sysClr");a?t.colors[i.localName]=r.attr(a,"val"):n&&(t.colors[i.localName]=r.attr(n,"lastClr"))}return t}function Ih(e,r){var t={name:r.attr(e,"name")};for(let a of r.elements(e))switch(a.localName){case"majorFont":t.majorFont=Bi(a,r);break;case"minorFont":t.minorFont=Bi(a,r);break}return t}function Bi(e,r){return{latinTypeface:r.elementAttr(e,"latin","typeface"),eaTypeface:r.elementAttr(e,"ea","typeface"),csTypeface:r.elementAttr(e,"cs","typeface")}}class Lh extends ue{constructor(r,t){super(r,t)}parseXml(r){this.theme=Dh(r,this._package.xmlParser)}}class _o{}class zh extends _o{constructor(){super(...arguments),this.type=k.Footnote}}class $h extends _o{constructor(){super(...arguments),this.type=k.Endnote}}class yo extends ue{constructor(r,t,a){super(r,t),this._documentParser=a}}class Uh extends yo{constructor(r,t,a){super(r,t,a)}parseXml(r){this.notes=this._documentParser.parseNotes(r,"footnote",zh)}}class jh extends yo{constructor(r,t,a){super(r,t,a)}parseXml(r){this.notes=this._documentParser.parseNotes(r,"endnote",$h)}}function Wh(e,r){var t={};for(let a of r.elements(e))switch(a.localName){case"defaultTabStop":t.defaultTabStop=r.lengthAttr(a,"val");break;case"footnotePr":t.footnoteProps=Oi(a,r);break;case"endnotePr":t.endnoteProps=Oi(a,r);break;case"autoHyphenation":t.autoHyphenation=r.boolAttr(a,"val");break}return t}function Oi(e,r){var t={defaultNoteIds:[]};for(let a of r.elements(e))switch(a.localName){case"numFmt":t.nummeringFormat=r.attr(a,"val");break;case"footnote":case"endnote":t.defaultNoteIds.push(r.attr(a,"id"));break}return t}class Hh extends ue{constructor(r,t){super(r,t)}parseXml(r){this.settings=Wh(r,this._package.xmlParser)}}function Zh(e,r){return r.elements(e,"property").map(t=>{const a=t.firstChild;return{formatId:r.attr(t,"fmtid"),name:r.attr(t,"name"),type:a.nodeName,value:a.textContent}})}class qh extends ue{parseXml(r){this.props=Zh(r,this._package.xmlParser)}}class Vh extends ue{constructor(r,t,a){super(r,t),this._documentParser=a}parseXml(r){this.comments=this._documentParser.parseComments(r),this.commentMap=Le(this.comments,t=>t.id)}}class Xh extends ue{constructor(r,t){super(r,t),this.comments=[]}parseXml(r){const t=this._package.xmlParser;for(let a of t.elements(r,"commentEx"))this.comments.push({paraId:t.attr(a,"paraId"),paraIdParent:t.attr(a,"paraIdParent"),done:t.boolAttr(a,"done")});this.commentMap=Le(this.comments,a=>a.paraId)}}const Gh=[{type:J.OfficeDocument,target:"word/document.xml"},{type:J.ExtendedProperties,target:"docProps/app.xml"},{type:J.CoreProperties,target:"docProps/core.xml"},{type:J.CustomProperties,target:"docProps/custom.xml"}];class en{constructor(){this.parts=[],this.partsMap={}}static async load(r,t,a){var n=new en;return n._options=a,n._parser=t,n._package=await Ja.load(r,a),n.rels=await n._package.loadRelationships(),await Promise.all(Gh.map(i=>{const s=n.rels.find(o=>o.type===i.type)??i;return n.loadRelationshipPart(s.target,s.type)})),n}save(r="blob"){return this._package.save(r)}async loadRelationshipPart(r,t){var n;if(this.partsMap[r])return this.partsMap[r];if(!this._package.get(r))return null;let a=null;switch(t){case J.OfficeDocument:this.documentPart=a=new hh(this._package,r,this._parser);break;case J.FontTable:this.fontTablePart=a=new fh(this._package,r);break;case J.Numbering:this.numberingPart=a=new xh(this._package,r,this._parser);break;case J.Styles:this.stylesPart=a=new Eh(this._package,r,this._parser);break;case J.Theme:this.themePart=a=new Lh(this._package,r);break;case J.Footnotes:this.footnotesPart=a=new Uh(this._package,r,this._parser);break;case J.Endnotes:this.endnotesPart=a=new jh(this._package,r,this._parser);break;case J.Footer:a=new Nh(this._package,r,this._parser);break;case J.Header:a=new Ph(this._package,r,this._parser);break;case J.CoreProperties:this.corePropsPart=a=new Bh(this._package,r);break;case J.ExtendedProperties:this.extendedPropsPart=a=new Rh(this._package,r);break;case J.CustomProperties:a=new qh(this._package,r);break;case J.Settings:this.settingsPart=a=new Hh(this._package,r);break;case J.Comments:this.commentsPart=a=new Vh(this._package,r,this._parser);break;case J.CommentsExtended:this.commentsExtendedPart=a=new Xh(this._package,r);break}if(a==null)return Promise.resolve(null);if(this.partsMap[r]=a,this.parts.push(a),await a.load(),((n=a.rels)==null?void 0:n.length)>0){const[i]=br(a.path);await Promise.all(a.rels.map(s=>this.loadRelationshipPart(ya(s.target,i),s.type)))}return a}async loadDocumentImage(r,t){const a=await this.loadResource(t??this.documentPart,r,"blob");return this.blobToURL(a)}async loadNumberingImage(r){const t=await this.loadResource(this.numberingPart,r,"blob");return this.blobToURL(t)}async loadFont(r,t){const a=await this.loadResource(this.fontTablePart,r,"uint8array");return a&&this.blobToURL(new Blob([Yh(a,t)]))}async loadAltChunk(r,t){return await this.loadResource(t??this.documentPart,r,"string")}blobToURL(r){return r?this._options.useBase64URL?Ku(r):URL.createObjectURL(r):null}findPartByRelId(r,t=null){var a=(t.rels??this.rels).find(i=>i.id==r);const n=t?br(t.path)[0]:"";return a?this.partsMap[ya(a.target,n)]:null}getPathById(r,t){const a=r.rels.find(i=>i.id==t),[n]=br(r.path);return a?ya(a.target,n):null}loadResource(r,t,a){const n=this.getPathById(r,t);return n?this._package.load(n,a):Promise.resolve(null)}}function Yh(e,r){const a=r.replace(/{|}|-/g,""),n=new Array(16);for(let i=0;i<16;i++)n[16-i-1]=parseInt(a.substr(i*2,2),16);for(let i=0;i<32;i++)e[i]=e[i]^n[i%16];return e}function Kh(e,r){return{type:k.BookmarkStart,id:r.attr(e,"id"),name:r.attr(e,"name"),colFirst:r.intAttr(e,"colFirst"),colLast:r.intAttr(e,"colLast")}}function Jh(e,r){return{type:k.BookmarkEnd,id:r.attr(e,"id")}}class Qh extends at{constructor(){super(...arguments),this.type=k.VmlElement,this.attrs={}}}function wo(e,r){var t=new Qh;switch(e.localName){case"rect":t.tagName="rect",Object.assign(t.attrs,{width:"100%",height:"100%"});break;case"oval":t.tagName="ellipse",Object.assign(t.attrs,{cx:"50%",cy:"50%",rx:"50%",ry:"50%"});break;case"line":t.tagName="line";break;case"shape":t.tagName="g";break;case"textbox":t.tagName="foreignObject",Object.assign(t.attrs,{width:"100%",height:"100%"});break;default:return null}for(const a of _.attrs(e))switch(a.localName){case"style":t.cssStyleText=a.value;break;case"fillcolor":t.attrs.fill=a.value;break;case"from":const[n,i]=Di(a.value);Object.assign(t.attrs,{x1:n,y1:i});break;case"to":const[s,o]=Di(a.value);Object.assign(t.attrs,{x2:s,y2:o});break}for(const a of _.elements(e))switch(a.localName){case"stroke":Object.assign(t.attrs,ec(a));break;case"fill":Object.assign(t.attrs,tc());break;case"imagedata":t.tagName="image",Object.assign(t.attrs,{width:"100%",height:"100%"}),t.imageHref={id:_.attr(a,"id"),title:_.attr(a,"title")};break;case"txbxContent":t.children.push(...r.parseBodyElements(a));break;default:const n=wo(a,r);n&&t.children.push(n);break}return t}function ec(e){return{stroke:_.attr(e,"color"),"stroke-width":_.lengthAttr(e,"weight",Q.Emu)??"1px"}}function tc(e){return{}}function Di(e){return e.split(",")}class rc extends at{constructor(){super(...arguments),this.type=k.Comment}}class ac extends at{constructor(r){super(),this.id=r,this.type=k.CommentReference}}class nc extends at{constructor(r){super(),this.id=r,this.type=k.CommentRangeStart}}class ic extends at{constructor(r){super(),this.id=r,this.type=k.CommentRangeEnd}}var _r={shd:"inherit",color:"black",borderColor:"black",highlight:"transparent"};const sc=[],Fi={oMath:k.MmlMath,oMathPara:k.MmlMathParagraph,f:k.MmlFraction,func:k.MmlFunction,fName:k.MmlFunctionName,num:k.MmlNumerator,den:k.MmlDenominator,rad:k.MmlRadical,deg:k.MmlDegree,e:k.MmlBase,sSup:k.MmlSuperscript,sSub:k.MmlSubscript,sPre:k.MmlPreSubSuper,sup:k.MmlSuperArgument,sub:k.MmlSubArgument,d:k.MmlDelimiter,nary:k.MmlNary,eqArr:k.MmlEquationArray,lim:k.MmlLimit,limLow:k.MmlLimitLower,m:k.MmlMatrix,mr:k.MmlMatrixRow,box:k.MmlBox,bar:k.MmlBar,groupChr:k.MmlGroupChar};class oc{constructor(r){this.options={ignoreWidth:!1,debug:!1,...r}}parseNotes(r,t,a){var n=[];for(let i of _.elements(r,t)){const s=new a;s.id=_.attr(i,"id"),s.noteType=_.attr(i,"type"),s.children=this.parseBodyElements(i),n.push(s)}return n}parseComments(r){var t=[];for(let a of _.elements(r,"comment")){const n=new rc;n.id=_.attr(a,"id"),n.author=_.attr(a,"author"),n.initials=_.attr(a,"initials"),n.date=_.attr(a,"date"),n.children=this.parseBodyElements(a),t.push(n)}return t}parseDocumentFile(r){var t=_.element(r,"body"),a=_.element(r,"background"),n=_.element(t,"sectPr");return{type:k.Document,children:this.parseBodyElements(t),props:n?po(n,_):{},cssStyle:a?this.parseBackground(a):{}}}parseBackground(r){var t={},a=X.colorAttr(r,"color");return a&&(t["background-color"]=a),t}parseBodyElements(r){var t=[];for(let a of _.elements(r))switch(a.localName){case"p":t.push(this.parseParagraph(a));break;case"altChunk":t.push(this.parseAltChunk(a));break;case"tbl":t.push(this.parseTable(a));break;case"sdt":t.push(...this.parseSdt(a,n=>this.parseBodyElements(n)));break}return t}parseStylesFile(r){var t=[];return X.foreach(r,a=>{switch(a.localName){case"style":t.push(this.parseStyle(a));break;case"docDefaults":t.push(this.parseDefaultStyles(a));break}}),t}parseDefaultStyles(r){var t={id:null,name:null,target:null,basedOn:null,styles:[]};return X.foreach(r,a=>{switch(a.localName){case"rPrDefault":var n=_.element(a,"rPr");n&&t.styles.push({target:"span",values:this.parseDefaultProperties(n,{})});break;case"pPrDefault":var i=_.element(a,"pPr");i&&t.styles.push({target:"p",values:this.parseDefaultProperties(i,{})});break}}),t}parseStyle(r){var t={id:_.attr(r,"styleId"),isDefault:_.boolAttr(r,"default"),name:null,target:null,basedOn:null,styles:[],linked:null};switch(_.attr(r,"type")){case"paragraph":t.target="p";break;case"table":t.target="table";break;case"character":t.target="span";break}return X.foreach(r,a=>{switch(a.localName){case"basedOn":t.basedOn=_.attr(a,"val");break;case"name":t.name=_.attr(a,"val");break;case"link":t.linked=_.attr(a,"val");break;case"next":t.next=_.attr(a,"val");break;case"aliases":t.aliases=_.attr(a,"val").split(",");break;case"pPr":t.styles.push({target:"p",values:this.parseDefaultProperties(a,{})}),t.paragraphProps=mo(a,_);break;case"rPr":t.styles.push({target:"span",values:this.parseDefaultProperties(a,{})}),t.runProps=Qa(a,_);break;case"tblPr":case"tcPr":t.styles.push({target:"td",values:this.parseDefaultProperties(a,{})});break;case"tblStylePr":for(let n of this.parseTableStyle(a))t.styles.push(n);break;case"rsid":case"qFormat":case"hidden":case"semiHidden":case"unhideWhenUsed":case"autoRedefine":case"uiPriority":break;default:this.options.debug&&console.warn(`DOCX: Unknown style element: ${a.localName}`)}}),t}parseTableStyle(r){var t=[],a=_.attr(r,"type"),n="",i="";switch(a){case"firstRow":i=".first-row",n="tr.first-row td";break;case"lastRow":i=".last-row",n="tr.last-row td";break;case"firstCol":i=".first-col",n="td.first-col";break;case"lastCol":i=".last-col",n="td.last-col";break;case"band1Vert":i=":not(.no-vband)",n="td.odd-col";break;case"band2Vert":i=":not(.no-vband)",n="td.even-col";break;case"band1Horz":i=":not(.no-hband)",n="tr.odd-row";break;case"band2Horz":i=":not(.no-hband)",n="tr.even-row";break;default:return[]}return X.foreach(r,s=>{switch(s.localName){case"pPr":t.push({target:`${n} p`,mod:i,values:this.parseDefaultProperties(s,{})});break;case"rPr":t.push({target:`${n} span`,mod:i,values:this.parseDefaultProperties(s,{})});break;case"tblPr":case"tcPr":t.push({target:n,mod:i,values:this.parseDefaultProperties(s,{})});break}}),t}parseNumberingFile(r){var t=[],a={},n=[];return X.foreach(r,i=>{switch(i.localName){case"abstractNum":this.parseAbstractNumbering(i,n).forEach(f=>t.push(f));break;case"numPicBullet":n.push(this.parseNumberingPicBullet(i));break;case"num":var s=_.attr(i,"numId"),o=_.elementAttr(i,"abstractNumId","val");a[o]=s;break}}),t.forEach(i=>i.id=a[i.id]),t}parseNumberingPicBullet(r){var t=_.element(r,"pict"),a=t&&_.element(t,"shape"),n=a&&_.element(a,"imagedata");return n?{id:_.intAttr(r,"numPicBulletId"),src:_.attr(n,"id"),style:_.attr(a,"style")}:null}parseAbstractNumbering(r,t){var a=[],n=_.attr(r,"abstractNumId");return X.foreach(r,i=>{switch(i.localName){case"lvl":a.push(this.parseNumberingLevel(n,i,t));break}}),a}parseNumberingLevel(r,t,a){var n={id:r,level:_.intAttr(t,"ilvl"),start:1,pStyleName:void 0,pStyle:{},rStyle:{},suff:"tab"};return X.foreach(t,i=>{switch(i.localName){case"start":n.start=_.intAttr(i,"val");break;case"pPr":this.parseDefaultProperties(i,n.pStyle);break;case"rPr":this.parseDefaultProperties(i,n.rStyle);break;case"lvlPicBulletId":var s=_.intAttr(i,"val");n.bullet=a.find(o=>(o==null?void 0:o.id)==s);break;case"lvlText":n.levelText=_.attr(i,"val");break;case"pStyle":n.pStyleName=_.attr(i,"val");break;case"numFmt":n.format=_.attr(i,"val");break;case"suff":n.suff=_.attr(i,"val");break}}),n}parseSdt(r,t){const a=_.element(r,"sdtContent");return a?t(a):[]}parseInserted(r,t){var a;return{type:k.Inserted,children:((a=t(r))==null?void 0:a.children)??[]}}parseDeleted(r,t){var a;return{type:k.Deleted,children:((a=t(r))==null?void 0:a.children)??[]}}parseAltChunk(r){return{type:k.AltChunk,children:[],id:_.attr(r,"id")}}parseParagraph(r){var t={type:k.Paragraph,children:[]};for(let a of _.elements(r))switch(a.localName){case"pPr":this.parseParagraphProperties(a,t);break;case"r":t.children.push(this.parseRun(a,t));break;case"hyperlink":t.children.push(this.parseHyperlink(a,t));break;case"smartTag":t.children.push(this.parseSmartTag(a,t));break;case"bookmarkStart":t.children.push(Kh(a,_));break;case"bookmarkEnd":t.children.push(Jh(a,_));break;case"commentRangeStart":t.children.push(new nc(_.attr(a,"id")));break;case"commentRangeEnd":t.children.push(new ic(_.attr(a,"id")));break;case"oMath":case"oMathPara":t.children.push(this.parseMathElement(a));break;case"sdt":t.children.push(...this.parseSdt(a,n=>this.parseParagraph(n).children));break;case"ins":t.children.push(this.parseInserted(a,n=>this.parseParagraph(n)));break;case"del":t.children.push(this.parseDeleted(a,n=>this.parseParagraph(n)));break}return t}parseParagraphProperties(r,t){this.parseDefaultProperties(r,t.cssStyle={},null,a=>{if(vo(a,t,_))return!0;switch(a.localName){case"pStyle":t.styleName=_.attr(a,"val");break;case"cnfStyle":t.className=V.classNameOfCnfStyle(a);break;case"framePr":this.parseFrame(a,t);break;case"rPr":break;default:return!1}return!0})}parseFrame(r,t){var a=_.attr(r,"dropCap");a=="drop"&&(t.cssStyle.float="left")}parseHyperlink(r,t){var a={type:k.Hyperlink,parent:t,children:[]};return a.anchor=_.attr(r,"anchor"),a.id=_.attr(r,"id"),X.foreach(r,n=>{switch(n.localName){case"r":a.children.push(this.parseRun(n,a));break}}),a}parseSmartTag(r,t){var a={type:k.SmartTag,parent:t,children:[]},n=_.attr(r,"uri"),i=_.attr(r,"element");return n&&(a.uri=n),i&&(a.element=i),X.foreach(r,s=>{switch(s.localName){case"r":a.children.push(this.parseRun(s,a));break}}),a}parseRun(r,t){var a={type:k.Run,parent:t,children:[]};return X.foreach(r,n=>{switch(n=this.checkAlternateContent(n),n.localName){case"t":a.children.push({type:k.Text,text:n.textContent});break;case"delText":a.children.push({type:k.DeletedText,text:n.textContent});break;case"commentReference":a.children.push(new ac(_.attr(n,"id")));break;case"fldSimple":a.children.push({type:k.SimpleField,instruction:_.attr(n,"instr"),lock:_.boolAttr(n,"lock",!1),dirty:_.boolAttr(n,"dirty",!1)});break;case"instrText":a.fieldRun=!0,a.children.push({type:k.Instruction,text:n.textContent});break;case"fldChar":a.fieldRun=!0,a.children.push({type:k.ComplexField,charType:_.attr(n,"fldCharType"),lock:_.boolAttr(n,"lock",!1),dirty:_.boolAttr(n,"dirty",!1)});break;case"noBreakHyphen":a.children.push({type:k.NoBreakHyphen});break;case"br":a.children.push({type:k.Break,break:_.attr(n,"type")||"textWrapping"});break;case"lastRenderedPageBreak":a.children.push({type:k.Break,break:"lastRenderedPageBreak"});break;case"sym":a.children.push({type:k.Symbol,font:Ba(_.attr(n,"font")),char:_.attr(n,"char")});break;case"tab":a.children.push({type:k.Tab});break;case"footnoteReference":a.children.push({type:k.FootnoteReference,id:_.attr(n,"id")});break;case"endnoteReference":a.children.push({type:k.EndnoteReference,id:_.attr(n,"id")});break;case"drawing":let i=this.parseDrawing(n);i&&(a.children=[i]);break;case"pict":a.children.push(this.parseVmlPicture(n));break;case"rPr":this.parseRunProperties(n,a);break}}),a}parseMathElement(r){const t=`${r.localName}Pr`,a={type:Fi[r.localName],children:[]};for(const i of _.elements(r))if(Fi[i.localName])a.children.push(this.parseMathElement(i));else if(i.localName=="r"){var n=this.parseRun(i);n.type=k.MmlRun,a.children.push(n)}else i.localName==t&&(a.props=this.parseMathProperies(i));return a}parseMathProperies(r){const t={};for(const a of _.elements(r))switch(a.localName){case"chr":t.char=_.attr(a,"val");break;case"vertJc":t.verticalJustification=_.attr(a,"val");break;case"pos":t.position=_.attr(a,"val");break;case"degHide":t.hideDegree=_.boolAttr(a,"val");break;case"begChr":t.beginChar=_.attr(a,"val");break;case"endChr":t.endChar=_.attr(a,"val");break}return t}parseRunProperties(r,t){this.parseDefaultProperties(r,t.cssStyle={},null,a=>{switch(a.localName){case"rStyle":t.styleName=_.attr(a,"val");break;case"vertAlign":t.verticalAlign=V.valueOfVertAlign(a,!0);break;default:return!1}return!0})}parseVmlPicture(r){const t={type:k.VmlPicture,children:[]};for(const a of _.elements(r)){const n=wo(a,this);n&&t.children.push(n)}return t}checkAlternateContent(r){var i;if(r.localName!="AlternateContent")return r;var t=_.element(r,"Choice");if(t){var a=_.attr(t,"Requires"),n=r.lookupNamespaceURI(a);if(sc.includes(n))return t.firstElementChild}return(i=_.element(r,"Fallback"))==null?void 0:i.firstElementChild}parseDrawing(r){for(var t of _.elements(r))switch(t.localName){case"inline":case"anchor":return this.parseDrawingWrapper(t)}}parseDrawingWrapper(r){var t={type:k.Drawing,children:[],cssStyle:{}},a=r.localName=="anchor";let n=null,i=_.boolAttr(r,"simplePos");_.boolAttr(r,"behindDoc");let s={relative:"page",align:"left",offset:"0"},o={relative:"page",align:"top",offset:"0"};for(var f of _.elements(r))switch(f.localName){case"simplePos":i&&(s.offset=_.lengthAttr(f,"x",Q.Emu),o.offset=_.lengthAttr(f,"y",Q.Emu));break;case"extent":t.cssStyle.width=_.lengthAttr(f,"cx",Q.Emu),t.cssStyle.height=_.lengthAttr(f,"cy",Q.Emu);break;case"positionH":case"positionV":if(!i){let c=f.localName=="positionH"?s:o;var l=_.element(f,"align"),u=_.element(f,"posOffset");c.relative=_.attr(f,"relativeFrom")??c.relative,l&&(c.align=l.textContent),u&&(c.offset=X.sizeValue(u,Q.Emu))}break;case"wrapTopAndBottom":n="wrapTopAndBottom";break;case"wrapNone":n="wrapNone";break;case"graphic":var y=this.parseGraphic(f);y&&t.children.push(y);break}return n=="wrapTopAndBottom"?(t.cssStyle.display="block",s.align&&(t.cssStyle["text-align"]=s.align,t.cssStyle.width="100%")):n=="wrapNone"?(t.cssStyle.display="block",t.cssStyle.position="relative",t.cssStyle.width="0px",t.cssStyle.height="0px",s.offset&&(t.cssStyle.left=s.offset),o.offset&&(t.cssStyle.top=o.offset)):a&&(s.align=="left"||s.align=="right")&&(t.cssStyle.float=s.align),t}parseGraphic(r){var t=_.element(r,"graphicData");for(let a of _.elements(t))switch(a.localName){case"pic":return this.parsePicture(a)}return null}parsePicture(r){var t={type:k.Image,src:"",cssStyle:{}},a=_.element(r,"blipFill"),n=_.element(a,"blip");t.src=_.attr(n,"embed");var i=_.element(r,"spPr"),s=_.element(i,"xfrm");t.cssStyle.position="relative";for(var o of _.elements(s))switch(o.localName){case"ext":t.cssStyle.width=_.lengthAttr(o,"cx",Q.Emu),t.cssStyle.height=_.lengthAttr(o,"cy",Q.Emu);break;case"off":t.cssStyle.left=_.lengthAttr(o,"x",Q.Emu),t.cssStyle.top=_.lengthAttr(o,"y",Q.Emu);break}return t}parseTable(r){var t={type:k.Table,children:[]};return X.foreach(r,a=>{switch(a.localName){case"tr":t.children.push(this.parseTableRow(a));break;case"tblGrid":t.columns=this.parseTableColumns(a);break;case"tblPr":this.parseTableProperties(a,t);break}}),t}parseTableColumns(r){var t=[];return X.foreach(r,a=>{switch(a.localName){case"gridCol":t.push({width:_.lengthAttr(a,"w")});break}}),t}parseTableProperties(r,t){switch(t.cssStyle={},t.cellStyle={},this.parseDefaultProperties(r,t.cssStyle,t.cellStyle,a=>{switch(a.localName){case"tblStyle":t.styleName=_.attr(a,"val");break;case"tblLook":t.className=V.classNameOftblLook(a);break;case"tblpPr":this.parseTablePosition(a,t);break;case"tblStyleColBandSize":t.colBandSize=_.intAttr(a,"val");break;case"tblStyleRowBandSize":t.rowBandSize=_.intAttr(a,"val");break;default:return!1}return!0}),t.cssStyle["text-align"]){case"center":delete t.cssStyle["text-align"],t.cssStyle["margin-left"]="auto",t.cssStyle["margin-right"]="auto";break;case"right":delete t.cssStyle["text-align"],t.cssStyle["margin-left"]="auto";break}}parseTablePosition(r,t){var a=_.lengthAttr(r,"topFromText"),n=_.lengthAttr(r,"bottomFromText"),i=_.lengthAttr(r,"rightFromText"),s=_.lengthAttr(r,"leftFromText");t.cssStyle.float="left",t.cssStyle["margin-bottom"]=V.addSize(t.cssStyle["margin-bottom"],n),t.cssStyle["margin-left"]=V.addSize(t.cssStyle["margin-left"],s),t.cssStyle["margin-right"]=V.addSize(t.cssStyle["margin-right"],i),t.cssStyle["margin-top"]=V.addSize(t.cssStyle["margin-top"],a)}parseTableRow(r){var t={type:k.Row,children:[]};return X.foreach(r,a=>{switch(a.localName){case"tc":t.children.push(this.parseTableCell(a));break;case"trPr":this.parseTableRowProperties(a,t);break}}),t}parseTableRowProperties(r,t){t.cssStyle=this.parseDefaultProperties(r,{},null,a=>{switch(a.localName){case"cnfStyle":t.className=V.classNameOfCnfStyle(a);break;case"tblHeader":t.isHeader=_.boolAttr(a,"val");break;default:return!1}return!0})}parseTableCell(r){var t={type:k.Cell,children:[]};return X.foreach(r,a=>{switch(a.localName){case"tbl":t.children.push(this.parseTable(a));break;case"p":t.children.push(this.parseParagraph(a));break;case"tcPr":this.parseTableCellProperties(a,t);break}}),t}parseTableCellProperties(r,t){t.cssStyle=this.parseDefaultProperties(r,{},null,a=>{switch(a.localName){case"gridSpan":t.span=_.intAttr(a,"val",null);break;case"vMerge":t.verticalMerge=_.attr(a,"val")??"continue";break;case"cnfStyle":t.className=V.classNameOfCnfStyle(a);break;default:return!1}return!0}),this.parseTableCellVerticalText(r,t)}parseTableCellVerticalText(r,t){const a={btLr:{writingMode:"vertical-rl",transform:"rotate(180deg)"},lrTb:{writingMode:"vertical-lr",transform:"none"},tbRl:{writingMode:"vertical-rl",transform:"none"}};X.foreach(r,n=>{if(n.localName==="textDirection"){const i=_.attr(n,"val"),s=a[i]||{writingMode:"horizontal-tb"};t.cssStyle["writing-mode"]=s.writingMode,t.cssStyle.transform=s.transform}})}parseDefaultProperties(r,t=null,a=null,n=null){return t=t||{},X.foreach(r,i=>{if(!(n!=null&&n(i)))switch(i.localName){case"jc":t["text-align"]=V.valueOfJc(i);break;case"textAlignment":t["vertical-align"]=V.valueOfTextAlignment(i);break;case"color":t.color=X.colorAttr(i,"val",null,_r.color);break;case"sz":t["font-size"]=t["min-height"]=_.lengthAttr(i,"val",Q.FontSize);break;case"shd":t["background-color"]=X.colorAttr(i,"fill",null,_r.shd);break;case"highlight":t["background-color"]=X.colorAttr(i,"val",null,_r.highlight);break;case"vertAlign":break;case"position":t.verticalAlign=_.lengthAttr(i,"val",Q.FontSize);break;case"tcW":if(this.options.ignoreWidth)break;case"tblW":t.width=V.valueOfSize(i,"w");break;case"trHeight":this.parseTrHeight(i,t);break;case"strike":t["text-decoration"]=_.boolAttr(i,"val",!0)?"line-through":"none";break;case"b":t["font-weight"]=_.boolAttr(i,"val",!0)?"bold":"normal";break;case"i":t["font-style"]=_.boolAttr(i,"val",!0)?"italic":"normal";break;case"caps":t["text-transform"]=_.boolAttr(i,"val",!0)?"uppercase":"none";break;case"smallCaps":t["font-variant"]=_.boolAttr(i,"val",!0)?"small-caps":"none";break;case"u":this.parseUnderline(i,t);break;case"ind":case"tblInd":this.parseIndentation(i,t);break;case"rFonts":this.parseFont(i,t);break;case"tblBorders":this.parseBorderProperties(i,a||t);break;case"tblCellSpacing":t["border-spacing"]=V.valueOfMargin(i),t["border-collapse"]="separate";break;case"pBdr":this.parseBorderProperties(i,t);break;case"bdr":t.border=V.valueOfBorder(i);break;case"tcBorders":this.parseBorderProperties(i,t);break;case"vanish":_.boolAttr(i,"val",!0)&&(t.display="none");break;case"kern":break;case"noWrap":break;case"tblCellMar":case"tcMar":this.parseMarginProperties(i,a||t);break;case"tblLayout":t["table-layout"]=V.valueOfTblLayout(i);break;case"vAlign":t["vertical-align"]=V.valueOfTextAlignment(i);break;case"spacing":r.localName=="pPr"&&this.parseSpacing(i,t);break;case"wordWrap":_.boolAttr(i,"val")&&(t["overflow-wrap"]="break-word");break;case"suppressAutoHyphens":t.hyphens=_.boolAttr(i,"val",!0)?"none":"auto";break;case"lang":t.$lang=_.attr(i,"val");break;case"bCs":case"iCs":case"szCs":case"tabs":case"outlineLvl":case"contextualSpacing":case"tblStyleColBandSize":case"tblStyleRowBandSize":case"webHidden":case"pageBreakBefore":case"suppressLineNumbers":case"keepLines":case"keepNext":case"widowControl":case"bidi":case"rtl":case"noProof":break;default:this.options.debug&&console.warn(`DOCX: Unknown document element: ${r.localName}.${i.localName}`);break}}),t}parseUnderline(r,t){var a=_.attr(r,"val");if(a!=null){switch(a){case"dash":case"dashDotDotHeavy":case"dashDotHeavy":case"dashedHeavy":case"dashLong":case"dashLongHeavy":case"dotDash":case"dotDotDash":t["text-decoration"]="underline dashed";break;case"dotted":case"dottedHeavy":t["text-decoration"]="underline dotted";break;case"double":t["text-decoration"]="underline double";break;case"single":case"thick":t["text-decoration"]="underline";break;case"wave":case"wavyDouble":case"wavyHeavy":t["text-decoration"]="underline wavy";break;case"words":t["text-decoration"]="underline";break;case"none":t["text-decoration"]="none";break}var n=X.colorAttr(r,"color");n&&(t["text-decoration-color"]=n)}}parseFont(r,t){var a=_.attr(r,"ascii"),n=V.themeValue(r,"asciiTheme"),i=_.attr(r,"eastAsia"),s=[a,n,i].filter(o=>o).map(o=>Ba(o));s.length>0&&(t["font-family"]=[...new Set(s)].join(", "))}parseIndentation(r,t){var a=_.lengthAttr(r,"firstLine"),n=_.lengthAttr(r,"hanging"),i=_.lengthAttr(r,"left"),s=_.lengthAttr(r,"start"),o=_.lengthAttr(r,"right"),f=_.lengthAttr(r,"end");a&&(t["text-indent"]=a),n&&(t["text-indent"]=`-${n}`),(i||s)&&(t["margin-left"]=i||s),(o||f)&&(t["margin-right"]=o||f)}parseSpacing(r,t){var a=_.lengthAttr(r,"before"),n=_.lengthAttr(r,"after"),i=_.intAttr(r,"line",null),s=_.attr(r,"lineRule");if(a&&(t["margin-top"]=a),n&&(t["margin-bottom"]=n),i!==null)switch(s){case"auto":t["line-height"]=`${(i/240).toFixed(2)}`;break;case"atLeast":t["line-height"]=`calc(100% + ${i/20}pt)`;break;default:t["line-height"]=t["min-height"]=`${i/20}pt`;break}}parseMarginProperties(r,t){X.foreach(r,a=>{switch(a.localName){case"left":t["padding-left"]=V.valueOfMargin(a);break;case"right":t["padding-right"]=V.valueOfMargin(a);break;case"top":t["padding-top"]=V.valueOfMargin(a);break;case"bottom":t["padding-bottom"]=V.valueOfMargin(a);break}})}parseTrHeight(r,t){switch(_.attr(r,"hRule")){case"exact":t.height=_.lengthAttr(r,"val");break;case"atLeast":default:t.height=_.lengthAttr(r,"val");break}}parseBorderProperties(r,t){X.foreach(r,a=>{switch(a.localName){case"start":case"left":t["border-left"]=V.valueOfBorder(a);break;case"end":case"right":t["border-right"]=V.valueOfBorder(a);break;case"top":t["border-top"]=V.valueOfBorder(a);break;case"bottom":t["border-bottom"]=V.valueOfBorder(a);break}})}}const lc=["black","blue","cyan","darkBlue","darkCyan","darkGray","darkGreen","darkMagenta","darkRed","darkYellow","green","lightGray","magenta","none","red","white","yellow"];class X{static foreach(r,t){for(var a=0;a<r.childNodes.length;a++){let n=r.childNodes[a];n.nodeType==Node.ELEMENT_NODE&&t(n)}}static colorAttr(r,t,a=null,n="black"){var i=_.attr(r,t);if(i)return i=="auto"?n:lc.includes(i)?i:`#${i}`;var s=_.attr(r,"themeColor");return s?`var(--docx-${s}-color)`:a}static sizeValue(r,t=Q.Dxa){return uo(r.textContent,t)}}class V{static themeValue(r,t){var a=_.attr(r,t);return a?`var(--docx-${a}-font)`:null}static valueOfSize(r,t){var a=Q.Dxa;switch(_.attr(r,"type")){case"dxa":break;case"pct":a=Q.Percent;break;case"auto":return"auto"}return _.lengthAttr(r,t,a)}static valueOfMargin(r){return _.lengthAttr(r,"w")}static valueOfBorder(r){var t=_.attr(r,"val");if(t=="nil")return"none";var a=X.colorAttr(r,"color"),n=_.lengthAttr(r,"sz",Q.Border);return`${n} solid ${a=="auto"?_r.borderColor:a}`}static valueOfTblLayout(r){var t=_.attr(r,"val");return t=="fixed"?"fixed":"auto"}static classNameOfCnfStyle(r){const t=_.attr(r,"val");return["first-row","last-row","first-col","last-col","odd-col","even-col","odd-row","even-row","ne-cell","nw-cell","se-cell","sw-cell"].filter((n,i)=>t[i]=="1").join(" ")}static valueOfJc(r){var t=_.attr(r,"val");switch(t){case"start":case"left":return"left";case"center":return"center";case"end":case"right":return"right";case"both":return"justify"}return t}static valueOfVertAlign(r,t=!1){var a=_.attr(r,"val");switch(a){case"subscript":return"sub";case"superscript":return t?"sup":"super"}return t?null:a}static valueOfTextAlignment(r){var t=_.attr(r,"val");switch(t){case"auto":case"baseline":return"baseline";case"top":return"top";case"center":return"middle";case"bottom":return"bottom"}return t}static addSize(r,t){return r==null?t:t==null?r:`calc(${r} + ${t})`}static classNameOftblLook(r){const t=_.hexAttr(r,"val",0);let a="";return(_.boolAttr(r,"firstRow")||t&32)&&(a+=" first-row"),(_.boolAttr(r,"lastRow")||t&64)&&(a+=" last-row"),(_.boolAttr(r,"firstColumn")||t&128)&&(a+=" first-col"),(_.boolAttr(r,"lastColumn")||t&256)&&(a+=" last-col"),(_.boolAttr(r,"noHBand")||t&512)&&(a+=" no-hband"),(_.boolAttr(r,"noVBand")||t&1024)&&(a+=" no-vband"),a.trim()}}const Ii={pos:0,leader:"none",style:"left"},fc=50;function uc(e=document.body){const r=document.createElement("div");r.style.width="100pt",e.appendChild(r);const t=100/r.offsetWidth;return e.removeChild(r),t}function hc(e,r,t,a=72/96){const n=e.closest("p"),i=e.getBoundingClientRect(),s=n.getBoundingClientRect(),o=getComputedStyle(n),f=(r==null?void 0:r.length)>0?r.map(v=>({pos:Li(v.position),leader:v.leader,style:v.style})).sort((v,w)=>v.pos-w.pos):[Ii],l=f[f.length-1],u=s.width*a,y=Li(t);let c=l.pos+y;if(c<u)for(;c<u&&f.length<fc;c+=y)f.push({...Ii,pos:c});const p=parseFloat(o.marginLeft),b=s.left+p,g=(i.left-b)*a,C=f.find(v=>v.style!="clear"&&v.pos>g);if(C==null)return;let h=1;if(C.style=="right"||C.style=="center"){const v=Array.from(n.querySelectorAll(`.${e.className}`)),w=v.indexOf(e)+1,E=document.createRange();E.setStart(e,1),w<v.length?E.setEndBefore(v[w]):E.setEndAfter(n);const A=C.style=="center"?.5:1,M=E.getBoundingClientRect(),B=M.left+A*M.width-(s.left-p);h=C.pos-B*a}else h=C.pos-g;switch(e.innerHTML="&nbsp;",e.style.textDecoration="inherit",e.style.wordSpacing=`${h.toFixed(0)}pt`,C.leader){case"dot":case"middleDot":e.style.textDecoration="underline",e.style.textDecorationStyle="dotted";break;case"hyphen":case"heavy":case"underscore":e.style.textDecoration="underline";break}}function Li(e){return parseFloat(e)}const L={svg:"http://www.w3.org/2000/svg",mathML:"http://www.w3.org/1998/Math/MathML"};class cc{constructor(r){this.htmlDocument=r,this.className="docx",this.styleMap={},this.currentPart=null,this.tableVerticalMerges=[],this.currentVerticalMerge=null,this.tableCellPositions=[],this.currentCellPosition=null,this.footnoteMap={},this.endnoteMap={},this.currentEndnoteIds=[],this.usedHederFooterParts=[],this.currentTabs=[],this.commentMap={},this.tasks=[],this.postRenderTasks=[]}async render(r,t,a=null,n){var s;this.document=r,this.options=n,this.className=n.className,this.rootSelector=n.inWrapper?`.${this.className}-wrapper`:":root",this.styleMap=null,this.tasks=[],this.options.renderComments&&globalThis.Highlight&&(this.commentHighlight=new Highlight),a=a||t,zi(a),zi(t),a.appendChild(this.createComment("docxjs library predefined styles")),a.appendChild(this.renderDefaultStyle()),r.themePart&&(a.appendChild(this.createComment("docxjs document theme values")),this.renderTheme(r.themePart,a)),r.stylesPart!=null&&(this.styleMap=this.processStyles(r.stylesPart.styles),a.appendChild(this.createComment("docxjs document styles")),a.appendChild(this.renderStyles(r.stylesPart.styles))),r.numberingPart&&(this.prodessNumberings(r.numberingPart.domNumberings),a.appendChild(this.createComment("docxjs document numbering styles")),a.appendChild(this.renderNumbering(r.numberingPart.domNumberings,a))),r.footnotesPart&&(this.footnoteMap=Le(r.footnotesPart.notes,o=>o.id)),r.endnotesPart&&(this.endnoteMap=Le(r.endnotesPart.notes,o=>o.id)),r.settingsPart&&(this.defaultTabSize=(s=r.settingsPart.settings)==null?void 0:s.defaultTabStop),!n.ignoreFonts&&r.fontTablePart&&this.renderFontTable(r.fontTablePart,a);var i=this.renderSections(r.documentPart.body);this.options.inWrapper?t.appendChild(this.renderWrapper(i)):ka(t,i),this.commentHighlight&&n.renderComments&&CSS.highlights.set(`${this.className}-comments`,this.commentHighlight),this.postRenderTasks.forEach(o=>o()),await Promise.allSettled(this.tasks),this.refreshTabStops()}renderTheme(r,t){var o,f;const a={},n=(o=r.theme)==null?void 0:o.fontScheme;n&&(n.majorFont&&(a["--docx-majorHAnsi-font"]=n.majorFont.latinTypeface),n.minorFont&&(a["--docx-minorHAnsi-font"]=n.minorFont.latinTypeface));const i=(f=r.theme)==null?void 0:f.colorScheme;if(i)for(let[l,u]of Object.entries(i.colors))a[`--docx-${l}-color`]=`#${u}`;const s=this.styleToString(`.${this.className}`,a);t.appendChild(this.createStyleElement(s))}renderFontTable(r,t){for(let a of r.fonts)for(let n of a.embedFontRefs)this.tasks.push(this.document.loadFont(n.id,n.key).then(i=>{const s={"font-family":Ba(a.name),src:`url(${i})`};(n.type=="bold"||n.type=="boldItalic")&&(s["font-weight"]="bold"),(n.type=="italic"||n.type=="boldItalic")&&(s["font-style"]="italic");const o=this.styleToString("@font-face",s);t.appendChild(this.createComment(`docxjs ${a.name} font`)),t.appendChild(this.createStyleElement(o))}))}processStyleName(r){return r?`${this.className}_${Yu(r)}`:this.className}processStyles(r){const t=Le(r.filter(n=>n.id!=null),n=>n.id);for(const n of r.filter(i=>i.basedOn)){var a=t[n.basedOn];if(a){n.paragraphProps=Sr(n.paragraphProps,a.paragraphProps),n.runProps=Sr(n.runProps,a.runProps);for(const i of a.styles){const s=n.styles.find(o=>o.target==i.target);s?this.copyStyleProperties(i.values,s.values):n.styles.push({...i,values:{...i.values}})}}else this.options.debug&&console.warn(`Can't find base style ${n.basedOn}`)}for(let n of r)n.cssName=this.processStyleName(n.id);return t}prodessNumberings(r){var t;for(let a of r.filter(n=>n.pStyleName)){const n=this.findStyle(a.pStyleName);(t=n==null?void 0:n.paragraphProps)!=null&&t.numbering&&(n.paragraphProps.numbering.level=a.level)}}processElement(r){if(r.children)for(var t of r.children)t.parent=r,t.type==k.Table?this.processTable(t):this.processElement(t)}processTable(r){for(var t of r.children)for(var a of t.children)a.cssStyle=this.copyStyleProperties(r.cellStyle,a.cssStyle,["border-left","border-right","border-top","border-bottom","padding-left","padding-right","padding-top","padding-bottom"]),this.processElement(a)}copyStyleProperties(r,t,a=null){if(!r)return t;t==null&&(t={}),a==null&&(a=Object.getOwnPropertyNames(r));for(var n of a)r.hasOwnProperty(n)&&!t.hasOwnProperty(n)&&(t[n]=r[n]);return t}createPageElement(r,t){var a=this.createElement("section",{className:r});return t&&(t.pageMargins&&(a.style.paddingLeft=t.pageMargins.left,a.style.paddingRight=t.pageMargins.right,a.style.paddingTop=t.pageMargins.top,a.style.paddingBottom=t.pageMargins.bottom),t.pageSize&&(this.options.ignoreWidth||(a.style.width=t.pageSize.width),this.options.ignoreHeight||(a.style.minHeight=t.pageSize.height))),a}createSectionContent(r){var t=this.createElement("article");return r.columns&&r.columns.numberOfColumns&&(t.style.columnCount=`${r.columns.numberOfColumns}`,t.style.columnGap=r.columns.space,r.columns.separator&&(t.style.columnRule="1px solid black")),t}renderSections(r){const t=[];this.processElement(r);const a=this.splitBySection(r.children,r.props),n=this.groupByPageBreaks(a);let i=null;for(let o=0,f=n.length;o<f;o++){this.currentFootnoteIds=[];let u=n[o][0].sectProps;const y=this.createPageElement(this.className,u);this.renderStyleValues(r.cssStyle,y),this.options.renderHeaders&&this.renderHeaderFooter(u.headerRefs,u,t.length,i!=u,y);for(const c of n[o]){var s=this.createSectionContent(c.sectProps);this.renderElements(c.elements,s),y.appendChild(s),u=c.sectProps}this.options.renderFootnotes&&this.renderNotes(this.currentFootnoteIds,this.footnoteMap,y),this.options.renderEndnotes&&o==f-1&&this.renderNotes(this.currentEndnoteIds,this.endnoteMap,y),this.options.renderFooters&&this.renderHeaderFooter(u.footerRefs,u,t.length,i!=u,y),t.push(y),i=u}return t}renderHeaderFooter(r,t,a,n,i){if(r){var s=(t.titlePage&&n?r.find(f=>f.type=="first"):null)??(a%2==1?r.find(f=>f.type=="even"):null)??r.find(f=>f.type=="default"),o=s&&this.document.findPartByRelId(s.id,this.document.documentPart);if(o){this.currentPart=o,this.usedHederFooterParts.includes(o.path)||(this.processElement(o.rootElement),this.usedHederFooterParts.push(o.path));const[f]=this.renderElements([o.rootElement],i);t!=null&&t.pageMargins&&(o.rootElement.type===k.Header?(f.style.marginTop=`calc(${t.pageMargins.header} - ${t.pageMargins.top})`,f.style.minHeight=`calc(${t.pageMargins.top} - ${t.pageMargins.header})`):o.rootElement.type===k.Footer&&(f.style.marginBottom=`calc(${t.pageMargins.footer} - ${t.pageMargins.bottom})`,f.style.minHeight=`calc(${t.pageMargins.bottom} - ${t.pageMargins.footer})`)),this.currentPart=null}}}isPageBreakElement(r){return r.type!=k.Break?!1:r.break=="lastRenderedPageBreak"?!this.options.ignoreLastRenderedPageBreak:r.break=="page"}isPageBreakSection(r,t){var a,n,i,s,o,f;return!r||!t?!1:((a=r.pageSize)==null?void 0:a.orientation)!=((n=t.pageSize)==null?void 0:n.orientation)||((i=r.pageSize)==null?void 0:i.width)!=((s=t.pageSize)==null?void 0:s.width)||((o=r.pageSize)==null?void 0:o.height)!=((f=t.pageSize)==null?void 0:f.height)}splitBySection(r,t){var y;var a={sectProps:null,elements:[],pageBreak:!1},n=[a];for(let c of r){if(c.type==k.Paragraph){const p=this.findStyle(c.styleName);(y=p==null?void 0:p.paragraphProps)!=null&&y.pageBreakBefore&&(a.sectProps=i,a.pageBreak=!0,a={sectProps:null,elements:[],pageBreak:!1},n.push(a))}if(a.elements.push(c),c.type==k.Paragraph){const p=c;var i=p.sectionProps,s=-1,o=-1;if(this.options.breakPages&&p.children&&(s=p.children.findIndex(b=>{var g;return o=((g=b.children)==null?void 0:g.findIndex(this.isPageBreakElement.bind(this)))??-1,o!=-1})),(i||s!=-1)&&(a.sectProps=i,a.pageBreak=s!=-1,a={sectProps:null,elements:[],pageBreak:!1},n.push(a)),s!=-1){let b=p.children[s],g=o<b.children.length-1;if(s<p.children.length-1||g){var f=c.children,l={...c,children:f.slice(s)};if(c.children=f.slice(0,s),a.elements.push(l),g){let C=b.children,h={...b,children:C.slice(0,o)};c.children.push(h),b.children=C.slice(o)}}}}}let u=null;for(let c=n.length-1;c>=0;c--)n[c].sectProps==null?n[c].sectProps=u??t:u=n[c].sectProps;return n}groupByPageBreaks(r){let t=[],a;const n=[t];for(let i of r)t.push(i),(this.options.ignoreLastRenderedPageBreak||i.pageBreak||this.isPageBreakSection(a,i.sectProps))&&n.push(t=[]),a=i.sectProps;return n.filter(i=>i.length>0)}renderWrapper(r){return this.createElement("div",{className:`${this.className}-wrapper`},r)}renderDefaultStyle(){var r=this.className,t=`
.${r}-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } 
.${r}-wrapper>section.${r} { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }`;this.options.hideWrapperOnPrint&&(t=`@media not print { ${t} }`);var a=`${t}
.${r} { color: black; hyphens: auto; text-underline-position: from-font; }
section.${r} { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }
section.${r}>article { margin-bottom: auto; z-index: 1; }
section.${r}>footer { z-index: 1; }
.${r} table { border-collapse: collapse; }
.${r} table td, .${r} table th { vertical-align: top; }
.${r} p { margin: 0pt; min-height: 1em; }
.${r} span { white-space: pre-wrap; overflow-wrap: break-word; }
.${r} a { color: inherit; text-decoration: inherit; }
.${r} svg { fill: transparent; }
`;return this.options.renderComments&&(a+=`
.${r}-comment-ref { cursor: default; }
.${r}-comment-popover { display: none; z-index: 1000; padding: 0.5rem; background: white; position: absolute; box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.25); width: 30ch; }
.${r}-comment-ref:hover~.${r}-comment-popover { display: block; }
.${r}-comment-author,.${r}-comment-date { font-size: 0.875rem; color: #888; }
`),this.createStyleElement(a)}renderNumbering(r,t){var a="",n=[];for(var i of r){var s=`p.${this.numberingClass(i.id,i.level)}`,o="none";if(i.bullet){let f=`--${this.className}-${i.bullet.src}`.toLowerCase();a+=this.styleToString(`${s}:before`,{content:"' '",display:"inline-block",background:`var(${f})`},i.bullet.style),this.tasks.push(this.document.loadNumberingImage(i.bullet.src).then(l=>{var u=`${this.rootSelector} { ${f}: url(${l}) }`;t.appendChild(this.createStyleElement(u))}))}else if(i.levelText){let f=this.numberingCounter(i.id,i.level);const l=f+" "+(i.start-1);i.level>0&&(a+=this.styleToString(`p.${this.numberingClass(i.id,i.level-1)}`,{"counter-set":l})),n.push(l),a+=this.styleToString(`${s}:before`,{content:this.levelTextToContent(i.levelText,i.suff,i.id,this.numFormatToCssValue(i.format)),"counter-increment":f,...i.rStyle})}else o=this.numFormatToCssValue(i.format);a+=this.styleToString(s,{display:"list-item","list-style-position":"inside","list-style-type":o,...i.pStyle})}return n.length>0&&(a+=this.styleToString(this.rootSelector,{"counter-reset":n.join(" ")})),this.createStyleElement(a)}renderStyles(r){var t="";const a=this.styleMap,n=Le(r.filter(f=>f.isDefault),f=>f.target);for(const f of r){var i=f.styles;if(f.linked){var s=f.linked&&a[f.linked];s?i=i.concat(s.styles):this.options.debug&&console.warn(`Can't find linked style ${f.linked}`)}for(const l of i){var o=`${f.target??""}.${f.cssName}`;f.target!=l.target&&(o+=` ${l.target}`),n[f.target]==f&&(o=`.${this.className} ${f.target}, `+o),t+=this.styleToString(o,l.values)}}return this.createStyleElement(t)}renderNotes(r,t,a){var n=r.map(s=>t[s]).filter(s=>s);if(n.length>0){var i=this.createElement("ol",null,this.renderElements(n));a.appendChild(i)}}renderElement(r){switch(r.type){case k.Paragraph:return this.renderParagraph(r);case k.BookmarkStart:return this.renderBookmarkStart(r);case k.BookmarkEnd:return null;case k.Run:return this.renderRun(r);case k.Table:return this.renderTable(r);case k.Row:return this.renderTableRow(r);case k.Cell:return this.renderTableCell(r);case k.Hyperlink:return this.renderHyperlink(r);case k.SmartTag:return this.renderSmartTag(r);case k.Drawing:return this.renderDrawing(r);case k.Image:return this.renderImage(r);case k.Text:return this.renderText(r);case k.Text:return this.renderText(r);case k.DeletedText:return this.renderDeletedText(r);case k.Tab:return this.renderTab(r);case k.Symbol:return this.renderSymbol(r);case k.Break:return this.renderBreak(r);case k.Footer:return this.renderContainer(r,"footer");case k.Header:return this.renderContainer(r,"header");case k.Footnote:case k.Endnote:return this.renderContainer(r,"li");case k.FootnoteReference:return this.renderFootnoteReference(r);case k.EndnoteReference:return this.renderEndnoteReference(r);case k.NoBreakHyphen:return this.createElement("wbr");case k.VmlPicture:return this.renderVmlPicture(r);case k.VmlElement:return this.renderVmlElement(r);case k.MmlMath:return this.renderContainerNS(r,L.mathML,"math",{xmlns:L.mathML});case k.MmlMathParagraph:return this.renderContainer(r,"span");case k.MmlFraction:return this.renderContainerNS(r,L.mathML,"mfrac");case k.MmlBase:return this.renderContainerNS(r,L.mathML,r.parent.type==k.MmlMatrixRow?"mtd":"mrow");case k.MmlNumerator:case k.MmlDenominator:case k.MmlFunction:case k.MmlLimit:case k.MmlBox:return this.renderContainerNS(r,L.mathML,"mrow");case k.MmlGroupChar:return this.renderMmlGroupChar(r);case k.MmlLimitLower:return this.renderContainerNS(r,L.mathML,"munder");case k.MmlMatrix:return this.renderContainerNS(r,L.mathML,"mtable");case k.MmlMatrixRow:return this.renderContainerNS(r,L.mathML,"mtr");case k.MmlRadical:return this.renderMmlRadical(r);case k.MmlSuperscript:return this.renderContainerNS(r,L.mathML,"msup");case k.MmlSubscript:return this.renderContainerNS(r,L.mathML,"msub");case k.MmlDegree:case k.MmlSuperArgument:case k.MmlSubArgument:return this.renderContainerNS(r,L.mathML,"mn");case k.MmlFunctionName:return this.renderContainerNS(r,L.mathML,"ms");case k.MmlDelimiter:return this.renderMmlDelimiter(r);case k.MmlRun:return this.renderMmlRun(r);case k.MmlNary:return this.renderMmlNary(r);case k.MmlPreSubSuper:return this.renderMmlPreSubSuper(r);case k.MmlBar:return this.renderMmlBar(r);case k.MmlEquationArray:return this.renderMllList(r);case k.Inserted:return this.renderInserted(r);case k.Deleted:return this.renderDeleted(r);case k.CommentRangeStart:return this.renderCommentRangeStart(r);case k.CommentRangeEnd:return this.renderCommentRangeEnd(r);case k.CommentReference:return this.renderCommentReference(r);case k.AltChunk:return this.renderAltChunk(r)}return null}renderElements(r,t){if(r==null)return null;var a=r.flatMap(n=>this.renderElement(n)).filter(n=>n!=null);return t&&ka(t,a),a}renderContainer(r,t,a){return this.createElement(t,a,this.renderElements(r.children))}renderContainerNS(r,t,a,n){return this.createElementNS(t,a,n,this.renderElements(r.children))}renderParagraph(r){var i,s;var t=this.renderContainer(r,"p");const a=this.findStyle(r.styleName);r.tabs??(r.tabs=(i=a==null?void 0:a.paragraphProps)==null?void 0:i.tabs),this.renderClass(r,t),this.renderStyleValues(r.cssStyle,t),this.renderCommonProperties(t.style,r);const n=r.numbering??((s=a==null?void 0:a.paragraphProps)==null?void 0:s.numbering);return n&&t.classList.add(this.numberingClass(n.id,n.level)),t}renderRunProperties(r,t){this.renderCommonProperties(r,t)}renderCommonProperties(r,t){t!=null&&(t.color&&(r.color=t.color),t.fontSize&&(r["font-size"]=t.fontSize))}renderHyperlink(r){var t=this.renderContainer(r,"a");this.renderStyleValues(r.cssStyle,t);let a="";if(r.id){const n=this.document.documentPart.rels.find(i=>i.id==r.id&&i.targetMode==="External");a=(n==null?void 0:n.target)??a}return r.anchor&&(a+=`#${r.anchor}`),t.href=a,t}renderSmartTag(r){return this.renderContainer(r,"span")}renderCommentRangeStart(r){var n;if(!this.options.renderComments)return null;const t=new Range;(n=this.commentHighlight)==null||n.add(t);const a=this.htmlDocument.createComment(`start of comment #${r.id}`);return this.later(()=>t.setStart(a,0)),this.commentMap[r.id]=t,a}renderCommentRangeEnd(r){if(!this.options.renderComments)return null;const t=this.commentMap[r.id],a=this.htmlDocument.createComment(`end of comment #${r.id}`);return this.later(()=>t==null?void 0:t.setEnd(a,0)),a}renderCommentReference(r){var s;if(!this.options.renderComments)return null;var t=(s=this.document.commentsPart)==null?void 0:s.commentMap[r.id];if(!t)return null;const a=new DocumentFragment,n=this.createElement("span",{className:`${this.className}-comment-ref`},["💬"]),i=this.createElement("div",{className:`${this.className}-comment-popover`});return this.renderCommentContent(t,i),a.appendChild(this.htmlDocument.createComment(`comment #${t.id} by ${t.author} on ${t.date}`)),a.appendChild(n),a.appendChild(i),a}renderAltChunk(r){if(!this.options.renderAltChunks)return null;var t=this.createElement("iframe");return this.tasks.push(this.document.loadAltChunk(r.id,this.currentPart).then(a=>{t.srcdoc=a})),t}renderCommentContent(r,t){t.appendChild(this.createElement("div",{className:`${this.className}-comment-author`},[r.author])),t.appendChild(this.createElement("div",{className:`${this.className}-comment-date`},[new Date(r.date).toLocaleString()])),this.renderElements(r.children,t)}renderDrawing(r){var t=this.renderContainer(r,"div");return t.style.display="inline-block",t.style.position="relative",t.style.textIndent="0px",this.renderStyleValues(r.cssStyle,t),t}renderImage(r){let t=this.createElement("img");return this.renderStyleValues(r.cssStyle,t),this.document&&this.tasks.push(this.document.loadDocumentImage(r.src,this.currentPart).then(a=>{t.src=a})),t}renderText(r){return this.htmlDocument.createTextNode(r.text)}renderDeletedText(r){return this.options.renderEndnotes?this.htmlDocument.createTextNode(r.text):null}renderBreak(r){return r.break=="textWrapping"?this.createElement("br"):null}renderInserted(r){return this.options.renderChanges?this.renderContainer(r,"ins"):this.renderElements(r.children)}renderDeleted(r){return this.options.renderChanges?this.renderContainer(r,"del"):null}renderSymbol(r){var t=this.createElement("span");return t.style.fontFamily=r.font,t.innerHTML=`&#x${r.char};`,t}renderFootnoteReference(r){var t=this.createElement("sup");return this.currentFootnoteIds.push(r.id),t.textContent=`${this.currentFootnoteIds.length}`,t}renderEndnoteReference(r){var t=this.createElement("sup");return this.currentEndnoteIds.push(r.id),t.textContent=`${this.currentEndnoteIds.length}`,t}renderTab(r){var n;var t=this.createElement("span");if(t.innerHTML="&emsp;",this.options.experimental){t.className=this.tabStopClass();var a=(n=dc(r,k.Paragraph))==null?void 0:n.tabs;this.currentTabs.push({stops:a,span:t})}return t}renderBookmarkStart(r){return this.createElement("span",{id:r.name})}renderRun(r){if(r.fieldRun)return null;const t=this.createElement("span");if(r.id&&(t.id=r.id),this.renderClass(r,t),this.renderStyleValues(r.cssStyle,t),r.verticalAlign){const a=this.createElement(r.verticalAlign);this.renderElements(r.children,a),t.appendChild(a)}else this.renderElements(r.children,t);return t}renderTable(r){let t=this.createElement("table");return this.tableCellPositions.push(this.currentCellPosition),this.tableVerticalMerges.push(this.currentVerticalMerge),this.currentVerticalMerge={},this.currentCellPosition={col:0,row:0},r.columns&&t.appendChild(this.renderTableColumns(r.columns)),this.renderClass(r,t),this.renderElements(r.children,t),this.renderStyleValues(r.cssStyle,t),this.currentVerticalMerge=this.tableVerticalMerges.pop(),this.currentCellPosition=this.tableCellPositions.pop(),t}renderTableColumns(r){let t=this.createElement("colgroup");for(let a of r){let n=this.createElement("col");a.width&&(n.style.width=a.width),t.appendChild(n)}return t}renderTableRow(r){let t=this.renderContainer(r,"tr");return this.currentCellPosition.col=0,this.renderClass(r,t),this.renderStyleValues(r.cssStyle,t),this.currentCellPosition.row++,t}renderTableCell(r){let t=this.renderContainer(r,"td");const a=this.currentCellPosition.col;return r.verticalMerge?r.verticalMerge=="restart"?(this.currentVerticalMerge[a]=t,t.rowSpan=1):this.currentVerticalMerge[a]&&(this.currentVerticalMerge[a].rowSpan+=1,t.style.display="none"):this.currentVerticalMerge[a]=null,this.renderClass(r,t),this.renderStyleValues(r.cssStyle,t),r.span&&(t.colSpan=r.span),this.currentCellPosition.col+=t.colSpan,t}renderVmlPicture(r){return this.renderContainer(r,"div")}renderVmlElement(r){var n,i;var t=this.createSvgElement("svg");t.setAttribute("style",r.cssStyleText);const a=this.renderVmlChildElement(r);return(n=r.imageHref)!=null&&n.id&&this.tasks.push((i=this.document)==null?void 0:i.loadDocumentImage(r.imageHref.id,this.currentPart).then(s=>a.setAttribute("href",s))),t.appendChild(a),requestAnimationFrame(()=>{const s=t.firstElementChild.getBBox();t.setAttribute("width",`${Math.ceil(s.x+s.width)}`),t.setAttribute("height",`${Math.ceil(s.y+s.height)}`)}),t}renderVmlChildElement(r){const t=this.createSvgElement(r.tagName);Object.entries(r.attrs).forEach(([a,n])=>t.setAttribute(a,n));for(let a of r.children)a.type==k.VmlElement?t.appendChild(this.renderVmlChildElement(a)):t.appendChild(...Ct(this.renderElement(a)));return t}renderMmlRadical(r){var n;const t=r.children.find(i=>i.type==k.MmlBase);if((n=r.props)!=null&&n.hideDegree)return this.createElementNS(L.mathML,"msqrt",null,this.renderElements([t]));const a=r.children.find(i=>i.type==k.MmlDegree);return this.createElementNS(L.mathML,"mroot",null,this.renderElements([t,a]))}renderMmlDelimiter(r){const t=[];return t.push(this.createElementNS(L.mathML,"mo",null,[r.props.beginChar??"("])),t.push(...this.renderElements(r.children)),t.push(this.createElementNS(L.mathML,"mo",null,[r.props.endChar??")"])),this.createElementNS(L.mathML,"mrow",null,t)}renderMmlNary(r){var l;const t=[],a=Le(r.children,u=>u.type),n=a[k.MmlSuperArgument],i=a[k.MmlSubArgument],s=n?this.createElementNS(L.mathML,"mo",null,Ct(this.renderElement(n))):null,o=i?this.createElementNS(L.mathML,"mo",null,Ct(this.renderElement(i))):null,f=this.createElementNS(L.mathML,"mo",null,[((l=r.props)==null?void 0:l.char)??"∫"]);return s||o?t.push(this.createElementNS(L.mathML,"munderover",null,[f,o,s])):s?t.push(this.createElementNS(L.mathML,"mover",null,[f,s])):o?t.push(this.createElementNS(L.mathML,"munder",null,[f,o])):t.push(f),t.push(...this.renderElements(a[k.MmlBase].children)),this.createElementNS(L.mathML,"mrow",null,t)}renderMmlPreSubSuper(r){const t=[],a=Le(r.children,l=>l.type),n=a[k.MmlSuperArgument],i=a[k.MmlSubArgument],s=n?this.createElementNS(L.mathML,"mo",null,Ct(this.renderElement(n))):null,o=i?this.createElementNS(L.mathML,"mo",null,Ct(this.renderElement(i))):null,f=this.createElementNS(L.mathML,"mo",null);return t.push(this.createElementNS(L.mathML,"msubsup",null,[f,o,s])),t.push(...this.renderElements(a[k.MmlBase].children)),this.createElementNS(L.mathML,"mrow",null,t)}renderMmlGroupChar(r){const t=r.props.verticalJustification==="bot"?"mover":"munder",a=this.renderContainerNS(r,L.mathML,t);return r.props.char&&a.appendChild(this.createElementNS(L.mathML,"mo",null,[r.props.char])),a}renderMmlBar(r){const t=this.renderContainerNS(r,L.mathML,"mrow");switch(r.props.position){case"top":t.style.textDecoration="overline";break;case"bottom":t.style.textDecoration="underline";break}return t}renderMmlRun(r){const t=this.createElementNS(L.mathML,"ms",null,this.renderElements(r.children));return this.renderClass(r,t),this.renderStyleValues(r.cssStyle,t),t}renderMllList(r){const t=this.createElementNS(L.mathML,"mtable");this.renderClass(r,t),this.renderStyleValues(r.cssStyle,t);for(let a of this.renderElements(r.children))t.appendChild(this.createElementNS(L.mathML,"mtr",null,[this.createElementNS(L.mathML,"mtd",null,[a])]));return t}renderStyleValues(r,t){for(let a in r)a.startsWith("$")?t.setAttribute(a.slice(1),r[a]):t.style[a]=r[a]}renderClass(r,t){r.className&&(t.className=r.className),r.styleName&&t.classList.add(this.processStyleName(r.styleName))}findStyle(r){var t;return r&&((t=this.styleMap)==null?void 0:t[r])}numberingClass(r,t){return`${this.className}-num-${r}-${t}`}tabStopClass(){return`${this.className}-tab-stop`}styleToString(r,t,a=null){let n=`${r} {\r
`;for(const i in t)i.startsWith("$")||(n+=`  ${i}: ${t[i]};\r
`);return a&&(n+=a),n+`}\r
`}numberingCounter(r,t){return`${this.className}-num-${r}-${t}`}levelTextToContent(r,t,a,n){const i={tab:"\\9",space:"\\a0"};var s=r.replace(/%\d*/g,o=>{let f=parseInt(o.substring(1),10)-1;return`"counter(${this.numberingCounter(a,f)}, ${n})"`});return`"${s}${i[t]??""}"`}numFormatToCssValue(r){var t={none:"none",bullet:"disc",decimal:"decimal",lowerLetter:"lower-alpha",upperLetter:"upper-alpha",lowerRoman:"lower-roman",upperRoman:"upper-roman",decimalZero:"decimal-leading-zero",aiueo:"katakana",aiueoFullWidth:"katakana",chineseCounting:"simp-chinese-informal",chineseCountingThousand:"simp-chinese-informal",chineseLegalSimplified:"simp-chinese-formal",chosung:"hangul-consonant",ideographDigital:"cjk-ideographic",ideographTraditional:"cjk-heavenly-stem",ideographLegalTraditional:"trad-chinese-formal",ideographZodiac:"cjk-earthly-branch",iroha:"katakana-iroha",irohaFullWidth:"katakana-iroha",japaneseCounting:"japanese-informal",japaneseDigitalTenThousand:"cjk-decimal",japaneseLegal:"japanese-formal",thaiNumbers:"thai",koreanCounting:"korean-hangul-formal",koreanDigital:"korean-hangul-formal",koreanDigital2:"korean-hanja-informal",hebrew1:"hebrew",hebrew2:"hebrew",hindiNumbers:"devanagari",ganada:"hangul",taiwaneseCounting:"cjk-ideographic",taiwaneseCountingThousand:"cjk-ideographic",taiwaneseDigital:"cjk-decimal"};return t[r]??r}refreshTabStops(){this.options.experimental&&setTimeout(()=>{const r=uc();for(let t of this.currentTabs)hc(t.span,t.stops,this.defaultTabSize,r)},500)}createElementNS(r,t,a,n){var i=r?this.htmlDocument.createElementNS(r,t):this.htmlDocument.createElement(t);return Object.assign(i,a),n&&ka(i,n),i}createElement(r,t,a){return this.createElementNS(void 0,r,t,a)}createSvgElement(r,t,a){return this.createElementNS(L.svg,r,t,a)}createStyleElement(r){return this.createElement("style",{innerHTML:r})}createComment(r){return this.htmlDocument.createComment(r)}later(r){this.postRenderTasks.push(r)}}function zi(e){e.innerHTML=""}function ka(e,r){r.forEach(t=>e.appendChild(Ju(t)?document.createTextNode(t):t))}function dc(e,r){for(var t=e.parent;t!=null&&t.type!=r;)t=t.parent;return t}const ko={ignoreHeight:!1,ignoreWidth:!1,ignoreFonts:!1,breakPages:!0,debug:!1,experimental:!1,className:"docx",inWrapper:!0,hideWrapperOnPrint:!1,trimXmlDeclaration:!0,ignoreLastRenderedPageBreak:!0,renderHeaders:!0,renderFooters:!0,renderFootnotes:!0,renderEndnotes:!0,useBase64URL:!1,renderChanges:!1,renderComments:!1,renderAltChunks:!0};function pc(e,r){const t={...ko,...r};return en.load(e,new oc(t),t)}async function mc(e,r,t,a){const n={...ko,...a};return await new cc(window.document).render(e,r,t,n)}async function gc(e,r,t,a){const n=await pc(e,a);return await mc(n,r,t,a),n}export{ko as defaultOptions,pc as parseAsync,gc as renderAsync,mc as renderDocument};
