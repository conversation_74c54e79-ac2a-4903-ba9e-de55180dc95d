import{S as p,P as u,E as S,T as g,U as h,H as D,V as F,W as H,D as O,C as I,X as _,O as b,Y as v,Z as Y,F as k}from"./CGmarHxI.js";function L(A,E,[t,s]=[0,0]){u&&t===0&&S();var a=A,f=null,e=null,i=Y,m=t>0?g:0,n=!1;const N=(c,l=!0)=>{n=!0,o(l,c)},o=(c,l)=>{if(i===(i=c))return;let T=!1;if(u&&s!==-1){if(t===0){const r=h(a);r===D?s=0:r===F?s=1/0:(s=parseInt(r.substring(1)),s!==s&&(s=i?1/0:-1))}const R=s>t;!!i===R&&(a=H(),O(a),I(!1),T=!0,s=-1)}i?(f?_(f):l&&(f=b(()=>l(a))),e&&v(e,()=>{e=null})):(e?_(e):l&&(e=b(()=>l(a,[t+1,s]))),f&&v(f,()=>{f=null})),T&&I(!0)};p(()=>{n=!1,E(N),n||o(null,null)},m),u&&(a=k)}export{L as i};
