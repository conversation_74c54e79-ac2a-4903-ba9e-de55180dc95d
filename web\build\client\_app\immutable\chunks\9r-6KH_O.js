const i=new Map,c=new Map,$=5*60*1e3;function g(s,n){return`${s}:${JSON.stringify(n)}`}let f=0;async function d(s,n={},l={}){const o=g(s,n),p=s.match(/(?:query|mutation)\s+(\w+)/),r=p?p[1]:"UnnamedOperation",t=++f;console.log(`[GraphQL ${t}] Request: ${r}`,{cacheKey:o,variables:n,stack:new Error().stack});const h=s.trim().startsWith("mutation");if(!h&&c.has(o)){const e=c.get(o);if(Date.now()-e.timestamp<$)return console.log(`[GraphQL ${t}] Cache hit for ${r}`),{data:e.data};console.log(`[GraphQL ${t}] Cache expired for ${r}`),c.delete(o)}if(i.has(o))return console.log(`[GraphQL ${t}] In-flight request found for ${r}`),i.get(o);const u=(async()=>{console.log(`[GraphQL ${t}] Executing network request for ${r}`);try{const e=await fetch("/api/graphql",{method:"POST",headers:{"Content-Type":"application/json",...l.headers},body:JSON.stringify({query:s,variables:n}),...l});if(!e.ok){const m=await e.text();throw console.error(`[GraphQL ${t}] Response not OK:`,e.status,m),new Error(`GraphQL request failed with status ${e.status}`)}const a=await e.json();return a.errors?console.error(`[GraphQL ${t}] Errors:`,a.errors):!h&&a.data&&(console.log(`[GraphQL ${t}] Caching result for ${r}`),c.set(o,{data:a.data,timestamp:Date.now()})),console.log(`[GraphQL ${t}] Request completed for ${r}`),a}catch(e){return console.error(`[GraphQL ${t}] Request error:`,e),{errors:[{message:e instanceof Error?e.message:String(e)}]}}finally{i.delete(o)}})();return i.set(o,u),u}const y=`
  query JobListings($filter: JobListingsFilterInput) {
    jobListings(filter: $filter) {
      jobs {
        id
        title
        company
        location
        url
        employmentType
        remoteType
        postedDate
        salary
        applyLink
        companyLogo
      }
      pagination {
        page
        limit
        totalCount
        totalPages
        hasMore
      }
    }
  }
`,C=`
  query Collections {
    collections {
      id
      name
      slug
      platform
      createdAt
    }
  }
`,L=`
  query GetCities($search: String, $country: String, $limit: Int) {
    locations(search: $search, country: $country, limit: $limit) {
      id
      name
      state {
        code
      }
      country
    }
  }
`,q=`
  query GetCompanies($search: String, $limit: Int) {
    companies(search: $search, limit: $limit) {
      id
      name
    }
  }
`;export{L as C,y as J,q as a,C as b,d as g};
