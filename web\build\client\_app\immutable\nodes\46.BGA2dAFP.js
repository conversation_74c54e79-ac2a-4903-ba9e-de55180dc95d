import{f as I,a as e,c as l,t as J}from"../chunks/BasJTneF.js";import{o as K}from"../chunks/nZgk9enP.js";import{p as L,f as r,a as N,s as M,c as Q,r as U,d as a,k,g as o,n as W,t as X}from"../chunks/CGmarHxI.js";import{s as Y}from"../chunks/CIt1g2O9.js";import{i as Z}from"../chunks/u21ee2wt.js";import{e as B,i as H}from"../chunks/C3w0v0gR.js";import{c}from"../chunks/BvdI7LR8.js";import{T as ee,R as re}from"../chunks/I7hvcB12.js";import{S as te}from"../chunks/C6g8ubaU.js";import{component as ae}from"./48.t0_DkjiB.js";import{component as oe}from"./49.C3MkV8mJ.js";import{component as se}from"./47.CK8mzipM.js";import{T as ne}from"../chunks/C88uNE8B.js";import{T as ie}from"../chunks/DmZyh-PW.js";var le=I('<div class="border-border border-b p-0"><!></div> <!>',1),ce=I('<!> <div class="border-border flex items-center justify-between border-b px-4 py-2"><h2 class="text-lg font-semibold">Email Settings</h2></div> <!>',1);function Ce(O,V){L(V,!0);let C=k("analytics"),u=k(!0),P=k(!0);async function q(){a(P,!0);try{const t=await fetch("/api/email/config");if(t.ok){const g=await t.json();a(u,g.resendConfigured===!0)}else a(u,!1)}catch(t){console.error("Error checking Resend API configuration:",t),a(u,!1)}finally{a(P,!1)}}K(()=>{q()});const R=[{id:"analytics",label:"Analytics",component:se},{id:"audiences",label:"Audiences",component:ae},{id:"broadcast",label:"Broadcast",component:oe}];var w=ce(),E=r(w);te(E,{title:"Email Management - Hirli"});var z=M(E,4);c(z,()=>re,(t,g)=>{g(t,{get value(){return o(C)},onValueChange:v=>{a(C,v,!0)},children:(v,me)=>{var A=le(),_=r(A),D=Q(_);c(D,()=>ee,(b,s)=>{s(b,{class:"flex flex-row divide-x",children:(m,S)=>{var d=l(),$=r(d);B($,17,()=>R,H,(h,x)=>{var n=l(),y=r(n);c(y,()=>ne,(T,i)=>{i(T,{get value(){return o(x).id},class:"no-border flex items-center rounded-none p-2",children:(f,j)=>{W();var p=J();X(()=>Y(p,o(x).label)),e(f,p)},$$slots:{default:!0}})}),e(h,n)}),e(m,d)},$$slots:{default:!0}})}),U(_);var F=M(_,2);B(F,17,()=>R,H,(b,s)=>{var m=l(),S=r(m);c(S,()=>ie,(d,$)=>{$(d,{get value(){return o(s).id},class:"space-y-4",children:(h,x)=>{var n=l(),y=r(n);{var T=i=>{var f=l(),j=r(f);c(j,()=>o(s).component,(p,G)=>{G(p,{})}),e(i,f)};Z(y,i=>{o(s).component&&i(T)})}e(h,n)},$$slots:{default:!0}})}),e(b,m)}),e(v,A)},$$slots:{default:!0}})}),e(O,w),N()}export{Ce as component};
