import{c as k,a as t,f as m}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as A}from"../chunks/nZgk9enP.js";import{p as D,f as S,a as E,d as p,m as u,g as l,c as n,r as d,n as L}from"../chunks/CGmarHxI.js";import{i as h}from"../chunks/u21ee2wt.js";import{s as M}from"../chunks/BBa424ah.js";import{i as R}from"../chunks/BIEMS98f.js";import{g as Y}from"../chunks/BiJhC7W5.js";import{S as q}from"../chunks/D8pQCLOH.js";var z=m('<div class="container mx-auto py-8"><div class="flex h-64 items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div></div>'),B=m('<div class="w-full"><!></div>'),C=m(`<div class="container mx-auto py-8"><div class="flex h-64 flex-col items-center justify-center"><!> <h2 class="mb-2 text-2xl font-bold">Access Denied</h2> <p class="text-muted-foreground mb-4">You don't have permission to access email settings.</p> <a href="/dashboard" class="text-primary hover:underline">Return to Dashboard</a></div></div>`);function Q(_,c){D(c,!1);let i=u(!1),f=u(!0);A(async()=>{try{const a=await fetch("/api/user/me");if(a.ok){const e=await a.json();p(i,e.isAdmin===!0)}}catch(a){console.error("Error checking user role:",a)}finally{p(f,!1),l(i)||Y("/dashboard")}}),R();var v=k(),b=S(v);{var x=a=>{var e=z();t(a,e)},y=(a,e)=>{{var g=s=>{var r=B(),o=n(r);M(o,c,"default",{},null),d(r),t(s,r)},w=s=>{var r=C(),o=n(r),j=n(o);q(j,{class:"mb-4 h-16 w-16 text-red-500"}),L(6),d(o),d(r),t(s,r)};h(a,s=>{l(i)?s(g):s(w,!1)},e)}};h(b,a=>{l(f)?a(x):a(y,!1)})}t(_,v),E()}export{Q as component};
