import{c as n,a as o}from"./BasJTneF.js";import{p as b,f as d,a as p,au as h}from"./CGmarHxI.js";import{c as x,s as y}from"./ncUU1dSD.js";import{e as w}from"./w80wGXGd.js";import{e as _}from"./B-Xjo-Yt.js";import{b as k}from"./5V1tIHTN.js";import{p as c,r as B}from"./Btcx8l8F.js";import{c as V}from"./DM07Bv7T.js";const j=V({base:"focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex w-fit shrink-0 items-center justify-center gap-1 overflow-hidden whitespace-nowrap rounded-md border px-2 py-0.5 text-xs font-medium transition-[color,box-shadow] focus-visible:ring-[3px] [&>svg]:pointer-events-none [&>svg]:size-3",variants:{variant:{default:"bg-primary text-primary-foreground [a&]:hover:bg-primary/90 border-transparent",secondary:"bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 border-transparent",destructive:"bg-destructive [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70 border-transparent text-white",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function G(f,r){b(r,!0);let a=c(r,"ref",15,null),v=c(r,"variant",3,"default"),u=B(r,["$$slots","$$events","$$legacy","ref","href","class","variant","children"]);var t=n(),l=d(t);w(l,()=>r.href?"a":"span",!1,(i,g)=>{k(i,e=>a(e),()=>a()),_(i,e=>({"data-slot":"badge",href:r.href,class:e,...u}),[()=>x(j({variant:v()}),r.class)]);var s=n(),m=d(s);y(m,()=>r.children??h),o(g,s)}),o(f,t),p()}export{G as B};
