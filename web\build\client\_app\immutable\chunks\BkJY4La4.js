import{f as i,a as m}from"./BasJTneF.js";import{p as c,a as f,c as n,au as d,r as l}from"./CGmarHxI.js";import{c as u,s as h}from"./ncUU1dSD.js";import{e as _}from"./B-Xjo-Yt.js";import{b as v}from"./5V1tIHTN.js";import{p as b,r as x}from"./Btcx8l8F.js";var g=i("<p><!></p>");function w(e,r){c(r,!0);let a=b(r,"ref",15,null),o=x(r,["$$slots","$$events","$$legacy","ref","class","children"]);var s=g();_(s,t=>({"data-slot":"card-description",class:t,...o}),[()=>u("text-muted-foreground text-sm",r.class)]);var p=n(s);h(p,()=>r.children??d),l(s),v(s,t=>a(t),()=>a()),m(e,s),f()}export{w as C};
