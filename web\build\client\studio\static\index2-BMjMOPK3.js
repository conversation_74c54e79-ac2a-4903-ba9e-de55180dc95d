import{a as P,b3 as L,j as r,N as $,p as w,b8 as C,v as G,b6 as H,y as W,b5 as E,Q as F,ab as R,O as z,b9 as K,ba as M,ac as N,B as k,T as O,bb as Q,b7 as U}from"./sanity-DV0NwVOn.js";const X=w(k)`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0.75rem 0 0.25rem 0;
`,A=w.hr`
  flex: 1;
  background-color: var(--card-border-color);
  height: 1px;
  margin: 0;
  border: none;
`,q=w(O)`
  padding-bottom: 0.75rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
`;function J(f){const e=P.c(5),{item:d}=f,{title:t}=L(d);let n;e[0]!==t?(n=r.jsx(q,{weight:"semibold",muted:!0,size:1,children:t}),e[0]=t,e[1]=n):n=e[1];let o;e[2]===Symbol.for("react.memo_cache_sentinel")?(o=r.jsx(A,{}),e[2]=o):o=e[2];let s;return e[3]!==n?(s=r.jsxs(X,{children:[n,o]}),e[3]=n,e[4]=s):s=e[4],s}function V(f){const e=P.c(20),{childItemId:d,items:t,isActive:n,layout:o,showIcons:s,title:l}=f,{collapsed:g}=z();let u;e[0]!==t?(u=t==null?void 0:t.filter(Y),e[0]=t,e[1]=u):u=e[1];const c=K(u);let i;e[2]!==t?(i=a=>{var y;return((y=t==null?void 0:t.find((j,T)=>T===a))==null?void 0:y.type)==="divider"},e[2]=t,e[3]=i):i=e[3];const m=i;let v;e[4]!==s?(v=a=>{var j;const y=(j=a.displayOptions)==null?void 0:j.showIcon;return typeof y<"u"?y!==!1:s!==!1},e[4]=s,e[5]=v):v=e[5];const h=v;let p;e[6]!==d||e[7]!==c||e[8]!==n||e[9]!==o||e[10]!==h?(p=(a,y)=>{const{virtualIndex:j}=y;if(a.type==="divider")return r.jsx(k,{marginBottom:1,children:a.title?r.jsx(J,{item:a}):r.jsx(A,{})},`divider-${j}`);const T=!n&&d===a.id,D=n&&d===a.id,S=a._id&&a.schemaType?{_id:a._id,_type:a.schemaType.name,title:a.title}:void 0;return r.jsx(Q,{icon:h(a)?a.icon:!1,id:a.id,layout:o,marginBottom:1,pressed:T,schemaType:a.schemaType,selected:D,title:c(a).title,value:S},a.id)},e[6]=d,e[7]=c,e[8]=n,e[9]=o,e[10]=h,e[11]=p):p=e[11];const I=p,b=g?"hidden":"auto";let x;e[12]!==m||e[13]!==t||e[14]!==I||e[15]!==l?(x=t&&t.length>0&&r.jsx(M,{activeItemDataAttr:"data-hovered",ariaLabel:l,canReceiveFocus:!0,getItemDisabled:m,itemHeight:51,items:t,onlyShowSelectionWhenActive:!0,paddingBottom:1,paddingX:3,renderItem:I,wrapAround:!1}),e[12]=m,e[13]=t,e[14]=I,e[15]=l,e[16]=x):x=e[16];let B;return e[17]!==b||e[18]!==x?(B=r.jsx(N,{overflow:b,children:x}),e[17]=b,e[18]=x,e[19]=B):B=e[19],B}function Y(f){return f.type!=="divider"}const Z=f=>{const e=P.c(11),{index:d,menuItems:t,menuItemGroups:n,title:o}=f,{features:s}=G(),{collapsed:l,isLast:g}=H(),u=g&&!l?-1:0;let c;e[0]!==n||e[1]!==t?(c=r.jsx(U,{menuItems:t,menuItemGroups:n}),e[0]=n,e[1]=t,e[2]=c):c=e[2];let i;e[3]!==s.backButton||e[4]!==d?(i=s.backButton&&d>0&&r.jsx(W,{as:F,"data-as":"a",icon:E,mode:"bleed",tooltipProps:{content:"Back"}}),e[3]=s.backButton,e[4]=d,e[5]=i):i=e[5];let m;return e[6]!==c||e[7]!==i||e[8]!==u||e[9]!==o?(m=r.jsx(R,{actions:c,backButton:i,tabIndex:u,title:o}),e[6]=c,e[7]=i,e[8]=u,e[9]=o,e[10]=m):m=e[10],m};function ee(f){const e=P.c(21),{childItemId:d,index:t,isActive:n,isSelected:o,pane:s,paneKey:l}=f,{defaultLayout:g,displayOptions:u,items:c,menuItems:i,menuItemGroups:m}=s,v=(u==null?void 0:u.showIcons)!==!1,{title:h}=L(s);let p;e[0]!==s.source?(p=C,e[0]=s.source,e[1]=p):p=e[1];let I;e[2]!==t||e[3]!==m||e[4]!==i||e[5]!==h?(I=r.jsx(Z,{index:t,menuItems:i,menuItemGroups:m,title:h}),e[2]=t,e[3]=m,e[4]=i,e[5]=h,e[6]=I):I=e[6];let b;e[7]!==d||e[8]!==g||e[9]!==n||e[10]!==c||e[11]!==l||e[12]!==v||e[13]!==h?(b=r.jsx(V,{childItemId:d,isActive:n,items:c,layout:g,showIcons:v,title:h},l),e[7]=d,e[8]=g,e[9]=n,e[10]=c,e[11]=l,e[12]=v,e[13]=h,e[14]=b):b=e[14];let x;return e[15]!==o||e[16]!==l||e[17]!==p||e[18]!==I||e[19]!==b?(x=r.jsxs($,{currentMaxWidth:350,"data-testid":"structure-tool-list-pane","data-ui":"ListPane",id:l,maxWidth:640,minWidth:320,selected:o,children:[p,I,b]}),e[15]=o,e[16]=l,e[17]=p,e[18]=I,e[19]=b,e[20]=x):x=e[20],x}export{ee as default};
