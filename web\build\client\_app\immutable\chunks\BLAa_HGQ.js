import{M as r,m as e,a as s,b as a}from"./BGYDhraB.js";const n=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,i=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,o=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,l=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,d=r.create({name:"italic",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:t=>t.style.fontStyle!=="normal"&&null},{style:"font-style=normal",clearMark:t=>t.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:t}){return["em",a(this.options.HTMLAttributes,t),0]},addCommands(){return{setItalic:()=>({commands:t})=>t.setMark(this.name),toggleItalic:()=>({commands:t})=>t.toggleMark(this.name),unsetItalic:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[s({find:n,type:this.type}),s({find:o,type:this.type})]},addPasteRules(){return[e({find:i,type:this.type}),e({find:l,type:this.type})]}});export{d as Italic,d as default,n as starInputRegex,i as starPasteRegex,o as underscoreInputRegex,l as underscorePasteRegex};
