import{d as e}from"./sanity-DV0NwVOn.js";const t=e("vision",{"action.copy-url-to-clipboard":"Copy to clipboard","action.delete":"Delete","action.edit-title":"Edit title","action.listen-cancel":"Stop","action.listen-execute":"Listen","action.load-queries":"Load queries","action.load-query":"Load query","action.query-cancel":"Cancel","action.query-execute":"Fetch","action.save-query":"Save query","action.update":"Update","label.actions":"Actions","label.edited":"Edited","label.new":"New","label.personal":"Personal","label.saved-at":"Saved at","label.saved-queries":"Saved queries","label.search-queries":"Search queries","label.share":"Share","label.team":"Team","params.error.params-invalid-json":"Parameters are not valid JSON","params.label":"Params","query.error.column":"Column","query.error.line":"Line","query.label":"Query","query.url":"Query URL","result.end-to-end-time-label":"End-to-end","result.execution-time-label":"Execution","result.label":"Result","result.save-result-as-csv.not-csv-encodable":"Result cannot be encoded as CSV","result.save-result-as-format":"Save result as <SaveResultButtons/>","result.timing-not-applicable":"n/a","save-query.already-saved":"Query already saved","save-query.error":"Error saving query","save-query.success":"Query saved","settings.api-version-label":"API version","settings.custom-api-version-label":"Custom API version","settings.dataset-label":"Dataset","settings.error.invalid-api-version":"Invalid API version","settings.other-api-version-label":"Other","settings.perspective-label":"Perspective","settings.perspective.preview-drafts-renamed-to-drafts.description":'The "<code>previewDrafts</code>" perspective has been renamed to "<code>drafts</code>" and is now deprecated. This change is effective for all versions with perspective support (>= v2021-03-25).',"settings.perspectives.action.docs-link":"Read docs","settings.perspectives.default":"No perspective (API default)","settings.perspectives.description":'Perspectives allow your query to run against different "views" of the content in your dataset',"settings.perspectives.new-default.description":'The default perspective will change from "<code>raw</code>" to "<code>published</code>" in an upcoming API version. Please consult docs for more details.',"settings.perspectives.pinned-release-label":"Pinned release","settings.perspectives.title":"Perspectives"});export{t as default};
