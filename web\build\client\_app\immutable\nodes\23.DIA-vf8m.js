import{f as v,a as d}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as sa,f as _,t as p,a as oa,g as x,e as f,s,c as o,n as F,r as t}from"../chunks/CGmarHxI.js";import{s as g}from"../chunks/CIt1g2O9.js";import{i as l}from"../chunks/u21ee2wt.js";import{e as H,i as L}from"../chunks/C3w0v0gR.js";import{a as h}from"../chunks/B-Xjo-Yt.js";import{i as na}from"../chunks/BIEMS98f.js";import{p as da}from"../chunks/Btcx8l8F.js";import{S as va}from"../chunks/C6g8ubaU.js";import{u as O}from"../chunks/DosGZj-c.js";import{P as la}from"../chunks/BMgaXnEE.js";import{P as ma}from"../chunks/Dy6ycI81.js";import{A as pa}from"../chunks/Ce6y1v79.js";var ca=v('<img class="h-8 w-8 rounded-full object-cover"/>'),fa=v('<div class="flex items-center gap-2"><!> <span> </span></div> <span class="hidden md:inline">•</span>',1),ga=v('<span class="hidden md:inline">•</span> <span> </span>',1),_a=v('<span class="rounded-full bg-gray-100 px-3 py-1 text-xs"> </span>'),xa=v('<span class="hidden md:inline">•</span> <div class="flex flex-wrap gap-2"></div>',1),ha=v('<div class="mb-8 overflow-hidden rounded-lg"><img class="h-auto w-full object-cover"/></div>'),ua=v("<p>Content not available.</p>"),ba=v('<div class="mt-16"><h2 class="mb-8 text-2xl font-bold">Related Articles</h2> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3"></div></div>'),wa=v('<!> <div class="container mx-auto max-w-4xl px-4 py-12"><div class="mb-8"><a href="/blog" class="text-primary mb-4 inline-flex items-center hover:underline"><!> Back to Blog</a> <h1 class="mb-4 text-3xl font-bold md:text-4xl"> </h1> <div class="mb-6 flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-gray-600"><!> <span> </span> <!> <!></div></div> <!> <div class="prose prose-lg max-w-none"><!></div> <!></div>',1);function Fa(U,k){sa(k,!1);let z=da(k,"data",8);const{post:e,relatedPosts:u}=z();function G(a){return new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}na();var D=wa(),T=_(D);const J=f(()=>e.excerpt||`Read ${e.title} on the Hirli blog`),K=f(()=>{var a;return((a=e.categories)==null?void 0:a.map(r=>r.title).join(", "))||"blog, career advice, job search"});va(T,{get title(){return e.title},get description(){return x(J)},get keywords(){return x(K)}});var B=s(T,2),b=o(B),w=o(b),M=o(w);pa(M,{class:"mr-2 h-4 w-4"}),F(),t(w);var y=s(w,2),N=o(y,!0);t(y);var C=s(y,2),I=o(C);{var Q=a=>{var r=fa(),i=_(r),n=o(i);{var c=R=>{var S=ca();p(ia=>{h(S,"src",ia),h(S,"alt",e.author.name)},[()=>O(e.author.image,{width:40,height:40})],f),d(R,S)};l(n,R=>{e.author.image&&R(c)})}var m=s(n,2),A=o(m,!0);t(m),t(i),F(2),p(()=>g(A,e.author.name)),d(a,r)};l(I,a=>{e.author&&a(Q)})}var P=s(I,2),V=o(P,!0);t(P);var q=s(P,2);{var W=a=>{var r=ga(),i=s(_(r),2),n=o(i);t(i),p(()=>g(n,`${e.estimatedReadingTime??""} min read`)),d(a,r)};l(q,a=>{e.estimatedReadingTime&&a(W)})}var X=s(q,2);{var Y=a=>{var r=xa(),i=s(_(r),2);H(i,5,()=>e.categories,L,(n,c)=>{var m=_a(),A=o(m,!0);t(m),p(()=>g(A,x(c).title)),d(n,m)}),t(i),d(a,r)};l(X,a=>{e.categories&&e.categories.length>0&&a(Y)})}t(C),t(b);var E=s(b,2);{var Z=a=>{var r=ha(),i=o(r);t(r),p(n=>{h(i,"src",n),h(i,"alt",e.title)},[()=>O(e.mainImage,{width:1200,height:600})],f),d(a,r)};l(E,a=>{e.mainImage&&a(Z)})}var j=s(E,2),$=o(j);{var aa=a=>{la(a,{get value(){return e.body}})},ea=a=>{var r=ua();d(a,r)};l($,a=>{e.body?a(aa):a(ea,!1)})}t(j);var ra=s(j,2);{var ta=a=>{var r=ba(),i=s(o(r),2);H(i,5,()=>u,L,(n,c)=>{ma(n,{get post(){return x(c)}})}),t(i),t(r),d(a,r)};l(ra,a=>{u&&u.length>0&&a(ta)})}t(B),p(a=>{g(N,e.title),g(V,a)},[()=>G(e.publishedAt)],f),d(U,D),oa()}export{Fa as component};
