import{f as h,t as p,a,c as qe}from"../chunks/BasJTneF.js";import{p as Et,c as i,n as l,s as t,r,t as N,g as e,m as oe,q as ft,aL as _,a as Mt,d as b,l as er,b as $r,f as E,e as Ct,x as wr,aM as Pr,k as vt}from"../chunks/CGmarHxI.js";import{s as B}from"../chunks/CIt1g2O9.js";import{i as de}from"../chunks/u21ee2wt.js";import{e as Ue,i as Re}from"../chunks/C3w0v0gR.js";import{c as ne}from"../chunks/BvdI7LR8.js";import{C as dr}from"../chunks/Cdn-N1RY.js";import{T as Fr,R as kr}from"../chunks/I7hvcB12.js";import{B as Dr}from"../chunks/B1K98fMG.js";import{S as Tr,a as Ar,b as Lr,R as Cr}from"../chunks/CGK0g3x_.js";import{S as jr}from"../chunks/C6g8ubaU.js";import"../chunks/CgXBgsce.js";import{t as Z}from"../chunks/DjPYYl4Z.js";import{r as D,d as pt,f as Sr}from"../chunks/B-Xjo-Yt.js";import{j as lr,e as jt,g as Er}from"../chunks/CmxjS0TN.js";import{b as L,c as ur}from"../chunks/CzsE_FAw.js";import{p as tr}from"../chunks/CWmzcjye.js";import{i as cr}from"../chunks/BIEMS98f.js";import{C as Mr}from"../chunks/DuGukytH.js";import{L as y}from"../chunks/BvvicRXk.js";import{a as Ze,L as P}from"../chunks/iTBjRg9v.js";import{o as vr,c as rr,s as Je,n as fr,a as Ir,b as Vr}from"../chunks/C8B1VUaq.js";import{P as mt}from"../chunks/DR5zc253.js";import{T as St}from"../chunks/Bpd96RWU.js";import{o as Or}from"../chunks/nZgk9enP.js";import{A as Br,a as Nr,b as qr,c as Ur}from"../chunks/BPr9JIwg.js";import{R as Rr,A as Jr,a as Gr,b as zr,c as Xr,d as Yr,e as Hr,f as Kr}from"../chunks/BnikQ10_.js";import{B as ir}from"../chunks/DaBofrVv.js";import{L as pr}from"../chunks/BhzFx1Wy.js";import{X as Qr}from"../chunks/CnpHcmx3.js";import{S as Wr}from"../chunks/BHzYYMdu.js";import{S as Zr}from"../chunks/DumgozFE.js";import{T as ar}from"../chunks/C88uNE8B.js";import{T as or}from"../chunks/DmZyh-PW.js";import{S as ei}from"../chunks/B2lQHLf_.js";import{B as ti}from"../chunks/CDnvByek.js";import{C as ri}from"../chunks/A-1J-2PQ.js";import{L as ii}from"../chunks/CLdCqm7k.js";const ai=vr({id:Je().min(1,{message:"Limit ID is required"}),name:Je().min(1,{message:"Limit name is required"}),description:Je().optional(),defaultValue:rr.number().min(0,{message:"Default value must be a positive number"}),type:fr(P),unit:Je().optional(),resetDay:rr.number().min(1).max(31).optional()});vr({id:Je().min(1,{message:"Feature ID is required"}),name:Je().min(1,{message:"Feature name is required"}),description:Je().optional(),category:fr(Ze),icon:Je().optional(),beta:Vr().default(!1),limits:Ir(ai).default([])});const sr={id:"",name:"",description:"",category:Ze.Core,icon:"",beta:!1,limits:[]},oi={id:"",name:"",description:"",defaultValue:10,type:P.Monthly,unit:"",resetDay:1};var si=h("<option> </option>"),ni=h('<div class="flex items-center justify-between rounded-md border p-2"><div><div class="font-medium"> </div> <div class="text-muted-foreground text-xs"> </div> <div class="text-muted-foreground text-xs"> </div></div> <button class="text-destructive hover:bg-destructive/10 hover:text-destructive rounded-full p-1"><!></button></div>'),di=h('<div class="mb-4 space-y-2"></div>'),li=h('<div class="text-muted-foreground mb-4 rounded-md border border-dashed p-4 text-center">No limits defined for this feature.</div>'),ui=h('<form><div class="grid grid-cols-2 gap-4"><div class="space-y-2"><!> <input id="feature-id" placeholder="e.g. custom_reports" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/> <p class="text-muted-foreground text-xs">A unique identifier for the feature</p></div> <div class="space-y-2"><!> <input id="feature-name" placeholder="e.g. Custom Reports" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/> <p class="text-muted-foreground text-xs">Display name for the feature</p></div> <div class="col-span-2 space-y-2"><!> <textarea id="feature-description" placeholder="Describe what this feature does" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[60px] w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"></textarea> <p class="text-muted-foreground text-xs">Detailed explanation of the feature</p></div> <div class="space-y-2"><!> <select id="feature-category" class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"></select> <p class="text-muted-foreground text-xs">Group this feature belongs to</p></div> <div class="space-y-2"><!> <input id="feature-icon" placeholder="e.g. file-bar-chart" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/> <p class="text-muted-foreground text-xs">Lucide icon name for the feature</p></div> <div class="col-span-2 flex items-center space-y-2"><input type="checkbox" id="feature-beta" class="mr-2"/> <!></div> <div class="col-span-2 mt-4 border-t pt-4"><h4 class="mb-2 text-base font-semibold">Feature Limits</h4> <!> <div class="rounded-md border p-3"><h5 class="mb-2 text-sm font-semibold">Add New Limit</h5> <div class="grid grid-cols-2 gap-2"><div><!> <input id="new-limit-id" placeholder="e.g. monthly_usage" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <input id="new-limit-name" placeholder="e.g. Monthly Usage" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="col-span-2"><!> <input id="new-limit-description" placeholder="e.g. Maximum usage per month" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <input id="new-limit-default" type="number" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <input id="new-limit-unit" placeholder="e.g. uses, items, GB" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <select id="new-limit-type" class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"><option>Monthly</option><option>Total</option><option>Concurrent</option><option>Unlimited</option></select></div> <div><!> <input id="new-limit-reset" type="number" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="col-span-2 mt-2"><button type="button" class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"><!> Add Limit</button></div></div></div></div></div> <div class="mt-6 flex justify-end"><button type="submit" class="bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex h-9 items-center justify-center rounded-md px-4 py-2 text-sm font-medium shadow transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"><!> Add Feature</button></div></form>');function ci(le,M){Et(M,!1);let d=oe({...sr}),u=oe({...oi});function Pe(){if(!e(u).id||!e(u).name){Z.error("Limit ID and name are required");return}_(d,e(d).limits=[...e(d).limits,{...e(u)}]),b(u,{id:"",name:"",description:"",defaultValue:10,type:P.Monthly,unit:"",resetDay:1})}function Fe(z){_(d,e(d).limits=e(d).limits.filter(T=>T.id!==z))}async function ke(z){z.preventDefault();try{if(!e(d).id||!e(d).name){Z.error("Feature ID and name are required");return}const T=await fetch("/api/admin/features",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({action:"add_feature",feature:e(d)})});if(!T.ok){const c=await T.text();throw new Error(c||`Failed to add feature: ${T.status}`)}const se=await T.json();console.log("Feature added response:",se),Z.success("Feature added",{description:`Feature ${e(d).name} added successfully`,duration:3e3}),b(d,{...sr}),window.dispatchEvent(new CustomEvent("featureAdded"))}catch(T){console.error("Error adding feature:",T),Z.error("Failed to add feature",{description:T.message,duration:5e3})}}cr(),Mr(le,{children:(z,T)=>{dr(z,{children:(se,c)=>{var m=ui(),be=i(m),ue=i(be),et=i(ue);y(et,{for:"feature-id",children:(o,g)=>{l();var v=p("Feature ID");a(o,v)},$$slots:{default:!0}});var tt=t(et,2);D(tt),l(2),r(ue);var Ge=t(ue,2),rt=i(Ge);y(rt,{for:"feature-name",children:(o,g)=>{l();var v=p("Feature Name");a(o,v)},$$slots:{default:!0}});var C=t(rt,2);D(C),l(2),r(Ge);var $=t(Ge,2),w=i($);y(w,{for:"feature-description",children:(o,g)=>{l();var v=p("Description");a(o,v)},$$slots:{default:!0}});var ee=t(w,2);lr(ee),l(2),r($);var X=t($,2),_e=i(X);y(_e,{for:"feature-category",children:(o,g)=>{l();var v=p("Category");a(o,v)},$$slots:{default:!0}});var De=t(_e,2);N(()=>{e(d),ft(()=>{})}),Ue(De,5,()=>Object.values(Ze),Re,(o,g)=>{var v=si(),k={},ae=i(v,!0);r(v),N(()=>{k!==(k=e(g))&&(v.value=(v.__value=e(g))??""),B(ae,e(g))}),a(o,v)}),r(De),l(2),r(X);var ge=t(X,2),te=i(ge);y(te,{for:"feature-icon",children:(o,g)=>{l();var v=p("Icon (optional)");a(o,v)},$$slots:{default:!0}});var s=t(te,2);D(s),l(2),r(ge);var f=t(ge,2),j=i(f);D(j);var ce=t(j,2);y(ce,{for:"feature-beta",children:(o,g)=>{l();var v=p("Beta Feature");a(o,v)},$$slots:{default:!0}}),r(f);var q=t(f,2),A=t(i(q),2);{var re=o=>{var g=di();Ue(g,5,()=>e(d).limits,Re,(v,k)=>{var ae=ni(),me=i(ae),we=i(me),Ke=i(we,!0);r(we);var Qe=t(we,2),nt=i(Qe);r(Qe);var Ne=t(Qe,2),dt=i(Ne,!0);r(Ne),r(me);var ot=t(me,2),ut=i(ot);St(ut,{class:"h-4 w-4"}),r(ot),r(ae),N(()=>{B(Ke,e(k).name),B(nt,`ID: ${e(k).id??""} | Default: ${e(k).defaultValue??""}
                      ${(e(k).unit||"")??""}`),B(dt,e(k).description)}),jt("click",ot,tr(()=>Fe(e(k).id))),a(v,ae)}),r(g),a(o,g)},U=o=>{var g=li();a(o,g)};de(A,o=>{e(d).limits&&e(d).limits.length>0?o(re):o(U,!1)})}var Y=t(A,2),R=t(i(Y),2),J=i(R),ie=i(J);y(ie,{for:"new-limit-id",class:"text-xs",children:(o,g)=>{l();var v=p("Limit ID");a(o,v)},$$slots:{default:!0}});var S=t(ie,2);D(S),r(J);var I=t(J,2),G=i(I);y(G,{for:"new-limit-name",class:"text-xs",children:(o,g)=>{l();var v=p("Name");a(o,v)},$$slots:{default:!0}});var Q=t(G,2);D(Q),r(I);var H=t(I,2),Te=i(H);y(Te,{for:"new-limit-description",class:"text-xs",children:(o,g)=>{l();var v=p("Description");a(o,v)},$$slots:{default:!0}});var Ee=t(Te,2);D(Ee),r(H);var V=t(H,2),ve=i(V);y(ve,{for:"new-limit-default",class:"text-xs",children:(o,g)=>{l();var v=p("Default Value");a(o,v)},$$slots:{default:!0}});var O=t(ve,2);D(O),r(V);var Ae=t(V,2),it=i(Ae);y(it,{for:"new-limit-unit",class:"text-xs",children:(o,g)=>{l();var v=p("Unit (optional)");a(o,v)},$$slots:{default:!0}});var at=t(it,2);D(at),r(Ae);var W=t(Ae,2),K=i(W);y(K,{for:"new-limit-type",class:"text-xs",children:(o,g)=>{l();var v=p("Limit Type");a(o,v)},$$slots:{default:!0}});var ze=t(K,2);N(()=>{e(u),ft(()=>{})});var Xe=i(ze),fe={},xe=t(Xe),he={},pe=t(xe),Le={},Ye=t(pe),ye={};r(ze),r(W);var Me=t(W,2),Ce=i(Me);y(Ce,{for:"new-limit-reset",class:"text-xs",children:(o,g)=>{l();var v=p("Reset Day (Monthly only)");a(o,v)},$$slots:{default:!0}});var je=t(Ce,2);D(je),r(Me);var He=t(Me,2),Ie=i(He),Ve=i(Ie);mt(Ve,{class:"mr-1.5 h-4 w-4"}),l(),r(Ie),r(He),r(R),r(Y),r(q),r(be);var Oe=t(be,2),Be=i(Oe),$e=i(Be);mt($e,{class:"mr-2 h-4 w-4"}),l(),r(Be),r(Oe),r(m),N(()=>{fe!==(fe=P.Monthly)&&(Xe.value=(Xe.__value=P.Monthly)??""),he!==(he=P.Total)&&(xe.value=(xe.__value=P.Total)??""),Le!==(Le=P.Concurrent)&&(pe.value=(pe.__value=P.Concurrent)??""),ye!==(ye=P.Unlimited)&&(Ye.value=(Ye.__value=P.Unlimited)??""),je.disabled=e(u).type!==P.Monthly}),L(tt,()=>e(d).id,o=>_(d,e(d).id=o)),L(C,()=>e(d).name,o=>_(d,e(d).name=o)),L(ee,()=>e(d).description,o=>_(d,e(d).description=o)),pt(De,()=>e(d).category,o=>_(d,e(d).category=o)),L(s,()=>e(d).icon,o=>_(d,e(d).icon=o)),ur(j,()=>e(d).beta,o=>_(d,e(d).beta=o)),L(S,()=>e(u).id,o=>_(u,e(u).id=o)),L(Q,()=>e(u).name,o=>_(u,e(u).name=o)),L(Ee,()=>e(u).description,o=>_(u,e(u).description=o)),L(O,()=>e(u).defaultValue,o=>_(u,e(u).defaultValue=o)),L(at,()=>e(u).unit,o=>_(u,e(u).unit=o)),pt(ze,()=>e(u).type,o=>_(u,e(u).type=o)),L(je,()=>e(u).resetDay,o=>_(u,e(u).resetDay=o)),jt("click",Ie,tr(()=>Pe())),jt("submit",m,ke),a(se,m)},$$slots:{default:!0}})},$$slots:{default:!0}}),Mt()}var vi=h('<div class="flex h-64 items-center justify-center"><div class="text-center"><div class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div> <p class="mt-4 text-lg">Loading features...</p></div></div>'),fi=h('<div class="mb-4 rounded-lg bg-red-100 p-4 text-sm text-red-700"><p> </p></div>'),pi=h("<option> </option>"),mi=(le,M,d)=>M(e(d).id),bi=h('<div class="flex items-center justify-between rounded-md border p-2"><div><div class="font-medium"> </div> <div class="text-muted-foreground text-xs"> </div> <div class="text-muted-foreground text-xs"> </div></div> <button class="text-destructive hover:text-destructive/80 hover:bg-destructive/10 rounded-full p-1"><!></button></div>'),_i=h('<div class="mb-4 space-y-2"></div>'),gi=h('<div class="text-muted-foreground mb-4 rounded-md border border-dashed p-4 text-center">No limits defined for this feature.</div>'),xi=(le,M)=>M(),hi=(le,M)=>M(),yi=(le,M)=>M(),$i=h('<div class="grid grid-cols-2 gap-4"><div class="space-y-2"><!> <input id="edit-feature-id" disabled class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="space-y-2"><!> <input id="edit-feature-name" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="col-span-2 space-y-2"><!> <textarea id="edit-feature-description" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[80px] w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"></textarea></div> <div class="space-y-2"><!> <select id="edit-feature-category" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"></select></div> <div class="space-y-2"><!> <input id="edit-feature-icon" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="col-span-2 flex items-center space-y-2"><input type="checkbox" id="edit-feature-beta" class="mr-2"/> <!></div> <div class="col-span-2 mt-4 border-t pt-4"><h4 class="mb-2 text-base font-semibold">Feature Limits</h4> <!> <div class="rounded-md border p-3"><h5 class="mb-2 text-sm font-semibold">Add New Limit</h5> <div class="grid grid-cols-2 gap-2"><div><!> <input id="new-limit-id" placeholder="e.g. monthly_usage" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <input id="new-limit-name" placeholder="e.g. Monthly Usage" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="col-span-2"><!> <input id="new-limit-description" placeholder="e.g. Maximum usage per month" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <input id="new-limit-default" type="number" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <input id="new-limit-unit" placeholder="e.g. uses, items, GB" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <select id="new-limit-type" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"><option>Monthly</option><option>Total</option><option>Concurrent</option><option>Unlimited</option></select></div> <div><!> <input id="new-limit-reset" type="number" min="1" max="31" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="col-span-2 mt-2"><button class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"><!> Add Limit</button></div></div></div></div> <div class="col-span-2 mt-4 flex justify-end space-x-2"><button class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-9 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"><!> Cancel</button> <button class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-9 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"><!> Save Changes</button></div></div>'),wi=h('<div class="mt-2"><p class="text-xs font-semibold">Limits:</p> <div class="mt-1 flex flex-wrap gap-1.5"></div></div>'),Pi=(le,M,d)=>M(e(d)),Fi=(le,M,d)=>M(e(d).id),ki=h('<div class="flex items-center justify-between"><div><h4 class="text-base font-semibold"> </h4> <p class="text-muted-foreground text-sm"> </p> <p class="text-muted-foreground text-sm"> </p> <!></div> <div class="flex items-center gap-2"><!> <button class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-xs font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"><!> Edit</button> <button class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-xs font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50"><!> Delete</button></div></div>'),Di=h('<div class="bover:border-primary/30 hover:bg-primary/5 rounded-md p-4 transition-colors"><!></div>'),Ti=h("<!> <!>",1),Ai=h("<!> <!>",1),Li=h("<!> Deleting...",1),Ci=h("<!> <!>",1),ji=h("<!> <!>",1),Si=h("<!> <!>",1);function Ei(le,M){Et(M,!1);const d=oe();let u=oe([]),Pe=oe(!0),Fe=oe(null),ke=oe(!1),z=oe(null),T=oe(!1),se=oe(null),c=oe({id:"",name:"",description:"",category:Ze.Core,icon:"",beta:!1,limits:[]}),m=oe({id:"",name:"",description:"",defaultValue:10,type:P.Monthly,unit:"",resetDay:1}),be=oe([]);async function ue(){try{b(Pe,!0),b(Fe,null);const s=await fetch("/api/admin/features",{credentials:"include"});if(!s.ok)throw new Error(`Failed to load features: ${s.status}`);const f=await s.json();console.log("Loaded features:",f),f&&f.features?b(u,f.features):Array.isArray(f)?b(u,f):b(u,[]),Object.keys(e(d)).length>0&&e(be).length===0&&b(be,[Object.keys(e(d))[0]])}catch(s){console.error("Error loading features:",s),b(Fe,s.message)}finally{b(Pe,!1)}}function et(s){b(z,s),b(ke,!0)}async function tt(){if(e(z))try{b(T,!0);const s=await fetch("/api/admin/features",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({action:"remove_feature",featureId:e(z)})});if(!s.ok){const f=await s.text();throw new Error(f||`Failed to remove feature: ${s.status}`)}await s.json(),Z.success("Feature removed",{description:`Feature ${e(z)} removed successfully`,duration:3e3}),b(ke,!1),b(z,null),await ue()}catch(s){console.error("Error removing feature:",s),Z.error("Failed to remove feature",{description:s.message,duration:5e3})}finally{b(T,!1)}}function Ge(s){b(se,s.id),b(c,{...s})}function rt(){b(se,null),b(c,{id:"",name:"",description:"",category:Ze.Core,icon:"",beta:!1,limits:[]})}function C(){if(!e(m).id||!e(m).name){Z.error("Limit ID and name are required");return}_(c,e(c).limits=[...e(c).limits,{...e(m)}]),b(m,{id:"",name:"",description:"",defaultValue:10,type:P.Monthly,unit:"",resetDay:1})}function $(s){_(c,e(c).limits=e(c).limits.filter(f=>f.id!==s))}async function w(){try{if(!e(c).id||!e(c).name){Z.error("Feature ID and name are required");return}const s=await fetch("/api/admin/features",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({action:"update_feature",feature:e(c)})});if(!s.ok){const f=await s.text();throw new Error(f||`Failed to update feature: ${s.status}`)}await s.json(),Z.success("Feature updated",{description:`Feature ${e(c).name} updated successfully`,duration:3e3}),b(se,null),b(c,{id:"",name:"",description:"",category:Ze.Core,icon:"",beta:!1,limits:[]}),await ue()}catch(s){console.error("Error updating feature:",s),Z.error("Failed to update feature",{description:s.message,duration:5e3})}}function ee(){ue()}Or(()=>(ue(),window.addEventListener("featureAdded",ee),()=>{window.removeEventListener("featureAdded",ee)})),er(()=>e(u),()=>{b(d,Array.isArray(e(u))?e(u).reduce((s,f)=>{const j=f.category||"uncategorized";return s[j]||(s[j]=[]),f.limits||(f.limits=[]),s[j].push(f),s},{}):{})}),er(()=>e(d),()=>{Object.keys(e(d)).forEach(s=>{Array.isArray(e(d)[s])||_(d,e(d)[s]=[])})}),$r(),cr();var X=Si(),_e=E(X);{var De=s=>{var f=vi();a(s,f)},ge=(s,f)=>{{var j=q=>{var A=fi(),re=i(A),U=i(re);r(re),r(A),N(()=>B(U,`Error loading features: ${e(Fe)??""}`)),a(q,A)},ce=q=>{Br(q,{class:"border-border w-full divide-x border",type:"multiple",get value(){return e(be)},children:(A,re)=>{var U=qe(),Y=E(U);Ue(Y,1,()=>Object.entries(e(d)),Re,(R,J)=>{var ie=wr(()=>Pr(e(J),2));let S=()=>e(ie)[0],I=()=>e(ie)[1];Nr(R,{get value(){return S()},children:(G,Q)=>{var H=Ti(),Te=E(H);qr(Te,{class:"px-4 text-base",children:(V,ve)=>{l();var O=p();N(Ae=>B(O,`${Ae??""} Features`),[()=>S().charAt(0).toUpperCase()+S().slice(1)],Ct),a(V,O)},$$slots:{default:!0}});var Ee=t(Te,2);Ur(Ee,{class:"border-border border-t",children:(V,ve)=>{var O=qe(),Ae=E(O);Ue(Ae,1,I,Re,(it,at)=>{var W=Di();const K=Ct(()=>e(at));var ze=i(W);{var Xe=xe=>{var he=$i(),pe=i(he),Le=i(pe);y(Le,{for:"edit-feature-id",children:(n,F)=>{l();var x=p("Feature ID");a(n,x)},$$slots:{default:!0}});var Ye=t(Le,2);D(Ye),r(pe);var ye=t(pe,2),Me=i(ye);y(Me,{for:"edit-feature-name",children:(n,F)=>{l();var x=p("Feature Name");a(n,x)},$$slots:{default:!0}});var Ce=t(Me,2);D(Ce),r(ye);var je=t(ye,2),He=i(je);y(He,{for:"edit-feature-description",children:(n,F)=>{l();var x=p("Description");a(n,x)},$$slots:{default:!0}});var Ie=t(He,2);lr(Ie),r(je);var Ve=t(je,2),Oe=i(Ve);y(Oe,{for:"edit-feature-category",children:(n,F)=>{l();var x=p("Category");a(n,x)},$$slots:{default:!0}});var Be=t(Oe,2);N(()=>{e(c),ft(()=>{})}),Ue(Be,5,()=>Object.values(Ze),Re,(n,F,x,We)=>{var Se=pi(),st={},lt=i(Se,!0);r(Se),N(()=>{st!==(st=e(F))&&(Se.value=(Se.__value=e(F))??""),B(lt,e(F))}),a(n,Se)}),r(Be),r(Ve);var $e=t(Ve,2),o=i($e);y(o,{for:"edit-feature-icon",children:(n,F)=>{l();var x=p("Icon (optional)");a(n,x)},$$slots:{default:!0}});var g=t(o,2);D(g),r($e);var v=t($e,2),k=i(v);D(k);var ae=t(k,2);y(ae,{for:"edit-feature-beta",children:(n,F)=>{l();var x=p("Beta Feature");a(n,x)},$$slots:{default:!0}}),r(v);var me=t(v,2),we=t(i(me),2);{var Ke=n=>{var F=_i();Ue(F,5,()=>e(c).limits,Re,(x,We)=>{var Se=bi(),st=i(Se),lt=i(st),gr=i(lt,!0);r(lt);var At=t(lt,2),xr=i(At);r(At);var Zt=t(At,2),hr=i(Zt,!0);r(Zt),r(st);var Lt=t(st,2);Lt.__click=[mi,$,We];var yr=i(Lt);St(yr,{class:"h-4 w-4"}),r(Lt),r(Se),N(()=>{B(gr,e(We).name),B(xr,`ID: ${e(We).id??""} | Default: ${e(We).defaultValue??""}
                                ${(e(We).unit||"")??""}`),B(hr,e(We).description)}),a(x,Se)}),r(F),a(n,F)},Qe=n=>{var F=gi();a(n,F)};de(we,n=>{e(c).limits&&e(c).limits.length>0?n(Ke):n(Qe,!1)})}var nt=t(we,2),Ne=t(i(nt),2),dt=i(Ne),ot=i(dt);y(ot,{for:"new-limit-id",class:"text-xs",children:(n,F)=>{l();var x=p("Limit ID");a(n,x)},$$slots:{default:!0}});var ut=t(ot,2);D(ut),r(dt);var bt=t(dt,2),It=i(bt);y(It,{for:"new-limit-name",class:"text-xs",children:(n,F)=>{l();var x=p("Name");a(n,x)},$$slots:{default:!0}});var Vt=t(It,2);D(Vt),r(bt);var _t=t(bt,2),Ot=i(_t);y(Ot,{for:"new-limit-description",class:"text-xs",children:(n,F)=>{l();var x=p("Description");a(n,x)},$$slots:{default:!0}});var Bt=t(Ot,2);D(Bt),r(_t);var gt=t(_t,2),Nt=i(gt);y(Nt,{for:"new-limit-default",class:"text-xs",children:(n,F)=>{l();var x=p("Default Value");a(n,x)},$$slots:{default:!0}});var qt=t(Nt,2);D(qt),r(gt);var xt=t(gt,2),Ut=i(xt);y(Ut,{for:"new-limit-unit",class:"text-xs",children:(n,F)=>{l();var x=p("Unit (optional)");a(n,x)},$$slots:{default:!0}});var Rt=t(Ut,2);D(Rt),r(xt);var ht=t(xt,2),Jt=i(ht);y(Jt,{for:"new-limit-type",class:"text-xs",children:(n,F)=>{l();var x=p("Limit Type");a(n,x)},$$slots:{default:!0}});var yt=t(Jt,2);N(()=>{e(m),ft(()=>{})});var $t=i(yt),Gt={},wt=t($t),zt={},Pt=t(wt),Xt={},Yt=t(Pt),Ht={};r(yt),r(ht);var Ft=t(ht,2),Kt=i(Ft);y(Kt,{for:"new-limit-reset",class:"text-xs",children:(n,F)=>{l();var x=p("Reset Day (Monthly only)");a(n,x)},$$slots:{default:!0}});var kt=t(Kt,2);D(kt),r(Ft);var Qt=t(Ft,2),Dt=i(Qt);Dt.__click=[xi,C];var mr=i(Dt);mt(mr,{class:"mr-1.5 h-4 w-4"}),l(),r(Dt),r(Qt),r(Ne),r(nt),r(me);var Wt=t(me,2),ct=i(Wt);ct.__click=[hi,rt];var br=i(ct);Qr(br,{class:"mr-2 h-4 w-4"}),l(),r(ct);var Tt=t(ct,2);Tt.__click=[yi,w];var _r=i(Tt);Wr(_r,{class:"mr-2 h-4 w-4"}),l(),r(Tt),r(Wt),r(he),N(()=>{Sr(Ye,e(c).id),Gt!==(Gt=P.Monthly)&&($t.value=($t.__value=P.Monthly)??""),zt!==(zt=P.Total)&&(wt.value=(wt.__value=P.Total)??""),Xt!==(Xt=P.Concurrent)&&(Pt.value=(Pt.__value=P.Concurrent)??""),Ht!==(Ht=P.Unlimited)&&(Yt.value=(Yt.__value=P.Unlimited)??""),kt.disabled=e(m).type!==P.Monthly}),L(Ce,()=>e(c).name,n=>_(c,e(c).name=n)),L(Ie,()=>e(c).description,n=>_(c,e(c).description=n)),pt(Be,()=>e(c).category,n=>_(c,e(c).category=n)),L(g,()=>e(c).icon,n=>_(c,e(c).icon=n)),ur(k,()=>e(c).beta,n=>_(c,e(c).beta=n)),L(ut,()=>e(m).id,n=>_(m,e(m).id=n)),L(Vt,()=>e(m).name,n=>_(m,e(m).name=n)),L(Bt,()=>e(m).description,n=>_(m,e(m).description=n)),L(qt,()=>e(m).defaultValue,n=>_(m,e(m).defaultValue=n)),L(Rt,()=>e(m).unit,n=>_(m,e(m).unit=n)),pt(yt,()=>e(m).type,n=>_(m,e(m).type=n)),L(kt,()=>e(m).resetDay,n=>_(m,e(m).resetDay=n)),a(xe,he)},fe=xe=>{var he=ki(),pe=i(he),Le=i(pe),Ye=i(Le,!0);r(Le);var ye=t(Le,2),Me=i(ye);r(ye);var Ce=t(ye,2),je=i(Ce,!0);r(Ce);var He=t(Ce,2);{var Ie=k=>{var ae=wi(),me=t(i(ae),2);Ue(me,5,()=>e(K).limits,Re,(we,Ke)=>{ir(we,{variant:"outline",class:"bg-primary/5 border-primary/20 text-xs",children:(Qe,nt)=>{l();var Ne=p();N(()=>B(Ne,`${e(Ke).name??""}: ${e(Ke).defaultValue??""}
                              ${(e(Ke).unit||"")??""}`)),a(Qe,Ne)},$$slots:{default:!0}})}),r(me),r(ae),a(k,ae)};de(He,k=>{e(K).limits&&e(K).limits.length>0&&k(Ie)})}r(pe);var Ve=t(pe,2),Oe=i(Ve);{var Be=k=>{ir(k,{variant:"outline",children:(ae,me)=>{l();var we=p("Beta");a(ae,we)},$$slots:{default:!0}})};de(Oe,k=>{e(K).beta&&k(Be)})}var $e=t(Oe,2);$e.__click=[Pi,Ge,K];var o=i($e);Zr(o,{class:"mr-1 h-4 w-4"}),l(),r($e);var g=t($e,2);g.__click=[Fi,et,K];var v=i(g);St(v,{class:"mr-1 h-4 w-4"}),l(),r(g),r(Ve),r(he),N(()=>{B(Ye,e(K).name),B(Me,`ID: ${e(K).id??""}`),B(je,e(K).description||"No description")}),a(xe,he)};de(ze,xe=>{e(se)===e(K).id?xe(Xe):xe(fe,!1)})}r(W),a(it,W)}),a(V,O)},$$slots:{default:!0}}),a(G,H)},$$slots:{default:!0}})}),a(A,U)},$$slots:{default:!0}})};de(s,q=>{e(Fe)?q(j):q(ce,!1)},f)}};de(_e,s=>{e(Pe)?s(De):s(ge,!1)})}var te=t(_e,2);Rr(te,{get open(){return e(ke)},set open(s){b(ke,s)},children:(s,f)=>{Jr(s,{children:(j,ce)=>{var q=ji(),A=E(q);Gr(A,{children:(U,Y)=>{var R=Ai(),J=E(R);zr(J,{children:(S,I)=>{l();var G=p("Delete Feature");a(S,G)},$$slots:{default:!0}});var ie=t(J,2);Xr(ie,{children:(S,I)=>{l();var G=p();N(()=>B(G,`Are you sure you want to remove feature "${e(z)??""}"? This will also remove it from
        all plans. This action cannot be undone.`)),a(S,G)},$$slots:{default:!0}}),a(U,R)},$$slots:{default:!0}});var re=t(A,2);Yr(re,{children:(U,Y)=>{var R=Ci(),J=E(R);Hr(J,{onclick:()=>b(ke,!1),children:(I,G)=>{l();var Q=p("Cancel");a(I,Q)},$$slots:{default:!0}});var ie=t(J,2);const S=Ct(()=>e(T)?"cursor-not-allowed opacity-70":"bg-destructive text-destructive-foreground hover:bg-destructive/90");Kr(ie,{onclick:tt,get disabled(){return e(T)},get class(){return e(S)},children:(I,G)=>{var Q=qe(),H=E(Q);{var Te=V=>{var ve=Li(),O=E(ve);pr(O,{class:"mr-2 h-4 w-4 animate-spin"}),l(),a(V,ve)},Ee=V=>{var ve=p("Delete");a(V,ve)};de(H,V=>{e(T)?V(Te):V(Ee,!1)})}a(I,Q)},$$slots:{default:!0}}),a(U,R)},$$slots:{default:!0}}),a(j,q)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),a(le,X),Mt()}Er(["click"]);const nr=[{id:"service",name:"Service Features",description:"Core service features including Auto-Apply, Co-Pilot, Job Tracker, Resume Builder, etc.",endpoint:"/api/admin/features/seed-service",icon:"briefcase"},{id:"analysis",name:"Analysis Features",description:"Analytics and data analysis features for job market insights and career planning.",endpoint:"/api/admin/features/seed-analysis",icon:"chart-bar"},{id:"all",name:"All Features",description:"Seed all feature types at once.",endpoint:"/api/admin/features/seed-all",icon:"layers"}];var Mi=h('<div class="flex items-center gap-2 rounded-md border px-3 py-2"><!> <span> </span></div>'),Ii=h('<div class="flex items-center gap-2"><!> <span> </span></div>'),Vi=h("<!> <!>",1),Oi=h("<!> <!>",1),Bi=h("<!> <!> <!>",1),Ni=h('<!> <div class="flex items-center justify-between gap-1 border-b px-4 py-2"><h2 class="text-lg font-semibold">Feature Management</h2> <div class="space-y-4"><div class="flex gap-2"><!> <div class="flex items-center gap-2"><!></div></div></div></div> <!>',1);function Fa(le,M){Et(M,!0);let d=vt("manage"),u=vt(!1),Pe=vt(""),Fe=vt(void 0);async function ke(C){if(e(u))return;b(Fe,C,!0);const $=nr.find(w=>w.id===C);if(!$){Z.error("Invalid feature type selected");return}try{b(u,!0),b(Pe,$.id,!0),Z.loading(`Seeding ${$.name}...`,{id:"seeding-toast"});const w=await fetch($.endpoint,{method:"POST",credentials:"include"});if(!w.ok){const X=await w.text();throw new Error(X||`Failed to seed ${$.name}: ${w.status}`)}const ee=await w.json();Z.success(`${$.name} seeded`,{id:"seeding-toast",description:ee.message||`${$.name} have been added to the system`,duration:3e3}),window.dispatchEvent(new CustomEvent("featureAdded"))}catch(w){console.error(`Error seeding ${$.name}:`,w),Z.error(`Failed to seed ${$.name}`,{id:"seeding-toast",description:w.message,duration:5e3})}finally{b(u,!1),b(Pe,""),b(Fe,void 0)}}var z=Ni(),T=E(z);jr(T,{title:"Feature Management"});var se=t(T,2),c=t(i(se),2),m=i(c),be=i(m);Dr(be,{variant:"outline",onclick:()=>window.location.href="/dashboard/settings/admin/plans",children:(C,$)=>{l();var w=p("Back to Plans");a(C,w)},$$slots:{default:!0}});var ue=t(be,2),et=i(ue);{var tt=C=>{var $=Mi(),w=i($);pr(w,{class:"h-4 w-4 animate-spin"});var ee=t(w,2),X=i(ee);r(ee),r($),N(()=>B(X,`Seeding ${e(Pe)??""}...`)),a(C,$)},Ge=C=>{var $=qe(),w=E($);ne(w,()=>Cr,(ee,X)=>{X(ee,{type:"single",onValueChange:ke,children:(_e,De)=>{var ge=Vi(),te=E(ge);ne(te,()=>Tr,(f,j)=>{j(f,{class:"w-[200px]",children:(ce,q)=>{var A=qe(),re=E(A);ne(re,()=>ei,(U,Y)=>{Y(U,{placeholder:"Seed Features..."})}),a(ce,A)},$$slots:{default:!0}})});var s=t(te,2);ne(s,()=>Ar,(f,j)=>{j(f,{class:"w-[200px]",children:(ce,q)=>{var A=qe(),re=E(A);Ue(re,17,()=>nr,Re,(U,Y)=>{var R=qe(),J=E(R);ne(J,()=>Lr,(ie,S)=>{S(ie,{get value(){return e(Y).id},children:(I,G)=>{var Q=Ii(),H=i(Q);{var Te=O=>{ti(O,{class:"h-4 w-4"})},Ee=(O,Ae)=>{{var it=W=>{ri(W,{class:"h-4 w-4"})},at=(W,K)=>{{var ze=fe=>{ii(fe,{class:"h-4 w-4"})},Xe=fe=>{mt(fe,{class:"h-4 w-4"})};de(W,fe=>{e(Y).icon==="layers"?fe(ze):fe(Xe,!1)},K)}};de(O,W=>{e(Y).icon==="chart-bar"?W(it):W(at,!1)},Ae)}};de(H,O=>{e(Y).icon==="briefcase"?O(Te):O(Ee,!1)})}var V=t(H,2),ve=i(V,!0);r(V),r(Q),N(()=>B(ve,e(Y).name)),a(I,Q)},$$slots:{default:!0}})}),a(U,R)}),a(ce,A)},$$slots:{default:!0}})}),a(_e,ge)},$$slots:{default:!0}})}),a(C,$)};de(et,C=>{e(u)?C(tt):C(Ge,!1)})}r(ue),r(m),r(c),r(se);var rt=t(se,2);ne(rt,()=>kr,(C,$)=>{$(C,{get value(){return e(d)},class:"w-full",onValueChange:w=>b(d,w,!0),children:(w,ee)=>{var X=Bi(),_e=E(X);ne(_e,()=>dr,(te,s)=>{s(te,{class:"border-border border-b p-0",children:(f,j)=>{var ce=qe(),q=E(ce);ne(q,()=>Fr,(A,re)=>{re(A,{class:"flex flex-row gap-2 divide-x",children:(U,Y)=>{var R=Oi(),J=E(R);ne(J,()=>ar,(S,I)=>{I(S,{value:"manage",class:"flex-1 rounded-none border-none",children:(G,Q)=>{l();var H=p("Manage Features");a(G,H)},$$slots:{default:!0}})});var ie=t(J,2);ne(ie,()=>ar,(S,I)=>{I(S,{value:"add",class:"flex-1",children:(G,Q)=>{l();var H=p("Add New Feature");a(G,H)},$$slots:{default:!0}})}),a(U,R)},$$slots:{default:!0}})}),a(f,ce)},$$slots:{default:!0}})});var De=t(_e,2);ne(De,()=>or,(te,s)=>{s(te,{value:"manage",class:"p-4",children:(f,j)=>{Ei(f,{})},$$slots:{default:!0}})});var ge=t(De,2);ne(ge,()=>or,(te,s)=>{s(te,{value:"add",class:"p-4",children:(f,j)=>{ci(f,{})},$$slots:{default:!0}})}),a(w,X)},$$slots:{default:!0}})}),a(le,z),Mt()}export{Fa as component};
