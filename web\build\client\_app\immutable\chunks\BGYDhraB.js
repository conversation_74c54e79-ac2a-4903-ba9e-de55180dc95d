function F(r){this.content=r}F.prototype={constructor:F,find:function(r){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===r)return e;return-1},get:function(r){var e=this.find(r);return e==-1?void 0:this.content[e+1]},update:function(r,e,t){var n=t&&t!=r?this.remove(t):this,i=n.find(r),s=n.content.slice();return i==-1?s.push(t||r,e):(s[i+1]=e,t&&(s[i]=t)),new F(s)},remove:function(r){var e=this.find(r);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new F(t)},addToStart:function(r,e){return new F([r,e].concat(this.remove(r).content))},addToEnd:function(r,e){var t=this.remove(r).content.slice();return t.push(r,e),new F(t)},addBefore:function(r,e,t){var n=this.remove(e),i=n.content.slice(),s=n.find(r);return i.splice(s==-1?i.length:s,0,e,t),new F(i)},forEach:function(r){for(var e=0;e<this.content.length;e+=2)r(this.content[e],this.content[e+1])},prepend:function(r){return r=F.from(r),r.size?new F(r.content.concat(this.subtract(r).content)):this},append:function(r){return r=F.from(r),r.size?new F(this.subtract(r).content.concat(r.content)):this},subtract:function(r){var e=this;r=F.from(r);for(var t=0;t<r.content.length;t+=2)e=e.remove(r.content[t]);return e},toObject:function(){var r={};return this.forEach(function(e,t){r[e]=t}),r},get size(){return this.content.length>>1}};F.from=function(r){if(r instanceof F)return r;var e=[];if(r)for(var t in r)e.push(t,r[t]);return new F(e)};function Di(r,e,t){for(let n=0;;n++){if(n==r.childCount||n==e.childCount)return r.childCount==e.childCount?null:t;let i=r.child(n),s=e.child(n);if(i==s){t+=i.nodeSize;continue}if(!i.sameMarkup(s))return t;if(i.isText&&i.text!=s.text){for(let o=0;i.text[o]==s.text[o];o++)t++;return t}if(i.content.size||s.content.size){let o=Di(i.content,s.content,t+1);if(o!=null)return o}t+=i.nodeSize}}function Ai(r,e,t,n){for(let i=r.childCount,s=e.childCount;;){if(i==0||s==0)return i==s?null:{a:t,b:n};let o=r.child(--i),l=e.child(--s),a=o.nodeSize;if(o==l){t-=a,n-=a;continue}if(!o.sameMarkup(l))return{a:t,b:n};if(o.isText&&o.text!=l.text){let c=0,f=Math.min(o.text.length,l.text.length);for(;c<f&&o.text[o.text.length-c-1]==l.text[l.text.length-c-1];)c++,t--,n--;return{a:t,b:n}}if(o.content.size||l.content.size){let c=Ai(o.content,l.content,t-1,n-1);if(c)return c}t-=a,n-=a}}class b{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let n=0;n<e.length;n++)this.size+=e[n].nodeSize}nodesBetween(e,t,n,i=0,s){for(let o=0,l=0;l<t;o++){let a=this.content[o],c=l+a.nodeSize;if(c>e&&n(a,i+l,s||null,o)!==!1&&a.content.size){let f=l+1;a.nodesBetween(Math.max(0,e-f),Math.min(a.content.size,t-f),n,i+f)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,i){let s="",o=!0;return this.nodesBetween(e,t,(l,a)=>{let c=l.isText?l.text.slice(Math.max(e,a)-a,t-a):l.isLeaf?i?typeof i=="function"?i(l):i:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&n&&(o?o=!1:s+=n),s+=c},0),s}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,i=this.content.slice(),s=0;for(t.isText&&t.sameMarkup(n)&&(i[i.length-1]=t.withText(t.text+n.text),s=1);s<e.content.length;s++)i.push(e.content[s]);return new b(i,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let n=[],i=0;if(t>e)for(let s=0,o=0;o<t;s++){let l=this.content[s],a=o+l.nodeSize;a>e&&((o<e||a>t)&&(l.isText?l=l.cut(Math.max(0,e-o),Math.min(l.text.length,t-o)):l=l.cut(Math.max(0,e-o-1),Math.min(l.content.size,t-o-1))),n.push(l),i+=l.nodeSize),o=a}return new b(n,i)}cutByIndex(e,t){return e==t?b.empty:e==0&&t==this.content.length?this:new b(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let i=this.content.slice(),s=this.size+t.nodeSize-n.nodeSize;return i[e]=t,new b(i,s)}addToStart(e){return new b([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new b(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let i=this.content[t];e(i,n,t),n+=i.nodeSize}}findDiffStart(e,t=0){return Di(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return Ai(this,e,t,n)}findIndex(e,t=-1){if(e==0)return Rt(0,e);if(e==this.size)return Rt(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,i=0;;n++){let s=this.child(n),o=i+s.nodeSize;if(o>=e)return o==e||t>0?Rt(n+1,o):Rt(n,i);i=o}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return b.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new b(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return b.empty;let t,n=0;for(let i=0;i<e.length;i++){let s=e[i];n+=s.nodeSize,i&&s.isText&&e[i-1].sameMarkup(s)?(t||(t=e.slice(0,i)),t[t.length-1]=s.withText(t[t.length-1].text+s.text)):t&&t.push(s)}return new b(t||e,n)}static from(e){if(!e)return b.empty;if(e instanceof b)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new b([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}b.empty=new b([],0);const gn={index:0,offset:0};function Rt(r,e){return gn.index=r,gn.offset=e,gn}function Lt(r,e){if(r===e)return!0;if(!(r&&typeof r=="object")||!(e&&typeof e=="object"))return!1;let t=Array.isArray(r);if(Array.isArray(e)!=t)return!1;if(t){if(r.length!=e.length)return!1;for(let n=0;n<r.length;n++)if(!Lt(r[n],e[n]))return!1}else{for(let n in r)if(!(n in e)||!Lt(r[n],e[n]))return!1;for(let n in e)if(!(n in r))return!1}return!0}let A=class vn{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let i=0;i<e.length;i++){let s=e[i];if(this.eq(s))return e;if(this.type.excludes(s.type))t||(t=e.slice(0,i));else{if(s.type.excludes(this.type))return e;!n&&s.type.rank>this.type.rank&&(t||(t=e.slice(0,i)),t.push(this),n=!0),t&&t.push(s)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&Lt(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw new RangeError(`There is no mark type ${t.type} in this schema`);let i=n.create(t.attrs);return n.checkAttrs(i.attrs),i}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return vn.none;if(e instanceof vn)return[e];let t=e.slice();return t.sort((n,i)=>n.type.rank-i.type.rank),t}};A.none=[];class Wt extends Error{}class k{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=Ri(this.content,e+this.openStart,t);return n&&new k(n,this.openStart,this.openEnd)}removeBetween(e,t){return new k(Ii(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return k.empty;let n=t.openStart||0,i=t.openEnd||0;if(typeof n!="number"||typeof i!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new k(b.fromJSON(e,t.content),n,i)}static maxOpen(e,t=!0){let n=0,i=0;for(let s=e.firstChild;s&&!s.isLeaf&&(t||!s.type.spec.isolating);s=s.firstChild)n++;for(let s=e.lastChild;s&&!s.isLeaf&&(t||!s.type.spec.isolating);s=s.lastChild)i++;return new k(e,n,i)}}k.empty=new k(b.empty,0,0);function Ii(r,e,t){let{index:n,offset:i}=r.findIndex(e),s=r.maybeChild(n),{index:o,offset:l}=r.findIndex(t);if(i==e||s.isText){if(l!=t&&!r.child(o).isText)throw new RangeError("Removing non-flat range");return r.cut(0,e).append(r.cut(t))}if(n!=o)throw new RangeError("Removing non-flat range");return r.replaceChild(n,s.copy(Ii(s.content,e-i-1,t-i-1)))}function Ri(r,e,t,n){let{index:i,offset:s}=r.findIndex(e),o=r.maybeChild(i);if(s==e||o.isText)return r.cut(0,e).append(t).append(r.cut(e));let l=Ri(o.content,e-s-1,t);return l&&r.replaceChild(i,o.copy(l))}function So(r,e,t){if(t.openStart>r.depth)throw new Wt("Inserted content deeper than insertion position");if(r.depth-t.openStart!=e.depth-t.openEnd)throw new Wt("Inconsistent open depths");return vi(r,e,t,0)}function vi(r,e,t,n){let i=r.index(n),s=r.node(n);if(i==e.index(n)&&n<r.depth-t.openStart){let o=vi(r,e,t,n+1);return s.copy(s.content.replaceChild(i,o))}else if(t.content.size)if(!t.openStart&&!t.openEnd&&r.depth==n&&e.depth==n){let o=r.parent,l=o.content;return Pe(o,l.cut(0,r.parentOffset).append(t.content).append(l.cut(e.parentOffset)))}else{let{start:o,end:l}=Mo(t,r);return Pe(s,Bi(r,o,l,e,n))}else return Pe(s,jt(r,e,n))}function Pi(r,e){if(!e.type.compatibleContent(r.type))throw new Wt("Cannot join "+e.type.name+" onto "+r.type.name)}function Pn(r,e,t){let n=r.node(t);return Pi(n,e.node(t)),n}function ve(r,e){let t=e.length-1;t>=0&&r.isText&&r.sameMarkup(e[t])?e[t]=r.withText(e[t].text+r.text):e.push(r)}function ct(r,e,t,n){let i=(e||r).node(t),s=0,o=e?e.index(t):i.childCount;r&&(s=r.index(t),r.depth>t?s++:r.textOffset&&(ve(r.nodeAfter,n),s++));for(let l=s;l<o;l++)ve(i.child(l),n);e&&e.depth==t&&e.textOffset&&ve(e.nodeBefore,n)}function Pe(r,e){return r.type.checkContent(e),r.copy(e)}function Bi(r,e,t,n,i){let s=r.depth>i&&Pn(r,e,i+1),o=n.depth>i&&Pn(t,n,i+1),l=[];return ct(null,r,i,l),s&&o&&e.index(i)==t.index(i)?(Pi(s,o),ve(Pe(s,Bi(r,e,t,n,i+1)),l)):(s&&ve(Pe(s,jt(r,e,i+1)),l),ct(e,t,i,l),o&&ve(Pe(o,jt(t,n,i+1)),l)),ct(n,null,i,l),new b(l)}function jt(r,e,t){let n=[];if(ct(null,r,t,n),r.depth>t){let i=Pn(r,e,t+1);ve(Pe(i,jt(r,e,t+1)),n)}return ct(e,null,t,n),new b(n)}function Mo(r,e){let t=e.depth-r.openStart,i=e.node(t).copy(r.content);for(let s=t-1;s>=0;s--)i=e.node(s).copy(b.from(i));return{start:i.resolveNoCache(r.openStart+t),end:i.resolveNoCache(i.content.size-r.openEnd-t)}}class ht{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[this.resolveDepth(e)*3]}index(e){return this.path[this.resolveDepth(e)*3+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e==this.depth&&!this.textOffset?0:1)}start(e){return e=this.resolveDepth(e),e==0?0:this.path[e*3-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]}after(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]+this.path[e*3].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],i=e.child(t);return n?e.child(t).cut(n):i}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[t*3],i=t==0?0:this.path[t*3-1]+1;for(let s=0;s<e;s++)i+=n.child(s).nodeSize;return i}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return A.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),i=e.maybeChild(t);if(!n){let l=n;n=i,i=l}let s=n.marks;for(var o=0;o<s.length;o++)s[o].type.spec.inclusive===!1&&(!i||!s[o].isInSet(i.marks))&&(s=s[o--].removeFromSet(s));return s}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,i=e.parent.maybeChild(e.index());for(var s=0;s<n.length;s++)n[s].type.spec.inclusive===!1&&(!i||!n[s].isInSet(i.marks))&&(n=n[s--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new Jt(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let n=[],i=0,s=t;for(let o=e;;){let{index:l,offset:a}=o.content.findIndex(s),c=s-a;if(n.push(o,l,i+a),!c||(o=o.child(l),o.isText))break;s=c-1,i+=a+1}return new ht(t,n,s)}static resolveCached(e,t){let n=Nr.get(e);if(n)for(let s=0;s<n.elts.length;s++){let o=n.elts[s];if(o.pos==t)return o}else Nr.set(e,n=new Co);let i=n.elts[n.i]=ht.resolve(e,t);return n.i=(n.i+1)%wo,i}}class Co{constructor(){this.elts=[],this.i=0}}const wo=12,Nr=new WeakMap;class Jt{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const Oo=Object.create(null);let Me=class Bn{constructor(e,t,n,i=A.none){this.type=e,this.attrs=t,this.marks=i,this.content=n||b.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,i=0){this.content.nodesBetween(e,t,n,i,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,i){return this.content.textBetween(e,t,n,i)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&Lt(this.attrs,t||e.defaultAttrs||Oo)&&A.sameSet(this.marks,n||A.none)}copy(e=null){return e==this.content?this:new Bn(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new Bn(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return k.empty;let i=this.resolve(e),s=this.resolve(t),o=n?0:i.sharedDepth(t),l=i.start(o),c=i.node(o).content.cut(i.pos-l,s.pos-l);return new k(c,i.depth-o,s.depth-o)}replace(e,t,n){return So(this.resolve(e),this.resolve(t),n)}nodeAt(e){for(let t=this;;){let{index:n,offset:i}=t.content.findIndex(e);if(t=t.maybeChild(n),!t)return null;if(i==e||t.isText)return t;e-=i+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let i=this.content.child(t-1);return{node:i,index:t-1,offset:n-i.nodeSize}}resolve(e){return ht.resolveCached(this,e)}resolveNoCache(e){return ht.resolve(this,e)}rangeHasMark(e,t,n){let i=!1;return t>e&&this.nodesBetween(e,t,s=>(n.isInSet(s.marks)&&(i=!0),!i)),i}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),zi(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=b.empty,i=0,s=n.childCount){let o=this.contentMatchAt(e).matchFragment(n,i,s),l=o&&o.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let a=i;a<s;a++)if(!this.type.allowsMarks(n.child(a).marks))return!1;return!0}canReplaceWith(e,t,n,i){if(i&&!this.type.allowsMarks(i))return!1;let s=this.contentMatchAt(e).matchType(n),o=s&&s.matchFragment(this.content,t);return o?o.validEnd:!1}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=A.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!A.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let n;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,n)}let i=b.fromJSON(e,t.content),s=e.nodeType(t.type).create(t.attrs,i,n);return s.type.checkAttrs(s.attrs),s}};Me.prototype.text=void 0;class qt extends Me{constructor(e,t,n,i){if(super(e,t,null,i),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):zi(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new qt(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new qt(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function zi(r,e){for(let t=r.length-1;t>=0;t--)e=r[t].type.name+"("+e+")";return e}class Fe{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let n=new No(e,t);if(n.next==null)return Fe.empty;let i=Fi(n);n.next&&n.err("Unexpected trailing text");let s=vo(Ro(i));return Po(s,n),s}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let i=this;for(let s=t;i&&s<n;s++)i=i.matchType(e.child(s).type);return i}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let i=[this];function s(o,l){let a=o.matchFragment(e,n);if(a&&(!t||a.validEnd))return b.from(l.map(c=>c.createAndFill()));for(let c=0;c<o.next.length;c++){let{type:f,next:d}=o.next[c];if(!(f.isText||f.hasRequiredAttrs())&&i.indexOf(d)==-1){i.push(d);let u=s(d,l.concat(f));if(u)return u}}return null}return s(this,[])}findWrapping(e){for(let n=0;n<this.wrapCache.length;n+=2)if(this.wrapCache[n]==e)return this.wrapCache[n+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let i=n.shift(),s=i.match;if(s.matchType(e)){let o=[];for(let l=i;l.type;l=l.via)o.push(l.type);return o.reverse()}for(let o=0;o<s.next.length;o++){let{type:l,next:a}=s.next[o];!l.isLeaf&&!l.hasRequiredAttrs()&&!(l.name in t)&&(!i.type||a.validEnd)&&(n.push({match:l.contentMatch,type:l,via:i}),t[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];function t(n){e.push(n);for(let i=0;i<n.next.length;i++)e.indexOf(n.next[i].next)==-1&&t(n.next[i].next)}return t(this),e.map((n,i)=>{let s=i+(n.validEnd?"*":" ")+" ";for(let o=0;o<n.next.length;o++)s+=(o?", ":"")+n.next[o].type.name+"->"+e.indexOf(n.next[o].next);return s}).join(`
`)}}Fe.empty=new Fe(!0);class No{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function Fi(r){let e=[];do e.push(To(r));while(r.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function To(r){let e=[];do e.push(Eo(r));while(r.next&&r.next!=")"&&r.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function Eo(r){let e=Io(r);for(;;)if(r.eat("+"))e={type:"plus",expr:e};else if(r.eat("*"))e={type:"star",expr:e};else if(r.eat("?"))e={type:"opt",expr:e};else if(r.eat("{"))e=Do(r,e);else break;return e}function Tr(r){/\D/.test(r.next)&&r.err("Expected number, got '"+r.next+"'");let e=Number(r.next);return r.pos++,e}function Do(r,e){let t=Tr(r),n=t;return r.eat(",")&&(r.next!="}"?n=Tr(r):n=-1),r.eat("}")||r.err("Unclosed braced range"),{type:"range",min:t,max:n,expr:e}}function Ao(r,e){let t=r.nodeTypes,n=t[e];if(n)return[n];let i=[];for(let s in t){let o=t[s];o.isInGroup(e)&&i.push(o)}return i.length==0&&r.err("No node type or group '"+e+"' found"),i}function Io(r){if(r.eat("(")){let e=Fi(r);return r.eat(")")||r.err("Missing closing paren"),e}else if(/\W/.test(r.next))r.err("Unexpected token '"+r.next+"'");else{let e=Ao(r,r.next).map(t=>(r.inline==null?r.inline=t.isInline:r.inline!=t.isInline&&r.err("Mixing inline and block content"),{type:"name",value:t}));return r.pos++,e.length==1?e[0]:{type:"choice",exprs:e}}}function Ro(r){let e=[[]];return i(s(r,0),t()),e;function t(){return e.push([])-1}function n(o,l,a){let c={term:a,to:l};return e[o].push(c),c}function i(o,l){o.forEach(a=>a.to=l)}function s(o,l){if(o.type=="choice")return o.exprs.reduce((a,c)=>a.concat(s(c,l)),[]);if(o.type=="seq")for(let a=0;;a++){let c=s(o.exprs[a],l);if(a==o.exprs.length-1)return c;i(c,l=t())}else if(o.type=="star"){let a=t();return n(l,a),i(s(o.expr,a),a),[n(a)]}else if(o.type=="plus"){let a=t();return i(s(o.expr,l),a),i(s(o.expr,a),a),[n(a)]}else{if(o.type=="opt")return[n(l)].concat(s(o.expr,l));if(o.type=="range"){let a=l;for(let c=0;c<o.min;c++){let f=t();i(s(o.expr,a),f),a=f}if(o.max==-1)i(s(o.expr,a),a);else for(let c=o.min;c<o.max;c++){let f=t();n(a,f),i(s(o.expr,a),f),a=f}return[n(a)]}else{if(o.type=="name")return[n(l,void 0,o.value)];throw new Error("Unknown expr type")}}}}function Vi(r,e){return e-r}function Er(r,e){let t=[];return n(e),t.sort(Vi);function n(i){let s=r[i];if(s.length==1&&!s[0].term)return n(s[0].to);t.push(i);for(let o=0;o<s.length;o++){let{term:l,to:a}=s[o];!l&&t.indexOf(a)==-1&&n(a)}}}function vo(r){let e=Object.create(null);return t(Er(r,0));function t(n){let i=[];n.forEach(o=>{r[o].forEach(({term:l,to:a})=>{if(!l)return;let c;for(let f=0;f<i.length;f++)i[f][0]==l&&(c=i[f][1]);Er(r,a).forEach(f=>{c||i.push([l,c=[]]),c.indexOf(f)==-1&&c.push(f)})})});let s=e[n.join(",")]=new Fe(n.indexOf(r.length-1)>-1);for(let o=0;o<i.length;o++){let l=i[o][1].sort(Vi);s.next.push({type:i[o][0],next:e[l.join(",")]||t(l)})}return s}}function Po(r,e){for(let t=0,n=[r];t<n.length;t++){let i=n[t],s=!i.validEnd,o=[];for(let l=0;l<i.next.length;l++){let{type:a,next:c}=i.next[l];o.push(a.name),s&&!(a.isText||a.hasRequiredAttrs())&&(s=!1),n.indexOf(c)==-1&&n.push(c)}s&&e.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function $i(r){let e=Object.create(null);for(let t in r){let n=r[t];if(!n.hasDefault)return null;e[t]=n.default}return e}function Li(r,e){let t=Object.create(null);for(let n in r){let i=e&&e[n];if(i===void 0){let s=r[n];if(s.hasDefault)i=s.default;else throw new RangeError("No value supplied for attribute "+n)}t[n]=i}return t}function Wi(r,e,t,n){for(let i in e)if(!(i in r))throw new RangeError(`Unsupported attribute ${i} for ${t} of type ${i}`);for(let i in r){let s=r[i];s.validate&&s.validate(e[i])}}function ji(r,e){let t=Object.create(null);if(e)for(let n in e)t[n]=new zo(r,n,e[n]);return t}let Dr=class Ji{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=ji(e,n.attrs),this.defaultAttrs=$i(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==Fe.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:Li(this.attrs,e)}create(e=null,t,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Me(this,this.computeAttrs(e),b.from(t),A.setFrom(n))}createChecked(e=null,t,n){return t=b.from(t),this.checkContent(t),new Me(this,this.computeAttrs(e),t,A.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),t=b.from(t),t.size){let o=this.contentMatch.fillBefore(t);if(!o)return null;t=o.append(t)}let i=this.contentMatch.matchFragment(t),s=i&&i.fillBefore(b.empty,!0);return s?new Me(this,e,t.append(s),A.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let n=0;n<e.childCount;n++)if(!this.allowsMarks(e.child(n).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){Wi(this.attrs,e,"node",this.name)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:A.none:e}static compile(e,t){let n=Object.create(null);e.forEach((s,o)=>n[s]=new Ji(s,t,o));let i=t.spec.topNode||"doc";if(!n[i])throw new RangeError("Schema is missing its top node type ('"+i+"')");if(!n.text)throw new RangeError("Every schema needs a 'text' type");for(let s in n.text.attrs)throw new RangeError("The text node type should not have attributes");return n}};function Bo(r,e,t){let n=t.split("|");return i=>{let s=i===null?"null":typeof i;if(n.indexOf(s)<0)throw new RangeError(`Expected value of type ${n} for attribute ${e} on type ${r}, got ${s}`)}}class zo{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate=typeof n.validate=="string"?Bo(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class nn{constructor(e,t,n,i){this.name=e,this.rank=t,this.schema=n,this.spec=i,this.attrs=ji(e,i.attrs),this.excluded=null;let s=$i(this.attrs);this.instance=s?new A(this,s):null}create(e=null){return!e&&this.instance?this.instance:new A(this,Li(this.attrs,e))}static compile(e,t){let n=Object.create(null),i=0;return e.forEach((s,o)=>n[s]=new nn(s,i++,t,o)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){Wi(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class qi{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let i in e)t[i]=e[i];t.nodes=F.from(e.nodes),t.marks=F.from(e.marks||{}),this.nodes=Dr.compile(this.spec.nodes,this),this.marks=nn.compile(this.spec.marks,this);let n=Object.create(null);for(let i in this.nodes){if(i in this.marks)throw new RangeError(i+" can not be both a node and a mark");let s=this.nodes[i],o=s.spec.content||"",l=s.spec.marks;if(s.contentMatch=n[o]||(n[o]=Fe.parse(o,this.nodes)),s.inlineContent=s.contentMatch.inlineContent,s.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!s.isInline||!s.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=s}s.markSet=l=="_"?null:l?Ar(this,l.split(" ")):l==""||!s.inlineContent?[]:null}for(let i in this.marks){let s=this.marks[i],o=s.spec.excludes;s.excluded=o==null?[s]:o==""?[]:Ar(this,o.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,i){if(typeof e=="string")e=this.nodeType(e);else if(e instanceof Dr){if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}else throw new RangeError("Invalid node type: "+e);return e.createChecked(t,n,i)}text(e,t){let n=this.nodes.text;return new qt(n,n.defaultAttrs,e,A.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return Me.fromJSON(this,e)}markFromJSON(e){return A.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function Ar(r,e){let t=[];for(let n=0;n<e.length;n++){let i=e[n],s=r.marks[i],o=s;if(s)t.push(s);else for(let l in r.marks){let a=r.marks[l];(i=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(i)>-1)&&t.push(o=a)}if(!o)throw new SyntaxError("Unknown mark type: '"+e[n]+"'")}return t}function Fo(r){return r.tag!=null}function Vo(r){return r.style!=null}class Ce{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(i=>{if(Fo(i))this.tags.push(i);else if(Vo(i)){let s=/[^=]*/.exec(i.style)[0];n.indexOf(s)<0&&n.push(s),this.styles.push(i)}}),this.normalizeLists=!this.tags.some(i=>{if(!/^(ul|ol)\b/.test(i.tag)||!i.node)return!1;let s=e.nodes[i.node];return s.contentMatch.matchType(s)})}parse(e,t={}){let n=new Rr(this,t,!1);return n.addAll(e,A.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new Rr(this,t,!0);return n.addAll(e,A.none,t.from,t.to),k.maxOpen(n.finish())}matchTag(e,t,n){for(let i=n?this.tags.indexOf(n)+1:0;i<this.tags.length;i++){let s=this.tags[i];if(Wo(e,s.tag)&&(s.namespace===void 0||e.namespaceURI==s.namespace)&&(!s.context||t.matchesContext(s.context))){if(s.getAttrs){let o=s.getAttrs(e);if(o===!1)continue;s.attrs=o||void 0}return s}}}matchStyle(e,t,n,i){for(let s=i?this.styles.indexOf(i)+1:0;s<this.styles.length;s++){let o=this.styles[s],l=o.style;if(!(l.indexOf(e)!=0||o.context&&!n.matchesContext(o.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=t))){if(o.getAttrs){let a=o.getAttrs(t);if(a===!1)continue;o.attrs=a||void 0}return o}}}static schemaRules(e){let t=[];function n(i){let s=i.priority==null?50:i.priority,o=0;for(;o<t.length;o++){let l=t[o];if((l.priority==null?50:l.priority)<s)break}t.splice(o,0,i)}for(let i in e.marks){let s=e.marks[i].spec.parseDOM;s&&s.forEach(o=>{n(o=vr(o)),o.mark||o.ignore||o.clearMark||(o.mark=i)})}for(let i in e.nodes){let s=e.nodes[i].spec.parseDOM;s&&s.forEach(o=>{n(o=vr(o)),o.node||o.ignore||o.mark||(o.node=i)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new Ce(e,Ce.schemaRules(e)))}}const Ki={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},$o={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},Hi={ol:!0,ul:!0},pt=1,zn=2,zt=4;function Ir(r,e,t){return e!=null?(e?pt:0)|(e==="full"?zn:0):r&&r.whitespace=="pre"?pt|zn:t&-5}class vt{constructor(e,t,n,i,s,o){this.type=e,this.attrs=t,this.marks=n,this.solid=i,this.options=o,this.content=[],this.activeMarks=A.none,this.match=s||(o&zt?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(b.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let n=this.type.contentMatch,i;return(i=n.findWrapping(e.type))?(this.match=n,i):null}}return this.match.findWrapping(e.type)}finish(e){if(!(this.options&pt)){let n=this.content[this.content.length-1],i;if(n&&n.isText&&(i=/[ \t\r\n\u000c]+$/.exec(n.text))){let s=n;n.text.length==i[0].length?this.content.pop():this.content[this.content.length-1]=s.withText(s.text.slice(0,s.text.length-i[0].length))}}let t=b.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(b.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!Ki.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class Rr{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let i=t.topNode,s,o=Ir(null,t.preserveWhitespace,0)|(n?zt:0);i?s=new vt(i.type,i.attrs,A.none,!0,t.topMatch||i.type.contentMatch,o):n?s=new vt(null,null,A.none,!0,null,o):s=new vt(e.schema.topNodeType,null,A.none,!0,null,o),this.nodes=[s],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){e.nodeType==3?this.addTextNode(e,t):e.nodeType==1&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,i=this.top,s=i.options&zn?"full":this.localPreserveWS||(i.options&pt)>0;if(s==="full"||i.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(s)s!=="full"?n=n.replace(/\r?\n|\r/g," "):n=n.replace(/\r\n?/g,`
`);else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let o=i.content[i.content.length-1],l=e.previousSibling;(!o||l&&l.nodeName=="BR"||o.isText&&/[ \t\r\n\u000c]$/.test(o.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t,!/\S/.test(n)),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let i=this.localPreserveWS,s=this.top;(e.tagName=="PRE"||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let o=e.nodeName.toLowerCase(),l;Hi.hasOwnProperty(o)&&this.parser.normalizeLists&&Lo(e);let a=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(l=this.parser.matchTag(e,this,n));e:if(a?a.ignore:$o.hasOwnProperty(o))this.findInside(e),this.ignoreFallback(e,t);else if(!a||a.skip||a.closeParent){a&&a.closeParent?this.open=Math.max(0,this.open-1):a&&a.skip.nodeType&&(e=a.skip);let c,f=this.needsBlock;if(Ki.hasOwnProperty(o))s.content.length&&s.content[0].isInline&&this.open&&(this.open--,s=this.top),c=!0,s.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let d=a&&a.skip?t:this.readStyles(e,t);d&&this.addAll(e,d),c&&this.sync(s),this.needsBlock=f}else{let c=this.readStyles(e,t);c&&this.addElementByRule(e,a,c,a.consuming===!1?l:void 0)}this.localPreserveWS=i}leafFallback(e,t){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`),t)}ignoreFallback(e,t){e.nodeName=="BR"&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let i=0;i<this.parser.matchedStyles.length;i++){let s=this.parser.matchedStyles[i],o=n.getPropertyValue(s);if(o)for(let l=void 0;;){let a=this.parser.matchStyle(s,o,this,l);if(!a)break;if(a.ignore)return null;if(a.clearMark?t=t.filter(c=>!a.clearMark(c)):t=t.concat(this.parser.schema.marks[a.mark].create(a.attrs)),a.consuming===!1)l=a;else break}}return t}addElementByRule(e,t,n,i){let s,o;if(t.node)if(o=this.parser.schema.nodes[t.node],o.isLeaf)this.insertNode(o.create(t.attrs),n,e.nodeName=="BR")||this.leafFallback(e,n);else{let a=this.enter(o,t.attrs||null,n,t.preserveWhitespace);a&&(s=!0,n=a)}else{let a=this.parser.schema.marks[t.mark];n=n.concat(a.create(t.attrs))}let l=this.top;if(o&&o.isLeaf)this.findInside(e);else if(i)this.addElement(e,n,i);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a,n,!1));else{let a=e;typeof t.contentElement=="string"?a=e.querySelector(t.contentElement):typeof t.contentElement=="function"?a=t.contentElement(e):t.contentElement&&(a=t.contentElement),this.findAround(e,a,!0),this.addAll(a,n),this.findAround(e,a,!1)}s&&this.sync(l)&&this.open--}addAll(e,t,n,i){let s=n||0;for(let o=n?e.childNodes[n]:e.firstChild,l=i==null?null:e.childNodes[i];o!=l;o=o.nextSibling,++s)this.findAtPoint(e,s),this.addDOM(o,t);this.findAtPoint(e,s)}findPlace(e,t,n){let i,s;for(let o=this.open,l=0;o>=0;o--){let a=this.nodes[o],c=a.findWrapping(e);if(c&&(!i||i.length>c.length+l)&&(i=c,s=a,!c.length))break;if(a.solid){if(n)break;l+=2}}if(!i)return null;this.sync(s);for(let o=0;o<i.length;o++)t=this.enterInner(i[o],null,t,!1);return t}insertNode(e,t,n){if(e.isInline&&this.needsBlock&&!this.top.type){let s=this.textblockFromContext();s&&(t=this.enterInner(s,null,t))}let i=this.findPlace(e,t,n);if(i){this.closeExtra();let s=this.top;s.match&&(s.match=s.match.matchType(e.type));let o=A.none;for(let l of i.concat(e.marks))(s.type?s.type.allowsMarkType(l.type):Pr(l.type,e.type))&&(o=l.addToSet(o));return s.content.push(e.mark(o)),!0}return!1}enter(e,t,n,i){let s=this.findPlace(e.create(t),n,!1);return s&&(s=this.enterInner(e,t,n,!0,i)),s}enterInner(e,t,n,i=!1,s){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(e);let l=Ir(e,s,o.options);o.options&zt&&o.content.length==0&&(l|=zt);let a=A.none;return n=n.filter(c=>(o.type?o.type.allowsMarkType(c.type):Pr(c.type,e))?(a=c.addToSet(a),!1):!0),this.nodes.push(new vt(e,t,a,i,null,l)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=pt)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let i=n.length-1;i>=0;i--)e+=n[i].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let i=0;i<this.find.length;i++)this.find[i].pos==null&&e.nodeType==1&&e.contains(this.find[i].node)&&t.compareDocumentPosition(this.find[i].node)&(n?2:4)&&(this.find[i].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,i=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),s=-(n?n.depth+1:0)+(i?0:1),o=(l,a)=>{for(;l>=0;l--){let c=t[l];if(c==""){if(l==t.length-1||l==0)continue;for(;a>=s;a--)if(o(l-1,a))return!0;return!1}else{let f=a>0||a==0&&i?this.nodes[a].type:n&&a>=s?n.node(a-s).type:null;if(!f||f.name!=c&&!f.isInGroup(c))return!1;a--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let n=this.parser.schema.nodes[t];if(n.isTextblock&&n.defaultAttrs)return n}}}function Lo(r){for(let e=r.firstChild,t=null;e;e=e.nextSibling){let n=e.nodeType==1?e.nodeName.toLowerCase():null;n&&Hi.hasOwnProperty(n)&&t?(t.appendChild(e),e=t):n=="li"?t=e:n&&(t=null)}}function Wo(r,e){return(r.matches||r.msMatchesSelector||r.webkitMatchesSelector||r.mozMatchesSelector).call(r,e)}function vr(r){let e={};for(let t in r)e[t]=r[t];return e}function Pr(r,e){let t=e.schema.nodes;for(let n in t){let i=t[n];if(!i.allowsMarkType(r))continue;let s=[],o=l=>{s.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:f}=l.edge(a);if(c==e||s.indexOf(f)<0&&o(f))return!0}};if(o(i.contentMatch))return!0}}class We{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=yn(t).createDocumentFragment());let i=n,s=[];return e.forEach(o=>{if(s.length||o.marks.length){let l=0,a=0;for(;l<s.length&&a<o.marks.length;){let c=o.marks[a];if(!this.marks[c.type.name]){a++;continue}if(!c.eq(s[l][0])||c.type.spec.spanning===!1)break;l++,a++}for(;l<s.length;)i=s.pop()[1];for(;a<o.marks.length;){let c=o.marks[a++],f=this.serializeMark(c,o.isInline,t);f&&(s.push([c,i]),i.appendChild(f.dom),i=f.contentDOM||f.dom)}}i.appendChild(this.serializeNodeInner(o,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:i}=Ft(yn(t),this.nodes[e.type.name](e),null,e.attrs);if(i){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,i)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let i=e.marks.length-1;i>=0;i--){let s=this.serializeMark(e.marks[i],e.isInline,t);s&&((s.contentDOM||s.dom).appendChild(n),n=s.dom)}return n}serializeMark(e,t,n={}){let i=this.marks[e.type.name];return i&&Ft(yn(n),i(e,t),null,e.attrs)}static renderSpec(e,t,n=null,i){return Ft(e,t,n,i)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new We(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=Br(e.nodes);return t.text||(t.text=n=>n.text),t}static marksFromSchema(e){return Br(e.marks)}}function Br(r){let e={};for(let t in r){let n=r[t].spec.toDOM;n&&(e[t]=n)}return e}function yn(r){return r.document||window.document}const zr=new WeakMap;function jo(r){let e=zr.get(r);return e===void 0&&zr.set(r,e=Jo(r)),e}function Jo(r){let e=null;function t(n){if(n&&typeof n=="object")if(Array.isArray(n))if(typeof n[0]=="string")e||(e=[]),e.push(n);else for(let i=0;i<n.length;i++)t(n[i]);else for(let i in n)t(n[i])}return t(r),e}function Ft(r,e,t,n){if(typeof e=="string")return{dom:r.createTextNode(e)};if(e.nodeType!=null)return{dom:e};if(e.dom&&e.dom.nodeType!=null)return e;let i=e[0],s;if(typeof i!="string")throw new RangeError("Invalid array passed to renderSpec");if(n&&(s=jo(n))&&s.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let o=i.indexOf(" ");o>0&&(t=i.slice(0,o),i=i.slice(o+1));let l,a=t?r.createElementNS(t,i):r.createElement(i),c=e[1],f=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){f=2;for(let d in c)if(c[d]!=null){let u=d.indexOf(" ");u>0?a.setAttributeNS(d.slice(0,u),d.slice(u+1),c[d]):a.setAttribute(d,c[d])}}for(let d=f;d<e.length;d++){let u=e[d];if(u===0){if(d<e.length-1||d>f)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}else{let{dom:h,contentDOM:p}=Ft(r,u,t,n);if(a.appendChild(h),p){if(l)throw new RangeError("Multiple content holes");l=p}}}return{dom:a,contentDOM:l}}const Ui=65535,Gi=Math.pow(2,16);function qo(r,e){return r+e*Gi}function Fr(r){return r&Ui}function Ko(r){return(r-(r&Ui))/Gi}const _i=1,Yi=2,Vt=4,Xi=8;class Fn{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(this.delInfo&Xi)>0}get deletedBefore(){return(this.delInfo&(_i|Vt))>0}get deletedAfter(){return(this.delInfo&(Yi|Vt))>0}get deletedAcross(){return(this.delInfo&Vt)>0}}class _{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&_.empty)return _.empty}recover(e){let t=0,n=Fr(e);if(!this.inverted)for(let i=0;i<n;i++)t+=this.ranges[i*3+2]-this.ranges[i*3+1];return this.ranges[n*3]+t+Ko(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let i=0,s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?i:0);if(a>e)break;let c=this.ranges[l+s],f=this.ranges[l+o],d=a+c;if(e<=d){let u=c?e==a?-1:e==d?1:t:t,h=a+i+(u<0?0:f);if(n)return h;let p=e==(t<0?a:d)?null:qo(l/3,e-a),m=e==a?Yi:e==d?_i:Vt;return(t<0?e!=a:e!=d)&&(m|=Xi),new Fn(h,m,p)}i+=f-c}return n?e+i:new Fn(e+i,0,null)}touches(e,t){let n=0,i=Fr(t),s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?n:0);if(a>e)break;let c=this.ranges[l+s],f=a+c;if(e<=f&&l==i*3)return!0;n+=this.ranges[l+o]-c}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let i=0,s=0;i<this.ranges.length;i+=3){let o=this.ranges[i],l=o-(this.inverted?s:0),a=o+(this.inverted?0:s),c=this.ranges[i+t],f=this.ranges[i+n];e(l,l+c,a,a+f),s+=f-c}}invert(){return new _(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?_.empty:new _(e<0?[0,-e,0]:[0,0,e])}}_.empty=new _([]);class Kt{constructor(e,t,n=0,i=e?e.length:0){this.mirror=t,this.from=n,this.to=i,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new Kt(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),t!=null&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let i=e.getMirror(t);this.appendMap(e._maps[t],i!=null&&i<t?n+i:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let i=e.getMirror(t);this.appendMap(e._maps[t].invert(),i!=null&&i>t?n-i-1:void 0)}}invert(){let e=new Kt;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let i=0;for(let s=this.from;s<this.to;s++){let o=this._maps[s],l=o.mapResult(e,t);if(l.recover!=null){let a=this.getMirror(s);if(a!=null&&a>s&&a<this.to){s=a,e=this._maps[a].recover(l.recover);continue}}i|=l.delInfo,e=l.pos}return n?e:new Fn(e,i,null)}}const bn=Object.create(null);class j{getMap(){return _.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=bn[t.stepType];if(!n)throw new RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in bn)throw new RangeError("Duplicate use of step JSON ID "+e);return bn[e]=t,t.prototype.jsonID=e,t}}class R{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new R(e,null)}static fail(e){return new R(null,e)}static fromReplace(e,t,n,i){try{return R.ok(e.replace(t,n,i))}catch(s){if(s instanceof Wt)return R.fail(s.message);throw s}}}function Qn(r,e,t){let n=[];for(let i=0;i<r.childCount;i++){let s=r.child(i);s.content.size&&(s=s.copy(Qn(s.content,e,s))),s.isInline&&(s=e(s,t,i)),n.push(s)}return b.fromArray(n)}class be extends j{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),i=n.node(n.sharedDepth(this.to)),s=new k(Qn(t.content,(o,l)=>!o.isAtom||!l.type.allowsMarkType(this.mark.type)?o:o.mark(this.mark.addToSet(o.marks)),i),t.openStart,t.openEnd);return R.fromReplace(e,this.from,this.to,s)}invert(){return new re(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new be(t.pos,n.pos,this.mark)}merge(e){return e instanceof be&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new be(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new be(t.from,t.to,e.markFromJSON(t.mark))}}j.jsonID("addMark",be);class re extends j{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new k(Qn(t.content,i=>i.mark(this.mark.removeFromSet(i.marks)),e),t.openStart,t.openEnd);return R.fromReplace(e,this.from,this.to,n)}invert(){return new be(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new re(t.pos,n.pos,this.mark)}merge(e){return e instanceof re&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new re(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new re(t.from,t.to,e.markFromJSON(t.mark))}}j.jsonID("removeMark",re);class ke extends j{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return R.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return R.fromReplace(e,this.pos,this.pos+1,new k(b.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let n=this.mark.addToSet(t.marks);if(n.length==t.marks.length){for(let i=0;i<t.marks.length;i++)if(!t.marks[i].isInSet(n))return new ke(this.pos,t.marks[i]);return new ke(this.pos,this.mark)}}return new Ve(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new ke(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new ke(t.pos,e.markFromJSON(t.mark))}}j.jsonID("addNodeMark",ke);class Ve extends j{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return R.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return R.fromReplace(e,this.pos,this.pos+1,new k(b.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return!t||!this.mark.isInSet(t.marks)?this:new ke(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Ve(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new Ve(t.pos,e.markFromJSON(t.mark))}}j.jsonID("removeNodeMark",Ve);class B extends j{constructor(e,t,n,i=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=i}apply(e){return this.structure&&Vn(e,this.from,this.to)?R.fail("Structure replace would overwrite content"):R.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new _([this.from,this.to-this.from,this.slice.size])}invert(e){return new B(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new B(t.pos,Math.max(t.pos,n.pos),this.slice,this.structure)}merge(e){if(!(e instanceof B)||e.structure||this.structure)return null;if(this.from+this.slice.size==e.from&&!this.slice.openEnd&&!e.slice.openStart){let t=this.slice.size+e.slice.size==0?k.empty:new k(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new B(this.from,this.to+(e.to-e.from),t,this.structure)}else if(e.to==this.from&&!this.slice.openStart&&!e.slice.openEnd){let t=this.slice.size+e.slice.size==0?k.empty:new k(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new B(e.from,this.to,t,this.structure)}else return null}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new B(t.from,t.to,k.fromJSON(e,t.slice),!!t.structure)}}j.jsonID("replace",B);class z extends j{constructor(e,t,n,i,s,o,l=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=i,this.slice=s,this.insert=o,this.structure=l}apply(e){if(this.structure&&(Vn(e,this.from,this.gapFrom)||Vn(e,this.gapTo,this.to)))return R.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return R.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?R.fromReplace(e,this.from,this.to,n):R.fail("Content does not fit in gap")}getMap(){return new _([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new z(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),i=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),s=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||i<t.pos||s>n.pos?null:new z(t.pos,n.pos,i,s,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new z(t.from,t.to,t.gapFrom,t.gapTo,k.fromJSON(e,t.slice),t.insert,!!t.structure)}}j.jsonID("replaceAround",z);function Vn(r,e,t){let n=r.resolve(e),i=t-e,s=n.depth;for(;i>0&&s>0&&n.indexAfter(s)==n.node(s).childCount;)s--,i--;if(i>0){let o=n.node(s).maybeChild(n.indexAfter(s));for(;i>0;){if(!o||o.isLeaf)return!0;o=o.firstChild,i--}}return!1}function Ho(r,e,t,n){let i=[],s=[],o,l;r.doc.nodesBetween(e,t,(a,c,f)=>{if(!a.isInline)return;let d=a.marks;if(!n.isInSet(d)&&f.type.allowsMarkType(n.type)){let u=Math.max(c,e),h=Math.min(c+a.nodeSize,t),p=n.addToSet(d);for(let m=0;m<d.length;m++)d[m].isInSet(p)||(o&&o.to==u&&o.mark.eq(d[m])?o.to=h:i.push(o=new re(u,h,d[m])));l&&l.to==u?l.to=h:s.push(l=new be(u,h,n))}}),i.forEach(a=>r.step(a)),s.forEach(a=>r.step(a))}function Uo(r,e,t,n){let i=[],s=0;r.doc.nodesBetween(e,t,(o,l)=>{if(!o.isInline)return;s++;let a=null;if(n instanceof nn){let c=o.marks,f;for(;f=n.isInSet(c);)(a||(a=[])).push(f),c=f.removeFromSet(c)}else n?n.isInSet(o.marks)&&(a=[n]):a=o.marks;if(a&&a.length){let c=Math.min(l+o.nodeSize,t);for(let f=0;f<a.length;f++){let d=a[f],u;for(let h=0;h<i.length;h++){let p=i[h];p.step==s-1&&d.eq(i[h].style)&&(u=p)}u?(u.to=c,u.step=s):i.push({style:d,from:Math.max(l,e),to:c,step:s})}}}),i.forEach(o=>r.step(new re(o.from,o.to,o.style)))}function er(r,e,t,n=t.contentMatch,i=!0){let s=r.doc.nodeAt(e),o=[],l=e+1;for(let a=0;a<s.childCount;a++){let c=s.child(a),f=l+c.nodeSize,d=n.matchType(c.type);if(!d)o.push(new B(l,f,k.empty));else{n=d;for(let u=0;u<c.marks.length;u++)t.allowsMarkType(c.marks[u].type)||r.step(new re(l,f,c.marks[u]));if(i&&c.isText&&t.whitespace!="pre"){let u,h=/\r?\n|\r/g,p;for(;u=h.exec(c.text);)p||(p=new k(b.from(t.schema.text(" ",t.allowedMarks(c.marks))),0,0)),o.push(new B(l+u.index,l+u.index+u[0].length,p))}}l=f}if(!n.validEnd){let a=n.fillBefore(b.empty,!0);r.replace(l,l,new k(a,0,0))}for(let a=o.length-1;a>=0;a--)r.step(o[a])}function Go(r,e,t){return(e==0||r.canReplace(e,r.childCount))&&(t==r.childCount||r.canReplace(0,t))}function nt(r){let t=r.parent.content.cutByIndex(r.startIndex,r.endIndex);for(let n=r.depth;;--n){let i=r.$from.node(n),s=r.$from.index(n),o=r.$to.indexAfter(n);if(n<r.depth&&i.canReplace(s,o,t))return n;if(n==0||i.type.spec.isolating||!Go(i,s,o))break}return null}function _o(r,e,t){let{$from:n,$to:i,depth:s}=e,o=n.before(s+1),l=i.after(s+1),a=o,c=l,f=b.empty,d=0;for(let p=s,m=!1;p>t;p--)m||n.index(p)>0?(m=!0,f=b.from(n.node(p).copy(f)),d++):a--;let u=b.empty,h=0;for(let p=s,m=!1;p>t;p--)m||i.after(p+1)<i.end(p)?(m=!0,u=b.from(i.node(p).copy(u)),h++):c++;r.step(new z(a,c,o,l,new k(f.append(u),d,h),f.size-d,!0))}function tr(r,e,t=null,n=r){let i=Yo(r,e),s=i&&Xo(n,e);return s?i.map(Vr).concat({type:e,attrs:t}).concat(s.map(Vr)):null}function Vr(r){return{type:r,attrs:null}}function Yo(r,e){let{parent:t,startIndex:n,endIndex:i}=r,s=t.contentMatchAt(n).findWrapping(e);if(!s)return null;let o=s.length?s[0]:e;return t.canReplaceWith(n,i,o)?s:null}function Xo(r,e){let{parent:t,startIndex:n,endIndex:i}=r,s=t.child(n),o=e.contentMatch.findWrapping(s.type);if(!o)return null;let a=(o.length?o[o.length-1]:e).contentMatch;for(let c=n;a&&c<i;c++)a=a.matchType(t.child(c).type);return!a||!a.validEnd?null:o}function Zo(r,e,t){let n=b.empty;for(let o=t.length-1;o>=0;o--){if(n.size){let l=t[o].type.contentMatch.matchFragment(n);if(!l||!l.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}n=b.from(t[o].type.create(t[o].attrs,n))}let i=e.start,s=e.end;r.step(new z(i,s,i,s,new k(n,0,0),t.length,!0))}function Qo(r,e,t,n,i){if(!n.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let s=r.steps.length;r.doc.nodesBetween(e,t,(o,l)=>{let a=typeof i=="function"?i(o):i;if(o.isTextblock&&!o.hasMarkup(n,a)&&el(r.doc,r.mapping.slice(s).map(l),n)){let c=null;if(n.schema.linebreakReplacement){let h=n.whitespace=="pre",p=!!n.contentMatch.matchType(n.schema.linebreakReplacement);h&&!p?c=!1:!h&&p&&(c=!0)}c===!1&&Qi(r,o,l,s),er(r,r.mapping.slice(s).map(l,1),n,void 0,c===null);let f=r.mapping.slice(s),d=f.map(l,1),u=f.map(l+o.nodeSize,1);return r.step(new z(d,u,d+1,u-1,new k(b.from(n.create(a,null,o.marks)),0,0),1,!0)),c===!0&&Zi(r,o,l,s),!1}})}function Zi(r,e,t,n){e.forEach((i,s)=>{if(i.isText){let o,l=/\r?\n|\r/g;for(;o=l.exec(i.text);){let a=r.mapping.slice(n).map(t+1+s+o.index);r.replaceWith(a,a+1,e.type.schema.linebreakReplacement.create())}}})}function Qi(r,e,t,n){e.forEach((i,s)=>{if(i.type==i.type.schema.linebreakReplacement){let o=r.mapping.slice(n).map(t+1+s);r.replaceWith(o,o+1,e.type.schema.text(`
`))}})}function el(r,e,t){let n=r.resolve(e),i=n.index();return n.parent.canReplaceWith(i,i+1,t)}function tl(r,e,t,n,i){let s=r.doc.nodeAt(e);if(!s)throw new RangeError("No node at given position");t||(t=s.type);let o=t.create(n,null,i||s.marks);if(s.isLeaf)return r.replaceWith(e,e+s.nodeSize,o);if(!t.validContent(s.content))throw new RangeError("Invalid content for node type "+t.name);r.step(new z(e,e+s.nodeSize,e+1,e+s.nodeSize-1,new k(b.from(o),0,0),1,!0))}function fe(r,e,t=1,n){let i=r.resolve(e),s=i.depth-t,o=n&&n[n.length-1]||i.parent;if(s<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!o.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let c=i.depth-1,f=t-2;c>s;c--,f--){let d=i.node(c),u=i.index(c);if(d.type.spec.isolating)return!1;let h=d.content.cutByIndex(u,d.childCount),p=n&&n[f+1];p&&(h=h.replaceChild(0,p.type.create(p.attrs)));let m=n&&n[f]||d;if(!d.canReplace(u+1,d.childCount)||!m.type.validContent(h))return!1}let l=i.indexAfter(s),a=n&&n[0];return i.node(s).canReplaceWith(l,l,a?a.type:i.node(s+1).type)}function nl(r,e,t=1,n){let i=r.doc.resolve(e),s=b.empty,o=b.empty;for(let l=i.depth,a=i.depth-t,c=t-1;l>a;l--,c--){s=b.from(i.node(l).copy(s));let f=n&&n[c];o=b.from(f?f.type.create(f.attrs,o):i.node(l).copy(o))}r.step(new B(e,e,new k(s.append(o),t,t),!0))}function Ee(r,e){let t=r.resolve(e),n=t.index();return es(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(n,n+1)}function rl(r,e){e.content.size||r.type.compatibleContent(e.type);let t=r.contentMatchAt(r.childCount),{linebreakReplacement:n}=r.type.schema;for(let i=0;i<e.childCount;i++){let s=e.child(i),o=s.type==n?r.type.schema.nodes.text:s.type;if(t=t.matchType(o),!t||!r.type.allowsMarks(s.marks))return!1}return t.validEnd}function es(r,e){return!!(r&&e&&!r.isLeaf&&rl(r,e))}function rn(r,e,t=-1){let n=r.resolve(e);for(let i=n.depth;;i--){let s,o,l=n.index(i);if(i==n.depth?(s=n.nodeBefore,o=n.nodeAfter):t>0?(s=n.node(i+1),l++,o=n.node(i).maybeChild(l)):(s=n.node(i).maybeChild(l-1),o=n.node(i+1)),s&&!s.isTextblock&&es(s,o)&&n.node(i).canReplace(l,l+1))return e;if(i==0)break;e=t<0?n.before(i):n.after(i)}}function il(r,e,t){let n=null,{linebreakReplacement:i}=r.doc.type.schema,s=r.doc.resolve(e-t),o=s.node().type;if(i&&o.inlineContent){let f=o.whitespace=="pre",d=!!o.contentMatch.matchType(i);f&&!d?n=!1:!f&&d&&(n=!0)}let l=r.steps.length;if(n===!1){let f=r.doc.resolve(e+t);Qi(r,f.node(),f.before(),l)}o.inlineContent&&er(r,e+t-1,o,s.node().contentMatchAt(s.index()),n==null);let a=r.mapping.slice(l),c=a.map(e-t);if(r.step(new B(c,a.map(e+t,-1),k.empty,!0)),n===!0){let f=r.doc.resolve(c);Zi(r,f.node(),f.before(),r.steps.length)}return r}function sl(r,e,t){let n=r.resolve(e);if(n.parent.canReplaceWith(n.index(),n.index(),t))return e;if(n.parentOffset==0)for(let i=n.depth-1;i>=0;i--){let s=n.index(i);if(n.node(i).canReplaceWith(s,s,t))return n.before(i+1);if(s>0)return null}if(n.parentOffset==n.parent.content.size)for(let i=n.depth-1;i>=0;i--){let s=n.indexAfter(i);if(n.node(i).canReplaceWith(s,s,t))return n.after(i+1);if(s<n.node(i).childCount)return null}return null}function ol(r,e,t){let n=r.resolve(e);if(!t.content.size)return e;let i=t.content;for(let s=0;s<t.openStart;s++)i=i.firstChild.content;for(let s=1;s<=(t.openStart==0&&t.size?2:1);s++)for(let o=n.depth;o>=0;o--){let l=o==n.depth?0:n.pos<=(n.start(o+1)+n.end(o+1))/2?-1:1,a=n.index(o)+(l>0?1:0),c=n.node(o),f=!1;if(s==1)f=c.canReplace(a,a,i);else{let d=c.contentMatchAt(a).findWrapping(i.firstChild.type);f=d&&c.canReplaceWith(a,a,d[0])}if(f)return l==0?n.pos:l<0?n.before(o+1):n.after(o+1)}return null}function sn(r,e,t=e,n=k.empty){if(e==t&&!n.size)return null;let i=r.resolve(e),s=r.resolve(t);return ts(i,s,n)?new B(e,t,n):new ll(i,s,n).fit()}function ts(r,e,t){return!t.openStart&&!t.openEnd&&r.start()==e.start()&&r.parent.canReplace(r.index(),e.index(),t.content)}class ll{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=b.empty;for(let i=0;i<=e.depth;i++){let s=e.node(i);this.frontier.push({type:s.type,match:s.contentMatchAt(e.indexAfter(i))})}for(let i=e.depth;i>0;i--)this.placed=b.from(e.node(i).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,i=this.close(e<0?this.$to:n.doc.resolve(e));if(!i)return null;let s=this.placed,o=n.depth,l=i.depth;for(;o&&l&&s.childCount==1;)s=s.firstChild.content,o--,l--;let a=new k(s,o,l);return e>-1?new z(n.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||n.pos!=this.$to.pos?new B(n.pos,i.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,i=this.unplaced.openEnd;n<e;n++){let s=t.firstChild;if(t.childCount>1&&(i=0),s.type.spec.isolating&&i<=n){e=n;break}t=s.content}for(let t=1;t<=2;t++)for(let n=t==1?e:this.unplaced.openStart;n>=0;n--){let i,s=null;n?(s=kn(this.unplaced.content,n-1).firstChild,i=s.content):i=this.unplaced.content;let o=i.firstChild;for(let l=this.depth;l>=0;l--){let{type:a,match:c}=this.frontier[l],f,d=null;if(t==1&&(o?c.matchType(o.type)||(d=c.fillBefore(b.from(o),!1)):s&&a.compatibleContent(s.type)))return{sliceDepth:n,frontierDepth:l,parent:s,inject:d};if(t==2&&o&&(f=c.findWrapping(o.type)))return{sliceDepth:n,frontierDepth:l,parent:s,wrap:f};if(s&&c.matchType(s.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,i=kn(e,t);return!i.childCount||i.firstChild.isLeaf?!1:(this.unplaced=new k(e,t+1,Math.max(n,i.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,i=kn(e,t);if(i.childCount<=1&&t>0){let s=e.size-t<=t+i.size;this.unplaced=new k(it(e,t-1,1),t-1,s?t-1:n)}else this.unplaced=new k(it(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:i,wrap:s}){for(;this.depth>t;)this.closeFrontierNode();if(s)for(let m=0;m<s.length;m++)this.openFrontierNode(s[m]);let o=this.unplaced,l=n?n.content:o.content,a=o.openStart-e,c=0,f=[],{match:d,type:u}=this.frontier[t];if(i){for(let m=0;m<i.childCount;m++)f.push(i.child(m));d=d.matchFragment(i)}let h=l.size+e-(o.content.size-o.openEnd);for(;c<l.childCount;){let m=l.child(c),g=d.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(d=g,f.push(ns(m.mark(u.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?h:-1)))}let p=c==l.childCount;p||(h=-1),this.placed=st(this.placed,t,b.from(f)),this.frontier[t].match=d,p&&h<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<h;m++){let y=g.lastChild;this.frontier.push({type:y.type,match:y.contentMatchAt(y.childCount)}),g=y.content}this.unplaced=p?e==0?k.empty:new k(it(o.content,e-1,1),e-1,h<0?o.openEnd:e-1):new k(it(o.content,e,c),o.openStart,o.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!xn(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:n}=this.$to,i=this.$to.after(n);for(;n>1&&i==this.$to.end(--n);)++i;return i}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:i}=this.frontier[t],s=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=xn(e,t,i,n,s);if(o){for(let l=t-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],f=xn(e,l,c,a,!0);if(!f||f.childCount)continue e}return{depth:t,fit:o,move:s?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=st(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let i=e.node(n),s=i.type.contentMatch.fillBefore(i.content,!0,e.index(n));this.openFrontierNode(i.type,i.attrs,s)}return e}openFrontierNode(e,t=null,n){let i=this.frontier[this.depth];i.match=i.match.matchType(e),this.placed=st(this.placed,this.depth,b.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(b.empty,!0);t.childCount&&(this.placed=st(this.placed,this.frontier.length,t))}}function it(r,e,t){return e==0?r.cutByIndex(t,r.childCount):r.replaceChild(0,r.firstChild.copy(it(r.firstChild.content,e-1,t)))}function st(r,e,t){return e==0?r.append(t):r.replaceChild(r.childCount-1,r.lastChild.copy(st(r.lastChild.content,e-1,t)))}function kn(r,e){for(let t=0;t<e;t++)r=r.firstChild.content;return r}function ns(r,e,t){if(e<=0)return r;let n=r.content;return e>1&&(n=n.replaceChild(0,ns(n.firstChild,e-1,n.childCount==1?t-1:0))),e>0&&(n=r.type.contentMatch.fillBefore(n).append(n),t<=0&&(n=n.append(r.type.contentMatch.matchFragment(n).fillBefore(b.empty,!0)))),r.copy(n)}function xn(r,e,t,n,i){let s=r.node(e),o=i?r.indexAfter(e):r.index(e);if(o==s.childCount&&!t.compatibleContent(s.type))return null;let l=n.fillBefore(s.content,!0,o);return l&&!al(t,s.content,o)?l:null}function al(r,e,t){for(let n=t;n<e.childCount;n++)if(!r.allowsMarks(e.child(n).marks))return!0;return!1}function cl(r){return r.spec.defining||r.spec.definingForContent}function fl(r,e,t,n){if(!n.size)return r.deleteRange(e,t);let i=r.doc.resolve(e),s=r.doc.resolve(t);if(ts(i,s,n))return r.step(new B(e,t,n));let o=is(i,r.doc.resolve(t));o[o.length-1]==0&&o.pop();let l=-(i.depth+1);o.unshift(l);for(let u=i.depth,h=i.pos-1;u>0;u--,h--){let p=i.node(u).type.spec;if(p.defining||p.definingAsContext||p.isolating)break;o.indexOf(u)>-1?l=u:i.before(u)==h&&o.splice(1,0,-u)}let a=o.indexOf(l),c=[],f=n.openStart;for(let u=n.content,h=0;;h++){let p=u.firstChild;if(c.push(p),h==n.openStart)break;u=p.content}for(let u=f-1;u>=0;u--){let h=c[u],p=cl(h.type);if(p&&!h.sameMarkup(i.node(Math.abs(l)-1)))f=u;else if(p||!h.type.isTextblock)break}for(let u=n.openStart;u>=0;u--){let h=(u+f+1)%(n.openStart+1),p=c[h];if(p)for(let m=0;m<o.length;m++){let g=o[(m+a)%o.length],y=!0;g<0&&(y=!1,g=-g);let M=i.node(g-1),O=i.index(g-1);if(M.canReplaceWith(O,O,p.type,p.marks))return r.replace(i.before(g),y?s.after(g):t,new k(rs(n.content,0,n.openStart,h),h,n.openEnd))}}let d=r.steps.length;for(let u=o.length-1;u>=0&&(r.replace(e,t,n),!(r.steps.length>d));u--){let h=o[u];h<0||(e=i.before(h),t=s.after(h))}}function rs(r,e,t,n,i){if(e<t){let s=r.firstChild;r=r.replaceChild(0,s.copy(rs(s.content,e+1,t,n,s)))}if(e>n){let s=i.contentMatchAt(0),o=s.fillBefore(r).append(r);r=o.append(s.matchFragment(o).fillBefore(b.empty,!0))}return r}function dl(r,e,t,n){if(!n.isInline&&e==t&&r.doc.resolve(e).parent.content.size){let i=sl(r.doc,e,n.type);i!=null&&(e=t=i)}r.replaceRange(e,t,new k(b.from(n),0,0))}function ul(r,e,t){let n=r.doc.resolve(e),i=r.doc.resolve(t),s=is(n,i);for(let o=0;o<s.length;o++){let l=s[o],a=o==s.length-1;if(a&&l==0||n.node(l).type.contentMatch.validEnd)return r.delete(n.start(l),i.end(l));if(l>0&&(a||n.node(l-1).canReplace(n.index(l-1),i.indexAfter(l-1))))return r.delete(n.before(l),i.after(l))}for(let o=1;o<=n.depth&&o<=i.depth;o++)if(e-n.start(o)==n.depth-o&&t>n.end(o)&&i.end(o)-t!=i.depth-o&&n.start(o-1)==i.start(o-1)&&n.node(o-1).canReplace(n.index(o-1),i.index(o-1)))return r.delete(n.before(o),t);r.delete(e,t)}function is(r,e){let t=[],n=Math.min(r.depth,e.depth);for(let i=n;i>=0;i--){let s=r.start(i);if(s<r.pos-(r.depth-i)||e.end(i)>e.pos+(e.depth-i)||r.node(i).type.spec.isolating||e.node(i).type.spec.isolating)break;(s==e.start(i)||i==r.depth&&i==e.depth&&r.parent.inlineContent&&e.parent.inlineContent&&i&&e.start(i-1)==s-1)&&t.push(i)}return t}class _e extends j{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return R.fail("No node at attribute step's position");let n=Object.create(null);for(let s in t.attrs)n[s]=t.attrs[s];n[this.attr]=this.value;let i=t.type.create(n,null,t.marks);return R.fromReplace(e,this.pos,this.pos+1,new k(b.from(i),0,t.isLeaf?0:1))}getMap(){return _.empty}invert(e){return new _e(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new _e(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new _e(t.pos,t.attr,t.value)}}j.jsonID("attr",_e);class mt extends j{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let i in e.attrs)t[i]=e.attrs[i];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return R.ok(n)}getMap(){return _.empty}invert(e){return new mt(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new mt(t.attr,t.value)}}j.jsonID("docAttr",mt);let Xe=class extends Error{};Xe=function r(e){let t=Error.call(this,e);return t.__proto__=r.prototype,t};Xe.prototype=Object.create(Error.prototype);Xe.prototype.constructor=Xe;Xe.prototype.name="TransformError";class hl{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new Kt}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new Xe(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=k.empty){let i=sn(this.doc,e,t,n);return i&&this.step(i),this}replaceWith(e,t,n){return this.replace(e,t,new k(b.from(n),0,0))}delete(e,t){return this.replace(e,t,k.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return fl(this,e,t,n),this}replaceRangeWith(e,t,n){return dl(this,e,t,n),this}deleteRange(e,t){return ul(this,e,t),this}lift(e,t){return _o(this,e,t),this}join(e,t=1){return il(this,e,t),this}wrap(e,t){return Zo(this,e,t),this}setBlockType(e,t=e,n,i=null){return Qo(this,e,t,n,i),this}setNodeMarkup(e,t,n=null,i){return tl(this,e,t,n,i),this}setNodeAttribute(e,t,n){return this.step(new _e(e,t,n)),this}setDocAttribute(e,t){return this.step(new mt(e,t)),this}addNodeMark(e,t){return this.step(new ke(e,t)),this}removeNodeMark(e,t){let n=this.doc.nodeAt(e);if(!n)throw new RangeError("No node at position "+e);if(t instanceof A)t.isInSet(n.marks)&&this.step(new Ve(e,t));else{let i=n.marks,s,o=[];for(;s=t.isInSet(i);)o.push(new Ve(e,s)),i=s.removeFromSet(i);for(let l=o.length-1;l>=0;l--)this.step(o[l])}return this}split(e,t=1,n){return nl(this,e,t,n),this}addMark(e,t,n){return Ho(this,e,t,n),this}removeMark(e,t,n){return Uo(this,e,t,n),this}clearIncompatible(e,t,n){return er(this,e,t,n),this}}const Sn=Object.create(null);class N{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new pl(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=k.empty){let n=t.content.lastChild,i=null;for(let l=0;l<t.openEnd;l++)i=n,n=n.lastChild;let s=e.steps.length,o=this.ranges;for(let l=0;l<o.length;l++){let{$from:a,$to:c}=o[l],f=e.mapping.slice(s);e.replaceRange(f.map(a.pos),f.map(c.pos),l?k.empty:t),l==0&&Wr(e,s,(n?n.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,i=this.ranges;for(let s=0;s<i.length;s++){let{$from:o,$to:l}=i[s],a=e.mapping.slice(n),c=a.map(o.pos),f=a.map(l.pos);s?e.deleteRange(c,f):(e.replaceRangeWith(c,f,t),Wr(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let i=e.parent.inlineContent?new w(e):Ke(e.node(0),e.parent,e.pos,e.index(),t,n);if(i)return i;for(let s=e.depth-1;s>=0;s--){let o=t<0?Ke(e.node(0),e.node(s),e.before(s+1),e.index(s),t,n):Ke(e.node(0),e.node(s),e.after(s+1),e.index(s)+1,t,n);if(o)return o}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new Y(e.node(0))}static atStart(e){return Ke(e,e,0,0,1)||new Y(e)}static atEnd(e){return Ke(e,e,e.content.size,e.childCount,-1)||new Y(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=Sn[t.type];if(!n)throw new RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in Sn)throw new RangeError("Duplicate use of selection JSON ID "+e);return Sn[e]=t,t.prototype.jsonID=e,t}getBookmark(){return w.between(this.$anchor,this.$head).getBookmark()}}N.prototype.visible=!0;class pl{constructor(e,t){this.$from=e,this.$to=t}}let $r=!1;function Lr(r){!$r&&!r.parent.inlineContent&&($r=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+r.parent.type.name+")"))}class w extends N{constructor(e,t=e){Lr(e),Lr(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return N.near(n);let i=e.resolve(t.map(this.anchor));return new w(i.parent.inlineContent?i:n,n)}replace(e,t=k.empty){if(super.replace(e,t),t==k.empty){let n=this.$from.marksAcross(this.$to);n&&e.ensureMarks(n)}}eq(e){return e instanceof w&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new on(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new w(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let i=e.resolve(t);return new this(i,n==t?i:e.resolve(n))}static between(e,t,n){let i=e.pos-t.pos;if((!n||i)&&(n=i>=0?1:-1),!t.parent.inlineContent){let s=N.findFrom(t,n,!0)||N.findFrom(t,-n,!0);if(s)t=s.$head;else return N.near(t,n)}return e.parent.inlineContent||(i==0?e=t:(e=(N.findFrom(e,-n,!0)||N.findFrom(e,n,!0)).$anchor,e.pos<t.pos!=i<0&&(e=t))),new w(e,t)}}N.jsonID("text",w);class on{constructor(e,t){this.anchor=e,this.head=t}map(e){return new on(e.map(this.anchor),e.map(this.head))}resolve(e){return w.between(e.resolve(this.anchor),e.resolve(this.head))}}class S extends N{constructor(e){let t=e.nodeAfter,n=e.node(0).resolve(e.pos+t.nodeSize);super(e,n),this.node=t}map(e,t){let{deleted:n,pos:i}=t.mapResult(this.anchor),s=e.resolve(i);return n?N.near(s):new S(s)}content(){return new k(b.from(this.node),0,0)}eq(e){return e instanceof S&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new nr(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new S(e.resolve(t.anchor))}static create(e,t){return new S(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}}S.prototype.visible=!1;N.jsonID("node",S);class nr{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new on(n,n):new nr(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&S.isSelectable(n)?new S(t):N.near(t)}}class Y extends N{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=k.empty){if(t==k.empty){e.delete(0,e.doc.content.size);let n=N.atStart(e.doc);n.eq(e.selection)||e.setSelection(n)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new Y(e)}map(e){return new Y(e)}eq(e){return e instanceof Y}getBookmark(){return ml}}N.jsonID("all",Y);const ml={map(){return this},resolve(r){return new Y(r)}};function Ke(r,e,t,n,i,s=!1){if(e.inlineContent)return w.create(r,t);for(let o=n-(i>0?0:1);i>0?o<e.childCount:o>=0;o+=i){let l=e.child(o);if(l.isAtom){if(!s&&S.isSelectable(l))return S.create(r,t-(i<0?l.nodeSize:0))}else{let a=Ke(r,l,t+i,i<0?l.childCount:0,i,s);if(a)return a}t+=l.nodeSize*i}return null}function Wr(r,e,t){let n=r.steps.length-1;if(n<e)return;let i=r.steps[n];if(!(i instanceof B||i instanceof z))return;let s=r.mapping.maps[n],o;s.forEach((l,a,c,f)=>{o==null&&(o=f)}),r.setSelection(N.near(r.doc.resolve(o),t))}const jr=1,Jr=2,qr=4;class gl extends hl{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(this.updated|jr)&-3,this.storedMarks=null,this}get selectionSet(){return(this.updated&jr)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=Jr,this}ensureMarks(e){return A.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(this.updated&Jr)>0}addStep(e,t){super.addStep(e,t),this.updated=this.updated&-3,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||A.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let i=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(i.text(e),!0):this.deleteSelection();{if(n==null&&(n=t),n=n??t,!e)return this.deleteRange(t,n);let s=this.storedMarks;if(!s){let o=this.doc.resolve(t);s=n==t?o.marks():o.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,i.text(e,s)),this.selection.empty||this.setSelection(N.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=qr,this}get scrolledIntoView(){return(this.updated&qr)>0}}function Kr(r,e){return!e||!r?r:r.bind(e)}class ot{constructor(e,t,n){this.name=e,this.init=Kr(t.init,n),this.apply=Kr(t.apply,n)}}const yl=[new ot("doc",{init(r){return r.doc||r.schema.topNodeType.createAndFill()},apply(r){return r.doc}}),new ot("selection",{init(r,e){return r.selection||N.atStart(e.doc)},apply(r){return r.selection}}),new ot("storedMarks",{init(r){return r.storedMarks||null},apply(r,e,t,n){return n.selection.$cursor?r.storedMarks:null}}),new ot("scrollToSelection",{init(){return 0},apply(r,e){return r.scrolledIntoView?e+1:e}})];class Mn{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=yl.slice(),t&&t.forEach(n=>{if(this.pluginsByKey[n.key])throw new RangeError("Adding different instances of a keyed plugin ("+n.key+")");this.plugins.push(n),this.pluginsByKey[n.key]=n,n.spec.state&&this.fields.push(new ot(n.key,n.spec.state,n))})}}class Ue{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let i=this.config.plugins[n];if(i.spec.filterTransaction&&!i.spec.filterTransaction.call(i,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),i=null;for(;;){let s=!1;for(let o=0;o<this.config.plugins.length;o++){let l=this.config.plugins[o];if(l.spec.appendTransaction){let a=i?i[o].n:0,c=i?i[o].state:this,f=a<t.length&&l.spec.appendTransaction.call(l,a?t.slice(a):t,c,n);if(f&&n.filterTransaction(f,o)){if(f.setMeta("appendedTransaction",e),!i){i=[];for(let d=0;d<this.config.plugins.length;d++)i.push(d<o?{state:n,n:t.length}:{state:this,n:0})}t.push(f),n=n.applyInner(f),s=!0}i&&(i[o]={state:n,n:t.length})}}if(!s)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new Ue(this.config),n=this.config.fields;for(let i=0;i<n.length;i++){let s=n[i];t[s.name]=s.apply(e,this[s.name],this,t)}return t}get tr(){return new gl(this)}static create(e){let t=new Mn(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new Ue(t);for(let i=0;i<t.fields.length;i++)n[t.fields[i].name]=t.fields[i].init(e,n);return n}reconfigure(e){let t=new Mn(this.schema,e.plugins),n=t.fields,i=new Ue(t);for(let s=0;s<n.length;s++){let o=n[s].name;i[o]=this.hasOwnProperty(o)?this[o]:n[s].init(e,i)}return i}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(n=>n.toJSON())),e&&typeof e=="object")for(let n in e){if(n=="doc"||n=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let i=e[n],s=i.spec.state;s&&s.toJSON&&(t[n]=s.toJSON.call(i,this[i.key]))}return t}static fromJSON(e,t,n){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let i=new Mn(e.schema,e.plugins),s=new Ue(i);return i.fields.forEach(o=>{if(o.name=="doc")s.doc=Me.fromJSON(e.schema,t.doc);else if(o.name=="selection")s.selection=N.fromJSON(s.doc,t.selection);else if(o.name=="storedMarks")t.storedMarks&&(s.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let l in n){let a=n[l],c=a.spec.state;if(a.key==o.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,l)){s[o.name]=c.fromJSON.call(a,e,t[l],s);return}}s[o.name]=o.init(e,s)}}),s}}function ss(r,e,t){for(let n in r){let i=r[n];i instanceof Function?i=i.bind(e):n=="handleDOMEvents"&&(i=ss(i,e,{})),t[n]=i}return t}class oe{constructor(e){this.spec=e,this.props={},e.props&&ss(e.props,this,this.props),this.key=e.key?e.key.key:ls("plugin")}getState(e){return e[this.key]}}const Cn=Object.create(null);function ls(r){return r in Cn?r+"$"+ ++Cn[r]:(Cn[r]=0,r+"$")}class je{constructor(e="key"){this.key=ls(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const V=function(r){for(var e=0;;e++)if(r=r.previousSibling,!r)return e},Ze=function(r){let e=r.assignedSlot||r.parentNode;return e&&e.nodeType==11?e.host:e};let $n=null;const ae=function(r,e,t){let n=$n||($n=document.createRange());return n.setEnd(r,t??r.nodeValue.length),n.setStart(r,e||0),n},bl=function(){$n=null},$e=function(r,e,t,n){return t&&(Hr(r,e,t,n,-1)||Hr(r,e,t,n,1))},kl=/^(img|br|input|textarea|hr)$/i;function Hr(r,e,t,n,i){for(;;){if(r==t&&e==n)return!0;if(e==(i<0?0:Z(r))){let s=r.parentNode;if(!s||s.nodeType!=1||Mt(r)||kl.test(r.nodeName)||r.contentEditable=="false")return!1;e=V(r)+(i<0?0:1),r=s}else if(r.nodeType==1){if(r=r.childNodes[e+(i<0?-1:0)],r.contentEditable=="false")return!1;e=i<0?Z(r):0}else return!1}}function Z(r){return r.nodeType==3?r.nodeValue.length:r.childNodes.length}function xl(r,e){for(;;){if(r.nodeType==3&&e)return r;if(r.nodeType==1&&e>0){if(r.contentEditable=="false")return null;r=r.childNodes[e-1],e=Z(r)}else if(r.parentNode&&!Mt(r))e=V(r),r=r.parentNode;else return null}}function Sl(r,e){for(;;){if(r.nodeType==3&&e<r.nodeValue.length)return r;if(r.nodeType==1&&e<r.childNodes.length){if(r.contentEditable=="false")return null;r=r.childNodes[e],e=0}else if(r.parentNode&&!Mt(r))e=V(r)+1,r=r.parentNode;else return null}}function Ml(r,e,t){for(let n=e==0,i=e==Z(r);n||i;){if(r==t)return!0;let s=V(r);if(r=r.parentNode,!r)return!1;n=n&&s==0,i=i&&s==Z(r)}}function Mt(r){let e;for(let t=r;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==r||e.contentDOM==r)}const ln=function(r){return r.focusNode&&$e(r.focusNode,r.focusOffset,r.anchorNode,r.anchorOffset)};function Ae(r,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=r,t.key=t.code=e,t}function Cl(r){let e=r.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function wl(r,e,t){if(r.caretPositionFromPoint)try{let n=r.caretPositionFromPoint(e,t);if(n)return{node:n.offsetNode,offset:Math.min(Z(n.offsetNode),n.offset)}}catch{}if(r.caretRangeFromPoint){let n=r.caretRangeFromPoint(e,t);if(n)return{node:n.startContainer,offset:Math.min(Z(n.startContainer),n.startOffset)}}}const ie=typeof navigator<"u"?navigator:null,Ur=typeof document<"u"?document:null,De=ie&&ie.userAgent||"",Ln=/Edge\/(\d+)/.exec(De),as=/MSIE \d/.exec(De),Wn=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(De),G=!!(as||Wn||Ln),we=as?document.documentMode:Wn?+Wn[1]:Ln?+Ln[1]:0,ne=!G&&/gecko\/(\d+)/i.test(De);ne&&+(/Firefox\/(\d+)/.exec(De)||[0,0])[1];const jn=!G&&/Chrome\/(\d+)/.exec(De),W=!!jn,cs=jn?+jn[1]:0,q=!G&&!!ie&&/Apple Computer/.test(ie.vendor),Qe=q&&(/Mobile\/\w+/.test(De)||!!ie&&ie.maxTouchPoints>2),X=Qe||(ie?/Mac/.test(ie.platform):!1),Ol=ie?/Win/.test(ie.platform):!1,ce=/Android \d/.test(De),Ct=!!Ur&&"webkitFontSmoothing"in Ur.documentElement.style,Nl=Ct?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function Tl(r){let e=r.defaultView&&r.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:r.documentElement.clientWidth,top:0,bottom:r.documentElement.clientHeight}}function le(r,e){return typeof r=="number"?r:r[e]}function El(r){let e=r.getBoundingClientRect(),t=e.width/r.offsetWidth||1,n=e.height/r.offsetHeight||1;return{left:e.left,right:e.left+r.clientWidth*t,top:e.top,bottom:e.top+r.clientHeight*n}}function Gr(r,e,t){let n=r.someProp("scrollThreshold")||0,i=r.someProp("scrollMargin")||5,s=r.dom.ownerDocument;for(let o=t||r.dom;o;){if(o.nodeType!=1){o=Ze(o);continue}let l=o,a=l==s.body,c=a?Tl(s):El(l),f=0,d=0;if(e.top<c.top+le(n,"top")?d=-(c.top-e.top+le(i,"top")):e.bottom>c.bottom-le(n,"bottom")&&(d=e.bottom-e.top>c.bottom-c.top?e.top+le(i,"top")-c.top:e.bottom-c.bottom+le(i,"bottom")),e.left<c.left+le(n,"left")?f=-(c.left-e.left+le(i,"left")):e.right>c.right-le(n,"right")&&(f=e.right-c.right+le(i,"right")),f||d)if(a)s.defaultView.scrollBy(f,d);else{let h=l.scrollLeft,p=l.scrollTop;d&&(l.scrollTop+=d),f&&(l.scrollLeft+=f);let m=l.scrollLeft-h,g=l.scrollTop-p;e={left:e.left-m,top:e.top-g,right:e.right-m,bottom:e.bottom-g}}let u=a?"fixed":getComputedStyle(o).position;if(/^(fixed|sticky)$/.test(u))break;o=u=="absolute"?o.offsetParent:Ze(o)}}function Dl(r){let e=r.dom.getBoundingClientRect(),t=Math.max(0,e.top),n,i;for(let s=(e.left+e.right)/2,o=t+1;o<Math.min(innerHeight,e.bottom);o+=5){let l=r.root.elementFromPoint(s,o);if(!l||l==r.dom||!r.dom.contains(l))continue;let a=l.getBoundingClientRect();if(a.top>=t-20){n=l,i=a.top;break}}return{refDOM:n,refTop:i,stack:fs(r.dom)}}function fs(r){let e=[],t=r.ownerDocument;for(let n=r;n&&(e.push({dom:n,top:n.scrollTop,left:n.scrollLeft}),r!=t);n=Ze(n));return e}function Al({refDOM:r,refTop:e,stack:t}){let n=r?r.getBoundingClientRect().top:0;ds(t,n==0?0:n-e)}function ds(r,e){for(let t=0;t<r.length;t++){let{dom:n,top:i,left:s}=r[t];n.scrollTop!=i+e&&(n.scrollTop=i+e),n.scrollLeft!=s&&(n.scrollLeft=s)}}let Je=null;function Il(r){if(r.setActive)return r.setActive();if(Je)return r.focus(Je);let e=fs(r);r.focus(Je==null?{get preventScroll(){return Je={preventScroll:!0},!0}}:void 0),Je||(Je=!1,ds(e,0))}function us(r,e){let t,n=2e8,i,s=0,o=e.top,l=e.top,a,c;for(let f=r.firstChild,d=0;f;f=f.nextSibling,d++){let u;if(f.nodeType==1)u=f.getClientRects();else if(f.nodeType==3)u=ae(f).getClientRects();else continue;for(let h=0;h<u.length;h++){let p=u[h];if(p.top<=o&&p.bottom>=l){o=Math.max(p.bottom,o),l=Math.min(p.top,l);let m=p.left>e.left?p.left-e.left:p.right<e.left?e.left-p.right:0;if(m<n){t=f,n=m,i=m&&t.nodeType==3?{left:p.right<e.left?p.right:p.left,top:e.top}:e,f.nodeType==1&&m&&(s=d+(e.left>=(p.left+p.right)/2?1:0));continue}}else p.top>e.top&&!a&&p.left<=e.left&&p.right>=e.left&&(a=f,c={left:Math.max(p.left,Math.min(p.right,e.left)),top:p.top});!t&&(e.left>=p.right&&e.top>=p.top||e.left>=p.left&&e.top>=p.bottom)&&(s=d+1)}}return!t&&a&&(t=a,i=c,n=0),t&&t.nodeType==3?Rl(t,i):!t||n&&t.nodeType==1?{node:r,offset:s}:us(t,i)}function Rl(r,e){let t=r.nodeValue.length,n=document.createRange();for(let i=0;i<t;i++){n.setEnd(r,i+1),n.setStart(r,i);let s=pe(n,1);if(s.top!=s.bottom&&rr(e,s))return{node:r,offset:i+(e.left>=(s.left+s.right)/2?1:0)}}return{node:r,offset:0}}function rr(r,e){return r.left>=e.left-1&&r.left<=e.right+1&&r.top>=e.top-1&&r.top<=e.bottom+1}function vl(r,e){let t=r.parentNode;return t&&/^li$/i.test(t.nodeName)&&e.left<r.getBoundingClientRect().left?t:r}function Pl(r,e,t){let{node:n,offset:i}=us(e,t),s=-1;if(n.nodeType==1&&!n.firstChild){let o=n.getBoundingClientRect();s=o.left!=o.right&&t.left>(o.left+o.right)/2?1:-1}return r.docView.posFromDOM(n,i,s)}function Bl(r,e,t,n){let i=-1;for(let s=e,o=!1;s!=r.dom;){let l=r.docView.nearestDesc(s,!0),a;if(!l)return null;if(l.dom.nodeType==1&&(l.node.isBlock&&l.parent||!l.contentDOM)&&((a=l.dom.getBoundingClientRect()).width||a.height)&&(l.node.isBlock&&l.parent&&(!o&&a.left>n.left||a.top>n.top?i=l.posBefore:(!o&&a.right<n.left||a.bottom<n.top)&&(i=l.posAfter),o=!0),!l.contentDOM&&i<0&&!l.node.isText))return(l.node.isBlock?n.top<(a.top+a.bottom)/2:n.left<(a.left+a.right)/2)?l.posBefore:l.posAfter;s=l.dom.parentNode}return i>-1?i:r.docView.posFromDOM(e,t,-1)}function hs(r,e,t){let n=r.childNodes.length;if(n&&t.top<t.bottom)for(let i=Math.max(0,Math.min(n-1,Math.floor(n*(e.top-t.top)/(t.bottom-t.top))-2)),s=i;;){let o=r.childNodes[s];if(o.nodeType==1){let l=o.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(rr(e,c))return hs(o,e,c)}}if((s=(s+1)%n)==i)break}return r}function zl(r,e){let t=r.dom.ownerDocument,n,i=0,s=wl(t,e.left,e.top);s&&({node:n,offset:i}=s);let o=(r.root.elementFromPoint?r.root:t).elementFromPoint(e.left,e.top),l;if(!o||!r.dom.contains(o.nodeType!=1?o.parentNode:o)){let c=r.dom.getBoundingClientRect();if(!rr(e,c)||(o=hs(r.dom,e,c),!o))return null}if(q)for(let c=o;n&&c;c=Ze(c))c.draggable&&(n=void 0);if(o=vl(o,e),n){if(ne&&n.nodeType==1&&(i=Math.min(i,n.childNodes.length),i<n.childNodes.length)){let f=n.childNodes[i],d;f.nodeName=="IMG"&&(d=f.getBoundingClientRect()).right<=e.left&&d.bottom>e.top&&i++}let c;Ct&&i&&n.nodeType==1&&(c=n.childNodes[i-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&i--,n==r.dom&&i==n.childNodes.length-1&&n.lastChild.nodeType==1&&e.top>n.lastChild.getBoundingClientRect().bottom?l=r.state.doc.content.size:(i==0||n.nodeType!=1||n.childNodes[i-1].nodeName!="BR")&&(l=Bl(r,n,i,e))}l==null&&(l=Pl(r,o,e));let a=r.docView.nearestDesc(o,!0);return{pos:l,inside:a?a.posAtStart-a.border:-1}}function _r(r){return r.top<r.bottom||r.left<r.right}function pe(r,e){let t=r.getClientRects();if(t.length){let n=t[e<0?0:t.length-1];if(_r(n))return n}return Array.prototype.find.call(t,_r)||r.getBoundingClientRect()}const Fl=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function ps(r,e,t){let{node:n,offset:i,atom:s}=r.docView.domFromPos(e,t<0?-1:1),o=Ct||ne;if(n.nodeType==3)if(o&&(Fl.test(n.nodeValue)||(t<0?!i:i==n.nodeValue.length))){let a=pe(ae(n,i,i),t);if(ne&&i&&/\s/.test(n.nodeValue[i-1])&&i<n.nodeValue.length){let c=pe(ae(n,i-1,i-1),-1);if(c.top==a.top){let f=pe(ae(n,i,i+1),-1);if(f.top!=a.top)return rt(f,f.left<c.left)}}return a}else{let a=i,c=i,f=t<0?1:-1;return t<0&&!i?(c++,f=-1):t>=0&&i==n.nodeValue.length?(a--,f=1):t<0?a--:c++,rt(pe(ae(n,a,c),f),f<0)}if(!r.state.doc.resolve(e-(s||0)).parent.inlineContent){if(s==null&&i&&(t<0||i==Z(n))){let a=n.childNodes[i-1];if(a.nodeType==1)return wn(a.getBoundingClientRect(),!1)}if(s==null&&i<Z(n)){let a=n.childNodes[i];if(a.nodeType==1)return wn(a.getBoundingClientRect(),!0)}return wn(n.getBoundingClientRect(),t>=0)}if(s==null&&i&&(t<0||i==Z(n))){let a=n.childNodes[i-1],c=a.nodeType==3?ae(a,Z(a)-(o?0:1)):a.nodeType==1&&(a.nodeName!="BR"||!a.nextSibling)?a:null;if(c)return rt(pe(c,1),!1)}if(s==null&&i<Z(n)){let a=n.childNodes[i];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let c=a?a.nodeType==3?ae(a,0,o?0:1):a.nodeType==1?a:null:null;if(c)return rt(pe(c,-1),!0)}return rt(pe(n.nodeType==3?ae(n):n,-t),t>=0)}function rt(r,e){if(r.width==0)return r;let t=e?r.left:r.right;return{top:r.top,bottom:r.bottom,left:t,right:t}}function wn(r,e){if(r.height==0)return r;let t=e?r.top:r.bottom;return{top:t,bottom:t,left:r.left,right:r.right}}function ms(r,e,t){let n=r.state,i=r.root.activeElement;n!=e&&r.updateState(e),i!=r.dom&&r.focus();try{return t()}finally{n!=e&&r.updateState(n),i!=r.dom&&i&&i.focus()}}function Vl(r,e,t){let n=e.selection,i=t=="up"?n.$from:n.$to;return ms(r,e,()=>{let{node:s}=r.docView.domFromPos(i.pos,t=="up"?-1:1);for(;;){let l=r.docView.nearestDesc(s,!0);if(!l)break;if(l.node.isBlock){s=l.contentDOM||l.dom;break}s=l.dom.parentNode}let o=ps(r,i.pos,1);for(let l=s.firstChild;l;l=l.nextSibling){let a;if(l.nodeType==1)a=l.getClientRects();else if(l.nodeType==3)a=ae(l,0,l.nodeValue.length).getClientRects();else continue;for(let c=0;c<a.length;c++){let f=a[c];if(f.bottom>f.top+1&&(t=="up"?o.top-f.top>(f.bottom-o.top)*2:f.bottom-o.bottom>(o.bottom-f.top)*2))return!1}}return!0})}const $l=/[\u0590-\u08ac]/;function Ll(r,e,t){let{$head:n}=e.selection;if(!n.parent.isTextblock)return!1;let i=n.parentOffset,s=!i,o=i==n.parent.content.size,l=r.domSelection();return l?!$l.test(n.parent.textContent)||!l.modify?t=="left"||t=="backward"?s:o:ms(r,e,()=>{let{focusNode:a,focusOffset:c,anchorNode:f,anchorOffset:d}=r.domSelectionRange(),u=l.caretBidiLevel;l.modify("move",t,"character");let h=n.depth?r.docView.domAfterPos(n.before()):r.dom,{focusNode:p,focusOffset:m}=r.domSelectionRange(),g=p&&!h.contains(p.nodeType==1?p:p.parentNode)||a==p&&c==m;try{l.collapse(f,d),a&&(a!=f||c!=d)&&l.extend&&l.extend(a,c)}catch{}return u!=null&&(l.caretBidiLevel=u),g}):n.pos==n.start()||n.pos==n.end()}let Yr=null,Xr=null,Zr=!1;function Wl(r,e,t){return Yr==e&&Xr==t?Zr:(Yr=e,Xr=t,Zr=t=="up"||t=="down"?Vl(r,e,t):Ll(r,e,t))}const Q=0,Qr=1,Ie=2,se=3;class wt{constructor(e,t,n,i){this.parent=e,this.children=t,this.dom=n,this.contentDOM=i,this.dirty=Q,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let i=this.children[t];if(i==e)return n;n+=i.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode))if(n<0){let s,o;if(e==this.contentDOM)s=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;s=e.previousSibling}for(;s&&!((o=s.pmViewDesc)&&o.parent==this);)s=s.previousSibling;return s?this.posBeforeChild(o)+o.size:this.posAtStart}else{let s,o;if(e==this.contentDOM)s=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;s=e.nextSibling}for(;s&&!((o=s.pmViewDesc)&&o.parent==this);)s=s.nextSibling;return s?this.posBeforeChild(o):this.posAtEnd}let i;if(e==this.dom&&this.contentDOM)i=t>V(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))i=e.compareDocumentPosition(this.contentDOM)&2;else if(this.dom.firstChild){if(t==0)for(let s=e;;s=s.parentNode){if(s==this.dom){i=!1;break}if(s.previousSibling)break}if(i==null&&t==e.childNodes.length)for(let s=e;;s=s.parentNode){if(s==this.dom){i=!0;break}if(s.nextSibling)break}}return i??n>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,i=e;i;i=i.parentNode){let s=this.getDesc(i),o;if(s&&(!t||s.node))if(n&&(o=s.nodeDOM)&&!(o.nodeType==1?o.contains(e.nodeType==1?e:e.parentNode):o==e))n=!1;else return s}}getDesc(e){let t=e.pmViewDesc;for(let n=t;n;n=n.parent)if(n==this)return t}posFromDOM(e,t,n){for(let i=e;i;i=i.parentNode){let s=this.getDesc(i);if(s)return s.localPosFromDOM(e,t,n)}return-1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let i=this.children[t],s=n+i.size;if(n==e&&s!=n){for(;!i.border&&i.children.length;)for(let o=0;o<i.children.length;o++){let l=i.children[o];if(l.size){i=l;break}}return i}if(e<s)return i.descAt(e-n-i.border);n=s}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,i=0;for(let s=0;n<this.children.length;n++){let o=this.children[n],l=s+o.size;if(l>e||o instanceof ys){i=e-s;break}s=l}if(i)return this.children[n].domFromPos(i-this.children[n].border,t);for(let s;n&&!(s=this.children[n-1]).size&&s instanceof gs&&s.side>=0;n--);if(t<=0){let s,o=!0;for(;s=n?this.children[n-1]:null,!(!s||s.dom.parentNode==this.contentDOM);n--,o=!1);return s&&t&&o&&!s.border&&!s.domAtom?s.domFromPos(s.size,t):{node:this.contentDOM,offset:s?V(s.dom)+1:0}}else{let s,o=!0;for(;s=n<this.children.length?this.children[n]:null,!(!s||s.dom.parentNode==this.contentDOM);n++,o=!1);return s&&o&&!s.border&&!s.domAtom?s.domFromPos(0,t):{node:this.contentDOM,offset:s?V(s.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let i=-1,s=-1;for(let o=n,l=0;;l++){let a=this.children[l],c=o+a.size;if(i==-1&&e<=c){let f=o+a.border;if(e>=f&&t<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,f);e=o;for(let d=l;d>0;d--){let u=this.children[d-1];if(u.size&&u.dom.parentNode==this.contentDOM&&!u.emptyChildAt(1)){i=V(u.dom)+1;break}e-=u.size}i==-1&&(i=0)}if(i>-1&&(c>t||l==this.children.length-1)){t=c;for(let f=l+1;f<this.children.length;f++){let d=this.children[f];if(d.size&&d.dom.parentNode==this.contentDOM&&!d.emptyChildAt(-1)){s=V(d.dom);break}t+=d.size}s==-1&&(s=this.contentDOM.childNodes.length);break}o=c}return{node:this.contentDOM,from:e,to:t,fromOffset:i,toOffset:s}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(t.nodeType!=1||n==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,i=!1){let s=Math.min(e,t),o=Math.max(e,t);for(let h=0,p=0;h<this.children.length;h++){let m=this.children[h],g=p+m.size;if(s>p&&o<g)return m.setSelection(e-p-m.border,t-p-m.border,n,i);p=g}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),c=n.root.getSelection(),f=n.domSelectionRange(),d=!1;if((ne||q)&&e==t){let{node:h,offset:p}=l;if(h.nodeType==3){if(d=!!(p&&h.nodeValue[p-1]==`
`),d&&p==h.nodeValue.length)for(let m=h,g;m;m=m.parentNode){if(g=m.nextSibling){g.nodeName=="BR"&&(l=a={node:g.parentNode,offset:V(g)+1});break}let y=m.pmViewDesc;if(y&&y.node&&y.node.isBlock)break}}else{let m=h.childNodes[p-1];d=m&&(m.nodeName=="BR"||m.contentEditable=="false")}}if(ne&&f.focusNode&&f.focusNode!=a.node&&f.focusNode.nodeType==1){let h=f.focusNode.childNodes[f.focusOffset];h&&h.contentEditable=="false"&&(i=!0)}if(!(i||d&&q)&&$e(l.node,l.offset,f.anchorNode,f.anchorOffset)&&$e(a.node,a.offset,f.focusNode,f.focusOffset))return;let u=!1;if((c.extend||e==t)&&!d){c.collapse(l.node,l.offset);try{e!=t&&c.extend(a.node,a.offset),u=!0}catch{}}if(!u){if(e>t){let p=l;l=a,a=p}let h=document.createRange();h.setEnd(a.node,a.offset),h.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(h)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,i=0;i<this.children.length;i++){let s=this.children[i],o=n+s.size;if(n==o?e<=o&&t>=n:e<o&&t>n){let l=n+s.border,a=o-s.border;if(e>=l&&t<=a){this.dirty=e==n||t==o?Ie:Qr,e==l&&t==a&&(s.contentLost||s.dom.parentNode!=this.contentDOM)?s.dirty=se:s.markDirty(e-l,t-l);return}else s.dirty=s.dom==s.contentDOM&&s.dom.parentNode==this.contentDOM&&!s.children.length?Ie:se}n=o}this.dirty=Ie}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=e==1?Ie:Qr;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(e){return!1}}class gs extends wt{constructor(e,t,n,i){let s,o=t.type.toDOM;if(typeof o=="function"&&(o=o(n,()=>{if(!s)return i;if(s.parent)return s.parent.posBeforeChild(s)})),!t.type.spec.raw){if(o.nodeType!=1){let l=document.createElement("span");l.appendChild(o),o=l}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(e,[],o,null),this.widget=t,this.widget=t,s=this}matchesWidget(e){return this.dirty==Q&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return t?t(e):!1}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class jl extends wt{constructor(e,t,n,i){super(e,[],t,null),this.textDOM=n,this.text=i}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}}class Le extends wt{constructor(e,t,n,i,s){super(e,[],n,i),this.mark=t,this.spec=s}static create(e,t,n,i){let s=i.nodeViews[t.type.name],o=s&&s(t,i,n);return(!o||!o.dom)&&(o=We.renderSpec(document,t.type.spec.toDOM(t,n),null,t.attrs)),new Le(e,t,o.dom,o.contentDOM||o.dom,o)}parseRule(){return this.dirty&se||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=se&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=Q){let n=this.parent;for(;!n.node;)n=n.parent;n.dirty<this.dirty&&(n.dirty=this.dirty),this.dirty=Q}}slice(e,t,n){let i=Le.create(this.parent,this.mark,!0,n),s=this.children,o=this.size;t<o&&(s=qn(s,t,o,n)),e>0&&(s=qn(s,0,e,n));for(let l=0;l<s.length;l++)s[l].parent=i;return i.children=s,i}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class Oe extends wt{constructor(e,t,n,i,s,o,l,a,c){super(e,[],s,o),this.node=t,this.outerDeco=n,this.innerDeco=i,this.nodeDOM=l}static create(e,t,n,i,s,o){let l=s.nodeViews[t.type.name],a,c=l&&l(t,s,()=>{if(!a)return o;if(a.parent)return a.parent.posBeforeChild(a)},n,i),f=c&&c.dom,d=c&&c.contentDOM;if(t.isText){if(!f)f=document.createTextNode(t.text);else if(f.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else f||({dom:f,contentDOM:d}=We.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs));!d&&!t.isText&&f.nodeName!="BR"&&(f.hasAttribute("contenteditable")||(f.contentEditable="false"),t.type.spec.draggable&&(f.draggable=!0));let u=f;return f=xs(f,n,t),c?a=new Jl(e,t,n,i,f,d||null,u,c,s,o+1):t.isText?new an(e,t,n,i,f,u,s):new Oe(e,t,n,i,f,d||null,u,s,o+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),!this.contentDOM)e.getContent=()=>this.node.content;else if(!this.contentLost)e.contentElement=this.contentDOM;else{for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>b.empty)}return e}matchesNode(e,t,n){return this.dirty==Q&&e.eq(this.node)&&Ht(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let n=this.node.inlineContent,i=t,s=e.composing?this.localCompositionInfo(e,t):null,o=s&&s.pos>-1?s:null,l=s&&s.pos<0,a=new Kl(this,o&&o.node,e);Gl(this.node,this.innerDeco,(c,f,d)=>{c.spec.marks?a.syncToMarks(c.spec.marks,n,e):c.type.side>=0&&!d&&a.syncToMarks(f==this.node.childCount?A.none:this.node.child(f).marks,n,e),a.placeWidget(c,e,i)},(c,f,d,u)=>{a.syncToMarks(c.marks,n,e);let h;a.findNodeMatch(c,f,d,u)||l&&e.state.selection.from>i&&e.state.selection.to<i+c.nodeSize&&(h=a.findIndexWithChild(s.node))>-1&&a.updateNodeAt(c,f,d,h,e)||a.updateNextNode(c,f,d,e,u,i)||a.addNode(c,f,d,e,i),i+=c.nodeSize}),a.syncToMarks([],n,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==Ie)&&(o&&this.protectLocalComposition(e,o),bs(this.contentDOM,this.children,e),Qe&&_l(this.dom))}localCompositionInfo(e,t){let{from:n,to:i}=e.state.selection;if(!(e.state.selection instanceof w)||n<t||i>t+this.node.content.size)return null;let s=e.input.compositionNode;if(!s||!this.dom.contains(s.parentNode))return null;if(this.node.inlineContent){let o=s.nodeValue,l=Yl(this.node.content,o,n-t,i-t);return l<0?null:{node:s,pos:l,text:o}}else return{node:s,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:n,text:i}){if(this.getDesc(t))return;let s=t;for(;s.parentNode!=this.contentDOM;s=s.parentNode){for(;s.previousSibling;)s.parentNode.removeChild(s.previousSibling);for(;s.nextSibling;)s.parentNode.removeChild(s.nextSibling);s.pmViewDesc&&(s.pmViewDesc=void 0)}let o=new jl(this,s,t,i);e.input.compositionNodes.push(o),this.children=qn(this.children,n,n+i.length,e,o)}update(e,t,n,i){return this.dirty==se||!e.sameMarkup(this.node)?!1:(this.updateInner(e,t,n,i),!0)}updateInner(e,t,n,i){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(i,this.posAtStart),this.dirty=Q}updateOuterDeco(e){if(Ht(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,n=this.dom;this.dom=ks(this.dom,this.nodeDOM,Jn(this.outerDeco,this.node,t),Jn(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function ei(r,e,t,n,i){xs(n,e,r);let s=new Oe(void 0,r,e,t,n,n,n,i,0);return s.contentDOM&&s.updateChildren(i,0),s}class an extends Oe{constructor(e,t,n,i,s,o,l){super(e,t,n,i,s,null,o,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,i){return this.dirty==se||this.dirty!=Q&&!this.inParent()||!e.sameMarkup(this.node)?!1:(this.updateOuterDeco(t),(this.dirty!=Q||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,i.trackWrites==this.nodeDOM&&(i.trackWrites=null)),this.node=e,this.dirty=Q,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,n){let i=this.node.cut(e,t),s=document.createTextNode(i.text);return new an(this.parent,i,this.outerDeco,this.innerDeco,s,s,n)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(e==0||t==this.nodeDOM.nodeValue.length)&&(this.dirty=se)}get domAtom(){return!1}isText(e){return this.node.text==e}}class ys extends wt{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==Q&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}}class Jl extends Oe{constructor(e,t,n,i,s,o,l,a,c,f){super(e,t,n,i,s,o,l,c,f),this.spec=a}update(e,t,n,i){if(this.dirty==se)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let s=this.spec.update(e,t,n);return s&&this.updateInner(e,t,n,i),s}else return!this.contentDOM&&!e.isLeaf?!1:super.update(e,t,n,i)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,i){this.spec.setSelection?this.spec.setSelection(e,t,n.root):super.setSelection(e,t,n,i)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return this.spec.stopEvent?this.spec.stopEvent(e):!1}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function bs(r,e,t){let n=r.firstChild,i=!1;for(let s=0;s<e.length;s++){let o=e[s],l=o.dom;if(l.parentNode==r){for(;l!=n;)n=ti(n),i=!0;n=n.nextSibling}else i=!0,r.insertBefore(l,n);if(o instanceof Le){let a=n?n.previousSibling:r.lastChild;bs(o.contentDOM,o.children,t),n=a?a.nextSibling:r.firstChild}}for(;n;)n=ti(n),i=!0;i&&t.trackWrites==r&&(t.trackWrites=null)}const ft=function(r){r&&(this.nodeName=r)};ft.prototype=Object.create(null);const Re=[new ft];function Jn(r,e,t){if(r.length==0)return Re;let n=t?Re[0]:new ft,i=[n];for(let s=0;s<r.length;s++){let o=r[s].type.attrs;if(o){o.nodeName&&i.push(n=new ft(o.nodeName));for(let l in o){let a=o[l];a!=null&&(t&&i.length==1&&i.push(n=new ft(e.isInline?"span":"div")),l=="class"?n.class=(n.class?n.class+" ":"")+a:l=="style"?n.style=(n.style?n.style+";":"")+a:l!="nodeName"&&(n[l]=a))}}}return i}function ks(r,e,t,n){if(t==Re&&n==Re)return e;let i=e;for(let s=0;s<n.length;s++){let o=n[s],l=t[s];if(s){let a;l&&l.nodeName==o.nodeName&&i!=r&&(a=i.parentNode)&&a.nodeName.toLowerCase()==o.nodeName||(a=document.createElement(o.nodeName),a.pmIsDeco=!0,a.appendChild(i),l=Re[0]),i=a}ql(i,l||Re[0],o)}return i}function ql(r,e,t){for(let n in e)n!="class"&&n!="style"&&n!="nodeName"&&!(n in t)&&r.removeAttribute(n);for(let n in t)n!="class"&&n!="style"&&n!="nodeName"&&t[n]!=e[n]&&r.setAttribute(n,t[n]);if(e.class!=t.class){let n=e.class?e.class.split(" ").filter(Boolean):[],i=t.class?t.class.split(" ").filter(Boolean):[];for(let s=0;s<n.length;s++)i.indexOf(n[s])==-1&&r.classList.remove(n[s]);for(let s=0;s<i.length;s++)n.indexOf(i[s])==-1&&r.classList.add(i[s]);r.classList.length==0&&r.removeAttribute("class")}if(e.style!=t.style){if(e.style){let n=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,i;for(;i=n.exec(e.style);)r.style.removeProperty(i[1])}t.style&&(r.style.cssText+=t.style)}}function xs(r,e,t){return ks(r,r,Re,Jn(e,t,r.nodeType!=1))}function Ht(r,e){if(r.length!=e.length)return!1;for(let t=0;t<r.length;t++)if(!r[t].type.eq(e[t].type))return!1;return!0}function ti(r){let e=r.nextSibling;return r.parentNode.removeChild(r),e}class Kl{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=Hl(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let i=0,s=this.stack.length>>1,o=Math.min(s,e.length);for(;i<o&&(i==s-1?this.top:this.stack[i+1<<1]).matchesMark(e[i])&&e[i].type.spec.spanning!==!1;)i++;for(;i<s;)this.destroyRest(),this.top.dirty=Q,this.index=this.stack.pop(),this.top=this.stack.pop(),s--;for(;s<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(e[s])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=Le.create(this.top,e[s],t,n);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,s++}}findNodeMatch(e,t,n,i){let s=-1,o;if(i>=this.preMatch.index&&(o=this.preMatch.matches[i-this.preMatch.index]).parent==this.top&&o.matchesNode(e,t,n))s=this.top.children.indexOf(o,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,t,n)&&!this.preMatch.matched.has(c)){s=l;break}}return s<0?!1:(this.destroyBetween(this.index,s),this.index++,!0)}updateNodeAt(e,t,n,i,s){let o=this.top.children[i];return o.dirty==se&&o.dom==o.contentDOM&&(o.dirty=Ie),o.update(e,t,n,s)?(this.destroyBetween(this.index,i),this.index++,!0):!1}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let n=e.pmViewDesc;if(n){for(let i=this.index;i<this.top.children.length;i++)if(this.top.children[i]==n)return i}return-1}e=t}}updateNextNode(e,t,n,i,s,o){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof Oe){let c=this.preMatch.matched.get(a);if(c!=null&&c!=s)return!1;let f=a.dom,d,u=this.isLocked(f)&&!(e.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==e.text&&a.dirty!=se&&Ht(t,a.outerDeco));if(!u&&a.update(e,t,n,i))return this.destroyBetween(this.index,l),a.dom!=f&&(this.changed=!0),this.index++,!0;if(!u&&(d=this.recreateWrapper(a,e,t,n,i,o)))return this.destroyBetween(this.index,l),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=Ie,d.updateChildren(i,o+1),d.dirty=Q),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,i,s,o){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!Ht(n,e.outerDeco)||!i.eq(e.innerDeco))return null;let l=Oe.create(this.top,t,n,i,s,o);if(l.contentDOM){l.children=e.children,e.children=[];for(let a of l.children)a.parent=l}return e.destroy(),l}addNode(e,t,n,i,s){let o=Oe.create(this.top,e,t,n,i,s);o.contentDOM&&o.updateChildren(i,s+1),this.top.children.splice(this.index++,0,o),this.changed=!0}placeWidget(e,t,n){let i=this.index<this.top.children.length?this.top.children[this.index]:null;if(i&&i.matchesWidget(e)&&(e==i.widget||!i.widget.type.toDOM.parentNode))this.index++;else{let s=new gs(this.top,e,t,n);this.top.children.splice(this.index++,0,s),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof Le;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof an)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((q||W)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);e=="IMG"&&(n.className="ProseMirror-separator",n.alt=""),e=="BR"&&(n.className="ProseMirror-trailingBreak");let i=new ys(this.top,[],n,null);t!=this.top?t.children.push(i):t.children.splice(this.index++,0,i),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}}function Hl(r,e){let t=e,n=t.children.length,i=r.childCount,s=new Map,o=[];e:for(;i>0;){let l;for(;;)if(n){let c=t.children[n-1];if(c instanceof Le)t=c,n=c.children.length;else{l=c,n--;break}}else{if(t==e)break e;n=t.parent.children.indexOf(t),t=t.parent}let a=l.node;if(a){if(a!=r.child(i-1))break;--i,s.set(l,i),o.push(l)}}return{index:i,matched:s,matches:o.reverse()}}function Ul(r,e){return r.type.side-e.type.side}function Gl(r,e,t,n){let i=e.locals(r),s=0;if(i.length==0){for(let c=0;c<r.childCount;c++){let f=r.child(c);n(f,i,e.forChild(s,f),c),s+=f.nodeSize}return}let o=0,l=[],a=null;for(let c=0;;){let f,d;for(;o<i.length&&i[o].to==s;){let g=i[o++];g.widget&&(f?(d||(d=[f])).push(g):f=g)}if(f)if(d){d.sort(Ul);for(let g=0;g<d.length;g++)t(d[g],c,!!a)}else t(f,c,!!a);let u,h;if(a)h=-1,u=a,a=null;else if(c<r.childCount)h=c,u=r.child(c++);else break;for(let g=0;g<l.length;g++)l[g].to<=s&&l.splice(g--,1);for(;o<i.length&&i[o].from<=s&&i[o].to>s;)l.push(i[o++]);let p=s+u.nodeSize;if(u.isText){let g=p;o<i.length&&i[o].from<g&&(g=i[o].from);for(let y=0;y<l.length;y++)l[y].to<g&&(g=l[y].to);g<p&&(a=u.cut(g-s),u=u.cut(0,g-s),p=g,h=-1)}else for(;o<i.length&&i[o].to<p;)o++;let m=u.isInline&&!u.isLeaf?l.filter(g=>!g.inline):l.slice();n(u,m,e.forChild(s,u),h),s=p}}function _l(r){if(r.nodeName=="UL"||r.nodeName=="OL"){let e=r.style.cssText;r.style.cssText=e+"; list-style: square !important",window.getComputedStyle(r).listStyle,r.style.cssText=e}}function Yl(r,e,t,n){for(let i=0,s=0;i<r.childCount&&s<=n;){let o=r.child(i++),l=s;if(s+=o.nodeSize,!o.isText)continue;let a=o.text;for(;i<r.childCount;){let c=r.child(i++);if(s+=c.nodeSize,!c.isText)break;a+=c.text}if(s>=t){if(s>=n&&a.slice(n-e.length-l,n-l)==e)return n-e.length;let c=l<n?a.lastIndexOf(e,n-l-1):-1;if(c>=0&&c+e.length+l>=t)return l+c;if(t==n&&a.length>=n+e.length-l&&a.slice(n-l,n-l+e.length)==e)return n}}return-1}function qn(r,e,t,n,i){let s=[];for(let o=0,l=0;o<r.length;o++){let a=r[o],c=l,f=l+=a.size;c>=t||f<=e?s.push(a):(c<e&&s.push(a.slice(0,e-c,n)),i&&(s.push(i),i=void 0),f>t&&s.push(a.slice(t-c,a.size,n)))}return s}function ir(r,e=null){let t=r.domSelectionRange(),n=r.state.doc;if(!t.focusNode)return null;let i=r.docView.nearestDesc(t.focusNode),s=i&&i.size==0,o=r.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(o<0)return null;let l=n.resolve(o),a,c;if(ln(t)){for(a=o;i&&!i.node;)i=i.parent;let d=i.node;if(i&&d.isAtom&&S.isSelectable(d)&&i.parent&&!(d.isInline&&Ml(t.focusNode,t.focusOffset,i.dom))){let u=i.posBefore;c=new S(o==u?l:n.resolve(u))}}else{if(t instanceof r.dom.ownerDocument.defaultView.Selection&&t.rangeCount>1){let d=o,u=o;for(let h=0;h<t.rangeCount;h++){let p=t.getRangeAt(h);d=Math.min(d,r.docView.posFromDOM(p.startContainer,p.startOffset,1)),u=Math.max(u,r.docView.posFromDOM(p.endContainer,p.endOffset,-1))}if(d<0)return null;[a,o]=u==r.state.selection.anchor?[u,d]:[d,u],l=n.resolve(o)}else a=r.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(a<0)return null}let f=n.resolve(a);if(!c){let d=e=="pointer"||r.state.selection.head<l.pos&&!s?1:-1;c=sr(r,f,l,d)}return c}function Ss(r){return r.editable?r.hasFocus():Cs(r)&&document.activeElement&&document.activeElement.contains(r.dom)}function de(r,e=!1){let t=r.state.selection;if(Ms(r,t),!!Ss(r)){if(!e&&r.input.mouseDown&&r.input.mouseDown.allowDefault&&W){let n=r.domSelectionRange(),i=r.domObserver.currentSelection;if(n.anchorNode&&i.anchorNode&&$e(n.anchorNode,n.anchorOffset,i.anchorNode,i.anchorOffset)){r.input.mouseDown.delayedSelectionSync=!0,r.domObserver.setCurSelection();return}}if(r.domObserver.disconnectSelection(),r.cursorWrapper)Zl(r);else{let{anchor:n,head:i}=t,s,o;ni&&!(t instanceof w)&&(t.$from.parent.inlineContent||(s=ri(r,t.from)),!t.empty&&!t.$from.parent.inlineContent&&(o=ri(r,t.to))),r.docView.setSelection(n,i,r,e),ni&&(s&&ii(s),o&&ii(o)),t.visible?r.dom.classList.remove("ProseMirror-hideselection"):(r.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&Xl(r))}r.domObserver.setCurSelection(),r.domObserver.connectSelection()}}const ni=q||W&&cs<63;function ri(r,e){let{node:t,offset:n}=r.docView.domFromPos(e,0),i=n<t.childNodes.length?t.childNodes[n]:null,s=n?t.childNodes[n-1]:null;if(q&&i&&i.contentEditable=="false")return On(i);if((!i||i.contentEditable=="false")&&(!s||s.contentEditable=="false")){if(i)return On(i);if(s)return On(s)}}function On(r){return r.contentEditable="true",q&&r.draggable&&(r.draggable=!1,r.wasDraggable=!0),r}function ii(r){r.contentEditable="false",r.wasDraggable&&(r.draggable=!0,r.wasDraggable=null)}function Xl(r){let e=r.dom.ownerDocument;e.removeEventListener("selectionchange",r.input.hideSelectionGuard);let t=r.domSelectionRange(),n=t.anchorNode,i=t.anchorOffset;e.addEventListener("selectionchange",r.input.hideSelectionGuard=()=>{(t.anchorNode!=n||t.anchorOffset!=i)&&(e.removeEventListener("selectionchange",r.input.hideSelectionGuard),setTimeout(()=>{(!Ss(r)||r.state.selection.visible)&&r.dom.classList.remove("ProseMirror-hideselection")},20))})}function Zl(r){let e=r.domSelection(),t=document.createRange();if(!e)return;let n=r.cursorWrapper.dom,i=n.nodeName=="IMG";i?t.setStart(n.parentNode,V(n)+1):t.setStart(n,0),t.collapse(!0),e.removeAllRanges(),e.addRange(t),!i&&!r.state.selection.visible&&G&&we<=11&&(n.disabled=!0,n.disabled=!1)}function Ms(r,e){if(e instanceof S){let t=r.docView.descAt(e.from);t!=r.lastSelectedViewDesc&&(si(r),t&&t.selectNode(),r.lastSelectedViewDesc=t)}else si(r)}function si(r){r.lastSelectedViewDesc&&(r.lastSelectedViewDesc.parent&&r.lastSelectedViewDesc.deselectNode(),r.lastSelectedViewDesc=void 0)}function sr(r,e,t,n){return r.someProp("createSelectionBetween",i=>i(r,e,t))||w.between(e,t,n)}function oi(r){return r.editable&&!r.hasFocus()?!1:Cs(r)}function Cs(r){let e=r.domSelectionRange();if(!e.anchorNode)return!1;try{return r.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(r.editable||r.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function Ql(r){let e=r.docView.domFromPos(r.state.selection.anchor,0),t=r.domSelectionRange();return $e(e.node,e.offset,t.anchorNode,t.anchorOffset)}function Kn(r,e){let{$anchor:t,$head:n}=r.selection,i=e>0?t.max(n):t.min(n),s=i.parent.inlineContent?i.depth?r.doc.resolve(e>0?i.after():i.before()):null:i;return s&&N.findFrom(s,e)}function me(r,e){return r.dispatch(r.state.tr.setSelection(e).scrollIntoView()),!0}function li(r,e,t){let n=r.state.selection;if(n instanceof w)if(t.indexOf("s")>-1){let{$head:i}=n,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!s||s.isText||!s.isLeaf)return!1;let o=r.state.doc.resolve(i.pos+s.nodeSize*(e<0?-1:1));return me(r,new w(n.$anchor,o))}else if(n.empty){if(r.endOfTextblock(e>0?"forward":"backward")){let i=Kn(r.state,e);return i&&i instanceof S?me(r,i):!1}else if(!(X&&t.indexOf("m")>-1)){let i=n.$head,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter,o;if(!s||s.isText)return!1;let l=e<0?i.pos-s.nodeSize:i.pos;return s.isAtom||(o=r.docView.descAt(l))&&!o.contentDOM?S.isSelectable(s)?me(r,new S(e<0?r.state.doc.resolve(i.pos-s.nodeSize):i)):Ct?me(r,new w(r.state.doc.resolve(e<0?l:l+s.nodeSize))):!1:!1}}else return!1;else{if(n instanceof S&&n.node.isInline)return me(r,new w(e>0?n.$to:n.$from));{let i=Kn(r.state,e);return i?me(r,i):!1}}}function Ut(r){return r.nodeType==3?r.nodeValue.length:r.childNodes.length}function dt(r,e){let t=r.pmViewDesc;return t&&t.size==0&&(e<0||r.nextSibling||r.nodeName!="BR")}function qe(r,e){return e<0?ea(r):ta(r)}function ea(r){let e=r.domSelectionRange(),t=e.focusNode,n=e.focusOffset;if(!t)return;let i,s,o=!1;for(ne&&t.nodeType==1&&n<Ut(t)&&dt(t.childNodes[n],-1)&&(o=!0);;)if(n>0){if(t.nodeType!=1)break;{let l=t.childNodes[n-1];if(dt(l,-1))i=t,s=--n;else if(l.nodeType==3)t=l,n=t.nodeValue.length;else break}}else{if(ws(t))break;{let l=t.previousSibling;for(;l&&dt(l,-1);)i=t.parentNode,s=V(l),l=l.previousSibling;if(l)t=l,n=Ut(t);else{if(t=t.parentNode,t==r.dom)break;n=0}}}o?Hn(r,t,n):i&&Hn(r,i,s)}function ta(r){let e=r.domSelectionRange(),t=e.focusNode,n=e.focusOffset;if(!t)return;let i=Ut(t),s,o;for(;;)if(n<i){if(t.nodeType!=1)break;let l=t.childNodes[n];if(dt(l,1))s=t,o=++n;else break}else{if(ws(t))break;{let l=t.nextSibling;for(;l&&dt(l,1);)s=l.parentNode,o=V(l)+1,l=l.nextSibling;if(l)t=l,n=0,i=Ut(t);else{if(t=t.parentNode,t==r.dom)break;n=i=0}}}s&&Hn(r,s,o)}function ws(r){let e=r.pmViewDesc;return e&&e.node&&e.node.isBlock}function na(r,e){for(;r&&e==r.childNodes.length&&!Mt(r);)e=V(r)+1,r=r.parentNode;for(;r&&e<r.childNodes.length;){let t=r.childNodes[e];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;r=t,e=0}}function ra(r,e){for(;r&&!e&&!Mt(r);)e=V(r),r=r.parentNode;for(;r&&e;){let t=r.childNodes[e-1];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;r=t,e=r.childNodes.length}}function Hn(r,e,t){if(e.nodeType!=3){let s,o;(o=na(e,t))?(e=o,t=0):(s=ra(e,t))&&(e=s,t=s.nodeValue.length)}let n=r.domSelection();if(!n)return;if(ln(n)){let s=document.createRange();s.setEnd(e,t),s.setStart(e,t),n.removeAllRanges(),n.addRange(s)}else n.extend&&n.extend(e,t);r.domObserver.setCurSelection();let{state:i}=r;setTimeout(()=>{r.state==i&&de(r)},50)}function ai(r,e){let t=r.state.doc.resolve(e);if(!(W||Ol)&&t.parent.inlineContent){let i=r.coordsAtPos(e);if(e>t.start()){let s=r.coordsAtPos(e-1),o=(s.top+s.bottom)/2;if(o>i.top&&o<i.bottom&&Math.abs(s.left-i.left)>1)return s.left<i.left?"ltr":"rtl"}if(e<t.end()){let s=r.coordsAtPos(e+1),o=(s.top+s.bottom)/2;if(o>i.top&&o<i.bottom&&Math.abs(s.left-i.left)>1)return s.left>i.left?"ltr":"rtl"}}return getComputedStyle(r.dom).direction=="rtl"?"rtl":"ltr"}function ci(r,e,t){let n=r.state.selection;if(n instanceof w&&!n.empty||t.indexOf("s")>-1||X&&t.indexOf("m")>-1)return!1;let{$from:i,$to:s}=n;if(!i.parent.inlineContent||r.endOfTextblock(e<0?"up":"down")){let o=Kn(r.state,e);if(o&&o instanceof S)return me(r,o)}if(!i.parent.inlineContent){let o=e<0?i:s,l=n instanceof Y?N.near(o,e):N.findFrom(o,e);return l?me(r,l):!1}return!1}function fi(r,e){if(!(r.state.selection instanceof w))return!0;let{$head:t,$anchor:n,empty:i}=r.state.selection;if(!t.sameParent(n))return!0;if(!i)return!1;if(r.endOfTextblock(e>0?"forward":"backward"))return!0;let s=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(s&&!s.isText){let o=r.state.tr;return e<0?o.delete(t.pos-s.nodeSize,t.pos):o.delete(t.pos,t.pos+s.nodeSize),r.dispatch(o),!0}return!1}function di(r,e,t){r.domObserver.stop(),e.contentEditable=t,r.domObserver.start()}function ia(r){if(!q||r.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:t}=r.domSelectionRange();if(e&&e.nodeType==1&&t==0&&e.firstChild&&e.firstChild.contentEditable=="false"){let n=e.firstChild;di(r,n,"true"),setTimeout(()=>di(r,n,"false"),20)}return!1}function sa(r){let e="";return r.ctrlKey&&(e+="c"),r.metaKey&&(e+="m"),r.altKey&&(e+="a"),r.shiftKey&&(e+="s"),e}function oa(r,e){let t=e.keyCode,n=sa(e);if(t==8||X&&t==72&&n=="c")return fi(r,-1)||qe(r,-1);if(t==46&&!e.shiftKey||X&&t==68&&n=="c")return fi(r,1)||qe(r,1);if(t==13||t==27)return!0;if(t==37||X&&t==66&&n=="c"){let i=t==37?ai(r,r.state.selection.from)=="ltr"?-1:1:-1;return li(r,i,n)||qe(r,i)}else if(t==39||X&&t==70&&n=="c"){let i=t==39?ai(r,r.state.selection.from)=="ltr"?1:-1:1;return li(r,i,n)||qe(r,i)}else{if(t==38||X&&t==80&&n=="c")return ci(r,-1,n)||qe(r,-1);if(t==40||X&&t==78&&n=="c")return ia(r)||ci(r,1,n)||qe(r,1);if(n==(X?"m":"c")&&(t==66||t==73||t==89||t==90))return!0}return!1}function or(r,e){r.someProp("transformCopied",h=>{e=h(e,r)});let t=[],{content:n,openStart:i,openEnd:s}=e;for(;i>1&&s>1&&n.childCount==1&&n.firstChild.childCount==1;){i--,s--;let h=n.firstChild;t.push(h.type.name,h.attrs!=h.type.defaultAttrs?h.attrs:null),n=h.content}let o=r.someProp("clipboardSerializer")||We.fromSchema(r.state.schema),l=As(),a=l.createElement("div");a.appendChild(o.serializeFragment(n,{document:l}));let c=a.firstChild,f,d=0;for(;c&&c.nodeType==1&&(f=Ds[c.nodeName.toLowerCase()]);){for(let h=f.length-1;h>=0;h--){let p=l.createElement(f[h]);for(;a.firstChild;)p.appendChild(a.firstChild);a.appendChild(p),d++}c=a.firstChild}c&&c.nodeType==1&&c.setAttribute("data-pm-slice",`${i} ${s}${d?` -${d}`:""} ${JSON.stringify(t)}`);let u=r.someProp("clipboardTextSerializer",h=>h(e,r))||e.content.textBetween(0,e.content.size,`

`);return{dom:a,text:u,slice:e}}function Os(r,e,t,n,i){let s=i.parent.type.spec.code,o,l;if(!t&&!e)return null;let a=e&&(n||s||!t);if(a){if(r.someProp("transformPastedText",u=>{e=u(e,s||n,r)}),s)return e?new k(b.from(r.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):k.empty;let d=r.someProp("clipboardTextParser",u=>u(e,i,n,r));if(d)l=d;else{let u=i.marks(),{schema:h}=r.state,p=We.fromSchema(h);o=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=o.appendChild(document.createElement("p"));m&&g.appendChild(p.serializeNode(h.text(m,u)))})}}else r.someProp("transformPastedHTML",d=>{t=d(t,r)}),o=fa(t),Ct&&da(o);let c=o&&o.querySelector("[data-pm-slice]"),f=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(f&&f[3])for(let d=+f[3];d>0;d--){let u=o.firstChild;for(;u&&u.nodeType!=1;)u=u.nextSibling;if(!u)break;o=u}if(l||(l=(r.someProp("clipboardParser")||r.someProp("domParser")||Ce.fromSchema(r.state.schema)).parseSlice(o,{preserveWhitespace:!!(a||f),context:i,ruleFromNode(u){return u.nodeName=="BR"&&!u.nextSibling&&u.parentNode&&!la.test(u.parentNode.nodeName)?{ignore:!0}:null}})),f)l=ua(ui(l,+f[1],+f[2]),f[4]);else if(l=k.maxOpen(aa(l.content,i),!0),l.openStart||l.openEnd){let d=0,u=0;for(let h=l.content.firstChild;d<l.openStart&&!h.type.spec.isolating;d++,h=h.firstChild);for(let h=l.content.lastChild;u<l.openEnd&&!h.type.spec.isolating;u++,h=h.lastChild);l=ui(l,d,u)}return r.someProp("transformPasted",d=>{l=d(l,r)}),l}const la=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function aa(r,e){if(r.childCount<2)return r;for(let t=e.depth;t>=0;t--){let i=e.node(t).contentMatchAt(e.index(t)),s,o=[];if(r.forEach(l=>{if(!o)return;let a=i.findWrapping(l.type),c;if(!a)return o=null;if(c=o.length&&s.length&&Ts(a,s,l,o[o.length-1],0))o[o.length-1]=c;else{o.length&&(o[o.length-1]=Es(o[o.length-1],s.length));let f=Ns(l,a);o.push(f),i=i.matchType(f.type),s=a}}),o)return b.from(o)}return r}function Ns(r,e,t=0){for(let n=e.length-1;n>=t;n--)r=e[n].create(null,b.from(r));return r}function Ts(r,e,t,n,i){if(i<r.length&&i<e.length&&r[i]==e[i]){let s=Ts(r,e,t,n.lastChild,i+1);if(s)return n.copy(n.content.replaceChild(n.childCount-1,s));if(n.contentMatchAt(n.childCount).matchType(i==r.length-1?t.type:r[i+1]))return n.copy(n.content.append(b.from(Ns(t,r,i+1))))}}function Es(r,e){if(e==0)return r;let t=r.content.replaceChild(r.childCount-1,Es(r.lastChild,e-1)),n=r.contentMatchAt(r.childCount).fillBefore(b.empty,!0);return r.copy(t.append(n))}function Un(r,e,t,n,i,s){let o=e<0?r.firstChild:r.lastChild,l=o.content;return r.childCount>1&&(s=0),i<n-1&&(l=Un(l,e,t,n,i+1,s)),i>=t&&(l=e<0?o.contentMatchAt(0).fillBefore(l,s<=i).append(l):l.append(o.contentMatchAt(o.childCount).fillBefore(b.empty,!0))),r.replaceChild(e<0?0:r.childCount-1,o.copy(l))}function ui(r,e,t){return e<r.openStart&&(r=new k(Un(r.content,-1,e,r.openStart,0,r.openEnd),e,r.openEnd)),t<r.openEnd&&(r=new k(Un(r.content,1,t,r.openEnd,0,0),r.openStart,t)),r}const Ds={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let hi=null;function As(){return hi||(hi=document.implementation.createHTMLDocument("title"))}let Nn=null;function ca(r){let e=window.trustedTypes;return e?(Nn||(Nn=e.defaultPolicy||e.createPolicy("ProseMirrorClipboard",{createHTML:t=>t})),Nn.createHTML(r)):r}function fa(r){let e=/^(\s*<meta [^>]*>)*/.exec(r);e&&(r=r.slice(e[0].length));let t=As().createElement("div"),n=/<([a-z][^>\s]+)/i.exec(r),i;if((i=n&&Ds[n[1].toLowerCase()])&&(r=i.map(s=>"<"+s+">").join("")+r+i.map(s=>"</"+s+">").reverse().join("")),t.innerHTML=ca(r),i)for(let s=0;s<i.length;s++)t=t.querySelector(i[s])||t;return t}function da(r){let e=r.querySelectorAll(W?"span:not([class]):not([style])":"span.Apple-converted-space");for(let t=0;t<e.length;t++){let n=e[t];n.childNodes.length==1&&n.textContent==" "&&n.parentNode&&n.parentNode.replaceChild(r.ownerDocument.createTextNode(" "),n)}}function ua(r,e){if(!r.size)return r;let t=r.content.firstChild.type.schema,n;try{n=JSON.parse(e)}catch{return r}let{content:i,openStart:s,openEnd:o}=r;for(let l=n.length-2;l>=0;l-=2){let a=t.nodes[n[l]];if(!a||a.hasRequiredAttrs())break;i=b.from(a.create(n[l+1],i)),s++,o++}return new k(i,s,o)}const K={},H={},ha={touchstart:!0,touchmove:!0};class pa{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function ma(r){for(let e in K){let t=K[e];r.dom.addEventListener(e,r.input.eventHandlers[e]=n=>{ya(r,n)&&!lr(r,n)&&(r.editable||!(n.type in H))&&t(r,n)},ha[e]?{passive:!0}:void 0)}q&&r.dom.addEventListener("input",()=>null),Gn(r)}function xe(r,e){r.input.lastSelectionOrigin=e,r.input.lastSelectionTime=Date.now()}function ga(r){r.domObserver.stop();for(let e in r.input.eventHandlers)r.dom.removeEventListener(e,r.input.eventHandlers[e]);clearTimeout(r.input.composingTimeout),clearTimeout(r.input.lastIOSEnterFallbackTimeout)}function Gn(r){r.someProp("handleDOMEvents",e=>{for(let t in e)r.input.eventHandlers[t]||r.dom.addEventListener(t,r.input.eventHandlers[t]=n=>lr(r,n))})}function lr(r,e){return r.someProp("handleDOMEvents",t=>{let n=t[e.type];return n?n(r,e)||e.defaultPrevented:!1})}function ya(r,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=r.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function ba(r,e){!lr(r,e)&&K[e.type]&&(r.editable||!(e.type in H))&&K[e.type](r,e)}H.keydown=(r,e)=>{let t=e;if(r.input.shiftKey=t.keyCode==16||t.shiftKey,!Rs(r,t)&&(r.input.lastKeyCode=t.keyCode,r.input.lastKeyCodeTime=Date.now(),!(ce&&W&&t.keyCode==13)))if(t.keyCode!=229&&r.domObserver.forceFlush(),Qe&&t.keyCode==13&&!t.ctrlKey&&!t.altKey&&!t.metaKey){let n=Date.now();r.input.lastIOSEnter=n,r.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{r.input.lastIOSEnter==n&&(r.someProp("handleKeyDown",i=>i(r,Ae(13,"Enter"))),r.input.lastIOSEnter=0)},200)}else r.someProp("handleKeyDown",n=>n(r,t))||oa(r,t)?t.preventDefault():xe(r,"key")};H.keyup=(r,e)=>{e.keyCode==16&&(r.input.shiftKey=!1)};H.keypress=(r,e)=>{let t=e;if(Rs(r,t)||!t.charCode||t.ctrlKey&&!t.altKey||X&&t.metaKey)return;if(r.someProp("handleKeyPress",i=>i(r,t))){t.preventDefault();return}let n=r.state.selection;if(!(n instanceof w)||!n.$from.sameParent(n.$to)){let i=String.fromCharCode(t.charCode);!/[\r\n]/.test(i)&&!r.someProp("handleTextInput",s=>s(r,n.$from.pos,n.$to.pos,i))&&r.dispatch(r.state.tr.insertText(i).scrollIntoView()),t.preventDefault()}};function cn(r){return{left:r.clientX,top:r.clientY}}function ka(r,e){let t=e.x-r.clientX,n=e.y-r.clientY;return t*t+n*n<100}function ar(r,e,t,n,i){if(n==-1)return!1;let s=r.state.doc.resolve(n);for(let o=s.depth+1;o>0;o--)if(r.someProp(e,l=>o>s.depth?l(r,t,s.nodeAfter,s.before(o),i,!0):l(r,t,s.node(o),s.before(o),i,!1)))return!0;return!1}function Ye(r,e,t){if(r.focused||r.focus(),r.state.selection.eq(e))return;let n=r.state.tr.setSelection(e);n.setMeta("pointer",!0),r.dispatch(n)}function xa(r,e){if(e==-1)return!1;let t=r.state.doc.resolve(e),n=t.nodeAfter;return n&&n.isAtom&&S.isSelectable(n)?(Ye(r,new S(t)),!0):!1}function Sa(r,e){if(e==-1)return!1;let t=r.state.selection,n,i;t instanceof S&&(n=t.node);let s=r.state.doc.resolve(e);for(let o=s.depth+1;o>0;o--){let l=o>s.depth?s.nodeAfter:s.node(o);if(S.isSelectable(l)){n&&t.$from.depth>0&&o>=t.$from.depth&&s.before(t.$from.depth+1)==t.$from.pos?i=s.before(t.$from.depth):i=s.before(o);break}}return i!=null?(Ye(r,S.create(r.state.doc,i)),!0):!1}function Ma(r,e,t,n,i){return ar(r,"handleClickOn",e,t,n)||r.someProp("handleClick",s=>s(r,e,n))||(i?Sa(r,t):xa(r,t))}function Ca(r,e,t,n){return ar(r,"handleDoubleClickOn",e,t,n)||r.someProp("handleDoubleClick",i=>i(r,e,n))}function wa(r,e,t,n){return ar(r,"handleTripleClickOn",e,t,n)||r.someProp("handleTripleClick",i=>i(r,e,n))||Oa(r,t,n)}function Oa(r,e,t){if(t.button!=0)return!1;let n=r.state.doc;if(e==-1)return n.inlineContent?(Ye(r,w.create(n,0,n.content.size)),!0):!1;let i=n.resolve(e);for(let s=i.depth+1;s>0;s--){let o=s>i.depth?i.nodeAfter:i.node(s),l=i.before(s);if(o.inlineContent)Ye(r,w.create(n,l+1,l+1+o.content.size));else if(S.isSelectable(o))Ye(r,S.create(n,l));else continue;return!0}}function cr(r){return Gt(r)}const Is=X?"metaKey":"ctrlKey";K.mousedown=(r,e)=>{let t=e;r.input.shiftKey=t.shiftKey;let n=cr(r),i=Date.now(),s="singleClick";i-r.input.lastClick.time<500&&ka(t,r.input.lastClick)&&!t[Is]&&r.input.lastClick.button==t.button&&(r.input.lastClick.type=="singleClick"?s="doubleClick":r.input.lastClick.type=="doubleClick"&&(s="tripleClick")),r.input.lastClick={time:i,x:t.clientX,y:t.clientY,type:s,button:t.button};let o=r.posAtCoords(cn(t));o&&(s=="singleClick"?(r.input.mouseDown&&r.input.mouseDown.done(),r.input.mouseDown=new Na(r,o,t,!!n)):(s=="doubleClick"?Ca:wa)(r,o.pos,o.inside,t)?t.preventDefault():xe(r,"pointer"))};class Na{constructor(e,t,n,i){this.view=e,this.pos=t,this.event=n,this.flushed=i,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[Is],this.allowDefault=n.shiftKey;let s,o;if(t.inside>-1)s=e.state.doc.nodeAt(t.inside),o=t.inside;else{let f=e.state.doc.resolve(t.pos);s=f.parent,o=f.depth?f.before():0}const l=i?null:n.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a&&a.dom.nodeType==1?a.dom:null;let{selection:c}=e.state;(n.button==0&&s.type.spec.draggable&&s.type.spec.selectable!==!1||c instanceof S&&c.from<=o&&c.to>o)&&(this.mightDrag={node:s,pos:o,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&ne&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),xe(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>de(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(cn(e))),this.updateAllowDefault(e),this.allowDefault||!t?xe(this.view,"pointer"):Ma(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||q&&this.mightDrag&&!this.mightDrag.node.isAtom||W&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(Ye(this.view,N.near(this.view.state.doc.resolve(t.pos))),e.preventDefault()):xe(this.view,"pointer")}move(e){this.updateAllowDefault(e),xe(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}K.touchstart=r=>{r.input.lastTouch=Date.now(),cr(r),xe(r,"pointer")};K.touchmove=r=>{r.input.lastTouch=Date.now(),xe(r,"pointer")};K.contextmenu=r=>cr(r);function Rs(r,e){return r.composing?!0:q&&Math.abs(e.timeStamp-r.input.compositionEndedAt)<500?(r.input.compositionEndedAt=-2e8,!0):!1}const Ta=ce?5e3:-1;H.compositionstart=H.compositionupdate=r=>{if(!r.composing){r.domObserver.flush();let{state:e}=r,t=e.selection.$to;if(e.selection instanceof w&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(n=>n.type.spec.inclusive===!1)))r.markCursor=r.state.storedMarks||t.marks(),Gt(r,!0),r.markCursor=null;else if(Gt(r,!e.selection.empty),ne&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let n=r.domSelectionRange();for(let i=n.focusNode,s=n.focusOffset;i&&i.nodeType==1&&s!=0;){let o=s<0?i.lastChild:i.childNodes[s-1];if(!o)break;if(o.nodeType==3){let l=r.domSelection();l&&l.collapse(o,o.nodeValue.length);break}else i=o,s=-1}}r.input.composing=!0}vs(r,Ta)};H.compositionend=(r,e)=>{r.composing&&(r.input.composing=!1,r.input.compositionEndedAt=e.timeStamp,r.input.compositionPendingChanges=r.domObserver.pendingRecords().length?r.input.compositionID:0,r.input.compositionNode=null,r.input.compositionPendingChanges&&Promise.resolve().then(()=>r.domObserver.flush()),r.input.compositionID++,vs(r,20))};function vs(r,e){clearTimeout(r.input.composingTimeout),e>-1&&(r.input.composingTimeout=setTimeout(()=>Gt(r),e))}function Ps(r){for(r.composing&&(r.input.composing=!1,r.input.compositionEndedAt=Da());r.input.compositionNodes.length>0;)r.input.compositionNodes.pop().markParentsDirty()}function Ea(r){let e=r.domSelectionRange();if(!e.focusNode)return null;let t=xl(e.focusNode,e.focusOffset),n=Sl(e.focusNode,e.focusOffset);if(t&&n&&t!=n){let i=n.pmViewDesc,s=r.domObserver.lastChangedTextNode;if(t==s||n==s)return s;if(!i||!i.isText(n.nodeValue))return n;if(r.input.compositionNode==n){let o=t.pmViewDesc;if(!(!o||!o.isText(t.nodeValue)))return n}}return t||n}function Da(){let r=document.createEvent("Event");return r.initEvent("event",!0,!0),r.timeStamp}function Gt(r,e=!1){if(!(ce&&r.domObserver.flushingSoon>=0)){if(r.domObserver.forceFlush(),Ps(r),e||r.docView&&r.docView.dirty){let t=ir(r),n=r.state.selection;return t&&!t.eq(n)?r.dispatch(r.state.tr.setSelection(t)):(r.markCursor||e)&&!n.$from.node(n.$from.sharedDepth(n.to)).inlineContent?r.dispatch(r.state.tr.deleteSelection()):r.updateState(r.state),!0}return!1}}function Aa(r,e){if(!r.dom.parentNode)return;let t=r.dom.parentNode.appendChild(document.createElement("div"));t.appendChild(e),t.style.cssText="position: fixed; left: -10000px; top: 10px";let n=getSelection(),i=document.createRange();i.selectNodeContents(e),r.dom.blur(),n.removeAllRanges(),n.addRange(i),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t),r.focus()},50)}const gt=G&&we<15||Qe&&Nl<604;K.copy=H.cut=(r,e)=>{let t=e,n=r.state.selection,i=t.type=="cut";if(n.empty)return;let s=gt?null:t.clipboardData,o=n.content(),{dom:l,text:a}=or(r,o);s?(t.preventDefault(),s.clearData(),s.setData("text/html",l.innerHTML),s.setData("text/plain",a)):Aa(r,l),i&&r.dispatch(r.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))};function Ia(r){return r.openStart==0&&r.openEnd==0&&r.content.childCount==1?r.content.firstChild:null}function Ra(r,e){if(!r.dom.parentNode)return;let t=r.input.shiftKey||r.state.selection.$from.parent.type.spec.code,n=r.dom.parentNode.appendChild(document.createElement(t?"textarea":"div"));t||(n.contentEditable="true"),n.style.cssText="position: fixed; left: -10000px; top: 10px",n.focus();let i=r.input.shiftKey&&r.input.lastKeyCode!=45;setTimeout(()=>{r.focus(),n.parentNode&&n.parentNode.removeChild(n),t?yt(r,n.value,null,i,e):yt(r,n.textContent,n.innerHTML,i,e)},50)}function yt(r,e,t,n,i){let s=Os(r,e,t,n,r.state.selection.$from);if(r.someProp("handlePaste",a=>a(r,i,s||k.empty)))return!0;if(!s)return!1;let o=Ia(s),l=o?r.state.tr.replaceSelectionWith(o,n):r.state.tr.replaceSelection(s);return r.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Bs(r){let e=r.getData("text/plain")||r.getData("Text");if(e)return e;let t=r.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}H.paste=(r,e)=>{let t=e;if(r.composing&&!ce)return;let n=gt?null:t.clipboardData,i=r.input.shiftKey&&r.input.lastKeyCode!=45;n&&yt(r,Bs(n),n.getData("text/html"),i,t)?t.preventDefault():Ra(r,t)};class zs{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}const va=X?"altKey":"ctrlKey";function Fs(r,e){let t=r.someProp("dragCopies",n=>!n(e));return t??!e[va]}K.dragstart=(r,e)=>{let t=e,n=r.input.mouseDown;if(n&&n.done(),!t.dataTransfer)return;let i=r.state.selection,s=i.empty?null:r.posAtCoords(cn(t)),o;if(!(s&&s.pos>=i.from&&s.pos<=(i instanceof S?i.to-1:i.to))){if(n&&n.mightDrag)o=S.create(r.state.doc,n.mightDrag.pos);else if(t.target&&t.target.nodeType==1){let d=r.docView.nearestDesc(t.target,!0);d&&d.node.type.spec.draggable&&d!=r.docView&&(o=S.create(r.state.doc,d.posBefore))}}let l=(o||r.state.selection).content(),{dom:a,text:c,slice:f}=or(r,l);(!t.dataTransfer.files.length||!W||cs>120)&&t.dataTransfer.clearData(),t.dataTransfer.setData(gt?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",gt||t.dataTransfer.setData("text/plain",c),r.dragging=new zs(f,Fs(r,t),o)};K.dragend=r=>{let e=r.dragging;window.setTimeout(()=>{r.dragging==e&&(r.dragging=null)},50)};H.dragover=H.dragenter=(r,e)=>e.preventDefault();H.drop=(r,e)=>{let t=e,n=r.dragging;if(r.dragging=null,!t.dataTransfer)return;let i=r.posAtCoords(cn(t));if(!i)return;let s=r.state.doc.resolve(i.pos),o=n&&n.slice;o?r.someProp("transformPasted",p=>{o=p(o,r)}):o=Os(r,Bs(t.dataTransfer),gt?null:t.dataTransfer.getData("text/html"),!1,s);let l=!!(n&&Fs(r,t));if(r.someProp("handleDrop",p=>p(r,t,o||k.empty,l))){t.preventDefault();return}if(!o)return;t.preventDefault();let a=o?ol(r.state.doc,s.pos,o):s.pos;a==null&&(a=s.pos);let c=r.state.tr;if(l){let{node:p}=n;p?p.replace(c):c.deleteSelection()}let f=c.mapping.map(a),d=o.openStart==0&&o.openEnd==0&&o.content.childCount==1,u=c.doc;if(d?c.replaceRangeWith(f,f,o.content.firstChild):c.replaceRange(f,f,o),c.doc.eq(u))return;let h=c.doc.resolve(f);if(d&&S.isSelectable(o.content.firstChild)&&h.nodeAfter&&h.nodeAfter.sameMarkup(o.content.firstChild))c.setSelection(new S(h));else{let p=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,y,M)=>p=M),c.setSelection(sr(r,h,c.doc.resolve(p)))}r.focus(),r.dispatch(c.setMeta("uiEvent","drop"))};K.focus=r=>{r.input.lastFocus=Date.now(),r.focused||(r.domObserver.stop(),r.dom.classList.add("ProseMirror-focused"),r.domObserver.start(),r.focused=!0,setTimeout(()=>{r.docView&&r.hasFocus()&&!r.domObserver.currentSelection.eq(r.domSelectionRange())&&de(r)},20))};K.blur=(r,e)=>{let t=e;r.focused&&(r.domObserver.stop(),r.dom.classList.remove("ProseMirror-focused"),r.domObserver.start(),t.relatedTarget&&r.dom.contains(t.relatedTarget)&&r.domObserver.currentSelection.clear(),r.focused=!1)};K.beforeinput=(r,e)=>{if(W&&ce&&e.inputType=="deleteContentBackward"){r.domObserver.flushSoon();let{domChangeCount:n}=r.input;setTimeout(()=>{if(r.input.domChangeCount!=n||(r.dom.blur(),r.focus(),r.someProp("handleKeyDown",s=>s(r,Ae(8,"Backspace")))))return;let{$cursor:i}=r.state.selection;i&&i.pos>0&&r.dispatch(r.state.tr.delete(i.pos-1,i.pos).scrollIntoView())},50)}};for(let r in H)K[r]=H[r];function bt(r,e){if(r==e)return!0;for(let t in r)if(r[t]!==e[t])return!1;for(let t in e)if(!(t in r))return!1;return!0}class _t{constructor(e,t){this.toDOM=e,this.spec=t||Be,this.side=this.spec.side||0}map(e,t,n,i){let{pos:s,deleted:o}=e.mapResult(t.from+i,this.side<0?-1:1);return o?null:new te(s-n,s-n,this)}valid(){return!0}eq(e){return this==e||e instanceof _t&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&bt(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class Ne{constructor(e,t){this.attrs=e,this.spec=t||Be}map(e,t,n,i){let s=e.map(t.from+i,this.spec.inclusiveStart?-1:1)-n,o=e.map(t.to+i,this.spec.inclusiveEnd?1:-1)-n;return s>=o?null:new te(s,o,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof Ne&&bt(this.attrs,e.attrs)&&bt(this.spec,e.spec)}static is(e){return e.type instanceof Ne}destroy(){}}class fr{constructor(e,t){this.attrs=e,this.spec=t||Be}map(e,t,n,i){let s=e.mapResult(t.from+i,1);if(s.deleted)return null;let o=e.mapResult(t.to+i,-1);return o.deleted||o.pos<=s.pos?null:new te(s.pos-n,o.pos-n,this)}valid(e,t){let{index:n,offset:i}=e.content.findIndex(t.from),s;return i==t.from&&!(s=e.child(n)).isText&&i+s.nodeSize==t.to}eq(e){return this==e||e instanceof fr&&bt(this.attrs,e.attrs)&&bt(this.spec,e.spec)}destroy(){}}class te{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new te(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new te(e,e,new _t(t,n))}static inline(e,t,n,i){return new te(e,t,new Ne(n,i))}static node(e,t,n,i){return new te(e,t,new fr(n,i))}get spec(){return this.type.spec}get inline(){return this.type instanceof Ne}get widget(){return this.type instanceof _t}}const He=[],Be={};class P{constructor(e,t){this.local=e.length?e:He,this.children=t.length?t:He}static create(e,t){return t.length?Yt(t,e,0,Be):L}find(e,t,n){let i=[];return this.findInner(e??0,t??1e9,i,0,n),i}findInner(e,t,n,i,s){for(let o=0;o<this.local.length;o++){let l=this.local[o];l.from<=t&&l.to>=e&&(!s||s(l.spec))&&n.push(l.copy(l.from+i,l.to+i))}for(let o=0;o<this.children.length;o+=3)if(this.children[o]<t&&this.children[o+1]>e){let l=this.children[o]+1;this.children[o+2].findInner(e-l,t-l,n,i+l,s)}}map(e,t,n){return this==L||e.maps.length==0?this:this.mapInner(e,t,0,0,n||Be)}mapInner(e,t,n,i,s){let o;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,n,i);a&&a.type.valid(t,a)?(o||(o=[])).push(a):s.onRemove&&s.onRemove(this.local[l].spec)}return this.children.length?Pa(this.children,o||[],e,t,n,i,s):o?new P(o.sort(ze),He):L}add(e,t){return t.length?this==L?P.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let i,s=0;e.forEach((l,a)=>{let c=a+n,f;if(f=$s(t,l,c)){for(i||(i=this.children.slice());s<i.length&&i[s]<a;)s+=3;i[s]==a?i[s+2]=i[s+2].addInner(l,f,c+1):i.splice(s,0,a,a+l.nodeSize,Yt(f,l,c+1,Be)),s+=3}});let o=Vs(s?Ls(t):t,-n);for(let l=0;l<o.length;l++)o[l].type.valid(e,o[l])||o.splice(l--,1);return new P(o.length?this.local.concat(o).sort(ze):this.local,i||this.children)}remove(e){return e.length==0||this==L?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,i=this.local;for(let s=0;s<n.length;s+=3){let o,l=n[s]+t,a=n[s+1]+t;for(let f=0,d;f<e.length;f++)(d=e[f])&&d.from>l&&d.to<a&&(e[f]=null,(o||(o=[])).push(d));if(!o)continue;n==this.children&&(n=this.children.slice());let c=n[s+2].removeInner(o,l+1);c!=L?n[s+2]=c:(n.splice(s,3),s-=3)}if(i.length){for(let s=0,o;s<e.length;s++)if(o=e[s])for(let l=0;l<i.length;l++)i[l].eq(o,t)&&(i==this.local&&(i=this.local.slice()),i.splice(l--,1))}return n==this.children&&i==this.local?this:i.length||n.length?new P(i,n):L}forChild(e,t){if(this==L)return this;if(t.isLeaf)return P.empty;let n,i;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(n=this.children[l+2]);break}let s=e+1,o=s+t.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<o&&a.to>s&&a.type instanceof Ne){let c=Math.max(s,a.from)-s,f=Math.min(o,a.to)-s;c<f&&(i||(i=[])).push(a.copy(c,f))}}if(i){let l=new P(i.sort(ze),He);return n?new ge([l,n]):l}return n||L}eq(e){if(this==e)return!0;if(!(e instanceof P)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return dr(this.localsInner(e))}localsInner(e){if(this==L)return He;if(e.inlineContent||!this.local.some(Ne.is))return this.local;let t=[];for(let n=0;n<this.local.length;n++)this.local[n].type instanceof Ne||t.push(this.local[n]);return t}forEachSet(e){e(this)}}P.empty=new P([],[]);P.removeOverlap=dr;const L=P.empty;class ge{constructor(e){this.members=e}map(e,t){const n=this.members.map(i=>i.map(e,t,Be));return ge.from(n)}forChild(e,t){if(t.isLeaf)return P.empty;let n=[];for(let i=0;i<this.members.length;i++){let s=this.members[i].forChild(e,t);s!=L&&(s instanceof ge?n=n.concat(s.members):n.push(s))}return ge.from(n)}eq(e){if(!(e instanceof ge)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let i=0;i<this.members.length;i++){let s=this.members[i].localsInner(e);if(s.length)if(!t)t=s;else{n&&(t=t.slice(),n=!1);for(let o=0;o<s.length;o++)t.push(s[o])}}return t?dr(n?t:t.sort(ze)):He}static from(e){switch(e.length){case 0:return L;case 1:return e[0];default:return new ge(e.every(t=>t instanceof P)?e:e.reduce((t,n)=>t.concat(n instanceof P?n:n.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function Pa(r,e,t,n,i,s,o){let l=r.slice();for(let c=0,f=s;c<t.maps.length;c++){let d=0;t.maps[c].forEach((u,h,p,m)=>{let g=m-p-(h-u);for(let y=0;y<l.length;y+=3){let M=l[y+1];if(M<0||u>M+f-d)continue;let O=l[y]+f-d;h>=O?l[y+1]=u<=O?-2:-1:u>=f&&g&&(l[y]+=g,l[y+1]+=g)}d+=g}),f=t.maps[c].map(f,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(l[c+1]==-2){a=!0,l[c+1]=-1;continue}let f=t.map(r[c]+s),d=f-i;if(d<0||d>=n.content.size){a=!0;continue}let u=t.map(r[c+1]+s,-1),h=u-i,{index:p,offset:m}=n.content.findIndex(d),g=n.maybeChild(p);if(g&&m==d&&m+g.nodeSize==h){let y=l[c+2].mapInner(t,g,f+1,r[c]+s+1,o);y!=L?(l[c]=d,l[c+1]=h,l[c+2]=y):(l[c+1]=-2,a=!0)}else a=!0}if(a){let c=Ba(l,r,e,t,i,s,o),f=Yt(c,n,0,o);e=f.local;for(let d=0;d<l.length;d+=3)l[d+1]<0&&(l.splice(d,3),d-=3);for(let d=0,u=0;d<f.children.length;d+=3){let h=f.children[d];for(;u<l.length&&l[u]<h;)u+=3;l.splice(u,0,f.children[d],f.children[d+1],f.children[d+2])}}return new P(e.sort(ze),l)}function Vs(r,e){if(!e||!r.length)return r;let t=[];for(let n=0;n<r.length;n++){let i=r[n];t.push(new te(i.from+e,i.to+e,i.type))}return t}function Ba(r,e,t,n,i,s,o){function l(a,c){for(let f=0;f<a.local.length;f++){let d=a.local[f].map(n,i,c);d?t.push(d):o.onRemove&&o.onRemove(a.local[f].spec)}for(let f=0;f<a.children.length;f+=3)l(a.children[f+2],a.children[f]+c+1)}for(let a=0;a<r.length;a+=3)r[a+1]==-1&&l(r[a+2],e[a]+s+1);return t}function $s(r,e,t){if(e.isLeaf)return null;let n=t+e.nodeSize,i=null;for(let s=0,o;s<r.length;s++)(o=r[s])&&o.from>t&&o.to<n&&((i||(i=[])).push(o),r[s]=null);return i}function Ls(r){let e=[];for(let t=0;t<r.length;t++)r[t]!=null&&e.push(r[t]);return e}function Yt(r,e,t,n){let i=[],s=!1;e.forEach((l,a)=>{let c=$s(r,l,a+t);if(c){s=!0;let f=Yt(c,l,t+a+1,n);f!=L&&i.push(a,a+l.nodeSize,f)}});let o=Vs(s?Ls(r):r,-t).sort(ze);for(let l=0;l<o.length;l++)o[l].type.valid(e,o[l])||(n.onRemove&&n.onRemove(o[l].spec),o.splice(l--,1));return o.length||i.length?new P(o,i):L}function ze(r,e){return r.from-e.from||r.to-e.to}function dr(r){let e=r;for(let t=0;t<e.length-1;t++){let n=e[t];if(n.from!=n.to)for(let i=t+1;i<e.length;i++){let s=e[i];if(s.from==n.from){s.to!=n.to&&(e==r&&(e=r.slice()),e[i]=s.copy(s.from,n.to),pi(e,i+1,s.copy(n.to,s.to)));continue}else{s.from<n.to&&(e==r&&(e=r.slice()),e[t]=n.copy(n.from,s.from),pi(e,i,n.copy(s.from,n.to)));break}}}return e}function pi(r,e,t){for(;e<r.length&&ze(t,r[e])>0;)e++;r.splice(e,0,t)}function Tn(r){let e=[];return r.someProp("decorations",t=>{let n=t(r.state);n&&n!=L&&e.push(n)}),r.cursorWrapper&&e.push(P.create(r.state.doc,[r.cursorWrapper.deco])),ge.from(e)}const za={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},Fa=G&&we<=11;class Va{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class $a{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new Va,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(n=>{for(let i=0;i<n.length;i++)this.queue.push(n[i]);G&&we<=11&&n.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),Fa&&(this.onCharData=n=>{this.queue.push({target:n.target,type:"characterData",oldValue:n.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,za)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(oi(this.view)){if(this.suppressingSelectionUpdates)return de(this.view);if(G&&we<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&$e(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,n;for(let s=e.focusNode;s;s=Ze(s))t.add(s);for(let s=e.anchorNode;s;s=Ze(s))if(t.has(s)){n=s;break}let i=n&&this.view.docView.nearestDesc(n);if(i&&i.ignoreMutation({type:"selection",target:n.nodeType==3?n.parentNode:n}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let n=e.domSelectionRange(),i=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&oi(e)&&!this.ignoreSelectionChange(n),s=-1,o=-1,l=!1,a=[];if(e.editable)for(let f=0;f<t.length;f++){let d=this.registerMutation(t[f],a);d&&(s=s<0?d.from:Math.min(d.from,s),o=o<0?d.to:Math.max(d.to,o),d.typeOver&&(l=!0))}if(ne&&a.length){let f=a.filter(d=>d.nodeName=="BR");if(f.length==2){let[d,u]=f;d.parentNode&&d.parentNode.parentNode==u.parentNode?u.remove():d.remove()}else{let{focusNode:d}=this.currentSelection;for(let u of f){let h=u.parentNode;h&&h.nodeName=="LI"&&(!d||ja(e,d)!=h)&&u.remove()}}}let c=null;s<0&&i&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&ln(n)&&(c=ir(e))&&c.eq(N.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,de(e),this.currentSelection.set(n),e.scrollToSelection()):(s>-1||i)&&(s>-1&&(e.docView.markDirty(s,o),La(e)),this.handleDOMChange(s,o,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(n)||de(e),this.currentSelection.set(n))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(n==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!n||n.ignoreMutation(e))return null;if(e.type=="childList"){for(let f=0;f<e.addedNodes.length;f++){let d=e.addedNodes[f];t.push(d),d.nodeType==3&&(this.lastChangedTextNode=d)}if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let i=e.previousSibling,s=e.nextSibling;if(G&&we<=11&&e.addedNodes.length)for(let f=0;f<e.addedNodes.length;f++){let{previousSibling:d,nextSibling:u}=e.addedNodes[f];(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(i=d),(!u||Array.prototype.indexOf.call(e.addedNodes,u)<0)&&(s=u)}let o=i&&i.parentNode==e.target?V(i)+1:0,l=n.localPosFromDOM(e.target,o,-1),a=s&&s.parentNode==e.target?V(s):e.target.childNodes.length,c=n.localPosFromDOM(e.target,a,1);return{from:l,to:c}}else return e.type=="attributes"?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:(this.lastChangedTextNode=e.target,{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let mi=new WeakMap,gi=!1;function La(r){if(!mi.has(r)&&(mi.set(r,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(r.dom).whiteSpace)!==-1)){if(r.requiresGeckoHackNode=ne,gi)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),gi=!0}}function yi(r,e){let t=e.startContainer,n=e.startOffset,i=e.endContainer,s=e.endOffset,o=r.domAtPos(r.state.selection.anchor);return $e(o.node,o.offset,i,s)&&([t,n,i,s]=[i,s,t,n]),{anchorNode:t,anchorOffset:n,focusNode:i,focusOffset:s}}function Wa(r,e){if(e.getComposedRanges){let i=e.getComposedRanges(r.root)[0];if(i)return yi(r,i)}let t;function n(i){i.preventDefault(),i.stopImmediatePropagation(),t=i.getTargetRanges()[0]}return r.dom.addEventListener("beforeinput",n,!0),document.execCommand("indent"),r.dom.removeEventListener("beforeinput",n,!0),t?yi(r,t):null}function ja(r,e){for(let t=e.parentNode;t&&t!=r.dom;t=t.parentNode){let n=r.docView.nearestDesc(t,!0);if(n&&n.node.isBlock)return t}return null}function Ja(r,e,t){let{node:n,fromOffset:i,toOffset:s,from:o,to:l}=r.docView.parseRange(e,t),a=r.domSelectionRange(),c,f=a.anchorNode;if(f&&r.dom.contains(f.nodeType==1?f:f.parentNode)&&(c=[{node:f,offset:a.anchorOffset}],ln(a)||c.push({node:a.focusNode,offset:a.focusOffset})),W&&r.input.lastKeyCode===8)for(let g=s;g>i;g--){let y=n.childNodes[g-1],M=y.pmViewDesc;if(y.nodeName=="BR"&&!M){s=g;break}if(!M||M.size)break}let d=r.state.doc,u=r.someProp("domParser")||Ce.fromSchema(r.state.schema),h=d.resolve(o),p=null,m=u.parse(n,{topNode:h.parent,topMatch:h.parent.contentMatchAt(h.index()),topOpen:!0,from:i,to:s,preserveWhitespace:h.parent.type.whitespace=="pre"?"full":!0,findPositions:c,ruleFromNode:qa,context:h});if(c&&c[0].pos!=null){let g=c[0].pos,y=c[1]&&c[1].pos;y==null&&(y=g),p={anchor:g+o,head:y+o}}return{doc:m,sel:p,from:o,to:l}}function qa(r){let e=r.pmViewDesc;if(e)return e.parseRule();if(r.nodeName=="BR"&&r.parentNode){if(q&&/^(ul|ol)$/i.test(r.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}else if(r.parentNode.lastChild==r||q&&/^(tr|table)$/i.test(r.parentNode.nodeName))return{ignore:!0}}else if(r.nodeName=="IMG"&&r.getAttribute("mark-placeholder"))return{ignore:!0};return null}const Ka=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function Ha(r,e,t,n,i){let s=r.input.compositionPendingChanges||(r.composing?r.input.compositionID:0);if(r.input.compositionPendingChanges=0,e<0){let T=r.input.lastSelectionTime>Date.now()-50?r.input.lastSelectionOrigin:null,he=ir(r,T);if(he&&!r.state.selection.eq(he)){if(W&&ce&&r.input.lastKeyCode===13&&Date.now()-100<r.input.lastKeyCodeTime&&r.someProp("handleKeyDown",xo=>xo(r,Ae(13,"Enter"))))return;let It=r.state.tr.setSelection(he);T=="pointer"?It.setMeta("pointer",!0):T=="key"&&It.scrollIntoView(),s&&It.setMeta("composition",s),r.dispatch(It)}return}let o=r.state.doc.resolve(e),l=o.sharedDepth(t);e=o.before(l+1),t=r.state.doc.resolve(t).after(l+1);let a=r.state.selection,c=Ja(r,e,t),f=r.state.doc,d=f.slice(c.from,c.to),u,h;r.input.lastKeyCode===8&&Date.now()-100<r.input.lastKeyCodeTime?(u=r.state.selection.to,h="end"):(u=r.state.selection.from,h="start"),r.input.lastKeyCode=null;let p=_a(d.content,c.doc.content,c.from,u,h);if(p&&r.input.domChangeCount++,(Qe&&r.input.lastIOSEnter>Date.now()-225||ce)&&i.some(T=>T.nodeType==1&&!Ka.test(T.nodeName))&&(!p||p.endA>=p.endB)&&r.someProp("handleKeyDown",T=>T(r,Ae(13,"Enter")))){r.input.lastIOSEnter=0;return}if(!p)if(n&&a instanceof w&&!a.empty&&a.$head.sameParent(a.$anchor)&&!r.composing&&!(c.sel&&c.sel.anchor!=c.sel.head))p={start:a.from,endA:a.to,endB:a.to};else{if(c.sel){let T=bi(r,r.state.doc,c.sel);if(T&&!T.eq(r.state.selection)){let he=r.state.tr.setSelection(T);s&&he.setMeta("composition",s),r.dispatch(he)}}return}r.state.selection.from<r.state.selection.to&&p.start==p.endB&&r.state.selection instanceof w&&(p.start>r.state.selection.from&&p.start<=r.state.selection.from+2&&r.state.selection.from>=c.from?p.start=r.state.selection.from:p.endA<r.state.selection.to&&p.endA>=r.state.selection.to-2&&r.state.selection.to<=c.to&&(p.endB+=r.state.selection.to-p.endA,p.endA=r.state.selection.to)),G&&we<=11&&p.endB==p.start+1&&p.endA==p.start&&p.start>c.from&&c.doc.textBetween(p.start-c.from-1,p.start-c.from+1)=="  "&&(p.start--,p.endA--,p.endB--);let m=c.doc.resolveNoCache(p.start-c.from),g=c.doc.resolveNoCache(p.endB-c.from),y=f.resolve(p.start),M=m.sameParent(g)&&m.parent.inlineContent&&y.end()>=p.endA,O;if((Qe&&r.input.lastIOSEnter>Date.now()-225&&(!M||i.some(T=>T.nodeName=="DIV"||T.nodeName=="P"))||!M&&m.pos<c.doc.content.size&&(!m.sameParent(g)||!m.parent.inlineContent)&&!/\S/.test(c.doc.textBetween(m.pos,g.pos,"",""))&&(O=N.findFrom(c.doc.resolve(m.pos+1),1,!0))&&O.head>m.pos)&&r.someProp("handleKeyDown",T=>T(r,Ae(13,"Enter")))){r.input.lastIOSEnter=0;return}if(r.state.selection.anchor>p.start&&Ga(f,p.start,p.endA,m,g)&&r.someProp("handleKeyDown",T=>T(r,Ae(8,"Backspace")))){ce&&W&&r.domObserver.suppressSelectionUpdates();return}W&&p.endB==p.start&&(r.input.lastChromeDelete=Date.now()),ce&&!M&&m.start()!=g.start()&&g.parentOffset==0&&m.depth==g.depth&&c.sel&&c.sel.anchor==c.sel.head&&c.sel.head==p.endA&&(p.endB-=2,g=c.doc.resolveNoCache(p.endB-c.from),setTimeout(()=>{r.someProp("handleKeyDown",function(T){return T(r,Ae(13,"Enter"))})},20));let I=p.start,E=p.endA,D,U,J;if(M){if(m.pos==g.pos)G&&we<=11&&m.parentOffset==0&&(r.domObserver.suppressSelectionUpdates(),setTimeout(()=>de(r),20)),D=r.state.tr.delete(I,E),U=f.resolve(p.start).marksAcross(f.resolve(p.endA));else if(p.endA==p.endB&&(J=Ua(m.parent.content.cut(m.parentOffset,g.parentOffset),y.parent.content.cut(y.parentOffset,p.endA-y.start()))))D=r.state.tr,J.type=="add"?D.addMark(I,E,J.mark):D.removeMark(I,E,J.mark);else if(m.parent.child(m.index()).isText&&m.index()==g.index()-(g.textOffset?0:1)){let T=m.parent.textBetween(m.parentOffset,g.parentOffset);if(r.someProp("handleTextInput",he=>he(r,I,E,T)))return;D=r.state.tr.insertText(T,I,E)}}if(D||(D=r.state.tr.replace(I,E,c.doc.slice(p.start-c.from,p.endB-c.from))),c.sel){let T=bi(r,D.doc,c.sel);T&&!(W&&r.composing&&T.empty&&(p.start!=p.endB||r.input.lastChromeDelete<Date.now()-100)&&(T.head==I||T.head==D.mapping.map(E)-1)||G&&T.empty&&T.head==I)&&D.setSelection(T)}U&&D.ensureMarks(U),s&&D.setMeta("composition",s),r.dispatch(D.scrollIntoView())}function bi(r,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:sr(r,e.resolve(t.anchor),e.resolve(t.head))}function Ua(r,e){let t=r.firstChild.marks,n=e.firstChild.marks,i=t,s=n,o,l,a;for(let f=0;f<n.length;f++)i=n[f].removeFromSet(i);for(let f=0;f<t.length;f++)s=t[f].removeFromSet(s);if(i.length==1&&s.length==0)l=i[0],o="add",a=f=>f.mark(l.addToSet(f.marks));else if(i.length==0&&s.length==1)l=s[0],o="remove",a=f=>f.mark(l.removeFromSet(f.marks));else return null;let c=[];for(let f=0;f<e.childCount;f++)c.push(a(e.child(f)));if(b.from(c).eq(r))return{mark:l,type:o}}function Ga(r,e,t,n,i){if(t-e<=i.pos-n.pos||En(n,!0,!1)<i.pos)return!1;let s=r.resolve(e);if(!n.parent.isTextblock){let l=s.nodeAfter;return l!=null&&t==e+l.nodeSize}if(s.parentOffset<s.parent.content.size||!s.parent.isTextblock)return!1;let o=r.resolve(En(s,!0,!0));return!o.parent.isTextblock||o.pos>t||En(o,!0,!1)<t?!1:n.parent.content.cut(n.parentOffset).eq(o.parent.content)}function En(r,e,t){let n=r.depth,i=e?r.end():r.pos;for(;n>0&&(e||r.indexAfter(n)==r.node(n).childCount);)n--,i++,e=!1;if(t){let s=r.node(n).maybeChild(r.indexAfter(n));for(;s&&!s.isLeaf;)s=s.firstChild,i++}return i}function _a(r,e,t,n,i){let s=r.findDiffStart(e,t);if(s==null)return null;let{a:o,b:l}=r.findDiffEnd(e,t+r.size,t+e.size);if(i=="end"){let a=Math.max(0,s-Math.min(o,l));n-=o+a-s}if(o<s&&r.size<e.size){let a=n<=s&&n>=o?s-n:0;s-=a,s&&s<e.size&&ki(e.textBetween(s-1,s+1))&&(s+=a?1:-1),l=s+(l-o),o=s}else if(l<s){let a=n<=s&&n>=l?s-n:0;s-=a,s&&s<r.size&&ki(r.textBetween(s-1,s+1))&&(s+=a?1:-1),o=s+(o-l),l=s}return{start:s,endA:o,endB:l}}function ki(r){if(r.length!=2)return!1;let e=r.charCodeAt(0),t=r.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}class Ya{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new pa,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(wi),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=Mi(this),Si(this),this.nodeViews=Ci(this),this.docView=ei(this.state.doc,xi(this),Tn(this),this.dom,this),this.domObserver=new $a(this,(n,i,s,o)=>Ha(this,n,i,s,o)),this.domObserver.start(),ma(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&Gn(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(wi),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let n in this._props)t[n]=this._props[n];t.state=this.state;for(let n in e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n;let i=this.state,s=!1,o=!1;e.storedMarks&&this.composing&&(Ps(this),o=!0),this.state=e;let l=i.plugins!=e.plugins||this._props.plugins!=t.plugins;if(l||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let h=Ci(this);Za(h,this.nodeViews)&&(this.nodeViews=h,s=!0)}(l||t.handleDOMEvents!=this._props.handleDOMEvents)&&Gn(this),this.editable=Mi(this),Si(this);let a=Tn(this),c=xi(this),f=i.plugins!=e.plugins&&!i.doc.eq(e.doc)?"reset":e.scrollToSelection>i.scrollToSelection?"to selection":"preserve",d=s||!this.docView.matchesNode(e.doc,c,a);(d||!e.selection.eq(i.selection))&&(o=!0);let u=f=="preserve"&&o&&this.dom.style.overflowAnchor==null&&Dl(this);if(o){this.domObserver.stop();let h=d&&(G||W)&&!this.composing&&!i.selection.empty&&!e.selection.empty&&Xa(i.selection,e.selection);if(d){let p=W?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=Ea(this)),(s||!this.docView.update(e.doc,c,a,this))&&(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=ei(e.doc,c,a,this.dom,this)),p&&!this.trackWrites&&(h=!0)}h||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&Ql(this))?de(this,h):(Ms(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(i),!((n=this.dragging)===null||n===void 0)&&n.node&&!i.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,i),f=="reset"?this.dom.scrollTop=0:f=="to selection"?this.scrollToSelection():u&&Al(u)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!(!e||!this.dom.contains(e.nodeType==1?e:e.parentNode))){if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof S){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&Gr(this,t.getBoundingClientRect(),e)}else Gr(this,this.coordsAtPos(this.state.selection.head,1),e)}}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(!e||e.plugins!=this.state.plugins||this.directPlugins!=this.prevDirectPlugins){this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let n=this.directPlugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let n=this.state.plugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}}else for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}}updateDraggedNode(e,t){let n=e.node,i=-1;if(this.state.doc.nodeAt(n.from)==n.node)i=n.from;else{let s=n.from+(this.state.doc.content.size-t.doc.content.size);(s>0&&this.state.doc.nodeAt(s))==n.node&&(i=s)}this.dragging=new zs(e.slice,e.move,i<0?void 0:S.create(this.state.doc,i))}someProp(e,t){let n=this._props&&this._props[e],i;if(n!=null&&(i=t?t(n):n))return i;for(let o=0;o<this.directPlugins.length;o++){let l=this.directPlugins[o].props[e];if(l!=null&&(i=t?t(l):l))return i}let s=this.state.plugins;if(s)for(let o=0;o<s.length;o++){let l=s[o].props[e];if(l!=null&&(i=t?t(l):l))return i}}hasFocus(){if(G){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&Il(this.dom),de(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}updateRoot(){this._root=null}posAtCoords(e){return zl(this,e)}coordsAtPos(e,t=1){return ps(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let i=this.docView.posFromDOM(e,t,n);if(i==null)throw new RangeError("DOM position not inside the editor");return i}endOfTextblock(e,t){return Wl(this,t||this.state,e)}pasteHTML(e,t){return yt(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return yt(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return or(this,e)}destroy(){this.docView&&(ga(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],Tn(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,bl())}get isDestroyed(){return this.docView==null}dispatchEvent(e){return ba(this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){let e=this.domSelection();return e?q&&this.root.nodeType===11&&Cl(this.dom.ownerDocument)==this.dom&&Wa(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function xi(r){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(r.editable),r.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(r.state)),t)for(let n in t)n=="class"?e.class+=" "+t[n]:n=="style"?e.style=(e.style?e.style+";":"")+t[n]:!e[n]&&n!="contenteditable"&&n!="nodeName"&&(e[n]=String(t[n]))}),e.translate||(e.translate="no"),[te.node(0,r.state.doc.content.size,e)]}function Si(r){if(r.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),r.cursorWrapper={dom:e,deco:te.widget(r.state.selection.from,e,{raw:!0,marks:r.markCursor})}}else r.cursorWrapper=null}function Mi(r){return!r.someProp("editable",e=>e(r.state)===!1)}function Xa(r,e){let t=Math.min(r.$anchor.sharedDepth(r.head),e.$anchor.sharedDepth(e.head));return r.$anchor.start(t)!=e.$anchor.start(t)}function Ci(r){let e=Object.create(null);function t(n){for(let i in n)Object.prototype.hasOwnProperty.call(e,i)||(e[i]=n[i])}return r.someProp("nodeViews",t),r.someProp("markViews",t),e}function Za(r,e){let t=0,n=0;for(let i in r){if(r[i]!=e[i])return!0;t++}for(let i in e)n++;return t!=n}function wi(r){if(r.spec.state||r.spec.filterTransaction||r.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}var Te={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Xt={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Qa=typeof navigator<"u"&&/Mac/.test(navigator.platform),ec=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var $=0;$<10;$++)Te[48+$]=Te[96+$]=String($);for(var $=1;$<=24;$++)Te[$+111]="F"+$;for(var $=65;$<=90;$++)Te[$]=String.fromCharCode($+32),Xt[$]=String.fromCharCode($);for(var Dn in Te)Xt.hasOwnProperty(Dn)||(Xt[Dn]=Te[Dn]);function tc(r){var e=Qa&&r.metaKey&&r.shiftKey&&!r.ctrlKey&&!r.altKey||ec&&r.shiftKey&&r.key&&r.key.length==1||r.key=="Unidentified",t=!e&&r.key||(r.shiftKey?Xt:Te)[r.keyCode]||r.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}const nc=typeof navigator<"u"&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),rc=typeof navigator<"u"&&/Win/.test(navigator.platform);function ic(r){let e=r.split(/-(?!$)/),t=e[e.length-1];t=="Space"&&(t=" ");let n,i,s,o;for(let l=0;l<e.length-1;l++){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))n=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))s=!0;else if(/^mod$/i.test(a))nc?o=!0:i=!0;else throw new Error("Unrecognized modifier name: "+a)}return n&&(t="Alt-"+t),i&&(t="Ctrl-"+t),o&&(t="Meta-"+t),s&&(t="Shift-"+t),t}function sc(r){let e=Object.create(null);for(let t in r)e[ic(t)]=r[t];return e}function An(r,e,t=!0){return e.altKey&&(r="Alt-"+r),e.ctrlKey&&(r="Ctrl-"+r),e.metaKey&&(r="Meta-"+r),t&&e.shiftKey&&(r="Shift-"+r),r}function oc(r){return new oe({props:{handleKeyDown:lc(r)}})}function lc(r){let e=sc(r);return function(t,n){let i=tc(n),s,o=e[An(i,n)];if(o&&o(t.state,t.dispatch,t))return!0;if(i.length==1&&i!=" "){if(n.shiftKey){let l=e[An(i,n,!1)];if(l&&l(t.state,t.dispatch,t))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(rc&&n.ctrlKey&&n.altKey)&&(s=Te[n.keyCode])&&s!=i){let l=e[An(s,n)];if(l&&l(t.state,t.dispatch,t))return!0}}return!1}}const ur=(r,e)=>r.selection.empty?!1:(e&&e(r.tr.deleteSelection().scrollIntoView()),!0);function Ws(r,e){let{$cursor:t}=r.selection;return!t||(e?!e.endOfTextblock("backward",r):t.parentOffset>0)?null:t}const js=(r,e,t)=>{let n=Ws(r,t);if(!n)return!1;let i=hr(n);if(!i){let o=n.blockRange(),l=o&&nt(o);return l==null?!1:(e&&e(r.tr.lift(o,l).scrollIntoView()),!0)}let s=i.nodeBefore;if(Xs(r,i,e,-1))return!0;if(n.parent.content.size==0&&(et(s,"end")||S.isSelectable(s)))for(let o=n.depth;;o--){let l=sn(r.doc,n.before(o),n.after(o),k.empty);if(l&&l.slice.size<l.to-l.from){if(e){let a=r.tr.step(l);a.setSelection(et(s,"end")?N.findFrom(a.doc.resolve(a.mapping.map(i.pos,-1)),-1):S.create(a.doc,i.pos-s.nodeSize)),e(a.scrollIntoView())}return!0}if(o==1||n.node(o-1).childCount>1)break}return s.isAtom&&i.depth==n.depth-1?(e&&e(r.tr.delete(i.pos-s.nodeSize,i.pos).scrollIntoView()),!0):!1},ac=(r,e,t)=>{let n=Ws(r,t);if(!n)return!1;let i=hr(n);return i?Js(r,i,e):!1},cc=(r,e,t)=>{let n=Ks(r,t);if(!n)return!1;let i=pr(n);return i?Js(r,i,e):!1};function Js(r,e,t){let n=e.nodeBefore,i=n,s=e.pos-1;for(;!i.isTextblock;s--){if(i.type.spec.isolating)return!1;let f=i.lastChild;if(!f)return!1;i=f}let o=e.nodeAfter,l=o,a=e.pos+1;for(;!l.isTextblock;a++){if(l.type.spec.isolating)return!1;let f=l.firstChild;if(!f)return!1;l=f}let c=sn(r.doc,s,a,k.empty);if(!c||c.from!=s||c instanceof B&&c.slice.size>=a-s)return!1;if(t){let f=r.tr.step(c);f.setSelection(w.create(f.doc,s)),t(f.scrollIntoView())}return!0}function et(r,e,t=!1){for(let n=r;n;n=e=="start"?n.firstChild:n.lastChild){if(n.isTextblock)return!0;if(t&&n.childCount!=1)return!1}return!1}const qs=(r,e,t)=>{let{$head:n,empty:i}=r.selection,s=n;if(!i)return!1;if(n.parent.isTextblock){if(t?!t.endOfTextblock("backward",r):n.parentOffset>0)return!1;s=hr(n)}let o=s&&s.nodeBefore;return!o||!S.isSelectable(o)?!1:(e&&e(r.tr.setSelection(S.create(r.doc,s.pos-o.nodeSize)).scrollIntoView()),!0)};function hr(r){if(!r.parent.type.spec.isolating)for(let e=r.depth-1;e>=0;e--){if(r.index(e)>0)return r.doc.resolve(r.before(e+1));if(r.node(e).type.spec.isolating)break}return null}function Ks(r,e){let{$cursor:t}=r.selection;return!t||(e?!e.endOfTextblock("forward",r):t.parentOffset<t.parent.content.size)?null:t}const Hs=(r,e,t)=>{let n=Ks(r,t);if(!n)return!1;let i=pr(n);if(!i)return!1;let s=i.nodeAfter;if(Xs(r,i,e,1))return!0;if(n.parent.content.size==0&&(et(s,"start")||S.isSelectable(s))){let o=sn(r.doc,n.before(),n.after(),k.empty);if(o&&o.slice.size<o.to-o.from){if(e){let l=r.tr.step(o);l.setSelection(et(s,"start")?N.findFrom(l.doc.resolve(l.mapping.map(i.pos)),1):S.create(l.doc,l.mapping.map(i.pos))),e(l.scrollIntoView())}return!0}}return s.isAtom&&i.depth==n.depth-1?(e&&e(r.tr.delete(i.pos,i.pos+s.nodeSize).scrollIntoView()),!0):!1},Us=(r,e,t)=>{let{$head:n,empty:i}=r.selection,s=n;if(!i)return!1;if(n.parent.isTextblock){if(t?!t.endOfTextblock("forward",r):n.parentOffset<n.parent.content.size)return!1;s=pr(n)}let o=s&&s.nodeAfter;return!o||!S.isSelectable(o)?!1:(e&&e(r.tr.setSelection(S.create(r.doc,s.pos)).scrollIntoView()),!0)};function pr(r){if(!r.parent.type.spec.isolating)for(let e=r.depth-1;e>=0;e--){let t=r.node(e);if(r.index(e)+1<t.childCount)return r.doc.resolve(r.after(e+1));if(t.type.spec.isolating)break}return null}const fc=(r,e)=>{let t=r.selection,n=t instanceof S,i;if(n){if(t.node.isTextblock||!Ee(r.doc,t.from))return!1;i=t.from}else if(i=rn(r.doc,t.from,-1),i==null)return!1;if(e){let s=r.tr.join(i);n&&s.setSelection(S.create(s.doc,i-r.doc.resolve(i).nodeBefore.nodeSize)),e(s.scrollIntoView())}return!0},dc=(r,e)=>{let t=r.selection,n;if(t instanceof S){if(t.node.isTextblock||!Ee(r.doc,t.to))return!1;n=t.to}else if(n=rn(r.doc,t.to,1),n==null)return!1;return e&&e(r.tr.join(n).scrollIntoView()),!0},uc=(r,e)=>{let{$from:t,$to:n}=r.selection,i=t.blockRange(n),s=i&&nt(i);return s==null?!1:(e&&e(r.tr.lift(i,s).scrollIntoView()),!0)},Gs=(r,e)=>{let{$head:t,$anchor:n}=r.selection;return!t.parent.type.spec.code||!t.sameParent(n)?!1:(e&&e(r.tr.insertText(`
`).scrollIntoView()),!0)};function mr(r){for(let e=0;e<r.edgeCount;e++){let{type:t}=r.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}const hc=(r,e)=>{let{$head:t,$anchor:n}=r.selection;if(!t.parent.type.spec.code||!t.sameParent(n))return!1;let i=t.node(-1),s=t.indexAfter(-1),o=mr(i.contentMatchAt(s));if(!o||!i.canReplaceWith(s,s,o))return!1;if(e){let l=t.after(),a=r.tr.replaceWith(l,l,o.createAndFill());a.setSelection(N.near(a.doc.resolve(l),1)),e(a.scrollIntoView())}return!0},_s=(r,e)=>{let t=r.selection,{$from:n,$to:i}=t;if(t instanceof Y||n.parent.inlineContent||i.parent.inlineContent)return!1;let s=mr(i.parent.contentMatchAt(i.indexAfter()));if(!s||!s.isTextblock)return!1;if(e){let o=(!n.parentOffset&&i.index()<i.parent.childCount?n:i).pos,l=r.tr.insert(o,s.createAndFill());l.setSelection(w.create(l.doc,o+1)),e(l.scrollIntoView())}return!0},Ys=(r,e)=>{let{$cursor:t}=r.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let s=t.before();if(fe(r.doc,s))return e&&e(r.tr.split(s).scrollIntoView()),!0}let n=t.blockRange(),i=n&&nt(n);return i==null?!1:(e&&e(r.tr.lift(n,i).scrollIntoView()),!0)};function pc(r){return(e,t)=>{let{$from:n,$to:i}=e.selection;if(e.selection instanceof S&&e.selection.node.isBlock)return!n.parentOffset||!fe(e.doc,n.pos)?!1:(t&&t(e.tr.split(n.pos).scrollIntoView()),!0);if(!n.depth)return!1;let s=[],o,l,a=!1,c=!1;for(let h=n.depth;;h--)if(n.node(h).isBlock){a=n.end(h)==n.pos+(n.depth-h),c=n.start(h)==n.pos-(n.depth-h),l=mr(n.node(h-1).contentMatchAt(n.indexAfter(h-1))),s.unshift(a&&l?{type:l}:null),o=h;break}else{if(h==1)return!1;s.unshift(null)}let f=e.tr;(e.selection instanceof w||e.selection instanceof Y)&&f.deleteSelection();let d=f.mapping.map(n.pos),u=fe(f.doc,d,s.length,s);if(u||(s[0]=l?{type:l}:null,u=fe(f.doc,d,s.length,s)),!u)return!1;if(f.split(d,s.length,s),!a&&c&&n.node(o).type!=l){let h=f.mapping.map(n.before(o)),p=f.doc.resolve(h);l&&n.node(o-1).canReplaceWith(p.index(),p.index()+1,l)&&f.setNodeMarkup(f.mapping.map(n.before(o)),l)}return t&&t(f.scrollIntoView()),!0}}const mc=pc(),gc=(r,e)=>{let{$from:t,to:n}=r.selection,i,s=t.sharedDepth(n);return s==0?!1:(i=t.before(s),e&&e(r.tr.setSelection(S.create(r.doc,i))),!0)};function yc(r,e,t){let n=e.nodeBefore,i=e.nodeAfter,s=e.index();return!n||!i||!n.type.compatibleContent(i.type)?!1:!n.content.size&&e.parent.canReplace(s-1,s)?(t&&t(r.tr.delete(e.pos-n.nodeSize,e.pos).scrollIntoView()),!0):!e.parent.canReplace(s,s+1)||!(i.isTextblock||Ee(r.doc,e.pos))?!1:(t&&t(r.tr.join(e.pos).scrollIntoView()),!0)}function Xs(r,e,t,n){let i=e.nodeBefore,s=e.nodeAfter,o,l,a=i.type.spec.isolating||s.type.spec.isolating;if(!a&&yc(r,e,t))return!0;let c=!a&&e.parent.canReplace(e.index(),e.index()+1);if(c&&(o=(l=i.contentMatchAt(i.childCount)).findWrapping(s.type))&&l.matchType(o[0]||s.type).validEnd){if(t){let h=e.pos+s.nodeSize,p=b.empty;for(let y=o.length-1;y>=0;y--)p=b.from(o[y].create(null,p));p=b.from(i.copy(p));let m=r.tr.step(new z(e.pos-1,h,e.pos,h,new k(p,1,0),o.length,!0)),g=m.doc.resolve(h+2*o.length);g.nodeAfter&&g.nodeAfter.type==i.type&&Ee(m.doc,g.pos)&&m.join(g.pos),t(m.scrollIntoView())}return!0}let f=s.type.spec.isolating||n>0&&a?null:N.findFrom(e,1),d=f&&f.$from.blockRange(f.$to),u=d&&nt(d);if(u!=null&&u>=e.depth)return t&&t(r.tr.lift(d,u).scrollIntoView()),!0;if(c&&et(s,"start",!0)&&et(i,"end")){let h=i,p=[];for(;p.push(h),!h.isTextblock;)h=h.lastChild;let m=s,g=1;for(;!m.isTextblock;m=m.firstChild)g++;if(h.canReplace(h.childCount,h.childCount,m.content)){if(t){let y=b.empty;for(let O=p.length-1;O>=0;O--)y=b.from(p[O].copy(y));let M=r.tr.step(new z(e.pos-p.length,e.pos+s.nodeSize,e.pos+g,e.pos+s.nodeSize-g,new k(y,p.length,0),0,!0));t(M.scrollIntoView())}return!0}}return!1}function Zs(r){return function(e,t){let n=e.selection,i=r<0?n.$from:n.$to,s=i.depth;for(;i.node(s).isInline;){if(!s)return!1;s--}return i.node(s).isTextblock?(t&&t(e.tr.setSelection(w.create(e.doc,r<0?i.start(s):i.end(s)))),!0):!1}}const bc=Zs(-1),kc=Zs(1);function xc(r,e=null){return function(t,n){let{$from:i,$to:s}=t.selection,o=i.blockRange(s),l=o&&tr(o,r,e);return l?(n&&n(t.tr.wrap(o,l).scrollIntoView()),!0):!1}}function Oi(r,e=null){return function(t,n){let i=!1;for(let s=0;s<t.selection.ranges.length&&!i;s++){let{$from:{pos:o},$to:{pos:l}}=t.selection.ranges[s];t.doc.nodesBetween(o,l,(a,c)=>{if(i)return!1;if(!(!a.isTextblock||a.hasMarkup(r,e)))if(a.type==r)i=!0;else{let f=t.doc.resolve(c),d=f.index();i=f.parent.canReplaceWith(d,d+1,r)}})}if(!i)return!1;if(n){let s=t.tr;for(let o=0;o<t.selection.ranges.length;o++){let{$from:{pos:l},$to:{pos:a}}=t.selection.ranges[o];s.setBlockType(l,a,r,e)}n(s.scrollIntoView())}return!0}}function gr(...r){return function(e,t,n){for(let i=0;i<r.length;i++)if(r[i](e,t,n))return!0;return!1}}gr(ur,js,qs);gr(ur,Hs,Us);gr(Gs,_s,Ys,mc);typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform&&os.platform()=="darwin";function Sc(r,e=null){return function(t,n){let{$from:i,$to:s}=t.selection,o=i.blockRange(s);if(!o)return!1;let l=n?t.tr:null;return Mc(l,o,r,e)?(n&&n(l.scrollIntoView()),!0):!1}}function Mc(r,e,t,n=null){let i=!1,s=e,o=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(t)&&e.startIndex==0){if(e.$from.index(e.depth-1)==0)return!1;let a=o.resolve(e.start-2);s=new Jt(a,a,e.depth),e.endIndex<e.parent.childCount&&(e=new Jt(e.$from,o.resolve(e.$to.end(e.depth)),e.depth)),i=!0}let l=tr(s,t,n,e);return l?(r&&Cc(r,e,l,i,t),!0):!1}function Cc(r,e,t,n,i){let s=b.empty;for(let f=t.length-1;f>=0;f--)s=b.from(t[f].type.create(t[f].attrs,s));r.step(new z(e.start-(n?2:0),e.end,e.start,e.end,new k(s,0,0),t.length,!0));let o=0;for(let f=0;f<t.length;f++)t[f].type==i&&(o=f+1);let l=t.length-o,a=e.start+t.length-(n?2:0),c=e.parent;for(let f=e.startIndex,d=e.endIndex,u=!0;f<d;f++,u=!1)!u&&fe(r.doc,a,l)&&(r.split(a,l),a+=2*l),a+=c.child(f).nodeSize;return r}function wc(r){return function(e,t){let{$from:n,$to:i}=e.selection,s=n.blockRange(i,o=>o.childCount>0&&o.firstChild.type==r);return s?t?n.node(s.depth-1).type==r?Oc(e,t,r,s):Nc(e,t,s):!0:!1}}function Oc(r,e,t,n){let i=r.tr,s=n.end,o=n.$to.end(n.depth);s<o&&(i.step(new z(s-1,o,s,o,new k(b.from(t.create(null,n.parent.copy())),1,0),1,!0)),n=new Jt(i.doc.resolve(n.$from.pos),i.doc.resolve(o),n.depth));const l=nt(n);if(l==null)return!1;i.lift(n,l);let a=i.doc.resolve(i.mapping.map(s,-1)-1);return Ee(i.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&i.join(a.pos),e(i.scrollIntoView()),!0}function Nc(r,e,t){let n=r.tr,i=t.parent;for(let h=t.end,p=t.endIndex-1,m=t.startIndex;p>m;p--)h-=i.child(p).nodeSize,n.delete(h-1,h+1);let s=n.doc.resolve(t.start),o=s.nodeAfter;if(n.mapping.map(t.end)!=t.start+s.nodeAfter.nodeSize)return!1;let l=t.startIndex==0,a=t.endIndex==i.childCount,c=s.node(-1),f=s.index(-1);if(!c.canReplace(f+(l?0:1),f+1,o.content.append(a?b.empty:b.from(i))))return!1;let d=s.pos,u=d+o.nodeSize;return n.step(new z(d-(l?1:0),u+(a?1:0),d+1,u-1,new k((l?b.empty:b.from(i.copy(b.empty))).append(a?b.empty:b.from(i.copy(b.empty))),l?0:1,a?0:1),l?0:1)),e(n.scrollIntoView()),!0}function Tc(r){return function(e,t){let{$from:n,$to:i}=e.selection,s=n.blockRange(i,c=>c.childCount>0&&c.firstChild.type==r);if(!s)return!1;let o=s.startIndex;if(o==0)return!1;let l=s.parent,a=l.child(o-1);if(a.type!=r)return!1;if(t){let c=a.lastChild&&a.lastChild.type==l.type,f=b.from(c?r.create():null),d=new k(b.from(r.create(null,b.from(l.type.create(null,f)))),c?3:1,0),u=s.start,h=s.end;t(e.tr.step(new z(u-(c?3:1),h,u,h,d,1,!0)).scrollIntoView())}return!0}}function Ot(r){const{state:e,transaction:t}=r;let{selection:n}=t,{doc:i}=t,{storedMarks:s}=t;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return s},get selection(){return n},get doc(){return i},get tr(){return n=t.selection,i=t.doc,s=t.storedMarks,t}}}class Nt{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:e,editor:t,state:n}=this,{view:i}=t,{tr:s}=n,o=this.buildProps(s);return Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...f)=>{const d=a(...f)(o);return!s.getMeta("preventDispatch")&&!this.hasCustomState&&i.dispatch(s),d}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){const{rawCommands:n,editor:i,state:s}=this,{view:o}=i,l=[],a=!!e,c=e||s.tr,f=()=>(!a&&t&&!c.getMeta("preventDispatch")&&!this.hasCustomState&&o.dispatch(c),l.every(u=>u===!0)),d={...Object.fromEntries(Object.entries(n).map(([u,h])=>[u,(...m)=>{const g=this.buildProps(c,t),y=h(...m)(g);return l.push(y),d}])),run:f};return d}createCan(e){const{rawCommands:t,state:n}=this,i=!1,s=e||n.tr,o=this.buildProps(s,i);return{...Object.fromEntries(Object.entries(t).map(([a,c])=>[a,(...f)=>c(...f)({...o,dispatch:void 0})])),chain:()=>this.createChain(s,i)}}buildProps(e,t=!0){const{rawCommands:n,editor:i,state:s}=this,{view:o}=i,l={tr:e,editor:i,view:o,state:Ot({state:s,transaction:e}),dispatch:t?()=>{}:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(n).map(([a,c])=>[a,(...f)=>c(...f)(l)]))}};return l}}class Ec{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){const n=this.callbacks[e];return n&&n.forEach(i=>i.apply(this,t)),this}off(e,t){const n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter(i=>i!==t):delete this.callbacks[e]),this}once(e,t){const n=(...i)=>{this.off(e,n),t.apply(this,i)};return this.on(e,n)}removeAllListeners(){this.callbacks={}}}function x(r,e,t){return r.config[e]===void 0&&r.parent?x(r.parent,e,t):typeof r.config[e]=="function"?r.config[e].bind({...t,parent:r.parent?x(r.parent,e,t):null}):r.config[e]}function Tt(r){const e=r.filter(i=>i.type==="extension"),t=r.filter(i=>i.type==="node"),n=r.filter(i=>i.type==="mark");return{baseExtensions:e,nodeExtensions:t,markExtensions:n}}function yr(r){const e=[],{nodeExtensions:t,markExtensions:n}=Tt(r),i=[...t,...n],s={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return r.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage,extensions:i},a=x(o,"addGlobalAttributes",l);if(!a)return;a().forEach(f=>{f.types.forEach(d=>{Object.entries(f.attributes).forEach(([u,h])=>{e.push({type:d,name:u,attribute:{...s,...h}})})})})}),i.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage},a=x(o,"addAttributes",l);if(!a)return;const c=a();Object.entries(c).forEach(([f,d])=>{const u={...s,...d};typeof(u==null?void 0:u.default)=="function"&&(u.default=u.default()),u!=null&&u.isRequired&&(u==null?void 0:u.default)===void 0&&delete u.default,e.push({type:o.name,name:f,attribute:u})})}),e}function v(r,e){if(typeof r=="string"){if(!e.nodes[r])throw Error(`There is no node type named '${r}'. Maybe you forgot to add the extension?`);return e.nodes[r]}return r}function Qs(...r){return r.filter(e=>!!e).reduce((e,t)=>{const n={...e};return Object.entries(t).forEach(([i,s])=>{if(!n[i]){n[i]=s;return}if(i==="class"){const l=s?String(s).split(" "):[],a=n[i]?n[i].split(" "):[],c=l.filter(f=>!a.includes(f));n[i]=[...a,...c].join(" ")}else if(i==="style"){const l=s?s.split(";").map(f=>f.trim()).filter(Boolean):[],a=n[i]?n[i].split(";").map(f=>f.trim()).filter(Boolean):[],c=new Map;a.forEach(f=>{const[d,u]=f.split(":").map(h=>h.trim());c.set(d,u)}),l.forEach(f=>{const[d,u]=f.split(":").map(h=>h.trim());c.set(d,u)}),n[i]=Array.from(c.entries()).map(([f,d])=>`${f}: ${d}`).join("; ")}else n[i]=s}),n},{})}function Zt(r,e){return e.filter(t=>t.type===r.type.name).filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(r.attrs)||{}:{[t.name]:r.attrs[t.name]}).reduce((t,n)=>Qs(t,n),{})}function br(r){return typeof r=="function"}function C(r,e=void 0,...t){return br(r)?e?r.bind(e)(...t):r(...t):r}function eo(r={}){return Object.keys(r).length===0&&r.constructor===Object}function to(r){return typeof r!="string"?r:r.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(r):r==="true"?!0:r==="false"?!1:r}function _n(r,e){return"style"in r?r:{...r,getAttrs:t=>{const n=r.getAttrs?r.getAttrs(t):r.attrs;if(n===!1)return!1;const i=e.reduce((s,o)=>{const l=o.attribute.parseHTML?o.attribute.parseHTML(t):to(t.getAttribute(o.name));return l==null?s:{...s,[o.name]:l}},{});return{...n,...i}}}}function Ni(r){return Object.fromEntries(Object.entries(r).filter(([e,t])=>e==="attrs"&&eo(t)?!1:t!=null))}function no(r,e){var t;const n=yr(r),{nodeExtensions:i,markExtensions:s}=Tt(r),o=(t=i.find(c=>x(c,"topNode")))===null||t===void 0?void 0:t.name,l=Object.fromEntries(i.map(c=>{const f=n.filter(y=>y.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},u=r.reduce((y,M)=>{const O=x(M,"extendNodeSchema",d);return{...y,...O?O(c):{}}},{}),h=Ni({...u,content:C(x(c,"content",d)),marks:C(x(c,"marks",d)),group:C(x(c,"group",d)),inline:C(x(c,"inline",d)),atom:C(x(c,"atom",d)),selectable:C(x(c,"selectable",d)),draggable:C(x(c,"draggable",d)),code:C(x(c,"code",d)),whitespace:C(x(c,"whitespace",d)),linebreakReplacement:C(x(c,"linebreakReplacement",d)),defining:C(x(c,"defining",d)),isolating:C(x(c,"isolating",d)),attrs:Object.fromEntries(f.map(y=>{var M;return[y.name,{default:(M=y==null?void 0:y.attribute)===null||M===void 0?void 0:M.default}]}))}),p=C(x(c,"parseHTML",d));p&&(h.parseDOM=p.map(y=>_n(y,f)));const m=x(c,"renderHTML",d);m&&(h.toDOM=y=>m({node:y,HTMLAttributes:Zt(y,f)}));const g=x(c,"renderText",d);return g&&(h.toText=g),[c.name,h]})),a=Object.fromEntries(s.map(c=>{const f=n.filter(g=>g.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},u=r.reduce((g,y)=>{const M=x(y,"extendMarkSchema",d);return{...g,...M?M(c):{}}},{}),h=Ni({...u,inclusive:C(x(c,"inclusive",d)),excludes:C(x(c,"excludes",d)),group:C(x(c,"group",d)),spanning:C(x(c,"spanning",d)),code:C(x(c,"code",d)),attrs:Object.fromEntries(f.map(g=>{var y;return[g.name,{default:(y=g==null?void 0:g.attribute)===null||y===void 0?void 0:y.default}]}))}),p=C(x(c,"parseHTML",d));p&&(h.parseDOM=p.map(g=>_n(g,f)));const m=x(c,"renderHTML",d);return m&&(h.toDOM=g=>m({mark:g,HTMLAttributes:Zt(g,f)})),[c.name,h]}));return new qi({topNode:o,nodes:l,marks:a})}function $t(r,e){return e.nodes[r]||e.marks[r]||null}function Yn(r,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===r.name):e}function fn(r,e){const t=We.fromSchema(e).serializeFragment(r),i=document.implementation.createHTMLDocument().createElement("div");return i.appendChild(t),i.innerHTML}const ro=(r,e=500)=>{let t="";const n=r.parentOffset;return r.parent.nodesBetween(Math.max(0,n-e),n,(i,s,o,l)=>{var a,c;const f=((c=(a=i.type.spec).toText)===null||c===void 0?void 0:c.call(a,{node:i,pos:s,parent:o,index:l}))||i.textContent||"%leaf%";t+=i.isAtom&&!i.isText?f:f.slice(0,Math.max(0,n-s))}),t};function dn(r){return Object.prototype.toString.call(r)==="[object RegExp]"}class Et{constructor(e){this.find=e.find,this.handler=e.handler}}const Dc=(r,e)=>{if(dn(e))return e.exec(r);const t=e(r);if(!t)return null;const n=[t.text];return n.index=t.index,n.input=r,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),n.push(t.replaceWith)),n};function Pt(r){var e;const{editor:t,from:n,to:i,text:s,rules:o,plugin:l}=r,{view:a}=t;if(a.composing)return!1;const c=a.state.doc.resolve(n);if(c.parent.type.spec.code||!((e=c.nodeBefore||c.nodeAfter)===null||e===void 0)&&e.marks.find(u=>u.type.spec.code))return!1;let f=!1;const d=ro(c)+s;return o.forEach(u=>{if(f)return;const h=Dc(d,u.find);if(!h)return;const p=a.state.tr,m=Ot({state:a.state,transaction:p}),g={from:n-(h[0].length-s.length),to:i},{commands:y,chain:M,can:O}=new Nt({editor:t,state:m});u.handler({state:m,range:g,match:h,commands:y,chain:M,can:O})===null||!p.steps.length||(p.setMeta(l,{transform:p,from:n,to:i,text:s}),a.dispatch(p),f=!0)}),f}function io(r){const{editor:e,rules:t}=r,n=new oe({state:{init(){return null},apply(i,s,o){const l=i.getMeta(n);if(l)return l;const a=i.getMeta("applyInputRules");return!!a&&setTimeout(()=>{let{text:f}=a;typeof f=="string"?f=f:f=fn(b.from(f),o.schema);const{from:d}=a,u=d+f.length;Pt({editor:e,from:d,to:u,text:f,rules:t,plugin:n})}),i.selectionSet||i.docChanged?null:s}},props:{handleTextInput(i,s,o,l){return Pt({editor:e,from:s,to:o,text:l,rules:t,plugin:n})},handleDOMEvents:{compositionend:i=>(setTimeout(()=>{const{$cursor:s}=i.state.selection;s&&Pt({editor:e,from:s.pos,to:s.pos,text:"",rules:t,plugin:n})}),!1)},handleKeyDown(i,s){if(s.key!=="Enter")return!1;const{$cursor:o}=i.state.selection;return o?Pt({editor:e,from:o.pos,to:o.pos,text:`
`,rules:t,plugin:n}):!1}},isInputRules:!0});return n}function Ac(r){return Object.prototype.toString.call(r).slice(8,-1)}function lt(r){return Ac(r)!=="Object"?!1:r.constructor===Object&&Object.getPrototypeOf(r)===Object.prototype}function Dt(r,e){const t={...r};return lt(r)&&lt(e)&&Object.keys(e).forEach(n=>{lt(e[n])&&lt(r[n])?t[n]=Dt(r[n],e[n]):t[n]=e[n]}),t}class kt{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=C(x(this,"addOptions",{name:this.name}))),this.storage=C(x(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new kt(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>Dt(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new kt(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=C(x(t,"addOptions",{name:t.name})),t.storage=C(x(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){const{tr:n}=e.state,i=e.state.selection.$from;if(i.pos===i.end()){const o=i.marks();if(!!!o.find(c=>(c==null?void 0:c.type.name)===t.name))return!1;const a=o.find(c=>(c==null?void 0:c.type.name)===t.name);return a&&n.removeStoredMark(a),n.insertText(" ",i.pos),e.view.dispatch(n),!0}return!1}}function so(r){return typeof r=="number"}class oo{constructor(e){this.find=e.find,this.handler=e.handler}}const Ic=(r,e,t)=>{if(dn(e))return[...r.matchAll(e)];const n=e(r,t);return n?n.map(i=>{const s=[i.text];return s.index=i.index,s.input=r,s.data=i.data,i.replaceWith&&(i.text.includes(i.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),s.push(i.replaceWith)),s}):[]};function Rc(r){const{editor:e,state:t,from:n,to:i,rule:s,pasteEvent:o,dropEvent:l}=r,{commands:a,chain:c,can:f}=new Nt({editor:e,state:t}),d=[];return t.doc.nodesBetween(n,i,(h,p)=>{if(!h.isTextblock||h.type.spec.code)return;const m=Math.max(n,p),g=Math.min(i,p+h.content.size),y=h.textBetween(m-p,g-p,void 0,"￼");Ic(y,s.find,o).forEach(O=>{if(O.index===void 0)return;const I=m+O.index+1,E=I+O[0].length,D={from:t.tr.mapping.map(I),to:t.tr.mapping.map(E)},U=s.handler({state:t,range:D,match:O,commands:a,chain:c,can:f,pasteEvent:o,dropEvent:l});d.push(U)})}),d.every(h=>h!==null)}let Bt=null;const vc=r=>{var e;const t=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(e=t.clipboardData)===null||e===void 0||e.setData("text/html",r),t};function lo(r){const{editor:e,rules:t}=r;let n=null,i=!1,s=!1,o=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,l;try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}const a=({state:f,from:d,to:u,rule:h,pasteEvt:p})=>{const m=f.tr,g=Ot({state:f,transaction:m});if(!(!Rc({editor:e,state:g,from:Math.max(d-1,0),to:u.b-1,rule:h,pasteEvent:p,dropEvent:l})||!m.steps.length)){try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}return o=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,m}};return t.map(f=>new oe({view(d){const u=p=>{var m;n=!((m=d.dom.parentElement)===null||m===void 0)&&m.contains(p.target)?d.dom.parentElement:null,n&&(Bt=e)},h=()=>{Bt&&(Bt=null)};return window.addEventListener("dragstart",u),window.addEventListener("dragend",h),{destroy(){window.removeEventListener("dragstart",u),window.removeEventListener("dragend",h)}}},props:{handleDOMEvents:{drop:(d,u)=>{if(s=n===d.dom.parentElement,l=u,!s){const h=Bt;h&&setTimeout(()=>{const p=h.state.selection;p&&h.commands.deleteRange({from:p.from,to:p.to})},10)}return!1},paste:(d,u)=>{var h;const p=(h=u.clipboardData)===null||h===void 0?void 0:h.getData("text/html");return o=u,i=!!(p!=null&&p.includes("data-pm-slice")),!1}}},appendTransaction:(d,u,h)=>{const p=d[0],m=p.getMeta("uiEvent")==="paste"&&!i,g=p.getMeta("uiEvent")==="drop"&&!s,y=p.getMeta("applyPasteRules"),M=!!y;if(!m&&!g&&!M)return;if(M){let{text:E}=y;typeof E=="string"?E=E:E=fn(b.from(E),h.schema);const{from:D}=y,U=D+E.length,J=vc(E);return a({rule:f,state:h,from:D,to:{b:U},pasteEvt:J})}const O=u.doc.content.findDiffStart(h.doc.content),I=u.doc.content.findDiffEnd(h.doc.content);if(!(!so(O)||!I||O===I.b))return a({rule:f,state:h,from:O,to:I,pasteEvt:o})}}))}function ao(r){const e=r.filter((t,n)=>r.indexOf(t)!==n);return Array.from(new Set(e))}class Ge{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=Ge.resolve(e),this.schema=no(this.extensions,t),this.setupExtensions()}static resolve(e){const t=Ge.sort(Ge.flatten(e)),n=ao(t.map(i=>i.name));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map(i=>`'${i}'`).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map(t=>{const n={name:t.name,options:t.options,storage:t.storage},i=x(t,"addExtensions",n);return i?[t,...this.flatten(i())]:t}).flat(10)}static sort(e){return e.sort((n,i)=>{const s=x(n,"priority")||100,o=x(i,"priority")||100;return s>o?-1:s<o?1:0})}get commands(){return this.extensions.reduce((e,t)=>{const n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:$t(t.name,this.schema)},i=x(t,"addCommands",n);return i?{...e,...i()}:e},{})}get plugins(){const{editor:e}=this,t=Ge.sort([...this.extensions].reverse()),n=[],i=[],s=t.map(o=>{const l={name:o.name,options:o.options,storage:o.storage,editor:e,type:$t(o.name,this.schema)},a=[],c=x(o,"addKeyboardShortcuts",l);let f={};if(o.type==="mark"&&x(o,"exitable",l)&&(f.ArrowRight=()=>kt.handleExit({editor:e,mark:o})),c){const m=Object.fromEntries(Object.entries(c()).map(([g,y])=>[g,()=>y({editor:e})]));f={...f,...m}}const d=oc(f);a.push(d);const u=x(o,"addInputRules",l);Yn(o,e.options.enableInputRules)&&u&&n.push(...u());const h=x(o,"addPasteRules",l);Yn(o,e.options.enablePasteRules)&&h&&i.push(...h());const p=x(o,"addProseMirrorPlugins",l);if(p){const m=p();a.push(...m)}return a}).flat();return[io({editor:e,rules:n}),...lo({editor:e,rules:i}),...s]}get attributes(){return yr(this.extensions)}get nodeViews(){const{editor:e}=this,{nodeExtensions:t}=Tt(this.extensions);return Object.fromEntries(t.filter(n=>!!x(n,"addNodeView")).map(n=>{const i=this.attributes.filter(a=>a.type===n.name),s={name:n.name,options:n.options,storage:n.storage,editor:e,type:v(n.name,this.schema)},o=x(n,"addNodeView",s);if(!o)return[];const l=(a,c,f,d,u)=>{const h=Zt(a,i);return o()({node:a,view:c,getPos:f,decorations:d,innerDecorations:u,editor:e,extension:n,HTMLAttributes:h})};return[n.name,l]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;const n={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:$t(e.name,this.schema)};e.type==="mark"&&(!((t=C(x(e,"keepOnSplit",n)))!==null&&t!==void 0)||t)&&this.splittableMarks.push(e.name);const i=x(e,"onBeforeCreate",n),s=x(e,"onCreate",n),o=x(e,"onUpdate",n),l=x(e,"onSelectionUpdate",n),a=x(e,"onTransaction",n),c=x(e,"onFocus",n),f=x(e,"onBlur",n),d=x(e,"onDestroy",n);i&&this.editor.on("beforeCreate",i),s&&this.editor.on("create",s),o&&this.editor.on("update",o),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),f&&this.editor.on("blur",f),d&&this.editor.on("destroy",d)})}}class ee{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=C(x(this,"addOptions",{name:this.name}))),this.storage=C(x(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new ee(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>Dt(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new ee({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=C(x(t,"addOptions",{name:t.name})),t.storage=C(x(t,"addStorage",{name:t.name,options:t.options})),t}}function kr(r,e,t){const{from:n,to:i}=e,{blockSeparator:s=`

`,textSerializers:o={}}=t||{};let l="";return r.nodesBetween(n,i,(a,c,f,d)=>{var u;a.isBlock&&c>n&&(l+=s);const h=o==null?void 0:o[a.type.name];if(h)return f&&(l+=h({node:a,pos:c,parent:f,index:d,range:e})),!1;a.isText&&(l+=(u=a==null?void 0:a.text)===null||u===void 0?void 0:u.slice(Math.max(n,c)-c,i-c))}),l}function xr(r){return Object.fromEntries(Object.entries(r.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}const Pc=ee.create({name:"clipboardTextSerializer",addOptions(){return{blockSeparator:void 0}},addProseMirrorPlugins(){return[new oe({key:new je("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:r}=this,{state:e,schema:t}=r,{doc:n,selection:i}=e,{ranges:s}=i,o=Math.min(...s.map(f=>f.$from.pos)),l=Math.max(...s.map(f=>f.$to.pos)),a=xr(t);return kr(n,{from:o,to:l},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}}),Bc=()=>({editor:r,view:e})=>(requestAnimationFrame(()=>{var t;r.isDestroyed||(e.dom.blur(),(t=window==null?void 0:window.getSelection())===null||t===void 0||t.removeAllRanges())}),!0),zc=(r=!1)=>({commands:e})=>e.setContent("",r),Fc=()=>({state:r,tr:e,dispatch:t})=>{const{selection:n}=e,{ranges:i}=n;return t&&i.forEach(({$from:s,$to:o})=>{r.doc.nodesBetween(s.pos,o.pos,(l,a)=>{if(l.type.isText)return;const{doc:c,mapping:f}=e,d=c.resolve(f.map(a)),u=c.resolve(f.map(a+l.nodeSize)),h=d.blockRange(u);if(!h)return;const p=nt(h);if(l.type.isTextblock){const{defaultType:m}=d.parent.contentMatchAt(d.index());e.setNodeMarkup(h.start,m)}(p||p===0)&&e.lift(h,p)})}),!0},Vc=r=>e=>r(e),$c=()=>({state:r,dispatch:e})=>_s(r,e),Lc=(r,e)=>({editor:t,tr:n})=>{const{state:i}=t,s=i.doc.slice(r.from,r.to);n.deleteRange(r.from,r.to);const o=n.mapping.map(e);return n.insert(o,s.content),n.setSelection(new w(n.doc.resolve(o-1))),!0},Wc=()=>({tr:r,dispatch:e})=>{const{selection:t}=r,n=t.$anchor.node();if(n.content.size>0)return!1;const i=r.selection.$anchor;for(let s=i.depth;s>0;s-=1)if(i.node(s).type===n.type){if(e){const l=i.before(s),a=i.after(s);r.delete(l,a).scrollIntoView()}return!0}return!1},jc=r=>({tr:e,state:t,dispatch:n})=>{const i=v(r,t.schema),s=e.selection.$anchor;for(let o=s.depth;o>0;o-=1)if(s.node(o).type===i){if(n){const a=s.before(o),c=s.after(o);e.delete(a,c).scrollIntoView()}return!0}return!1},Jc=r=>({tr:e,dispatch:t})=>{const{from:n,to:i}=r;return t&&e.delete(n,i),!0},qc=()=>({state:r,dispatch:e})=>ur(r,e),Kc=()=>({commands:r})=>r.keyboardShortcut("Enter"),Hc=()=>({state:r,dispatch:e})=>hc(r,e);function xt(r,e,t={strict:!0}){const n=Object.keys(e);return n.length?n.every(i=>t.strict?e[i]===r[i]:dn(e[i])?e[i].test(r[i]):e[i]===r[i]):!0}function co(r,e,t={}){return r.find(n=>n.type===e&&xt(Object.fromEntries(Object.keys(t).map(i=>[i,n.attrs[i]])),t))}function Ti(r,e,t={}){return!!co(r,e,t)}function un(r,e,t){var n;if(!r||!e)return;let i=r.parent.childAfter(r.parentOffset);if((!i.node||!i.node.marks.some(f=>f.type===e))&&(i=r.parent.childBefore(r.parentOffset)),!i.node||!i.node.marks.some(f=>f.type===e)||(t=t||((n=i.node.marks[0])===null||n===void 0?void 0:n.attrs),!co([...i.node.marks],e,t)))return;let o=i.index,l=r.start()+i.offset,a=o+1,c=l+i.node.nodeSize;for(;o>0&&Ti([...r.parent.child(o-1).marks],e,t);)o-=1,l-=r.parent.child(o).nodeSize;for(;a<r.parent.childCount&&Ti([...r.parent.child(a).marks],e,t);)c+=r.parent.child(a).nodeSize,a+=1;return{from:l,to:c}}function ue(r,e){if(typeof r=="string"){if(!e.marks[r])throw Error(`There is no mark type named '${r}'. Maybe you forgot to add the extension?`);return e.marks[r]}return r}const Uc=(r,e={})=>({tr:t,state:n,dispatch:i})=>{const s=ue(r,n.schema),{doc:o,selection:l}=t,{$from:a,from:c,to:f}=l;if(i){const d=un(a,s,e);if(d&&d.from<=c&&d.to>=f){const u=w.create(o,d.from,d.to);t.setSelection(u)}}return!0},Gc=r=>e=>{const t=typeof r=="function"?r(e):r;for(let n=0;n<t.length;n+=1)if(t[n](e))return!0;return!1};function Sr(r){return r instanceof w}function Se(r=0,e=0,t=0){return Math.min(Math.max(r,e),t)}function Mr(r,e=null){if(!e)return null;const t=N.atStart(r),n=N.atEnd(r);if(e==="start"||e===!0)return t;if(e==="end")return n;const i=t.from,s=n.to;return e==="all"?w.create(r,Se(0,i,s),Se(r.content.size,i,s)):w.create(r,Se(e,i,s),Se(e,i,s))}function _c(){return navigator.platform==="Android"||/android/i.test(navigator.userAgent)}function hn(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const Yc=(r=null,e={})=>({editor:t,view:n,tr:i,dispatch:s})=>{e={scrollIntoView:!0,...e};const o=()=>{(hn()||_c())&&n.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(n.focus(),e!=null&&e.scrollIntoView&&t.commands.scrollIntoView())})};if(n.hasFocus()&&r===null||r===!1)return!0;if(s&&r===null&&!Sr(t.state.selection))return o(),!0;const l=Mr(i.doc,r)||t.state.selection,a=t.state.selection.eq(l);return s&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),o()),!0},Xc=(r,e)=>t=>r.every((n,i)=>e(n,{...t,index:i})),Zc=(r,e)=>({tr:t,commands:n})=>n.insertContentAt({from:t.selection.from,to:t.selection.to},r,e),fo=r=>{const e=r.childNodes;for(let t=e.length-1;t>=0;t-=1){const n=e[t];n.nodeType===3&&n.nodeValue&&/^(\n\s\s|\n)$/.test(n.nodeValue)?r.removeChild(n):n.nodeType===1&&fo(n)}return r};function at(r){const e=`<body>${r}</body>`,t=new window.DOMParser().parseFromString(e,"text/html").body;return fo(t)}function St(r,e,t){if(r instanceof Me||r instanceof b)return r;t={slice:!0,parseOptions:{},...t};const n=typeof r=="object"&&r!==null,i=typeof r=="string";if(n)try{if(Array.isArray(r)&&r.length>0)return b.fromArray(r.map(l=>e.nodeFromJSON(l)));const o=e.nodeFromJSON(r);return t.errorOnInvalidContent&&o.check(),o}catch(s){if(t.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:s});return console.warn("[tiptap warn]: Invalid content.","Passed value:",r,"Error:",s),St("",e,t)}if(i){if(t.errorOnInvalidContent){let o=!1,l="";const a=new qi({topNode:e.spec.topNode,marks:e.spec.marks,nodes:e.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:c=>(o=!0,l=typeof c=="string"?c:c.outerHTML,null)}]}})});if(t.slice?Ce.fromSchema(a).parseSlice(at(r),t.parseOptions):Ce.fromSchema(a).parse(at(r),t.parseOptions),t.errorOnInvalidContent&&o)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${l}`)})}const s=Ce.fromSchema(e);return t.slice?s.parseSlice(at(r),t.parseOptions).content:s.parse(at(r),t.parseOptions)}return St("",e,t)}function uo(r,e,t){const n=r.steps.length-1;if(n<e)return;const i=r.steps[n];if(!(i instanceof B||i instanceof z))return;const s=r.mapping.maps[n];let o=0;s.forEach((l,a,c,f)=>{o===0&&(o=f)}),r.setSelection(N.near(r.doc.resolve(o),t))}const Qc=r=>!("type"in r),ef=(r,e,t)=>({tr:n,dispatch:i,editor:s})=>{var o;if(i){t={parseOptions:s.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...t};let l;try{l=St(e,s.schema,{parseOptions:{preserveWhitespace:"full",...t.parseOptions},errorOnInvalidContent:(o=t.errorOnInvalidContent)!==null&&o!==void 0?o:s.options.enableContentCheck})}catch(p){return s.emit("contentError",{editor:s,error:p,disableCollaboration:()=>{s.storage.collaboration&&(s.storage.collaboration.isDisabled=!0)}}),!1}let{from:a,to:c}=typeof r=="number"?{from:r,to:r}:{from:r.from,to:r.to},f=!0,d=!0;if((Qc(l)?l:[l]).forEach(p=>{p.check(),f=f?p.isText&&p.marks.length===0:!1,d=d?p.isBlock:!1}),a===c&&d){const{parent:p}=n.doc.resolve(a);p.isTextblock&&!p.type.spec.code&&!p.childCount&&(a-=1,c+=1)}let h;if(f){if(Array.isArray(e))h=e.map(p=>p.text||"").join("");else if(e instanceof b){let p="";e.forEach(m=>{m.text&&(p+=m.text)}),h=p}else typeof e=="object"&&e&&e.text?h=e.text:h=e;n.insertText(h,a,c)}else h=l,n.replaceWith(a,c,h);t.updateSelection&&uo(n,n.steps.length-1,-1),t.applyInputRules&&n.setMeta("applyInputRules",{from:a,text:h}),t.applyPasteRules&&n.setMeta("applyPasteRules",{from:a,text:h})}return!0},tf=()=>({state:r,dispatch:e})=>fc(r,e),nf=()=>({state:r,dispatch:e})=>dc(r,e),rf=()=>({state:r,dispatch:e})=>js(r,e),sf=()=>({state:r,dispatch:e})=>Hs(r,e),of=()=>({state:r,dispatch:e,tr:t})=>{try{const n=rn(r.doc,r.selection.$from.pos,-1);return n==null?!1:(t.join(n,2),e&&e(t),!0)}catch{return!1}},lf=()=>({state:r,dispatch:e,tr:t})=>{try{const n=rn(r.doc,r.selection.$from.pos,1);return n==null?!1:(t.join(n,2),e&&e(t),!0)}catch{return!1}},af=()=>({state:r,dispatch:e})=>ac(r,e),cf=()=>({state:r,dispatch:e})=>cc(r,e);function Cr(){return typeof navigator<"u"?/Mac/.test(navigator.platform):!1}function ff(r){const e=r.split(/-(?!$)/);let t=e[e.length-1];t==="Space"&&(t=" ");let n,i,s,o;for(let l=0;l<e.length-1;l+=1){const a=e[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))n=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))s=!0;else if(/^mod$/i.test(a))hn()||Cr()?o=!0:i=!0;else throw new Error(`Unrecognized modifier name: ${a}`)}return n&&(t=`Alt-${t}`),i&&(t=`Ctrl-${t}`),o&&(t=`Meta-${t}`),s&&(t=`Shift-${t}`),t}const df=r=>({editor:e,view:t,tr:n,dispatch:i})=>{const s=ff(r).split(/-(?!$)/),o=s.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:o==="Space"?" ":o,altKey:s.includes("Alt"),ctrlKey:s.includes("Ctrl"),metaKey:s.includes("Meta"),shiftKey:s.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,l))});return a==null||a.steps.forEach(c=>{const f=c.map(n.mapping);f&&i&&n.maybeStep(f)}),!0};function tt(r,e,t={}){const{from:n,to:i,empty:s}=r.selection,o=e?v(e,r.schema):null,l=[];r.doc.nodesBetween(n,i,(d,u)=>{if(d.isText)return;const h=Math.max(n,u),p=Math.min(i,u+d.nodeSize);l.push({node:d,from:h,to:p})});const a=i-n,c=l.filter(d=>o?o.name===d.node.type.name:!0).filter(d=>xt(d.node.attrs,t,{strict:!1}));return s?!!c.length:c.reduce((d,u)=>d+u.to-u.from,0)>=a}const uf=(r,e={})=>({state:t,dispatch:n})=>{const i=v(r,t.schema);return tt(t,i,e)?uc(t,n):!1},hf=()=>({state:r,dispatch:e})=>Ys(r,e),pf=r=>({state:e,dispatch:t})=>{const n=v(r,e.schema);return wc(n)(e,t)},mf=()=>({state:r,dispatch:e})=>Gs(r,e);function At(r,e){return e.nodes[r]?"node":e.marks[r]?"mark":null}function Xn(r,e){const t=typeof e=="string"?[e]:e;return Object.keys(r).reduce((n,i)=>(t.includes(i)||(n[i]=r[i]),n),{})}const gf=(r,e)=>({tr:t,state:n,dispatch:i})=>{let s=null,o=null;const l=At(typeof r=="string"?r:r.name,n.schema);return l?(l==="node"&&(s=v(r,n.schema)),l==="mark"&&(o=ue(r,n.schema)),i&&t.selection.ranges.forEach(a=>{n.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,f)=>{s&&s===c.type&&t.setNodeMarkup(f,void 0,Xn(c.attrs,e)),o&&c.marks.length&&c.marks.forEach(d=>{o===d.type&&t.addMark(f,f+c.nodeSize,o.create(Xn(d.attrs,e)))})})}),!0):!1},yf=()=>({tr:r,dispatch:e})=>(e&&r.scrollIntoView(),!0),bf=()=>({tr:r,dispatch:e})=>{if(e){const t=new Y(r.doc);r.setSelection(t)}return!0},kf=()=>({state:r,dispatch:e})=>qs(r,e),xf=()=>({state:r,dispatch:e})=>Us(r,e),Sf=()=>({state:r,dispatch:e})=>gc(r,e),Mf=()=>({state:r,dispatch:e})=>kc(r,e),Cf=()=>({state:r,dispatch:e})=>bc(r,e);function Qt(r,e,t={},n={}){return St(r,e,{slice:!1,parseOptions:t,errorOnInvalidContent:n.errorOnInvalidContent})}const wf=(r,e=!1,t={},n={})=>({editor:i,tr:s,dispatch:o,commands:l})=>{var a,c;const{doc:f}=s;if(t.preserveWhitespace!=="full"){const d=Qt(r,i.schema,t,{errorOnInvalidContent:(a=n.errorOnInvalidContent)!==null&&a!==void 0?a:i.options.enableContentCheck});return o&&s.replaceWith(0,f.content.size,d).setMeta("preventUpdate",!e),!0}return o&&s.setMeta("preventUpdate",!e),l.insertContentAt({from:0,to:f.content.size},r,{parseOptions:t,errorOnInvalidContent:(c=n.errorOnInvalidContent)!==null&&c!==void 0?c:i.options.enableContentCheck})};function wr(r,e){const t=ue(e,r.schema),{from:n,to:i,empty:s}=r.selection,o=[];s?(r.storedMarks&&o.push(...r.storedMarks),o.push(...r.selection.$head.marks())):r.doc.nodesBetween(n,i,a=>{o.push(...a.marks)});const l=o.find(a=>a.type.name===t.name);return l?{...l.attrs}:{}}function ho(r){for(let e=0;e<r.edgeCount;e+=1){const{type:t}=r.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}function po(r,e){for(let t=r.depth;t>0;t-=1){const n=r.node(t);if(e(n))return{pos:t>0?r.before(t):0,start:r.start(t),depth:t,node:n}}}function pn(r){return e=>po(e.$from,r)}function mo(r,e){const t={from:0,to:r.content.size};return kr(r,t,e)}function go(r,e){const t=v(e,r.schema),{from:n,to:i}=r.selection,s=[];r.doc.nodesBetween(n,i,l=>{s.push(l)});const o=s.reverse().find(l=>l.type.name===t.name);return o?{...o.attrs}:{}}function yo(r,e){const t=At(typeof e=="string"?e:e.name,r.schema);return t==="node"?go(r,e):t==="mark"?wr(r,e):{}}function Or(r,e,t){const n=[];return r===e?t.resolve(r).marks().forEach(i=>{const s=t.resolve(r),o=un(s,i.type);o&&n.push({mark:i,...o})}):t.nodesBetween(r,e,(i,s)=>{!i||(i==null?void 0:i.nodeSize)===void 0||n.push(...i.marks.map(o=>({from:s,to:s+i.nodeSize,mark:o})))}),n}function ut(r,e,t){return Object.fromEntries(Object.entries(t).filter(([n])=>{const i=r.find(s=>s.type===e&&s.name===n);return i?i.attribute.keepOnSplit:!1}))}function en(r,e,t={}){const{empty:n,ranges:i}=r.selection,s=e?ue(e,r.schema):null;if(n)return!!(r.storedMarks||r.selection.$from.marks()).filter(d=>s?s.name===d.type.name:!0).find(d=>xt(d.attrs,t,{strict:!1}));let o=0;const l=[];if(i.forEach(({$from:d,$to:u})=>{const h=d.pos,p=u.pos;r.doc.nodesBetween(h,p,(m,g)=>{if(!m.isText&&!m.marks.length)return;const y=Math.max(h,g),M=Math.min(p,g+m.nodeSize),O=M-y;o+=O,l.push(...m.marks.map(I=>({mark:I,from:y,to:M})))})}),o===0)return!1;const a=l.filter(d=>s?s.name===d.mark.type.name:!0).filter(d=>xt(d.mark.attrs,t,{strict:!1})).reduce((d,u)=>d+u.to-u.from,0),c=l.filter(d=>s?d.mark.type!==s&&d.mark.type.excludes(s):!0).reduce((d,u)=>d+u.to-u.from,0);return(a>0?a+c:a)>=o}function bo(r,e,t={}){if(!e)return tt(r,null,t)||en(r,null,t);const n=At(e,r.schema);return n==="node"?tt(r,e,t):n==="mark"?en(r,e,t):!1}function Zn(r,e){const{nodeExtensions:t}=Tt(e),n=t.find(o=>o.name===r);if(!n)return!1;const i={name:n.name,options:n.options,storage:n.storage},s=C(x(n,"group",i));return typeof s!="string"?!1:s.split(" ").includes("list")}function mn(r,{checkChildren:e=!0,ignoreWhitespace:t=!1}={}){var n;if(t){if(r.type.name==="hardBreak")return!0;if(r.isText)return/^\s*$/m.test((n=r.text)!==null&&n!==void 0?n:"")}if(r.isText)return!r.text;if(r.isAtom||r.isLeaf)return!1;if(r.content.childCount===0)return!0;if(e){let i=!0;return r.content.forEach(s=>{i!==!1&&(mn(s,{ignoreWhitespace:t,checkChildren:e})||(i=!1))}),i}return!1}function Of(r){return r instanceof S}function Nf(r,e,t){var n;const{selection:i}=e;let s=null;if(Sr(i)&&(s=i.$cursor),s){const l=(n=r.storedMarks)!==null&&n!==void 0?n:s.marks();return!!t.isInSet(l)||!l.some(a=>a.type.excludes(t))}const{ranges:o}=i;return o.some(({$from:l,$to:a})=>{let c=l.depth===0?r.doc.inlineContent&&r.doc.type.allowsMarkType(t):!1;return r.doc.nodesBetween(l.pos,a.pos,(f,d,u)=>{if(c)return!1;if(f.isInline){const h=!u||u.type.allowsMarkType(t),p=!!t.isInSet(f.marks)||!f.marks.some(m=>m.type.excludes(t));c=h&&p}return!c}),c})}const Tf=(r,e={})=>({tr:t,state:n,dispatch:i})=>{const{selection:s}=t,{empty:o,ranges:l}=s,a=ue(r,n.schema);if(i)if(o){const c=wr(n,a);t.addStoredMark(a.create({...c,...e}))}else l.forEach(c=>{const f=c.$from.pos,d=c.$to.pos;n.doc.nodesBetween(f,d,(u,h)=>{const p=Math.max(h,f),m=Math.min(h+u.nodeSize,d);u.marks.find(y=>y.type===a)?u.marks.forEach(y=>{a===y.type&&t.addMark(p,m,a.create({...y.attrs,...e}))}):t.addMark(p,m,a.create(e))})});return Nf(n,t,a)},Ef=(r,e)=>({tr:t})=>(t.setMeta(r,e),!0),Df=(r,e={})=>({state:t,dispatch:n,chain:i})=>{const s=v(r,t.schema);let o;return t.selection.$anchor.sameParent(t.selection.$head)&&(o=t.selection.$anchor.parent.attrs),s.isTextblock?i().command(({commands:l})=>Oi(s,{...o,...e})(t)?!0:l.clearNodes()).command(({state:l})=>Oi(s,{...o,...e})(l,n)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},Af=r=>({tr:e,dispatch:t})=>{if(t){const{doc:n}=e,i=Se(r,0,n.content.size),s=S.create(n,i);e.setSelection(s)}return!0},If=r=>({tr:e,dispatch:t})=>{if(t){const{doc:n}=e,{from:i,to:s}=typeof r=="number"?{from:r,to:r}:r,o=w.atStart(n).from,l=w.atEnd(n).to,a=Se(i,o,l),c=Se(s,o,l),f=w.create(n,a,c);e.setSelection(f)}return!0},Rf=r=>({state:e,dispatch:t})=>{const n=v(r,e.schema);return Tc(n)(e,t)};function Ei(r,e){const t=r.storedMarks||r.selection.$to.parentOffset&&r.selection.$from.marks();if(t){const n=t.filter(i=>e==null?void 0:e.includes(i.type.name));r.tr.ensureMarks(n)}}const vf=({keepMarks:r=!0}={})=>({tr:e,state:t,dispatch:n,editor:i})=>{const{selection:s,doc:o}=e,{$from:l,$to:a}=s,c=i.extensionManager.attributes,f=ut(c,l.node().type.name,l.node().attrs);if(s instanceof S&&s.node.isBlock)return!l.parentOffset||!fe(o,l.pos)?!1:(n&&(r&&Ei(t,i.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;const d=a.parentOffset===a.parent.content.size,u=l.depth===0?void 0:ho(l.node(-1).contentMatchAt(l.indexAfter(-1)));let h=d&&u?[{type:u,attrs:f}]:void 0,p=fe(e.doc,e.mapping.map(l.pos),1,h);if(!h&&!p&&fe(e.doc,e.mapping.map(l.pos),1,u?[{type:u}]:void 0)&&(p=!0,h=u?[{type:u,attrs:f}]:void 0),n){if(p&&(s instanceof w&&e.deleteSelection(),e.split(e.mapping.map(l.pos),1,h),u&&!d&&!l.parentOffset&&l.parent.type!==u)){const m=e.mapping.map(l.before()),g=e.doc.resolve(m);l.node(-1).canReplaceWith(g.index(),g.index()+1,u)&&e.setNodeMarkup(e.mapping.map(l.before()),u)}r&&Ei(t,i.extensionManager.splittableMarks),e.scrollIntoView()}return p},Pf=(r,e={})=>({tr:t,state:n,dispatch:i,editor:s})=>{var o;const l=v(r,n.schema),{$from:a,$to:c}=n.selection,f=n.selection.node;if(f&&f.isBlock||a.depth<2||!a.sameParent(c))return!1;const d=a.node(-1);if(d.type!==l)return!1;const u=s.extensionManager.attributes;if(a.parent.content.size===0&&a.node(-1).childCount===a.indexAfter(-1)){if(a.depth===2||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(i){let y=b.empty;const M=a.index(-1)?1:a.index(-2)?2:3;for(let J=a.depth-M;J>=a.depth-3;J-=1)y=b.from(a.node(J).copy(y));const O=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,I={...ut(u,a.node().type.name,a.node().attrs),...e},E=((o=l.contentMatch.defaultType)===null||o===void 0?void 0:o.createAndFill(I))||void 0;y=y.append(b.from(l.createAndFill(null,E)||void 0));const D=a.before(a.depth-(M-1));t.replace(D,a.after(-O),new k(y,4-M,0));let U=-1;t.doc.nodesBetween(D,t.doc.content.size,(J,T)=>{if(U>-1)return!1;J.isTextblock&&J.content.size===0&&(U=T+1)}),U>-1&&t.setSelection(w.near(t.doc.resolve(U))),t.scrollIntoView()}return!0}const h=c.pos===a.end()?d.contentMatchAt(0).defaultType:null,p={...ut(u,d.type.name,d.attrs),...e},m={...ut(u,a.node().type.name,a.node().attrs),...e};t.delete(a.pos,c.pos);const g=h?[{type:l,attrs:p},{type:h,attrs:m}]:[{type:l,attrs:p}];if(!fe(t.doc,a.pos,2))return!1;if(i){const{selection:y,storedMarks:M}=n,{splittableMarks:O}=s.extensionManager,I=M||y.$to.parentOffset&&y.$from.marks();if(t.split(a.pos,2,g).scrollIntoView(),!I||!i)return!0;const E=I.filter(D=>O.includes(D.type.name));t.ensureMarks(E)}return!0},In=(r,e)=>{const t=pn(o=>o.type===e)(r.selection);if(!t)return!0;const n=r.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(n===void 0)return!0;const i=r.doc.nodeAt(n);return t.node.type===(i==null?void 0:i.type)&&Ee(r.doc,t.pos)&&r.join(t.pos),!0},Rn=(r,e)=>{const t=pn(o=>o.type===e)(r.selection);if(!t)return!0;const n=r.doc.resolve(t.start).after(t.depth);if(n===void 0)return!0;const i=r.doc.nodeAt(n);return t.node.type===(i==null?void 0:i.type)&&Ee(r.doc,n)&&r.join(n),!0},Bf=(r,e,t,n={})=>({editor:i,tr:s,state:o,dispatch:l,chain:a,commands:c,can:f})=>{const{extensions:d,splittableMarks:u}=i.extensionManager,h=v(r,o.schema),p=v(e,o.schema),{selection:m,storedMarks:g}=o,{$from:y,$to:M}=m,O=y.blockRange(M),I=g||m.$to.parentOffset&&m.$from.marks();if(!O)return!1;const E=pn(D=>Zn(D.type.name,d))(m);if(O.depth>=1&&E&&O.depth-E.depth<=1){if(E.node.type===h)return c.liftListItem(p);if(Zn(E.node.type.name,d)&&h.validContent(E.node.content)&&l)return a().command(()=>(s.setNodeMarkup(E.pos,h),!0)).command(()=>In(s,h)).command(()=>Rn(s,h)).run()}return!t||!I||!l?a().command(()=>f().wrapInList(h,n)?!0:c.clearNodes()).wrapInList(h,n).command(()=>In(s,h)).command(()=>Rn(s,h)).run():a().command(()=>{const D=f().wrapInList(h,n),U=I.filter(J=>u.includes(J.type.name));return s.ensureMarks(U),D?!0:c.clearNodes()}).wrapInList(h,n).command(()=>In(s,h)).command(()=>Rn(s,h)).run()},zf=(r,e={},t={})=>({state:n,commands:i})=>{const{extendEmptyMarkRange:s=!1}=t,o=ue(r,n.schema);return en(n,o,e)?i.unsetMark(o,{extendEmptyMarkRange:s}):i.setMark(o,e)},Ff=(r,e,t={})=>({state:n,commands:i})=>{const s=v(r,n.schema),o=v(e,n.schema),l=tt(n,s,t);let a;return n.selection.$anchor.sameParent(n.selection.$head)&&(a=n.selection.$anchor.parent.attrs),l?i.setNode(o,a):i.setNode(s,{...a,...t})},Vf=(r,e={})=>({state:t,commands:n})=>{const i=v(r,t.schema);return tt(t,i,e)?n.lift(i):n.wrapIn(i,e)},$f=()=>({state:r,dispatch:e})=>{const t=r.plugins;for(let n=0;n<t.length;n+=1){const i=t[n];let s;if(i.spec.isInputRules&&(s=i.getState(r))){if(e){const o=r.tr,l=s.transform;for(let a=l.steps.length-1;a>=0;a-=1)o.step(l.steps[a].invert(l.docs[a]));if(s.text){const a=o.doc.resolve(s.from).marks();o.replaceWith(s.from,s.to,r.schema.text(s.text,a))}else o.delete(s.from,s.to)}return!0}}return!1},Lf=()=>({tr:r,dispatch:e})=>{const{selection:t}=r,{empty:n,ranges:i}=t;return n||e&&i.forEach(s=>{r.removeMark(s.$from.pos,s.$to.pos)}),!0},Wf=(r,e={})=>({tr:t,state:n,dispatch:i})=>{var s;const{extendEmptyMarkRange:o=!1}=e,{selection:l}=t,a=ue(r,n.schema),{$from:c,empty:f,ranges:d}=l;if(!i)return!0;if(f&&o){let{from:u,to:h}=l;const p=(s=c.marks().find(g=>g.type===a))===null||s===void 0?void 0:s.attrs,m=un(c,a,p);m&&(u=m.from,h=m.to),t.removeMark(u,h,a)}else d.forEach(u=>{t.removeMark(u.$from.pos,u.$to.pos,a)});return t.removeStoredMark(a),!0},jf=(r,e={})=>({tr:t,state:n,dispatch:i})=>{let s=null,o=null;const l=At(typeof r=="string"?r:r.name,n.schema);return l?(l==="node"&&(s=v(r,n.schema)),l==="mark"&&(o=ue(r,n.schema)),i&&t.selection.ranges.forEach(a=>{const c=a.$from.pos,f=a.$to.pos;let d,u,h,p;t.selection.empty?n.doc.nodesBetween(c,f,(m,g)=>{s&&s===m.type&&(h=Math.max(g,c),p=Math.min(g+m.nodeSize,f),d=g,u=m)}):n.doc.nodesBetween(c,f,(m,g)=>{g<c&&s&&s===m.type&&(h=Math.max(g,c),p=Math.min(g+m.nodeSize,f),d=g,u=m),g>=c&&g<=f&&(s&&s===m.type&&t.setNodeMarkup(g,void 0,{...m.attrs,...e}),o&&m.marks.length&&m.marks.forEach(y=>{if(o===y.type){const M=Math.max(g,c),O=Math.min(g+m.nodeSize,f);t.addMark(M,O,o.create({...y.attrs,...e}))}}))}),u&&(d!==void 0&&t.setNodeMarkup(d,void 0,{...u.attrs,...e}),o&&u.marks.length&&u.marks.forEach(m=>{o===m.type&&t.addMark(h,p,o.create({...m.attrs,...e}))}))}),!0):!1},Jf=(r,e={})=>({state:t,dispatch:n})=>{const i=v(r,t.schema);return xc(i,e)(t,n)},qf=(r,e={})=>({state:t,dispatch:n})=>{const i=v(r,t.schema);return Sc(i,e)(t,n)};var Kf=Object.freeze({__proto__:null,blur:Bc,clearContent:zc,clearNodes:Fc,command:Vc,createParagraphNear:$c,cut:Lc,deleteCurrentNode:Wc,deleteNode:jc,deleteRange:Jc,deleteSelection:qc,enter:Kc,exitCode:Hc,extendMarkRange:Uc,first:Gc,focus:Yc,forEach:Xc,insertContent:Zc,insertContentAt:ef,joinBackward:rf,joinDown:nf,joinForward:sf,joinItemBackward:of,joinItemForward:lf,joinTextblockBackward:af,joinTextblockForward:cf,joinUp:tf,keyboardShortcut:df,lift:uf,liftEmptyBlock:hf,liftListItem:pf,newlineInCode:mf,resetAttributes:gf,scrollIntoView:yf,selectAll:bf,selectNodeBackward:kf,selectNodeForward:xf,selectParentNode:Sf,selectTextblockEnd:Mf,selectTextblockStart:Cf,setContent:wf,setMark:Tf,setMeta:Ef,setNode:Df,setNodeSelection:Af,setTextSelection:If,sinkListItem:Rf,splitBlock:vf,splitListItem:Pf,toggleList:Bf,toggleMark:zf,toggleNode:Ff,toggleWrap:Vf,undoInputRule:$f,unsetAllMarks:Lf,unsetMark:Wf,updateAttributes:jf,wrapIn:Jf,wrapInList:qf});const Hf=ee.create({name:"commands",addCommands(){return{...Kf}}}),Uf=ee.create({name:"drop",addProseMirrorPlugins(){return[new oe({key:new je("tiptapDrop"),props:{handleDrop:(r,e,t,n)=>{this.editor.emit("drop",{editor:this.editor,event:e,slice:t,moved:n})}}})]}}),Gf=ee.create({name:"editable",addProseMirrorPlugins(){return[new oe({key:new je("editable"),props:{editable:()=>this.editor.options.editable}})]}}),_f=new je("focusEvents"),Yf=ee.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:r}=this;return[new oe({key:_f,props:{handleDOMEvents:{focus:(e,t)=>{r.isFocused=!0;const n=r.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(n),!1},blur:(e,t)=>{r.isFocused=!1;const n=r.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(n),!1}}}})]}}),Xf=ee.create({name:"keymap",addKeyboardShortcuts(){const r=()=>this.editor.commands.first(({commands:o})=>[()=>o.undoInputRule(),()=>o.command(({tr:l})=>{const{selection:a,doc:c}=l,{empty:f,$anchor:d}=a,{pos:u,parent:h}=d,p=d.parent.isTextblock&&u>0?l.doc.resolve(u-1):d,m=p.parent.type.spec.isolating,g=d.pos-d.parentOffset,y=m&&p.parent.childCount===1?g===d.pos:N.atStart(c).from===u;return!f||!h.type.isTextblock||h.textContent.length||!y||y&&d.parent.type.name==="paragraph"?!1:o.clearNodes()}),()=>o.deleteSelection(),()=>o.joinBackward(),()=>o.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:o})=>[()=>o.deleteSelection(),()=>o.deleteCurrentNode(),()=>o.joinForward(),()=>o.selectNodeForward()]),n={Enter:()=>this.editor.commands.first(({commands:o})=>[()=>o.newlineInCode(),()=>o.createParagraphNear(),()=>o.liftEmptyBlock(),()=>o.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:r,"Mod-Backspace":r,"Shift-Backspace":r,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},i={...n},s={...n,"Ctrl-h":r,"Alt-Backspace":r,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return hn()||Cr()?s:i},addProseMirrorPlugins(){return[new oe({key:new je("clearDocument"),appendTransaction:(r,e,t)=>{if(r.some(m=>m.getMeta("composition")))return;const n=r.some(m=>m.docChanged)&&!e.doc.eq(t.doc),i=r.some(m=>m.getMeta("preventClearDocument"));if(!n||i)return;const{empty:s,from:o,to:l}=e.selection,a=N.atStart(e.doc).from,c=N.atEnd(e.doc).to;if(s||!(o===a&&l===c)||!mn(t.doc))return;const u=t.tr,h=Ot({state:t,transaction:u}),{commands:p}=new Nt({editor:this.editor,state:h});if(p.clearNodes(),!!u.steps.length)return u}})]}}),Zf=ee.create({name:"paste",addProseMirrorPlugins(){return[new oe({key:new je("tiptapPaste"),props:{handlePaste:(r,e,t)=>{this.editor.emit("paste",{editor:this.editor,event:e,slice:t})}}})]}}),Qf=ee.create({name:"tabindex",addProseMirrorPlugins(){return[new oe({key:new je("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class ye{get name(){return this.node.type.name}constructor(e,t,n=!1,i=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=i}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!==null&&e!==void 0?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(this.content.size===0){console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);return}t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;const e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new ye(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new ye(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new ye(e,this.editor)}get children(){const e=[];return this.node.content.forEach((t,n)=>{const i=t.isBlock&&!t.isTextblock,s=t.isAtom&&!t.isText,o=this.pos+n+(s?0:1),l=this.resolvedPos.doc.resolve(o);if(!i&&l.depth<=this.depth)return;const a=new ye(l,this.editor,i,i?t:null);i&&(a.actualDepth=this.depth+1),e.push(new ye(l,this.editor,i,i?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){const e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,i=this.parent;for(;i&&!n;){if(i.node.type.name===e)if(Object.keys(t).length>0){const s=i.node.attrs,o=Object.keys(t);for(let l=0;l<o.length;l+=1){const a=o[l];if(s[a]!==t[a])break}}else n=i;i=i.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let i=[];if(!this.children||this.children.length===0)return i;const s=Object.keys(t);return this.children.forEach(o=>{n&&i.length>0||(o.node.type.name===e&&s.every(a=>t[a]===o.node.attrs[a])&&i.push(o),!(n&&i.length>0)&&(i=i.concat(o.querySelectorAll(e,t,n))))}),i}setAttribute(e){const{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}}const ed=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;function ko(r,e,t){const n=document.querySelector("style[data-tiptap-style]");if(n!==null)return n;const i=document.createElement("style");return e&&i.setAttribute("nonce",e),i.setAttribute("data-tiptap-style",""),i.innerHTML=r,document.getElementsByTagName("head")[0].appendChild(i),i}class td extends Ec{constructor(e={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:t})=>{throw t},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:t,slice:n,moved:i})=>this.options.onDrop(t,n,i)),this.on("paste",({event:t,slice:n})=>this.options.onPaste(t,n)),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=ko(ed,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},!(!this.view||!this.state||this.isDestroyed)&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){const n=br(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],i=this.state.reconfigure({plugins:n});return this.view.updateState(i),i}unregisterPlugin(e){if(this.isDestroyed)return;const t=this.state.plugins;let n=t;if([].concat(e).forEach(s=>{const o=typeof s=="string"?`${s}$`:s.key;n=n.filter(l=>!l.key.startsWith(o))}),t.length===n.length)return;const i=this.state.reconfigure({plugins:n});return this.view.updateState(i),i}createExtensionManager(){var e,t;const i=[...this.options.enableCoreExtensions?[Gf,Pc.configure({blockSeparator:(t=(e=this.options.coreExtensionOptions)===null||e===void 0?void 0:e.clipboardTextSerializer)===null||t===void 0?void 0:t.blockSeparator}),Hf,Yf,Xf,Qf,Uf,Zf].filter(s=>typeof this.options.enableCoreExtensions=="object"?this.options.enableCoreExtensions[s.name]!==!1:!0):[],...this.options.extensions].filter(s=>["extension","node","mark"].includes(s==null?void 0:s.type));this.extensionManager=new Ge(i,this)}createCommandManager(){this.commandManager=new Nt({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var e;let t;try{t=Qt(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(o){if(!(o instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(o.message))throw o;this.emit("contentError",{editor:this,error:o,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(l=>l.name!=="collaboration"),this.createExtensionManager()}}),t=Qt(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}const n=Mr(t,this.options.autofocus);this.view=new Ya(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...(e=this.options.editorProps)===null||e===void 0?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:Ue.create({doc:t,selection:n||void 0})});const i=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(i),this.createNodeViews(),this.prependClass();const s=this.view.dom;s.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(o=>{var l;return(l=this.capturedTransaction)===null||l===void 0?void 0:l.step(o)});return}const t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});const i=e.getMeta("focus"),s=e.getMeta("blur");i&&this.emit("focus",{editor:this,event:i.event,transaction:e}),s&&this.emit("blur",{editor:this,event:s.event,transaction:e}),!(!e.docChanged||e.getMeta("preventUpdate"))&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return yo(this.state,e)}isActive(e,t){const n=typeof e=="string"?e:null,i=typeof e=="string"?t:e;return bo(this.state,n,i)}getJSON(){return this.state.doc.toJSON()}getHTML(){return fn(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t=`

`,textSerializers:n={}}=e||{};return mo(this.state.doc,{blockSeparator:t,textSerializers:{...xr(this.schema),...n}})}get isEmpty(){return mn(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){const e=this.view.dom;e&&e.editor&&delete e.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var e;return!(!((e=this.view)===null||e===void 0)&&e.docView)}$node(e,t){var n;return((n=this.$doc)===null||n===void 0?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return((n=this.$doc)===null||n===void 0?void 0:n.querySelectorAll(e,t))||null}$pos(e){const t=this.state.doc.resolve(e);return new ye(t,this)}get $doc(){return this.$pos(0)}}function nd(r){return new Et({find:r.find,handler:({state:e,range:t,match:n})=>{const i=C(r.getAttributes,void 0,n);if(i===!1||i===null)return null;const{tr:s}=e,o=n[n.length-1],l=n[0];if(o){const a=l.search(/\S/),c=t.from+l.indexOf(o),f=c+o.length;if(Or(t.from,t.to,e.doc).filter(h=>h.mark.type.excluded.find(m=>m===r.type&&m!==h.mark.type)).filter(h=>h.to>c).length)return null;f<t.to&&s.delete(f,t.to),c>t.from&&s.delete(t.from+a,c);const u=t.from+a+o.length;s.addMark(t.from+a,u,r.type.create(i||{})),s.removeStoredMark(r.type)}}})}function rd(r){return new Et({find:r.find,handler:({state:e,range:t,match:n})=>{const i=C(r.getAttributes,void 0,n)||{},{tr:s}=e,o=t.from;let l=t.to;const a=r.type.create(i);if(n[1]){const c=n[0].lastIndexOf(n[1]);let f=o+c;f>l?f=l:l=f+n[1].length;const d=n[0][n[0].length-1];s.insertText(d,o+n[0].length-1),s.replaceWith(f,l,a)}else if(n[0]){const c=r.type.isInline?o:o-1;s.insert(c,r.type.create(i)).delete(s.mapping.map(o),s.mapping.map(l))}s.scrollIntoView()}})}function id(r){return new Et({find:r.find,handler:({state:e,range:t,match:n})=>{const i=e.doc.resolve(t.from),s=C(r.getAttributes,void 0,n)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),r.type))return null;e.tr.delete(t.from,t.to).setBlockType(t.from,t.from,r.type,s)}})}function sd(r){return new Et({find:r.find,handler:({state:e,range:t,match:n,chain:i})=>{const s=C(r.getAttributes,void 0,n)||{},o=e.tr.delete(t.from,t.to),a=o.doc.resolve(t.from).blockRange(),c=a&&tr(a,r.type,s);if(!c)return null;if(o.wrap(a,c),r.keepMarks&&r.editor){const{selection:d,storedMarks:u}=e,{splittableMarks:h}=r.editor.extensionManager,p=u||d.$to.parentOffset&&d.$from.marks();if(p){const m=p.filter(g=>h.includes(g.type.name));o.ensureMarks(m)}}if(r.keepAttributes){const d=r.type.name==="bulletList"||r.type.name==="orderedList"?"listItem":"taskList";i().updateAttributes(d,s).run()}const f=o.doc.resolve(t.from-1).nodeBefore;f&&f.type===r.type&&Ee(o.doc,t.from-1)&&(!r.joinPredicate||r.joinPredicate(n,f))&&o.join(t.from-1)}})}class tn{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=C(x(this,"addOptions",{name:this.name}))),this.storage=C(x(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new tn(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>Dt(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new tn(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=C(x(t,"addOptions",{name:t.name})),t.storage=C(x(t,"addStorage",{name:t.name,options:t.options})),t}}function od(r){return new oo({find:r.find,handler:({state:e,range:t,match:n,pasteEvent:i})=>{const s=C(r.getAttributes,void 0,n,i);if(s===!1||s===null)return null;const{tr:o}=e,l=n[n.length-1],a=n[0];let c=t.to;if(l){const f=a.search(/\S/),d=t.from+a.indexOf(l),u=d+l.length;if(Or(t.from,t.to,e.doc).filter(p=>p.mark.type.excluded.find(g=>g===r.type&&g!==p.mark.type)).filter(p=>p.to>d).length)return null;u<t.to&&o.delete(u,t.to),d>t.from&&o.delete(t.from+f,d),c=t.from+f+l.length,o.addMark(t.from+f,c,r.type.create(s||{})),o.removeStoredMark(r.type)}}})}const ld=Object.freeze(Object.defineProperty({__proto__:null,CommandManager:Nt,Editor:td,Extension:ee,InputRule:Et,Mark:kt,Node:tn,NodePos:ye,PasteRule:oo,callOrReturn:C,createChainableState:Ot,createDocument:Qt,createNodeFromContent:St,createStyleTag:ko,defaultBlockAt:ho,deleteProps:Xn,elementFromString:at,findDuplicates:ao,findParentNode:pn,findParentNodeClosestToPos:po,fromString:to,getAttributes:yo,getAttributesFromExtensions:yr,getExtensionField:x,getHTMLFromFragment:fn,getMarkAttributes:wr,getMarkRange:un,getMarkType:ue,getMarksBetween:Or,getNodeAttributes:go,getNodeType:v,getRenderedAttributes:Zt,getSchemaByResolvedExtensions:no,getSchemaTypeByName:$t,getSchemaTypeNameByName:At,getSplittedAttributes:ut,getText:mo,getTextBetween:kr,getTextContentFromNodes:ro,getTextSerializersFromSchema:xr,injectExtensionAttributesToParseRule:_n,inputRulesPlugin:io,isActive:bo,isEmptyObject:eo,isExtensionRulesEnabled:Yn,isFunction:br,isList:Zn,isMacOS:Cr,isMarkActive:en,isNodeActive:tt,isNodeEmpty:mn,isNodeSelection:Of,isNumber:so,isPlainObject:lt,isRegExp:dn,isTextSelection:Sr,isiOS:hn,markInputRule:nd,markPasteRule:od,mergeAttributes:Qs,mergeDeep:Dt,minMax:Se,nodeInputRule:rd,objectIncludes:xt,pasteRulesPlugin:lo,resolveFocusPosition:Mr,selectionToInsertionEnd:uo,splitExtensions:Tt,textblockTypeInputRule:id,wrappingInputRule:sd},Symbol.toStringTag,{value:"Module"}));export{P as D,ee as E,b as F,kt as M,tn as N,oe as P,N as S,w as T,nd as a,Qs as b,je as c,ol as d,k as e,S as f,te as g,C as h,x as i,Kt as j,lc as k,Of as l,od as m,rd as n,ld as o,id as t,sd as w};
