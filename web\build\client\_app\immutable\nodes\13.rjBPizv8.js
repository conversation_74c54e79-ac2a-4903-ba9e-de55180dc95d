import{f as c,a,t as h}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as j,s as p,f as A,a as B,g as r,m as d,c as g,n as _,d as o,r as b,t as C,e as F}from"../chunks/CGmarHxI.js";import{s as O}from"../chunks/CIt1g2O9.js";import{i as T}from"../chunks/u21ee2wt.js";import{e as D}from"../chunks/CmxjS0TN.js";import{p as J}from"../chunks/CWmzcjye.js";import{i as N}from"../chunks/BIEMS98f.js";import{I as R}from"../chunks/DMTMHyMa.js";import{B as z}from"../chunks/B1K98fMG.js";import{L as G}from"../chunks/BvvicRXk.js";import{t as x}from"../chunks/DjPYYl4Z.js";var H=c('<p class="text-muted-foreground">Check your inbox for a password reset link.</p>'),K=c('<form class="max-w-md space-y-4"><div><!> <!></div> <!></form>'),M=c('<h1 class="mb-6 text-2xl font-semibold">Forgot Password</h1> <!>',1);function oe(y,w){j(w,!1);let i=d(""),n=d(!1),m=d(!1);async function $(){o(n,!0),o(m,!1);try{const e=await fetch("/api/auth/forgot-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r(i)})});if(!e.ok){const t=await e.json();throw new Error((t==null?void 0:t.error)||"Failed to request password reset")}o(m,!0),x.success("Success",{description:"If an account exists, a reset link has been sent."})}catch(e){x.error("Error",{description:e instanceof Error?e.message:"An error occurred"})}finally{o(n,!1)}}N();var u=M(),k=p(A(u),2);{var S=e=>{var t=H();a(e,t)},E=e=>{var t=K(),f=g(t),v=g(f);G(v,{for:"email",children:(s,I)=>{_();var l=h("Email Address");a(s,l)},$$slots:{default:!0}});var L=p(v,2);R(L,{id:"email",type:"email",required:!0,get value(){return r(i)},set value(s){o(i,s)},$$legacy:!0}),b(f);var P=p(f,2);const q=F(()=>r(n)||!r(i));z(P,{type:"submit",get disabled(){return r(q)},children:(s,I)=>{_();var l=h();C(()=>O(l,r(n)?"Sending...":"Send Reset Link")),a(s,l)},$$slots:{default:!0}}),b(t),D("submit",t,J($)),a(e,t)};T(k,e=>{r(m)?e(S):e(E,!1)})}a(y,u),B()}export{oe as component};
