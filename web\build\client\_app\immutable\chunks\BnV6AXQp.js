import{c as p,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as m}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l,s as c}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function B(s,o){const t=l(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z"}]];f(s,c({name:"badge"},()=>t,{get iconNode(){return a},children:(e,$)=>{var r=p(),n=m(r);d(n,o,"default",{},null),i(e,r)},$$slots:{default:!0}}))}export{B};
