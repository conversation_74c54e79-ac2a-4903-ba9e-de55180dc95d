import{f as pe,a as fe}from"../chunks/BasJTneF.js";import{p as ge,k as p,v as k,g as t,d as s,a_ as ye,i as be,f as me,a as we,s as K}from"../chunks/CGmarHxI.js";import{S as je}from"../chunks/C6g8ubaU.js";import{g as _}from"../chunks/BiJhC7W5.js";import"../chunks/CgXBgsce.js";import{t as w}from"../chunks/DjPYYl4Z.js";import{b as Q}from"../chunks/Cf6rS4LV.js";import{J as Pe,a as Se}from"../chunks/DV_57wcZ.js";import{g as ve,J as Te}from"../chunks/9r-6KH_O.js";import{d as C}from"../chunks/ncUU1dSD.js";async function Ue(P){var l;try{if(!P.title&&!P.location&&!((l=P.companies)!=null&&l.length))throw new Error("At least one search parameter is required");const g=await fetch("/api/jobs/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(P)}),d=await g.json();return g.ok?{searchId:d.searchId||null,results:d.results||[],saved:d.saved||!1}:(console.error("Job search error:",d.error),{results:[],error:d.error||"Failed to search jobs",limitReached:d.limitReached||!1})}catch(g){return console.error("Error searching jobs:",g),{results:[],error:g instanceof Error?g.message:"Unknown error occurred"}}}var Je=pe("<!> <!> <!>",1);function Fe(P,l){var Y,z,G;ge(l,!0);let g=p(!1),d=p(k([])),A=p(!1),U=p(null),O=p(0),v=p(k([])),S=p(k([])),J=p(!1),x=p(!1),j=p(0);const F=2e3;let L=p(0);const W=2e3;let a=p(k({...l.data.searchParams,locations:(Y=l.data.searchParams)!=null&&Y.location?[l.data.searchParams.location]:[],locationType:((z=l.data.searchParams)==null?void 0:z.locationType)||[],experience:((G=l.data.searchParams)==null?void 0:G.experience)||[],category:[],education:[]}));console.log("Initializing jobs page component"),N(!0),te(),Object.values(t(a)).some(e=>e&&(!Array.isArray(e)||e.length>0))?(console.log("Loading initial jobs with search parameters"),E(t(a)),s(j,Date.now(),!0),console.log("Updating URL from initial parameters"),R(t(a)),s(L,Date.now(),!0)):(console.log("Loading initial jobs with no search parameters"),E(),s(j,Date.now(),!0)),console.log("Initial job load complete"),console.log("Setting up event listeners"),window.addEventListener("popstate",B),window.addEventListener("urlparamsupdated",M),ye(()=>()=>{window.removeEventListener("popstate",B),window.removeEventListener("urlparamsupdated",M)});async function X(e){var r;Object.keys(e).forEach(o=>{t(a)[o]=e[o]}),t(a).title||(t(a).title=""),t(a).locations||(t(a).locations=[]),t(a).locationType||(t(a).locationType=[]),t(a).experience||(t(a).experience=[]),t(a).salary||(t(a).salary=""),s(g,!0);try{if(ie(t(a)),!e.saveSearch||!e.title||!l.data.user)return;let o=null;if(((r=e.locations)==null?void 0:r.length)>0)try{const n=e.locations[0];if(n.includes("|")){const[c,i,y]=n.split("|");o=`${i}, ${y}`}else o=n}catch(n){console.error("Error extracting location:",n)}if(!l.data.user.id)return;if(e.saveSearch)try{const n={title:e.title,location:o,locationType:e.locationType||[],experience:e.experience||[],category:e.category||[],education:e.education||[],salary:e.salary||"",saveSearch:!0},c=await Ue(n);if(console.log("Search completed successfully"),c.error){c.limitReached?(w.error("You have reached your search limit"),console.warn("Search limit reached")):console.warn("Failed to perform search:",c.error);return}c.saved&&c.searchId&&w.success("Search saved as job alert",{description:"View your saved search",action:{label:"View",onClick:()=>_(`/dashboard/jobs/${c.searchId}`)}})}catch(n){console.warn("Error performing search:",n)}}catch(o){console.error("Search error:",o),w.error("Failed to complete search",{description:"Please try again later"})}finally{s(g,!1)}}function R(e){var i,y,b,m;const r=new URL(window.location.href),o=new URLSearchParams,h=["title","locations","locationType","experience","salary","state","country","datePosted","easyApply","companies"],n=new URLSearchParams(r.search);for(const[f,u]of n.entries())h.includes(f)||o.append(f,u);if(e.title&&o.set("title",e.title),(i=e.locations)!=null&&i.length){const f=e.locations.map(u=>typeof u=="string"?u:u.id?u.id:u);o.set("locations",f.join(","))}(y=e.locationType)!=null&&y.length&&o.set("locationType",e.locationType.join(",")),(b=e.experience)!=null&&b.length&&o.set("experience",e.experience.join(",")),e.salary&&o.set("salary",e.salary),e.datePosted&&o.set("datePosted",e.datePosted),e.easyApply===!0&&o.set("easyApply","true"),(m=e.companies)!=null&&m.length&&o.set("companies",e.companies.join(",")),e.state&&o.set("state",e.state),e.country&&o.set("country",e.country);const c=`${r.origin}${r.pathname}?${o.toString()}`;window.history.pushState({},"",c),window.dispatchEvent(new CustomEvent("urlparamsupdated",{detail:{params:Object.fromEntries(o.entries())}}))}async function E(e={}){var r,o,h,n,c;if(t(J))return console.log("Job loading already in progress, skipping duplicate request"),[];s(J,!0),s(A,!0);try{const i={limit:20},y=e.title&&e.title.trim()!==""||e.locations&&e.locations.length>0||e.locationType&&e.locationType.length>0||e.experience&&e.experience.length>0||e.salary&&e.salary!=="";if(Object.keys(e).length===0&&(s(a,{title:"",locations:[],locationType:[],experience:[],category:[],education:[],salary:"",state:"",country:"US",location:"",datePosted:"",easyApply:!1,companies:[]},!0),R(t(a))),y){if(e.title&&e.title.trim()!==""&&(i.title=e.title.trim()),(r=e.locations)!=null&&r.length)try{const f=e.locations.map(u=>{if(u.includes("|")){const[le,de,ue,he]=u.split("|");return{id:le,name:de,stateCode:ue,country:he}}else return{id:u,name:u,stateCode:"",country:"US"}});i.locations=f.map(u=>u.id),f.length>0&&(i.location=`${f[0].name}, ${f[0].stateCode}`)}catch(f){console.error("Error parsing locations:",f)}else e.location&&(i.location=e.location);(o=e.locationType)!=null&&o.length&&(i.locationType=e.locationType),(h=e.experience)!=null&&h.length&&(i.experienceLevel=e.experience),e.salary&&(i.salary=e.salary),e.state&&(i.state=e.state),e.country&&(i.country=e.country)}const b=await ve(Te,{filter:i});if(b.errors)throw new Error(`Failed to fetch jobs: ${b.errors[0].message}`);const m=(n=b.data)==null?void 0:n.jobListings;s(d,(m==null?void 0:m.jobs)||[],!0),s(O,((c=m==null?void 0:m.pagination)==null?void 0:c.totalCount)||0,!0),t(d).length>0&&!t(U)&&s(U,t(d)[0],!0),t(d).length===0&&y&&w.info("No jobs found with these filters",{description:"Try adjusting your search criteria"})}catch(i){console.error("Error loading jobs:",i),w.error("Failed to load jobs")}finally{s(A,!1),s(J,!1)}}async function Z(){return[]}async function $(e){if(t(S).includes(e.id)){_("/dashboard/tracker");return}try{const r=await fetch(`/api/jobs/${e.id}/apply`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})});if(!r.ok){const o=await r.json();throw new Error(o.error||"Failed to apply to job")}s(S,[...t(S),e.id],!0),w.success("Application started",{description:"Tracking this application in your dashboard"}),setTimeout(()=>{_("/dashboard/tracker")},1500)}catch(r){console.error("Error applying to job:",r),w.error("Failed to apply to job")}}async function ee(e){if(!l.data.user){window.location.href="/auth/sign-in";return}try{const r=await fetch(`/api/jobs/${e.id}/save`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({notes:""})});if(!r.ok){const o=await r.json();throw new Error(o.error||"Failed to save job")}t(v).includes(e.id)||s(v,[...t(v),e.id],!0),w.success("Job saved",{description:"Added to your saved jobs"})}catch(r){console.error("Error saving job:",r),w.error("Failed to save job")}}async function te(){if(l.data.user)try{const e=await fetch("/api/jobs/saved");if(!e.ok)throw new Error("Failed to fetch saved jobs");const r=await e.json();r.savedJobs&&Array.isArray(r.savedJobs)&&s(v,r.savedJobs.map(o=>o.jobId),!0)}catch(e){console.error("Error checking saved jobs:",e)}}let T=null,D=0;const oe=6e4;async function re(){if(!l.data.user)return;const e=Date.now();if(!T||e-D>oe)try{const r=await fetch("/api/applications");if(!r.ok)throw new Error("Failed to fetch applied jobs");const o=await r.json();if(o.applications&&Array.isArray(o.applications)){T=o.applications,D=e;const h=T.map(n=>n.url);s(S,t(d).filter(n=>n.url&&h.includes(n.url)).map(n=>n.id),!0)}}catch(r){console.error("Error checking applied jobs:",r)}else if(T){const r=T.map(o=>o.url);s(S,t(d).filter(o=>o.url&&r.includes(o.url)).map(o=>o.id),!0)}}function ae(e){s(U,e,!0)}function N(e=!1){var n,c,i,y;console.log("Parsing URL parameters, skipJobLoad:",e);const r=new URL(window.location.href),o={title:r.searchParams.get("title")||"",locations:((n=r.searchParams.get("locations"))==null?void 0:n.split(",").filter(Boolean))||[],locationType:((c=r.searchParams.get("locationType"))==null?void 0:c.split(",").filter(Boolean))||[],experience:((i=r.searchParams.get("experience"))==null?void 0:i.split(",").filter(Boolean))||[],category:[],education:[],salary:r.searchParams.get("salary")||"",state:r.searchParams.get("state")||"",country:r.searchParams.get("country")||"US",datePosted:r.searchParams.get("datePosted")||"",easyApply:r.searchParams.get("easyApply")==="true",companies:((y=r.searchParams.get("companies"))==null?void 0:y.split(",").filter(Boolean))||[],location:r.searchParams.get("location")||""},h=o.title||o.locations.length>0||o.locationType.length>0||o.experience.length>0||o.salary;return console.log("URL parameters parsed, hasSearchParams:",h),Object.keys(o).forEach(b=>{t(a)[b]=o[b]}),!e&&Date.now()-t(j)>F?(console.log("Loading jobs from parsed URL parameters"),E(o),s(j,Date.now(),!0)):e||console.log("Skipping job load from parsed URL parameters - too soon since last load"),h}function B(){N()}const ne=C(e=>{const r=Date.now();if(r-t(j)<F){console.log(`Skipping job load - too soon since last load (${r-t(j)}ms)`);return}!t(x)&&!t(J)?(s(x,!0),console.log("Debounced job loading triggered"),s(j,r,!0),E(e).finally(()=>{s(x,!1)})):console.log("Skipping debounced job loading - another job search is in progress")},1200);function M(e){var h,n,c;const r=Date.now();if(r-t(L)<W){console.log(`Skipping URL params update handling - too soon since last update (${r-t(L)}ms)`);return}s(L,r,!0),console.log("URL params updated, event details:",e.detail),console.log("Current searchParams:",t(a)),t(a).title||((h=t(a).locations)==null?void 0:h.length)>0||((n=t(a).locationType)==null?void 0:n.length)>0||((c=t(a).experience)==null?void 0:c.length)>0||t(a).salary?(console.log("URL params updated with search criteria, triggering debounced job loading"),ne(t(a))):console.log("URL params updated but no search criteria, skipping job loading")}const se=C(()=>{t(d).length>0&&Q&&l.data.user&&re()},1e3);be(()=>{t(d).length>0&&Q&&se()});let I=p(!1);const ie=C(e=>{if(t(I))console.log("Skipping debounced URL update - another update is in progress");else{s(I,!0),console.log("Debounced URL update triggered");try{R(e)}finally{s(I,!1)}}},800);var V=Je(),q=me(V);je(q,{title:"Job Search | Hirli",description:"Search for jobs that match your profile and experience. Track your job applications and get insights on your job search progress.",keywords:"job search, job applications, job tracking, career search, job matching, application tracking"});var H=K(q,2);Pe(H,{onSearch:X,get isSearching(){return t(g)},get initialParams(){return t(a)},get user(){return l.data.user}});var ce=K(H,2);Se(ce,{get jobs(){return t(d)},isAuthenticated:!0,get isLoading(){return t(A)},onLoadMore:Z,onApply:$,onSave:ee,get selectedJob(){return t(U)},onSelectJob:ae,get searchParams(){return t(a)},get totalJobCount(){return t(O)},get savedJobs(){return t(v)},get appliedJobs(){return t(S)}}),fe(P,V),we()}export{Fe as component};
