import{c as n,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as p}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function k(s,o){const t=m(o,["children","$$slots","$$events","$$legacy"]),e=[["circle",{cx:"12",cy:"12",r:"10"}],["polyline",{points:"12 6 12 12 16 14"}]];f(s,d({name:"clock"},()=>t,{get iconNode(){return e},children:(a,$)=>{var r=n(),c=i(r);p(c,o,"default",{},null),l(a,r)},$$slots:{default:!0}}))}export{k as C};
