import{f as n,a as l}from"./BasJTneF.js";import{p as m,c as p,au as d,r as f,a as c}from"./CGmarHxI.js";import{c as u,s as v}from"./ncUU1dSD.js";import{e as b}from"./B-Xjo-Yt.js";import{b as h}from"./5V1tIHTN.js";import{p as _,r as x}from"./Btcx8l8F.js";var w=n("<div><!></div>");function q(r,a){m(a,!0);let e=_(a,"ref",15,null),o=x(a,["$$slots","$$events","$$legacy","ref","class","inset","children"]);var s=w();b(s,t=>({"data-slot":"dropdown-menu-label","data-inset":a.inset,class:t,...o}),[()=>u("px-2 py-1.5 text-sm font-semibold data-[inset]:pl-8",a.class)]);var i=p(s);v(i,()=>a.children??d),f(s),h(s,t=>e(t),()=>e()),l(r,s),c()}export{q as D};
