import{N as n,w as r,b as i}from"./BGYDhraB.js";const p="listItem",o="textStyle",a=/^(\d+)\.\s$/,d=n.create({name:"orderedList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes(){return{start:{default:1,parseHTML:t=>t.hasAttribute("start")?parseInt(t.getAttribute("start")||"",10):1},type:{default:null,parseHTML:t=>t.getAttribute("type")}}},parseHTML(){return[{tag:"ol"}]},renderHTML({HTMLAttributes:t}){const{start:e,...s}=t;return e===1?["ol",i(this.options.HTMLAttributes,s),0]:["ol",i(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleOrderedList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(p,this.editor.getAttributes(o)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let t=r({find:a,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,s)=>s.childCount+s.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(t=r({find:a,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(o)}),joinPredicate:(e,s)=>s.childCount+s.attrs.start===+e[1],editor:this.editor})),[t]}});export{d as OrderedList,d as default,a as inputRegex};
