var W=Object.defineProperty;var J=r=>{throw TypeError(r)};var X=(r,t,e)=>t in r?W(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var w=(r,t,e)=>X(r,typeof t!="symbol"?t+"":t,e),Y=(r,t,e)=>t.has(r)||J("Cannot "+e);var A=(r,t,e)=>(Y(r,t,"read from private field"),e?e.call(r):t.get(r)),I=(r,t,e)=>t.has(r)?J("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,e);import{c as y,a as v,f as F}from"./BasJTneF.js";import{x as _,g as u,d as z,u as Z,o as $,p as b,f as S,a as P,c as N,au as Q,r as U}from"./CGmarHxI.js";import{c as V}from"./BvdI7LR8.js";import{p as d,r as T,s as B}from"./Btcx8l8F.js";import{s as x,c as j}from"./ncUU1dSD.js";import{i as q}from"./u21ee2wt.js";import{e as E}from"./B-Xjo-Yt.js";import{u as G,b as p,m as K}from"./BfX7a-t9.js";import{u as D}from"./CnMg5bH0.js";import{k as tt}from"./CmxjS0TN.js";import{C as rt}from"./DuoUhxYL.js";const et="data-avatar-root",at="data-avatar-image",st="data-avatar-fallback";var O;class ot{constructor(t){w(this,"opts");I(this,O,_(()=>({id:this.opts.id.current,[et]:"","data-status":this.opts.loadingStatus.current})));this.opts=t,this.loadImage=this.loadImage.bind(this),G(t)}loadImage(t,e,s){if(this.opts.loadingStatus.current==="loaded")return;let i;const o=new Image;return o.src=t,e!==void 0&&(o.crossOrigin=e),s&&(o.referrerPolicy=s),this.opts.loadingStatus.current="loading",o.onload=()=>{i=window.setTimeout(()=>{this.opts.loadingStatus.current="loaded"},this.opts.delayMs.current)},o.onerror=()=>{this.opts.loadingStatus.current="error"},()=>{window.clearTimeout(i)}}get props(){return u(A(this,O))}set props(t){z(A(this,O),t)}}O=new WeakMap;var C;class it{constructor(t,e){w(this,"opts");w(this,"root");I(this,C,_(()=>({id:this.opts.id.current,style:{display:this.root.opts.loadingStatus.current==="loaded"?"block":"none"},"data-status":this.root.opts.loadingStatus.current,[at]:"",src:this.opts.src.current,crossorigin:this.opts.crossOrigin.current,referrerpolicy:this.opts.referrerPolicy.current})));this.opts=t,this.root=e,G(t),Z(()=>{if(!this.opts.src.current){this.root.opts.loadingStatus.current="error";return}this.opts.crossOrigin.current,$(()=>this.root.loadImage(this.opts.src.current??"",this.opts.crossOrigin.current,this.opts.referrerPolicy.current))})}get props(){return u(A(this,C))}set props(t){z(A(this,C),t)}}C=new WeakMap;var M,L;class nt{constructor(t,e){w(this,"opts");w(this,"root");I(this,M,_(()=>this.root.opts.loadingStatus.current==="loaded"?{display:"none"}:void 0));I(this,L,_(()=>({style:this.style,"data-status":this.root.opts.loadingStatus.current,[st]:""})));this.opts=t,this.root=e,G(t)}get style(){return u(A(this,M))}set style(t){z(A(this,M),t)}get props(){return u(A(this,L))}set props(t){z(A(this,L),t)}}M=new WeakMap,L=new WeakMap;const H=new rt("Avatar.Root");function lt(r){return H.set(new ot(r))}function ct(r){return new it(r,H.get())}function dt(r){return new nt(r,H.get())}var ut=F("<div><!></div>");function ft(r,t){b(t,!0);let e=d(t,"delayMs",3,0),s=d(t,"loadingStatus",15,"loading"),i=d(t,"id",19,D),o=d(t,"ref",15,null),f=T(t,["$$slots","$$events","$$legacy","delayMs","loadingStatus","onLoadingStatusChange","child","children","id","ref"]);const g=lt({delayMs:p.with(()=>e()),loadingStatus:p.with(()=>s(),a=>{var n;s()!==a&&(s(a),(n=t.onLoadingStatusChange)==null||n.call(t,a))}),id:p.with(()=>i()),ref:p.with(()=>o(),a=>o(a))}),l=_(()=>K(f,g.props));var c=y(),k=S(c);{var h=a=>{var n=y(),R=S(n);x(R,()=>t.child,()=>({props:u(l)})),v(a,n)},m=a=>{var n=ut();E(n,()=>({...u(l)}));var R=N(n);x(R,()=>t.children??Q),U(n),v(a,n)};q(k,a=>{t.child?a(h):a(m,!1)})}v(r,c),P()}var gt=F("<img/>");function ht(r,t){b(t,!0);let e=d(t,"id",19,D),s=d(t,"ref",15,null),i=d(t,"crossorigin",3,void 0),o=d(t,"referrerpolicy",3,void 0),f=T(t,["$$slots","$$events","$$legacy","src","child","id","ref","crossorigin","referrerpolicy"]);const g=ct({src:p.with(()=>t.src),id:p.with(()=>e()),ref:p.with(()=>s(),a=>s(a)),crossOrigin:p.with(()=>i()),referrerPolicy:p.with(()=>o())}),l=_(()=>K(f,g.props));var c=y(),k=S(c);{var h=a=>{var n=y(),R=S(n);x(R,()=>t.child,()=>({props:u(l)})),v(a,n)},m=a=>{var n=gt();E(n,()=>({...u(l),src:t.src})),tt(n),v(a,n)};q(k,a=>{t.child?a(h):a(m,!1)})}v(r,c),P()}var vt=F("<span><!></span>");function mt(r,t){b(t,!0);let e=d(t,"id",19,D),s=d(t,"ref",15,null),i=T(t,["$$slots","$$events","$$legacy","children","child","id","ref"]);const o=dt({id:p.with(()=>e()),ref:p.with(()=>s(),h=>s(h))}),f=_(()=>K(i,o.props));var g=y(),l=S(g);{var c=h=>{var m=y(),a=S(m);x(a,()=>t.child,()=>({props:u(f)})),v(h,m)},k=h=>{var m=vt();E(m,()=>({...u(f)}));var a=N(m);x(a,()=>t.children??Q),U(m),v(h,m)};q(l,h=>{t.child?h(c):h(k,!1)})}v(r,g),P()}function xt(r,t){b(t,!0);let e=d(t,"ref",15,null),s=T(t,["$$slots","$$events","$$legacy","ref","class"]);var i=y(),o=S(i);const f=_(()=>j("relative flex size-8 shrink-0 overflow-hidden rounded-full",t.class));V(o,()=>ft,(g,l)=>{l(g,B({"data-slot":"avatar",get class(){return u(f)}},()=>s,{get ref(){return e()},set ref(c){e(c)}}))}),v(r,i),P()}function Ot(r,t){b(t,!0);let e=d(t,"ref",15,null),s=T(t,["$$slots","$$events","$$legacy","ref","class"]);var i=y(),o=S(i);const f=_(()=>j("aspect-square size-full",t.class));V(o,()=>ht,(g,l)=>{l(g,B({"data-slot":"avatar-image",get class(){return u(f)}},()=>s,{get ref(){return e()},set ref(c){e(c)}}))}),v(r,i),P()}function Ct(r,t){b(t,!0);let e=d(t,"ref",15,null),s=T(t,["$$slots","$$events","$$legacy","ref","class"]);var i=y(),o=S(i);const f=_(()=>j("bg-muted flex size-full items-center justify-center rounded-full",t.class));V(o,()=>mt,(g,l)=>{l(g,B({"data-slot":"avatar-fallback",get class(){return u(f)}},()=>s,{get ref(){return e()},set ref(c){e(c)}}))}),v(r,i),P()}export{xt as A,Ot as a,Ct as b};
