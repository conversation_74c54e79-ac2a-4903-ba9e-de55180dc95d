import{c as i,a}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as c}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as y,s as $}from"./Btcx8l8F.js";import{I as x}from"./D4f2twK-.js";function h(o,e){const t=y(e,["children","$$slots","$$events","$$legacy"]),l=[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"}],["line",{x1:"12",x2:"12",y1:"7",y2:"13"}],["line",{x1:"15",x2:"9",y1:"10",y2:"10"}]];x(o,$({name:"bookmark-plus"},()=>t,{get iconNode(){return l},children:(r,m)=>{var n=i(),s=c(n);d(s,e,"default",{},null),a(r,n)},$$slots:{default:!0}}))}function N(o,e){const t=y(e,["children","$$slots","$$events","$$legacy"]),l=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"M16 16s-1.5-2-4-2-4 2-4 2"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9"}]];x(o,$({name:"frown"},()=>t,{get iconNode(){return l},children:(r,m)=>{var n=i(),s=c(n);d(s,e,"default",{},null),a(r,n)},$$slots:{default:!0}}))}function k(o,e){const t=y(e,["children","$$slots","$$events","$$legacy"]),l=[["line",{x1:"4",x2:"4",y1:"21",y2:"14"}],["line",{x1:"4",x2:"4",y1:"10",y2:"3"}],["line",{x1:"12",x2:"12",y1:"21",y2:"12"}],["line",{x1:"12",x2:"12",y1:"8",y2:"3"}],["line",{x1:"20",x2:"20",y1:"21",y2:"16"}],["line",{x1:"20",x2:"20",y1:"12",y2:"3"}],["line",{x1:"2",x2:"6",y1:"14",y2:"14"}],["line",{x1:"10",x2:"14",y1:"8",y2:"8"}],["line",{x1:"18",x2:"22",y1:"16",y2:"16"}]];x(o,$({name:"sliders-vertical"},()=>t,{get iconNode(){return l},children:(r,m)=>{var n=i(),s=c(n);d(s,e,"default",{},null),a(r,n)},$$slots:{default:!0}}))}export{h as B,N as F,k as S};
