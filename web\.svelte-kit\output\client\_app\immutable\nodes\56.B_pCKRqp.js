import{f as j,a as n,t as c}from"../chunks/BasJTneF.js";import{p as E,a as B,s as o,c as i,g as h,k as O,d as l,n as u,r as d}from"../chunks/CGmarHxI.js";import{B as f}from"../chunks/B1K98fMG.js";import"../chunks/CgXBgsce.js";import{t as e}from"../chunks/DjPYYl4Z.js";import{g as T}from"../chunks/BiJhC7W5.js";var L=j('<div class="container mx-auto p-6"><div class="mb-6 flex items-center justify-between"><h1 class="text-2xl font-bold">Seed Features</h1> <!></div> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border p-6 shadow-sm"><h2 class="mb-4 text-xl font-semibold">Service Features</h2> <p class="text-muted-foreground mb-4 text-sm">Seed service features including document storage with storage limits.</p> <!></div> <div class="rounded-lg border p-6 shadow-sm"><h2 class="mb-4 text-xl font-semibold">All Features</h2> <p class="text-muted-foreground mb-4 text-sm">Seed all features including core, resume, job search, and application features.</p> <!></div> <div class="rounded-lg border p-6 shadow-sm"><h2 class="mb-4 text-xl font-semibold">Analysis Features</h2> <p class="text-muted-foreground mb-4 text-sm">Seed analysis features for job market insights and resume analysis.</p> <!></div></div></div>');function I($,b){E(b,!0);let a=O(!1);async function S(){l(a,!0);try{e.loading("Seeding service features...");const r=await(await fetch("/api/admin/features/seed-service",{method:"POST"})).json();r.success?(e.dismiss(),e.success("Service features seeded successfully!"),console.log("Seed results:",r)):(e.dismiss(),e.error(`Failed to seed service features: ${r.error}`))}catch(s){console.error("Error seeding service features:",s),e.dismiss(),e.error(`Error seeding service features: ${s.message}`)}finally{l(a,!1)}}async function w(){l(a,!0);try{e.loading("Seeding all features...");const r=await(await fetch("/api/admin/features/seed-all",{method:"POST"})).json();r.success?(e.dismiss(),e.success("All features seeded successfully!"),console.log("Seed results:",r)):(e.dismiss(),e.error(`Failed to seed all features: ${r.error}`))}catch(s){console.error("Error seeding all features:",s),e.dismiss(),e.error(`Error seeding all features: ${s.message}`)}finally{l(a,!1)}}async function F(){l(a,!0);try{e.loading("Seeding analysis features...");const r=await(await fetch("/api/admin/features/seed-analysis",{method:"POST"})).json();r.success?(e.dismiss(),e.success("Analysis features seeded successfully!"),console.log("Seed results:",r)):(e.dismiss(),e.error(`Failed to seed analysis features: ${r.error}`))}catch(s){console.error("Error seeding analysis features:",s),e.dismiss(),e.error(`Error seeding analysis features: ${s.message}`)}finally{l(a,!1)}}var m=L(),g=i(m),_=o(i(g),2);f(_,{variant:"outline",onclick:()=>T("/dashboard/settings/admin"),children:(s,r)=>{u();var t=c("Back to Admin");n(s,t)},$$slots:{default:!0}}),d(g);var y=o(g,2),v=i(y),A=o(i(v),4);f(A,{onclick:S,get disabled(){return h(a)},class:"w-full",children:(s,r)=>{u();var t=c("Seed Service Features");n(s,t)},$$slots:{default:!0}}),d(v);var p=o(v,2),k=o(i(p),4);f(k,{onclick:w,get disabled(){return h(a)},class:"w-full",children:(s,r)=>{u();var t=c("Seed All Features");n(s,t)},$$slots:{default:!0}}),d(p);var x=o(p,2),P=o(i(x),4);f(P,{onclick:F,get disabled(){return h(a)},class:"w-full",children:(s,r)=>{u();var t=c("Seed Analysis Features");n(s,t)},$$slots:{default:!0}}),d(x),d(y),d(m),n($,m),B()}export{I as component};
