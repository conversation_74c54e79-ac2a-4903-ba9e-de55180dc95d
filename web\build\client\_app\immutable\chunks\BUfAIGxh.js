import{M as r,m as e,a as s,b as n}from"./BGYDhraB.js";const a=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,o=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,d=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,u=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,l=r.create({name:"bold",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:t=>t.style.fontWeight!=="normal"&&null},{style:"font-weight=400",clearMark:t=>t.type.name===this.name},{style:"font-weight",getAttrs:t=>/^(bold(er)?|[5-9]\d{2,})$/.test(t)&&null}]},renderHTML({HTMLAttributes:t}){return["strong",n(this.options.HTMLAttributes,t),0]},addCommands(){return{setBold:()=>({commands:t})=>t.setMark(this.name),toggleBold:()=>({commands:t})=>t.toggleMark(this.name),unsetBold:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[s({find:a,type:this.type}),s({find:d,type:this.type})]},addPasteRules(){return[e({find:o,type:this.type}),e({find:u,type:this.type})]}});export{l as Bold,l as default,a as starInputRegex,o as starPasteRegex,d as underscoreInputRegex,u as underscorePasteRegex};
