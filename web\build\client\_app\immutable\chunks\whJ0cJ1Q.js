import{c,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as h,s as p}from"./Btcx8l8F.js";import{I as $}from"./D4f2twK-.js";function y(r,t){const o=h(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M11 13H7"}],["path",{d:"M19 9h-4"}],["path",{d:"M3 3v16a2 2 0 0 0 2 2h16"}],["rect",{x:"15",y:"5",width:"4",height:"12",rx:"1"}],["rect",{x:"7",y:"8",width:"4",height:"9",rx:"1"}]];$(r,p({name:"chart-column-stacked"},()=>o,{get iconNode(){return s},children:(a,m)=>{var e=c(),n=d(e);l(n,t,"default",{},null),i(a,e)},$$slots:{default:!0}}))}function M(r,t){const o=h(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M20 7h-9"}],["path",{d:"M14 17H5"}],["circle",{cx:"17",cy:"17",r:"3"}],["circle",{cx:"7",cy:"7",r:"3"}]];$(r,p({name:"settings-2"},()=>o,{get iconNode(){return s},children:(a,m)=>{var e=c(),n=d(e);l(n,t,"default",{},null),i(a,e)},$$slots:{default:!0}}))}export{y as C,M as S};
