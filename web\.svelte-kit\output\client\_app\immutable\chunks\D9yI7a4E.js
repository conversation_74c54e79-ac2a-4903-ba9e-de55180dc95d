var he=Object.defineProperty;var Q=t=>{throw TypeError(t)};var ue=(t,e,r)=>e in t?he(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var C=(t,e,r)=>ue(t,typeof e!="symbol"?e+"":e,r),V=(t,e,r)=>e.has(t)||Q("Cannot "+r);var s=(t,e,r)=>(V(t,e,"read from private field"),r?r.call(t):e.get(t)),m=(t,e,r)=>e.has(t)?Q("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,r);var U=(t,e,r)=>(V(t,e,"access private method"),r);import{c as y,a as k,f as G}from"./BasJTneF.js";import{x as h,g as i,d as w,p as j,f as _,a as z,s as pe,e as Z,c as $,au as ee,r as te}from"./CGmarHxI.js";import{c as X}from"./BvdI7LR8.js";import{s as re,p as a,r as J}from"./Btcx8l8F.js";import{s as W,c as Y}from"./ncUU1dSD.js";import{i as K}from"./u21ee2wt.js";import{e as se}from"./B-Xjo-Yt.js";import{u as ie,b as g,m as oe}from"./BfX7a-t9.js";import{C as le}from"./DuoUhxYL.js";import{e as fe,f as me,h as ge,i as ke,j as be,k as ve}from"./Bd3zs5C6.js";import{d as we,S as _e}from"./CIOgxH3l.js";import{u as ae}from"./CnMg5bH0.js";import"./CgXBgsce.js";import{i as Pe}from"./BIEMS98f.js";import{H as Se}from"./BjCTmJLi.js";import{n as ye}from"./DX6rZLP_.js";const Re="data-switch-root",xe="data-switch-thumb";var q,F,T,D,I;class Ce{constructor(e){m(this,q);C(this,"opts");m(this,T,h(()=>({"data-disabled":ge(this.opts.disabled.current),"data-state":me(this.opts.checked.current),"data-required":fe(this.opts.required.current)})));m(this,D,h(()=>({checked:this.opts.checked.current})));m(this,I,h(()=>({...this.sharedProps,id:this.opts.id.current,role:"switch",disabled:ve(this.opts.disabled.current),"aria-checked":be(this.opts.checked.current,!1),"aria-required":ke(this.opts.required.current),[Re]:"",onclick:this.onclick,onkeydown:this.onkeydown})));this.opts=e,ie(e),this.onkeydown=this.onkeydown.bind(this),this.onclick=this.onclick.bind(this)}onkeydown(e){!(e.key===we||e.key===_e)||this.opts.disabled.current||(e.preventDefault(),U(this,q,F).call(this))}onclick(e){this.opts.disabled.current||U(this,q,F).call(this)}get sharedProps(){return i(s(this,T))}set sharedProps(e){w(s(this,T),e)}get snippetProps(){return i(s(this,D))}set snippetProps(e){w(s(this,D),e)}get props(){return i(s(this,I))}set props(e){w(s(this,I),e)}}q=new WeakSet,F=function(){this.opts.checked.current=!this.opts.checked.current},T=new WeakMap,D=new WeakMap,I=new WeakMap;var A,H;class qe{constructor(e){C(this,"root");m(this,A,h(()=>this.root.opts.name.current!==void 0));m(this,H,h(()=>({type:"checkbox",name:this.root.opts.name.current,value:this.root.opts.value.current,checked:this.root.opts.checked.current,disabled:this.root.opts.disabled.current,required:this.root.opts.required.current})));this.root=e}get shouldRender(){return i(s(this,A))}set shouldRender(e){w(s(this,A),e)}get props(){return i(s(this,H))}set props(e){w(s(this,H),e)}}A=new WeakMap,H=new WeakMap;var E,B;class Te{constructor(e,r){C(this,"opts");C(this,"root");m(this,E,h(()=>({checked:this.root.opts.checked.current})));m(this,B,h(()=>({...this.root.sharedProps,id:this.opts.id.current,[xe]:""})));this.opts=e,this.root=r,ie(e)}get snippetProps(){return i(s(this,E))}set snippetProps(e){w(s(this,E),e)}get props(){return i(s(this,B))}set props(e){w(s(this,B),e)}}E=new WeakMap,B=new WeakMap;const L=new le("Switch.Root");function De(t){return L.set(new Ce(t))}function Ie(){return new qe(L.get())}function Ae(t){return new Te(t,L.get())}function He(t,e){j(e,!1);const r=Ie();Pe();var l=y(),v=_(l);{var u=n=>{Se(n,re(()=>r.props))};K(v,n=>{r.shouldRender&&n(u)})}k(t,l),z()}var Ee=G("<button><!></button>"),Be=G("<!> <!>",1);function Oe(t,e){j(e,!0);let r=a(e,"ref",15,null),l=a(e,"id",19,ae),v=a(e,"disabled",3,!1),u=a(e,"required",3,!1),n=a(e,"checked",15,!1),P=a(e,"value",3,"on"),R=a(e,"name",3,void 0),x=a(e,"type",3,"button"),f=a(e,"onCheckedChange",3,ye),d=J(e,["$$slots","$$events","$$legacy","child","children","ref","id","disabled","required","checked","value","name","type","onCheckedChange"]);const o=De({checked:g.with(()=>n(),c=>{var p;n(c),(p=f())==null||p(c)}),disabled:g.with(()=>v()??!1),required:g.with(()=>u()),value:g.with(()=>P()),name:g.with(()=>R()),id:g.with(()=>l()),ref:g.with(()=>r(),c=>r(c))}),b=h(()=>oe(d,o.props,{type:x()}));var S=Be(),O=_(S);{var M=c=>{var p=y(),N=_(p),ce=Z(()=>({props:i(b),...o.snippetProps}));W(N,()=>e.child,()=>i(ce)),k(c,p)},ne=c=>{var p=Ee();se(p,()=>({...i(b)}));var N=$(p);W(N,()=>e.children??ee,()=>o.snippetProps),te(p),k(c,p)};K(O,c=>{e.child?c(M):c(ne,!1)})}var de=pe(O,2);He(de,{}),k(t,S),z()}var We=G("<span><!></span>");function je(t,e){j(e,!0);let r=a(e,"ref",15,null),l=a(e,"id",19,ae),v=J(e,["$$slots","$$events","$$legacy","child","children","ref","id"]);const u=Ae({id:g.with(()=>l()),ref:g.with(()=>r(),d=>r(d))}),n=h(()=>oe(v,u.props));var P=y(),R=_(P);{var x=d=>{var o=y(),b=_(o),S=Z(()=>({props:i(n),...u.snippetProps}));W(b,()=>e.child,()=>i(S)),k(d,o)},f=d=>{var o=We();se(o,()=>({...i(n)}));var b=$(o);W(b,()=>e.children??ee,()=>u.snippetProps),te(o),k(d,o)};K(R,d=>{e.child?d(x):d(f,!1)})}k(t,P),z()}function rt(t,e){j(e,!0);let r=a(e,"ref",15,null),l=a(e,"checked",15,!1),v=J(e,["$$slots","$$events","$$legacy","ref","class","checked"]);var u=y(),n=_(u);const P=h(()=>Y("data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 shadow-xs peer inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent outline-none transition-all focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e.class));X(n,()=>Oe,(R,x)=>{x(R,re({"data-slot":"switch",get class(){return i(P)}},()=>v,{get ref(){return r()},set ref(f){r(f)},get checked(){return l()},set checked(f){l(f)},children:(f,d)=>{var o=y(),b=_(o);const S=h(()=>Y("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0"));X(b,()=>je,(O,M)=>{M(O,{"data-slot":"switch-thumb",get class(){return i(S)}})}),k(f,o)},$$slots:{default:!0}}))}),k(t,u),z()}export{rt as S};
