import{e as E,a as w}from"./BasJTneF.js";import"./CgXBgsce.js";import{c as k,s as g,r as b,t as T}from"./CGmarHxI.js";import{e as L,a as l}from"./B-Xjo-Yt.js";import{l as u,p as r}from"./Btcx8l8F.js";import{g as o}from"./BiJhC7W5.js";var f=E('<svg><rect height="240" stroke-width="26" width="240" x="8" y="8" rx="26"></rect><path d="M80 130 L110 160 L180 90" fill="none" stroke-dasharray="300" stroke-dashoffset="300" stroke-linecap="round" stroke-linejoin="round" stroke-width="26"><animate attributeName="stroke-dashoffset" begin="0.2s" dur="0.6s" fill="freeze" from="300" to="0"></animate></path></svg>');function B(t,a){const i=u(a,["children","$$slots","$$events","$$legacy"]),s=u(i,["className","fill","stroke"]);let m=r(a,"className",8,""),v=r(a,"fill",8,""),c=r(a,"stroke",8,"currentColor");var d=f();L(d,()=>({xmlns:"http://www.w3.org/2000/svg",class:m(),fill:v(),viewBox:"0 0 256 256",height:"32",width:"32",stroke:c(),...s}));var p=k(d),A=g(p);b(d),T(()=>{l(p,"stroke",c()),l(A,"stroke",c())}),w(t,d)}var e=(t=>(t.ALT="Alt",t.WINDOWS="Win",t.COMMAND="⌘",t.CONTROL="Ctrl",t))(e||{}),n=(t=>(t.GLOBAL="global",t.DASHBOARD="dashboard",t.JOBS="jobs",t.APPLICATIONS="applications",t.RESUMES="resumes",t.DOCUMENTS="documents",t.TRACKER="tracker",t.AUTOMATION="automation",t.MATCHES="matches",t.SETTINGS="settings",t.ADMIN="admin",t.SYSTEM_STATUS="system-status",t.NOTIFICATIONS="notifications",t))(n||{});const y={id:"navigation",name:"Navigation",activePages:[n.GLOBAL],shortcuts:[{id:"nav-dashboard",action:"Go to Dashboard",keys:`${e.ALT}+D`,handler:()=>{o("/dashboard")},description:"Navigate to the dashboard page"},{id:"nav-jobs",action:"Go to Jobs",keys:`${e.ALT}+J`,handler:()=>{o("/dashboard/jobs")},description:"Navigate to the jobs page"},{id:"nav-applications",action:"Go to Applications",keys:`${e.ALT}+A`,handler:()=>{o("/dashboard/applications")},description:"Navigate to the applications page"},{id:"nav-matches",action:"Go to Matches",keys:`${e.ALT}+M`,handler:()=>{o("/dashboard/matches")},description:"Navigate to the job matches page"},{id:"nav-tracker",action:"Go to Job Tracker",keys:`${e.ALT}+T`,handler:()=>{o("/dashboard/tracker")},description:"Navigate to the job tracker page"},{id:"nav-documents",action:"Go to Documents",keys:`${e.ALT}+O`,handler:()=>{o("/dashboard/documents")},description:"Navigate to the documents page"},{id:"nav-automation",action:"Go to Automation",keys:`${e.ALT}+U`,handler:()=>{o("/dashboard/automation")},description:"Navigate to the automation page"},{id:"nav-settings",action:"Go to Settings",keys:`${e.ALT}+S`,handler:()=>{o("/dashboard/settings")},description:"Navigate to the settings page"},{id:"nav-profile",action:"Go to Profile",keys:`${e.ALT}+P`,handler:()=>{o("/dashboard/settings/profile")},description:"Navigate to the profile page"},{id:"nav-billing",action:"Go to Billing",keys:`${e.ALT}+B`,handler:()=>{o("/dashboard/settings/billing")},description:"Navigate to the billing page"}]},C={id:"ui",name:"User Interface",activePages:[n.GLOBAL],shortcuts:[{id:"ui-search",action:"Open Search",keys:`${e.ALT}+K`,handler:t=>{t.preventDefault(),document.dispatchEvent(new CustomEvent("toggle-global-search"))},description:"Open the global search dialog"},{id:"ui-shortcuts",action:"Show Keyboard Shortcuts",keys:`${e.ALT}+/`,handler:t=>{t.preventDefault(),document.dispatchEvent(new CustomEvent("toggle-keyboard-shortcuts"))},description:"Show this keyboard shortcuts dialog"},{id:"ui-notifications",action:"Open Notifications",keys:`${e.ALT}+N`,handler:()=>{document.dispatchEvent(new CustomEvent("toggle-notifications"))},description:"Open the notifications panel"},{id:"ui-feedback",action:"Open Feedback",keys:`${e.ALT}+F`,handler:()=>{document.dispatchEvent(new CustomEvent("toggle-feedback"))},description:"Open the feedback panel"},{id:"ui-user-menu",action:"Open User Menu",keys:`${e.ALT}+U`,handler:()=>{document.dispatchEvent(new CustomEvent("toggle-user-menu"))},description:"Open the user menu"},{id:"ui-logout",action:"Log Out",keys:`${e.ALT}+Q`,handler:()=>{document.dispatchEvent(new CustomEvent("logout-user"))},description:"Log out of the application"},{id:"ui-refresh",action:"Refresh Page",keys:`${e.ALT}+R`,handler:()=>{window.location.reload()},description:"Refresh the current page"}]},$={id:"jobs",name:"Job Search",activePages:[n.JOBS],shortcuts:[{id:"job-save",action:"Save Job",keys:`${e.ALT}+S`,handler:()=>{document.dispatchEvent(new CustomEvent("save-current-job"))},description:"Save the currently selected job"},{id:"job-apply",action:"Apply to Job",keys:`${e.ALT}+Y`,handler:()=>{document.dispatchEvent(new CustomEvent("apply-to-current-job"))},description:"Apply to the currently selected job"},{id:"job-filter",action:"Toggle Filters",keys:`${e.ALT}+F`,handler:()=>{document.dispatchEvent(new CustomEvent("toggle-job-filters"))},description:"Toggle job search filters"},{id:"job-refresh",action:"Refresh Jobs",keys:`${e.ALT}+R`,handler:()=>{document.dispatchEvent(new CustomEvent("refresh-jobs"))},description:"Refresh job listings"},{id:"job-next",action:"Next Job",keys:`${e.ALT}+ArrowDown`,handler:()=>{document.dispatchEvent(new CustomEvent("next-job"))},description:"Navigate to the next job in the list"},{id:"job-prev",action:"Previous Job",keys:`${e.ALT}+ArrowUp`,handler:()=>{document.dispatchEvent(new CustomEvent("previous-job"))},description:"Navigate to the previous job in the list"},{id:"job-details",action:"View Job Details",keys:`${e.ALT}+Enter`,handler:()=>{document.dispatchEvent(new CustomEvent("view-job-details"))},description:"View details of the selected job"},{id:"job-share",action:"Share Job",keys:`${e.ALT}+H`,handler:()=>{document.dispatchEvent(new CustomEvent("share-job"))},description:"Share the selected job"},{id:"job-clear-filters",action:"Clear Filters",keys:`${e.ALT}+C`,handler:()=>{document.dispatchEvent(new CustomEvent("clear-job-filters"))},description:"Clear all job filters"}]},j={id:"applications",name:"Applications",activePages:[n.APPLICATIONS],shortcuts:[{id:"app-view",action:"View Application Details",keys:`${e.ALT}+V`,handler:()=>{document.dispatchEvent(new CustomEvent("view-application-details"))},description:"View details of the selected application"},{id:"app-status",action:"Update Status",keys:`${e.ALT}+U`,handler:()=>{document.dispatchEvent(new CustomEvent("update-application-status"))},description:"Update the status of the selected application"},{id:"app-note",action:"Add Note",keys:`${e.ALT}+E`,handler:()=>{document.dispatchEvent(new CustomEvent("add-application-note"))},description:"Add a note to the selected application"},{id:"app-filter",action:"Toggle Filters",keys:`${e.ALT}+F`,handler:()=>{document.dispatchEvent(new CustomEvent("toggle-application-filters"))},description:"Toggle application filters"},{id:"app-next",action:"Next Application",keys:`${e.ALT}+ArrowDown`,handler:()=>{document.dispatchEvent(new CustomEvent("next-application"))},description:"Navigate to the next application in the list"},{id:"app-prev",action:"Previous Application",keys:`${e.ALT}+ArrowUp`,handler:()=>{document.dispatchEvent(new CustomEvent("previous-application"))},description:"Navigate to the previous application in the list"},{id:"app-withdraw",action:"Withdraw Application",keys:`${e.ALT}+W`,handler:()=>{document.dispatchEvent(new CustomEvent("withdraw-application"))},description:"Withdraw the selected application"},{id:"app-refresh",action:"Refresh Applications",keys:`${e.ALT}+R`,handler:()=>{document.dispatchEvent(new CustomEvent("refresh-applications"))},description:"Refresh application listings"},{id:"app-clear-filters",action:"Clear Filters",keys:`${e.ALT}+C`,handler:()=>{document.dispatchEvent(new CustomEvent("clear-application-filters"))},description:"Clear all application filters"}]},N={id:"documents",name:"Documents",activePages:[n.RESUMES],shortcuts:[{id:"doc-new",action:"New Document",keys:`${e.ALT}+N`,handler:()=>{document.dispatchEvent(new CustomEvent("create-new-document"))},description:"Create a new document"},{id:"doc-save",action:"Save Document",keys:`${e.ALT}+S`,handler:()=>{document.dispatchEvent(new CustomEvent("save-current-document"))},description:"Save the current document"},{id:"doc-preview",action:"Preview Document",keys:`${e.ALT}+V`,handler:()=>{document.dispatchEvent(new CustomEvent("preview-document"))},description:"Preview the current document"},{id:"doc-download",action:"Download Document",keys:`${e.ALT}+W`,handler:()=>{document.dispatchEvent(new CustomEvent("download-document"))},description:"Download the current document"},{id:"doc-next",action:"Next Document",keys:`${e.ALT}+ArrowDown`,handler:()=>{document.dispatchEvent(new CustomEvent("next-document"))},description:"Navigate to the next document in the list"},{id:"doc-prev",action:"Previous Document",keys:`${e.ALT}+ArrowUp`,handler:()=>{document.dispatchEvent(new CustomEvent("previous-document"))},description:"Navigate to the previous document in the list"},{id:"doc-delete",action:"Delete Document",keys:`${e.ALT}+Delete`,handler:()=>{document.dispatchEvent(new CustomEvent("delete-document"))},description:"Delete the selected document"},{id:"doc-duplicate",action:"Duplicate Document",keys:`${e.ALT}+D`,handler:()=>{document.dispatchEvent(new CustomEvent("duplicate-document"))},description:"Duplicate the selected document"},{id:"doc-rename",action:"Rename Document",keys:`${e.ALT}+R`,handler:()=>{document.dispatchEvent(new CustomEvent("rename-document"))},description:"Rename the selected document"},{id:"doc-share",action:"Share Document",keys:`${e.ALT}+H`,handler:()=>{document.dispatchEvent(new CustomEvent("share-document"))},description:"Share the selected document"}]},D={id:"tracker",name:"Job Tracker",activePages:[n.DASHBOARD],shortcuts:[{id:"tracker-new",action:"Add New Job",keys:`${e.ALT}+N`,handler:()=>{document.dispatchEvent(new CustomEvent("add-new-job-to-tracker"))},description:"Add a new job to the tracker"},{id:"tracker-update",action:"Update Job Status",keys:`${e.ALT}+U`,handler:()=>{document.dispatchEvent(new CustomEvent("update-job-status"))},description:"Update the status of the selected job"},{id:"tracker-note",action:"Add Note",keys:`${e.ALT}+E`,handler:()=>{document.dispatchEvent(new CustomEvent("add-job-note"))},description:"Add a note to the selected job"},{id:"tracker-delete",action:"Delete Job",keys:`${e.ALT}+Delete`,handler:()=>{document.dispatchEvent(new CustomEvent("delete-job-from-tracker"))},description:"Delete the selected job from the tracker"},{id:"tracker-filter",action:"Toggle Filters",keys:`${e.ALT}+F`,handler:()=>{document.dispatchEvent(new CustomEvent("toggle-tracker-filters"))},description:"Toggle job tracker filters"},{id:"tracker-next",action:"Next Job",keys:`${e.ALT}+ArrowDown`,handler:()=>{document.dispatchEvent(new CustomEvent("next-tracker-job"))},description:"Navigate to the next job in the tracker"},{id:"tracker-prev",action:"Previous Job",keys:`${e.ALT}+ArrowUp`,handler:()=>{document.dispatchEvent(new CustomEvent("previous-tracker-job"))},description:"Navigate to the previous job in the tracker"},{id:"tracker-refresh",action:"Refresh Tracker",keys:`${e.ALT}+R`,handler:()=>{document.dispatchEvent(new CustomEvent("refresh-job-tracker"))},description:"Refresh the job tracker"}]},S={id:"automation",name:"Automation",activePages:[n.DASHBOARD],shortcuts:[{id:"auto-new",action:"New Automation",keys:`${e.ALT}+N`,handler:()=>{document.dispatchEvent(new CustomEvent("create-new-automation"))},description:"Create a new automation"},{id:"auto-start",action:"Start Automation",keys:`${e.ALT}+G`,handler:()=>{document.dispatchEvent(new CustomEvent("start-automation"))},description:"Start the selected automation"},{id:"auto-stop",action:"Stop Automation",keys:`${e.ALT}+X`,handler:()=>{document.dispatchEvent(new CustomEvent("stop-automation"))},description:"Stop the selected automation"},{id:"auto-edit",action:"Edit Automation",keys:`${e.ALT}+E`,handler:()=>{document.dispatchEvent(new CustomEvent("edit-automation"))},description:"Edit the selected automation"},{id:"auto-delete",action:"Delete Automation",keys:`${e.ALT}+Delete`,handler:()=>{document.dispatchEvent(new CustomEvent("delete-automation"))},description:"Delete the selected automation"},{id:"auto-logs",action:"View Logs",keys:`${e.ALT}+L`,handler:()=>{document.dispatchEvent(new CustomEvent("view-automation-logs"))},description:"View logs for the selected automation"}]},h=[y,C,$,j,N,D,S];function F(t){return h.filter(a=>{var i,s;return((i=a.activePages)==null?void 0:i.includes(n.GLOBAL))||((s=a.activePages)==null?void 0:s.includes(t))})}function I(){return h.flatMap(t=>t.shortcuts)}export{B as L,n as S,I as a,F as g,h as s};
