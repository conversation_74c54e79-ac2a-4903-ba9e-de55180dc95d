import{f as g,a as l,t as M}from"../chunks/BasJTneF.js";import{o as be,a as xe}from"../chunks/nZgk9enP.js";import{p as Ee,i as ne,k as x,v as H,f as B,a as ke,g as d,s as u,c,t as q,d as v,r as f,n as A}from"../chunks/CGmarHxI.js";import{s as Y}from"../chunks/CIt1g2O9.js";import{i as ie}from"../chunks/u21ee2wt.js";import{c as E}from"../chunks/BvdI7LR8.js";import{p as Pe}from"../chunks/Btcx8l8F.js";import{g as de}from"../chunks/BiJhC7W5.js";import"../chunks/CgXBgsce.js";import{t as k}from"../chunks/DjPYYl4Z.js";import{S as Se}from"../chunks/C6g8ubaU.js";import{B as F}from"../chunks/B1K98fMG.js";import{I as Re}from"../chunks/DMTMHyMa.js";import{R as Te,a as Ie,E as je,b as Ce}from"../chunks/CEzG2ALi.js";import{D as Be,a as Oe,R as Ae}from"../chunks/WD4kvFhR.js";import{S as Fe}from"../chunks/BHzYYMdu.js";import{E as Ne}from"../chunks/8b74MdfD.js";import{D as G}from"../chunks/Z6UAQTuv.js";import{D as Je}from"../chunks/Dz4exfp3.js";import{C as Le}from"../chunks/BNEH2jqx.js";import{S as ze}from"../chunks/DumgozFE.js";var Ue=g('<div class="flex items-center gap-2"><!> <!></div>'),Me=g('<div class="flex items-center gap-2"><h1 class="text-2xl font-bold"> </h1> <!></div>'),He=g('<span class="text-sm text-gray-500"> </span>'),qe=g("<!> ",1),Ye=g("<!> Actions",1),Ge=g("<!> <!> <!> <!>",1),Ke=g("<!> <!>",1),Qe=g('<div slot="content"><!></div>'),Ve=g('<div slot="design"><!></div>'),We=g('<!> <main><div class="flex items-center justify-between border border-l border-r border-t border-neutral-500 px-6 py-4"><div class="flex items-center gap-2"><!></div> <div class="flex items-center gap-2"><!> <!> <!></div></div> <div class="grid h-[calc(100vh-8.4rem)] grid-cols-1 md:grid-cols-[2.5fr_3fr]"><div class="border border-b-0 border-l-0 border-t-0 border-zinc-900 text-white"><!></div> <div class="flex items-center justify-center overflow-y-auto bg-neutral-950 p-4"><!></div></div></main>',1);function ya(me,K){Ee(K,!0);let e=Pe(K,"data",7);console.log("Builder page data:",e()),ne(()=>{e()&&e().form&&e().form.resume&&(console.log("Resume form data in builder page:",e().form.resume),e().form.resume.data&&console.log("Resume form data.data in builder page:",e().form.resume.data))}),ne(()=>{var r,o,t,s;if(e()&&e().resumeData&&e().resumeData.parsedData&&e().form&&e().form.resume){const a=e().resumeData.parsedData;console.log("Initializing form data from parsedData:",a);const i={header:{name:((r=a.header)==null?void 0:r.name)||"",email:((o=a.header)==null?void 0:o.email)||"",phone:((t=a.header)==null?void 0:t.phone)||""},summary:{content:((s=a.summary)==null?void 0:s.content)||""},experience:a.experience||[],education:a.education||[],skills:a.skills||[],projects:a.projects||[],certifications:a.certifications||[]};e().form.resume.data=i,e().form.resume.data&&e().form.resume.data.header&&(e().form.resume.data.header.name=i.header.name,e().form.resume.data.header.email=i.header.email,e().form.resume.data.header.phone=i.header.phone),console.log("Form data initialized:",e().form.resume.data)}}),e().resumeData||(console.error("Resume data is missing"),de("/dashboard/documents"));let P=x(!1),D=null,S=x(null),N=x(!1),R=x(H(e().resumeData.document.label||"Untitled Resume")),T=x(!1),Q=x(H(JSON.stringify(e().form.resume.data||{}))),V=x(H(JSON.stringify(e().form.design.data||{})));function le(){const r=JSON.stringify(e().form.resume.data||{}),o=JSON.stringify(e().form.design.data||{});(r!==d(Q)||o!==d(V))&&(console.log("Changes detected, scheduling auto-save"),v(T,!0),D&&clearTimeout(D),D=setTimeout(()=>{d(T)&&ue()},2e3),v(Q,r,!0),v(V,o,!0))}be(()=>{if(typeof window<"u"){const r=()=>le();return window.addEventListener("form-data-changed",r),window.addEventListener("design-data-changed",r),()=>{window.removeEventListener("form-data-changed",r),window.removeEventListener("design-data-changed",r),D&&clearTimeout(D)}}}),xe(()=>{D&&clearTimeout(D)});async function J(){var r,o,t,s;v(P,!0);try{const a=e().form.resume.data;let i=((r=a.header)==null?void 0:r.name)||"",$=((o=a.header)==null?void 0:o.email)||"",_=((t=a.header)==null?void 0:t.phone)||"";if(typeof document<"u")try{const n=document.getElementById("name-input"),p=document.getElementById("email-input"),h=document.getElementById("phone-input");n&&n.value&&(i=n.value),p&&p.value&&($=p.value),h&&h.value&&(_=h.value)}catch(n){console.error("Error getting values from DOM:",n)}const m={header:{name:i,email:$,phone:_},summary:{content:((s=a.summary)==null?void 0:s.content)||""},experience:a.experience||[],education:a.education||[],skills:a.skills||[],projects:a.projects||[],certifications:a.certifications||[]},y=await fetch(`/api/resume/${e().resumeData.id}/data`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({parsedData:m})});if(y.ok){const n=await y.json();v(S,new Date,!0),n.data&&(e().form.resume.data=n.data,e().form.resume.data&&e().form.resume.data.header&&(e().form.resume.data.header.name=m.header.name,e().form.resume.data.header.email=m.header.email,e().form.resume.data.header.phone=m.header.phone),console.log("Form data updated after save:",e().form.resume.data)),v(T,!1),k.success("Resume saved",{description:"Your resume has been saved successfully."})}else k.error("Error saving resume",{description:"Could not save your resume. Please try again."})}catch(a){console.error("Error saving resume:",a),k.error("Error saving resume",{description:"Could not save your resume. Please try again."})}finally{v(P,!1)}}async function ue(){var r,o,t,s;if(!d(P)&&d(T)){console.log("Auto-saving changes...");try{const a=e().form.resume.data;if(!a||!a.header)return;let i=((r=a.header)==null?void 0:r.name)||"",$=((o=a.header)==null?void 0:o.email)||"",_=((t=a.header)==null?void 0:t.phone)||"";if(typeof document<"u")try{const n=document.getElementById("name-input"),p=document.getElementById("email-input"),h=document.getElementById("phone-input");n&&n.value&&(i=n.value),p&&p.value&&($=p.value),h&&h.value&&(_=h.value)}catch(n){console.error("Error getting values from DOM (auto-save):",n)}const m={header:{name:i,email:$,phone:_},summary:{content:((s=a.summary)==null?void 0:s.content)||""},experience:a.experience||[],education:a.education||[],skills:a.skills||[],projects:a.projects||[],certifications:a.certifications||[]},y=await fetch(`/api/resume/${e().resumeData.id}/data`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({parsedData:m})});if(y.ok){const n=await y.json();v(S,new Date,!0),n.data&&(e().form.resume.data=n.data,e().form.resume.data&&e().form.resume.data.header&&(e().form.resume.data.header.name=m.header.name,e().form.resume.data.header.email=m.header.email,e().form.resume.data.header.phone=m.header.phone),console.log("Form data updated after auto-save:",e().form.resume.data)),console.log("Auto-saved resume at",d(S)),v(T,!1)}else console.error("Auto-save failed")}catch(a){console.error("Error during auto-save:",a)}}}function ce(){J().then(()=>{typeof window<"u"&&window.open(`/api/resume/${e().resumeData.id}/preview`,"_blank")})}function fe(){J().then(()=>{typeof window<"u"&&window.open(`/api/resume/${e().resumeData.id}/download`,"_blank")})}async function pe(){try{(await fetch(`/api/documents/${e().resumeData.document.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({label:d(R)})})).ok?(e().resumeData.document.label=d(R),v(N,!1),k.success("Resume title updated")):k.error("Failed to update resume title")}catch(r){console.error("Error updating resume title:",r),k.error("Failed to update resume title")}}var W=We(),X=B(W);Se(X,{get title(){return`${d(R)??""} | Auto Apply`},description:"Build your professional resume"});var Z=u(X,2),L=c(Z),z=c(L),ve=c(z);{var he=r=>{var o=Ue(),t=c(o);Re(t,{type:"text",class:"h-10 text-xl font-bold",autofocus:!0,get value(){return d(R)},set value(a){v(R,a,!0)}});var s=u(t,2);F(s,{variant:"ghost",size:"icon",onclick:pe,children:(a,i)=>{Le(a,{class:"h-5 w-5"})},$$slots:{default:!0}}),f(o),l(r,o)},ge=r=>{var o=Me(),t=c(o),s=c(t,!0);f(t);var a=u(t,2);F(a,{variant:"ghost",size:"icon",onclick:()=>v(N,!0),children:(i,$)=>{ze(i,{class:"h-4 w-4"})},$$slots:{default:!0}}),f(o),q(()=>Y(s,e().resumeData.document.label||"Resume Builder")),l(r,o)};ie(ve,r=>{d(N)?r(he):r(ge,!1)})}f(z);var ee=u(z,2),ae=c(ee);{var _e=r=>{var o=He(),t=c(o);f(o),q(s=>Y(t,`Last saved: ${s??""}`),[()=>d(S).toLocaleTimeString()]),l(r,o)};ie(ae,r=>{d(S)&&r(_e)})}var re=u(ae,2);F(re,{variant:"default",onclick:J,get disabled(){return d(P)},children:(r,o)=>{var t=qe(),s=B(t);Fe(s,{class:"mr-2 h-4 w-4"});var a=u(s);q(()=>Y(a,` ${d(P)?"Saving...":"Save Resume"}`)),l(r,t)},$$slots:{default:!0}});var ye=u(re,2);E(ye,()=>Ae,(r,o)=>{o(r,{children:(t,s)=>{var a=Ke(),i=B(a);E(i,()=>Be,(_,m)=>{m(_,{children:(y,n)=>{F(y,{variant:"ghost",class:"relative rounded-md border",children:(p,h)=>{var I=Ye(),O=B(I);Ne(O,{class:"mr-2 h-4 w-4"}),A(),l(p,I)},$$slots:{default:!0}})},$$slots:{default:!0}})});var $=u(i,2);E($,()=>Oe,(_,m)=>{m(_,{sideOffset:15,align:"end",children:(y,n)=>{var p=Ge(),h=B(p);E(h,()=>G,(w,b)=>{b(w,{onclick:()=>de("/dashboard/documents"),children:(j,se)=>{A();var C=M("Documents");l(j,C)},$$slots:{default:!0}})});var I=u(h,2);E(I,()=>G,(w,b)=>{b(w,{onclick:ce,children:(j,se)=>{A();var C=M("Preview");l(j,C)},$$slots:{default:!0}})});var O=u(I,2);E(O,()=>Je,(w,b)=>{b(w,{})});var we=u(O,2);E(we,()=>G,(w,b)=>{b(w,{onclick:fe,children:(j,se)=>{A();var C=M("Download");l(j,C)},$$slots:{default:!0}})}),l(y,p)},$$slots:{default:!0}})}),l(t,a)},$$slots:{default:!0}})}),f(ee),f(L);var te=u(L,2),U=c(te),$e=c(U);Te($e,{$$slots:{content:(r,o)=>{var t=Qe(),s=c(t);Ce(s,{get data(){return e().form.resume}}),f(t),l(r,t)},design:(r,o)=>{var t=Ve(),s=c(t);je(s,{get data(){return e().form.design}}),f(t),l(r,t)}}}),f(U);var oe=u(U,2),De=c(oe);Ie(De,{get formData(){return e().form.resume},get designData(){return e().form.design}}),f(oe),f(te),f(Z),l(me,W),ke()}export{ya as component};
