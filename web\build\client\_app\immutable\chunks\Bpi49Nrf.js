const t=typeof document<"u",s=i();function i(){var n,e;return t&&((n=window==null?void 0:window.navigator)==null?void 0:n.userAgent)&&(/iP(ad|hone|od)/.test(window.navigator.userAgent)||((e=window==null?void 0:window.navigator)==null?void 0:e.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function r(n){return n instanceof HTMLElement}function u(n){return n instanceof Element}function a(n){return n instanceof Element||n instanceof SVGElement}function o(n){return n.matches(":focus-visible")}function l(n){return n!==null}function c(n){return n instanceof HTMLInputElement&&"select"in n}function f(n,e){if(getComputedStyle(n).visibility==="hidden")return!0;for(;n;){if(e!==void 0&&n===e)return!1;if(getComputedStyle(n).display==="none")return!0;n=n.parentElement}return!1}export{r as a,t as b,a as c,o as d,l as e,s as f,c as g,f as h,u as i};
