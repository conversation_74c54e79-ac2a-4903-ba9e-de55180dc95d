import{c as g,a as d,f as P}from"./BasJTneF.js";import{p as x,f as _,a as w,g as b,x as C,au as z,c as j,s as q,r as A}from"./CGmarHxI.js";import{c as B}from"./BvdI7LR8.js";import{p as i,r as y,s as I}from"./Btcx8l8F.js";import{s as k,c as D}from"./ncUU1dSD.js";import{I as E}from"./DxW95yuQ.js";import{i as F}from"./u21ee2wt.js";import{r as G,e as H}from"./B-Xjo-Yt.js";import{b as J}from"./CzsE_FAw.js";import{b as v,m as K}from"./BfX7a-t9.js";import{c as L}from"./Dmwghw4a.js";import{u as M}from"./CnMg5bH0.js";var O=P("<input/>");function Q(l,e){x(e,!0);let a=i(e,"value",15,""),n=i(e,"autofocus",3,!1),u=i(e,"id",19,M),r=i(e,"ref",15,null),s=y(e,["$$slots","$$events","$$legacy","value","autofocus","id","ref","child"]);const c=L({id:v.with(()=>u()),ref:v.with(()=>r(),t=>r(t)),value:v.with(()=>a(),t=>{a(t)}),autofocus:v.with(()=>n()??!1)}),f=C(()=>K(s,c.props));var p=g(),h=_(p);{var m=t=>{var o=g(),S=_(o);k(S,()=>e.child,()=>({props:b(f)})),d(t,o)},N=t=>{var o=O();G(o),H(o,()=>({...b(f)})),J(o,a),d(t,o)};F(h,t=>{e.child?t(m):t(N,!1)})}d(l,p),w()}function R(l,e){x(e,!0);let a=y(e,["$$slots","$$events","$$legacy"]);const n=[["circle",{cx:"11",cy:"11",r:"8"}],["path",{d:"m21 21-4.3-4.3"}]];E(l,I({name:"search"},()=>a,{get iconNode(){return n},children:(u,r)=>{var s=g(),c=_(s);k(c,()=>e.children??z),d(u,s)},$$slots:{default:!0}})),w()}var T=P('<div class="flex h-9 items-center gap-2 border-b px-3" data-slot="command-input-wrapper"><!> <!></div>');function oe(l,e){x(e,!0);let a=i(e,"ref",15,null),n=i(e,"value",15,""),u=y(e,["$$slots","$$events","$$legacy","ref","class","value"]);var r=T(),s=j(r);R(s,{class:"size-4 shrink-0 opacity-50"});var c=q(s,2);const f=C(()=>D("placeholder:text-muted-foreground outline-hidden flex h-10 w-full rounded-md bg-transparent py-3 text-sm disabled:cursor-not-allowed disabled:opacity-50",e.class));B(c,()=>Q,(p,h)=>{h(p,I({"data-slot":"command-input",get class(){return b(f)}},()=>u,{get ref(){return a()},set ref(m){a(m)},get value(){return n()},set value(m){n(m)}}))}),A(r),d(l,r),w()}export{oe as C};
