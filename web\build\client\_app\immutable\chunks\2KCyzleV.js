import{c as a,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as m}from"./CGmarHxI.js";import{s as p}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function v(t,o){const e=c(o,["children","$$slots","$$events","$$legacy"]),s=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21"}]];f(t,d({name:"monitor"},()=>e,{get iconNode(){return s},children:(n,$)=>{var r=a(),i=m(r);p(i,o,"default",{},null),l(n,r)},$$slots:{default:!0}}))}export{v as M};
