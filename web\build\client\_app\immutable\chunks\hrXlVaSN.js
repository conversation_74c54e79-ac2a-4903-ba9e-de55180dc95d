import{g as i,k as T,d as g,x as w}from"./CGmarHxI.js";import{o as m}from"./CmxjS0TN.js";import{w as b,e as v}from"./BfX7a-t9.js";import{b as E}from"./Ntteq2n_.js";import{i as A,a as G}from"./Bpi49Nrf.js";function S(e){const r=w(()=>e.enabled()),t=E(!1,e.transitTimeout??300,s=>{var o;i(r)&&((o=e.setIsPointerInTransit)==null||o.call(e,s))});let n=T(null);function c(){g(n,null),t.current=!1}function l(s,o){const a=s.currentTarget;if(!G(a))return;const u={x:s.clientX,y:s.clientY},y=k(u,a.getBoundingClientRect()),f=M(u,y),h=R(o.getBoundingClientRect()),x=H([...f,...h]);g(n,x,!0),t.current=!0}return b([e.triggerNode,e.contentNode,e.enabled],([s,o,a])=>{if(!s||!o||!a)return;const u=f=>{l(f,o)},y=f=>{l(f,s)};return v(m(s,"pointerleave",u),m(o,"pointerleave",y))}),b(()=>i(n),()=>m(document,"pointermove",o=>{var h,x;if(!i(n))return;const a=o.target;if(!A(a))return;const u={x:o.clientX,y:o.clientY},y=((h=e.triggerNode())==null?void 0:h.contains(a))||((x=e.contentNode())==null?void 0:x.contains(a)),f=!C(u,i(n));y?c():f&&(c(),e.onPointerExit())})),{isPointerInTransit:t}}function k(e,r){const t=Math.abs(r.top-e.y),n=Math.abs(r.bottom-e.y),c=Math.abs(r.right-e.x),l=Math.abs(r.left-e.x);switch(Math.min(t,n,c,l)){case l:return"left";case c:return"right";case t:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function M(e,r,t=5){const n=t*1.5;switch(r){case"top":return[{x:e.x-t,y:e.y+t},{x:e.x,y:e.y-n},{x:e.x+t,y:e.y+t}];case"bottom":return[{x:e.x-t,y:e.y-t},{x:e.x,y:e.y+n},{x:e.x+t,y:e.y-t}];case"left":return[{x:e.x+t,y:e.y-t},{x:e.x-n,y:e.y},{x:e.x+t,y:e.y+t}];case"right":return[{x:e.x-t,y:e.y-t},{x:e.x+n,y:e.y},{x:e.x-t,y:e.y+t}]}}function R(e){const{top:r,right:t,bottom:n,left:c}=e;return[{x:c,y:r},{x:t,y:r},{x:t,y:n},{x:c,y:n}]}function C(e,r){const{x:t,y:n}=e;let c=!1;for(let l=0,s=r.length-1;l<r.length;s=l++){const o=r[l].x,a=r[l].y,u=r[s].x,y=r[s].y;a>n!=y>n&&t<(u-o)*(n-a)/(y-a)+o&&(c=!c)}return c}function H(e){const r=e.slice();return r.sort((t,n)=>t.x<n.x?-1:t.x>n.x?1:t.y<n.y?-1:t.y>n.y?1:0),I(r)}function I(e){if(e.length<=1)return e.slice();const r=[];for(let n=0;n<e.length;n++){const c=e[n];for(;r.length>=2;){const l=r[r.length-1],s=r[r.length-2];if((l.x-s.x)*(c.y-s.y)>=(l.y-s.y)*(c.x-s.x))r.pop();else break}r.push(c)}r.pop();const t=[];for(let n=e.length-1;n>=0;n--){const c=e[n];for(;t.length>=2;){const l=t[t.length-1],s=t[t.length-2];if((l.x-s.x)*(c.y-s.y)>=(l.y-s.y)*(c.x-s.x))t.pop();else break}t.push(c)}return t.pop(),r.length===1&&t.length===1&&r[0].x===t[0].x&&r[0].y===t[0].y?r:r.concat(t)}export{S as u};
