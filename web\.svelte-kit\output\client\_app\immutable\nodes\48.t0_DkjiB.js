import{f as u,a as r,t as w,c as ie}from"../chunks/BasJTneF.js";import{o as Lr}from"../chunks/nZgk9enP.js";import{p as Rr,k as O,v as Qe,f as h,a as Ur,s as n,c as A,d as o,n as m,r as C,g as e,t as ee,x as Re}from"../chunks/CGmarHxI.js";import{s as re}from"../chunks/CIt1g2O9.js";import{i as te}from"../chunks/u21ee2wt.js";import{e as fr,i as mr}from"../chunks/C3w0v0gR.js";import{c as a}from"../chunks/BvdI7LR8.js";import{g as Kr}from"../chunks/CmxjS0TN.js";import{s as zr}from"../chunks/B-Xjo-Yt.js";import{C as _r}from"../chunks/DuGukytH.js";import{C as $r}from"../chunks/Cdn-N1RY.js";import{C as pr}from"../chunks/BkJY4La4.js";import{C as hr}from"../chunks/GwmmX_iF.js";import{C as gr}from"../chunks/D50jIuLr.js";import{B as K}from"../chunks/B1K98fMG.js";import{I as Ne}from"../chunks/DMTMHyMa.js";import{T as qr,a as Vr,b as yr,c as ke,d as Br,e as Fe}from"../chunks/LESefvxV.js";import{a as We,R as Xe}from"../chunks/tdzGgazS.js";import"../chunks/CgXBgsce.js";import{t as _}from"../chunks/DjPYYl4Z.js";import{P as Jr}from"../chunks/DR5zc253.js";import{R as Mr}from"../chunks/qwsZpUIl.js";import{U as Yr}from"../chunks/CzSntoiK.js";import{D as Ze,a as er,b as rr,c as tr}from"../chunks/CKh8VGVX.js";import{T as Gr}from"../chunks/CTO_B1Jk.js";import{U as Hr}from"../chunks/BSHZ37s_.js";import{T as xr}from"../chunks/Bpd96RWU.js";import{S as Qr}from"../chunks/DumgozFE.js";var Wr=u(`<div class="mb-4 rounded-md border border-amber-200 bg-amber-50 p-4 text-amber-800"><div class="flex items-center"><!> <h3 class="text-sm font-medium">Resend API Key Not Configured</h3></div> <div class="mt-2 text-sm"><p>The Resend API key is not configured. You need to set the RESEND_API_KEY environment
        variable to use audience and broadcast features.</p></div></div>`),Xr=u("<!> New",1),Zr=u('<div class="flex items-center justify-between"><div><!> <!></div> <!></div>'),et=u('<div class="flex h-40 items-center justify-center"><div class="h-6 w-6 animate-spin rounded-full border-2 border-current border-t-transparent"></div></div>'),rt=u('<div class="text-muted-foreground flex h-40 flex-col items-center justify-center"><p>No audiences found</p> <!></div>'),tt=(ar,Ue,W)=>Ue(e(W).id),at=u('<div class="flex items-center justify-between p-3"><button><div class="flex items-center"><!> </div></button> <!></div>'),ot=u('<div class="max-h-[500px] divide-y overflow-y-auto rounded-md border"></div>'),st=u("<!> <!>",1),nt=u("<!> Import",1),lt=u("<!> Add Contact",1),it=u('<div class="flex space-x-2"><!> <!></div>'),dt=u('<div class="flex items-center justify-between"><div><!> <!></div> <!></div>'),ct=u('<div class="text-muted-foreground flex h-40 items-center justify-center"><p>Select an audience to view contacts</p></div>'),ut=u('<div class="flex h-40 items-center justify-center"><div class="h-6 w-6 animate-spin rounded-full border-2 border-current border-t-transparent"></div></div>'),vt=u('<div class="text-muted-foreground flex h-40 flex-col items-center justify-center"><p>No contacts found in this audience</p> <!></div>'),ft=u("<!> <!> <!> <!> <!>",1),mt=u('<div class="flex space-x-2"><!> <!></div>'),_t=u("<!> <!> <!> <!> <!>",1),$t=u("<!> <!>",1),pt=u("<!> <!>",1),ht=u("<!> <!>",1),gt=u('<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>'),yt=u("<!> Create Audience",1),xt=u("<!> <!>",1),Pt=u('<!> <div class="space-y-4 py-4"><div class="space-y-2"><label for="audienceName" class="text-sm font-medium">Audience Name</label> <!></div></div> <!>',1),bt=u("<!> <!>",1),Ct=u('<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>'),wt=u("<!> ",1),At=u("<!> <!>",1),Et=u('<!> <div class="space-y-4 py-4"><div class="space-y-2"><label for="contactEmail" class="text-sm font-medium">Email *</label> <!></div> <div class="space-y-2"><label for="contactFirstName" class="text-sm font-medium">First Name</label> <!></div> <div class="space-y-2"><label for="contactLastName" class="text-sm font-medium">Last Name</label> <!></div></div> <!>',1),Nt=u("<!> <!>",1),kt=u('<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>'),Ft=u("<!> Import Contacts",1),jt=u("<!> <!>",1),Tt=u('<!> <div class="space-y-4 py-4"><div class="space-y-2"><label for="importFile" class="text-sm font-medium">CSV File</label> <!> <p class="text-muted-foreground mt-1 text-xs">CSV file should have columns: email, first_name, last_name</p></div></div> <!>',1),St=u('<!> <div class="grid grid-cols-1 gap-6 md:grid-cols-3"><div class="md:col-span-1"><!></div> <div class="md:col-span-2"><!></div></div> <!> <!> <!>',1);function ia(ar,Ue){Rr(Ue,!0);let W=O(Qe([])),T=O(null),G=O(Qe([])),Ke=O(!0),ze=O(!1),$e=O(""),xe=O(!1),pe=O(!1),me=O(!1),H=O(""),de=O(""),ce=O(""),_e=O(!1),ae=O(null),Pe=O(!1),be=O(null),Ce=O(!1),he=Qe({isAvailable:!1,hasApiKey:!1,error:null});const or="/api/email";Lr(async()=>{await Pr()});async function Pr(){o(Ke,!0);try{const t=await fetch(`${or}/audiences`);if(t.ok){const s=await t.json();o(W,s,!0),he.isAvailable=!0,he.hasApiKey=!0}else{const s=await t.json();s.error&&s.error.includes("API key")?(he.hasApiKey=!1,_.error("Resend API key not configured. Please set the RESEND_API_KEY environment variable.")):_.error(s.error||"Failed to load audiences")}}catch(t){console.error("Error loading audiences:",t),he.error=t.message,he.isAvailable=!1,_.error("Failed to connect to email API server.")}finally{o(Ke,!1)}}async function sr(t){if(t){o(ze,!0),o(T,e(W).find(s=>s.id===t),!0);try{const s=await fetch(`/api/email/audiences/contacts?audienceId=${t}`);if(s.ok){const i=await s.json();Array.isArray(i)?o(G,i,!0):(console.error("Unexpected response format:",i),_.error("Unexpected response format from server"),o(G,[],!0))}else{const i=await s.json();_.error(i.error||"Failed to load contacts")}}catch(s){console.error("Error loading contacts:",s),_.error("Failed to load contacts")}finally{o(ze,!1)}}}async function br(){if(!e($e)){_.error("Audience name is required");return}o(xe,!0);try{const t=await fetch(`${or}/audiences`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e($e)})});if(t.ok){const s=await t.json();o(W,[...e(W),s],!0),o($e,""),o(pe,!1),_.success("Audience created successfully")}else{const s=await t.json();_.error(s.error||"Failed to create audience")}}catch(t){console.error("Error creating audience:",t),_.error("Failed to create audience")}finally{o(xe,!1)}}async function Cr(t){var s;if(confirm("Are you sure you want to delete this audience? This action cannot be undone."))try{const i=await fetch(`/api/email/audiences?id=${t}`,{method:"DELETE"});if(i.ok)o(W,e(W).filter(X=>X.id!==t),!0),((s=e(T))==null?void 0:s.id)===t&&(o(T,null),o(G,[],!0)),_.success("Audience deleted successfully");else{const X=await i.json();_.error(X.error||"Failed to delete audience")}}catch(i){console.error("Error deleting audience:",i),_.error("Failed to delete audience")}}async function wr(){if(!e(T)){_.error("No audience selected");return}if(!e(H)){_.error("Email is required");return}o(_e,!0);try{const t=await fetch("/api/email/audiences/contacts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({audienceId:e(T).id,email:e(H),firstName:e(de),lastName:e(ce)})});if(t.ok){const s=await t.json();o(G,[...e(G),s],!0),o(H,""),o(de,""),o(ce,""),o(me,!1),_.success("Contact added successfully")}else{const s=await t.json();_.error(s.error||"Failed to add contact")}}catch(t){console.error("Error adding contact:",t),_.error("Failed to add contact")}finally{o(_e,!1)}}async function Ar(){if(!e(T)||!e(ae)){_.error("No audience or contact selected");return}if(!e(H)){_.error("Email is required");return}o(_e,!0);try{const t=await fetch("/api/email/audiences/contacts",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({audienceId:e(T).id,id:e(ae).id,email:e(H),firstName:e(de),lastName:e(ce)})});if(t.ok){const s=await t.json();o(G,e(G).map(i=>i.id===s.id?s:i),!0),o(H,""),o(de,""),o(ce,""),o(ae,null),o(me,!1),_.success("Contact updated successfully")}else{const s=await t.json();_.error(s.error||"Failed to update contact")}}catch(t){console.error("Error updating contact:",t),_.error("Failed to update contact")}finally{o(_e,!1)}}async function Er(t){if(!e(T)){_.error("No audience selected");return}if(confirm("Are you sure you want to delete this contact?"))try{const s=await fetch(`/api/email/audiences/contacts?audienceId=${e(T).id}&id=${t}`,{method:"DELETE"});if(s.ok)o(G,e(G).filter(i=>i.id!==t),!0),_.success("Contact deleted successfully");else{const i=await s.json();_.error(i.error||"Failed to delete contact")}}catch(s){console.error("Error deleting contact:",s),_.error("Failed to delete contact")}}function Nr(t){o(ae,t,!0),o(H,t.email,!0),o(de,t.first_name||"",!0),o(ce,t.last_name||"",!0),o(me,!0)}function nr(){o(ae,null),o(H,""),o(de,""),o(ce,""),o(me,!0)}function kr(t){const s=t.target;s.files&&s.files.length>0&&o(be,s.files[0],!0)}async function Fr(){if(!e(T)){_.error("No audience selected");return}if(!e(be)){_.error("Please select a CSV file");return}o(Ce,!0);try{const t=new FormData;t.append("file",e(be)),t.append("audienceId",e(T).id);const s=await fetch("/api/email/audiences/contacts/import",{method:"POST",body:t});if(s.ok){const i=await s.json();await sr(e(T).id),o(Pe,!1),o(be,null),_.success(`Imported ${i.imported} contacts successfully`)}else{const i=await s.json();_.error(i.error||"Failed to import contacts")}}catch(t){console.error("Error importing contacts:",t),_.error("Failed to import contacts")}finally{o(Ce,!1)}}var lr=St(),ir=h(lr);{var jr=t=>{var s=Wr(),i=A(s),X=A(i);Gr(X,{class:"mr-2 h-5 w-5"}),m(2),C(i),m(2),C(s),r(t,s)};te(ir,t=>{he.hasApiKey||t(jr)})}var qe=n(ir,2),Ve=A(qe),Tr=A(Ve);a(Tr,()=>_r,(t,s)=>{s(t,{children:(i,X)=>{var U=st(),Q=h(U);a(Q,()=>hr,(z,q)=>{q(z,{children:(Z,B)=>{var k=Zr(),E=A(k),S=A(E);a(S,()=>gr,(g,f)=>{f(g,{children:(y,x)=>{m();var l=w("Audiences");r(y,l)},$$slots:{default:!0}})});var V=n(S,2);a(V,()=>pr,(g,f)=>{f(g,{children:(y,x)=>{m();var l=w("Manage your email audiences");r(y,l)},$$slots:{default:!0}})}),C(E);var b=n(E,2);a(b,()=>K,(g,f)=>{f(g,{variant:"outline",size:"sm",onclick:()=>o(pe,!0),children:(y,x)=>{var l=Xr(),d=h(l);Jr(d,{class:"mr-2 h-4 w-4"}),m(),r(y,l)},$$slots:{default:!0}})}),C(k),r(Z,k)},$$slots:{default:!0}})});var oe=n(Q,2);a(oe,()=>$r,(z,q)=>{q(z,{children:(Z,B)=>{var k=ie(),E=h(k);{var S=b=>{var g=et();r(b,g)},V=(b,g)=>{{var f=x=>{var l=rt(),d=n(A(l),2);a(d,()=>K,($,p)=>{p($,{variant:"outline",class:"mt-4",onclick:()=>o(pe,!0),children:(c,v)=>{m();var P=w("Create Audience");r(c,P)},$$slots:{default:!0}})}),C(l),r(x,l)},y=x=>{var l=ot();fr(l,21,()=>e(W),mr,(d,$)=>{var p=at(),c=A(p);c.__click=[tt,sr,$];var v=A(c),P=A(v);Hr(P,{class:"text-muted-foreground mr-2 h-4 w-4"});var N=n(P);C(v),C(c);var j=n(c,2);a(j,()=>K,(I,L)=>{L(I,{variant:"ghost",size:"sm",class:"text-red-500 hover:text-red-700",onclick:()=>Cr(e($).id),children:(F,ue)=>{xr(F,{class:"h-4 w-4"})},$$slots:{default:!0}})}),C(p),ee(()=>{var I;zr(c,1,`hover:text-primary flex-1 text-left transition-colors ${((I=e(T))==null?void 0:I.id)===e($).id?"text-primary font-medium":""}`),re(N,` ${e($).name??""}`)}),r(d,p)}),C(l),r(x,l)};te(b,x=>{e(W).length===0?x(f):x(y,!1)},g)}};te(E,b=>{e(Ke)?b(S):b(V,!1)})}r(Z,k)},$$slots:{default:!0}})}),r(i,U)},$$slots:{default:!0}})}),C(Ve);var dr=n(Ve,2),Sr=A(dr);a(Sr,()=>_r,(t,s)=>{s(t,{children:(i,X)=>{var U=pt(),Q=h(U);a(Q,()=>hr,(z,q)=>{q(z,{children:(Z,B)=>{var k=dt(),E=A(k),S=A(E);a(S,()=>gr,(f,y)=>{y(f,{children:(x,l)=>{m();var d=w();ee(()=>re(d,e(T)?`Contacts in ${e(T).name}`:"Contacts")),r(x,d)},$$slots:{default:!0}})});var V=n(S,2);a(V,()=>pr,(f,y)=>{y(f,{children:(x,l)=>{m();var d=w();ee(()=>re(d,e(T)?`Manage contacts in ${e(T).name}`:"Select an audience to view contacts")),r(x,d)},$$slots:{default:!0}})}),C(E);var b=n(E,2);{var g=f=>{var y=it(),x=A(y);a(x,()=>K,(d,$)=>{$(d,{variant:"outline",size:"sm",onclick:()=>o(Pe,!0),children:(p,c)=>{var v=nt(),P=h(v);Mr(P,{class:"mr-2 h-4 w-4"}),m(),r(p,v)},$$slots:{default:!0}})});var l=n(x,2);a(l,()=>K,(d,$)=>{$(d,{size:"sm",onclick:nr,children:(p,c)=>{var v=lt(),P=h(v);Yr(P,{class:"mr-2 h-4 w-4"}),m(),r(p,v)},$$slots:{default:!0}})}),C(y),r(f,y)};te(b,f=>{e(T)&&f(g)})}C(k),r(Z,k)},$$slots:{default:!0}})});var oe=n(Q,2);a(oe,()=>$r,(z,q)=>{q(z,{children:(Z,B)=>{var k=ie(),E=h(k);{var S=b=>{var g=ct();r(b,g)},V=(b,g)=>{{var f=x=>{var l=ut();r(x,l)},y=(x,l)=>{{var d=p=>{var c=vt(),v=n(A(c),2);a(v,()=>K,(P,N)=>{N(P,{variant:"outline",class:"mt-4",onclick:nr,children:(j,I)=>{m();var L=w("Add Contact");r(j,L)},$$slots:{default:!0}})}),C(c),r(p,c)},$=p=>{var c=ie(),v=h(c);a(v,()=>qr,(P,N)=>{N(P,{children:(j,I)=>{var L=$t(),F=h(L);a(F,()=>Vr,(ve,ge)=>{ge(ve,{children:(we,Be)=>{var se=ie(),Ae=h(se);a(Ae,()=>yr,(Je,fe)=>{fe(Je,{children:(je,vr)=>{var Te=ft(),Se=h(Te);a(Se,()=>ke,(J,M)=>{M(J,{children:(Y,ye)=>{m();var D=w("Email");r(Y,D)},$$slots:{default:!0}})});var De=n(Se,2);a(De,()=>ke,(J,M)=>{M(J,{children:(Y,ye)=>{m();var D=w("First Name");r(Y,D)},$$slots:{default:!0}})});var Me=n(De,2);a(Me,()=>ke,(J,M)=>{M(J,{children:(Y,ye)=>{m();var D=w("Last Name");r(Y,D)},$$slots:{default:!0}})});var Ee=n(Me,2);a(Ee,()=>ke,(J,M)=>{M(J,{children:(Y,ye)=>{m();var D=w("Created");r(Y,D)},$$slots:{default:!0}})});var Ie=n(Ee,2);a(Ie,()=>ke,(J,M)=>{M(J,{children:(Y,ye)=>{m();var D=w("Actions");r(Y,D)},$$slots:{default:!0}})}),r(je,Te)},$$slots:{default:!0}})}),r(we,se)},$$slots:{default:!0}})});var ue=n(F,2);a(ue,()=>Br,(ve,ge)=>{ge(ve,{children:(we,Be)=>{var se=ie(),Ae=h(se);fr(Ae,17,()=>e(G),mr,(Je,fe)=>{var je=ie(),vr=h(je);a(vr,()=>yr,(Te,Se)=>{Se(Te,{children:(De,Me)=>{var Ee=_t(),Ie=h(Ee);a(Ie,()=>Fe,(D,ne)=>{ne(D,{children:(le,Oe)=>{m();var R=w();ee(()=>re(R,e(fe).email)),r(le,R)},$$slots:{default:!0}})});var J=n(Ie,2);a(J,()=>Fe,(D,ne)=>{ne(D,{children:(le,Oe)=>{m();var R=w();ee(()=>re(R,e(fe).first_name||"-")),r(le,R)},$$slots:{default:!0}})});var M=n(J,2);a(M,()=>Fe,(D,ne)=>{ne(D,{children:(le,Oe)=>{m();var R=w();ee(()=>re(R,e(fe).last_name||"-")),r(le,R)},$$slots:{default:!0}})});var Y=n(M,2);a(Y,()=>Fe,(D,ne)=>{ne(D,{children:(le,Oe)=>{m();var R=w();ee(Le=>re(R,Le),[()=>new Date(e(fe).created_at).toLocaleDateString()]),r(le,R)},$$slots:{default:!0}})});var ye=n(Y,2);a(ye,()=>Fe,(D,ne)=>{ne(D,{children:(le,Oe)=>{var R=mt(),Le=A(R);a(Le,()=>K,(Ye,Ge)=>{Ge(Ye,{variant:"ghost",size:"sm",onclick:()=>Nr(e(fe)),children:(He,Or)=>{Qr(He,{class:"h-4 w-4"})},$$slots:{default:!0}})});var Ir=n(Le,2);a(Ir,()=>K,(Ye,Ge)=>{Ge(Ye,{variant:"ghost",size:"sm",class:"text-red-500 hover:text-red-700",onclick:()=>Er(e(fe).id),children:(He,Or)=>{xr(He,{class:"h-4 w-4"})},$$slots:{default:!0}})}),C(R),r(le,R)},$$slots:{default:!0}})}),r(De,Ee)},$$slots:{default:!0}})}),r(Je,je)}),r(we,se)},$$slots:{default:!0}})}),r(j,L)},$$slots:{default:!0}})}),r(p,c)};te(x,p=>{e(G).length===0?p(d):p($,!1)},l)}};te(b,x=>{e(ze)?x(f):x(y,!1)},g)}};te(E,b=>{e(T)?b(V,!1):b(S)})}r(Z,k)},$$slots:{default:!0}})}),r(i,U)},$$slots:{default:!0}})}),C(dr),C(qe);var cr=n(qe,2);a(cr,()=>Xe,(t,s)=>{s(t,{get open(){return e(pe)},onOpenChange:i=>o(pe,i,!0),children:(i,X)=>{var U=ie(),Q=h(U);a(Q,()=>We,(oe,z)=>{z(oe,{children:(q,Z)=>{var B=Pt(),k=h(B);a(k,()=>Ze,(g,f)=>{f(g,{children:(y,x)=>{var l=ht(),d=h(l);a(d,()=>er,(p,c)=>{c(p,{children:(v,P)=>{m();var N=w("Create New Audience");r(v,N)},$$slots:{default:!0}})});var $=n(d,2);a($,()=>rr,(p,c)=>{c(p,{children:(v,P)=>{m();var N=w("Create a new audience for your email campaigns");r(v,N)},$$slots:{default:!0}})}),r(y,l)},$$slots:{default:!0}})});var E=n(k,2),S=A(E),V=n(A(S),2);a(V,()=>Ne,(g,f)=>{f(g,{id:"audienceName",placeholder:"Enter audience name",get value(){return e($e)},set value(y){o($e,y,!0)}})}),C(S),C(E);var b=n(E,2);a(b,()=>tr,(g,f)=>{f(g,{children:(y,x)=>{var l=xt(),d=h(l);a(d,()=>K,(c,v)=>{v(c,{variant:"outline",onclick:()=>o(pe,!1),get disabled(){return e(xe)},children:(P,N)=>{m();var j=w("Cancel");r(P,j)},$$slots:{default:!0}})});var $=n(d,2);const p=Re(()=>e(xe)||!e($e));a($,()=>K,(c,v)=>{v(c,{onclick:br,get disabled(){return e(p)},children:(P,N)=>{var j=yt(),I=h(j);{var L=F=>{var ue=gt();r(F,ue)};te(I,F=>{e(xe)&&F(L)})}m(),r(P,j)},$$slots:{default:!0}})}),r(y,l)},$$slots:{default:!0}})}),r(q,B)},$$slots:{default:!0}})}),r(i,U)},$$slots:{default:!0}})});var ur=n(cr,2);a(ur,()=>Xe,(t,s)=>{s(t,{get open(){return e(me)},onOpenChange:i=>o(me,i,!0),children:(i,X)=>{var U=ie(),Q=h(U);a(Q,()=>We,(oe,z)=>{z(oe,{children:(q,Z)=>{var B=Et(),k=h(B);a(k,()=>Ze,(l,d)=>{d(l,{children:($,p)=>{var c=bt(),v=h(c);a(v,()=>er,(N,j)=>{j(N,{children:(I,L)=>{m();var F=w();ee(()=>re(F,e(ae)?"Edit Contact":"Add Contact")),r(I,F)},$$slots:{default:!0}})});var P=n(v,2);a(P,()=>rr,(N,j)=>{j(N,{children:(I,L)=>{m();var F=w();ee(()=>re(F,e(ae)?"Edit contact information":"Add a new contact to the audience")),r(I,F)},$$slots:{default:!0}})}),r($,c)},$$slots:{default:!0}})});var E=n(k,2),S=A(E),V=n(A(S),2);a(V,()=>Ne,(l,d)=>{d(l,{id:"contactEmail",type:"email",placeholder:"Enter email address",get value(){return e(H)},set value($){o(H,$,!0)}})}),C(S);var b=n(S,2),g=n(A(b),2);a(g,()=>Ne,(l,d)=>{d(l,{id:"contactFirstName",placeholder:"Enter first name",get value(){return e(de)},set value($){o(de,$,!0)}})}),C(b);var f=n(b,2),y=n(A(f),2);a(y,()=>Ne,(l,d)=>{d(l,{id:"contactLastName",placeholder:"Enter last name",get value(){return e(ce)},set value($){o(ce,$,!0)}})}),C(f),C(E);var x=n(E,2);a(x,()=>tr,(l,d)=>{d(l,{children:($,p)=>{var c=At(),v=h(c);a(v,()=>K,(I,L)=>{L(I,{variant:"outline",onclick:()=>o(me,!1),get disabled(){return e(_e)},children:(F,ue)=>{m();var ve=w("Cancel");r(F,ve)},$$slots:{default:!0}})});var P=n(v,2);const N=Re(()=>e(ae)?Ar:wr),j=Re(()=>e(_e)||!e(H));a(P,()=>K,(I,L)=>{L(I,{get onclick(){return e(N)},get disabled(){return e(j)},children:(F,ue)=>{var ve=wt(),ge=h(ve);{var we=se=>{var Ae=Ct();r(se,Ae)};te(ge,se=>{e(_e)&&se(we)})}var Be=n(ge);ee(()=>re(Be,` ${e(ae)?"Update Contact":"Add Contact"}`)),r(F,ve)},$$slots:{default:!0}})}),r($,c)},$$slots:{default:!0}})}),r(q,B)},$$slots:{default:!0}})}),r(i,U)},$$slots:{default:!0}})});var Dr=n(ur,2);a(Dr,()=>Xe,(t,s)=>{s(t,{get open(){return e(Pe)},onOpenChange:i=>o(Pe,i,!0),children:(i,X)=>{var U=ie(),Q=h(U);a(Q,()=>We,(oe,z)=>{z(oe,{children:(q,Z)=>{var B=Tt(),k=h(B);a(k,()=>Ze,(g,f)=>{f(g,{children:(y,x)=>{var l=Nt(),d=h(l);a(d,()=>er,(p,c)=>{c(p,{children:(v,P)=>{m();var N=w("Import Contacts");r(v,N)},$$slots:{default:!0}})});var $=n(d,2);a($,()=>rr,(p,c)=>{c(p,{children:(v,P)=>{m();var N=w("Import contacts from a CSV file");r(v,N)},$$slots:{default:!0}})}),r(y,l)},$$slots:{default:!0}})});var E=n(k,2),S=A(E),V=n(A(S),2);a(V,()=>Ne,(g,f)=>{f(g,{id:"importFile",type:"file",accept:".csv",$$events:{change:kr}})}),m(2),C(S),C(E);var b=n(E,2);a(b,()=>tr,(g,f)=>{f(g,{children:(y,x)=>{var l=jt(),d=h(l);a(d,()=>K,(c,v)=>{v(c,{variant:"outline",onclick:()=>o(Pe,!1),get disabled(){return e(Ce)},children:(P,N)=>{m();var j=w("Cancel");r(P,j)},$$slots:{default:!0}})});var $=n(d,2);const p=Re(()=>e(Ce)||!e(be));a($,()=>K,(c,v)=>{v(c,{onclick:Fr,get disabled(){return e(p)},children:(P,N)=>{var j=Ft(),I=h(j);{var L=F=>{var ue=kt();r(F,ue)};te(I,F=>{e(Ce)&&F(L)})}m(),r(P,j)},$$slots:{default:!0}})}),r(y,l)},$$slots:{default:!0}})}),r(q,B)},$$slots:{default:!0}})}),r(i,U)},$$slots:{default:!0}})}),r(ar,lr),Ur()}Kr(["click"]);export{ia as component};
