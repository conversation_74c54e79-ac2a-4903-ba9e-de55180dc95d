import{f as o,a,c as Re,t as F}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as rt,m as Ie,f as v,a as tt,g as n,e as U,s as t,c as r,d as Q,t as R,n as l,r as e}from"../chunks/CGmarHxI.js";import{s as x}from"../chunks/CIt1g2O9.js";import{i as E}from"../chunks/u21ee2wt.js";import{e as Ne,i as Oe}from"../chunks/C3w0v0gR.js";import{h as at}from"../chunks/DYwWIJ9y.js";import{a as mr,c as st,s as ot,b as fr}from"../chunks/B-Xjo-Yt.js";import{e as it}from"../chunks/CmxjS0TN.js";import{i as lt}from"../chunks/BIEMS98f.js";import{p as nt}from"../chunks/Btcx8l8F.js";import{S as dt}from"../chunks/C6g8ubaU.js";import{B as Ee}from"../chunks/DaBofrVv.js";import{B as te}from"../chunks/B1K98fMG.js";import{C as Ke}from"../chunks/DuGukytH.js";import{C as Ue}from"../chunks/Cdn-N1RY.js";import{C as ur}from"../chunks/BkJY4La4.js";import{C as vt}from"../chunks/DETxXRrJ.js";import{C as We}from"../chunks/GwmmX_iF.js";import{C as Qe}from"../chunks/D50jIuLr.js";import{R as ct,T as pt}from"../chunks/I7hvcB12.js";import{R as mt,D as ft,a as ut}from"../chunks/tdzGgazS.js";import{R as _t,A as gt,a as ht,b as $t,c as xt,d as yt,e as bt,f as wt}from"../chunks/BnikQ10_.js";import{L as Pt}from"../chunks/BvvicRXk.js";import{T as kt}from"../chunks/VNuMAkuB.js";import{t as q}from"../chunks/DjPYYl4Z.js";import{g as Rr}from"../chunks/BiJhC7W5.js";import{C as Ct}from"../chunks/BBNNmnYR.js";import{S as jt}from"../chunks/BAawoUIy.js";import{F as At,B as St,C as _r}from"../chunks/BM9SsHQg.js";import{M as gr}from"../chunks/CwgkX8t9.js";import{B as Xe}from"../chunks/C2AK_5VT.js";import{B as hr}from"../chunks/CDnvByek.js";import{C as Mt}from"../chunks/DZCYCPd3.js";import{S as $e}from"../chunks/FAbXdqfL.js";import{C as qe}from"../chunks/DW7T7T22.js";import{T as $r}from"../chunks/C88uNE8B.js";import{T as xr}from"../chunks/DmZyh-PW.js";import{D as Tt,a as Dt,b as Rt,c as Et}from"../chunks/CKh8VGVX.js";import{B as Bt}from"../chunks/CIPPbbaT.js";import{D as Jt}from"../chunks/6BxQgNmX.js";import{E as Ft}from"../chunks/zNKWipEG.js";import{C as It}from"../chunks/BAIxhb6t.js";var Nt=o("<!> Back to Matches",1),Ot=o("<!> Saved",1),qt=o("<!> Save",1),Lt=o("<!> Share",1),zt=o("<!> Report",1),Vt=o('<a target="_blank" rel="noopener noreferrer"><!> View Original</a>'),Yt=o("<!> View Application",1),Ht=o("<!> Apply Now",1),Gt=o("<!> ",1),Kt=o('<div class="flex items-start justify-between"><div class="flex-1"><!> <!> <!> <div class="text-muted-foreground mt-2 flex items-center gap-2 text-sm"><!> <span> </span></div></div> <div class="ml-4 flex-shrink-0"><div class="bg-primary/10 text-primary flex h-16 w-16 items-center justify-center rounded-md"><!></div></div></div>'),Ut=o('<div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium"><!> <span>Salary</span></div> <p class="mt-1 text-sm font-medium"> </p></div>'),Wt=o('<div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium"><!> <span>Experience</span></div> <p class="mt-1 text-sm font-medium"> </p></div>'),Qt=o('<div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium"><!> <span>Company</span></div> <p class="mt-1 text-sm font-medium"> </p></div>'),Xt=o("<!> View Application",1),Zt=o("<!> Apply with AI",1),ea=o("<!> <!> <!>",1),ra=o('<div class="prose max-w-none"><!></div>'),ta=o("<li> </li>"),aa=o('<ul class="list-disc space-y-2 pl-5"></ul>'),sa=o("<p>No specific requirements listed.</p>"),oa=o("<li> </li>"),ia=o('<ul class="list-disc space-y-2 pl-5"></ul>'),la=o("<p>No benefits listed.</p>"),na=o("<!> <!> <!> <!>",1),da=o('<div class="bg-muted/5 mb-6 rounded-lg border p-4"><h3 class="text-muted-foreground mb-3 text-sm font-medium">Key Job Details</h3> <div class="grid grid-cols-2 gap-x-6 gap-y-4 md:grid-cols-3"><!> <div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium"><!> <span>Job Type</span></div> <div class="mt-1 flex items-center gap-2"><p class="text-sm font-medium"> </p> <!></div></div> <div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium"><!> <span>Location</span></div> <p class="mt-1 text-sm font-medium"> </p></div> <div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium"><!> <span>Posted</span></div> <p class="mt-1 text-sm font-medium"> </p></div> <!> <!></div></div> <div class="mb-6 overflow-hidden rounded-lg border"><div class="bg-primary/10 p-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="bg-primary/20 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><h3 class="font-semibold">Apply with Hirli AI</h3> <p class="text-muted-foreground text-sm">Increase your chances of getting hired</p></div></div> <!></div></div> <div class="bg-primary/5 p-4"><ul class="grid gap-2 text-sm md:grid-cols-2"><li class="flex items-start gap-2"><!> <span>AI-optimized resume tailored for this job</span></li> <li class="flex items-start gap-2"><!> <span>Keyword matching for ATS systems</span></li> <li class="flex items-start gap-2"><!> <span>Highlight relevant skills and experience</span></li> <li class="flex items-start gap-2"><!> <span>Professional formatting and layout</span></li></ul></div></div> <!>',1),va=o("<!> View Application",1),ca=o("<!> Apply Now",1),pa=o('<div class="flex flex-wrap items-center justify-between gap-2 border-b p-4"><div class="flex flex-wrap gap-2"><!> <!> <!> <!></div> <!></div> <!> <!> <!>',1),ma=o("<!> About the company",1),fa=o('<div class="flex items-center gap-4"><div class="bg-primary/10 text-primary flex h-16 w-16 items-center justify-center rounded-md"><!></div> <div><h3 class="text-lg font-semibold"> </h3> <p class="text-muted-foreground text-sm"> </p></div> <!></div> <p class="mt-4 text-sm"> </p>',1),ua=o("<!> <!>",1),_a=o("<!> AI Match Analysis",1),ga=o('<div class="flex items-center justify-between"><!> <!></div> <!>',1),ha=o(' <span class="ml-1 text-xs opacity-70"> </span>',1),$a=o(' <span class="ml-1 text-xs opacity-70"> </span>',1),xa=o('<div class="mt-3 rounded-lg border bg-yellow-50/50 p-3"><h4 class="mb-2 flex items-center gap-1.5 text-sm font-medium text-yellow-700"><!> Skills to develop</h4> <div class="flex flex-wrap gap-2"></div></div>'),ya=o("<!> Optimize your resume for this job",1),ba=o('<div class="mb-6 flex flex-col items-center"><div class="relative mb-2 h-32 w-32"><svg class="h-32 w-32 -rotate-90 transform" viewBox="0 0 100 100"><circle class="stroke-gray-200" cx="50" cy="50" r="45" fill="none" stroke-width="10"></circle><circle cx="50" cy="50" r="45" fill="none" stroke-width="10" stroke-dasharray="282.7"></circle></svg> <div class="absolute inset-0 flex items-center justify-center"><span class="text-2xl font-bold"> </span></div></div> <p class="text-muted-foreground text-center text-sm">Overall Match Score</p></div> <div class="space-y-4"><div><div class="mb-1 flex items-center justify-between text-sm"><div class="flex items-center gap-2"><div class="h-3 w-3 rounded-full bg-blue-500"></div> <span>Skills Match</span></div> <span class="font-medium"> </span></div> <div class="h-2 rounded-full bg-gray-200"><div class="h-2 rounded-full bg-blue-500"></div></div></div> <div><div class="mb-1 flex items-center justify-between text-sm"><div class="flex items-center gap-2"><div class="h-3 w-3 rounded-full bg-green-500"></div> <span>Experience Match</span></div> <span class="font-medium"> </span></div> <div class="h-2 rounded-full bg-gray-200"><div class="h-2 rounded-full bg-green-500"></div></div></div> <div><div class="mb-1 flex items-center justify-between text-sm"><div class="flex items-center gap-2"><div class="h-3 w-3 rounded-full bg-purple-500"></div> <span>Education Match</span></div> <span class="font-medium"> </span></div> <div class="h-2 rounded-full bg-gray-200"><div class="h-2 rounded-full bg-purple-500"></div></div></div></div> <div class="mt-6 rounded-lg border bg-green-50/50 p-3"><h4 class="mb-2 flex items-center gap-1.5 text-sm font-medium text-green-700"><!> Your matching skills</h4> <div class="flex flex-wrap gap-2"></div></div> <!> <!>',1),wa=o("<!> <!>",1),Pa=o("<!> Similar Jobs",1),ka=o("<!> <!>",1),Ca=o("<!> ",1),ja=o('<span class="text-xs text-gray-500"> </span>'),Aa=o('<span class="text-xs text-gray-500"> </span>'),Sa=o('<a class="hover:border-primary/30 hover:bg-primary/5 group relative block rounded-lg border p-3 transition-colors"><div class="flex justify-between"><h4 class="group-hover:text-primary line-clamp-1 font-medium"> </h4> <!></div> <div class="flex items-center gap-2"><p class="line-clamp-1 text-sm text-gray-600"> </p> <!></div> <div class="mt-2 flex items-center justify-between"><div class="flex items-center gap-1 text-xs text-gray-500"><!> <span> </span></div> <!></div> <div class="group-hover:border-primary/30 absolute inset-0 rounded-lg border-2 border-transparent transition-colors"></div></a>'),Ma=o('<a href="/dashboard/jobs"><!> Find more matching jobs</a>'),Ta=o('<div class="space-y-3"><!> <!></div>'),Da=o("<!> <!>",1),Ra=o("<!> <!>",1),Ea=o('<p class="text-muted-foreground text-xs">Please provide details to help our team review this listing.</p>'),Ba=o("<!> <!>",1),Ja=o('<!> <form class="grid gap-4 py-4"><div class="grid gap-2"><!> <!> <!></div> <div class="text-muted-foreground text-xs"><p>Common reasons for reporting:</p> <ul class="mt-1 list-disc pl-5"><li>Job posting is a scam</li> <li>Misleading information</li> <li>Discriminatory content</li> <li>Duplicate listing</li></ul></div> <!></form>',1),Fa=o("<!> <!>",1),Ia=o("<!> <!>",1),Na=o("<!> <!>",1),Oa=o("<!> <!>",1),qa=o('<!> <div class="container mx-auto px-4 py-8"><!> <div class="grid grid-cols-1 gap-6 lg:grid-cols-3"><div class="lg:col-span-2"><!> <!></div> <div class="space-y-6"><!> <!></div></div></div> <!> <!>',1);function Ts(Er,yr){rt(yr,!1);let Le=nt(yr,"data",8);const{job:i,matchScore:Ze,similarJobs:er}=Le(),Br=(i==null?void 0:i.title)||"Job Details";let xe=Ie(Le().isSaved||!1),ce=Ie(Le().isApplied||!1);const L=Le().skillMatchData||null;let Se=Ie(!1),ye=Ie(""),ze=Ie(!1);function Me(s){return s===null?"N/A":`${Math.round(s*100)}%`}function rr(s){return s===null?"bg-gray-100 text-gray-800":s>=.8?"bg-green-100 text-green-800":s>=.6?"bg-blue-100 text-blue-800":s>=.4?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}function Jr(s){return s>=.8?"bg-green-500":s>=.6?"bg-blue-500":s>=.4?"bg-yellow-500":"bg-gray-500"}function tr(){if(n(ce)){Rr("/dashboard/tracker");return}i.url&&window.open(i.url,"_blank","noopener,noreferrer"),Q(ze,!0)}async function Fr(){try{const s=q.loading("Adding to your tracker..."),f=await fetch("/api/applications/add",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({jobId:i.id,jobUrl:i.url,company:i.company,position:i.title,location:i.location})}),M=await f.json();if(q.dismiss(s),!f.ok)throw new Error(M.error||"Failed to add application");Q(ce,!0),n(xe)&&Q(xe,!1),Q(ze,!1),q.success("Application tracked",{description:"Added to your application tracker"}),setTimeout(()=>{q.message("View your application",{description:"Go to your application tracker to manage it",action:{label:"Go to Tracker",onClick:()=>Rr("/dashboard/tracker")}})},2e3)}catch(s){console.error("Error adding application:",s),q.error("Failed to add application",{description:s instanceof Error?s.message:"Please try again later"})}}async function Ir(){if(!n(ye).trim()){q.error("Please provide a reason for reporting this job",{description:"We need this information to review the listing"});return}try{const s=q.loading("Submitting report..."),f=await fetch(`/api/jobs/${i.id}/report`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({reason:n(ye)})});q.dismiss(s);let M={};try{M=await f.json()}catch(T){console.error("Error parsing JSON response:",T),M={error:"Invalid server response"}}if(!f.ok)throw new Error(M.error||"Failed to report job");Q(Se,!1),Q(ye,""),q.success("Job reported",{description:"Thank you for helping us maintain quality listings"})}catch(s){console.error("Error reporting job:",s),Q(Se,!0),q.error("Failed to report job",{description:s instanceof Error?s.message:"Please try again later"})}}async function Nr(){if(n(ce)){q.info("This job is in your applications",{description:"Applied jobs are automatically tracked"});return}try{const s=await fetch(`/api/jobs/${i.id}/save`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({notes:""})}),f=await s.json();if(!s.ok)throw new Error(f.error||"Failed to save job");Q(xe,!n(xe)),q.success(n(xe)?"Job saved":"Job removed",{description:n(xe)?"Added to your saved jobs":"Removed from your saved jobs"})}catch(s){console.error("Error saving job:",s),q.error("Failed to save job")}}function Or(){const s=document.createElement("input");s.value=window.location.href,document.body.appendChild(s);try{typeof navigator<"u"&&navigator.share?navigator.share({title:(i==null?void 0:i.title)||"Job Listing",text:`Check out this job: ${(i==null?void 0:i.title)||"Job Listing"} at ${(i==null?void 0:i.company)||"Company"}`,url:window.location.href}).then(()=>{q.success("Job shared successfully")}).catch(f=>{console.error("Error sharing job:",f),ar(s)}):ar(s)}catch(f){console.error("Error in share function:",f),ar(s)}finally{document.body.removeChild(s)}}function ar(s){try{navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(window.location.href).then(()=>{q.success("Link copied to clipboard",{description:"You can now paste and share it"})}).catch(f=>{console.error("Clipboard API failed:",f),sr(s)}):sr(s)}catch(f){console.error("Failed to copy to clipboard:",f),sr(s)}}function sr(s){try{s.select(),s.setSelectionRange(0,99999);let f=!1;try{f=document.execCommand("copy")}catch(M){console.error("execCommand error:",M)}if(f)q.success("Link copied to clipboard",{description:"You can now paste and share it"});else throw new Error("Copy command failed")}catch(f){console.error("Selection copy failed:",f),q.error("Could not copy link",{description:"Please copy the URL from the address bar"})}}function br(s){if(!s)return"Recently";const f=new Date(s),T=Math.abs(new Date().getTime()-f.getTime()),k=Math.floor(T/(1e3*60*60*24));return k===0?"Today":k===1?"Yesterday":k<7?`${k} days ago`:k<30?`${Math.floor(k/7)} weeks ago`:`${Math.floor(k/30)} months ago`}function qr(){window.history.back()}lt();var wr=qa(),Pr=v(wr);const Lr=U(()=>i==null?void 0:i.title),zr=U(()=>i==null?void 0:i.company);dt(Pr,{get title(){return`${Br} | Hirli`},description:"View detailed information about this job opportunity and take action. Apply, save, or share this job with others.",get keywords(){return`job details, job opportunity, job application, career search, job description, ${n(Lr)??""}, ${n(zr)??""}`}});var or=t(Pr,2),kr=r(or);te(kr,{variant:"outline",class:"mb-6",onclick:qr,children:(s,f)=>{var M=Nt(),T=v(M);Ct(T,{class:"mr-2 h-4 w-4"}),l(),a(s,M)},$$slots:{default:!0}});var Cr=t(kr,2),ir=r(Cr),jr=r(ir);Ke(jr,{children:(s,f)=>{var M=pa(),T=v(M),k=r(T),X=r(k);const z=U(()=>n(xe)?"default":"outline");te(X,{get variant(){return n(z)},size:"sm",onclick:Nr,get disabled(){return n(ce)},children:(d,j)=>{var c=Re(),p=v(c);{var A=u=>{var _=Ot(),b=v(_);St(b,{class:"mr-2 h-4 w-4"}),l(),a(u,_)},S=u=>{var _=qt(),b=v(_);Bt(b,{class:"mr-2 h-4 w-4"}),l(),a(u,_)};E(p,u=>{n(xe)?u(A):u(S,!1)})}a(d,c)},$$slots:{default:!0}});var Y=t(X,2);te(Y,{variant:"outline",size:"sm",onclick:Or,children:(d,j)=>{var c=Lt(),p=v(c);jt(p,{class:"mr-2 h-4 w-4"}),l(),a(d,c)},$$slots:{default:!0}});var C=t(Y,2);te(C,{variant:"outline",size:"sm",onclick:()=>Q(Se,!0),children:(d,j)=>{var c=zt(),p=v(c);At(p,{class:"mr-2 h-4 w-4"}),l(),a(d,c)},$$slots:{default:!0}});var $=t(C,2);{var y=d=>{te(d,{variant:"outline",size:"sm",asChild:!0,children:(j,c)=>{var p=Vt(),A=r(p);Ft(A,{class:"mr-2 h-4 w-4"}),l(),e(p),R(()=>mr(p,"href",i.url)),a(j,p)},$$slots:{default:!0}})};E($,d=>{i.url&&d(y)})}e(k);var w=t(k,2);const m=U(()=>n(ce)?"bg-green-600 hover:bg-green-700":"bg-primary hover:bg-primary/90");te(w,{size:"sm",onclick:tr,get class(){return n(m)},children:(d,j)=>{var c=Re(),p=v(c);{var A=u=>{var _=Yt(),b=v(_);_r(b,{class:"mr-2 h-4 w-4"}),l(),a(u,_)},S=u=>{var _=Ht(),b=v(_);$e(b,{class:"mr-2 h-4 w-4"}),l(),a(u,_)};E(p,u=>{n(ce)?u(A):u(S,!1)})}a(d,c)},$$slots:{default:!0}}),e(T);var g=t(T,2);We(g,{class:"p-6 pb-3",children:(d,j)=>{var c=Kt(),p=r(c),A=r(p);{var S=P=>{const B=U(()=>`${rr(Ze)} mb-2`);Ee(P,{variant:"secondary",get class(){return n(B)},children:(N,we)=>{var ie=Gt(),Z=v(ie);$e(Z,{class:"mr-1 h-3 w-3"});var _e=t(Z);R(Te=>x(_e,` ${Te??""} Match`),[()=>Me(Ze)],U),a(N,ie)},$$slots:{default:!0}})};E(A,P=>{Ze!==null&&P(S)})}var u=t(A,2);Qe(u,{class:"text-2xl font-bold",children:(P,B)=>{l();var N=F();R(()=>x(N,i.title)),a(P,N)},$$slots:{default:!0}});var _=t(u,2);ur(_,{class:"text-primary text-lg font-medium",children:(P,B)=>{l();var N=F();R(()=>x(N,i.company)),a(P,N)},$$slots:{default:!0}});var b=t(_,2),I=r(b);gr(I,{class:"h-4 w-4"});var V=t(I,2),G=r(V,!0);e(V),e(b),e(p);var be=t(p,2),fe=r(be),ue=r(fe);Xe(ue,{class:"h-8 w-8"}),e(fe),e(be),e(c),R(()=>x(G,i.location||"Remote")),a(d,c)},$$slots:{default:!0}});var h=t(g,2);Ue(h,{class:"p-6 pt-3",children:(d,j)=>{var c=da(),p=v(c),A=t(r(p),2),S=r(A);{var u=D=>{var K=Ut(),O=r(K),se=r(O);Jt(se,{class:"h-4 w-4"}),l(2),e(O);var ee=t(O,2),me=r(ee,!0);e(ee),e(K),R(()=>x(me,i.salary)),a(D,K)};E(S,D=>{i.salary&&D(u)})}var _=t(S,2),b=r(_),I=r(b);hr(I,{class:"h-4 w-4"}),l(2),e(b);var V=t(b,2),G=r(V),be=r(G,!0);e(G);var fe=t(G,2);{var ue=D=>{Ee(D,{variant:"outline",class:"text-xs",children:(K,O)=>{l();var se=F("Remote");a(K,se)},$$slots:{default:!0}})};E(fe,D=>{i.remoteType==="remote"&&D(ue)})}e(V),e(_);var P=t(_,2),B=r(P),N=r(B);gr(N,{class:"h-4 w-4"}),l(2),e(B);var we=t(B,2),ie=r(we,!0);e(we),e(P);var Z=t(P,2),_e=r(Z),Te=r(_e);Mt(Te,{class:"h-4 w-4"}),l(2),e(_e);var Be=t(_e,2),lr=r(Be,!0);e(Be),e(Z);var Ve=t(Z,2);{var le=D=>{var K=Wt(),O=r(K),se=r(O);hr(se,{class:"h-4 w-4"}),l(2),e(O);var ee=t(O,2),me=r(ee,!0);e(ee),e(K),R(()=>x(me,i.experienceLevel)),a(D,K)};E(Ve,D=>{i.experienceLevel&&D(le)})}var ne=t(Ve,2);{var ae=D=>{var K=Qt(),O=r(K),se=r(O);Xe(se,{class:"h-4 w-4"}),l(2),e(O);var ee=t(O,2),me=r(ee,!0);e(ee),e(K),R(()=>x(me,i.company)),a(D,K)};E(ne,D=>{i.company&&D(ae)})}e(A),e(p);var pe=t(p,2),de=r(pe),ge=r(de),ve=r(ge),Pe=r(ve),Tr=r(Pe);$e(Tr,{class:"text-primary h-5 w-5"}),e(Pe),l(2),e(ve);var Ye=t(ve,2);const He=U(()=>n(ce)?"bg-green-600 hover:bg-green-700":"bg-primary hover:bg-primary/90");te(Ye,{size:"sm",onclick:tr,get class(){return n(He)},children:(D,K)=>{var O=Re(),se=v(O);{var ee=ke=>{var re=Xt(),je=v(re);_r(je,{class:"mr-1 h-3.5 w-3.5"}),l(),a(ke,re)},me=ke=>{var re=Zt(),je=v(re);$e(je,{class:"mr-1 h-3.5 w-3.5"}),l(),a(ke,re)};E(se,ke=>{n(ce)?ke(ee):ke(me,!1)})}a(D,O)},$$slots:{default:!0}}),e(ge),e(de);var Je=t(de,2),Ge=r(Je),nr=r(Ge),Wr=r(nr);qe(Wr,{class:"text-primary mt-0.5 h-4 w-4"}),l(2),e(nr);var dr=t(nr,2),Qr=r(dr);qe(Qr,{class:"text-primary mt-0.5 h-4 w-4"}),l(2),e(dr);var vr=t(dr,2),Xr=r(vr);qe(Xr,{class:"text-primary mt-0.5 h-4 w-4"}),l(2),e(vr);var Dr=t(vr,2),Zr=r(Dr);qe(Zr,{class:"text-primary mt-0.5 h-4 w-4"}),l(2),e(Dr),e(Ge),e(Je),e(pe);var et=t(pe,2);ct(et,{children:(D,K)=>{var O=na(),se=v(O);pt(se,{class:"w-full border-b",children:(re,je)=>{var oe=ea(),Ce=v(oe);$r(Ce,{value:"description",class:"flex-1",children:(J,W)=>{l();var he=F("Description");a(J,he)},$$slots:{default:!0}});var De=t(Ce,2);$r(De,{value:"requirements",class:"flex-1",children:(J,W)=>{l();var he=F("Requirements");a(J,he)},$$slots:{default:!0}});var Fe=t(De,2);$r(Fe,{value:"benefits",class:"flex-1",children:(J,W)=>{l();var he=F("Benefits");a(J,he)},$$slots:{default:!0}}),a(re,oe)},$$slots:{default:!0}});var ee=t(se,2);xr(ee,{value:"description",class:"mt-4",children:(re,je)=>{var oe=ra(),Ce=r(oe);at(Ce,()=>i.description||"No description available."),e(oe),a(re,oe)},$$slots:{default:!0}});var me=t(ee,2);xr(me,{value:"requirements",class:"mt-4",children:(re,je)=>{var oe=Re(),Ce=v(oe);{var De=J=>{var W=aa();Ne(W,5,()=>i.requirements,Oe,(he,cr)=>{var Ae=ta(),pr=r(Ae,!0);e(Ae),R(()=>x(pr,n(cr))),a(he,Ae)}),e(W),a(J,W)},Fe=J=>{var W=sa();a(J,W)};E(Ce,J=>{i.requirements&&i.requirements.length>0?J(De):J(Fe,!1)})}a(re,oe)},$$slots:{default:!0}});var ke=t(me,2);xr(ke,{value:"benefits",class:"mt-4",children:(re,je)=>{var oe=Re(),Ce=v(oe);{var De=J=>{var W=ia();Ne(W,5,()=>i.benefits,Oe,(he,cr)=>{var Ae=oa(),pr=r(Ae,!0);e(Ae),R(()=>x(pr,n(cr))),a(he,Ae)}),e(W),a(J,W)},Fe=J=>{var W=la();a(J,W)};E(Ce,J=>{i.benefits&&i.benefits.length>0?J(De):J(Fe,!1)})}a(re,oe)},$$slots:{default:!0}}),a(D,O)},$$slots:{default:!0}}),R(D=>{x(be,i.employmentType||"Full-time"),x(ie,i.location||"Remote"),x(lr,D)},[()=>i.postedDate?br(i.postedDate):"Recently"],U),a(d,c)},$$slots:{default:!0}});var H=t(h,2);vt(H,{class:"flex justify-center p-6",children:(d,j)=>{const c=U(()=>n(ce)?"bg-green-600 px-8 hover:bg-green-700":"bg-primary hover:bg-primary/90 px-8");te(d,{onclick:tr,get class(){return n(c)},children:(p,A)=>{var S=Re(),u=v(S);{var _=I=>{var V=va(),G=v(V);_r(G,{class:"mr-2 h-4 w-4"}),l(),a(I,V)},b=I=>{var V=ca(),G=v(V);$e(G,{class:"mr-2 h-4 w-4"}),l(),a(I,V)};E(u,I=>{n(ce)?I(_):I(b,!1)})}a(p,S)},$$slots:{default:!0}})},$$slots:{default:!0}}),a(s,M)},$$slots:{default:!0}});var Vr=t(jr,2);{var Yr=s=>{Ke(s,{class:"mt-6",children:(f,M)=>{var T=ua(),k=v(T);We(k,{class:"p-6",children:(z,Y)=>{Qe(z,{class:"flex items-center gap-2",children:(C,$)=>{var y=ma(),w=v(y);Xe(w,{class:"h-5 w-5"}),l(),a(C,y)},$$slots:{default:!0}})},$$slots:{default:!0}});var X=t(k,2);Ue(X,{class:"p-6 pt-0",children:(z,Y)=>{var C=fa(),$=v(C),y=r($),w=r(y);Xe(w,{class:"h-8 w-8"}),e(y);var m=t(y,2),g=r(m),h=r(g,!0);e(g);var H=t(g,2),d=r(H,!0);e(H),e(m);var j=t(m,2);te(j,{variant:"outline",size:"sm",class:"ml-auto",children:(A,S)=>{l();var u=F("Follow");a(A,u)},$$slots:{default:!0}}),e($);var c=t($,2),p=r(c,!0);e(c),R(()=>{x(h,i.company),x(d,i.location||"Remote"),x(p,`${i.company} is hiring for the role of ${i.title}. Apply now to join their team.`)}),a(z,C)},$$slots:{default:!0}}),a(f,T)},$$slots:{default:!0}})};E(Vr,s=>{i.company&&s(Yr)})}e(ir);var Ar=t(ir,2),Sr=r(Ar);{var Hr=s=>{Ke(s,{class:"overflow-hidden",children:(f,M)=>{var T=wa(),k=v(T);We(k,{class:"bg-primary/5 p-4",children:(z,Y)=>{var C=ga(),$=v(C),y=r($);Qe(y,{class:"flex items-center gap-2",children:(h,H)=>{var d=_a(),j=v(d);$e(j,{class:"text-primary h-5 w-5"}),l(),a(h,d)},$$slots:{default:!0}});var w=t(y,2);const m=U(()=>`${rr(L.overallMatch)} px-2.5 py-1 text-base`);Ee(w,{get class(){return n(m)},children:(h,H)=>{l();var d=F();R(j=>x(d,j),[()=>Me(L.overallMatch)],U),a(h,d)},$$slots:{default:!0}}),e($);var g=t($,2);ur(g,{children:(h,H)=>{l();var d=F("How your profile matches this job");a(h,d)},$$slots:{default:!0}}),a(z,C)},$$slots:{default:!0}});var X=t(k,2);Ue(X,{class:"p-4",children:(z,Y)=>{var C=ba(),$=v(C),y=r($),w=r(y),m=t(r(w));e(w);var g=t(w,2),h=r(g),H=r(h,!0);e(h),e(g),e(y),l(2),e($);var d=t($,2),j=r(d),c=r(j),p=t(r(c),2),A=r(p,!0);e(p),e(c);var S=t(c,2),u=r(S);e(S),e(j);var _=t(j,2),b=r(_),I=t(r(b),2),V=r(I,!0);e(I),e(b);var G=t(b,2),be=r(G);e(G),e(_);var fe=t(_,2),ue=r(fe),P=t(r(ue),2),B=r(P,!0);e(P),e(ue);var N=t(ue,2),we=r(N);e(N),e(fe),e(d);var ie=t(d,2),Z=r(ie),_e=r(Z);qe(_e,{class:"h-4 w-4 text-green-600"}),l(),e(Z);var Te=t(Z,2);Ne(Te,5,()=>L.matchedSkills,Oe,(le,ne)=>{Ee(le,{variant:"outline",class:"border-green-200 bg-green-100/50 text-green-800",children:(ae,pe)=>{l();var de=ha(),ge=v(de),ve=t(ge),Pe=r(ve);e(ve),R(()=>{x(ge,`${n(ne).name??""} `),x(Pe,`(${n(ne).level??""})`)}),a(ae,de)},$$slots:{default:!0}})}),e(Te),e(ie);var Be=t(ie,2);{var lr=le=>{var ne=xa(),ae=r(ne),pe=r(ae);It(pe,{class:"h-4 w-4 text-yellow-600"}),l(),e(ae);var de=t(ae,2);Ne(de,5,()=>L.missingSkills,Oe,(ge,ve)=>{Ee(ge,{variant:"outline",class:"border-yellow-200 bg-yellow-100/50 text-yellow-800",children:(Pe,Tr)=>{l();var Ye=$a(),He=v(Ye),Je=t(He),Ge=r(Je);e(Je),R(()=>{x(He,`${n(ve).name??""} `),x(Ge,`(${n(ve).importance??""})`)}),a(Pe,Ye)},$$slots:{default:!0}})}),e(de),e(ne),a(le,ne)};E(Be,le=>{L.missingSkills&&L.missingSkills.length>0&&le(lr)})}var Ve=t(Be,2);te(Ve,{class:"mt-4 w-full",variant:"outline",children:(le,ne)=>{var ae=ya(),pe=v(ae);$e(pe,{class:"mr-2 h-4 w-4"}),l(),a(le,ae)},$$slots:{default:!0}}),R((le,ne,ae,pe,de,ge,ve,Pe)=>{ot(m,0,le),mr(m,"stroke-dashoffset",282.7-282.7*L.overallMatch),x(H,ne),x(A,ae),fr(u,`width: ${pe??""}%`),x(V,de),fr(be,`width: ${ge??""}%`),x(B,ve),fr(we,`width: ${Pe??""}%`)},[()=>st(Jr(L.overallMatch)),()=>Me(L.overallMatch),()=>Me(L.skillsMatch),()=>Math.min(L.skillsMatch*100,100),()=>Me(L.experienceMatch),()=>Math.min(L.experienceMatch*100,100),()=>Me(L.educationMatch),()=>Math.min(L.educationMatch*100,100)],U),a(z,C)},$$slots:{default:!0}}),a(f,T)},$$slots:{default:!0}})};E(Sr,s=>{L&&s(Hr)})}var Gr=t(Sr,2);{var Kr=s=>{Ke(s,{children:(f,M)=>{var T=Da(),k=v(T);We(k,{class:"bg-muted/5 p-4",children:(z,Y)=>{var C=ka(),$=v(C);Qe($,{class:"flex items-center gap-2",children:(w,m)=>{var g=Pa(),h=v(g);hr(h,{class:"h-5 w-5"}),l(),a(w,g)},$$slots:{default:!0}});var y=t($,2);ur(y,{children:(w,m)=>{l();var g=F("Jobs that match your profile and interests");a(w,g)},$$slots:{default:!0}}),a(z,C)},$$slots:{default:!0}});var X=t(k,2);Ue(X,{class:"p-4 pt-0",children:(z,Y)=>{var C=Ta(),$=r(C);Ne($,1,()=>er,Oe,(w,m)=>{var g=Sa(),h=r(g),H=r(h),d=r(H,!0);e(H);var j=t(H,2);{var c=P=>{const B=U(()=>rr(n(m).matchPercentage/100));Ee(P,{variant:"outline",get class(){return n(B)},children:(N,we)=>{var ie=Ca(),Z=v(ie);$e(Z,{class:"mr-1 h-3 w-3"});var _e=t(Z);R(()=>x(_e,` ${n(m).matchPercentage??""}% Match`)),a(N,ie)},$$slots:{default:!0}})};E(j,P=>{n(m).matchPercentage&&P(c)})}e(h);var p=t(h,2),A=r(p),S=r(A,!0);e(A);var u=t(A,2);{var _=P=>{var B=ja(),N=r(B);e(B),R(()=>x(N,`• ${n(m).salary??""}`)),a(P,B)};E(u,P=>{n(m).salary&&P(_)})}e(p);var b=t(p,2),I=r(b),V=r(I);gr(V,{class:"h-3 w-3"});var G=t(V,2),be=r(G,!0);e(G),e(I);var fe=t(I,2);{var ue=P=>{var B=Aa(),N=r(B,!0);e(B),R(we=>x(N,we),[()=>br(n(m).postedDate)],U),a(P,B)};E(fe,P=>{n(m).postedDate&&P(ue)})}e(b),l(2),e(g),R(()=>{mr(g,"href",`/dashboard/jobs/${n(m).id??""}`),x(d,n(m).title),x(S,n(m).company),x(be,n(m).location||"Remote")}),a(w,g)});var y=t($,2);te(y,{variant:"outline",class:"w-full",asChild:!0,children:(w,m)=>{var g=Ma(),h=r(g);$e(h,{class:"mr-2 h-4 w-4"}),l(),e(g),a(w,g)},$$slots:{default:!0}}),e(C),a(z,C)},$$slots:{default:!0}}),a(f,T)},$$slots:{default:!0}})};E(Gr,s=>{er&&er.length>0&&s(Kr)})}e(Ar),e(Cr),e(or);var Mr=t(or,2);mt(Mr,{get open(){return n(Se)},set open(s){Q(Se,s)},children:(s,f)=>{var M=Fa(),T=v(M);ft(T,{});var k=t(T,2);ut(k,{class:"sm:max-w-md",children:(X,z)=>{var Y=Ja(),C=v(Y);Tt(C,{children:(d,j)=>{var c=Ra(),p=v(c);Dt(p,{children:(S,u)=>{l();var _=F("Report Job");a(S,_)},$$slots:{default:!0}});var A=t(p,2);Rt(A,{children:(S,u)=>{l();var _=F("Help us maintain quality job listings by reporting issues with this job.");a(S,_)},$$slots:{default:!0}}),a(d,c)},$$slots:{default:!0}});var $=t(C,2),y=r($),w=r(y);Pt(w,{children:(d,j)=>{l();var c=F("Reason for reporting");a(d,c)},$$slots:{default:!0}});var m=t(w,2);kt(m,{class:"min-h-[120px]",get value(){return n(ye)},set value(d){Q(ye,d)},$$legacy:!0});var g=t(m,2);{var h=d=>{var j=Ea();a(d,j)};E(g,d=>{n(ye).trim().length===0&&d(h)})}e(y);var H=t(y,4);Et(H,{class:"pt-2 sm:justify-between",children:(d,j)=>{var c=Ba(),p=v(c);te(p,{type:"button",variant:"outline",onclick:()=>{Q(Se,!1),Q(ye,"")},children:(u,_)=>{l();var b=F("Cancel");a(u,b)},$$slots:{default:!0}});var A=t(p,2);const S=U(()=>!n(ye).trim());te(A,{type:"submit",variant:"destructive",get disabled(){return n(S)},children:(u,_)=>{l();var b=F("Report Job");a(u,b)},$$slots:{default:!0}}),a(d,c)},$$slots:{default:!0}}),e($),it("submit",$,d=>{d.preventDefault(),Ir()}),a(X,Y)},$$slots:{default:!0}}),a(s,M)},$$slots:{default:!0},$$legacy:!0});var Ur=t(Mr,2);_t(Ur,{get open(){return n(ze)},set open(s){Q(ze,s)},children:(s,f)=>{gt(s,{children:(M,T)=>{var k=Oa(),X=v(k);ht(X,{children:(Y,C)=>{var $=Ia(),y=v($);$t(y,{children:(m,g)=>{l();var h=F("Did you apply to this job?");a(m,h)},$$slots:{default:!0}});var w=t(y,2);xt(w,{children:(m,g)=>{l();var h=F(`We opened the job posting in a new tab. If you submitted an application, click "Yes" to add
        it to your tracker.`);a(m,h)},$$slots:{default:!0}}),a(Y,$)},$$slots:{default:!0}});var z=t(X,2);yt(z,{children:(Y,C)=>{var $=Na(),y=v($);bt(y,{children:(m,g)=>{l();var h=F("No, I didn't apply");a(m,h)},$$slots:{default:!0}});var w=t(y,2);wt(w,{onclick:Fr,children:(m,g)=>{l();var h=F("Yes, I applied");a(m,h)},$$slots:{default:!0}}),a(Y,$)},$$slots:{default:!0}}),a(M,k)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),a(Er,wr),tt()}export{Ts as component};
