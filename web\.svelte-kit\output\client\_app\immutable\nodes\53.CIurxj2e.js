import{f as c,t as d,a as t,c as ut}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as Ct,l as ht,b as nr,a as wt,f as $,s as e,h as xt,n as i,c as s,g as r,m as rt,r as a,t as R,d as it,e as nt,o as x,_ as ir,aL as V}from"../chunks/CGmarHxI.js";import{s as E}from"../chunks/CIt1g2O9.js";import{i as U}from"../chunks/u21ee2wt.js";import{k as qt}from"../chunks/DT9WCdWY.js";import{e as pt,i as _t}from"../chunks/C3w0v0gR.js";import{c as Wt}from"../chunks/BvdI7LR8.js";import{i as Et}from"../chunks/BIEMS98f.js";import{p as M}from"../chunks/Btcx8l8F.js";import{s as Lt,b as fe,a as Ft,e as lr,x as Vt}from"../chunks/CmxjS0TN.js";import{i as zt}from"../chunks/BiJhC7W5.js";import{C as Xt}from"../chunks/DuGukytH.js";import{C as Yt}from"../chunks/Cdn-N1RY.js";import{C as Zt}from"../chunks/BkJY4La4.js";import{C as er}from"../chunks/GwmmX_iF.js";import{C as tr}from"../chunks/D50jIuLr.js";import{R as Pr,T as br}from"../chunks/I7hvcB12.js";import{B as Pe}from"../chunks/B1K98fMG.js";import{B as Dt}from"../chunks/DaBofrVv.js";import{t as ot}from"../chunks/DjPYYl4Z.js";import{R as Mt,P as Sr,D as At,a as Ut}from"../chunks/tdzGgazS.js";import{S as kt,a as $r,b as Tr}from"../chunks/D0KcwhQz.js";import{D as Nt,a as It,b as Ht,c as jt}from"../chunks/CKh8VGVX.js";import{C as or}from"../chunks/-SpbofVw.js";import{M as pr}from"../chunks/QtAhPN2H.js";import{R as Cr}from"../chunks/qwsZpUIl.js";import{a as dr}from"../chunks/DDUgF6Ik.js";import{r as Gt,f as _r}from"../chunks/B-Xjo-Yt.js";import{p as vr}from"../chunks/CWmzcjye.js";import{I as Ot}from"../chunks/DMTMHyMa.js";import{L as pe}from"../chunks/BvvicRXk.js";import{T as Kt}from"../chunks/VNuMAkuB.js";import{C as Qt}from"../chunks/T7uRAIbG.js";import{R as Pt,S as bt,a as St,b as $e}from"../chunks/CGK0g3x_.js";import{e as cr,s as rr}from"../chunks/B8blszX7.js";import{S as Tt}from"../chunks/B2lQHLf_.js";import{b as gr}from"../chunks/CzsE_FAw.js";import{S as wr}from"../chunks/C2MdR6K0.js";import{H as hr}from"../chunks/CqJi5rQC.js";import{S as Er}from"../chunks/C6g8ubaU.js";import{P as Dr}from"../chunks/DR5zc253.js";import{T as ar}from"../chunks/C88uNE8B.js";import{T as sr}from"../chunks/DmZyh-PW.js";import{S as ur}from"../chunks/DumgozFE.js";import{T as mr}from"../chunks/C33xR25f.js";import{T as fr}from"../chunks/CTO_B1Jk.js";import{C as Or}from"../chunks/BAIxhb6t.js";import{C as Fr}from"../chunks/DW7T7T22.js";var kr=c("<!> <!>",1),Mr=c('<div class="flex justify-center py-8"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>'),Ar=c('<div class="border-destructive/50 bg-destructive/10 rounded-lg border p-4 text-center"><p class="text-destructive"> </p> <!></div>'),Ur=c('<div class="rounded-lg border p-6 text-center"><p class="text-muted-foreground">No history available for this maintenance event.</p></div>'),Nr=c("Status changed from <!> to <!>",1),Ir=c('<div class="bg-muted mt-2 rounded-md p-3"><p class="text-sm"> </p></div>'),Hr=c('<div class="mt-2"><p class="text-muted-foreground text-xs">Changed fields:</p> <div class="mt-1 flex flex-wrap gap-1"></div></div>'),jr=c('<div class="rounded-lg border p-4"><div class="flex items-start gap-3"><div class="mt-0.5"><!></div> <div class="flex-1"><div class="mb-1 flex items-center justify-between"><p class="text-sm font-medium"><!></p> <p class="text-muted-foreground text-xs"> </p></div> <!> <!></div></div></div>'),Rr=c('<div class="space-y-4"></div>'),Br=c('<!> <div class="py-4"><!></div> <!>',1),zr=c("<!> <!>",1);function Lr(at,h){Ct(h,!1);let Je=M(h,"eventId",8),Qe=M(h,"open",12,!1),n=M(h,"onClose",8,()=>{}),F=rt([]),_e=rt(!1),S=rt(null);function Ie(de){return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(new Date(de))}function Ve(de){switch(de){case"status_change":return Cr;case"comment":return pr;case"update":return or;default:return or}}async function be(){if(Je()){it(_e,!0),it(S,null);try{const de=await fetch(`/api/maintenance/${Je()}/history`);if(!de.ok)throw new Error(`Failed to fetch history: ${de.status}`);const Xe=await de.json();it(F,Xe)}catch(de){console.error("Error fetching maintenance history:",de),it(S,de instanceof Error?de.message:"Failed to load history")}finally{it(_e,!1)}}}ht(()=>(xt(Qe()),xt(Je())),()=>{Qe()&&Je()&&be()}),nr(),Et(),Mt(at,{get open(){return Qe()},set open(de){Qe(de)},children:(de,Xe)=>{Sr(de,{children:(Be,f)=>{var y=zr(),X=$(y);At(X,{});var He=e(X,2);Ut(He,{class:"sm:max-w-[600px]",children:(qe,et)=>{var Ye=Br(),Se=$(Ye);Nt(Se,{children:(q,ve)=>{var Ce=kr(),Y=$(Ce);It(Y,{children:(ce,we)=>{i();var Ee=d("Maintenance History");t(ce,Ee)},$$slots:{default:!0}});var Q=e(Y,2);Ht(Q,{children:(ce,we)=>{i();var Ee=d("View the history of updates and status changes for this maintenance event.");t(ce,Ee)},$$slots:{default:!0}}),t(q,Ce)},$$slots:{default:!0}});var se=e(Se,2),ge=s(se);{var Te=q=>{var ve=Mr();t(q,ve)},oe=(q,ve)=>{{var Ce=Q=>{var ce=Ar(),we=s(ce),Ee=s(we,!0);a(we);var ee=e(we,2);Pe(ee,{variant:"outline",class:"mt-2",onclick:be,children:(T,De)=>{i();var C=d("Retry");t(T,C)},$$slots:{default:!0}}),a(ce),R(()=>E(Ee,r(S))),t(Q,ce)},Y=(Q,ce)=>{{var we=ee=>{var T=Ur();t(ee,T)},Ee=ee=>{var T=Rr();pt(T,5,()=>r(F),_t,(De,C)=>{var xe=jr(),je=s(xe),ne=s(je),We=s(ne);qt(We,()=>r(C).changeType,B=>{var W=ut(),I=$(W);Wt(I,()=>Ve(r(C).changeType),(ue,L)=>{L(ue,{class:"text-muted-foreground h-5 w-5"})}),t(B,W)}),a(ne);var k=e(ne,2),Ge=s(k),o=s(Ge),w=s(o);{var Ue=B=>{var W=Nr(),I=e($(W));{var ue=Z=>{kt(Z,{get status(){return r(C).previousStatus}})};U(I,Z=>{r(C).previousStatus&&Z(ue)})}var L=e(I,2);{var te=Z=>{kt(Z,{get status(){return r(C).newStatus}})};U(L,Z=>{r(C).newStatus&&Z(te)})}t(B,W)},Oe=(B,W)=>{{var I=L=>{var te=d("Comment added");t(L,te)},ue=L=>{var te=d("Event updated");t(L,te)};U(B,L=>{r(C).changeType==="comment"?L(I):L(ue,!1)},W)}};U(w,B=>{r(C).changeType==="status_change"?B(Ue):B(Oe,!1)})}a(o);var ye=e(o,2),ie=s(ye,!0);a(ye),a(Ge);var ke=e(Ge,2);{var mt=B=>{var W=Ir(),I=s(W),ue=s(I,!0);a(I),a(W),R(()=>E(ue,r(C).comment)),t(B,W)};U(ke,B=>{r(C).comment&&B(mt)})}var tt=e(ke,2);{var lt=B=>{var W=Hr(),I=e(s(W),2);pt(I,5,()=>r(C).metadata.changedFields,_t,(ue,L)=>{Dt(ue,{variant:"outline",class:"text-xs",children:(te,Z)=>{i();var me=d();R(()=>E(me,r(L))),t(te,me)},$$slots:{default:!0}})}),a(I),a(W),t(B,W)};U(tt,B=>{var W,I;r(C).changeType==="update"&&((I=(W=r(C).metadata)==null?void 0:W.changedFields)==null?void 0:I.length)>0&&B(lt)})}a(k),a(je),a(xe),R(B=>E(ie,B),[()=>Ie(r(C).createdAt)],nt),t(De,xe)}),a(T),t(ee,T)};U(Q,ee=>{r(F).length===0?ee(we):ee(Ee,!1)},ce)}};U(q,Q=>{r(S)?Q(Ce):Q(Y,!1)},ve)}};U(ge,q=>{r(_e)?q(Te):q(oe,!1)})}a(se);var he=e(se,2);jt(he,{children:(q,ve)=>{Pe(q,{variant:"outline",onclick:n(),children:(Ce,Y)=>{i();var Q=d("Close");t(Ce,Q)},$$slots:{default:!0}})},$$slots:{default:!0}}),t(qe,Ye)},$$slots:{default:!0}}),t(Be,y)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),wt()}var Jr=c("<!> <!>",1),Vr=c('<p class="text-sm text-red-500"> </p>'),qr=c('<p class="text-sm text-red-500"> </p>'),Wr=c('<p class="text-sm text-red-500"> </p>'),Gr=c('<p class="text-sm text-red-500"> </p>'),Kr=c('<div class="mt-2"><!> <!></div>'),Qr=c("<!> <!> <!> <!>",1),Xr=c("<!> <!>",1),Yr=c("<!> <!> <!> <!> <!>",1),Zr=c("<!> <!>",1),ea=c("<!> <!>",1),ta=c('<p class="text-sm text-red-500"> </p>'),ra=c("<!> <!>",1),aa=c('<!> <form method="POST" action="?/create" id="create-maintenance-form"><input type="hidden" name="affectedServices"/> <div class="grid gap-4 py-4"><div class="grid gap-2"><!> <!> <!></div> <div class="grid gap-2"><!> <!> <!></div> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2"><!> <!> <!></div> <div class="grid gap-2"><!> <!> <!></div></div> <!> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2"><!> <div class="flex items-center gap-2"><!> <!></div></div> <div class="grid gap-2"><!> <div class="flex items-center gap-2"><!> <!></div></div></div> <div class="grid gap-2"><!> <div><!></div> <!> <p class="text-muted-foreground text-xs">Select the service that will be affected by this maintenance</p></div> <div class="flex items-center space-x-2"><!> <!></div></div> <!> <!></form>',1),sa=c("<!> <!>",1);function oa(at,h){Ct(h,!1);const[Je,Qe]=Lt(),n=()=>Ft(S(),"$createForm",Je),F=()=>Ft(Ie(),"$createErrors",Je);let _e=M(h,"open",12),S=M(h,"createForm",8),Ie=M(h,"createErrors",8),Ve=M(h,"serviceOptions",8),be=M(h,"onClose",8),de=M(h,"onSubmit",8),Xe=M(h,"resetForm",8);Et(),Mt(at,{get open(){return _e()},set open(Be){_e(Be)},children:(Be,f)=>{var y=sa(),X=$(y);At(X,{});var He=e(X,2);Ut(He,{class:"sm:max-w-[600px]",children:(qe,et)=>{var Ye=aa(),Se=$(Ye);Nt(Se,{children:(v,_)=>{var p=Jr(),G=$(p);It(G,{children:(u,m)=>{i();var b=d("Schedule Maintenance");t(u,b)},$$slots:{default:!0}});var l=e(G,2);Ht(l,{children:(u,m)=>{i();var b=d("Create a new scheduled maintenance event. This will be displayed on the system status page.");t(u,b)},$$slots:{default:!0}}),t(v,p)},$$slots:{default:!0}});var se=e(Se,2),ge=s(se);Gt(ge);var Te=e(ge,2),oe=s(Te),he=s(oe);pe(he,{for:"title",children:(v,_)=>{i();var p=d("Title");t(v,p)},$$slots:{default:!0}});var q=e(he,2);Ot(q,{id:"title",placeholder:"Database Maintenance",get value(){return n().title},set value(v){fe(S(),x(n).title=v,x(n))},$$legacy:!0});var ve=e(q,2);{var Ce=v=>{var _=Vr(),p=s(_,!0);a(_),R(()=>E(p,F().title)),t(v,_)};U(ve,v=>{F().title&&v(Ce)})}a(oe);var Y=e(oe,2),Q=s(Y);pe(Q,{for:"description",children:(v,_)=>{i();var p=d("Description");t(v,p)},$$slots:{default:!0}});var ce=e(Q,2);Kt(ce,{get value(){return n().description},set value(v){fe(S(),x(n).description=v,x(n))},$$legacy:!0});var we=e(ce,2);{var Ee=v=>{var _=qr(),p=s(_,!0);a(_),R(()=>E(p,F().description)),t(v,_)};U(we,v=>{F().description&&v(Ee)})}a(Y);var ee=e(Y,2),T=s(ee),De=s(T);pe(De,{for:"startTime",children:(v,_)=>{i();var p=d("Start Time");t(v,p)},$$slots:{default:!0}});var C=e(De,2);Ot(C,{id:"startTime",type:"datetime-local",get value(){return n().startTime},set value(v){fe(S(),x(n).startTime=v,x(n))},$$legacy:!0});var xe=e(C,2);{var je=v=>{var _=Wr(),p=s(_,!0);a(_),R(()=>E(p,F().startTime)),t(v,_)};U(xe,v=>{F().startTime&&v(je)})}a(T);var ne=e(T,2),We=s(ne);pe(We,{for:"endTime",children:(v,_)=>{i();var p=d("End Time");t(v,p)},$$slots:{default:!0}});var k=e(We,2);Ot(k,{id:"endTime",type:"datetime-local",get value(){return n().endTime},set value(v){fe(S(),x(n).endTime=v,x(n))},$$legacy:!0});var Ge=e(k,2);{var o=v=>{var _=Gr(),p=s(_,!0);a(_),R(()=>E(p,F().endTime)),t(v,_)};U(Ge,v=>{F().endTime&&v(o)})}a(ne),a(ee);var w=e(ee,2);{var Ue=v=>{var _=Kr(),p=s(_);pe(p,{children:(l,u)=>{i();var m=d("Progress");t(l,m)},$$slots:{default:!0}});var G=e(p,2);Tr(G,{get startTime(){return n().startTime},get endTime(){return n().endTime},get status(){return n().status}}),a(_),t(v,_)};U(w,v=>{n().startTime&&n().endTime&&v(Ue)})}var Oe=e(w,2),ye=s(Oe),ie=s(ye);pe(ie,{for:"status",children:(v,_)=>{i();var p=d("Status");t(v,p)},$$slots:{default:!0}});var ke=e(ie,2),mt=s(ke);Pt(mt,{type:"single",get value(){return n().status},set value(v){fe(S(),x(n).status=v,x(n))},children:(v,_)=>{var p=Xr(),G=$(p);bt(G,{class:"w-full",children:(u,m)=>{Tt(u,{placeholder:"Select status"})},$$slots:{default:!0}});var l=e(G,2);St(l,{class:"w-full",children:(u,m)=>{var b=Qr(),le=$(b);$e(le,{value:"scheduled",children:(J,P)=>{i();var A=d("Scheduled");t(J,A)},$$slots:{default:!0}});var D=e(le,2);$e(D,{value:"in-progress",children:(J,P)=>{i();var A=d("In Progress");t(J,A)},$$slots:{default:!0}});var z=e(D,2);$e(z,{value:"completed",children:(J,P)=>{i();var A=d("Completed");t(J,A)},$$slots:{default:!0}});var O=e(z,2);$e(O,{value:"cancelled",children:(J,P)=>{i();var A=d("Cancelled");t(J,A)},$$slots:{default:!0}}),t(u,b)},$$slots:{default:!0}}),t(v,p)},$$slots:{default:!0},$$legacy:!0});var tt=e(mt,2);kt(tt,{get status(){return n().status}}),a(ke),a(ye);var lt=e(ye,2),B=s(lt);pe(B,{for:"severity",children:(v,_)=>{i();var p=d("Severity");t(v,p)},$$slots:{default:!0}});var W=e(B,2),I=s(W);Pt(I,{type:"single",get value(){return n().severity},set value(v){fe(S(),x(n).severity=v,x(n))},children:(v,_)=>{var p=Zr(),G=$(p);bt(G,{class:"w-full",children:(u,m)=>{Tt(u,{placeholder:"Select severity"})},$$slots:{default:!0}});var l=e(G,2);St(l,{class:"w-full",children:(u,m)=>{var b=Yr(),le=$(b);$e(le,{value:"info",children:(P,A)=>{i();var re=d("Information");t(P,re)},$$slots:{default:!0}});var D=e(le,2);$e(D,{value:"maintenance",children:(P,A)=>{i();var re=d("Maintenance");t(P,re)},$$slots:{default:!0}});var z=e(D,2);$e(z,{value:"minor",children:(P,A)=>{i();var re=d("Minor Outage");t(P,re)},$$slots:{default:!0}});var O=e(z,2);$e(O,{value:"major",children:(P,A)=>{i();var re=d("Major Outage");t(P,re)},$$slots:{default:!0}});var J=e(O,2);$e(J,{value:"critical",children:(P,A)=>{i();var re=d("Critical Outage");t(P,re)},$$slots:{default:!0}}),t(u,b)},$$slots:{default:!0}}),t(v,p)},$$slots:{default:!0},$$legacy:!0});var ue=e(I,2);$r(ue,{get severity(){return n().severity}}),a(W),a(lt),a(Oe);var L=e(Oe,2),te=s(L);pe(te,{for:"affectedServices",children:(v,_)=>{i();var p=d("Affected Services");t(v,p)},$$slots:{default:!0}});var Z=e(te,2),me=s(Z);Pt(me,{type:"single",onValueChange:v=>{v?fe(S(),x(n).affectedServices=[v],x(n)):fe(S(),x(n).affectedServices=[Ve()[0].value],x(n))},get value(){return n().affectedServices[0]},set value(v){fe(S(),x(n).affectedServices[0]=v,x(n))},children:(v,_)=>{var p=ea(),G=$(p);bt(G,{class:"w-full",children:(u,m)=>{Tt(u,{placeholder:"Select a service"})},$$slots:{default:!0}});var l=e(G,2);St(l,{class:"w-full",children:(u,m)=>{var b=ut(),le=$(b);pt(le,1,Ve,_t,(D,z)=>{$e(D,{get value(){return r(z).value},children:(O,J)=>{i();var P=d();R(()=>E(P,r(z).label)),t(O,P)},$$slots:{default:!0}})}),t(u,b)},$$slots:{default:!0}}),t(v,p)},$$slots:{default:!0},$$legacy:!0}),a(Z);var ze=e(Z,2);{var Ze=v=>{var _=ta(),p=s(_,!0);a(_),R(()=>E(p,F().affectedServices)),t(v,_)};U(ze,v=>{F().affectedServices&&v(Ze)})}i(2),a(L);var Me=e(L,2),H=s(Me);Qt(H,{id:"sendNotification",name:"sendNotification",get checked(){return n().sendNotification},set checked(v){fe(S(),x(n).sendNotification=v,x(n))},$$legacy:!0});var j=e(H,2);pe(j,{for:"sendNotification",class:"text-sm font-normal",children:(v,_)=>{i();var p=d("Send notification to all users");t(v,p)},$$slots:{default:!0}}),a(Me),a(Te);var dt=e(Te,2);U(dt,v=>{});var g=e(dt,2);jt(g,{children:(v,_)=>{var p=ra(),G=$(p);Pe(G,{type:"button",variant:"outline",onclick:()=>{Xe()(),be()()},children:(u,m)=>{i();var b=d("Cancel");t(u,b)},$$slots:{default:!0}});var l=e(G,2);Pe(l,{type:"submit",children:(u,m)=>{i();var b=d("Create");t(u,b)},$$slots:{default:!0}}),t(v,p)},$$slots:{default:!0}}),a(se),dr(se,v=>{var _;return(_=cr)==null?void 0:_(v)}),ir(()=>lr("submit",se,vr(function(...v){var _;(_=de())==null||_.apply(this,v)}))),R(()=>_r(ge,n().affectedServices)),t(qe,Ye)},$$slots:{default:!0}}),t(Be,y)},$$slots:{default:!0},$$legacy:!0}),wt(),Qe()}var na=c("<!> <!>",1),ia=c('<p class="text-sm text-red-500"> </p>'),la=c('<p class="text-sm text-red-500"> </p>'),da=c('<p class="text-sm text-red-500"> </p>'),va=c('<p class="text-sm text-red-500"> </p>'),ca=c("<!> <!> <!> <!>",1),ua=c("<!> <!>",1),ma=c("<!> <!> <!> <!> <!>",1),fa=c("<!> <!>",1),$a=c("<!> <!>",1),pa=c('<p class="text-sm text-red-500"> </p>'),_a=c("<!> Add Status Update",1),ga=c('<span class="text-xs font-medium">Comment</span>'),ha=c('<span class="text-xs font-medium">Update</span>'),xa=c('<p class="mt-1 text-sm"> </p>'),ya=c('<div class="relative border-l-2 border-gray-200 pl-4 dark:border-gray-700"><div class="absolute -left-1.5 top-1 h-3 w-3 rounded-full bg-gray-200 dark:bg-gray-700"></div> <div class="flex items-center justify-between"><div class="flex items-center gap-2"><!> <p class="text-xs font-medium"> </p></div></div> <!></div>'),Pa=c('<div class="max-h-60 rounded-md border p-3"><div class="space-y-3"></div></div>'),ba=c("<!> View History",1),Sa=c('<div class="flex w-full items-center justify-between"><!> <div class="flex gap-2"><!> <!></div></div>'),Ta=c('<!> <form method="POST" action="?/update" id="edit-maintenance-form"><input type="hidden" name="id"/> <input type="hidden" name="affectedServices"/> <div class="grid gap-4 py-4"><div class="grid gap-2"><!> <!> <!></div> <div class="grid gap-2"><!> <!> <!></div> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2"><!> <!> <!></div> <div class="grid gap-2"><!> <!> <!></div></div> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2"><!> <div class="flex items-center gap-2"><!> <!></div></div> <div class="grid gap-2"><!> <div class="flex items-center gap-2"><!> <!></div></div></div> <div class="grid gap-2"><!> <!> <!></div> <div class="flex items-center justify-between"><h3 class="text-sm font-medium">Comments & Status Updates</h3> <!></div> <!> <div class="mt-4 flex items-center space-x-2"><!> <!></div></div> <!> <!></form>',1),Ca=c("<!> <!>",1);function wa(at,h){Ct(h,!1);const[Je,Qe]=Lt(),n=()=>Ft(S(),"$editForm",Je),F=()=>Ft(Ie(),"$editErrors",Je);let _e=M(h,"open",12),S=M(h,"editForm",8),Ie=M(h,"editErrors",8),Ve=M(h,"serviceOptions",8),be=M(h,"eventHistory",8),de=M(h,"onClose",8),Xe=M(h,"onSubmit",8),Be=M(h,"resetForm",8),f=M(h,"onOpenHistory",8),y=M(h,"onOpenAddUpdate",8);Et(),Mt(at,{get open(){return _e()},set open(X){_e(X)},children:(X,He)=>{var qe=Ca(),et=$(qe);At(et,{});var Ye=e(et,2);Ut(Ye,{class:"sm:max-w-[600px]",children:(Se,se)=>{var ge=Ta(),Te=$(ge);Nt(Te,{children:(l,u)=>{var m=na(),b=$(m);It(b,{children:(D,z)=>{i();var O=d("Edit Maintenance Event");t(D,O)},$$slots:{default:!0}});var le=e(b,2);Ht(le,{children:(D,z)=>{i();var O=d("Update the maintenance event details.");t(D,O)},$$slots:{default:!0}}),t(l,m)},$$slots:{default:!0}});var oe=e(Te,2),he=s(oe);Gt(he);var q=e(he,2);Gt(q);var ve=e(q,2),Ce=s(ve),Y=s(Ce);pe(Y,{for:"edit-title",children:(l,u)=>{i();var m=d("Title");t(l,m)},$$slots:{default:!0}});var Q=e(Y,2);Ot(Q,{id:"edit-title",placeholder:"Database Maintenance",get value(){return n().title},set value(l){fe(S(),x(n).title=l,x(n))},$$legacy:!0});var ce=e(Q,2);{var we=l=>{var u=ia(),m=s(u,!0);a(u),R(()=>E(m,F().title)),t(l,u)};U(ce,l=>{F().title&&l(we)})}a(Ce);var Ee=e(Ce,2),ee=s(Ee);pe(ee,{for:"edit-description",children:(l,u)=>{i();var m=d("Description");t(l,m)},$$slots:{default:!0}});var T=e(ee,2);Kt(T,{get value(){return n().description},set value(l){fe(S(),x(n).description=l,x(n))},$$legacy:!0});var De=e(T,2);{var C=l=>{var u=la(),m=s(u,!0);a(u),R(()=>E(m,F().description)),t(l,u)};U(De,l=>{F().description&&l(C)})}a(Ee);var xe=e(Ee,2),je=s(xe),ne=s(je);pe(ne,{for:"edit-startTime",children:(l,u)=>{i();var m=d("Start Time");t(l,m)},$$slots:{default:!0}});var We=e(ne,2);Ot(We,{id:"edit-startTime",type:"datetime-local",get value(){return n().startTime},set value(l){fe(S(),x(n).startTime=l,x(n))},$$legacy:!0});var k=e(We,2);{var Ge=l=>{var u=da(),m=s(u,!0);a(u),R(()=>E(m,F().startTime)),t(l,u)};U(k,l=>{F().startTime&&l(Ge)})}a(je);var o=e(je,2),w=s(o);pe(w,{for:"edit-endTime",children:(l,u)=>{i();var m=d("End Time");t(l,m)},$$slots:{default:!0}});var Ue=e(w,2);Ot(Ue,{id:"edit-endTime",type:"datetime-local",get value(){return n().endTime},set value(l){fe(S(),x(n).endTime=l,x(n))},$$legacy:!0});var Oe=e(Ue,2);{var ye=l=>{var u=va(),m=s(u,!0);a(u),R(()=>E(m,F().endTime)),t(l,u)};U(Oe,l=>{F().endTime&&l(ye)})}a(o),a(xe);var ie=e(xe,2),ke=s(ie),mt=s(ke);pe(mt,{for:"edit-status",children:(l,u)=>{i();var m=d("Current Status");t(l,m)},$$slots:{default:!0}});var tt=e(mt,2),lt=s(tt);Pt(lt,{type:"single",get value(){return n().status},set value(l){fe(S(),x(n).status=l,x(n))},children:(l,u)=>{var m=ua(),b=$(m);bt(b,{class:"w-full",children:(D,z)=>{Tt(D,{placeholder:"Select status"})},$$slots:{default:!0}});var le=e(b,2);St(le,{class:"w-full",children:(D,z)=>{var O=ca(),J=$(O);$e(J,{value:"scheduled",children:(Fe,ae)=>{i();var Ne=d("Scheduled");t(Fe,Ne)},$$slots:{default:!0}});var P=e(J,2);$e(P,{value:"in-progress",children:(Fe,ae)=>{i();var Ne=d("In Progress");t(Fe,Ne)},$$slots:{default:!0}});var A=e(P,2);$e(A,{value:"completed",children:(Fe,ae)=>{i();var Ne=d("Completed");t(Fe,Ne)},$$slots:{default:!0}});var re=e(A,2);$e(re,{value:"cancelled",children:(Fe,ae)=>{i();var Ne=d("Cancelled");t(Fe,Ne)},$$slots:{default:!0}}),t(D,O)},$$slots:{default:!0}}),t(l,m)},$$slots:{default:!0},$$legacy:!0});var B=e(lt,2);kt(B,{get status(){return n().status}}),a(tt),a(ke);var W=e(ke,2),I=s(W);pe(I,{for:"edit-severity",children:(l,u)=>{i();var m=d("Severity");t(l,m)},$$slots:{default:!0}});var ue=e(I,2),L=s(ue);Pt(L,{type:"single",get value(){return n().severity},set value(l){fe(S(),x(n).severity=l,x(n))},children:(l,u)=>{var m=fa(),b=$(m);bt(b,{class:"w-full",children:(D,z)=>{Tt(D,{placeholder:"Select severity"})},$$slots:{default:!0}});var le=e(b,2);St(le,{class:"w-full",children:(D,z)=>{var O=ma(),J=$(O);$e(J,{value:"info",children:(ae,Ne)=>{i();var Le=d("Information");t(ae,Le)},$$slots:{default:!0}});var P=e(J,2);$e(P,{value:"maintenance",children:(ae,Ne)=>{i();var Le=d("Maintenance");t(ae,Le)},$$slots:{default:!0}});var A=e(P,2);$e(A,{value:"minor",children:(ae,Ne)=>{i();var Le=d("Minor Outage");t(ae,Le)},$$slots:{default:!0}});var re=e(A,2);$e(re,{value:"major",children:(ae,Ne)=>{i();var Le=d("Major Outage");t(ae,Le)},$$slots:{default:!0}});var Fe=e(re,2);$e(Fe,{value:"critical",children:(ae,Ne)=>{i();var Le=d("Critical Outage");t(ae,Le)},$$slots:{default:!0}}),t(D,O)},$$slots:{default:!0}}),t(l,m)},$$slots:{default:!0},$$legacy:!0});var te=e(L,2);$r(te,{get severity(){return n().severity}}),a(ue),a(W),a(ie);var Z=e(ie,2),me=s(Z);pe(me,{for:"edit-affectedServices",children:(l,u)=>{i();var m=d("Affected Services");t(l,m)},$$slots:{default:!0}});var ze=e(me,2);Pt(ze,{type:"single",onValueChange:l=>{l?fe(S(),x(n).affectedServices=[l],x(n)):fe(S(),x(n).affectedServices=[Ve()[0].value],x(n))},get value(){return n().affectedServices[0]},set value(l){fe(S(),x(n).affectedServices[0]=l,x(n))},children:(l,u)=>{var m=$a(),b=$(m);bt(b,{class:"w-full",children:(D,z)=>{Tt(D,{placeholder:"Select a service"})},$$slots:{default:!0}});var le=e(b,2);St(le,{class:"w-full",children:(D,z)=>{var O=ut(),J=$(O);pt(J,1,Ve,_t,(P,A)=>{$e(P,{get value(){return r(A).value},children:(re,Fe)=>{i();var ae=d();R(()=>E(ae,r(A).label)),t(re,ae)},$$slots:{default:!0}})}),t(D,O)},$$slots:{default:!0}}),t(l,m)},$$slots:{default:!0},$$legacy:!0});var Ze=e(ze,2);{var Me=l=>{var u=pa(),m=s(u,!0);a(u),R(()=>E(m,F().affectedServices)),t(l,u)};U(Ze,l=>{F().affectedServices&&l(Me)})}a(Z);var H=e(Z,2),j=e(s(H),2);Pe(j,{type:"button",variant:"outline",class:"gap-2",get onclick(){return y()},children:(l,u)=>{var m=_a(),b=$(m);pr(b,{class:"h-4 w-4"}),i(),t(l,m)},$$slots:{default:!0}}),a(H);var dt=e(H,2);wr(dt,{orientation:"vertical",class:"!mb-0 flex h-[125px] flex-col space-y-4",children:(l,u)=>{var m=ut(),b=$(m);{var le=D=>{var z=Pa(),O=s(z);pt(O,5,be,_t,(J,P)=>{var A=ya(),re=e(s(A),2),Fe=s(re),ae=s(Fe);{var Ne=N=>{kt(N,{get status(){return r(P).newStatus}})},Le=(N,Re)=>{{var ct=Ae=>{var Ke=ga();t(Ae,Ke)},K=Ae=>{var Ke=ha();t(Ae,Ke)};U(N,Ae=>{r(P).changeType==="comment"?Ae(ct):Ae(K,!1)},Re)}};U(ae,N=>{r(P).changeType==="status_change"&&r(P).newStatus?N(Ne):N(Le,!1)})}var ft=e(ae,2),vt=s(ft,!0);a(ft),a(Fe),a(re);var $t=e(re,2);{var gt=N=>{var Re=xa(),ct=s(Re,!0);a(Re),R(()=>E(ct,r(P).comment)),t(N,Re)};U($t,N=>{r(P).comment&&N(gt)})}a(A),R(N=>E(vt,N),[()=>new Date(r(P).createdAt).toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})],nt),t(J,A)}),a(O),a(z),t(D,z)};U(b,D=>{be()&&be().length>0&&D(le)})}t(l,m)},$$slots:{default:!0}});var g=e(dt,2),v=s(g);Qt(v,{id:"edit-sendNotification",get checked(){return n().sendNotification},set checked(l){fe(S(),x(n).sendNotification=l,x(n))},$$legacy:!0});var _=e(v,2);pe(_,{for:"edit-sendNotification",class:"text-sm font-normal",children:(l,u)=>{i();var m=d("Send notification to all users about this update");t(l,m)},$$slots:{default:!0}}),a(g),a(ve);var p=e(ve,2);U(p,l=>{});var G=e(p,2);jt(G,{children:(l,u)=>{var m=Sa(),b=s(m);Pe(b,{type:"button",variant:"outline",size:"sm",class:"gap-1",get onclick(){return f()},children:(O,J)=>{var P=ba(),A=$(P);hr(A,{class:"h-4 w-4"}),i(),t(O,P)},$$slots:{default:!0}});var le=e(b,2),D=s(le);Pe(D,{type:"button",variant:"outline",onclick:()=>{Be()(),de()()},children:(O,J)=>{i();var P=d("Cancel");t(O,P)},$$slots:{default:!0}});var z=e(D,2);Pe(z,{type:"submit",children:(O,J)=>{i();var P=d("Update");t(O,P)},$$slots:{default:!0}}),a(le),a(m),t(l,m)},$$slots:{default:!0}}),a(oe),dr(oe,l=>{var u;return(u=cr)==null?void 0:u(l)}),ir(()=>lr("submit",oe,vr(function(...l){var u;(u=Xe())==null||u.apply(this,l)}))),R(()=>_r(q,n().affectedServices)),gr(he,()=>n().id,l=>fe(S(),x(n).id=l,x(n))),t(Se,ge)},$$slots:{default:!0}}),t(X,qe)},$$slots:{default:!0},$$legacy:!0}),wt(),Qe()}var Ea=c("<!> <!>",1),Da=c("<!> <!>",1),Oa=c('<form method="POST" action="?/delete" id="delete-maintenance-form"><input type="hidden" name="id"/> <div class="py-4"><p class="font-medium"> </p> <p class="text-muted-foreground text-sm"> </p></div> <!></form>'),Fa=c("<!> <!>",1),ka=c("<!> <!>",1);function Ma(at,h){Ct(h,!1);const[Je,Qe]=Lt(),n=()=>Ft(_e(),"$deleteForm",Je);let F=M(h,"open",12),_e=M(h,"deleteForm",8),S=M(h,"selectedEvent",8),Ie=M(h,"onClose",8),Ve=M(h,"onSubmit",8);Et(),Mt(at,{get open(){return F()},set open(be){F(be)},children:(be,de)=>{var Xe=ka(),Be=$(Xe);At(Be,{});var f=e(Be,2);Ut(f,{children:(y,X)=>{var He=Fa(),qe=$(He);Nt(qe,{children:(Se,se)=>{var ge=Ea(),Te=$(ge);It(Te,{children:(he,q)=>{i();var ve=d("Delete Maintenance Event");t(he,ve)},$$slots:{default:!0}});var oe=e(Te,2);Ht(oe,{children:(he,q)=>{i();var ve=d("Are you sure you want to delete this maintenance event? This action cannot be undone.");t(he,ve)},$$slots:{default:!0}}),t(Se,ge)},$$slots:{default:!0}});var et=e(qe,2);{var Ye=Se=>{var se=Oa(),ge=s(se);Gt(ge);var Te=e(ge,2),oe=s(Te),he=s(oe,!0);a(oe);var q=e(oe,2),ve=s(q,!0);a(q),a(Te);var Ce=e(Te,2);jt(Ce,{children:(Y,Q)=>{var ce=Da(),we=$(ce);Pe(we,{type:"button",variant:"outline",get onclick(){return Ie()},children:(ee,T)=>{i();var De=d("Cancel");t(ee,De)},$$slots:{default:!0}});var Ee=e(we,2);Pe(Ee,{type:"submit",variant:"destructive",children:(ee,T)=>{i();var De=d("Delete");t(ee,De)},$$slots:{default:!0}}),t(Y,ce)},$$slots:{default:!0}}),a(se),dr(se,Y=>{var Q;return(Q=cr)==null?void 0:Q(Y)}),ir(()=>lr("submit",se,vr(function(...Y){var Q;(Q=Ve())==null||Q.apply(this,Y)}))),R(()=>{E(he,S().title),E(ve,S().description)}),gr(ge,()=>n().id,Y=>fe(_e(),x(n).id=Y,x(n))),t(Se,se)};U(et,Se=>{S()&&Se(Ye)})}t(y,He)},$$slots:{default:!0}}),t(be,Xe)},$$slots:{default:!0},$$legacy:!0}),wt(),Qe()}var Aa=c("<!> <!>",1),Ua=c("<!> <!>",1),Na=c('<!> <div class="py-4"><div class="grid gap-4"><div class="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2"><!> <div class="flex items-center gap-2"><!> <p class="text-sm"> </p></div></div> <div class="grid gap-2"><!> <div class="bg-muted flex h-9 items-center rounded-md border px-3 py-1"> </div> <p class="text-muted-foreground text-xs">Current date and time will be used for this comment</p></div></div> <div class="grid gap-2"><!> <!> <p class="text-muted-foreground text-xs">This comment will be recorded in the maintenance history along with the status change</p></div> <div class="mt-4 flex items-center space-x-2"><!> <!></div></div></div> <!>',1),Ia=c("<!> <!>",1);function Ha(at,h){Ct(h,!1);const Je=rt(),Qe=rt(),n=rt(),F=rt(),_e=rt();let S=M(h,"open",12),Ie=M(h,"commentAction",8);M(h,"commentEvent",8);let Ve=M(h,"commentText",12),be=M(h,"onClose",8),de=M(h,"onSubmit",8),Xe=M(h,"onCommentTextChange",8),Be=rt(!1);ht(()=>(xt(Xe()),xt(Ve())),()=>{Xe()(Ve())}),ht(()=>xt(Ie()),()=>{it(Je,Ie()==="start"?"Start Maintenance":"Complete Maintenance")}),ht(()=>xt(Ie()),()=>{it(Qe,Ie()==="start"?"in-progress":"resolved")}),ht(()=>xt(Ie()),()=>{it(n,Ie()==="start"?"In Progress":"Resolved")}),ht(()=>xt(Ie()),()=>{it(F,Ie()==="start"?"Start Maintenance":"Complete Maintenance")}),ht(()=>{},()=>{it(_e,new Date().toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))}),nr(),Et(),Mt(at,{get open(){return S()},set open(f){S(f)},children:(f,y)=>{var X=Ia(),He=$(X);At(He,{});var qe=e(He,2);Ut(qe,{class:"sm:max-w-[500px]",children:(et,Ye)=>{var Se=Na(),se=$(Se);Nt(se,{children:(k,Ge)=>{var o=Aa(),w=$(o);It(w,{children:(Oe,ye)=>{i();var ie=d();R(()=>E(ie,r(Je))),t(Oe,ie)},$$slots:{default:!0}});var Ue=e(w,2);Ht(Ue,{children:(Oe,ye)=>{i();var ie=d("Update the maintenance status and add a comment. This will be recorded in the history.");t(Oe,ie)},$$slots:{default:!0}}),t(k,o)},$$slots:{default:!0}});var ge=e(se,2),Te=s(ge),oe=s(Te),he=s(oe),q=s(he);pe(q,{children:(k,Ge)=>{i();var o=d("Status Update");t(k,o)},$$slots:{default:!0}});var ve=e(q,2),Ce=s(ve);kt(Ce,{get status(){return r(Qe)}});var Y=e(Ce,2),Q=s(Y,!0);a(Y),a(ve),a(he);var ce=e(he,2),we=s(ce);pe(we,{for:"comment-date",children:(k,Ge)=>{i();var o=d("Comment Date");t(k,o)},$$slots:{default:!0}});var Ee=e(we,2),ee=s(Ee,!0);a(Ee),i(2),a(ce),a(oe);var T=e(oe,2),De=s(T);pe(De,{for:"comment",children:(k,Ge)=>{i();var o=d("Comment");t(k,o)},$$slots:{default:!0}});var C=e(De,2);Kt(C,{get value(){return Ve()},set value(k){Ve(k)},$$legacy:!0}),i(2),a(T);var xe=e(T,2),je=s(xe);Qt(je,{id:"comment-sendNotification",get checked(){return r(Be)},set checked(k){it(Be,k)},$$legacy:!0});var ne=e(je,2);pe(ne,{for:"comment-sendNotification",class:"text-sm font-normal",children:(k,Ge)=>{i();var o=d("Send notification to all users about this update");t(k,o)},$$slots:{default:!0}}),a(xe),a(Te),a(ge);var We=e(ge,2);jt(We,{children:(k,Ge)=>{var o=Ua(),w=$(o);Pe(w,{type:"button",variant:"outline",get onclick(){return be()},children:(Oe,ye)=>{i();var ie=d("Cancel");t(Oe,ie)},$$slots:{default:!0}});var Ue=e(w,2);Pe(Ue,{type:"button",get onclick(){return de()},children:(Oe,ye)=>{i();var ie=d();R(()=>E(ie,r(F))),t(Oe,ie)},$$slots:{default:!0}}),t(k,o)},$$slots:{default:!0}}),R(()=>{E(Q,r(n)),E(ee,r(_e))}),t(et,Se)},$$slots:{default:!0}}),t(f,X)},$$slots:{default:!0},$$legacy:!0}),wt()}var ja=c("<!> <!>",1),Ra=c("<!> <!> <!> <!> <!>",1),Ba=c("<!> <!>",1),za=c("<!> <!>",1),La=c('<!> <div class="py-4"><div class="grid gap-4"><div class="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2"><!> <!></div> <div class="grid gap-2"><!> <div class="bg-muted flex h-9 items-center rounded-md border px-3 py-1"> </div></div></div> <div class="grid gap-2"><!> <!> <p class="text-muted-foreground text-xs">This comment will be recorded in the maintenance history along with the status update</p></div> <div class="mt-4 flex items-center space-x-2"><!> <!></div></div></div> <!>',1),Ja=c("<!> <!>",1);function Va(at,h){Ct(h,!1);const[Je,Qe]=Lt(),n=()=>Ft(S(),"$editForm",Je),F=rt();let _e=M(h,"open",12),S=M(h,"editForm",8),Ie=M(h,"onClose",8),Ve=M(h,"onSubmit",8);ht(()=>{},()=>{it(F,new Date().toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))}),nr(),Et(),Mt(at,{get open(){return _e()},set open(be){_e(be)},children:(be,de)=>{var Xe=Ja(),Be=$(Xe);At(Be,{});var f=e(Be,2);Ut(f,{class:"sm:max-w-[500px]",children:(y,X)=>{var He=La(),qe=$(He);Nt(qe,{children:(T,De)=>{var C=ja(),xe=$(C);It(xe,{children:(ne,We)=>{i();var k=d("Add Status Update");t(ne,k)},$$slots:{default:!0}});var je=e(xe,2);Ht(je,{children:(ne,We)=>{i();var k=d("Update the maintenance status and add a comment. This will be recorded in the history.");t(ne,k)},$$slots:{default:!0}}),t(T,C)},$$slots:{default:!0}});var et=e(qe,2),Ye=s(et),Se=s(Ye),se=s(Se),ge=s(se);pe(ge,{for:"update-status",children:(T,De)=>{i();var C=d("Update Status");t(T,C)},$$slots:{default:!0}});var Te=e(ge,2);Pt(Te,{type:"single",get value(){return n().commentStatus},set value(T){fe(S(),x(n).commentStatus=T,x(n))},children:(T,De)=>{var C=Ba(),xe=$(C);bt(xe,{class:"w-full",children:(ne,We)=>{Tt(ne,{placeholder:"Select status update"})},$$slots:{default:!0}});var je=e(xe,2);St(je,{class:"w-full",children:(ne,We)=>{var k=Ra(),Ge=$(k);$e(Ge,{value:"investigating",children:(ye,ie)=>{i();var ke=d("Investigating");t(ye,ke)},$$slots:{default:!0}});var o=e(Ge,2);$e(o,{value:"identified",children:(ye,ie)=>{i();var ke=d("Issue Identified");t(ye,ke)},$$slots:{default:!0}});var w=e(o,2);$e(w,{value:"in-progress",children:(ye,ie)=>{i();var ke=d("In Progress");t(ye,ke)},$$slots:{default:!0}});var Ue=e(w,2);$e(Ue,{value:"monitoring",children:(ye,ie)=>{i();var ke=d("Monitoring");t(ye,ke)},$$slots:{default:!0}});var Oe=e(Ue,2);$e(Oe,{value:"resolved",children:(ye,ie)=>{i();var ke=d("Resolved");t(ye,ke)},$$slots:{default:!0}}),t(ne,k)},$$slots:{default:!0}}),t(T,C)},$$slots:{default:!0},$$legacy:!0}),a(se);var oe=e(se,2),he=s(oe);pe(he,{for:"update-date",children:(T,De)=>{i();var C=d("Comment Date");t(T,C)},$$slots:{default:!0}});var q=e(he,2),ve=s(q,!0);a(q),a(oe),a(Se);var Ce=e(Se,2),Y=s(Ce);pe(Y,{for:"update-comment",children:(T,De)=>{i();var C=d("Comment");t(T,C)},$$slots:{default:!0}});var Q=e(Y,2);Kt(Q,{get value(){return n().comment},set value(T){fe(S(),x(n).comment=T,x(n))},$$legacy:!0}),i(2),a(Ce);var ce=e(Ce,2),we=s(ce);Qt(we,{id:"update-sendNotification",get checked(){return n().sendNotification},set checked(T){fe(S(),x(n).sendNotification=T,x(n))},$$legacy:!0});var Ee=e(we,2);pe(Ee,{for:"update-sendNotification",class:"text-sm font-normal",children:(T,De)=>{i();var C=d("Send notification to all users about this update");t(T,C)},$$slots:{default:!0}}),a(ce),a(Ye),a(et);var ee=e(et,2);jt(ee,{children:(T,De)=>{var C=za(),xe=$(C);Pe(xe,{type:"button",variant:"outline",get onclick(){return Ie()},children:(ne,We)=>{i();var k=d("Cancel");t(ne,k)},$$slots:{default:!0}});var je=e(xe,2);Pe(je,{type:"button",get onclick(){return Ve()},children:(ne,We)=>{i();var k=d("Add Update");t(ne,k)},$$slots:{default:!0}}),t(T,C)},$$slots:{default:!0}}),R(()=>E(ve,r(F))),t(y,He)},$$slots:{default:!0}}),t(be,Xe)},$$slots:{default:!0},$$legacy:!0}),wt(),Qe()}var qa=c("<!> Schedule Maintenance",1),Wa=c("<!> <!> <!>",1),Ga=c("<!> <!>",1),Ka=c('<div class="rounded-lg border p-6 text-center"><p class="text-muted-foreground">No upcoming maintenance events</p></div>'),Qa=c("<!> ",1),Xa=c('<div class="mt-2"><p class="text-xs font-medium">Affected Services</p> <div class="mt-1 flex flex-wrap gap-1"></div></div>'),Ya=c('<div class="flex items-start justify-between rounded-lg border p-4"><div class="flex-1"><div class="mb-2 flex items-center gap-2"><h3 class="font-medium"> </h3> <!> <!></div> <p class="text-muted-foreground mb-2 text-sm"> </p> <div class="grid grid-cols-1 gap-2 sm:grid-cols-2"><div><p class="text-xs font-medium">Start Time</p> <p class="text-muted-foreground text-xs"> </p></div> <div><p class="text-xs font-medium">End Time</p> <p class="text-muted-foreground text-xs"> </p></div></div> <!></div> <div class="ml-4 flex flex-col gap-2"><!> <!> <!> <!></div></div>'),Za=c('<div class="space-y-4"></div>'),es=c("<!> <!>",1),ts=c("<!> <!>",1),rs=c('<div class="rounded-lg border p-6 text-center"><p class="text-muted-foreground">No past maintenance events</p></div>'),as=c("<!> ",1),ss=c('<div class="flex items-start justify-between rounded-lg border p-4"><div class="flex-1"><div class="mb-2 flex items-center gap-2"><h3 class="font-medium"> </h3> <!></div> <p class="text-muted-foreground mb-2 text-sm"> </p> <div class="grid grid-cols-1 gap-2 sm:grid-cols-2"><div><p class="text-xs font-medium">Start Time</p> <p class="text-muted-foreground text-xs"> </p></div> <div><p class="text-xs font-medium">End Time</p> <p class="text-muted-foreground text-xs"> </p></div></div></div></div>'),os=c('<div class="space-y-4"></div>'),ns=c("<!> <!>",1),is=c("<!> <!>",1),ls=c('<div class="rounded-lg border p-6 text-center"><p class="text-muted-foreground">No maintenance events found</p></div>'),ds=c("<!> ",1),vs=c('<div class="flex items-start justify-between rounded-lg border p-4"><div class="flex-1"><div class="mb-2 flex items-center gap-2"><h3 class="font-medium"> </h3> <!></div> <p class="text-muted-foreground mb-2 text-sm"> </p> <div class="grid grid-cols-1 gap-2 sm:grid-cols-2"><div><p class="text-xs font-medium">Start Time</p> <p class="text-muted-foreground text-xs"> </p></div> <div><p class="text-xs font-medium">End Time</p> <p class="text-muted-foreground text-xs"> </p></div></div></div> <div class="ml-4 flex flex-col gap-2"><!> <!> <!> <!> <!></div></div>'),cs=c('<div class="space-y-4"></div>'),us=c("<!> <!>",1),ms=c('<div class="border-border border-b p-0"><!></div> <!> <!> <!>',1),fs=c('<!> <div class="border-border flex flex-col gap-1 border-b p-4"><div class="flex items-center justify-between"><h1 class="text-2xl font-bold">Maintenance Management</h1> <!></div></div> <!> <!> <!> <!> <!> <!> <!>',1);function co(at,h){Ct(h,!1);const[Je,Qe]=Lt();let n=M(h,"data",8),F=rt("upcoming");const _e=[{value:"Matches",label:"Matches (Job matching and recommendations)"},{value:"Jobs",label:"Jobs (Job search and listings)"},{value:"Tracker",label:"Tracker (Application tracking)"},{value:"Documents",label:"Documents (Resume and document management)"},{value:"Automation",label:"Automation (Automated job application tools)"},{value:"System",label:"System (Core system services)"},{value:"Website",label:"Website (Website and user interface)"}],{form:S,reset:Ie,errors:Ve}=rr(n().createForm,{resetForm:!0,validationMethod:"submit-only",onResult({result:o}){o.type==="success"?(V(f,r(f).isCreateDialogOpen=!1),ot.success("Maintenance event created successfully"),zt()):o.type==="failure"&&ot.error(typeof o.data=="string"?o.data:"Failed to create maintenance event")},dataType:"json"}),{form:be,reset:de,errors:Xe}=rr(n().editForm,{resetForm:!0,validationMethod:"submit-only",onResult:({result:o})=>{o.type==="success"?(V(f,r(f).isEditDialogOpen=!1),ot.success("Maintenance event updated successfully"),zt()):o.type==="failure"&&ot.error(typeof o.data=="string"?o.data:"Failed to update maintenance event")},dataType:"json"}),{form:Be}=rr(n().deleteForm,{resetForm:!0,onResult:({result:o})=>{o.type==="success"?(V(f,r(f).isDeleteDialogOpen=!1),ot.success("Maintenance event deleted successfully"),zt()):o.type==="failure"&&ot.error(typeof o.data=="string"?o.data:"Failed to delete maintenance event")}}),f=rt({isCreateDialogOpen:!1,isEditDialogOpen:!1,isDeleteDialogOpen:!1,isHistoryDialogOpen:!1,isAddUpdateDialogOpen:!1,isCommentDialogOpen:!1}),y=rt({commentAction:"start",commentEvent:null,commentText:"",sendNotification:!1}),X=rt({selectedEvent:null,selectedEventId:"",eventHistory:[]});Vt(S,{title:"",description:"",startTime:"",endTime:"",status:"scheduled",severity:"maintenance",affectedServices:[_e[0].value],sendNotification:!1}),Vt(be,{id:"",title:"",description:"",startTime:"",endTime:"",status:"scheduled",severity:"maintenance",affectedServices:[_e[0].value],sendNotification:!1,comment:"",commentStatus:"investigating"});function He(o){return new Date(o).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}function qe(o){switch(o){case"scheduled":return"secondary";case"in-progress":return"warning";case"completed":return"success";case"cancelled":return"destructive";default:return"outline"}}function et(o){switch(o){case"info":return"secondary";case"warning":return"warning";case"critical":return"destructive";default:return"outline"}}function Ye(o){switch(o){case"scheduled":return or;case"in-progress":return fr;case"completed":return Fr;case"cancelled":return Or;default:return fr}}function Se(o){V(X,r(X).selectedEvent=o),V(X,r(X).selectedEventId=o.id),Vt(be,{id:o.id,title:o.title,description:o.description,startTime:new Date(o.startTime).toISOString().slice(0,16),endTime:new Date(o.endTime).toISOString().slice(0,16),status:o.status,severity:o.severity,affectedServices:Array.isArray(o.affectedServices)&&o.affectedServices.length>0?o.affectedServices:[_e[0].value],sendNotification:!1,comment:"",commentStatus:"investigating"}),Te(o.id),V(f,r(f).isEditDialogOpen=!0)}function se(o){V(X,r(X).selectedEvent=o),Vt(Be,{id:o.id}),V(f,r(f).isDeleteDialogOpen=!0)}function ge(o){V(X,r(X).selectedEventId=o.id),V(f,r(f).isHistoryDialogOpen=!0),Te(o.id)}async function Te(o){try{const w=await fetch(`/api/maintenance/${o}/history`);if(!w.ok)throw new Error(`Failed to fetch history: ${w.status}`);const Ue=await w.json();V(X,r(X).eventHistory=Ue)}catch(w){console.error("Error fetching event history:",w),ot.error("Failed to load event history")}}function oe(o,w){V(y,r(y).commentAction=o),V(y,r(y).commentEvent=w),V(y,r(y).commentText=""),V(f,r(f).isCommentDialogOpen=!0)}function he(o){oe("start",o)}async function q(){try{if(!r(y).commentEvent)return;const o=await fetch("/api/maintenance",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({id:r(y).commentEvent.id,title:r(y).commentEvent.title,description:r(y).commentEvent.description,startTime:r(y).commentEvent.startTime,endTime:r(y).commentEvent.endTime,status:"in-progress",severity:r(y).commentEvent.severity,affectedServices:r(y).commentEvent.affectedServices,sendNotification:r(y).sendNotification,comment:r(y).commentText||void 0,commentStatus:"in-progress"})}),w=await o.json();o.ok?(ot.success("Maintenance started successfully"),V(f,r(f).isCommentDialogOpen=!1),zt()):ot.error(w.error||"Failed to start maintenance")}catch(o){ot.error("An error occurred while starting maintenance"),console.error("Error starting maintenance:",o)}}function ve(o){oe("complete",o)}async function Ce(){try{if(!r(y).commentEvent)return;const o=await fetch("/api/maintenance",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({id:r(y).commentEvent.id,title:r(y).commentEvent.title,description:r(y).commentEvent.description,startTime:r(y).commentEvent.startTime,endTime:r(y).commentEvent.endTime,status:"completed",severity:r(y).commentEvent.severity,affectedServices:r(y).commentEvent.affectedServices,sendNotification:r(y).sendNotification,comment:r(y).commentText||void 0,commentStatus:"resolved"})}),w=await o.json();o.ok?(ot.success("Maintenance completed successfully"),V(f,r(f).isCommentDialogOpen=!1),zt()):ot.error(w.error||"Failed to complete maintenance")}catch(o){ot.error("An error occurred while completing maintenance"),console.error("Error completing maintenance:",o)}}function Y(o){V(y,r(y).commentText=o);const w=document.getElementById("comment-sendNotification");V(y,r(y).sendNotification=(w==null?void 0:w.checked)||!1)}function Q(){const o=document.getElementById("edit-maintenance-form");if(o)try{o.dispatchEvent(new Event("submit",{cancelable:!0})),V(f,r(f).isAddUpdateDialogOpen=!1)}catch(w){console.error("Error submitting form:",w)}}function ce(){r(y).commentAction==="start"?q():r(y).commentAction==="complete"&&Ce()}Et();var we=fs(),Ee=$(we);Er(Ee,{title:"Maintenance Management - Hirli"});var ee=e(Ee,2),T=s(ee),De=e(s(T),2);Pe(De,{variant:"outline",onclick:()=>{V(f,r(f).isCreateDialogOpen=!0)},children:(o,w)=>{var Ue=qa(),Oe=$(Ue);Dr(Oe,{class:"mr-2 h-4 w-4"}),i(),t(o,Ue)},$$slots:{default:!0}}),a(T),a(ee);var C=e(ee,2);Pr(C,{get value(){return r(F)},set value(o){it(F,o)},children:(o,w)=>{var Ue=ms(),Oe=$(Ue),ye=s(Oe);br(ye,{class:"flex flex-row gap-2 divide-x",children:(tt,lt)=>{var B=Wa(),W=$(B);ar(W,{value:"upcoming",class:"flex-1 border-none",children:(L,te)=>{i();var Z=d("Upcoming");t(L,Z)},$$slots:{default:!0}});var I=e(W,2);ar(I,{value:"past",class:"flex-1 border-none",children:(L,te)=>{i();var Z=d("Past");t(L,Z)},$$slots:{default:!0}});var ue=e(I,2);ar(ue,{value:"all",class:"flex-1 border-none",children:(L,te)=>{i();var Z=d("All Events");t(L,Z)},$$slots:{default:!0}}),t(tt,B)},$$slots:{default:!0}}),a(Oe);var ie=e(Oe,2);sr(ie,{value:"upcoming",children:(tt,lt)=>{Xt(tt,{children:(B,W)=>{var I=es(),ue=$(I);er(ue,{children:(te,Z)=>{var me=Ga(),ze=$(me);tr(ze,{children:(Me,H)=>{i();var j=d("Upcoming Maintenance");t(Me,j)},$$slots:{default:!0}});var Ze=e(ze,2);Zt(Ze,{children:(Me,H)=>{i();var j=d("Scheduled and in-progress maintenance events");t(Me,j)},$$slots:{default:!0}}),t(te,me)},$$slots:{default:!0}});var L=e(ue,2);Yt(L,{children:(te,Z)=>{var me=ut(),ze=$(me);{var Ze=H=>{var j=Ka();t(H,j)},Me=H=>{var j=Za();pt(j,5,()=>n().upcomingEvents,_t,(dt,g)=>{var v=Ya(),_=s(v),p=s(_),G=s(p),l=s(G,!0);a(G);var u=e(G,2);const m=nt(()=>qe(r(g).status));Dt(u,{get variant(){return r(m)},children:(K,Ae)=>{var Ke=Qa(),st=$(Ke);qt(st,()=>r(g).status,yt=>{var Bt=ut(),Jt=$(Bt);Wt(Jt,()=>Ye(r(g).status),(xr,yr)=>{yr(xr,{class:"mr-1 h-3 w-3"})}),t(yt,Bt)});var Rt=e(st);R(yt=>E(Rt,` ${yt??""}`),[()=>r(g).status.charAt(0).toUpperCase()+r(g).status.slice(1)],nt),t(K,Ke)},$$slots:{default:!0}});var b=e(u,2);const le=nt(()=>et(r(g).severity));Dt(b,{get variant(){return r(le)},children:(K,Ae)=>{i();var Ke=d();R(st=>E(Ke,st),[()=>r(g).severity.charAt(0).toUpperCase()+r(g).severity.slice(1)],nt),t(K,Ke)},$$slots:{default:!0}}),a(p);var D=e(p,2),z=s(D,!0);a(D);var O=e(D,2),J=s(O),P=e(s(J),2),A=s(P,!0);a(P),a(J);var re=e(J,2),Fe=e(s(re),2),ae=s(Fe,!0);a(Fe),a(re),a(O);var Ne=e(O,2);{var Le=K=>{var Ae=Xa(),Ke=e(s(Ae),2);pt(Ke,5,()=>r(g).affectedServices,_t,(st,Rt)=>{Dt(st,{variant:"outline",class:"text-xs",children:(yt,Bt)=>{i();var Jt=d();R(()=>E(Jt,r(Rt))),t(yt,Jt)},$$slots:{default:!0}})}),a(Ke),a(Ae),t(K,Ae)};U(Ne,K=>{r(g).affectedServices&&r(g).affectedServices.length>0&&K(Le)})}a(_);var ft=e(_,2),vt=s(ft);{var $t=K=>{Pe(K,{variant:"outline",size:"sm",onclick:()=>he(r(g)),children:(Ae,Ke)=>{i();var st=d("Start");t(Ae,st)},$$slots:{default:!0}})};U(vt,K=>{r(g).status==="scheduled"&&K($t)})}var gt=e(vt,2);{var N=K=>{Pe(K,{variant:"outline",size:"sm",onclick:()=>ve(r(g)),children:(Ae,Ke)=>{i();var st=d("Complete");t(Ae,st)},$$slots:{default:!0}})};U(gt,K=>{r(g).status==="in-progress"&&K(N)})}var Re=e(gt,2);Pe(Re,{variant:"ghost",size:"icon",onclick:()=>Se(r(g)),children:(K,Ae)=>{ur(K,{class:"h-4 w-4"})},$$slots:{default:!0}});var ct=e(Re,2);Pe(ct,{variant:"ghost",size:"icon",onclick:()=>se(r(g)),children:(K,Ae)=>{mr(K,{class:"h-4 w-4"})},$$slots:{default:!0}}),a(ft),a(v),R((K,Ae)=>{E(l,r(g).title),E(z,r(g).description),E(A,K),E(ae,Ae)},[()=>He(r(g).startTime),()=>He(r(g).endTime)],nt),t(dt,v)}),a(j),t(H,j)};U(ze,H=>{n().upcomingEvents.length===0?H(Ze):H(Me,!1)})}t(te,me)},$$slots:{default:!0}}),t(B,I)},$$slots:{default:!0}})},$$slots:{default:!0}});var ke=e(ie,2);sr(ke,{value:"past",children:(tt,lt)=>{Xt(tt,{children:(B,W)=>{var I=ns(),ue=$(I);er(ue,{children:(te,Z)=>{var me=ts(),ze=$(me);tr(ze,{children:(Me,H)=>{i();var j=d("Past Maintenance");t(Me,j)},$$slots:{default:!0}});var Ze=e(ze,2);Zt(Ze,{children:(Me,H)=>{i();var j=d("Completed and cancelled maintenance events");t(Me,j)},$$slots:{default:!0}}),t(te,me)},$$slots:{default:!0}});var L=e(ue,2);Yt(L,{children:(te,Z)=>{var me=ut(),ze=$(me);{var Ze=H=>{var j=rs();t(H,j)},Me=H=>{var j=os();pt(j,5,()=>n().pastEvents,_t,(dt,g)=>{var v=ss(),_=s(v),p=s(_),G=s(p),l=s(G,!0);a(G);var u=e(G,2);const m=nt(()=>qe(r(g).status));Dt(u,{get variant(){return r(m)},children:(Fe,ae)=>{var Ne=as(),Le=$(Ne);qt(Le,()=>r(g).status,vt=>{var $t=ut(),gt=$($t);Wt(gt,()=>Ye(r(g).status),(N,Re)=>{Re(N,{class:"mr-1 h-3 w-3"})}),t(vt,$t)});var ft=e(Le);R(vt=>E(ft,` ${vt??""}`),[()=>r(g).status.charAt(0).toUpperCase()+r(g).status.slice(1)],nt),t(Fe,Ne)},$$slots:{default:!0}}),a(p);var b=e(p,2),le=s(b,!0);a(b);var D=e(b,2),z=s(D),O=e(s(z),2),J=s(O,!0);a(O),a(z);var P=e(z,2),A=e(s(P),2),re=s(A,!0);a(A),a(P),a(D),a(_),a(v),R((Fe,ae)=>{E(l,r(g).title),E(le,r(g).description),E(J,Fe),E(re,ae)},[()=>He(r(g).startTime),()=>He(r(g).endTime)],nt),t(dt,v)}),a(j),t(H,j)};U(ze,H=>{n().pastEvents.length===0?H(Ze):H(Me,!1)})}t(te,me)},$$slots:{default:!0}}),t(B,I)},$$slots:{default:!0}})},$$slots:{default:!0}});var mt=e(ke,2);sr(mt,{value:"all",children:(tt,lt)=>{Xt(tt,{children:(B,W)=>{var I=us(),ue=$(I);er(ue,{children:(te,Z)=>{var me=is(),ze=$(me);tr(ze,{children:(Me,H)=>{i();var j=d("All Maintenance Events");t(Me,j)},$$slots:{default:!0}});var Ze=e(ze,2);Zt(Ze,{children:(Me,H)=>{i();var j=d("Complete history of maintenance events");t(Me,j)},$$slots:{default:!0}}),t(te,me)},$$slots:{default:!0}});var L=e(ue,2);Yt(L,{children:(te,Z)=>{var me=ut(),ze=$(me);{var Ze=H=>{var j=ls();t(H,j)},Me=H=>{var j=cs();pt(j,5,()=>n().maintenanceEvents,_t,(dt,g)=>{var v=vs(),_=s(v),p=s(_),G=s(p),l=s(G,!0);a(G);var u=e(G,2);const m=nt(()=>qe(r(g).status));Dt(u,{get variant(){return r(m)},children:(N,Re)=>{var ct=ds(),K=$(ct);qt(K,()=>r(g).status,Ke=>{var st=ut(),Rt=$(st);Wt(Rt,()=>Ye(r(g).status),(yt,Bt)=>{Bt(yt,{class:"mr-1 h-3 w-3"})}),t(Ke,st)});var Ae=e(K);R(Ke=>E(Ae,` ${Ke??""}`),[()=>r(g).status.charAt(0).toUpperCase()+r(g).status.slice(1)],nt),t(N,ct)},$$slots:{default:!0}}),a(p);var b=e(p,2),le=s(b,!0);a(b);var D=e(b,2),z=s(D),O=e(s(z),2),J=s(O,!0);a(O),a(z);var P=e(z,2),A=e(s(P),2),re=s(A,!0);a(A),a(P),a(D),a(_);var Fe=e(_,2),ae=s(Fe);{var Ne=N=>{Pe(N,{variant:"outline",size:"sm",onclick:()=>he(r(g)),children:(Re,ct)=>{i();var K=d("Start");t(Re,K)},$$slots:{default:!0}})};U(ae,N=>{r(g).status==="scheduled"&&N(Ne)})}var Le=e(ae,2);{var ft=N=>{Pe(N,{variant:"outline",size:"sm",onclick:()=>ve(r(g)),children:(Re,ct)=>{i();var K=d("Complete");t(Re,K)},$$slots:{default:!0}})};U(Le,N=>{r(g).status==="in-progress"&&N(ft)})}var vt=e(Le,2);Pe(vt,{variant:"ghost",size:"icon",onclick:()=>Se(r(g)),children:(N,Re)=>{ur(N,{class:"h-4 w-4"})},$$slots:{default:!0}});var $t=e(vt,2);Pe($t,{variant:"ghost",size:"icon",onclick:()=>ge(r(g)),children:(N,Re)=>{hr(N,{class:"h-4 w-4"})},$$slots:{default:!0}});var gt=e($t,2);Pe(gt,{variant:"ghost",size:"icon",onclick:()=>se(r(g)),children:(N,Re)=>{mr(N,{class:"h-4 w-4"})},$$slots:{default:!0}}),a(Fe),a(v),R((N,Re)=>{E(l,r(g).title),E(le,r(g).description),E(J,N),E(re,Re)},[()=>He(r(g).startTime),()=>He(r(g).endTime)],nt),t(dt,v)}),a(j),t(H,j)};U(ze,H=>{n().maintenanceEvents.length===0?H(Ze):H(Me,!1)})}t(te,me)},$$slots:{default:!0}}),t(B,I)},$$slots:{default:!0}})},$$slots:{default:!0}}),t(o,Ue)},$$slots:{default:!0},$$legacy:!0});var xe=e(C,2);oa(xe,{get open(){return r(f).isCreateDialogOpen},get createForm(){return S},get createErrors(){return Ve},get serviceOptions(){return _e},onClose:()=>V(f,r(f).isCreateDialogOpen=!1),onSubmit:()=>{const o=document.getElementById("create-maintenance-form");if(o)try{o.dispatchEvent(new Event("submit",{cancelable:!0}))}catch(w){console.error("Error submitting form:",w)}},get resetForm(){return Ie}});var je=e(xe,2);wa(je,{get open(){return r(f).isEditDialogOpen},get editForm(){return be},get editErrors(){return Xe},get serviceOptions(){return _e},get eventHistory(){return r(X).eventHistory},onClose:()=>V(f,r(f).isEditDialogOpen=!1),onSubmit:()=>{const o=document.getElementById("edit-maintenance-form");if(o)try{o.dispatchEvent(new Event("submit",{cancelable:!0}))}catch(w){console.error("Error submitting form:",w)}},get resetForm(){return de},onOpenHistory:()=>{V(f,r(f).isHistoryDialogOpen=!0),V(f,r(f).isEditDialogOpen=!1)},onOpenAddUpdate:()=>V(f,r(f).isAddUpdateDialogOpen=!0)});var ne=e(je,2);Ma(ne,{get open(){return r(f).isDeleteDialogOpen},get deleteForm(){return Be},get selectedEvent(){return r(X).selectedEvent},onClose:()=>V(f,r(f).isDeleteDialogOpen=!1),onSubmit:()=>{const o=document.getElementById("delete-maintenance-form");if(o)try{o.dispatchEvent(new Event("submit",{cancelable:!0}))}catch(w){console.error("Error submitting form:",w)}}});var We=e(ne,2);Lr(We,{get eventId(){return r(X).selectedEventId},onClose:()=>V(f,r(f).isHistoryDialogOpen=!1),get open(){return r(f).isHistoryDialogOpen},set open(o){V(f,r(f).isHistoryDialogOpen=o)},$$legacy:!0});var k=e(We,2);Va(k,{get open(){return r(f).isAddUpdateDialogOpen},get editForm(){return be},onClose:()=>V(f,r(f).isAddUpdateDialogOpen=!1),onSubmit:Q});var Ge=e(k,2);Ha(Ge,{get open(){return r(f).isCommentDialogOpen},get commentAction(){return r(y).commentAction},get commentEvent(){return r(y).commentEvent},get commentText(){return r(y).commentText},onClose:()=>V(f,r(f).isCommentDialogOpen=!1),onSubmit:ce,onCommentTextChange:Y}),t(at,we),wt(),Qe()}export{co as component};
