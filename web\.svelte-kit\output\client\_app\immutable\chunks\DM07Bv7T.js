var ye=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,I=e=>!e||typeof e!="object"||Object.keys(e).length===0,Le=(e,t)=>JSON.stringify(e)===JSON.stringify(t);function Me(e,t){e.forEach(function(r){Array.isArray(r)?Me(r,t):t.push(r)})}function ze(e){let t=[];return Me(e,t),t}var Se=(...e)=>ze(e).filter(Boolean),Ce=(e,t)=>{let r={},o=Object.keys(e),c=Object.keys(t);for(let l of o)if(c.includes(l)){let i=e[l],y=t[l];Array.isArray(i)||Array.isArray(y)?r[l]=Se(y,i):typeof i=="object"&&typeof y=="object"?r[l]=Ce(i,y):r[l]=y+" "+i}else r[l]=e[l];for(let l of c)o.includes(l)||(r[l]=t[l]);return r},we=e=>!e||typeof e!="string"?e:e.replace(/\s+/g," ").trim();const me="-",Fe=e=>{const t=Be(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:i=>{const y=i.split(me);return y[0]===""&&y.length!==1&&y.shift(),Ge(y,t)||_e(i)},getConflictingClassGroupIds:(i,y)=>{const d=r[i]||[];return y&&o[i]?[...d,...o[i]]:d}}},Ge=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),c=o?Ge(e.slice(1),o):void 0;if(c)return c;if(t.validators.length===0)return;const l=e.join(me);return(i=t.validators.find(({validator:y})=>y(l)))==null?void 0:i.classGroupId},ve=/^\[(.+)\]$/,_e=e=>{if(ve.test(e)){const t=ve.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},Be=e=>{const{theme:t,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(const c in r)de(r[c],o,c,t);return o},de=(e,t,r,o)=>{e.forEach(c=>{if(typeof c=="string"){const l=c===""?t:xe(t,c);l.classGroupId=r;return}if(typeof c=="function"){if($e(c)){de(c(o),t,r,o);return}t.validators.push({validator:c,classGroupId:r});return}Object.entries(c).forEach(([l,i])=>{de(i,xe(t,l),r,o)})})},xe=(e,t)=>{let r=e;return t.split(me).forEach(o=>{r.nextPart.has(o)||r.nextPart.set(o,{nextPart:new Map,validators:[]}),r=r.nextPart.get(o)}),r},$e=e=>e.isThemeGetter,We=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,o=new Map;const c=(l,i)=>{r.set(l,i),t++,t>e&&(t=0,o=r,r=new Map)};return{get(l){let i=r.get(l);if(i!==void 0)return i;if((i=o.get(l))!==void 0)return c(l,i),i},set(l,i){r.has(l)?r.set(l,i):c(l,i)}}},ue="!",pe=":",Ue=pe.length,Je=e=>{const{prefix:t,experimentalParseClassName:r}=e;let o=c=>{const l=[];let i=0,y=0,d=0,M;for(let z=0;z<c.length;z++){let S=c[z];if(i===0&&y===0){if(S===pe){l.push(c.slice(d,z)),d=z+Ue;continue}if(S==="/"){M=z;continue}}S==="["?i++:S==="]"?i--:S==="("?y++:S===")"&&y--}const v=l.length===0?c:c.substring(d),j=qe(v),V=j!==v,T=M&&M>d?M-d:void 0;return{modifiers:l,hasImportantModifier:V,baseClassName:j,maybePostfixModifierPosition:T}};if(t){const c=t+pe,l=o;o=i=>i.startsWith(c)?l(i.substring(c.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(r){const c=o;o=l=>r({className:l,parseClassName:c})}return o},qe=e=>e.endsWith(ue)?e.substring(0,e.length-1):e.startsWith(ue)?e.substring(1):e,He=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const c=[];let l=[];return o.forEach(i=>{i[0]==="["||t[i]?(c.push(...l.sort(),i),l=[]):l.push(i)}),c.push(...l.sort()),c}},Ke=e=>({cache:We(e.cacheSize),parseClassName:Je(e),sortModifiers:He(e),...Fe(e)}),De=/\s+/,Qe=(e,t)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:c,sortModifiers:l}=t,i=[],y=e.trim().split(De);let d="";for(let M=y.length-1;M>=0;M-=1){const v=y[M],{isExternal:j,modifiers:V,hasImportantModifier:T,baseClassName:z,maybePostfixModifierPosition:S}=r(v);if(j){d=v+(d.length>0?" "+d:d);continue}let G=!!S,N=o(G?z.substring(0,S):z);if(!N){if(!G){d=v+(d.length>0?" "+d:d);continue}if(N=o(z),!N){d=v+(d.length>0?" "+d:d);continue}G=!1}const k=l(V).join(":"),W=T?k+ue:k,E=W+N;if(i.includes(E))continue;i.push(E);const F=c(N,G);for(let u=0;u<F.length;++u){const R=F[u];i.push(W+R)}d=v+(d.length>0?" "+d:d)}return d};function Xe(){let e=0,t,r,o="";for(;e<arguments.length;)(t=arguments[e++])&&(r=Pe(t))&&(o&&(o+=" "),o+=r);return o}const Pe=e=>{if(typeof e=="string")return e;let t,r="";for(let o=0;o<e.length;o++)e[o]&&(t=Pe(e[o]))&&(r&&(r+=" "),r+=t);return r};function fe(e,...t){let r,o,c,l=i;function i(d){const M=t.reduce((v,j)=>j(v),e());return r=Ke(M),o=r.cache.get,c=r.cache.set,l=y,y(d)}function y(d){const M=o(d);if(M)return M;const v=Qe(d,r);return c(d,v),v}return function(){return l(Xe.apply(null,arguments))}}const C=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},Re=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ie=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Ye=/^\d+\/\d+$/,Ze=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,er=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,rr=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,tr=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,or=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Q=e=>Ye.test(e),m=e=>!!e&&!Number.isNaN(Number(e)),J=e=>!!e&&Number.isInteger(Number(e)),ke=e=>e.endsWith("%")&&m(e.slice(0,-1)),$=e=>Ze.test(e),nr=()=>!0,sr=e=>er.test(e)&&!rr.test(e),he=()=>!1,lr=e=>tr.test(e),ir=e=>or.test(e),ar=e=>!n(e)&&!s(e),cr=e=>X(e,Te,he),n=e=>Re.test(e),q=e=>X(e,Ne,sr),ae=e=>X(e,vr,m),dr=e=>X(e,je,he),ur=e=>X(e,Ve,ir),pr=e=>X(e,he,lr),s=e=>Ie.test(e),se=e=>Y(e,Ne),fr=e=>Y(e,xr),br=e=>Y(e,je),gr=e=>Y(e,Te),mr=e=>Y(e,Ve),hr=e=>Y(e,kr,!0),X=(e,t,r)=>{const o=Re.exec(e);return o?o[1]?t(o[1]):r(o[2]):!1},Y=(e,t,r=!1)=>{const o=Ie.exec(e);return o?o[1]?t(o[1]):r:!1},je=e=>e==="position",yr=new Set(["image","url"]),Ve=e=>yr.has(e),wr=new Set(["length","size","percentage"]),Te=e=>wr.has(e),Ne=e=>e==="length",vr=e=>e==="number",xr=e=>e==="family-name",kr=e=>e==="shadow",be=()=>{const e=C("color"),t=C("font"),r=C("text"),o=C("font-weight"),c=C("tracking"),l=C("leading"),i=C("breakpoint"),y=C("container"),d=C("spacing"),M=C("radius"),v=C("shadow"),j=C("inset-shadow"),V=C("drop-shadow"),T=C("blur"),z=C("perspective"),S=C("aspect"),G=C("ease"),N=C("animate"),k=()=>["auto","avoid","all","avoid-page","page","left","right","column"],W=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],E=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto","contain","none"],u=()=>[s,n,d],R=()=>[Q,"full","auto",...u()],Z=()=>[J,"none","subgrid",s,n],ee=()=>["auto",{span:["full",J,s,n]},s,n],H=()=>[J,"auto",s,n],ne=()=>["auto","min","max","fr",s,n],g=()=>["start","end","center","between","around","evenly","stretch","baseline"],f=()=>["start","end","center","stretch"],p=()=>["auto",...u()],h=()=>[Q,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...u()],a=()=>[e,s,n],x=()=>[ke,q],b=()=>["","none","full",M,s,n],w=()=>["",m,se,q],A=()=>["solid","dashed","dotted","double"],O=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],P=()=>["","none",T,s,n],U=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",s,n],_=()=>["none",m,s,n],B=()=>["none",m,s,n],K=()=>[m,s,n],D=()=>[Q,"full",...u()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[$],breakpoint:[$],color:[nr],container:[$],"drop-shadow":[$],ease:["in","out","in-out"],font:[ar],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[$],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[$],shadow:[$],spacing:["px",m],text:[$],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Q,n,s,S]}],container:["container"],columns:[{columns:[m,n,s,y]}],"break-after":[{"break-after":k()}],"break-before":[{"break-before":k()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...W(),n,s]}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:F()}],"overscroll-x":[{"overscroll-x":F()}],"overscroll-y":[{"overscroll-y":F()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:R()}],"inset-x":[{"inset-x":R()}],"inset-y":[{"inset-y":R()}],start:[{start:R()}],end:[{end:R()}],top:[{top:R()}],right:[{right:R()}],bottom:[{bottom:R()}],left:[{left:R()}],visibility:["visible","invisible","collapse"],z:[{z:[J,"auto",s,n]}],basis:[{basis:[Q,"full","auto",y,...u()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[m,Q,"auto","initial","none",n]}],grow:[{grow:["",m,s,n]}],shrink:[{shrink:["",m,s,n]}],order:[{order:[J,"first","last","none",s,n]}],"grid-cols":[{"grid-cols":Z()}],"col-start-end":[{col:ee()}],"col-start":[{"col-start":H()}],"col-end":[{"col-end":H()}],"grid-rows":[{"grid-rows":Z()}],"row-start-end":[{row:ee()}],"row-start":[{"row-start":H()}],"row-end":[{"row-end":H()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ne()}],"auto-rows":[{"auto-rows":ne()}],gap:[{gap:u()}],"gap-x":[{"gap-x":u()}],"gap-y":[{"gap-y":u()}],"justify-content":[{justify:[...g(),"normal"]}],"justify-items":[{"justify-items":[...f(),"normal"]}],"justify-self":[{"justify-self":["auto",...f()]}],"align-content":[{content:["normal",...g()]}],"align-items":[{items:[...f(),"baseline"]}],"align-self":[{self:["auto",...f(),"baseline"]}],"place-content":[{"place-content":g()}],"place-items":[{"place-items":[...f(),"baseline"]}],"place-self":[{"place-self":["auto",...f()]}],p:[{p:u()}],px:[{px:u()}],py:[{py:u()}],ps:[{ps:u()}],pe:[{pe:u()}],pt:[{pt:u()}],pr:[{pr:u()}],pb:[{pb:u()}],pl:[{pl:u()}],m:[{m:p()}],mx:[{mx:p()}],my:[{my:p()}],ms:[{ms:p()}],me:[{me:p()}],mt:[{mt:p()}],mr:[{mr:p()}],mb:[{mb:p()}],ml:[{ml:p()}],"space-x":[{"space-x":u()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":u()}],"space-y-reverse":["space-y-reverse"],size:[{size:h()}],w:[{w:[y,"screen",...h()]}],"min-w":[{"min-w":[y,"screen","none",...h()]}],"max-w":[{"max-w":[y,"screen","none","prose",{screen:[i]},...h()]}],h:[{h:["screen",...h()]}],"min-h":[{"min-h":["screen","none",...h()]}],"max-h":[{"max-h":["screen",...h()]}],"font-size":[{text:["base",r,se,q]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,s,ae]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ke,n]}],"font-family":[{font:[fr,n,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[c,s,n]}],"line-clamp":[{"line-clamp":[m,"none",s,ae]}],leading:[{leading:[l,...u()]}],"list-image":[{"list-image":["none",s,n]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",s,n]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:a()}],"text-color":[{text:a()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...A(),"wavy"]}],"text-decoration-thickness":[{decoration:[m,"from-font","auto",s,q]}],"text-decoration-color":[{decoration:a()}],"underline-offset":[{"underline-offset":[m,"auto",s,n]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:u()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",s,n]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",s,n]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...W(),br,dr]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",gr,cr]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},J,s,n],radial:["",s,n],conic:[J,s,n]},mr,ur]}],"bg-color":[{bg:a()}],"gradient-from-pos":[{from:x()}],"gradient-via-pos":[{via:x()}],"gradient-to-pos":[{to:x()}],"gradient-from":[{from:a()}],"gradient-via":[{via:a()}],"gradient-to":[{to:a()}],rounded:[{rounded:b()}],"rounded-s":[{"rounded-s":b()}],"rounded-e":[{"rounded-e":b()}],"rounded-t":[{"rounded-t":b()}],"rounded-r":[{"rounded-r":b()}],"rounded-b":[{"rounded-b":b()}],"rounded-l":[{"rounded-l":b()}],"rounded-ss":[{"rounded-ss":b()}],"rounded-se":[{"rounded-se":b()}],"rounded-ee":[{"rounded-ee":b()}],"rounded-es":[{"rounded-es":b()}],"rounded-tl":[{"rounded-tl":b()}],"rounded-tr":[{"rounded-tr":b()}],"rounded-br":[{"rounded-br":b()}],"rounded-bl":[{"rounded-bl":b()}],"border-w":[{border:w()}],"border-w-x":[{"border-x":w()}],"border-w-y":[{"border-y":w()}],"border-w-s":[{"border-s":w()}],"border-w-e":[{"border-e":w()}],"border-w-t":[{"border-t":w()}],"border-w-r":[{"border-r":w()}],"border-w-b":[{"border-b":w()}],"border-w-l":[{"border-l":w()}],"divide-x":[{"divide-x":w()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":w()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...A(),"hidden","none"]}],"divide-style":[{divide:[...A(),"hidden","none"]}],"border-color":[{border:a()}],"border-color-x":[{"border-x":a()}],"border-color-y":[{"border-y":a()}],"border-color-s":[{"border-s":a()}],"border-color-e":[{"border-e":a()}],"border-color-t":[{"border-t":a()}],"border-color-r":[{"border-r":a()}],"border-color-b":[{"border-b":a()}],"border-color-l":[{"border-l":a()}],"divide-color":[{divide:a()}],"outline-style":[{outline:[...A(),"none","hidden"]}],"outline-offset":[{"outline-offset":[m,s,n]}],"outline-w":[{outline:["",m,se,q]}],"outline-color":[{outline:[e]}],shadow:[{shadow:["","none",v,hr,pr]}],"shadow-color":[{shadow:a()}],"inset-shadow":[{"inset-shadow":["none",s,n,j]}],"inset-shadow-color":[{"inset-shadow":a()}],"ring-w":[{ring:w()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:a()}],"ring-offset-w":[{"ring-offset":[m,q]}],"ring-offset-color":[{"ring-offset":a()}],"inset-ring-w":[{"inset-ring":w()}],"inset-ring-color":[{"inset-ring":a()}],opacity:[{opacity:[m,s,n]}],"mix-blend":[{"mix-blend":[...O(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":O()}],filter:[{filter:["","none",s,n]}],blur:[{blur:P()}],brightness:[{brightness:[m,s,n]}],contrast:[{contrast:[m,s,n]}],"drop-shadow":[{"drop-shadow":["","none",V,s,n]}],grayscale:[{grayscale:["",m,s,n]}],"hue-rotate":[{"hue-rotate":[m,s,n]}],invert:[{invert:["",m,s,n]}],saturate:[{saturate:[m,s,n]}],sepia:[{sepia:["",m,s,n]}],"backdrop-filter":[{"backdrop-filter":["","none",s,n]}],"backdrop-blur":[{"backdrop-blur":P()}],"backdrop-brightness":[{"backdrop-brightness":[m,s,n]}],"backdrop-contrast":[{"backdrop-contrast":[m,s,n]}],"backdrop-grayscale":[{"backdrop-grayscale":["",m,s,n]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[m,s,n]}],"backdrop-invert":[{"backdrop-invert":["",m,s,n]}],"backdrop-opacity":[{"backdrop-opacity":[m,s,n]}],"backdrop-saturate":[{"backdrop-saturate":[m,s,n]}],"backdrop-sepia":[{"backdrop-sepia":["",m,s,n]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":u()}],"border-spacing-x":[{"border-spacing-x":u()}],"border-spacing-y":[{"border-spacing-y":u()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",s,n]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[m,"initial",s,n]}],ease:[{ease:["linear","initial",G,s,n]}],delay:[{delay:[m,s,n]}],animate:[{animate:["none",N,s,n]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[z,s,n]}],"perspective-origin":[{"perspective-origin":U()}],rotate:[{rotate:_()}],"rotate-x":[{"rotate-x":_()}],"rotate-y":[{"rotate-y":_()}],"rotate-z":[{"rotate-z":_()}],scale:[{scale:B()}],"scale-x":[{"scale-x":B()}],"scale-y":[{"scale-y":B()}],"scale-z":[{"scale-z":B()}],"scale-3d":["scale-3d"],skew:[{skew:K()}],"skew-x":[{"skew-x":K()}],"skew-y":[{"skew-y":K()}],transform:[{transform:[s,n,"","none","gpu","cpu"]}],"transform-origin":[{origin:U()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:D()}],"translate-x":[{"translate-x":D()}],"translate-y":[{"translate-y":D()}],"translate-z":[{"translate-z":D()}],"translate-none":["translate-none"],accent:[{accent:a()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:a()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",s,n]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":u()}],"scroll-mx":[{"scroll-mx":u()}],"scroll-my":[{"scroll-my":u()}],"scroll-ms":[{"scroll-ms":u()}],"scroll-me":[{"scroll-me":u()}],"scroll-mt":[{"scroll-mt":u()}],"scroll-mr":[{"scroll-mr":u()}],"scroll-mb":[{"scroll-mb":u()}],"scroll-ml":[{"scroll-ml":u()}],"scroll-p":[{"scroll-p":u()}],"scroll-px":[{"scroll-px":u()}],"scroll-py":[{"scroll-py":u()}],"scroll-ps":[{"scroll-ps":u()}],"scroll-pe":[{"scroll-pe":u()}],"scroll-pt":[{"scroll-pt":u()}],"scroll-pr":[{"scroll-pr":u()}],"scroll-pb":[{"scroll-pb":u()}],"scroll-pl":[{"scroll-pl":u()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",s,n]}],fill:[{fill:["none",...a()]}],"stroke-w":[{stroke:[m,se,q,ae]}],stroke:[{stroke:["none",...a()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},Ar=(e,{cacheSize:t,prefix:r,experimentalParseClassName:o,extend:c={},override:l={}})=>(te(e,"cacheSize",t),te(e,"prefix",r),te(e,"experimentalParseClassName",o),le(e.theme,l.theme),le(e.classGroups,l.classGroups),le(e.conflictingClassGroups,l.conflictingClassGroups),le(e.conflictingClassGroupModifiers,l.conflictingClassGroupModifiers),te(e,"orderSensitiveModifiers",l.orderSensitiveModifiers),ie(e.theme,c.theme),ie(e.classGroups,c.classGroups),ie(e.conflictingClassGroups,c.conflictingClassGroups),ie(e.conflictingClassGroupModifiers,c.conflictingClassGroupModifiers),Ee(e,c,"orderSensitiveModifiers"),e),te=(e,t,r)=>{r!==void 0&&(e[t]=r)},le=(e,t)=>{if(t)for(const r in t)te(e,r,t[r])},ie=(e,t)=>{if(t)for(const r in t)Ee(e,t,r)},Ee=(e,t,r)=>{const o=t[r];o!==void 0&&(e[r]=e[r]?e[r].concat(o):o)},Mr=(e,...t)=>typeof e=="function"?fe(be,e,...t):fe(()=>Ar(be(),e),...t),zr=fe(be);var Sr={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},Oe=e=>e||void 0,oe=(...e)=>Oe(ze(e).filter(Boolean).join(" ")),ce=null,L={},ge=!1,re=(...e)=>t=>t.twMerge?((!ce||ge)&&(ge=!1,ce=I(L)?zr:Mr({...L,extend:{theme:L.theme,classGroups:L.classGroups,conflictingClassGroupModifiers:L.conflictingClassGroupModifiers,conflictingClassGroups:L.conflictingClassGroups,...L.extend}})),Oe(ce(oe(e)))):oe(e),Ae=(e,t)=>{for(let r in t)e.hasOwnProperty(r)?e[r]=oe(e[r],t[r]):e[r]=t[r];return e},Cr=(e,t)=>{let{extend:r=null,slots:o={},variants:c={},compoundVariants:l=[],compoundSlots:i=[],defaultVariants:y={}}=e,d={...Sr,...t},M=r!=null&&r.base?oe(r.base,e==null?void 0:e.base):e==null?void 0:e.base,v=r!=null&&r.variants&&!I(r.variants)?Ce(c,r.variants):c,j=r!=null&&r.defaultVariants&&!I(r.defaultVariants)?{...r.defaultVariants,...y}:y;!I(d.twMergeConfig)&&!Le(d.twMergeConfig,L)&&(ge=!0,L=d.twMergeConfig);let V=I(r==null?void 0:r.slots),T=I(o)?{}:{base:oe(e==null?void 0:e.base,V&&(r==null?void 0:r.base)),...o},z=V?T:Ae({...r==null?void 0:r.slots},I(T)?{base:e==null?void 0:e.base}:T),S=I(r==null?void 0:r.compoundVariants)?l:Se(r==null?void 0:r.compoundVariants,l),G=k=>{if(I(v)&&I(o)&&V)return re(M,k==null?void 0:k.class,k==null?void 0:k.className)(d);if(S&&!Array.isArray(S))throw new TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof S}`);if(i&&!Array.isArray(i))throw new TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof i}`);let W=(g,f,p=[],h)=>{let a=p;if(typeof f=="string")a=a.concat(we(f).split(" ").map(x=>`${g}:${x}`));else if(Array.isArray(f))a=a.concat(f.reduce((x,b)=>x.concat(`${g}:${b}`),[]));else if(typeof f=="object"&&typeof h=="string"){for(let x in f)if(f.hasOwnProperty(x)&&x===h){let b=f[x];if(b&&typeof b=="string"){let w=we(b);a[h]?a[h]=a[h].concat(w.split(" ").map(A=>`${g}:${A}`)):a[h]=w.split(" ").map(A=>`${g}:${A}`)}else Array.isArray(b)&&b.length>0&&(a[h]=b.reduce((w,A)=>w.concat(`${g}:${A}`),[]))}}return a},E=(g,f=v,p=null,h=null)=>{var a;let x=f[g];if(!x||I(x))return null;let b=(a=h==null?void 0:h[g])!=null?a:k==null?void 0:k[g];if(b===null)return null;let w=ye(b),A=Array.isArray(d.responsiveVariants)&&d.responsiveVariants.length>0||d.responsiveVariants===!0,O=j==null?void 0:j[g],P=[];if(typeof w=="object"&&A)for(let[B,K]of Object.entries(w)){let D=x[K];if(B==="initial"){O=K;continue}Array.isArray(d.responsiveVariants)&&!d.responsiveVariants.includes(B)||(P=W(B,D,P,p))}let U=w!=null&&typeof w!="object"?w:ye(O),_=x[U||"false"];return typeof P=="object"&&typeof p=="string"&&P[p]?Ae(P,_):P.length>0?(P.push(_),p==="base"?P.join(" "):P):_},F=()=>v?Object.keys(v).map(g=>E(g,v)):null,u=(g,f)=>{if(!v||typeof v!="object")return null;let p=new Array;for(let h in v){let a=E(h,v,g,f),x=g==="base"&&typeof a=="string"?a:a&&a[g];x&&(p[p.length]=x)}return p},R={};for(let g in k)k[g]!==void 0&&(R[g]=k[g]);let Z=(g,f)=>{var p;let h=typeof(k==null?void 0:k[g])=="object"?{[g]:(p=k[g])==null?void 0:p.initial}:{};return{...j,...R,...h,...f}},ee=(g=[],f)=>{let p=[];for(let{class:h,className:a,...x}of g){let b=!0;for(let[w,A]of Object.entries(x)){let O=Z(w,f)[w];if(Array.isArray(A)){if(!A.includes(O)){b=!1;break}}else{let P=U=>U==null||U===!1;if(P(A)&&P(O))continue;if(O!==A){b=!1;break}}}b&&(h&&p.push(h),a&&p.push(a))}return p},H=g=>{let f=ee(S,g);if(!Array.isArray(f))return f;let p={};for(let h of f)if(typeof h=="string"&&(p.base=re(p.base,h)(d)),typeof h=="object")for(let[a,x]of Object.entries(h))p[a]=re(p[a],x)(d);return p},ne=g=>{if(i.length<1)return null;let f={};for(let{slots:p=[],class:h,className:a,...x}of i){if(!I(x)){let b=!0;for(let w of Object.keys(x)){let A=Z(w,g)[w];if(A===void 0||(Array.isArray(x[w])?!x[w].includes(A):x[w]!==A)){b=!1;break}}if(!b)continue}for(let b of p)f[b]=f[b]||[],f[b].push([h,a])}return f};if(!I(o)||!V){let g={};if(typeof z=="object"&&!I(z))for(let f of Object.keys(z))g[f]=p=>{var h,a;return re(z[f],u(f,p),((h=H(p))!=null?h:[])[f],((a=ne(p))!=null?a:[])[f],p==null?void 0:p.class,p==null?void 0:p.className)(d)};return g}return re(M,F(),ee(S),k==null?void 0:k.class,k==null?void 0:k.className)(d)},N=()=>{if(!(!v||typeof v!="object"))return Object.keys(v)};return G.variantKeys=N(),G.extend=r,G.base=M,G.slots=z,G.variants=v,G.defaultVariants=j,G.compoundSlots=i,G.compoundVariants=S,G};export{Cr as c};
