var vt=Object.defineProperty;var it=e=>{throw TypeError(e)};var kt=(e,t,o)=>t in e?vt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;var d=(e,t,o)=>kt(e,typeof t!="symbol"?t+"":t,o),mt=(e,t,o)=>t.has(e)||it("Cannot "+o);var r=(e,t,o)=>(mt(e,t,"read from private field"),o?o.call(e):t.get(e)),n=(e,t,o)=>t.has(e)?it("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,o);import{c as V,a as m,f as nt}from"./BasJTneF.js";import{k as u,g as i,d as a,x as h,p as at,f as W,a as dt,c as ht,au as pt,r as ct,e as yt}from"./CGmarHxI.js";import{s as X}from"./ncUU1dSD.js";import{i as lt}from"./u21ee2wt.js";import{e as ut}from"./B-Xjo-Yt.js";import{p as y,r as gt}from"./Btcx8l8F.js";import{u as g,b as D,m as U}from"./BfX7a-t9.js";import{u as ft}from"./CnMg5bH0.js";import{C as bt}from"./DuoUhxYL.js";import{c as wt,d as Dt}from"./Bd3zs5C6.js";import{S as ot,d as st}from"./CIOgxH3l.js";import{P as Pt}from"./XESq6qWN.js";function Ct(e){return{content:`data-${e}-content`,trigger:`data-${e}-trigger`,overlay:`data-${e}-overlay`,title:`data-${e}-title`,description:`data-${e}-description`,close:`data-${e}-close`,cancel:`data-${e}-cancel`,action:`data-${e}-action`}}var P,C,I,_,N,S,x,R,A,O;class It{constructor(t){d(this,"opts");n(this,P,u(null));n(this,C,u(null));n(this,I,u(null));n(this,_,u(void 0));n(this,N,u(void 0));n(this,S,u(void 0));n(this,x,u(void 0));n(this,R,u(null));n(this,A,h(()=>Ct(this.opts.variant.current)));n(this,O,h(()=>({"data-state":wt(this.opts.open.current)})));this.opts=t,this.handleOpen=this.handleOpen.bind(this),this.handleClose=this.handleClose.bind(this)}get triggerNode(){return i(r(this,P))}set triggerNode(t){a(r(this,P),t,!0)}get contentNode(){return i(r(this,C))}set contentNode(t){a(r(this,C),t,!0)}get descriptionNode(){return i(r(this,I))}set descriptionNode(t){a(r(this,I),t,!0)}get contentId(){return i(r(this,_))}set contentId(t){a(r(this,_),t,!0)}get titleId(){return i(r(this,N))}set titleId(t){a(r(this,N),t,!0)}get triggerId(){return i(r(this,S))}set triggerId(t){a(r(this,S),t,!0)}get descriptionId(){return i(r(this,x))}set descriptionId(t){a(r(this,x),t,!0)}get cancelNode(){return i(r(this,R))}set cancelNode(t){a(r(this,R),t,!0)}get attrs(){return i(r(this,A))}set attrs(t){a(r(this,A),t)}handleOpen(){this.opts.open.current||(this.opts.open.current=!0)}handleClose(){this.opts.open.current&&(this.opts.open.current=!1)}get sharedProps(){return i(r(this,O))}set sharedProps(t){a(r(this,O),t)}}P=new WeakMap,C=new WeakMap,I=new WeakMap,_=new WeakMap,N=new WeakMap,S=new WeakMap,x=new WeakMap,R=new WeakMap,A=new WeakMap,O=new WeakMap;var E;class _t{constructor(t,o){d(this,"opts");d(this,"root");n(this,E,h(()=>({id:this.opts.id.current,"aria-haspopup":"dialog","aria-expanded":Dt(this.root.opts.open.current),"aria-controls":this.root.contentId,[this.root.attrs.trigger]:"",onkeydown:this.onkeydown,onclick:this.onclick,disabled:this.opts.disabled.current?!0:void 0,...this.root.sharedProps})));this.opts=t,this.root=o,g({...t,onRefChange:s=>{this.root.triggerNode=s,this.root.triggerId=s==null?void 0:s.id}}),this.onclick=this.onclick.bind(this),this.onkeydown=this.onkeydown.bind(this)}onclick(t){this.opts.disabled.current||t.button>0||this.root.handleOpen()}onkeydown(t){this.opts.disabled.current||(t.key===ot||t.key===st)&&(t.preventDefault(),this.root.handleOpen())}get props(){return i(r(this,E))}set props(t){a(r(this,E),t)}}E=new WeakMap;var Y,T;class Nt{constructor(t,o){d(this,"opts");d(this,"root");n(this,Y,h(()=>this.root.attrs[this.opts.variant.current]));n(this,T,h(()=>({id:this.opts.id.current,[i(r(this,Y))]:"",onclick:this.onclick,onkeydown:this.onkeydown,disabled:this.opts.disabled.current?!0:void 0,tabindex:0,...this.root.sharedProps})));this.opts=t,this.root=o,this.onclick=this.onclick.bind(this),this.onkeydown=this.onkeydown.bind(this),g({...t,deps:()=>this.root.opts.open.current})}onclick(t){this.opts.disabled.current||t.button>0||this.root.handleClose()}onkeydown(t){this.opts.disabled.current||(t.key===ot||t.key===st)&&(t.preventDefault(),this.root.handleClose())}get props(){return i(r(this,T))}set props(t){a(r(this,T),t)}}Y=new WeakMap,T=new WeakMap;var Z,M;class St{constructor(t,o){d(this,"opts");d(this,"root");n(this,Z,h(()=>this.root.attrs.action));n(this,M,h(()=>({id:this.opts.id.current,[i(r(this,Z))]:"",...this.root.sharedProps})));this.opts=t,this.root=o,g(t)}get props(){return i(r(this,M))}set props(t){a(r(this,M),t)}}Z=new WeakMap,M=new WeakMap;var q;class xt{constructor(t,o){d(this,"opts");d(this,"root");n(this,q,h(()=>({id:this.opts.id.current,role:"heading","aria-level":this.opts.level.current,[this.root.attrs.title]:"",...this.root.sharedProps})));this.opts=t,this.root=o,g({...t,onRefChange:s=>{this.root.titleId=s==null?void 0:s.id},deps:()=>this.root.opts.open.current})}get props(){return i(r(this,q))}set props(t){a(r(this,q),t)}}q=new WeakMap;var B;class Rt{constructor(t,o){d(this,"opts");d(this,"root");n(this,B,h(()=>({id:this.opts.id.current,[this.root.attrs.description]:"",...this.root.sharedProps})));this.opts=t,this.root=o,g({...t,deps:()=>this.root.opts.open.current,onRefChange:s=>{this.root.descriptionNode=s,this.root.descriptionId=s==null?void 0:s.id}})}get props(){return i(r(this,B))}set props(t){a(r(this,B),t)}}B=new WeakMap;var j,z;class At{constructor(t,o){d(this,"opts");d(this,"root");n(this,j,h(()=>({open:this.root.opts.open.current})));n(this,z,h(()=>({id:this.opts.id.current,role:this.root.opts.variant.current==="alert-dialog"?"alertdialog":"dialog","aria-modal":"true","aria-describedby":this.root.descriptionId,"aria-labelledby":this.root.titleId,[this.root.attrs.content]:"",style:{pointerEvents:"auto",outline:this.root.opts.variant.current==="alert-dialog"?"none":void 0},tabindex:this.root.opts.variant.current==="alert-dialog"?-1:void 0,...this.root.sharedProps})));this.opts=t,this.root=o,g({...t,deps:()=>this.root.opts.open.current,onRefChange:s=>{this.root.contentNode=s,this.root.contentId=s==null?void 0:s.id}})}get snippetProps(){return i(r(this,j))}set snippetProps(t){a(r(this,j),t)}get props(){return i(r(this,z))}set props(t){a(r(this,z),t)}}j=new WeakMap,z=new WeakMap;var F,G;class Ot{constructor(t,o){d(this,"opts");d(this,"root");n(this,F,h(()=>({open:this.root.opts.open.current})));n(this,G,h(()=>({id:this.opts.id.current,[this.root.attrs.overlay]:"",style:{pointerEvents:"auto"},...this.root.sharedProps})));this.opts=t,this.root=o,g({...t,deps:()=>this.root.opts.open.current})}get snippetProps(){return i(r(this,F))}set snippetProps(t){a(r(this,F),t)}get props(){return i(r(this,G))}set props(t){a(r(this,G),t)}}F=new WeakMap,G=new WeakMap;var H;class Et{constructor(t,o){d(this,"opts");d(this,"root");n(this,H,h(()=>({id:this.opts.id.current,[this.root.attrs.cancel]:"",onclick:this.onclick,onkeydown:this.onkeydown,tabindex:0,...this.root.sharedProps})));this.opts=t,this.root=o,this.onclick=this.onclick.bind(this),this.onkeydown=this.onkeydown.bind(this),g({...t,deps:()=>this.root.opts.open.current,onRefChange:s=>{this.root.cancelNode=s}})}onclick(t){this.opts.disabled.current||t.button>0||this.root.handleClose()}onkeydown(t){this.opts.disabled.current||(t.key===ot||t.key===st)&&(t.preventDefault(),this.root.handleClose())}get props(){return i(r(this,H))}set props(t){a(r(this,H),t)}}H=new WeakMap;const l=new bt("Dialog.Root");function Yt(e){return l.set(new It(e))}function Zt(e){return new _t(e,l.get())}function Tt(e){return new xt(e,l.get())}function $t(e){return new At(e,l.get())}function Mt(e){return new Ot(e,l.get())}function te(e){return new Rt(e,l.get())}function ee(e){return new Nt(e,l.get())}function re(e){return new Et(e,l.get())}function oe(e){return new St(e,l.get())}var qt=nt("<div><!></div>");function se(e,t){at(t,!0);let o=y(t,"id",19,ft),s=y(t,"ref",15,null),J=y(t,"level",3,2),$=gt(t,["$$slots","$$events","$$legacy","id","ref","child","children","level"]);const v=Tt({id:D.with(()=>o()),level:D.with(()=>J()),ref:D.with(()=>s(),p=>s(p))}),b=h(()=>U($,v.props));var K=V(),L=W(K);{var tt=p=>{var c=V(),w=W(c);X(w,()=>t.child,()=>({props:i(b)})),m(p,c)},Q=p=>{var c=qt();ut(c,()=>({...i(b)}));var w=ht(c);X(w,()=>t.children??pt),ct(c),m(p,c)};lt(L,p=>{t.child?p(tt):p(Q,!1)})}m(e,K),dt()}function ie({forceMount:e,present:t,trapFocus:o,open:s}){return e?s&&o:t&&o&&s}var Bt=nt("<div><!></div>");function ne(e,t){at(t,!0);let o=y(t,"id",19,ft),s=y(t,"forceMount",3,!1),J=y(t,"ref",15,null),$=gt(t,["$$slots","$$events","$$legacy","id","forceMount","child","children","ref"]);const v=Mt({id:D.with(()=>o()),ref:D.with(()=>J(),L=>J(L))}),b=h(()=>U($,v.props)),K=h(()=>v.root.opts.open.current||s());Pt(e,{get id(){return o()},get present(){return i(K)},presence:tt=>{var Q=V(),p=W(Q);{var c=k=>{var f=V(),et=W(f),rt=yt(()=>({props:U(i(b)),...v.snippetProps}));X(et,()=>t.child,()=>i(rt)),m(k,f)},w=k=>{var f=Bt();ut(f,rt=>({...rt}),[()=>U(i(b))]);var et=ht(f);X(et,()=>t.children??pt,()=>v.snippetProps),ct(f),m(k,f)};lt(p,k=>{t.child?k(c):k(w,!1)})}m(tt,Q)},$$slots:{presence:!0}}),dt()}export{se as D,Zt as a,Yt as b,ee as c,$t as d,ne as e,oe as f,re as g,ie as s,te as u};
