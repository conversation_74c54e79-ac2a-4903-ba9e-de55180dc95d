import * as server from '../entries/pages/studio/_page.server.ts.js';

export const index = 94;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/studio/_page.svelte.js')).default;
export { server };
export const server_id = "src/routes/studio/+page.server.ts";
export const imports = ["_app/immutable/nodes/94.B0lTtAW5.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/BiJhC7W5.js"];
export const stylesheets = ["_app/immutable/assets/94.DmtFXJ4w.css"];
export const fonts = [];
