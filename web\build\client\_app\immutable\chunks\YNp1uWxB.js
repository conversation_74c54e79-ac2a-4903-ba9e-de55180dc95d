import{b as c}from"./Cf6rS4LV.js";let r=[],n={},i=0;const f=60*1e3;async function u(){try{const t=Date.now();if(r.length>0&&t-i<f)return r;const e=await fetch("/api/admin/features",{credentials:"include"});if(!e.ok)throw new Error("Failed to fetch features");r=(await e.json()).features??[],i=t,n={};for(const a of r)if(a.limits)for(const o of a.limits)n[o.id]=o;return r}catch(t){return console.error("Error fetching features:",t),[]}}const s=[];function d(t){return r.find(e=>e.id===t)}function g(t){return r.filter(e=>e.category===t)}function m(t){return n[t]}u().then(t=>{s.length=0,s.push(...t);for(const e in n)n[e]});export{s as F,d as a,g as b,m as g};
