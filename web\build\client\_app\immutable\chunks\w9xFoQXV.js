import{c as n,a as m}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function L(a,o){const r=c(o,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16"}]];f(a,d({name:"laptop"},()=>r,{get iconNode(){return s},children:(e,$)=>{var t=n(),p=i(t);l(p,o,"default",{},null),m(e,t)},$$slots:{default:!0}}))}export{L};
