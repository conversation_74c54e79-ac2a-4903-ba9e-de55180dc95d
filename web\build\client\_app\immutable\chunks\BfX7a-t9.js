var P=t=>{throw TypeError(t)};var W=(t,e,r)=>e.has(t)||P("Cannot "+r);var g=(t,e,r)=>(W(t,e,"read from private field"),r?r.call(t):e.get(t)),x=(t,e,r)=>e.has(t)?P("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,r),A=(t,e,r,n)=>(W(t,e,"write to private field"),n?n.call(t,r):e.set(t,r),r);import{d as X,be as z,g as T,a7 as K,$ as Z,o as k,j as q,k as J,v as Q,x as tt,u as et,i as H}from"./CGmarHxI.js";import{h as I}from"./B-Xjo-Yt.js";import{c as B}from"./BosuxZz1.js";import{o as L}from"./CmxjS0TN.js";function rt(t){X(t,t.v+1)}function nt(t){let e=0,r=K(0),n;return()=>{z()&&(T(r),Z(()=>(e===0&&(n=k(()=>t(()=>rt(r)))),e+=1,()=>{q().then(()=>{e-=1,e===0&&(n==null||n(),n=void 0)})})))}}function it(t){return typeof t=="function"}function ot(t){return t!==null&&typeof t=="object"}const st=["string","number","bigint","boolean"];function O(t){return t==null||st.includes(typeof t)?!0:Array.isArray(t)?t.every(e=>O(e)):typeof t=="object"?Object.getPrototypeOf(t)===Object.prototype:!1}const w=Symbol("box"),j=Symbol("is-writable");function ut(t){return ot(t)&&w in t}function ct(t){return l.isBox(t)&&j in t}function l(t){let e=J(Q(t));return{[w]:!0,[j]:!0,get current(){return T(e)},set current(r){X(e,r,!0)}}}function at(t,e){const r=tt(t);return e?{[w]:!0,[j]:!0,get current(){return T(r)},set current(n){e(n)}}:{[w]:!0,get current(){return t()}}}function ft(t){return l.isBox(t)?t:it(t)?l.with(t):l(t)}function lt(t){return Object.entries(t).reduce((e,[r,n])=>l.isBox(n)?(l.isWritableBox(n)?Object.defineProperty(e,r,{get(){return n.current},set(i){n.current=i}}):Object.defineProperty(e,r,{get(){return n.current}}),e):Object.assign(e,{[r]:n}),{})}function pt(t){return l.isWritableBox(t)?{[w]:!0,get current(){return t.current}}:t}l.from=ft;l.with=at;l.flatten=lt;l.readonly=pt;l.isBox=ut;l.isWritableBox=ct;function dt(...t){return function(e){var r;for(const n of t)if(n){if(e.defaultPrevented)return;typeof n=="function"?n.call(this,e):(r=n.current)==null||r.call(this,e)}}}var U={},R=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,ht=/\n/g,bt=/^\s*/,mt=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,yt=/^:\s*/,Et=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,vt=/^[;\s]*/,wt=/^\s+|\s+$/g,_t=`
`,C="/",M="*",m="",gt="comment",St="declaration",xt=function(t,e){if(typeof t!="string")throw new TypeError("First argument must be a string");if(!t)return[];e=e||{};var r=1,n=1;function i(c){var s=c.match(ht);s&&(r+=s.length);var h=c.lastIndexOf(_t);n=~h?c.length-h:n+c.length}function o(){var c={line:r,column:n};return function(s){return s.position=new u(c),f(),s}}function u(c){this.start=c,this.end={line:r,column:n},this.source=e.source}u.prototype.content=t;function a(c){var s=new Error(e.source+":"+r+":"+n+": "+c);if(s.reason=c,s.filename=e.source,s.line=r,s.column=n,s.source=t,!e.silent)throw s}function p(c){var s=c.exec(t);if(s){var h=s[0];return i(h),t=t.slice(h.length),s}}function f(){p(bt)}function d(c){var s;for(c=c||[];s=y();)s!==!1&&c.push(s);return c}function y(){var c=o();if(!(C!=t.charAt(0)||M!=t.charAt(1))){for(var s=2;m!=t.charAt(s)&&(M!=t.charAt(s)||C!=t.charAt(s+1));)++s;if(s+=2,m===t.charAt(s-1))return a("End of comment missing");var h=t.slice(2,s-2);return n+=2,i(h),t=t.slice(s),n+=2,c({type:gt,comment:h})}}function v(){var c=o(),s=p(mt);if(s){if(y(),!p(yt))return a("property missing ':'");var h=p(Et),Y=c({type:St,property:F(s[0].replace(R,m)),value:h?F(h[0].replace(R,m)):m});return p(vt),Y}}function b(){var c=[];d(c);for(var s;s=v();)s!==!1&&(c.push(s),d(c));return c}return f(),b()};function F(t){return t?t.replace(wt,m):m}var At=B&&B.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(U,"__esModule",{value:!0});var G=U.default=Ot,It=At(xt);function Ot(t,e){var r=null;if(!t||typeof t!="string")return r;var n=(0,It.default)(t),i=typeof e=="function";return n.forEach(function(o){if(o.type==="declaration"){var u=o.property,a=o.value;i?e(u,a,o):a&&(r=r||{},r[u]=a)}}),r}const Tt=G.default||G,jt=/\d/,Pt=["-","_","/","."];function Wt(t=""){if(!jt.test(t))return t!==t.toLowerCase()}function Bt(t){const e=[];let r="",n,i;for(const o of t){const u=Pt.includes(o);if(u===!0){e.push(r),r="",n=void 0;continue}const a=Wt(o);if(i===!1){if(n===!1&&a===!0){e.push(r),r=o,n=a;continue}if(n===!0&&a===!1&&r.length>1){const p=r.at(-1);e.push(r.slice(0,Math.max(0,r.length-1))),r=p+o,n=a;continue}}r+=o,n=a,i=u}return e.push(r),e}function V(t){return t?Bt(t).map(e=>Rt(e)).join(""):""}function Lt(t){return Ct(V(t||""))}function Rt(t){return t?t[0].toUpperCase()+t.slice(1):""}function Ct(t){return t?t[0].toLowerCase()+t.slice(1):""}function S(t){if(!t)return{};const e={};function r(n,i){if(n.startsWith("-moz-")||n.startsWith("-webkit-")||n.startsWith("-ms-")||n.startsWith("-o-")){e[V(n)]=i;return}if(n.startsWith("--")){e[n]=i;return}e[Lt(n)]=i}return Tt(t,r),e}function Mt(...t){return(...e)=>{for(const r of t)typeof r=="function"&&r(...e)}}function Ft(t,e){const r=RegExp(t,"g");return n=>{if(typeof n!="string")throw new TypeError(`expected an argument of type string, but got ${typeof n}`);return n.match(r)?n.replace(r,e):n}}const Gt=Ft(/[A-Z]/,t=>`-${t.toLowerCase()}`);function Xt(t){if(!t||typeof t!="object"||Array.isArray(t))throw new TypeError(`expected an argument of type object, but got ${typeof t}`);return Object.keys(t).map(e=>`${Gt(e)}: ${t[e]};`).join(`
`)}function $(t={}){return Xt(t).replace(`
`," ")}const kt={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",transform:"translateX(-100%)"},Qt=$(kt);function Ht(t){var e;return t.length>2&&t.startsWith("on")&&t[2]===((e=t[2])==null?void 0:e.toLowerCase())}function te(...t){const e={...t[0]};for(let r=1;r<t.length;r++){const n=t[r];for(const i in n){const o=e[i],u=n[i],a=typeof o=="function",p=typeof u=="function";if(a&&Ht(i)){const f=o,d=u;e[i]=dt(f,d)}else if(a&&p)e[i]=Mt(o,u);else if(i==="class"){const f=O(o),d=O(u);f&&d?e[i]=I(o,u):f?e[i]=I(o):d&&(e[i]=I(u))}else if(i==="style"){const f=typeof o=="object",d=typeof u=="object",y=typeof o=="string",v=typeof u=="string";if(f&&d)e[i]={...o,...u};else if(f&&v){const b=S(u);e[i]={...o,...b}}else if(y&&d){const b=S(o);e[i]={...b,...u}}else if(y&&v){const b=S(o),c=S(u);e[i]={...b,...c}}else f?e[i]=o:d?e[i]=u:y?e[i]=o:v&&(e[i]=u)}else e[i]=u!==void 0?u:o}}return typeof e.style=="object"&&(e.style=$(e.style).replaceAll(`
`," ")),e.hidden!==!0&&(e.hidden=void 0,delete e.hidden),e.disabled!==!0&&(e.disabled=void 0,delete e.disabled),e}const Ut=typeof window<"u"?window:void 0;function Vt(t){let e=t.activeElement;for(;e!=null&&e.shadowRoot;){const r=e.shadowRoot.activeElement;if(r===e)break;e=r}return e}var E,_;class $t{constructor(e={}){x(this,E);x(this,_);const{window:r=Ut,document:n=r==null?void 0:r.document}=e;r!==void 0&&(A(this,E,n),A(this,_,nt(i=>{const o=L(r,"focusin",i),u=L(r,"focusout",i);return()=>{o(),u()}})))}get current(){var e;return(e=g(this,_))==null||e.call(this),g(this,E)?Vt(g(this,E)):null}}E=new WeakMap,_=new WeakMap;new $t;function Nt(t,e){switch(t){case"post":H(e);break;case"pre":et(e);break}}function N(t,e,r,n={}){const{lazy:i=!1}=n;let o=!i,u=Array.isArray(t)?[]:void 0;Nt(e,()=>{const a=Array.isArray(t)?t.map(f=>f()):t();if(!o){o=!0,u=a;return}const p=k(()=>r(a,u));return u=a,p})}function D(t,e,r){N(t,"post",e,r)}function Dt(t,e,r){N(t,"pre",e,r)}D.pre=Dt;function Yt(t){H(()=>()=>{t()})}function ee({id:t,ref:e,deps:r=()=>!0,onRefChange:n,getRootNode:i}){D([()=>t.current,r],([o])=>{const u=(i==null?void 0:i())??document,a=u==null?void 0:u.getElementById(o);a?e.current=a:e.current=null,n==null||n(e.current)}),Yt(()=>{e.current=null,n==null||n(null)})}export{kt as a,l as b,nt as c,Ut as d,Mt as e,S as f,$ as g,dt as h,rt as i,te as m,Yt as o,Tt as p,Qt as s,ee as u,D as w};
