import{c as Fe,a as r,f as i,t as J}from"../chunks/BasJTneF.js";import{p as Ft,f as d,a as Lt,k as dt,v as _r,w as Mr,i as br,s as t,n as g,t as $,g as e,x as O,c as o,r as s,d as j,l as Ct,m as Dt,h as ft,b as Zr,e as ea,o as H}from"../chunks/CGmarHxI.js";import{i as h}from"../chunks/u21ee2wt.js";import{c as v}from"../chunks/BvdI7LR8.js";import{a as Xt,s as yr,b as Xe}from"../chunks/CmxjS0TN.js";import"../chunks/CgXBgsce.js";import{t as We}from"../chunks/DjPYYl4Z.js";import{S as ta}from"../chunks/C6g8ubaU.js";import{s as y}from"../chunks/CIt1g2O9.js";import{e as It,i as Nr}from"../chunks/C3w0v0gR.js";import{a as Jr,r as mt,f as xt}from"../chunks/B-Xjo-Yt.js";import{p as pt,s as ra,r as aa}from"../chunks/Btcx8l8F.js";import{P as sa,S as oa,a as na,b as ia,c as la,d as da,R as ca}from"../chunks/CTn0v-X8.js";import{B as Ge}from"../chunks/B1K98fMG.js";import{B as At}from"../chunks/DaBofrVv.js";import{P as lr}from"../chunks/DrGkVJ95.js";import{S as Or}from"../chunks/D9yI7a4E.js";import{P as ua,g as va,A as fa,a as ma,b as pa,c as ga,d as _a,e as ha,f as xa,R as ya}from"../chunks/BnikQ10_.js";import{A as $a,a as ba,b as wa,c as Pa}from"../chunks/BPr9JIwg.js";import{S as Fr}from"../chunks/C2MdR6K0.js";import{a as wr,e as Sa}from"../chunks/ncUU1dSD.js";import{R as Lr}from"../chunks/C4zOxlM4.js";import{R as Ur}from"../chunks/B8CsXmVA.js";import{D as Aa}from"../chunks/CQeqUgF6.js";import{R as Pr}from"../chunks/qwsZpUIl.js";import{S as Ra}from"../chunks/KVutzy_p.js";import{C as hr}from"../chunks/D6Qh9vtB.js";import{T as Sr}from"../chunks/CZ8wIJN8.js";import{F as zr}from"../chunks/ChqRiddM.js";import{B as ka}from"../chunks/CDnvByek.js";import{B as Ca}from"../chunks/C2AK_5VT.js";import{M as Da}from"../chunks/CwgkX8t9.js";import{D as Ia}from"../chunks/6BxQgNmX.js";import{C as Ta}from"../chunks/DZCYCPd3.js";import{C as dr}from"../chunks/-SpbofVw.js";import{C as Br}from"../chunks/BAIxhb6t.js";import{C as cr}from"../chunks/DW7T7T22.js";import{P as Ot}from"../chunks/DvO_AOqy.js";import{a as Ea}from"../chunks/DDUgF6Ik.js";import{b as or}from"../chunks/CzsE_FAw.js";import{I as Vr}from"../chunks/DMTMHyMa.js";import{C as qr}from"../chunks/DuGukytH.js";import{C as Kr}from"../chunks/Cdn-N1RY.js";import{C as Yr}from"../chunks/BkJY4La4.js";import{C as Gr}from"../chunks/DETxXRrJ.js";import{C as Hr}from"../chunks/GwmmX_iF.js";import{C as Qr}from"../chunks/D50jIuLr.js";import{R as Ar,S as Rr,a as kr,b as Cr}from"../chunks/CGK0g3x_.js";import{S as nr}from"../chunks/P6MDDUUJ.js";import{L as Gt}from"../chunks/BvvicRXk.js";import{M as Dr}from"../chunks/Ci8yIwIB.js";import{s as ja}from"../chunks/BBa424ah.js";import{i as Ma}from"../chunks/BIEMS98f.js";import{c as Na}from"../chunks/BMZasLyv.js";import{o as Ja}from"../chunks/DrHxToS6.js";import{isFeatureEnabled as Oa,shouldBypassLimits as Fa,getFeatureConfig as La}from"../chunks/Bjxev4T5.js";import{T as $r}from"../chunks/CTO_B1Jk.js";import{L as Ua}from"../chunks/DHNQRrgO.js";import{g as Ir}from"../chunks/BiJhC7W5.js";import{R as za,D as Ba,a as Va}from"../chunks/tdzGgazS.js";import{S as Ht}from"../chunks/yW0TxTga.js";import{S as Tr}from"../chunks/B2lQHLf_.js";import{D as qa,a as Ka,b as Ya,c as Ga}from"../chunks/CKh8VGVX.js";import{P as Xr}from"../chunks/DR5zc253.js";import{X as Ha}from"../chunks/CnpHcmx3.js";import{a as ur}from"../chunks/Dqigtbi1.js";import{s as Qa}from"../chunks/B8blszX7.js";import{z as Xa}from"../chunks/CrHU05dq.js";import{o as Wa,a as Pt,s as St,b as Za,d as ir}from"../chunks/C8B1VUaq.js";import{T as es,R as ts}from"../chunks/I7hvcB12.js";import{T as Er}from"../chunks/C88uNE8B.js";import{U as rs}from"../chunks/B_6ivTD3.js";import{T as jr}from"../chunks/DmZyh-PW.js";function as(w,l){Ft(l,!0);let re=pt(l,"ref",15,null),Le=aa(l,["$$slots","$$events","$$legacy","ref"]);var a=Fe(),ve=d(a);v(ve,()=>Aa,(he,Re)=>{Re(he,ra({"data-slot":"sheet-trigger"},()=>Le,{get ref(){return re()},set ref(ke){re(ke)}}))}),r(w,a),Lt()}var ss=i("<!> <!>",1),os=i("<!> ",1),ns=i('<span class="text-xs text-gray-400"> </span>'),is=i("<!> ",1),ls=i("<!> ",1),ds=i('<h3 class="text-sm font-medium text-gray-400">Search Parameters</h3>'),cs=i("<!> ",1),us=i('<div class="mb-1 text-base font-medium"> </div> <div class="mb-2 text-sm text-gray-400"> </div> <!>',1),vs=i('<div class="text-sm text-gray-400">Profile information not available</div>'),fs=i('<div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="rounded-lg border p-4"><h4 class="mb-2 text-sm font-medium text-gray-400">Profile</h4> <!></div> <div class="rounded-lg border p-4"><h4 class="mb-2 text-sm font-medium text-gray-400">Search Criteria</h4> <div class="space-y-2"><div><span class="text-xs text-gray-400">Keywords:</span> <div class="text-sm"><!></div></div> <div><span class="text-xs text-gray-400">Location:</span> <div class="text-sm"><!></div></div></div></div></div>'),ms=i("<!> <!>",1),ps=i("<!> ",1),gs=i('<div class="flex flex-col items-center justify-center rounded-lg border border-dashed p-6 text-center"><!> <p class="text-sm text-gray-400">Loading jobs...</p></div>'),_s=i('<div class="flex flex-col items-center justify-center rounded-lg border border-dashed p-6 text-center"><!> <p class="text-sm text-gray-400"><!></p></div>'),hs=i('<div class="flex items-center text-sm text-gray-400"><!> </div>'),xs=i('<div class="flex items-center text-gray-400"><!> </div>'),ys=i('<div class="flex items-center text-gray-400"><!> </div>'),$s=i('<div class="flex items-center text-gray-400"><!> </div>'),bs=i('<div class="flex items-center gap-1"><!></div>'),ws=i('<div class="flex flex-wrap gap-1"><!> <!></div>'),Ps=i('<div class="flex flex-col space-y-3 rounded-lg border p-4"><div class="flex items-start justify-between"><div class="min-w-0 flex-1"><div class="flex items-center gap-2"><a target="_blank" class="font-medium"> </a> <!></div> <!></div> <div class="flex items-center gap-2"><!></div></div> <div class="grid grid-cols-2 gap-2 text-xs"><!> <!> <!> <!></div> <div class="space-y-2"><!> <!></div></div>'),Ss=i('<div class="grid grid-cols-2 gap-4"></div>'),As=i('<div class="space-y-6 px-4"><div class="flex items-center justify-between"><div class="flex items-center gap-2"><!> <!></div> <div class="flex items-center gap-2"><!> <!></div></div> <!> <div><div class="mb-4 flex items-center justify-between"><h3 class="text-sm font-medium text-gray-400"> </h3> <!></div> <!></div></div>'),Rs=i('<div class="flex h-40 items-center justify-center"><p class="text-gray-400">No automation run data available</p></div>'),ks=i("<!> <!>",1),Cs=i('<!> <!> <div class="flex flex-row justify-between p-4 pt-2"><div class="text-xs font-medium text-gray-400">Progress</div> <div class="text-right text-xs text-gray-400"> </div></div> <!> <!>',1),Ds=i("<!> <!>",1),Is=i("<!> <!>",1),Ts=i(" <br/><br/> This will automatically submit applications to the selected jobs using your profile and resume.",1),Es=i("<!> Enable Auto-Apply",1),js=i("<!> <!>",1),Ms=i("<!> <!> <!>",1),Ns=i("<!> <!>",1),Js=i("<!> <!>",1);function Os(w,l){Ft(l,!0);const[re,Le]=yr(),a=()=>Xt(xe,"$runStore",re),ve=pt(l,"open",11,!1),he=pt(l,"onClose",3,()=>{}),Re=pt(l,"onRefresh",3,()=>{}),ke=pt(l,"onStop",3,()=>{});let fe=dt(!1),ee=dt(!1),Z=dt(_r(new Set)),st=dt(!1),ot=dt(_r([])),q=dt(!1);const xe=Mr(l.automationRun);br(()=>{l.automationRun&&xe.set({...l.automationRun,autoApplyEnabled:!1})});async function Ce(){if(!a()||!a().id||!a().matchedJobIds||a().matchedJobIds.length===0){j(ot,[],!0);return}j(q,!0);try{const n=await fetch(`/api/automation/runs/${a().id}/jobs`);if(n.ok){const p=await n.json();j(ot,p.map(c=>({...c,matchScore:De(c.id),postedAt:c.postedDate||c.createdAt})),!0)}else console.error("Failed to fetch job listings"),j(ot,[],!0)}catch(n){console.error("Error fetching job listings:",n),j(ot,[],!0)}finally{j(q,!1)}}function De(n){var c;if(!((c=a())!=null&&c.jobMatchData))return 0;const p=a().jobMatchData[n];return(p==null?void 0:p.matchScore)||0}function be(n){if(n.salary)return n.salary;if(n.salaryMin&&n.salaryMax){const p=Math.round(n.salaryMin/1e3),c=Math.round(n.salaryMax/1e3);return`$${p}k - $${c}k`}return n.salaryMin?`$${Math.round(n.salaryMin/1e3)}k+`:""}const je=O(()=>e(ot));br(()=>{a()&&ve()&&Ce()});function m(n){e(Z).has(n)?e(Z).delete(n):e(Z).add(n),j(Z,new Set(e(Z)),!0)}function we(){if(e(Z).size===0){We.error("Please select at least one job to enable auto-apply");return}j(st,!0)}async function I(){if(!(!a()||!a().id))try{const n=Array.from(e(Z));console.log("Enabling auto-apply for jobs:",n);const p=await fetch(`/api/automation/runs/${a().id}/settings`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({autoApplyEnabled:!0,selectedJobIds:n})});if(p.ok){const c=await p.json();xe.set(c),We.success(`Auto-apply enabled for ${n.length} job${n.length===1?"":"s"}`),j(st,!1),e(Z).clear()}else{const c=await p.json();We.error(c.message||"Failed to enable auto-apply")}}catch(n){console.error("Error enabling auto-apply:",n),We.error("Failed to enable auto-apply")}}async function me(){if(!(!a()||!a().id)){j(ee,!0);try{const n=await fetch(`/api/automation/runs/${a().id}/stop`,{method:"POST"});if(n.ok){const p=await n.json();xe.update(c=>({...c,status:"stopped",stoppedAt:p.stoppedAt})),We.success("Automation run stopped"),ke()(a().id)}else{const p=await n.json();We.error(p.message||"Failed to stop automation run")}}catch(n){console.error("Error stopping automation run:",n),We.error("An error occurred while stopping the automation run")}finally{j(ee,!1)}}}async function pe(){if(!(!a()||!a().id)){j(fe,!0);try{const n=await fetch(`/api/automation/runs/${a().id}`);if(n.ok){const p=await n.json();xe.set(p),await Ce(),We.success("Data refreshed"),Re()(p)}else We.error("Failed to refresh data")}catch(n){console.error("Error refreshing data:",n),We.error("An error occurred while refreshing data")}finally{j(fe,!1)}}}function Ve(n){switch(n){case"start":case"running":return"default";case"completed":return"outline";case"failed":return"destructive";case"stopped":return"secondary";case"in progress":case"pending":return"secondary";default:return"secondary"}}function Ze(n){switch(n){case"start":case"running":return Ot;case"completed":return cr;case"failed":return Br;case"stopped":return hr;case"in progress":case"pending":return dr;default:return dr}}function ct(n){switch(n){case"start":return"Starting";case"running":return"Running";case"completed":return"Completed";case"failed":return"Failed";case"stopped":return"Stopped";case"in progress":return"In Progress";case"pending":return"Pending";default:return n.charAt(0).toUpperCase()+n.slice(1)}}function qe(n){return n?n.status==="completed"?100:["failed","stopped"].includes(n.status)?n.progress||0:n.status==="start"?5:n.status==="in progress"?n.progress||50:n.progress||0:0}function Ue(n){if(!(n!=null&&n.data))return{};try{return typeof n.data=="string"?JSON.parse(n.data):n.data}catch(p){return console.error("Error parsing profile data:",p),{}}}function u(){he()()}var T=Js(),F=d(T);v(F,()=>ca,(n,p)=>{p(n,{get open(){return ve()},onOpenChange:u,children:(c,ae)=>{var b=Is(),S=d(b);v(S,()=>as,(f,U)=>{U(f,{})});var L=t(S,2);v(L,()=>sa,(f,U)=>{U(f,{children:(se,oe)=>{var A=Ds(),X=d(A);v(X,()=>oa,(K,ne)=>{ne(K,{})});var z=t(X,2);v(z,()=>na,(K,ne)=>{ne(K,{side:"right",class:"w-full gap-0 sm:max-w-xl md:max-w-2xl lg:max-w-3xl",children:(Me,ut)=>{var Pe=Cs(),Ke=d(Pe);v(Ke,()=>ia,(Se,_)=>{_(Se,{class:"border-border m-0 flex flex-col gap-0 border-b",children:(P,M)=>{var ie=ss(),B=d(ie);v(B,()=>la,(k,N)=>{N(k,{class:"text-lg",children:(C,le)=>{g();var V=J();$(W=>y(V,W),[()=>Ue(a().profile).fullName||"Unnamed Profile"]),r(C,V)},$$slots:{default:!0}})});var R=t(B,2);v(R,()=>da,(k,N)=>{N(k,{children:(C,le)=>{g();var V=J();$(W=>y(V,W),[()=>Ue(a().profile).title||Ue(a().profile).headline||"No title specified"]),r(C,V)},$$slots:{default:!0}})}),r(P,ie)},$$slots:{default:!0}})});var et=t(Ke,2);const ze=O(()=>qe(a()));lr(et,{get value(){return e(ze)},max:100,class:"mb-0 rounded-none"});var Ye=t(et,2),nt=t(o(Ye),2),tt=o(nt);s(nt),s(Ye);var ce=t(Ye,2);Fr(ce,{class:"h-[calc(100vh-100px)] w-full",children:(Se,_)=>{var P=Fe(),M=d(P);{var ie=R=>{var k=As(),N=o(k),C=o(N),le=o(C);const V=O(()=>Ve(a().status));At(le,{get variant(){return e(V)},class:"text-sm",children:(de,Ee)=>{var Je=os();const rt=O(()=>Ze(a().status));var ge=d(Je);v(ge,()=>e(rt),(Qe,Y)=>{Y(Qe,{class:"mr-1 h-3 w-3"})});var $e=t(ge);$(Qe=>y($e,` ${Qe??""}`),[()=>ct(a().status)]),r(de,Je)},$$slots:{default:!0}});var W=t(le,2);{var Ie=de=>{var Ee=ns(),Je=o(Ee);s(Ee),$(rt=>y(Je,`Started ${rt??""} ago`),[()=>wr(new Date(a().createdAt))]),r(de,Ee)};h(W,de=>{a().createdAt&&de(Ie)})}s(C);var Te=t(C,2),ue=o(Te);{var ye=de=>{Ge(de,{variant:"outline",size:"sm",onclick:me,get disabled(){return e(ee)},children:(Ee,Je)=>{var rt=is(),ge=d(rt);hr(ge,{class:"mr-2 h-4 w-4"});var $e=t(ge);$(()=>y($e,` ${e(ee)?"Stopping...":"Stop Run"}`)),r(Ee,rt)},$$slots:{default:!0}})};h(ue,de=>{["running","pending","start","in progress"].includes(a().status)&&de(ye)})}var it=t(ue,2);Ge(it,{variant:"outline",size:"sm",onclick:pe,get disabled(){return e(fe)},children:(de,Ee)=>{var Je=ls(),rt=d(Je);const ge=O(()=>e(fe)?"animate-spin":"");Pr(rt,{get class(){return`mr-2 h-4 w-4 ${e(ge)??""}`}});var $e=t(rt);$(()=>y($e,` ${e(fe)?"Refreshing...":"Refresh"}`)),r(de,Je)},$$slots:{default:!0}}),s(Te),s(N);var gt=t(N,2);v(gt,()=>$a,(de,Ee)=>{Ee(de,{type:"single",class:"border-border w-full rounded-md border",children:(Je,rt)=>{var ge=Fe(),$e=d(ge);v($e,()=>ba,(Qe,Y)=>{Y(Qe,{value:"run-info",children:(ht,x)=>{var _e=ms(),G=d(_e);v(G,()=>wa,(at,zt)=>{zt(at,{class:"p-4 text-left",children:(Bt,vr)=>{var Rt=ds();r(Bt,Rt)},$$slots:{default:!0}})});var te=t(G,2);v(te,()=>Pa,(at,zt)=>{zt(at,{children:(Bt,vr)=>{var Rt=fs(),jt=o(Rt),fr=t(o(jt),2);{var mr=bt=>{var Nt=us(),Kt=d(Nt),D=o(Kt,!0);s(Kt);var E=t(Kt,2),Ae=o(E,!0);s(E);var Oe=t(E,2);{var wt=lt=>{At(lt,{variant:"outline",class:"text-xs",children:(kt,gr)=>{var Jt=cs(),Yt=d(Jt);zr(Yt,{class:"mr-1 h-3 w-3"});var Wr=t(Yt);$(()=>y(Wr,` ${a().profile.documents.length??""}
                            ${a().profile.documents.length===1?"resume":"resumes"}`)),r(kt,Jt)},$$slots:{default:!0}})};h(Oe,lt=>{a().profile.documents&&a().profile.documents.length>0&&lt(wt)})}$((lt,kt)=>{y(D,lt),y(Ae,kt)},[()=>Ue(a().profile).fullName||"Unnamed Profile",()=>Ue(a().profile).title||Ue(a().profile).headline||"No title specified"]),r(bt,Nt)},Vt=bt=>{var Nt=vs();r(bt,Nt)};h(fr,bt=>{a().profile?bt(mr):bt(Vt,!1)})}s(jt);var qt=t(jt,2),Wt=t(o(qt),2),Mt=o(Wt),Zt=t(o(Mt),2),er=o(Zt);const pr=O(()=>a().keywords||"");Lr(er,{get keywordIds(){return e(pr)},fallback:"None specified"}),s(Zt),s(Mt);var tr=t(Mt,2),rr=t(o(tr),2),ar=o(rr);const sr=O(()=>a().location||"");Ur(ar,{get locationIds(){return e(sr)},fallback:"None specified"}),s(rr),s(tr),s(Wt),s(qt),s(Rt),r(Bt,Rt)},$$slots:{default:!0}})}),r(ht,_e)},$$slots:{default:!0}})}),r(Je,ge)},$$slots:{default:!0}})});var _t=t(gt,2),Ne=o(_t),He=o(Ne),vt=o(He);s(He);var Tt=t(He,2);{var Ut=de=>{const Ee=O(()=>e(Z).size===0);Ge(de,{variant:"outline",size:"sm",onclick:we,get disabled(){return e(Ee)},class:"h-8 text-xs",children:(Je,rt)=>{var ge=ps(),$e=d(ge);Sr($e,{class:"mr-1 h-3 w-3"});var Qe=t($e);$(()=>y(Qe,` Auto Apply (${e(Z).size??""})`)),r(Je,ge)},$$slots:{default:!0}})};h(Tt,de=>{e(je).length>0&&!a().autoApplyEnabled&&de(Ut)})}s(Ne);var yt=t(Ne,2);{var $t=de=>{var Ee=gs(),Je=o(Ee);Pr(Je,{class:"mb-2 h-8 w-8 animate-spin text-gray-400"}),g(2),s(Ee),r(de,Ee)},Et=(de,Ee)=>{{var Je=ge=>{var $e=_s(),Qe=o($e);ka(Qe,{class:"mb-2 h-8 w-8 text-gray-400"});var Y=t(Qe,2),ht=o(Y);{var x=G=>{var te=J("Jobs will appear here as they are found");r(G,te)},_e=G=>{var te=J("No jobs were found during this automation run");r(G,te)};h(ht,G=>{["running","pending","start","in progress"].includes(a().status)?G(x):G(_e,!1)})}s(Y),s($e),r(ge,$e)},rt=ge=>{var $e=Ss();It($e,21,()=>e(je),Qe=>Qe.id,(Qe,Y)=>{var ht=Ps(),x=o(ht),_e=o(x),G=o(_e),te=o(G),at=o(te,!0);s(te);var zt=t(te,2);{var Bt=D=>{At(D,{variant:"default",class:"bg-blue-600 text-xs",children:(E,Ae)=>{g();var Oe=J("Auto-Apply");r(E,Oe)},$$slots:{default:!0}})};h(zt,D=>{var E;a().autoApplyEnabled&&((E=a().selectedJobIds)!=null&&E.includes(e(Y).id))&&D(Bt)})}s(G);var vr=t(G,2);{var Rt=D=>{var E=hs(),Ae=o(E);Ca(Ae,{class:"mr-1 h-3 w-3"});var Oe=t(Ae);s(E),$(()=>y(Oe,` ${e(Y).company??""}`)),r(D,E)};h(vr,D=>{e(Y).company&&D(Rt)})}s(_e);var jt=t(_e,2),fr=o(jt);{var mr=D=>{const E=O(()=>e(Z).has(e(Y).id));Or(D,{get checked(){return e(E)},onCheckedChange:Ae=>{m(Ae?e(Y).id:e(Y).id)}})};h(fr,D=>{a().autoApplyEnabled||D(mr)})}s(jt),s(x);var Vt=t(x,2),qt=o(Vt);{var Wt=D=>{At(D,{variant:"outline",class:"text-xs",children:(E,Ae)=>{g();var Oe=J();$(()=>y(Oe,`${e(Y).matchScore??""}% match`)),r(E,Oe)},$$slots:{default:!0}})};h(qt,D=>{e(Y).matchScore&&D(Wt)})}var Mt=t(qt,2);{var Zt=D=>{var E=xs(),Ae=o(E);Da(Ae,{class:"mr-1 h-3 w-3"});var Oe=t(Ae);s(E),$(()=>y(Oe,` ${e(Y).location??""}`)),r(D,E)};h(Mt,D=>{e(Y).location&&D(Zt)})}var er=t(Mt,2);{var pr=D=>{var E=ys();const Ae=O(()=>be(e(Y)));var Oe=o(E);Ia(Oe,{class:"mr-1 h-3 w-3"});var wt=t(Oe);s(E),$(()=>y(wt,` ${e(Ae)??""}`)),r(D,E)};h(er,D=>{be(e(Y))&&D(pr)})}var tr=t(er,2);{var rr=D=>{var E=$s(),Ae=o(E);Ta(Ae,{class:"mr-1 h-3 w-3"});var Oe=t(Ae);s(E),$(wt=>y(Oe,` Posted ${wt??""} ago`),[()=>wr(new Date(e(Y).postedAt))]),r(D,E)};h(tr,D=>{e(Y).postedAt&&D(rr)})}s(Vt);var ar=t(Vt,2),sr=o(ar);{var bt=D=>{var E=bs(),Ae=o(E);At(Ae,{variant:"outline",class:"text-xs",children:(Oe,wt)=>{g();var lt=J();$(()=>y(lt,e(Y).remoteType)),r(Oe,lt)},$$slots:{default:!0}}),s(E),r(D,E)};h(sr,D=>{e(Y).remoteType&&D(bt)})}var Nt=t(sr,2);{var Kt=D=>{var E=ws(),Ae=o(E);It(Ae,17,()=>e(Y).benefits.slice(0,2),Nr,(lt,kt)=>{At(lt,{variant:"secondary",class:"text-xs",children:(gr,Jt)=>{g();var Yt=J();$(()=>y(Yt,e(kt))),r(gr,Yt)},$$slots:{default:!0}})});var Oe=t(Ae,2);{var wt=lt=>{At(lt,{variant:"secondary",class:"text-xs",children:(kt,gr)=>{g();var Jt=J();$(()=>y(Jt,`+${e(Y).benefits.length-2} more`)),r(kt,Jt)},$$slots:{default:!0}})};h(Oe,lt=>{e(Y).benefits.length>2&&lt(wt)})}s(E),r(D,E)};h(Nt,D=>{e(Y).benefits&&e(Y).benefits.length>0&&D(Kt)})}s(ar),s(ht),$(()=>{Jr(te,"href",e(Y).applyLink),y(at,e(Y).title)}),r(Qe,ht)}),s($e),r(ge,$e)};h(de,ge=>{e(je).length===0?ge(Je):ge(rt,!1)},Ee)}};h(yt,de=>{e(q)?de($t):de(Et,!1)})}s(_t),s(k),$(()=>y(vt,`Jobs Found (${(e(q)?"...":e(je).length)??""})`)),r(R,k)},B=R=>{var k=Rs();r(R,k)};h(M,R=>{a()?R(ie):R(B,!1)})}r(Se,P)},$$slots:{default:!0}});var Be=t(ce,2);v(Be,()=>Ra,(Se,_)=>{_(Se,{class:"border-border m-0 grid grid-cols-4 flex-col-reverse gap-4 border-t p-2 sm:flex-row sm:justify-end",children:(P,M)=>{var ie=ks(),B=d(ie);Ge(B,{variant:"outline",onclick:u,children:(k,N)=>{g();var C=J("Close");r(k,C)},$$slots:{default:!0}});var R=t(B,2);Ge(R,{variant:"default",onclick:()=>{u(),a()&&a().id&&(window.location.href=`/dashboard/automation/${a().id}`)},children:(k,N)=>{g();var C=J("View Full Details");r(k,C)},$$slots:{default:!0}}),r(P,ie)},$$slots:{default:!0}})}),$(Se=>y(tt,`${Se??""}% Complete`),[()=>qe(a())]),r(Me,Pe)},$$slots:{default:!0}})}),r(se,A)},$$slots:{default:!0}})}),r(c,b)},$$slots:{default:!0}})});var Q=t(F,2);v(Q,()=>ya,(n,p)=>{p(n,{get open(){return e(st)},set open(c){j(st,c,!0)},children:(c,ae)=>{var b=Fe(),S=d(b);v(S,()=>ua,(L,f)=>{f(L,{children:(U,se)=>{var oe=Ns(),A=d(oe);v(A,()=>va,(z,K)=>{K(z,{})});var X=t(A,2);v(X,()=>fa,(z,K)=>{K(z,{class:"gap-0 p-0 sm:max-w-[425px]",children:(ne,Me)=>{var ut=Ms(),Pe=d(ut);v(Pe,()=>ma,(ze,Ye)=>{Ye(ze,{class:"border-border border-b p-4",children:(nt,tt)=>{var ce=Fe(),Be=d(ce);v(Be,()=>pa,(Se,_)=>{_(Se,{children:(P,M)=>{g();var ie=J("Confirm Auto-Apply");r(P,ie)},$$slots:{default:!0}})}),r(nt,ce)},$$slots:{default:!0}})});var Ke=t(Pe,2);v(Ke,()=>ga,(ze,Ye)=>{Ye(ze,{class:"p-4",children:(nt,tt)=>{g();var ce=Ts(),Be=d(ce);g(3),$(()=>y(Be,`Are you sure you want to enable auto-apply for ${e(Z).size??""} selected job${e(Z).size===1?"":"s"}? `)),r(nt,ce)},$$slots:{default:!0}})});var et=t(Ke,2);v(et,()=>_a,(ze,Ye)=>{Ye(ze,{class:"border-border flex justify-end gap-4 border-t p-2",children:(nt,tt)=>{var ce=js(),Be=d(ce);v(Be,()=>ha,(_,P)=>{P(_,{onclick:()=>j(st,!1),children:(M,ie)=>{g();var B=J("Cancel");r(M,B)},$$slots:{default:!0}})});var Se=t(Be,2);v(Se,()=>xa,(_,P)=>{P(_,{onclick:I,children:(M,ie)=>{var B=Es(),R=d(B);Sr(R,{class:"mr-2 h-4 w-4"}),g(),r(M,B)},$$slots:{default:!0}})}),r(nt,ce)},$$slots:{default:!0}})}),r(ne,ut)},$$slots:{default:!0}})}),r(U,oe)},$$slots:{default:!0}})}),r(c,b)},$$slots:{default:!0}})}),r(w,T),Lt(),Le()}var Fs=i('<div class="mb-4 rounded border border-yellow-500 bg-yellow-50 p-2 text-xs"><strong>FeatureGuard Debug:</strong> <pre> </pre></div>'),Ls=i('<div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8 text-center"><div class="bg-muted mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full"><!></div> <h3 class="mb-2 text-lg font-medium"><!></h3> <p class="text-muted-foreground mb-4 max-w-md"> </p> <!></div>'),Us=i("<!> <!>",1);function xr(w,l){Ft(l,!1);const re=Dt(),Le=Dt(),a=Dt(),ve=Dt(),he=Dt(),Re=Dt(),ke=Dt();let fe=pt(l,"userData",8),ee=pt(l,"featureId",8),Z=pt(l,"limitId",8,void 0),st=pt(l,"showUpgradePrompt",8,!0),ot=pt(l,"fallbackMessage",8,void 0),q=pt(l,"debugMode",8,!1);function xe(){var I;Ja({section:"pro",currentPlanId:fe().role||((I=fe().subscription)==null?void 0:I.planId)||"free"})}Ct(()=>ft(ee()),()=>{j(re,Oa(ee()))}),Ct(()=>ft(ee()),()=>{j(Le,Fa(ee()))}),Ct(()=>ft(ee()),()=>{j(a,La(ee()))}),Ct(()=>(e(re),ft(fe())),()=>{j(ve,e(re)?Na(fe()):null)}),Ct(()=>(e(re),e(Le),e(ve),ft(Z()),ft(ee())),()=>{j(he,e(re)?e(Le)?!0:e(ve)?Z()?e(ve).canPerformAction(ee(),Z()):e(ve).hasAccess(ee()):!1:!1)}),Ct(()=>(e(re),ft(ot()),e(a),ft(ee()),e(Le),e(ve),ft(Z())),()=>{j(Re,(()=>{var I;return e(re)?e(Le)?"":e(ve)?Z()?e(ve).getBlockReason(ee(),Z()):e(ve).getBlockReason(ee()):"Unable to check feature access.":ot()||`The ${((I=e(a))==null?void 0:I.description)||ee()} feature is currently disabled.`})())}),Ct(()=>(ft(q()),ft(ee()),e(re),e(Le),e(he),e(Re),ft(Z()),ft(fe())),()=>{var I;j(ke,q()?{featureId:ee(),featureEnabled:e(re),bypassLimits:e(Le),canAccess:e(he),blockReason:e(Re),limitId:Z(),userRole:fe().role,planId:(I=fe().subscription)==null?void 0:I.planId}:null)}),Zr(),Ma();var Ce=Us(),De=d(Ce);{var be=I=>{var me=Fs(),pe=t(o(me),2),Ve=o(pe,!0);s(pe),s(me),$(Ze=>y(Ve,Ze),[()=>JSON.stringify(e(ke),null,2)],ea),r(I,me)};h(De,I=>{q()&&e(ke)&&I(be)})}var je=t(De,2);{var m=I=>{var me=Fe(),pe=d(me);ja(pe,l,"default",{},null),r(I,me)},we=I=>{var me=Ls(),pe=o(me),Ve=o(pe);{var Ze=c=>{$r(c,{class:"text-warning h-6 w-6"})},ct=c=>{Ua(c,{class:"text-muted-foreground h-6 w-6"})};h(Ve,c=>{e(re)?c(ct,!1):c(Ze)})}s(pe);var qe=t(pe,2),Ue=o(qe);{var u=c=>{var ae=J("Feature Unavailable");r(c,ae)},T=c=>{var ae=J("Access Restricted");r(c,ae)};h(Ue,c=>{e(re)?c(T,!1):c(u)})}s(qe);var F=t(qe,2),Q=o(F,!0);s(F);var n=t(F,2);{var p=c=>{Ge(c,{variant:"outline",onclick:xe,children:(ae,b)=>{g();var S=J("Upgrade Plan");r(ae,S)},$$slots:{default:!0}})};h(n,c=>{st()&&e(re)&&c(p)})}s(me),$(()=>y(Q,e(Re))),r(I,me)};h(je,I=>{e(he)?I(m):I(we,!1)})}r(w,Ce),Lt()}function Qt(w){if(!w)return{};try{return w.ProfileData?{fullName:w.ProfileData.fullName,email:w.ProfileData.email,phone:w.ProfileData.phone,location:w.ProfileData.address,summary:w.ProfileData.summary,skills:w.ProfileData.skills?typeof w.ProfileData.skills=="string"?JSON.parse(w.ProfileData.skills):w.ProfileData.skills:void 0}:w.data?typeof w.data=="string"?JSON.parse(w.data):w.data.data&&typeof w.data.data=="string"?JSON.parse(w.data.data):w.data:{}}catch(l){return console.error("Error parsing profile data:",l),{}}}var zs=i("<!> <!>",1),Bs=i("<!> New Run",1),Vs=i("<!> New Automation Run",1),qs=i('<div class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center"><!> <h3 class="text-xl font-semibold text-gray-300">No automation runs yet</h3> <p class="mt-2 text-gray-400">Create your first automation run to start searching for jobs</p> <!></div>'),Ks=i('<div class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center"><!> <h3 class="text-xl font-semibold text-gray-300">No runs match your filters</h3> <p class="mt-2 text-gray-400">Try adjusting your search or filter criteria</p></div>'),Ys=i("<!> ",1),Gs=i('<div class="flex items-center justify-between"><!> <!></div> <!>',1),Hs=i('<span class="text-xs text-gray-400">(in progress)</span>'),Qs=i('<div class="flex flex-row justify-between text-xs"><div class="text-primary/50">Progress</div> <div class="text-primary/50 text-right"> </div></div> <div class="grid grid-cols-2 gap-4 text-sm"><div><div class="font-medium text-gray-400">Keywords</div> <div class="truncate"><!></div></div> <div><div class="font-medium text-gray-400">Location</div> <div class="truncate"><!></div></div></div> <div class="flex flex-col"><div class="font-medium text-gray-400">Jobs Found</div> <div class="flex items-center gap-2"><span class="text-lg font-semibold"> </span> <!></div></div>',1),Xs=i("<!> <!>",1),Ws=i("<!> <!> <!> <!>",1),Zs=i('<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"></div>'),eo=i('<div class="border-border flex flex-wrap items-center justify-between gap-4 border-b p-2"><div class="flex flex-wrap items-center gap-2"><div class="relative flex items-center gap-2"><!> <!></div> <!></div> <!></div> <div class="p-2"><!></div>',1),to=i("<!> <!>",1),ro=i("<!> <!>",1),ao=i('<!> <span class=" text-green-700">Profile Eligible for Automation</span>',1),so=i('<!> <span class="text-orange-700">Profile Needs Completion</span>',1),oo=i('<div class="flex items-center gap-2 text-sm text-gray-600"><!> </div>'),no=i('<div class="space-y-1"><p class="text-sm font-medium text-gray-700">Missing Requirements:</p> <!></div>'),io=i('<div class="rounded-lg border p-4"><div class="mb-2 flex items-center gap-2 text-sm"><!></div> <div class="mb-3"><div class="mb-1 flex items-center justify-between text-sm"><span>Profile Completion</span> <span> </span></div> <!></div> <!></div>'),lo=i("<!> Use Profile Suggestions",1),co=i('<p class="text-muted-foreground text-xs"> </p>'),uo=i('<p class="text-muted-foreground text-xs"> </p>'),vo=i('Maximum Jobs to Apply: <span class="text-primary font-semibold"> </span>',1),fo=i('Minimum Match Score: <span class="text-primary font-semibold"> </span>',1),mo=i('Salary Range: <span class="text-primary font-semibold"> </span>',1),po=i('Experience Range: <span class="text-primary font-normal"> </span>',1),go=i('<div class="space-y-6"><div class="mb-4 flex items-center justify-between"><h4 class="text-md font-light">Search Criteria</h4> <!></div> <div class="grid gap-6 md:grid-cols-2"><div class="flex flex-col space-y-2"><label for="keywords" class="text-sm font-normal">Job Keywords *</label> <!> <!></div> <div class="flex flex-col space-y-2"><label for="location" class="text-sm font-normal">Locations</label> <!> <!></div></div> <h4 class="text-md mb-4 font-light">Automation Settings</h4> <div class="grid gap-6 md:grid-cols-2"><div class="space-y-6"><div class="space-y-3"><!> <!> <div class="text-muted-foreground flex justify-between text-xs"><span>1 job</span> <span>50 jobs</span></div></div> <div class="space-y-3"><!> <!> <div class="text-muted-foreground flex justify-between text-xs"><span>60%</span> <span>95%</span></div></div></div> <div class="space-y-6"><div class="space-y-3"><!> <!> <div class="text-muted-foreground flex justify-between text-xs"><span>$30k</span> <span>$250k+</span></div></div> <div class="space-y-3"><!> <!> <div class="text-muted-foreground flex justify-between text-xs"><span>0 years</span> <span>15+ years</span></div></div></div></div> <div class="bg-muted/50 mt-6 flex items-center justify-between rounded-lg border p-4"><div><!> <p class="text-muted-foreground text-xs">Automatically apply to jobs that match your criteria</p></div> <!></div></div>'),_o=i("<!> <!>",1),ho=i('<form method="POST"><input type="hidden" name="profileId"/> <input type="hidden" name="keywords"/> <input type="hidden" name="locations"/> <input type="hidden" name="maxJobsToApply"/> <input type="hidden" name="minMatchScore"/> <input type="hidden" name="autoApplyEnabled"/> <input type="hidden" name="salaryRange"/> <input type="hidden" name="experienceRange"/> <input type="hidden" name="jobTypes"/> <input type="hidden" name="remotePreference"/> <input type="hidden" name="companySizePreference"/> <input type="hidden" name="excludeCompanies"/> <input type="hidden" name="preferredCompanies"/> <div class="mb-0 grid gap-4 p-4"><div class="grid gap-1"><div class="flex items-center justify-between"><label for="profile" class="text-sm font-medium">Profile *</label> <!></div> <!></div> <!> <!></div> <!></form>'),xo=i("<!> <!>",1),yo=i("<!> <!>",1),$o=i("<!> <!>",1);function bo(w,l){Ft(l,!0);const[re,Le]=yr(),a=()=>Xt(l.form,"$form",re),ve=()=>Xt(l.submitting,"$submitting",re);let he=dt("all"),Re=dt(""),ke=dt(!1);const fe=[{value:"all",label:"All Status"},{value:"pending",label:"Pending"},{value:"start",label:"Starting"},{value:"in progress",label:"In Progress"},{value:"running",label:"Running"},{value:"completed",label:"Completed"},{value:"failed",label:"Failed"},{value:"stopped",label:"Stopped"}],ee=O(()=>()=>fe.find(m=>m.value===e(he))||fe[0]),Z=O(()=>()=>l.automationRuns.filter(m=>{if(e(he)!=="all"&&m.status!==e(he))return!1;if(e(Re).trim()){const we=e(Re).toLowerCase(),I=m.profile&&Qt(m.profile).fullName||"",me=m.keywords||"",pe=m.location||"";return I.toLowerCase().includes(we)||me.toLowerCase().includes(we)||pe.toLowerCase().includes(we)}return!0}));function st(m){if(!m)return"";const we=typeof m=="string"?new Date(m):m;return Sa(we,new Date,{addSuffix:!0})}function ot(m){switch(m){case"completed":return"default";case"start":case"running":return"secondary";case"failed":return"destructive";case"stopped":return"outline";case"in progress":case"pending":return"outline";default:return"outline"}}function q(m){switch(m){case"completed":return cr;case"start":case"running":return Ot;case"failed":return Br;case"stopped":return hr;case"in progress":case"pending":return dr;default:return dr}}function xe(m){return m.status==="completed"?100:m.status==="failed"||m.status==="stopped"?m.progress||0:m.status==="start"?5:m.status==="in progress"||m.status==="running"?m.progress||50:m.progress||0}function Ce(m){switch(m){case"start":return"Starting";case"in progress":return"In Progress";case"running":return"Running";case"completed":return"Completed";case"failed":return"Failed";case"stopped":return"Stopped";case"pending":return"Pending";default:return m.charAt(0).toUpperCase()+m.slice(1)}}var De=$o(),be=d(De);xr(be,{get userData(){return l.userData},featureId:"automation",limitId:"automation_runs_per_month",showUpgradePrompt:!0,fallbackMessage:"Automation features are not available in your current plan",children:(m,we)=>{var I=eo(),me=d(I),pe=o(me),Ve=o(pe),Ze=o(Ve);Ht(Ze,{class:"text-muted-foreground absolute left-2.5 top-3 h-4 w-4"});var ct=t(Ze,2);Vr(ct,{placeholder:"Search runs...",class:"h-9 w-[200px] pl-9",get value(){return e(Re)},set value(n){j(Re,n,!0)}}),s(Ve);var qe=t(Ve,2);v(qe,()=>Ar,(n,p)=>{p(n,{type:"single",get value(){return e(he)},onValueChange:c=>{j(he,c||"all",!0)},children:(c,ae)=>{var b=zs(),S=d(b);v(S,()=>Rr,(f,U)=>{U(f,{class:"w-[140px] p-2",children:(se,oe)=>{var A=Fe(),X=d(A);v(X,()=>Tr,(z,K)=>{K(z,{get placeholder(){return e(ee)().label}})}),r(se,A)},$$slots:{default:!0}})});var L=t(S,2);v(L,()=>kr,(f,U)=>{U(f,{class:"w-[140px]",children:(se,oe)=>{var A=Fe(),X=d(A);It(X,17,()=>fe,z=>z.value,(z,K)=>{var ne=Fe(),Me=d(ne);v(Me,()=>Cr,(ut,Pe)=>{Pe(ut,{get value(){return e(K).value},children:(Ke,et)=>{g();var ze=J();$(()=>y(ze,e(K).label)),r(Ke,ze)},$$slots:{default:!0}})}),r(z,ne)}),r(se,A)},$$slots:{default:!0}})}),r(c,b)},$$slots:{default:!0}})}),s(pe);var Ue=t(pe,2);Ge(Ue,{size:"default",onclick:()=>j(ke,!0),children:(n,p)=>{var c=Bs(),ae=d(c);Ot(ae,{class:"mr-1 h-3 w-3"}),g(),r(n,c)},$$slots:{default:!0}}),s(me);var u=t(me,2),T=o(u);{var F=n=>{var p=qs(),c=o(p);Ht(c,{class:"mb-4 h-12 w-12 text-gray-400"});var ae=t(c,6);Ge(ae,{variant:"default",get onclick(){return l.onCreateRun},class:"mt-4",children:(b,S)=>{var L=Vs(),f=d(L);Ot(f,{class:"mr-2 h-4 w-4"}),g(),r(b,L)},$$slots:{default:!0}}),s(p),r(n,p)},Q=(n,p)=>{{var c=b=>{var S=Ks(),L=o(S);Ht(L,{class:"mb-4 h-12 w-12 text-gray-400"}),g(4),s(S),r(b,S)},ae=b=>{var S=Zs();It(S,21,()=>e(Z)(),L=>L.id,(L,f)=>{var U=Fe(),se=d(U);v(se,()=>qr,(oe,A)=>{A(oe,{class:"gap-0 overflow-hidden p-0",children:(X,z)=>{var K=Ws(),ne=d(K);v(ne,()=>Hr,(et,ze)=>{ze(et,{class:"border-border border-b !p-4",children:(Ye,nt)=>{var tt=Gs(),ce=d(tt),Be=o(ce);v(Be,()=>Qr,(M,ie)=>{ie(M,{children:(B,R)=>{var k=Fe(),N=d(k);{var C=V=>{var W=J();$(Ie=>y(W,Ie),[()=>Qt(e(f).profile).fullName||"Unnamed Profile"]),r(V,W)},le=V=>{var W=J("Automation Run");r(V,W)};h(N,V=>{e(f).profile?V(C):V(le,!1)})}r(B,k)},$$slots:{default:!0}})});var Se=t(Be,2);const _=O(()=>ot(e(f).status));At(Se,{get variant(){return e(_)},class:"ml-2",children:(M,ie)=>{var B=Ys(),R=d(B);{var k=C=>{var le=Fe();const V=O(()=>q(e(f).status));var W=d(le);v(W,()=>e(V),(Ie,Te)=>{Te(Ie,{class:"mr-1 h-3 w-3"})}),r(C,le)};h(R,C=>{q(e(f).status)&&C(k)})}var N=t(R);$(C=>y(N,` ${C??""}`),[()=>Ce(e(f).status)]),r(M,B)},$$slots:{default:!0}}),s(ce);var P=t(ce,2);v(P,()=>Yr,(M,ie)=>{ie(M,{children:(B,R)=>{var k=Fe(),N=d(k);{var C=le=>{var V=J();$(W=>y(V,`Started ${W??""} ago`),[()=>st(new Date(e(f).createdAt))]),r(le,V)};h(N,le=>{e(f).createdAt&&le(C)})}r(B,k)},$$slots:{default:!0}})}),r(Ye,tt)},$$slots:{default:!0}})});var Me=t(ne,2);const ut=O(()=>xe(e(f)));lr(Me,{get value(){return e(ut)},max:100,class:"rounded-none"});var Pe=t(Me,2);v(Pe,()=>Kr,(et,ze)=>{ze(et,{class:"flex flex-col gap-4 p-4 pt-3",children:(Ye,nt)=>{var tt=Qs(),ce=d(tt),Be=t(o(ce),2),Se=o(Be);s(Be),s(ce);var _=t(ce,2),P=o(_),M=t(o(P),2),ie=o(M);const B=O(()=>e(f).keywords||"");Lr(ie,{get keywordIds(){return e(B)},fallback:"None"}),s(M),s(P);var R=t(P,2),k=t(o(R),2),N=o(k);const C=O(()=>e(f).location||"");Ur(N,{get locationIds(){return e(C)},fallback:"None"}),s(k),s(R),s(_);var le=t(_,2),V=t(o(le),2),W=o(V),Ie=o(W,!0);s(W);var Te=t(W,2);{var ue=ye=>{var it=Hs();r(ye,it)};h(Te,ye=>{["running","pending","start","in progress"].includes(e(f).status)&&ye(ue)})}s(V),s(le),$(ye=>{var it;y(Se,`${ye??""}% Complete`),y(Ie,((it=e(f).matchedJobIds)==null?void 0:it.length)||e(f).jobsFound||0)},[()=>xe(e(f))]),r(Ye,tt)},$$slots:{default:!0}})});var Ke=t(Pe,2);v(Ke,()=>Gr,(et,ze)=>{ze(et,{class:"grid grid-cols-2 gap-4 border-t !p-2",children:(Ye,nt)=>{var tt=Xs(),ce=d(tt);Ge(ce,{variant:"outline",size:"sm",onclick:()=>l.onRunSelect(e(f)),children:(Se,_)=>{g();var P=J("View Details");r(Se,P)},$$slots:{default:!0}});var Be=t(ce,2);Ge(Be,{variant:"outline",size:"sm",onclick:()=>Ir(`/dashboard/automation/${e(f).id}`),children:(Se,_)=>{g();var P=J("Full View");r(Se,P)},$$slots:{default:!0}}),r(Ye,tt)},$$slots:{default:!0}})}),r(X,K)},$$slots:{default:!0}})}),r(L,U)}),s(S),r(b,S)};h(n,b=>{e(Z)().length===0?b(c):b(ae,!1)},p)}};h(T,n=>{l.automationRuns.length===0?n(F):n(Q,!1)})}s(u),r(m,I)},$$slots:{default:!0}});var je=t(be,2);v(je,()=>za,(m,we)=>{we(m,{get open(){return e(ke)},set open(I){j(ke,I,!0)},children:(I,me)=>{var pe=yo(),Ve=d(pe);v(Ve,()=>Ba,(ct,qe)=>{qe(ct,{})});var Ze=t(Ve,2);v(Ze,()=>Va,(ct,qe)=>{qe(ct,{class:"max-h-[90vh] max-w-4xl gap-0 p-0",children:(Ue,u)=>{xr(Ue,{get userData(){return l.userData},featureId:"automation",limitId:"automation_runs_per_month",showUpgradePrompt:!0,fallbackMessage:"Automation features are not available in your current plan",children:(T,F)=>{var Q=xo(),n=d(Q);v(n,()=>qa,(c,ae)=>{ae(c,{class:"border-border gap-1 border-b p-4",children:(b,S)=>{var L=to(),f=d(L);v(f,()=>Ka,(se,oe)=>{oe(se,{children:(A,X)=>{g();var z=J("Configure Automation Run");r(A,z)},$$slots:{default:!0}})});var U=t(f,2);v(U,()=>Ya,(se,oe)=>{oe(se,{children:(A,X)=>{g();var z=J("Set up detailed automation specifications for intelligent job matching and application.");r(A,z)},$$slots:{default:!0}})}),r(b,L)},$$slots:{default:!0}})});var p=t(n,2);Fr(p,{orientation:"vertical",class:"max-h-[calc(100vh-200px)] overflow-hidden",children:(c,ae)=>{var b=ho(),S=o(b);mt(S);var L=t(S,2);mt(L);var f=t(L,2);mt(f);var U=t(f,2);mt(U);var se=t(U,2);mt(se);var oe=t(se,2);mt(oe);var A=t(oe,2);mt(A);var X=t(A,2);mt(X);var z=t(X,2);mt(z);var K=t(z,2);mt(K);var ne=t(K,2);mt(ne);var Me=t(ne,2);mt(Me);var ut=t(Me,2);mt(ut);var Pe=t(ut,2),Ke=o(Pe),et=o(Ke),ze=t(o(et),2);Ge(ze,{variant:"link",size:"sm",onclick:()=>Ir("/dashboard/settings/profile"),children:(_,P)=>{g();var M=J("Manage Profiles");r(_,M)},$$slots:{default:!0}}),s(et);var Ye=t(et,2);v(Ye,()=>Ar,(_,P)=>{P(_,{type:"single",get value(){return a().profileId},onValueChange:M=>{Xe(l.form,H(a).profileId=M||"",H(a))},children:(M,ie)=>{var B=ro(),R=d(B);v(R,()=>Rr,(N,C)=>{C(N,{class:"w-full p-2",children:(le,V)=>{var W=Fe(),Ie=d(W);const Te=O(()=>{var ue;return((ue=l.profiles.find(ye=>ye.id===a().profileId))==null?void 0:ue.name)||"Select a profile"});v(Ie,()=>Tr,(ue,ye)=>{ye(ue,{get placeholder(){return e(Te)}})}),r(le,W)},$$slots:{default:!0}})});var k=t(R,2);v(k,()=>kr,(N,C)=>{C(N,{class:"max-h-60",children:(le,V)=>{var W=Fe(),Ie=d(W);It(Ie,17,()=>l.profiles,Te=>Te.id,(Te,ue)=>{var ye=Fe(),it=d(ye);v(it,()=>Cr,(gt,_t)=>{_t(gt,{get value(){return e(ue).id},children:(Ne,He)=>{g();var vt=J();$(()=>y(vt,e(ue).name)),r(Ne,vt)},$$slots:{default:!0}})}),r(Te,ye)}),r(le,W)},$$slots:{default:!0}})}),r(M,B)},$$slots:{default:!0}})}),s(Ke);var nt=t(Ke,2);{var tt=_=>{var P=Fe();const M=O(()=>l.profiles.find(R=>R.id===a().profileId));var ie=d(P);{var B=R=>{var k=io();const N=O(()=>l.checkAutomationEligibility(e(M)));var C=o(k),le=o(C);{var V=Ne=>{var He=ao(),vt=d(He);cr(vt,{class:"h-4 w-4 text-green-500"}),g(2),r(Ne,He)},W=Ne=>{var He=so(),vt=d(He);$r(vt,{class:"h-4 w-4 text-orange-500"}),g(2),r(Ne,He)};h(le,Ne=>{e(N).isEligible?Ne(V):Ne(W,!1)})}s(C);var Ie=t(C,2),Te=o(Ie),ue=t(o(Te),2),ye=o(ue);s(ue),s(Te);var it=t(Te,2);lr(it,{get value(){return e(N).completionPercentage},max:100}),s(Ie);var gt=t(Ie,2);{var _t=Ne=>{var He=no(),vt=t(o(He),2);It(vt,17,()=>e(N).missingRequirements,Nr,(Tt,Ut)=>{var yt=oo(),$t=o(yt);Ha($t,{class:"h-3 w-3 text-red-500"});var Et=t($t);s(yt),$(()=>y(Et,` ${e(Ut)??""}`)),r(Tt,yt)}),s(He),r(Ne,He)};h(gt,Ne=>{e(N).isEligible||Ne(_t)})}s(k),$(()=>y(ye,`${e(N).completionPercentage??""}%`)),r(R,k)};h(ie,R=>{e(M)&&R(B)})}r(_,P)};h(nt,_=>{a().profileId&&_(tt)})}var ce=t(nt,2);{var Be=_=>{var P=go(),M=o(P),ie=t(o(M),2);{var B=x=>{Ge(x,{variant:"outline",size:"sm",get onclick(){return l.applySuggestions},class:"text-xs",children:(_e,G)=>{var te=lo(),at=d(te);Xr(at,{class:"mr-1 h-3 w-3"}),g(),r(_e,te)},$$slots:{default:!0}})};h(ie,x=>{l.profileSuggestions()&&l.profileSuggestions().jobTitles.length>0&&x(B)})}s(M);var R=t(M,2),k=o(R),N=t(o(k),2);const C=O(()=>l.occupationOptions());Dr(N,{placeholder:"Search for occupations...",get selectedValues(){return a().keywords},get options(){return e(C)},onSelectedValuesChange:x=>Xe(l.form,H(a).keywords=x,H(a)),get searchOptions(){return l.searchOccupations},maxDisplayItems:1,width:"w-55"});var le=t(N,2);{var V=x=>{var _e=co(),G=o(_e);s(_e),$(te=>y(G,`Suggestions: ${te??""}`),[()=>l.profileSuggestions().jobTitles.join(", ")]),r(x,_e)};h(le,x=>{l.profileSuggestions()&&l.profileSuggestions().jobTitles.length>0&&x(V)})}s(k);var W=t(k,2),Ie=t(o(W),2);const Te=O(()=>l.locationOptions());Dr(Ie,{placeholder:"Search for cities...",get selectedValues(){return a().locations},get options(){return e(Te)},onSelectedValuesChange:x=>Xe(l.form,H(a).locations=x,H(a)),get searchOptions(){return l.searchLocations},maxDisplayItems:1,width:"w-55"});var ue=t(Ie,2);{var ye=x=>{var _e=uo(),G=o(_e);s(_e),$(te=>y(G,`From profile: ${te??""}`),[()=>l.profileSuggestions().location]),r(x,_e)};h(ue,x=>{l.profileSuggestions()&&l.profileSuggestions().location&&x(ye)})}s(W),s(R);var it=t(R,4),gt=o(it),_t=o(gt),Ne=o(_t);Gt(Ne,{class:"text-xs font-normal",children:(x,_e)=>{g();var G=vo(),te=t(d(G)),at=o(te,!0);s(te),$(()=>y(at,a().maxJobsToApply)),r(x,G)},$$slots:{default:!0}});var He=t(Ne,2);nr(He,{type:"single",min:1,max:50,step:1,class:"w-full",get value(){return a().maxJobsToApply},set value(x){Xe(l.form,H(a).maxJobsToApply=x,H(a))}}),g(2),s(_t);var vt=t(_t,2),Tt=o(vt);Gt(Tt,{class:"text-xs font-normal",children:(x,_e)=>{g();var G=fo(),te=t(d(G)),at=o(te);s(te),$(()=>y(at,`${a().minMatchScore??""}%`)),r(x,G)},$$slots:{default:!0}});var Ut=t(Tt,2);nr(Ut,{type:"single",min:60,max:95,step:5,class:"w-full",get value(){return a().minMatchScore},set value(x){Xe(l.form,H(a).minMatchScore=x,H(a))}}),g(2),s(vt),s(gt);var yt=t(gt,2),$t=o(yt),Et=o($t);Gt(Et,{class:"text-xs font-normal",children:(x,_e)=>{g();var G=mo(),te=t(d(G)),at=o(te);s(te),$(()=>y(at,`$${a().salaryRange[0]??""}k - $${a().salaryRange[1]??""}k`)),r(x,G)},$$slots:{default:!0}});var de=t(Et,2);nr(de,{type:"multiple",min:30,max:250,step:5,class:"w-full",get value(){return a().salaryRange},set value(x){Xe(l.form,H(a).salaryRange=x,H(a))}}),g(2),s($t);var Ee=t($t,2),Je=o(Ee);Gt(Je,{class:"text-xs font-medium",children:(x,_e)=>{g();var G=po(),te=t(d(G)),at=o(te);s(te),$(()=>y(at,`${a().experienceRange[0]??""} - ${a().experienceRange[1]??""} years`)),r(x,G)},$$slots:{default:!0}});var rt=t(Je,2);nr(rt,{type:"multiple",min:0,max:15,step:1,class:"w-full",get value(){return a().experienceRange},set value(x){Xe(l.form,H(a).experienceRange=x,H(a))}}),g(2),s(Ee),s(yt),s(it);var ge=t(it,2),$e=o(ge),Qe=o($e);Gt(Qe,{for:"auto-apply",class:"text-sm font-medium",children:(x,_e)=>{g();var G=J("Enable Automatic Applications");r(x,G)},$$slots:{default:!0}}),g(2),s($e);var Y=t($e,2);const ht=O(()=>!!a().autoApplyEnabled);Or(Y,{id:"auto-apply",get checked(){return e(ht)},onCheckedChange:x=>{Xe(l.form,H(a).autoApplyEnabled=x,H(a))}}),s(ge),s(P),r(_,P)};h(ce,_=>{a().profileId&&l.isProfileEligible()&&_(Be)})}s(Pe);var Se=t(Pe,2);v(Se,()=>Ga,(_,P)=>{P(_,{class:"border-border grid grid-cols-3 gap-4 border-t p-2",children:(M,ie)=>{var B=_o(),R=d(B);Ge(R,{variant:"outline",onclick:()=>j(ke,!1),children:(C,le)=>{g();var V=J("Cancel");r(C,V)},$$slots:{default:!0}});var k=t(R,2);const N=O(()=>!l.isFormValid()||ve());Ge(k,{type:"submit",variant:"default",get disabled(){return e(N)},children:(C,le)=>{var V=Fe(),W=d(V);{var Ie=ue=>{var ye=J("Creating...");r(ue,ye)},Te=ue=>{var ye=J("Start Automation");r(ue,ye)};h(W,ue=>{ve()?ue(Ie):ue(Te,!1)})}r(C,V)},$$slots:{default:!0}}),r(M,B)},$$slots:{default:!0}})}),s(b),Ea(b,_=>{var P;return(P=l.enhance)==null?void 0:P.call(l,_)}),$((_,P,M,ie,B,R,k,N)=>{xt(L,_),xt(f,P),xt(oe,a().autoApplyEnabled),xt(A,M),xt(X,ie),xt(z,B),xt(ne,R),xt(Me,k),xt(ut,N)},[()=>JSON.stringify(a().keywords),()=>JSON.stringify(a().locations),()=>JSON.stringify(a().salaryRange),()=>JSON.stringify(a().experienceRange),()=>JSON.stringify(a().jobTypes),()=>JSON.stringify(a().companySizePreference),()=>JSON.stringify(a().excludeCompanies),()=>JSON.stringify(a().preferredCompanies)]),or(S,()=>a().profileId,_=>Xe(l.form,H(a).profileId=_,H(a))),or(U,()=>a().maxJobsToApply,_=>Xe(l.form,H(a).maxJobsToApply=_,H(a))),or(se,()=>a().minMatchScore,_=>Xe(l.form,H(a).minMatchScore=_,H(a))),or(K,()=>a().remotePreference,_=>Xe(l.form,H(a).remotePreference=_,H(a))),r(c,b)},$$slots:{default:!0}}),r(T,Q)},$$slots:{default:!0}})},$$slots:{default:!0}})}),r(I,pe)},$$slots:{default:!0}})}),r(w,De),Lt(),Le()}var wo=i("<!> Manage Profiles",1),Po=i('<div class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center"><!> <h3 class="text-xl font-semibold text-gray-300">No profiles available</h3> <p class="mt-2 text-gray-400">Create a profile in Settings to start using automation</p> <!></div>'),So=i('<div class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center"><!> <h3 class="text-xl font-semibold text-gray-300">No profiles match your search</h3> <p class="mt-2 text-gray-400">Try adjusting your search criteria</p></div>'),Ao=i('<a class="hover:underline"> </a>'),Ro=i("<!> <!>",1),ko=i('<!> <span class="text-sm font-medium text-green-700">Automation Ready</span>',1),Co=i('<!> <span class="text-sm font-medium text-orange-700">Needs Completion</span>',1),Do=i('<div class="mt-2 flex flex-row items-center gap-4"><!></div>'),Io=i("<!> <!>",1),To=i('<!> <!> <div class="flex items-center justify-between px-4 py-2 text-xs"><span class="text-primary/50">Profile Completion</span> <span class="text-primary/50"> </span></div> <!> <!>',1),Eo=i('<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"></div>'),jo=i('<div class="border-border flex flex-wrap items-center justify-between gap-4 border-b p-2"><div class="relative flex items-center gap-2"><!> <!></div> <!></div> <div class="p-2"><!></div>',1);function Mo(w,l){Ft(l,!0);let re=dt("");const Le=O(()=>()=>l.profiles.filter(q=>{if(e(re).trim()){const xe=e(re).toLowerCase(),Ce=Qt(q),De=Ce.fullName||q.name||"",be=Ce.title||"";return De.toLowerCase().includes(xe)||be.toLowerCase().includes(xe)}return!0}));var a=jo(),ve=d(a),he=o(ve),Re=o(he);Ht(Re,{class:"text-muted-foreground absolute left-2.5 top-3 h-4 w-4"});var ke=t(Re,2);Vr(ke,{placeholder:"Search profiles...",class:"h-9 w-[200px] pl-9",get value(){return e(re)},set value(q){j(re,q,!0)}}),s(he);var fe=t(he,2);Ge(fe,{variant:"default",onclick:()=>window.location.href="/dashboard/settings/profile",children:(q,xe)=>{var Ce=wo(),De=d(Ce);Xr(De,{class:"mr-1 h-4 w-4"}),g(),r(q,Ce)},$$slots:{default:!0}}),s(ve);var ee=t(ve,2),Z=o(ee);{var st=q=>{var xe=Po(),Ce=o(xe);zr(Ce,{class:"mb-4 h-12 w-12 text-gray-400"});var De=t(Ce,6);Ge(De,{variant:"default",onclick:()=>window.location.href="/dashboard/settings/profile",class:"mt-4",children:(be,je)=>{g();var m=J("Go to Profile Settings");r(be,m)},$$slots:{default:!0}}),s(xe),r(q,xe)},ot=(q,xe)=>{{var Ce=be=>{var je=So(),m=o(je);Ht(m,{class:"mb-4 h-12 w-12 text-gray-400"}),g(4),s(je),r(be,je)},De=be=>{var je=Eo();It(je,21,()=>e(Le)(),m=>m.id,(m,we)=>{var I=Fe(),me=d(I);v(me,()=>qr,(pe,Ve)=>{Ve(pe,{class:"gap-0 p-0",children:(Ze,ct)=>{var qe=To();const Ue=O(()=>ur(e(we)));var u=d(qe);v(u,()=>Hr,(ae,b)=>{b(ae,{class:"border-border flex flex-col gap-1 border-b !p-4",children:(S,L)=>{var f=Ro(),U=d(f);v(U,()=>Qr,(oe,A)=>{A(oe,{children:(X,z)=>{var K=Ao(),ne=o(K,!0);s(K),$(Me=>{Jr(K,"href",`/dashboard/automation/profile/${e(we).id}`),y(ne,Me)},[()=>Qt(e(we)).fullName||"Unnamed Profile"]),r(X,K)},$$slots:{default:!0}})});var se=t(U,2);v(se,()=>Yr,(oe,A)=>{A(oe,{children:(X,z)=>{g();var K=J();$(ne=>y(K,ne),[()=>Qt(e(we)).title||"No title specified"]),r(X,K)},$$slots:{default:!0}})}),r(S,f)},$$slots:{default:!0}})});var T=t(u,2);lr(T,{get value(){return e(Ue).completionPercentage},max:100,class:"h-2 rounded-none"});var F=t(T,2),Q=t(o(F),2),n=o(Q);s(Q),s(F);var p=t(F,2);v(p,()=>Kr,(ae,b)=>{b(ae,{class:"p-4 pt-0",children:(S,L)=>{var f=Do(),U=o(f);{var se=A=>{var X=ko(),z=d(X);cr(z,{class:"h-4 w-4 text-green-500"}),g(2),r(A,X)},oe=A=>{var X=Co(),z=d(X);$r(z,{class:"h-4 w-4 text-orange-500"}),g(2),r(A,X)};h(U,A=>{e(Ue).isEligible?A(se):A(oe,!1)})}s(f),r(S,f)},$$slots:{default:!0}})});var c=t(p,2);v(c,()=>Gr,(ae,b)=>{b(ae,{class:"border-border border-t !p-2",children:(S,L)=>{xr(S,{get userData(){return l.userData},featureId:"automation",limitId:"automation_runs_per_month",showUpgradePrompt:!0,fallbackMessage:"Automation features are not available in your current plan",children:(f,U)=>{const se=O(()=>ur(e(we))),oe=O(()=>!e(se).isEligible);Ge(f,{variant:"outline",class:"flex w-full flex-row gap-2",get disabled(){return e(oe)},onclick:()=>l.onProfileSelect(e(we).id),children:(A,X)=>{var z=Io(),K=d(z);Ot(K,{class:"font-lighter h-2 w-2"});var ne=t(K,2);{var Me=Pe=>{var Ke=J("Run Automation");r(Pe,Ke)},ut=Pe=>{var Ke=J("Complete Profile First");r(Pe,Ke)};h(ne,Pe=>{e(se).isEligible?Pe(Me):Pe(ut,!1)})}r(A,z)},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}})}),$(()=>y(n,`${e(Ue).completionPercentage??""}%`)),r(Ze,qe)},$$slots:{default:!0}})}),r(m,I)}),s(je),r(be,je)};h(q,be=>{e(Le)().length===0?be(Ce):be(De,!1)},xe)}};h(Z,q=>{l.profiles.length===0?q(st):q(ot,!1)})}s(ee),r(w,a),Lt()}const No=Wa({profileId:St().min(1,"Please select a profile"),keywords:Pt(St()).min(0),locations:Pt(St()).min(0),maxJobsToApply:ir().min(1).max(50).default(10),minMatchScore:ir().min(60).max(95).default(70),autoApplyEnabled:Za().default(!1),salaryRange:Pt(ir()).length(2).default([50,120]),experienceRange:Pt(ir()).length(2).default([2,8]),jobTypes:Pt(St()).default([]),remotePreference:St().default("any"),companySizePreference:Pt(St()).default([]),excludeCompanies:Pt(St()).default([]),preferredCompanies:Pt(St()).default([])}).refine(w=>w.keywords.length>0||w.locations.length>0,{message:"Please select at least one keyword or location",path:["keywords"]}).refine(w=>w.salaryRange[0]<=w.salaryRange[1],{message:"Minimum salary cannot be greater than maximum salary",path:["salaryRange"]}).refine(w=>w.experienceRange[0]<=w.experienceRange[1],{message:"Minimum experience cannot be greater than maximum experience",path:["experienceRange"]});var Jo=i("<!> Automation Runs",1),Oo=i("<!> Available Profiles",1),Fo=i("<!> <!>",1),Lo=i("<!> <!> <!>",1),Uo=i("<!> <!> <!>",1);function li(w,l){Ft(l,!0);const[re,Le]=yr(),a=()=>Xt(q,"$form",re),ve=()=>Xt(Re,"$automationRuns",re);let he=_r(l.data.profiles||[]);const Re=Mr(l.data.automationRuns||[]);let ke=dt(null),fe=dt(!1),ee=dt(!1),Z=dt("runs");const st=O(()=>()=>l.data.occupations.map(u=>({value:u.id,label:u.title}))),ot=O(()=>()=>l.data.locations.map(u=>({value:`${u.id}|${u.name}|${u.state.code}|${u.country}`,label:`${u.name}, ${u.state.code}`}))),{form:q,enhance:xe,submitting:Ce}=Qa(l.data.form,{validators:Xa(No),dataType:"json",resetForm:!0,onSubmit:()=>{We.loading("Creating automation run...")},onResult:({result:u})=>{var T;We.dismiss(),u.type==="redirect"?(We.success("Automation run created successfully"),j(ee,!1)):u.type==="failure"&&We.error(((T=u.data)==null?void 0:T.error)||"Failed to create automation run")},onError:()=>{We.dismiss(),We.error("An error occurred while creating the automation run")}}),De=O(()=>()=>he.find(u=>u.id===a().profileId)),be=O(()=>()=>e(De)()?ur(e(De)()).isEligible:!1);async function je(u=""){try{const T=await fetch(`/api/occupations?search=${encodeURIComponent(u)}&limit=20`);if(T.ok)return(await T.json()).map(Q=>({value:Q.id,label:Q.title}))}catch(T){console.error("Error searching occupations:",T)}return[]}async function m(u=""){try{const T=await fetch(`/api/locations?search=${encodeURIComponent(u)}&limit=20`);if(T.ok)return(await T.json()).map(Q=>({value:`${Q.id}|${Q.name}|${Q.state.code}|${Q.country}`,label:`${Q.name}, ${Q.state.code}`}))}catch(T){console.error("Error searching locations:",T)}return[]}const we=O(()=>()=>!(!a().profileId||!e(be)()||a().keywords.length===0&&a().locations.length===0||a().salaryRange[0]>a().salaryRange[1]||a().experienceRange[0]>a().experienceRange[1])),I=O(()=>()=>{var p,c,ae,b,S,L;if(!((c=(p=e(De)())==null?void 0:p.data)!=null&&c.data))return null;const u=e(De)().data.data,T=((ae=u.workExperience)==null?void 0:ae.map(f=>f.title).filter(Boolean))||[],F=((b=u.skillsData)==null?void 0:b.list)||u.skills||[];let Q=0;u.workExperience&&u.workExperience.forEach(f=>{if(f.startDate&&f.endDate){const U=new Date(f.startDate),oe=((f.current?new Date:new Date(f.endDate)).getTime()-U.getTime())/(1e3*60*60*24*365);Q+=oe}});const n=((S=u.personalInfo)==null?void 0:S.city)||((L=u.personalInfo)==null?void 0:L.address)||"";return{jobTitles:[...new Set(T)].slice(0,3),skills:Array.isArray(F)?F.slice(0,5):[],experienceYears:Math.floor(Q),location:n}});function me(){const u=e(I)();if(!u)return;const T=e(st)();if(u.jobTitles.length>0&&u.skills.length>0){const F=[u.jobTitles[0],...u.skills.slice(0,2)],Q=[];for(const n of F){const p=T.find(c=>c.label.toLowerCase().includes(n.toLowerCase())||n.toLowerCase().includes(c.label.toLowerCase()));p&&Q.push(p.value)}Q.length>0&&Xe(q,H(a).keywords=Q,H(a))}if(u.location&&Xe(q,H(a).locations=[`custom|${u.location}|${u.location}|US`],H(a)),u.experienceYears>0){const F=Math.max(0,u.experienceYears-2),Q=Math.min(15,u.experienceYears+3);Xe(q,H(a).experienceRange=[F,Q],H(a))}}function pe(u){Re.update(T=>T.map(F=>F.id===u.id?u:F))}var Ve=Uo(),Ze=d(Ve);ta(Ze,{title:"Job Automation | Hirli",description:"Automate your job search and application process with Hirli's intelligent automation tools.",keywords:"job automation, automated job search, job application automation, resume matching, career automation, job search tools"});var ct=t(Ze,2);v(ct,()=>ts,(u,T)=>{T(u,{class:"",get value(){return e(Z)},set value(F){j(Z,F,!0)},children:(F,Q)=>{var n=Lo(),p=d(n);v(p,()=>es,(b,S)=>{S(b,{class:"border-t-0",children:(L,f)=>{var U=Fo(),se=d(U);v(se,()=>Er,(A,X)=>{X(A,{value:"runs",children:(z,K)=>{var ne=Jo(),Me=d(ne);Ot(Me,{class:"h-3 w-3"}),g(),r(z,ne)},$$slots:{default:!0}})});var oe=t(se,2);v(oe,()=>Er,(A,X)=>{X(A,{value:"profiles",children:(z,K)=>{var ne=Oo(),Me=d(ne);rs(Me,{class:"h-3 w-3"}),g(),r(z,ne)},$$slots:{default:!0}})}),r(L,U)},$$slots:{default:!0}})});var c=t(p,2);v(c,()=>jr,(b,S)=>{S(b,{value:"runs",children:(L,f)=>{bo(L,{get userData(){return l.data.user},get automationRuns(){return ve()},get profiles(){return he},get form(){return q},get enhance(){return xe},get submitting(){return Ce},get occupationOptions(){return e(st)},get locationOptions(){return e(ot)},searchOccupations:je,searchLocations:m,get isFormValid(){return e(we)},get profileSuggestions(){return e(I)},applySuggestions:me,get checkAutomationEligibility(){return ur},get isProfileEligible(){return e(be)},onRunSelect:U=>{j(ke,U,!0),j(fe,!0)},onCreateRun:()=>j(ee,!0)})},$$slots:{default:!0}})});var ae=t(c,2);v(ae,()=>jr,(b,S)=>{S(b,{value:"profiles",children:(L,f)=>{Mo(L,{get userData(){return l.data.user},get profiles(){return he},onProfileSelect:U=>{Xe(q,H(a).profileId=U,H(a)),j(ee,!0)}})},$$slots:{default:!0}})}),r(F,n)},$$slots:{default:!0}})});var qe=t(ct,2);{var Ue=u=>{Os(u,{get automationRun(){return e(ke)},onClose:()=>{j(ke,null)},onRefresh:pe,onStop:()=>{Re.update(T=>T.map(F=>F.id===e(ke).id?{...F,status:"stopped"}:F))},get open(){return e(fe)},set open(T){j(fe,T,!0)}})};h(qe,u=>{e(ke)&&u(Ue)})}r(w,Ve),Lt(),Le()}export{li as component};
