import{S as V,as as k,O as Y,_ as q,P as S,o as Q,ao as W,b4 as X,b5 as Z,a6 as m,b6 as x,b7 as rr,aA as ir,g as fr,b8 as ar,b9 as ur,C as y,ba as tr}from"./CGmarHxI.js";import{l as er,n as sr,p as lr,g as or,q as cr,t as nr,u as vr,v as dr}from"./CmxjS0TN.js";function br(r,f){var i=void 0,a;V(()=>{i!==(i=f())&&(a&&(k(a),a=null),i&&(a=Y(()=>{q(()=>i(r))})))})}function B(r){var f,i,a="";if(typeof r=="string"||typeof r=="number")a+=r;else if(typeof r=="object")if(Array.isArray(r)){var u=r.length;for(f=0;f<u;f++)r[f]&&(i=B(r[f]))&&(a&&(a+=" "),a+=i)}else for(i in r)r[i]&&(a&&(a+=" "),a+=i);return a}function gr(){for(var r,f,i=0,a="",u=arguments.length;i<u;i++)(r=arguments[i])&&(f=B(r))&&(a&&(a+=" "),a+=f);return a}function hr(r){return typeof r=="object"?gr(r):r??""}const R=[...` 	
\r\f \v\uFEFF`];function _r(r,f,i){var a=r==null?"":""+r;if(f&&(a=a?a+" "+f:f),i){for(var u in i)if(i[u])a=a?a+" "+u:u;else if(a.length)for(var t=u.length,e=0;(e=a.indexOf(u,e))>=0;){var s=e+t;(e===0||R.includes(a[e-1]))&&(s===a.length||R.includes(a[s]))?a=(e===0?"":a.substring(0,e))+a.substring(s+1):e=s}}return a===""?null:a}function H(r,f=!1){var i=f?" !important;":";",a="";for(var u in r){var t=r[u];t!=null&&t!==""&&(a+=" "+u+": "+t+i)}return a}function I(r){return r[0]!=="-"||r[1]!=="-"?r.toLowerCase():r}function Ar(r,f){if(f){var i="",a,u;if(Array.isArray(f)?(a=f[0],u=f[1]):a=f,r){r=String(r).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var t=!1,e=0,s=!1,v=[];a&&v.push(...Object.keys(a).map(I)),u&&v.push(...Object.keys(u).map(I));var o=0,A=-1;const N=r.length;for(var c=0;c<N;c++){var d=r[c];if(s?d==="/"&&r[c-1]==="*"&&(s=!1):t?t===d&&(t=!1):d==="/"&&r[c+1]==="*"?s=!0:d==='"'||d==="'"?t=d:d==="("?e++:d===")"&&e--,!s&&t===!1&&e===0){if(d===":"&&A===-1)A=c;else if(d===";"||c===N-1){if(A!==-1){var g=I(r.substring(o,A).trim());if(!v.includes(g)){d!==";"&&c++;var T=r.substring(o,c).trim();i+=" "+T+";"}}o=c+1,A=-1}}}}return a&&(i+=H(a)),u&&(i+=H(u,!0)),i=i.trim(),i===""?null:i}return r==null?null:String(r)}function Sr(r,f,i,a,u,t){var e=r.__className;if(S||e!==i||e===void 0){var s=_r(i,a,t);(!S||s!==r.getAttribute("class"))&&(s==null?r.removeAttribute("class"):f?r.className=s:r.setAttribute("class",s)),r.__className=i}else if(t&&u!==t)for(var v in t){var o=!!t[v];(u==null||o!==!!u[v])&&r.classList.toggle(v,o)}return t}function P(r,f={},i,a){for(var u in i){var t=i[u];f[u]!==t&&(i[u]==null?r.style.removeProperty(u):r.style.setProperty(u,t,a))}}function pr(r,f,i,a){var u=r.__style;if(S||u!==f){var t=Ar(f,a);(!S||t!==r.getAttribute("style"))&&(t==null?r.removeAttribute("style"):r.style.cssText=t),r.__style=f}else a&&(Array.isArray(a)?(P(r,i==null?void 0:i[0],a[0]),P(r,i==null?void 0:i[1],a[1],"important")):P(r,i,a));return a}function C(r,f,i){if(r.multiple){if(f==null)return;if(!W(f))return X();for(var a of r.options)a.selected=f.includes(L(a));return}for(a of r.options){var u=L(a);if(Z(u,f)){a.selected=!0;return}}(!i||f!==void 0)&&(r.selectedIndex=-1)}function G(r,f){let i=!0;q(()=>{f&&C(r,Q(f),i),i=!1;var a=new MutationObserver(()=>{var u=r.__value;C(r,u)});return a.observe(r,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),()=>{a.disconnect()}})}function Tr(r,f,i=f){var a=!0;er(r,"change",u=>{var t=u?"[selected]":":checked",e;if(r.multiple)e=[].map.call(r.querySelectorAll(t),L);else{var s=r.querySelector(t)??r.querySelector("option:not([disabled])");e=s&&L(s)}i(e)}),q(()=>{var u=f();if(C(r,u,a),a&&u===void 0){var t=r.querySelector(":checked");t!==null&&(u=L(t),i(u))}r.__value=u,a=!1}),G(r)}function L(r){return"__value"in r?r.__value:r.value}const O=Symbol("class"),E=Symbol("style"),K=Symbol("is custom element"),z=Symbol("is html");function Cr(r){if(S){var f=!1,i=()=>{if(!f){if(f=!0,r.hasAttribute("value")){var a=r.value;M(r,"value",null),r.value=a}if(r.hasAttribute("checked")){var u=r.checked;M(r,"checked",null),r.checked=u}}};r.__on_r=i,tr(i),vr()}}function Mr(r,f){var i=$(r);i.value===(i.value=f??void 0)||r.value===f&&(f!==0||r.nodeName!=="PROGRESS")||(r.value=f??"")}function Nr(r,f){f?r.hasAttribute("selected")||r.setAttribute("selected",""):r.removeAttribute("selected")}function M(r,f,i,a){var u=$(r);S&&(u[f]=r.getAttribute(f),f==="src"||f==="srcset"||f==="href"&&r.nodeName==="LINK")||u[f]!==(u[f]=i)&&(f==="loading"&&(r[x]=i),i==null?r.removeAttribute(f):typeof i!="string"&&D(r).includes(f)?r[f]=i:r.setAttribute(f,i))}function Or(r,f,i,a,u=!1){var t=$(r),e=t[K],s=!t[z];let v=S&&e;v&&y(!1);var o=f||{},A=r.tagName==="OPTION";for(var c in f)c in i||(i[c]=null);i.class?i.class=hr(i.class):(a||i[O])&&(i.class=null),i[E]&&(i.style??(i.style=null));var d=D(r);for(const l in i){let n=i[l];if(A&&l==="value"&&n==null){r.value=r.__value="",o[l]=n;continue}if(l==="class"){var g=r.namespaceURI==="http://www.w3.org/1999/xhtml";Sr(r,g,n,a,f==null?void 0:f[O],i[O]),o[l]=n,o[O]=i[O];continue}if(l==="style"){pr(r,n,f==null?void 0:f[E],i[E]),o[l]=n,o[E]=i[E];continue}var T=o[l];if(n!==T){o[l]=n;var N=l[0]+l[1];if(N!=="$$")if(N==="on"){const h={},p="$$"+l;let b=l.slice(2);var w=dr(b);if(sr(b)&&(b=b.slice(0,-7),h.capture=!0),!w&&T){if(n!=null)continue;r.removeEventListener(b,o[p],h),o[p]=null}if(n!=null)if(w)r[`__${b}`]=n,or([b]);else{let F=function(J){o[l].call(this,J)};o[p]=lr(b,r,F,h)}else w&&(r[`__${b}`]=void 0)}else if(l==="style")M(r,l,n);else if(l==="autofocus")cr(r,!!n);else if(!e&&(l==="__value"||l==="value"&&n!=null))r.value=r.__value=n;else if(l==="selected"&&A)Nr(r,n);else{var _=l;s||(_=nr(_));var j=_==="defaultValue"||_==="defaultChecked";if(n==null&&!e&&!j)if(t[l]=null,_==="value"||_==="checked"){let h=r;const p=f===void 0;if(_==="value"){let b=h.defaultValue;h.removeAttribute(_),h.defaultValue=b,h.value=h.__value=p?b:null}else{let b=h.defaultChecked;h.removeAttribute(_),h.defaultChecked=b,h.checked=p?b:!1}}else r.removeAttribute(l);else j||d.includes(_)&&(e||typeof n!="string")?r[_]=n:typeof n!="function"&&M(r,_,n)}}}return v&&y(!0),o}function wr(r,f,i=[],a,u=!1,t=m){const e=i.map(t);var s=void 0,v={},o=r.nodeName==="SELECT",A=!1;V(()=>{var c=f(...e.map(fr));Or(r,s,c,a,u),A&&o&&"value"in c&&C(r,c.value,!1);for(let g of Object.getOwnPropertySymbols(v))c[g]||k(v[g]);for(let g of Object.getOwnPropertySymbols(c)){var d=c[g];g.description===ar&&(!s||d!==s[g])&&(v[g]&&k(v[g]),v[g]=Y(()=>br(r,()=>d)))}s=c}),o&&G(r,()=>s.value),A=!0}function $(r){return r.__attributes??(r.__attributes={[K]:r.nodeName.includes("-"),[z]:r.namespaceURI===rr})}var U=new Map;function D(r){var f=U.get(r.nodeName);if(f)return f;U.set(r.nodeName,f=[]);for(var i,a=r,u=Element.prototype;u!==a;){i=ur(a);for(var t in i)i[t].set&&f.push(t);a=ir(a)}return f}export{O as C,E as S,M as a,pr as b,hr as c,Tr as d,wr as e,Mr as f,C as g,gr as h,G as i,Cr as r,Sr as s};
