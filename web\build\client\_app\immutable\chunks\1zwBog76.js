import{c as n,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(r,o){const s=c(o,["children","$$slots","$$events","$$legacy"]),t=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"}]];f(r,d({name:"zap"},()=>s,{get iconNode(){return t},children:(e,$)=>{var a=n(),l=i(a);m(l,o,"default",{},null),p(e,a)},$$slots:{default:!0}}))}export{y as Z};
