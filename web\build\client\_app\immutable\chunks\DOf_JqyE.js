import{c as i,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as c}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as p,s as $}from"./Btcx8l8F.js";import{I as m}from"./D4f2twK-.js";function y(o,t){const r=p(t,["children","$$slots","$$events","$$legacy"]),s=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1"}]];m(o,$({name:"pause"},()=>r,{get iconNode(){return s},children:(a,h)=>{var e=i(),n=c(e);d(n,t,"default",{},null),l(a,e)},$$slots:{default:!0}}))}function N(o,t){const r=p(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8"}],["path",{d:"M12 17.5v-11"}]];m(o,$({name:"receipt"},()=>r,{get iconNode(){return s},children:(a,h)=>{var e=i(),n=c(e);d(n,t,"default",{},null),l(a,e)},$$slots:{default:!0}}))}export{y as P,N as R};
