import{c,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as n}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as p,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function v(o,r){const t=p(r,["children","$$slots","$$events","$$legacy"]),s=[["circle",{cx:"12",cy:"12",r:"10"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16"}]];f(o,d({name:"circle-alert"},()=>t,{get iconNode(){return s},children:(a,$)=>{var e=c(),l=n(e);m(l,r,"default",{},null),i(a,e)},$$slots:{default:!0}}))}export{v as C};
