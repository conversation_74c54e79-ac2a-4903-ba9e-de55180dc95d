import{f as d,a as n}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as E,f as F,t as v,a as G,g as b,e as y,s,c as r,r as o,n as O}from"../chunks/CGmarHxI.js";import{s as l}from"../chunks/CIt1g2O9.js";import{i as S}from"../chunks/u21ee2wt.js";import{i as U}from"../chunks/BIEMS98f.js";import{p as $}from"../chunks/Btcx8l8F.js";import{S as j}from"../chunks/C6g8ubaU.js";import{P as z}from"../chunks/BMgaXnEE.js";var B=d('<p class="mb-6 text-gray-500"> </p>'),I=d('<div class="prose max-w-none"><!></div>'),J=d('<div class="prose max-w-none"><p class="mb-6 rounded-lg border border-amber-200 bg-amber-50 p-4 text-amber-700">This content is not yet available in the CMS. Please add content for this page in Sanity.</p> <p> </p> <p>To add content to this page:</p> <ol><li>Go to your Sanity Studio</li> <li> </li> <li>Add your content using the rich text editor</li> <li>Publish the changes</li></ol></div>'),K=d('<!> <div class="max-w-none"><h1 class="mb-4 text-2xl font-bold"> </h1> <!> <!></div>',1);function et(w,m){E(m,!1);let T=$(m,"data",8);const{legalPage:t}=T(),c=t!=null&&t.updatedAt?new Date(t.updatedAt).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"}):null;U();var f=K(),h=F(f);const A=y(()=>`${t.title} | Hirli`),C=y(()=>`${t.title.toLowerCase()}, legal, Hirli`);j(h,{get title(){return b(A)},get description(){return t.description},get keywords(){return b(C)}});var u=s(h,2),p=r(u),D=r(p,!0);o(p);var g=s(p,2);{var L=a=>{var e=B(),i=r(e);o(e),v(()=>l(i,`Last updated: ${c??""}`)),n(a,e)};S(g,a=>{c&&a(L)})}var P=s(g,2);{var k=a=>{var e=I(),i=r(e);z(i,{get value(){return t.content}}),o(e),n(a,e)},H=a=>{var e=J(),i=s(r(e),2),M=r(i);o(i);var _=s(i,4),x=s(r(_),2),q=r(x);o(x),O(4),o(_),o(e),v(()=>{l(M,`This is a placeholder for the ${t.title??""} page. The actual content should be added in the
        Sanity CMS.`),l(q,`Find or create a page with the slug "${t.slug??""}"`)}),n(a,e)};S(P,a=>{t.content?a(k):a(H,!1)})}o(u),v(()=>l(D,t.title)),n(w,f),G()}export{et as component};
