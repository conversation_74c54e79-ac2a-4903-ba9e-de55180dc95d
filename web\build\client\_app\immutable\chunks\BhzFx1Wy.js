import{c,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as p}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function I(e,o){const s=m(o,["children","$$slots","$$events","$$legacy"]),t=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56"}]];f(e,d({name:"loader-circle"},()=>s,{get iconNode(){return t},children:(a,$)=>{var r=c(),n=l(r);p(n,o,"default",{},null),i(a,r)},$$slots:{default:!0}}))}export{I as L};
