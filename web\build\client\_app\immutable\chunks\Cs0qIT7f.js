import{c as i,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as m}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(t,r){const s=c(r,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M5 12h14"}],["path",{d:"m12 5 7 7-7 7"}]];f(t,d({name:"arrow-right"},()=>s,{get iconNode(){return a},children:(e,$)=>{var o=i(),n=m(o);l(n,r,"default",{},null),p(e,o)},$$slots:{default:!0}}))}export{y as A};
