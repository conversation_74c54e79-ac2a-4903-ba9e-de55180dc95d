import{c as i,a as n}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as p,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function v(o,r){const s=p(r,["children","$$slots","$$events","$$legacy"]),c=[["circle",{cx:"18",cy:"5",r:"3"}],["circle",{cx:"6",cy:"12",r:"3"}],["circle",{cx:"18",cy:"19",r:"3"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49"}]];f(o,d({name:"share-2"},()=>s,{get iconNode(){return c},children:(t,$)=>{var e=i(),a=l(e);m(a,r,"default",{},null),n(t,e)},$$slots:{default:!0}}))}export{v as S};
