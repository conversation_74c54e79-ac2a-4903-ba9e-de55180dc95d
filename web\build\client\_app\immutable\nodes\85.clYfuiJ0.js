import{f as n,a as d}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{f as C,g as i,e as v,s as a,c as t,r as s,n as k,t as J}from"../chunks/CGmarHxI.js";import{s as D}from"../chunks/CIt1g2O9.js";import{i as K}from"../chunks/u21ee2wt.js";import{e as Q,i as R}from"../chunks/C3w0v0gR.js";import{a as u}from"../chunks/B-Xjo-Yt.js";import{e as V}from"../chunks/CmxjS0TN.js";import{p as W}from"../chunks/Btcx8l8F.js";import{S as X}from"../chunks/C6g8ubaU.js";import"../chunks/B1K98fMG.js";import{P as Y}from"../chunks/BMgaXnEE.js";import{D as Z}from"../chunks/tr-scC-m.js";var $=n('<div class="mb-8"><!></div>'),P=n(`<p class="text-muted-foreground mb-6">Download official Hirli logos, product screenshots, and other brand assets for media use. All
      images are available in high resolution and can be used in accordance with our brand
      guidelines.</p>`),I=n('<div class="border-border bg-card text-card-foreground overflow-hidden rounded-lg border shadow-sm"><div class="bg-muted relative aspect-video overflow-hidden"><img class="h-full w-full object-cover"/></div> <div class="p-4"><h3 class="mb-2 text-lg font-medium"> </h3> <p class="text-muted-foreground mb-4 text-sm"> </p> <a download="" class="text-primary inline-flex items-center gap-2 text-sm font-medium hover:underline"><!> <span>Download</span></a></div></div>'),ee=n('<!> <div><h2 class="mb-8 text-3xl font-semibold">Press Images</h2> <!> <div class="mt-8 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3"></div> <div class="mt-12 rounded-lg border bg-gray-50 p-6"><h3 class="mb-4 text-xl font-medium">Usage Guidelines</h3> <ul class="text-muted-foreground list-inside list-disc space-y-2"><li>Do not alter, distort, or modify the logos in any way</li> <li>Maintain adequate spacing around logos</li> <li>Do not use Hirli logos or images to imply partnership or endorsement without permission</li> <li>For questions about usage, please contact <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a></li></ul></div></div>',1);function fe(U,O){var H;let j=W(O,"data",8);const{pressImagesPage:e}=j(),S=[{title:"Hirli Logo",description:"Official Hirli logo in full color",image:"/assets/images/press/logo.png",downloadUrl:"/assets/images/press/logo.png"},{title:"Hirli Logo (Dark)",description:"Official Hirli logo for dark backgrounds",image:"/assets/images/press/logo-dark.png",downloadUrl:"/assets/images/press/logo-dark.png"},{title:"Hirli App Screenshot",description:"Screenshot of the Hirli application dashboard",image:"/assets/images/press/app-screenshot.png",downloadUrl:"/assets/images/press/app-screenshot.png"},{title:"Founder Photo",description:"Official photo of Hirli founder",image:"/assets/images/press/founder.jpg",downloadUrl:"/assets/images/press/founder.jpg"}],q=((H=e==null?void 0:e.images)==null?void 0:H.length)>0?e.images:S;var h=ee(),b=C(h);const F=v(()=>{var o;return((o=e==null?void 0:e.seo)==null?void 0:o.metaTitle)||"Press Images | Hirli"}),T=v(()=>{var o;return((o=e==null?void 0:e.seo)==null?void 0:o.metaDescription)||"Official Hirli brand assets, logos, and images for media use."}),A=v(()=>{var o,r;return((r=(o=e==null?void 0:e.seo)==null?void 0:o.keywords)==null?void 0:r.join(", "))||"Hirli logo, brand assets, press images, media kit"});X(b,{get title(){return i(F)},get description(){return i(T)},get keywords(){return i(A)}});var x=a(b,2),_=a(t(x),2);{var L=o=>{var r=$(),l=t(r);Y(l,{get value(){return e.content}}),s(r),d(o,r)},E=o=>{var r=P();d(o,r)};K(_,o=>{e!=null&&e.content?o(L):o(E,!1)})}var w=a(_,2);Q(w,5,()=>q,R,(o,r)=>{var l=I(),m=t(l),c=t(m);s(m);var y=a(m,2),p=t(y),G=t(p,!0);s(p);var g=a(p,2),M=t(g,!0);s(g);var f=a(g,2),N=t(f);Z(N,{class:"h-4 w-4"}),k(2),s(f),s(y),s(l),J(()=>{u(c,"src",i(r).image),u(c,"alt",i(r).title),D(G,i(r).title),D(M,i(r).description),u(f,"href",i(r).downloadUrl)}),V("error",c,z=>{const B=z.currentTarget;B.src="https://placehold.co/800x450?text=Image+Not+Found"}),d(o,l)}),s(w),k(2),s(x),d(U,h)}export{fe as component};
