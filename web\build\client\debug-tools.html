<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Subscription Debug Tools</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
      }
      button {
        background-color: #4caf50;
        border: none;
        color: white;
        padding: 10px 20px;
        text-align: center;
        text-decoration: none;
        display: inline-block;
        font-size: 16px;
        margin: 4px 2px;
        cursor: pointer;
        border-radius: 4px;
      }
      button.primary {
        background-color: #2196f3;
      }
      button.warning {
        background-color: #ff9800;
      }
      pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow: auto;
        max-height: 300px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
      }
      select,
      input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
      }
      .alert {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
      }
      .alert-info {
        color: #31708f;
        background-color: #d9edf7;
        border-color: #bce8f1;
      }
    </style>
  </head>
  <body>
    <h1>Subscription Debug Tools</h1>

    <div class="alert alert-info">
      <strong>Quick Fix:</strong> If you've successfully paid for a subscription but your role is
      still showing as "free", click the "Fix My Role Now" button below to automatically update your
      role based on your Stripe subscription.
    </div>

    <div class="card">
      <h2>Fix My Role Now</h2>
      <p>
        This will automatically detect your Stripe subscription and update your role to match it.
      </p>
      <button id="fixMyRole" class="primary">Fix My Role Now</button>
      <pre id="fixMyRoleResult">Results will appear here...</pre>
    </div>

    <div class="card">
      <h2>Check Subscription Status</h2>
      <p>This will check your current subscription status and role.</p>
      <button id="checkSubscription">Check Subscription</button>
      <pre id="subscriptionResult">Results will appear here...</pre>
    </div>

    <div class="card">
      <h2>Trigger Webhook</h2>
      <p>
        This will manually trigger the webhook to update your role based on your Stripe
        subscription.
      </p>
      <button id="triggerWebhook">Trigger Webhook</button>
      <pre id="webhookResult">Results will appear here...</pre>
    </div>

    <div class="card">
      <h2>Update Role</h2>
      <p>This will directly update your role in the database and create a subscription record.</p>

      <div class="form-group">
        <label for="roleSelect">Select Role:</label>
        <select id="roleSelect">
          <option value="free">Free</option>
          <option value="casual">Casual</option>
          <option value="active">Active</option>
          <option value="daily">Daily</option>
          <option value="power">Power</option>
          <option value="startup">Startup</option>
          <option value="medium">Medium</option>
          <option value="enterprise">Enterprise</option>
        </select>
      </div>

      <div class="form-group">
        <label for="priceIdInput">Stripe Price ID (optional):</label>
        <input type="text" id="priceIdInput" placeholder="e.g., price_1R9WXPPvxCOa4C05MDynzxE2" />
      </div>

      <button id="updateRole">Update Role</button>
      <pre id="updateRoleResult">Results will appear here...</pre>
    </div>

    <script>
      document.getElementById('fixMyRole').addEventListener('click', async () => {
        const resultElement = document.getElementById('fixMyRoleResult');
        resultElement.textContent = 'Loading...';

        try {
          const response = await fetch('/api/debug/fix-my-role');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          resultElement.textContent = JSON.stringify(data, null, 2);

          // If successful, show a message to refresh the page
          if (data.success) {
            setTimeout(() => {
              if (confirm('Role updated successfully! Refresh the page to see the changes?')) {
                window.location.href = '/dashboard';
              }
            }, 1000);
          }
        } catch (error) {
          resultElement.textContent = `Error: ${error.message}`;
        }
      });

      document.getElementById('checkSubscription').addEventListener('click', async () => {
        const resultElement = document.getElementById('subscriptionResult');
        resultElement.textContent = 'Loading...';

        try {
          const response = await fetch('/api/debug/subscription');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          resultElement.textContent = JSON.stringify(data, null, 2);
        } catch (error) {
          resultElement.textContent = `Error: ${error.message}`;
        }
      });

      document.getElementById('triggerWebhook').addEventListener('click', async () => {
        const resultElement = document.getElementById('webhookResult');
        resultElement.textContent = 'Loading...';

        try {
          const response = await fetch('/api/debug/trigger-webhook', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
          });
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          resultElement.textContent = JSON.stringify(data, null, 2);

          // If successful, show a message to refresh the page
          if (data.success) {
            setTimeout(() => {
              if (confirm('Webhook processed successfully! Refresh the page to see the changes?')) {
                window.location.href = '/dashboard';
              }
            }, 1000);
          }
        } catch (error) {
          resultElement.textContent = `Error: ${error.message}`;
        }
      });

      document.getElementById('updateRole').addEventListener('click', async () => {
        const resultElement = document.getElementById('updateRoleResult');
        resultElement.textContent = 'Loading...';

        const role = document.getElementById('roleSelect').value;
        const priceId = document.getElementById('priceIdInput').value;

        try {
          const response = await fetch('/api/debug/update-role', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ role, priceId: priceId || undefined }),
          });
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          resultElement.textContent = JSON.stringify(data, null, 2);

          // If successful, show a message to refresh the page
          if (data.success) {
            setTimeout(() => {
              if (confirm('Role updated successfully! Refresh the page to see the changes?')) {
                window.location.href = '/dashboard';
              }
            }, 1000);
          }
        } catch (error) {
          resultElement.textContent = `Error: ${error.message}`;
        }
      });
    </script>
  </body>
</html>
