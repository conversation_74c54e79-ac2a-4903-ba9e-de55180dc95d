var Ot=Object.defineProperty;var ft=o=>{throw TypeError(o)};var Ft=(o,t,e)=>t in o?Ot(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e;var _=(o,t,e)=>Ft(o,typeof t!="symbol"?t+"":t,e),ht=(o,t,e)=>t.has(o)||ft("Cannot "+e);var u=(o,t,e)=>(ht(o,t,"read from private field"),e?e.call(o):t.get(o)),A=(o,t,e)=>t.has(o)?ft("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(o):t.set(o,e);var vt=(o,t,e)=>(ht(o,t,"access private method"),e);import{c as f,a as p,f as dt}from"./BasJTneF.js";import{x as F,g as s,d as q,k as gt,p as Q,f as h,a as U,e as mt,c as T,au as rt,r as z}from"./CGmarHxI.js";import{c as pt}from"./BvdI7LR8.js";import{p as r,s as V,r as nt}from"./Btcx8l8F.js";import{s as I,c as _t}from"./ncUU1dSD.js";import{P as kt}from"./BaVT73bJ.js";import{i as D}from"./u21ee2wt.js";import{e as B}from"./B-Xjo-Yt.js";import{u as wt,b as O,m as et}from"./BfX7a-t9.js";import{P as Et,a as St,g as Pt,F as Nt,b as xt}from"./D-o7ybA5.js";import{n as ot}from"./DX6rZLP_.js";import{u as yt}from"./CnMg5bH0.js";import{C as At}from"./DuoUhxYL.js";import{c as bt,d as It}from"./Bd3zs5C6.js";import{i as Rt}from"./Bpi49Nrf.js";import{d as Kt,S as Mt}from"./CIOgxH3l.js";var j,G;class Tt{constructor(t){_(this,"opts");A(this,j,gt(null));A(this,G,gt(null));this.opts=t}get contentNode(){return s(u(this,j))}set contentNode(t){q(u(this,j),t,!0)}get triggerNode(){return s(u(this,G))}set triggerNode(t){q(u(this,G),t,!0)}toggleOpen(){this.opts.open.current=!this.opts.open.current}handleClose(){this.opts.open.current&&(this.opts.open.current=!1)}}j=new WeakMap,G=new WeakMap;var st,Ct,H;class zt{constructor(t,e){A(this,st);_(this,"opts");_(this,"root");A(this,H,F(()=>({id:this.opts.id.current,"aria-haspopup":"dialog","aria-expanded":It(this.root.opts.open.current),"data-state":bt(this.root.opts.open.current),"aria-controls":vt(this,st,Ct).call(this),"data-popover-trigger":"",disabled:this.opts.disabled.current,onkeydown:this.onkeydown,onclick:this.onclick})));this.opts=t,this.root=e,wt({...t,onRefChange:n=>{this.root.triggerNode=n}}),this.onclick=this.onclick.bind(this),this.onkeydown=this.onkeydown.bind(this)}onclick(t){this.opts.disabled.current||t.button===0&&this.root.toggleOpen()}onkeydown(t){this.opts.disabled.current||(t.key===Kt||t.key===Mt)&&(t.preventDefault(),this.root.toggleOpen())}get props(){return s(u(this,H))}set props(t){q(u(this,H),t)}}st=new WeakSet,Ct=function(){var t,e;if(this.root.opts.open.current&&((t=this.root.contentNode)!=null&&t.id))return(e=this.root.contentNode)==null?void 0:e.id},H=new WeakMap;var J,L;class Dt{constructor(t,e){_(this,"opts");_(this,"root");_(this,"onInteractOutside",t=>{this.opts.onInteractOutside.current(t),!(t.defaultPrevented||!Rt(t.target)||t.target.closest("[data-popover-trigger]")===this.root.triggerNode)&&this.root.handleClose()});_(this,"onEscapeKeydown",t=>{this.opts.onEscapeKeydown.current(t),!t.defaultPrevented&&this.root.handleClose()});_(this,"onCloseAutoFocus",t=>{var e;this.opts.onCloseAutoFocus.current(t),!t.defaultPrevented&&(t.preventDefault(),(e=this.root.triggerNode)==null||e.focus())});A(this,J,F(()=>({open:this.root.opts.open.current})));A(this,L,F(()=>({id:this.opts.id.current,tabindex:-1,"data-state":bt(this.root.opts.open.current),"data-popover-content":"",style:{pointerEvents:"auto"}})));_(this,"popperProps",{onInteractOutside:this.onInteractOutside,onEscapeKeydown:this.onEscapeKeydown,onCloseAutoFocus:this.onCloseAutoFocus});this.opts=t,this.root=e,wt({...t,deps:()=>this.root.opts.open.current,onRefChange:n=>{this.root.contentNode=n}})}get snippetProps(){return s(u(this,J))}set snippetProps(t){q(u(this,J),t)}get props(){return s(u(this,L))}set props(t){q(u(this,L),t)}}J=new WeakMap,L=new WeakMap;const lt=new At("Popover.Root");function Bt(o){return lt.set(new Tt(o))}function qt(o){return new zt(o,lt.get())}function Vt(o){return new Dt(o,lt.get())}var jt=dt("<div><div><!></div></div>"),Gt=dt("<div><div><!></div></div>");function Ht(o,t){Q(t,!0);let e=r(t,"ref",15,null),n=r(t,"id",19,yt),a=r(t,"forceMount",3,!1),k=r(t,"onCloseAutoFocus",3,ot),d=r(t,"onEscapeKeydown",3,ot),w=r(t,"onInteractOutside",3,ot),y=r(t,"trapFocus",3,!0),v=r(t,"preventScroll",3,!1),W=nt(t,["$$slots","$$events","$$legacy","child","children","ref","id","forceMount","onCloseAutoFocus","onEscapeKeydown","onInteractOutside","trapFocus","preventScroll"]);const l=Vt({id:O.with(()=>n()),ref:O.with(()=>e(),i=>e(i)),onInteractOutside:O.with(()=>w()),onEscapeKeydown:O.with(()=>d()),onCloseAutoFocus:O.with(()=>k())}),S=F(()=>et(W,l.props));var R=f(),K=h(R);{var g=i=>{Et(i,V(()=>s(S),()=>l.popperProps,{get enabled(){return l.root.opts.open.current},get id(){return n()},get trapFocus(){return y()},get preventScroll(){return v()},loop:!0,forceMount:!0,popper:(at,m)=>{let ut=()=>m==null?void 0:m().props,X=()=>m==null?void 0:m().wrapperProps;var b=f();const Y=F(()=>et(ut(),{style:Pt("popover")}));var Z=h(b);{var $=E=>{var C=f(),N=h(C),P=mt(()=>({props:s(Y),wrapperProps:X(),...l.snippetProps}));I(N,()=>t.child,()=>s(P)),p(E,C)},tt=E=>{var C=jt();B(C,()=>({...X()}));var N=T(C);B(N,()=>({...s(Y)}));var P=T(N);I(P,()=>t.children??rt),z(N),z(C),p(E,C)};D(Z,E=>{t.child?E($):E(tt,!1)})}p(at,b)},$$slots:{popper:!0}}))},c=(i,ct)=>{{var at=m=>{St(m,V(()=>s(S),()=>l.popperProps,{get present(){return l.root.opts.open.current},get id(){return n()},get trapFocus(){return y()},get preventScroll(){return v()},loop:!0,forceMount:!1,popper:(X,b)=>{let Y=()=>b==null?void 0:b().props,Z=()=>b==null?void 0:b().wrapperProps;var $=f();const tt=F(()=>et(Y(),{style:Pt("popover")}));var E=h($);{var C=P=>{var x=f(),M=h(x),it=mt(()=>({props:s(tt),wrapperProps:Z(),...l.snippetProps}));I(M,()=>t.child,()=>s(it)),p(P,x)},N=P=>{var x=Gt();B(x,()=>({...Z()}));var M=T(x);B(M,()=>({...s(tt)}));var it=T(M);I(it,()=>t.children??rt),z(M),z(x),p(P,x)};D(E,P=>{t.child?P(C):P(N,!1)})}p(X,$)},$$slots:{popper:!0}}))};D(i,m=>{a()||m(at)},ct)}};D(K,i=>{a()?i(g):i(c,!1)})}p(o,R),U()}var Jt=dt("<button><!></button>");function Lt(o,t){Q(t,!0);let e=r(t,"id",19,yt),n=r(t,"ref",15,null),a=r(t,"type",3,"button"),k=r(t,"disabled",3,!1),d=nt(t,["$$slots","$$events","$$legacy","children","child","id","ref","type","disabled"]);const w=qt({id:O.with(()=>e()),ref:O.with(()=>n(),v=>n(v)),disabled:O.with(()=>!!k())}),y=F(()=>et(d,w.props,{type:a()}));Nt(o,{get id(){return e()},children:(v,W)=>{var l=f(),S=h(l);{var R=g=>{var c=f(),i=h(c);I(i,()=>t.child,()=>({props:s(y)})),p(g,c)},K=g=>{var c=Jt();B(c,()=>({...s(y)}));var i=T(c);I(i,()=>t.children??rt),z(c),p(g,c)};D(S,g=>{t.child?g(R):g(K,!1)})}p(v,l)},$$slots:{default:!0}}),U()}function Qt(o,t){Q(t,!0);let e=r(t,"open",15,!1),n=r(t,"onOpenChange",3,ot);Bt({open:O.with(()=>e(),a=>{e(a),n()(a)})}),xt(o,{children:(a,k)=>{var d=f(),w=h(d);I(w,()=>t.children??rt),p(a,d)},$$slots:{default:!0}}),U()}function ce(o,t){Q(t,!0);let e=r(t,"ref",15,null),n=r(t,"sideOffset",3,4),a=r(t,"align",3,"center"),k=nt(t,["$$slots","$$events","$$legacy","ref","class","sideOffset","align","portalProps"]);var d=f(),w=h(d);pt(w,()=>kt,(y,v)=>{v(y,V(()=>t.portalProps,{children:(W,l)=>{var S=f(),R=h(S);const K=F(()=>_t("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-(--bits-popover-content-transform-origin) outline-hidden z-50 w-72 rounded-md border p-4 shadow-md",t.class));pt(R,()=>Ht,(g,c)=>{c(g,V({"data-slot":"popover-content",get sideOffset(){return n()},get align(){return a()},get class(){return s(K)}},()=>k,{get ref(){return e()},set ref(i){e(i)}}))}),p(W,S)},$$slots:{default:!0}}))}),p(o,d),U()}function ue(o,t){Q(t,!0);let e=r(t,"ref",15,null),n=nt(t,["$$slots","$$events","$$legacy","ref","class"]);var a=f(),k=h(a);const d=F(()=>_t("",t.class));pt(k,()=>Lt,(w,y)=>{y(w,V({"data-slot":"popover-trigger",get class(){return s(d)}},()=>n,{get ref(){return e()},set ref(v){e(v)}}))}),p(o,a),U()}const fe=Qt;export{ue as P,fe as R,ce as a};
