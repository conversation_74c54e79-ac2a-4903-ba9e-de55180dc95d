import{f as m,a as i,c as le}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as ce,a as $e}from"../chunks/nZgk9enP.js";import{p as je,m as b,g as e,l as J,b as Be,a as Ne,d,c as n,f as K,s as w,r as o,t as k,e as N}from"../chunks/CGmarHxI.js";import{s as fe}from"../chunks/CIt1g2O9.js";import{i as L}from"../chunks/u21ee2wt.js";import{e as de,i as me}from"../chunks/C3w0v0gR.js";import{s as Re}from"../chunks/BBa424ah.js";import{c as ue}from"../chunks/BvdI7LR8.js";import{c as y,s as S,a as pe}from"../chunks/B-Xjo-Yt.js";import{s as Ue,a as Ee,e as Te}from"../chunks/CmxjS0TN.js";import{i as De}from"../chunks/BIEMS98f.js";import{g as We}from"../chunks/Buv24VCh.js";import{t as Q}from"../chunks/DjPYYl4Z.js";import"../chunks/BfX7a-t9.js";import{R as qe,P as ve,a as Ge}from"../chunks/DEWNd2N2.js";import{c as z}from"../chunks/ncUU1dSD.js";import{S as He,C as Me}from"../chunks/whJ0cJ1Q.js";import{F as Oe}from"../chunks/ChqRiddM.js";import{A as Je}from"../chunks/B-l1ubNa.js";import{B as Ke}from"../chunks/iTqMWrIH.js";import{U as Qe}from"../chunks/B_6ivTD3.js";import{S as Ve}from"../chunks/rNI1Perp.js";import{C as Xe}from"../chunks/CxmsTEaf.js";import{B as Ye}from"../chunks/hA0h0kTo.js";import{S as Ze}from"../chunks/BAawoUIy.js";import{U as et}from"../chunks/BSHZ37s_.js";import{L as tt}from"../chunks/CHsAkgDv.js";import{F as at}from"../chunks/CY_6SfHi.js";var rt=m("<span> </span>"),ot=m("<a><!> <!></a>"),st=m("<span> </span>"),it=m("<a><!> <!></a>"),nt=m("<div></div>"),lt=m("<span>Log out</span>"),ct=m('<div><div class="flex flex-1 flex-col gap-4"><!> <!></div> <div><button><div><!> <!></div></button></div></div>'),ft=m('<div class="show-scrollbar w-auto overflow-auto"><!></div>'),dt=m("<!> <!> <!>",1),mt=m('<div class="flex h-[calc(100vh-65px)] w-full flex-col"><main class="flex flex-1 flex-col"><!></main></div>');function qt(he,V){je(V,!1);const[ge,_e]=Ue(),A=()=>Ee(xe,"$page",ge),I=b(),R=b(),X=b(),{page:xe}=We(),Y=[{href:"/dashboard/settings",label:"General",icon:He,exact:!0},{href:"/dashboard/settings/profile",label:"Profile",icon:Oe},{href:"/dashboard/settings/usage",label:"Usage",icon:Je},{href:"/dashboard/settings/interview-coach",label:"AI Coach",icon:Ke},{href:"/dashboard/settings/analysis",label:"Analysis",icon:Me},{href:"/dashboard/settings/account",label:"Account",icon:Qe},{href:"/dashboard/settings/security",label:"Security",icon:Ve},{href:"/dashboard/settings/billing",label:"Billing",icon:Xe},{href:"/dashboard/settings/notifications",label:"Notifications",icon:Ye},{href:"/dashboard/settings/referrals",label:"Referrals",icon:Ze}],be={href:"/dashboard/settings/team",label:"Team",icon:et,exact:!1},we=[{href:"/dashboard/settings/admin",label:"Admin Settings",icon:at,exact:!1}];let Z=b(!1),s=b(!1);const ee={desktop:1024,tablet:768},U={desktop:{defaultLayout:[20,80],collapsedSize:5,minSize:12,maxSize:14},tablet:{defaultLayout:[25,75],collapsedSize:8,minSize:16,maxSize:22}};let g=b(U.desktop),$=b(e(g).defaultLayout);function E(){const t=window.innerWidth;t>=ee.desktop?d(g,U.desktop):t>=ee.tablet&&d(g,U.tablet),d($,e(g).defaultLayout)}ce(()=>{E(),window.addEventListener("resize",E)}),$e(()=>{window.removeEventListener("resize",E)}),ce(async()=>{await ye()});async function ye(){try{const t=await fetch("/api/user/me");if(t.ok){const _=await t.json();d(Z,_.isAdmin===!0)}}catch(t){console.error("Error checking admin status:",t)}}function te(t,_=!1){return _?A().url.pathname===t:A().url.pathname===t||A().url.pathname.startsWith(t+"/")}async function Se(){try{(await fetch("/api/auth/logout",{method:"POST"})).ok?(Q.success("Logged out successfully"),window.location.href="/auth/sign-in"):Q.error("Failed to log out",{description:"Please try again later"})}catch(t){Q.error("Network error",{description:"Could not connect to the server"}),console.error("Logout error:",t)}}J(()=>A(),()=>{d(I,A().data.user)}),J(()=>e(I),()=>{var t,_;d(R,((t=e(I))==null?void 0:t.teamId)||((_=e(I))==null?void 0:_.hasTeamFeature)||!1)}),J(()=>e(R),()=>{d(X,e(R)?[...Y,be]:Y)}),Be(),De();var T=mt(),ae=n(T),ze=n(ae);qe(ze,{direction:"horizontal",onLayoutChange:t=>t&&d($,t),class:"h-full items-stretch",children:(t,_)=>{var re=dt(),oe=K(re);ve(oe,{get defaultSize(){return e($)[0]},get collapsedSize(){return e(g).collapsedSize},collapsible:!0,get minSize(){return e(g).minSize},get maxSize(){return e(g).maxSize},onCollapse:()=>d(s,!0),onExpand:()=>d(s,!1),children:(D,Le)=>{var u=ct(),C=n(u),ie=n(C);de(ie,1,()=>e(X),me,(l,r)=>{let p=()=>e(r).href,x=()=>e(r).label,P=()=>e(r).icon,G=()=>e(r).exact;var v=ot(),h=n(v);{var B=c=>{var a=le(),f=K(a);ue(f,P,(F,O)=>{O(F,{class:"h-2 w-2 sm:h-3 sm:w-3 xl:h-4 xl:w-4"})}),i(c,a)};L(h,c=>{P()&&c(B)})}var H=w(h,2);{var M=c=>{var a=rt(),f=n(a,!0);o(a),k(()=>fe(f,x())),i(c,a)};L(H,c=>{e(s)||c(M)})}o(v),k(c=>{pe(v,"href",p()),S(v,1,c)},[()=>y(z("sm:text-md flex items-center text-sm font-light transition-colors xl:text-base",te(p(),G())?"text-foreground font-semibold":"text-foreground/60 hover:text-foreground",e(s)?"mb-4 justify-center":"gap-4"))],N),i(l,v)});var Ae=w(ie,2);{var Ce=l=>{var r=nt();de(r,5,()=>we,me,(p,x)=>{let P=()=>e(x).href,G=()=>e(x).label,v=()=>e(x).icon;var h=it(),B=n(h);{var H=a=>{var f=le(),F=K(f);ue(F,v,(O,Ie)=>{Ie(O,{class:"h-2 w-2 sm:h-3 sm:w-3 xl:h-4 xl:w-4"})}),i(a,f)};L(B,a=>{v()&&a(H)})}var M=w(B,2);{var c=a=>{var f=st(),F=n(f,!0);o(f),k(()=>fe(F,G())),i(a,f)};L(M,a=>{e(s)||a(c)})}o(h),k(a=>{pe(h,"href",P()),S(h,1,a)},[()=>y(z("sm:text-md flex items-center text-sm font-light transition-colors xl:text-base",te(P())?"text-foreground font-semibold":"text-foreground/60 hover:text-foreground",e(s)?"justify-center":"gap-4"))],N),i(p,h)}),o(r),k(p=>S(r,1,p),[()=>y(z("flex flex-col",e(s)?"mt-2":"mt-2 gap-4 border-t pt-4"))],N),i(l,r)};L(Ae,l=>{e(Z)&&l(Ce)})}o(C);var W=w(C,2),j=n(W),q=n(j),ne=n(q);tt(ne,{class:"h-2 w-2 sm:h-3 sm:w-3 xl:h-4 xl:w-4"});var Pe=w(ne,2);{var Fe=l=>{var r=lt();i(l,r)};L(Pe,l=>{e(s)||l(Fe)})}o(q),o(j),o(W),o(u),k((l,r,p,x)=>{S(u,1,l),S(W,1,r),S(j,1,p),S(q,1,x)},[()=>y(z("bg-secondary/40 flex h-full flex-col",e(s)?"p-4 pt-6":"p-6")),()=>y(z("mt-auto",e(s)?"flex justify-center":"border-t pt-4")),()=>y(z("text-foreground/60 hover:text-foreground sm:text-md flex items-center text-sm font-light transition-colors xl:text-base",e(s)?"flex justify-center p-2":"")),()=>y(z("flex items-center",e(s)?"":"gap-4"))],N),Te("click",j,Se),i(D,u)},$$slots:{default:!0}});var se=w(oe,2);Ge(se,{withHandle:!0});var ke=w(se,2);ve(ke,{get defaultSize(){return e($)[1]},minSize:45,children:(D,Le)=>{var u=ft(),C=n(u);Re(C,V,"default",{},null),o(u),i(D,u)},$$slots:{default:!0}}),i(t,re)},$$slots:{default:!0}}),o(ae),o(T),i(he,T),Ne(),_e()}export{qt as component};
