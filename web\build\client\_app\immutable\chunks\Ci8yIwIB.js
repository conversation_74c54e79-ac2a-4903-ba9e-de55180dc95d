import{c as x,a as s,f as C,t as ye}from"./BasJTneF.js";import{c as ot,o as st,a as lt}from"./nZgk9enP.js";import{p as nt,k as D,v as Ae,f as c,a as it,g as e,d as a,s as E,n as ke,c as L,r as S,t as F,x as Oe,j as ct}from"./CGmarHxI.js";import{s as q}from"./CIt1g2O9.js";import{i as G}from"./u21ee2wt.js";import{k as ut}from"./DT9WCdWY.js";import{e as Pe,i as Te}from"./C3w0v0gR.js";import{c as w}from"./BvdI7LR8.js";import{p as m,s as dt}from"./Btcx8l8F.js";import{P as ft,a as mt,R as pt}from"./3WmhYGjL.js";import{B as Ee}from"./B1K98fMG.js";import{c as ht}from"./ncUU1dSD.js";import{b as vt}from"./Cf6rS4LV.js";import{a as $e}from"./B6TiSgAN.js";import{C as gt,a as _t,b as Ie}from"./Dmwghw4a.js";import{C as bt}from"./DW5gea7N.js";import{C as xt}from"./B5tu6DNS.js";import{C as wt}from"./BwkAotBa.js";import{C as Ct}from"./BNEH2jqx.js";var yt=C('<span class="text-muted-foreground"> </span>'),kt=C('<span class="truncate"> </span>'),Pt=C('<span class="truncate"> </span>'),$t=C('<!> <span class="text-muted-foreground ml-1 text-xs"> </span>',1),Dt=C('<div class="flex flex-1 items-center gap-1 overflow-hidden align-middle"><!></div> <!>',1),Lt=C('<div class="flex w-full items-center justify-between"><span> </span> <!></div>'),St=C('<div class="border-t px-2"><!></div>'),Mt=C("<!> <!> <!>",1),Vt=C("<!> <!>",1);function Yt(je,u){nt(u,!0);const ae=ot(),y=m(u,"options",19,()=>[]),Be=m(u,"selectedValues",19,()=>[]),De=m(u,"placeholder",3,"Select items..."),Ke=m(u,"searchPlaceholder",3,"Search...");m(u,"emptyMessage",3,"No items found.");const ze=m(u,"width",3,"w-[200px]"),Fe=m(u,"disabled",3,!1),v=m(u,"paramName",3,""),H=m(u,"maxDisplayItems",3,2),Le=m(u,"onSelectedValuesChange",3,void 0),re=m(u,"searchOptions",3,function(){return Promise.resolve([])});let o=D(Ae([...Be()])),k=D(!1),d=D(Ae([])),oe=D(""),g=D(-1),J=D(null),Q=D(!1),_=D(null);const se=`multi-combobox-${Math.random().toString(36).substring(2,9)}`;let le=null;function qe(t){a(o,t,!0),ie()}function Ge(){return[...e(o)]}const ne=Oe(()=>e(o).map(t=>{var r;return((r=y().find(l=>l.value===t))==null?void 0:r.label)||t}));function ie(){v()&&vt?(console.log(`MultiCombobox: Updating URL param ${v()} with values:`,e(o)),He()):console.log("MultiCombobox: No paramName provided, skipping URL update"),ae("change",{selectedValues:e(o)}),Le()&&(console.log("MultiCombobox: Calling onSelectedValuesChange with values:",e(o)),Le()(e(o)))}function He(){if(!v())return;const t=new URL(window.location.href),r=new URLSearchParams(t.search);if(e(o).length>0){const ue=e(o).filter(Boolean);if(ue.length>0){const j=ue.map(W=>encodeURIComponent(W)).join(",");r.set(v(),j),console.log(`MultiCombobox: Setting URL param ${v()} to ${j}`)}else r.delete(v()),console.log(`MultiCombobox: Deleting URL param ${v()} (empty filtered values)`)}else r.delete(v()),console.log(`MultiCombobox: Deleting URL param ${v()} (no selected values)`);const l=`${t.pathname}?${r.toString()}`;console.log(`MultiCombobox: New URL: ${l}`),window.history.replaceState({},"",l)}function Je(t){e(_)&&(clearTimeout(e(_)),a(_,null)),a(Q,!0),a(_,setTimeout(()=>{re()&&typeof re()=="function"?re()(t).then(r=>{Array.isArray(r)?a(d,[...r],!0):(console.warn("searchOptions did not return an array:",r),a(d,t?[...y().filter(l=>l.label.toLowerCase().includes(t.toLowerCase()))]:[...y()],!0))}).catch(r=>{console.error("Error in searchOptions:",r),a(d,t?[...y().filter(l=>l.label.toLowerCase().includes(t.toLowerCase()))]:[...y()],!0)}).finally(()=>{ae("search",{searchValue:t}),a(Q,!1),a(_,null)}):(a(d,t?[...y().filter(r=>r.label.toLowerCase().includes(t.toLowerCase()))]:[...y()],!0),ae("search",{searchValue:t}),a(Q,!1),a(_,null))},300),!0)}function Se(t){e(o).includes(t)?a(o,e(o).filter(r=>r!==t),!0):a(o,[...e(o),t],!0),ie()}function ce(){a(o,[],!0),ie()}function I(){a(k,!1),a(g,-1),ct().then(()=>{e(J)&&e(J).focus()})}function Qe(t){if(e(k)){if(t.altKey&&t.key==="c"){t.preventDefault(),e(o).length>0&&(ce(),I());return}switch(t.key){case"ArrowDown":t.preventDefault(),a(g,Math.min(e(g)+1,e(d).length-1),!0);break;case"ArrowUp":t.preventDefault(),a(g,Math.max(e(g)-1,-1),!0);break;case"Enter":t.preventDefault(),e(g)>=0&&e(g)<e(d).length&&Se(e(d)[e(g)].value);break;case"Escape":t.preventDefault(),I();break;case"Tab":t.shiftKey||I();break}}}st(()=>{a(d,y(),!0),le=$e.subscribe(t=>{t&&t!==se&&e(k)&&a(k,!1)})}),lt(()=>{e(_)&&(clearTimeout(e(_)),a(_,null)),le&&le()});function We(t){a(k,t,!0),t?(a(g,-1),$e.set(se)):$e.update(r=>r===se?null:r)}var Me=x(),Xe=c(Me);return w(Xe,()=>pt,(t,r)=>{r(t,{onopenchange:l=>We(l.detail),get open(){return e(k)},set open(l){a(k,l,!0)},children:(l,ue)=>{var j=Vt(),W=c(j);w(W,()=>ft,(de,fe)=>{fe(de,{class:"!overflow-hidden",get ref(){return e(J)},set ref(X){a(J,X,!0)},child:(X,A)=>{Ee(X,dt({variant:"outline",role:"combobox",get"aria-expanded"(){return e(k)},"aria-haspopup":"listbox","aria-controls":"multi-combobox-options",get"aria-label"(){return De()},get disabled(){return Fe()}},()=>A==null?void 0:A().props,{children:(me,pe)=>{var Y=Dt(),B=c(Y),he=L(B);{var ve=b=>{var P=yt(),K=L(P,!0);S(P),F(()=>q(K,De())),s(b,P)},Re=(b,P)=>{{var K=n=>{var i=x(),p=c(i);Pe(p,17,()=>e(ne),Te,(f,M,V)=>{var U=kt(),z=L(U);S(U),F(()=>q(z,`${e(M)??""}${V<e(ne).length-1?", ":""}`)),s(f,U)}),s(n,i)},ge=n=>{var i=$t(),p=c(i);Pe(p,17,()=>e(ne).slice(0,H()),Te,(V,U,z)=>{var h=Pt(),O=L(h);S(h),F(()=>q(O,`${e(U)??""}${z<H()-1?", ":""}`)),s(V,h)});var f=E(p,2),M=L(f);S(f),F(()=>q(M,`+${e(o).length-H()} more`)),s(n,i)};G(b,n=>{e(o).length<=H()?n(K):n(ge,!1)},P)}};G(he,b=>{e(o).length===0?b(ve):b(Re,!1)})}S(B);var Z=E(B,2);wt(Z,{class:"ml-2 size-4 opacity-50"}),s(me,Y)},$$slots:{default:!0}}))},$$slots:{child:!0}})});var Ye=E(W,2);w(Ye,()=>mt,(de,fe)=>{fe(de,{get class(){return`${ze()??""} rounded-none p-0`},align:"start",sideOffset:8,children:(Ve,X)=>{var A=x(),Ue=c(A);ut(Ue,()=>e(d),me=>{var pe=x(),Y=c(pe);w(Y,()=>gt,(B,he)=>{he(B,{shouldFilter:!1,children:(ve,Re)=>{var Z=Mt(),b=c(Z);w(b,()=>xt,(n,i)=>{i(n,{get placeholder(){return Ke()},oninput:()=>Je(e(oe)),onkeydown:Qe,get value(){return e(oe)},set value(p){a(oe,p,!0)}})});var P=E(b,2);w(P,()=>_t,(n,i)=>{i(n,{class:"py-1",children:(p,f)=>{var M=x(),V=c(M);{var U=h=>{var O=x(),_e=c(O);w(_e,()=>Ie,(be,$)=>{$(be,{class:"p-2",children:(R,ee)=>{ke();var N=ye("Searching...");s(R,N)},$$slots:{default:!0}})}),s(h,O)},z=(h,O)=>{{var _e=$=>{var R=x(),ee=c(R);w(ee,()=>Ie,(N,T)=>{T(N,{class:"p-2",children:(te,Ne)=>{ke();var xe=ye("No results found.");s(te,xe)},$$slots:{default:!0}})}),s($,R)},be=$=>{var R=x(),ee=c(R);Pe(ee,19,()=>e(d),N=>N.value,(N,T)=>{var te=x(),Ne=c(te);w(Ne,()=>bt,(xe,Ze)=>{Ze(xe,{get value(){return e(T).value},onSelect:()=>Se(e(T).value),children:(et,Ut)=>{var we=Lt(),Ce=L(we),tt=L(Ce,!0);S(Ce);var at=E(Ce,2);const rt=Oe(()=>ht("h-4 w-4",!e(o).includes(e(T).value)&&"text-transparent"));Ct(at,{get class(){return e(rt)}}),S(we),F(()=>q(tt,e(T).label)),s(et,we)},$$slots:{default:!0}})}),s(N,te)}),s($,R)};G(h,$=>{e(d).length===0?$(_e):$(be,!1)},O)}};G(V,h=>{e(Q)?h(U):h(z,!1)})}s(p,M)},$$slots:{default:!0}})});var K=E(P,2);{var ge=n=>{var i=St(),p=L(i);Ee(p,{type:"button",size:"sm",variant:"ghost",class:"my-2 h-6 w-full rounded-none p-1 text-center text-xs","aria-label":"Clear all selections",onclick:()=>{ce(),I()},onkeydown:f=>{(f.key==="Enter"||f.key===" ")&&(f.preventDefault(),ce(),I())},children:(f,M)=>{ke();var V=ye("Clear selections");s(f,V)},$$slots:{default:!0}}),S(i),s(n,i)};G(K,n=>{e(o).length>0&&n(ge)})}s(ve,Z)},$$slots:{default:!0}})}),s(me,pe)}),s(Ve,A)},$$slots:{default:!0}})}),s(l,j)},$$slots:{default:!0}})}),s(je,Me),it({setSelectedValues:qe,getSelectedValues:Ge})}export{Yt as M};
