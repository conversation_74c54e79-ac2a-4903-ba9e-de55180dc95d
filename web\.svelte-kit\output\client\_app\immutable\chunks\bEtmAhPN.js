import{c,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as $,s as h}from"./Btcx8l8F.js";import{I as p}from"./D4f2twK-.js";function x(o,t){const r=$(t,["children","$$slots","$$events","$$legacy"]),n=[["rect",{width:"14",height:"8",x:"5",y:"2",rx:"2"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2"}],["path",{d:"M6 18h2"}],["path",{d:"M12 18h6"}]];p(o,h({name:"computer"},()=>r,{get iconNode(){return n},children:(s,m)=>{var e=c(),a=l(e);d(a,t,"default",{},null),i(s,e)},$$slots:{default:!0}}))}function N(o,t){const r=$(t,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"}],["path",{d:"m21 2-9.6 9.6"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5"}]];p(o,h({name:"key"},()=>r,{get iconNode(){return n},children:(s,m)=>{var e=c(),a=l(e);d(a,t,"default",{},null),i(s,e)},$$slots:{default:!0}}))}function w(o,t){const r=$(t,["children","$$slots","$$events","$$legacy"]),n=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2"}],["path",{d:"M12 18h.01"}]];p(o,h({name:"smartphone"},()=>r,{get iconNode(){return n},children:(s,m)=>{var e=c(),a=l(e);d(a,t,"default",{},null),i(s,e)},$$slots:{default:!0}}))}export{x as C,N as K,w as S};
