import{c as n,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as d,s as l}from"./Btcx8l8F.js";import{I as h}from"./D4f2twK-.js";function k(o,t){const r=d(t,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"m3 17 2 2 4-4"}],["path",{d:"m3 7 2 2 4-4"}],["path",{d:"M13 6h8"}],["path",{d:"M13 12h8"}],["path",{d:"M13 18h8"}]];h(o,l({name:"list-checks"},()=>r,{get iconNode(){return a},children:(e,f)=>{var s=n(),p=i(s);m(p,t,"default",{},null),c(e,s)},$$slots:{default:!0}}))}export{k as L};
