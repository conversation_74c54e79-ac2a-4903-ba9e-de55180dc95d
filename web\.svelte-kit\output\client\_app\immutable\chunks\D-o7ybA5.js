var Ye=Object.defineProperty;var pe=e=>{throw TypeError(e)};var Ue=(e,t,n)=>t in e?Ye(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var B=(e,t,n)=>Ue(e,typeof t!="symbol"?t+"":t,n),Xe=(e,t,n)=>t.has(e)||pe("Cannot "+n);var m=(e,t,n)=>(Xe(e,t,"read from private field"),n?n.call(e):t.get(e)),C=(e,t,n)=>t.has(e)?pe("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n);import{c as dt,a as st,f as je}from"./BasJTneF.js";import{k as N,v as Ut,d as F,i as it,g as v,x as E,p as Nt,f as ct,a as Vt,au as Ht,s as qe,e as Ze}from"./CGmarHxI.js";import{s as zt}from"./ncUU1dSD.js";import{d as Je,b as S,u as Xt,f as Qe,g as Ge,w as ve,m as Ee}from"./BfX7a-t9.js";import{p as P,s as jt,r as te}from"./Btcx8l8F.js";import{P as $e}from"./XESq6qWN.js";import{i as re}from"./u21ee2wt.js";import{F as tn,S as xe,E as en,D as nn,T as on}from"./BaVT73bJ.js";import{o as rn}from"./nZgk9enP.js";import{C as Re}from"./DuoUhxYL.js";import{e as sn}from"./Bpi49Nrf.js";import{u as De}from"./CnMg5bH0.js";function cn(e){return typeof e=="function"}function an(e){return cn(e)?e():e}var Q;class ln{constructor(t,n={box:"border-box"}){C(this,Q,N(Ut({width:0,height:0})));var o,r;const i=n.window??Je;F(m(this,Q),{width:((o=n.initialSize)==null?void 0:o.width)??0,height:((r=n.initialSize)==null?void 0:r.height)??0},!0),it(()=>{if(!i)return;const s=an(t);if(!s)return;const c=new i.ResizeObserver(a=>{for(const u of a){const l=n.box==="content-box"?u.contentBoxSize:u.borderBoxSize,d=Array.isArray(l)?l:[l];v(m(this,Q)).width=d.reduce((g,f)=>Math.max(g,f.inlineSize),0),v(m(this,Q)).height=d.reduce((g,f)=>Math.max(g,f.blockSize),0)}});return c.observe(s),()=>{c.disconnect()}})}get current(){return v(m(this,Q))}get width(){return v(m(this,Q)).width}get height(){return v(m(this,Q)).height}}Q=new WeakMap;const un=["top","right","bottom","left"],ot=Math.min,L=Math.max,qt=Math.round,Yt=Math.floor,j=e=>({x:e,y:e}),dn={left:"right",right:"left",bottom:"top",top:"bottom"},fn={start:"end",end:"start"};function se(e,t,n){return L(e,ot(t,n))}function $(e,t){return typeof e=="function"?e(t):e}function tt(e){return e.split("-")[0]}function ft(e){return e.split("-")[1]}function le(e){return e==="x"?"y":"x"}function ue(e){return e==="y"?"height":"width"}function G(e){return["top","bottom"].includes(tt(e))?"y":"x"}function de(e){return le(G(e))}function hn(e,t,n){n===void 0&&(n=!1);const i=ft(e),o=de(e),r=ue(o);let s=o==="x"?i===(n?"end":"start")?"right":"left":i==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(s=Zt(s)),[s,Zt(s)]}function gn(e){const t=Zt(e);return[ce(e),t,ce(t)]}function ce(e){return e.replace(/start|end/g,t=>fn[t])}function mn(e,t,n){const i=["left","right"],o=["right","left"],r=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:i:t?i:o;case"left":case"right":return t?r:s;default:return[]}}function wn(e,t,n,i){const o=ft(e);let r=mn(tt(e),n==="start",i);return o&&(r=r.map(s=>s+"-"+o),t&&(r=r.concat(r.map(ce)))),r}function Zt(e){return e.replace(/left|right|bottom|top/g,t=>dn[t])}function yn(e){return{top:0,right:0,bottom:0,left:0,...e}}function Be(e){return typeof e!="number"?yn(e):{top:e,right:e,bottom:e,left:e}}function Jt(e){const{x:t,y:n,width:i,height:o}=e;return{width:i,height:o,top:n,left:t,right:t+i,bottom:n+o,x:t,y:n}}function be(e,t,n){let{reference:i,floating:o}=e;const r=G(t),s=de(t),c=ue(s),a=tt(t),u=r==="y",l=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,g=i[c]/2-o[c]/2;let f;switch(a){case"top":f={x:l,y:i.y-o.height};break;case"bottom":f={x:l,y:i.y+i.height};break;case"right":f={x:i.x+i.width,y:d};break;case"left":f={x:i.x-o.width,y:d};break;default:f={x:i.x,y:i.y}}switch(ft(t)){case"start":f[s]-=g*(n&&u?-1:1);break;case"end":f[s]+=g*(n&&u?-1:1);break}return f}const pn=async(e,t,n)=>{const{placement:i="bottom",strategy:o="absolute",middleware:r=[],platform:s}=n,c=r.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(t));let u=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:l,y:d}=be(u,i,a),g=i,f={},h=0;for(let w=0;w<c.length;w++){const{name:y,fn:p}=c[w],{x:O,y:b,data:A,reset:x}=await p({x:l,y:d,initialPlacement:i,placement:g,strategy:o,middlewareData:f,rects:u,platform:s,elements:{reference:e,floating:t}});l=O??l,d=b??d,f={...f,[y]:{...f[y],...A}},x&&h<=50&&(h++,typeof x=="object"&&(x.placement&&(g=x.placement),x.rects&&(u=x.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):x.rects),{x:l,y:d}=be(u,g,a)),w=-1)}return{x:l,y:d,placement:g,strategy:o,middlewareData:f}};async function mt(e,t){var n;t===void 0&&(t={});const{x:i,y:o,platform:r,rects:s,elements:c,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:l="viewport",elementContext:d="floating",altBoundary:g=!1,padding:f=0}=$(t,e),h=Be(f),y=c[g?d==="floating"?"reference":"floating":d],p=Jt(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(y)))==null||n?y:y.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(c.floating)),boundary:u,rootBoundary:l,strategy:a})),O=d==="floating"?{x:i,y:o,width:s.floating.width,height:s.floating.height}:s.reference,b=await(r.getOffsetParent==null?void 0:r.getOffsetParent(c.floating)),A=await(r.isElement==null?void 0:r.isElement(b))?await(r.getScale==null?void 0:r.getScale(b))||{x:1,y:1}:{x:1,y:1},x=Jt(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:O,offsetParent:b,strategy:a}):O);return{top:(p.top-x.top+h.top)/A.y,bottom:(x.bottom-p.bottom+h.bottom)/A.y,left:(p.left-x.left+h.left)/A.x,right:(x.right-p.right+h.right)/A.x}}const vn=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:i,placement:o,rects:r,platform:s,elements:c,middlewareData:a}=t,{element:u,padding:l=0}=$(e,t)||{};if(u==null)return{};const d=Be(l),g={x:n,y:i},f=de(o),h=ue(f),w=await s.getDimensions(u),y=f==="y",p=y?"top":"left",O=y?"bottom":"right",b=y?"clientHeight":"clientWidth",A=r.reference[h]+r.reference[f]-g[f]-r.floating[h],x=g[f]-r.reference[f],R=await(s.getOffsetParent==null?void 0:s.getOffsetParent(u));let D=R?R[b]:0;(!D||!await(s.isElement==null?void 0:s.isElement(R)))&&(D=c.floating[b]||r.floating[h]);const Y=A/2-x/2,I=D/2-w[h]/2-1,T=ot(d[p],I),M=ot(d[O],I),W=T,U=D-w[h]-M,V=D/2-w[h]/2+Y,_=se(W,V,U),J=!a.arrow&&ft(o)!=null&&V!==_&&r.reference[h]/2-(V<W?T:M)-w[h]/2<0,H=J?V<W?V-W:V-U:0;return{[f]:g[f]+H,data:{[f]:_,centerOffset:V-_-H,...J&&{alignmentOffset:H}},reset:J}}}),xn=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,i;const{placement:o,middlewareData:r,rects:s,initialPlacement:c,platform:a,elements:u}=t,{mainAxis:l=!0,crossAxis:d=!0,fallbackPlacements:g,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:w=!0,...y}=$(e,t);if((n=r.arrow)!=null&&n.alignmentOffset)return{};const p=tt(o),O=G(c),b=tt(c)===c,A=await(a.isRTL==null?void 0:a.isRTL(u.floating)),x=g||(b||!w?[Zt(c)]:gn(c)),R=h!=="none";!g&&R&&x.push(...wn(c,w,h,A));const D=[c,...x],Y=await mt(t,y),I=[];let T=((i=r.flip)==null?void 0:i.overflows)||[];if(l&&I.push(Y[p]),d){const _=hn(o,s,A);I.push(Y[_[0]],Y[_[1]])}if(T=[...T,{placement:o,overflows:I}],!I.every(_=>_<=0)){var M,W;const _=(((M=r.flip)==null?void 0:M.index)||0)+1,J=D[_];if(J){var U;const et=d==="alignment"?O!==G(J):!1,X=((U=T[0])==null?void 0:U.overflows[0])>0;if(!et||X)return{data:{index:_,overflows:T},reset:{placement:J}}}let H=(W=T.filter(et=>et.overflows[0]<=0).sort((et,X)=>et.overflows[1]-X.overflows[1])[0])==null?void 0:W.placement;if(!H)switch(f){case"bestFit":{var V;const et=(V=T.filter(X=>{if(R){const nt=G(X.placement);return nt===O||nt==="y"}return!0}).map(X=>[X.placement,X.overflows.filter(nt=>nt>0).reduce((nt,Ke)=>nt+Ke,0)]).sort((X,nt)=>X[1]-nt[1])[0])==null?void 0:V[0];et&&(H=et);break}case"initialPlacement":H=c;break}if(o!==H)return{reset:{placement:H}}}return{}}}};function Oe(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Ae(e){return un.some(t=>e[t]>=0)}const bn=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:i="referenceHidden",...o}=$(e,t);switch(i){case"referenceHidden":{const r=await mt(t,{...o,elementContext:"reference"}),s=Oe(r,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:Ae(s)}}}case"escaped":{const r=await mt(t,{...o,altBoundary:!0}),s=Oe(r,n.floating);return{data:{escapedOffsets:s,escaped:Ae(s)}}}default:return{}}}}};async function On(e,t){const{placement:n,platform:i,elements:o}=e,r=await(i.isRTL==null?void 0:i.isRTL(o.floating)),s=tt(n),c=ft(n),a=G(n)==="y",u=["left","top"].includes(s)?-1:1,l=r&&a?-1:1,d=$(t,e);let{mainAxis:g,crossAxis:f,alignmentAxis:h}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return c&&typeof h=="number"&&(f=c==="end"?h*-1:h),a?{x:f*l,y:g*u}:{x:g*u,y:f*l}}const An=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,i;const{x:o,y:r,placement:s,middlewareData:c}=t,a=await On(t,e);return s===((n=c.offset)==null?void 0:n.placement)&&(i=c.arrow)!=null&&i.alignmentOffset?{}:{x:o+a.x,y:r+a.y,data:{...a,placement:s}}}}},Pn=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:i,placement:o}=t,{mainAxis:r=!0,crossAxis:s=!1,limiter:c={fn:y=>{let{x:p,y:O}=y;return{x:p,y:O}}},...a}=$(e,t),u={x:n,y:i},l=await mt(t,a),d=G(tt(o)),g=le(d);let f=u[g],h=u[d];if(r){const y=g==="y"?"top":"left",p=g==="y"?"bottom":"right",O=f+l[y],b=f-l[p];f=se(O,f,b)}if(s){const y=d==="y"?"top":"left",p=d==="y"?"bottom":"right",O=h+l[y],b=h-l[p];h=se(O,h,b)}const w=c.fn({...t,[g]:f,[d]:h});return{...w,data:{x:w.x-n,y:w.y-i,enabled:{[g]:r,[d]:s}}}}}},Sn=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:i,placement:o,rects:r,middlewareData:s}=t,{offset:c=0,mainAxis:a=!0,crossAxis:u=!0}=$(e,t),l={x:n,y:i},d=G(o),g=le(d);let f=l[g],h=l[d];const w=$(c,t),y=typeof w=="number"?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(a){const b=g==="y"?"height":"width",A=r.reference[g]-r.floating[b]+y.mainAxis,x=r.reference[g]+r.reference[b]-y.mainAxis;f<A?f=A:f>x&&(f=x)}if(u){var p,O;const b=g==="y"?"width":"height",A=["top","left"].includes(tt(o)),x=r.reference[d]-r.floating[b]+(A&&((p=s.offset)==null?void 0:p[d])||0)+(A?0:y.crossAxis),R=r.reference[d]+r.reference[b]+(A?0:((O=s.offset)==null?void 0:O[d])||0)-(A?y.crossAxis:0);h<x?h=x:h>R&&(h=R)}return{[g]:f,[d]:h}}}},Cn=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,i;const{placement:o,rects:r,platform:s,elements:c}=t,{apply:a=()=>{},...u}=$(e,t),l=await mt(t,u),d=tt(o),g=ft(o),f=G(o)==="y",{width:h,height:w}=r.floating;let y,p;d==="top"||d==="bottom"?(y=d,p=g===(await(s.isRTL==null?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(p=d,y=g==="end"?"top":"bottom");const O=w-l.top-l.bottom,b=h-l.left-l.right,A=ot(w-l[y],O),x=ot(h-l[p],b),R=!t.middlewareData.shift;let D=A,Y=x;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(Y=b),(i=t.middlewareData.shift)!=null&&i.enabled.y&&(D=O),R&&!g){const T=L(l.left,0),M=L(l.right,0),W=L(l.top,0),U=L(l.bottom,0);f?Y=h-2*(T!==0||M!==0?T+M:L(l.left,l.right)):D=w-2*(W!==0||U!==0?W+U:L(l.top,l.bottom))}await a({...t,availableWidth:Y,availableHeight:D});const I=await s.getDimensions(c.floating);return h!==I.width||w!==I.height?{reset:{rects:!0}}:{}}}};function ee(){return typeof window<"u"}function ht(e){return Te(e)?(e.nodeName||"").toLowerCase():"#document"}function k(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Z(e){var t;return(t=(Te(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Te(e){return ee()?e instanceof Node||e instanceof k(e).Node:!1}function z(e){return ee()?e instanceof Element||e instanceof k(e).Element:!1}function q(e){return ee()?e instanceof HTMLElement||e instanceof k(e).HTMLElement:!1}function Pe(e){return!ee()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof k(e).ShadowRoot}function Kt(e){const{overflow:t,overflowX:n,overflowY:i,display:o}=K(e);return/auto|scroll|overlay|hidden|clip/.test(t+i+n)&&!["inline","contents"].includes(o)}function Fn(e){return["table","td","th"].includes(ht(e))}function ne(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function fe(e){const t=he(),n=z(e)?K(e):e;return["transform","translate","scale","rotate","perspective"].some(i=>n[i]?n[i]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(i=>(n.willChange||"").includes(i))||["paint","layout","strict","content"].some(i=>(n.contain||"").includes(i))}function En(e){let t=rt(e);for(;q(t)&&!ut(t);){if(fe(t))return t;if(ne(t))return null;t=rt(t)}return null}function he(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ut(e){return["html","body","#document"].includes(ht(e))}function K(e){return k(e).getComputedStyle(e)}function ie(e){return z(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function rt(e){if(ht(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Pe(e)&&e.host||Z(e);return Pe(t)?t.host:t}function _e(e){const t=rt(e);return ut(t)?e.ownerDocument?e.ownerDocument.body:e.body:q(t)&&Kt(t)?t:_e(t)}function wt(e,t,n){var i;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=_e(e),r=o===((i=e.ownerDocument)==null?void 0:i.body),s=k(o);if(r){const c=ae(s);return t.concat(s,s.visualViewport||[],Kt(o)?o:[],c&&n?wt(c):[])}return t.concat(o,wt(o,[],n))}function ae(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Le(e){const t=K(e);let n=parseFloat(t.width)||0,i=parseFloat(t.height)||0;const o=q(e),r=o?e.offsetWidth:n,s=o?e.offsetHeight:i,c=qt(n)!==r||qt(i)!==s;return c&&(n=r,i=s),{width:n,height:i,$:c}}function ge(e){return z(e)?e:e.contextElement}function lt(e){const t=ge(e);if(!q(t))return j(1);const n=t.getBoundingClientRect(),{width:i,height:o,$:r}=Le(t);let s=(r?qt(n.width):n.width)/i,c=(r?qt(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!c||!Number.isFinite(c))&&(c=1),{x:s,y:c}}const Rn=j(0);function ke(e){const t=k(e);return!he()||!t.visualViewport?Rn:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Dn(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==k(e)?!1:t}function at(e,t,n,i){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=ge(e);let s=j(1);t&&(i?z(i)&&(s=lt(i)):s=lt(e));const c=Dn(r,n,i)?ke(r):j(0);let a=(o.left+c.x)/s.x,u=(o.top+c.y)/s.y,l=o.width/s.x,d=o.height/s.y;if(r){const g=k(r),f=i&&z(i)?k(i):i;let h=g,w=ae(h);for(;w&&i&&f!==h;){const y=lt(w),p=w.getBoundingClientRect(),O=K(w),b=p.left+(w.clientLeft+parseFloat(O.paddingLeft))*y.x,A=p.top+(w.clientTop+parseFloat(O.paddingTop))*y.y;a*=y.x,u*=y.y,l*=y.x,d*=y.y,a+=b,u+=A,h=k(w),w=ae(h)}}return Jt({width:l,height:d,x:a,y:u})}function me(e,t){const n=ie(e).scrollLeft;return t?t.left+n:at(Z(e)).left+n}function Ie(e,t,n){n===void 0&&(n=!1);const i=e.getBoundingClientRect(),o=i.left+t.scrollLeft-(n?0:me(e,i)),r=i.top+t.scrollTop;return{x:o,y:r}}function Bn(e){let{elements:t,rect:n,offsetParent:i,strategy:o}=e;const r=o==="fixed",s=Z(i),c=t?ne(t.floating):!1;if(i===s||c&&r)return n;let a={scrollLeft:0,scrollTop:0},u=j(1);const l=j(0),d=q(i);if((d||!d&&!r)&&((ht(i)!=="body"||Kt(s))&&(a=ie(i)),q(i))){const f=at(i);u=lt(i),l.x=f.x+i.clientLeft,l.y=f.y+i.clientTop}const g=s&&!d&&!r?Ie(s,a,!0):j(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+l.x+g.x,y:n.y*u.y-a.scrollTop*u.y+l.y+g.y}}function Tn(e){return Array.from(e.getClientRects())}function _n(e){const t=Z(e),n=ie(e),i=e.ownerDocument.body,o=L(t.scrollWidth,t.clientWidth,i.scrollWidth,i.clientWidth),r=L(t.scrollHeight,t.clientHeight,i.scrollHeight,i.clientHeight);let s=-n.scrollLeft+me(e);const c=-n.scrollTop;return K(i).direction==="rtl"&&(s+=L(t.clientWidth,i.clientWidth)-o),{width:o,height:r,x:s,y:c}}function Ln(e,t){const n=k(e),i=Z(e),o=n.visualViewport;let r=i.clientWidth,s=i.clientHeight,c=0,a=0;if(o){r=o.width,s=o.height;const u=he();(!u||u&&t==="fixed")&&(c=o.offsetLeft,a=o.offsetTop)}return{width:r,height:s,x:c,y:a}}function kn(e,t){const n=at(e,!0,t==="fixed"),i=n.top+e.clientTop,o=n.left+e.clientLeft,r=q(e)?lt(e):j(1),s=e.clientWidth*r.x,c=e.clientHeight*r.y,a=o*r.x,u=i*r.y;return{width:s,height:c,x:a,y:u}}function Se(e,t,n){let i;if(t==="viewport")i=Ln(e,n);else if(t==="document")i=_n(Z(e));else if(z(t))i=kn(t,n);else{const o=ke(e);i={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Jt(i)}function Me(e,t){const n=rt(e);return n===t||!z(n)||ut(n)?!1:K(n).position==="fixed"||Me(n,t)}function In(e,t){const n=t.get(e);if(n)return n;let i=wt(e,[],!1).filter(c=>z(c)&&ht(c)!=="body"),o=null;const r=K(e).position==="fixed";let s=r?rt(e):e;for(;z(s)&&!ut(s);){const c=K(s),a=fe(s);!a&&c.position==="fixed"&&(o=null),(r?!a&&!o:!a&&c.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Kt(s)&&!a&&Me(e,s))?i=i.filter(l=>l!==s):o=c,s=rt(s)}return t.set(e,i),i}function Mn(e){let{element:t,boundary:n,rootBoundary:i,strategy:o}=e;const s=[...n==="clippingAncestors"?ne(t)?[]:In(t,this._c):[].concat(n),i],c=s[0],a=s.reduce((u,l)=>{const d=Se(t,l,o);return u.top=L(d.top,u.top),u.right=ot(d.right,u.right),u.bottom=ot(d.bottom,u.bottom),u.left=L(d.left,u.left),u},Se(t,c,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function Wn(e){const{width:t,height:n}=Le(e);return{width:t,height:n}}function Nn(e,t,n){const i=q(t),o=Z(t),r=n==="fixed",s=at(e,!0,r,t);let c={scrollLeft:0,scrollTop:0};const a=j(0);function u(){a.x=me(o)}if(i||!i&&!r)if((ht(t)!=="body"||Kt(o))&&(c=ie(t)),i){const f=at(t,!0,r,t);a.x=f.x+t.clientLeft,a.y=f.y+t.clientTop}else o&&u();r&&!i&&o&&u();const l=o&&!i&&!r?Ie(o,c):j(0),d=s.left+c.scrollLeft-a.x-l.x,g=s.top+c.scrollTop-a.y-l.y;return{x:d,y:g,width:s.width,height:s.height}}function oe(e){return K(e).position==="static"}function Ce(e,t){if(!q(e)||K(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Z(e)===n&&(n=n.ownerDocument.body),n}function We(e,t){const n=k(e);if(ne(e))return n;if(!q(e)){let o=rt(e);for(;o&&!ut(o);){if(z(o)&&!oe(o))return o;o=rt(o)}return n}let i=Ce(e,t);for(;i&&Fn(i)&&oe(i);)i=Ce(i,t);return i&&ut(i)&&oe(i)&&!fe(i)?n:i||En(e)||n}const Vn=async function(e){const t=this.getOffsetParent||We,n=this.getDimensions,i=await n(e.floating);return{reference:Nn(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}};function Hn(e){return K(e).direction==="rtl"}const zn={convertOffsetParentRelativeRectToViewportRelativeRect:Bn,getDocumentElement:Z,getClippingRect:Mn,getOffsetParent:We,getElementRects:Vn,getClientRects:Tn,getDimensions:Wn,getScale:lt,isElement:z,isRTL:Hn};function Ne(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Kn(e,t){let n=null,i;const o=Z(e);function r(){var c;clearTimeout(i),(c=n)==null||c.disconnect(),n=null}function s(c,a){c===void 0&&(c=!1),a===void 0&&(a=1),r();const u=e.getBoundingClientRect(),{left:l,top:d,width:g,height:f}=u;if(c||t(),!g||!f)return;const h=Yt(d),w=Yt(o.clientWidth-(l+g)),y=Yt(o.clientHeight-(d+f)),p=Yt(l),b={rootMargin:-h+"px "+-w+"px "+-y+"px "+-p+"px",threshold:L(0,ot(1,a))||1};let A=!0;function x(R){const D=R[0].intersectionRatio;if(D!==a){if(!A)return s();D?s(!1,D):i=setTimeout(()=>{s(!1,1e-7)},1e3)}D===1&&!Ne(u,e.getBoundingClientRect())&&s(),A=!1}try{n=new IntersectionObserver(x,{...b,root:o.ownerDocument})}catch{n=new IntersectionObserver(x,b)}n.observe(e)}return s(!0),r}function Yn(e,t,n,i){i===void 0&&(i={});const{ancestorScroll:o=!0,ancestorResize:r=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:a=!1}=i,u=ge(e),l=o||r?[...u?wt(u):[],...wt(t)]:[];l.forEach(p=>{o&&p.addEventListener("scroll",n,{passive:!0}),r&&p.addEventListener("resize",n)});const d=u&&c?Kn(u,n):null;let g=-1,f=null;s&&(f=new ResizeObserver(p=>{let[O]=p;O&&O.target===u&&f&&(f.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var b;(b=f)==null||b.observe(t)})),n()}),u&&!a&&f.observe(u),f.observe(t));let h,w=a?at(e):null;a&&y();function y(){const p=at(e);w&&!Ne(w,p)&&n(),w=p,h=requestAnimationFrame(y)}return n(),()=>{var p;l.forEach(O=>{o&&O.removeEventListener("scroll",n),r&&O.removeEventListener("resize",n)}),d==null||d(),(p=f)==null||p.disconnect(),f=null,a&&cancelAnimationFrame(h)}}const Un=An,Xn=Pn,jn=xn,qn=Cn,Zn=bn,Jn=vn,Qn=Sn,Gn=(e,t,n)=>{const i=new Map,o={platform:zn,...n},r={...o.platform,_c:i};return pn(e,t,{...o,platform:r})};function gt(e){return typeof e=="function"?e():e}function Ve(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Fe(e,t){const n=Ve(e);return Math.round(t*n)/n}function Ei(e){return{[`--bits-${e}-content-transform-origin`]:"var(--bits-floating-transform-origin)",[`--bits-${e}-content-available-width`]:"var(--bits-floating-available-width)",[`--bits-${e}-content-available-height`]:"var(--bits-floating-available-height)",[`--bits-${e}-anchor-width`]:"var(--bits-floating-anchor-width)",[`--bits-${e}-anchor-height`]:"var(--bits-floating-anchor-height)"}}function $n(e){const t=e.whileElementsMounted,n=E(()=>gt(e.open)??!0),i=E(()=>gt(e.middleware)),o=E(()=>gt(e.transform)??!0),r=E(()=>gt(e.placement)??"bottom"),s=E(()=>gt(e.strategy)??"absolute"),c=e.reference;let a=N(0),u=N(0);const l=S(null);let d=N(Ut(v(s))),g=N(Ut(v(r))),f=N(Ut({})),h=N(!1);const w=E(()=>{const x={position:v(d),left:"0",top:"0"};if(!l.current)return x;const R=Fe(l.current,v(a)),D=Fe(l.current,v(u));return v(o)?{...x,transform:`translate(${R}px, ${D}px)`,...Ve(l.current)>=1.5&&{willChange:"transform"}}:{position:v(d),left:`${R}px`,top:`${D}px`}});let y;function p(){c.current===null||l.current===null||Gn(c.current,l.current,{middleware:v(i),placement:v(r),strategy:v(s)}).then(x=>{F(a,x.x,!0),F(u,x.y,!0),F(d,x.strategy,!0),F(g,x.placement,!0),F(f,x.middlewareData,!0),F(h,!0)})}function O(){typeof y=="function"&&(y(),y=void 0)}function b(){if(O(),t===void 0){p();return}c.current===null||l.current===null||(y=t(c.current,l.current,p))}function A(){v(n)||F(h,!1)}return it(p),it(b),it(A),it(()=>O),{floating:l,reference:c,get strategy(){return v(d)},get placement(){return v(g)},get middlewareData(){return v(f)},get isPositioned(){return v(h)},get floatingStyles(){return v(w)},get update(){return p}}}const ti={top:"bottom",right:"left",bottom:"top",left:"right"};class ei{constructor(){B(this,"anchorNode",S(null));B(this,"customAnchorNode",S(null));B(this,"triggerNode",S(null));it(()=>{this.customAnchorNode.current?typeof this.customAnchorNode.current=="string"?this.anchorNode.current=document.querySelector(this.customAnchorNode.current):this.anchorNode.current=this.customAnchorNode.current:this.anchorNode.current=this.triggerNode.current})}}var yt,Qt,pt,Gt,vt,$t,xt,bt,Ot,At,Pt,St,Ct,Ft,Et,Rt,Dt,Bt,Tt,_t,Lt,kt,It,Mt;class ni{constructor(t,n){B(this,"opts");B(this,"root");B(this,"contentRef",S(null));B(this,"wrapperRef",S(null));B(this,"arrowRef",S(null));B(this,"arrowId",S(De()));C(this,yt,E(()=>{if(typeof this.opts.style=="string")return Qe(this.opts.style);if(!this.opts.style)return{}}));C(this,Qt);C(this,pt,new ln(()=>this.arrowRef.current??void 0));C(this,Gt,E(()=>{var t;return((t=m(this,pt))==null?void 0:t.width)??0}));C(this,vt,E(()=>{var t;return((t=m(this,pt))==null?void 0:t.height)??0}));C(this,$t,E(()=>{var t;return((t=this.opts.side)==null?void 0:t.current)+(this.opts.align.current!=="center"?`-${this.opts.align.current}`:"")}));C(this,xt,E(()=>Array.isArray(this.opts.collisionBoundary.current)?this.opts.collisionBoundary.current:[this.opts.collisionBoundary.current]));C(this,bt,E(()=>v(m(this,xt)).length>0));C(this,Ot,E(()=>({padding:this.opts.collisionPadding.current,boundary:v(m(this,xt)).filter(sn),altBoundary:this.hasExplicitBoundaries})));C(this,At,N(void 0));C(this,Pt,N(void 0));C(this,St,N(void 0));C(this,Ct,N(void 0));C(this,Ft,E(()=>[Un({mainAxis:this.opts.sideOffset.current+v(m(this,vt)),alignmentAxis:this.opts.alignOffset.current}),this.opts.avoidCollisions.current&&Xn({mainAxis:!0,crossAxis:!1,limiter:this.opts.sticky.current==="partial"?Qn():void 0,...this.detectOverflowOptions}),this.opts.avoidCollisions.current&&jn({...this.detectOverflowOptions}),qn({...this.detectOverflowOptions,apply:({rects:t,availableWidth:n,availableHeight:i})=>{const{width:o,height:r}=t.reference;F(m(this,At),n,!0),F(m(this,Pt),i,!0),F(m(this,St),o,!0),F(m(this,Ct),r,!0)}}),this.arrowRef.current&&Jn({element:this.arrowRef.current,padding:this.opts.arrowPadding.current}),ai({arrowWidth:v(m(this,Gt)),arrowHeight:v(m(this,vt))}),this.opts.hideWhenDetached.current&&Zn({strategy:"referenceHidden",...this.detectOverflowOptions})].filter(Boolean)));B(this,"floating");C(this,Et,E(()=>li(this.floating.placement)));C(this,Rt,E(()=>ui(this.floating.placement)));C(this,Dt,E(()=>{var t;return((t=this.floating.middlewareData.arrow)==null?void 0:t.x)??0}));C(this,Bt,E(()=>{var t;return((t=this.floating.middlewareData.arrow)==null?void 0:t.y)??0}));C(this,Tt,E(()=>{var t;return((t=this.floating.middlewareData.arrow)==null?void 0:t.centerOffset)!==0}));C(this,_t,N());C(this,Lt,E(()=>ti[this.placedSide]));C(this,kt,E(()=>{var t,n,i;return{id:this.opts.wrapperId.current,"data-bits-floating-content-wrapper":"",style:{...this.floating.floatingStyles,transform:this.floating.isPositioned?this.floating.floatingStyles.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:this.contentZIndex,"--bits-floating-transform-origin":`${(t=this.floating.middlewareData.transformOrigin)==null?void 0:t.x} ${(n=this.floating.middlewareData.transformOrigin)==null?void 0:n.y}`,"--bits-floating-available-width":`${v(m(this,At))}px`,"--bits-floating-available-height":`${v(m(this,Pt))}px`,"--bits-floating-anchor-width":`${v(m(this,St))}px`,"--bits-floating-anchor-height":`${v(m(this,Ct))}px`,...((i=this.floating.middlewareData.hide)==null?void 0:i.referenceHidden)&&{visibility:"hidden","pointer-events":"none"},...v(m(this,yt))},dir:this.opts.dir.current}}));C(this,It,E(()=>({"data-side":this.placedSide,"data-align":this.placedAlign,style:Ge({...v(m(this,yt))})})));C(this,Mt,E(()=>({position:"absolute",left:this.arrowX?`${this.arrowX}px`:void 0,top:this.arrowY?`${this.arrowY}px`:void 0,[this.arrowBaseSide]:0,"transform-origin":{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[this.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[this.placedSide],visibility:this.cannotCenterArrow?"hidden":void 0})));this.opts=t,this.root=n,t.customAnchor&&(this.root.customAnchorNode.current=t.customAnchor.current),ve(()=>t.customAnchor.current,i=>{this.root.customAnchorNode.current=i}),Xt({id:this.opts.wrapperId,ref:this.wrapperRef,deps:()=>this.opts.enabled.current}),Xt({id:this.opts.id,ref:this.contentRef,deps:()=>this.opts.enabled.current}),this.floating=$n({strategy:()=>this.opts.strategy.current,placement:()=>v(m(this,$t)),middleware:()=>this.middleware,reference:this.root.anchorNode,whileElementsMounted:(...i)=>{var r;return Yn(...i,{animationFrame:((r=m(this,Qt))==null?void 0:r.current)==="always"})},open:()=>this.opts.enabled.current}),it(()=>{var i;this.floating.isPositioned&&((i=this.opts.onPlaced)==null||i.current())}),ve(()=>this.contentRef.current,i=>{i&&(this.contentZIndex=window.getComputedStyle(i).zIndex)}),it(()=>{this.floating.floating.current=this.wrapperRef.current})}get hasExplicitBoundaries(){return v(m(this,bt))}set hasExplicitBoundaries(t){F(m(this,bt),t)}get detectOverflowOptions(){return v(m(this,Ot))}set detectOverflowOptions(t){F(m(this,Ot),t)}get middleware(){return v(m(this,Ft))}set middleware(t){F(m(this,Ft),t)}get placedSide(){return v(m(this,Et))}set placedSide(t){F(m(this,Et),t)}get placedAlign(){return v(m(this,Rt))}set placedAlign(t){F(m(this,Rt),t)}get arrowX(){return v(m(this,Dt))}set arrowX(t){F(m(this,Dt),t)}get arrowY(){return v(m(this,Bt))}set arrowY(t){F(m(this,Bt),t)}get cannotCenterArrow(){return v(m(this,Tt))}set cannotCenterArrow(t){F(m(this,Tt),t)}get contentZIndex(){return v(m(this,_t))}set contentZIndex(t){F(m(this,_t),t,!0)}get arrowBaseSide(){return v(m(this,Lt))}set arrowBaseSide(t){F(m(this,Lt),t)}get wrapperProps(){return v(m(this,kt))}set wrapperProps(t){F(m(this,kt),t)}get props(){return v(m(this,It))}set props(t){F(m(this,It),t)}get arrowStyle(){return v(m(this,Mt))}set arrowStyle(t){F(m(this,Mt),t)}}yt=new WeakMap,Qt=new WeakMap,pt=new WeakMap,Gt=new WeakMap,vt=new WeakMap,$t=new WeakMap,xt=new WeakMap,bt=new WeakMap,Ot=new WeakMap,At=new WeakMap,Pt=new WeakMap,St=new WeakMap,Ct=new WeakMap,Ft=new WeakMap,Et=new WeakMap,Rt=new WeakMap,Dt=new WeakMap,Bt=new WeakMap,Tt=new WeakMap,_t=new WeakMap,Lt=new WeakMap,kt=new WeakMap,It=new WeakMap,Mt=new WeakMap;var Wt;class ii{constructor(t,n){B(this,"opts");B(this,"content");C(this,Wt,E(()=>({id:this.opts.id.current,style:this.content.arrowStyle,"data-side":this.content.placedSide})));this.opts=t,this.content=n,Xt({...t,onRefChange:i=>{this.content.arrowRef.current=i},deps:()=>this.content.opts.enabled.current})}get props(){return v(m(this,Wt))}set props(t){F(m(this,Wt),t)}}Wt=new WeakMap;class oi{constructor(t,n){B(this,"opts");B(this,"root");B(this,"ref",S(null));this.opts=t,this.root=n,t.virtualEl&&t.virtualEl.current?n.triggerNode=S.from(t.virtualEl.current):Xt({id:t.id,ref:this.ref,onRefChange:i=>{n.triggerNode.current=i}})}}const we=new Re("Floating.Root"),He=new Re("Floating.Content");function ri(){return we.set(new ei)}function si(e){return He.set(new ni(e,we.get()))}function Ri(e){return new ii(e,He.get())}function ci(e){return new oi(e,we.get())}function ai(e){return{name:"transformOrigin",options:e,fn(t){var y,p,O;const{placement:n,rects:i,middlewareData:o}=t,s=((y=o.arrow)==null?void 0:y.centerOffset)!==0,c=s?0:e.arrowWidth,a=s?0:e.arrowHeight,[u,l]=ye(n),d={start:"0%",center:"50%",end:"100%"}[l],g=(((p=o.arrow)==null?void 0:p.x)??0)+c/2,f=(((O=o.arrow)==null?void 0:O.y)??0)+a/2;let h="",w="";return u==="bottom"?(h=s?d:`${g}px`,w=`${-a}px`):u==="top"?(h=s?d:`${g}px`,w=`${i.floating.height+a}px`):u==="right"?(h=`${-a}px`,w=s?d:`${f}px`):u==="left"&&(h=`${i.floating.width+a}px`,w=s?d:`${f}px`),{data:{x:h,y:w}}}}}function ye(e){const[t,n="center"]=e.split("-");return[t,n]}function li(e){return ye(e)[0]}function ui(e){return ye(e)[1]}function Di(e,t){Nt(t,!0),ri();var n=dt(),i=ct(n);zt(i,()=>t.children??Ht),st(e,n),Vt()}function Bi(e,t){Nt(t,!0),ci({id:S.with(()=>t.id),virtualEl:S.with(()=>t.virtualEl)});var n=dt(),i=ct(n);zt(i,()=>t.children??Ht),st(e,n),Vt()}function di(e,t){Nt(t,!0);let n=P(t,"side",3,"bottom"),i=P(t,"sideOffset",3,0),o=P(t,"align",3,"center"),r=P(t,"alignOffset",3,0),s=P(t,"arrowPadding",3,0),c=P(t,"avoidCollisions",3,!0),a=P(t,"collisionBoundary",19,()=>[]),u=P(t,"collisionPadding",3,0),l=P(t,"hideWhenDetached",3,!1),d=P(t,"onPlaced",3,()=>{}),g=P(t,"sticky",3,"partial"),f=P(t,"updatePositionStrategy",3,"optimized"),h=P(t,"strategy",3,"fixed"),w=P(t,"dir",3,"ltr"),y=P(t,"style",19,()=>({})),p=P(t,"wrapperId",19,De),O=P(t,"customAnchor",3,null);const b=si({side:S.with(()=>n()),sideOffset:S.with(()=>i()),align:S.with(()=>o()),alignOffset:S.with(()=>r()),id:S.with(()=>t.id),arrowPadding:S.with(()=>s()),avoidCollisions:S.with(()=>c()),collisionBoundary:S.with(()=>a()),collisionPadding:S.with(()=>u()),hideWhenDetached:S.with(()=>l()),onPlaced:S.with(()=>d()),sticky:S.with(()=>g()),updatePositionStrategy:S.with(()=>f()),strategy:S.with(()=>h()),dir:S.with(()=>w()),style:S.with(()=>y()),enabled:S.with(()=>t.enabled),wrapperId:S.with(()=>p()),customAnchor:S.with(()=>O())}),A=E(()=>Ee(b.wrapperProps,{style:{pointerEvents:"auto"}}));var x=dt(),R=ct(x);zt(R,()=>t.content??Ht,()=>({props:b.props,wrapperProps:v(A)})),st(e,x),Vt()}function fi(e,t){Nt(t,!0),rn(()=>{var o;(o=t.onPlaced)==null||o.call(t)});var n=dt(),i=ct(n);zt(i,()=>t.content??Ht,()=>({props:{},wrapperProps:{}})),st(e,n),Vt()}function hi(e,t){let n=P(t,"isStatic",3,!1),i=te(t,["$$slots","$$events","$$legacy","content","isStatic","onPlaced"]);var o=dt(),r=ct(o);{var s=a=>{fi(a,{get content(){return t.content},get onPlaced(){return t.onPlaced}})},c=a=>{di(a,jt({get content(){return t.content},get onPlaced(){return t.onPlaced}},()=>i))};re(r,a=>{n()?a(s):a(c,!1)})}st(e,o)}var gi=je("<!> <!>",1);function ze(e,t){Nt(t,!0);let n=P(t,"interactOutsideBehavior",3,"close"),i=P(t,"trapFocus",3,!0),o=P(t,"isValidEvent",3,()=>!1),r=P(t,"customAnchor",3,null),s=P(t,"isStatic",3,!1),c=te(t,["$$slots","$$events","$$legacy","popper","onEscapeKeydown","escapeKeydownBehavior","preventOverflowTextSelection","id","onPointerDown","onPointerUp","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","strategy","dir","preventScroll","wrapperId","style","onPlaced","onInteractOutside","onCloseAutoFocus","onOpenAutoFocus","onFocusOutside","interactOutsideBehavior","loop","trapFocus","isValidEvent","customAnchor","isStatic","enabled"]);hi(e,{get isStatic(){return s()},get id(){return t.id},get side(){return t.side},get sideOffset(){return t.sideOffset},get align(){return t.align},get alignOffset(){return t.alignOffset},get arrowPadding(){return t.arrowPadding},get avoidCollisions(){return t.avoidCollisions},get collisionBoundary(){return t.collisionBoundary},get collisionPadding(){return t.collisionPadding},get sticky(){return t.sticky},get hideWhenDetached(){return t.hideWhenDetached},get updatePositionStrategy(){return t.updatePositionStrategy},get strategy(){return t.strategy},get dir(){return t.dir},get wrapperId(){return t.wrapperId},get style(){return t.style},get onPlaced(){return t.onPlaced},get customAnchor(){return r()},get enabled(){return t.enabled},content:(u,l)=>{let d=()=>l==null?void 0:l().props,g=()=>l==null?void 0:l().wrapperProps;var f=gi(),h=ct(f);{var w=b=>{xe(b,{get preventScroll(){return t.preventScroll}})},y=(b,A)=>{{var x=R=>{xe(R,{get preventScroll(){return t.preventScroll}})};re(b,R=>{t.forceMount||R(x)},A)}};re(h,b=>{t.forceMount&&t.enabled?b(w):b(y,!1)})}var p=qe(h,2);const O=E(()=>t.enabled&&i());tn(p,{get id(){return t.id},get onOpenAutoFocus(){return t.onOpenAutoFocus},get onCloseAutoFocus(){return t.onCloseAutoFocus},get loop(){return t.loop},get trapFocus(){return v(O)},get forceMount(){return t.forceMount},focusScope:(A,x)=>{let R=()=>x==null?void 0:x().props;en(A,{get onEscapeKeydown(){return t.onEscapeKeydown},get escapeKeydownBehavior(){return t.escapeKeydownBehavior},get enabled(){return t.enabled},children:(D,Y)=>{{const I=(T,M)=>{let W=()=>M==null?void 0:M().props;on(T,{get id(){return t.id},get preventOverflowTextSelection(){return t.preventOverflowTextSelection},get onPointerDown(){return t.onPointerDown},get onPointerUp(){return t.onPointerUp},get enabled(){return t.enabled},children:(U,V)=>{var _=dt(),J=ct(_),H=Ze(()=>({props:Ee(c,d(),W(),R(),{style:{pointerEvents:"auto"}}),wrapperProps:g()}));zt(J,()=>t.popper??Ht,()=>v(H)),st(U,_)},$$slots:{default:!0}})};nn(D,{get id(){return t.id},get onInteractOutside(){return t.onInteractOutside},get onFocusOutside(){return t.onFocusOutside},get interactOutsideBehavior(){return n()},isValidEvent:o(),get enabled(){return t.enabled},children:I,$$slots:{default:!0}})}},$$slots:{default:!0}})},$$slots:{focusScope:!0}}),st(u,f)},$$slots:{content:!0}}),Vt()}function Ti(e,t){let n=P(t,"interactOutsideBehavior",3,"close"),i=P(t,"trapFocus",3,!0),o=P(t,"isValidEvent",3,()=>!1),r=P(t,"customAnchor",3,null),s=P(t,"isStatic",3,!1),c=te(t,["$$slots","$$events","$$legacy","popper","present","onEscapeKeydown","escapeKeydownBehavior","preventOverflowTextSelection","id","onPointerDown","onPointerUp","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","strategy","dir","preventScroll","wrapperId","style","onPlaced","onInteractOutside","onCloseAutoFocus","onOpenAutoFocus","onFocusOutside","interactOutsideBehavior","loop","trapFocus","isValidEvent","customAnchor","isStatic"]);$e(e,jt({get id(){return t.id},get present(){return t.present}},()=>c,{presence:u=>{ze(u,jt({get popper(){return t.popper},get onEscapeKeydown(){return t.onEscapeKeydown},get escapeKeydownBehavior(){return t.escapeKeydownBehavior},get preventOverflowTextSelection(){return t.preventOverflowTextSelection},get id(){return t.id},get onPointerDown(){return t.onPointerDown},get onPointerUp(){return t.onPointerUp},get side(){return t.side},get sideOffset(){return t.sideOffset},get align(){return t.align},get alignOffset(){return t.alignOffset},get arrowPadding(){return t.arrowPadding},get avoidCollisions(){return t.avoidCollisions},get collisionBoundary(){return t.collisionBoundary},get collisionPadding(){return t.collisionPadding},get sticky(){return t.sticky},get hideWhenDetached(){return t.hideWhenDetached},get updatePositionStrategy(){return t.updatePositionStrategy},get strategy(){return t.strategy},get dir(){return t.dir},get preventScroll(){return t.preventScroll},get wrapperId(){return t.wrapperId},get style(){return t.style},get onPlaced(){return t.onPlaced},get customAnchor(){return r()},get isStatic(){return s()},get enabled(){return t.present},get onInteractOutside(){return t.onInteractOutside},get onCloseAutoFocus(){return t.onCloseAutoFocus},get onOpenAutoFocus(){return t.onOpenAutoFocus},get interactOutsideBehavior(){return n()},get loop(){return t.loop},get trapFocus(){return i()},isValidEvent:o(),get onFocusOutside(){return t.onFocusOutside},forceMount:!1},()=>c))},$$slots:{presence:!0}}))}function _i(e,t){let n=P(t,"interactOutsideBehavior",3,"close"),i=P(t,"trapFocus",3,!0),o=P(t,"isValidEvent",3,()=>!1),r=P(t,"customAnchor",3,null),s=P(t,"isStatic",3,!1),c=te(t,["$$slots","$$events","$$legacy","popper","onEscapeKeydown","escapeKeydownBehavior","preventOverflowTextSelection","id","onPointerDown","onPointerUp","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","strategy","dir","preventScroll","wrapperId","style","onPlaced","onInteractOutside","onCloseAutoFocus","onOpenAutoFocus","onFocusOutside","interactOutsideBehavior","loop","trapFocus","isValidEvent","customAnchor","isStatic","enabled"]);ze(e,jt({get popper(){return t.popper},get onEscapeKeydown(){return t.onEscapeKeydown},get escapeKeydownBehavior(){return t.escapeKeydownBehavior},get preventOverflowTextSelection(){return t.preventOverflowTextSelection},get id(){return t.id},get onPointerDown(){return t.onPointerDown},get onPointerUp(){return t.onPointerUp},get side(){return t.side},get sideOffset(){return t.sideOffset},get align(){return t.align},get alignOffset(){return t.alignOffset},get arrowPadding(){return t.arrowPadding},get avoidCollisions(){return t.avoidCollisions},get collisionBoundary(){return t.collisionBoundary},get collisionPadding(){return t.collisionPadding},get sticky(){return t.sticky},get hideWhenDetached(){return t.hideWhenDetached},get updatePositionStrategy(){return t.updatePositionStrategy},get strategy(){return t.strategy},get dir(){return t.dir},get preventScroll(){return t.preventScroll},get wrapperId(){return t.wrapperId},get style(){return t.style},get onPlaced(){return t.onPlaced},get customAnchor(){return r()},get isStatic(){return s()},get enabled(){return t.enabled},get onInteractOutside(){return t.onInteractOutside},get onCloseAutoFocus(){return t.onCloseAutoFocus},get onOpenAutoFocus(){return t.onOpenAutoFocus},get interactOutsideBehavior(){return n()},get loop(){return t.loop},get trapFocus(){return i()},isValidEvent:o(),get onFocusOutside(){return t.onFocusOutside}},()=>c,{forceMount:!0}))}export{Bi as F,_i as P,Ti as a,Di as b,Ei as g,Ri as u};
