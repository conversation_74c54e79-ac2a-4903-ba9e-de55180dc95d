import{f as p,t as le,a as l,c as Le}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as ye,l as Ve,h as We,b as qe,f as k,s,c as t,n as W,r as e,d as A,m as ue,g as r,t as Z,e as pe,a as be,o as ve}from"../chunks/CGmarHxI.js";import{s as F}from"../chunks/CIt1g2O9.js";import{i as K}from"../chunks/u21ee2wt.js";import{e as me,i as ge}from"../chunks/C3w0v0gR.js";import{c as Ee}from"../chunks/BvdI7LR8.js";import{i as he}from"../chunks/BIEMS98f.js";import{p as xe}from"../chunks/Btcx8l8F.js";import{R as Be,T as Ge}from"../chunks/I7hvcB12.js";import{S as ze}from"../chunks/C6g8ubaU.js";import{o as He}from"../chunks/nZgk9enP.js";import{e as oe,b as we,a as $e,s as Ke}from"../chunks/CmxjS0TN.js";import{C as Ie}from"../chunks/DuGukytH.js";import{C as Me}from"../chunks/Cdn-N1RY.js";import{R as ke,D as Se,P as Ce,a as De}from"../chunks/tdzGgazS.js";import{t as $}from"../chunks/DjPYYl4Z.js";import{a as Qe}from"../chunks/sDlmbjaf.js";import{L as Xe}from"../chunks/BvvicRXk.js";import{I as fe}from"../chunks/DMTMHyMa.js";import{P as Ne}from"../chunks/DR5zc253.js";import{I as Ze}from"../chunks/BuYRPDDz.js";import{D as Te,a as je,b as Ae,c as Fe}from"../chunks/CKh8VGVX.js";import{K as Oe,S as es,C as ss}from"../chunks/bEtmAhPN.js";import{F as ts}from"../chunks/CY_6SfHi.js";import{T as rs}from"../chunks/C33xR25f.js";import{a as os}from"../chunks/DDUgF6Ik.js";import"../chunks/BiJhC7W5.js";import{s as as}from"../chunks/B8blszX7.js";import{z as ns}from"../chunks/CrHU05dq.js";import{B as is}from"../chunks/B1K98fMG.js";import{L as Je}from"../chunks/DHNQRrgO.js";import{o as ls,s as Pe}from"../chunks/C8B1VUaq.js";import{G as ds}from"../chunks/D1zde6Ej.js";import{M as cs}from"../chunks/CwgkX8t9.js";import{C as us}from"../chunks/-SpbofVw.js";import{L as Re}from"../chunks/CHsAkgDv.js";import{M as vs}from"../chunks/2KCyzleV.js";import{L as ps}from"../chunks/w9xFoQXV.js";import{T as fs}from"../chunks/C88uNE8B.js";import{T as ms}from"../chunks/DmZyh-PW.js";var gs=p(`<div class="rounded-lg border border-dashed p-8 text-center"><!> <h3 class="mb-2 text-lg font-medium">No passkeys added yet</h3> <p class="text-muted-foreground mb-4 text-sm">Add a passkey to sign in without a password using your device's biometric authentication or
        PIN.</p> <div class="mx-auto mb-6 max-w-md"><div class="text-muted-foreground mb-2 text-sm font-medium">Benefits of passkeys:</div> <ul class="text-muted-foreground list-disc space-y-1 pl-6 text-left text-sm"><li>No passwords to remember or type</li> <li>More secure than passwords</li> <li>Can't be phished or stolen in data breaches</li> <li>Works across your devices</li></ul></div> <button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground mx-auto inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"><!> Add Your First Passkey</button></div>`),ys=p('<p class="text-muted-foreground text-xs"> </p>'),bs=p('<div class="flex w-full items-center gap-4"><!> <div><p class="font-medium"> </p> <p class="text-muted-foreground text-xs"> </p> <p class="text-muted-foreground text-xs"> </p> <!></div></div> <button type="button" class="text-destructive hover:text-destructive/90 hover:bg-destructive/10 inline-flex h-8 w-8 items-center justify-center rounded-md p-0"><!></button>',1),hs=p('<div class="space-y-4"></div>'),xs=p("<!> <!>",1),_s=p('<button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50">Cancel</button> <button type="button" class="bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"> </button>',1),ws=p('<!> <div class="grid gap-4 px-4"><div class="space-y-2"><!> <!> <p class="text-muted-foreground text-xs">Give your passkey a name to help you identify it later</p></div> <div class="bg-muted rounded-md p-3 text-sm"><p class="font-medium">What happens next?</p> <ul class="text-muted-foreground mt-2 list-disc pl-5 text-xs"><li>Your device will prompt you to create a passkey</li> <li>You may need to use your fingerprint, face, or device PIN</li> <li>This passkey will be stored securely on your current device</li></ul></div></div> <!>',1),$s=p("<!> <!>",1),Ps=p("<!> <!>",1),ks=p('<p class="text-muted-foreground text-xs"> </p>'),Ss=p('<div class="flex items-center gap-4 rounded-lg border p-4"><!> <div><p class="font-medium"> </p> <p class="text-muted-foreground text-xs"> </p> <!></div></div>'),Cs=p('<button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50">Cancel</button> <button type="button" class="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Remove Passkey</button>',1),Ds=p('<!> <div class="py-4"><!></div> <!>',1),Ts=p("<!> <!>",1),js=p(`<div class="border-border mb-4 flex items-center justify-between border-b p-4"><div class="flex flex-col"><h4 class="text-md font-normal">Passkeys</h4> <p class="text-muted-foreground text-sm">Manage passkeys for passwordless sign-in.</p></div> <button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"><!> Add Passkey</button></div> <div class="space-y-6 p-6 pt-0"><!> <div class="bg-muted/40 mt-12 rounded-lg border p-4"><div class="flex items-start gap-4"><!> <div><h3 class="font-medium">About Passkeys</h3> <p class="text-muted-foreground text-sm">Passkeys are a more secure alternative to passwords. They use biometric authentication
          (like fingerprint or face recognition) or a device PIN to sign you in without having to
          remember or type a password.</p> <p class="text-muted-foreground mt-2 text-sm">Your passkey is stored securely on your device and can't be phished or stolen in a data
          breach.</p> <p class="text-muted-foreground mt-2 text-sm"><strong>Tip:</strong> Add passkeys to multiple devices to ensure you can always sign in, even
          if one device is lost or unavailable.</p> <p class="text-muted-foreground mt-2 text-sm"><a href="https://passkeys.com/what-are-passkeys/" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">Learn more about passkeys</a></p></div></div></div></div> <!> <!>`,1);function As(de,se){ye(se,!1);let v=xe(se,"passkeys",28,()=>[]);He(()=>{console.log("Passkeys component mounted with passkeys:",v())});let N=ue(!1),m=ue(null),R=ue(!1),H=ue(""),Q=ue(!1);async function ae(){if(!r(H)){$.error("Please provide a name for your passkey");return}A(Q,!0);try{console.log("Fetching registration options...");const n=await fetch("/api/passkeys?action=getRegistrationOptions",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:r(H)})});if(!n.ok){const a=await n.text();throw console.error("Registration options error:",a),new Error(`Failed to get registration options: ${n.status} ${a}`)}const d=await n.json();console.log("Registration options received:",d),console.log("Starting registration in browser...");try{console.log("Starting registration with options:",d);const a=await Qe(d);console.log("Registration response:",JSON.stringify(a,null,2)),console.log("Client challenge:",a.response.clientDataJSON);try{const w=JSON.parse(atob(a.response.clientDataJSON));console.log("Decoded clientDataJSON:",w),console.log("Challenge from client:",w.challenge)}catch(w){console.error("Error decoding clientDataJSON:",w)}console.log("Sending verification request...");const o=await fetch("/api/passkeys?action=verifyRegistration",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:r(H),registrationResponse:a})});if(!o.ok){let w="";try{const h=await o.json();console.error("Verification error:",h),w=JSON.stringify(h)}catch{w=await o.text(),console.error("Verification error (text):",w)}throw new Error(`Failed to verify registration: ${o.status} ${w}`)}const y=await o.json();console.log("Verification result:",y),y.success?($.success("Passkey added successfully"),v(y.passkeys),A(N,!1),A(H,"")):$.error(y.error||"Failed to add passkey")}catch(a){a.name==="NotAllowedError"?console.log("Passkey operation was canceled or timed out"):a.name==="AbortError"?console.log("Passkey operation was aborted"):a.name==="InvalidStateError"?$.error("The passkey already exists on this device"):a.name==="SecurityError"?$.error("Security error during passkey operation"):a.name==="ConstraintError"?$.error("Your device may not support passkeys or has reached its limit"):a.name==="NotSupportedError"?$.error("Your browser or device does not support passkeys"):(console.error("WebAuthn error:",a),$.error(a.message||"Failed to create passkey"))}}catch(n){console.error("Passkey registration error:",n),$.error(n.message||"Failed to register passkey")}finally{A(Q,!1)}}async function ne(n){try{console.log("Removing passkey with ID:",n);const d=await fetch("/api/passkeys?action=removePasskey",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({passkeyId:n})});if(!d.ok){let o="";try{const y=await d.json();console.error("Remove passkey error:",y),o=JSON.stringify(y)}catch{o=await d.text(),console.error("Remove passkey error (text):",o)}throw new Error(`Failed to remove passkey: ${d.status} ${o}`)}const a=await d.json();a.success?($.success("Passkey removed successfully"),v(a.passkeys),A(R,!1)):$.error(a.error||"Failed to remove passkey")}catch(d){console.error("Error removing passkey:",d),$.error(d.message||"Failed to remove passkey")}}function q(n){if(!n)return"Unknown date";try{const d=new Date(n);return d.toLocaleDateString()+" "+d.toLocaleTimeString()}catch(d){return console.error("Error formatting date:",d),"Invalid date"}}Ve(()=>We(v()),()=>{v()?Array.isArray(v())?(console.log("Passkeys component has",v().length,"passkeys"),v().length>0&&console.log("First passkey:",v()[0])):(console.error("Passkeys is not an array:",v()),v([])):(console.error("Passkeys is null or undefined"),v([]))}),qe(),he();var ie=js(),ee=k(ie),te=s(t(ee),2),ce=t(te);Ne(ce,{class:"h-4 w-4"}),W(),e(te),e(ee);var u=s(ee,2),c=t(u);{var g=n=>{var d=gs(),a=t(d);ts(a,{class:"text-muted-foreground mx-auto mb-4 h-10 w-10"});var o=s(a,8),y=t(o);Ne(y,{class:"h-4 w-4"}),W(),e(o),e(d),oe("click",o,()=>A(N,!0)),l(n,d)},E=n=>{var d=hs();me(d,5,v,ge,(a,o)=>{Ie(a,{children:(y,w)=>{Me(y,{class:"flex items-center justify-between rounded-lg",children:(h,X)=>{var U=bs(),S=k(U),M=t(S);Oe(M,{class:"text-primary h-8 w-8"});var j=s(M,2),i=t(j),f=t(i,!0);e(i);var O=s(i,2),B=t(O);e(O);var C=s(O,2),Y=t(C);e(C);var D=s(C,2);{var L=z=>{var b=ys(),V=t(b);e(b),Z(re=>F(V,`ID: ${re??""}`),[()=>typeof r(o).id=="string"&&r(o).id.length>10?r(o).id.substring(0,10)+"...":r(o).id],pe),l(z,b)};K(D,z=>{r(o).id&&z(L)})}e(j),e(S);var P=s(S,2),G=t(P);rs(G,{class:"h-4 w-4"}),e(P),Z((z,b)=>{F(f,r(o).name||"Unnamed Passkey"),F(B,`Created: ${z??""}`),F(Y,`Last used: ${b??""}`)},[()=>q(r(o).createdAt),()=>q(r(o).lastUsed)],pe),oe("click",P,()=>{console.log("Setting passkey to remove:",r(o)),A(m,r(o)),A(R,!0)}),l(h,U)},$$slots:{default:!0}})},$$slots:{default:!0}})}),e(d),l(n,d)};K(c,n=>{v().length===0?n(g):n(E,!1)})}var x=s(c,2),_=t(x),J=t(_);Ze(J,{class:"text-primary mt-1 h-6 w-6"}),W(2),e(_),e(x),e(u);var I=s(u,2);ke(I,{get open(){return r(N)},set open(n){A(N,n)},children:(n,d)=>{var a=$s(),o=k(a);Se(o,{});var y=s(o,2);Ce(y,{children:(w,h)=>{De(w,{class:"p-0 sm:max-w-[425px]",children:(X,U)=>{var S=ws(),M=k(S);Te(M,{class:"border-border gap-1 border-b p-4",children:(C,Y)=>{var D=xs(),L=k(D);je(L,{children:(G,z)=>{W();var b=le("Add Passkey");l(G,b)},$$slots:{default:!0}});var P=s(L,2);Ae(P,{children:(G,z)=>{W();var b=le("Create a new passkey for passwordless sign-in.");l(G,b)},$$slots:{default:!0}}),l(C,D)},$$slots:{default:!0}});var j=s(M,2),i=t(j),f=t(i);Xe(f,{for:"passkey-name",class:"text-sm font-medium leading-none",children:(C,Y)=>{W();var D=le("Passkey Name");l(C,D)},$$slots:{default:!0}});var O=s(f,2);fe(O,{id:"passkey-name",type:"text",placeholder:"e.g., Work Laptop, Personal Phone",class:"border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm",get value(){return r(H)},set value(C){A(H,C)},$$legacy:!0}),W(2),e(i),W(2),e(j);var B=s(j,2);Fe(B,{class:"border-border border-t p-2",children:(C,Y)=>{var D=_s(),L=k(D),P=s(L,2),G=t(P,!0);e(P),Z(()=>{L.disabled=r(Q),P.disabled=r(Q),F(G,r(Q)?"Creating...":"Create Passkey")}),oe("click",L,()=>{r(Q)?$.error("Please wait for passkey registration to complete"):A(N,!1)}),oe("click",P,ae),l(C,D)},$$slots:{default:!0}}),l(X,S)},$$slots:{default:!0}})},$$slots:{default:!0}}),l(n,a)},$$slots:{default:!0},$$legacy:!0});var T=s(I,2);ke(T,{get open(){return r(R)},set open(n){A(R,n)},children:(n,d)=>{var a=Ts(),o=k(a);Se(o,{});var y=s(o,2);Ce(y,{children:(w,h)=>{De(w,{class:"sm:max-w-[425px]",children:(X,U)=>{var S=Ds(),M=k(S);Te(M,{children:(B,C)=>{var Y=Ps(),D=k(Y);je(D,{children:(P,G)=>{W();var z=le("Remove Passkey");l(P,z)},$$slots:{default:!0}});var L=s(D,2);Ae(L,{children:(P,G)=>{W();var z=le("Are you sure you want to remove this passkey? This action cannot be undone.");l(P,z)},$$slots:{default:!0}}),l(B,Y)},$$slots:{default:!0}});var j=s(M,2),i=t(j);{var f=B=>{var C=Ss(),Y=t(C);Oe(Y,{class:"text-primary h-6 w-6"});var D=s(Y,2),L=t(D),P=t(L,!0);e(L);var G=s(L,2),z=t(G);e(G);var b=s(G,2);{var V=re=>{var _e=ks(),Ue=t(_e);e(_e),Z(Ye=>F(Ue,`ID: ${Ye??""}`),[()=>typeof r(m).id=="string"&&r(m).id.length>10?r(m).id.substring(0,10)+"...":r(m).id],pe),l(re,_e)};K(b,re=>{r(m).id&&re(V)})}e(D),e(C),Z(re=>{F(P,r(m).name||"Unnamed Passkey"),F(z,`Created: ${re??""}`)},[()=>q(r(m).createdAt)],pe),l(B,C)};K(i,B=>{r(m)&&B(f)})}e(j);var O=s(j,2);Fe(O,{children:(B,C)=>{var Y=Cs(),D=k(Y),L=s(D,2);oe("click",D,()=>A(R,!1)),oe("click",L,()=>{if(r(m)){const P=r(m).credentialID||r(m).id;console.log("Removing passkey with ID from dialog:",P),ne(P)}}),l(B,Y)},$$slots:{default:!0}}),l(X,S)},$$slots:{default:!0}})},$$slots:{default:!0}}),l(n,a)},$$slots:{default:!0},$$legacy:!0}),oe("click",te,()=>A(N,!0)),l(de,ie),be()}var Fs=p('<p class="text-destructive text-sm"> </p>'),Os=p('<p class="text-destructive text-sm"> </p>'),Ls=p('<p class="text-destructive text-sm"> </p>'),Ns=p(`<form method="POST" action="?/changePassword"><div class="flex flex-col justify-between"><div class="border-border flex flex-col border-b p-4"><h4 class="text-md font-normal">Change Password</h4> <p class="text-muted-foreground text-sm">Update your account password.</p></div> <div class="flex flex-col gap-6 p-4"><div class="space-y-4"><div class="space-y-2"><label for="currentPassword" class="text-sm font-medium leading-none">Current Password</label> <!> <!></div> <div class="space-y-2"><label for="newPassword" class="text-sm font-medium leading-none">New Password</label> <!> <!></div> <div class="space-y-2"><label for="confirmPassword" class="text-sm font-medium leading-none">Confirm New Password</label> <!> <!></div></div> <div class="bg-muted/40 rounded-lg border p-4"><div class="flex items-start gap-4"><!> <div><h4 class="font-medium">Password Security Tips</h4> <ul class="text-muted-foreground mt-2 list-disc space-y-1 pl-5 text-xs"><li>Use at least 8 characters</li> <li>Include uppercase and lowercase letters</li> <li>Add numbers and special characters</li> <li>Avoid using personal information</li> <li>Don't reuse passwords across different sites</li></ul></div></div></div></div> <div class="flex justify-end p-6"><!></div></div></form>`);function Rs(de,se){ye(se,!1);const[v,N]=Ke(),m=()=>$e(q,"$formData",v),R=()=>$e(ee,"$errors",v),H=()=>$e(te,"$submitting",v),Q=ls({currentPassword:Pe().min(1,"Current password is required"),newPassword:Pe().min(8,"Password must be at least 8 characters"),confirmPassword:Pe().min(1,"Please confirm your password")}).refine(i=>i.newPassword===i.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});let ae=xe(se,"passwordForm",8);const ne=as(ae(),{validators:ns(Q),dataType:"json",onUpdated:({form:i,result:f})=>{i.valid&&f.type==="success"&&($.success("Password updated successfully"),ce())},onError:()=>{$.error("Failed to update password")}}),{form:q,enhance:ie,errors:ee,submitting:te}=ne;function ce(){q.update(i=>({...i,currentPassword:"",newPassword:"",confirmPassword:""}))}he();var u=Ns(),c=t(u),g=s(t(c),2),E=t(g),x=t(E),_=s(t(x),2);fe(_,{id:"currentPassword",type:"password",placeholder:"Enter your current password",get value(){return m().currentPassword},set value(i){we(q,ve(m).currentPassword=i,ve(m))},$$legacy:!0});var J=s(_,2);{var I=i=>{var f=Fs(),O=t(f,!0);e(f),Z(()=>F(O,R().currentPassword)),l(i,f)};K(J,i=>{R().currentPassword&&i(I)})}e(x);var T=s(x,2),n=s(t(T),2);fe(n,{id:"newPassword",type:"password",placeholder:"Enter your new password",get value(){return m().newPassword},set value(i){we(q,ve(m).newPassword=i,ve(m))},$$legacy:!0});var d=s(n,2);{var a=i=>{var f=Os(),O=t(f,!0);e(f),Z(()=>F(O,R().newPassword)),l(i,f)};K(d,i=>{R().newPassword&&i(a)})}e(T);var o=s(T,2),y=s(t(o),2);fe(y,{id:"confirmPassword",type:"password",placeholder:"Confirm your new password",get value(){return m().confirmPassword},set value(i){we(q,ve(m).confirmPassword=i,ve(m))},$$legacy:!0});var w=s(y,2);{var h=i=>{var f=Ls(),O=t(f,!0);e(f),Z(()=>F(O,R().confirmPassword)),l(i,f)};K(w,i=>{R().confirmPassword&&i(h)})}e(o),e(E);var X=s(E,2),U=t(X),S=t(U);Je(S,{class:"text-primary mt-1 h-5 w-5"}),W(2),e(U),e(X),e(g);var M=s(g,2),j=t(M);is(j,{type:"submit",get disabled(){return H()},children:(i,f)=>{W();var O=le();Z(()=>F(O,H()?"Updating...":"Update Password")),l(i,O)},$$slots:{default:!0}}),e(M),e(c),e(u),os(u,i=>ie==null?void 0:ie(i)),l(de,u),be(),N()}var Es=p('<button class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"><!> Log Out All Other Sessions</button>'),Is=p('<span class="bg-success/20 text-success ml-2 rounded-full px-2 py-0.5 text-xs font-medium">Current</span>'),Ms=p('<div class="text-muted-foreground text-xs"> </div>'),Js=p('<button class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-2 rounded-md border px-3 text-xs font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"><!> Log Out</button>'),Us=p('<div class="flex items-start gap-4"><!> <div class="space-y-1"><div class="flex items-center"><p class="font-medium"> </p> <!></div> <div class="text-muted-foreground flex items-center gap-4 text-sm"><div class="flex items-center gap-1"><!> <span> </span></div> <div class="flex items-center gap-1"><!> <span> </span></div></div> <div class="text-muted-foreground flex items-center gap-1 text-xs"><!> <span> </span></div> <!></div></div> <!>',1),Ys=p("<!> <!>",1),Vs=p('<button type="button" class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Cancel</button> <button type="button" class="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Log Out All Other Sessions</button>',1),Ws=p('<!> <div class="py-4"><p class="text-muted-foreground text-sm"> </p></div> <!>',1),qs=p("<!> <!>",1),Bs=p('<div class="border-border flex flex-col justify-between border-b p-4"><div class="flex flex-col"><h4 class="text-md font-normal">Active Sessions</h4> <p class="text-muted-foreground text-sm">Manage your active sessions across devices.</p></div> <!></div> <div class="p-4"><div class="space-y-4"></div></div> <!>',1);function Gs(de,se){ye(se,!1);let v=xe(se,"sessions",28,()=>[]),N=ue(!1);async function m(u){try{const c=v().find(_=>_.isCurrent);if(c&&c.id===u){$.error("Cannot log out of current session using this method");return}const g=new FormData;g.append("sessionId",u);const x=await(await fetch("?/logoutSession",{method:"POST",body:g,headers:{Accept:"application/json"}})).json();x.type==="success"?($.success("Session logged out successfully"),v(v().filter(_=>_.id!==u))):$.error(x.error||"Failed to log out session")}catch(c){console.error("Error logging out session:",c),$.error("Failed to log out session")}}async function R(){try{const u=new FormData,g=await(await fetch("?/logoutAllSessions",{method:"POST",body:u,headers:{Accept:"application/json"}})).json();if(g.type==="success"){const E=g.count!==void 0?`Logged out of ${g.count} other session${g.count!==1?"s":""} successfully`:"All other sessions logged out successfully";$.success(E),v(v().filter(x=>x.isCurrent)),A(N,!1)}else $.error(g.error||"Failed to log out all sessions")}catch(u){console.error("Error logging out all sessions:",u),$.error("Failed to log out all sessions")}}function H(u){const c=new Date(u),E=new Date().getTime()-c.getTime(),x=Math.round(E/1e3),_=Math.round(x/60),J=Math.round(_/60),I=Math.round(J/24);return x<60?"just now":_<60?`${_} minute${_>1?"s":""} ago`:J<24?`${J} hour${J>1?"s":""} ago`:I<30?`${I} day${I>1?"s":""} ago`:c.toLocaleDateString()}function Q(u){return u.toLowerCase().includes("iphone")||u.toLowerCase().includes("android")||u.toLowerCase().includes("mobile")?es:u.toLowerCase().includes("ipad")||u.toLowerCase().includes("tablet")?vs:ps}he();var ae=Bs(),ne=k(ae),q=s(t(ne),2);{var ie=u=>{var c=Es(),g=t(c);Re(g,{class:"h-4 w-4"}),W(),e(c),oe("click",c,()=>A(N,!0)),l(u,c)};K(q,u=>{v().filter(c=>!c.isCurrent).length>0&&u(ie)})}e(ne);var ee=s(ne,2),te=t(ee);me(te,5,v,ge,(u,c)=>{Ie(u,{children:(g,E)=>{Me(g,{class:"border-border flex items-center justify-between rounded-lg",children:(x,_)=>{var J=Us(),I=k(J),T=t(I);Ee(T,()=>Q(r(c).device),(b,V)=>{V(b,{class:"text-primary mt-1 h-8 w-8"})});var n=s(T,2),d=t(n),a=t(d),o=t(a,!0);e(a);var y=s(a,2);{var w=b=>{var V=Is();l(b,V)};K(y,b=>{r(c).isCurrent&&b(w)})}e(d);var h=s(d,2),X=t(h),U=t(X);ds(U,{class:"h-3.5 w-3.5"});var S=s(U,2),M=t(S);e(S),e(X);var j=s(X,2),i=t(j);cs(i,{class:"h-3.5 w-3.5"});var f=s(i,2),O=t(f,!0);e(f),e(j),e(h);var B=s(h,2),C=t(B);us(C,{class:"h-3 w-3"});var Y=s(C,2),D=t(Y);e(Y),e(B);var L=s(B,2);{var P=b=>{var V=Ms(),re=t(V);e(V),Z(()=>F(re,`IP: ${r(c).ip??""}`)),l(b,V)};K(L,b=>{r(c).ip&&b(P)})}e(n),e(I);var G=s(I,2);{var z=b=>{var V=Js(),re=t(V);Re(re,{class:"h-4 w-4"}),W(),e(V),oe("click",V,()=>m(r(c).id)),l(b,V)};K(G,b=>{r(c).isCurrent||b(z)})}Z(b=>{F(o,r(c).device),F(M,`${r(c).browser??""} on ${r(c).os??""}`),F(O,r(c).location),F(D,`Last active: ${b??""}`)},[()=>H(r(c).lastActive)],pe),l(x,J)},$$slots:{default:!0}})},$$slots:{default:!0}})}),e(te),e(ee);var ce=s(ee,2);ke(ce,{get open(){return r(N)},set open(u){A(N,u)},children:(u,c)=>{var g=qs(),E=k(g);Se(E,{});var x=s(E,2);Ce(x,{children:(_,J)=>{De(_,{class:"sm:max-w-[425px]",children:(I,T)=>{var n=Ws(),d=k(n);Te(d,{children:(h,X)=>{var U=Ys(),S=k(U);je(S,{children:(j,i)=>{W();var f=le("Log Out All Other Sessions");l(j,f)},$$slots:{default:!0}});var M=s(S,2);Ae(M,{children:(j,i)=>{W();var f=le(`Are you sure you want to log out all other sessions? You will remain logged in on this
          device.`);l(j,f)},$$slots:{default:!0}}),l(h,U)},$$slots:{default:!0}});var a=s(d,2),o=t(a),y=t(o);e(o),e(a);var w=s(a,2);Fe(w,{children:(h,X)=>{var U=Vs(),S=k(U),M=s(S,2);oe("click",S,()=>A(N,!1)),oe("click",M,R),l(h,U)},$$slots:{default:!0}}),Z(h=>F(y,`This will log you out from ${h??""} other device(s).`),[()=>v().filter(h=>!h.isCurrent).length],pe),l(I,n)},$$slots:{default:!0}})},$$slots:{default:!0}}),l(u,g)},$$slots:{default:!0},$$legacy:!0}),l(de,ae),be()}var zs=p("<!> <span> </span>",1),Hs=p("<!> <!>",1),Ks=p(`<!> <div class="flex flex-row justify-between p-6"><div class="flex flex-col"><h2 class="text-lg font-semibold">Security</h2> <p class="text-foreground/80">Manage your account security settings, including password, two-factor authentication, and
      more.</p></div></div> <div class="grid gap-6"><div><!></div></div>`,1);function It(de,se){ye(se,!1);let v=xe(se,"data",8);console.log("Security page data:",v()),console.log("Passkeys from data:",v().passkeys),console.log("User data from server:",v().userData);const N=[{id:"password",label:"Password",icon:Je},{id:"passkeys",label:"Passkeys",icon:Oe},{id:"sessions",label:"Sessions",icon:ss}];let m=ue("password");he();var R=Ks(),H=k(R);ze(H,{title:"Security Settings | Hirli",description:"Manage your account security settings, including active sessions, two-factor authentication, and connected devices.",keywords:"account security, two-factor authentication, 2FA, sessions, devices, login history",url:"https://hirli.com/dashboard/settings/security"});var Q=s(H,4),ae=t(Q),ne=t(ae);Be(ne,{get value(){return r(m)},onValueChange:q=>A(m,q),children:(q,ie)=>{var ee=Hs(),te=k(ee);Ge(te,{class:"w-full",children:(u,c)=>{var g=Le(),E=k(g);me(E,1,()=>N,ge,(x,_)=>{fs(x,{get value(){return r(_).id},children:(J,I)=>{var T=zs(),n=k(T);Ee(n,()=>r(_).icon,(o,y)=>{y(o,{class:"h-4 w-4"})});var d=s(n,2),a=t(d,!0);e(d),Z(()=>F(a,r(_).label)),l(J,T)},$$slots:{default:!0}})}),l(u,g)},$$slots:{default:!0}});var ce=s(te,2);me(ce,1,()=>N,ge,(u,c)=>{ms(u,{get value(){return r(c).id},children:(g,E)=>{var x=Le(),_=k(x);{var J=T=>{Rs(T,{get passwordForm(){return v().passwordForm}})},I=(T,n)=>{{var d=o=>{As(o,{get passkeys(){return v().passkeys}})},a=(o,y)=>{{var w=h=>{Gs(h,{get sessions(){return v().sessions}})};K(o,h=>{r(c).id==="sessions"&&h(w)},y)}};K(T,o=>{r(c).id==="passkeys"?o(d):o(a,!1)},n)}};K(_,T=>{r(c).id==="password"?T(J):T(I,!1)})}l(g,x)},$$slots:{default:!0}})}),l(q,ee)},$$slots:{default:!0}}),e(ae),e(Q),l(de,R),be()}export{It as component};
