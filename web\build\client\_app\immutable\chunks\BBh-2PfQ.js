import{c as l,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as p,s as m}from"./Btcx8l8F.js";import{I as $}from"./D4f2twK-.js";function g(o,e){const a=p(e,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M14 4.1 12 6"}],["path",{d:"m5.1 8-2.9-.8"}],["path",{d:"m6 12-1.9 2"}],["path",{d:"M7.2 2.2 8 5.1"}],["path",{d:"M9.037 9.69a.498.498 0 0 1 .653-.653l11 4.5a.5.5 0 0 1-.074.949l-4.349 1.041a1 1 0 0 0-.74.739l-1.04 4.35a.5.5 0 0 1-.95.074z"}]];$(o,m({name:"mouse-pointer-click"},()=>a,{get iconNode(){return r},children:(s,f)=>{var t=l(),n=i(t);d(n,e,"default",{},null),c(s,t)},$$slots:{default:!0}}))}function M(o,e){const a=p(e,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["circle",{cx:"9",cy:"7",r:"4"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13"}]];$(o,m({name:"user-x"},()=>a,{get iconNode(){return r},children:(s,f)=>{var t=l(),n=i(t);d(n,e,"default",{},null),c(s,t)},$$slots:{default:!0}}))}export{g as M,M as U};
