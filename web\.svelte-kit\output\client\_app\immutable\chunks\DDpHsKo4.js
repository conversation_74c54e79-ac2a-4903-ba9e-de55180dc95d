import{c as n,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as p,s as m}from"./Btcx8l8F.js";import{I as $}from"./D4f2twK-.js";function M(a,t){const e=p(t,["children","$$slots","$$events","$$legacy"]),o=[["circle",{cx:"16",cy:"4",r:"1"}],["path",{d:"m18 19 1-7-6 1"}],["path",{d:"m5 8 3-3 5.5 3-2.36 3.5"}],["path",{d:"M4.24 14.5a5 5 0 0 0 6.88 6"}],["path",{d:"M13.76 17.5a5 5 0 0 0-6.88-6"}]];$(a,m({name:"accessibility"},()=>e,{get iconNode(){return o},children:(r,h)=>{var s=n(),c=l(s);d(c,t,"default",{},null),i(r,s)},$$slots:{default:!0}}))}function N(a,t){const e=p(t,["children","$$slots","$$events","$$legacy"]),o=[["path",{d:"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z"}],["path",{d:"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z"}],["path",{d:"M7 21h10"}],["path",{d:"M12 3v18"}],["path",{d:"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2"}]];$(a,m({name:"scale"},()=>e,{get iconNode(){return o},children:(r,h)=>{var s=n(),c=l(s);d(c,t,"default",{},null),i(r,s)},$$slots:{default:!0}}))}export{M as A,N as S};
