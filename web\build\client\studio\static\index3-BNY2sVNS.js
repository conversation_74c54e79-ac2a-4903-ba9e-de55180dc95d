const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/index-D8KJVLq7.js","static/sanity-DV0NwVOn.js","static/index2-BMjMOPK3.js"])))=>i.map(i=>d[i]);
import{o as Oe,e as Me,a as E,u as Ae,r as S,j as p,E as Ve,f as Be,s as he,h as ae,k as se,l as de,S as pe,T as N,m as Pe,B as ne,n as $e,C as re,b as Ee,p as Re,q as fe,t as ye,v as oe,w as We,x as xe,H as Ke,y as He,z as Fe,_ as Z,A as me,D as Ge,F as Ue,G as ke,I as Je,J as Ye,L as ge,P as qe,K as Y,M as Qe,N as Xe,O as Ze,Q as et,U as tt,V as we,W as F,X as nt,Y as Te,Z as Ce,$ as rt,a0 as at,a1 as st,a2 as ot,a3 as it,a4 as lt,a5 as ct,a6 as dt,a7 as ut,a8 as ee,a9 as pt,aa as Ie,ab as mt,ac as ht,ad as _e,ae as ft,af as yt,ag as xt,ah as gt,ai as It,aj as Pt,ak as ve,al as wt,am as _t,an as vt}from"./sanity-DV0NwVOn.js";function bt(){return Oe(function(t,e){var a,n=!1;t.subscribe(Me(e,function(o){var r=a;a=o,n&&e.next([r,o]),n=!0}))})}const jt=[];function St(t){const e=E.c(60),{children:a,flatIndex:n,index:o,params:r,payload:i,siblingIndex:s}=t,{navigate:l,navigateIntent:u,resolvePathFromState:c}=fe(),d=ye(),{panes:x,expand:I}=Ze();let f;f=(d==null?void 0:d.panes)||jt;const m=f;let P;P=x==null?void 0:x[x.length-2];const g=P,h=o-1;let y;e[0]!==h||e[1]!==m||e[2]!==d||e[3]!==s?(y=v=>{const j=m[h]||[],$=j[s],M=v(j,$),X=[...m.slice(0,h),M,...m.slice(h+1)];return{...d||{},panes:X}},e[0]=h,e[1]=m,e[2]=d,e[3]=s,e[4]=y):y=e[4];const w=y;let _;e[5]!==w||e[6]!==l?(_=v=>{const j=w(v);return setTimeout(()=>l(j),0),j},e[5]=w,e[6]=l,e[7]=_):_=e[7];const b=_;let R;e[8]!==w||e[9]!==c||e[10]!==s?(R=v=>{const j=w(($,M)=>[...$.slice(0,s),{...M,params:v},...$.slice(s+1)]);return c(j)},e[8]=w,e[9]=c,e[10]=s,e[11]=R):R=e[11];const U=R;let T;e[12]!==b||e[13]!==s?(T=v=>{b((j,$)=>[...j.slice(0,s),{...$,payload:v},...j.slice(s+1)])},e[12]=b,e[13]=s,e[14]=T):T=e[14];const L=T;let C;e[15]!==b||e[16]!==s?(C=v=>{b((j,$)=>[...j.slice(0,s),{...$,params:v},...j.slice(s+1)])},e[15]=b,e[16]=s,e[17]=C):C=e[17];const k=C;let z;e[18]!==h||e[19]!==l||e[20]!==m?(z=v=>{const{id:j,parentRefPath:$,type:M,template:X,version:Ne}=v;l({panes:[...m.slice(0,h+1),[{id:j,params:{template:X.id,parentRefPath:it($),type:M,version:Ne},payload:X.params}]]})},e[18]=h,e[19]=l,e[20]=m,e[21]=z):z=e[21];const O=z;let J;const D=m[h]?m[h].length>1:!1,A=m[h]?m[h].length:0,le=n?et:void 0;let V,B;e[22]!==b?(V=v=>{const j=v===void 0?{}:v;b(()=>[{id:j.id||"",payload:j.payload,params:j.params||{}}])},B=()=>{b($t)},e[22]=b,e[23]=V,e[24]=B):(V=e[23],B=e[24]);let W;e[25]!==I||e[26]!==h||e[27]!==g||e[28]!==l||e[29]!==m?(W=v=>{(v===void 0||v)&&g&&I(g.element),l({panes:[...m.slice(0,h)]})},e[25]=I,e[26]=h,e[27]=g,e[28]=l,e[29]=m,e[30]=W):W=e[30];let K;e[31]!==b||e[32]!==s?(K=v=>{b((j,$)=>{const M={...$,payload:(v==null?void 0:v.payload)||$.payload,params:(v==null?void 0:v.params)||$.params};return[...j.slice(0,s),M,...j.slice(s)]})},e[31]=b,e[32]=s,e[33]=K):K=e[33];let H;e[34]!==r||e[35]!==k?(H=v=>{const j=Ce(r,"view");return k(v?{...j,view:v}:j)},e[34]=r,e[35]=k,e[36]=H):H=e[36];let q;e[37]!==U||e[38]!==n||e[39]!==h||e[40]!==O||e[41]!==u||e[42]!==r||e[43]!==i||e[44]!==m||e[45]!==k||e[46]!==L||e[47]!==s||e[48]!==A||e[49]!==le||e[50]!==V||e[51]!==B||e[52]!==W||e[53]!==K||e[54]!==H||e[55]!==D?(q={index:n,groupIndex:h,siblingIndex:s,payload:i,params:r,hasGroupSiblings:D,groupLength:A,routerPanesState:m,ChildLink:dt,BackLink:le,ReferenceChildLink:ct,handleEditReference:O,ParameterizedLink:lt,replaceCurrent:V,closeCurrent:B,closeCurrentAndAfter:W,duplicateCurrent:K,setView:H,setParams:k,setPayload:L,createPathWithParams:U,navigateIntent:u},e[37]=U,e[38]=n,e[39]=h,e[40]=O,e[41]=u,e[42]=r,e[43]=i,e[44]=m,e[45]=k,e[46]=L,e[47]=s,e[48]=A,e[49]=le,e[50]=V,e[51]=B,e[52]=W,e[53]=K,e[54]=H,e[55]=D,e[56]=q):q=e[56],J=q;const ce=J;let Q;return e[57]!==a||e[58]!==ce?(Q=p.jsx(tt.Provider,{value:ce,children:a}),e[57]=a,e[58]=ce,e[59]=Q):Q=e[59],Q}function $t(t,e){return t.length>1?t.filter(a=>a!==e):t}class G extends Error{constructor({message:e,context:a,helpId:n,cause:o}){super(e),this.name="PaneResolutionError",this.context=a,this.helpId=n,this.cause=o}}const be=new WeakMap;function ie(t){const e=be.get(t);if(e)return e;const a=It();return be.set(t,a),a}const Et=t=>!!t&&typeof(t==null?void 0:t.then)=="function",Rt=t=>xe(t)?typeof t.serialize=="function":!1,kt=t=>(e,a,n)=>{try{return t(e,a,n)}catch(o){throw o instanceof G?o:new G({message:typeof(o==null?void 0:o.message)=="string"?o.message:"",context:a,cause:o})}},Tt=t=>(...e)=>t(...e).pipe(xt(1),gt());function De(t){const e=kt(Tt(t((a,n,o)=>{if(!a)throw new G({message:"Pane returned no child",context:n,helpId:"structure-item-returned-no-child"});return Et(a)||pt(a)?ut(a).pipe(Ie(r=>e(r,n,o))):Rt(a)?e(a.serialize(n),n,o):typeof a=="function"?e(a(n.id,n),n,o):ee(a)})));return e}const je=new WeakMap;function Le(t,e){const a=je.get(t)||new Map;if(a){const r=a.get(e);if(r)return r}const n=t[e];if(typeof n!="function")throw new Error(`Expected property \`${e}\` to be a function but got ${typeof n} instead.`);const o=n.bind(t);return a.set(e,o),je.set(t,a),o}async function Ct(t){const e=new Map,a=De(i=>(s,l,u)=>{const c=s&&`${ie(s)}-${l.path.join("__")}`,d=c&&e.get(c);if(d)return d;const x=i(s,l,u);return c&&e.set(c,x),x}),n=[[{id:`__edit__${t.params.id}`,params:{...Ce(t.params,["id"]),type:t.params.type},payload:t.payload}]];async function o({currentId:i,flatIndex:s,intent:l,params:u,parent:c,path:d,payload:x,unresolvedPane:I,levelIndex:f,structureContext:m}){var w;if(!I)return[];const{id:P,type:g,...h}=u,y=await Te(a(I,{id:i,splitIndex:0,parent:c,path:d,index:s,params:{},payload:void 0,structureContext:m},s));return y.type==="document"&&y.id===P?[{panes:[...d.slice(0,d.length-1).map(_=>[{id:_}]),[{id:P,params:h,payload:x}]],depthIndex:d.length,levelIndex:f}]:(w=y.canHandleIntent)!=null&&w.call(y,l,u,{pane:y,index:s})||y.type==="documentList"&&y.schemaTypeName===g&&y.options.filter==="_type == $type"?[{panes:[...d.map(_=>[{id:_}]),[{id:u.id,params:h,payload:x}]],depthIndex:d.length,levelIndex:f}]:y.type==="list"&&y.child&&y.items?(await Promise.all(y.items.map((_,b)=>_.type==="divider"?Promise.resolve([]):o({currentId:_._id||_.id,flatIndex:s+1,intent:l,params:u,parent:y,path:[...d,_.id],payload:x,unresolvedPane:typeof y.child=="function"?Le(y,"child"):y.child,levelIndex:b,structureContext:m})))).flat():[]}const r=(await o({currentId:"root",flatIndex:0,levelIndex:0,intent:t.intent,params:t.params,parent:null,path:[],payload:t.payload,unresolvedPane:t.rootPaneNode,structureContext:t.structureContext})).sort((i,s)=>i.depthIndex===s.depthIndex?i.levelIndex-s.levelIndex:i.depthIndex-s.depthIndex)[0];return r?r.panes:n}const Dt=(t,e)=>{const a=t.replace(/^__edit__/,""),{params:n,payload:o,structureContext:{resolveDocumentNode:r}}=e,{type:i,template:s}=n;if(!i)throw new Error(`Document type for document with ID ${a} was not provided in the router params.`);let l=r({schemaType:i,documentId:a}).id("editor");return s&&(l=l.initialValueTemplate(s,o)),l.serialize()};function Lt(t){var e,a;return`contextHash(${JSON.stringify({id:t.id,parentId:parent&&ie(parent),path:t.path,index:t.index,splitIndex:t.splitIndex,serializeOptionsIndex:(e=t.serializeOptions)==null?void 0:e.index,serializeOptionsPath:(a=t.serializeOptions)==null?void 0:a.path})})`}const Se=t=>{const e={type:t.type,id:t.routerPaneSibling.id,params:t.routerPaneSibling.params||{},payload:t.routerPaneSibling.payload||null,flatIndex:t.flatIndex,groupIndex:t.groupIndex,siblingIndex:t.siblingIndex,path:t.path,paneNode:t.type==="resolvedMeta"?ie(t.paneNode):null};return`metaHash(${JSON.stringify(e)})`};function te({unresolvedPane:t,flattenedRouterPanes:e,parent:a,path:n,resolvePane:o,structureContext:r}){const[i,...s]=e,l=s[0],u={id:i.routerPaneSibling.id,splitIndex:i.siblingIndex,parent:a,path:[...n,i.routerPaneSibling.id],index:i.flatIndex,params:i.routerPaneSibling.params||{},payload:i.routerPaneSibling.payload,structureContext:r};try{return o(t,u,i.flatIndex).pipe(Ie(c=>{const d={type:"resolvedMeta",...i,paneNode:c,path:u.path},x=s.map((f,m)=>({type:"loading",path:[...u.path,...s.slice(m).map((P,g)=>`[${f.flatIndex+g}]`)],paneNode:null,...f}));if(!s.length)return ee([d]);let I;return l!=null&&l.routerPaneSibling.id.startsWith("__edit__")?I=te({unresolvedPane:Dt,flattenedRouterPanes:s,parent:a,path:u.path,resolvePane:o,structureContext:r}):i.groupIndex===(l==null?void 0:l.groupIndex)?I=te({unresolvedPane:t,flattenedRouterPanes:s,parent:a,path:n,resolvePane:o,structureContext:r}):I=te({unresolvedPane:typeof c.child=="function"?Le(c,"child"):c.child,flattenedRouterPanes:s,parent:c,path:u.path,resolvePane:o,structureContext:r}),vt(ee([d,...x]),I.pipe(F(f=>[d,...f])))}))}catch(c){if(c instanceof G&&(c.context&&console.warn(`Pane resolution error at index ${c.context.index}${c.context.splitIndex>0?` for split pane index ${c.context.splitIndex}`:""}: ${c.message}${c.helpId?` - see ${$e(c.helpId)}`:""}`,c),c.helpId==="structure-item-returned-no-child"))return ee([]);throw c}}function zt({routerPanesStream:t,rootPaneNode:e,initialCacheState:a={cacheKeysByFlatIndex:[],flattenedRouterPanes:[],resolvedPaneCache:new Map,resolvePane:()=>wt},structureContext:n}){return t.pipe(F(o=>[[{id:"root"}],...o]),F(o=>o.flatMap((r,i)=>r.map((s,l)=>({routerPaneSibling:s,groupIndex:i,siblingIndex:l}))).map((r,i)=>({...r,flatIndex:i}))),Pt([]),bt(),F(([o,r])=>{for(let i=0;i<r.length;i++){const s=o[i],l=r[i];if(!me(s,l))return{flattenedRouterPanes:r,diffIndex:i}}return{flattenedRouterPanes:r,diffIndex:r.length}}),ve((o,r)=>{const{cacheKeysByFlatIndex:i,resolvedPaneCache:s}=o,{flattenedRouterPanes:l,diffIndex:u}=r,c=i.slice(0,u+1),d=i.slice(u+1),x=new Set(c.flatMap(f=>Array.from(f))),I=d.flatMap(f=>Array.from(f)).filter(f=>!x.has(f));for(const f of I)s.delete(f);return{flattenedRouterPanes:l,cacheKeysByFlatIndex:i,resolvedPaneCache:s,resolvePane:De(f=>(m,P,g)=>{const h=m&&`${ie(m)}-${Lt(P)}`,y=h&&s.get(h);if(y)return y;const w=f(m,P,g);if(!h)return w;const _=i[g]||new Set;return _.add(h),i[g]=_,s.set(h,w),w})}},a),Ie(({flattenedRouterPanes:o,resolvePane:r})=>te({unresolvedPane:e,flattenedRouterPanes:o,parent:null,path:[],resolvePane:r,structureContext:n}))).pipe(ve((o,r)=>r.map((i,s)=>{const l=o[s];return!l||i.type!=="loading"?i:l.routerPaneSibling.id===i.routerPaneSibling.id?l:i}),[]),_t((o,r)=>{if(o.length!==r.length)return!1;for(let i=0;i<r.length;i++){const s=o[i],l=r[i];if(Se(s)!==Se(l))return!1}return!0}))}function Nt(){const t=E.c(6),[e]=S.useState(Mt);let a,n;t[0]!==e?(n=e.asObservable().pipe(F(Ot)),t[0]=e,t[1]=n):n=t[1],a=n;const o=a,{state:r}=fe();let i,s;return t[2]!==r||t[3]!==e?(i=()=>{e.next(r)},s=[r,e],t[2]=r,t[3]=e,t[4]=i,t[5]=s):(i=t[4],s=t[5]),S.useEffect(i,s),o}function Ot(t){return(t==null?void 0:t.panes)||[]}function Mt(){return new rt(1)}function At(){const t=E.c(6),[e,a]=S.useState();if(e)throw e;const{structureContext:n,rootPaneNode:o}=oe();let r;t[0]===Symbol.for("react.memo_cache_sentinel")?(r={paneDataItems:[],resolvedPanes:[],routerPanes:[]},t[0]=r):r=t[0];const[i,s]=S.useState(r),l=Nt();let u,c;return t[1]!==o||t[2]!==l||t[3]!==n?(u=()=>{const d=zt({rootPaneNode:o,routerPanesStream:l,structureContext:n}).pipe(F(Vt)).subscribe({next:x=>s(x),error:x=>a(x)});return()=>d.unsubscribe()},c=[o,l,n],t[1]=o,t[2]=l,t[3]=n,t[4]=u,t[5]=c):(u=t[4],c=t[5]),S.useEffect(u,c),i}function Vt(t){const e=t.reduce(Wt,[]),a=e.length,n=t.map(o=>{const{groupIndex:r,flatIndex:i,siblingIndex:s,routerPaneSibling:l,path:u}=o,c=l.id,d=e[r+1];return{active:r===a-2,childItemId:(d==null?void 0:d[0].id)??null,index:i,itemId:l.id,groupIndex:r,key:`${o.type==="loading"?"unknown":o.paneNode.id}-${c}-${s}`,pane:o.type==="loading"?Y:o.paneNode,params:l.params||{},path:u.join(";"),payload:l.payload,selected:i===t.length-1,siblingIndex:s}});return{paneDataItems:n,routerPanes:e,resolvedPanes:n.map(Bt)}}function Bt(t){return t.pane}function Wt(t,e){const a=t[e.groupIndex]||[];return a[e.siblingIndex]=e.routerPaneSibling,t[e.groupIndex]=a,t}async function Kt(t,e,a){if(e&&a)return{id:e,type:a};if(!e&&a)return{id:nt(),type:a};if(e&&!a){const n=await Te(t.resolveTypeForDocument(e));return{id:e,type:n}}throw new G({message:"Neither document `id` or `type` was provided when trying to resolve intent."})}const Ht={},Ft=S.memo(function(){const t=E.c(7),{navigate:e}=fe(),a=ye(Gt),{rootPaneNode:n,structureContext:o}=oe(),r=We(),[i,s]=S.useState(null);if(i)throw i;let l,u;return t[0]!==r||t[1]!==a||t[2]!==e||t[3]!==n||t[4]!==o?(l=()=>{if(a){const{intent:c,params:d,payload:x}=a;let I=!1;return async function(){const{id:f,type:m}=await Kt(r,typeof d.id=="string"?d.id:void 0,typeof d.type=="string"?d.type:void 0);if(I)return;const P=await Ct({intent:c,params:{...d,id:f,type:m},payload:x,rootPaneNode:n,structureContext:o});I||e({panes:P},{replace:!0})}().catch(s),()=>{I=!0}}},u=[r,a,e,n,o],t[0]=r,t[1]=a,t[2]=e,t[3]=n,t[4]=o,t[5]=l,t[6]=u):(l=t[5],u=t[6]),S.useEffect(l,u),null});function Gt(t){const e=typeof t.intent=="string"?t.intent:void 0;return e?{intent:e,params:xe(t.params)?t.params:Ht,payload:t.payload}:void 0}const Ut=Re.span`
  &:not(:last-child)::after {
    content: ' ➝ ';
    opacity: 0.5;
  }
`;function Jt(t){return t.replace(/\(\.\.\.\)\./g,`(...)
  .`).replace(/__WEBPACK_IMPORTED_MODULE_\d+_+/g,"").replace(/___default\./g,".").replace(new RegExp(` \\(https?:\\/\\/${window.location.host}`,"g")," (")}function Yt(t){const e=E.c(37),{error:a}=t;if(!(a instanceof G))throw a;const{cause:n}=a,{t:o}=ae(se),r=(n==null?void 0:n.stack)||a.stack,i=r&&!(n instanceof de)&&!a.message.includes("Module build failed:");let s;e[0]!==n?(s=n instanceof de?n.path:[],e[0]=n,e[1]=s):s=e[1];const l=s,u=n instanceof de&&n.helpId||a.helpId,c=Qt;let d;e[2]!==o?(d=o("structure-error.header.text"),e[2]=o,e[3]=d):d=e[3];let x;e[4]!==d?(x=p.jsx(Ke,{as:"h2",children:d}),e[4]=d,e[5]=x):x=e[5];let I;e[6]!==l||e[7]!==o?(I=l.length>0&&p.jsxs(pe,{space:2,children:[p.jsx(N,{size:1,weight:"medium",children:o("structure-error.structure-path.label")}),p.jsx(Pe,{children:l.slice(1).map(qt)})]}),e[6]=l,e[7]=o,e[8]=I):I=e[8];let f;e[9]!==o?(f=o("structure-error.error.label"),e[9]=o,e[10]=f):f=e[10];let m;e[11]!==f?(m=p.jsx(N,{size:1,weight:"medium",children:f}),e[11]=f,e[12]=m):m=e[12];let P;e[13]!==a.message||e[14]!==i||e[15]!==r?(P=i?Jt(r):a.message,e[13]=a.message,e[14]=i,e[15]=r,e[16]=P):P=e[16];let g;e[17]!==P?(g=p.jsx(Pe,{children:P}),e[17]=P,e[18]=g):g=e[18];let h;e[19]!==m||e[20]!==g?(h=p.jsxs(pe,{marginTop:4,space:2,children:[m,g]}),e[19]=m,e[20]=g,e[21]=h):h=e[21];let y;e[22]!==u||e[23]!==o?(y=u&&p.jsx(ne,{marginTop:4,children:p.jsx(N,{children:p.jsx("a",{href:$e(u),rel:"noopener noreferrer",target:"_blank",children:o("structure-error.docs-link.text")})})}),e[22]=u,e[23]=o,e[24]=y):y=e[24];let w;e[25]!==o?(w=o("structure-error.reload-button.text"),e[25]=o,e[26]=w):w=e[26];let _;e[27]!==w?(_=p.jsx(ne,{marginTop:4,children:p.jsx(He,{text:w,icon:Fe,tone:"primary",onClick:c})}),e[27]=w,e[28]=_):_=e[28];let b;e[29]!==y||e[30]!==_||e[31]!==I||e[32]!==h?(b=p.jsxs(re,{marginTop:4,padding:4,radius:2,overflow:"auto",shadow:1,tone:"inherit",children:[I,h,y,_]}),e[29]=y,e[30]=_,e[31]=I,e[32]=h,e[33]=b):b=e[33];let R;return e[34]!==b||e[35]!==x?(R=p.jsx(re,{height:"fill",overflow:"auto",padding:4,sizing:"border",tone:"critical",children:p.jsxs(Ee,{children:[x,b]})}),e[34]=b,e[35]=x,e[36]=R):R=e[36],R}function qt(t,e){return p.jsx(Ut,{children:t},`${t}-${e}`)}function Qt(){window.location.reload()}function Xt(t){const e=E.c(14),{isSelected:a,pane:n,paneKey:o}=t;let r;e[0]!==n?(r=xe(n)&&n.type||null,e[0]=n,e[1]=r):r=e[1];const i=r,{t:s}=ae(se);let l;e[2]!==s?(l=s("panes.unknown-pane-type.title"),e[2]=s,e[3]=l):l=e[3];let u;e[4]!==l?(u=p.jsx(mt,{title:l}),e[4]=l,e[5]=u):u=e[5];let c;e[6]!==s||e[7]!==i?(c=p.jsx(ht,{children:p.jsx(ne,{padding:4,children:typeof i=="string"?p.jsx(N,{as:"p",muted:!0,children:p.jsx(_e,{t:s,i18nKey:"panes.unknown-pane-type.unknown-type.text",values:{type:i}})}):p.jsx(N,{as:"p",muted:!0,children:p.jsx(_e,{t:s,i18nKey:"panes.unknown-pane-type.missing-type.text"})})})}),e[6]=s,e[7]=i,e[8]=c):c=e[8];let d;return e[9]!==a||e[10]!==o||e[11]!==u||e[12]!==c?(d=p.jsxs(Xe,{id:o,selected:a,children:[u,c]}),e[9]=a,e[10]=o,e[11]=u,e[12]=c,e[13]=d):d=e[13],d}const Zt={component:S.lazy(()=>Z(()=>import("./index-D8KJVLq7.js"),__vite__mapDeps([0,1]))),document:S.lazy(()=>Z(()=>import("./sanity-DV0NwVOn.js").then(t=>t.bc),[]).then(function(t){return t.pane})),documentList:S.lazy(()=>Z(()=>import("./sanity-DV0NwVOn.js").then(t=>t.bc),[]).then(function(t){return t.pane$1})),list:S.lazy(()=>Z(()=>import("./index2-BMjMOPK3.js"),__vite__mapDeps([2,1])))},en=S.memo(function(t){const e=E.c(23),{active:a,childItemId:n,groupIndex:o,index:r,itemId:i,pane:s,paneKey:l,params:u,payload:c,path:d,selected:x,siblingIndex:I}=t,f=Zt[s.type]||Xt;let m;e[0]!==l||e[1]!==d||e[2]!==x?(m=p.jsx(ge,{paneKey:l,path:d,selected:x}),e[0]=l,e[1]=d,e[2]=x,e[3]=m):m=e[3];const P=n||"";let g;e[4]!==f||e[5]!==a||e[6]!==r||e[7]!==i||e[8]!==s||e[9]!==l||e[10]!==x||e[11]!==P?(g=p.jsx(f,{childItemId:P,index:r,itemId:i,isActive:a,isSelected:x,paneKey:l,pane:s}),e[4]=f,e[5]=a,e[6]=r,e[7]=i,e[8]=s,e[9]=l,e[10]=x,e[11]=P,e[12]=g):g=e[12];let h;e[13]!==m||e[14]!==g?(h=p.jsx(S.Suspense,{fallback:m,children:g}),e[13]=m,e[14]=g,e[15]=h):h=e[15];let y;return e[16]!==o||e[17]!==r||e[18]!==u||e[19]!==c||e[20]!==I||e[21]!==h?(y=p.jsx(St,{flatIndex:r,index:o,params:u,payload:c,siblingIndex:I,children:h}),e[16]=o,e[17]=r,e[18]=u,e[19]=c,e[20]=I,e[21]=h,e[22]=y):y=e[22],y},({params:t={},payload:e=null,...a},{params:n={},payload:o=null,...r})=>{if(!me(t,n)||!me(e,o))return!1;const i=new Set([...Object.keys(a),...Object.keys(r)]);for(const s of i)if(a[s]!==r[s])return!1;return!0});function tn(){const t=E.c(17),{t:e}=ae(se);let a;t[0]===Symbol.for("react.memo_cache_sentinel")?(a=p.jsx(ne,{children:p.jsx(N,{size:1,children:p.jsx(ft,{})})}),t[0]=a):a=t[0];let n;t[1]!==e?(n=e("no-document-types-screen.title"),t[1]=e,t[2]=n):n=t[2];let o;t[3]!==n?(o=p.jsx(N,{as:"h1",size:1,weight:"medium",children:n}),t[3]=n,t[4]=o):o=t[4];let r;t[5]!==e?(r=e("no-document-types-screen.subtitle"),t[5]=e,t[6]=r):r=t[6];let i;t[7]!==r?(i=p.jsx(N,{as:"p",muted:!0,size:1,children:r}),t[7]=r,t[8]=i):i=t[8];let s;t[9]!==e?(s=e("no-document-types-screen.link-text"),t[9]=e,t[10]=s):s=t[10];let l;t[11]!==s?(l=p.jsx(N,{as:"p",muted:!0,size:1,children:p.jsx("a",{href:"https://www.sanity.io/docs/create-a-schema-and-configure-sanity-studio",target:"_blank",rel:"noreferrer",children:s})}),t[11]=s,t[12]=l):l=t[12];let u;return t[13]!==o||t[14]!==i||t[15]!==l?(u=p.jsx(re,{height:"fill",children:p.jsx(we,{align:"center",height:"fill",justify:"center",padding:4,sizing:"border",children:p.jsx(Ee,{width:0,children:p.jsx(re,{padding:4,radius:2,shadow:1,tone:"caution",children:p.jsxs(we,{children:[a,p.jsxs(pe,{flex:1,marginLeft:3,space:3,children:[o,i,l]})]})})})})}),t[13]=o,t[14]=i,t[15]=l,t[16]=u):u=t[16],u}const nn=t=>{const e=E.c(7),{documentId:a,documentType:n}=t,{selectedReleaseId:o}=at(),r=st(a,n,"default",o),i=ke(),{t:s}=ae(se),l=!(r!=null&&r.published)&&!(r!=null&&r.draft),u=(r==null?void 0:r.version)||(r==null?void 0:r.draft)||(r==null?void 0:r.published),c=i.get(n),{value:d,isLoading:x}=ot({enabled:!0,schemaType:c,value:u}),I=l?s("browser-document-title.new-document",{schemaType:(c==null?void 0:c.title)||(c==null?void 0:c.name)}):(d==null?void 0:d.title)||s("browser-document-title.untitled-document"),f=r.ready&&!x,m=ze(I);let P;e[0]!==m||e[1]!==f?(P=()=>{f&&(document.title=m)},e[0]=m,e[1]=f,e[2]=P):P=e[2];let g;return e[3]!==I||e[4]!==m||e[5]!==f?(g=[I,f,m],e[3]=I,e[4]=m,e[5]=f,e[6]=g):g=e[6],S.useEffect(P,g),null},ue=t=>{const e=E.c(5),{title:a}=t,n=ze(a);let o;e[0]!==n?(o=()=>{document.title=n},e[0]=n,e[1]=o):o=e[1];let r;return e[2]!==n||e[3]!==a?(r=[n,a],e[2]=n,e[3]=a,e[4]=r):r=e[4],S.useEffect(o,r),null},rn=t=>{const e=E.c(8),{resolvedPanes:a}=t;if(!(a!=null&&a.length))return null;const n=a[a.length-1];if(on(n)){let i;return e[0]===Symbol.for("react.memo_cache_sentinel")?(i=p.jsx(ue,{}),e[0]=i):i=e[0],i}if(sn(n)){if(n!=null&&n.title){let s;return e[1]!==n.title?(s=p.jsx(ue,{title:n.title}),e[1]=n.title,e[2]=s):s=e[2],s}let i;return e[3]!==n.options.id||e[4]!==n.options.type?(i=p.jsx(nn,{documentId:n.options.id,documentType:n.options.type}),e[3]=n.options.id,e[4]=n.options.type,e[5]=i):i=e[5],i}const o=n==null?void 0:n.title;let r;return e[6]!==o?(r=p.jsx(ue,{title:o}),e[6]=o,e[7]=r):r=e[7],r};function ze(t){const e=E.c(3),a=oe().structureContext.title;let n;return e[0]!==t||e[1]!==a?(n=[t,a].filter(an),e[0]=t,e[1]=a,e[2]=n):n=e[2],n.join(" | ")}function an(t){return t}function sn(t){return t!==Y&&t.type==="document"}function on(t){return t===Y}const ln=Re(yt)`
  min-height: 100%;
  min-width: 320px;
`,cn=Ge("mod+s"),dn=S.memo(function(t){var J;const e=E.c(31),{onPaneChange:a}=t,{push:n}=Ue(),o=ke(),{layoutCollapsed:r,setLayoutCollapsed:i}=oe(),{paneDataItems:s,resolvedPanes:l}=At(),u=ye(un),{sanity:c}=Je(),{media:d}=c,[x,I]=S.useState(null);let f;e[0]!==i?(f=()=>i(!0),e[0]=i,e[1]=f):f=e[1];const m=f;let P;e[2]!==i?(P=()=>i(!1),e[2]=i,e[3]=P):P=e[3];const g=P;let h,y;e[4]!==a||e[5]!==l?(h=()=>{l.length&&a(l)},y=[a,l],e[4]=a,e[5]=l,e[6]=h,e[7]=y):(h=e[6],y=e[7]),S.useEffect(h,y);let w,_;if(e[8]!==n?(w=()=>{const D=A=>{cn(A)&&(A.preventDefault(),n({closable:!0,id:"auto-save-message",status:"info",title:"Your work is automatically saved!",duration:4e3}))};return window.addEventListener("keydown",D),()=>window.removeEventListener("keydown",D)},_=[n],e[8]=n,e[9]=w,e[10]=_):(w=e[9],_=e[10]),S.useEffect(w,_),!((J=o._original)!=null&&J.types.some(Ye))){let D;return e[11]===Symbol.for("react.memo_cache_sentinel")?(D=p.jsx(tn,{}),e[11]=D):D=e[11],D}const b=x||null,R=r?void 0:"fill",U=d[1];let T;e[12]!==s?(T=s.map(pn),e[12]=s,e[13]=T):T=e[13];let L;e[14]!==u||e[15]!==s.length?(L=s.length<=1&&u&&p.jsx(ge,{paneKey:"intent-resolver"}),e[14]=u,e[15]=s.length,e[16]=L):L=e[16];let C;e[17]!==m||e[18]!==g||e[19]!==d[1]||e[20]!==T||e[21]!==L||e[22]!==R?(C=p.jsxs(ln,{flex:1,height:R,minWidth:U,onCollapse:m,onExpand:g,children:[T,L]}),e[17]=m,e[18]=g,e[19]=d[1],e[20]=T,e[21]=L,e[22]=R,e[23]=C):C=e[23];let k;e[24]!==l?(k=p.jsx(rn,{resolvedPanes:l}),e[24]=l,e[25]=k):k=e[25];let z;e[26]===Symbol.for("react.memo_cache_sentinel")?(z=p.jsx("div",{"data-portal":"",ref:I}),e[26]=z):z=e[26];let O;return e[27]!==C||e[28]!==k||e[29]!==b?(O=p.jsxs(qe,{element:b,children:[C,k,z]}),e[27]=C,e[28]=k,e[29]=b,e[30]=O):O=e[30],O});function un(t){return typeof t.intent=="string"}function pn(t){const{active:e,childItemId:a,groupIndex:n,itemId:o,key:r,pane:i,index:s,params:l,path:u,payload:c,siblingIndex:d,selected:x}=t;return p.jsx(S.Fragment,{children:i===Y?p.jsx(ge,{paneKey:r,path:u,selected:x}):p.jsx(en,{active:e,groupIndex:n,index:s,pane:i,childItemId:a,itemId:o,paneKey:r,params:l,payload:c,path:u,selected:x,siblingIndex:d})},`${i===Y?"loading":i.type}-${s}`)}function yn(t){const e=E.c(14),{tool:a}=t,{options:n}=a,{unstable_sources:o}=Ae(),[r]=o;let i;e[0]!==n?(i=n||{},e[0]=n,e[1]=i):i=e[1];const{source:s,defaultDocumentNode:l,structure:u}=i;let c;e[2]===Symbol.for("react.memo_cache_sentinel")?(c=[],e[2]=c):c=e[2],S.useEffect(mn,c);let d;e[3]===Symbol.for("react.memo_cache_sentinel")?(d={error:null},e[3]=d):d=e[3];const[x,I]=S.useState(d),{error:f}=x;if(f){let w;return e[4]!==f?(w=p.jsx(Yt,{error:f}),e[4]=f,e[5]=w):w=e[5],w}const m=s||r.name;let P,g;e[6]===Symbol.for("react.memo_cache_sentinel")?(P=p.jsx(dn,{onPaneChange:he}),g=p.jsx(Ft,{}),e[6]=P,e[7]=g):(P=e[6],g=e[7]);let h;e[8]!==l||e[9]!==u?(h=p.jsxs(Qe,{defaultDocumentNode:l,structure:u,children:[P,g]}),e[8]=l,e[9]=u,e[10]=h):h=e[10];let y;return e[11]!==m||e[12]!==h?(y=p.jsx(Ve,{onCatch:I,children:p.jsx(Be,{name:m,children:h})}),e[11]=m,e[12]=h,e[13]=y):y=e[13],y}function mn(){return he([]),hn}function hn(){return he([])}export{yn as default};
