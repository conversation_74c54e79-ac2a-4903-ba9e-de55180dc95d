import{c as l,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(s,o){const t=c(o,["children","$$slots","$$events","$$legacy"]),a=[["line",{x1:"12",x2:"12",y1:"2",y2:"22"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"}]];f(s,d({name:"dollar-sign"},()=>t,{get iconNode(){return a},children:(e,$)=>{var r=l(),n=p(r);m(n,o,"default",{},null),i(e,r)},$$slots:{default:!0}}))}export{x as D};
