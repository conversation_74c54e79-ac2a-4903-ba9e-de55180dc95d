import{c as ie,a as t,f as i,t as me}from"../chunks/BasJTneF.js";import{p as Je,f as v,a as qe,g as e,x as G,c as l,au as pt,r as a,s as o,e as ct,l as kt,b as ar,d as D,m as ot,n as S,t as pe,_ as Ut,k as mr}from"../chunks/CGmarHxI.js";import{s as Z}from"../chunks/CIt1g2O9.js";import{s as Re,c as St}from"../chunks/ncUU1dSD.js";import{i as R}from"../chunks/u21ee2wt.js";import{c as m}from"../chunks/BvdI7LR8.js";import{e as Ve,s as sr,a as Bt}from"../chunks/CmxjS0TN.js";import"../chunks/CgXBgsce.js";import{e as at,i as st}from"../chunks/C3w0v0gR.js";import{e as it,r as nr,s as Ct,c as zt,a as dr}from"../chunks/B-Xjo-Yt.js";import{i as Et}from"../chunks/BIEMS98f.js";import{p as M,r as ut,s as xt}from"../chunks/Btcx8l8F.js";import{p as lr}from"../chunks/Buv24VCh.js";import{s as Jt,L as pr}from"../chunks/AN59Tc0U.js";import{o as Lt,a as gr}from"../chunks/nZgk9enP.js";import{a as _r}from"../chunks/DDUgF6Ik.js";import{b as ir}from"../chunks/CzsE_FAw.js";import{b as hr}from"../chunks/VYoCKyli.js";import{g as le}from"../chunks/BiJhC7W5.js";import{A as cr,b as ur,a as vr}from"../chunks/CE9Bts7j.js";import{b as Ee,m as mt}from"../chunks/BfX7a-t9.js";import{g as br,C as xr,a as $r,b as qt}from"../chunks/Dmwghw4a.js";import{u as Mt}from"../chunks/CnMg5bH0.js";import{d as yr,c as wr,b as Pr,R as kr,P as Sr,D as Cr,a as Dr}from"../chunks/tdzGgazS.js";import{S as fr}from"../chunks/C2MdR6K0.js";import{a as ht}from"../chunks/B6TiSgAN.js";import{S as Dt}from"../chunks/yW0TxTga.js";import{X as Nt}from"../chunks/CnpHcmx3.js";import{C as Ft}from"../chunks/CDeW2UsS.js";import{S as Mr}from"../chunks/CYoZicO9.js";import{C as Ot}from"../chunks/DW5gea7N.js";import{B as Ar}from"../chunks/CDnvByek.js";import{F as Fr}from"../chunks/ChqRiddM.js";import{i as Or}from"../chunks/CWA2dVWH.js";import{n as Kr,u as jr}from"../chunks/xCOJ4D9d.js";import{c as Br,d as Lr,M as Nr,S as Er,e as Ir,R as Tr,D as Rr,a as Ur,f as Gt}from"../chunks/WD4kvFhR.js";import{R as Ht,T as Qt,a as Wt,P as zr}from"../chunks/ChRM_Un0.js";import{B as Kt}from"../chunks/B1K98fMG.js";import{B as Jr}from"../chunks/DaBofrVv.js";import{d as Vt,s as jt}from"../chunks/BlYzNxlg.js";import{w as qr}from"../chunks/26EXiO5K.js";import{P as Gr}from"../chunks/BaVT73bJ.js";import{e as Hr,D as Qr}from"../chunks/DMoa_yM9.js";import{C as Wr}from"../chunks/BwkAotBa.js";import{D as we}from"../chunks/Z6UAQTuv.js";import{D as bt}from"../chunks/Dz4exfp3.js";import{P as Vr,a as Xr,g as Xt,F as Yr}from"../chunks/D-o7ybA5.js";import{n as ft}from"../chunks/DX6rZLP_.js";import{C as Zr}from"../chunks/CQdOabBG.js";import{a as eo}from"../chunks/Bpi49Nrf.js";import{M as Yt}from"../chunks/D2egQzE8.js";import{S as Zt,M as er}from"../chunks/aemnuA_0.js";import{M as tr}from"../chunks/2KCyzleV.js";import{G as to}from"../chunks/BEVim9wJ.js";import{B as ro}from"../chunks/hA0h0kTo.js";import{U as oo}from"../chunks/BSHZ37s_.js";import{B as ao}from"../chunks/C2AK_5VT.js";var so=i("<div><!></div>");function no(Q,r){Je(r,!0);let _=M(r,"progress",3,0),A=M(r,"id",19,Mt),b=M(r,"ref",15,null),f=ut(r,["$$slots","$$events","$$legacy","progress","id","ref","children","child"]);const n=br({id:Ee.with(()=>A()),ref:Ee.with(()=>b(),x=>b(x)),progress:Ee.with(()=>_())}),c=G(()=>mt(f,n.props));var g=ie(),u=v(g);{var ee=x=>{var L=ie(),W=v(L);Re(W,()=>r.child,()=>({props:e(c)})),t(x,L)},y=x=>{var L=so();it(L,()=>({...e(c)}));var W=l(L);Re(W,()=>r.children??pt),a(L),t(x,L)};R(u,x=>{r.child?x(ee):x(y,!1)})}t(Q,g),qe()}var lo=i("<div><!></div>");function io(Q,r){Je(r,!0);let _=M(r,"ref",15,null),A=M(r,"id",19,Mt),b=ut(r,["$$slots","$$events","$$legacy","children","child","ref","id"]);const f=Br({id:Ee.with(()=>A()),ref:Ee.with(()=>_(),y=>_(y))}),n=G(()=>mt(b,f.props));var c=ie(),g=v(c);{var u=y=>{var x=ie(),L=v(x);Re(L,()=>r.child,()=>({props:e(n)})),t(y,x)},ee=y=>{var x=lo();it(x,()=>({...e(n)}));var L=l(x);Re(L,()=>r.children??pt),a(x),t(y,x)};R(g,y=>{r.child?y(u):y(ee,!1)})}t(Q,c),qe()}var co=i("<div><div><!></div></div>"),uo=i("<!> <!>",1),vo=i("<div><div><!></div></div>"),fo=i("<!> <!>",1);function mo(Q,r){Je(r,!0);let _=M(r,"id",19,Mt),A=M(r,"ref",15,null),b=M(r,"loop",3,!0),f=M(r,"onInteractOutside",3,ft),n=M(r,"forceMount",3,!1),c=M(r,"onEscapeKeydown",3,ft),g=M(r,"interactOutsideBehavior",3,"defer-otherwise-close"),u=M(r,"escapeKeydownBehavior",3,"defer-otherwise-close"),ee=M(r,"onOpenAutoFocus",3,ft),y=M(r,"onCloseAutoFocus",3,ft),x=M(r,"onFocusOutside",3,ft),L=M(r,"side",3,"right"),W=M(r,"trapFocus",3,!1),ce=ut(r,["$$slots","$$events","$$legacy","id","ref","children","child","loop","onInteractOutside","forceMount","onEscapeKeydown","interactOutsideBehavior","escapeKeydownBehavior","onOpenAutoFocus","onCloseAutoFocus","onFocusOutside","side","trapFocus"]);const p=Lr({id:Ee.with(()=>_()),loop:Ee.with(()=>b()),ref:Ee.with(()=>A(),s=>A(s)),isSub:!0,onCloseAutoFocus:Ee.with(()=>Le)});function Fe(s){const U=s.currentTarget.contains(s.target),Ce=Er[p.parentMenu.root.opts.dir.current].includes(s.key);if(U&&Ce){p.parentMenu.onClose();const N=p.parentMenu.triggerNode;N==null||N.focus(),s.preventDefault()}}const Be=G(()=>p.parentMenu.root.getAttr("sub-content")),ge=G(()=>mt(ce,p.props,{side:L(),onkeydown:Fe,[e(Be)]:""}));function Oe(s){ee()(s),!s.defaultPrevented&&(s.preventDefault(),p.parentMenu.root.isUsingKeyboard&&p.parentMenu.contentNode&&Nr.dispatch(p.parentMenu.contentNode))}function Le(s){y()(s),!s.defaultPrevented&&s.preventDefault()}function Ge(s){f()(s),!s.defaultPrevented&&p.parentMenu.onClose()}function Ue(s){c()(s),!s.defaultPrevented&&p.parentMenu.onClose()}function ze(s){var U;x()(s),!s.defaultPrevented&&eo(s.target)&&s.target.id!==((U=p.parentMenu.triggerNode)==null?void 0:U.id)&&p.parentMenu.onClose()}var He=ie(),Xe=v(He);{var lt=s=>{Vr(s,xt(()=>e(ge),{get interactOutsideBehavior(){return g()},get escapeKeydownBehavior(){return u()},onOpenAutoFocus:Oe,get enabled(){return p.parentMenu.opts.open.current},onInteractOutside:Ge,onEscapeKeydown:Ue,onFocusOutside:ze,preventScroll:!1,get loop(){return b()},get trapFocus(){return W()},popper:(Ce,N)=>{let Ie=()=>N==null?void 0:N().props,Pe=()=>N==null?void 0:N().wrapperProps;var w=uo();const se=G(()=>mt(Ie(),e(ge),{style:Xt("menu")}));var z=v(w);{var De=P=>{var C=ie(),K=v(C),k=ct(()=>({props:e(se),wrapperProps:Pe(),...p.snippetProps}));Re(K,()=>r.child,()=>e(k)),t(P,C)},ne=P=>{var C=co();it(C,()=>({...Pe()}));var K=l(C);it(K,()=>({...e(se)}));var k=l(K);Re(k,()=>r.children??pt),a(K),a(C),t(P,C)};R(z,P=>{r.child?P(De):P(ne,!1)})}var E=o(z,2);Yt(E,{get mounted(){return p.mounted},set mounted(P){p.mounted=P}}),t(Ce,w)},$$slots:{popper:!0}}))},d=(s,U)=>{{var Ce=N=>{Xr(N,xt(()=>e(ge),{get interactOutsideBehavior(){return g()},get escapeKeydownBehavior(){return u()},onCloseAutoFocus:Le,onOpenAutoFocus:Oe,get present(){return p.parentMenu.opts.open.current},onInteractOutside:Ge,onEscapeKeydown:Ue,onFocusOutside:ze,preventScroll:!1,get loop(){return b()},get trapFocus(){return W()},popper:(Pe,w)=>{let se=()=>w==null?void 0:w().props,z=()=>w==null?void 0:w().wrapperProps;var De=fo();const ne=G(()=>mt(se(),e(ge),{style:Xt("menu")}));var E=v(De);{var P=k=>{var V=ie(),j=v(V),X=ct(()=>({props:e(ne),wrapperProps:z(),...p.snippetProps}));Re(j,()=>r.child,()=>e(X)),t(k,V)},C=k=>{var V=vo();it(V,()=>({...z()}));var j=l(V);it(j,()=>({...e(ne)}));var X=l(j);Re(X,()=>r.children??pt),a(j),a(V),t(k,V)};R(E,k=>{r.child?k(P):k(C,!1)})}var K=o(E,2);Yt(K,{get mounted(){return p.mounted},set mounted(k){p.mounted=k}}),t(Pe,De)},$$slots:{popper:!0}}))};R(s,N=>{n()||N(Ce)},U)}};R(Xe,s=>{n()?s(lt):s(d,!1)})}t(Q,He),qe()}var po=i("<div><!></div>");function go(Q,r){Je(r,!0);let _=M(r,"id",19,Mt),A=M(r,"disabled",3,!1),b=M(r,"ref",15,null),f=M(r,"onSelect",3,ft),n=ut(r,["$$slots","$$events","$$legacy","id","disabled","ref","children","child","onSelect"]);const c=Ir({disabled:Ee.with(()=>A()),onSelect:Ee.with(()=>f()),id:Ee.with(()=>_()),ref:Ee.with(()=>b(),u=>b(u))}),g=G(()=>mt(n,c.props));Yr(Q,{get id(){return _()},children:(u,ee)=>{var y=ie(),x=v(y);{var L=ce=>{var p=ie(),Fe=v(p);Re(Fe,()=>r.child,()=>({props:e(g)})),t(ce,p)},W=ce=>{var p=po();it(p,()=>({...e(g)}));var Fe=l(p);Re(Fe,()=>r.children??pt),a(p),t(ce,p)};R(x,ce=>{r.child?ce(L):ce(W,!1)})}t(u,y)},$$slots:{default:!0}}),qe()}function _o(Q,r){Je(r,!0);let _=M(r,"ref",15,null),A=ut(r,["$$slots","$$events","$$legacy","ref"]);var b=ie(),f=v(b);m(f,()=>io,(n,c)=>{c(n,xt({"data-slot":"dropdown-menu-group"},()=>A,{get ref(){return _()},set ref(g){_(g)}}))}),t(Q,b),qe()}function rr(Q,r){Je(r,!0);let _=M(r,"ref",15,null),A=ut(r,["$$slots","$$events","$$legacy","ref","class"]);var b=ie(),f=v(b);const n=G(()=>St("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-(--radix-dropdown-menu-content-transform-origin) z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-lg",r.class));m(f,()=>mo,(c,g)=>{g(c,xt({"data-slot":"dropdown-menu-sub-content",get class(){return e(n)}},()=>A,{get ref(){return _()},set ref(u){_(u)}}))}),t(Q,b),qe()}var ho=i("<!> <!>",1);function or(Q,r){Je(r,!0);let _=M(r,"ref",15,null),A=ut(r,["$$slots","$$events","$$legacy","ref","class","inset","children"]);var b=ie(),f=v(b);const n=G(()=>St("data-highlighted:bg-accent data-highlighted:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground outline-hidden [&_svg:not([class*='text-'])]:text-muted-foreground flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm data-[disabled]:pointer-events-none data-[inset]:pl-8 data-[disabled]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",r.class));m(f,()=>go,(c,g)=>{g(c,xt({"data-slot":"dropdown-menu-sub-trigger",get"data-inset"(){return r.inset},get class(){return e(n)}},()=>A,{get ref(){return _()},set ref(u){_(u)},children:(u,ee)=>{var y=ho(),x=v(y);Re(x,()=>r.children??pt);var L=o(x,2);Zr(L,{class:"ml-auto size-4"}),t(u,y)},$$slots:{default:!0}}))}),t(Q,b),qe()}var bo=i('<button> <span class="text-muted-foreground ml-2 text-xs"> </span></button>'),xo=i('<h3 class="text-primary mb-3 text-sm font-medium"> </h3>'),$o=i('<span class="text-muted-foreground text-xs"> </span>'),yo=i('<span class="text-muted-foreground">+</span>'),wo=i('<kbd class="bg-muted text-muted-foreground border-border min-w-[28px] rounded-md border px-2 py-1 text-center text-xs font-medium shadow-sm"> </kbd> <!>',1),Po=i('<div class="hover:bg-muted/50 flex items-center justify-between rounded-md p-1"><div class="flex flex-col"><span class="text-sm font-medium"> </span> <!></div> <div class="ml-4 flex items-center gap-1"></div></div>'),ko=i('<div class="mb-8 mt-4 last:mb-0"><!> <div class="space-y-4"></div></div>'),So=i('<div class="flex h-full flex-col items-center justify-center py-12"><!> <h3 class="mb-2 text-lg font-medium">No shortcuts found</h3> <p class="text-muted-foreground text-sm">Try a different search term</p></div>'),Co=i("<!> <!>",1),Do=i(`<div class="border-border flex items-center justify-between border-b px-6 py-4"><!> <!></div> <div class="flex flex-col"><div class="border-border bg-muted text-foreground relative border-b px-6 py-2"><div class="absolute inset-y-0 left-6 flex items-center"><!></div> <input type="text" placeholder="Search shortcuts" class="placeholder:text-muted-foreground w-full rounded-md py-2 pl-10 pr-4 text-sm focus:outline-none"/></div> <div class="grid max-h-[60vh] grid-cols-[220px_1fr]"><div class="border-border overflow-y-auto border-r p-4"></div> <!></div> <div class="border-border text-muted-foreground border-t px-6 py-4 text-center text-sm"><p>Press <kbd class="bg-muted text-muted-foreground border-border min-w-[28px] rounded-md border px-2 py-0.5 text-center text-xs font-medium shadow-sm">Alt</kbd> + <kbd class="bg-muted text-muted-foreground border-border min-w-[28px] rounded-md border px-2 py-0.5 text-center text-xs font-medium shadow-sm">/</kbd> anywhere in the app to open this dialog</p> <p class="mt-2 text-xs">All shortcuts use Alt+ combinations for consistency across browsers and operating
            systems</p></div></div>`,1),Mo=i("<!> <!>",1);function Ao(Q,r){Je(r,!1);const _=ot(),A=ot(),b=ot();let f=M(r,"open",12,!1),n=ot("");Lt(()=>{{const c=()=>{f(!f())};return document.addEventListener("toggle-keyboard-shortcuts",c),()=>{document.removeEventListener("toggle-keyboard-shortcuts",c)}}}),kt(()=>Jt,()=>{D(_,Jt.map(c=>({name:c.name,shortcuts:c.shortcuts.map(g=>({action:g.action,keys:g.keys,description:g.description}))})))}),kt(()=>(e(n),e(_)),()=>{D(A,e(n)?e(_).map(c=>({name:c.name,shortcuts:c.shortcuts.filter(g=>g.action.toLowerCase().includes(e(n).toLowerCase())||g.keys.toLowerCase().includes(e(n).toLowerCase())||g.description&&g.description.toLowerCase().includes(e(n).toLowerCase()))})).filter(c=>c.shortcuts.length>0):e(_))}),kt(()=>e(_),()=>{D(b,e(_).length>0?e(_)[0].name:"")}),ar(),Et(),yr(Q,{get open(){return f()},set open(c){f(c)},children:(c,g)=>{Gr(c,{children:(u,ee)=>{var y=Mo(),x=v(y);Hr(x,{});var L=o(x,2);wr(L,{class:"bg-background",children:(W,ce)=>{var p=Do(),Fe=v(p),Be=l(Fe);Qr(Be,{class:"flex items-center text-lg font-semibold",children:(d,s)=>{S();var U=me("Keyboard Shortcuts");t(d,U)},$$slots:{default:!0}});var ge=o(Be,2);Pr(ge,{class:"text-muted-foreground hover:text-foreground rounded-full p-1 opacity-70 transition-opacity hover:opacity-100",children:(d,s)=>{Nt(d,{class:"h-4 w-4"})},$$slots:{default:!0}}),a(Fe);var Oe=o(Fe,2),Le=l(Oe),Ge=l(Le),Ue=l(Ge);Dt(Ue,{class:"text-muted-foreground h-4 w-4"}),a(Ge);var ze=o(Ge,2);nr(ze),a(Le);var He=o(Le,2),Xe=l(He);at(Xe,5,()=>e(A),st,(d,s)=>{var U=bo(),Ce=l(U),N=o(Ce),Ie=l(N);a(N),a(U),pe(()=>{Ct(U,1,`flex w-full flex-row items-center justify-between rounded-md px-3 py-2 text-left text-sm ${e(b)===e(s).name?"bg-primary/10 text-primary font-medium":"text-muted-foreground hover:bg-muted/50"}`),Z(Ce,`${e(s).name??""} `),Z(Ie,`(${e(s).shortcuts.length??""})`)}),Ve("click",U,()=>D(b,e(s).name)),t(d,U)}),a(Xe);var lt=o(Xe,2);fr(lt,{class:"h-full max-h-[320px] overflow-hidden px-4",children:(d,s)=>{var U=Co(),Ce=v(U);at(Ce,1,()=>e(A).filter(Pe=>Pe.name===e(b)||e(n)),st,(Pe,w)=>{var se=ko(),z=l(se);{var De=E=>{var P=xo(),C=l(P,!0);a(P),pe(()=>Z(C,e(w).name)),t(E,P)};R(z,E=>{(e(n)||e(A).length>1)&&E(De)})}var ne=o(z,2);at(ne,5,()=>e(w).shortcuts,st,(E,P)=>{var C=Po(),K=l(C),k=l(K),V=l(k,!0);a(k);var j=o(k,2);{var X=_e=>{var Ke=$o(),Ne=l(Ke,!0);a(Ke),pe(()=>Z(Ne,e(P).description)),t(_e,Ke)};R(j,_e=>{e(P).description&&_e(X)})}a(K);var Te=o(K,2);at(Te,5,()=>e(P).keys.split("+"),st,(_e,Ke,Ne)=>{var Qe=wo(),je=v(Qe),nt=l(je,!0);a(je);var Me=o(je,2);{var Ae=Ye=>{var dt=yo();t(Ye,dt)};R(Me,Ye=>{Ne<e(P).keys.split("+").length-1&&Ye(Ae)})}pe(()=>Z(nt,e(Ke))),t(_e,Qe)}),a(Te),a(C),pe(()=>Z(V,e(P).action)),t(E,C)}),a(ne),a(se),t(Pe,se)});var N=o(Ce,2);{var Ie=Pe=>{var w=So(),se=l(w);Dt(se,{class:"text-muted-foreground mb-4 h-12 w-12 opacity-20"}),S(4),a(w),t(Pe,w)};R(N,Pe=>{e(A).filter(w=>w.name===e(b)||e(n)).length===0&&Pe(Ie)})}t(d,U)},$$slots:{default:!0}}),a(He),S(2),a(Oe),ir(ze,()=>e(n),d=>D(n,d)),t(W,p)},$$slots:{default:!0}}),t(u,y)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),qe()}var Fo=i("<a> </a>"),Oo=i("<nav></nav>");function Ko(Q,r){Je(r,!1);const[_,A]=sr(),b=()=>Bt(lr,"$page",_);let f=M(r,"class",8,void 0);const n=[{href:"/dashboard/jobs",label:"Jobs"},{href:"/dashboard/automation",label:"Automation"},{href:"/dashboard/matches",label:"Matches"},{href:"/dashboard/tracker",label:"Tracker"},{href:"/dashboard/documents",label:"Documents"}];function c(u,ee=!1){return ee?b().url.pathname===u:b().url.pathname.includes(u)}Et();var g=Oo();at(g,5,()=>n,st,(u,ee)=>{let y=()=>e(ee).href,x=()=>e(ee).label,L=()=>e(ee).exact;var W=Fo(),ce=l(W,!0);a(W),pe(p=>{dr(W,"href",y()),Ct(W,1,p),Z(ce,x())},[()=>zt(St("relative rounded-md px-3 py-1.5 text-sm font-medium transition-all duration-200",c(y(),L())?"bg-card text-card-foreground shadow-sm ":"text-muted-foreground hover:text-foreground hover:bg-muted"))],ct),t(u,W)}),a(g),pe(u=>Ct(g,1,u),[()=>zt(St("bg-foreground/5 ring-ring/10 flex items-center gap-1 rounded-lg p-1 ring-1",f()))],ct),t(Q,g),qe(),A()}const jo=no;var Bo=i('<span class="text-xs">⌘</span>'),Lo=i('<span class="text-xs">Ctrl</span>'),No=i('<div class="flex items-center justify-center p-6"><div class="border-t-primary border-muted h-5 w-5 animate-spin rounded-full border-2"></div> <span class="text-muted-foreground ml-2 text-sm">Searching...</span></div>'),Eo=i('<div class="hover:bg-accent flex items-center justify-between rounded-md px-2 py-1.5"><div class="flex flex-1 items-center gap-2"><button type="button" class="flex h-4 w-4 items-center justify-center"><!></button> <button type="button" class="flex-1 cursor-pointer text-left text-sm"> </button></div> <button type="button" class="hover:bg-muted ml-2 flex h-5 w-5 items-center justify-center rounded-full" aria-label="Remove from recent searches"><!></button></div>'),Io=i('<div class="p-4"><h2 class="text-muted-foreground mb-2 text-xs font-medium">Recent</h2> <div class="space-y-1"></div></div>'),To=i('<div class="p-6 text-center text-sm">Type at least 2 characters to search</div>'),Ro=i('<div class="p-6 text-center"><p class="text-muted-foreground text-sm"> </p> <div class="mt-4"><h3 class="mb-2 text-sm font-semibold">Try searching for:</h3> <div class="text-muted-foreground space-y-2 text-sm"><button type="button" class="hover:bg-accent mt-2 flex w-full cursor-pointer items-center rounded-md px-2 py-1 text-left"><span class="ml-2">Users</span></button> <button type="button" class="hover:bg-accent mt-2 flex w-full cursor-pointer items-center rounded-md px-2 py-1 text-left"><span class="ml-2">Jobs</span></button> <button type="button" class="hover:bg-accent mt-2 flex w-full cursor-pointer items-center rounded-md px-2 py-1 text-left"><span class="ml-2">Documents</span></button></div></div></div>'),Uo=i("<!> <!>",1),zo=i('<div class="flex items-center gap-2"><!> <div><div class="font-medium"> </div> <div class="text-muted-foreground text-xs"> </div></div></div> <div class="ml-auto"><span class="bg-muted text-muted-foreground rounded-full px-2 py-0.5 text-xs font-medium"> </span></div>',1),Jo=i('<!> <div><div class="font-medium"> </div> <div class="text-muted-foreground text-xs"> </div></div>',1),qo=i('<!> <div><div class="font-medium"> </div> <div class="text-muted-foreground text-xs"> </div></div>',1),Go=i("<!> <!> <!>",1),Ho=i('<div class="flex h-[80vh] flex-col border-none"><div class="flex items-center border-b p-3"><!> <input type="search" placeholder="Search users, jobs, and documents..." class="placeholder:text-muted-foreground flex-1 border-none bg-transparent py-3 text-sm outline-none focus:ring-0 disabled:cursor-not-allowed disabled:opacity-50"/> <button class="cursor-point bg-muted pointer-events-none hidden h-5 select-none items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-50 sm:flex"><span class="text-xs">ESC</span> <!></button></div> <div class="flex-1 overflow-hidden"><!></div></div>'),Qo=i("<!> <!>",1),Wo=i('<div><button type="button" class="border-input bg-background text-muted-foreground hover:bg-accent hover:text-accent-foreground flex h-9 w-full items-center gap-2 rounded-md border px-3 py-2 text-sm shadow-sm transition-colors"><!> <span class="hidden md:inline-flex">Search</span> <kbd class="bg-muted ml-auto hidden h-5 select-none items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex"><!> K</kbd></button> <!></div>');function Vo(Q,r){Je(r,!1);function _(d){return e(n)&&setTimeout(()=>d.focus(),50),{update(){e(n)&&setTimeout(()=>d.focus(),50)}}}const A="Search...";let b=M(r,"className",8,""),f=ot(""),n=ot(!1),c=ot({users:[],jobs:[],documents:[]}),g=ot(!1),u=ot([]);Lt(()=>{{const d=localStorage.getItem("recentSearches");if(d)try{D(u,JSON.parse(d))}catch(s){console.error("Failed to parse recent searches:",s),D(u,[])}}});function ee(d){d.length<2||(D(u,e(u).filter(s=>s.query!==d)),D(u,[{query:d,favorite:!1},...e(u).slice(0,9)]),localStorage.setItem("recentSearches",JSON.stringify(e(u))))}function y(d){D(u,e(u).filter(s=>s.query!==d.query)),localStorage.setItem("recentSearches",JSON.stringify(e(u)))}function x(d){D(u,e(u).map(s=>s.query===d.query?{...s,favorite:!s.favorite}:s)),localStorage.setItem("recentSearches",JSON.stringify(e(u)))}async function L(){if(e(f).length<2){D(c,{users:[],jobs:[],documents:[]});return}D(g,!0),D(n,!0),ht.set(Be);try{const d=await fetch("/api/search/global",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e(f),limit:10})});if(!d.ok)throw new Error(`Search failed with status: ${d.status}`);const s=await d.json();D(c,{users:s.users.hits||[],jobs:s.jobs.hits||[],documents:s.documents.hits||[]}),(e(c).users.length>0||e(c).jobs.length>0||e(c).documents.length>0)&&ee(e(f))}catch(d){console.error("Search error:",d),D(c,{users:[],jobs:[],documents:[]})}finally{D(g,!1)}}function W(d){d.key==="Escape"?p():(d.key==="ArrowDown"||d.key==="ArrowUp")&&d.preventDefault()}function ce(d,s){switch(e(f).length>=2&&ee(e(f)),d){case"user":le(`/dashboard/admin/users/${s.id}`);break;case"job":le(`/dashboard/jobs/${s.id}`);break;case"document":le(`/dashboard/documents/${s.id}`);break}p()}function p(){D(n,!1),D(f,""),ht.update(d=>d===Be?null:d)}function Fe(d){(d.ctrlKey||d.metaKey)&&d.key==="k"&&(d.preventDefault(),ht.set(Be),D(n,!0))}const Be=`global-search-${Math.random().toString(36).substring(2,9)}`;let ge=null;Lt(()=>{window.addEventListener("keydown",Fe),ge=ht.subscribe(d=>{d&&d!==Be&&e(n)&&p()})}),gr(()=>{window.removeEventListener("keydown",Fe),ge&&ge()}),kt(()=>e(f),()=>{e(f)!==void 0&&L()}),ar(),Et();var Oe=Wo(),Le=l(Oe),Ge=l(Le);Dt(Ge,{class:"h-4 w-4"});var Ue=o(Ge,4),ze=l(Ue);{var He=d=>{var s=Bo();t(d,s)},Xe=d=>{var s=Lo();t(d,s)};R(ze,d=>{/Mac|iPod|iPhone|iPad/.test(navigator.userAgent)?d(He):d(Xe,!1)})}S(),a(Ue),a(Le);var lt=o(Le,2);return kr(lt,{get open(){return e(n)},set open(d){D(n,d)},children:(d,s)=>{Sr(d,{children:(U,Ce)=>{var N=Qo(),Ie=v(N);Cr(Ie,{});var Pe=o(Ie,2);Dr(Pe,{class:"gap-0 p-0 sm:max-w-[90vw] md:max-w-[65vw] lg:max-w-[50vw]",children:(w,se)=>{var z=Ho(),De=l(z),ne=l(De);Dt(ne,{class:"mr-2 h-4 w-4 shrink-0 opacity-50"});var E=o(ne,2);nr(E),Ut(()=>ir(E,()=>e(f),V=>D(f,V))),Ut(()=>Ve("keydown",E,W)),_r(E,V=>_==null?void 0:_(V));var P=o(E,2),C=o(l(P),2);Nt(C,{class:"h-4 w-4"}),a(P),a(De);var K=o(De,2),k=l(K);fr(k,{class:"h-full",children:(V,j)=>{xr(V,{class:"h-full",children:(X,Te)=>{$r(X,{class:"h-full",children:(_e,Ke)=>{var Ne=ie(),Qe=v(Ne);{var je=Me=>{jo(Me,{children:(Ae,Ye)=>{var dt=No();t(Ae,dt)},$$slots:{default:!0}})},nt=(Me,Ae)=>{{var Ye=Ze=>{var vt=ie(),gt=v(vt);{var J=B=>{var ue=Io(),I=o(l(ue),2);at(I,5,()=>[...e(u)].sort((te,ve)=>Number(ve.favorite)-Number(te.favorite)),st,(te,ve)=>{var ke=Eo(),H=l(ke),$=l(H),de=l($);const he=ct(()=>e(ve).favorite?"text-warning":"text-muted-foreground opacity-50");Mr(de,{get class(){return`h-3 w-3 flex-shrink-0 ${e(he)??""}`}}),a($);var T=o($,2),re=l(T,!0);a(T),a(H);var be=o(H,2),h=l(be);Nt(h,{class:"h-3 w-3 opacity-50"}),a(be),a(ke),pe(()=>{dr($,"aria-label",e(ve).favorite?"Remove from favorites":"Add to favorites"),Z(re,e(ve).query)}),Ve("click",$,()=>x(e(ve))),Ve("click",T,()=>{D(f,e(ve).query)}),Ve("click",be,()=>y(e(ve))),t(te,ke)}),a(I),a(ue),t(B,ue)},F=B=>{qt(B,{children:(ue,I)=>{var te=To();t(ue,te)},$$slots:{default:!0}})};R(gt,B=>{e(u).length>0?B(J):B(F,!1)})}t(Ze,vt)},dt=(Ze,vt)=>{{var gt=F=>{qt(F,{children:(B,ue)=>{var I=Ro(),te=l(I),ve=l(te);a(te);var ke=o(te,2),H=o(l(ke),2),$=l(H),de=o($,2),he=o(de,2);a(H),a(ke),a(I),pe(()=>Z(ve,`No results for "${e(f)??""}"`)),Ve("click",$,()=>D(f,"users")),Ve("click",de,()=>D(f,"jobs")),Ve("click",he,()=>D(f,"documents")),t(B,I)},$$slots:{default:!0}})},J=F=>{var B=Go(),ue=v(B);{var I=$=>{Ft($,{heading:"Users",class:"text-muted-foreground px-2 py-1.5 text-xs font-medium",children:(de,he)=>{var T=ie(),re=v(T);at(re,1,()=>e(c).users,st,(be,h)=>{Ot(be,{onSelect:()=>ce("user",e(h)),class:"aria-selected:bg-accent aria-selected:text-accent-foreground flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none",children:(fe,xe)=>{var Se=zo(),O=v(Se),q=l(O);cr(q,{class:"h-6 w-6",children:(At,It)=>{var oe=Uo(),ae=v(oe);{var ye=Y=>{const Tt=ct(()=>e(h).name||"User");vr(Y,{get src(){return e(h).image},get alt(){return e(Tt)}})};R(ae,Y=>{e(h).image&&Y(ye)})}var rt=o(ae,2);ur(rt,{class:"border-border bg-muted rounded-full border text-xs",children:(Y,Tt)=>{S();var Rt=me();pe(Pt=>Z(Rt,Pt),[()=>{var Pt;return((Pt=e(h).name)==null?void 0:Pt.charAt(0))||"U"}],ct),t(Y,Rt)},$$slots:{default:!0}}),t(At,oe)},$$slots:{default:!0}});var $e=o(q,2),et=l($e),We=l(et,!0);a(et);var tt=o(et,2),$t=l(tt,!0);a(tt),a($e),a(O);var yt=o(O,2),_t=l(yt),wt=l(_t,!0);a(_t),a(yt),pe(()=>{Z(We,e(h).name||"Unknown User"),Z($t,e(h).email),Z(wt,e(h).role)}),t(fe,Se)},$$slots:{default:!0}})}),t(de,T)},$$slots:{default:!0}})};R(ue,$=>{e(c).users.length>0&&$(I)})}var te=o(ue,2);{var ve=$=>{Ft($,{heading:"Jobs",class:"text-muted-foreground px-2 py-1.5 text-xs font-medium",children:(de,he)=>{var T=ie(),re=v(T);at(re,1,()=>e(c).jobs,st,(be,h)=>{Ot(be,{onSelect:()=>ce("job",e(h)),class:"aria-selected:bg-accent aria-selected:text-accent-foreground flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none",children:(fe,xe)=>{var Se=Jo(),O=v(Se);Ar(O,{class:"mr-2 h-4 w-4 shrink-0 opacity-50"});var q=o(O,2),$e=l(q),et=l($e,!0);a($e);var We=o($e,2),tt=l(We,!0);a(We),a(q),pe(()=>{Z(et,e(h).title),Z(tt,e(h).company)}),t(fe,Se)},$$slots:{default:!0}})}),t(de,T)},$$slots:{default:!0}})};R(te,$=>{e(c).jobs.length>0&&$(ve)})}var ke=o(te,2);{var H=$=>{Ft($,{heading:"Documents",class:"text-muted-foreground px-2 py-1.5 text-xs font-medium",children:(de,he)=>{var T=ie(),re=v(T);at(re,1,()=>e(c).documents,st,(be,h)=>{Ot(be,{onSelect:()=>ce("document",e(h)),class:"aria-selected:bg-accent aria-selected:text-accent-foreground flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none",children:(fe,xe)=>{var Se=qo(),O=v(Se);Fr(O,{class:"mr-2 h-4 w-4 shrink-0 opacity-50"});var q=o(O,2),$e=l(q),et=l($e,!0);a($e);var We=o($e,2),tt=l(We,!0);a(We),a(q),pe(()=>{Z(et,e(h).title),Z(tt,e(h).type)}),t(fe,Se)},$$slots:{default:!0}})}),t(de,T)},$$slots:{default:!0}})};R(ke,$=>{e(c).documents.length>0&&$(H)})}t(F,B)};R(Ze,F=>{e(c).users.length===0&&e(c).jobs.length===0&&e(c).documents.length===0?F(gt):F(J,!1)},vt)}};R(Me,Ze=>{e(f).length<2?Ze(Ye):Ze(dt,!1)},Ae)}};R(Qe,Me=>{e(g)?Me(je):Me(nt,!1)})}t(_e,Ne)},$$slots:{default:!0}})},$$slots:{default:!0}})},$$slots:{default:!0}}),a(K),a(z),Ve("click",P,p),t(w,z)},$$slots:{default:!0}}),t(U,N)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),a(Oe),pe(()=>Ct(Oe,1,`relative ${b()??""}`)),Ve("click",Le,()=>{ht.set(Be),D(n,!0)}),t(Q,Oe),hr(r,"placeholder",A),qe({placeholder:A})}var Xo=i('<span class="rounded-sm bg-gradient-to-r from-orange-500 to-purple-600 p-0.5"><!></span> <!>',1),Yo=i("<!> <!> <!> <!> <!> <!> <!> <!> <!>",1),Zo=i("<!> <!>",1),ea=i("<!> Light Mode",1),ta=i("<!> Dark Mode",1),ra=i("<!> System",1),oa=i("<!> Light",1),aa=i("<!> Dark",1),sa=i("<!> System",1),na=i("<!> <!> <!>",1),da=i("<!> <!>",1),la=i("<!> Team Settings",1),ia=i("<!> Workspace Settings",1),ca=i("<!> <!> <!> <!>",1),ua=i("<!> <!> <!> <!> <!> <!> <!> <!> <!> <!>",1),va=i("<!> <!>",1),fa=i("<!> Invite & Earn",1),ma=i("<!> <!>",1),pa=i("<p> </p>"),ga=i("<!> <!>",1),_a=i("<!> <!>",1),ha=i("<p> </p>"),ba=i("<!> <!>",1),xa=i('<div class="flex h-16 items-center px-4"><div class="flex items-center gap-4"><!> <!></div> <div class="flex flex-1 justify-center"><!></div> <div class="flex items-center gap-4"><!> <!> <div class="inline-block rounded-lg bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 p-0.5"><!></div></div></div>'),$a=i('<div class="h-[calc(100vh-65px)]"><div class="bg-secondary/80"><!></div> <main class="bg-secondary/80 h-full px-4"><div class="bg-background border-border h-full flex-1 space-y-4 rounded-lg rounded-b-none border border-b-0"><!></div></main></div>  <!>',1);function ks(Q,r){var Be;Je(r,!0);const[_,A]=sr(),b=()=>Bt(lr,"$page",_),f=()=>Bt(jr,"$unreadCount",_),n=(Be=r.data)==null?void 0:Be.user;let c=mr(!1);console.log("Initializing WebSocket singleton..."),qr.initialize(),console.log("Fetching notifications from server..."),Kr.fetchFromServer(),console.log("Initializing resume parsing handler..."),Or();function g(){console.log("Opening keyboard shortcuts dialog"),D(c,!e(c)),console.log("Keyboard shortcuts dialog state:",e(c))}function u(ge,Oe=!1){return Oe?b().url.pathname===ge:b().url.pathname.includes(ge)}var ee=$a(),y=v(ee),x=l(y),L=l(x);m(L,()=>zr,(ge,Oe)=>{Oe(ge,{children:(Le,Ge)=>{var Ue=xa(),ze=l(Ue),He=l(ze);m(He,()=>Tr,(w,se)=>{se(w,{children:(z,De)=>{var ne=va(),E=v(ne);m(E,()=>Rr,(C,K)=>{K(C,{class:"border-foreground/10 hover:bg-muted/5  flex h-9 cursor-pointer items-center gap-2 rounded-md border py-1.5 pl-2 pr-1",children:(k,V)=>{var j=Xo(),X=v(j),Te=l(X);pr(Te,{fill:"white",stroke:"black",class:"bg-secondary h-4 w-4"}),a(X);var _e=o(X,2);Wr(_e,{class:"text-muted-foreground h-4 w-4"}),t(k,j)},$$slots:{default:!0}})});var P=o(E,2);m(P,()=>Ur,(C,K)=>{K(C,{align:"start",class:"w-60",sideOffset:10,children:(k,V)=>{var j=ua(),X=v(j);Vo(X,{className:"mb-2 m-1",placeholder:"Search..."});var Te=o(X,2);const _e=G(()=>u("/dashboard",!0)?"bg-accent text-accent-foreground":"");m(Te,()=>we,(J,F)=>{F(J,{onclick:()=>le("/dashboard"),get class(){return e(_e)},children:(B,ue)=>{S();var I=me("Go to dashboard");t(B,I)},$$slots:{default:!0}})});var Ke=o(Te,2);m(Ke,()=>bt,(J,F)=>{F(J,{})});var Ne=o(Ke,2);const Qe=G(()=>u("/dashboard/settings/profile")?"bg-accent text-accent-foreground":"");m(Ne,()=>we,(J,F)=>{F(J,{onclick:()=>le("/dashboard/settings/profile"),get class(){return e(Qe)},children:(B,ue)=>{S();var I=me("Profiles");t(B,I)},$$slots:{default:!0}})});var je=o(Ne,2);const nt=G(()=>u("/dashboard/settings/analysis")?"bg-accent text-accent-foreground":"");m(je,()=>we,(J,F)=>{F(J,{onclick:()=>le("/dashboard/settings/analysis"),get class(){return e(nt)},children:(B,ue)=>{S();var I=me("Analysis");t(B,I)},$$slots:{default:!0}})});var Me=o(je,2);const Ae=G(()=>u("/dashboard/notifications")?"bg-accent text-accent-foreground":"");m(Me,()=>we,(J,F)=>{F(J,{onclick:()=>le("/dashboard/notifications"),get class(){return e(Ae)},children:(B,ue)=>{S();var I=me("Notifications");t(B,I)},$$slots:{default:!0}})});var Ye=o(Me,2);m(Ye,()=>_o,(J,F)=>{F(J,{children:(B,ue)=>{var I=ie(),te=v(I);m(te,()=>Gt,(ve,ke)=>{ke(ve,{children:(H,$)=>{var de=Zo(),he=v(de);m(he,()=>or,(re,be)=>{be(re,{children:(h,fe)=>{S();var xe=me("Help & Account");t(h,xe)},$$slots:{default:!0}})});var T=o(he,2);m(T,()=>rr,(re,be)=>{be(re,{children:(h,fe)=>{var xe=Yo(),Se=v(xe);const O=G(()=>u("/help")?"bg-accent text-accent-foreground":"");m(Se,()=>we,(oe,ae)=>{ae(oe,{onclick:()=>le("/help"),get class(){return e(O)},children:(ye,rt)=>{S();var Y=me("Help Center");t(ye,Y)},$$slots:{default:!0}})});var q=o(Se,2);m(q,()=>we,(oe,ae)=>{ae(oe,{onclick:g,children:(ye,rt)=>{S();var Y=me("Keyboard Shortcuts");t(ye,Y)},$$slots:{default:!0}})});var $e=o(q,2);const et=G(()=>u("/resources")?"bg-accent text-accent-foreground":"");m($e,()=>we,(oe,ae)=>{ae(oe,{onclick:()=>le("/resources"),get class(){return e(et)},children:(ye,rt)=>{S();var Y=me("Resources");t(ye,Y)},$$slots:{default:!0}})});var We=o($e,2);m(We,()=>we,(oe,ae)=>{ae(oe,{onclick:()=>le("https://autoapply.featurebase.app/roadmap"),children:(ye,rt)=>{S();var Y=me("Roadmap");t(ye,Y)},$$slots:{default:!0}})});var tt=o(We,2);m(tt,()=>we,(oe,ae)=>{ae(oe,{onclick:()=>le("https://autoapply.featurebase.app/"),children:(ye,rt)=>{S();var Y=me("Submit Feedback");t(ye,Y)},$$slots:{default:!0}})});var $t=o(tt,2);const yt=G(()=>u("/system-status")?"bg-accent text-accent-foreground":"");m($t,()=>we,(oe,ae)=>{ae(oe,{onclick:()=>le("/system-status"),get class(){return e(yt)},children:(ye,rt)=>{S();var Y=me("System Status");t(ye,Y)},$$slots:{default:!0}})});var _t=o($t,2);m(_t,()=>bt,(oe,ae)=>{ae(oe,{})});var wt=o(_t,2);const At=G(()=>u("/dashboard/settings")?"bg-accent text-accent-foreground":"");m(wt,()=>we,(oe,ae)=>{ae(oe,{onclick:()=>le("/dashboard/settings"),get class(){return e(At)},children:(ye,rt)=>{S();var Y=me("Manage Account");t(ye,Y)},$$slots:{default:!0}})});var It=o(wt,2);m(It,()=>we,(oe,ae)=>{ae(oe,{onclick:()=>le("/auth/sign-out"),children:(ye,rt)=>{S();var Y=me("Sign Out");t(ye,Y)},$$slots:{default:!0}})}),t(h,xe)},$$slots:{default:!0}})}),t(H,de)},$$slots:{default:!0}})}),t(B,I)},$$slots:{default:!0}})});var dt=o(Ye,2);m(dt,()=>bt,(J,F)=>{F(J,{})});var Ze=o(dt,2);m(Ze,()=>Gt,(J,F)=>{F(J,{children:(B,ue)=>{var I=da(),te=v(I);m(te,()=>or,(ke,H)=>{H(ke,{children:($,de)=>{var he=ie(),T=v(he);{var re=h=>{var fe=ea(),xe=v(fe);Zt(xe,{class:"mr-2 h-4 w-4"}),S(),t(h,fe)},be=(h,fe)=>{{var xe=O=>{var q=ta(),$e=v(q);er($e,{class:"mr-2 h-4 w-4"}),S(),t(O,q)},Se=O=>{var q=ra(),$e=v(q);tr($e,{class:"mr-2 h-4 w-4"}),S(),t(O,q)};R(h,O=>{Vt.current==="dark"?O(xe):O(Se,!1)},fe)}};R(T,h=>{Vt.current==="light"?h(re):h(be,!1)})}t($,he)},$$slots:{default:!0}})});var ve=o(te,2);m(ve,()=>rr,(ke,H)=>{H(ke,{children:($,de)=>{var he=na(),T=v(he);m(T,()=>we,(h,fe)=>{fe(h,{onclick:()=>jt("light"),children:(xe,Se)=>{var O=oa(),q=v(O);Zt(q,{class:"mr-2 h-4 w-4"}),S(),t(xe,O)},$$slots:{default:!0}})});var re=o(T,2);m(re,()=>we,(h,fe)=>{fe(h,{onclick:()=>jt("dark"),children:(xe,Se)=>{var O=aa(),q=v(O);er(q,{class:"mr-2 h-4 w-4"}),S(),t(xe,O)},$$slots:{default:!0}})});var be=o(re,2);m(be,()=>we,(h,fe)=>{fe(h,{onclick:()=>jt("system"),children:(xe,Se)=>{var O=sa(),q=v(O);tr(q,{class:"mr-2 h-4 w-4"}),S(),t(xe,O)},$$slots:{default:!0}})}),t($,he)},$$slots:{default:!0}})}),t(B,I)},$$slots:{default:!0}})});var vt=o(Ze,2);{var gt=J=>{var F=ca(),B=v(F);m(B,()=>bt,(H,$)=>{$(H,{})});var ue=o(B,2);const I=G(()=>u("/dashboard/settings/team")?"bg-accent text-accent-foreground":"");m(ue,()=>we,(H,$)=>{$(H,{onclick:()=>le("/dashboard/settings/team"),get class(){return e(I)},children:(de,he)=>{var T=la(),re=v(T);oo(re,{class:"mr-2 h-4 w-4"}),S(),t(de,T)},$$slots:{default:!0}})});var te=o(ue,2);m(te,()=>bt,(H,$)=>{$(H,{})});var ve=o(te,2);const ke=G(()=>u("/dashboard/settings")?"bg-accent text-accent-foreground":"");m(ve,()=>we,(H,$)=>{$(H,{onclick:()=>le("/dashboard/settings"),get class(){return e(ke)},children:(de,he)=>{var T=ia(),re=v(T);ao(re,{class:"mr-2 h-4 w-4"}),S(),t(de,T)},$$slots:{default:!0}})}),t(J,F)};R(vt,J=>{n&&"teamId"in n&&n.teamId&&J(gt)})}t(k,j)},$$slots:{default:!0}})}),t(z,ne)},$$slots:{default:!0}})});var Xe=o(He,2);const lt=G(()=>`border-foreground/10 gap-2 border ${u("/dashboard/settings/referrals")?"bg-accent text-accent-foreground":""}`);Kt(Xe,{variant:"ghost",onclick:()=>le("/dashboard/settings/referrals"),get class(){return e(lt)},children:(w,se)=>{var z=fa(),De=v(z);to(De,{class:"h-4 w-4"}),S(),t(w,z)},$$slots:{default:!0}}),a(ze);var d=o(ze,2),s=l(d);Ko(s,{}),a(d);var U=o(d,2),Ce=l(U);m(Ce,()=>Ht,(w,se)=>{se(w,{children:(z,De)=>{var ne=ga(),E=v(ne);m(E,()=>Qt,(C,K)=>{K(C,{children:(k,V)=>{const j=G(()=>`relative h-9 w-9 p-0 ${u("/dashboard/notifications")?"bg-accent text-accent-foreground":""}`);Kt(k,{variant:"ghost",size:"sm",onclick:()=>le("/dashboard/notifications"),get class(){return e(j)},children:(X,Te)=>{var _e=ma(),Ke=v(_e);ro(Ke,{class:"h-4 w-4"});var Ne=o(Ke,2);{var Qe=je=>{Jr(je,{variant:"destructive",class:"absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs",children:(nt,Me)=>{S();var Ae=me();pe(()=>Z(Ae,f()>99?"99+":f())),t(nt,Ae)},$$slots:{default:!0}})};R(Ne,je=>{f()>0&&je(Qe)})}t(X,_e)},$$slots:{default:!0}})},$$slots:{default:!0}})});var P=o(E,2);m(P,()=>Wt,(C,K)=>{K(C,{children:(k,V)=>{var j=pa(),X=l(j);a(j),pe(()=>Z(X,`Notifications ${f()>0?`(${f()} unread)`:""}`)),t(k,j)},$$slots:{default:!0}})}),t(z,ne)},$$slots:{default:!0}})});var N=o(Ce,2);m(N,()=>Ht,(w,se)=>{se(w,{children:(z,De)=>{var ne=ba(),E=v(ne);m(E,()=>Qt,(C,K)=>{K(C,{children:(k,V)=>{cr(k,{class:"h-8 w-8",children:(j,X)=>{var Te=_a(),_e=v(Te);const Ke=G(()=>n==null?void 0:n.image),Ne=G(()=>(n==null?void 0:n.name)||(n==null?void 0:n.email));vr(_e,{get src(){return e(Ke)},get alt(){return e(Ne)}});var Qe=o(_e,2);ur(Qe,{children:(je,nt)=>{S();var Me=me();pe(Ae=>Z(Me,Ae),[()=>{var Ae;return n!=null&&n.name?n.name.charAt(0).toUpperCase():((Ae=n==null?void 0:n.email)==null?void 0:Ae.charAt(0).toUpperCase())||"U"}]),t(je,Me)},$$slots:{default:!0}}),t(j,Te)},$$slots:{default:!0}})},$$slots:{default:!0}})});var P=o(E,2);m(P,()=>Wt,(C,K)=>{K(C,{children:(k,V)=>{var j=ha(),X=l(j,!0);a(j),pe(()=>Z(X,(n==null?void 0:n.name)||(n==null?void 0:n.email))),t(k,j)},$$slots:{default:!0}})}),t(z,ne)},$$slots:{default:!0}})});var Ie=o(N,2),Pe=l(Ie);Kt(Pe,{size:"sm",onclick:()=>le("/pricing"),class:"font-semibold",children:(w,se)=>{S();var z=me("Upgrade");t(w,z)},$$slots:{default:!0}}),a(Ie),a(U),a(Ue),t(Le,Ue)},$$slots:{default:!0}})}),a(x);var W=o(x,2),ce=l(W),p=l(ce);Re(p,()=>r.children),a(ce),a(W),a(y);var Fe=o(y,2);Ao(Fe,{get open(){return e(c)},set open(ge){D(c,ge,!0)}}),t(Q,ee),qe(),A()}export{ks as component};
