import{c,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(s,r){const t=m(r,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"}],["circle",{cx:"12",cy:"7",r:"4"}]];f(s,d({name:"user"},()=>t,{get iconNode(){return e},children:(a,$)=>{var o=c(),n=p(o);l(n,r,"default",{},null),i(a,o)},$$slots:{default:!0}}))}export{x as U};
