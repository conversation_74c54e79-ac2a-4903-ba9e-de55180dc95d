import{c as i,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as c}from"./BBa424ah.js";import{l as p,s as $}from"./Btcx8l8F.js";import{I as m}from"./D4f2twK-.js";function N(t,o){const e=p(o,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M5 12h14"}]];m(t,$({name:"minus"},()=>e,{get iconNode(){return s},children:(r,f)=>{var n=i(),a=d(n);c(a,o,"default",{},null),l(r,n)},$$slots:{default:!0}}))}function M(t,o){const e=p(o,["children","$$slots","$$events","$$legacy"]),s=[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7"}],["polyline",{points:"16 17 22 17 22 11"}]];m(t,$({name:"trending-down"},()=>e,{get iconNode(){return s},children:(r,f)=>{var n=i(),a=d(n);c(a,o,"default",{},null),l(r,n)},$$slots:{default:!0}}))}export{N as M,M as T};
