import{f as i,a as t,t as O}from"../chunks/BasJTneF.js";import{o as P}from"../chunks/nZgk9enP.js";import{p as C,a as G,s as c,c as f,d as r,k as u,g as d,r as p,n as g,f as J}from"../chunks/CGmarHxI.js";import{i as y}from"../chunks/u21ee2wt.js";import{c as L}from"../chunks/BvdI7LR8.js";import{B as N}from"../chunks/B1K98fMG.js";import"../chunks/CgXBgsce.js";import{t as v}from"../chunks/DjPYYl4Z.js";var $=i('<div class="my-8 flex justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>'),q=i('<div class="mb-6 rounded-md bg-green-100 p-4 text-green-800"><p class="font-medium">You are an admin</p> <p class="mt-1 text-sm">You have access to all admin features</p></div> <div class="mt-4"><a href="/dashboard/settings/admin" class="text-primary hover:underline">Go to Admin Dashboard</a></div>',1),z=i(`<div class="mb-6 rounded-md bg-yellow-100 p-4 text-yellow-800"><p class="font-medium">You are not an admin</p> <p class="mt-1 text-sm">You don't have access to admin features</p></div> <!>`,1),H=i('<div class="bg-card rounded-lg border p-6 shadow-sm"><h2 class="mb-4 text-xl font-semibold">Admin Status</h2> <!></div>'),I=i('<div class="container mx-auto py-8"><div class="mx-auto max-w-md"><h1 class="mb-6 text-2xl font-bold">Admin Access</h1> <!></div></div>');function ea(x,_){C(_,!0);let m=u(!1),o=u(!0),h=u("");P(async()=>{try{const a=await fetch("/api/user/me");if(a.ok){const e=await a.json();r(m,e.isAdmin===!0),r(h,e.email,!0)}}catch(a){console.error("Error checking user role:",a)}finally{r(o,!1)}});async function w(){r(o,!0);try{const a=await fetch("/api/admin/make-admin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d(h)})}),e=await a.json();a.ok?(v.success("You are now an admin"),r(m,!0),setTimeout(()=>window.location.href="/dashboard/settings/admin",1500)):v.error(e.error||"Failed to make you an admin")}catch{v.error("Failed to make you an admin")}finally{r(o,!1)}}var l=I(),b=f(l),k=c(f(b),2);{var A=a=>{var e=$();t(a,e)},Y=a=>{var e=H(),j=c(f(e),2);{var S=s=>{var n=q();g(2),t(s,n)},T=s=>{var n=z(),B=c(J(n),2);L(B,()=>N,(D,E)=>{E(D,{onclick:w,get disabled(){return d(o)},class:"w-full",children:(F,K)=>{g();var M=O("Make Yourself an Admin");t(F,M)},$$slots:{default:!0}})}),t(s,n)};y(j,s=>{d(m)?s(S):s(T,!1)})}p(e),t(a,e)};y(k,a=>{d(o)?a(A):a(Y,!1)})}p(b),p(l),t(x,l),G()}export{ea as component};
