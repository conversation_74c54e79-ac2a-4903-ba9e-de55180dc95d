import{d as e}from"./sanity-DV0NwVOn.js";const n=e("create",{"start-in-create-dialog.cta.learn-more":"Learn more.","studio-create-link-banner.text":"This document is linked to Sanity Create","create-link-info.tooltip":"Learn more","create-link-info-popover.eyebrow-title":"Sanity Create","create-link-info-popover.eyebrow-badge":"Early access","create-link-info-popover.header":"Idea-first authoring","create-link-info-popover.text":"Write naturally in an AI-powered editor. Your content automatically maps to Studio fields as you type.","edit-in-create-button.text":"Edit with Sanity Create","unlink-from-create-button.text":"Unlink","unlink-from-create-dialog.header":"Switch editing to Studio?","unlink-from-create-dialog.first-paragraph":"You’re unlinking “<strong>{{title}}</strong>” from Sanity Create so it can be edited here.","unlink-from-create-dialog.second-paragraph":"You’ll keep your content in both places. Any new changes in Sanity Create will stop syncing to this Studio.","unlink-from-create-dialog.cancel.text":"Cancel","unlink-from-create-dialog.document.untitled.text":"Untitled","unlink-from-create-dialog.unlink.text":"Unlink now"});export{n as default};
