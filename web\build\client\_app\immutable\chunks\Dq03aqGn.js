const g=t=>t;function h(t){const n=t-1;return n*n*n+1}function x(t){return t<.5?4*t*t*t:.5*Math.pow(2*t-2,3)+1}function u(t){const n=typeof t=="string"&&t.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return n?[parseFloat(n[1]),n[2]||"px"]:[t,"px"]}function k(t,{delay:n=0,duration:o=400,easing:s=g}={}){const c=+getComputedStyle(t).opacity;return{delay:n,duration:o,easing:s,css:e=>`opacity: ${e*c}`}}function b(t,{delay:n=0,duration:o=400,easing:s=h,x:c=0,y:e=0,opacity:r=0}={}){const f=getComputedStyle(t),a=+f.opacity,p=f.transform==="none"?"":f.transform,l=a*(1-r),[y,_]=u(c),[$,m]=u(e);return{delay:n,duration:o,easing:s,css:(i,d)=>`
			transform: ${p} translate(${(1-i)*y}${_}, ${(1-i)*$}${m});
			opacity: ${a-l*d}`}}function C(t,{delay:n=0,speed:o,duration:s,easing:c=x}={}){let e=t.getTotalLength();const r=getComputedStyle(t);return r.strokeLinecap!=="butt"&&(e+=parseInt(r.strokeWidth)),s===void 0?o===void 0?s=800:s=e/o:typeof s=="function"&&(s=s(e)),{delay:n,duration:s,easing:c,css:(f,a)=>`
			stroke-dasharray: ${e};
			stroke-dashoffset: ${a*e};
		`}}export{k as a,C as d,b as f};
