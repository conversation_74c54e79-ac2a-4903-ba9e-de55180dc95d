import{c as i,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(s,o){const r=c(o,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}],["polyline",{points:"16 17 21 12 16 7"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12"}]];f(s,d({name:"log-out"},()=>r,{get iconNode(){return e},children:(a,$)=>{var t=i(),n=p(t);m(n,o,"default",{},null),l(a,t)},$$slots:{default:!0}}))}export{x as L};
