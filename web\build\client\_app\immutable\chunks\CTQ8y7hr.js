import{c as p,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(r,o){const s=l(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8"}],["path",{d:"M21 3v5h-5"}]];f(r,d({name:"rotate-cw"},()=>s,{get iconNode(){return a},children:(e,$)=>{var t=p(),n=i(t);m(n,o,"default",{},null),c(e,t)},$$slots:{default:!0}}))}export{y as R};
