import{c,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as i}from"./BBa424ah.js";import{l as $,s as m}from"./Btcx8l8F.js";import{I as p}from"./D4f2twK-.js";function y(o,e){const a=$(e,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2Z"}],["path",{d:"m9 10 2 2 4-4"}]];p(o,m({name:"bookmark-check"},()=>a,{get iconNode(){return r},children:(s,h)=>{var t=c(),n=d(t);i(n,e,"default",{},null),l(s,t)},$$slots:{default:!0}}))}function N(o,e){const a=$(e,["children","$$slots","$$events","$$legacy"]),r=[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}],["path",{d:"m9 14 2 2 4-4"}]];p(o,m({name:"clipboard-check"},()=>a,{get iconNode(){return r},children:(s,h)=>{var t=c(),n=d(t);i(n,e,"default",{},null),l(s,t)},$$slots:{default:!0}}))}function x(o,e){const a=$(e,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15"}]];p(o,m({name:"flag"},()=>a,{get iconNode(){return r},children:(s,h)=>{var t=c(),n=d(t);i(n,e,"default",{},null),l(s,t)},$$slots:{default:!0}}))}export{y as B,N as C,x as F};
