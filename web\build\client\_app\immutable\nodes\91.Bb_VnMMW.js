import{f as v,a as n,t as pe}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{p as fe,l as ue,b as xe,f as b,t as $,a as _e,s as o,c as s,d as W,g as e,m as X,r,n as he,e as B}from"../chunks/CGmarHxI.js";import{s as _}from"../chunks/CIt1g2O9.js";import{i as Y}from"../chunks/u21ee2wt.js";import{e as z,i as G}from"../chunks/C3w0v0gR.js";import{c as Z}from"../chunks/BvdI7LR8.js";import{a as N}from"../chunks/B-Xjo-Yt.js";import{i as ge}from"../chunks/BIEMS98f.js";import{p as ye}from"../chunks/Btcx8l8F.js";import{S as be}from"../chunks/C6g8ubaU.js";import{C as ee}from"../chunks/DuGukytH.js";import{C as te}from"../chunks/Cdn-N1RY.js";import{C as re}from"../chunks/GwmmX_iF.js";import{B as $e}from"../chunks/B1K98fMG.js";import{A as ae}from"../chunks/Cs0qIT7f.js";import{F as se}from"../chunks/ChqRiddM.js";import{S as we}from"../chunks/yW0TxTga.js";import{C as Ce}from"../chunks/ITUnHPIu.js";import{B as Re}from"../chunks/CDnvByek.js";var Te=v('<div class="bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full"><!></div> <h3 class="text-xl font-semibold"> </h3>',1),Ae=v('<p class="mb-4 flex-1 text-gray-600"> </p> <div class="mt-auto flex items-center justify-between"><span class="text-sm capitalize text-gray-500"> </span> <a class="text-primary inline-flex items-center text-sm font-medium hover:underline">Access Resource <!></a></div>',1),je=v("<!> <!>",1),Pe=v('<div class="mb-16"><h2 class="mb-6 text-2xl font-semibold">Featured Resources</h2> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3"></div></div>'),Fe=v('<div class="bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full"><!></div> <h3 class="text-xl font-semibold"> </h3>',1),Se=v('<p class="mb-4 flex-1 text-gray-600"> </p> <div class="mt-auto flex items-center justify-between"><span class="text-sm capitalize text-gray-500"> </span> <a class="text-primary inline-flex items-center text-sm font-medium hover:underline">Access Resource <!></a></div>',1),ke=v("<!> <!>",1),Be=v('<div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3"></div>'),ze=v('<div class="py-8 text-center"><p class="text-gray-500">No resources found for this category.</p></div>'),Ge=v(`<!> <div class="container mx-auto px-4 py-16"><div><div class="mb-12 text-center"><h1 class="mb-4 text-4xl font-bold">Career Resources</h1> <p class="mx-auto max-w-2xl text-lg text-gray-600">Free tools, templates, and guides to help you succeed in your job search and career
        development.</p></div> <div class="mb-12"><div class="flex flex-wrap gap-2"></div></div> <!> <div><h2 class="mb-6 text-2xl font-semibold"> </h2> <!></div></div></div>`,1);function rt(oe,q){fe(q,!1);const j=X();let E=ye(q,"data",8);const le=["All","Templates","Tools","Guides","Reports"],H=E().resources||[],I=E().featuredResources||H.filter(t=>t.featured);let i=X("All");function L(t){if(typeof t!="string")return t;switch(t){case"FileText":return se;case"Briefcase":return Re;case"Calculator":return Ce;case"Search":return we;default:return se}}function O(t){return t.slug&&t.slug.current?`/resources/${t.slug.current}`:t.link||"#"}ue(()=>e(i),()=>{W(j,H.filter(t=>{const a=t.resourceType||t.type;return e(i)==="All"||e(i)==="Templates"&&(a==="resumeTemplate"||a==="coverLetterTemplate"||a==="template")||e(i)==="Tools"&&a==="tool"||e(i)==="Guides"&&a==="guide"||e(i)==="Reports"&&a==="report"}))}),xe(),ge();var U=Ge(),D=b(U);be(D,{title:"Career Resources | Hirli",description:"Free tools, templates, and guides to help you succeed in your job search and career development.",keywords:"resume templates, cover letter templates, ATS optimization, interview preparation, salary calculator, career planning, job market research"});var J=o(D,2),K=s(J),P=o(s(K),2),M=s(P);z(M,5,()=>le,G,(t,a)=>{const h=B(()=>e(i)===e(a)?"default":"outline");$e(t,{get variant(){return e(h)},size:"sm",$$events:{click:()=>W(i,e(a))},children:(m,p)=>{he();var w=pe();$(()=>_(w,e(a))),n(m,w)},$$slots:{default:!0}})}),r(M),r(P);var Q=o(P,2);{var ie=t=>{var a=Pe(),h=o(s(a),2);z(h,5,()=>I,G,(m,p)=>{ee(m,{class:"flex h-full flex-col overflow-hidden border shadow-md",children:(w,S)=>{var C=je(),A=b(C);re(A,{class:"bg-primary/5 flex items-center gap-3 p-6",children:(T,g)=>{var l=Te(),c=b(l),f=s(c);Z(f,()=>L(e(p).icon),(x,y)=>{y(x,{class:"h-5 w-5"})}),r(c);var d=o(c,2),u=s(d,!0);r(d),$(()=>_(u,e(p).title)),n(T,l)},$$slots:{default:!0}});var R=o(A,2);te(R,{class:"flex flex-1 flex-col p-6",children:(T,g)=>{var l=Ae(),c=b(l),f=s(c,!0);r(c);var d=o(c,2),u=s(d),x=s(u,!0);r(u);var y=o(u,2),k=o(s(y));ae(k,{class:"ml-1 h-3 w-3"}),r(y),r(d),$(ve=>{_(f,e(p).description),_(x,e(p).resourceType||e(p).type||"Resource"),N(y,"href",ve)},[()=>O(e(p))],B),n(T,l)},$$slots:{default:!0}}),n(w,C)},$$slots:{default:!0}})}),r(h),r(a),n(t,a)};Y(Q,t=>{e(i)==="All"&&I.length>0&&t(ie)})}var V=o(Q,2),F=s(V),ce=s(F,!0);r(F);var de=o(F,2);{var ne=t=>{var a=Be();z(a,5,()=>e(j),G,(h,m)=>{ee(h,{class:"flex h-full flex-col overflow-hidden border shadow-md",children:(p,w)=>{var S=ke(),C=b(S);re(C,{class:"bg-primary/5 flex items-center gap-3 p-6",children:(R,T)=>{var g=Fe(),l=b(g),c=s(l);Z(c,()=>L(e(m).icon),(u,x)=>{x(u,{class:"h-5 w-5"})}),r(l);var f=o(l,2),d=s(f,!0);r(f),$(()=>_(d,e(m).title)),n(R,g)},$$slots:{default:!0}});var A=o(C,2);te(A,{class:"flex flex-1 flex-col p-6",children:(R,T)=>{var g=Se(),l=b(g),c=s(l,!0);r(l);var f=o(l,2),d=s(f),u=s(d,!0);r(d);var x=o(d,2),y=o(s(x));ae(y,{class:"ml-1 h-3 w-3"}),r(x),r(f),$(k=>{_(c,e(m).description),_(u,e(m).resourceType||e(m).type||"Resource"),N(x,"href",k)},[()=>O(e(m))],B),n(R,g)},$$slots:{default:!0}}),n(p,S)},$$slots:{default:!0}})}),r(a),n(t,a)},me=t=>{var a=ze();n(t,a)};Y(de,t=>{e(j).length>0?t(ne):t(me,!1)})}r(V),r(K),r(J),$(()=>_(ce,e(i)==="All"?"All Resources":e(i))),n(oe,U),_e()}export{rt as component};
