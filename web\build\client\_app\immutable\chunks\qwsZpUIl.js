import{c as n,a as c}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as i}from"./BBa424ah.js";import{l as m,s as l}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function w(t,o){const s=m(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"}],["path",{d:"M21 3v5h-5"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"}],["path",{d:"M8 16H3v5"}]];f(t,l({name:"refresh-cw"},()=>s,{get iconNode(){return a},children:(e,h)=>{var r=n(),p=d(r);i(p,o,"default",{},null),c(e,r)},$$slots:{default:!0}}))}export{w as R};
