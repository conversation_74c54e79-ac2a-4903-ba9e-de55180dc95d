import{g as o}from"./BosuxZz1.js";function u(r){return typeof r!="object"&&typeof r!="function"||r===null}function s(){this.childBranches=new WeakMap,this.primitiveKeys=new Map,this.hasValue=!1,this.value=void 0}s.prototype.has=function(e){var t=u(e)?this.primitiveKeys.get(e):e;return t?this.childBranches.has(t):!1};s.prototype.get=function(e){var t=u(e)?this.primitiveKeys.get(e):e;return t?this.childBranches.get(t):void 0};s.prototype.resolveBranch=function(e){if(this.has(e))return this.get(e);var t=new s,a=this.createKey(e);return this.childBranches.set(a,t),t};s.prototype.setValue=function(e){return this.hasValue=!0,this.value=e};s.prototype.createKey=function(e){if(u(e)){var t={};return this.primitiveKeys.set(e,t),t}return e};s.prototype.clear=function(){if(arguments.length===0)this.childBranches=new WeakMap,this.primitiveKeys.clear(),this.hasValue=!1,this.value=void 0;else if(arguments.length===1){var e=arguments[0];if(u(e)){var t=this.primitiveKeys.get(e);t&&(this.childBranches.delete(t),this.primitiveKeys.delete(e))}else this.childBranches.delete(e)}else{var a=arguments[0];if(this.has(a)){var i=this.get(a);i.clear.apply(i,Array.prototype.slice.call(arguments,1))}}};var f=function(e){var t=new s;function a(){var i=Array.prototype.slice.call(arguments),n=i.reduce(function(l,h){return l.resolveBranch(h)},t);if(n.hasValue)return n.value;var c=e.apply(null,i);return n.setValue(c)}return a.clear=t.clear.bind(t),a},p=f;const v=o(p),m=v;new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");async function d(r,e,t){const a=await r.safeParseAsync(e,{errorMap:t});return a.success?{data:a.data,success:!0}:{issues:a.error.issues.map(({message:i,path:n})=>({message:i,path:n})),success:!1}}function g(r,e){return{superFormValidationLibrary:"zod",validate:async t=>d(r,t,e==null?void 0:e.errorMap)}}const z=m(g);export{z};
