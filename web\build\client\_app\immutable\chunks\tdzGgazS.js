import{c as O,a as c,f as k}from"./BasJTneF.js";import{p as T,f as d,a as q,au as G,g as n,x as C,c as ee,r as te,s as j,e as de,n as ue}from"./CGmarHxI.js";import{s as A,c as oe}from"./ncUU1dSD.js";import{c as K}from"./BvdI7LR8.js";import{p as o,r as H,s as P}from"./Btcx8l8F.js";import{F as fe,E as pe,D as ve,T as ge,S as $,P as me}from"./BaVT73bJ.js";import{b as _e,c as he,d as be,s as Pe,e as ye}from"./DMoa_yM9.js";import{i as V}from"./u21ee2wt.js";import{e as re}from"./B-Xjo-Yt.js";import{b as _,m as X}from"./BfX7a-t9.js";import{P as Se}from"./XESq6qWN.js";import{u as ae}from"./CnMg5bH0.js";import{n as R}from"./DX6rZLP_.js";import{X as we}from"./CnpHcmx3.js";function De(f,e){T(e,!0);let s=o(e,"open",15,!1),i=o(e,"onOpenChange",3,R);_e({variant:_.with(()=>"dialog"),open:_.with(()=>s(),u=>{s(u),i()(u)})});var r=O(),p=d(r);A(p,()=>e.children??G),c(f,r),q()}var Fe=k("<button><!></button>");function xe(f,e){T(e,!0);let s=o(e,"id",19,ae),i=o(e,"ref",15,null),r=o(e,"disabled",3,!1),p=H(e,["$$slots","$$events","$$legacy","children","child","id","ref","disabled"]);const u=he({variant:_.with(()=>"close"),id:_.with(()=>s()),ref:_.with(()=>i(),l=>i(l)),disabled:_.with(()=>!!r())}),v=C(()=>X(p,u.props));var g=O(),y=d(g);{var h=l=>{var t=O(),a=d(t);A(a,()=>e.child,()=>({props:n(v)})),c(l,t)},S=l=>{var t=Fe();re(t,()=>({...n(v)}));var a=ee(t);A(a,()=>e.children??G),te(t),c(l,t)};V(y,l=>{e.child?l(h):l(S,!1)})}c(f,g),q()}var Ce=k("<!> <!>",1),Oe=k("<!> <div><!></div>",1);function Ae(f,e){T(e,!0);let s=o(e,"id",19,ae),i=o(e,"ref",15,null),r=o(e,"forceMount",3,!1),p=o(e,"onCloseAutoFocus",3,R),u=o(e,"onOpenAutoFocus",3,R),v=o(e,"onEscapeKeydown",3,R),g=o(e,"onInteractOutside",3,R),y=o(e,"trapFocus",3,!0),h=o(e,"preventScroll",3,!0),S=o(e,"restoreScrollDelay",3,null),l=H(e,["$$slots","$$events","$$legacy","id","children","child","ref","forceMount","onCloseAutoFocus","onOpenAutoFocus","onEscapeKeydown","onInteractOutside","trapFocus","preventScroll","restoreScrollDelay"]);const t=be({id:_.with(()=>s()),ref:_.with(()=>i(),w=>i(w))}),a=C(()=>X(l,t.props)),z=C(()=>t.root.opts.open.current||r());Se(f,P(()=>n(a),{get forceMount(){return r()},get present(){return n(z)},presence:W=>{const B=C(()=>Pe({forceMount:r(),present:t.root.opts.open.current,trapFocus:y(),open:t.root.opts.open.current}));fe(W,{loop:!0,get trapFocus(){return n(B)},get onOpenAutoFocus(){return u()},get id(){return s()},onCloseAutoFocus:D=>{var m;p()(D),!D.defaultPrevented&&((m=t.root.triggerNode)==null||m.focus())},focusScope:(D,m)=>{let N=()=>m==null?void 0:m().props;pe(D,P(()=>n(a),{get enabled(){return t.root.opts.open.current},onEscapeKeydown:F=>{v()(F),!F.defaultPrevented&&t.root.handleClose()},children:(F,se)=>{ve(F,P(()=>n(a),{get enabled(){return t.root.opts.open.current},onInteractOutside:b=>{g()(b),!b.defaultPrevented&&t.root.handleClose()},children:(b,Y)=>{ge(b,P(()=>n(a),{get enabled(){return t.root.opts.open.current},children:(ne,Ke)=>{var Z=O(),le=d(Z);{var ce=x=>{var E=Ce(),I=d(E);{var M=U=>{$(U,{get preventScroll(){return h()},get restoreScrollDelay(){return S()}})};V(I,U=>{t.root.opts.open.current&&U(M)})}var L=j(I,2),Q=de(()=>({props:X(n(a),N()),...t.snippetProps}));A(L,()=>e.child,()=>n(Q)),c(x,E)},ie=x=>{var E=Oe(),I=d(E);$(I,{get preventScroll(){return h()}});var M=j(I,2);re(M,Q=>({...Q}),[()=>X(n(a),N())]);var L=ee(M);A(L,()=>e.children??G),te(M),c(x,E)};V(le,x=>{e.child?x(ce):x(ie,!1)})}c(ne,Z)},$$slots:{default:!0}}))},$$slots:{default:!0}}))},$$slots:{default:!0}}))},$$slots:{focusScope:!0}})},$$slots:{presence:!0}})),q()}function ke(f,e){T(e,!0);let s=o(e,"ref",15,null),i=H(e,["$$slots","$$events","$$legacy","ref","class"]);var r=O(),p=d(r);const u=C(()=>oe("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e.class));K(p,()=>ye,(v,g)=>{g(v,P({"data-slot":"dialog-overlay",get class(){return n(u)}},()=>i,{get ref(){return s()},set ref(y){s(y)}}))}),c(f,r),q()}var ze=k('<!> <span class="sr-only">Close</span>',1),Ee=k("<!> <!>",1),Ie=k("<!> <!>",1);function We(f,e){T(e,!0);let s=o(e,"ref",15,null),i=H(e,["$$slots","$$events","$$legacy","ref","class","portalProps","children"]);var r=O(),p=d(r);K(p,()=>Me,(u,v)=>{v(u,P(()=>e.portalProps,{children:(g,y)=>{var h=Ie(),S=d(h);K(S,()=>ke,(a,z)=>{z(a,{})});var l=j(S,2);const t=C(()=>oe("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed left-[50%] top-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e.class));K(l,()=>Ae,(a,z)=>{z(a,P({"data-slot":"dialog-content",get class(){return n(t)}},()=>i,{get ref(){return s()},set ref(w){s(w)},children:(w,W)=>{var B=Ee(),J=d(B);A(J,()=>e.children??G);var D=j(J,2);K(D,()=>xe,(m,N)=>{N(m,{class:"ring-offset-background focus:ring-ring rounded-xs focus:outline-hidden absolute right-4 top-4 opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",children:(F,se)=>{var b=ze(),Y=d(b);we(Y,{}),ue(2),c(F,b)},$$slots:{default:!0}})}),c(w,B)},$$slots:{default:!0}}))}),c(g,h)},$$slots:{default:!0}}))}),c(f,r),q()}const Ye=De,Me=me;export{ke as D,Me as P,Ye as R,We as a,xe as b,Ae as c,De as d};
