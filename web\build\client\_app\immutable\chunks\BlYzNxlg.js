var oe=r=>{throw TypeError(r)};var H=(r,e,t)=>e.has(r)||oe("Cannot "+t);var n=(r,e,t)=>(H(r,e,"read from private field"),t?t.call(r):e.get(r)),i=(r,e,t)=>e.has(r)?oe("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(r):e.set(r,t),f=(r,e,t,s)=>(H(r,e,"write to private field"),s?s.call(r,t):e.set(r,t),t),m=(r,e,t)=>(H(r,e,"access private method"),t);import{u as he,i as ke,o as ze,k as G,g as l,d as z,v as re,a_ as ne,x as fe}from"./CGmarHxI.js";import{o as Z}from"./CmxjS0TN.js";import{c as me,b as A}from"./BfX7a-t9.js";import{M as Ee}from"./BQ5jqT_2.js";const ge=typeof window<"u"?window:void 0;function Me(r){let e=r.activeElement;for(;e!=null&&e.shadowRoot;){const t=e.shadowRoot.activeElement;if(t===e)break;e=t}return e}var E,T;class Ie{constructor(e={}){i(this,E);i(this,T);const{window:t=ge,document:s=t==null?void 0:t.document}=e;t!==void 0&&(f(this,E,s),f(this,T,me(c=>{const u=Z(t,"focusin",c),o=Z(t,"focusout",c);return()=>{u(),o()}})))}get current(){var e;return(e=n(this,T))==null||e.call(this),n(this,E)?Me(n(this,E)):null}}E=new WeakMap,T=new WeakMap;new Ie;function Ae(r,e){switch(r){case"post":ke(e);break;case"pre":he(e);break}}function pe(r,e,t,s={}){const{lazy:c=!1}=s;let u=!c,o=Array.isArray(r)?[]:void 0;Ae(e,()=>{const a=Array.isArray(r)?r.map(h=>h()):r();if(!u){u=!0,o=a;return}const d=ze(()=>t(a,o));return o=a,d})}function se(r,e,t){pe(r,"post",e,t)}function Te(r,e,t){pe(r,"pre",e,t)}se.pre=Te;function _e(r,e){switch(r){case"local":return e.localStorage;case"session":return e.sessionStorage}}var y,w,M,b,_,g,O,p,F,R;class we{constructor(e,t,s={}){i(this,p);i(this,y);i(this,w);i(this,M);i(this,b);i(this,_);i(this,g,G(0));i(this,O,e=>{e.key!==n(this,w)||e.newValue===null||(f(this,y,m(this,p,F).call(this,e.newValue)),z(n(this,g),l(n(this,g))+1))});const{storage:c="local",serializer:u={serialize:JSON.stringify,deserialize:JSON.parse},syncTabs:o=!0,window:a=ge}=s;if(f(this,y,t),f(this,w,e),f(this,M,u),a===void 0)return;const d=_e(c,a);f(this,b,d);const h=d.getItem(e);h!==null?f(this,y,m(this,p,F).call(this,h)):m(this,p,R).call(this,t),o&&c==="local"&&f(this,_,me(()=>Z(a,"storage",n(this,O))))}get current(){var c,u;(c=n(this,_))==null||c.call(this),l(n(this,g));const e=m(this,p,F).call(this,(u=n(this,b))==null?void 0:u.getItem(n(this,w)))??n(this,y),t=new WeakMap,s=o=>{if(o===null||(o==null?void 0:o.constructor.name)==="Date"||typeof o!="object")return o;let a=t.get(o);return a||(a=new Proxy(o,{get:(d,h)=>(l(n(this,g)),s(Reflect.get(d,h))),set:(d,h,B)=>(z(n(this,g),l(n(this,g))+1),Reflect.set(d,h,B),m(this,p,R).call(this,e),!0)}),t.set(o,a)),a};return s(e)}set current(e){m(this,p,R).call(this,e),z(n(this,g),l(n(this,g))+1)}}y=new WeakMap,w=new WeakMap,M=new WeakMap,b=new WeakMap,_=new WeakMap,g=new WeakMap,O=new WeakMap,p=new WeakSet,F=function(e){try{return n(this,M).deserialize(e)}catch(t){console.error(`Error when parsing "${e}" from persisted store "${n(this,w)}"`,t);return}},R=function(e){var t;try{e!=null&&((t=n(this,b))==null||t.setItem(n(this,w),n(this,M).serialize(e)))}catch(s){console.error(`Error when writing value from persisted store "${n(this,w)}" to ${n(this,b)}`,s)}};function ce(r){return r.filter(e=>e.length>0)}const ye={getItem:r=>null,setItem:(r,e)=>{}},C=typeof document<"u",X=A("mode-watcher-mode"),Y=A("mode-watcher-theme"),Le=["dark","light","system"];function ue(r){return typeof r!="string"?!1:Le.includes(r)}var L,W,V,j,S,x,K;class Ve{constructor(){i(this,x);i(this,L,"system");i(this,W,C?localStorage:ye);i(this,V,n(this,W).getItem(X.current));i(this,j,ue(n(this,V))?n(this,V):n(this,L));i(this,S,G(re(m(this,x,K).call(this))));ne(()=>se.pre(()=>X.current,(e,t)=>{const s=l(n(this,S)).current;z(n(this,S),m(this,x,K).call(this,s),!0),t&&localStorage.removeItem(t)}))}get current(){return l(n(this,S)).current}set current(e){l(n(this,S)).current=e}}L=new WeakMap,W=new WeakMap,V=new WeakMap,j=new WeakMap,S=new WeakMap,x=new WeakSet,K=function(e=n(this,j)){return new we(X.current,e,{serializer:{serialize:t=>t,deserialize:t=>ue(t)?t:n(this,L)}})};var J,P,q,N;class xe{constructor(){i(this,J);i(this,P,!0);i(this,q,G(re(n(this,J))));i(this,N,typeof window<"u"&&typeof window.matchMedia=="function"?new Ee("prefers-color-scheme: light"):{current:!1});ne(()=>{he(()=>{n(this,P)&&this.query()})}),this.query=this.query.bind(this),this.tracking=this.tracking.bind(this)}query(){C&&z(n(this,q),n(this,N).current?"light":"dark",!0)}tracking(e){f(this,P,e)}get current(){return l(n(this,q))}}J=new WeakMap,P=new WeakMap,q=new WeakMap,N=new WeakMap;const ee=new Ve,Pe=new xe;var Q,I,U,k,v,te;class qe{constructor(){i(this,v);i(this,Q,C?localStorage:ye);i(this,I,n(this,Q).getItem(Y.current));i(this,U,n(this,I)===null||n(this,I)===void 0?"":n(this,I));i(this,k,G(re(m(this,v,te).call(this))));ne(()=>se.pre(()=>Y.current,(e,t)=>{const s=l(n(this,k)).current;z(n(this,k),m(this,v,te).call(this,s),!0),t&&localStorage.removeItem(t)}))}get current(){return l(n(this,k)).current}set current(e){l(n(this,k)).current=e}}Q=new WeakMap,I=new WeakMap,U=new WeakMap,k=new WeakMap,v=new WeakSet,te=function(e=n(this,U)){return new we(Y.current,e,{serializer:{serialize:t=>typeof t!="string"?"":t,deserialize:t=>t}})};const $=new qe;let ae,le,de=!1;function be(r){if(typeof document>"u")return;if(!de){de=!0,r();return}clearTimeout(ae),clearTimeout(le);const e=document.createElement("style"),t=document.createTextNode(`* {
     -webkit-transition: none !important;
     -moz-transition: none !important;
     -o-transition: none !important;
     -ms-transition: none !important;
     transition: none !important;
  }`);e.appendChild(t);const s=()=>document.head.appendChild(e),c=()=>document.head.removeChild(e);if(typeof window.getComputedStyle<"u"){s(),r(),window.getComputedStyle(e).opacity,c();return}if(typeof window.requestAnimationFrame<"u"){s(),r(),window.requestAnimationFrame(c);return}s(),ae=window.setTimeout(()=>{r(),le=window.setTimeout(c,120)},120)}const D=A(void 0),Se=A(!0),ve=A([]),Ce=A([]);function Be(){const r=fe(()=>{if(!C)return;const e=ee.current==="system"?Pe.current:ee.current,t=ce(ve.current),s=ce(Ce.current);function c(){const u=document.documentElement,o=document.querySelector('meta[name="theme-color"]');e==="light"?(t.length&&u.classList.remove(...t),s.length&&u.classList.add(...s),u.style.colorScheme="light",o&&D.current&&o.setAttribute("content",D.current.light)):(s.length&&u.classList.remove(...s),t.length&&u.classList.add(...t),u.style.colorScheme="dark",o&&D.current&&o.setAttribute("content",D.current.dark))}return Se.current?be(c):c(),e});return{get current(){return l(r)}}}function De(){const r=fe(()=>{if($.current,!C)return;function e(){document.documentElement.setAttribute("data-theme",$.current)}return Se.current?be(e):e(),$.current});return{get current(){return l(r)}}}const je=Be(),Je=De();function Ne(r){ee.current=r}function Qe(r){$.current=r}function Ue(r){return r}function Ge({defaultMode:r="system",themeColors:e,darkClassNames:t=["dark"],lightClassNames:s=[],defaultTheme:c="",modeStorageKey:u="mode-watcher-mode",themeStorageKey:o="mode-watcher-theme"}){const a=document.documentElement,d=localStorage.getItem(u)??r,h=localStorage.getItem(o)??c,B=d==="light"||d==="system"&&window.matchMedia("(prefers-color-scheme: light)").matches;if(B?(t.length&&a.classList.remove(...t.filter(Boolean)),s.length&&a.classList.add(...s.filter(Boolean))):(s.length&&a.classList.remove(...s.filter(Boolean)),t.length&&a.classList.add(...t.filter(Boolean))),a.style.colorScheme=B?"light":"dark",e){const ie=document.querySelector('meta[name="theme-color"]');ie&&ie.setAttribute("content",d==="light"?e.light:e.dark)}h&&(a.setAttribute("data-theme",h),localStorage.setItem(o,h)),localStorage.setItem(u,d)}export{Ge as a,ve as b,Se as c,je as d,D as e,Ue as f,Je as g,Pe as h,ue as i,Qe as j,Ce as l,X as m,Ne as s,Y as t};
