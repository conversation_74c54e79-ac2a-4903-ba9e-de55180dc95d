import{c as p,a as f,f as B}from"./BasJTneF.js";import{p as P,f as h,a as b,au as M,g as u,x,c as q,r as N,s as X,e as ue}from"./CGmarHxI.js";import{c as k}from"./BvdI7LR8.js";import{p as s,r as A,s as y}from"./Btcx8l8F.js";import{s as F,c as E}from"./ncUU1dSD.js";import{b as fe,f as ge,g as ve,d as me,s as _e,D as he,e as pe}from"./DMoa_yM9.js";import{b as re}from"./B1K98fMG.js";import{i as J}from"./u21ee2wt.js";import{e as V}from"./B-Xjo-Yt.js";import{b as D,m as j}from"./BfX7a-t9.js";import{u as Y}from"./CnMg5bH0.js";import{b as ae}from"./5V1tIHTN.js";import{a as Pe}from"./OOsIR5sE.js";import{F as be,E as ye,D as xe,T as Ae,S as te,P as oe}from"./BaVT73bJ.js";import{P as we}from"./XESq6qWN.js";import{n as R}from"./DX6rZLP_.js";import{D as Se}from"./BKLOCbjP.js";function De(n,e){P(e,!0);let r=s(e,"open",15,!1),o=s(e,"onOpenChange",3,R);fe({variant:D.with(()=>"alert-dialog"),open:D.with(()=>r(),a=>{r(a),o()(a)})});var t=p(),l=h(t);F(l,()=>e.children??M),f(n,t),b()}var Fe=B("<button><!></button>");function Oe(n,e){P(e,!0);let r=s(e,"id",19,Y),o=s(e,"ref",15,null),t=A(e,["$$slots","$$events","$$legacy","children","child","id","ref"]);const l=ge({id:D.with(()=>r()),ref:D.with(()=>o(),m=>o(m))}),a=x(()=>j(t,l.props));var i=p(),g=h(i);{var v=m=>{var c=p(),_=h(c);F(_,()=>e.child,()=>({props:u(a)})),f(m,c)},w=m=>{var c=Fe();V(c,()=>({...u(a)}));var _=q(c);F(_,()=>e.children??M),N(c),f(m,c)};J(g,m=>{e.child?m(v):m(w,!1)})}f(n,i),b()}var Ce=B("<button><!></button>");function Ee(n,e){P(e,!0);let r=s(e,"id",19,Y),o=s(e,"ref",15,null),t=s(e,"disabled",3,!1),l=A(e,["$$slots","$$events","$$legacy","id","ref","children","child","disabled"]);const a=ve({id:D.with(()=>r()),ref:D.with(()=>o(),c=>o(c)),disabled:D.with(()=>!!t())}),i=x(()=>j(l,a.props));var g=p(),v=h(g);{var w=c=>{var _=p(),d=h(_);F(d,()=>e.child,()=>({props:u(i)})),f(c,_)},m=c=>{var _=Ce();V(_,()=>({...u(i)}));var d=q(_);F(d,()=>e.children??M),N(_),f(c,_)};J(v,c=>{e.child?c(w):c(m,!1)})}f(n,g),b()}var ke=B("<!> <!>",1),Be=B("<!> <div><!></div>",1);function Ie(n,e){P(e,!0);let r=s(e,"id",19,Y),o=s(e,"ref",15,null),t=s(e,"forceMount",3,!1),l=s(e,"interactOutsideBehavior",3,"ignore"),a=s(e,"onCloseAutoFocus",3,R),i=s(e,"onEscapeKeydown",3,R),g=s(e,"onOpenAutoFocus",3,R),v=s(e,"onInteractOutside",3,R),w=s(e,"preventScroll",3,!0),m=s(e,"trapFocus",3,!0),c=s(e,"restoreScrollDelay",3,null),_=A(e,["$$slots","$$events","$$legacy","id","children","child","ref","forceMount","interactOutsideBehavior","onCloseAutoFocus","onEscapeKeydown","onOpenAutoFocus","onInteractOutside","preventScroll","trapFocus","restoreScrollDelay"]);const d=me({id:D.with(()=>r()),ref:D.with(()=>o(),Z=>o(Z))}),O=x(()=>j(_,d.props)),L=x(()=>d.root.opts.open.current||t());we(n,y(()=>u(O),{get forceMount(){return t()},get present(){return u(L)},presence:se=>{const ne=x(()=>_e({forceMount:t(),present:d.root.opts.open.current,trapFocus:m(),open:d.root.opts.open.current}));be(se,{loop:!0,get trapFocus(){return u(ne)},get id(){return r()},onCloseAutoFocus:C=>{var S;a()(C),!C.defaultPrevented&&((S=d.root.triggerNode)==null||S.focus())},onOpenAutoFocus:C=>{g()(C),!C.defaultPrevented&&(C.preventDefault(),Pe(()=>{var S;(S=d.opts.ref.current)==null||S.focus()}))},focusScope:(C,S)=>{let $=()=>S==null?void 0:S().props;ye(C,y(()=>u(O),{get enabled(){return d.root.opts.open.current},onEscapeKeydown:G=>{i()(G),!G.defaultPrevented&&d.root.handleClose()},children:(G,je)=>{xe(G,y(()=>u(O),{get enabled(){return d.root.opts.open.current},get interactOutsideBehavior(){return l()},onInteractOutside:H=>{v()(H),!H.defaultPrevented&&d.root.handleClose()},children:(H,qe)=>{Ae(H,y(()=>u(O),{get enabled(){return d.root.opts.open.current},children:(le,Ne)=>{var ee=p(),ce=h(ee);{var ie=I=>{var z=ke(),K=h(z);{var T=W=>{te(W,{get preventScroll(){return w()},get restoreScrollDelay(){return c()}})};J(K,W=>{d.root.opts.open.current&&W(T)})}var Q=X(K,2),U=ue(()=>({props:j(u(O),$()),...d.snippetProps}));F(Q,()=>e.child,()=>u(U)),f(I,z)},de=I=>{var z=Be(),K=h(z);te(K,{get preventScroll(){return w()}});var T=X(K,2);V(T,U=>({...U}),[()=>j(u(O),$())]);var Q=q(T);F(Q,()=>e.children??M),N(T),f(I,z)};J(ce,I=>{e.child?I(ie):I(de,!1)})}f(le,ee)},$$slots:{default:!0}}))},$$slots:{default:!0}}))},$$slots:{default:!0}}))},$$slots:{focusScope:!0}})},$$slots:{presence:!0}})),b()}function st(n,e){P(e,!0);let r=s(e,"ref",15,null),o=A(e,["$$slots","$$events","$$legacy","ref","class"]);var t=p(),l=h(t);const a=x(()=>E("text-lg font-semibold",e.class));k(l,()=>he,(i,g)=>{g(i,y({"data-slot":"alert-dialog-title",get class(){return u(a)}},()=>o,{get ref(){return r()},set ref(v){r(v)}}))}),f(n,t),b()}function nt(n,e){P(e,!0);let r=s(e,"ref",15,null),o=A(e,["$$slots","$$events","$$legacy","ref","class"]);var t=p(),l=h(t);const a=x(()=>E(re(),e.class));k(l,()=>Oe,(i,g)=>{g(i,y({"data-slot":"alert-dialog-action",get class(){return u(a)}},()=>o,{get ref(){return r()},set ref(v){r(v)}}))}),f(n,t),b()}function lt(n,e){P(e,!0);let r=s(e,"ref",15,null),o=A(e,["$$slots","$$events","$$legacy","ref","class"]);var t=p(),l=h(t);const a=x(()=>E(re({variant:"outline"}),e.class));k(l,()=>Ee,(i,g)=>{g(i,y({"data-slot":"alert-dialog-cancel",get class(){return u(a)}},()=>o,{get ref(){return r()},set ref(v){r(v)}}))}),f(n,t),b()}var Me=B("<div><!></div>");function ct(n,e){P(e,!0);let r=s(e,"ref",15,null),o=A(e,["$$slots","$$events","$$legacy","ref","class","children"]);var t=Me();V(t,a=>({"data-slot":"alert-dialog-footer",class:a,...o}),[()=>E("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e.class)]);var l=q(t);F(l,()=>e.children??M),N(t),ae(t,a=>r(a),()=>r()),f(n,t),b()}var ze=B("<div><!></div>");function it(n,e){P(e,!0);let r=s(e,"ref",15,null),o=A(e,["$$slots","$$events","$$legacy","ref","class","children"]);var t=ze();V(t,a=>({"data-slot":"alert-dialog-header",class:a,...o}),[()=>E("flex flex-col gap-2 text-center sm:text-left",e.class)]);var l=q(t);F(l,()=>e.children??M),N(t),ae(t,a=>r(a),()=>r()),f(n,t),b()}function Ke(n,e){P(e,!0);let r=s(e,"ref",15,null),o=A(e,["$$slots","$$events","$$legacy","ref","class"]);var t=p(),l=h(t);const a=x(()=>E("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e.class));k(l,()=>pe,(i,g)=>{g(i,y({"data-slot":"alert-dialog-overlay",get class(){return u(a)}},()=>o,{get ref(){return r()},set ref(v){r(v)}}))}),f(n,t),b()}var Te=B("<!> <!>",1);function dt(n,e){P(e,!0);let r=s(e,"ref",15,null),o=A(e,["$$slots","$$events","$$legacy","ref","class","portalProps"]);var t=p(),l=h(t);k(l,()=>oe,(a,i)=>{i(a,y(()=>e.portalProps,{children:(g,v)=>{var w=Te(),m=h(w);Ke(m,{});var c=X(m,2);const _=x(()=>E("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed left-[50%] top-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e.class));k(c,()=>Ie,(d,O)=>{O(d,y({"data-slot":"alert-dialog-content",get class(){return u(_)}},()=>o,{get ref(){return r()},set ref(L){r(L)}}))}),f(g,w)},$$slots:{default:!0}}))}),f(n,t),b()}function ut(n,e){P(e,!0);let r=s(e,"ref",15,null),o=A(e,["$$slots","$$events","$$legacy","ref","class"]);var t=p(),l=h(t);const a=x(()=>E("text-muted-foreground text-sm",e.class));k(l,()=>Se,(i,g)=>{g(i,y({"data-slot":"alert-dialog-description",get class(){return u(a)}},()=>o,{get ref(){return r()},set ref(v){r(v)}}))}),f(n,t),b()}const ft=De,gt=oe;export{dt as A,gt as P,ft as R,it as a,st as b,ut as c,ct as d,lt as e,nt as f,Ke as g};
