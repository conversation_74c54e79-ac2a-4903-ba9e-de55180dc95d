import{c as i,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function C(t,o){const e=c(o,["children","$$slots","$$events","$$legacy"]),r=[["polyline",{points:"16 18 22 12 16 6"}],["polyline",{points:"8 6 2 12 8 18"}]];f(t,d({name:"code"},()=>e,{get iconNode(){return r},children:(n,$)=>{var s=i(),a=l(s);m(a,o,"default",{},null),p(n,s)},$$slots:{default:!0}}))}export{C};
