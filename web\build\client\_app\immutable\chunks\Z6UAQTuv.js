import{c as p,a as c,f as D}from"./BasJTneF.js";import{p as S,f as _,a as w,g as b,x as y,c as I,au as M,r as j}from"./CGmarHxI.js";import{c as q}from"./BvdI7LR8.js";import{p as r,r as O,s as A}from"./Btcx8l8F.js";import{s as x,c as B}from"./ncUU1dSD.js";import{i as C}from"./u21ee2wt.js";import{e as E}from"./B-Xjo-Yt.js";import{b as i,m as F}from"./BfX7a-t9.js";import{u as G}from"./WD4kvFhR.js";import{u as H}from"./CnMg5bH0.js";import{n as J}from"./DX6rZLP_.js";var K=D("<div><!></div>");function L(l,e){S(e,!0);let s=r(e,"ref",15,null),m=r(e,"id",19,H),v=r(e,"disabled",3,!1),n=r(e,"onSelect",3,J),u=r(e,"closeOnSelect",3,!0),f=O(e,["$$slots","$$events","$$legacy","child","children","ref","id","disabled","onSelect","closeOnSelect"]);const g=G({id:i.with(()=>m()),disabled:i.with(()=>v()),onSelect:i.with(()=>n()),ref:i.with(()=>s(),t=>s(t)),closeOnSelect:i.with(()=>u())}),d=y(()=>F(f,g.props));var o=p(),P=_(o);{var k=t=>{var a=p(),h=_(a);x(h,()=>e.child,()=>({props:b(d)})),c(t,a)},z=t=>{var a=K();E(a,()=>({...b(d)}));var h=I(a);x(h,()=>e.children??M),j(a),c(t,a)};C(P,t=>{e.child?t(k):t(z,!1)})}c(l,o),w()}function ee(l,e){S(e,!0);let s=r(e,"ref",15,null),m=r(e,"variant",3,"default"),v=O(e,["$$slots","$$events","$$legacy","ref","class","inset","variant"]);var n=p(),u=_(n);const f=y(()=>B("data-highlighted:bg-accent data-highlighted:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:data-highlighted:bg-destructive/10 dark:data-[variant=destructive]:data-highlighted:bg-destructive/20 data-[variant=destructive]:data-highlighted:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground outline-hidden relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm data-[disabled]:pointer-events-none data-[inset]:pl-8 data-[disabled]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",e.class));q(u,()=>L,(g,d)=>{d(g,A({"data-slot":"dropdown-menu-item",get"data-inset"(){return e.inset},get"data-variant"(){return m()},get class(){return b(f)}},()=>v,{get ref(){return s()},set ref(o){s(o)}}))}),c(l,n),w()}export{ee as D};
