import{f as Dt,a as n,t as c}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{f as Zt,s as a,c as t,n as s,r as e}from"../chunks/CGmarHxI.js";import{e as xe}from"../chunks/CmxjS0TN.js";import{B as d}from"../chunks/B1K98fMG.js";import{S as fe}from"../chunks/C6g8ubaU.js";import{R as Bt}from"../chunks/BMRJMPdn.js";import{C as i}from"../chunks/DW7T7T22.js";import{T as Ht}from"../chunks/CZ8wIJN8.js";import{Z as Rt}from"../chunks/1zwBog76.js";import{C as Gt}from"../chunks/-SpbofVw.js";import{B as Ot}from"../chunks/CDnvByek.js";import{S as Ft}from"../chunks/yW0TxTga.js";import{A as be}from"../chunks/Cs0qIT7f.js";var ye=Dt('<span>Watch Demo</span> <span class="ml-2 flex h-6 w-6 items-center justify-center rounded-full bg-orange-100 text-orange-500"><!></span>',1),we=Dt(`<!> <section class="relative overflow-hidden py-20 md:py-32"><div class="absolute inset-0 z-0 opacity-5"><div class="absolute h-64 w-64 rounded-full bg-orange-500 blur-3xl"></div> <div class="absolute right-0 top-1/4 h-96 w-96 rounded-full bg-blue-500 blur-3xl"></div> <div class="absolute bottom-0 left-1/3 h-64 w-64 rounded-full bg-purple-500 blur-3xl"></div></div> <div class="container relative z-10 mx-auto px-4"><div class="grid grid-cols-1 items-center gap-12 md:grid-cols-2"><div class="text-center md:text-left"><div class="mb-6 inline-flex items-center rounded-full bg-orange-100 px-4 py-2 text-sm font-medium text-orange-600"><!> Automate Your Job Search</div> <h1 class="mb-6 text-5xl font-bold leading-tight md:text-6xl">Apply to <span class="relative inline-block"><span class="relative z-10">hundreds</span> <span class="absolute bottom-2 left-0 z-0 h-3 w-full bg-orange-200"></span></span> of jobs with a single click</h1> <p class="mb-8 text-xl text-gray-600">Our AI-powered platform automatically matches your resume to job listings, fills out
          applications, and tracks your progress—saving you hours of tedious work.</p> <div class="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0"><!> <!></div> <div class="mt-12"><p class="mb-4 text-sm font-medium text-gray-500">TRUSTED BY JOB SEEKERS FROM</p> <div class="flex flex-wrap items-center justify-center gap-8 grayscale md:justify-start"><img src="https://placehold.co/100x30?text=Google" alt="Google" class="h-6"/> <img src="https://placehold.co/100x30?text=Microsoft" alt="Microsoft" class="h-6"/> <img src="https://placehold.co/100x30?text=Amazon" alt="Amazon" class="h-5"/> <img src="https://placehold.co/100x30?text=Meta" alt="Meta" class="h-6"/></div></div></div> <div class="relative"><div class="relative z-10 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl"><div class="bg-gray-50 p-4"><div class="flex items-center space-x-2"><div class="h-3 w-3 rounded-full bg-red-400"></div> <div class="h-3 w-3 rounded-full bg-yellow-400"></div> <div class="h-3 w-3 rounded-full bg-green-400"></div> <div class="ml-2 text-xs font-medium text-gray-500">Auto-Apply Dashboard</div></div></div> <img src="/images/auto-apply-hero.png" alt="Auto Apply Dashboard" class="w-full"/></div> <div class="absolute -left-6 -top-6 z-20 rounded-lg bg-white p-4 shadow-lg"><div class="flex items-center space-x-3"><div class="flex h-10 w-10 items-center justify-center rounded-full bg-green-100 text-green-500"><!></div> <div><p class="text-sm font-medium">Application Sent!</p> <p class="text-xs text-gray-500">Software Engineer at Google</p></div></div></div> <div class="absolute -bottom-4 -right-4 z-20 rounded-lg bg-white p-4 shadow-lg"><div class="flex items-center space-x-3"><div class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 text-blue-500"><!></div> <div><p class="text-sm font-medium">10 New Matches</p> <p class="text-xs text-gray-500">Based on your profile</p></div></div></div></div></div></div></section> <section class="relative overflow-hidden bg-gradient-to-r from-orange-50 to-orange-100 py-20"><div class="container mx-auto px-4"><div class="mx-auto max-w-5xl"><div class="grid grid-cols-1 gap-8 md:grid-cols-3"><div class="transform rounded-xl bg-white p-8 shadow-lg transition-transform hover:-translate-y-1 hover:shadow-xl"><div class="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500"><!></div> <div class="text-5xl font-bold text-orange-500">10x</div> <p class="mt-2 text-xl text-gray-600">More applications submitted in the same amount of time</p></div> <div class="transform rounded-xl bg-white p-8 shadow-lg transition-transform hover:-translate-y-1 hover:shadow-xl"><div class="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500"><!></div> <div class="text-5xl font-bold text-orange-500">80%</div> <p class="mt-2 text-xl text-gray-600">Less time spent on repetitive application tasks</p></div> <div class="transform rounded-xl bg-white p-8 shadow-lg transition-transform hover:-translate-y-1 hover:shadow-xl"><div class="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500"><!></div> <div class="text-5xl font-bold text-orange-500">3x</div> <p class="mt-2 text-xl text-gray-600">More interview callbacks compared to manual applications</p></div></div></div></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto max-w-3xl text-center"><div class="mb-6 inline-flex items-center rounded-full bg-orange-100 px-4 py-2 text-sm font-medium text-orange-600"><!> Simple 3-Step Process</div> <h2 class="mb-8 text-4xl font-bold">How Auto Apply Works</h2> <p class="mb-16 text-xl text-gray-600">Our intelligent system streamlines your job search, saving you countless hours of manual
        work.</p></div> <div class="relative mx-auto max-w-6xl"><div class="absolute left-1/2 top-0 h-full w-1 -translate-x-1/2 bg-orange-200 md:hidden"></div> <div class="absolute top-1/2 hidden h-1 w-full -translate-y-1/2 bg-orange-200 md:block"></div> <div class="relative z-10 grid grid-cols-1 gap-16 md:grid-cols-3"><div class="group relative"><div class="absolute left-1/2 top-0 -ml-6 md:left-auto md:right-0 md:top-1/2 md:-mt-6 md:ml-0"><div class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-500 text-white transition-transform group-hover:scale-110"><span class="text-xl font-bold">1</span></div></div> <div class="transform rounded-xl bg-white p-8 pb-12 shadow-lg transition-all group-hover:-translate-y-1 group-hover:shadow-xl"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-8 w-8"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"></path></svg></div> <h3 class="mb-4 text-2xl font-bold">Upload Your Resume</h3> <p class="text-gray-600">Our AI analyzes your resume to extract skills, experience, and qualifications,
              creating a comprehensive profile that matches you with the right opportunities.</p> <div class="mt-6 flex flex-wrap gap-2"><span class="rounded-full bg-orange-50 px-3 py-1 text-xs font-medium text-orange-600">Skill Extraction</span> <span class="rounded-full bg-orange-50 px-3 py-1 text-xs font-medium text-orange-600">Experience Analysis</span></div></div></div> <div class="group relative"><div class="absolute left-1/2 top-0 -ml-6 md:left-1/2 md:top-1/2 md:-ml-6 md:-mt-6"><div class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-500 text-white transition-transform group-hover:scale-110"><span class="text-xl font-bold">2</span></div></div> <div class="transform rounded-xl bg-white p-8 pb-12 shadow-lg transition-all group-hover:-translate-y-1 group-hover:shadow-xl"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-8 w-8"><path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75"></path></svg></div> <h3 class="mb-4 text-2xl font-bold">Set Your Preferences</h3> <p class="text-gray-600">Customize your job search with specific criteria including job titles, locations,
              salary range, and company preferences to target only the positions you're interested
              in.</p> <div class="mt-6 flex flex-wrap gap-2"><span class="rounded-full bg-orange-50 px-3 py-1 text-xs font-medium text-orange-600">Location Filtering</span> <span class="rounded-full bg-orange-50 px-3 py-1 text-xs font-medium text-orange-600">Salary Matching</span></div></div></div> <div class="group relative"><div class="absolute left-1/2 top-0 -ml-6 md:left-0 md:top-1/2 md:-mt-6 md:ml-0"><div class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-500 text-white transition-transform group-hover:scale-110"><span class="text-xl font-bold">3</span></div></div> <div class="transform rounded-xl bg-white p-8 pb-12 shadow-lg transition-all group-hover:-translate-y-1 group-hover:shadow-xl"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-8 w-8"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"></path></svg></div> <h3 class="mb-4 text-2xl font-bold">Automated Applications</h3> <p class="text-gray-600">Our system automatically applies to matching jobs across multiple platforms, filling
              out applications and answering screening questions based on your profile.</p> <div class="mt-6 flex flex-wrap gap-2"><span class="rounded-full bg-orange-50 px-3 py-1 text-xs font-medium text-orange-600">Form Auto-Fill</span> <span class="rounded-full bg-orange-50 px-3 py-1 text-xs font-medium text-orange-600">Real-Time Tracking</span></div></div></div></div></div> <div class="mt-16 text-center"><!></div></div></section> <section class="bg-gradient-to-b from-white to-orange-50 py-24"><div class="container mx-auto px-4"><div class="mx-auto mb-16 max-w-3xl text-center"><div class="mb-6 inline-flex items-center rounded-full bg-orange-100 px-4 py-2 text-sm font-medium text-orange-600"><!> Powerful Capabilities</div> <h2 class="mb-6 text-4xl font-bold">Features That Set Us Apart</h2> <p class="text-xl text-gray-600">Our platform combines cutting-edge AI with user-friendly design to revolutionize your job
        search experience.</p></div> <div class="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3"><div class="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-8 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"><div class="absolute -right-20 -top-20 h-40 w-40 rounded-full bg-orange-100 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div> <div class="relative z-10"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500 transition-transform duration-300 group-hover:scale-110 group-hover:bg-orange-500 group-hover:text-white"><!></div> <h3 class="mb-4 text-2xl font-bold">One-Click Apply</h3> <p class="text-gray-600">Apply to hundreds of jobs with a single click, saving you hours of repetitive
            form-filling. Our system handles everything from basic information to custom questions.</p> <div class="mt-6 h-1 w-16 bg-orange-500 transition-all duration-300 group-hover:w-24"></div></div></div> <div class="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-8 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"><div class="absolute -right-20 -top-20 h-40 w-40 rounded-full bg-orange-100 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div> <div class="relative z-10"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500 transition-transform duration-300 group-hover:scale-110 group-hover:bg-orange-500 group-hover:text-white"><!></div> <h3 class="mb-4 text-2xl font-bold">Smart Matching</h3> <p class="text-gray-600">Our AI analyzes job descriptions to find positions that truly match your skills and
            preferences, ensuring you only apply to relevant opportunities.</p> <div class="mt-6 h-1 w-16 bg-orange-500 transition-all duration-300 group-hover:w-24"></div></div></div> <div class="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-8 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"><div class="absolute -right-20 -top-20 h-40 w-40 rounded-full bg-orange-100 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div> <div class="relative z-10"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500 transition-transform duration-300 group-hover:scale-110 group-hover:bg-orange-500 group-hover:text-white"><!></div> <h3 class="mb-4 text-2xl font-bold">Custom Targeting</h3> <p class="text-gray-600">Create multiple application profiles for different job types, with tailored resumes and
            cover letters to maximize your chances of success.</p> <div class="mt-6 h-1 w-16 bg-orange-500 transition-all duration-300 group-hover:w-24"></div></div></div></div> <div class="mx-auto mt-12 grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3"><div class="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-8 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"><div class="absolute -right-20 -top-20 h-40 w-40 rounded-full bg-orange-100 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div> <div class="relative z-10"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500 transition-transform duration-300 group-hover:scale-110 group-hover:bg-orange-500 group-hover:text-white"><!></div> <h3 class="mb-4 text-2xl font-bold">Job Board Integration</h3> <p class="text-gray-600">Seamlessly connect with all major job boards including LinkedIn, Indeed, Glassdoor, and
            ZipRecruiter to cast a wider net in your job search.</p> <div class="mt-6 h-1 w-16 bg-orange-500 transition-all duration-300 group-hover:w-24"></div></div></div> <div class="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-8 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"><div class="absolute -right-20 -top-20 h-40 w-40 rounded-full bg-orange-100 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div> <div class="relative z-10"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500 transition-transform duration-300 group-hover:scale-110 group-hover:bg-orange-500 group-hover:text-white"><!></div> <h3 class="mb-4 text-2xl font-bold">Application Tracking</h3> <p class="text-gray-600">Monitor all your applications in one place with real-time status updates, interview
            scheduling, and follow-up reminders to stay organized.</p> <div class="mt-6 h-1 w-16 bg-orange-500 transition-all duration-300 group-hover:w-24"></div></div></div> <div class="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-8 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"><div class="absolute -right-20 -top-20 h-40 w-40 rounded-full bg-orange-100 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div> <div class="relative z-10"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-500 transition-transform duration-300 group-hover:scale-110 group-hover:bg-orange-500 group-hover:text-white"><!></div> <h3 class="mb-4 text-2xl font-bold">Smart Screening</h3> <p class="text-gray-600">Our AI automatically answers common screening questions based on your profile,
            increasing your chances of making it to the interview stage.</p> <div class="mt-6 h-1 w-16 bg-orange-500 transition-all duration-300 group-hover:w-24"></div></div></div></div></div></section> <section class="bg-gray-50 py-24"><div class="container mx-auto px-4"><div class="mx-auto max-w-4xl text-center"><div class="mb-8"><img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="mx-auto h-20 w-20 rounded-full"/></div> <blockquote class="mb-8 text-3xl font-light italic">"Auto-Apply has completely transformed my job search. I applied to over 200 positions in
        just one week and landed 5 interviews. It's a game-changer!"</blockquote> <div><p class="text-xl font-medium">Robert K.</p> <p class="text-gray-600">Software Developer</p></div></div></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto mb-20 max-w-3xl text-center"><h2 class="mb-6 text-4xl font-light">Simple, Transparent Pricing</h2> <p class="text-xl text-gray-600">Choose the plan that fits your needs. All plans include our core auto-apply features.</p></div> <div class="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2"><div class="bg-white p-12"><h3 class="mb-2 text-2xl font-light">Basic</h3> <p class="mb-6 text-5xl font-light">$0<span class="text-lg font-normal text-gray-500">/month</span></p> <p class="mb-6 border-b border-gray-100 pb-6 text-gray-600">Perfect for casual job seekers testing the waters.</p> <ul class="mb-8 space-y-4"><li class="flex items-start"><!> <span>Up to 10 auto-applications per month</span></li> <li class="flex items-start"><!> <span>Basic job matching</span></li> <li class="flex items-start"><!> <span>Application tracking</span></li></ul> <!></div> <div class="bg-white p-12"><h3 class="mb-2 text-2xl font-light">Pro</h3> <p class="mb-6 text-5xl font-light">$29<span class="text-lg font-normal text-gray-500">/month</span></p> <p class="mb-6 border-b border-gray-100 pb-6 text-gray-600">For serious job seekers who want to maximize their chances.</p> <ul class="mb-8 space-y-4"><li class="flex items-start"><!> <span>Unlimited auto-applications</span></li> <li class="flex items-start"><!> <span>Advanced AI job matching</span></li> <li class="flex items-start"><!> <span>Multiple application profiles</span></li> <li class="flex items-start"><!> <span>Priority application processing</span></li> <li class="flex items-start"><!> <span>Email notifications</span></li></ul> <!></div></div></div></section> <section class="bg-gray-50 py-24"><div class="container mx-auto px-4"><div class="mx-auto max-w-3xl"><h2 class="mb-16 text-center text-4xl font-light">Frequently Asked Questions</h2> <div class="space-y-12"><div><h3 class="mb-4 text-2xl font-light">How does Auto Apply work?</h3> <p class="text-lg text-gray-600">Our system uses AI to analyze your resume and job listings, automatically filling out
            applications on your behalf. You simply upload your resume, set your preferences, and
            our system does the rest, applying to jobs that match your criteria.</p></div> <div><h3 class="mb-4 text-2xl font-light">Is my data secure?</h3> <p class="text-lg text-gray-600">Yes, we take data security very seriously. All your personal information is encrypted
            and stored securely. We never share your data with third parties without your explicit
            consent.</p></div> <div><h3 class="mb-4 text-2xl font-light">Can I customize my applications?</h3> <p class="text-lg text-gray-600">Absolutely! You can create multiple application profiles with different resumes, cover
            letters, and preferences for different types of jobs. Our system will use the
            appropriate profile based on the job you're applying to.</p></div> <div><h3 class="mb-4 text-2xl font-light">What job boards do you support?</h3> <p class="text-lg text-gray-600">We support all major job boards including LinkedIn, Indeed, Glassdoor, ZipRecruiter, and
            many more. Our system is constantly being updated to support new platforms and improve
            compatibility with existing ones.</p></div></div></div></div></section> <section class="bg-black py-24 text-white"><div class="container mx-auto px-4 text-center"><h2 class="mb-8 text-4xl font-light">Ready to Revolutionize Your Job Search?</h2> <p class="mx-auto mb-12 max-w-3xl text-xl text-white/80">Join thousands of job seekers who have streamlined their job search with Auto Apply. Start
      applying to more jobs today and land your dream position faster.</p> <div class="flex flex-col justify-center gap-4 sm:flex-row"><!> <!></div> <p class="mt-8 text-white/60">No credit card required. Start your free trial today.</p></div></section>`,1);function Re(Tt){var O=we(),F=Zt(O);fe(F,{title:"Hirli Auto-Apply - Apply to Jobs Automatically",description:"Save time and increase your chances of landing interviews with our automated job application tool. Apply to hundreds of jobs with just a few clicks.",keywords:"auto apply, job application automation, job search, one-click apply, job application tool",url:"https://hirli.com/auto-apply",image:"/assets/og-image-auto-apply.jpg"});var v=a(F,2),D=a(t(v),2),T=t(D),p=t(T),g=t(p),Jt=t(g);Bt(Jt,{class:"mr-2 h-4 w-4"}),s(),e(g);var J=a(g,6),W=t(J);d(W,{class:"bg-orange-500 px-8 py-6 text-lg font-medium text-white hover:bg-orange-600",children:(r,l)=>{s();var o=c("Start Applying Free");n(r,o)},$$slots:{default:!0}});var Wt=a(W,2);d(Wt,{variant:"outline",class:"border-gray-300 px-8 py-6 text-lg font-medium hover:bg-gray-50",children:(r,l)=>{var o=ye(),Ct=a(Zt(o),2),he=t(Ct);be(he,{class:"h-3 w-3"}),e(Ct),n(r,o)},$$slots:{default:!0}}),e(J),s(2),e(p);var Y=a(p,2),u=t(Y),Yt=a(t(u),2);e(u);var m=a(u,2),E=t(m),N=t(E),Et=t(N);i(Et,{class:"h-5 w-5"}),e(N),s(2),e(E),e(m);var V=a(m,2),q=t(V),L=t(q),Nt=t(L);Ht(Nt,{class:"h-5 w-5"}),e(L),s(2),e(q),e(V),e(Y),e(T),e(D),e(v);var h=a(v,2),U=t(h),Q=t(U),X=t(Q),x=t(X),K=t(x),Vt=t(K);Rt(Vt,{class:"h-8 w-8"}),e(K),s(4),e(x);var f=a(x,2),tt=t(f),qt=t(tt);Gt(qt,{class:"h-8 w-8"}),e(tt),s(4),e(f);var et=a(f,2),at=t(et),Lt=t(at);Ot(Lt,{class:"h-8 w-8"}),e(at),s(4),e(et),e(X),e(Q),e(U),e(h);var b=a(h,2),st=t(b),y=t(st),rt=t(y),Ut=t(rt);Ft(Ut,{class:"mr-2 h-4 w-4"}),s(),e(rt),s(4),e(y);var ot=a(y,4),Qt=t(ot);d(Qt,{class:"bg-orange-500 px-8 py-6 text-lg font-medium text-white hover:bg-orange-600",children:(r,l)=>{s();var o=c("Start Your Automated Job Search");n(r,o)},$$slots:{default:!0}}),e(ot),e(st),e(b);var w=a(b,2),it=t(w),_=t(it),lt=t(_),Xt=t(lt);Bt(Xt,{class:"mr-2 h-4 w-4"}),s(),e(lt),s(4),e(_);var j=a(_,2),k=t(j),nt=a(t(k),2),dt=t(nt),Kt=t(dt);Rt(Kt,{class:"h-8 w-8"}),e(dt),s(6),e(nt),e(k);var A=a(k,2),ct=a(t(A),2),vt=t(ct),te=t(vt);Ft(te,{class:"h-8 w-8"}),e(vt),s(6),e(ct),e(A);var pt=a(A,2),gt=a(t(pt),2),ut=t(gt),ee=t(ut);Ht(ee,{class:"h-8 w-8"}),e(ut),s(6),e(gt),e(pt),e(j);var mt=a(j,2),I=t(mt),ht=a(t(I),2),xt=t(ht),ae=t(xt);Ot(ae,{class:"h-8 w-8"}),e(xt),s(6),e(ht),e(I);var S=a(I,2),ft=a(t(S),2),bt=t(ft),se=t(bt);Gt(se,{class:"h-8 w-8"}),e(bt),s(6),e(ft),e(S);var yt=a(S,2),wt=a(t(yt),2),_t=t(wt),re=t(_t);i(re,{class:"h-8 w-8"}),e(_t),s(6),e(wt),e(yt),e(mt),e(it),e(w);var z=a(w,4),jt=t(z),kt=a(t(jt),2),$=t(kt),M=a(t($),6),P=t(M),oe=t(P);i(oe,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-orange-500"}),s(2),e(P);var C=a(P,2),ie=t(C);i(ie,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-orange-500"}),s(2),e(C);var At=a(C,2),le=t(At);i(le,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-orange-500"}),s(2),e(At),e(M);var ne=a(M,2);d(ne,{variant:"outline",class:"w-full border-gray-300 p-4 text-lg font-medium",children:(r,l)=>{s();var o=c("Get Started Free");n(r,o)},$$slots:{default:!0}}),e($);var It=a($,2),Z=a(t(It),6),B=t(Z),de=t(B);i(de,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-orange-500"}),s(2),e(B);var H=a(B,2),ce=t(H);i(ce,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-orange-500"}),s(2),e(H);var R=a(H,2),ve=t(R);i(ve,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-orange-500"}),s(2),e(R);var G=a(R,2),pe=t(G);i(pe,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-orange-500"}),s(2),e(G);var St=a(G,2),ge=t(St);i(ge,{class:"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-orange-500"}),s(2),e(St),e(Z);var ue=a(Z,2);d(ue,{class:"w-full bg-black p-4 text-lg font-medium text-white hover:bg-gray-800",children:(r,l)=>{s();var o=c("Start 7-Day Free Trial");n(r,o)},$$slots:{default:!0}}),e(It),e(kt),e(jt),e(z);var zt=a(z,4),$t=t(zt),Mt=a(t($t),4),Pt=t(Mt);d(Pt,{class:"bg-white px-10 py-5 text-lg font-medium text-black hover:bg-gray-100",children:(r,l)=>{s();var o=c("Get Started Free");n(r,o)},$$slots:{default:!0}});var me=a(Pt,2);d(me,{variant:"outline",class:"border-white px-10 py-5 text-lg font-medium text-white hover:bg-white/10",children:(r,l)=>{s();var o=c("Schedule Demo");n(r,o)},$$slots:{default:!0}}),e(Mt),s(2),e($t),e(zt),xe("error",Yt,r=>{const l=r.currentTarget;l.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjUwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkF1dG8gQXBwbHkgRGFzaGJvYXJkPC90ZXh0Pjwvc3ZnPg=="}),n(Tt,O)}export{Re as component};
