var st=Object.defineProperty;var Re=r=>{throw TypeError(r)};var at=(r,e,t)=>e in r?st(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var _=(r,e,t)=>at(r,typeof e!="symbol"?e+"":e,t),Ne=(r,e,t)=>e.has(r)||Re("Cannot "+t);var o=(r,e,t)=>(Ne(r,e,"read from private field"),t?t.call(r):e.get(r)),d=(r,e,t)=>e.has(r)?Re("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(r):e.set(r,t),w=(r,e,t,n)=>(Ne(r,e,"write to private field"),n?n.call(r,t):e.set(r,t),t),se=(r,e,t)=>(Ne(r,e,"access private method"),t);import{c as D,a as k}from"./BasJTneF.js";import{f as x,au as j,p as X,bf as ot,a as Y,g as E,x as Te,k as J,d as P,i as Q,a_ as ut,o as ct}from"./CGmarHxI.js";import{m as lt,u as dt}from"./CIt1g2O9.js";import{s as Z}from"./ncUU1dSD.js";import{i as ft}from"./u21ee2wt.js";import{p as g}from"./Btcx8l8F.js";import{w as B,b as f,u as Oe,o as ht,e as G,h as bt}from"./BfX7a-t9.js";import{k as vt}from"./DT9WCdWY.js";import{b as He,i as mt,g as yt,h as gt,a as Ie,f as Pe}from"./Bpi49Nrf.js";import{o as C}from"./CmxjS0TN.js";import{a as Ce}from"./OOsIR5sE.js";import{a as We,C as qe}from"./Cb-3cdbh.js";import{n as S}from"./DX6rZLP_.js";import{f as pt,T as St}from"./CIOgxH3l.js";import{C as wt}from"./DuoUhxYL.js";import{u as je}from"./CnMg5bH0.js";import{S as Et}from"./BJIrNhIJ.js";function De(r,e){return setTimeout(e,r)}function Tt(r,e){var t=D(),n=x(t);vt(n,()=>e.children,i=>{var a=D(),s=x(a);Z(s,()=>e.children??j),k(i,a)}),k(r,t)}function Pr(r,e){X(e,!0);let t=g(e,"to",3,"body");const n=ot();let i=Te(a);function a(){if(!He||e.disabled)return null;let v=null;return typeof t()=="string"?v=document.querySelector(t()):(t()instanceof HTMLElement||t()instanceof DocumentFragment)&&(v=t()),v}let s;function u(){s&&(dt(s),s=null)}B([()=>E(i),()=>e.disabled],([v,F])=>{if(!v||F){u();return}return s=lt(Tt,{target:v,props:{children:e.children},context:n}),()=>{u()}});var c=D(),h=x(c);{var y=v=>{var F=D(),A=x(F);Z(A,()=>e.children??j),k(v,F)};ft(h,v=>{e.disabled&&v(y)})}k(r,c),Y()}function Be(r,e=500){let t=null;const n=(...i)=>{t!==null&&clearTimeout(t),t=setTimeout(()=>{r(...i)},e)};return n.destroy=()=>{t!==null&&(clearTimeout(t),t=null)},n}function ke(r,e){return r===e||r.contains(e)}function Xe(r){return(r==null?void 0:r.ownerDocument)??document}function Br(r){return(r==null?void 0:r.ownerDocument)??document}function Mr(r){if(!r)return null;for(const e of r.childNodes)if(e.nodeType!==Node.COMMENT_NODE)return e;return null}function Ft(r,e){const{clientX:t,clientY:n}=r,i=e.getBoundingClientRect();return t<i.left||t>i.right||n<i.top||n>i.bottom}globalThis.bitsDismissableLayers??(globalThis.bitsDismissableLayers=new Map);var K,R,L,U,V,O,$,ee,T,de,q,Ye,fe,H,he,be,ve,me,te,Ze,ye,ge;class Nt{constructor(e){d(this,q);_(this,"opts");d(this,K);d(this,R);d(this,L,{pointerdown:!1});d(this,U,!1);d(this,V,!1);_(this,"node",f(null));d(this,O);d(this,$);d(this,ee,J(null));d(this,T,S);d(this,de,e=>{e.defaultPrevented||this.currNode&&Ce(()=>{var t,n;!this.currNode||o(this,me).call(this,e.target)||e.target&&!o(this,V)&&((n=(t=o(this,$)).current)==null||n.call(t,e))})});d(this,fe,e=>{let t=e;t.defaultPrevented&&(t=Me(e)),o(this,K).current(e)});d(this,H,Be(e=>{if(!this.currNode){o(this,T).call(this);return}const t=this.opts.isValidEvent.current(e,this.currNode)||Ct(e,this.currNode);if(!o(this,U)||se(this,q,Ze).call(this)||!t){o(this,T).call(this);return}let n=e;if(n.defaultPrevented&&(n=Me(n)),o(this,R).current!=="close"&&o(this,R).current!=="defer-otherwise-close"){o(this,T).call(this);return}e.pointerType==="touch"?(o(this,T).call(this),w(this,T,We(o(this,O),"click",o(this,fe),{once:!0}))):o(this,K).current(n)},10));d(this,he,e=>{o(this,L)[e.type]=!0});d(this,be,e=>{o(this,L)[e.type]=!1});d(this,ve,()=>{this.node.current&&w(this,U,Ot(this.node.current))});d(this,me,e=>this.node.current?ke(this.node.current,e):!1);d(this,te,Be(()=>{for(const e in o(this,L))o(this,L)[e]=!1;w(this,U,!1)},20));d(this,ye,()=>{w(this,V,!0)});d(this,ge,()=>{w(this,V,!1)});_(this,"props",{onfocuscapture:o(this,ye),onblurcapture:o(this,ge)});this.opts=e,Oe({id:e.id,ref:this.node,deps:()=>e.enabled.current,onRefChange:i=>{this.currNode=i}}),w(this,R,e.interactOutsideBehavior),w(this,K,e.onInteractOutside),w(this,$,e.onFocusOutside),Q(()=>{w(this,O,Xe(this.currNode))});let t=S;const n=()=>{o(this,te).call(this),globalThis.bitsDismissableLayers.delete(this),o(this,H).destroy(),t()};B([()=>this.opts.enabled.current,()=>this.currNode],([i,a])=>{if(!(!i||!a))return De(1,()=>{this.currNode&&(globalThis.bitsDismissableLayers.set(this,o(this,R)),t(),t=se(this,q,Ye).call(this))}),n}),ht(()=>{o(this,te).destroy(),globalThis.bitsDismissableLayers.delete(this),o(this,H).destroy(),o(this,T).call(this),t()})}get currNode(){return E(o(this,ee))}set currNode(e){P(o(this,ee),e,!0)}}K=new WeakMap,R=new WeakMap,L=new WeakMap,U=new WeakMap,V=new WeakMap,O=new WeakMap,$=new WeakMap,ee=new WeakMap,T=new WeakMap,de=new WeakMap,q=new WeakSet,Ye=function(){return G(C(o(this,O),"pointerdown",G(o(this,he),o(this,ve)),{capture:!0}),C(o(this,O),"pointerdown",G(o(this,be),o(this,H))),C(o(this,O),"focusin",o(this,de)))},fe=new WeakMap,H=new WeakMap,he=new WeakMap,be=new WeakMap,ve=new WeakMap,me=new WeakMap,te=new WeakMap,Ze=function(){return Object.values(o(this,L)).some(Boolean)},ye=new WeakMap,ge=new WeakMap;function It(r){return new Nt(r)}function Lt(r){return r.findLast(([e,{current:t}])=>t==="close"||t==="ignore")}function Ot(r){const e=[...globalThis.bitsDismissableLayers],t=Lt(e);if(t)return t[0].node.current===r;const[n]=e[0];return n.node.current===r}function Ct(r,e){if("button"in r&&r.button>0)return!1;const t=r.target;return mt(t)?Xe(t).documentElement.contains(t)&&!ke(e,t)&&Ft(r,e):!1}function Me(r){const e=r.currentTarget,t=r.target;let n;r instanceof PointerEvent?n=new PointerEvent(r.type,r):n=new PointerEvent("pointerdown",r);let i=!1;return new Proxy(n,{get:(s,u)=>u==="currentTarget"?e:u==="target"?t:u==="preventDefault"?()=>{i=!0,typeof s.preventDefault=="function"&&s.preventDefault()}:u==="defaultPrevented"?i:u in s?s[u]:r[u]})}function _r(r,e){X(e,!0);let t=g(e,"interactOutsideBehavior",3,"close"),n=g(e,"onInteractOutside",3,S),i=g(e,"onFocusOutside",3,S),a=g(e,"isValidEvent",3,()=>!1);const s=It({id:f.with(()=>e.id),interactOutsideBehavior:f.with(()=>t()),onInteractOutside:f.with(()=>n()),enabled:f.with(()=>e.enabled),onFocusOutside:f.with(()=>i()),isValidEvent:f.with(()=>a())});var u=D(),c=x(u);Z(c,()=>e.children??j,()=>({props:s.props})),k(r,u),Y()}globalThis.bitsEscapeLayers??(globalThis.bitsEscapeLayers=new Map);var pe,Se;class Dt{constructor(e){_(this,"opts");d(this,pe,()=>C(document,"keydown",o(this,Se),{passive:!1}));d(this,Se,e=>{if(e.key!==pt||!xt(this))return;const t=new KeyboardEvent(e.type,e);e.preventDefault();const n=this.opts.escapeKeydownBehavior.current;n!=="close"&&n!=="defer-otherwise-close"||this.opts.onEscapeKeydown.current(t)});this.opts=e;let t=S;B(()=>e.enabled.current,n=>(n&&(globalThis.bitsEscapeLayers.set(this,e.escapeKeydownBehavior),t=o(this,pe).call(this)),()=>{t(),globalThis.bitsEscapeLayers.delete(this)}))}}pe=new WeakMap,Se=new WeakMap;function kt(r){return new Dt(r)}function xt(r){const e=[...globalThis.bitsEscapeLayers],t=e.findLast(([i,{current:a}])=>a==="close"||a==="ignore");if(t)return t[0]===r;const[n]=e[0];return n===r}function Kr(r,e){X(e,!0);let t=g(e,"escapeKeydownBehavior",3,"close"),n=g(e,"onEscapeKeydown",3,S);kt({escapeKeydownBehavior:f.with(()=>t()),onEscapeKeydown:f.with(()=>n()),enabled:f.with(()=>e.enabled)});var i=D(),a=x(i);Z(a,()=>e.children??j),k(r,i),Y()}const N=f([]);function At(){return{add(r){const e=N.current[0];e&&r.id!==e.id&&e.pause(),N.current=_e(N.current,r),N.current.unshift(r)},remove(r){var e;N.current=_e(N.current,r),(e=N.current[0])==null||e.resume()},get current(){return N.current}}}function Rt(){let r=J(!1),e=J(!1);return{id:je(),get paused(){return E(r)},get isHandlingFocus(){return E(e)},set isHandlingFocus(t){P(e,t,!0)},pause(){P(r,!0)},resume(){P(r,!1)}}}function _e(r,e){return[...r].filter(t=>t.id!==e.id)}function Pt(r){return r.filter(e=>e.tagName!=="A")}function I(r,{select:e=!1}={}){if(!(r&&r.focus)||document.activeElement===r)return;const t=document.activeElement;r.focus({preventScroll:!0}),r!==t&&yt(r)&&e&&r.select()}function Bt(r,{select:e=!1}={}){const t=document.activeElement;for(const n of r)if(I(n,{select:e}),document.activeElement!==t)return!0}function Ke(r,e){for(const t of r)if(!gt(t,e))return t}function ze(r){const e=[],t=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const i=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||i?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;t.nextNode();)e.push(t.currentNode);return e}function Mt(r){const e=ze(r),t=Ke(e,r),n=Ke(e.reverse(),r);return[t,n]}var z={};/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/Object.defineProperty(z,"__esModule",{value:!0});var Ge=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],oe=Ge.join(","),Je=typeof Element>"u",M=Je?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,ue=!Je&&Element.prototype.getRootNode?function(r){var e;return r==null||(e=r.getRootNode)===null||e===void 0?void 0:e.call(r)}:function(r){return r==null?void 0:r.ownerDocument},ce=function r(e,t){var n;t===void 0&&(t=!0);var i=e==null||(n=e.getAttribute)===null||n===void 0?void 0:n.call(e,"inert"),a=i===""||i==="true",s=a||t&&e&&r(e.parentNode);return s},_t=function(e){var t,n=e==null||(t=e.getAttribute)===null||t===void 0?void 0:t.call(e,"contenteditable");return n===""||n==="true"},Qe=function(e,t,n){if(ce(e))return[];var i=Array.prototype.slice.apply(e.querySelectorAll(oe));return t&&M.call(e,oe)&&i.unshift(e),i=i.filter(n),i},$e=function r(e,t,n){for(var i=[],a=Array.from(e);a.length;){var s=a.shift();if(!ce(s,!1))if(s.tagName==="SLOT"){var u=s.assignedElements(),c=u.length?u:s.children,h=r(c,!0,n);n.flatten?i.push.apply(i,h):i.push({scopeParent:s,candidates:h})}else{var y=M.call(s,oe);y&&n.filter(s)&&(t||!e.includes(s))&&i.push(s);var v=s.shadowRoot||typeof n.getShadowRoot=="function"&&n.getShadowRoot(s),F=!ce(v,!1)&&(!n.shadowRootFilter||n.shadowRootFilter(s));if(v&&F){var A=r(v===!0?s.children:v.children,!0,n);n.flatten?i.push.apply(i,A):i.push({scopeParent:s,candidates:A})}else a.unshift.apply(a,s.children)}}return i},et=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},xe=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||_t(e))&&!et(e)?0:e.tabIndex},Kt=function(e,t){var n=xe(e);return n<0&&t&&!et(e)?0:n},Ut=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},tt=function(e){return e.tagName==="INPUT"},Vt=function(e){return tt(e)&&e.type==="hidden"},Ht=function(e){var t=e.tagName==="DETAILS"&&Array.prototype.slice.apply(e.children).some(function(n){return n.tagName==="SUMMARY"});return t},Wt=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]},qt=function(e){if(!e.name)return!0;var t=e.form||ue(e),n=function(u){return t.querySelectorAll('input[type="radio"][name="'+u+'"]')},i;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")i=n(window.CSS.escape(e.name));else try{i=n(e.name)}catch(s){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",s.message),!1}var a=Wt(i,e.form);return!a||a===e},jt=function(e){return tt(e)&&e.type==="radio"},Xt=function(e){return jt(e)&&!qt(e)},Yt=function(e){var t,n=e&&ue(e),i=(t=n)===null||t===void 0?void 0:t.host,a=!1;if(n&&n!==e){var s,u,c;for(a=!!((s=i)!==null&&s!==void 0&&(u=s.ownerDocument)!==null&&u!==void 0&&u.contains(i)||e!=null&&(c=e.ownerDocument)!==null&&c!==void 0&&c.contains(e));!a&&i;){var h,y,v;n=ue(i),i=(h=n)===null||h===void 0?void 0:h.host,a=!!((y=i)!==null&&y!==void 0&&(v=y.ownerDocument)!==null&&v!==void 0&&v.contains(i))}}return a},Ue=function(e){var t=e.getBoundingClientRect(),n=t.width,i=t.height;return n===0&&i===0},Zt=function(e,t){var n=t.displayCheck,i=t.getShadowRoot;if(getComputedStyle(e).visibility==="hidden")return!0;var a=M.call(e,"details>summary:first-of-type"),s=a?e.parentElement:e;if(M.call(s,"details:not([open]) *"))return!0;if(!n||n==="full"||n==="legacy-full"){if(typeof i=="function"){for(var u=e;e;){var c=e.parentElement,h=ue(e);if(c&&!c.shadowRoot&&i(c)===!0)return Ue(e);e.assignedSlot?e=e.assignedSlot:!c&&h!==e.ownerDocument?e=h.host:e=c}e=u}if(Yt(e))return!e.getClientRects().length;if(n!=="legacy-full")return!0}else if(n==="non-zero-area")return Ue(e);return!1},zt=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if(t.tagName==="FIELDSET"&&t.disabled){for(var n=0;n<t.children.length;n++){var i=t.children.item(n);if(i.tagName==="LEGEND")return M.call(t,"fieldset[disabled] *")?!0:!i.contains(e)}return!0}t=t.parentElement}return!1},le=function(e,t){return!(t.disabled||ce(t)||Vt(t)||Zt(t,e)||Ht(t)||zt(t))},Le=function(e,t){return!(Xt(t)||xe(t)<0||!le(e,t))},Gt=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},Jt=function r(e){var t=[],n=[];return e.forEach(function(i,a){var s=!!i.scopeParent,u=s?i.scopeParent:i,c=Kt(u,s),h=s?r(i.candidates):u;c===0?s?t.push.apply(t,h):t.push(u):n.push({documentOrder:a,tabIndex:c,item:i,isScope:s,content:h})}),n.sort(Ut).reduce(function(i,a){return a.isScope?i.push.apply(i,a.content):i.push(a.content),i},[]).concat(t)},Qt=function(e,t){t=t||{};var n;return t.getShadowRoot?n=$e([e],t.includeContainer,{filter:Le.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:Gt}):n=Qe(e,t.includeContainer,Le.bind(null,t)),Jt(n)},$t=function(e,t){t=t||{};var n;return t.getShadowRoot?n=$e([e],t.includeContainer,{filter:le.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):n=Qe(e,t.includeContainer,le.bind(null,t)),n},er=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return M.call(e,oe)===!1?!1:Le(t,e)},tr=Ge.concat("iframe").join(","),rr=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return M.call(e,tr)===!1?!1:le(t,e)},Ur=z.focusable=$t;z.getTabIndex=xe;var Vr=z.isFocusable=rr,nr=z.isTabbable=er,Hr=z.tabbable=Qt;const ir=new qe("focusScope.autoFocusOnMount",{bubbles:!1,cancelable:!0}),sr=new qe("focusScope.autoFocusOnDestroy",{bubbles:!1,cancelable:!0}),ar=new wt("FocusScope");function or({id:r,loop:e,enabled:t,onOpenAutoFocus:n,onCloseAutoFocus:i,forceMount:a}){const s=At(),u=Rt(),c=f(null),h=ar.getOr({ignoreCloseAutoFocus:!1});let y=null;Oe({id:r,ref:c,deps:()=>t.current});function v(l){if(!(u.paused||!c.current||u.isHandlingFocus)){u.isHandlingFocus=!0;try{const b=l.target;if(!Ie(b))return;const m=c.current.contains(b);if(l.type==="focusin")if(m)y=b;else{if(h.ignoreCloseAutoFocus)return;I(y,{select:!0})}else l.type==="focusout"&&!m&&!h.ignoreCloseAutoFocus&&I(y,{select:!0})}finally{u.isHandlingFocus=!1}}}function F(l){if(!y||!c.current)return;let b=!1;for(const m of l){if(m.type==="childList"&&m.removedNodes.length>0)for(const p of m.removedNodes){if(p===y){b=!0;break}if(p.nodeType===Node.ELEMENT_NODE&&p.contains(y)){b=!0;break}}if(b)break}b&&c.current&&!c.current.contains(document.activeElement)&&I(c.current)}B([()=>c.current,()=>t.current],([l,b])=>{if(!l||!b)return;const m=G(C(document,"focusin",v),C(document,"focusout",v)),p=new MutationObserver(F);return p.observe(l,{childList:!0,subtree:!0,attributes:!1}),()=>{m(),p.disconnect()}}),B([()=>a.current,()=>c.current],([l,b])=>{if(l)return;const m=document.activeElement;return A(b,m),()=>{b&&Ae(m)}}),B([()=>a.current,()=>c.current,()=>t.current],([l,b])=>{if(!l)return;const m=document.activeElement;return A(b,m),()=>{b&&Ae(m)}});function A(l,b){if(l||(l=document.getElementById(r.current)),!l||!t.current)return;if(s.add(u),!l.contains(b)){const p=ir.createEvent();n.current(p),p.defaultPrevented||Ce(()=>{if(!l)return;Bt(Pt(ze(l)),{select:!0})||I(l)})}}function Ae(l){var p;const b=sr.createEvent();(p=i.current)==null||p.call(i,b);const m=h.ignoreCloseAutoFocus;De(0,()=>{!b.defaultPrevented&&l&&!m&&I(nr(l)?l:document.body,{select:!0}),s.remove(u)})}function nt(l){if(!t.current||!e.current&&!t.current||u.paused)return;const b=l.key===St&&!l.ctrlKey&&!l.altKey&&!l.metaKey,m=document.activeElement;if(!(b&&m))return;const p=c.current;if(!p)return;const[ie,Fe]=Mt(p);ie&&Fe?!l.shiftKey&&m===Fe?(l.preventDefault(),e.current&&I(ie,{select:!0})):l.shiftKey&&m===ie&&(l.preventDefault(),e.current&&I(Fe,{select:!0})):m===p&&l.preventDefault()}const it=Te(()=>({id:r.current,tabindex:-1,onkeydown:nt}));return{get props(){return E(it)}}}function Wr(r,e){X(e,!0);let t=g(e,"trapFocus",3,!1),n=g(e,"loop",3,!1),i=g(e,"onCloseAutoFocus",3,S),a=g(e,"onOpenAutoFocus",3,S),s=g(e,"forceMount",3,!1);const u=or({enabled:f.with(()=>t()),loop:f.with(()=>n()),onCloseAutoFocus:f.with(()=>i()),onOpenAutoFocus:f.with(()=>a()),id:f.with(()=>e.id),forceMount:f.with(()=>s())});var c=D(),h=x(c);Z(h,()=>e.focusScope??j,()=>({props:u.props})),k(r,c),Y()}globalThis.bitsTextSelectionLayers??(globalThis.bitsTextSelectionLayers=new Map);var W,re,we,rt,Ee,ne;class ur{constructor(e){d(this,we);_(this,"opts");d(this,W,S);d(this,re,f(null));d(this,Ee,e=>{const t=o(this,re).current,n=e.target;!Ie(t)||!Ie(n)||!this.opts.enabled.current||!dr(this)||!ke(t,n)||(this.opts.onPointerDown.current(e),!e.defaultPrevented&&w(this,W,lr(t)))});d(this,ne,()=>{o(this,W).call(this),w(this,W,S)});this.opts=e,Oe({id:e.id,ref:o(this,re),deps:()=>this.opts.enabled.current});let t=S;B(()=>this.opts.enabled.current,n=>(n&&(globalThis.bitsTextSelectionLayers.set(this,this.opts.enabled),t(),t=se(this,we,rt).call(this)),()=>{t(),o(this,ne).call(this),globalThis.bitsTextSelectionLayers.delete(this)}))}}W=new WeakMap,re=new WeakMap,we=new WeakSet,rt=function(){return G(C(document,"pointerdown",o(this,Ee)),C(document,"pointerup",bt(o(this,ne),this.opts.onPointerUp.current)))},Ee=new WeakMap,ne=new WeakMap;function cr(r){return new ur(r)}const Ve=r=>r.style.userSelect||r.style.webkitUserSelect;function lr(r){const e=document.body,t=Ve(e),n=Ve(r);return ae(e,"none"),ae(r,"text"),()=>{ae(e,t),ae(r,n)}}function ae(r,e){r.style.userSelect=e,r.style.webkitUserSelect=e}function dr(r){const e=[...globalThis.bitsTextSelectionLayers];if(!e.length)return!1;const t=e.at(-1);return t?t[0]===r:!1}function qr(r,e){X(e,!0);let t=g(e,"preventOverflowTextSelection",3,!0),n=g(e,"onPointerDown",3,S),i=g(e,"onPointerUp",3,S);cr({id:f.with(()=>e.id),onPointerDown:f.with(()=>n()),onPointerUp:f.with(()=>i()),enabled:f.with(()=>e.enabled&&t())});var a=D(),s=x(a);Z(s,()=>e.children??j),k(r,a),Y()}function fr(r){let e=0,t=J(void 0),n;function i(){e-=1,n&&e<=0&&(n(),P(t,void 0),n=void 0)}return(...a)=>(e+=1,E(t)===void 0&&(n=ut(()=>{P(t,r(...a),!0)})),Q(()=>()=>{i()}),E(t))}const hr=fr(()=>{const r=new Et,e=Te(()=>{for(const a of r.values())if(a)return!0;return!1});let t=J(null),n=null;function i(){He&&(document.body.setAttribute("style",E(t)??""),document.body.style.removeProperty("--scrollbar-width"),Pe&&(n==null||n()))}return Q(()=>{const a=E(e);return ct(()=>{if(!a)return;P(t,document.body.getAttribute("style"),!0);const s=getComputedStyle(document.body),u=window.innerWidth-document.documentElement.clientWidth,h={padding:Number.parseInt(s.paddingRight??"0",10)+u,margin:Number.parseInt(s.marginRight??"0",10)};u>0&&(document.body.style.paddingRight=`${h.padding}px`,document.body.style.marginRight=`${h.margin}px`,document.body.style.setProperty("--scrollbar-width",`${u}px`),document.body.style.overflow="hidden"),Pe&&(n=We(document,"touchmove",y=>{y.target===document.documentElement&&(y.touches.length>1||y.preventDefault())},{passive:!1})),Ce(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})})}),Q(()=>()=>{n==null||n()}),{get map(){return r},resetBodyStyle:i}});function br(r,e=()=>null){const t=je(),n=hr();if(!n)return;const i=Te(e);n.map.set(t,r??!1);const a=f.with(()=>n.map.get(t)??!1,s=>n.map.set(t,s));return Q(()=>()=>{n.map.delete(t),!vr(n.map)&&(E(i)===null?requestAnimationFrame(()=>n.resetBodyStyle()):De(E(i),()=>n.resetBodyStyle()))}),a}function vr(r){for(const[e,t]of r)if(t)return!0;return!1}function jr(r,e){X(e,!0);let t=g(e,"preventScroll",3,!0),n=g(e,"restoreScrollDelay",3,null);br(t(),()=>n()),Y()}export{_r as D,Kr as E,Wr as F,Pr as P,jr as S,qr as T,Vr as a,ar as b,Bt as c,Mr as d,De as e,Ur as f,Br as g,ze as h,nr as i,Hr as t};
