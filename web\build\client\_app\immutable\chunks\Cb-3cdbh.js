var i=Object.defineProperty;var c=(n,e,t)=>e in n?i(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var o=(n,e,t)=>c(n,typeof e!="symbol"?e+"":e,t);import{o as v}from"./CmxjS0TN.js";function E(n,e,t,s){const a=Array.isArray(e)?e:[e];return a.forEach(r=>n.addEventListener(r,t,s)),()=>{a.forEach(r=>n.removeEventListener(r,t,s))}}class m{constructor(e,t={bubbles:!0,cancelable:!0}){o(this,"eventName");o(this,"options");this.eventName=e,this.options=t}createEvent(e){return new CustomEvent(this.eventName,{...this.options,detail:e})}dispatch(e,t){const s=this.createEvent(t);return e.dispatchEvent(s),s}listen(e,t,s){const a=r=>{t(r)};return v(e,this.eventName,a,s)}}export{m as C,E as a};
