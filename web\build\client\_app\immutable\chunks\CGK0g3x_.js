var He=Object.defineProperty;var pe=o=>{throw TypeError(o)};var Ae=(o,t,e)=>t in o?He(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e;var d=(o,t,e)=>Ae(o,typeof t!="symbol"?t+"":t,e),re=(o,t,e)=>t.has(o)||pe("Cannot "+e);var s=(o,t,e)=>(re(o,t,"read from private field"),e?e.call(o):t.get(o)),g=(o,t,e)=>t.has(o)?pe("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(o):t.set(o,e),se=(o,t,e,r)=>(re(o,t,"write to private field"),r?r.call(o,e):t.set(o,e),e),at=(o,t,e)=>(re(o,t,"access private method"),e);import{c as I,a as u,f as U,t as ke}from"./BasJTneF.js";import{g as i,x as v,d as w,i as he,k as R,u as De,p as E,f as _,a as B,e as ne,c as Y,au as q,r as J,s as ot,t as Oe}from"./CGmarHxI.js";import{s as Ve}from"./CIt1g2O9.js";import{s as O,c as ut}from"./ncUU1dSD.js";import{i as M}from"./u21ee2wt.js";import{c as tt}from"./BvdI7LR8.js";import{p as c,s as G,r as F}from"./Btcx8l8F.js";import{e as $}from"./B-Xjo-Yt.js";import{w as Q,u as ct,o as Me,b as C,m as et}from"./BfX7a-t9.js";import{u as pt}from"./CnMg5bH0.js";import{n as lt}from"./DX6rZLP_.js";import{g as Re,u as Ee,n as Be,p as Ue,f as Ke,b as ze,M as de}from"./D2egQzE8.js";import{C as Fe}from"./DrQfh6BY.js";import{I as me}from"./DxW95yuQ.js";import{P as Le,a as qe,b as Ge,F as je}from"./D-o7ybA5.js";import{e as We,P as Ye}from"./BaVT73bJ.js";import{e as Je,i as Qe}from"./C3w0v0gR.js";import{o as ue}from"./CmxjS0TN.js";import{a as Xe}from"./XESq6qWN.js";import{C as ce}from"./DuoUhxYL.js";import{a as be}from"./OOsIR5sE.js";import{h as we,c as Se,a as Ze,d as $e,p as to,k as eo}from"./Bd3zs5C6.js";import{b as oo}from"./Ntteq2n_.js";import{f as ro}from"./Bpi49Nrf.js";import{A as ht,c as vt,d as ge,S as ie,T as fe,P as _e,H as ye,e as Ne,E as Pe}from"./CIOgxH3l.js";import{H as so}from"./BjCTmJLi.js";function io(o){const t=oo("",1e3),e=v(()=>o.candidateValues());function r(f){if(!o.enabled||!i(e).length)return;t.current=t.current+f;const n=o.getCurrentItem(),p=i(e).find(a=>a===n)??"",S=i(e).map(a=>a??""),m=Re(S,t.current,p),l=i(e).find(a=>a===m);return l&&o.onMatch(l),l}function h(){t.current=""}return{search:t,handleTypeaheadSearch:r,resetTypeahead:h}}const no=[vt,_e,ye],lo=[ht,Ne,Pe],ao=[...no,...lo];var mt,bt,wt,St,_t,yt,Nt,Pt,xt,Ct;class xe{constructor(t){d(this,"opts");g(this,mt,R(!1));g(this,bt,R(""));g(this,wt,R(null));g(this,St,R(null));g(this,_t,R(null));g(this,yt,R(""));g(this,Nt,R(null));g(this,Pt,v(()=>this.highlightedNode?this.highlightedNode.getAttribute("data-value"):null));g(this,xt,v(()=>{if(this.highlightedNode)return this.highlightedNode.id}));g(this,Ct,v(()=>this.highlightedNode?this.highlightedNode.getAttribute("data-label"):null));d(this,"isUsingKeyboard",!1);d(this,"isCombobox",!1);d(this,"bitsAttrs");this.opts=t,this.isCombobox=t.isCombobox,this.bitsAttrs=Ao(this),De(()=>{this.opts.open.current||this.setHighlightedNode(null)})}get touchedInput(){return i(s(this,mt))}set touchedInput(t){w(s(this,mt),t,!0)}get inputValue(){return i(s(this,bt))}set inputValue(t){w(s(this,bt),t,!0)}get inputNode(){return i(s(this,wt))}set inputNode(t){w(s(this,wt),t,!0)}get contentNode(){return i(s(this,St))}set contentNode(t){w(s(this,St),t,!0)}get triggerNode(){return i(s(this,_t))}set triggerNode(t){w(s(this,_t),t,!0)}get valueId(){return i(s(this,yt))}set valueId(t){w(s(this,yt),t,!0)}get highlightedNode(){return i(s(this,Nt))}set highlightedNode(t){w(s(this,Nt),t,!0)}get highlightedValue(){return i(s(this,Pt))}set highlightedValue(t){w(s(this,Pt),t)}get highlightedId(){return i(s(this,xt))}set highlightedId(t){w(s(this,xt),t)}get highlightedLabel(){return i(s(this,Ct))}set highlightedLabel(t){w(s(this,Ct),t)}setHighlightedNode(t,e=!1){this.highlightedNode=t,t&&(this.isUsingKeyboard||e)&&t.scrollIntoView({block:this.opts.scrollAlignment.current})}getCandidateNodes(){const t=this.contentNode;return t?Array.from(t.querySelectorAll(`[${this.bitsAttrs.item}]:not([data-disabled])`)):[]}setHighlightedToFirstCandidate(){this.setHighlightedNode(null);const t=this.getCandidateNodes();t.length&&this.setHighlightedNode(t[0])}getNodeByValue(t){return this.getCandidateNodes().find(r=>r.dataset.value===t)??null}setOpen(t){this.opts.open.current=t}toggleOpen(){this.opts.open.current=!this.opts.open.current}handleOpen(){this.setOpen(!0)}handleClose(){this.setHighlightedNode(null),this.setOpen(!1)}toggleMenu(){this.toggleOpen()}}mt=new WeakMap,bt=new WeakMap,wt=new WeakMap,St=new WeakMap,_t=new WeakMap,yt=new WeakMap,Nt=new WeakMap,Pt=new WeakMap,xt=new WeakMap,Ct=new WeakMap;var It,Tt,Ht,At;class ho extends xe{constructor(e){super(e);d(this,"opts");d(this,"isMulti",!1);g(this,It,v(()=>this.opts.value.current!==""));g(this,Tt,v(()=>{var r;return this.opts.items.current.length?((r=this.opts.items.current.find(h=>h.value===this.opts.value.current))==null?void 0:r.label)??"":""}));g(this,Ht,v(()=>this.opts.items.current.length?this.opts.items.current.filter(r=>!r.disabled).map(r=>r.label):[]));g(this,At,v(()=>!(this.isMulti||this.opts.items.current.length===0)));this.opts=e,he(()=>{!this.opts.open.current&&this.highlightedNode&&this.setHighlightedNode(null)}),Q(()=>this.opts.open.current,()=>{this.opts.open.current&&this.setInitialHighlightedNode()})}get hasValue(){return i(s(this,It))}set hasValue(e){w(s(this,It),e)}get currentLabel(){return i(s(this,Tt))}set currentLabel(e){w(s(this,Tt),e)}get candidateLabels(){return i(s(this,Ht))}set candidateLabels(e){w(s(this,Ht),e)}get dataTypeaheadEnabled(){return i(s(this,At))}set dataTypeaheadEnabled(e){w(s(this,At),e)}includesItem(e){return this.opts.value.current===e}toggleItem(e,r=e){this.opts.value.current=this.includesItem(e)?"":e,this.inputValue=r}setInitialHighlightedNode(){be(()=>{if(this.highlightedNode&&document.contains(this.highlightedNode))return;if(this.opts.value.current!==""){const r=this.getNodeByValue(this.opts.value.current);if(r){this.setHighlightedNode(r,!0);return}}const e=this.getCandidateNodes()[0];e&&this.setHighlightedNode(e,!0)})}}It=new WeakMap,Tt=new WeakMap,Ht=new WeakMap,At=new WeakMap;var kt;class uo extends xe{constructor(e){super(e);d(this,"opts");d(this,"isMulti",!0);g(this,kt,v(()=>this.opts.value.current.length>0));this.opts=e,he(()=>{!this.opts.open.current&&this.highlightedNode&&this.setHighlightedNode(null)}),Q(()=>this.opts.open.current,()=>{this.opts.open.current&&this.setInitialHighlightedNode()})}get hasValue(){return i(s(this,kt))}set hasValue(e){w(s(this,kt),e)}includesItem(e){return this.opts.value.current.includes(e)}toggleItem(e,r=e){this.includesItem(e)?this.opts.value.current=this.opts.value.current.filter(h=>h!==e):this.opts.value.current=[...this.opts.value.current,e],this.inputValue=r}setInitialHighlightedNode(){be(()=>{if(this.highlightedNode&&document.contains(this.highlightedNode))return;if(this.opts.value.current.length&&this.opts.value.current[0]!==""){const r=this.getNodeByValue(this.opts.value.current[0]);if(r){this.setHighlightedNode(r,!0);return}}const e=this.getCandidateNodes()[0];e&&this.setHighlightedNode(e,!0)})}}kt=new WeakMap;var nt,dt,X,Ce,le,ae,Dt;class co{constructor(t,e){g(this,X);d(this,"opts");d(this,"root");g(this,nt);g(this,dt);g(this,Dt,v(()=>({id:this.opts.id.current,disabled:this.root.opts.disabled.current?!0:void 0,"aria-haspopup":"listbox","aria-expanded":$e(this.root.opts.open.current),"aria-activedescendant":this.root.highlightedId,"data-state":Se(this.root.opts.open.current),"data-disabled":we(this.root.opts.disabled.current),"data-placeholder":this.root.hasValue?void 0:"",[this.root.bitsAttrs.trigger]:"",onpointerdown:this.onpointerdown,onkeydown:this.onkeydown,onclick:this.onclick,onpointerup:this.onpointerup})));this.opts=t,this.root=e,ct({...t,onRefChange:r=>{this.root.triggerNode=r}}),se(this,nt,Ee({getCurrentItem:()=>this.root.highlightedNode,onMatch:r=>{this.root.setHighlightedNode(r)}})),se(this,dt,io({getCurrentItem:()=>this.root.isMulti?"":this.root.currentLabel,onMatch:r=>{if(this.root.isMulti||!this.root.opts.items.current)return;const h=this.root.opts.items.current.find(f=>f.label===r);h&&(this.root.opts.value.current=h.value)},enabled:!this.root.isMulti&&this.root.dataTypeaheadEnabled,candidateValues:()=>this.root.isMulti?[]:this.root.candidateLabels})),this.onkeydown=this.onkeydown.bind(this),this.onpointerdown=this.onpointerdown.bind(this),this.onpointerup=this.onpointerup.bind(this),this.onclick=this.onclick.bind(this)}onkeydown(t){if(this.root.isUsingKeyboard=!0,(t.key===ht||t.key===vt)&&t.preventDefault(),!this.root.opts.open.current){if(t.key===ge||t.key===ie||t.key===vt||t.key===ht)t.preventDefault(),this.root.handleOpen();else if(!this.root.isMulti&&this.root.dataTypeaheadEnabled){s(this,dt).handleTypeaheadSearch(t.key);return}if(this.root.hasValue)return;const n=this.root.getCandidateNodes();if(!n.length)return;if(t.key===vt){const p=n[0];this.root.setHighlightedNode(p)}else if(t.key===ht){const p=n[n.length-1];this.root.setHighlightedNode(p)}return}if(t.key===fe){this.root.handleClose();return}if((t.key===ge||t.key===ie&&s(this,nt).search.current==="")&&!t.isComposing&&(t.preventDefault(),at(this,X,ae).call(this)))return;if(t.key===ht&&t.altKey&&this.root.handleClose(),ao.includes(t.key)){t.preventDefault();const n=this.root.getCandidateNodes(),p=this.root.highlightedNode,S=p?n.indexOf(p):-1,m=this.root.opts.loop.current;let l;if(t.key===vt?l=Be(n,S,m):t.key===ht?l=Ue(n,S,m):t.key===Ne?l=Ke(n,S,10,m):t.key===_e?l=ze(n,S,10,m):t.key===ye?l=n[0]:t.key===Pe&&(l=n[n.length-1]),!l)return;this.root.setHighlightedNode(l);return}const e=t.ctrlKey||t.altKey||t.metaKey,r=t.key.length===1,h=t.key===ie,f=this.root.getCandidateNodes();if(t.key!==fe){if(!e&&(r||h)){!s(this,nt).handleTypeaheadSearch(t.key,f)&&h&&(t.preventDefault(),at(this,X,ae).call(this));return}this.root.highlightedNode||this.root.setHighlightedToFirstCandidate()}}onclick(t){t.currentTarget.focus()}onpointerdown(t){if(this.root.opts.disabled.current)return;if(t.pointerType==="touch")return t.preventDefault();const e=t.target;e!=null&&e.hasPointerCapture(t.pointerId)&&(e==null||e.releasePointerCapture(t.pointerId)),t.button===0&&t.ctrlKey===!1&&(this.root.opts.open.current===!1?at(this,X,le).call(this,t):this.root.handleClose())}onpointerup(t){t.preventDefault(),t.pointerType==="touch"&&(this.root.opts.open.current===!1?at(this,X,le).call(this,t):this.root.handleClose())}get props(){return i(s(this,Dt))}set props(t){w(s(this,Dt),t)}}nt=new WeakMap,dt=new WeakMap,X=new WeakSet,Ce=function(){this.root.opts.open.current=!0,s(this,dt).resetTypeahead(),s(this,nt).resetTypeahead()},le=function(t){at(this,X,Ce).call(this)},ae=function(){const t=this.root.highlightedValue===this.root.opts.value.current;return!this.root.opts.allowDeselect.current&&t&&!this.root.isMulti?(this.root.handleClose(),!0):(this.root.highlightedValue!==null&&this.root.toggleItem(this.root.highlightedValue,this.root.highlightedLabel??void 0),!this.root.isMulti&&!t?(this.root.handleClose(),!0):!1)},Dt=new WeakMap;var Ot,Vt,te,Mt,Rt;class po{constructor(t,e){d(this,"opts");d(this,"root");g(this,Ot,R(null));g(this,Vt,R(!1));g(this,te,v(()=>{const t=this.root.isCombobox?"--bits-combobox":"--bits-select";return{[`${t}-content-transform-origin`]:"var(--bits-floating-transform-origin)",[`${t}-content-available-width`]:"var(--bits-floating-available-width)",[`${t}-content-available-height`]:"var(--bits-floating-available-height)",[`${t}-anchor-width`]:" var(--bits-floating-anchor-width)",[`${t}-anchor-height`]:"var(--bits-floating-anchor-height)"}}));d(this,"onInteractOutside",t=>{if(t.target===this.root.triggerNode||t.target===this.root.inputNode){t.preventDefault();return}this.opts.onInteractOutside.current(t),!t.defaultPrevented&&this.root.handleClose()});d(this,"onEscapeKeydown",t=>{this.opts.onEscapeKeydown.current(t),!t.defaultPrevented&&this.root.handleClose()});d(this,"onOpenAutoFocus",t=>{t.preventDefault()});d(this,"onCloseAutoFocus",t=>{t.preventDefault()});g(this,Mt,v(()=>({open:this.root.opts.open.current})));g(this,Rt,v(()=>({id:this.opts.id.current,role:"listbox","aria-multiselectable":this.root.isMulti?"true":void 0,"data-state":Se(this.root.opts.open.current),[this.root.bitsAttrs.content]:"",style:{display:"flex",flexDirection:"column",outline:"none",boxSizing:"border-box",pointerEvents:"auto",...i(s(this,te))},onpointermove:this.onpointermove})));d(this,"popperProps",{onInteractOutside:this.onInteractOutside,onEscapeKeydown:this.onEscapeKeydown,onOpenAutoFocus:this.onOpenAutoFocus,onCloseAutoFocus:this.onCloseAutoFocus,trapFocus:!1,loop:!1,onPlaced:()=>{this.root.opts.open.current&&(this.isPositioned=!0)}});this.opts=t,this.root=e,ct({...t,onRefChange:r=>{this.root.contentNode=r},deps:()=>this.root.opts.open.current}),Me(()=>{this.root.contentNode=null,this.isPositioned=!1}),Q(()=>this.root.opts.open.current,()=>{this.root.opts.open.current||(this.isPositioned=!1)}),this.onpointermove=this.onpointermove.bind(this)}get viewportNode(){return i(s(this,Ot))}set viewportNode(t){w(s(this,Ot),t,!0)}get isPositioned(){return i(s(this,Vt))}set isPositioned(t){w(s(this,Vt),t,!0)}onpointermove(t){this.root.isUsingKeyboard=!1}get snippetProps(){return i(s(this,Mt))}set snippetProps(t){w(s(this,Mt),t)}get props(){return i(s(this,Rt))}set props(t){w(s(this,Rt),t)}}Ot=new WeakMap,Vt=new WeakMap,te=new WeakMap,Mt=new WeakMap,Rt=new WeakMap;var Et,Bt,Ut,Kt,zt;class go{constructor(t,e){d(this,"opts");d(this,"root");g(this,Et,v(()=>this.root.includesItem(this.opts.value.current)));g(this,Bt,v(()=>this.root.highlightedValue===this.opts.value.current));d(this,"prevHighlighted",new Xe(()=>this.isHighlighted));g(this,Ut,R(!1));g(this,Kt,v(()=>({selected:this.isSelected,highlighted:this.isHighlighted})));g(this,zt,v(()=>({id:this.opts.id.current,role:"option","aria-selected":this.root.includesItem(this.opts.value.current)?"true":void 0,"data-value":this.opts.value.current,"data-disabled":we(this.opts.disabled.current),"data-highlighted":this.root.highlightedValue===this.opts.value.current&&!this.opts.disabled.current?"":void 0,"data-selected":this.root.includesItem(this.opts.value.current)?"":void 0,"data-label":this.opts.label.current,[this.root.bitsAttrs.item]:"",onpointermove:this.onpointermove,onpointerdown:this.onpointerdown,onpointerup:this.onpointerup})));this.opts=t,this.root=e,ct({...t,deps:()=>this.mounted}),Q([()=>this.isHighlighted,()=>this.prevHighlighted.current],()=>{this.isHighlighted?this.opts.onHighlight.current():this.prevHighlighted.current&&this.opts.onUnhighlight.current()}),Q(()=>this.mounted,()=>{this.mounted&&this.root.setInitialHighlightedNode()}),this.onpointerdown=this.onpointerdown.bind(this),this.onpointerup=this.onpointerup.bind(this),this.onpointermove=this.onpointermove.bind(this)}get isSelected(){return i(s(this,Et))}set isSelected(t){w(s(this,Et),t)}get isHighlighted(){return i(s(this,Bt))}set isHighlighted(t){w(s(this,Bt),t)}get mounted(){return i(s(this,Ut))}set mounted(t){w(s(this,Ut),t,!0)}handleSelect(){if(this.opts.disabled.current)return;const t=this.opts.value.current===this.root.opts.value.current;if(!this.root.opts.allowDeselect.current&&t&&!this.root.isMulti){this.root.handleClose();return}this.root.toggleItem(this.opts.value.current,this.opts.label.current),!this.root.isMulti&&!t&&this.root.handleClose()}get snippetProps(){return i(s(this,Kt))}set snippetProps(t){w(s(this,Kt),t)}onpointerdown(t){t.preventDefault()}onpointerup(t){if(!(t.defaultPrevented||!this.opts.ref.current)){if(t.pointerType==="touch"&&!ro){ue(this.opts.ref.current,"click",()=>{this.handleSelect(),this.root.setHighlightedNode(this.opts.ref.current)},{once:!0});return}t.preventDefault(),this.handleSelect(),t.pointerType==="touch"&&this.root.setHighlightedNode(this.opts.ref.current)}}onpointermove(t){t.pointerType!=="touch"&&this.root.highlightedNode!==this.opts.ref.current&&this.root.setHighlightedNode(this.opts.ref.current)}get props(){return i(s(this,zt))}set props(t){w(s(this,zt),t)}}Et=new WeakMap,Bt=new WeakMap,Ut=new WeakMap,Kt=new WeakMap,zt=new WeakMap;var Ft,Lt;class fo{constructor(t,e){d(this,"opts");d(this,"root");g(this,Ft,R(null));g(this,Lt,v(()=>{var t;return{id:this.opts.id.current,role:"group",[this.root.bitsAttrs.group]:"","aria-labelledby":((t=this.labelNode)==null?void 0:t.id)??void 0}}));this.opts=t,this.root=e,ct(t)}get labelNode(){return i(s(this,Ft))}set labelNode(t){w(s(this,Ft),t,!0)}get props(){return i(s(this,Lt))}set props(t){w(s(this,Lt),t)}}Ft=new WeakMap,Lt=new WeakMap;var qt,Gt;class vo{constructor(t,e){d(this,"opts");d(this,"root");g(this,qt,v(()=>this.root.opts.name.current!==""));g(this,Gt,v(()=>({disabled:eo(this.root.opts.disabled.current),required:to(this.root.opts.required.current),name:this.root.opts.name.current,value:this.opts.value.current,onfocus:this.onfocus})));this.opts=t,this.root=e,this.onfocus=this.onfocus.bind(this)}get shouldRender(){return i(s(this,qt))}set shouldRender(t){w(s(this,qt),t)}onfocus(t){var e,r;t.preventDefault(),this.root.isCombobox?(r=this.root.inputNode)==null||r.focus():(e=this.root.triggerNode)==null||e.focus()}get props(){return i(s(this,Gt))}set props(t){w(s(this,Gt),t)}}qt=new WeakMap,Gt=new WeakMap;var jt,Wt;class mo{constructor(t,e){d(this,"opts");d(this,"content");d(this,"root");g(this,jt,R(0));g(this,Wt,v(()=>({id:this.opts.id.current,role:"presentation",[this.root.bitsAttrs.viewport]:"",style:{position:"relative",flex:1,overflow:"auto"}})));this.opts=t,this.content=e,this.root=e.root,ct({...t,onRefChange:r=>{this.content.viewportNode=r},deps:()=>this.root.opts.open.current})}get prevScrollTop(){return i(s(this,jt))}set prevScrollTop(t){w(s(this,jt),t,!0)}get props(){return i(s(this,Wt))}set props(t){w(s(this,Wt),t)}}jt=new WeakMap,Wt=new WeakMap;var Yt,Jt;class Ie{constructor(t,e){d(this,"opts");d(this,"content");d(this,"root");d(this,"autoScrollTimer",null);d(this,"userScrollTimer",-1);d(this,"isUserScrolling",!1);d(this,"onAutoScroll",lt);g(this,Yt,R(!1));g(this,Jt,v(()=>({id:this.opts.id.current,"aria-hidden":Ze(!0),style:{flexShrink:0},onpointerdown:this.onpointerdown,onpointermove:this.onpointermove,onpointerleave:this.onpointerleave})));this.opts=t,this.content=e,this.root=e.root,ct({...t,deps:()=>this.mounted}),Q([()=>this.mounted],()=>{if(!this.mounted){this.isUserScrolling=!1;return}this.isUserScrolling}),he(()=>{this.mounted||this.clearAutoScrollInterval()}),this.onpointerdown=this.onpointerdown.bind(this),this.onpointermove=this.onpointermove.bind(this),this.onpointerleave=this.onpointerleave.bind(this)}get mounted(){return i(s(this,Yt))}set mounted(t){w(s(this,Yt),t,!0)}handleUserScroll(){window.clearTimeout(this.userScrollTimer),this.isUserScrolling=!0,this.userScrollTimer=window.setTimeout(()=>{this.isUserScrolling=!1},200)}clearAutoScrollInterval(){this.autoScrollTimer!==null&&(window.clearTimeout(this.autoScrollTimer),this.autoScrollTimer=null)}onpointerdown(t){if(this.autoScrollTimer!==null)return;const e=r=>{this.onAutoScroll(),this.autoScrollTimer=window.setTimeout(()=>e(r+1),this.opts.delay.current(r))};this.autoScrollTimer=window.setTimeout(()=>e(1),this.opts.delay.current(0))}onpointermove(t){this.onpointerdown(t)}onpointerleave(t){this.clearAutoScrollInterval()}get props(){return i(s(this,Jt))}set props(t){w(s(this,Jt),t)}}Yt=new WeakMap,Jt=new WeakMap;var Qt,Xt;class bo{constructor(t){d(this,"scrollButtonState");d(this,"content");d(this,"root");g(this,Qt,R(!1));d(this,"scrollIntoViewTimer",null);d(this,"handleScroll",(t=!1)=>{if(t||this.scrollButtonState.handleUserScroll(),!this.content.viewportNode)return;const e=this.content.viewportNode.scrollHeight-this.content.viewportNode.clientHeight,r=Number.parseInt(getComputedStyle(this.content.viewportNode).paddingTop,10);this.canScrollDown=Math.ceil(this.content.viewportNode.scrollTop)<e-r});d(this,"handleAutoScroll",()=>{const t=this.content.viewportNode,e=this.root.highlightedNode;!t||!e||(t.scrollTop=t.scrollTop+e.offsetHeight)});g(this,Xt,v(()=>({...this.scrollButtonState.props,[this.root.bitsAttrs["scroll-down-button"]]:""})));this.scrollButtonState=t,this.content=t.content,this.root=t.root,this.scrollButtonState.onAutoScroll=this.handleAutoScroll,Q([()=>this.content.viewportNode,()=>this.content.isPositioned],()=>{if(!(!this.content.viewportNode||!this.content.isPositioned))return this.handleScroll(!0),ue(this.content.viewportNode,"scroll",()=>this.handleScroll())}),Q(()=>this.scrollButtonState.mounted,()=>{this.scrollButtonState.mounted&&(this.scrollIntoViewTimer&&clearTimeout(this.scrollIntoViewTimer),this.scrollIntoViewTimer=We(5,()=>{const e=this.root.highlightedNode;e==null||e.scrollIntoView({block:this.root.opts.scrollAlignment.current})}))})}get canScrollDown(){return i(s(this,Qt))}set canScrollDown(t){w(s(this,Qt),t,!0)}get props(){return i(s(this,Xt))}set props(t){w(s(this,Xt),t)}}Qt=new WeakMap,Xt=new WeakMap;var Zt,$t;class wo{constructor(t){d(this,"scrollButtonState");d(this,"content");d(this,"root");g(this,Zt,R(!1));d(this,"handleScroll",(t=!1)=>{if(t||this.scrollButtonState.handleUserScroll(),!this.content.viewportNode)return;const e=Number.parseInt(getComputedStyle(this.content.viewportNode).paddingTop,10);this.canScrollUp=this.content.viewportNode.scrollTop-e>.1});d(this,"handleAutoScroll",()=>{!this.content.viewportNode||!this.root.highlightedNode||(this.content.viewportNode.scrollTop=this.content.viewportNode.scrollTop-this.root.highlightedNode.offsetHeight)});g(this,$t,v(()=>({...this.scrollButtonState.props,[this.root.bitsAttrs["scroll-up-button"]]:""})));this.scrollButtonState=t,this.content=t.content,this.root=t.root,this.scrollButtonState.onAutoScroll=this.handleAutoScroll,Q([()=>this.content.viewportNode,()=>this.content.isPositioned],()=>{if(!(!this.content.viewportNode||!this.content.isPositioned))return this.handleScroll(!0),ue(this.content.viewportNode,"scroll",()=>this.handleScroll())})}get canScrollUp(){return i(s(this,Zt))}set canScrollUp(t){w(s(this,Zt),t,!0)}get props(){return i(s(this,$t))}set props(t){w(s(this,$t),t)}}Zt=new WeakMap,$t=new WeakMap;const gt=new ce("Select.Root | Combobox.Root"),So=new ce("Select.Group | Combobox.Group"),ee=new ce("Select.Content | Combobox.Content");function _o(o){const{type:t,...e}=o,r=t==="single"?new ho(e):new uo(e);return gt.set(r)}function yo(o){return ee.set(new po(o,gt.get()))}function No(o){return new co(o,gt.get())}function Po(o){return new go(o,gt.get())}function xo(o){return new mo(o,ee.get())}function Co(o){return new wo(new Ie(o,ee.get()))}function Io(o){return new bo(new Ie(o,ee.get()))}function Tr(o){return So.set(new fo(o,gt.get()))}function To(o){return new vo(o,gt.get())}const Ho=["trigger","content","item","viewport","scroll-up-button","scroll-down-button","group","group-label","separator","arrow","input","content-wrapper","item-text","value"];function Ao(o){const t=o.isCombobox,e={};for(const r of Ho)e[r]=t?`data-combobox-${r}`:`data-select-${r}`;return e}function ve(o,t){E(t,!0);let e=c(t,"value",15,"");const r=To({value:C.with(()=>e())});var h=I(),f=_(h);{var n=p=>{so(p,G(()=>r.props,{get value(){return e()},set value(S){e(S)}}))};M(f,p=>{r.shouldRender&&p(n)})}u(o,h),B()}var ko=U("<div><div><!></div></div>"),Do=U("<div><div><!></div></div>");function Oo(o,t){E(t,!0);let e=c(t,"id",19,pt),r=c(t,"ref",15,null),h=c(t,"forceMount",3,!1),f=c(t,"side",3,"bottom"),n=c(t,"onInteractOutside",3,lt),p=c(t,"onEscapeKeydown",3,lt),S=c(t,"preventScroll",3,!1),m=F(t,["$$slots","$$events","$$legacy","id","ref","forceMount","side","onInteractOutside","onEscapeKeydown","children","child","preventScroll"]);const l=yo({id:C.with(()=>e()),ref:C.with(()=>r(),P=>r(P)),onInteractOutside:C.with(()=>n()),onEscapeKeydown:C.with(()=>p())}),a=v(()=>et(m,l.props));var x=I(),H=_(x);{var A=P=>{Le(P,G(()=>i(a),()=>l.popperProps,{get side(){return f()},get enabled(){return l.root.opts.open.current},get id(){return e()},get preventScroll(){return S()},forceMount:!0,popper:(b,N)=>{let T=()=>N==null?void 0:N().props,k=()=>N==null?void 0:N().wrapperProps;var D=I();const L=v(()=>et(T(),{style:l.props.style}));var j=_(D);{var Z=K=>{var z=I(),st=_(z),W=ne(()=>({props:i(L),wrapperProps:k(),...l.snippetProps}));O(st,()=>t.child,()=>i(W)),u(K,z)},rt=K=>{var z=ko();$(z,()=>({...k()}));var st=Y(z);$(st,()=>({...i(L)}));var W=Y(st);O(W,()=>t.children??q),J(st),J(z),u(K,z)};M(j,K=>{t.child?K(Z):K(rt,!1)})}u(b,D)},$$slots:{popper:!0}}))},V=(P,y)=>{{var b=N=>{qe(N,G(()=>i(a),()=>l.popperProps,{get side(){return f()},get present(){return l.root.opts.open.current},get id(){return e()},get preventScroll(){return S()},forceMount:!1,popper:(k,D)=>{let L=()=>D==null?void 0:D().props,j=()=>D==null?void 0:D().wrapperProps;var Z=I();const rt=v(()=>et(L(),{style:l.props.style}));var K=_(Z);{var z=W=>{var it=I(),ft=_(it),oe=ne(()=>({props:i(rt),wrapperProps:j(),...l.snippetProps}));O(ft,()=>t.child,()=>i(oe)),u(W,it)},st=W=>{var it=Do();$(it,()=>({...j()}));var ft=Y(it);$(ft,()=>({...i(rt)}));var oe=Y(ft);O(oe,()=>t.children??q),J(ft),J(it),u(W,it)};M(K,W=>{t.child?W(z):W(st,!1)})}u(k,Z)},$$slots:{popper:!0}}))};M(P,N=>{h()||N(b)},y)}};M(H,P=>{h()?P(A):P(V,!1)})}u(o,x),B()}var Vo=U("<div><!></div>"),Mo=U("<!> <!>",1);function Ro(o,t){E(t,!0);let e=c(t,"id",19,pt),r=c(t,"ref",15,null),h=c(t,"label",19,()=>t.value),f=c(t,"disabled",3,!1),n=c(t,"onHighlight",3,lt),p=c(t,"onUnhighlight",3,lt),S=F(t,["$$slots","$$events","$$legacy","id","ref","value","label","disabled","children","child","onHighlight","onUnhighlight"]);const m=Po({id:C.with(()=>e()),ref:C.with(()=>r(),P=>r(P)),value:C.with(()=>t.value),disabled:C.with(()=>f()),label:C.with(()=>h()),onHighlight:C.with(()=>n()),onUnhighlight:C.with(()=>p())}),l=v(()=>et(S,m.props));var a=Mo(),x=_(a);{var H=P=>{var y=I(),b=_(y),N=ne(()=>({props:i(l),...m.snippetProps}));O(b,()=>t.child,()=>i(N)),u(P,y)},A=P=>{var y=Vo();$(y,()=>({...i(l)}));var b=Y(y);O(b,()=>t.children??q,()=>m.snippetProps),J(y),u(P,y)};M(x,P=>{t.child?P(H):P(A,!1)})}var V=ot(x,2);de(V,{get mounted(){return m.mounted},set mounted(P){m.mounted=P}}),u(o,a),B()}var Eo=U("<div><!></div>");function Bo(o,t){E(t,!0);let e=c(t,"id",19,pt),r=c(t,"ref",15,null),h=F(t,["$$slots","$$events","$$legacy","id","ref","children","child"]);const f=xo({id:C.with(()=>e()),ref:C.with(()=>r(),a=>r(a))}),n=v(()=>et(h,f.props));var p=I(),S=_(p);{var m=a=>{var x=I(),H=_(x);O(H,()=>t.child,()=>({props:i(n)})),u(a,x)},l=a=>{var x=Eo();$(x,()=>({...i(n)}));var H=Y(x);O(H,()=>t.children??q),J(x),u(a,x)};M(S,a=>{t.child?a(m):a(l,!1)})}u(o,p),B()}var Uo=U("<div><!></div>"),Ko=U("<!> <!>",1);function zo(o,t){E(t,!0);let e=c(t,"id",19,pt),r=c(t,"ref",15,null),h=c(t,"delay",3,()=>50),f=F(t,["$$slots","$$events","$$legacy","id","ref","delay","child","children"]);const n=Io({id:C.with(()=>e()),ref:C.with(()=>r(),a=>r(a)),delay:C.with(()=>h())}),p=v(()=>et(f,n.props));var S=I(),m=_(S);{var l=a=>{var x=Ko(),H=_(x);de(H,{get mounted(){return n.scrollButtonState.mounted},set mounted(y){n.scrollButtonState.mounted=y}});var A=ot(H,2);{var V=y=>{var b=I(),N=_(b);O(N,()=>t.child,()=>({props:f})),u(y,b)},P=y=>{var b=Uo();$(b,()=>({...i(p)}));var N=Y(b);O(N,()=>t.children??q),J(b),u(y,b)};M(A,y=>{t.child?y(V):y(P,!1)})}u(a,x)};M(m,a=>{n.canScrollDown&&a(l)})}u(o,S),B()}var Fo=U("<div><!></div>"),Lo=U("<!> <!>",1);function qo(o,t){E(t,!0);let e=c(t,"id",19,pt),r=c(t,"ref",15,null),h=c(t,"delay",3,()=>50),f=F(t,["$$slots","$$events","$$legacy","id","ref","delay","child","children"]);const n=Co({id:C.with(()=>e()),ref:C.with(()=>r(),a=>r(a)),delay:C.with(()=>h())}),p=v(()=>et(f,n.props));var S=I(),m=_(S);{var l=a=>{var x=Lo(),H=_(x);de(H,{get mounted(){return n.scrollButtonState.mounted},set mounted(y){n.scrollButtonState.mounted=y}});var A=ot(H,2);{var V=y=>{var b=I(),N=_(b);O(N,()=>t.child,()=>({props:f})),u(y,b)},P=y=>{var b=Fo();$(b,()=>({...i(p)}));var N=Y(b);O(N,()=>t.children??q),J(b),u(y,b)};M(A,y=>{t.child?y(V):y(P,!1)})}u(a,x)};M(m,a=>{n.canScrollUp&&a(l)})}u(o,S),B()}var Go=U("<!> <!>",1);function jo(o,t){E(t,!0);let e=c(t,"value",15),r=c(t,"onValueChange",3,lt),h=c(t,"name",3,""),f=c(t,"disabled",3,!1),n=c(t,"open",15,!1),p=c(t,"onOpenChange",3,lt),S=c(t,"loop",3,!1),m=c(t,"scrollAlignment",3,"nearest"),l=c(t,"required",3,!1),a=c(t,"items",19,()=>[]),x=c(t,"allowDeselect",3,!1);function H(){e()===void 0&&e(t.type==="single"?"":[])}H(),Q.pre(()=>e(),()=>{H()});const A=_o({type:t.type,value:C.with(()=>e(),T=>{e(T),r()(T)}),disabled:C.with(()=>f()),required:C.with(()=>l()),open:C.with(()=>n(),T=>{n(T),p()(T)}),loop:C.with(()=>S()),scrollAlignment:C.with(()=>m()),name:C.with(()=>h()),isCombobox:!1,items:C.with(()=>a()),allowDeselect:C.with(()=>x())});var V=Go(),P=_(V);Ge(P,{children:(T,k)=>{var D=I(),L=_(D);O(L,()=>t.children??q),u(T,D)},$$slots:{default:!0}});var y=ot(P,2);{var b=T=>{var k=I(),D=_(k);{var L=j=>{var Z=I(),rt=_(Z);Je(rt,17,()=>A.opts.value.current,Qe,(K,z)=>{ve(K,{get value(){return i(z)}})}),u(j,Z)};M(D,j=>{A.opts.value.current.length&&j(L)})}u(T,k)},N=T=>{ve(T,{get value(){return A.opts.value.current},set value(k){A.opts.value.current=k}})};M(y,T=>{Array.isArray(A.opts.value.current)?T(b):T(N,!1)})}u(o,V),B()}var Wo=U("<button><!></button>");function Yo(o,t){E(t,!0);let e=c(t,"id",19,pt),r=c(t,"ref",15,null),h=c(t,"type",3,"button"),f=F(t,["$$slots","$$events","$$legacy","id","ref","child","children","type"]);const n=No({id:C.with(()=>e()),ref:C.with(()=>r(),l=>r(l))}),p=v(()=>et(f,n.props,{type:h()}));var S=I(),m=_(S);tt(m,()=>je,(l,a)=>{a(l,{get id(){return e()},children:(x,H)=>{var A=I(),V=_(A);{var P=b=>{var N=I(),T=_(N);O(T,()=>t.child,()=>({props:i(p)})),u(b,N)},y=b=>{var N=Wo();$(N,()=>({...i(p)}));var T=Y(N);O(T,()=>t.children??q),J(N),u(b,N)};M(V,b=>{t.child?b(P):b(y,!1)})}u(x,A)},$$slots:{default:!0}})}),u(o,S),B()}var Jo=U('<span class="absolute right-2 flex size-3.5 items-center justify-center"><!></span> <!>',1);function Hr(o,t){E(t,!0);let e=c(t,"ref",15,null),r=F(t,["$$slots","$$events","$$legacy","ref","class","value","label","children"]);var h=I(),f=_(h);const n=v(()=>ut("data-[highlighted]:bg-accent data-[highlighted]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground outline-hidden *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2 relative flex w-full cursor-default select-none items-center gap-2 rounded-sm py-1.5 pl-2 pr-8 text-sm data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",t.class));tt(f,()=>Ro,(p,S)=>{S(p,G({get value(){return t.value},"data-slot":"select-item",get class(){return i(n)}},()=>r,{get ref(){return e()},set ref(l){e(l)},children:(l,a)=>{let x=()=>a==null?void 0:a().selected,H=()=>a==null?void 0:a().highlighted;var A=Jo(),V=_(A),P=Y(V);{var y=k=>{Fe(k,{class:"size-4"})};M(P,k=>{x()&&k(y)})}J(V);var b=ot(V,2);{var N=k=>{var D=I(),L=_(D);O(L,()=>t.children,()=>({selected:x(),highlighted:H()})),u(k,D)},T=k=>{var D=ke();Oe(()=>Ve(D,t.label||t.value)),u(k,D)};M(b,k=>{t.children?k(N):k(T,!1)})}u(l,A)},$$slots:{default:!0}}))}),u(o,h),B()}function Qo(o,t){E(t,!0);let e=F(t,["$$slots","$$events","$$legacy"]);const r=[["path",{d:"m18 15-6-6-6 6"}]];me(o,G({name:"chevron-up"},()=>e,{get iconNode(){return r},children:(h,f)=>{var n=I(),p=_(n);O(p,()=>t.children??q),u(h,n)},$$slots:{default:!0}})),B()}function Xo(o,t){E(t,!0);let e=c(t,"ref",15,null),r=F(t,["$$slots","$$events","$$legacy","ref","class"]);var h=I(),f=_(h);const n=v(()=>ut("flex cursor-default items-center justify-center py-1",t.class));tt(f,()=>qo,(p,S)=>{S(p,G({"data-slot":"select-scroll-up-button",get class(){return i(n)}},()=>r,{get ref(){return e()},set ref(m){e(m)},children:(m,l)=>{Qo(m,{class:"size-4"})},$$slots:{default:!0}}))}),u(o,h),B()}function Te(o,t){E(t,!0);let e=F(t,["$$slots","$$events","$$legacy"]);const r=[["path",{d:"m6 9 6 6 6-6"}]];me(o,G({name:"chevron-down"},()=>e,{get iconNode(){return r},children:(h,f)=>{var n=I(),p=_(n);O(p,()=>t.children??q),u(h,n)},$$slots:{default:!0}})),B()}function Zo(o,t){E(t,!0);let e=c(t,"ref",15,null),r=F(t,["$$slots","$$events","$$legacy","ref","class"]);var h=I(),f=_(h);const n=v(()=>ut("flex cursor-default items-center justify-center py-1",t.class));tt(f,()=>zo,(p,S)=>{S(p,G({"data-slot":"select-scroll-down-button",get class(){return i(n)}},()=>r,{get ref(){return e()},set ref(m){e(m)},children:(m,l)=>{Te(m,{class:"size-4"})},$$slots:{default:!0}}))}),u(o,h),B()}var $o=U("<!> <!> <!>",1);function Ar(o,t){E(t,!0);let e=c(t,"ref",15,null),r=c(t,"sideOffset",3,4),h=F(t,["$$slots","$$events","$$legacy","ref","class","sideOffset","portalProps","children"]);var f=I(),n=_(f);tt(n,()=>Ye,(p,S)=>{S(p,G(()=>t.portalProps,{children:(m,l)=>{var a=I(),x=_(a);const H=v(()=>ut("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 max-h-(--bits-select-content-available-height) origin-(--bits-select-content-transform-origin) relative z-50 min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border shadow-md data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t.class));tt(x,()=>Oo,(A,V)=>{V(A,G({get sideOffset(){return r()},"data-slot":"select-content",get class(){return i(H)}},()=>h,{get ref(){return e()},set ref(P){e(P)},children:(P,y)=>{var b=$o(),N=_(b);Xo(N,{});var T=ot(N,2);const k=v(()=>ut("h-(--bits-select-anchor-height) min-w-(--bits-select-anchor-width) w-full scroll-my-1 p-1"));tt(T,()=>Bo,(L,j)=>{j(L,{get class(){return i(k)},children:(Z,rt)=>{var K=I(),z=_(K);O(z,()=>t.children??q),u(Z,K)},$$slots:{default:!0}})});var D=ot(T,2);Zo(D,{}),u(P,b)},$$slots:{default:!0}}))}),u(m,a)},$$slots:{default:!0}}))}),u(o,f),B()}var tr=U("<!> <!>",1);function kr(o,t){E(t,!0);let e=c(t,"ref",15,null),r=c(t,"size",3,"default"),h=F(t,["$$slots","$$events","$$legacy","ref","class","children","size"]);var f=I(),n=_(f);const p=v(()=>ut("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 shadow-xs flex w-fit items-center justify-between gap-2 whitespace-nowrap rounded-md border bg-transparent px-3 py-2 text-sm outline-none transition-[color,box-shadow] focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",t.class));tt(n,()=>Yo,(S,m)=>{m(S,G({"data-slot":"select-trigger",get"data-size"(){return r()},get class(){return i(p)}},()=>h,{get ref(){return e()},set ref(l){e(l)},children:(l,a)=>{var x=tr(),H=_(x);O(H,()=>t.children??q);var A=ot(H,2);Te(A,{class:"size-4 opacity-50"}),u(l,x)},$$slots:{default:!0}}))}),u(o,f),B()}const Dr=jo;export{Dr as R,kr as S,Ar as a,Hr as b,Tr as u};
