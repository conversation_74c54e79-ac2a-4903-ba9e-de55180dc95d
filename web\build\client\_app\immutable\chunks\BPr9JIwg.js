var It=Object.defineProperty;var vt=i=>{throw TypeError(i)};var Pt=(i,t,e)=>t in i?It(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var C=(i,t,e)=>Pt(i,typeof t!="symbol"?t+"":t,e),mt=(i,t,e)=>t.has(i)||vt("Cannot "+e);var r=(i,t,e)=>(mt(i,t,"read from private field"),e?e.call(i):t.get(i)),w=(i,t,e)=>t.has(i)?vt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(i):t.set(i,e),H=(i,t,e,s)=>(mt(i,t,"write to private field"),s?s.call(i,e):t.set(i,e),e);import{c as I,a as f,f as G}from"./BasJTneF.js";import{x as _,g as a,d as R,k as gt,u as Ct,p as O,f as y,a as N,c as K,au as B,r as J,e as Dt,s as St}from"./CGmarHxI.js";import{c as L}from"./BvdI7LR8.js";import{p as c,r as M,s as ct}from"./Btcx8l8F.js";import{s as x,c as ft}from"./ncUU1dSD.js";import{i as st}from"./u21ee2wt.js";import{e as nt}from"./B-Xjo-Yt.js";import{u as ot,w as pt,b,m as at}from"./BfX7a-t9.js";import{u as z}from"./CnMg5bH0.js";import{n as xt}from"./DX6rZLP_.js";import{P as Rt}from"./XESq6qWN.js";import{a as Tt}from"./OOsIR5sE.js";import{C as bt}from"./DuoUhxYL.js";import{h as lt,g as dt,c as ut,n as kt,d as Ot}from"./Bd3zs5C6.js";import{u as Nt}from"./OXTnUuEm.js";import{S as Mt,d as Et}from"./CIOgxH3l.js";import{C as Vt}from"./BwkAotBa.js";const Ft="data-accordion-root",_t="data-accordion-trigger",Gt="data-accordion-content",Bt="data-accordion-item",Ht="data-accordion-header";var Q;class At{constructor(t){C(this,"opts");C(this,"rovingFocusGroup");w(this,Q,_(()=>({id:this.opts.id.current,"data-orientation":dt(this.opts.orientation.current),"data-disabled":lt(this.opts.disabled.current),[Ft]:""})));this.opts=t,ot(this.opts),this.rovingFocusGroup=Nt({rootNodeId:this.opts.id,candidateAttr:_t,loop:this.opts.loop,orientation:this.opts.orientation})}get props(){return a(r(this,Q))}set props(t){R(r(this,Q),t)}}Q=new WeakMap;class qt extends At{constructor(e){super(e);C(this,"opts");C(this,"isMulti",!1);this.opts=e,this.includesItem=this.includesItem.bind(this),this.toggleItem=this.toggleItem.bind(this)}includesItem(e){return this.opts.value.current===e}toggleItem(e){this.opts.value.current=this.includesItem(e)?"":e}}var k;class jt extends At{constructor(e){super(e);w(this,k);C(this,"isMulti",!0);H(this,k,e.value),this.includesItem=this.includesItem.bind(this),this.toggleItem=this.toggleItem.bind(this)}includesItem(e){return r(this,k).current.includes(e)}toggleItem(e){this.includesItem(e)?r(this,k).current=r(this,k).current.filter(s=>s!==e):r(this,k).current=[...r(this,k).current,e]}}k=new WeakMap;var U,W,X;class zt{constructor(t){C(this,"opts");C(this,"root");w(this,U,_(()=>this.root.includesItem(this.opts.value.current)));w(this,W,_(()=>this.opts.disabled.current||this.root.opts.disabled.current));w(this,X,_(()=>({id:this.opts.id.current,"data-state":ut(this.isActive),"data-disabled":lt(this.isDisabled),"data-orientation":dt(this.root.opts.orientation.current),[Bt]:""})));this.opts=t,this.root=t.rootState,this.updateValue=this.updateValue.bind(this),ot({...t,deps:()=>this.isActive})}get isActive(){return a(r(this,U))}set isActive(t){R(r(this,U),t)}get isDisabled(){return a(r(this,W))}set isDisabled(t){R(r(this,W),t)}updateValue(){this.root.toggleItem(this.opts.value.current)}get props(){return a(r(this,X))}set props(t){R(r(this,X),t)}}U=new WeakMap,W=new WeakMap,X=new WeakMap;var F,V,Y;class Kt{constructor(t,e){C(this,"opts");C(this,"itemState");w(this,F);w(this,V,_(()=>this.opts.disabled.current||this.itemState.opts.disabled.current||r(this,F).opts.disabled.current));w(this,Y,_(()=>({id:this.opts.id.current,disabled:a(r(this,V)),"aria-expanded":Ot(this.itemState.isActive),"aria-disabled":kt(a(r(this,V))),"data-disabled":lt(a(r(this,V))),"data-state":ut(this.itemState.isActive),"data-orientation":dt(r(this,F).opts.orientation.current),[_t]:"",tabindex:0,onclick:this.onclick,onkeydown:this.onkeydown})));this.opts=t,this.itemState=e,H(this,F,e.root),this.onkeydown=this.onkeydown.bind(this),this.onclick=this.onclick.bind(this),ot(t)}onclick(t){if(!a(r(this,V))){if(t.button!==0)return t.preventDefault();this.itemState.updateValue()}}onkeydown(t){if(!a(r(this,V))){if(t.key===Mt||t.key===Et){t.preventDefault(),this.itemState.updateValue();return}r(this,F).rovingFocusGroup.handleKeydown(this.opts.ref.current,t)}}get props(){return a(r(this,Y))}set props(t){R(r(this,Y),t)}}F=new WeakMap,V=new WeakMap,Y=new WeakMap;var q,j,Z,$,tt,et,it;class Jt{constructor(t,e){C(this,"opts");C(this,"item");w(this,q);w(this,j,!1);w(this,Z,gt(0));w(this,$,gt(0));w(this,tt,_(()=>this.opts.forceMount.current||this.item.isActive));w(this,et,_(()=>({open:this.item.isActive})));w(this,it,_(()=>({id:this.opts.id.current,"data-state":ut(this.item.isActive),"data-disabled":lt(this.item.isDisabled),"data-orientation":dt(this.item.root.opts.orientation.current),[Gt]:"",style:{"--bits-accordion-content-height":`${a(r(this,$))}px`,"--bits-accordion-content-width":`${a(r(this,Z))}px`}})));this.opts=t,this.item=e,H(this,j,this.item.isActive),ot(t),Ct(()=>{const s=requestAnimationFrame(()=>{H(this,j,!1)});return()=>{cancelAnimationFrame(s)}}),pt([()=>this.present,()=>this.opts.ref.current],([s,n])=>{n&&Tt(()=>{if(!this.opts.ref.current)return;H(this,q,r(this,q)||{transitionDuration:n.style.transitionDuration,animationName:n.style.animationName}),n.style.transitionDuration="0s",n.style.animationName="none";const u=n.getBoundingClientRect();if(R(r(this,$),u.height,!0),R(r(this,Z),u.width,!0),!r(this,j)){const{animationName:v,transitionDuration:h}=r(this,q);n.style.transitionDuration=h,n.style.animationName=v}})})}get present(){return a(r(this,tt))}set present(t){R(r(this,tt),t)}get snippetProps(){return a(r(this,et))}set snippetProps(t){R(r(this,et),t)}get props(){return a(r(this,it))}set props(t){R(r(this,it),t)}}q=new WeakMap,j=new WeakMap,Z=new WeakMap,$=new WeakMap,tt=new WeakMap,et=new WeakMap,it=new WeakMap;var rt;class Lt{constructor(t,e){C(this,"opts");C(this,"item");w(this,rt,_(()=>({id:this.opts.id.current,role:"heading","aria-level":this.opts.level.current,"data-heading-level":this.opts.level.current,"data-state":ut(this.item.isActive),"data-orientation":dt(this.item.root.opts.orientation.current),[Ht]:""})));this.opts=t,this.item=e,ot(t)}get props(){return a(r(this,rt))}set props(t){R(r(this,rt),t)}}rt=new WeakMap;const wt=new bt("Accordion.Root"),ht=new bt("Accordion.Item");function Qt(i){const{type:t,...e}=i,s=t==="single"?new qt(e):new jt(e);return wt.set(s)}function Ut(i){const t=wt.get();return ht.set(new zt({...i,rootState:t}))}function Wt(i){return new Kt(i,ht.get())}function Xt(i){return new Jt(i,ht.get())}function Yt(i){return new Lt(i,ht.get())}var Zt=G("<div><!></div>");function $t(i,t){O(t,!0);let e=c(t,"disabled",3,!1),s=c(t,"value",15),n=c(t,"ref",15,null),u=c(t,"id",19,z),v=c(t,"onValueChange",3,xt),h=c(t,"loop",3,!0),g=c(t,"orientation",3,"vertical"),l=M(t,["$$slots","$$events","$$legacy","disabled","children","child","type","value","ref","id","onValueChange","loop","orientation"]);function D(){s()===void 0&&s(t.type==="single"?"":[])}D(),pt.pre(()=>s(),()=>{D()});const A=Qt({type:t.type,value:b.with(()=>s(),p=>{s(p),v()(p)}),id:b.with(()=>u()),disabled:b.with(()=>e()),loop:b.with(()=>h()),orientation:b.with(()=>g()),ref:b.with(()=>n(),p=>n(p))}),d=_(()=>at(l,A.props));var o=I(),m=y(o);{var T=p=>{var P=I(),E=y(P);x(E,()=>t.child,()=>({props:a(d)})),f(p,P)},S=p=>{var P=Zt();nt(P,()=>({...a(d)}));var E=K(P);x(E,()=>t.children??B),J(P),f(p,P)};st(m,p=>{t.child?p(T):p(S,!1)})}f(i,o),N()}var te=G("<div><!></div>");function ee(i,t){O(t,!0);let e=c(t,"id",19,z),s=c(t,"disabled",3,!1),n=c(t,"value",19,z),u=c(t,"ref",15,null),v=M(t,["$$slots","$$events","$$legacy","id","disabled","value","children","child","ref"]);const h=Ut({value:b.with(()=>n()),disabled:b.with(()=>s()),id:b.with(()=>e()),ref:b.with(()=>u(),o=>u(o))}),g=_(()=>at(v,h.props));var l=I(),D=y(l);{var A=o=>{var m=I(),T=y(m);x(T,()=>t.child,()=>({props:a(g)})),f(o,m)},d=o=>{var m=te();nt(m,()=>({...a(g)}));var T=K(m);x(T,()=>t.children??B),J(m),f(o,m)};st(D,o=>{t.child?o(A):o(d,!1)})}f(i,l),N()}var ie=G("<div><!></div>");function re(i,t){O(t,!0);let e=c(t,"id",19,z),s=c(t,"level",3,2),n=c(t,"ref",15,null),u=M(t,["$$slots","$$events","$$legacy","id","level","children","child","ref"]);const v=Yt({id:b.with(()=>e()),level:b.with(()=>s()),ref:b.with(()=>n(),d=>n(d))}),h=_(()=>at(u,v.props));var g=I(),l=y(g);{var D=d=>{var o=I(),m=y(o);x(m,()=>t.child,()=>({props:a(h)})),f(d,o)},A=d=>{var o=ie();nt(o,()=>({...a(h)}));var m=K(o);x(m,()=>t.children??B),J(o),f(d,o)};st(l,d=>{t.child?d(D):d(A,!1)})}f(i,g),N()}var se=G("<button><!></button>");function ne(i,t){O(t,!0);let e=c(t,"disabled",3,!1),s=c(t,"ref",15,null),n=c(t,"id",19,z),u=M(t,["$$slots","$$events","$$legacy","disabled","ref","id","children","child"]);const v=Wt({disabled:b.with(()=>e()),id:b.with(()=>n()),ref:b.with(()=>s(),d=>s(d))}),h=_(()=>at(u,v.props));var g=I(),l=y(g);{var D=d=>{var o=I(),m=y(o);x(m,()=>t.child,()=>({props:a(h)})),f(d,o)},A=d=>{var o=se();nt(o,()=>({type:"button",...a(h)}));var m=K(o);x(m,()=>t.children??B),J(o),f(d,o)};st(l,d=>{t.child?d(D):d(A,!1)})}f(i,g),N()}var oe=G("<div><!></div>");function ae(i,t){O(t,!0);let e=c(t,"ref",15,null),s=c(t,"id",19,z),n=c(t,"forceMount",3,!1),u=M(t,["$$slots","$$events","$$legacy","child","ref","id","forceMount","children"]);const v=Xt({forceMount:b.with(()=>n()),id:b.with(()=>s()),ref:b.with(()=>e(),h=>e(h))});Rt(i,{forceMount:!0,get present(){return v.present},get id(){return s()},presence:(g,l)=>{let D=()=>l==null?void 0:l().present;var A=I();const d=_(()=>at(u,v.props,{hidden:n()?void 0:!D().current}));var o=y(A);{var m=S=>{var p=I(),P=y(p),E=Dt(()=>({props:a(d),...v.snippetProps}));x(P,()=>t.child,()=>a(E)),f(S,p)},T=S=>{var p=oe();nt(p,()=>({...a(d)}));var P=K(p);x(P,()=>t.children??B),J(p),f(S,p)};st(o,S=>{t.child?S(m):S(T,!1)})}f(g,A)},$$slots:{presence:!0}}),N()}function xe(i,t){O(t,!0);let e=c(t,"ref",15,null),s=c(t,"value",15),n=M(t,["$$slots","$$events","$$legacy","ref","value"]);var u=I(),v=y(u);L(v,()=>$t,(h,g)=>{g(h,ct({"data-slot":"accordion"},()=>n,{get ref(){return e()},set ref(l){e(l)},get value(){return s()},set value(l){s(l)}}))}),f(i,u),N()}var de=G('<div class="p-4"><!></div>');function Re(i,t){O(t,!0);let e=c(t,"ref",15,null),s=M(t,["$$slots","$$events","$$legacy","ref","class","children"]);var n=I(),u=y(n);const v=_(()=>ft("data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",t.class));L(u,()=>ae,(h,g)=>{g(h,ct({"data-slot":"accordion-content",get class(){return a(v)}},()=>s,{get ref(){return e()},set ref(l){e(l)},children:(l,D)=>{var A=de(),d=K(A);x(d,()=>t.children??B),J(A),f(l,A)},$$slots:{default:!0}}))}),f(i,n),N()}function Te(i,t){O(t,!0);let e=c(t,"ref",15,null),s=M(t,["$$slots","$$events","$$legacy","ref","class"]);var n=I(),u=y(n);const v=_(()=>ft("border-b last:border-b-0",t.class));L(u,()=>ee,(h,g)=>{g(h,ct({"data-slot":"accordion-item",get class(){return a(v)}},()=>s,{get ref(){return e()},set ref(l){e(l)}}))}),f(i,n),N()}var ce=G("<!> <!>",1);function ke(i,t){O(t,!0);let e=c(t,"ref",15,null),s=c(t,"level",3,3),n=M(t,["$$slots","$$events","$$legacy","ref","class","level","children"]);var u=I(),v=y(u);L(v,()=>re,(h,g)=>{g(h,{get level(){return s()},class:"flex",children:(l,D)=>{var A=I(),d=y(A);const o=_(()=>ft("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-row items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium outline-none transition-all hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",t.class));L(d,()=>ne,(m,T)=>{T(m,ct({"data-slot":"accordion-trigger",get class(){return a(o)}},()=>n,{get ref(){return e()},set ref(S){e(S)},children:(S,p)=>{var P=ce(),E=y(P);x(E,()=>t.children??B);var yt=St(E,2);Vt(yt,{class:"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"}),f(S,P)},$$slots:{default:!0}}))}),f(l,A)},$$slots:{default:!0}})}),f(i,u),N()}export{xe as A,Te as a,ke as b,Re as c};
