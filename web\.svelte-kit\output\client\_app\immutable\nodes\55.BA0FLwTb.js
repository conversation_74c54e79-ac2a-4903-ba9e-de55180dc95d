import{f,a as d,c as Ue,t as V}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as ft}from"../chunks/nZgk9enP.js";import{p as Be,l as He,d as v,m as me,h as et,b as rt,f as T,c as l,r as o,s as n,t as ne,g as e,a as Ve,n as C,e as X,x as mt,aM as gt,aL as Ee}from"../chunks/CGmarHxI.js";import{s as U}from"../chunks/CIt1g2O9.js";import{i as ie}from"../chunks/u21ee2wt.js";import{e as je,i as Re}from"../chunks/C3w0v0gR.js";import{i as We}from"../chunks/BIEMS98f.js";import{C as ht}from"../chunks/Cdn-N1RY.js";import{R as _t,T as yt}from"../chunks/I7hvcB12.js";import{R as at,D as st,a as it}from"../chunks/WD4kvFhR.js";import{B as Ge}from"../chunks/B1K98fMG.js";import{B as bt}from"../chunks/DaBofrVv.js";import{S as xt}from"../chunks/C6g8ubaU.js";import{F as K,a as $,L as De}from"../chunks/iTBjRg9v.js";import{t as I}from"../chunks/DjPYYl4Z.js";import{b as $t}from"../chunks/VYoCKyli.js";import{p as W}from"../chunks/Btcx8l8F.js";import{A as wt,a as Pt,b as St,c as Ct}from"../chunks/BPr9JIwg.js";import{s as It,r as Me,a as Ft,f as Oe,i as Tt,g as kt}from"../chunks/B-Xjo-Yt.js";import{C as nt}from"../chunks/BwkAotBa.js";import{D as Ae}from"../chunks/Z6UAQTuv.js";import{C as ot}from"../chunks/BNEH2jqx.js";import{e as Ne}from"../chunks/CmxjS0TN.js";import{L as Ce}from"../chunks/BvvicRXk.js";import{S as At}from"../chunks/D9yI7a4E.js";import{R as tt}from"../chunks/qwsZpUIl.js";import{C as Lt}from"../chunks/CKg8MWp_.js";import{D as ut}from"../chunks/Dz4exfp3.js";import{E as Et}from"../chunks/zNKWipEG.js";import{S as Mt}from"../chunks/BHzYYMdu.js";import{A as Ot}from"../chunks/Cs0qIT7f.js";import{T as Xe}from"../chunks/C88uNE8B.js";import{T as Ze}from"../chunks/DmZyh-PW.js";var Nt=f('<button aria-label="Select feature access level"><span> </span> <!></button>'),jt=f('<div class="flex-1"> </div> <!>',1),Rt=f("<!> <!>",1);function Jt(xe,g){Be(g,!1);const s=me(),y=me();let b=W(g,"featureId",8),m=W(g,"value",28,()=>K.NotIncluded),S=W(g,"onChange",8);const J=[{value:K.NotIncluded,label:"Not Included"},{value:K.Included,label:"Included"},{value:K.Limited,label:"Limited"},{value:K.Unlimited,label:"Unlimited"}];function w(_){m()!==_&&(m(_),S()(b(),_))}function O(_){switch(_){case K.NotIncluded:return"bg-gray-100 text-gray-700 hover:bg-gray-200";case K.Included:return"bg-blue-100 text-blue-700 hover:bg-blue-200";case K.Limited:return"bg-amber-100 text-amber-700 hover:bg-amber-200";case K.Unlimited:return"bg-green-100 text-green-700 hover:bg-green-200";default:return"bg-gray-100 text-gray-700 hover:bg-gray-200"}}He(()=>et(m()),()=>{var _;v(s,((_=J.find(A=>A.value===m()))==null?void 0:_.label)||"Not Included")}),He(()=>et(m()),()=>{v(y,O(m()))}),rt(),We(),at(xe,{children:(_,A)=>{var q=Rt(),H=T(q);st(H,{children:(ae,L)=>{var Y=Nt(),F=l(Y),k=l(F,!0);o(F);var B=n(F,2);nt(B,{class:"h-4 w-4 ml-2"}),o(Y),ne(()=>{It(Y,1,`flex items-center justify-between rounded-md px-3 py-2 text-sm font-medium ${e(y)??""} w-full`),U(k,e(s))}),d(ae,Y)},$$slots:{default:!0}});var re=n(H,2);it(re,{align:"start",class:"w-48",children:(ae,L)=>{var Y=Ue(),F=T(Y);je(F,1,()=>J,Re,(k,B)=>{Ae(k,{onclick:()=>w(e(B).value),class:"flex items-center cursor-pointer",children:(z,_e)=>{var N=jt(),j=T(N),M=l(j,!0);o(j);var ce=n(j,2);{var Z=G=>{ot(G,{class:"h-4 w-4 text-green-500"})};ie(ce,G=>{m()===e(B).value&&G(Z)})}ne(()=>U(M,e(B).label)),d(z,N)},$$slots:{default:!0}})}),d(ae,Y)},$$slots:{default:!0}}),d(_,q)},$$slots:{default:!0}}),Ve()}var Dt=f('<button type="button" class="h-6 w-6 inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-gray-100 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring" aria-label="Select limit value"><!></button>'),Ut=f('<div class="flex-1"> </div> <!>',1),Bt=f("<!> <!>",1),Vt=f('<div class="flex items-center gap-2"><div class="flex-1"><!> <div class="text-muted-foreground text-xs"> </div></div> <div class="w-32"><div class="flex items-center gap-2"><div class="relative w-full"><input type="text" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50 pr-8"/> <!></div> <div class="text-muted-foreground text-xs"> </div></div></div></div>');function vt(xe,g){Be(g,!1);const s=me();let y=W(g,"featureId",8),b=W(g,"limit",8),m=W(g,"value",28,()=>b().defaultValue),S=W(g,"onChange",8);const J=[{value:5,label:"5"},{value:10,label:"10"},{value:25,label:"25"},{value:50,label:"50"},{value:100,label:"100"},{value:"unlimited",label:"Unlimited"}];function w(N){const j=N.target,M=j.value==="unlimited"?"unlimited":parseInt(j.value,10);m()!==M&&(m(M),S()(y(),b().id,M))}function O(N){m()!==N&&(m(N),S()(y(),b().id,N))}He(()=>et(m()),()=>{v(s,m()==="unlimited"?"Unlimited":m().toString())}),rt(),We();var _=Vt(),A=l(_),q=l(A);const H=X(()=>`${y()}-${b().id}`);Ce(q,{get for(){return e(H)},class:"text-xs",children:(N,j)=>{C();var M=V();ne(()=>U(M,b().name)),d(N,M)},$$slots:{default:!0}});var re=n(q,2),ae=l(re,!0);o(re),o(A);var L=n(A,2),Y=l(L),F=l(Y),k=l(F);Me(k);var B=n(k,2);at(B,{children:(N,j)=>{var M=Bt(),ce=T(M);st(ce,{class:"absolute right-1 top-1/2 -translate-y-1/2",children:(G,ge)=>{var Q=Dt(),oe=l(Q);nt(oe,{class:"h-4 w-4"}),o(Q),d(G,Q)},$$slots:{default:!0}});var Z=n(ce,2);it(Z,{align:"end",class:"w-32",children:(G,ge)=>{var Q=Ue(),oe=T(Q);je(oe,1,()=>J,Re,(ee,le)=>{Ae(ee,{onclick:()=>O(e(le).value),class:"flex items-center cursor-pointer",children:(Pe,P)=>{var ue=Ut(),se=T(ue),ye=l(se);o(se);var t=n(se,2);{var a=r=>{ot(r,{class:"h-4 w-4 text-green-500"})};ie(t,r=>{m()===e(le).value&&r(a)})}ne(()=>U(ye,`${e(le).label??""} ${(b().unit||"")??""}`)),d(Pe,ue)},$$slots:{default:!0}})}),d(G,Q)},$$slots:{default:!0}}),d(N,M)},$$slots:{default:!0}}),o(F);var z=n(F,2),_e=l(z,!0);o(z),o(Y),o(L),o(_),ne(()=>{U(ae,b().description),Ft(k,"id",`${y()}-${b().id}`),Oe(k,e(s)),U(_e,b().unit||"")}),Ne("input",k,w),d(xe,_),Ve()}var Wt=f('<div class="space-y-2"><!></div>'),Yt=f('<div class="grid grid-cols-12 items-center gap-4 p-4 pb-4"><div class="col-span-4"><div class="font-medium"> </div> <div class="text-muted-foreground text-sm"> </div></div> <div class="col-span-3"><!></div> <div class="col-span-5"><!></div></div>'),zt=f("<!> <!>",1),Kt=f('<div class="force-overflow-y-auto max-h-[calc(100vh-300px)] overflow-y-auto p-4"><!></div>');function qt(xe,g){Be(g,!1);function s(L){switch(L){case $.Resume:return"Monthly Usage";case $.JobSearch:return"Saved Items";case $.Applications:return"Monthly Applications";case $.Team:return"Team Members";case $.Integration:return"API Calls";case $.Analytics:return"Reports";default:return"Usage Limit"}}function y(L){switch(L){case $.Resume:return"Maximum number of uses per month";case $.JobSearch:return"Maximum number of items you can save";case $.Applications:return"Maximum number of applications per month";case $.Team:return"Maximum number of team members";case $.Integration:return"Maximum number of API calls per month";case $.Analytics:return"Maximum number of reports you can create";default:return"Maximum usage limit for this feature"}}function b(L){switch(L){case $.Resume:return 10;case $.JobSearch:return 25;case $.Applications:return 20;case $.Team:return 3;case $.Integration:return 100;case $.Analytics:return 5;default:return 10}}function m(L){switch(L){case $.Resume:return De.Monthly;case $.JobSearch:return De.Total;case $.Applications:return De.Monthly;case $.Team:return De.Concurrent;case $.Integration:return De.Monthly;case $.Analytics:return De.Total;default:return De.Total}}function S(L){switch(L){case $.Resume:return"uses";case $.JobSearch:return"items";case $.Applications:return"applications";case $.Team:return"members";case $.Integration:return"calls";case $.Analytics:return"reports";default:return"uses"}}const J=void 0;let w=W(g,"featuresByCategory",8),O=W(g,"expandedCategories",8),_=W(g,"getFeatureAccessLevel",8),A=W(g,"getFeatureLimitValue",8),q=W(g,"updateFeatureAccessLevel",8),H=W(g,"updateFeatureLimitValue",8);We();var re=Kt(),ae=l(re);return wt(ae,{class:"border-border w-full border",type:"multiple",get value(){return O()},children:(L,Y)=>{var F=Ue(),k=T(F);je(k,1,()=>Object.entries(w()),Re,(B,z)=>{var _e=mt(()=>gt(e(z),2));let N=()=>e(_e)[0],j=()=>e(_e)[1];Pt(B,{get value(){return N()},children:(M,ce)=>{var Z=zt(),G=T(Z);St(G,{class:"px-4 text-base capitalize",children:(Q,oe)=>{C();var ee=V();ne(le=>U(ee,`${le??""} Features`),[()=>N().replace("_"," ")],X),d(Q,ee)},$$slots:{default:!0}});var ge=n(G,2);Ct(ge,{class:"border-border flex flex-col border-t",children:(Q,oe)=>{var ee=Ue(),le=T(ee);je(le,1,j,Re,(Pe,P)=>{var ue=Yt(),se=l(ue),ye=l(se),t=l(ye,!0);o(ye);var a=n(ye,2),r=l(a,!0);o(a),o(se);var i=n(se,2),u=l(i);const p=X(()=>_()(e(P).id));Jt(u,{get featureId(){return e(P).id},get value(){return e(p)},get onChange(){return q()}}),o(i);var h=n(i,2),he=l(h);{var be=ve=>{var c=Wt(),E=l(c);{var x=pe=>{var Ie=Ue(),Fe=T(Ie);je(Fe,1,()=>e(P).limits,Re,(Ye,Le)=>{const Te=X(()=>A()(e(P).id,e(Le).id)||e(Le).defaultValue);vt(Ye,{get featureId(){return e(P).id},get limit(){return e(Le)},get value(){return e(Te)},get onChange(){return H()}})}),d(pe,Ie)},Je=pe=>{const Ie=X(()=>({id:`${e(P).id}_limit`,name:s(e(P).category),description:y(e(P).category),defaultValue:b(e(P).category),type:m(e(P).category),unit:S(e(P).category)})),Fe=X(()=>A()(e(P).id,`${e(P).id}_limit`)||b(e(P).category));vt(pe,{get featureId(){return e(P).id},get limit(){return e(Ie)},get value(){return e(Fe)},get onChange(){return H()}})};ie(E,pe=>{e(P).limits&&e(P).limits.length>0?pe(x):pe(Je,!1)})}o(c),d(ve,c)};ie(he,ve=>{_()(e(P).id)===K.Limited&&ve(be)})}o(h),o(ue),ne(()=>{U(t,e(P).name),U(r,e(P).description)}),d(Pe,ue)}),d(Q,ee)},$$slots:{default:!0}}),d(M,Z)},$$slots:{default:!0}})}),d(L,F)},$$slots:{default:!0}}),o(re),d(xe,re),$t(g,"selectedPlan",J),Ve({selectedPlan:J})}var Ht=f("<!> Sync with Stripe",1),Gt=f('<p class="mt-2 text-sm text-green-600"> </p>'),Qt=f('<div class="space-y-4"><div class="grid grid-cols-2 gap-4"><div><!> <input id="plan-name" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <input id="plan-id" readonly="" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div></div> <div><!> <input id="plan-description" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="grid grid-cols-2 gap-4"><div><!> <input id="monthly-price" type="number" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <input id="annual-price" type="number" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div></div> <div class="space-y-4"><div class="grid grid-cols-2 gap-4"><div><!> <input id="stripe-monthly-id" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div><!> <input id="stripe-yearly-id" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div></div> <div><!> <!></div></div> <div class="flex items-center space-x-2"><div class="flex items-center space-x-2"><!> <!></div></div> <div><!> <div class="relative"><select id="section" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"><option>Pro</option><option>Teams</option></select></div></div></div>');function Xt(xe,g){Be(g,!1);let s=W(g,"selectedPlan",12),y=W(g,"syncPlanWithStripe",8),b=W(g,"syncingWithStripe",8),m=W(g,"stripeMessage",8);function S(c,E){s(s()[c]=E,!0),s({...s()})}We();var J=Qt(),w=l(J),O=l(w),_=l(O);Ce(_,{for:"plan-name",children:(c,E)=>{C();var x=V("Plan Name");d(c,x)},$$slots:{default:!0}});var A=n(_,2);Me(A),o(O);var q=n(O,2),H=l(q);Ce(H,{for:"plan-id",children:(c,E)=>{C();var x=V("Plan ID");d(c,x)},$$slots:{default:!0}});var re=n(H,2);Me(re),o(q),o(w);var ae=n(w,2),L=l(ae);Ce(L,{for:"plan-description",children:(c,E)=>{C();var x=V("Description");d(c,x)},$$slots:{default:!0}});var Y=n(L,2);Me(Y),o(ae);var F=n(ae,2),k=l(F),B=l(k);Ce(B,{for:"monthly-price",children:(c,E)=>{C();var x=V("Monthly Price (cents)");d(c,x)},$$slots:{default:!0}});var z=n(B,2);Me(z),o(k);var _e=n(k,2),N=l(_e);Ce(N,{for:"annual-price",children:(c,E)=>{C();var x=V("Annual Price (cents)");d(c,x)},$$slots:{default:!0}});var j=n(N,2);Me(j),o(_e),o(F);var M=n(F,2),ce=l(M),Z=l(ce),G=l(Z);Ce(G,{for:"stripe-monthly-id",children:(c,E)=>{C();var x=V("Stripe Monthly Price ID");d(c,x)},$$slots:{default:!0}});var ge=n(G,2);Me(ge),o(Z);var Q=n(Z,2),oe=l(Q);Ce(oe,{for:"stripe-yearly-id",children:(c,E)=>{C();var x=V("Stripe Yearly Price ID");d(c,x)},$$slots:{default:!0}});var ee=n(oe,2);Me(ee),o(Q),o(ce);var le=n(ce,2),Pe=l(le);Ge(Pe,{variant:"outline",get onclick(){return y()},get disabled(){return b()},class:"w-full",children:(c,E)=>{var x=Ht(),Je=T(x);const pe=X(()=>`mr-2 h-4 w-4 ${b()?"animate-spin":""}`);tt(Je,{get class(){return e(pe)}}),C(),d(c,x)},$$slots:{default:!0}});var P=n(Pe,2);{var ue=c=>{var E=Gt(),x=l(E,!0);o(E),ne(()=>U(x,m())),d(c,E)};ie(P,c=>{m()&&c(ue)})}o(le),o(M);var se=n(M,2),ye=l(se),t=l(ye);const a=X(()=>s().popular||!1);At(t,{id:"popular",get checked(){return e(a)},onCheckedChange:c=>S("popular",c)});var r=n(t,2);Ce(r,{for:"popular",class:"cursor-pointer",children:(c,E)=>{C();var x=V("Mark as popular plan");d(c,x)},$$slots:{default:!0}}),o(ye),o(se);var i=n(se,2),u=l(i);Ce(u,{for:"section",children:(c,E)=>{C();var x=V("Section");d(c,x)},$$slots:{default:!0}});var p=n(u,2),h=l(p);Tt(h,()=>s().section);var he,be=l(h);be.value=be.__value="pro";var ve=n(be);ve.value=ve.__value="teams",o(h),o(p),o(i),o(J),ne(()=>{Oe(A,s().name),Oe(re,s().id),Oe(Y,s().description),Oe(z,s().monthlyPrice),Oe(j,s().annualPrice),Oe(ge,s().stripePriceMonthlyId||""),Oe(ee,s().stripePriceYearlyId||""),he!==(he=s().section)&&(h.value=(h.__value=s().section)??"",kt(h,s().section))}),Ne("input",A,c=>S("name",c.currentTarget.value)),Ne("input",Y,c=>S("description",c.currentTarget.value)),Ne("input",z,c=>S("monthlyPrice",parseInt(c.currentTarget.value,10))),Ne("input",j,c=>S("annualPrice",parseInt(c.currentTarget.value,10))),Ne("input",ge,c=>S("stripePriceMonthlyId",c.currentTarget.value)),Ne("input",ee,c=>S("stripePriceYearlyId",c.currentTarget.value)),Ne("change",h,c=>S("section",c.currentTarget.value)),d(xe,J),Ve()}var Zt=f(" <!>",1),er=f('<span class="text-muted-foreground ml-1">( <!> )</span>'),tr=f('<li class="flex items-center text-sm"><!> <span> </span> <!></li>'),rr=f(`<div class="space-y-6"><div class="rounded-lg border p-6"><h3 class="mb-2 text-lg font-medium"> </h3> <p class="text-3xl font-bold"> </p> <p class="text-muted-foreground">per month</p> <div class="mt-6 space-y-4"><h4 class="font-medium">Features</h4> <ul class="space-y-2"></ul></div> <!></div> <div class="rounded-lg border p-6"><h3 class="mb-4 text-lg font-medium">Feature Check Integration</h3> <div class="space-y-4"><p class="text-sm">This plan's features are automatically connected to the feature check system. Here's how you
        can use feature checks in your components:</p> <div class="rounded-md bg-gray-50 p-4"><h4 class="mb-2 text-sm font-medium">Using the FeatureCheck Component</h4> <div class="rounded bg-gray-100 p-2 text-xs"><div>1. Import the component:</div> <div class="mt-1 pl-2">import FeatureCheck from '$lib/components/features/FeatureCheck.svelte';</div> <div class="mt-2">2. Use it in your component:</div> <div class="mt-1 pl-2">&lt;FeatureCheck featureId="feature_id"&gt;</div> <div class="pl-4">Content only visible with access</div> <div class="pl-2">&lt;/FeatureCheck&gt;</div></div></div> <div class="rounded-md bg-gray-50 p-4"><h4 class="mb-2 text-sm font-medium">Using the Feature Check with Limits</h4> <div class="rounded bg-gray-100 p-2 text-xs"><div>1. Import the component:</div> <div class="mt-1 pl-2">import FeatureCheck from '$lib/components/features/FeatureCheck.svelte';</div> <div class="mt-2">2. Use it with a limit:</div> <div class="mt-1 pl-2">&lt;FeatureCheck featureId="feature_id" limitId="limit_id"&gt;</div> <div class="pl-4">Content only visible if limit not reached</div> <div class="pl-2">&lt;/FeatureCheck&gt;</div></div></div></div></div> <div class="space-y-4"><div class="rounded-md border border-yellow-200 bg-yellow-50 p-4 text-sm text-yellow-800"><div class="flex items-center"><!> <div class="font-medium">Preview</div></div> <div class="mt-2 pl-6">This is a preview of how the plan will appear to users. The actual appearance may vary
        depending on the UI implementation.</div></div> <div class="rounded-md border border-blue-200 bg-blue-50 p-4 text-sm text-blue-800"><div class="flex items-center"><div class="font-medium">Usage Tips</div></div> <div class="mt-2 pl-6"><ul class="list-disc space-y-1 pl-4"><li>Use the <strong>Features</strong> tab to configure which features are included in this plan</li> <li>Use the <strong>Plan Details</strong> tab to set pricing and other plan metadata</li> <li>Click <strong>Save Changes</strong> at the top of the page when you're done</li></ul></div></div></div></div>`);function ar(xe,g){Be(g,!1);let s=W(g,"selectedPlan",8),y=W(g,"formatPrice",8),b=W(g,"features",8);We();var m=rr(),S=l(m),J=l(S),w=l(J,!0);o(J);var O=n(J,2),_=l(O);o(O);var A=n(O,4),q=n(l(A),2);je(q,5,()=>s().features.filter(F=>F.accessLevel!==K.NotIncluded),Re,(F,k)=>{var B=Ue();const z=X(()=>b().find(j=>j.id===e(k).featureId));var _e=T(B);{var N=j=>{var M=tr(),ce=l(M);ot(ce,{class:"mr-2 h-4 w-4 text-green-500"});var Z=n(ce,2),G=l(Z,!0);o(Z);var ge=n(Z,2);{var Q=oe=>{var ee=er(),le=n(l(ee));je(le,1,()=>e(k).limits,Re,(Pe,P)=>{var ue=Ue();const se=X(()=>e(z).limits.find(a=>a.id===e(P).limitId));var ye=T(ue);{var t=a=>{var r=Zt(),i=T(r),u=n(i);{var p=h=>{var he=V(",");d(h,he)};ie(u,h=>{e(P)!==e(k).limits[e(k).limits.length-1]&&h(p)})}ne(()=>U(i,`${e(P).value??""}
                      ${e(se).unit??""} `)),d(a,r)};ie(ye,a=>{e(se)&&a(t)})}d(Pe,ue)}),C(),o(ee),d(oe,ee)};ie(ge,oe=>{e(k).accessLevel===K.Limited&&e(k).limits&&e(z).limits&&oe(Q)})}o(M),ne(()=>U(G,e(z).name)),d(j,M)};ie(_e,j=>{e(z)&&j(N)})}d(F,B)}),o(q),o(A);var H=n(A,2);Ge(H,{class:"mt-6 w-full",children:(F,k)=>{C();var B=V();ne(()=>U(B,`Choose ${s().name??""}`)),d(F,B)},$$slots:{default:!0}}),o(S);var re=n(S,4),ae=l(re),L=l(ae),Y=l(L);Lt(Y,{class:"mr-2 h-4 w-4"}),C(2),o(L),C(2),o(ae),C(2),o(re),o(m),ne(F=>{U(w,s().name),U(_,`$${F??""}`)},[()=>y()(s().monthlyPrice)],X),d(xe,m),Ve()}var sr=f("Actions <!>",1),ir=f('<span class="text-muted-foreground ml-2">(Loading...)</span>'),nr=f("<!> Load from Stripe <!>",1),or=f('<span class="text-muted-foreground ml-2">(Syncing...)</span>'),lr=f("<!> Sync All with Stripe <!>",1),dr=f('<span class="text-muted-foreground ml-2">(Syncing...)</span>'),cr=f("<!> Sync Features <!>",1),ur=f("<!> Save Changes",1),vr=f("<!> <!> <!> <!> <!> <!> <!> <!> <!>",1),pr=f("<!> <!>",1),fr=f('<div class="flex h-64 items-center justify-center"><div class="text-center"><div class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div> <p class="mt-4 text-lg">Loading plans...</p></div></div>'),mr=f('<div class="mb-4 rounded-lg bg-red-100 p-4 text-sm text-red-700"><p> </p> <p class="mt-2">Using fallback data. Changes may not be saved correctly.</p></div>'),gr=f(" <!>",1),hr=f("<!> <!> <!>",1),_r=f("<!> <!> <!> <!>",1),yr=f('<div class="border-border col-span-2 flex flex-col gap-2 border-r p-2"></div> <div class="col-span-10 flex flex-col"><div class="border-border flex items-center justify-between border-b p-4"><div class="flex flex-col gap-1"><h5> </h5> <div class="text-muted-foreground text-sm"> </div></div> <div class="flex items-center gap-4"><div class="text-right"><div class="text-muted-foreground text-sm">Monthly</div> <div class="text-xl font-bold"> </div></div> <!> <div class="text-right"><div class="text-muted-foreground text-sm">Annual</div> <div class="text-xl font-bold"> </div></div></div></div> <!></div>',1),br=f('<!> <div class="flex items-center justify-between gap-1 border-b px-4 py-2"><h2 class="text-lg font-semibold">Plan Management</h2> <div class="space-y-4"><div class="flex gap-2"><!></div></div></div> <!> <div class="grid grid-cols-12"><!></div>',1);function ta(xe,g){Be(g,!1);const s=me();let y=me([]),b=me(!0),m=me(null),S=me(!1),J=me(!1),w=me(""),O=me("");async function _(){try{v(b,!0),v(m,null);try{const i=await fetch("/api/admin/plans/initialize",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include"});i.ok||console.warn(`Failed to initialize plans: ${i.status}`)}catch(i){console.warn("Error initializing plans:",i)}const t=await fetch("/api/admin/plans",{credentials:"include"});let a;try{a=await t.text()}catch{a=""}if(!t.ok){let i="Failed to load plans";if(a)try{i=JSON.parse(a).error||i}catch{i=a}throw new Error(i)}try{v(y,JSON.parse(a))}catch(i){throw console.error("Error parsing plans JSON:",i),new Error("Invalid response format from server")}const r=["email_support","priority_support","dedicated_support"];e(y).forEach(i=>{i.features=i.features.filter(u=>!r.includes(u.featureId))}),e(y).length>0&&v(O,e(y)[0].id)}catch(t){console.error("Error loading plans:",t),v(m,t.message),v(y,[{id:"free",name:"Free",description:"Basic features for personal use",section:"pro",monthlyPrice:0,annualPrice:0,features:[]},{id:"casual",name:"Casual",description:"For occasional job seekers",section:"pro",monthlyPrice:999,annualPrice:9990,features:[]}]);const a=["email_support","priority_support","dedicated_support"];e(y).forEach(r=>{r.features=r.features.filter(i=>!a.includes(i.featureId))}),v(O,e(y)[0].id)}finally{v(b,!1)}}ft(()=>{_(),re()});let A=me([]),q=me({}),H=me([]);async function re(){try{const t=await fetch("/api/admin/features",{credentials:"include"});if(!t.ok)throw new Error("Failed to load features");const a=await t.json();v(A,a.features||[]),v(q,e(A).reduce((i,u)=>(i[u.category]||(i[u.category]=[]),i[u.category].push(u),i),{}));const r=Object.keys(e(q));r.length>0?v(H,[r[0]]):v(H,[]),console.log(`Loaded ${e(A).length} features in ${e(H).length} categories`)}catch(t){console.error("Error loading features:",t),v(q,{}),v(H,[]),I.error("Failed to load features",{description:"Please try refreshing the page or contact support if the issue persists.",duration:5e3})}}function ae(t){if(!e(s))return K.NotIncluded;const a=e(s).features.find(r=>r.featureId===t);return(a==null?void 0:a.accessLevel)||K.NotIncluded}function L(t,a){var u;if(!e(s))return;const r=e(s).features.find(p=>p.featureId===t);if(!r||r.accessLevel!==K.Limited)return;const i=(u=r.limits)==null?void 0:u.find(p=>p.limitId===a);return i==null?void 0:i.value}async function Y(t,a){if(!e(s))return;const r=e(s).features.findIndex(u=>u.featureId===t);let i;if(r>=0)if(Ee(s,e(s).features[r].accessLevel=a),a!==K.Limited)Ee(s,e(s).features[r].limits=void 0);else if(e(s).features[r].limits)i=e(s).features[r].limits;else{const u=e(A).find(p=>p.id===t);u!=null&&u.limits&&u.limits.length>0?(i=u.limits.map(p=>({limitId:p.id,value:p.defaultValue||10})),Ee(s,e(s).features[r].limits=i)):(i=[{limitId:"monthly_usage",value:10},{limitId:"max_items",value:5}],Ee(s,e(s).features[r].limits=i))}else{const u={featureId:t,accessLevel:a};if(a===K.Limited){const p=e(A).find(h=>h.id===t);p!=null&&p.limits&&p.limits.length>0?(i=p.limits.map(h=>({limitId:h.id,value:h.defaultValue||10})),u.limits=i):(i=[{limitId:"monthly_usage",value:10},{limitId:"max_items",value:5}],u.limits=i)}e(s).features.push(u)}v(s,{...e(s)});try{const u=await fetch("/api/admin/plans",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({action:"update_feature",planId:e(s).id,featureId:t,accessLevel:a,limits:i})});if(u.ok){const p=await u.json();I.success("Feature updated",{description:p.message||`Feature ${t} updated successfully`,duration:3e3})}else{const p=await u.text();console.error("Error updating feature:",p),I.error("Failed to update feature",{description:p||"An error occurred while updating the feature",duration:5e3}),await _()}}catch(u){console.error("Error updating feature:",u),I.error("Failed to update feature",{description:u.message||"An error occurred while updating the feature",duration:5e3}),await _()}}async function F(t,a,r){if(!e(s))return;const i=e(s).features.findIndex(u=>u.featureId===t);if(i>=0&&e(s).features[i].accessLevel===K.Limited){e(s).features[i].limits||Ee(s,e(s).features[i].limits=[]);const u=e(s).features[i].limits.findIndex(p=>p.limitId===a);u>=0?Ee(s,e(s).features[i].limits[u].value=r):e(s).features[i].limits.push({limitId:a,value:r}),v(s,{...e(s)});try{const p=await fetch("/api/admin/plans",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({action:"update_feature",planId:e(s).id,featureId:t,accessLevel:K.Limited,limits:e(s).features[i].limits})});if(p.ok){const h=await p.json();I.success("Limit updated",{description:h.message||`Limit ${a} updated successfully`,duration:3e3})}else{const h=await p.text();console.error("Error updating feature limit:",h),I.error("Failed to update limit",{description:h||"An error occurred while updating the limit",duration:5e3}),await _()}}catch(p){console.error("Error updating feature limit:",p),I.error("Failed to update limit",{description:p.message||"An error occurred while updating the limit",duration:5e3}),await _()}}}async function k(){try{const t=await fetch("/api/admin/plans",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({plans:e(y)})});let a;try{a=await t.text()}catch{a=""}if(t.ok)try{const r=JSON.parse(a);r.success?I.success("Changes saved successfully!",{description:"All plan changes have been saved to the database",duration:3e3}):I.error("Failed to save plans",{description:r.error||"An error occurred while saving plans",duration:5e3})}catch{I.success("Changes saved successfully!",{description:"All plan changes have been saved to the database",duration:3e3})}else{let r="Failed to save plans";if(a)try{r=JSON.parse(a).error||r}catch{r=a}throw new Error(r)}}catch(t){console.error("Error saving plans:",t),I.error("Failed to save plans",{description:t.message||"An unexpected error occurred",duration:5e3})}}async function B(){try{v(b,!0);const t=await fetch("/api/admin/plans");if(!t.ok)throw new Error("Failed to load plans");v(y,await t.json());const a=["email_support","priority_support","dedicated_support"];e(y).forEach(r=>{r.features=r.features.filter(i=>!a.includes(i.featureId))}),v(s,e(y).find(r=>r.id===e(O)))}catch(t){console.error("Error resetting plans:",t),v(y,[{id:"free",name:"Free",description:"Basic features for personal use",section:"pro",monthlyPrice:0,annualPrice:0,features:[]},{id:"casual",name:"Casual",description:"For occasional job seekers",section:"pro",monthlyPrice:999,annualPrice:9990,features:[]}]),v(s,e(y).find(a=>a.id===e(O)))}finally{v(b,!1)}}function z(t){return(t/100).toFixed(2)}function _e(){confirm("Are you sure you want to save these changes? This will update the subscription plans for all users.")&&k()}async function N(){if(e(s))try{v(S,!0),v(w,"");const t=await fetch("/api/admin/plans/sync-stripe",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({planId:e(s).id})});let a;try{a=await t.text()}catch{a=""}if(t.ok){let r;try{r=JSON.parse(a),r.success&&r.plan?(Ee(s,e(s).stripePriceMonthlyId=r.plan.stripePriceMonthlyId),Ee(s,e(s).stripePriceYearlyId=r.plan.stripePriceYearlyId),v(s,{...e(s)}),v(w,r.message||"Plan synced with Stripe successfully"),I.success("Plan synced with Stripe",{description:e(w),duration:3e3})):(v(w,r.error||"Failed to sync plan with Stripe"),I.error("Failed to sync plan with Stripe",{description:e(w),duration:5e3}))}catch{v(w,"Plan synced with Stripe successfully"),I.success("Plan synced with Stripe",{description:e(w),duration:3e3})}}else{let r="Failed to sync plan with Stripe";if(a)try{r=JSON.parse(a).error||r}catch{r=a}v(w,r),alert(`Error: ${e(w)} (Status: ${t.status})`)}}catch(t){console.error("Error syncing plan with Stripe:",t),v(w,t.message),I.error("Failed to sync plan with Stripe",{description:t.message||"An unexpected error occurred",duration:5e3})}finally{v(S,!1)}}async function j(){try{v(S,!0),v(w,"");const t=await fetch("/api/admin/plans/sync-stripe",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include"});let a;try{a=await t.text()}catch{a=""}if(t.ok){let r;try{r=JSON.parse(a)}catch{r={message:"Plans synced with Stripe successfully"}}await _(),v(w,r.message||"Plans synced with Stripe successfully"),I.success("Plans synced with Stripe",{description:e(w),duration:3e3})}else{let r="Failed to sync plans with Stripe";if(a)try{r=JSON.parse(a).error||r}catch{r=a}v(w,r),alert(`Error: ${e(w)} (Status: ${t.status})`)}}catch(t){console.error("Error syncing plans with Stripe:",t),v(w,t.message),I.error("Failed to sync plans with Stripe",{description:t.message||"An unexpected error occurred",duration:5e3})}finally{v(S,!1)}}async function M(){try{if(!confirm("This will sync features with plans in the database. Continue?"))return;v(J,!0),I.loading("Syncing features with plans...");const t=await fetch("/api/admin/features",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({action:"sync_features",manual:!0})});if(console.log("Sync features response status:",t.status),!t.ok){console.error("Sync features response not OK:",t.statusText),I.dismiss(),I.error("Failed to sync features",{description:`Error: ${t.status} ${t.statusText}`,duration:5e3});return}const a=await t.json();console.log("Sync features result:",a),a.success?(I.dismiss(),I.success("Features synced successfully",{description:a.message||"All features have been synced across plans",duration:3e3}),await _()):(I.dismiss(),I.error("Failed to sync features",{description:a.error||"An error occurred while syncing features",duration:5e3}))}catch(t){console.error("Error syncing features:",t),I.dismiss(),I.error("Failed to sync features",{description:t.message||"An unexpected error occurred",duration:5e3})}finally{v(J,!1)}}async function ce(){try{v(b,!0),v(m,null);const t=await fetch("/api/admin/plans/load-from-stripe",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include"});let a;try{a=await t.text()}catch{a=""}if(t.ok){let r;try{r=JSON.parse(a)}catch{r={message:"Successfully loaded plans from Stripe"}}await _(),I.success("Plans loaded from Stripe",{description:r.message||`Successfully loaded ${r.count} plans from Stripe`,duration:3e3})}else{let r="Failed to load plans from Stripe";if(a)try{r=JSON.parse(a).error||r}catch{r=a}v(m,r),alert(`Error: ${e(m)} (Status: ${t.status})`)}}catch(t){console.error("Error loading plans from Stripe:",t),alert(`Error loading plans from Stripe: ${t.message}`)}finally{v(b,!1)}}He(()=>(e(y),e(O)),()=>{v(s,e(y).find(t=>t.id===e(O)))}),rt(),We();var Z=br(),G=T(Z);xt(G,{title:"Plan Management"});var ge=n(G,2),Q=n(l(ge),2),oe=l(Q),ee=l(oe);at(ee,{children:(t,a)=>{var r=pr(),i=T(r);st(i,{children:(p,h)=>{Ge(p,{variant:"outline",children:(he,be)=>{C();var ve=sr(),c=n(T(ve));nt(c,{class:"ml-2 h-4 w-4"}),d(he,ve)},$$slots:{default:!0}})},$$slots:{default:!0}});var u=n(i,2);it(u,{align:"end",children:(p,h)=>{var he=vr(),be=T(he);Ae(be,{onclick:()=>window.location.href="/dashboard/settings/admin/plans/view",children:(D,$e)=>{C();var R=V("View Plans");d(D,R)},$$slots:{default:!0}});var ve=n(be,2);Ae(ve,{onclick:()=>window.location.href="/dashboard/settings/admin/plans/edit",children:(D,$e)=>{C();var R=V("Edit Plans");d(D,R)},$$slots:{default:!0}});var c=n(ve,2);Ae(c,{onclick:B,children:(D,$e)=>{C();var R=V("Reset Changes");d(D,R)},$$slots:{default:!0}});var E=n(c,2);ut(E,{class:"border-border my-2 border-b"});var x=n(E,2);const Je=X(()=>e(b)?void 0:ce);Ae(x,{get onclick(){return e(Je)},children:(D,$e)=>{var R=nr(),fe=T(R);Et(fe,{class:"mr-2 h-4 w-4"});var we=n(fe,2);{var Se=te=>{var de=ir();d(te,de)};ie(we,te=>{e(b)&&te(Se)})}d(D,R)},$$slots:{default:!0}});var pe=n(x,2);const Ie=X(()=>e(S)?void 0:j);Ae(pe,{get onclick(){return e(Ie)},children:(D,$e)=>{var R=lr(),fe=T(R);const we=X(()=>`mr-2 h-4 w-4 ${e(S)?"animate-spin":""}`);tt(fe,{get class(){return e(we)}});var Se=n(fe,2);{var te=de=>{var ke=or();d(de,ke)};ie(Se,de=>{e(S)&&de(te)})}d(D,R)},$$slots:{default:!0}});var Fe=n(pe,2);const Ye=X(()=>e(J)?void 0:M);Ae(Fe,{get onclick(){return e(Ye)},children:(D,$e)=>{var R=cr(),fe=T(R);const we=X(()=>`mr-2 h-4 w-4 ${e(J)?"animate-spin":""}`);tt(fe,{get class(){return e(we)}});var Se=n(fe,2);{var te=de=>{var ke=dr();d(de,ke)};ie(Se,de=>{e(J)&&de(te)})}d(D,R)},$$slots:{default:!0}});var Le=n(Fe,2);ut(Le,{});var Te=n(Le,2);Ae(Te,{onclick:_e,children:(D,$e)=>{var R=ur(),fe=T(R);Mt(fe,{class:"mr-2 h-4 w-4"}),C(),d(D,R)},$$slots:{default:!0}}),d(p,he)},$$slots:{default:!0}}),d(t,r)},$$slots:{default:!0}}),o(oe),o(Q),o(ge);var le=n(ge,2);{var Pe=t=>{var a=fr();d(t,a)},P=(t,a)=>{{var r=i=>{var u=mr(),p=l(u),h=l(p);o(p),C(2),o(u),ne(()=>U(h,`Error loading plans: ${e(m)??""}`)),d(i,u)};ie(t,i=>{e(m)&&i(r)},a)}};ie(le,t=>{e(b)?t(Pe):t(P,!1)})}var ue=n(le,2),se=l(ue);{var ye=t=>{var a=yr(),r=T(a);je(r,5,()=>e(y),Re,(Te,D)=>{const $e=X(()=>e(O)===e(D).id?"background-primary/80":"");Ge(Te,{variant:"outline",get class(){return`w-full justify-start ${e($e)??""}`},onclick:()=>v(O,e(D).id),children:(R,fe)=>{C();var we=gr(),Se=T(we),te=n(Se);{var de=ke=>{bt(ke,{variant:"secondary",class:"bg-primary/10 text-primary ml-2",children:(lt,Qe)=>{C();var qe=V("Popular");d(lt,qe)},$$slots:{default:!0}})};ie(te,ke=>{e(D).popular&&ke(de)})}ne(()=>U(Se,`${e(D).name??""} `)),d(R,we)},$$slots:{default:!0}})}),o(r);var i=n(r,2),u=l(i),p=l(u),h=l(p),he=l(h);o(h);var be=n(h,2),ve=l(be,!0);o(be),o(p);var c=n(p,2),E=l(c),x=n(l(E),2),Je=l(x);o(x),o(E);var pe=n(E,2);Ot(pe,{class:"text-muted-foreground h-4 w-4"});var Ie=n(pe,2),Fe=n(l(Ie),2),Ye=l(Fe);o(Fe),o(Ie),o(c),o(u);var Le=n(u,2);_t(Le,{value:"features",class:"w-full",children:(Te,D)=>{var $e=_r(),R=T($e);ht(R,{class:"border-border border-b p-0",children:(te,de)=>{yt(te,{class:"flex flex-row gap-2 divide-x",children:(ke,lt)=>{var Qe=hr(),qe=T(Qe);Xe(qe,{value:"features",class:"flex-1 border-none",children:(ze,ct)=>{C();var Ke=V("Features");d(ze,Ke)},$$slots:{default:!0}});var dt=n(qe,2);Xe(dt,{value:"details",class:"flex-1 border-none",children:(ze,ct)=>{C();var Ke=V("Plan Details");d(ze,Ke)},$$slots:{default:!0}});var pt=n(dt,2);Xe(pt,{value:"preview",class:"flex-1 border-none",children:(ze,ct)=>{C();var Ke=V("Preview");d(ze,Ke)},$$slots:{default:!0}}),d(ke,Qe)},$$slots:{default:!0}})},$$slots:{default:!0}});var fe=n(R,2);Ze(fe,{value:"features",children:(te,de)=>{qt(te,{get featuresByCategory(){return e(q)},get expandedCategories(){return e(H)},getFeatureAccessLevel:ae,getFeatureLimitValue:L,updateFeatureAccessLevel:Y,updateFeatureLimitValue:F})},$$slots:{default:!0}});var we=n(fe,2);Ze(we,{value:"details",class:"h-full w-full p-4",children:(te,de)=>{Xt(te,{get selectedPlan(){return e(s)},syncPlanWithStripe:N,get syncingWithStripe(){return e(S)},get stripeMessage(){return e(w)}})},$$slots:{default:!0}});var Se=n(we,2);Ze(Se,{value:"preview",class:"p-4",children:(te,de)=>{ar(te,{get selectedPlan(){return e(s)},formatPrice:z,get features(){return e(A)}})},$$slots:{default:!0}}),d(Te,$e)},$$slots:{default:!0}}),o(i),ne((Te,D)=>{U(he,`${e(s).name??""} Plan`),U(ve,e(s).description),U(Je,`$${Te??""}`),U(Ye,`$${D??""}/mo`)},[()=>z(e(s).monthlyPrice),()=>z(e(s).annualPrice/12)],X),d(t,a)};ie(se,t=>{e(s)&&!e(b)&&t(ye)})}o(ue),d(xe,Z),Ve()}export{ta as component};
