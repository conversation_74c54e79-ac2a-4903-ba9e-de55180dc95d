import{c,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(e,o){const s=m(o,["children","$$slots","$$events","$$legacy"]),t=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"}],["circle",{cx:"12",cy:"12",r:"3"}]];f(e,d({name:"eye"},()=>s,{get iconNode(){return t},children:(a,$)=>{var r=c(),n=p(r);l(n,o,"default",{},null),i(a,r)},$$slots:{default:!0}}))}export{x as E};
