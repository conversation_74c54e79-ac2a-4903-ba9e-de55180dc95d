import{f as l,a as n}from"./BasJTneF.js";import{p as f,a as m,c as d,au as p,r as c}from"./CGmarHxI.js";import{c as v,s as h}from"./ncUU1dSD.js";import{e as u}from"./B-Xjo-Yt.js";import{b as _}from"./5V1tIHTN.js";import{p as b,r as g}from"./Btcx8l8F.js";var C=l("<div><!></div>");function w(e,a){f(a,!0);let t=b(a,"ref",15,null),o=g(a,["$$slots","$$events","$$legacy","ref","class","children"]);var r=C();u(r,s=>({"data-slot":"card-title",class:s,...o}),[()=>v("font-semibold leading-none",a.class)]);var i=d(r);h(i,()=>a.children??p),c(r),_(r,s=>t(s),()=>t()),n(e,r),m()}export{w as C};
