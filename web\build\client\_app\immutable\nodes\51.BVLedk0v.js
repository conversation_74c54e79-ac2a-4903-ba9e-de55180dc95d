import{f as n,a,c as _e,t as L}from"../chunks/BasJTneF.js";import{p as Rt,k as M,v as rt,i as Ot,f as y,a as Jt,s as o,c as s,d as i,g as e,n as f,r,t as oe,x as V}from"../chunks/CGmarHxI.js";import{s as B}from"../chunks/CIt1g2O9.js";import{i as X}from"../chunks/u21ee2wt.js";import{e as Oe,i as Je}from"../chunks/C3w0v0gR.js";import{c as w}from"../chunks/BvdI7LR8.js";import{g as Tt}from"../chunks/CmxjS0TN.js";import{d as st}from"../chunks/B-Xjo-Yt.js";import{C as Te}from"../chunks/DuGukytH.js";import{C as Ye}from"../chunks/Cdn-N1RY.js";import{C as Ve}from"../chunks/BkJY4La4.js";import{C as Qe}from"../chunks/GwmmX_iF.js";import{C as We}from"../chunks/D50jIuLr.js";import{B as W}from"../chunks/B1K98fMG.js";import{I as ht}from"../chunks/DMTMHyMa.js";import{L as qe}from"../chunks/BvvicRXk.js";import{S as Yt}from"../chunks/C6g8ubaU.js";import{F as xt}from"../chunks/YNp1uWxB.js";import"../chunks/CgXBgsce.js";import{t as le}from"../chunks/DjPYYl4Z.js";import{C as $t,B as bt,a as yt}from"../chunks/XnZcpgwi.js";import{D as wt}from"../chunks/BRdyUBC_.js";import{L as ot}from"../chunks/BhzFx1Wy.js";import{C as Vt}from"../chunks/CKg8MWp_.js";import{R as it}from"../chunks/qwsZpUIl.js";import{S as Qt}from"../chunks/yW0TxTga.js";import{D as Pt}from"../chunks/tr-scC-m.js";var Wt=n("<!> Initialize Feature Data",1),qt=n('<div class="flex justify-center py-16"><!></div>'),Gt=n("<!> <!>",1),Ht=n("<!> Initialize Feature Data",1),Kt=n("<!> Check Again",1),Xt=n(`<div class="flex flex-col items-center justify-center py-8 text-center"><!> <h3 class="mb-2 text-xl font-semibold">No Feature Usage Data</h3> <p class="text-muted-foreground mb-6 max-w-md">Feature usage tables have not been created yet. You can initialize feature data to start
          tracking usage.</p> <div class="flex flex-col gap-2 sm:flex-row"><!> <!></div></div>`),Zt=n("<!> <!>",1),ea=n("<!> <!>",1),ta=n("<option> </option>"),aa=n("<option> </option>"),ra=n("<!> Apply Filters",1),sa=n('<div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-4"><div class="space-y-2"><!> <div class="relative"><select id="feature" class="border-input bg-background w-full appearance-none rounded-md border px-3 py-2 pr-8 text-sm"><option>All Features</option><!></select> <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2"><svg class="h-4 w-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4 4 4-4"></path></svg></div></div></div> <div class="space-y-2"><!> <div class="relative"><select id="limit" class="border-input bg-background w-full appearance-none rounded-md border px-3 py-2 pr-8 text-sm"><option>All Limits</option><!></select> <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2"><svg class="h-4 w-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4 4 4-4"></path></svg></div></div></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div></div> <div class="mt-4 flex justify-end space-x-2"><!> <!></div>',1),oa=n("<!> <!>",1),ia=(lt,Ge)=>Ge(),la=n('<!> <span class="sr-only">Refresh</span>',1),na=n('<div class="flex items-center justify-between"><div><!> <!></div> <div class="flex items-center space-x-2"><div class="relative w-[140px]"><select class="border-input bg-background w-full appearance-none rounded-md border px-3 py-2 pr-8 text-sm"><option>By Feature</option><option>By Limit</option></select> <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2"><svg class="h-4 w-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4 4 4-4"></path></svg></div></div> <!></div></div>'),da=n('<div class="flex justify-center py-8"><!></div>'),va=n('<div class="bg-destructive/10 text-destructive rounded-md p-4 text-sm"><p> </p></div>'),ua=n('<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No usage data available.</p></div>'),ca=n('<div class="flex h-[300px] items-center justify-center"><div class="text-muted-foreground text-sm">No data available</div></div>'),pa=n('<div class="flex h-[300px] items-center justify-center"><div class="text-muted-foreground text-sm">No data available</div></div>'),ma=n('<div class="grid gap-6 md:grid-cols-2"><div class="rounded-md border p-4"><h3 class="mb-4 text-lg font-medium"> </h3> <!></div> <div class="rounded-md border p-4"><h3 class="mb-4 text-lg font-medium"> </h3> <!></div></div>'),fa=n("<!> <!>",1),_a=n("<!> Export CSV",1),ga=n("<!> Export JSON",1),ha=n('<!> <span class="sr-only">Refresh</span>',1),xa=n('<div class="flex items-center justify-between"><div><!> <!></div> <div class="flex flex-wrap items-center gap-2"><!> <!> <!></div></div>'),$a=n('<div class="flex justify-center py-8"><!></div>'),ba=n('<div class="bg-destructive/10 text-destructive rounded-md p-4 text-sm"><p> </p></div>'),ya=n('<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No usage data available.</p></div>'),wa=n('<tr class="hover:bg-muted/50 border-b"><td class="px-4 py-2"><div class="flex flex-col"><span class="font-medium"> </span> <span class="text-muted-foreground text-xs"> </span> <span class="text-muted-foreground text-xs"> </span></div></td><td class="px-4 py-2"> </td><td class="px-4 py-2"> </td><td class="px-4 py-2 text-right font-medium"> </td><td class="px-4 py-2"> </td><td class="px-4 py-2"> </td></tr>'),Pa=n('<div class="mt-4 flex flex-wrap items-center justify-between gap-4"><div class="text-muted-foreground text-sm"> </div> <div class="flex flex-wrap items-center gap-2"><!> <!> <!></div></div>'),ka=n('<div class="overflow-x-auto"><table class="w-full border-collapse"><thead><tr class="border-b"><th class="px-4 py-2 text-left">User</th><th class="px-4 py-2 text-left">Feature</th><th class="px-4 py-2 text-left">Limit</th><th class="px-4 py-2 text-right">Used</th><th class="px-4 py-2 text-left">Period</th><th class="px-4 py-2 text-left">Last Updated</th></tr></thead><tbody></tbody></table></div> <!>',1),Ia=n("<!> <!>",1),Sa=n('<div class="space-y-8"><!> <!> <!></div>'),Fa=n('<!> <div class="border-border flex flex-col gap-1 border-b p-4"><div class="flex items-center justify-between"><div class="flex flex-col"><h2 class="text-lg font-semibold">Feature Usage Admin</h2> <p class="text-foreground/70">Monitor and analyze feature usage across all users.</p></div> <!></div></div> <!>',1);function ar(lt,Ge){Rt(Ge,!0);let He=M(rt([])),ze=M(rt([])),ne=M(!1),de=M(!1),ve=M(null),ue=M(null),Ae=M(!0),Ke=M(!1);new Date().toISOString(),new Date().toISOString(),new Date().toISOString(),new Date().toISOString(),new Date().toISOString();let ce=M(""),Se=M(""),Fe=M(""),Ne=M(""),Xe=M(""),q=M(1),Ee=50,Ce=M("feature"),N=M(rt({page:1,limit:50,total:0,totalPages:0,hasMore:!1})),kt=V(()=>xt.find(t=>t.id===e(ce))),It=V(()=>{var t;return((t=e(kt))==null?void 0:t.limits)||[]});Ot(()=>{Ze()});async function Ze(){i(Ke,!0),i(ve,null),i(ue,null);try{const t=await fetch("/api/admin/feature-usage/check");if(!t.ok)throw new Error("Failed to check feature tables");const m=await t.json();if(i(Ae,m.tablesExist,!0),e(Ae))await Promise.all([ge(),Le()]);else{const C="Feature usage tables do not exist yet. Start using features to generate usage data.";i(ve,C),i(ue,C),i(ne,!1),i(de,!1)}}catch(t){console.error("Error checking feature tables:",t);const m=t.message||"Failed to check feature tables";i(ve,m,!0),i(ue,m,!0),i(ne,!1),i(de,!1)}finally{i(Ke,!1)}}function nt(){const t=new URLSearchParams;return e(ce)&&t.append("featureId",e(ce)),e(Se)&&t.append("limitId",e(Se)),e(Fe)&&t.append("period",e(Fe)),e(Ne)&&t.append("userId",e(Ne)),e(Xe)&&t.append("planId",e(Xe)),t.append("page",e(q).toString()),t.append("limit",Ee.toString()),t}async function ge(){i(ne,!0),i(ve,null);try{const t=nt(),m=await fetch(`/api/admin/feature-usage?${t.toString()}`);if(!m.ok)throw new Error("Failed to fetch usage data");const C=await m.json();i(He,C,!0),i(N,{page:e(q),limit:Ee,total:C.length,totalPages:Math.ceil(C.length/Ee),hasMore:C.length>Ee},!0)}catch(t){console.error("Error fetching usage data:",t),i(ve,t.message||"Failed to fetch usage data",!0)}finally{i(ne,!1)}}async function Le(){i(de,!0),i(ue,null);try{const t=new URLSearchParams;t.append("groupBy",e(Ce));const m=await fetch(`/api/admin/feature-usage/summary?${t.toString()}`);if(!m.ok)throw new Error("Failed to fetch summary data");const C=await m.json();i(ze,C,!0)}catch(t){console.error("Error fetching summary data:",t),i(ue,t.message||"Failed to fetch summary data",!0)}finally{i(de,!1)}}function dt(){i(q,1),ge(),Le()}function St(){i(ce,""),i(Se,""),i(Fe,""),i(Ne,""),i(Xe,""),i(q,1),dt()}function vt(t="csv"){if(!e(Ae)){le.error("Feature usage tables do not exist yet. Cannot export data.");return}const m=nt();m.append("format",t),window.open(`/api/admin/feature-usage/export?${m.toString()}`,"_blank")}function Ft(t){return new Date(t).toLocaleString()}function Nt(t){if(!t)return"N/A";const[m,C]=t.split("-");return C?new Date(parseInt(m),parseInt(C)-1,1).toLocaleDateString("en-US",{month:"long",year:"numeric"}):m}function Ct(t){return t.firstName||t.lastName?`${t.firstName||""} ${t.lastName||""}`.trim():t.email}function Ut(t){i(q,t,!0),ge()}function Dt(){e(q)<e(N).totalPages&&(i(q,e(q)+1),ge())}function jt(){e(q)>1&&(i(q,e(q)-1),ge())}const he={totalUsed:{label:"Total Usage",color:"var(--chart-1)"},userCount:{label:"User Count",color:"var(--chart-2)"}},ut=V(()=>()=>e(ze).map(t=>({name:t.name,totalUsed:t.totalUsed}))),ct=V(()=>()=>e(ze).map(t=>({name:t.name,userCount:t.userCount})));async function pt(){try{le.loading("Initializing feature data...");const m=await(await fetch("/api/admin/initialize-features",{method:"POST"})).json();m.success?(le.dismiss(),le.success("Feature data initialized successfully!"),console.log("Feature initialization results:",m.results),await Ze()):(le.dismiss(),le.error(`Failed to initialize feature data: ${m.error}`))}catch(t){console.error("Error initializing feature data:",t),le.dismiss(),le.error(`Error initializing feature data: ${t.message}`)}}var mt=Fa(),ft=y(mt);Yt(ft,{title:"Feature Usage Admin"});var et=o(ft,2),_t=s(et),zt=o(s(_t),2);W(zt,{variant:"outline",size:"sm",onclick:pt,children:(t,m)=>{var C=Wt(),Me=y(C);wt(Me,{class:"mr-2 h-4 w-4"}),f(),a(t,C)},$$slots:{default:!0}}),r(_t),r(et);var At=o(et,2);{var Et=t=>{var m=qt(),C=s(m);ot(C,{class:"text-primary h-12 w-12 animate-spin"}),r(m),a(t,m)},Lt=(t,m)=>{{var C=xe=>{var $e=_e(),Be=y($e);w(Be,()=>Te,(Re,tt)=>{tt(Re,{children:(pe,be)=>{var ie=Zt(),Ue=y(ie);w(Ue,()=>Qe,(Q,te)=>{te(Q,{children:(R,Y)=>{var U=Gt(),G=y(U);w(G,()=>We,(g,S)=>{S(g,{children:(k,u)=>{f();var l=L("Feature Usage Not Available");a(k,l)},$$slots:{default:!0}})});var b=o(G,2);w(b,()=>Ve,(g,S)=>{S(g,{children:(k,u)=>{f();var l=L("Feature usage tracking is not set up yet.");a(k,l)},$$slots:{default:!0}})}),a(R,U)},$$slots:{default:!0}})});var Z=o(Ue,2);w(Z,()=>Ye,(Q,te)=>{te(Q,{children:(R,Y)=>{var U=Xt(),G=s(U);Vt(G,{class:"text-muted-foreground mb-4 h-12 w-12"});var b=o(G,6),g=s(b);W(g,{variant:"default",onclick:pt,children:(k,u)=>{var l=Ht(),h=y(l);wt(h,{class:"mr-2 h-4 w-4"}),f(),a(k,l)},$$slots:{default:!0}});var S=o(g,2);W(S,{variant:"outline",onclick:Ze,children:(k,u)=>{var l=Kt(),h=y(l);it(h,{class:"mr-2 h-4 w-4"}),f(),a(k,l)},$$slots:{default:!0}}),r(b),r(U),a(R,U)},$$slots:{default:!0}})}),a(pe,ie)},$$slots:{default:!0}})}),a(xe,$e)},Me=xe=>{var $e=Sa(),Be=s($e);w(Be,()=>Te,(pe,be)=>{be(pe,{children:(ie,Ue)=>{var Z=oa(),Q=y(Z);w(Q,()=>Qe,(R,Y)=>{Y(R,{children:(U,G)=>{var b=ea(),g=y(b);w(g,()=>We,(k,u)=>{u(k,{children:(l,h)=>{f();var O=L("Filters");a(l,O)},$$slots:{default:!0}})});var S=o(g,2);w(S,()=>Ve,(k,u)=>{u(k,{children:(l,h)=>{f();var O=L("Filter feature usage data by various criteria.");a(l,O)},$$slots:{default:!0}})}),a(U,b)},$$slots:{default:!0}})});var te=o(Q,2);w(te,()=>Ye,(R,Y)=>{Y(R,{children:(U,G)=>{var b=sa(),g=y(b),S=s(g),k=s(S);qe(k,{for:"feature",children:(v,I)=>{f();var $=L("Feature");a(v,$)},$$slots:{default:!0}});var u=o(k,2),l=s(u),h=s(l);h.value=h.__value="";var O=o(h);Oe(O,17,()=>xt,Je,(v,I)=>{var $=ta(),_={},F=s($,!0);r($),oe(()=>{_!==(_=e(I).id)&&($.value=($.__value=e(I).id)??""),B(F,e(I).name)}),a(v,$)}),r(l),f(2),r(u),r(S);var c=o(S,2),P=s(c);qe(P,{for:"limit",children:(v,I)=>{f();var $=L("Limit");a(v,$)},$$slots:{default:!0}});var d=o(P,2),x=s(d),p=s(x);p.value=p.__value="";var A=o(p);Oe(A,17,()=>e(It),Je,(v,I,$,_)=>{var F=aa(),H={},E=s(F,!0);r(F),oe(()=>{H!==(H=e(I).id)&&(F.value=(F.__value=e(I).id)??""),B(E,e(I).name)}),a(v,F)}),r(x),f(2),r(d),r(c);var j=o(c,2),ae=s(j);qe(ae,{for:"period",children:(v,I)=>{f();var $=L("Period");a(v,$)},$$slots:{default:!0}});var ye=o(ae,2);ht(ye,{id:"period",type:"month",placeholder:"YYYY-MM",get value(){return e(Fe)},set value(v){i(Fe,v,!0)}}),r(j);var we=o(j,2),Pe=s(we);qe(Pe,{for:"userId",children:(v,I)=>{f();var $=L("User ID");a(v,$)},$$slots:{default:!0}});var re=o(Pe,2);ht(re,{id:"userId",placeholder:"User ID",get value(){return e(Ne)},set value(v){i(Ne,v,!0)}}),r(we),r(g);var D=o(g,2),J=s(D);W(J,{variant:"outline",onclick:St,children:(v,I)=>{f();var $=L("Reset");a(v,$)},$$slots:{default:!0}});var se=o(J,2);W(se,{onclick:dt,children:(v,I)=>{var $=ra(),_=y($);Qt(_,{class:"mr-2 h-4 w-4"}),f(),a(v,$)},$$slots:{default:!0}}),r(D),oe(()=>x.disabled=!e(ce)),st(l,()=>e(ce),v=>i(ce,v)),st(x,()=>e(Se),v=>i(Se,v)),a(U,b)},$$slots:{default:!0}})}),a(ie,Z)},$$slots:{default:!0}})});var Re=o(Be,2);w(Re,()=>Te,(pe,be)=>{be(pe,{children:(ie,Ue)=>{var Z=fa(),Q=y(Z);w(Q,()=>Qe,(R,Y)=>{Y(R,{children:(U,G)=>{var b=na(),g=s(b),S=s(g);w(S,()=>We,(d,x)=>{x(d,{children:(p,A)=>{f();var j=L("Usage Summary");a(p,j)},$$slots:{default:!0}})});var k=o(S,2);w(k,()=>Ve,(d,x)=>{x(d,{children:(p,A)=>{f();var j=L("Overview of feature usage across all users.");a(p,j)},$$slots:{default:!0}})}),r(g);var u=o(g,2),l=s(u),h=s(l);h.__change=[ia,Le];var O=s(h);O.value=O.__value="feature";var c=o(O);c.value=c.__value="limit",r(h),f(2),r(l);var P=o(l,2);W(P,{variant:"outline",size:"sm",onclick:Le,get disabled(){return e(de)},children:(d,x)=>{var p=la(),A=y(p);const j=V(()=>e(de)?"h-4 w-4 animate-spin":"h-4 w-4");it(A,{get class(){return e(j)}}),f(2),a(d,p)},$$slots:{default:!0}}),r(u),r(b),st(h,()=>e(Ce),d=>i(Ce,d)),a(U,b)},$$slots:{default:!0}})});var te=o(Q,2);w(te,()=>Ye,(R,Y)=>{Y(R,{children:(U,G)=>{var b=_e(),g=y(b);{var S=u=>{var l=da(),h=s(l);ot(h,{class:"text-primary h-8 w-8 animate-spin"}),r(l),a(u,l)},k=(u,l)=>{{var h=c=>{var P=va(),d=s(P),x=s(d);r(d),r(P),oe(()=>B(x,`Error loading summary data: ${e(ue)??""}`)),a(c,P)},O=(c,P)=>{{var d=p=>{var A=ua();a(p,A)},x=p=>{var A=ma(),j=s(A),ae=s(j),ye=s(ae);r(ae);var we=o(ae,2);{var Pe=_=>{var F=ca();a(_,F)},re=_=>{var F=_e(),H=y(F);w(H,()=>$t,(E,z)=>{z(E,{get config(){return he},class:"h-[300px] w-full",children:(T,me)=>{const ke=V(()=>e(ut)()),ee=V(()=>[{key:"totalUsed",label:he.totalUsed.label,color:he.totalUsed.color}]);bt(T,{get data(){return e(ke)},x:"name",axis:"x",get series(){return e(ee)},props:{xAxis:{format:K=>K.slice(0,10)}},tooltip:K=>{var fe=_e(),Ie=y(fe);w(Ie,()=>yt,(De,je)=>{je(De,{})}),a(K,fe)},$$slots:{tooltip:!0}})},$$slots:{default:!0}})}),a(_,F)};X(we,_=>{e(ut)().length===0?_(Pe):_(re,!1)})}r(j);var D=o(j,2),J=s(D),se=s(J);r(J);var v=o(J,2);{var I=_=>{var F=pa();a(_,F)},$=_=>{var F=_e(),H=y(F);w(H,()=>$t,(E,z)=>{z(E,{get config(){return he},class:"h-[300px] w-full",children:(T,me)=>{const ke=V(()=>e(ct)()),ee=V(()=>[{key:"userCount",label:he.userCount.label,color:he.userCount.color}]);bt(T,{get data(){return e(ke)},x:"name",axis:"x",get series(){return e(ee)},props:{xAxis:{format:K=>K.slice(0,10)}},tooltip:K=>{var fe=_e(),Ie=y(fe);w(Ie,()=>yt,(De,je)=>{je(De,{})}),a(K,fe)},$$slots:{tooltip:!0}})},$$slots:{default:!0}})}),a(_,F)};X(v,_=>{e(ct)().length===0?_(I):_($,!1)})}r(D),r(A),oe(()=>{B(ye,`Total Usage by ${e(Ce)==="feature"?"Feature":"Limit"}`),B(se,`User Count by ${e(Ce)==="feature"?"Feature":"Limit"}`)}),a(p,A)};X(c,p=>{e(ze).length===0?p(d):p(x,!1)},P)}};X(u,c=>{e(ue)?c(h):c(O,!1)},l)}};X(g,u=>{e(de)?u(S):u(k,!1)})}a(U,b)},$$slots:{default:!0}})}),a(ie,Z)},$$slots:{default:!0}})});var tt=o(Re,2);w(tt,()=>Te,(pe,be)=>{be(pe,{children:(ie,Ue)=>{var Z=Ia(),Q=y(Z);w(Q,()=>Qe,(R,Y)=>{Y(R,{children:(U,G)=>{var b=xa(),g=s(b),S=s(g);w(S,()=>We,(c,P)=>{P(c,{children:(d,x)=>{f();var p=L("Usage Data");a(d,p)},$$slots:{default:!0}})});var k=o(S,2);w(k,()=>Ve,(c,P)=>{P(c,{children:(d,x)=>{f();var p=L("Detailed feature usage data for all users.");a(d,p)},$$slots:{default:!0}})}),r(g);var u=o(g,2),l=s(u);W(l,{variant:"outline",size:"sm",onclick:()=>vt("csv"),children:(c,P)=>{var d=_a(),x=y(d);Pt(x,{class:"mr-2 h-4 w-4"}),f(),a(c,d)},$$slots:{default:!0}});var h=o(l,2);W(h,{variant:"outline",size:"sm",onclick:()=>vt("json"),children:(c,P)=>{var d=ga(),x=y(d);Pt(x,{class:"mr-2 h-4 w-4"}),f(),a(c,d)},$$slots:{default:!0}});var O=o(h,2);W(O,{variant:"outline",size:"sm",onclick:ge,get disabled(){return e(ne)},children:(c,P)=>{var d=ha(),x=y(d);const p=V(()=>e(ne)?"h-4 w-4 animate-spin":"h-4 w-4");it(x,{get class(){return e(p)}}),f(2),a(c,d)},$$slots:{default:!0}}),r(u),r(b),a(U,b)},$$slots:{default:!0}})});var te=o(Q,2);w(te,()=>Ye,(R,Y)=>{Y(R,{children:(U,G)=>{var b=_e(),g=y(b);{var S=u=>{var l=$a(),h=s(l);ot(h,{class:"text-primary h-8 w-8 animate-spin"}),r(l),a(u,l)},k=(u,l)=>{{var h=c=>{var P=ba(),d=s(P),x=s(d);r(d),r(P),oe(()=>B(x,`Error loading usage data: ${e(ve)??""}`)),a(c,P)},O=(c,P)=>{{var d=p=>{var A=ya();a(p,A)},x=p=>{var A=ka(),j=y(A),ae=s(j),ye=o(s(ae));Oe(ye,21,()=>e(He),Je,(re,D)=>{var J=wa(),se=s(J),v=s(se),I=s(v),$=s(I,!0);r(I);var _=o(I,2),F=s(_,!0);r(_);var H=o(_,2),E=s(H);r(H),r(v),r(se);var z=o(se),T=s(z,!0);r(z);var me=o(z),ke=s(me,!0);r(me);var ee=o(me),at=s(ee,!0);r(ee);var K=o(ee),fe=s(K,!0);r(K);var Ie=o(K),De=s(Ie,!0);r(Ie),r(J),oe((je,Mt,Bt)=>{var gt;B($,je),B(F,e(D).user.email),B(E,`Plan: ${((gt=e(D).user.plan)==null?void 0:gt.name)||"No Plan"}`),B(T,e(D).featureName),B(ke,e(D).limitName),B(at,e(D).used),B(fe,Mt),B(De,Bt)},[()=>Ct(e(D).user),()=>Nt(e(D).period),()=>Ft(e(D).updatedAt)]),a(re,J)}),r(ye),r(ae),r(j);var we=o(j,2);{var Pe=re=>{var D=Pa(),J=s(D),se=s(J);r(J);var v=o(J,2),I=s(v);const $=V(()=>e(N).page===1);W(I,{variant:"outline",size:"sm",onclick:jt,get disabled(){return e($)},children:(E,z)=>{f();var T=L("Previous");a(E,T)},$$slots:{default:!0}});var _=o(I,2);Oe(_,17,()=>Array.from({length:Math.min(5,e(N).totalPages)},(E,z)=>{const T=e(N).page<=3?z+1:e(N).page>=e(N).totalPages-2?e(N).totalPages-4+z:e(N).page-2+z;return T<=e(N).totalPages?T:null}).filter(Boolean),Je,(E,z)=>{const T=V(()=>e(z)===e(N).page?"default":"outline");W(E,{get variant(){return e(T)},size:"sm",onclick:()=>Ut(e(z)),children:(me,ke)=>{f();var ee=L();oe(()=>B(ee,e(z))),a(me,ee)},$$slots:{default:!0}})});var F=o(_,2);const H=V(()=>e(N).page===e(N).totalPages);W(F,{variant:"outline",size:"sm",onclick:Dt,get disabled(){return e(H)},children:(E,z)=>{f();var T=L("Next");a(E,T)},$$slots:{default:!0}}),r(v),r(D),oe(E=>B(se,`Showing ${(e(N).page-1)*e(N).limit+1} to ${E??""} of ${e(N).total??""} entries`),[()=>Math.min(e(N).page*e(N).limit,e(N).total)]),a(re,D)};X(we,re=>{e(N).totalPages>0&&re(Pe)})}a(p,A)};X(c,p=>{e(He).length===0?p(d):p(x,!1)},P)}};X(u,c=>{e(ve)?c(h):c(O,!1)},l)}};X(g,u=>{e(ne)?u(S):u(k,!1)})}a(U,b)},$$slots:{default:!0}})}),a(ie,Z)},$$slots:{default:!0}})}),r($e),a(xe,$e)};X(t,xe=>{e(Ae)?xe(Me,!1):xe(C)},m)}};X(At,t=>{e(Ke)?t(Et):t(Lt,!1)})}a(lt,mt),Jt()}Tt(["change"]);export{ar as component};
