import{c as n,a as m}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as p}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(r,o){const s=c(o,["children","$$slots","$$events","$$legacy"]),a=[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"}]];f(r,d({name:"mail"},()=>s,{get iconNode(){return a},children:(e,$)=>{var t=n(),i=l(t);p(i,o,"default",{},null),m(e,t)},$$slots:{default:!0}}))}export{y as M};
