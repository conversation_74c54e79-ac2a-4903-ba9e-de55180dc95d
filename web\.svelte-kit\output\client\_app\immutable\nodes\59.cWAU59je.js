import{f as v,a as r,c as Pe,t as z}from"../chunks/BasJTneF.js";import{p as Ge,aD as jt,g as n,m as Ve,d as m,a as qe,l as ft,h as De,aE as Pt,b as xt,f as g,s as u,e as Yt,n as K,c,r as l,t as B,x as Re,v as $e,i as St,k as ke}from"../chunks/CGmarHxI.js";import{s as O}from"../chunks/CIt1g2O9.js";import{i as w}from"../chunks/u21ee2wt.js";import{e as bt,i as Dt}from"../chunks/C3w0v0gR.js";import{c as _e}from"../chunks/BvdI7LR8.js";import{p as Ce,r as Bt}from"../chunks/Btcx8l8F.js";import{T as zt,R as Vt}from"../chunks/I7hvcB12.js";import{A as Ht,a as Gt,b as qt,c as Jt,d as Wt,e as Qt,f as Kt,R as Xt}from"../chunks/BnikQ10_.js";import{S as Zt}from"../chunks/C6g8ubaU.js";import{o as $t}from"../chunks/DrHxToS6.js";import"../chunks/CgXBgsce.js";import{c as er,o as Ct,a as tr}from"../chunks/nZgk9enP.js";import{e as rr}from"../chunks/CmxjS0TN.js";import{p as nr}from"../chunks/CWmzcjye.js";import{i as yt}from"../chunks/BIEMS98f.js";import{R as ar,P as ir,D as or,a as sr}from"../chunks/tdzGgazS.js";import{B as Oe}from"../chunks/B1K98fMG.js";import{t as Y}from"../chunks/DjPYYl4Z.js";import{b as dt}from"../chunks/VYoCKyli.js";import{b as lr}from"../chunks/5V1tIHTN.js";import{a as cr}from"../chunks/BosuxZz1.js";import{s as dr}from"../chunks/BBa424ah.js";import{D as ur,a as vr,b as fr,c as mr}from"../chunks/CKh8VGVX.js";import{L as Ye}from"../chunks/BhzFx1Wy.js";import{D as _r,a as gr,R as pr}from"../chunks/WD4kvFhR.js";import{s as Tt,a as hr}from"../chunks/B-Xjo-Yt.js";import{P as br}from"../chunks/CdkBcXOf.js";import{C as ct}from"../chunks/CxmsTEaf.js";import{P as yr}from"../chunks/DvO_AOqy.js";import{R as wr}from"../chunks/qwsZpUIl.js";import{S as Pr}from"../chunks/BoNCRmBc.js";import{C as Sr}from"../chunks/BwkAotBa.js";import{D as Er}from"../chunks/BgDjIxoO.js";import{D as xr}from"../chunks/Dz4exfp3.js";import{D as _t}from"../chunks/Z6UAQTuv.js";import{P as Dr,R as gt}from"../chunks/DOf_JqyE.js";import{C as Cr}from"../chunks/BAIxhb6t.js";import{S as Rt}from"../chunks/rNI1Perp.js";import{P as Tr}from"../chunks/DR5zc253.js";import{T as Rr}from"../chunks/C33xR25f.js";import{C as Ir}from"../chunks/BNEH2jqx.js";import{C as Or}from"../chunks/DZCYCPd3.js";import{D as Ar}from"../chunks/tr-scC-m.js";import{T as pt}from"../chunks/C88uNE8B.js";import{T as ht}from"../chunks/DmZyh-PW.js";var It="https://js.stripe.com/v3",Nr=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/;var Ur=function(){for(var e=document.querySelectorAll('script[src^="'.concat(It,'"]')),a=0;a<e.length;a++){var y=e[a];if(Nr.test(y.src))return y}return null},Et=function(e){var a="",y=document.createElement("script");y.src="".concat(It).concat(a);var d=document.head||document.body;if(!d)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return d.appendChild(y),y},Lr=function(e,a){!e||!e._registerWrapper||e._registerWrapper({name:"stripe-js",version:"4.6.0",startTime:a})},st=null,ut=null,vt=null,Mr=function(e){return function(){e(new Error("Failed to load Stripe.js"))}},kr=function(e,a){return function(){window.Stripe?e(window.Stripe):a(new Error("Stripe.js not available"))}},Fr=function(e){return st!==null?st:(st=new Promise(function(a,y){if(typeof window>"u"||typeof document>"u"){a(null);return}if(window.Stripe){a(window.Stripe);return}try{var d=Ur();if(!(d&&e)){if(!d)d=Et(e);else if(d&&vt!==null&&ut!==null){var _;d.removeEventListener("load",vt),d.removeEventListener("error",ut),(_=d.parentNode)===null||_===void 0||_.removeChild(d),d=Et(e)}}vt=kr(a,y),ut=Mr(y),d.addEventListener("load",vt),d.addEventListener("error",ut)}catch(C){y(C);return}}),st.catch(function(a){return st=null,Promise.reject(a)}))},jr=function(e,a,y){if(e===null)return null;var d=e.apply(void 0,a);return Lr(d,y),d},lt,Ot=!1,At=function(){return lt||(lt=Fr(null).catch(function(e){return lt=null,Promise.reject(e)}),lt)};Promise.resolve().then(function(){return At()}).catch(function(W){Ot||console.warn(W)});var Yr=function(){for(var e=arguments.length,a=new Array(e),y=0;y<e;y++)a[y]=arguments[y];Ot=!0;var d=Date.now();return At().then(function(_){return jr(_,a,d)})};const Br=Object.freeze(Object.defineProperty({__proto__:null,loadStripe:Yr},Symbol.toStringTag,{value:"Module"})),zr=cr(Br);var Vr=zr;function Hr(W,e,a,y,d={}){const _=a.create(e,d);return _.mount(W),_.on("change",C=>y("change",C)),_.on("ready",C=>y("ready",C)),_.on("focus",C=>y("focus",C)),_.on("blur",C=>y("blur",C)),_.on("escape",C=>y("escape",C)),_.on("click",C=>y("click",C)),_.on("loaderror",C=>y("loaderror",C)),_.on("loaderstart",C=>y("loaderstart",C)),_.on("networkschange",C=>y("networkschange",C)),_}const Nt=typeof window>"u";function Gr(W){if(!Nt)return W.registerAppInfo({name:"hirli.co",url:"https://hirli.co"})}const Ut="pk_live_51R8SnHL0zwkUpKXmx71gq4MzhNjxyIM56jJHQFisYQaG22f9cGu8Rcg7eI2z8pUe7ef1n3WRsC2zTo7TjwsaFQ9X00BuQFNCqO";Nt||console.log("Stripe key available:",!0,`(starts with ${Ut.substring(0,3)}...)`);const qr=Vr.loadStripe(Ut);var Jr=v("<div></div>");function Wr(W,e){Ge(e,!1);let a,y=Ve();const d=er(),{elements:_}=jt("stripe");let C=Ce(e,"options",8,void 0);Ct(()=>(a=Hr(n(y),"payment",_,d,C()),()=>a.destroy()));function V(){a.blur()}function ue(){a.clear()}function p(){a.destroy()}function te(){a.focus()}yt();var oe=Jr();return lr(oe,ge=>m(y,ge),()=>n(y)),r(W,oe),dt(e,"blur",V),dt(e,"clear",ue),dt(e,"destroy",p),dt(e,"focus",te),qe({blur:V,clear:ue,destroy:p,focus:te})}function Qr(W,e){Ge(e,!1);const a=Ve();let y=Ce(e,"stripe",8),d=Ce(e,"mode",8,void 0),_=Ce(e,"theme",8,"stripe"),C=Ce(e,"variables",24,()=>({})),V=Ce(e,"rules",24,()=>({})),ue=Ce(e,"labels",8,"above"),p=Ce(e,"loader",8,"auto"),te=Ce(e,"fonts",24,()=>[]),oe=Ce(e,"locale",8,"auto"),ge=Ce(e,"currency",8,void 0),xe=Ce(e,"amount",8,void 0),L=Ce(e,"clientSecret",8,void 0),P=Ce(e,"elements",12,null);ft(()=>(De(_()),De(C()),De(V()),De(ue())),()=>{m(a,{theme:_(),variables:C(),rules:V(),labels:ue()})}),ft(()=>(De(y()),De(P()),De(d()),De(ge()),De(xe()),n(a),De(L()),De(te()),De(p()),De(oe()),Pt),()=>{y()&&!P()&&(P(y().elements({mode:d(),currency:ge(),amount:xe(),appearance:n(a),clientSecret:L(),fonts:te(),loader:p(),locale:oe()})),Gr(y()),Pt("stripe",{stripe:y(),elements:P()}))}),ft(()=>(De(P()),n(a),De(oe())),()=>{P()&&P().update({appearance:n(a),locale:oe()})}),xt(),yt();var ae=Pe(),M=g(ae);{var S=R=>{var se=Pe(),le=g(se);dr(le,e,"default",{},null),r(R,se)};w(M,R=>{y()&&P()&&R(S)})}r(W,ae),qe()}var Kr=v("<!> <!>",1),Xr=v('<div class="flex justify-center"><!></div>'),Zr=v('<div class="bg-destructive/10 text-destructive rounded-md"><p> </p></div>'),$r=v('<div class="text-destructive mt-2 text-sm"><p> </p></div>'),en=v("<!> <!>",1),tn=v('<form id="payment-form"><!></form>'),rn=v('<div class="flex justify-center"><p class="text-muted-foreground">Loading payment form...</p></div>'),nn=v("<!> Processing...",1),an=v("<!> <!>",1),on=v('<!> <div class="p-4"><!></div> <!>',1),sn=v("<!> <!>",1);function ln(W,e){Ge(e,!1);let a=Ce(e,"open",12,!1),y=Ce(e,"onSuccess",8,()=>{}),d=Ve(!1),_=Ve(null),C=Ve(null),V=Ve(null),ue=Ve(!1),p=Ve(!1),te=Ve(null);Ct(async()=>{a()&&!n(ue)&&(m(ue,!0),oe())});async function oe(){try{if(m(V,await qr),!n(V)){console.error("Failed to initialize Stripe: No Stripe instance created"),m(_,"Failed to initialize Stripe. Please try again later.");return}console.log("Stripe initialized successfully",{type:typeof n(V),hasConfirmSetup:!!n(V).confirmSetup}),await ge(),n(V)&&n(C)&&console.log("Client secret obtained, ready to create Elements instance")}catch(L){console.error("Error initializing Stripe:",L),m(_,"Failed to initialize payment system. Please try again later.")}}async function ge(){try{if(m(d,!0),m(_,null),console.log("Creating setup intent..."),!(await fetch("/api/auth/check-session",{method:"GET",credentials:"include"})).ok)throw console.warn("User is not authenticated"),m(_,"You need to be signed in to add a payment method. Please sign in and try again."),new Error("Authentication required");const P=await fetch("/api/billing/create-setup-intent",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({paymentMethod:"card"})});if(!P.ok){let M="Unknown error";try{const S=await P.json();console.error("Setup intent error response:",S),M=S.error||"Failed to create setup intent"}catch{const R=await P.text();console.error("Setup intent error response:",R),M=R}throw P.status===401?(m(_,"You need to be signed in to add a payment method. Please sign in and try again."),new Error("Authentication required")):new Error(`Failed to create setup intent: ${M}`)}const ae=await P.json();console.log("Setup intent created successfully"),m(C,ae.clientSecret)}catch(L){console.error("Error creating setup intent:",L),n(_)||m(_,"Failed to initialize payment form. Please try again later.")}finally{m(d,!1)}}tr(()=>{m(ue,!1)});async function xe(L=null){if(L&&L.preventDefault(),!n(V)||!n(C)||!n(te)){m(_,"Payment processing is not available. Please try again later.");return}if(!n(p)){m(_,"Payment form is still loading. Please wait a moment and try again.");return}m(d,!0),m(_,null);try{console.log("Confirming card setup...");const{error:P,setupIntent:ae}=await n(V).confirmSetup({elements:n(te),confirmParams:{},redirect:"if_required"});if(console.log("Setup confirmation result:",{confirmError:P,setupIntent:ae}),P)console.error("Confirmation error:",P),m(_,P.message||"An error occurred while processing your payment method.");else{console.log("Payment method added successfully"),Y.success("Your payment method has been added successfully.");try{const M=await fetch("/api/billing/get-payment-methods",{method:"GET",credentials:"include"});if(M.ok){const S=await M.json();console.log("Updated payment methods:",S.paymentMethods),a(!1),y()(S.paymentMethods)}else console.error("Failed to fetch updated payment methods"),a(!1),y()()}catch(M){console.error("Error fetching updated payment methods:",M),a(!1),y()()}}}catch(P){console.error("Error submitting payment form:",P),P.message==="Stripe is not properly initialized"?m(_,"Payment system is not properly initialized. Please refresh the page and try again."):P.message&&P.message.includes("Stripe")?m(_,`Stripe error: ${P.message}`):m(_,"Failed to process your payment method. Please try again later.")}finally{m(d,!1)}}ft(()=>(De(a()),n(ue)),()=>{a()&&!n(ue)&&(m(_,null),m(d,!1),m(ue,!0),m(p,!1),oe())}),xt(),yt(),ar(W,{get open(){return a()},set open(L){a(L)},children:(L,P)=>{ir(L,{children:(ae,M)=>{var S=sn(),R=g(S);or(R,{});var se=u(R,2);sr(se,{class:"p-0 sm:max-w-[425px]",children:(le,X)=>{var ie=on(),ve=g(ie);ur(ve,{class:"border-border gap-1 border-b p-4",children:(i,f)=>{var U=Kr(),re=g(U);vr(re,{children:(N,ee)=>{K();var H=z("Add Payment Method");r(N,H)},$$slots:{default:!0}});var k=u(re,2);fr(k,{children:(N,ee)=>{K();var H=z("Add a new payment method to your account.");r(N,H)},$$slots:{default:!0}}),r(i,U)},$$slots:{default:!0}});var ce=u(ve,2),pe=c(ce);{var Te=i=>{var f=Xr(),U=c(f);Ye(U,{class:"text-primary h-8 w-8 animate-spin"}),l(f),r(i,f)},he=(i,f)=>{{var U=k=>{var N=Zr(),ee=c(N),H=c(ee,!0);l(ee),l(N),B(()=>O(H,n(_))),r(k,N)},re=(k,N)=>{{var ee=D=>{var I=tn(),Z=c(I);Qr(Z,{get stripe(){return n(V)},get clientSecret(){return n(C)},get elements(){return n(te)},set elements(G){m(te,G)},children:(G,F)=>{var ne=en(),be=g(ne);Wr(be,{$$events:{ready:()=>{m(p,!0),console.log("Payment element ready")}}});var Me=u(be,2);{var ye=we=>{var Se=$r(),Fe=c(Se),Ee=c(Fe,!0);l(Fe),l(Se),B(()=>O(Ee,n(_))),r(we,Se)};w(Me,we=>{n(_)&&we(ye)})}r(G,ne)},$$slots:{default:!0},$$legacy:!0}),l(I),rr("submit",I,nr(xe)),r(D,I)},H=D=>{var I=rn();r(D,I)};w(k,D=>{n(V)&&n(C)?D(ee):D(H,!1)},N)}};w(i,k=>{n(_)?k(U):k(re,!1)},f)}};w(pe,i=>{n(d)&&!n(C)?i(Te):i(he,!1)})}l(ce);var Le=u(ce,2);mr(Le,{class:"border-border gap-2 border-t p-2",children:(i,f)=>{var U=an(),re=g(U);Oe(re,{onclick:()=>a(!1),get disabled(){return n(d)},type:"button",children:(ee,H)=>{K();var D=z("Cancel");r(ee,D)},$$slots:{default:!0}});var k=u(re,2);const N=Yt(()=>n(d)||!n(C)||!n(V)||!n(te)||!n(p));Oe(k,{variant:"outline",onclick:xe,get disabled(){return n(N)},type:"button",children:(ee,H)=>{var D=Pe(),I=g(D);{var Z=F=>{var ne=nn(),be=g(ne);Ye(be,{class:"mr-2 h-4 w-4 animate-spin"}),K(),r(F,ne)},G=F=>{var ne=z("Add Card");r(F,ne)};w(I,F=>{n(d)?F(Z):F(G,!1)})}r(ee,D)},$$slots:{default:!0}}),r(i,U)},$$slots:{default:!0}}),r(le,ie)},$$slots:{default:!0}}),r(ae,S)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0}),qe()}async function cn(W){try{const e=await fetch("/api/billing/set-default-payment-method",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({paymentMethodId:W})});if(!e.ok)throw new Error("Failed to set default payment method");return(await e.json()).paymentMethods}catch(e){throw console.error("Error setting default payment method:",e),e}}async function dn(W){try{const e=await fetch("/api/billing/delete-payment-method",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({paymentMethodId:W})});if(!e.ok){const y=await e.json().catch(()=>null);throw new Error((y==null?void 0:y.message)||"Failed to delete payment method")}return(await e.json()).paymentMethods}catch(e){throw console.error("Error deleting payment method:",e),e}}var un=v("<span> </span>");function vn(W,e){Ge(e,!0);const a={FREE:{text:"Free",class:"bg-gray-100 text-gray-800"},INACTIVE:{text:"Inactive",class:"bg-gray-100 text-gray-800"},INCOMPLETE:{text:"Incomplete",class:"bg-orange-100 text-orange-800"},PAUSED:{text:"Paused",class:"bg-yellow-100 text-yellow-800"},CANCELING:{text:"Canceling",class:"bg-red-100 text-red-800"},PAST_DUE:{text:"Past Due",class:"bg-orange-100 text-orange-800"},TRIAL:{text:"Trial",class:"bg-blue-100 text-blue-800"},ACTIVE:{text:"Active",class:"bg-green-100 text-green-800"}};function y(p){var oe,ge;if(!p)return a.FREE;const te=p.status;return["canceled","unpaid","incomplete_expired"].includes(te)?a.INACTIVE:te==="incomplete"?a.INCOMPLETE:p.isPaused||((oe=p.metadata)==null?void 0:oe.pause_at_period_end)==="true"||p.pause_collection||te==="pausing_at_period_end"||te==="paused"||(p.cancel_at_period_end||p.cancelAtPeriodEnd)&&((ge=p.metadata)==null?void 0:ge.action_at_period_end)==="pause"?a.PAUSED:p.cancel_at_period_end||p.cancelAtPeriodEnd?a.CANCELING:te==="past_due"?a.PAST_DUE:te==="trialing"?a.TRIAL:a.ACTIVE}let d=Re(()=>y(e.subscription)),_=Re(()=>n(d).text),C=Re(()=>n(d).class);var V=un(),ue=c(V,!0);l(V),B(()=>{Tt(V,1,`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${n(C)??""}`),O(ue,n(_))}),r(W,V),qe()}var fn=v(" <!>",1),mn=v('<span class="font-medium">Cancelled</span> <!>',1),_n=v('<span class="font-medium">Paused</span>'),gn=v('<span class="font-medium">Ends on:</span>  <!>',1),pn=v('<span class="font-medium">Renews on:</span>  <!>',1),hn=v('<span class="text-sm text-red-500">(Subscription cancelled)</span>'),bn=v('<span class="text-sm text-red-500">(Cancels at end of period)</span>'),yn=v(" <!>",1),wn=v('<div class="mt-1 text-sm"><span class="text-muted-foreground">Current period:</span> <span><!></span></div>'),Pn=v('<div class="mt-1 text-sm"><span class="text-muted-foreground">Subscription ends:</span> <span class="text-yellow-600"><!></span></div>'),Sn=v('<div class="mt-1 text-sm"><span class="text-muted-foreground">Subscription cancelled:</span> <span class="text-red-600"><!></span></div>'),En=v('<div class="mt-1 text-sm"><span class="text-muted-foreground">Subscription will be cancelled on:</span> <span class="text-red-600"><!></span></div>'),xn=v('<div class="mt-2 text-sm"><span class="text-muted-foreground"><!></span></div> <div class="mt-2 flex items-center gap-2"><div class="flex items-center"><span class="text-sm"><!></span></div> <!></div> <!> <!> <!> <!>',1),Dn=v('<div class="mt-2 text-sm"><span class="text-muted-foreground"> </span></div>'),Cn=v("<!> Loading...",1),Tn=v("<!> Upgrade Plan",1),Rn=v("<!> Resuming...",1),In=v("<!> Resume Subscription",1),On=v("<!> Reactivating...",1),An=v("<!> Reactivate Subscription",1),Nn=v("<!> Manage Subscription <!>",1),Un=v("<!> <span>Change Plan</span>",1),Ln=v("<!> <span>Pause Subscription</span>",1),Mn=v("<!> <span>Cancel Subscription</span>",1),kn=v("<!> <!> <!> <!> <!>",1),Fn=v("<!> <!>",1),jn=v("<!> Loading...",1),Yn=v('<p class="text-muted-foreground text-sm">No features available for this plan.</p>'),Bn=v('<div class="border-border flex items-end justify-between border-b p-4"><div class=" flex flex-col gap-0"><h4 class="text-md font-normal">Current Plan</h4> <p class="text-muted-foreground text-sm">Your current subscription plan and usage.</p></div> <!></div> <div class="grid grid-cols-2 gap-6 p-4"><div class="flex w-2/3 flex-col"><h3 class="text-lg font-medium"> </h3> <p class="text-muted-foreground"><!></p> <!> <div class="mt-6 flex gap-2"><!></div></div> <div class="flex w-1/3 flex-col space-y-4"><div class="rounded-md border p-4"><h4 class="mb-4 font-medium">Plan Features</h4> <!></div></div></div>',1);function zn(W,e){Ge(e,!0);function a(){var i;return((i=e.user)==null?void 0:i.role)||"free"}function y(){var i;return!!((i=e.user)!=null&&i.stripeCustomerId)}function d(i){console.log("Formatting date:",i,typeof i),i||(console.warn("No date provided, using fallback date"),i=new Date(Date.now()+30*24*60*60*1e3));try{const f=new Date(i);return console.log("Parsed date:",f,"isValid:",!isNaN(f.getTime())),isNaN(f.getTime())?(console.warn("Invalid date:",i,"using fallback date"),new Date(Date.now()+30*24*60*60*1e3).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})):f.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch(f){return console.error("Error formatting date:",f,i,"using fallback date"),new Date(Date.now()+30*24*60*60*1e3).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}}function _(i){if(!i)return console.warn("No subscription provided, using fallback date"),new Date(Date.now()+30*24*60*60*1e3);console.log("Getting plan changes date from subscription:",{planChangesOnDate:i.planChangesOnDate,current_period_end:i.current_period_end,currentPeriodEnd:i.currentPeriodEnd,items:i.items});try{if(i.planChangesOnDate){if(i.planChangesOnDate instanceof Date)return i.planChangesOnDate;const f=new Date(i.planChangesOnDate);if(!isNaN(f.getTime()))return f}if(i.current_period_end){if(i.current_period_end instanceof Date)return i.current_period_end;if(typeof i.current_period_end=="number"){const f=new Date(i.current_period_end*1e3);if(!isNaN(f.getTime()))return f}else{const f=new Date(i.current_period_end);if(!isNaN(f.getTime()))return f}}if(i.currentPeriodEnd){if(i.currentPeriodEnd instanceof Date)return i.currentPeriodEnd;const f=new Date(i.currentPeriodEnd);if(!isNaN(f.getTime()))return f}if(i.items&&Array.isArray(i.items)&&i.items.length>0){const f=i.items[0];if(console.log("Checking subscription item for period end date:",f),f.current_period_end){const U=new Date(f.current_period_end*1e3);if(!isNaN(U.getTime()))return console.log("Found valid date in subscription item:",U),U}}console.warn("Could not find valid date in subscription, using fallback date",i)}catch(f){console.error("Error getting plan changes date:",f)}return new Date(Date.now()+30*24*60*60*1e3)}function C(i,f="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:f.toUpperCase()}).format(i/100)}var V=Bn(),ue=g(V),p=u(c(ue),2);vn(p,{get subscription(){return e.subscription}}),l(ue);var te=u(ue,2),oe=c(te),ge=c(oe),xe=c(ge);l(ge);var L=u(ge,2),P=c(L);{var ae=i=>{var f=fn(),U=g(f),re=u(U);{var k=N=>{var ee=z();B(()=>O(ee,`(billed every ${e.subscription.price.intervalCount??""}
          ${e.subscription.price.interval??""}s)`)),r(N,ee)};w(re,N=>{e.subscription.price.intervalCount>1&&N(k)})}B(N=>O(U,`${N??""}
        /${e.subscription.price.interval??""} `),[()=>C(e.subscription.price.unitAmount,e.subscription.price.currency)]),r(i,f)},M=i=>{var f=z();B(()=>{var U;return O(f,`$${(((U=e.currentPlan)==null?void 0:U.monthlyPrice)||0)/100}/month`)}),r(i,f)};w(P,i=>{var f,U;(U=(f=e.subscription)==null?void 0:f.price)!=null&&U.unitAmount?i(ae):i(M,!1)})}l(L);var S=u(L,2);{var R=i=>{var f=xn(),U=g(f),re=c(U),k=c(re);{var N=T=>{var q=z("Your subscription has been cancelled. You can upgrade to a new plan at any time.");r(T,q)},ee=(T,q)=>{{var me=t=>{var o=z(`Your subscription is paused. You can resume your subscription at any time before it
            ends.`);r(t,o)},s=(t,o)=>{{var b=x=>{var Q=z(`Your subscription will be canceled at the end of the current billing period. You can
            reactivate your subscription at any time before the period ends.`);r(x,Q)},j=x=>{var Q=z(`Your subscription is active and will automatically renew at the end of the billing
            period.`);r(x,Q)};w(t,x=>{e.subscription.cancelAtPeriodEnd||e.subscription.cancel_at_period_end?x(b):x(j,!1)},o)}};w(T,t=>{var o;e.subscription.isPaused||e.subscription.pause_collection||((o=e.subscription.metadata)==null?void 0:o.pause_at_period_end)==="true"||e.subscription.status==="paused"||e.subscription.status==="pausing_at_period_end"?t(me):t(s,!1)},q)}};w(k,T=>{e.subscription.status==="canceled"||e.subscription.status==="cancelled"?T(N):T(ee,!1)})}l(re),l(U);var H=u(U,2),D=c(H),I=c(D),Z=c(I);{var G=T=>{var q=mn(),me=u(g(q),2);{var s=t=>{var o=z();B(b=>O(o,` on ${b??""}`),[()=>d(e.subscription.canceled_at||e.subscription.canceledAt)]),r(t,o)};w(me,t=>{(e.subscription.canceled_at||e.subscription.canceledAt)&&t(s)})}r(T,q)},F=(T,q)=>{{var me=t=>{var o=_n();r(t,o)},s=(t,o)=>{{var b=x=>{var Q=gn();const J=Re(()=>{var h,$;return e.subscription.current_period_end instanceof Date?e.subscription.current_period_end:e.subscription.currentPeriodEnd instanceof Date?e.subscription.currentPeriodEnd:($=(h=e.subscription.items)==null?void 0:h[0])!=null&&$.current_period_end?new Date(e.subscription.items[0].current_period_end*1e3):null});var fe=u(g(Q),2);{var A=h=>{var $=z();B(de=>O($,` ${de??""}`),[()=>d(n(J))]),r(h,$)},E=h=>{var $=z();B(de=>O($,` ${de??""}`),[()=>d(new Date(Date.now()+2592e6))]),r(h,$)};w(fe,h=>{n(J)?h(A):h(E,!1)})}r(x,Q)},j=x=>{var Q=pn();const J=Re(()=>{var h,$;return e.subscription.current_period_end instanceof Date?e.subscription.current_period_end:e.subscription.currentPeriodEnd instanceof Date?e.subscription.currentPeriodEnd:($=(h=e.subscription.items)==null?void 0:h[0])!=null&&$.current_period_end?new Date(e.subscription.items[0].current_period_end*1e3):null});var fe=u(g(Q),2);{var A=h=>{var $=z();B(de=>O($,` ${de??""}`),[()=>d(n(J))]),r(h,$)},E=h=>{var $=z();B(de=>O($,` ${de??""}`),[()=>d(new Date(Date.now()+2592e6))]),r(h,$)};w(fe,h=>{n(J)?h(A):h(E,!1)})}r(x,Q)};w(t,x=>{e.subscription.cancelAtPeriodEnd||e.subscription.cancel_at_period_end?x(b):x(j,!1)},o)}};w(T,t=>{var o;e.subscription.isPaused||e.subscription.pause_collection||((o=e.subscription.metadata)==null?void 0:o.pause_at_period_end)==="true"||e.subscription.status==="paused"||e.subscription.status==="pausing_at_period_end"?t(me):t(s,!1)},q)}};w(Z,T=>{e.subscription.status==="canceled"||e.subscription.status==="cancelled"?T(G):T(F,!1)})}l(I),l(D);var ne=u(D,2);{var be=T=>{var q=hn();r(T,q)},Me=(T,q)=>{{var me=t=>{},s=(t,o)=>{{var b=j=>{var x=bn();r(j,x)};w(t,j=>{(e.subscription.cancelAtPeriodEnd||e.subscription.cancel_at_period_end)&&j(b)},o)}};w(T,t=>{var o;e.subscription.isPaused||e.subscription.pause_collection||((o=e.subscription.metadata)==null?void 0:o.pause_at_period_end)==="true"||e.subscription.status==="paused"||e.subscription.status==="pausing_at_period_end"?t(me):t(s,!1)},q)}};w(ne,T=>{e.subscription.status==="canceled"||e.subscription.status==="cancelled"?T(be):T(Me,!1)})}l(H);var ye=u(H,2);{var we=T=>{var q=wn(),me=u(c(q),2),s=c(me);{var t=o=>{var b=yn();const j=Re(()=>{var E,h;return e.subscription.current_period_start instanceof Date?e.subscription.current_period_start:e.subscription.currentPeriodStart instanceof Date?e.subscription.currentPeriodStart:(h=(E=e.subscription.items)==null?void 0:E[0])!=null&&h.current_period_start?new Date(e.subscription.items[0].current_period_start*1e3):null}),x=Re(()=>{var E,h;return e.subscription.current_period_end instanceof Date?e.subscription.current_period_end:e.subscription.currentPeriodEnd instanceof Date?e.subscription.currentPeriodEnd:(h=(E=e.subscription.items)==null?void 0:E[0])!=null&&h.current_period_end?new Date(e.subscription.items[0].current_period_end*1e3):null});var Q=g(b),J=u(Q);{var fe=E=>{var h=z();B(($,de)=>O(h,`${$??""} - ${de??""}`),[()=>d(n(j)),()=>d(n(x))]),r(E,h)},A=E=>{var h=z();B(($,de)=>O(h,`${$??""} - ${de??""}`),[()=>d(new Date),()=>d(new Date(Date.now()+2592e6))]),r(E,h)};w(J,E=>{n(j)&&n(x)?E(fe):E(A,!1)})}B(E=>O(Q,`${E??""}  `),[()=>console.log("Subscription period data in component:",{start_snake:e.subscription.current_period_start,start_camel:e.subscription.currentPeriodStart,end_snake:e.subscription.current_period_end,end_camel:e.subscription.currentPeriodEnd,items:e.subscription.items,subscription:e.subscription})]),r(o,b)};w(s,o=>{e.subscription&&o(t)})}l(me),l(q),r(T,q)};w(ye,T=>{!e.subscription.isPaused&&!e.subscription.pause_collection&&e.subscription.status!=="canceled"&&e.subscription.status!=="cancelled"&&!e.subscription.cancelAtPeriodEnd&&!e.subscription.cancel_at_period_end&&T(we)})}var Se=u(ye,2);{var Fe=T=>{var q=Pn(),me=u(c(q),2),s=c(me);{var t=b=>{var j=z();B(x=>O(j,x),[()=>d(e.subscription.planChangesOnDate)]),r(b,j)},o=b=>{var j=Pe();const x=Re(()=>{var A,E;return e.subscription.current_period_end instanceof Date?e.subscription.current_period_end:e.subscription.currentPeriodEnd instanceof Date?e.subscription.currentPeriodEnd:(E=(A=e.subscription.items)==null?void 0:A[0])!=null&&E.current_period_end?new Date(e.subscription.items[0].current_period_end*1e3):null});var Q=g(j);{var J=A=>{var E=z();B(h=>O(E,h),[()=>d(n(x))]),r(A,E)},fe=A=>{var E=z();B(h=>O(E,h),[()=>d(_(e.subscription))]),r(A,E)};w(Q,A=>{n(x)?A(J):A(fe,!1)})}r(b,j)};w(s,b=>{e.subscription.planChangesOnDate instanceof Date?b(t):b(o,!1)})}l(me),l(q),r(T,q)};w(Se,T=>{(e.subscription.isPaused||e.subscription.pause_collection)&&T(Fe)})}var Ee=u(Se,2);{var Ae=T=>{var q=Sn(),me=u(c(q),2),s=c(me);{var t=b=>{var j=Pe();const x=Re(()=>e.subscription.canceled_at instanceof Date?e.subscription.canceled_at:e.subscription.canceledAt instanceof Date?e.subscription.canceledAt:typeof e.subscription.canceled_at=="number"?new Date(e.subscription.canceled_at*1e3):typeof e.subscription.canceledAt=="number"?new Date(e.subscription.canceledAt*1e3):null);var Q=g(j);{var J=A=>{var E=z();B(h=>O(E,h),[()=>d(n(x))]),r(A,E)},fe=A=>{var E=z();B(h=>O(E,h),[()=>d(new Date)]),r(A,E)};w(Q,A=>{n(x)?A(J):A(fe,!1)})}r(b,j)},o=b=>{var j=z();B(x=>O(j,x),[()=>d(new Date)]),r(b,j)};w(s,b=>{e.subscription.canceled_at||e.subscription.canceledAt?b(t):b(o,!1)})}l(me),l(q),r(T,q)};w(Ee,T=>{(e.subscription.status==="canceled"||e.subscription.status==="cancelled")&&T(Ae)})}var je=u(Ee,2);{var He=T=>{var q=En(),me=u(c(q),2),s=c(me);{var t=o=>{var b=Pe(),j=g(b);{var x=J=>{var fe=z();B(A=>O(fe,A),[()=>d(e.subscription.current_period_end)]),r(J,fe)},Q=(J,fe)=>{{var A=h=>{var $=z();B(de=>O($,de),[()=>d(e.subscription.currentPeriodEnd)]),r(h,$)},E=(h,$)=>{{var de=Ue=>{var Ne=z();B(Ie=>O(Ne,Ie),[()=>d(new Date(e.subscription.items[0].current_period_end*1e3))]),r(Ue,Ne)},et=Ue=>{var Ne=z();B(Ie=>O(Ne,Ie),[()=>d(_(e.subscription))]),r(Ue,Ne)};w(h,Ue=>{var Ne,Ie;(Ie=(Ne=e.subscription.items)==null?void 0:Ne[0])!=null&&Ie.current_period_end?Ue(de):Ue(et,!1)},$)}};w(J,h=>{e.subscription.currentPeriodEnd instanceof Date?h(A):h(E,!1)},fe)}};w(j,J=>{e.subscription.current_period_end instanceof Date?J(x):J(Q,!1)})}r(o,b)};w(s,o=>{e.subscription&&o(t)})}l(me),l(q),r(T,q)};w(je,T=>{(e.subscription.cancelAtPeriodEnd||e.subscription.cancel_at_period_end)&&T(He)})}r(i,f)},se=(i,f)=>{{var U=re=>{var k=Dn(),N=c(k),ee=c(N);l(N),l(k),B(()=>{var H;return O(ee,`Your ${((H=e.currentPlan)==null?void 0:H.name)??""} plan is active. You can manage your subscription settings below.`)}),r(re,k)};w(i,re=>{a()!=="free"&&re(U)},f)}};w(S,i=>{e.subscription?i(R):i(se,!1)})}var le=u(S,2),X=c(le);{var ie=i=>{var f=Pe(),U=g(f);{var re=N=>{Oe(N,{variant:"outline",get disabled(){return e.isLoading},get onclick(){return e.handleOpenPricingModal},children:(ee,H)=>{var D=Pe(),I=g(D);{var Z=F=>{var ne=Cn(),be=g(ne);Ye(be,{class:"mr-2 h-4 w-4 animate-spin"}),K(),r(F,ne)},G=F=>{var ne=Tn(),be=g(ne);ct(be,{class:"mr-2 h-4 w-4"}),K(),r(F,ne)};w(I,F=>{e.isLoading?F(Z):F(G,!1)})}r(ee,D)},$$slots:{default:!0}})},k=(N,ee)=>{{var H=I=>{Oe(I,{variant:"outline",get disabled(){return e.isSubscriptionActionLoading},get onclick(){return e.handleResumeSubscription},children:(Z,G)=>{var F=Pe(),ne=g(F);{var be=ye=>{var we=Rn(),Se=g(we);Ye(Se,{class:"mr-2 h-4 w-4 animate-spin"}),K(),r(ye,we)},Me=ye=>{var we=In(),Se=g(we);yr(Se,{class:"mr-2 h-4 w-4"}),K(),r(ye,we)};w(ne,ye=>{e.isSubscriptionActionLoading?ye(be):ye(Me,!1)})}r(Z,F)},$$slots:{default:!0}})},D=(I,Z)=>{{var G=ne=>{Oe(ne,{variant:"outline",get disabled(){return e.isSubscriptionActionLoading},get onclick(){return e.handleResumeSubscription},children:(be,Me)=>{var ye=Pe(),we=g(ye);{var Se=Ee=>{var Ae=On(),je=g(Ae);Ye(je,{class:"mr-2 h-4 w-4 animate-spin"}),K(),r(Ee,Ae)},Fe=Ee=>{var Ae=An(),je=g(Ae);wr(je,{class:"mr-2 h-4 w-4"}),K(),r(Ee,Ae)};w(we,Ee=>{e.isSubscriptionActionLoading?Ee(Se):Ee(Fe,!1)})}r(be,ye)},$$slots:{default:!0}})},F=ne=>{var be=Pe(),Me=g(be);_e(Me,()=>pr,(ye,we)=>{we(ye,{children:(Se,Fe)=>{var Ee=Fn(),Ae=g(Ee);_e(Ae,()=>_r,(He,T)=>{T(He,{children:(q,me)=>{Oe(q,{variant:"outline",get disabled(){return e.isSubscriptionActionLoading},children:(s,t)=>{var o=Nn(),b=g(o);Pr(b,{class:"mr-2 h-4 w-4"});var j=u(b,2);Sr(j,{class:"ml-2 h-4 w-4"}),r(s,o)},$$slots:{default:!0}})},$$slots:{default:!0}})});var je=u(Ae,2);_e(je,()=>gr,(He,T)=>{T(He,{children:(q,me)=>{var s=kn(),t=g(s);_e(t,()=>Er,(Q,J)=>{J(Q,{children:(fe,A)=>{K();var E=z("Subscription Options");r(fe,E)},$$slots:{default:!0}})});var o=u(t,2);_e(o,()=>xr,(Q,J)=>{J(Q,{})});var b=u(o,2);_e(b,()=>_t,(Q,J)=>{J(Q,{get onclick(){return e.handleOpenPricingModal},children:(fe,A)=>{var E=Un(),h=g(E);ct(h,{class:"mr-2 h-4 w-4"}),K(2),r(fe,E)},$$slots:{default:!0}})});var j=u(b,2);_e(j,()=>_t,(Q,J)=>{J(Q,{onclick:()=>e.setPauseDialogOpen(!0),children:(fe,A)=>{var E=Ln(),h=g(E);Dr(h,{class:"mr-2 h-4 w-4"}),K(2),r(fe,E)},$$slots:{default:!0}})});var x=u(j,2);_e(x,()=>_t,(Q,J)=>{J(Q,{onclick:()=>e.setCancelDialogOpen(!0),children:(fe,A)=>{var E=Mn(),h=g(E);Cr(h,{class:"mr-2 h-4 w-4"}),K(2),r(fe,E)},$$slots:{default:!0}})}),r(q,s)},$$slots:{default:!0}})}),r(Se,Ee)},$$slots:{default:!0}})}),r(ne,be)};w(I,ne=>{e.subscription.cancelAtPeriodEnd||e.subscription.cancel_at_period_end?ne(G):ne(F,!1)},Z)}};w(N,I=>{var Z;e.subscription.isPaused||e.subscription.pause_collection||((Z=e.subscription.metadata)==null?void 0:Z.pause_at_period_end)==="true"||e.subscription.status==="paused"||e.subscription.status==="pausing_at_period_end"?I(H):I(D,!1)},ee)}};w(U,N=>{e.subscription.status==="canceled"||e.subscription.status==="cancelled"?N(re):N(k,!1)})}r(i,f)},ve=i=>{Oe(i,{variant:"outline",get disabled(){return e.isLoading},get onclick(){return e.handleOpenPricingModal},children:(f,U)=>{var re=Pe(),k=g(re);{var N=H=>{var D=jn(),I=g(D);Ye(I,{class:"mr-2 h-4 w-4 animate-spin"}),K(),r(H,D)},ee=H=>{var D=z();B(I=>O(D,I),[()=>y()?"Change Plan":"Upgrade"]),r(H,D)};w(k,H=>{e.isLoading?H(N):H(ee,!1)})}r(f,re)},$$slots:{default:!0}})};w(X,i=>{e.subscription?i(ie):i(ve,!1)})}l(le),l(oe);var ce=u(oe,2),pe=c(ce),Te=u(c(pe),2);{var he=i=>{br(i,{get plan(){return e.currentPlan}})},Le=i=>{var f=Yn();r(i,f)};w(Te,i=>{e.currentPlan?i(he):i(Le,!1)})}l(pe),l(ce),l(te),B(i=>O(xe,`${i??""} Plan`),[()=>{var i;return((i=e.currentPlan)==null?void 0:i.name)||a().charAt(0).toUpperCase()+a().slice(1)}]),r(W,V),qe()}var Vn=v("<!> Add Method",1),Hn=v('<span class="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">Default</span>'),Gn=v("<!> Setting...",1),qn=v("<!> Set Default",1),Jn=v("<!> Deleting...",1),Wn=v("<!> Remove",1),Qn=v('<div class="flex items-center justify-between rounded-lg border p-4"><div class="flex items-center gap-4"><div class="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full"><!></div> <div><p class="font-medium"> </p> <p class="text-muted-foreground text-sm"> </p></div></div> <div class="flex items-center gap-2"><!> <div class="relative"><!></div></div></div>'),Kn=v('<div class="space-y-4"></div>'),Xn=v("<!> ",1),Zn=v(`<div class="rounded-lg border border-dashed p-6 text-center"><div class="mb-4 flex justify-center"><div class="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full"><!></div></div> <h3 class="mb-2 text-lg font-medium">No Payment Methods</h3> <p class="text-muted-foreground">You haven't added any payment methods yet.</p> <div class="mt-6"><!></div></div>`),$n=v('<div><div class="border-border flex items-end justify-between border-b p-4"><div class="flex flex-col gap-0"><h4 class="text-md font-normal">Payment Methods</h4> <p class="text-muted-foreground text-sm">View and manage your payment methods.</p></div> <!></div></div> <div class="p-4"><!></div> <div class="flex justify-between p-6"><div class="flex items-center gap-2"><!> <p class="text-muted-foreground text-sm">Your payment information is secure and encrypted</p></div></div>',1);function ea(W,e){Ge(e,!0);var a=$n(),y=g(a),d=c(y),_=u(c(d),2);{var C=L=>{Oe(L,{variant:"outline",size:"sm",onclick:()=>e.setAddPaymentMethodModalOpen(!0),children:(P,ae)=>{var M=Vn(),S=g(M);Tr(S,{class:"mr-2 h-4 w-4"}),K(),r(P,M)},$$slots:{default:!0}})};w(_,L=>{e.paymentMethods&&e.paymentMethods.length>0&&L(C)})}l(d),l(y);var V=u(y,2),ue=c(V);{var p=L=>{var P=Kn();bt(P,21,()=>e.paymentMethods,ae=>ae.id,(ae,M)=>{var S=Qn(),R=c(S),se=c(R),le=c(se);ct(le,{class:"text-primary h-5 w-5"}),l(se);var X=u(se,2),ie=c(X),ve=c(ie);l(ie);var ce=u(ie,2),pe=c(ce);l(ce),l(X),l(R);var Te=u(R,2),he=c(Te);{var Le=k=>{var N=Hn();r(k,N)},i=k=>{Oe(k,{variant:"outline",size:"sm",get disabled(){return e.isPaymentMethodLoading},onclick:()=>e.setDefaultPaymentMethod(n(M).id),children:(N,ee)=>{var H=Pe(),D=g(H);{var I=G=>{var F=Gn(),ne=g(F);Ye(ne,{class:"mr-2 h-4 w-4 animate-spin"}),K(),r(G,F)},Z=G=>{var F=qn(),ne=g(F);Ir(ne,{class:"mr-2 h-4 w-4"}),K(),r(G,F)};w(D,G=>{e.isPaymentMethodLoading?G(I):G(Z,!1)})}r(N,H)},$$slots:{default:!0}})};w(he,k=>{n(M).isDefault?k(Le):k(i,!1)})}var f=u(he,2),U=c(f);const re=Re(()=>e.isDeletePaymentMethodLoading||n(M).isDefault||e.paymentMethods.length<=1);Oe(U,{variant:"ghost",size:"sm",get disabled(){return n(re)},onclick:()=>e.openDeleteDialog(n(M).id),children:(k,N)=>{var ee=Pe(),H=g(ee);{var D=Z=>{var G=Jn(),F=g(G);Ye(F,{class:"mr-2 h-4 w-4 animate-spin"}),K(),r(Z,G)},I=Z=>{var G=Wn(),F=g(G);Rr(F,{class:"mr-2 h-4 w-4"}),K(),r(Z,G)};w(H,Z=>{e.isDeletePaymentMethodLoading&&e.paymentMethodToDelete===n(M).id?Z(D):Z(I,!1)})}r(k,ee)},$$slots:{default:!0}}),l(f),l(Te),l(S),B(k=>{O(ve,`${k??""} ending in
                ${n(M).card.last4??""}`),O(pe,`Expires ${n(M).card.exp_month??""}/${n(M).card.exp_year??""}`),hr(f,"title",n(M).isDefault?"Cannot delete the default payment method":e.paymentMethods.length<=1?"Cannot delete your only payment method":"Remove this payment method")},[()=>n(M).card.brand.charAt(0).toUpperCase()+n(M).card.brand.slice(1)]),r(ae,S)}),l(P),r(L,P)},te=L=>{var P=Zn(),ae=c(P),M=c(ae),S=c(M);ct(S,{class:"text-primary h-6 w-6"}),l(M),l(ae);var R=u(ae,6),se=c(R);const le=Re(()=>{var ie;return!((ie=e.user)!=null&&ie.stripeCustomerId)}),X=Re(()=>{var ie;return(ie=e.user)!=null&&ie.stripeCustomerId?()=>e.setAddPaymentMethodModalOpen(!0):()=>e.handlePlanChange("casual")});Oe(se,{variant:"default",get disabled(){return n(le)},get onclick(){return n(X)},children:(ie,ve)=>{var ce=Xn(),pe=g(ce);ct(pe,{class:"mr-2 h-4 w-4"});var Te=u(pe);B(()=>{var he;return O(Te,` ${(he=e.user)!=null&&he.stripeCustomerId?"Add First Payment Method":"Subscribe to Add Payment Method"}`)}),r(ie,ce)},$$slots:{default:!0}}),l(R),l(P),r(L,P)};w(ue,L=>{e.paymentMethods&&e.paymentMethods.length>0?L(p):L(te,!1)})}l(V);var oe=u(V,2),ge=c(oe),xe=c(ge);Rt(xe,{class:"text-muted-foreground h-5 w-5"}),K(2),l(ge),l(oe),r(W,a),qe()}var ta=v("<!> Loading...",1),ra=v("<!> Manage Billing",1),na=v('<div class="mb-6 rounded-lg border border-dashed p-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100"><!></div> <div><h3 class="font-medium">Upcoming Invoice</h3> <p class="text-muted-foreground text-sm"> </p></div></div> <div class="text-right"><p class="font-medium"> </p> <p class="text-muted-foreground text-sm"> </p></div></div></div>'),aa=v("<span> </span>"),ia=v("<!> View",1),oa=v("<!> Download",1),sa=v('<div class="grid grid-cols-5 gap-4 border-b p-4 last:border-0"><div> </div> <div> </div> <div> </div> <div><!></div> <div class="flex justify-end gap-2"><!> <!></div></div>'),la=v('<div class="rounded-md border"><div class="grid grid-cols-5 gap-4 border-b px-4 py-2 text-sm font-normal"><div>Invoice</div> <div>Date</div> <div>Amount</div> <div>Status</div> <div class="text-right">Actions</div></div> <!></div>'),ca=v(`<div class="rounded-lg border border-dashed p-6 text-center"><div class="mb-4 flex justify-center"><div class="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full"><!></div></div> <h3 class="mb-2 text-lg font-medium">No Invoices</h3> <p class="text-muted-foreground">You don't have any invoices yet.</p> <p class="text-muted-foreground mt-2 text-sm">When you make a payment, your invoices will appear here.</p></div>`),da=v('<div class="border-border flex flex-row items-end justify-between border-b p-4"><div class="flex flex-col gap-0"><h4 class="text-md font-normal">Billing History</h4> <p class="text-muted-foreground text-sm">View and download your invoices.</p></div> <div class="flex gap-2"><!></div></div> <div class="p-4"><!> <!></div> <div class="flex justify-between p-6"><div class="flex items-center gap-2"><!> <p class="text-muted-foreground text-sm">Your billing information is secure and encrypted</p></div></div>',1);function ua(W,e){Ge(e,!0);function a(S){if(!S)return"N/A";try{const R=new Date(S);return isNaN(R.getTime())?(console.warn("Invalid date:",S),"N/A"):R.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch(R){return console.error("Error formatting date:",R,S),"N/A"}}function y(S){return S.status==="paid"?{text:"Paid",class:"bg-green-100 text-green-800"}:S.status==="open"?{text:"Unpaid",class:"bg-yellow-100 text-yellow-800"}:S.status==="draft"?{text:"Draft",class:"bg-gray-100 text-gray-800"}:S.status==="void"?{text:"Void",class:"bg-red-100 text-red-800"}:{text:S.status.charAt(0).toUpperCase()+S.status.slice(1),class:"bg-gray-100 text-gray-800"}}var d=da(),_=g(d),C=u(c(_),2),V=c(C);const ue=Re(()=>{var S;return e.isPortalLoading||!((S=e.user)!=null&&S.stripeCustomerId)});Oe(V,{variant:"outline",size:"sm",get disabled(){return n(ue)},get onclick(){return e.openCustomerPortal},children:(S,R)=>{var se=Pe(),le=g(se);{var X=ve=>{var ce=ta(),pe=g(ce);Ye(pe,{class:"mr-2 h-4 w-4 animate-spin"}),K(),r(ve,ce)},ie=ve=>{var ce=ra(),pe=g(ce);gt(pe,{class:"mr-2 h-4 w-4"}),K(),r(ve,ce)};w(le,ve=>{e.isPortalLoading?ve(X):ve(ie,!1)})}r(S,se)},$$slots:{default:!0}}),l(C),l(_);var p=u(_,2),te=c(p);{var oe=S=>{var R=na(),se=c(R),le=c(se),X=c(le),ie=c(X);Or(ie,{class:"h-5 w-5 text-blue-600"}),l(X);var ve=u(X,2),ce=u(c(ve),2),pe=c(ce);l(ce),l(ve),l(le);var Te=u(le,2),he=c(Te),Le=c(he,!0);l(he);var i=u(he,2),f=c(i);l(i),l(Te),l(se),l(R),B((U,re)=>{O(pe,`Next billing date: ${U??""}`),O(Le,re),O(f,`${e.upcomingInvoice.lines.data.length??""} item${e.upcomingInvoice.lines.data.length!==1?"s":""}`)},[()=>a(new Date(e.upcomingInvoice.period_end*1e3)),()=>new Intl.NumberFormat("en-US",{style:"currency",currency:e.upcomingInvoice.currency.toUpperCase()}).format(e.upcomingInvoice.amount_due/100)]),r(S,R)};w(te,S=>{e.upcomingInvoice&&S(oe)})}var ge=u(te,2);{var xe=S=>{var R=la(),se=u(c(R),2);bt(se,17,()=>e.invoices,Dt,(le,X)=>{var ie=sa(),ve=c(ie),ce=c(ve,!0);l(ve);var pe=u(ve,2),Te=c(pe,!0);l(pe);var he=u(pe,2),Le=c(he,!0);l(he);var i=u(he,2),f=c(i);{var U=D=>{var I=aa();const Z=Re(()=>y(n(X)));var G=c(I,!0);l(I),B(()=>{Tt(I,1,`rounded-full px-2.5 py-0.5 text-xs font-medium ${n(Z).class}`),O(G,n(Z).text)}),r(D,I)};w(f,D=>{n(X).status&&D(U)})}l(i);var re=u(i,2),k=c(re);{var N=D=>{Oe(D,{variant:"ghost",size:"sm",onclick:()=>window.open(n(X).hosted_invoice_url,"_blank"),children:(I,Z)=>{var G=ia(),F=g(G);gt(F,{class:"mr-2 h-4 w-4"}),K(),r(I,G)},$$slots:{default:!0}})};w(k,D=>{n(X).hosted_invoice_url&&D(N)})}var ee=u(k,2);{var H=D=>{Oe(D,{variant:"ghost",size:"sm",onclick:()=>window.open(n(X).invoice_pdf,"_blank"),children:(I,Z)=>{var G=oa(),F=g(G);Ar(F,{class:"mr-2 h-4 w-4"}),K(),r(I,G)},$$slots:{default:!0}})};w(ee,D=>{n(X).invoice_pdf&&D(H)})}l(re),l(ie),B((D,I)=>{O(ce,n(X).number||"Draft"),O(Te,D),O(Le,I)},[()=>a(new Date(n(X).created*1e3)),()=>new Intl.NumberFormat("en-US",{style:"currency",currency:n(X).currency.toUpperCase()}).format(n(X).amount_paid/100)]),r(le,ie)}),l(R),r(S,R)},L=S=>{var R=ca(),se=c(R),le=c(se),X=c(le);gt(X,{class:"text-primary h-6 w-6"}),l(le),l(se),K(6),l(R),r(S,R)};w(ge,S=>{e.invoices&&e.invoices.length>0?S(xe):S(L,!1)})}l(p);var P=u(p,2),ae=c(P),M=c(ae);Rt(M,{class:"text-muted-foreground h-5 w-5"}),K(2),l(ae),l(P),r(W,d),qe()}var va=v("<!> <!> <!>",1),fa=v("<!> <!> <!> <!>",1),ma=v("<!> <!>",1),_a=v("<!> ",1),ga=v("<!> <!>",1),pa=v("<!> <!>",1),ha=v('<!> <div><div class="flex flex-col justify-between p-6"><h2 class="text-lg font-semibold">Billing & Subscription</h2> <p class="text-muted-foreground">Manage your subscription plan, payment methods, and billing history.</p></div> <!></div> <!> <!>',1);function ui(W,e){var be,Me,ye,we,Se,Fe,Ee,Ae,je,He,T,q,me;Ge(e,!0);const a={SUCCESS:{SUBSCRIPTION_UPDATED:s=>({title:"Subscription Updated",description:`Your subscription has been successfully updated to the ${s||"new"} plan.`,duration:5e3}),SUBSCRIPTION_RESUMED:{title:"Subscription resumed successfully",duration:3e3},SUBSCRIPTION_CANCELED:s=>({title:s?"Your subscription will be canceled at the end of the billing period":"Your subscription has been canceled",duration:3e3}),SUBSCRIPTION_PAUSED:{title:"Your subscription will be paused at the end of the billing period",duration:3e3},PAYMENT_METHOD_ADDED:{title:"Payment method added successfully",duration:3e3},PAYMENT_METHOD_DELETED:{title:"Payment method deleted successfully",duration:3e3},PAYMENT_METHOD_DEFAULT:{title:"Default payment method updated successfully",duration:3e3}},ERROR:{NO_SUBSCRIPTION:{title:"No subscription found",description:"You need to have an active subscription to view billing history."},PORTAL_NOT_CONFIGURED:{title:"Stripe Portal not configured",description:"The Stripe Customer Portal has not been set up. Please configure it in the Stripe Dashboard.",action:{label:"Configure",onClick:()=>{const t=window.location.hostname!=="localhost"&&!window.location.hostname.includes("127.0.0.1")?"https://dashboard.stripe.com/settings/billing/portal":"https://dashboard.stripe.com/test/settings/billing/portal";window.open(t,"_blank")}}},STRIPE_CONFIG_ISSUE:{title:"Stripe configuration issue",description:"There is a configuration issue with Stripe. Please contact support."},INVALID_REQUEST:{title:"Invalid request to Stripe",description:"There was a problem with the request to Stripe. Please try again later."},AUTH_ERROR:{title:"Authentication error",description:"There was a problem authenticating with Stripe. Please contact support."},PORTAL_FAILED:{title:"Failed to open customer portal",description:"Failed to open customer portal. Please try again later."},DELETE_PAYMENT_METHOD:s=>({title:"Failed to delete payment method",description:s||"Please try again later."}),RESUME_SUBSCRIPTION:{title:"Failed to resume subscription",description:"Failed to resume subscription. Please try again later."},CANCEL_SUBSCRIPTION:{title:"Failed to cancel subscription",description:"Failed to cancel subscription. Please try again later."},PAUSE_SUBSCRIPTION:{title:"Failed to pause subscription",description:"Failed to pause subscription. Please try again later."},CANNOT_DELETE_ONLY_PAYMENT_METHOD:{title:"Cannot Delete Payment Method",description:"You cannot delete your only payment method. Please add another payment method first."},CANNOT_DELETE_DEFAULT_PAYMENT_METHOD:{title:"Cannot Delete Default Payment Method",description:"You cannot delete your default payment method. Please set another payment method as default first."}},INFO:{SUBSCRIPTION_ALREADY_ACTIVE:{title:"Your subscription is already active",duration:3e3},VIEW_INVOICES_DIRECTLY:{title:"You can view your invoices directly",description:"You can view and download your invoices directly from this page.",duration:5e3},NO_INVOICES:{title:"No invoices available",description:"You don't have any invoices yet. They will appear here after your first payment.",duration:5e3}}},y=Bt(e,["$$slots","$$events","$$legacy"]);try{console.log("Page component initialized with props:",JSON.stringify({hasProps:!!y,hasData:!!(e!=null&&e.data),hasUser:!!((be=e==null?void 0:e.data)!=null&&be.user),hasBilling:!!((Me=e==null?void 0:e.data)!=null&&Me.billing)})),e.data?console.log("Data from server structure:",{hasUser:!!e.data.user,hasBilling:!!e.data.billing,userKeys:e.data.user?Object.keys(e.data.user):[],billingKeys:e.data.billing?Object.keys(e.data.billing):[]}):(console.error("props.data is undefined or null"),Y.error("Error loading billing data",{description:"Please try refreshing the page or contact support if the issue persists."}))}catch(s){console.error("Error during page initialization check:",s)}let d=$e(((ye=e==null?void 0:e.data)==null?void 0:ye.user)||{}),_=$e(((Se=(we=e==null?void 0:e.data)==null?void 0:we.billing)==null?void 0:Se.currentPlan)||{name:"Free",id:"free",features:[]}),C=$e(((Ee=(Fe=e==null?void 0:e.data)==null?void 0:Fe.billing)==null?void 0:Ee.paymentMethods)||[]),V=$e(((je=(Ae=e==null?void 0:e.data)==null?void 0:Ae.billing)==null?void 0:je.invoices)||[]),ue=$e(((T=(He=e==null?void 0:e.data)==null?void 0:He.billing)==null?void 0:T.upcomingInvoice)||null),p=$e(((me=(q=e==null?void 0:e.data)==null?void 0:q.billing)==null?void 0:me.subscription)||null);try{console.log("Initialized state variables:",{hasUser:!!d,hasCurrentPlan:!!_,hasPaymentMethods:C.length>0,hasInvoices:V.length>0,hasUpcomingInvoice:!!ue,hasSubscription:!!p})}catch(s){console.error("Error logging initialized state variables:",s)}async function te(){try{return console.log("Reloading page to get fresh data..."),typeof window<"u"?setTimeout(()=>{window.location.href=window.location.pathname},500):console.warn("Cannot reload page: not in browser context"),!0}catch(s){return console.error("Error reloading page:",s),!1}}let oe=ke(!1),ge=ke(!1),xe=ke(!1),L=ke(!1),P=ke(!1),ae=ke(!1),M=ke(""),S=ke(!1),R=ke(!1),se=ke(!1),le=ke("subscription"),X=new URLSearchParams(""),ie=null,ve=null,ce=null,pe=null;St(()=>{typeof window<"u"&&(X=new URLSearchParams(window.location.search),ie=X.get("checkout"),ve=X.get("resumed"),ce=X.get("canceled"),pe=X.get("paused"),console.log("URL parameters:",{checkoutStatus:ie,resumedStatus:ve,canceledStatus:ce,pausedStatus:pe}))}),St(()=>{if(ie==="success"&&_){const s=a.SUCCESS.SUBSCRIPTION_UPDATED(_==null?void 0:_.name);if(Y.success(s.title,{description:s.description,duration:s.duration}),typeof window<"u"){const t=new URL(window.location.href);t.searchParams.delete("checkout"),window.history.replaceState({},"",t)}console.log("Showing subscription updated toast due to checkout success parameter")}if(ve==="true"){const s=a.SUCCESS.SUBSCRIPTION_RESUMED;if(Y.success(s.title,{duration:s.duration}),typeof window<"u"){const t=new URL(window.location.href);t.searchParams.delete("resumed"),window.history.replaceState({},"",t)}console.log("Showing subscription resumed toast due to resumed parameter")}if(ce==="true"){const s=a.SUCCESS.SUBSCRIPTION_CANCELED(!0);if(Y.success(s.title,{duration:s.duration}),typeof window<"u"){const t=new URL(window.location.href);t.searchParams.delete("canceled"),window.history.replaceState({},"",t)}console.log("Showing subscription canceled toast due to canceled parameter")}if(pe==="true"){const s=a.SUCCESS.SUBSCRIPTION_PAUSED;if(Y.success(s.title,{duration:s.duration}),typeof window<"u"){const t=new URL(window.location.href);t.searchParams.delete("paused"),window.history.replaceState({},"",t)}console.log("Showing subscription paused toast due to paused parameter")}});async function Te(){if(!n(ge)){if(!d.stripeCustomerId){const s=a.ERROR.NO_SUBSCRIPTION;Y.error(s.title,{description:s.description});return}m(ge,!0);try{console.log("Opening customer portal for customer ID:",d.stripeCustomerId);const s=await fetch("/api/billing/create-portal-session",{method:"POST",headers:{"Content-Type":"application/json"}});let t;try{t=await s.json()}catch(o){throw console.error("Failed to parse response as JSON:",o),new Error("Invalid response from server")}if(!s.ok){const o=(t==null?void 0:t.error)||"Failed to create portal session",b=(t==null?void 0:t.details)||"unknown_error",j=(t==null?void 0:t.code)||"unknown_code";if(console.error("Error opening customer portal:",{message:o,details:b,code:j}),o.includes("No configuration provided")||o.includes("default configuration has not been created")){console.log("Stripe Customer Portal not configured. Please set up the portal in the Stripe Dashboard.");const x=a.ERROR.PORTAL_NOT_CONFIGURED;Y.error(x.title,{description:x.description,action:x.action})}else if(b==="StripeInvalidRequestError"&&j==="parameter_unknown"){console.log("Stripe API version issue detected. Please update the Stripe configuration.");const x=a.ERROR.STRIPE_CONFIG_ISSUE;Y.error(x.title,{description:x.description})}else if(b==="invalid_request_error"){const x=a.ERROR.INVALID_REQUEST;Y.error(x.title,{description:x.description})}else if(b==="authentication_error"){const x=a.ERROR.AUTH_ERROR;Y.error(x.title,{description:x.description})}else{const x=a.ERROR.PORTAL_FAILED;Y.error(x.title,{description:o||x.description})}throw new Error(o)}if(t!=null&&t.url)typeof window<"u"?window.location.href=t.url:console.warn("Cannot redirect: not in browser context");else throw new Error("No portal URL returned from server")}catch(s){if(console.error("Error opening customer portal:",s),!s.message||s.message==="Failed to create portal session"){const t=a.ERROR.PORTAL_FAILED;Y.error(t.title,{description:t.description})}if(V&&V.length>0){const t=a.INFO.VIEW_INVOICES_DIRECTLY;Y.info(t.title,{description:t.description,duration:t.duration}),m(le,"invoices")}else{const t=a.INFO.NO_INVOICES;Y.info(t.title,{description:t.description,duration:t.duration})}}finally{m(ge,!1)}}}async function he(s,t="monthly"){if(!n(oe)){m(oe,!0);try{const o=await fetch("/api/billing/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:s,billingCycle:t})});if(!o.ok)throw new Error("Failed to create checkout session");const{url:b}=await o.json();typeof window<"u"?window.location.href=b:console.warn("Cannot redirect: not in browser context")}catch(o){console.error("Error creating checkout session:",o),Y.error("Failed to create checkout session. Please try again later.")}finally{m(oe,!1)}}}function Le(){$t({section:"pro",currentPlanId:(_==null?void 0:_.id)||null,onSelectPlan:he})}async function i(s){if(!n(xe)){m(xe,!0);try{await cn(s);const t=a.SUCCESS.PAYMENT_METHOD_DEFAULT;Y.success(t.title,{duration:t.duration}),te()}catch(t){console.error("Error setting default payment method:",t);const o=a.ERROR.INVALID_REQUEST;Y.error(o.title,{description:o.description})}finally{m(xe,!1)}}}function f(s=null){const t=a.SUCCESS.PAYMENT_METHOD_ADDED;Y.success(t.title,{duration:t.duration}),m(le,"payment"),te()}function U(s){if(n(L))return;if(C.length<=1){const o=a.ERROR.CANNOT_DELETE_ONLY_PAYMENT_METHOD;Y.error(o.title,{description:o.description});return}const t=C.find(o=>o.id===s);if(t!=null&&t.isDefault){const o=a.ERROR.CANNOT_DELETE_DEFAULT_PAYMENT_METHOD;Y.error(o.title,{description:o.description});return}m(M,s,!0),m(ae,!0)}async function re(){if(!(n(L)||!n(M))){m(L,!0);try{await dn(n(M));const s=a.SUCCESS.PAYMENT_METHOD_DELETED;Y.success(s.title,{duration:s.duration}),m(le,"payment"),te()}catch(s){console.error("Error deleting payment method:",s);const t=a.ERROR.DELETE_PAYMENT_METHOD(s.message);Y.error(t.title,{description:t.description})}finally{m(L,!1),m(ae,!1)}}}async function k(){var s;if(!n(P))try{if(p&&!p.isPaused&&!p.pause_collection&&!p.cancel_at_period_end&&p.status==="active"&&!((s=p.metadata)!=null&&s.pause_at_period_end)){console.log("Subscription is already active, no need to resume"),Y.info("Your subscription is already active");return}if(m(P,!0),!p){console.error("Cannot resume subscription: subscription object is null or undefined"),Y.error("Error resuming subscription",{description:"Subscription data is missing"}),m(P,!1);return}console.log("Resuming subscription with current state:",{cancel_at_period_end:p==null?void 0:p.cancel_at_period_end,pause_collection:p==null?void 0:p.pause_collection,status:p==null?void 0:p.status,metadata:p==null?void 0:p.metadata});try{const t=await fetch("/api/billing/resume-subscription",{method:"POST",headers:{"Content-Type":"application/json"}});let o;try{o=await t.json()}catch(b){throw console.error("Error parsing response:",b),new Error("Invalid response from server")}if(!t.ok)throw console.error("API error resuming subscription:",o),new Error((o==null?void 0:o.error)||(o==null?void 0:o.details)||"Failed to resume subscription");if(o.success)if(console.log("Subscription resumed successfully"),o.message==="Subscription is already active"){const b=a.INFO.SUBSCRIPTION_ALREADY_ACTIVE;Y.info(b.title,{duration:b.duration})}else{const b=a.SUCCESS.SUBSCRIPTION_RESUMED;Y.success(b.title,{duration:b.duration}),console.log("Redirecting to refresh page after successful resume..."),typeof window<"u"?setTimeout(()=>{window.location.href=window.location.pathname+"?resumed=true"},500):console.warn("Cannot redirect: not in browser context")}else{const b=a.ERROR.RESUME_SUBSCRIPTION;Y.error(b.title,{description:b.description})}}catch(t){console.error("Error making API request:",t);const o=a.ERROR.RESUME_SUBSCRIPTION;Y.error(o.title,{description:t.message||o.description})}}catch(t){console.error("Unexpected error in handleResumeSubscription:",t);const o=a.ERROR.RESUME_SUBSCRIPTION;Y.error(o.title,{description:t.message||o.description})}finally{m(P,!1)}}async function N(s=!0){if(!n(P)){m(P,!0);try{const t=await fetch("/api/billing/cancel-subscription",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({cancelAtPeriodEnd:s})});if(!t.ok)throw new Error("Failed to cancel subscription");if((await t.json()).success){console.log("Subscription canceled successfully");const b=a.SUCCESS.SUBSCRIPTION_CANCELED(s);Y.success(b.title,{duration:b.duration}),m(S,!1),console.log("Redirecting to refresh page after successful cancel..."),typeof window<"u"?window.location.href=window.location.pathname+"?canceled=true":console.warn("Cannot redirect: not in browser context")}else{const b=a.ERROR.CANCEL_SUBSCRIPTION;Y.error(b.title,{description:b.description})}}catch(t){console.error("Error canceling subscription:",t);const o=a.ERROR.CANCEL_SUBSCRIPTION;Y.error(o.title,{description:o.description})}finally{m(P,!1)}}}async function ee(){if(!n(P)){m(P,!0);try{if(!p){console.error("Cannot pause subscription: subscription object is null or undefined"),Y.error("Error pausing subscription",{description:"Subscription data is missing"}),m(P,!1);return}console.log("Pausing subscription with current state:",{cancel_at_period_end:p==null?void 0:p.cancel_at_period_end,metadata:p==null?void 0:p.metadata,status:p==null?void 0:p.status});const s=await fetch("/api/billing/cancel-subscription",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pauseAtPeriodEnd:!0})});if(!s.ok)throw new Error("Failed to pause subscription");const t=await s.json();if(console.log("Pause subscription response:",t),t.success){console.log("Subscription paused successfully");const o=a.SUCCESS.SUBSCRIPTION_PAUSED;Y.success(o.title,{duration:o.duration}),m(R,!1),console.log("Redirecting to refresh page after successful pause..."),typeof window<"u"?window.location.href=window.location.pathname+"?paused=true":console.warn("Cannot redirect: not in browser context")}else{const o=a.ERROR.PAUSE_SUBSCRIPTION;Y.error(o.title,{description:o.description})}}catch(s){console.error("Error pausing subscription:",s);const t=a.ERROR.PAUSE_SUBSCRIPTION;Y.error(t.title,{description:t.description})}finally{m(P,!1)}}}const H=[{get isOpen(){return n(ae)},set isOpen(s){m(ae,s,!0)},title:"Delete Payment Method",description:"Are you sure you want to delete this payment method? This action cannot be undone.",cancelText:"Cancel",confirmText:"Delete",loadingText:"Deleting...",get isLoading(){return n(L)},onCancel:()=>m(ae,!1),onConfirm:re,confirmClass:"bg-destructive text-destructive-foreground hover:bg-destructive/90"},{get isOpen(){return n(S)},set isOpen(s){m(S,s,!0)},title:"Cancel Subscription",description:"Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your current billing period.",cancelText:"Keep Subscription",confirmText:"Cancel Subscription",loadingText:"Canceling...",get isLoading(){return n(P)},onCancel:()=>m(S,!1),onConfirm:()=>N(!0)},{get isOpen(){return n(R)},set isOpen(s){m(R,s,!0)},title:"Pause Subscription",description:"Your subscription will be paused at the end of the current billing period. You can resume your subscription at any time.",cancelText:"Keep Active",confirmText:"Pause Subscription",loadingText:"Pausing...",get isLoading(){return n(P)},onCancel:()=>m(R,!1),onConfirm:()=>ee()}];console.log("asdasdsadasd"+JSON.stringify(p,null,2));var D=ha(),I=g(D);Zt(I,{title:"Billing & Subscription - Hirli",description:"Manage your subscription plan, payment methods, and view your billing history and invoices.",keywords:"billing, subscription, payment methods, invoices, pricing plans, upgrade subscription",url:"https://hirli.com/dashboard/settings/billing"});var Z=u(I,2),G=u(c(Z),2);_e(G,()=>Vt,(s,t)=>{t(s,{get value(){return n(le)},set value(o){m(le,o,!0)},children:(o,b)=>{var j=fa(),x=g(j);_e(x,()=>zt,(A,E)=>{E(A,{children:(h,$)=>{var de=va(),et=g(de);_e(et,()=>pt,(Ie,Be)=>{Be(Ie,{value:"subscription",children:(ze,Xe)=>{K();var Je=z("Subscription");r(ze,Je)},$$slots:{default:!0}})});var Ue=u(et,2);_e(Ue,()=>pt,(Ie,Be)=>{Be(Ie,{value:"payment",children:(ze,Xe)=>{K();var Je=z("Payment Methods");r(ze,Je)},$$slots:{default:!0}})});var Ne=u(Ue,2);_e(Ne,()=>pt,(Ie,Be)=>{Be(Ie,{value:"invoices",children:(ze,Xe)=>{K();var Je=z("Invoices");r(ze,Je)},$$slots:{default:!0}})}),r(h,de)},$$slots:{default:!0}})});var Q=u(x,2);_e(Q,()=>ht,(A,E)=>{E(A,{value:"subscription",children:(h,$)=>{zn(h,{get subscription(){return p},get currentPlan(){return _},get isSubscriptionActionLoading(){return n(P)},get isLoading(){return n(oe)},get user(){return d},handleResumeSubscription:k,handleOpenPricingModal:Le,setPauseDialogOpen:de=>m(R,de,!0),setCancelDialogOpen:de=>m(S,de,!0)})},$$slots:{default:!0}})});var J=u(Q,2);_e(J,()=>ht,(A,E)=>{E(A,{value:"payment",children:(h,$)=>{ea(h,{get paymentMethods(){return C},get isPaymentMethodLoading(){return n(xe)},get isDeletePaymentMethodLoading(){return n(L)},get paymentMethodToDelete(){return n(M)},get user(){return d},handlePlanChange:he,setDefaultPaymentMethod:i,openDeleteDialog:U,setAddPaymentMethodModalOpen:de=>m(se,de,!0)})},$$slots:{default:!0}})});var fe=u(J,2);_e(fe,()=>ht,(A,E)=>{E(A,{value:"invoices",children:(h,$)=>{ua(h,{get invoices(){return V},get upcomingInvoice(){return ue},get user(){return d},get isPortalLoading(){return n(ge)},openCustomerPortal:Te})},$$slots:{default:!0}})}),r(o,j)},$$slots:{default:!0}})}),l(Z);var F=u(Z,2);ln(F,{onSuccess:f,get open(){return n(se)},set open(s){m(se,s,!0)}});var ne=u(F,2);bt(ne,17,()=>H,Dt,(s,t,o)=>{var b=Pe(),j=g(b);_e(j,()=>Xt,(x,Q)=>{Q(x,{get open(){return n(t).isOpen},set open(J){n(t).isOpen=J},children:(J,fe)=>{var A=Pe(),E=g(A);_e(E,()=>Ht,(h,$)=>{$(h,{children:(de,et)=>{var Ue=pa(),Ne=g(Ue);_e(Ne,()=>Gt,(Be,ze)=>{ze(Be,{children:(Xe,Je)=>{var tt=ma(),rt=g(tt);_e(rt,()=>qt,(nt,We)=>{We(nt,{children:(Qe,at)=>{K();var Ke=z();B(()=>O(Ke,n(t).title)),r(Qe,Ke)},$$slots:{default:!0}})});var mt=u(rt,2);_e(mt,()=>Jt,(nt,We)=>{We(nt,{children:(Qe,at)=>{K();var Ke=z();B(()=>O(Ke,n(t).description)),r(Qe,Ke)},$$slots:{default:!0}})}),r(Xe,tt)},$$slots:{default:!0}})});var Ie=u(Ne,2);_e(Ie,()=>Wt,(Be,ze)=>{ze(Be,{children:(Xe,Je)=>{var tt=ga(),rt=g(tt);_e(rt,()=>Qt,(We,Qe)=>{Qe(We,{get onclick(){return n(t).onCancel},children:(at,Ke)=>{K();var it=z();B(()=>O(it,n(t).cancelText)),r(at,it)},$$slots:{default:!0}})});var mt=u(rt,2);const nt=Re(()=>n(t).confirmClass||"");_e(mt,()=>Kt,(We,Qe)=>{Qe(We,{get onclick(){return n(t).onConfirm},get disabled(){return n(t).isLoading},get class(){return n(nt)},children:(at,Ke)=>{var it=Pe(),Lt=g(it);{var Mt=Ze=>{var ot=_a(),wt=g(ot);Ye(wt,{class:"mr-2 h-4 w-4 animate-spin"});var Ft=u(wt);B(()=>O(Ft,` ${n(t).loadingText??""}`)),r(Ze,ot)},kt=Ze=>{var ot=z();B(()=>O(ot,n(t).confirmText)),r(Ze,ot)};w(Lt,Ze=>{n(t).isLoading?Ze(Mt):Ze(kt,!1)})}r(at,it)},$$slots:{default:!0}})}),r(Xe,tt)},$$slots:{default:!0}})}),r(de,Ue)},$$slots:{default:!0}})}),r(J,A)},$$slots:{default:!0}})}),r(s,b)}),r(W,D),qe()}export{ui as component};
