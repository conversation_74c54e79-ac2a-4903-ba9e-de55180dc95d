import{c,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as p,s as $}from"./Btcx8l8F.js";import{I as m}from"./D4f2twK-.js";function M(e,t){const r=p(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16"}],["path",{d:"M18 17V9"}],["path",{d:"M13 17V5"}],["path",{d:"M8 17v-3"}]];m(e,$({name:"chart-column"},()=>r,{get iconNode(){return s},children:(a,f)=>{var o=c(),n=i(o);d(n,t,"default",{},null),l(a,o)},$$slots:{default:!0}}))}function N(e,t){const r=p(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["circle",{cx:"9",cy:"7",r:"4"}],["polyline",{points:"16 11 18 13 22 9"}]];m(e,$({name:"user-check"},()=>r,{get iconNode(){return s},children:(a,f)=>{var o=c(),n=i(o);d(n,t,"default",{},null),l(a,o)},$$slots:{default:!0}}))}export{M as C,N as U};
