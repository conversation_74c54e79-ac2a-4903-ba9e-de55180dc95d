import{c as n,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as d}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as l}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(r,t){const a=c(t,["children","$$slots","$$events","$$legacy"]),s=[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1"}],["path",{d:"M12 8v13"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5"}]];f(r,l({name:"gift"},()=>a,{get iconNode(){return s},children:(e,$)=>{var o=n(),i=d(o);m(i,t,"default",{},null),p(e,o)},$$slots:{default:!0}}))}export{y as G};
