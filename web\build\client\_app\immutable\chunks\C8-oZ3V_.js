import{c as p,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(s,o){const t=c(o,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"}]];f(s,d({name:"house"},()=>t,{get iconNode(){return r},children:(e,$)=>{var a=p(),n=i(a);m(n,o,"default",{},null),l(e,a)},$$slots:{default:!0}}))}export{y as H};
