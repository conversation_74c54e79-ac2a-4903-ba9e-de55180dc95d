import{c as l,a}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as n}from"./CGmarHxI.js";import{s as p}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function v(o,r){const e=m(r,["children","$$slots","$$events","$$legacy"]),c=[["circle",{cx:"12",cy:"12",r:"1"}],["circle",{cx:"19",cy:"12",r:"1"}],["circle",{cx:"5",cy:"12",r:"1"}]];f(o,d({name:"ellipsis"},()=>e,{get iconNode(){return c},children:(t,$)=>{var s=l(),i=n(s);p(i,r,"default",{},null),a(t,s)},$$slots:{default:!0}}))}export{v as E};
