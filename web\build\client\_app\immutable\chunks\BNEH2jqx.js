import{c,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(s,o){const t=m(o,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"M20 6 9 17l-5-5"}]];f(s,d({name:"check"},()=>t,{get iconNode(){return e},children:(a,$)=>{var r=c(),n=i(r);l(n,o,"default",{},null),p(a,r)},$$slots:{default:!0}}))}export{y as C};
