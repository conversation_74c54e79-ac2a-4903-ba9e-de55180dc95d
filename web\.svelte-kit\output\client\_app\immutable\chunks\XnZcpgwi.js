var ay=Object.defineProperty;var ch=t=>{throw TypeError(t)};var oy=(t,e,n)=>e in t?ay(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var dt=(t,e,n)=>oy(t,typeof e!="symbol"?e+"":e,n),yu=(t,e,n)=>e.has(t)||ch("Cannot "+n);var ie=(t,e,n)=>(yu(t,e,"read from private field"),n?n.call(t):e.get(t)),gt=(t,e,n)=>e.has(t)?ch("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),_t=(t,e,n,r)=>(yu(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),ls=(t,e,n)=>(yu(t,e,"access private method"),n);import{c as Ce,a as Q,p as Mr,f as rt,e as xt,t as So}from"./BasJTneF.js";import{_ as sy,o as Ao,a7 as Pr,g as o,d as Re,aE as eg,aD as lc,p as it,f as ce,x,a as at,c as nt,s as Fe,au as Rt,r as $e,$ as tg,bg as ly,k as Ve,i as Ir,aC as uy,u as vt,a_ as ng,v as Xo,t as mt,e as hf}from"./CGmarHxI.js";import{n as cy,g as fy,t as za,h as hy,i as Mi,b as dy,m as rg,j as ig,c as _a,s as Xe,k as gy,l as my}from"./ncUU1dSD.js";import{e as wt,h as yy,S as ir,c as pt,s as St,a as ft,b as wr,C as vy}from"./B-Xjo-Yt.js";import{b as Wt}from"./5V1tIHTN.js";import{p as P,r as bt,s as qe}from"./Btcx8l8F.js";import{i as ue}from"./u21ee2wt.js";import{k as Il}from"./DT9WCdWY.js";import{h as _y}from"./DYwWIJ9y.js";import{o as df}from"./nZgk9enP.js";import{e as $t,i as Pn}from"./C3w0v0gR.js";import{s as Jn}from"./CIt1g2O9.js";import{a as gf,d as xy}from"./Dq03aqGn.js";import{l as by,a as ag,b as ky}from"./BHEV2D3b.js";import{i as vu,c as wy}from"./BfX7a-t9.js";import{c as pi}from"./BvdI7LR8.js";import{c as Ka}from"./BosuxZz1.js";import{o as fh,g as mf,e as ba}from"./CmxjS0TN.js";import{s as _u}from"./BniYvUIG.js";import{r as og,l as sg,t as js}from"./BYB878do.js";import{M as Tr}from"./BQ5jqT_2.js";import{a as uc}from"./CbynRejM.js";var Ai,Pa,jo,El,lg;const Ol=class Ol{constructor(e){gt(this,El);gt(this,Ai,new WeakMap);gt(this,Pa);gt(this,jo);_t(this,jo,e)}observe(e,n){var r=ie(this,Ai).get(e)||new Set;return r.add(n),ie(this,Ai).set(e,r),ls(this,El,lg).call(this).observe(e,ie(this,jo)),()=>{var i=ie(this,Ai).get(e);i.delete(n),i.size===0&&(ie(this,Ai).delete(e),ie(this,Pa).unobserve(e))}}};Ai=new WeakMap,Pa=new WeakMap,jo=new WeakMap,El=new WeakSet,lg=function(){return ie(this,Pa)??_t(this,Pa,new ResizeObserver(e=>{for(var n of e){Ol.entries.set(n.target,n);for(var r of ie(this,Ai).get(n.target)||[])r(n)}}))},dt(Ol,"entries",new WeakMap);let cc=Ol;var My=new cc({box:"border-box"});function Fs(t,e,n){var r=My.observe(t,()=>n(t[e]));sy(()=>(Ao(()=>n(t[e])),r))}function py(t,e,n){const[r,i]=cy(n==null?void 0:n.in,t,e),a=hh(r,i),s=Math.abs(fy(r,i));r.setDate(r.getDate()-a*s);const l=+(hh(r,i)===-a),u=a*(s-l);return u===0?0:u}function hh(t,e){const n=t.getFullYear()-e.getFullYear()||t.getMonth()-e.getMonth()||t.getDate()-e.getDate()||t.getHours()-e.getHours()||t.getMinutes()-e.getMinutes()||t.getSeconds()-e.getSeconds()||t.getMilliseconds()-e.getMilliseconds();return n<0?-1:n>0?1:n}function Sy(t,e){const n=za(t,e==null?void 0:e.in),r=n.getMonth(),i=r-r%3;return n.setMonth(i,1),n.setHours(0,0,0,0),n}function Ay(t,e){var l,u,c,f;const n=hy(),r=(e==null?void 0:e.weekStartsOn)??((u=(l=e==null?void 0:e.locale)==null?void 0:l.options)==null?void 0:u.weekStartsOn)??n.weekStartsOn??((f=(c=n.locale)==null?void 0:c.options)==null?void 0:f.weekStartsOn)??0,i=za(t,e==null?void 0:e.in),a=i.getDay(),s=(a<r?-7:0)+6-(a-r);return i.setDate(i.getDate()+s),i.setHours(23,59,59,999),i}function Ty(t,e){const n=za(t,e==null?void 0:e.in),r=n.getMonth(),i=r-r%3+3;return n.setMonth(i,0),n.setHours(23,59,59,999),n}function Cy(t,e){const n=za(t,e==null?void 0:e.in);if(isNaN(+n))throw new RangeError("Invalid time value");let r="",i="";const a="-",s=":";{const l=Mi(n.getDate(),2),u=Mi(n.getMonth()+1,2);r=`${Mi(n.getFullYear(),4)}${a}${u}${a}${l}`}{const l=n.getTimezoneOffset();if(l!==0){const g=Math.abs(l),m=Mi(Math.trunc(g/60),2),v=Mi(g%60,2);i=`${l<0?"+":"-"}${m}:${v}`}else i="Z";const u=Mi(n.getHours(),2),c=Mi(n.getMinutes(),2),f=Mi(n.getSeconds(),2),h=r===""?"":"T",d=[u,c,f].join(s);r=`${r}${h}${d}${i}`}return r}function fc(t,e){const n=()=>dy(e==null?void 0:e.in,NaN),i=Ey(t);let a;if(i.date){const c=Oy(i.date,2);a=Ny(c.restDateString,c.year)}if(!a||isNaN(+a))return n();const s=+a;let l=0,u;if(i.time&&(l=Ly(i.time),isNaN(l)))return n();if(i.timezone){if(u=Wy(i.timezone),isNaN(u))return n()}else{const c=new Date(s+l),f=za(0,e==null?void 0:e.in);return f.setFullYear(c.getUTCFullYear(),c.getUTCMonth(),c.getUTCDate()),f.setHours(c.getUTCHours(),c.getUTCMinutes(),c.getUTCSeconds(),c.getUTCMilliseconds()),f}return za(s+l+u,e==null?void 0:e.in)}const us={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Dy=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Py=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,Ry=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Ey(t){const e={},n=t.split(us.dateTimeDelimiter);let r;if(n.length>2)return e;if(/:/.test(n[0])?r=n[0]:(e.date=n[0],r=n[1],us.timeZoneDelimiter.test(e.date)&&(e.date=t.split(us.timeZoneDelimiter)[0],r=t.substr(e.date.length,t.length))),r){const i=us.timezone.exec(r);i?(e.time=r.replace(i[1],""),e.timezone=i[1]):e.time=r}return e}function Oy(t,e){const n=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+e)+"})|(\\d{2}|[+-]\\d{"+(2+e)+"})$)"),r=t.match(n);if(!r)return{year:NaN,restDateString:""};const i=r[1]?parseInt(r[1]):null,a=r[2]?parseInt(r[2]):null;return{year:a===null?i:a*100,restDateString:t.slice((r[1]||r[2]).length)}}function Ny(t,e){if(e===null)return new Date(NaN);const n=t.match(Dy);if(!n)return new Date(NaN);const r=!!n[4],i=so(n[1]),a=so(n[2])-1,s=so(n[3]),l=so(n[4]),u=so(n[5])-1;if(r)return By(e,l,u)?Iy(e,l,u):new Date(NaN);{const c=new Date(0);return!jy(e,a,s)||!Fy(e,i)?new Date(NaN):(c.setUTCFullYear(e,a,Math.max(i,s)),c)}}function so(t){return t?parseInt(t):1}function Ly(t){const e=t.match(Py);if(!e)return NaN;const n=xu(e[1]),r=xu(e[2]),i=xu(e[3]);return Yy(n,r,i)?n*rg+r*ig+i*1e3:NaN}function xu(t){return t&&parseFloat(t.replace(",","."))||0}function Wy(t){if(t==="Z")return 0;const e=t.match(Ry);if(!e)return 0;const n=e[1]==="+"?-1:1,r=parseInt(e[2]),i=e[3]&&parseInt(e[3])||0;return Hy(r,i)?n*(r*rg+i*ig):NaN}function Iy(t,e,n){const r=new Date(0);r.setUTCFullYear(t,0,4);const i=r.getUTCDay()||7,a=(e-1)*7+n+1-i;return r.setUTCDate(r.getUTCDate()+a),r}const zy=[31,null,31,30,31,30,31,31,30,31,30,31];function ug(t){return t%400===0||t%4===0&&t%100!==0}function jy(t,e,n){return e>=0&&e<=11&&n>=1&&n<=(zy[e]||(ug(t)?29:28))}function Fy(t,e){return e>=1&&e<=(ug(t)?366:365)}function By(t,e,n){return e>=1&&e<=53&&n>=0&&n<=6}function Yy(t,e,n){return t===24?e===0&&n===0:n>=0&&n<60&&e>=0&&e<60&&t>=0&&t<25}function Hy(t,e){return e>=0&&e<=59}var qy=["forEach","isDisjointFrom","isSubsetOf","isSupersetOf"],Uy=["difference","intersection","symmetricDifference","union"],dh=!1,Ra,yr,Ti,Nl,cg;const Ll=class Ll extends Set{constructor(n){super();gt(this,Nl);gt(this,Ra,new Map);gt(this,yr,Pr(0));gt(this,Ti,Pr(0));if(n){for(var r of n)super.add(r);ie(this,Ti).v=super.size}dh||ls(this,Nl,cg).call(this)}has(n){var r=super.has(n),i=ie(this,Ra),a=i.get(n);if(a===void 0){if(!r)return o(ie(this,yr)),!1;a=Pr(!0),i.set(n,a)}return o(a),r}add(n){return super.has(n)||(super.add(n),Re(ie(this,Ti),super.size),vu(ie(this,yr))),this}delete(n){var r=super.delete(n),i=ie(this,Ra),a=i.get(n);return a!==void 0&&(i.delete(n),Re(a,!1)),r&&(Re(ie(this,Ti),super.size),vu(ie(this,yr))),r}clear(){if(super.size!==0){super.clear();var n=ie(this,Ra);for(var r of n.values())Re(r,!1);n.clear(),Re(ie(this,Ti),0),vu(ie(this,yr))}}keys(){return this.values()}values(){return o(ie(this,yr)),super.values()}entries(){return o(ie(this,yr)),super.entries()}[Symbol.iterator](){return this.keys()}get size(){return o(ie(this,Ti))}};Ra=new WeakMap,yr=new WeakMap,Ti=new WeakMap,Nl=new WeakSet,cg=function(){dh=!0;var n=Ll.prototype,r=Set.prototype;for(const i of qy)n[i]=function(...a){return o(ie(this,yr)),r[i].apply(this,a)};for(const i of Uy)n[i]=function(...a){o(ie(this,yr));var s=r[i].apply(this,a);return new Ll(s)}};let hc=Ll;const Gy={light:"",dark:".dark"};function gh(t,e,n){if(typeof e!="object"||e===null)return;const r="payload"in e&&typeof e.payload=="object"&&e.payload!==null?e.payload:void 0;let i=n;return e.key===n?i=e.key:e.name===n?i=e.name:n in e&&typeof e[n]=="string"?i=e[n]:r&&n in r&&typeof r[n]=="string"&&(i=r[n]),i in t?t[i]:t[n]}const fg=Symbol("chart-context");function Xy(t){return eg(fg,t)}function Vy(){return lc(fg)}function Zy(t,e){it(e,!0);const n=x(()=>e.config?Object.entries(e.config).filter(([,u])=>u.theme||u.color):null),r=">elyts<".split("").reverse().join(""),i=">elyts/<".split("").reverse().join("");var a=Ce(),s=ce(a);{var l=u=>{var c=Ce();const f=x(()=>Object.entries(Gy).map(([d,g])=>`
${g} [data-chart=${e.id}] {
${o(n).map(([m,v])=>{var b;const w=((b=v.theme)==null?void 0:b[d])||v.color;return w?`  --color-${m}: ${w};`:null}).join(`
`)}
}
`).join(`
`));var h=ce(c);Il(h,()=>e.id,d=>{var g=Ce(),m=ce(g);_y(m,()=>`${r}
		${o(f)}
	${i}`),Q(d,g)}),Q(u,c)};ue(s,u=>{o(n)&&o(n).length&&u(l)})}Q(t,a),at()}var Ky=rt("<div><!> <!></div>");function A8(t,e){const n=Mr();it(e,!0);let r=P(e,"ref",15,null),i=P(e,"id",3,n),a=bt(e,["$$slots","$$events","$$legacy","ref","id","class","children","config"]);const s=`chart-${i()||n.replace(/:/g,"")}`;Xy({get config(){return e.config}});var l=Ky();wt(l,f=>({"data-chart":s,"data-slot":"chart",class:f,...a}),[()=>_a("flex aspect-video justify-center overflow-visible text-xs","[&_.stroke-white]:stroke-transparent","[&_.lc-line]:stroke-border/50","[&_.lc-highlight-line]:stroke-0","[&_.lc-area-path]:opacity-100 [&_.lc-highlight-line]:opacity-100 [&_.lc-highlight-point]:opacity-100 [&_.lc-spline-path]:opacity-100 [&_.lc-text]:text-xs","[&_.lc-axis-tick]:stroke-0","[&_.lc-rule-x-line:not(.lc-grid-x-rule)]:stroke-0 [&_.lc-rule-y-line:not(.lc-grid-y-rule)]:stroke-0","[&_.lc-grid-x-radial-line]:stroke-border [&_.lc-grid-x-radial-circle]:stroke-border","[&_.lc-grid-y-radial-line]:stroke-border [&_.lc-grid-y-radial-circle]:stroke-border","[&_.lc-legend-swatch-button]:items-center [&_.lc-legend-swatch-button]:gap-1.5","[&_.lc-legend-swatch-group]:items-center [&_.lc-legend-swatch-group]:gap-4","[&_.lc-legend-swatch]:size-2.5 [&_.lc-legend-swatch]:rounded-[2px]","[&_.lc-labels-text:not([fill])]:fill-foreground [&_text]:stroke-transparent","[&_.lc-axis-tick-label]:fill-muted-foreground [&_.lc-axis-tick-label]:font-normal","[&_.lc-tooltip-rects-g]:fill-transparent","[&_.lc-layout-svg-g]:fill-transparent","[&_.lc-root-container]:w-full",e.class)]);var u=nt(l);Zy(u,{get id(){return s},get config(){return e.config}});var c=Fe(u,2);Xe(c,()=>e.children??Rt),$e(l),Wt(l,f=>r(f),()=>r()),Q(t,l),at()}function ii(t,e){return t==null||e==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function Qy(t,e){return t==null||e==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function To(t){let e,n,r;t.length!==2?(e=ii,n=(l,u)=>ii(t(l),u),r=(l,u)=>t(l)-u):(e=t===ii||t===Qy?t:Jy,n=t,r=t);function i(l,u,c=0,f=l.length){if(c<f){if(e(u,u)!==0)return f;do{const h=c+f>>>1;n(l[h],u)<0?c=h+1:f=h}while(c<f)}return c}function a(l,u,c=0,f=l.length){if(c<f){if(e(u,u)!==0)return f;do{const h=c+f>>>1;n(l[h],u)<=0?c=h+1:f=h}while(c<f)}return c}function s(l,u,c=0,f=l.length){const h=i(l,u,c,f-1);return h>c&&r(l[h-1],u)>-r(l[h],u)?h-1:h}return{left:i,center:s,right:a}}function Jy(){return 0}function $y(t){return t===null?NaN:+t}function*ev(t,e){for(let n of t)n!=null&&(n=+n)>=n&&(yield n)}const tv=To(ii),nv=tv.right;To($y).center;function ur(t,e){let n,r;if(e===void 0)for(const i of t)i!=null&&(n===void 0?i>=i&&(n=r=i):(n>i&&(n=i),r<i&&(r=i)));else{let i=-1;for(let a of t)(a=e(a,++i,t))!=null&&(n===void 0?a>=a&&(n=r=a):(n>a&&(n=a),r<a&&(r=a)))}return[n,r]}class xr{constructor(){this._partials=new Float64Array(32),this._n=0}add(e){const n=this._partials;let r=0;for(let i=0;i<this._n&&i<32;i++){const a=n[i],s=e+a,l=Math.abs(e)<Math.abs(a)?e-(s-a):a-(s-e);l&&(n[r++]=l),e=s}return n[r]=e,this._n=r+1,this}valueOf(){const e=this._partials;let n=this._n,r,i,a,s=0;if(n>0){for(s=e[--n];n>0&&(r=s,i=e[--n],s=r+i,a=i-(s-r),!a););n>0&&(a<0&&e[n-1]<0||a>0&&e[n-1]>0)&&(i=a*2,r=s+i,i==r-s&&(s=r))}return s}}class mh extends Map{constructor(e,n=gg){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),e!=null)for(const[r,i]of e)this.set(r,i)}get(e){return super.get(dc(this,e))}has(e){return super.has(dc(this,e))}set(e,n){return super.set(hg(this,e),n)}delete(e){return super.delete(dg(this,e))}}class rv extends Set{constructor(e,n=gg){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),e!=null)for(const r of e)this.add(r)}has(e){return super.has(dc(this,e))}add(e){return super.add(hg(this,e))}delete(e){return super.delete(dg(this,e))}}function dc({_intern:t,_key:e},n){const r=e(n);return t.has(r)?t.get(r):n}function hg({_intern:t,_key:e},n){const r=e(n);return t.has(r)?t.get(r):(t.set(r,n),n)}function dg({_intern:t,_key:e},n){const r=e(n);return t.has(r)&&(n=t.get(r),t.delete(r)),n}function gg(t){return t!==null&&typeof t=="object"?t.valueOf():t}function iv(t=ii){if(t===ii)return mg;if(typeof t!="function")throw new TypeError("compare is not a function");return(e,n)=>{const r=t(e,n);return r||r===0?r:(t(n,n)===0)-(t(e,e)===0)}}function mg(t,e){return(t==null||!(t>=t))-(e==null||!(e>=e))||(t<e?-1:t>e?1:0)}const av=Math.sqrt(50),ov=Math.sqrt(10),sv=Math.sqrt(2);function Bs(t,e,n){const r=(e-t)/Math.max(0,n),i=Math.floor(Math.log10(r)),a=r/Math.pow(10,i),s=a>=av?10:a>=ov?5:a>=sv?2:1;let l,u,c;return i<0?(c=Math.pow(10,-i)/s,l=Math.round(t*c),u=Math.round(e*c),l/c<t&&++l,u/c>e&&--u,c=-c):(c=Math.pow(10,i)*s,l=Math.round(t/c),u=Math.round(e/c),l*c<t&&++l,u*c>e&&--u),u<l&&.5<=n&&n<2?Bs(t,e,n*2):[l,u,c]}function lv(t,e,n){if(e=+e,t=+t,n=+n,!(n>0))return[];if(t===e)return[t];const r=e<t,[i,a,s]=r?Bs(e,t,n):Bs(t,e,n);if(!(a>=i))return[];const l=a-i+1,u=new Array(l);if(r)if(s<0)for(let c=0;c<l;++c)u[c]=(a-c)/-s;else for(let c=0;c<l;++c)u[c]=(a-c)*s;else if(s<0)for(let c=0;c<l;++c)u[c]=(i+c)/-s;else for(let c=0;c<l;++c)u[c]=(i+c)*s;return u}function gc(t,e,n){return e=+e,t=+t,n=+n,Bs(t,e,n)[2]}function mc(t,e,n){e=+e,t=+t,n=+n;const r=e<t,i=r?gc(e,t,n):gc(t,e,n);return(r?-1:1)*(i<0?1/-i:i)}function Xt(t,e){let n;for(const r of t)r!=null&&(n<r||n===void 0&&r>=r)&&(n=r);return n}function Bt(t,e){let n;for(const r of t)r!=null&&(n>r||n===void 0&&r>=r)&&(n=r);return n}function yg(t,e,n=0,r=1/0,i){if(e=Math.floor(e),n=Math.floor(Math.max(0,n)),r=Math.floor(Math.min(t.length-1,r)),!(n<=e&&e<=r))return t;for(i=i===void 0?mg:iv(i);r>n;){if(r-n>600){const u=r-n+1,c=e-n+1,f=Math.log(u),h=.5*Math.exp(2*f/3),d=.5*Math.sqrt(f*h*(u-h)/u)*(c-u/2<0?-1:1),g=Math.max(n,Math.floor(e-c*h/u+d)),m=Math.min(r,Math.floor(e+(u-c)*h/u+d));yg(t,e,g,m,i)}const a=t[e];let s=n,l=r;for(lo(t,n,e),i(t[r],a)>0&&lo(t,n,r);s<l;){for(lo(t,s,l),++s,--l;i(t[s],a)<0;)++s;for(;i(t[l],a)>0;)--l}i(t[n],a)===0?lo(t,n,l):(++l,lo(t,l,r)),l<=e&&(n=l+1),e<=l&&(r=l-1)}return t}function lo(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}function uv(t,e=ii){let n,r=!1;if(e.length===1){let i;for(const a of t){const s=e(a);(r?ii(s,i)>0:ii(s,s)===0)&&(n=a,i=s,r=!0)}}else for(const i of t)(r?e(i,n)>0:e(i,i)===0)&&(n=i,r=!0);return n}function cv(t,e,n){if(t=Float64Array.from(ev(t)),!(!(r=t.length)||isNaN(e=+e))){if(e<=0||r<2)return Bt(t);if(e>=1)return Xt(t);var r,i=(r-1)*e,a=Math.floor(i),s=Xt(yg(t,a).subarray(0,a+1)),l=Bt(t.subarray(a+1));return s+(l-s)*(i-a)}}function*fv(t){for(const e of t)yield*e}function vg(t){return Array.from(fv(t))}function Co(t,e,n){t=+t,e=+e,n=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+n;for(var r=-1,i=Math.max(0,Math.ceil((e-t)/n))|0,a=new Array(i);++r<i;)a[r]=t+r*n;return a}function hv(t,e){let n=0;if(e===void 0)for(let r of t)(r=+r)&&(n+=r);else{let r=-1;for(let i of t)(i=+e(i,++r,t))&&(n+=i)}return n}var _g=typeof global=="object"&&global&&global.Object===Object&&global,dv=typeof self=="object"&&self&&self.Object===Object&&self,Qa=_g||dv||Function("return this")(),ja=Qa.Symbol,xg=Object.prototype,gv=xg.hasOwnProperty,mv=xg.toString,uo=ja?ja.toStringTag:void 0;function yv(t){var e=gv.call(t,uo),n=t[uo];try{t[uo]=void 0;var r=!0}catch{}var i=mv.call(t);return r&&(e?t[uo]=n:delete t[uo]),i}var vv=Object.prototype,_v=vv.toString;function xv(t){return _v.call(t)}var bv="[object Null]",kv="[object Undefined]",yh=ja?ja.toStringTag:void 0;function Vo(t){return t==null?t===void 0?kv:bv:yh&&yh in Object(t)?yv(t):xv(t)}function Ja(t){return t!=null&&typeof t=="object"}var wv="[object Symbol]";function yf(t){return typeof t=="symbol"||Ja(t)&&Vo(t)==wv}function Mv(t,e){for(var n=-1,r=t==null?0:t.length,i=Array(r);++n<r;)i[n]=e(t[n],n,t);return i}var Fa=Array.isArray,vh=ja?ja.prototype:void 0,_h=vh?vh.toString:void 0;function bg(t){if(typeof t=="string")return t;if(Fa(t))return Mv(t,bg)+"";if(yf(t))return _h?_h.call(t):"";var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}function li(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}function kg(t){return t}var pv="[object AsyncFunction]",Sv="[object Function]",Av="[object GeneratorFunction]",Tv="[object Proxy]";function vf(t){if(!li(t))return!1;var e=Vo(t);return e==Sv||e==Av||e==pv||e==Tv}var bu=Qa["__core-js_shared__"],xh=function(){var t=/[^.]+$/.exec(bu&&bu.keys&&bu.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function Cv(t){return!!xh&&xh in t}var Dv=Function.prototype,Pv=Dv.toString;function Rv(t){if(t!=null){try{return Pv.call(t)}catch{}try{return t+""}catch{}}return""}var Ev=/[\\^$.*+?()[\]{}|]/g,Ov=/^\[object .+?Constructor\]$/,Nv=Function.prototype,Lv=Object.prototype,Wv=Nv.toString,Iv=Lv.hasOwnProperty,zv=RegExp("^"+Wv.call(Iv).replace(Ev,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function jv(t){if(!li(t)||Cv(t))return!1;var e=vf(t)?zv:Ov;return e.test(Rv(t))}function Fv(t,e){return t==null?void 0:t[e]}function _f(t,e){var n=Fv(t,e);return jv(n)?n:void 0}var bh=Object.create,Bv=function(){function t(){}return function(e){if(!li(e))return{};if(bh)return bh(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}();function wg(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}function Yv(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}var Hv=800,qv=16,Uv=Date.now;function Gv(t){var e=0,n=0;return function(){var r=Uv(),i=qv-(r-n);if(n=r,i>0){if(++e>=Hv)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}function Xv(t){return function(){return t}}var Ys=function(){try{var t=_f(Object,"defineProperty");return t({},"",{}),t}catch{}}(),Vv=Ys?function(t,e){return Ys(t,"toString",{configurable:!0,enumerable:!1,value:Xv(e),writable:!0})}:kg,Zv=Gv(Vv),Kv=9007199254740991,Qv=/^(?:0|[1-9]\d*)$/;function Mg(t,e){var n=typeof t;return e=e??Kv,!!e&&(n=="number"||n!="symbol"&&Qv.test(t))&&t>-1&&t%1==0&&t<e}function xf(t,e,n){e=="__proto__"&&Ys?Ys(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}function zl(t,e){return t===e||t!==t&&e!==e}var Jv=Object.prototype,$v=Jv.hasOwnProperty;function e_(t,e,n){var r=t[e];(!($v.call(t,e)&&zl(r,n))||n===void 0&&!(e in t))&&xf(t,e,n)}function t_(t,e,n,r){var i=!n;n||(n={});for(var a=-1,s=e.length;++a<s;){var l=e[a],u=void 0;u===void 0&&(u=t[l]),i?xf(n,l,u):e_(n,l,u)}return n}var kh=Math.max;function n_(t,e,n){return e=kh(e===void 0?t.length-1:e,0),function(){for(var r=arguments,i=-1,a=kh(r.length-e,0),s=Array(a);++i<a;)s[i]=r[e+i];i=-1;for(var l=Array(e+1);++i<e;)l[i]=r[i];return l[e]=n(s),wg(t,this,l)}}function pg(t,e){return Zv(n_(t,e,kg),t+"")}var r_=9007199254740991;function Sg(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=r_}function bf(t){return t!=null&&Sg(t.length)&&!vf(t)}function i_(t,e,n){if(!li(n))return!1;var r=typeof e;return(r=="number"?bf(n)&&Mg(e,n.length):r=="string"&&e in n)?zl(n[e],t):!1}function Ag(t){return pg(function(e,n){var r=-1,i=n.length,a=i>1?n[i-1]:void 0,s=i>2?n[2]:void 0;for(a=t.length>3&&typeof a=="function"?(i--,a):void 0,s&&i_(n[0],n[1],s)&&(a=i<3?void 0:a,i=1),e=Object(e);++r<i;){var l=n[r];l&&t(e,l,r,a)}return e})}var a_=Object.prototype;function Tg(t){var e=t&&t.constructor,n=typeof e=="function"&&e.prototype||a_;return t===n}function o_(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}var s_="[object Arguments]";function wh(t){return Ja(t)&&Vo(t)==s_}var Cg=Object.prototype,l_=Cg.hasOwnProperty,u_=Cg.propertyIsEnumerable,yc=wh(function(){return arguments}())?wh:function(t){return Ja(t)&&l_.call(t,"callee")&&!u_.call(t,"callee")};function c_(){return!1}var Dg=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Mh=Dg&&typeof module=="object"&&module&&!module.nodeType&&module,f_=Mh&&Mh.exports===Dg,ph=f_?Qa.Buffer:void 0,h_=ph?ph.isBuffer:void 0,Pg=h_||c_,d_="[object Arguments]",g_="[object Array]",m_="[object Boolean]",y_="[object Date]",v_="[object Error]",__="[object Function]",x_="[object Map]",b_="[object Number]",k_="[object Object]",w_="[object RegExp]",M_="[object Set]",p_="[object String]",S_="[object WeakMap]",A_="[object ArrayBuffer]",T_="[object DataView]",C_="[object Float32Array]",D_="[object Float64Array]",P_="[object Int8Array]",R_="[object Int16Array]",E_="[object Int32Array]",O_="[object Uint8Array]",N_="[object Uint8ClampedArray]",L_="[object Uint16Array]",W_="[object Uint32Array]",Qt={};Qt[C_]=Qt[D_]=Qt[P_]=Qt[R_]=Qt[E_]=Qt[O_]=Qt[N_]=Qt[L_]=Qt[W_]=!0;Qt[d_]=Qt[g_]=Qt[A_]=Qt[m_]=Qt[T_]=Qt[y_]=Qt[v_]=Qt[__]=Qt[x_]=Qt[b_]=Qt[k_]=Qt[w_]=Qt[M_]=Qt[p_]=Qt[S_]=!1;function I_(t){return Ja(t)&&Sg(t.length)&&!!Qt[Vo(t)]}function z_(t){return function(e){return t(e)}}var Rg=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Mo=Rg&&typeof module=="object"&&module&&!module.nodeType&&module,j_=Mo&&Mo.exports===Rg,ku=j_&&_g.process,Sh=function(){try{var t=Mo&&Mo.require&&Mo.require("util").types;return t||ku&&ku.binding&&ku.binding("util")}catch{}}(),Ah=Sh&&Sh.isTypedArray,Eg=Ah?z_(Ah):I_;function F_(t,e){var n=Fa(t),r=!n&&yc(t),i=!n&&!r&&Pg(t),a=!n&&!r&&!i&&Eg(t),s=n||r||i||a,l=s?o_(t.length,String):[],u=l.length;for(var c in t)s&&(c=="length"||i&&(c=="offset"||c=="parent")||a&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||Mg(c,u))||l.push(c);return l}function B_(t,e){return function(n){return t(e(n))}}function Y_(t){var e=[];if(t!=null)for(var n in Object(t))e.push(n);return e}var H_=Object.prototype,q_=H_.hasOwnProperty;function U_(t){if(!li(t))return Y_(t);var e=Tg(t),n=[];for(var r in t)r=="constructor"&&(e||!q_.call(t,r))||n.push(r);return n}function Og(t){return bf(t)?F_(t):U_(t)}var G_=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,X_=/^\w*$/;function V_(t,e){if(Fa(t))return!1;var n=typeof t;return n=="number"||n=="symbol"||n=="boolean"||t==null||yf(t)?!0:X_.test(t)||!G_.test(t)||e!=null&&t in Object(e)}var Do=_f(Object,"create");function Z_(){this.__data__=Do?Do(null):{},this.size=0}function K_(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}var Q_="__lodash_hash_undefined__",J_=Object.prototype,$_=J_.hasOwnProperty;function ex(t){var e=this.__data__;if(Do){var n=e[t];return n===Q_?void 0:n}return $_.call(e,t)?e[t]:void 0}var tx=Object.prototype,nx=tx.hasOwnProperty;function rx(t){var e=this.__data__;return Do?e[t]!==void 0:nx.call(e,t)}var ix="__lodash_hash_undefined__";function ax(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=Do&&e===void 0?ix:e,this}function na(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}na.prototype.clear=Z_;na.prototype.delete=K_;na.prototype.get=ex;na.prototype.has=rx;na.prototype.set=ax;function ox(){this.__data__=[],this.size=0}function jl(t,e){for(var n=t.length;n--;)if(zl(t[n][0],e))return n;return-1}var sx=Array.prototype,lx=sx.splice;function ux(t){var e=this.__data__,n=jl(e,t);if(n<0)return!1;var r=e.length-1;return n==r?e.pop():lx.call(e,n,1),--this.size,!0}function cx(t){var e=this.__data__,n=jl(e,t);return n<0?void 0:e[n][1]}function fx(t){return jl(this.__data__,t)>-1}function hx(t,e){var n=this.__data__,r=jl(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this}function hi(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}hi.prototype.clear=ox;hi.prototype.delete=ux;hi.prototype.get=cx;hi.prototype.has=fx;hi.prototype.set=hx;var Ng=_f(Qa,"Map");function dx(){this.size=0,this.__data__={hash:new na,map:new(Ng||hi),string:new na}}function gx(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}function Fl(t,e){var n=t.__data__;return gx(e)?n[typeof e=="string"?"string":"hash"]:n.map}function mx(t){var e=Fl(this,t).delete(t);return this.size-=e?1:0,e}function yx(t){return Fl(this,t).get(t)}function vx(t){return Fl(this,t).has(t)}function _x(t,e){var n=Fl(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this}function zi(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}zi.prototype.clear=dx;zi.prototype.delete=mx;zi.prototype.get=yx;zi.prototype.has=vx;zi.prototype.set=_x;var xx="Expected a function";function $a(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new TypeError(xx);var n=function(){var r=arguments,i=e?e.apply(this,r):r[0],a=n.cache;if(a.has(i))return a.get(i);var s=t.apply(this,r);return n.cache=a.set(i,s)||a,s};return n.cache=new($a.Cache||zi),n}$a.Cache=zi;var bx=500;function kx(t){var e=$a(t,function(r){return n.size===bx&&n.clear(),r}),n=e.cache;return e}var wx=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Mx=/\\(\\)?/g,px=kx(function(t){var e=[];return t.charCodeAt(0)===46&&e.push(""),t.replace(wx,function(n,r,i,a){e.push(i?a.replace(Mx,"$1"):r||n)}),e});function Sx(t){return t==null?"":bg(t)}function Ax(t,e){return Fa(t)?t:V_(t,e)?[t]:px(Sx(t))}function Tx(t){if(typeof t=="string"||yf(t))return t;var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}function Cx(t,e){e=Ax(e,t);for(var n=0,r=e.length;t!=null&&n<r;)t=t[Tx(e[n++])];return n&&n==r?t:void 0}function Lg(t,e,n){var r=t==null?void 0:Cx(t,e);return r===void 0?n:r}var Wg=B_(Object.getPrototypeOf,Object),Dx="[object Object]",Px=Function.prototype,Rx=Object.prototype,Ig=Px.toString,Ex=Rx.hasOwnProperty,Ox=Ig.call(Object);function Nx(t){if(!Ja(t)||Vo(t)!=Dx)return!1;var e=Wg(t);if(e===null)return!0;var n=Ex.call(e,"constructor")&&e.constructor;return typeof n=="function"&&n instanceof n&&Ig.call(n)==Ox}function Lx(){this.__data__=new hi,this.size=0}function Wx(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n}function Ix(t){return this.__data__.get(t)}function zx(t){return this.__data__.has(t)}var jx=200;function Fx(t,e){var n=this.__data__;if(n instanceof hi){var r=n.__data__;if(!Ng||r.length<jx-1)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new zi(r)}return n.set(t,e),this.size=n.size,this}function eo(t){var e=this.__data__=new hi(t);this.size=e.size}eo.prototype.clear=Lx;eo.prototype.delete=Wx;eo.prototype.get=Ix;eo.prototype.has=zx;eo.prototype.set=Fx;var zg=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Th=zg&&typeof module=="object"&&module&&!module.nodeType&&module,Bx=Th&&Th.exports===zg,Ch=Bx?Qa.Buffer:void 0;Ch&&Ch.allocUnsafe;function Yx(t,e){return t.slice()}var Dh=Qa.Uint8Array;function Hx(t){var e=new t.constructor(t.byteLength);return new Dh(e).set(new Dh(t)),e}function qx(t,e){var n=Hx(t.buffer);return new t.constructor(n,t.byteOffset,t.length)}function Ux(t){return typeof t.constructor=="function"&&!Tg(t)?Bv(Wg(t)):{}}function Gx(t){return function(e,n,r){for(var i=-1,a=Object(e),s=r(e),l=s.length;l--;){var u=s[++i];if(n(a[u],u,a)===!1)break}return e}}var Xx=Gx();function vc(t,e,n){(n!==void 0&&!zl(t[e],n)||n===void 0&&!(e in t))&&xf(t,e,n)}function Vx(t){return Ja(t)&&bf(t)}function _c(t,e){if(!(e==="constructor"&&typeof t[e]=="function")&&e!="__proto__")return t[e]}function Zx(t){return t_(t,Og(t))}function Kx(t,e,n,r,i,a,s){var l=_c(t,n),u=_c(e,n),c=s.get(u);if(c){vc(t,n,c);return}var f=a?a(l,u,n+"",t,e,s):void 0,h=f===void 0;if(h){var d=Fa(u),g=!d&&Pg(u),m=!d&&!g&&Eg(u);f=u,d||g||m?Fa(l)?f=l:Vx(l)?f=Yv(l):g?(h=!1,f=Yx(u)):m?(h=!1,f=qx(u)):f=[]:Nx(u)||yc(u)?(f=l,yc(l)?f=Zx(l):(!li(l)||vf(l))&&(f=Ux(u))):h=!1}h&&(s.set(u,f),i(f,u,r,a,s),s.delete(u)),vc(t,n,f)}function Bl(t,e,n,r,i){t!==e&&Xx(e,function(a,s){if(i||(i=new eo),li(a))Kx(t,e,s,n,Bl,r,i);else{var l=r?r(_c(t,s),a,s+"",t,e,i):void 0;l===void 0&&(l=a),vc(t,s,l)}},Og)}function jg(t,e,n,r,i,a){return li(t)&&li(e)&&(a.set(e,t),Bl(t,e,void 0,jg,a),a.delete(e)),t}var Qx=Ag(function(t,e,n,r){Bl(t,e,n,r)}),Hs=pg(function(t){return t.push(void 0,jg),wg(Qx,void 0,t)}),to=Ag(function(t,e,n){Bl(t,e,n)});function Jx(t){return Object.keys(t)}function Fg(t){return t instanceof Map?Array.from(t.entries()):Object.entries(t)}function $x(t){return Object.fromEntries(t)}function Bg(t){return t&&typeof t=="object"&&t.constructor===Object}function e2(t){return typeof t=="function"?t:typeof t=="string"?e=>Lg(e,t):e=>e}var wu=new WeakMap,t2=0;function n2(t){return wu.has(t)||wu.set(t,++t2),wu.get(t)}function r2(t){return Jx.length===0?t:$x(Fg(t).filter(([e,n])=>n!=null))}function i2(t){return Array.from(new Set(t))}function a2(t){return uv(t,(e,n)=>Math.abs(e)-Math.abs(n))}function cs(t){return t!=null}function o2(t){return!!t&&(t instanceof SVGElement||"ownerSVGElement"in t)}function s2(t){return!!t&&"createSVGPoint"in t}function l2(t){return!!t&&"getScreenCTM"in t}function u2(t){return!!t&&"changedTouches"in t}var Oe;(function(t){t[t.Custom=1]="Custom",t[t.Day=10]="Day",t[t.DayTime=11]="DayTime",t[t.TimeOnly=15]="TimeOnly",t[t.Week=20]="Week",t[t.WeekSun=21]="WeekSun",t[t.WeekMon=22]="WeekMon",t[t.WeekTue=23]="WeekTue",t[t.WeekWed=24]="WeekWed",t[t.WeekThu=25]="WeekThu",t[t.WeekFri=26]="WeekFri",t[t.WeekSat=27]="WeekSat",t[t.Month=30]="Month",t[t.MonthYear=31]="MonthYear",t[t.Quarter=40]="Quarter",t[t.CalendarYear=50]="CalendarYear",t[t.FiscalYearOctober=60]="FiscalYearOctober",t[t.BiWeek1=70]="BiWeek1",t[t.BiWeek1Sun=71]="BiWeek1Sun",t[t.BiWeek1Mon=72]="BiWeek1Mon",t[t.BiWeek1Tue=73]="BiWeek1Tue",t[t.BiWeek1Wed=74]="BiWeek1Wed",t[t.BiWeek1Thu=75]="BiWeek1Thu",t[t.BiWeek1Fri=76]="BiWeek1Fri",t[t.BiWeek1Sat=77]="BiWeek1Sat",t[t.BiWeek2=80]="BiWeek2",t[t.BiWeek2Sun=81]="BiWeek2Sun",t[t.BiWeek2Mon=82]="BiWeek2Mon",t[t.BiWeek2Tue=83]="BiWeek2Tue",t[t.BiWeek2Wed=84]="BiWeek2Wed",t[t.BiWeek2Thu=85]="BiWeek2Thu",t[t.BiWeek2Fri=86]="BiWeek2Fri",t[t.BiWeek2Sat=87]="BiWeek2Sat"})(Oe||(Oe={}));const Yg={[Oe.Custom]:"custom",[Oe.Day]:"day",[Oe.DayTime]:"daytime",[Oe.TimeOnly]:"time",[Oe.WeekSun]:"week-sun",[Oe.WeekMon]:"week-mon",[Oe.WeekTue]:"week-tue",[Oe.WeekWed]:"week-wed",[Oe.WeekThu]:"week-thu",[Oe.WeekFri]:"week-fri",[Oe.WeekSat]:"week-sat",[Oe.Week]:"week",[Oe.Month]:"month",[Oe.MonthYear]:"month-year",[Oe.Quarter]:"quarter",[Oe.CalendarYear]:"year",[Oe.FiscalYearOctober]:"fiscal-year-october",[Oe.BiWeek1Sun]:"biweek1-sun",[Oe.BiWeek1Mon]:"biweek1-mon",[Oe.BiWeek1Tue]:"biweek1-tue",[Oe.BiWeek1Wed]:"biweek1-wed",[Oe.BiWeek1Thu]:"biweek1-thu",[Oe.BiWeek1Fri]:"biweek1-fri",[Oe.BiWeek1Sat]:"biweek1-sat",[Oe.BiWeek1]:"biweek1",[Oe.BiWeek2Sun]:"biweek2-sun",[Oe.BiWeek2Mon]:"biweek2-mon",[Oe.BiWeek2Tue]:"biweek2-tue",[Oe.BiWeek2Wed]:"biweek2-wed",[Oe.BiWeek2Thu]:"biweek2-thu",[Oe.BiWeek2Fri]:"biweek2-fri",[Oe.BiWeek2Sat]:"biweek2-sat",[Oe.BiWeek2]:"biweek2"};var qs;(function(t){t[t.Sunday=0]="Sunday",t[t.Monday=1]="Monday",t[t.Tuesday=2]="Tuesday",t[t.Wednesday=3]="Wednesday",t[t.Thursday=4]="Thursday",t[t.Friday=5]="Friday",t[t.Saturday=6]="Saturday"})(qs||(qs={}));var ze;(function(t){t.Year_numeric="yyy",t.Year_2Digit="yy",t.Month_long="MMMM",t.Month_short="MMM",t.Month_2Digit="MM",t.Month_numeric="M",t.Hour_numeric="h",t.Hour_2Digit="hh",t.Hour_wAMPM="a",t.Hour_woAMPM="aaaaaa",t.Minute_numeric="m",t.Minute_2Digit="mm",t.Second_numeric="s",t.Second_2Digit="ss",t.MiliSecond_3="SSS",t.DayOfMonth_numeric="d",t.DayOfMonth_2Digit="dd",t.DayOfMonth_withOrdinal="do",t.DayOfWeek_narrow="eeeee",t.DayOfWeek_long="eeee",t.DayOfWeek_short="eee"})(ze||(ze={}));function c2(t){var r;if(!t)return qs.Sunday;const e=new Intl.Locale(t),n=e.weekInfo??((r=e.getWeekInfo)==null?void 0:r.call(e));return((n==null?void 0:n.firstDay)??0)%7}const f2={locale:"en",dictionary:{Ok:"Ok",Cancel:"Cancel",Date:{Start:"Start",End:"End",Empty:"Empty",Day:"Day",DayTime:"Day Time",Time:"Time",Week:"Week",BiWeek:"Bi-Week",Month:"Month",Quarter:"Quarter",CalendarYear:"Calendar Year",FiscalYearOct:"Fiscal Year (Oct)",PeriodDay:{Current:"Today",Last:"Yesterday",LastX:"Last {0} days"},PeriodWeek:{Current:"This week",Last:"Last week",LastX:"Last {0} weeks"},PeriodBiWeek:{Current:"This bi-week",Last:"Last bi-week",LastX:"Last {0} bi-weeks"},PeriodMonth:{Current:"This month",Last:"Last month",LastX:"Last {0} months"},PeriodQuarter:{Current:"This quarter",Last:"Last quarter",LastX:"Last {0} quarters"},PeriodQuarterSameLastyear:"Same quarter last year",PeriodYear:{Current:"This year",Last:"Last year",LastX:"Last {0} years"},PeriodFiscalYear:{Current:"This fiscal year",Last:"Last fiscal year",LastX:"Last {0} fiscal years"}}},formats:{numbers:{defaults:{currency:"USD",fractionDigits:2,currencyDisplay:"symbol"}},dates:{baseParsing:"MM/dd/yyyy",weekStartsOn:qs.Sunday,ordinalSuffixes:{one:"st",two:"nd",few:"rd",other:"th"},presets:{day:{short:[ze.DayOfMonth_numeric,ze.Month_numeric],default:[ze.DayOfMonth_numeric,ze.Month_numeric,ze.Year_numeric],long:[ze.DayOfMonth_numeric,ze.Month_short,ze.Year_numeric]},dayTime:{short:[ze.DayOfMonth_numeric,ze.Month_numeric,ze.Year_numeric,ze.Hour_numeric,ze.Minute_numeric],default:[ze.DayOfMonth_numeric,ze.Month_numeric,ze.Year_numeric,ze.Hour_2Digit,ze.Minute_2Digit],long:[ze.DayOfMonth_numeric,ze.Month_numeric,ze.Year_numeric,ze.Hour_2Digit,ze.Minute_2Digit,ze.Second_2Digit]},timeOnly:{short:[ze.Hour_numeric,ze.Minute_numeric],default:[ze.Hour_2Digit,ze.Minute_2Digit,ze.Second_2Digit],long:[ze.Hour_2Digit,ze.Minute_2Digit,ze.Second_2Digit,ze.MiliSecond_3]},week:{short:[ze.DayOfMonth_numeric,ze.Month_numeric],default:[ze.DayOfMonth_numeric,ze.Month_numeric,ze.Year_numeric],long:[ze.DayOfMonth_numeric,ze.Month_numeric,ze.Year_numeric]},month:{short:ze.Month_short,default:ze.Month_long,long:[ze.Month_long,ze.Year_numeric]},monthsYear:{short:[ze.Month_short,ze.Year_2Digit],default:[ze.Month_long,ze.Year_numeric],long:[ze.Month_long,ze.Year_numeric]},year:{short:ze.Year_2Digit,default:ze.Year_numeric,long:ze.Year_numeric}}}}};function h2(t,e=f2){var n,r,i,a;return(r=(n=t.formats)==null?void 0:n.dates)!=null&&r.ordinalSuffixes&&(t.formats.dates.ordinalSuffixes={one:"",two:"",few:"",other:"",zero:"",many:"",...t.formats.dates.ordinalSuffixes}),((a=(i=t.formats)==null?void 0:i.dates)==null?void 0:a.weekStartsOn)===void 0&&(t=Hs(t,{formats:{dates:{weekStartsOn:c2(t.locale)}}})),Hs(t,e)}const Ph=h2({locale:"en"});function d2(t){const e=Fg(Yg).find(n=>n[1]===t);return parseInt(String((e==null?void 0:e[0])??"0"))}function g2(t=new Date,e){return t===null?NaN:t.getMonth()>=10-1?t.getFullYear()+1:t.getFullYear()}const m2=[new Date("1799-12-22T00:00"),new Date("1799-12-15T00:00")];function Hg(t,e,n){var r=m2[e-1],i=uc(r,n),a=Math.floor(py(t,i)/14);return uc(i,a*14)}function y2(t,e,n){return uc(Hg(t,e,n),13)}function tr(t,e,n){const{locale:r,formats:{dates:{ordinalSuffixes:i}}}=t;function a(u,c=!1){if(c){const f=new Intl.PluralRules(r,{type:"ordinal"});return u.formatToParts(e).map(d=>{if(d.type==="day"){const g=f.select(parseInt(d.value,10)),m=i[g];return`${d.value}${m}`}return d.value}).join("")}return u.format(e)}if(typeof n!="string"&&!Array.isArray(n))return a(new Intl.DateTimeFormat(r,n),n.withOrdinal);const s=Array.isArray(n)?n.join(""):n,l=new Intl.DateTimeFormat(r,{year:s.includes(ze.Year_numeric)?"numeric":s.includes(ze.Year_2Digit)?"2-digit":void 0,month:s.includes(ze.Month_long)?"long":s.includes(ze.Month_short)?"short":s.includes(ze.Month_2Digit)?"2-digit":s.includes(ze.Month_numeric)?"numeric":void 0,day:s.includes(ze.DayOfMonth_2Digit)?"2-digit":s.includes(ze.DayOfMonth_numeric)?"numeric":void 0,hour:s.includes(ze.Hour_2Digit)?"2-digit":s.includes(ze.Hour_numeric)?"numeric":void 0,hour12:s.includes(ze.Hour_woAMPM)?!1:s.includes(ze.Hour_wAMPM)?!0:void 0,minute:s.includes(ze.Minute_2Digit)?"2-digit":s.includes(ze.Minute_numeric)?"numeric":void 0,second:s.includes(ze.Second_2Digit)?"2-digit":s.includes(ze.Second_numeric)?"numeric":void 0,fractionalSecondDigits:s.includes(ze.MiliSecond_3)?3:void 0,weekday:s.includes(ze.DayOfWeek_narrow)?"narrow":s.includes(ze.DayOfWeek_long)?"long":s.includes(ze.DayOfWeek_short)?"short":void 0});return a(l,s.includes(ze.DayOfMonth_withOrdinal))}function an(t,e,n,r,i=void 0){const a=i===void 0?gy(e,{weekStartsOn:n}):Hg(e,i,n),s=i===void 0?Ay(e,{weekStartsOn:n}):y2(e,i,n);return tr(t,a,r)+" - "+tr(t,s,r)}function v2(t,e){return e===Oe.Week?e=[Oe.WeekSun,Oe.WeekMon,Oe.WeekTue,Oe.WeekWed,Oe.WeekThu,Oe.WeekFri,Oe.WeekSat][t]:e===Oe.BiWeek1?e=[Oe.BiWeek1Sun,Oe.BiWeek1Mon,Oe.BiWeek1Tue,Oe.BiWeek1Wed,Oe.BiWeek1Thu,Oe.BiWeek1Fri,Oe.BiWeek1Sat][t]:e===Oe.BiWeek2&&(e=[Oe.BiWeek2Sun,Oe.BiWeek2Mon,Oe.BiWeek2Tue,Oe.BiWeek2Wed,Oe.BiWeek2Thu,Oe.BiWeek2Fri,Oe.BiWeek2Sat][t]),e}function _2(t,e,n,r={}){if(typeof e=="string"&&(e=fc(e)),e==null||isNaN(e))return"";const i=r.weekStartsOn??t.formats.dates.weekStartsOn,{day:a,dayTime:s,timeOnly:l,week:u,month:c,monthsYear:f,year:h}=t.formats.dates.presets;n=typeof n=="string"?d2(n):n??Oe.Day,n=v2(i,n)??n;function d(g){return r.variant==="custom"?r.custom??g.default:r.custom&&!r.variant?r.custom:g[r.variant??"default"]}switch(n){case Oe.Custom:return tr(t,e,r.custom);case Oe.Day:return tr(t,e,d(a));case Oe.DayTime:return tr(t,e,d(s));case Oe.TimeOnly:return tr(t,e,d(l));case Oe.Week:case Oe.WeekSun:return an(t,e,0,d(u));case Oe.WeekMon:return an(t,e,1,d(u));case Oe.WeekTue:return an(t,e,2,d(u));case Oe.WeekWed:return an(t,e,3,d(u));case Oe.WeekThu:return an(t,e,4,d(u));case Oe.WeekFri:return an(t,e,5,d(u));case Oe.WeekSat:return an(t,e,6,d(u));case Oe.Month:return tr(t,e,d(c));case Oe.MonthYear:return tr(t,e,d(f));case Oe.Quarter:return[tr(t,Sy(e),d(c)),tr(t,Ty(e),d(f))].join(" - ");case Oe.CalendarYear:return tr(t,e,d(h));case Oe.FiscalYearOctober:const g=new Date(g2(e),0,1);return tr(t,g,d(h));case Oe.BiWeek1:case Oe.BiWeek1Sun:return an(t,e,0,d(u),1);case Oe.BiWeek1Mon:return an(t,e,1,d(u),1);case Oe.BiWeek1Tue:return an(t,e,2,d(u),1);case Oe.BiWeek1Wed:return an(t,e,3,d(u),1);case Oe.BiWeek1Thu:return an(t,e,4,d(u),1);case Oe.BiWeek1Fri:return an(t,e,5,d(u),1);case Oe.BiWeek1Sat:return an(t,e,6,d(u),1);case Oe.BiWeek2:case Oe.BiWeek2Sun:return an(t,e,0,d(u),2);case Oe.BiWeek2Mon:return an(t,e,1,d(u),2);case Oe.BiWeek2Tue:return an(t,e,2,d(u),2);case Oe.BiWeek2Wed:return an(t,e,3,d(u),2);case Oe.BiWeek2Thu:return an(t,e,4,d(u),2);case Oe.BiWeek2Fri:return an(t,e,5,d(u),2);case Oe.BiWeek2Sat:return an(t,e,6,d(u),2);default:return Cy(e)}}const x2=/^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(.\d+|)(Z|(-|\+)\d{2}:\d{2}))?$/;function b2(t){return x2.test(t)}function ei(t,e){if(e||(e=t.currentTarget??t.target),!e||!t)return{x:0,y:0};const n=k2(t),r=o2(e)?e.ownerSVGElement:e,i=l2(r)?r.getScreenCTM():null;if(s2(r)&&i){let s=r.createSVGPoint();return s.x=n.x,s.y=n.y,s=s.matrixTransform(i.inverse()),{x:s.x,y:s.y}}const a=e.getBoundingClientRect();return{x:n.x-a.left-e.clientLeft,y:n.y-a.top-e.clientTop}}function k2(t){return t?u2(t)?t.changedTouches.length>0?{x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY}:{x:0,y:0}:{x:t.clientX,y:t.clientY}:{x:0,y:0}}var Rh;(function(t){t[t.Year=0]="Year",t[t.Day=1]="Day",t[t.Hour=2]="Hour",t[t.Minute=3]="Minute",t[t.Second=4]="Second",t[t.Millisecond=5]="Millisecond"})(Rh||(Rh={}));var nr,Xn,Vn,Zn,Kn,Ci;class Xr{constructor(e={}){gt(this,nr,0);gt(this,Xn,0);gt(this,Vn,0);gt(this,Zn,0);gt(this,Kn,0);gt(this,Ci,0);var a,s,l,u,c,f;const n=typeof e.start=="string"?fc(e.start):e.start,r=typeof e.end=="string"?fc(e.end):e.end,i=n?Math.abs(Number(r||new Date)-Number(n)):void 0;if(!(!Number.isFinite(i)&&e.duration==null)){if(_t(this,nr,((a=e.duration)==null?void 0:a.milliseconds)??i??0),_t(this,Xn,((s=e.duration)==null?void 0:s.seconds)??0),_t(this,Vn,((l=e.duration)==null?void 0:l.minutes)??0),_t(this,Zn,((u=e.duration)==null?void 0:u.hours)??0),_t(this,Kn,((c=e.duration)==null?void 0:c.days)??0),_t(this,Ci,((f=e.duration)==null?void 0:f.years)??0),ie(this,nr)>=1e3){const h=(ie(this,nr)-ie(this,nr)%1e3)/1e3;_t(this,Xn,ie(this,Xn)+h),_t(this,nr,ie(this,nr)-h*1e3)}if(ie(this,Xn)>=60){const h=(ie(this,Xn)-ie(this,Xn)%60)/60;_t(this,Vn,ie(this,Vn)+h),_t(this,Xn,ie(this,Xn)-h*60)}if(ie(this,Vn)>=60){const h=(ie(this,Vn)-ie(this,Vn)%60)/60;_t(this,Zn,ie(this,Zn)+h),_t(this,Vn,ie(this,Vn)-h*60)}if(ie(this,Zn)>=24){const h=(ie(this,Zn)-ie(this,Zn)%24)/24;_t(this,Kn,ie(this,Kn)+h),_t(this,Zn,ie(this,Zn)-h*24)}if(ie(this,Kn)>=365){const h=(ie(this,Kn)-ie(this,Kn)%365)/365;_t(this,Ci,ie(this,Ci)+h),_t(this,Kn,ie(this,Kn)-h*365)}}}get years(){return ie(this,Ci)}get days(){return ie(this,Kn)}get hours(){return ie(this,Zn)}get minutes(){return ie(this,Vn)}get seconds(){return ie(this,Xn)}get milliseconds(){return ie(this,nr)}valueOf(){return ie(this,nr)+ie(this,Xn)*1e3+ie(this,Vn)*60*1e3+ie(this,Zn)*60*60*1e3+ie(this,Kn)*24*60*60*1e3+ie(this,Ci)*365*24*60*60*1e3}toJSON(){return{years:ie(this,Ci),days:ie(this,Kn),hours:ie(this,Zn),minutes:ie(this,Vn),seconds:ie(this,Xn),milliseconds:ie(this,nr)}}format(e={}){const{minUnits:n,totalUnits:r=99,variant:i="short"}=e;var a=[],s=i==="short"?["y","d","h","m","s","ms"]:["years","days","hours","minutes","seconds","milliseconds"],l=[this.years,this.days,this.hours,this.minutes,this.seconds,this.milliseconds].filter((f,h)=>h<=(n??99));for(var u in l){if(a.length>=r)break;const f=l[u];let h=s[u];if(f!==0||a.length===0&&Number(u)===l.length-1)switch(i){case"short":a.push(f+h);break;case"long":f===1&&(h=h.slice(0,-1)),a.push(f+" "+h);break}}return a.join(i==="long"?" and ":" ")}toString(){return this.format()}}nr=new WeakMap,Xn=new WeakMap,Vn=new WeakMap,Zn=new WeakMap,Kn=new WeakMap,Ci=new WeakMap;function w2(t,e){const{numbers:n}=t.formats,r=e&&e!="none"?n[e]:{};return{...n.defaults,...r}}function M2(t,e,n,r={}){if(e==null)return"";if(n==="none")return`${e}`;n==null&&(n=Number.isInteger(e)?"integer":"decimal");const i=w2(t,n),s=Intl.NumberFormat(t.locale,{...i,...n!=="default"&&{style:n},minimumFractionDigits:r.fractionDigits??i.fractionDigits,maximumFractionDigits:r.fractionDigits??i.fractionDigits,...r2(r),...n==="currencyRound"&&{style:"currency",minimumFractionDigits:0,maximumFractionDigits:0},...n==="percentRound"&&{style:"percent",minimumFractionDigits:0,maximumFractionDigits:0},...n==="metric"&&{style:"decimal",notation:"compact",minimumFractionDigits:0},...n==="integer"&&{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}}).format(e);let l=r.suffix??"";return l&&Math.abs(e)>=2&&r.suffixExtraIfMany!==""&&(l+=r.suffixExtraIfMany??"s"),`${s}${l}`}function Hn(t,e,n){return t<e?e:t>n?n:t}function jt(t,e,n){return e&&typeof e=="object"&&"type"in e?Eh(Ph,t,e.type,e.options):Eh(Ph,t,e,n)}function Eh(t,e,n,r){const i=n&&typeof n=="object"&&"type"in n?n.type:n,a=n&&typeof n=="object"&&"type"in n&&"options"in n?n.options:r;return typeof i=="function"?i(e):e instanceof Date||b2(e)||i&&(i in Oe||Object.values(Yg).includes(i))?_2(t,e,i,a):typeof e=="number"?M2(t,e,i,a):typeof e=="string"?e:e==null?"":`${e}`}const p2=typeof window<"u",Oh=["TRACE","DEBUG","INFO","WARN","ERROR"];class qg{constructor(e){dt(this,"name");this.name=e}trace(...e){this.log("TRACE",...e)}debug(...e){this.log("DEBUG",...e)}info(...e){this.log("INFO",...e)}warn(...e){this.log("WARN",...e)}error(...e){this.log("ERROR",...e)}log(e,...n){var s;const i=(p2?((s=localStorage.getItem("logger"))==null?void 0:s.split(",").map(l=>l.split(":")))??[]:[]).find(l=>l[0]===this.name);if(i!=null&&Oh.indexOf(e)>=Oh.indexOf(i[1]??"DEBUG"))switch(e){case"TRACE":console.trace(`%c${this.name} %c${e}`,"color: hsl(200deg, 10%, 50%)","color: hsl(200deg, 40%, 50%)",...n);break;case"DEBUG":console.log(`%c${this.name} %c${e}`,"color: hsl(200deg, 10%, 50%)","color: hsl(200deg, 40%, 50%)",...n);break;case"INFO":console.log(`%c${this.name} %c${e}`,"color: hsl(200deg, 10%, 50%)","color: hsl(60deg, 100%, 50%)",...n);break;case"WARN":console.warn(`%c${this.name} %c${e}`,"color: hsl(200deg, 10%, 50%)","color: hsl(30deg, 100%, 50%)",...n);break;case"ERROR":console.warn(`%c${this.name} %c${e}`,"color: hsl(200deg, 10%, 50%)","color: hsl(0deg, 100%, 50%)",...n);break}}}function Mu(t,e="asc"){const n=e==="asc"?1:-1;return(r,i)=>{const a=e2(t),s=a(r),l=a(i);return s==null||l==null?s==null&&l!=null?-n:s!=null&&l==null?n:0:s<l?-n:s>l?n:0}}const Ug=(t,e)=>{if(typeof t=="number"){if(e===3)return{mode:"rgb",r:(t>>8&15|t>>4&240)/255,g:(t>>4&15|t&240)/255,b:(t&15|t<<4&240)/255};if(e===4)return{mode:"rgb",r:(t>>12&15|t>>8&240)/255,g:(t>>8&15|t>>4&240)/255,b:(t>>4&15|t&240)/255,alpha:(t&15|t<<4&240)/255};if(e===6)return{mode:"rgb",r:(t>>16&255)/255,g:(t>>8&255)/255,b:(t&255)/255};if(e===8)return{mode:"rgb",r:(t>>24&255)/255,g:(t>>16&255)/255,b:(t>>8&255)/255,alpha:(t&255)/255}}},S2={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074},A2=t=>Ug(S2[t.toLowerCase()],6),T2=/^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i,C2=t=>{let e;return(e=t.match(T2))?Ug(parseInt(e[1],16),e[1].length):void 0},Oi="([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)",po=`${Oi}%`,kf=`(?:${Oi}%|${Oi})`,D2=`(?:${Oi}(deg|grad|rad|turn)|${Oi})`,Ba="\\s*,\\s*",P2=new RegExp(`^rgba?\\(\\s*${Oi}${Ba}${Oi}${Ba}${Oi}\\s*(?:,\\s*${kf}\\s*)?\\)$`),R2=new RegExp(`^rgba?\\(\\s*${po}${Ba}${po}${Ba}${po}\\s*(?:,\\s*${kf}\\s*)?\\)$`),E2=t=>{let e={mode:"rgb"},n;if(n=t.match(P2))n[1]!==void 0&&(e.r=n[1]/255),n[2]!==void 0&&(e.g=n[2]/255),n[3]!==void 0&&(e.b=n[3]/255);else if(n=t.match(R2))n[1]!==void 0&&(e.r=n[1]/100),n[2]!==void 0&&(e.g=n[2]/100),n[3]!==void 0&&(e.b=n[3]/100);else return;return n[4]!==void 0?e.alpha=Math.max(0,Math.min(1,n[4]/100)):n[5]!==void 0&&(e.alpha=Math.max(0,Math.min(1,+n[5]))),e},O2=(t,e)=>t===void 0?void 0:typeof t!="object"?q2(t):t.mode!==void 0?t:e?{...t,mode:e}:void 0,N2=(t="rgb")=>e=>(e=O2(e,t))!==void 0?e.mode===t?e:Rr[e.mode][t]?Rr[e.mode][t](e):t==="rgb"?Rr[e.mode].rgb(e):Rr.rgb[t](Rr[e.mode].rgb(e)):void 0,Rr={},Gg={},Us=[],Xg={},L2=t=>t,Ht=t=>(Rr[t.mode]={...Rr[t.mode],...t.toMode},Object.keys(t.fromMode||{}).forEach(e=>{Rr[e]||(Rr[e]={}),Rr[e][t.mode]=t.fromMode[e]}),t.ranges||(t.ranges={}),t.difference||(t.difference={}),t.channels.forEach(e=>{if(t.ranges[e]===void 0&&(t.ranges[e]=[0,1]),!t.interpolate[e])throw new Error(`Missing interpolator for: ${e}`);typeof t.interpolate[e]=="function"&&(t.interpolate[e]={use:t.interpolate[e]}),t.interpolate[e].fixup||(t.interpolate[e].fixup=L2)}),Gg[t.mode]=t,(t.parse||[]).forEach(e=>{I2(e,t.mode)}),N2(t.mode)),W2=t=>Gg[t],I2=(t,e)=>{if(typeof t=="string"){if(!e)throw new Error("'mode' required when 'parser' is a string");Xg[t]=e}else typeof t=="function"&&Us.indexOf(t)<0&&Us.push(t)},xc=/[^\x00-\x7F]|[a-zA-Z_]/,z2=/[^\x00-\x7F]|[-\w]/,We={Function:"function",Ident:"ident",Number:"number",Percentage:"percentage",ParenClose:")",None:"none",Hue:"hue",Alpha:"alpha"};let st=0;function fs(t){let e=t[st],n=t[st+1];return e==="-"||e==="+"?/\d/.test(n)||n==="."&&/\d/.test(t[st+2]):e==="."?/\d/.test(n):/\d/.test(e)}function bc(t){if(st>=t.length)return!1;let e=t[st];if(xc.test(e))return!0;if(e==="-"){if(t.length-st<2)return!1;let n=t[st+1];return!!(n==="-"||xc.test(n))}return!1}const j2={deg:1,rad:180/Math.PI,grad:9/10,turn:360};function co(t){let e="";if((t[st]==="-"||t[st]==="+")&&(e+=t[st++]),e+=hs(t),t[st]==="."&&/\d/.test(t[st+1])&&(e+=t[st++]+hs(t)),(t[st]==="e"||t[st]==="E")&&((t[st+1]==="-"||t[st+1]==="+")&&/\d/.test(t[st+2])?e+=t[st++]+t[st++]+hs(t):/\d/.test(t[st+1])&&(e+=t[st++]+hs(t))),bc(t)){let n=Gs(t);return n==="deg"||n==="rad"||n==="turn"||n==="grad"?{type:We.Hue,value:e*j2[n]}:void 0}return t[st]==="%"?(st++,{type:We.Percentage,value:+e}):{type:We.Number,value:+e}}function hs(t){let e="";for(;/\d/.test(t[st]);)e+=t[st++];return e}function Gs(t){let e="";for(;st<t.length&&z2.test(t[st]);)e+=t[st++];return e}function F2(t){let e=Gs(t);return t[st]==="("?(st++,{type:We.Function,value:e}):e==="none"?{type:We.None,value:void 0}:{type:We.Ident,value:e}}function B2(t=""){let e=t.trim(),n=[],r;for(st=0;st<e.length;){if(r=e[st++],r===`
`||r==="	"||r===" "){for(;st<e.length&&(e[st]===`
`||e[st]==="	"||e[st]===" ");)st++;continue}if(r===",")return;if(r===")"){n.push({type:We.ParenClose});continue}if(r==="+"){if(st--,fs(e)){n.push(co(e));continue}return}if(r==="-"){if(st--,fs(e)){n.push(co(e));continue}if(bc(e)){n.push({type:We.Ident,value:Gs(e)});continue}return}if(r==="."){if(st--,fs(e)){n.push(co(e));continue}return}if(r==="/"){for(;st<e.length&&(e[st]===`
`||e[st]==="	"||e[st]===" ");)st++;let i;if(fs(e)&&(i=co(e),i.type!==We.Hue)){n.push({type:We.Alpha,value:i});continue}if(bc(e)&&Gs(e)==="none"){n.push({type:We.Alpha,value:{type:We.None,value:void 0}});continue}return}if(/\d/.test(r)){st--,n.push(co(e));continue}if(xc.test(r)){st--,n.push(F2(e));continue}return}return n}function Y2(t){t._i=0;let e=t[t._i++];if(!e||e.type!==We.Function||e.value!=="color"||(e=t[t._i++],e.type!==We.Ident))return;const n=Xg[e.value];if(!n)return;const r={mode:n},i=Vg(t,!1);if(!i)return;const a=W2(n).channels;for(let s=0,l,u;s<a.length;s++)l=i[s],u=a[s],l.type!==We.None&&(r[u]=l.type===We.Number?l.value:l.value/100,u==="alpha"&&(r[u]=Math.max(0,Math.min(1,r[u]))));return r}function Vg(t,e){const n=[];let r;for(;t._i<t.length;){if(r=t[t._i++],r.type===We.None||r.type===We.Number||r.type===We.Alpha||r.type===We.Percentage||e&&r.type===We.Hue){n.push(r);continue}if(r.type===We.ParenClose){if(t._i<t.length)return;continue}return}if(!(n.length<3||n.length>4)){if(n.length===4){if(n[3].type!==We.Alpha)return;n[3]=n[3].value}return n.length===3&&n.push({type:We.None,value:void 0}),n.every(i=>i.type!==We.Alpha)?n:void 0}}function H2(t,e){t._i=0;let n=t[t._i++];if(!n||n.type!==We.Function)return;let r=Vg(t,e);if(r)return r.unshift(n.value),r}const q2=t=>{if(typeof t!="string")return;const e=B2(t),n=e?H2(e,!0):void 0;let r,i=0,a=Us.length;for(;i<a;)if((r=Us[i++](t,n))!==void 0)return r;return e?Y2(e):void 0};function U2(t,e){if(!e||e[0]!=="rgb"&&e[0]!=="rgba")return;const n={mode:"rgb"},[,r,i,a,s]=e;if(!(r.type===We.Hue||i.type===We.Hue||a.type===We.Hue))return r.type!==We.None&&(n.r=r.type===We.Number?r.value/255:r.value/100),i.type!==We.None&&(n.g=i.type===We.Number?i.value/255:i.value/100),a.type!==We.None&&(n.b=a.type===We.Number?a.value/255:a.value/100),s.type!==We.None&&(n.alpha=Math.min(1,Math.max(0,s.type===We.Number?s.value:s.value/100))),n}const G2=t=>t==="transparent"?{mode:"rgb",r:0,g:0,b:0,alpha:0}:void 0,X2=(t,e,n)=>t+n*(e-t),V2=t=>{let e=[];for(let n=0;n<t.length-1;n++){let r=t[n],i=t[n+1];r===void 0&&i===void 0?e.push(void 0):r!==void 0&&i!==void 0?e.push([r,i]):e.push(r!==void 0?[r,r]:[i,i])}return e},Z2=t=>e=>{let n=V2(e);return r=>{let i=r*n.length,a=r>=1?n.length-1:Math.max(Math.floor(i),0),s=n[a];return s===void 0?void 0:t(s[0],s[1],i-a)}},Ye=Z2(X2),hn=t=>{let e=!1,n=t.map(r=>r!==void 0?(e=!0,r):1);return e?n:t},no={mode:"rgb",channels:["r","g","b","alpha"],parse:[U2,C2,E2,A2,G2,"srgb"],serialize:"srgb",interpolate:{r:Ye,g:Ye,b:Ye,alpha:{use:Ye,fixup:hn}},gamut:!0,white:{r:1,g:1,b:1},black:{r:0,g:0,b:0}},pu=(t=0)=>Math.pow(Math.abs(t),563/256)*Math.sign(t),Nh=t=>{let e=pu(t.r),n=pu(t.g),r=pu(t.b),i={mode:"xyz65",x:.5766690429101305*e+.1855582379065463*n+.1882286462349947*r,y:.297344975250536*e+.6273635662554661*n+.0752914584939979*r,z:.0270313613864123*e+.0706888525358272*n+.9913375368376386*r};return t.alpha!==void 0&&(i.alpha=t.alpha),i},Su=t=>Math.pow(Math.abs(t),256/563)*Math.sign(t),Lh=({x:t,y:e,z:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i={mode:"a98",r:Su(t*2.0415879038107465-e*.5650069742788597-.3447313507783297*n),g:Su(t*-.9692436362808798+e*1.8759675015077206+.0415550574071756*n),b:Su(t*.0134442806320312-e*.1183623922310184+1.0151749943912058*n)};return r!==void 0&&(i.alpha=r),i},Au=(t=0)=>{const e=Math.abs(t);return e<=.04045?t/12.92:(Math.sign(t)||1)*Math.pow((e+.055)/1.055,2.4)},ro=({r:t,g:e,b:n,alpha:r})=>{let i={mode:"lrgb",r:Au(t),g:Au(e),b:Au(n)};return r!==void 0&&(i.alpha=r),i},oa=t=>{let{r:e,g:n,b:r,alpha:i}=ro(t),a={mode:"xyz65",x:.4123907992659593*e+.357584339383878*n+.1804807884018343*r,y:.2126390058715102*e+.715168678767756*n+.0721923153607337*r,z:.0193308187155918*e+.119194779794626*n+.9505321522496607*r};return i!==void 0&&(a.alpha=i),a},Tu=(t=0)=>{const e=Math.abs(t);return e>.0031308?(Math.sign(t)||1)*(1.055*Math.pow(e,1/2.4)-.055):t*12.92},io=({r:t,g:e,b:n,alpha:r},i="rgb")=>{let a={mode:i,r:Tu(t),g:Tu(e),b:Tu(n)};return r!==void 0&&(a.alpha=r),a},sa=({x:t,y:e,z:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=io({r:t*3.2409699419045226-e*1.537383177570094-.4986107602930034*n,g:t*-.9692436362808796+e*1.8759675015077204+.0415550574071756*n,b:t*.0556300796969936-e*.2039769588889765+1.0569715142428784*n});return r!==void 0&&(i.alpha=r),i},K2={...no,mode:"a98",parse:["a98-rgb"],serialize:"a98-rgb",fromMode:{rgb:t=>Lh(oa(t)),xyz65:Lh},toMode:{rgb:t=>sa(Nh(t)),xyz65:Nh}},pn=t=>(t=t%360)<0?t+360:t,Q2=(t,e)=>t.map((n,r,i)=>{if(n===void 0)return n;let a=pn(n);return r===0||t[r-1]===void 0?a:e(a-pn(i[r-1]))}).reduce((n,r)=>!n.length||r===void 0||n[n.length-1]===void 0?(n.push(r),n):(n.push(r+n[n.length-1]),n),[]),di=t=>Q2(t,e=>Math.abs(e)<=180?e:e-360*Math.sign(e)),bn=[-.14861,1.78277,-.29227,-.90649,1.97294,0],J2=Math.PI/180,$2=180/Math.PI;let Wh=bn[3]*bn[4],Ih=bn[1]*bn[4],zh=bn[1]*bn[2]-bn[0]*bn[3];const eb=({r:t,g:e,b:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=(zh*n+t*Wh-e*Ih)/(zh+Wh-Ih),a=n-i,s=(bn[4]*(e-i)-bn[2]*a)/bn[3],l={mode:"cubehelix",l:i,s:i===0||i===1?void 0:Math.sqrt(a*a+s*s)/(bn[4]*i*(1-i))};return l.s&&(l.h=Math.atan2(s,a)*$2-120),r!==void 0&&(l.alpha=r),l},tb=({h:t,s:e,l:n,alpha:r})=>{let i={mode:"rgb"};t=(t===void 0?0:t+120)*J2,n===void 0&&(n=0);let a=e===void 0?0:e*n*(1-n),s=Math.cos(t),l=Math.sin(t);return i.r=n+a*(bn[0]*s+bn[1]*l),i.g=n+a*(bn[2]*s+bn[3]*l),i.b=n+a*(bn[4]*s+bn[5]*l),r!==void 0&&(i.alpha=r),i},Yl=(t,e)=>{if(t.h===void 0||e.h===void 0||!t.s||!e.s)return 0;let n=pn(t.h),r=pn(e.h),i=Math.sin((r-n+360)/2*Math.PI/180);return 2*Math.sqrt(t.s*e.s)*i},nb=(t,e)=>{if(t.h===void 0||e.h===void 0)return 0;let n=pn(t.h),r=pn(e.h);return Math.abs(r-n)>180?n-(r-360*Math.sign(r-n)):r-n},Hl=(t,e)=>{if(t.h===void 0||e.h===void 0||!t.c||!e.c)return 0;let n=pn(t.h),r=pn(e.h),i=Math.sin((r-n+360)/2*Math.PI/180);return 2*Math.sqrt(t.c*e.c)*i},gi=t=>{let e=t.reduce((r,i)=>{if(i!==void 0){let a=i*Math.PI/180;r.sin+=Math.sin(a),r.cos+=Math.cos(a)}return r},{sin:0,cos:0}),n=Math.atan2(e.sin,e.cos)*180/Math.PI;return n<0?360+n:n},rb={mode:"cubehelix",channels:["h","s","l","alpha"],parse:["--cubehelix"],serialize:"--cubehelix",ranges:{h:[0,360],s:[0,4.614],l:[0,1]},fromMode:{rgb:eb},toMode:{rgb:tb},interpolate:{h:{use:Ye,fixup:di},s:Ye,l:Ye,alpha:{use:Ye,fixup:hn}},difference:{h:Yl},average:{h:gi}},Li=({l:t,a:e,b:n,alpha:r},i="lch")=>{e===void 0&&(e=0),n===void 0&&(n=0);let a=Math.sqrt(e*e+n*n),s={mode:i,l:t,c:a};return a&&(s.h=pn(Math.atan2(n,e)*180/Math.PI)),r!==void 0&&(s.alpha=r),s},Wi=({l:t,c:e,h:n,alpha:r},i="lab")=>{n===void 0&&(n=0);let a={mode:i,l:t,a:e?e*Math.cos(n/180*Math.PI):0,b:e?e*Math.sin(n/180*Math.PI):0};return r!==void 0&&(a.alpha=r),a},Zg=Math.pow(29,3)/Math.pow(3,3),Kg=Math.pow(6,3)/Math.pow(29,3),ln={X:.3457/.3585,Y:1,Z:(1-.3457-.3585)/.3585},Aa={X:.3127/.329,Y:1,Z:(1-.3127-.329)/.329};let Cu=t=>Math.pow(t,3)>Kg?Math.pow(t,3):(116*t-16)/Zg;const Qg=({l:t,a:e,b:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=(t+16)/116,a=e/500+i,s=i-n/200,l={mode:"xyz65",x:Cu(a)*Aa.X,y:Cu(i)*Aa.Y,z:Cu(s)*Aa.Z};return r!==void 0&&(l.alpha=r),l},ql=t=>sa(Qg(t)),Du=t=>t>Kg?Math.cbrt(t):(Zg*t+16)/116,Jg=({x:t,y:e,z:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=Du(t/Aa.X),a=Du(e/Aa.Y),s=Du(n/Aa.Z),l={mode:"lab65",l:116*a-16,a:500*(i-a),b:200*(a-s)};return r!==void 0&&(l.alpha=r),l},Ul=t=>{let e=Jg(oa(t));return t.r===t.b&&t.b===t.g&&(e.a=e.b=0),e},Xs=1,$g=1,Po=26/180*Math.PI,Vs=Math.cos(Po),Zs=Math.sin(Po),e1=100/Math.log(139/100),kc=({l:t,c:e,h:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i={mode:"lab65",l:(Math.exp(t*Xs/e1)-1)/.0039},a=(Math.exp(.0435*e*$g*Xs)-1)/.075,s=a*Math.cos(n/180*Math.PI-Po),l=a*Math.sin(n/180*Math.PI-Po);return i.a=s*Vs-l/.83*Zs,i.b=s*Zs+l/.83*Vs,r!==void 0&&(i.alpha=r),i},wc=({l:t,a:e,b:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=e*Vs+n*Zs,a=.83*(n*Vs-e*Zs),s=Math.sqrt(i*i+a*a),l={mode:"dlch",l:e1/Xs*Math.log(1+.0039*t),c:Math.log(1+.075*s)/(.0435*$g*Xs)};return l.c&&(l.h=pn((Math.atan2(a,i)+Po)/Math.PI*180)),r!==void 0&&(l.alpha=r),l},jh=t=>kc(Li(t,"dlch")),Fh=t=>Wi(wc(t),"dlab"),ib={mode:"dlab",parse:["--din99o-lab"],serialize:"--din99o-lab",toMode:{lab65:jh,rgb:t=>ql(jh(t))},fromMode:{lab65:Fh,rgb:t=>Fh(Ul(t))},channels:["l","a","b","alpha"],ranges:{l:[0,100],a:[-40.09,45.501],b:[-40.469,44.344]},interpolate:{l:Ye,a:Ye,b:Ye,alpha:{use:Ye,fixup:hn}}},ab={mode:"dlch",parse:["--din99o-lch"],serialize:"--din99o-lch",toMode:{lab65:kc,dlab:t=>Wi(t,"dlab"),rgb:t=>ql(kc(t))},fromMode:{lab65:wc,dlab:t=>Li(t,"dlch"),rgb:t=>wc(Ul(t))},channels:["l","c","h","alpha"],ranges:{l:[0,100],c:[0,51.484],h:[0,360]},interpolate:{l:Ye,c:Ye,h:{use:Ye,fixup:di},alpha:{use:Ye,fixup:hn}},difference:{h:Hl},average:{h:gi}};function ob({h:t,s:e,i:n,alpha:r}){t=pn(t!==void 0?t:0),e===void 0&&(e=0),n===void 0&&(n=0);let i=Math.abs(t/60%2-1),a;switch(Math.floor(t/60)){case 0:a={r:n*(1+e*(3/(2-i)-1)),g:n*(1+e*(3*(1-i)/(2-i)-1)),b:n*(1-e)};break;case 1:a={r:n*(1+e*(3*(1-i)/(2-i)-1)),g:n*(1+e*(3/(2-i)-1)),b:n*(1-e)};break;case 2:a={r:n*(1-e),g:n*(1+e*(3/(2-i)-1)),b:n*(1+e*(3*(1-i)/(2-i)-1))};break;case 3:a={r:n*(1-e),g:n*(1+e*(3*(1-i)/(2-i)-1)),b:n*(1+e*(3/(2-i)-1))};break;case 4:a={r:n*(1+e*(3*(1-i)/(2-i)-1)),g:n*(1-e),b:n*(1+e*(3/(2-i)-1))};break;case 5:a={r:n*(1+e*(3/(2-i)-1)),g:n*(1-e),b:n*(1+e*(3*(1-i)/(2-i)-1))};break;default:a={r:n*(1-e),g:n*(1-e),b:n*(1-e)}}return a.mode="rgb",r!==void 0&&(a.alpha=r),a}function sb({r:t,g:e,b:n,alpha:r}){t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=Math.max(t,e,n),a=Math.min(t,e,n),s={mode:"hsi",s:t+e+n===0?0:1-3*a/(t+e+n),i:(t+e+n)/3};return i-a!==0&&(s.h=(i===t?(e-n)/(i-a)+(e<n)*6:i===e?(n-t)/(i-a)+2:(t-e)/(i-a)+4)*60),r!==void 0&&(s.alpha=r),s}const lb={mode:"hsi",toMode:{rgb:ob},parse:["--hsi"],serialize:"--hsi",fromMode:{rgb:sb},channels:["h","s","i","alpha"],ranges:{h:[0,360]},gamut:"rgb",interpolate:{h:{use:Ye,fixup:di},s:Ye,i:Ye,alpha:{use:Ye,fixup:hn}},difference:{h:Yl},average:{h:gi}};function ub({h:t,s:e,l:n,alpha:r}){t=pn(t!==void 0?t:0),e===void 0&&(e=0),n===void 0&&(n=0);let i=n+e*(n<.5?n:1-n),a=i-(i-n)*2*Math.abs(t/60%2-1),s;switch(Math.floor(t/60)){case 0:s={r:i,g:a,b:2*n-i};break;case 1:s={r:a,g:i,b:2*n-i};break;case 2:s={r:2*n-i,g:i,b:a};break;case 3:s={r:2*n-i,g:a,b:i};break;case 4:s={r:a,g:2*n-i,b:i};break;case 5:s={r:i,g:2*n-i,b:a};break;default:s={r:2*n-i,g:2*n-i,b:2*n-i}}return s.mode="rgb",r!==void 0&&(s.alpha=r),s}function cb({r:t,g:e,b:n,alpha:r}){t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=Math.max(t,e,n),a=Math.min(t,e,n),s={mode:"hsl",s:i===a?0:(i-a)/(1-Math.abs(i+a-1)),l:.5*(i+a)};return i-a!==0&&(s.h=(i===t?(e-n)/(i-a)+(e<n)*6:i===e?(n-t)/(i-a)+2:(t-e)/(i-a)+4)*60),r!==void 0&&(s.alpha=r),s}const fb=(t,e)=>{switch(e){case"deg":return+t;case"rad":return t/Math.PI*180;case"grad":return t/10*9;case"turn":return t*360}},hb=new RegExp(`^hsla?\\(\\s*${D2}${Ba}${po}${Ba}${po}\\s*(?:,\\s*${kf}\\s*)?\\)$`),db=t=>{let e=t.match(hb);if(!e)return;let n={mode:"hsl"};return e[3]!==void 0?n.h=+e[3]:e[1]!==void 0&&e[2]!==void 0&&(n.h=fb(e[1],e[2])),e[4]!==void 0&&(n.s=Math.min(Math.max(0,e[4]/100),1)),e[5]!==void 0&&(n.l=Math.min(Math.max(0,e[5]/100),1)),e[6]!==void 0?n.alpha=Math.max(0,Math.min(1,e[6]/100)):e[7]!==void 0&&(n.alpha=Math.max(0,Math.min(1,+e[7]))),n};function gb(t,e){if(!e||e[0]!=="hsl"&&e[0]!=="hsla")return;const n={mode:"hsl"},[,r,i,a,s]=e;if(r.type!==We.None){if(r.type===We.Percentage)return;n.h=r.value}if(i.type!==We.None){if(i.type===We.Hue)return;n.s=i.value/100}if(a.type!==We.None){if(a.type===We.Hue)return;n.l=a.value/100}return s.type!==We.None&&(n.alpha=Math.min(1,Math.max(0,s.type===We.Number?s.value:s.value/100))),n}const t1={mode:"hsl",toMode:{rgb:ub},fromMode:{rgb:cb},channels:["h","s","l","alpha"],ranges:{h:[0,360]},gamut:"rgb",parse:[gb,db],serialize:t=>`hsl(${t.h!==void 0?t.h:"none"} ${t.s!==void 0?t.s*100+"%":"none"} ${t.l!==void 0?t.l*100+"%":"none"}${t.alpha<1?` / ${t.alpha}`:""})`,interpolate:{h:{use:Ye,fixup:di},s:Ye,l:Ye,alpha:{use:Ye,fixup:hn}},difference:{h:Yl},average:{h:gi}};function n1({h:t,s:e,v:n,alpha:r}){t=pn(t!==void 0?t:0),e===void 0&&(e=0),n===void 0&&(n=0);let i=Math.abs(t/60%2-1),a;switch(Math.floor(t/60)){case 0:a={r:n,g:n*(1-e*i),b:n*(1-e)};break;case 1:a={r:n*(1-e*i),g:n,b:n*(1-e)};break;case 2:a={r:n*(1-e),g:n,b:n*(1-e*i)};break;case 3:a={r:n*(1-e),g:n*(1-e*i),b:n};break;case 4:a={r:n*(1-e*i),g:n*(1-e),b:n};break;case 5:a={r:n,g:n*(1-e),b:n*(1-e*i)};break;default:a={r:n*(1-e),g:n*(1-e),b:n*(1-e)}}return a.mode="rgb",r!==void 0&&(a.alpha=r),a}function r1({r:t,g:e,b:n,alpha:r}){t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=Math.max(t,e,n),a=Math.min(t,e,n),s={mode:"hsv",s:i===0?0:1-a/i,v:i};return i-a!==0&&(s.h=(i===t?(e-n)/(i-a)+(e<n)*6:i===e?(n-t)/(i-a)+2:(t-e)/(i-a)+4)*60),r!==void 0&&(s.alpha=r),s}const i1={mode:"hsv",toMode:{rgb:n1},parse:["--hsv"],serialize:"--hsv",fromMode:{rgb:r1},channels:["h","s","v","alpha"],ranges:{h:[0,360]},gamut:"rgb",interpolate:{h:{use:Ye,fixup:di},s:Ye,v:Ye,alpha:{use:Ye,fixup:hn}},difference:{h:Yl},average:{h:gi}};function mb({h:t,w:e,b:n,alpha:r}){if(e===void 0&&(e=0),n===void 0&&(n=0),e+n>1){let i=e+n;e/=i,n/=i}return n1({h:t,s:n===1?1:1-e/(1-n),v:1-n,alpha:r})}function yb(t){let e=r1(t);if(e===void 0)return;let n=e.s!==void 0?e.s:0,r=e.v!==void 0?e.v:0,i={mode:"hwb",w:(1-n)*r,b:1-r};return e.h!==void 0&&(i.h=e.h),e.alpha!==void 0&&(i.alpha=e.alpha),i}function vb(t,e){if(!e||e[0]!=="hwb")return;const n={mode:"hwb"},[,r,i,a,s]=e;if(r.type!==We.None){if(r.type===We.Percentage)return;n.h=r.value}if(i.type!==We.None){if(i.type===We.Hue)return;n.w=i.value/100}if(a.type!==We.None){if(a.type===We.Hue)return;n.b=a.value/100}return s.type!==We.None&&(n.alpha=Math.min(1,Math.max(0,s.type===We.Number?s.value:s.value/100))),n}const _b={mode:"hwb",toMode:{rgb:mb},fromMode:{rgb:yb},channels:["h","w","b","alpha"],ranges:{h:[0,360]},gamut:"rgb",parse:[vb],serialize:t=>`hwb(${t.h!==void 0?t.h:"none"} ${t.w!==void 0?t.w*100+"%":"none"} ${t.b!==void 0?t.b*100+"%":"none"}${t.alpha<1?` / ${t.alpha}`:""})`,interpolate:{h:{use:Ye,fixup:di},w:Ye,b:Ye,alpha:{use:Ye,fixup:hn}},difference:{h:nb},average:{h:gi}},a1=203,Gl=.1593017578125,o1=78.84375,Xl=.8359375,Vl=18.8515625,Zl=18.6875;function Pu(t){if(t<0)return 0;const e=Math.pow(t,1/o1);return 1e4*Math.pow(Math.max(0,e-Xl)/(Vl-Zl*e),1/Gl)}function Ru(t){if(t<0)return 0;const e=Math.pow(t/1e4,Gl);return Math.pow((Xl+Vl*e)/(1+Zl*e),o1)}const Eu=t=>Math.max(t/a1,0),Bh=({i:t,t:e,p:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);const i=Pu(t+.008609037037932761*e+.11102962500302593*n),a=Pu(t-.00860903703793275*e-.11102962500302599*n),s=Pu(t+.5600313357106791*e-.32062717498731885*n),l={mode:"xyz65",x:Eu(2.070152218389422*i-1.3263473389671556*a+.2066510476294051*s),y:Eu(.3647385209748074*i+.680566024947227*a-.0453045459220346*s),z:Eu(-.049747207535812*i-.0492609666966138*a+1.1880659249923042*s)};return r!==void 0&&(l.alpha=r),l},Ou=(t=0)=>Math.max(t*a1,0),Yh=({x:t,y:e,z:n,alpha:r})=>{const i=Ou(t),a=Ou(e),s=Ou(n),l=Ru(.3592832590121217*i+.6976051147779502*a-.0358915932320289*s),u=Ru(-.1920808463704995*i+1.1004767970374323*a+.0753748658519118*s),c=Ru(.0070797844607477*i+.0748396662186366*a+.8433265453898765*s),f=.5*l+.5*u,h=1.61376953125*l-3.323486328125*u+1.709716796875*c,d=4.378173828125*l-4.24560546875*u-.132568359375*c,g={mode:"itp",i:f,t:h,p:d};return r!==void 0&&(g.alpha=r),g},xb={mode:"itp",channels:["i","t","p","alpha"],parse:["--ictcp"],serialize:"--ictcp",toMode:{xyz65:Bh,rgb:t=>sa(Bh(t))},fromMode:{xyz65:Yh,rgb:t=>Yh(oa(t))},ranges:{i:[0,.581],t:[-.369,.272],p:[-.164,.331]},interpolate:{i:Ye,t:Ye,p:Ye,alpha:{use:Ye,fixup:hn}}},bb=134.03437499999998,kb=16295499532821565e-27,Nu=t=>{if(t<0)return 0;let e=Math.pow(t/1e4,Gl);return Math.pow((Xl+Vl*e)/(1+Zl*e),bb)},Lu=(t=0)=>Math.max(t*203,0),s1=({x:t,y:e,z:n,alpha:r})=>{t=Lu(t),e=Lu(e),n=Lu(n);let i=1.15*t-.15*n,a=.66*e+.34*t,s=Nu(.41478972*i+.579999*a+.014648*n),l=Nu(-.20151*i+1.120649*a+.0531008*n),u=Nu(-.0166008*i+.2648*a+.6684799*n),c=(s+l)/2,f={mode:"jab",j:.44*c/(1-.56*c)-kb,a:3.524*s-4.066708*l+.542708*u,b:.199076*s+1.096799*l-1.295875*u};return r!==void 0&&(f.alpha=r),f},wb=134.03437499999998,Hh=16295499532821565e-27,Wu=t=>{if(t<0)return 0;let e=Math.pow(t,1/wb);return 1e4*Math.pow((Xl-e)/(Zl*e-Vl),1/Gl)},Iu=t=>t/203,l1=({j:t,a:e,b:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=(t+Hh)/(.44+.56*(t+Hh)),a=Wu(i+.13860504*e+.058047316*n),s=Wu(i-.13860504*e-.058047316*n),l=Wu(i-.096019242*e-.8118919*n),u={mode:"xyz65",x:Iu(1.661373024652174*a-.914523081304348*s+.23136208173913045*l),y:Iu(-.3250758611844533*a+1.571847026732543*s-.21825383453227928*l),z:Iu(-.090982811*a-.31272829*s+1.5227666*l)};return r!==void 0&&(u.alpha=r),u},u1=t=>{let e=s1(oa(t));return t.r===t.b&&t.b===t.g&&(e.a=e.b=0),e},c1=t=>sa(l1(t)),Mb={mode:"jab",channels:["j","a","b","alpha"],parse:["--jzazbz"],serialize:"--jzazbz",fromMode:{rgb:u1,xyz65:s1},toMode:{rgb:c1,xyz65:l1},ranges:{j:[0,.222],a:[-.109,.129],b:[-.185,.134]},interpolate:{j:Ye,a:Ye,b:Ye,alpha:{use:Ye,fixup:hn}}},qh=({j:t,a:e,b:n,alpha:r})=>{e===void 0&&(e=0),n===void 0&&(n=0);let i=Math.sqrt(e*e+n*n),a={mode:"jch",j:t,c:i};return i&&(a.h=pn(Math.atan2(n,e)*180/Math.PI)),r!==void 0&&(a.alpha=r),a},Uh=({j:t,c:e,h:n,alpha:r})=>{n===void 0&&(n=0);let i={mode:"jab",j:t,a:e?e*Math.cos(n/180*Math.PI):0,b:e?e*Math.sin(n/180*Math.PI):0};return r!==void 0&&(i.alpha=r),i},pb={mode:"jch",parse:["--jzczhz"],serialize:"--jzczhz",toMode:{jab:Uh,rgb:t=>c1(Uh(t))},fromMode:{rgb:t=>qh(u1(t)),jab:qh},channels:["j","c","h","alpha"],ranges:{j:[0,.221],c:[0,.19],h:[0,360]},interpolate:{h:{use:Ye,fixup:di},c:Ye,j:Ye,alpha:{use:Ye,fixup:hn}},difference:{h:Hl},average:{h:gi}},Kl=Math.pow(29,3)/Math.pow(3,3),wf=Math.pow(6,3)/Math.pow(29,3);let zu=t=>Math.pow(t,3)>wf?Math.pow(t,3):(116*t-16)/Kl;const Mf=({l:t,a:e,b:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=(t+16)/116,a=e/500+i,s=i-n/200,l={mode:"xyz50",x:zu(a)*ln.X,y:zu(i)*ln.Y,z:zu(s)*ln.Z};return r!==void 0&&(l.alpha=r),l},Zo=({x:t,y:e,z:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=io({r:t*3.1341359569958707-e*1.6173863321612538-.4906619460083532*n,g:t*-.978795502912089+e*1.916254567259524+.03344273116131949*n,b:t*.07195537988411677-e*.2289768264158322+1.405386058324125*n});return r!==void 0&&(i.alpha=r),i},f1=t=>Zo(Mf(t)),Ko=t=>{let{r:e,g:n,b:r,alpha:i}=ro(t),a={mode:"xyz50",x:.436065742824811*e+.3851514688337912*n+.14307845442264197*r,y:.22249319175623702*e+.7168870538238823*n+.06061979053616537*r,z:.013923904500943465*e+.09708128566574634*n+.7140993584005155*r};return i!==void 0&&(a.alpha=i),a},ju=t=>t>wf?Math.cbrt(t):(Kl*t+16)/116,pf=({x:t,y:e,z:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=ju(t/ln.X),a=ju(e/ln.Y),s=ju(n/ln.Z),l={mode:"lab",l:116*a-16,a:500*(i-a),b:200*(a-s)};return r!==void 0&&(l.alpha=r),l},h1=t=>{let e=pf(Ko(t));return t.r===t.b&&t.b===t.g&&(e.a=e.b=0),e};function Sb(t,e){if(!e||e[0]!=="lab")return;const n={mode:"lab"},[,r,i,a,s]=e;if(!(r.type===We.Hue||i.type===We.Hue||a.type===We.Hue))return r.type!==We.None&&(n.l=Math.min(Math.max(0,r.value),100)),i.type!==We.None&&(n.a=i.type===We.Number?i.value:i.value*125/100),a.type!==We.None&&(n.b=a.type===We.Number?a.value:a.value*125/100),s.type!==We.None&&(n.alpha=Math.min(1,Math.max(0,s.type===We.Number?s.value:s.value/100))),n}const Sf={mode:"lab",toMode:{xyz50:Mf,rgb:f1},fromMode:{xyz50:pf,rgb:h1},channels:["l","a","b","alpha"],ranges:{l:[0,100],a:[-100,100],b:[-100,100]},parse:[Sb],serialize:t=>`lab(${t.l!==void 0?t.l:"none"} ${t.a!==void 0?t.a:"none"} ${t.b!==void 0?t.b:"none"}${t.alpha<1?` / ${t.alpha}`:""})`,interpolate:{l:Ye,a:Ye,b:Ye,alpha:{use:Ye,fixup:hn}}},Ab={...Sf,mode:"lab65",parse:["--lab-d65"],serialize:"--lab-d65",toMode:{xyz65:Qg,rgb:ql},fromMode:{xyz65:Jg,rgb:Ul},ranges:{l:[0,100],a:[-86.182,98.234],b:[-107.86,94.477]}};function Tb(t,e){if(!e||e[0]!=="lch")return;const n={mode:"lch"},[,r,i,a,s]=e;if(r.type!==We.None){if(r.type===We.Hue)return;n.l=Math.min(Math.max(0,r.value),100)}if(i.type!==We.None&&(n.c=Math.max(0,i.type===We.Number?i.value:i.value*150/100)),a.type!==We.None){if(a.type===We.Percentage)return;n.h=a.value}return s.type!==We.None&&(n.alpha=Math.min(1,Math.max(0,s.type===We.Number?s.value:s.value/100))),n}const Af={mode:"lch",toMode:{lab:Wi,rgb:t=>f1(Wi(t))},fromMode:{rgb:t=>Li(h1(t)),lab:Li},channels:["l","c","h","alpha"],ranges:{l:[0,100],c:[0,150],h:[0,360]},parse:[Tb],serialize:t=>`lch(${t.l!==void 0?t.l:"none"} ${t.c!==void 0?t.c:"none"} ${t.h!==void 0?t.h:"none"}${t.alpha<1?` / ${t.alpha}`:""})`,interpolate:{h:{use:Ye,fixup:di},c:Ye,l:Ye,alpha:{use:Ye,fixup:hn}},difference:{h:Hl},average:{h:gi}},Cb={...Af,mode:"lch65",parse:["--lch-d65"],serialize:"--lch-d65",toMode:{lab65:t=>Wi(t,"lab65"),rgb:t=>ql(Wi(t,"lab65"))},fromMode:{rgb:t=>Li(Ul(t),"lch65"),lab65:t=>Li(t,"lch65")},ranges:{l:[0,100],c:[0,133.807],h:[0,360]}},d1=({l:t,u:e,v:n,alpha:r})=>{e===void 0&&(e=0),n===void 0&&(n=0);let i=Math.sqrt(e*e+n*n),a={mode:"lchuv",l:t,c:i};return i&&(a.h=pn(Math.atan2(n,e)*180/Math.PI)),r!==void 0&&(a.alpha=r),a},g1=({l:t,c:e,h:n,alpha:r})=>{n===void 0&&(n=0);let i={mode:"luv",l:t,u:e?e*Math.cos(n/180*Math.PI):0,v:e?e*Math.sin(n/180*Math.PI):0};return r!==void 0&&(i.alpha=r),i},m1=(t,e,n)=>4*t/(t+15*e+3*n),y1=(t,e,n)=>9*e/(t+15*e+3*n),Db=m1(ln.X,ln.Y,ln.Z),Pb=y1(ln.X,ln.Y,ln.Z),Rb=t=>t<=wf?Kl*t:116*Math.cbrt(t)-16,Mc=({x:t,y:e,z:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=Rb(e/ln.Y),a=m1(t,e,n),s=y1(t,e,n);!isFinite(a)||!isFinite(s)?i=a=s=0:(a=13*i*(a-Db),s=13*i*(s-Pb));let l={mode:"luv",l:i,u:a,v:s};return r!==void 0&&(l.alpha=r),l},Eb=(t,e,n)=>4*t/(t+15*e+3*n),Ob=(t,e,n)=>9*e/(t+15*e+3*n),Nb=Eb(ln.X,ln.Y,ln.Z),Lb=Ob(ln.X,ln.Y,ln.Z),pc=({l:t,u:e,v:n,alpha:r})=>{if(t===void 0&&(t=0),t===0)return{mode:"xyz50",x:0,y:0,z:0};e===void 0&&(e=0),n===void 0&&(n=0);let i=e/(13*t)+Nb,a=n/(13*t)+Lb,s=ln.Y*(t<=8?t/Kl:Math.pow((t+16)/116,3)),l=s*(9*i)/(4*a),u=s*(12-3*i-20*a)/(4*a),c={mode:"xyz50",x:l,y:s,z:u};return r!==void 0&&(c.alpha=r),c},Wb=t=>d1(Mc(Ko(t))),Ib=t=>Zo(pc(g1(t))),zb={mode:"lchuv",toMode:{luv:g1,rgb:Ib},fromMode:{rgb:Wb,luv:d1},channels:["l","c","h","alpha"],parse:["--lchuv"],serialize:"--lchuv",ranges:{l:[0,100],c:[0,176.956],h:[0,360]},interpolate:{h:{use:Ye,fixup:di},c:Ye,l:Ye,alpha:{use:Ye,fixup:hn}},difference:{h:Hl},average:{h:gi}},jb={...no,mode:"lrgb",toMode:{rgb:io},fromMode:{rgb:ro},parse:["srgb-linear"],serialize:"srgb-linear"},Fb={mode:"luv",toMode:{xyz50:pc,rgb:t=>Zo(pc(t))},fromMode:{xyz50:Mc,rgb:t=>Mc(Ko(t))},channels:["l","u","v","alpha"],parse:["--luv"],serialize:"--luv",ranges:{l:[0,100],u:[-84.936,175.042],v:[-125.882,87.243]},interpolate:{l:Ye,u:Ye,v:Ye,alpha:{use:Ye,fixup:hn}}},v1=({r:t,g:e,b:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=Math.cbrt(.41222147079999993*t+.5363325363*e+.0514459929*n),a=Math.cbrt(.2119034981999999*t+.6806995450999999*e+.1073969566*n),s=Math.cbrt(.08830246189999998*t+.2817188376*e+.6299787005000002*n),l={mode:"oklab",l:.2104542553*i+.793617785*a-.0040720468*s,a:1.9779984951*i-2.428592205*a+.4505937099*s,b:.0259040371*i+.7827717662*a-.808675766*s};return r!==void 0&&(l.alpha=r),l},Ql=t=>{let e=v1(ro(t));return t.r===t.b&&t.b===t.g&&(e.a=e.b=0),e},Qo=({l:t,a:e,b:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=Math.pow(t*.9999999984505198+.39633779217376786*e+.2158037580607588*n,3),a=Math.pow(t*1.0000000088817609-.10556134232365635*e-.06385417477170591*n,3),s=Math.pow(t*1.0000000546724108-.08948418209496575*e-1.2914855378640917*n,3),l={mode:"lrgb",r:4.076741661347994*i-3.307711590408193*a+.230969928729428*s,g:-1.2684380040921763*i+2.6097574006633715*a-.3413193963102197*s,b:-.004196086541837188*i-.7034186144594493*a+1.7076147009309444*s};return r!==void 0&&(l.alpha=r),l},Jl=t=>io(Qo(t));function Sc(t){const r=1.170873786407767;return .5*(r*t-.206+Math.sqrt((r*t-.206)*(r*t-.206)+4*.03*r*t))}function Ks(t){return(t*t+.206*t)/(1.170873786407767*(t+.03))}function Bb(t,e){let n,r,i,a,s,l,u,c;-1.88170328*t-.80936493*e>1?(n=1.19086277,r=1.76576728,i=.59662641,a=.75515197,s=.56771245,l=4.0767416621,u=-3.3077115913,c=.2309699292):1.81444104*t-1.19445276*e>1?(n=.73956515,r=-.45954404,i=.08285427,a=.1254107,s=.14503204,l=-1.2684380046,u=2.6097574011,c=-.3413193965):(n=1.35733652,r=-.00915799,i=-1.1513021,a=-.50559606,s=.00692167,l=-.0041960863,u=-.7034186147,c=1.707614701);let f=n+r*t+i*e+a*t*t+s*t*e,h=.3963377774*t+.2158037573*e,d=-.1055613458*t-.0638541728*e,g=-.0894841775*t-1.291485548*e;{let m=1+f*h,v=1+f*d,w=1+f*g,b=m*m*m,S=v*v*v,A=w*w*w,N=3*h*m*m,I=3*d*v*v,T=3*g*w*w,L=6*h*h*m,C=6*d*d*v,_=6*g*g*w,M=l*b+u*S+c*A,k=l*N+u*I+c*T,p=l*L+u*C+c*_;f=f-M*k/(k*k-.5*M*p)}return f}function Tf(t,e){let n=Bb(t,e),r=Qo({l:1,a:n*t,b:n*e}),i=Math.cbrt(1/Math.max(r.r,r.g,r.b)),a=i*n;return[i,a]}function Yb(t,e,n,r,i,a=null){a||(a=Tf(t,e));let s;if((n-i)*a[1]-(a[0]-i)*r<=0)s=a[1]*i/(r*a[0]+a[1]*(i-n));else{s=a[1]*(i-1)/(r*(a[0]-1)+a[1]*(i-n));{let l=n-i,u=r,c=.3963377774*t+.2158037573*e,f=-.1055613458*t-.0638541728*e,h=-.0894841775*t-1.291485548*e,d=l+u*c,g=l+u*f,m=l+u*h;{let v=i*(1-s)+s*n,w=s*r,b=v+w*c,S=v+w*f,A=v+w*h,N=b*b*b,I=S*S*S,T=A*A*A,L=3*d*b*b,C=3*g*S*S,_=3*m*A*A,M=6*d*d*b,k=6*g*g*S,p=6*m*m*A,R=4.0767416621*N-3.3077115913*I+.2309699292*T-1,z=4.0767416621*L-3.3077115913*C+.2309699292*_,D=4.0767416621*M-3.3077115913*k+.2309699292*p,W=z/(z*z-.5*R*D),B=-R*W,H=-1.2684380046*N+2.6097574011*I-.3413193965*T-1,$=-1.2684380046*L+2.6097574011*C-.3413193965*_,oe=-1.2684380046*M+2.6097574011*k-.3413193965*p,ye=$/($*$-.5*H*oe),ge=-H*ye,Ee=-.0041960863*N-.7034186147*I+1.707614701*T-1,xe=-.0041960863*L-.7034186147*C+1.707614701*_,te=-.0041960863*M-.7034186147*k+1.707614701*p,ke=xe/(xe*xe-.5*Ee*te),Se=-Ee*ke;B=W>=0?B:1e6,ge=ye>=0?ge:1e6,Se=ke>=0?Se:1e6,s+=Math.min(B,Math.min(ge,Se))}}}return s}function Cf(t,e,n=null){n||(n=Tf(t,e));let r=n[0],i=n[1];return[i/r,i/(1-r)]}function _1(t,e,n){let r=Tf(e,n),i=Yb(e,n,t,1,t,r),a=Cf(e,n,r),s=.11516993+1/(7.4477897+4.1590124*n+e*(-2.19557347+1.75198401*n+e*(-2.13704948-10.02301043*n+e*(-4.24894561+5.38770819*n+4.69891013*e)))),l=.11239642+1/(1.6132032-.68124379*n+e*(.40370612+.90148123*n+e*(-.27087943+.6122399*n+e*(.00299215-.45399568*n-.14661872*e)))),u=i/Math.min(t*a[0],(1-t)*a[1]),c=t*s,f=(1-t)*l,h=.9*u*Math.sqrt(Math.sqrt(1/(1/(c*c*c*c)+1/(f*f*f*f))));return c=t*.4,f=(1-t)*.8,[Math.sqrt(1/(1/(c*c)+1/(f*f))),h,i]}function Gh(t){const e=t.l!==void 0?t.l:0,n=t.a!==void 0?t.a:0,r=t.b!==void 0?t.b:0,i={mode:"okhsl",l:Sc(e)};t.alpha!==void 0&&(i.alpha=t.alpha);let a=Math.sqrt(n*n+r*r);if(!a)return i.s=0,i;let[s,l,u]=_1(e,n/a,r/a),c;if(a<l){let f=0,h=.8*s,d=1-h/l;c=(a-f)/(h+d*(a-f))*.8}else{let f=l,h=.2*l*l*1.25*1.25/s,d=1-h/(u-l);c=.8+.2*((a-f)/(h+d*(a-f)))}return c&&(i.s=c,i.h=pn(Math.atan2(r,n)*180/Math.PI)),i}function Xh(t){let e=t.h!==void 0?t.h:0,n=t.s!==void 0?t.s:0,r=t.l!==void 0?t.l:0;const i={mode:"oklab",l:Ks(r)};if(t.alpha!==void 0&&(i.alpha=t.alpha),!n||r===1)return i.a=i.b=0,i;let a=Math.cos(e/180*Math.PI),s=Math.sin(e/180*Math.PI),[l,u,c]=_1(i.l,a,s),f,h,d,g;n<.8?(f=1.25*n,h=0,d=.8*l,g=1-d/u):(f=5*(n-.8),h=u,d=.2*u*u*1.25*1.25/l,g=1-d/(c-u));let m=h+f*d/(1-g*f);return i.a=m*a,i.b=m*s,i}const Hb={...t1,mode:"okhsl",channels:["h","s","l","alpha"],parse:["--okhsl"],serialize:"--okhsl",fromMode:{oklab:Gh,rgb:t=>Gh(Ql(t))},toMode:{oklab:Xh,rgb:t=>Jl(Xh(t))}};function Vh(t){let e=t.l!==void 0?t.l:0,n=t.a!==void 0?t.a:0,r=t.b!==void 0?t.b:0,i=Math.sqrt(n*n+r*r),a=i?n/i:1,s=i?r/i:1,[l,u]=Cf(a,s),c=.5,f=1-c/l,h=u/(i+e*u),d=h*e,g=h*i,m=Ks(d),v=g*m/d,w=Qo({l:m,a:a*v,b:s*v}),b=Math.cbrt(1/Math.max(w.r,w.g,w.b,0));e=e/b,i=i/b*Sc(e)/e,e=Sc(e);const S={mode:"okhsv",s:i?(c+u)*g/(u*c+u*f*g):0,v:e?e/d:0};return S.s&&(S.h=pn(Math.atan2(r,n)*180/Math.PI)),t.alpha!==void 0&&(S.alpha=t.alpha),S}function Zh(t){const e={mode:"oklab"};t.alpha!==void 0&&(e.alpha=t.alpha);const n=t.h!==void 0?t.h:0,r=t.s!==void 0?t.s:0,i=t.v!==void 0?t.v:0,a=Math.cos(n/180*Math.PI),s=Math.sin(n/180*Math.PI),[l,u]=Cf(a,s),c=.5,f=1-c/l,h=1-r*c/(c+u-u*f*r),d=r*u*c/(c+u-u*f*r),g=Ks(h),m=d*g/h,v=Qo({l:g,a:a*m,b:s*m}),w=Math.cbrt(1/Math.max(v.r,v.g,v.b,0)),b=Ks(i*h),S=d*b/h;return e.l=b*w,e.a=S*a*w,e.b=S*s*w,e}const qb={...i1,mode:"okhsv",channels:["h","s","v","alpha"],parse:["--okhsv"],serialize:"--okhsv",fromMode:{oklab:Vh,rgb:t=>Vh(Ql(t))},toMode:{oklab:Zh,rgb:t=>Jl(Zh(t))}};function Ub(t,e){if(!e||e[0]!=="oklab")return;const n={mode:"oklab"},[,r,i,a,s]=e;if(!(r.type===We.Hue||i.type===We.Hue||a.type===We.Hue))return r.type!==We.None&&(n.l=Math.min(Math.max(0,r.type===We.Number?r.value:r.value/100),1)),i.type!==We.None&&(n.a=i.type===We.Number?i.value:i.value*.4/100),a.type!==We.None&&(n.b=a.type===We.Number?a.value:a.value*.4/100),s.type!==We.None&&(n.alpha=Math.min(1,Math.max(0,s.type===We.Number?s.value:s.value/100))),n}const Gb={...Sf,mode:"oklab",toMode:{lrgb:Qo,rgb:Jl},fromMode:{lrgb:v1,rgb:Ql},ranges:{l:[0,1],a:[-.4,.4],b:[-.4,.4]},parse:[Ub],serialize:t=>`oklab(${t.l!==void 0?t.l:"none"} ${t.a!==void 0?t.a:"none"} ${t.b!==void 0?t.b:"none"}${t.alpha<1?` / ${t.alpha}`:""})`};function Xb(t,e){if(!e||e[0]!=="oklch")return;const n={mode:"oklch"},[,r,i,a,s]=e;if(r.type!==We.None){if(r.type===We.Hue)return;n.l=Math.min(Math.max(0,r.type===We.Number?r.value:r.value/100),1)}if(i.type!==We.None&&(n.c=Math.max(0,i.type===We.Number?i.value:i.value*.4/100)),a.type!==We.None){if(a.type===We.Percentage)return;n.h=a.value}return s.type!==We.None&&(n.alpha=Math.min(1,Math.max(0,s.type===We.Number?s.value:s.value/100))),n}const Vb={...Af,mode:"oklch",toMode:{oklab:t=>Wi(t,"oklab"),rgb:t=>Jl(Wi(t,"oklab"))},fromMode:{rgb:t=>Li(Ql(t),"oklch"),oklab:t=>Li(t,"oklch")},parse:[Xb],serialize:t=>`oklch(${t.l!==void 0?t.l:"none"} ${t.c!==void 0?t.c:"none"} ${t.h!==void 0?t.h:"none"}${t.alpha<1?` / ${t.alpha}`:""})`,ranges:{l:[0,1],c:[0,.4],h:[0,360]}},Kh=t=>{let{r:e,g:n,b:r,alpha:i}=ro(t),a={mode:"xyz65",x:.486570948648216*e+.265667693169093*n+.1982172852343625*r,y:.2289745640697487*e+.6917385218365062*n+.079286914093745*r,z:0*e+.0451133818589026*n+1.043944368900976*r};return i!==void 0&&(a.alpha=i),a},Qh=({x:t,y:e,z:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i=io({r:t*2.4934969119414263-e*.9313836179191242-.402710784450717*n,g:t*-.8294889695615749+e*1.7626640603183465+.0236246858419436*n,b:t*.0358458302437845-e*.0761723892680418+.9568845240076871*n},"p3");return r!==void 0&&(i.alpha=r),i},Zb={...no,mode:"p3",parse:["display-p3"],serialize:"display-p3",fromMode:{rgb:t=>Qh(oa(t)),xyz65:Qh},toMode:{rgb:t=>sa(Kh(t)),xyz65:Kh}},Fu=t=>{let e=Math.abs(t);return e>=1/512?Math.sign(t)*Math.pow(e,1/1.8):16*t},Jh=({x:t,y:e,z:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i={mode:"prophoto",r:Fu(t*1.3457868816471585-e*.2555720873797946-.0511018649755453*n),g:Fu(t*-.5446307051249019+e*1.5082477428451466+.0205274474364214*n),b:Fu(t*0+e*0+1.2119675456389452*n)};return r!==void 0&&(i.alpha=r),i},Bu=(t=0)=>{let e=Math.abs(t);return e>=16/512?Math.sign(t)*Math.pow(e,1.8):t/16},$h=t=>{let e=Bu(t.r),n=Bu(t.g),r=Bu(t.b),i={mode:"xyz50",x:.7977666449006423*e+.1351812974005331*n+.0313477341283922*r,y:.2880748288194013*e+.7118352342418731*n+899369387256e-16*r,z:0*e+0*n+.8251046025104602*r};return t.alpha!==void 0&&(i.alpha=t.alpha),i},Kb={...no,mode:"prophoto",parse:["prophoto-rgb"],serialize:"prophoto-rgb",fromMode:{xyz50:Jh,rgb:t=>Jh(Ko(t))},toMode:{xyz50:$h,rgb:t=>Zo($h(t))}},ed=1.09929682680944,Qb=.018053968510807,Yu=t=>{const e=Math.abs(t);return e>Qb?(Math.sign(t)||1)*(ed*Math.pow(e,.45)-(ed-1)):4.5*t},td=({x:t,y:e,z:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);let i={mode:"rec2020",r:Yu(t*1.7166511879712683-e*.3556707837763925-.2533662813736599*n),g:Yu(t*-.6666843518324893+e*1.6164812366349395+.0157685458139111*n),b:Yu(t*.0176398574453108-e*.0427706132578085+.9421031212354739*n)};return r!==void 0&&(i.alpha=r),i},nd=1.09929682680944,Jb=.018053968510807,Hu=(t=0)=>{let e=Math.abs(t);return e<Jb*4.5?t/4.5:(Math.sign(t)||1)*Math.pow((e+nd-1)/nd,1/.45)},rd=t=>{let e=Hu(t.r),n=Hu(t.g),r=Hu(t.b),i={mode:"xyz65",x:.6369580483012911*e+.1446169035862083*n+.1688809751641721*r,y:.262700212011267*e+.6779980715188708*n+.059301716469862*r,z:0*e+.0280726930490874*n+1.0609850577107909*r};return t.alpha!==void 0&&(i.alpha=t.alpha),i},$b={...no,mode:"rec2020",fromMode:{xyz65:td,rgb:t=>td(oa(t))},toMode:{xyz65:rd,rgb:t=>sa(rd(t))},parse:["rec2020"],serialize:"rec2020"},ea=.0037930732552754493,x1=Math.cbrt(ea),qu=t=>Math.cbrt(t)-x1,ek=t=>{const{r:e,g:n,b:r,alpha:i}=ro(t),a=qu(.3*e+.622*n+.078*r+ea),s=qu(.23*e+.692*n+.078*r+ea),l=qu(.2434226892454782*e+.2047674442449682*n+.5518098665095535*r+ea),u={mode:"xyb",x:(a-s)/2,y:(a+s)/2,b:l-(a+s)/2};return i!==void 0&&(u.alpha=i),u},Uu=t=>Math.pow(t+x1,3),tk=({x:t,y:e,b:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);const i=Uu(t+e)-ea,a=Uu(e-t)-ea,s=Uu(n+e)-ea,l=io({r:11.031566904639861*i-9.866943908131562*a-.16462299650829934*s,g:-3.2541473810744237*i+4.418770377582723*a-.16462299650829934*s,b:-3.6588512867136815*i+2.7129230459360922*a+1.9459282407775895*s});return r!==void 0&&(l.alpha=r),l},nk={mode:"xyb",channels:["x","y","b","alpha"],parse:["--xyb"],serialize:"--xyb",toMode:{rgb:tk},fromMode:{rgb:ek},ranges:{x:[-.0154,.0281],y:[0,.8453],b:[-.2778,.388]},interpolate:{x:Ye,y:Ye,b:Ye,alpha:{use:Ye,fixup:hn}}},rk={mode:"xyz50",parse:["xyz-d50"],serialize:"xyz-d50",toMode:{rgb:Zo,lab:pf},fromMode:{rgb:Ko,lab:Mf},channels:["x","y","z","alpha"],ranges:{x:[0,.964],y:[0,.999],z:[0,.825]},interpolate:{x:Ye,y:Ye,z:Ye,alpha:{use:Ye,fixup:hn}}},ik=t=>{let{x:e,y:n,z:r,alpha:i}=t;e===void 0&&(e=0),n===void 0&&(n=0),r===void 0&&(r=0);let a={mode:"xyz50",x:1.0479298208405488*e+.0229467933410191*n-.0501922295431356*r,y:.0296278156881593*e+.990434484573249*n-.0170738250293851*r,z:-.0092430581525912*e+.0150551448965779*n+.7518742899580008*r};return i!==void 0&&(a.alpha=i),a},ak=t=>{let{x:e,y:n,z:r,alpha:i}=t;e===void 0&&(e=0),n===void 0&&(n=0),r===void 0&&(r=0);let a={mode:"xyz65",x:.9554734527042182*e-.0230985368742614*n+.0632593086610217*r,y:-.0283697069632081*e+1.0099954580058226*n+.021041398966943*r,z:.0123140016883199*e-.0205076964334779*n+1.3303659366080753*r};return i!==void 0&&(a.alpha=i),a},ok={mode:"xyz65",toMode:{rgb:sa,xyz50:ik},fromMode:{rgb:oa,xyz50:ak},ranges:{x:[0,.95],y:[0,1],z:[0,1.088]},channels:["x","y","z","alpha"],parse:["xyz","xyz-d65"],serialize:"xyz-d65",interpolate:{x:Ye,y:Ye,z:Ye,alpha:{use:Ye,fixup:hn}}},sk=({r:t,g:e,b:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);const i={mode:"yiq",y:.29889531*t+.58662247*e+.11448223*n,i:.59597799*t-.2741761*e-.32180189*n,q:.21147017*t-.52261711*e+.31114694*n};return r!==void 0&&(i.alpha=r),i},lk=({y:t,i:e,q:n,alpha:r})=>{t===void 0&&(t=0),e===void 0&&(e=0),n===void 0&&(n=0);const i={mode:"rgb",r:t+.95608445*e+.6208885*n,g:t-.27137664*e-.6486059*n,b:t-1.10561724*e+1.70250126*n};return r!==void 0&&(i.alpha=r),i},uk={mode:"yiq",toMode:{rgb:lk},fromMode:{rgb:sk},channels:["y","i","q","alpha"],parse:["--yiq"],serialize:"--yiq",ranges:{i:[-.595,.595],q:[-.522,.522]},interpolate:{y:Ye,i:Ye,q:Ye,alpha:{use:Ye,fixup:hn}}};Ht(K2);Ht(rb);Ht(ib);Ht(ab);Ht(lb);Ht(t1);Ht(i1);Ht(_b);Ht(xb);Ht(Mb);Ht(pb);Ht(Sf);Ht(Ab);Ht(Af);Ht(Cb);Ht(zb);Ht(jb);Ht(Fb);Ht(Hb);Ht(qb);Ht(Gb);Ht(Vb);Ht(Zb);Ht(Kb);Ht($b);Ht(no);Ht(nk);Ht(rk);Ht(ok);Ht(uk);[...Co(100,1e3,100)];const ck=my({extend:{classGroups:{shadow:["shadow-border-l","shadow-border-r","shadow-border-t","shadow-border-b","elevation-none",...Co(1,25).map(t=>`elevation-${t}`)]}}}),Le=(...t)=>ck(yy(...t));class fk{constructor(){dt(this,"smScreen",this.width(640));dt(this,"mdScreen",this.width(768));dt(this,"lgScreen",this.width(1024));dt(this,"xlScreen",this.width(1280));dt(this,"xxlScreen",this.width(1536));dt(this,"screen",new Tr("screen and (min-width: 0)"));dt(this,"print",new Tr("print and (min-width: 0)"));dt(this,"dark",new Tr("(prefers-color-scheme: dark)"));dt(this,"light",new Tr("(prefers-color-scheme: light)"));dt(this,"motion",new Tr("(prefers-reduced-motion: no-preference)"));dt(this,"motionReduce",new Tr("(prefers-reduced-motion: reduce)"));dt(this,"landscape",new Tr("(orientation: landscape)"));dt(this,"portrait",new Tr("(orientation: portrait)"))}width(e){return new Tr(`(min-width: ${e}px)`)}height(e){return new Tr(`(min-height: ${e}px)`)}}var Dt;(function(t){t[t.Custom=1]="Custom",t[t.Day=10]="Day",t[t.DayTime=11]="DayTime",t[t.TimeOnly=15]="TimeOnly",t[t.Week=20]="Week",t[t.WeekSun=21]="WeekSun",t[t.WeekMon=22]="WeekMon",t[t.WeekTue=23]="WeekTue",t[t.WeekWed=24]="WeekWed",t[t.WeekThu=25]="WeekThu",t[t.WeekFri=26]="WeekFri",t[t.WeekSat=27]="WeekSat",t[t.Month=30]="Month",t[t.MonthYear=31]="MonthYear",t[t.Quarter=40]="Quarter",t[t.CalendarYear=50]="CalendarYear",t[t.FiscalYearOctober=60]="FiscalYearOctober",t[t.BiWeek1=70]="BiWeek1",t[t.BiWeek1Sun=71]="BiWeek1Sun",t[t.BiWeek1Mon=72]="BiWeek1Mon",t[t.BiWeek1Tue=73]="BiWeek1Tue",t[t.BiWeek1Wed=74]="BiWeek1Wed",t[t.BiWeek1Thu=75]="BiWeek1Thu",t[t.BiWeek1Fri=76]="BiWeek1Fri",t[t.BiWeek1Sat=77]="BiWeek1Sat",t[t.BiWeek2=80]="BiWeek2",t[t.BiWeek2Sun=81]="BiWeek2Sun",t[t.BiWeek2Mon=82]="BiWeek2Mon",t[t.BiWeek2Tue=83]="BiWeek2Tue",t[t.BiWeek2Wed=84]="BiWeek2Wed",t[t.BiWeek2Thu=85]="BiWeek2Thu",t[t.BiWeek2Fri=86]="BiWeek2Fri",t[t.BiWeek2Sat=87]="BiWeek2Sat"})(Dt||(Dt={}));Dt.Custom+"",Dt.Day+"",Dt.DayTime+"",Dt.TimeOnly+"",Dt.WeekSun+"",Dt.WeekMon+"",Dt.WeekTue+"",Dt.WeekWed+"",Dt.WeekThu+"",Dt.WeekFri+"",Dt.WeekSat+"",Dt.Week+"",Dt.Month+"",Dt.MonthYear+"",Dt.Quarter+"",Dt.CalendarYear+"",Dt.FiscalYearOctober+"",Dt.BiWeek1Sun+"",Dt.BiWeek1Mon+"",Dt.BiWeek1Tue+"",Dt.BiWeek1Wed+"",Dt.BiWeek1Thu+"",Dt.BiWeek1Fri+"",Dt.BiWeek1Sat+"",Dt.BiWeek1+"",Dt.BiWeek2Sun+"",Dt.BiWeek2Mon+"",Dt.BiWeek2Tue+"",Dt.BiWeek2Wed+"",Dt.BiWeek2Thu+"",Dt.BiWeek2Fri+"",Dt.BiWeek2Sat+"",Dt.BiWeek2+"";var Qs;(function(t){t[t.Sunday=0]="Sunday",t[t.Monday=1]="Monday",t[t.Tuesday=2]="Tuesday",t[t.Wednesday=3]="Wednesday",t[t.Thursday=4]="Thursday",t[t.Friday=5]="Friday",t[t.Saturday=6]="Saturday"})(Qs||(Qs={}));var tt;(function(t){t.Year_numeric="yyy",t.Year_2Digit="yy",t.Month_long="MMMM",t.Month_short="MMM",t.Month_2Digit="MM",t.Month_numeric="M",t.Hour_numeric="h",t.Hour_2Digit="hh",t.Hour_wAMPM="a",t.Hour_woAMPM="aaaaaa",t.Minute_numeric="m",t.Minute_2Digit="mm",t.Second_numeric="s",t.Second_2Digit="ss",t.MiliSecond_3="SSS",t.DayOfMonth_numeric="d",t.DayOfMonth_2Digit="dd",t.DayOfMonth_withOrdinal="do",t.DayOfWeek_narrow="eeeee",t.DayOfWeek_long="eeee",t.DayOfWeek_short="eee"})(tt||(tt={}));function hk(t){var r;if(!t)return Qs.Sunday;const e=new Intl.Locale(t),n=e.weekInfo??((r=e.getWeekInfo)==null?void 0:r.call(e));return((n==null?void 0:n.firstDay)??0)%7}const dk={locale:"en",dictionary:{Ok:"Ok",Cancel:"Cancel",Date:{Start:"Start",End:"End",Empty:"Empty",Day:"Day",DayTime:"Day Time",Time:"Time",Week:"Week",BiWeek:"Bi-Week",Month:"Month",Quarter:"Quarter",CalendarYear:"Calendar Year",FiscalYearOct:"Fiscal Year (Oct)",PeriodDay:{Current:"Today",Last:"Yesterday",LastX:"Last {0} days"},PeriodWeek:{Current:"This week",Last:"Last week",LastX:"Last {0} weeks"},PeriodBiWeek:{Current:"This bi-week",Last:"Last bi-week",LastX:"Last {0} bi-weeks"},PeriodMonth:{Current:"This month",Last:"Last month",LastX:"Last {0} months"},PeriodQuarter:{Current:"This quarter",Last:"Last quarter",LastX:"Last {0} quarters"},PeriodQuarterSameLastyear:"Same quarter last year",PeriodYear:{Current:"This year",Last:"Last year",LastX:"Last {0} years"},PeriodFiscalYear:{Current:"This fiscal year",Last:"Last fiscal year",LastX:"Last {0} fiscal years"}}},formats:{numbers:{defaults:{currency:"USD",fractionDigits:2,currencyDisplay:"symbol"}},dates:{baseParsing:"MM/dd/yyyy",weekStartsOn:Qs.Sunday,ordinalSuffixes:{one:"st",two:"nd",few:"rd",other:"th"},presets:{day:{short:[tt.DayOfMonth_numeric,tt.Month_numeric],default:[tt.DayOfMonth_numeric,tt.Month_numeric,tt.Year_numeric],long:[tt.DayOfMonth_numeric,tt.Month_short,tt.Year_numeric]},dayTime:{short:[tt.DayOfMonth_numeric,tt.Month_numeric,tt.Year_numeric,tt.Hour_numeric,tt.Minute_numeric],default:[tt.DayOfMonth_numeric,tt.Month_numeric,tt.Year_numeric,tt.Hour_2Digit,tt.Minute_2Digit],long:[tt.DayOfMonth_numeric,tt.Month_numeric,tt.Year_numeric,tt.Hour_2Digit,tt.Minute_2Digit,tt.Second_2Digit]},timeOnly:{short:[tt.Hour_numeric,tt.Minute_numeric],default:[tt.Hour_2Digit,tt.Minute_2Digit,tt.Second_2Digit],long:[tt.Hour_2Digit,tt.Minute_2Digit,tt.Second_2Digit,tt.MiliSecond_3]},week:{short:[tt.DayOfMonth_numeric,tt.Month_numeric],default:[tt.DayOfMonth_numeric,tt.Month_numeric,tt.Year_numeric],long:[tt.DayOfMonth_numeric,tt.Month_numeric,tt.Year_numeric]},month:{short:tt.Month_short,default:tt.Month_long,long:[tt.Month_long,tt.Year_numeric]},monthsYear:{short:[tt.Month_short,tt.Year_2Digit],default:[tt.Month_long,tt.Year_numeric],long:[tt.Month_long,tt.Year_numeric]},year:{short:tt.Year_2Digit,default:tt.Year_numeric,long:tt.Year_numeric}}}}};function gk(t,e=dk){var n,r,i,a;return(r=(n=t.formats)==null?void 0:n.dates)!=null&&r.ordinalSuffixes&&(t.formats.dates.ordinalSuffixes={one:"",two:"",few:"",other:"",zero:"",many:"",...t.formats.dates.ordinalSuffixes}),((a=(i=t.formats)==null?void 0:i.dates)==null?void 0:a.weekStartsOn)===void 0&&(t=Hs(t,{formats:{dates:{weekStartsOn:hk(t.locale)}}})),Hs(t,e)}gk({locale:"en"});var id;(function(t){t[t.Year=0]="Year",t[t.Day=1]="Day",t[t.Hour=2]="Hour",t[t.Minute=3]="Minute",t[t.Second=4]="Second",t[t.Millisecond=5]="Millisecond"})(id||(id={}));var Fo;class mk{constructor(e){gt(this,Fo);dt(this,"current");_t(this,Fo,e??[]),this.current=new hc(e??[])}clear(){this.current.clear()}reset(){this.clear(),this.addEach(ie(this,Fo))}add(e){this.current.add(e)}addEach(e){for(const n of e)this.current.add(n)}delete(e){this.current.delete(e)}toggle(e){this.current.has(e)?this.current.delete(e):this.current.add(e)}}Fo=new WeakMap;var Bo,Ft;class ad{constructor(e={}){gt(this,Bo);gt(this,Ft);dt(this,"all");dt(this,"single");dt(this,"max");_t(this,Bo,e.initial??[]),_t(this,Ft,new mk(ie(this,Bo))),this.all=e.all??[],this.single=e.single??!1,this.max=e.max}get current(){return this.single?Array.from(ie(this,Ft).current)[0]??null:Array.from(ie(this,Ft).current)}set current(e){if(Array.isArray(e))if(this.max==null||e.length<this.max)ie(this,Ft).clear(),ie(this,Ft).addEach(e);else throw new Error(`Too many values selected.  Current: ${e.length}, max: ${this.max}`);else e!=null?(ie(this,Ft).clear(),ie(this,Ft).add(e)):ie(this,Ft).clear()}isSelected(e){return ie(this,Ft).current.has(e)}isEmpty(){return ie(this,Ft).current.size===0}isAllSelected(){return this.all.every(e=>ie(this,Ft).current.has(e))}isAnySelected(){return this.all.some(e=>ie(this,Ft).current.has(e))}isMaxSelected(){return this.max!=null?ie(this,Ft).current.size>=this.max:!1}isDisabled(e){return!this.isSelected(e)&&this.isMaxSelected()}clear(){ie(this,Ft).clear()}reset(){ie(this,Ft).reset()}toggle(e){if(ie(this,Ft).current.has(e)){const n=[...ie(this,Ft).current];ie(this,Ft).clear(),ie(this,Ft).addEach(n.filter(r=>r!=e))}else if(this.single)ie(this,Ft).clear(),ie(this,Ft).add(e);else if(this.max==null||ie(this,Ft).current.size<this.max)return ie(this,Ft).add(e)}toggleAll(){let e;this.isAllSelected()?e=[...ie(this,Ft).current].filter(n=>!this.all.includes(n)):e=[...ie(this,Ft).current,...this.all],ie(this,Ft).clear(),ie(this,Ft).addEach(e)}}Bo=new WeakMap,Ft=new WeakMap;function Js(t){return Object.prototype.toString.call(t)==="[object Date]"}function Ac(t,e,n,r){if(typeof n=="number"||Js(n)){const i=r-n,a=(n-e)/(t.dt||1/60),s=t.opts.stiffness*i,l=t.opts.damping*a,u=(s-l)*t.inv_mass,c=(a+u)*t.dt;return Math.abs(c)<t.opts.precision&&Math.abs(i)<t.opts.precision?r:(t.settled=!1,Js(n)?new Date(n.getTime()+c):n+c)}else{if(Array.isArray(n))return n.map((i,a)=>Ac(t,e[a],n[a],r[a]));if(typeof n=="object"){const i={};for(const a in n)i[a]=Ac(t,e[a],n[a],r[a]);return i}else throw new Error(`Cannot spring ${typeof n} values`)}}var Vi,Zi,Ki,rr,Di,Qi,Ea,Ji,Yo,Vr,Oa,Wl,b1;const nh=class nh{constructor(e,n={}){gt(this,Wl);gt(this,Vi,Pr(.15));gt(this,Zi,Pr(.8));gt(this,Ki,Pr(.01));gt(this,rr,Pr(void 0));gt(this,Di,Pr(void 0));gt(this,Qi);gt(this,Ea,0);gt(this,Ji,1);gt(this,Yo,0);gt(this,Vr,null);gt(this,Oa,null);ie(this,rr).v=ie(this,Di).v=e,typeof n.stiffness=="number"&&(ie(this,Vi).v=ds(n.stiffness,0,1)),typeof n.damping=="number"&&(ie(this,Zi).v=ds(n.damping,0,1)),typeof n.precision=="number"&&(ie(this,Ki).v=n.precision)}static of(e,n){const r=new nh(e(),n);return tg(()=>{r.set(e())}),r}set(e,n){var i,a;if((i=ie(this,Oa))==null||i.reject(new Error("Aborted")),n!=null&&n.instant||ie(this,rr).v===void 0)return(a=ie(this,Vr))==null||a.abort(),_t(this,Vr,null),Re(ie(this,rr),Re(ie(this,Di),e)),_t(this,Qi,e),Promise.resolve();n!=null&&n.preserveMomentum&&(_t(this,Ji,0),_t(this,Yo,n.preserveMomentum));var r=_t(this,Oa,ly());return r.promise.catch(Rt),ls(this,Wl,b1).call(this,e).then(()=>{r===ie(this,Oa)&&r.resolve(void 0)}),r.promise}get current(){return o(ie(this,rr))}get damping(){return o(ie(this,Zi))}set damping(e){Re(ie(this,Zi),ds(e,0,1))}get precision(){return o(ie(this,Ki))}set precision(e){Re(ie(this,Ki),e)}get stiffness(){return o(ie(this,Vi))}set stiffness(e){Re(ie(this,Vi),ds(e,0,1))}get target(){return o(ie(this,Di))}set target(e){this.set(e)}};Vi=new WeakMap,Zi=new WeakMap,Ki=new WeakMap,rr=new WeakMap,Di=new WeakMap,Qi=new WeakMap,Ea=new WeakMap,Ji=new WeakMap,Yo=new WeakMap,Vr=new WeakMap,Oa=new WeakMap,Wl=new WeakSet,b1=function(e){var r;if(Re(ie(this,Di),e),(r=ie(this,rr)).v??(r.v=e),ie(this,Qi)??_t(this,Qi,ie(this,rr).v),!ie(this,Vr)){_t(this,Ea,og.now());var n=1e3/(ie(this,Yo)*60);ie(this,Vr)??_t(this,Vr,sg(i=>{_t(this,Ji,Math.min(ie(this,Ji)+n,1));const a=Math.min(i-ie(this,Ea),1e3/30),s={inv_mass:ie(this,Ji),opts:{stiffness:ie(this,Vi).v,damping:ie(this,Zi).v,precision:ie(this,Ki).v},settled:!0,dt:a*60/1e3};var l=Ac(s,ie(this,Qi),ie(this,rr).v,ie(this,Di).v);return _t(this,Qi,ie(this,rr).v),_t(this,Ea,i),Re(ie(this,rr),l),s.settled&&_t(this,Vr,null),!s.settled}))}return ie(this,Vr).promise};let Tc=nh;function ds(t,e,n){return Math.max(e,Math.min(n,t))}function Cc(t,e){if(t===e||t!==t)return()=>t;const n=typeof t;if(n!==typeof e||Array.isArray(t)!==Array.isArray(e))throw new Error("Cannot interpolate values of different type");if(Array.isArray(t)){const r=e.map((i,a)=>Cc(t[a],i));return i=>r.map(a=>a(i))}if(n==="object"){if(!t||!e)throw new Error("Object cannot be null");if(Js(t)&&Js(e)){const a=t.getTime(),l=e.getTime()-a;return u=>new Date(a+u*l)}const r=Object.keys(e),i={};return r.forEach(a=>{i[a]=Cc(t[a],e[a])}),a=>{const s={};return r.forEach(l=>{s[l]=i[l](a)}),s}}if(n==="number"){const r=e-t;return i=>t+i*r}return()=>e}var Zr,Na,Ho,$i;const rh=class rh{constructor(e,n={}){gt(this,Zr,Pr(void 0));gt(this,Na,Pr(void 0));gt(this,Ho);gt(this,$i,null);ie(this,Zr).v=ie(this,Na).v=e,_t(this,Ho,n)}static of(e,n){const r=new rh(e(),n);return tg(()=>{r.set(e())}),r}set(e,n){var h;Re(ie(this,Na),e);let{delay:r=0,duration:i=400,easing:a=by,interpolate:s=Cc}={...ie(this,Ho),...n};if(i===0)return(h=ie(this,$i))==null||h.abort(),Re(ie(this,Zr),e),Promise.resolve();const l=og.now()+r;let u,c=!1,f=ie(this,$i);return _t(this,$i,sg(d=>{if(d<l)return!0;if(!c){c=!0;const m=ie(this,Zr).v;u=s(m,e),typeof i=="function"&&(i=i(m,e)),f==null||f.abort()}const g=d-l;return g>i?(Re(ie(this,Zr),e),!1):(Re(ie(this,Zr),u(a(g/i))),!0)})),ie(this,$i).promise}get current(){return o(ie(this,Zr))}get target(){return o(ie(this,Na))}set target(e){this.set(e)}};Zr=new WeakMap,Na=new WeakMap,Ho=new WeakMap,$i=new WeakMap;let Dc=rh;class yk extends Tc{constructor(n,r){super(n,r);dt(this,"type","spring")}}class vk extends Dc{constructor(n,r){super(n,r);dt(this,"type","tween")}}var La,Wa;class _k{constructor(e,n={}){dt(this,"type","none");gt(this,La,Ve(null));gt(this,Wa,Ve(null));Re(ie(this,La),e,!0),Re(ie(this,Wa),e,!0)}set(e,n={}){return Re(ie(this,La),e,!0),Re(ie(this,Wa),e,!0),Promise.resolve()}get current(){return o(ie(this,La))}get target(){return o(ie(this,Wa))}set target(e){this.set(e)}}La=new WeakMap,Wa=new WeakMap;function xk(t,e,n){n.controlled||Ir(()=>{t.set(e())})}function on(t,e,n,r={}){const i=Pi(n),a=i.type==="spring"?new yk(t,i.options):i.type==="tween"?new vk(t,i.options):new _k(t);return xk(a,e,r),a}function Pc(t,e){return on(t,()=>t,e,{controlled:!0})}function od(){let t=0,e=Ve(!1);function n(r){if(t+=1,!r){Re(e,!1);return}let i=t;Re(e,!0),r.then(()=>{i===t&&Re(e,!1)}).catch(()=>{})}return{handle:n,get current(){return o(e)}}}function Jo(t){const e=Pi(t);if(e.type==="tween")return e}function Pi(t,e){if(typeof t=="object"&&"type"in t&&"options"in t)return typeof t.options=="object"?t:{type:t.type,options:{}};if(t===void 0)return{type:"none",options:{}};if(typeof t=="string")return t==="spring"?{type:"spring",options:{}}:t==="tween"?{type:"tween",options:{}}:{type:"none",options:{}};if(typeof t=="object"&&"type"in t)if(t.type==="spring"){const{type:n,...r}=t;return{type:"spring",options:r}}else if(t.type==="tween"){const{type:n,...r}=t;return{type:"tween",options:r}}else return{type:"none",options:{}};if(e){const n=t[e];if(n!==void 0)return Pi(n)}return{type:"none",options:{}}}function Yt(t){return Array.isArray(t)?e=>t.map(n=>Yt(n)(e)):typeof t=="function"?t:typeof t=="string"||typeof t=="number"?e=>Lg(e,t):e=>e}function Ta(t){return t==null?[]:Array.isArray(t)?t:"nodes"in t?t.nodes:"descendants"in t?t.descendants():[]}function bk(t=!0,e=!1){if(t!==!1)return{top:t===!0||t==="y"?4:0,left:t===!0||t==="y"?20:0,bottom:(t===!0||t==="x"?20:0)+(e===!0?32:0),right:t===!0||t==="x"?4:0}}function $l(t,e,n){return t.find(r=>{var i,a;return((i=n(r))==null?void 0:i.valueOf())===((a=n(e))==null?void 0:a.valueOf())})}function xn(t){return function(){return t}}const sd=Math.abs,Cn=Math.atan2,Hi=Math.cos,kk=Math.max,Gu=Math.min,Cr=Math.sin,ka=Math.sqrt,qn=1e-12,Ro=Math.PI,$s=Ro/2,wk=2*Ro;function Mk(t){return t>1?0:t<-1?Ro:Math.acos(t)}function ld(t){return t>=1?$s:t<=-1?-$s:Math.asin(t)}const Rc=Math.PI,Ec=2*Rc,Ui=1e-6,pk=Ec-Ui;function k1(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=arguments[e]+t[e]}function Sk(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return k1;const n=10**e;return function(r){this._+=r[0];for(let i=1,a=r.length;i<a;++i)this._+=Math.round(arguments[i]*n)/n+r[i]}}let Df=class{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=e==null?k1:Sk(e)}moveTo(e,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+n}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,n){this._append`L${this._x1=+e},${this._y1=+n}`}quadraticCurveTo(e,n,r,i){this._append`Q${+e},${+n},${this._x1=+r},${this._y1=+i}`}bezierCurveTo(e,n,r,i,a,s){this._append`C${+e},${+n},${+r},${+i},${this._x1=+a},${this._y1=+s}`}arcTo(e,n,r,i,a){if(e=+e,n=+n,r=+r,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let s=this._x1,l=this._y1,u=r-e,c=i-n,f=s-e,h=l-n,d=f*f+h*h;if(this._x1===null)this._append`M${this._x1=e},${this._y1=n}`;else if(d>Ui)if(!(Math.abs(h*u-c*f)>Ui)||!a)this._append`L${this._x1=e},${this._y1=n}`;else{let g=r-s,m=i-l,v=u*u+c*c,w=g*g+m*m,b=Math.sqrt(v),S=Math.sqrt(d),A=a*Math.tan((Rc-Math.acos((v+d-w)/(2*b*S)))/2),N=A/S,I=A/b;Math.abs(N-1)>Ui&&this._append`L${e+N*f},${n+N*h}`,this._append`A${a},${a},0,0,${+(h*g>f*m)},${this._x1=e+I*u},${this._y1=n+I*c}`}}arc(e,n,r,i,a,s){if(e=+e,n=+n,r=+r,s=!!s,r<0)throw new Error(`negative radius: ${r}`);let l=r*Math.cos(i),u=r*Math.sin(i),c=e+l,f=n+u,h=1^s,d=s?i-a:a-i;this._x1===null?this._append`M${c},${f}`:(Math.abs(this._x1-c)>Ui||Math.abs(this._y1-f)>Ui)&&this._append`L${c},${f}`,r&&(d<0&&(d=d%Ec+Ec),d>pk?this._append`A${r},${r},0,1,${h},${e-l},${n-u}A${r},${r},0,1,${h},${this._x1=c},${this._y1=f}`:d>Ui&&this._append`A${r},${r},0,${+(d>=Rc)},${h},${this._x1=e+r*Math.cos(a)},${this._y1=n+r*Math.sin(a)}`)}rect(e,n,r,i){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+n}h${r=+r}v${+i}h${-r}Z`}toString(){return this._}};function w1(){return new Df}w1.prototype=Df.prototype;function M1(t){let e=3;return t.digits=function(n){if(!arguments.length)return e;if(n==null)e=null;else{const r=Math.floor(n);if(!(r>=0))throw new RangeError(`invalid digits: ${n}`);e=r}return t},()=>new Df(e)}function Ak(t){return t.innerRadius}function Tk(t){return t.outerRadius}function Ck(t){return t.startAngle}function Dk(t){return t.endAngle}function Pk(t){return t&&t.padAngle}function Rk(t,e,n,r,i,a,s,l){var u=n-t,c=r-e,f=s-i,h=l-a,d=h*u-f*c;if(!(d*d<qn))return d=(f*(e-a)-h*(t-i))/d,[t+d*u,e+d*c]}function gs(t,e,n,r,i,a,s){var l=t-n,u=e-r,c=(s?a:-a)/ka(l*l+u*u),f=c*u,h=-c*l,d=t+f,g=e+h,m=n+f,v=r+h,w=(d+m)/2,b=(g+v)/2,S=m-d,A=v-g,N=S*S+A*A,I=i-a,T=d*v-m*g,L=(A<0?-1:1)*ka(kk(0,I*I*N-T*T)),C=(T*A-S*L)/N,_=(-T*S-A*L)/N,M=(T*A+S*L)/N,k=(-T*S+A*L)/N,p=C-w,R=_-b,z=M-w,D=k-b;return p*p+R*R>z*z+D*D&&(C=M,_=k),{cx:C,cy:_,x01:-f,y01:-h,x11:C*(i/I-1),y11:_*(i/I-1)}}function Eo(){var t=Ak,e=Tk,n=xn(0),r=null,i=Ck,a=Dk,s=Pk,l=null,u=M1(c);function c(){var f,h,d=+t.apply(this,arguments),g=+e.apply(this,arguments),m=i.apply(this,arguments)-$s,v=a.apply(this,arguments)-$s,w=sd(v-m),b=v>m;if(l||(l=f=u()),g<d&&(h=g,g=d,d=h),!(g>qn))l.moveTo(0,0);else if(w>wk-qn)l.moveTo(g*Hi(m),g*Cr(m)),l.arc(0,0,g,m,v,!b),d>qn&&(l.moveTo(d*Hi(v),d*Cr(v)),l.arc(0,0,d,v,m,b));else{var S=m,A=v,N=m,I=v,T=w,L=w,C=s.apply(this,arguments)/2,_=C>qn&&(r?+r.apply(this,arguments):ka(d*d+g*g)),M=Gu(sd(g-d)/2,+n.apply(this,arguments)),k=M,p=M,R,z;if(_>qn){var D=ld(_/d*Cr(C)),W=ld(_/g*Cr(C));(T-=D*2)>qn?(D*=b?1:-1,N+=D,I-=D):(T=0,N=I=(m+v)/2),(L-=W*2)>qn?(W*=b?1:-1,S+=W,A-=W):(L=0,S=A=(m+v)/2)}var B=g*Hi(S),H=g*Cr(S),$=d*Hi(I),oe=d*Cr(I);if(M>qn){var ye=g*Hi(A),ge=g*Cr(A),Ee=d*Hi(N),xe=d*Cr(N),te;if(w<Ro)if(te=Rk(B,H,Ee,xe,ye,ge,$,oe)){var ke=B-te[0],Se=H-te[1],we=ye-te[0],se=ge-te[1],V=1/Cr(Mk((ke*we+Se*se)/(ka(ke*ke+Se*Se)*ka(we*we+se*se)))/2),K=ka(te[0]*te[0]+te[1]*te[1]);k=Gu(M,(d-K)/(V-1)),p=Gu(M,(g-K)/(V+1))}else k=p=0}L>qn?p>qn?(R=gs(Ee,xe,B,H,g,p,b),z=gs(ye,ge,$,oe,g,p,b),l.moveTo(R.cx+R.x01,R.cy+R.y01),p<M?l.arc(R.cx,R.cy,p,Cn(R.y01,R.x01),Cn(z.y01,z.x01),!b):(l.arc(R.cx,R.cy,p,Cn(R.y01,R.x01),Cn(R.y11,R.x11),!b),l.arc(0,0,g,Cn(R.cy+R.y11,R.cx+R.x11),Cn(z.cy+z.y11,z.cx+z.x11),!b),l.arc(z.cx,z.cy,p,Cn(z.y11,z.x11),Cn(z.y01,z.x01),!b))):(l.moveTo(B,H),l.arc(0,0,g,S,A,!b)):l.moveTo(B,H),!(d>qn)||!(T>qn)?l.lineTo($,oe):k>qn?(R=gs($,oe,ye,ge,d,-k,b),z=gs(B,H,Ee,xe,d,-k,b),l.lineTo(R.cx+R.x01,R.cy+R.y01),k<M?l.arc(R.cx,R.cy,k,Cn(R.y01,R.x01),Cn(z.y01,z.x01),!b):(l.arc(R.cx,R.cy,k,Cn(R.y01,R.x01),Cn(R.y11,R.x11),!b),l.arc(0,0,d,Cn(R.cy+R.y11,R.cx+R.x11),Cn(z.cy+z.y11,z.cx+z.x11),b),l.arc(z.cx,z.cy,k,Cn(z.y11,z.x11),Cn(z.y01,z.x01),!b))):l.arc(0,0,d,I,N,b)}if(l.closePath(),f)return l=null,f+""||null}return c.centroid=function(){var f=(+t.apply(this,arguments)+ +e.apply(this,arguments))/2,h=(+i.apply(this,arguments)+ +a.apply(this,arguments))/2-Ro/2;return[Hi(h)*f,Cr(h)*f]},c.innerRadius=function(f){return arguments.length?(t=typeof f=="function"?f:xn(+f),c):t},c.outerRadius=function(f){return arguments.length?(e=typeof f=="function"?f:xn(+f),c):e},c.cornerRadius=function(f){return arguments.length?(n=typeof f=="function"?f:xn(+f),c):n},c.padRadius=function(f){return arguments.length?(r=f==null?null:typeof f=="function"?f:xn(+f),c):r},c.startAngle=function(f){return arguments.length?(i=typeof f=="function"?f:xn(+f),c):i},c.endAngle=function(f){return arguments.length?(a=typeof f=="function"?f:xn(+f),c):a},c.padAngle=function(f){return arguments.length?(s=typeof f=="function"?f:xn(+f),c):s},c.context=function(f){return arguments.length?(l=f??null,c):l},c}function p1(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function S1(t){this._context=t}S1.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e);break}}};function Pf(t){return new S1(t)}function Ek(t){return t[0]}function Ok(t){return t[1]}function el(t,e){var n=xn(!0),r=null,i=Pf,a=null,s=M1(l);t=typeof t=="function"?t:t===void 0?Ek:xn(t),e=typeof e=="function"?e:e===void 0?Ok:xn(e);function l(u){var c,f=(u=p1(u)).length,h,d=!1,g;for(r==null&&(a=i(g=s())),c=0;c<=f;++c)!(c<f&&n(h=u[c],c,u))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+t(h,c,u),+e(h,c,u));if(g)return a=null,g+""||null}return l.x=function(u){return arguments.length?(t=typeof u=="function"?u:xn(+u),l):t},l.y=function(u){return arguments.length?(e=typeof u=="function"?u:xn(+u),l):e},l.defined=function(u){return arguments.length?(n=typeof u=="function"?u:xn(!!u),l):n},l.curve=function(u){return arguments.length?(i=u,r!=null&&(a=i(r)),l):i},l.context=function(u){return arguments.length?(u==null?r=a=null:a=i(r=u),l):r},l}var Nk=T1(Pf);function A1(t){this._curve=t}A1.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,e){this._curve.point(e*Math.sin(t),e*-Math.cos(t))}};function T1(t){function e(n){return new A1(t(n))}return e._curve=t,e}function Lk(t){var e=t.curve;return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t.curve=function(n){return arguments.length?e(T1(n)):e()._curve},t}function ud(){return Lk(el().curve(Nk))}function br(t,e){return[(e=+e)*Math.cos(t-=Math.PI/2),e*Math.sin(t)]}class C1{constructor(e,n){this._context=e,this._x=n}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(e,n){switch(e=+e,n=+n,this._point){case 0:{this._point=1,this._line?this._context.lineTo(e,n):this._context.moveTo(e,n);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,n,e,n):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+n)/2,e,this._y0,e,n);break}}this._x0=e,this._y0=n}}function Wk(t){return new C1(t,!0)}function Ik(t){return new C1(t,!1)}function cd(){}function D1(t){this._context=t}D1.prototype={areaStart:cd,areaEnd:cd,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function P1(t){return new D1(t)}function tl(t,e){if((s=t.length)>1)for(var n=1,r,i,a=t[e[0]],s,l=a.length;n<s;++n)for(i=a,a=t[e[n]],r=0;r<l;++r)a[r][1]+=a[r][0]=isNaN(i[r][1])?i[r][0]:i[r][1]}function fd(t){for(var e=t.length,n=new Array(e);--e>=0;)n[e]=e;return n}function zk(t,e){return t[e]}function jk(t){const e=[];return e.key=t,e}function Fk(){var t=xn([]),e=fd,n=tl,r=zk;function i(a){var s=Array.from(t.apply(this,arguments),jk),l,u=s.length,c=-1,f;for(const h of a)for(l=0,++c;l<u;++l)(s[l][c]=[0,+r(h,s[l].key,c,a)]).data=h;for(l=0,f=p1(e(s));l<u;++l)s[f[l]].index=l;return n(s,f),s}return i.keys=function(a){return arguments.length?(t=typeof a=="function"?a:xn(Array.from(a)),i):t},i.value=function(a){return arguments.length?(r=typeof a=="function"?a:xn(+a),i):r},i.order=function(a){return arguments.length?(e=a==null?fd:typeof a=="function"?a:xn(Array.from(a)),i):e},i.offset=function(a){return arguments.length?(n=a??tl,i):n},i}function Bk(t,e){if((r=t.length)>0){for(var n,r,i=0,a=t[0].length,s;i<a;++i){for(s=n=0;n<r;++n)s+=t[n][i][1]||0;if(s)for(n=0;n<r;++n)t[n][i][1]/=s}tl(t,e)}}function Yk(t,e){if((u=t.length)>0)for(var n,r=0,i,a,s,l,u,c=t[e[0]].length;r<c;++r)for(s=l=0,n=0;n<u;++n)(a=(i=t[e[n]][r])[1]-i[0])>0?(i[0]=s,i[1]=s+=a):a<0?(i[1]=l,i[0]=l+=a):(i[0]=0,i[1]=a)}var Oc={exports:{}};(function(t,e){(function(n,r){r(e)})(Ka,function(n){function r(_,M){var k=Object.keys(_);if(Object.getOwnPropertySymbols){var p=Object.getOwnPropertySymbols(_);M&&(p=p.filter(function(R){return Object.getOwnPropertyDescriptor(_,R).enumerable})),k.push.apply(k,p)}return k}function i(_){for(var M=1;M<arguments.length;M++){var k=arguments[M]!=null?arguments[M]:{};M%2?r(Object(k),!0).forEach(function(p){s(_,p,k[p])}):Object.getOwnPropertyDescriptors?Object.defineProperties(_,Object.getOwnPropertyDescriptors(k)):r(Object(k)).forEach(function(p){Object.defineProperty(_,p,Object.getOwnPropertyDescriptor(k,p))})}return _}function a(_){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?a=function(M){return typeof M}:a=function(M){return M&&typeof Symbol=="function"&&M.constructor===Symbol&&M!==Symbol.prototype?"symbol":typeof M},a(_)}function s(_,M,k){return M in _?Object.defineProperty(_,M,{value:k,enumerable:!0,configurable:!0,writable:!0}):_[M]=k,_}function l(){return l=Object.assign||function(_){for(var M=1;M<arguments.length;M++){var k=arguments[M];for(var p in k)Object.prototype.hasOwnProperty.call(k,p)&&(_[p]=k[p])}return _},l.apply(this,arguments)}function u(_,M){if(_){if(typeof _=="string")return c(_,M);var k=Object.prototype.toString.call(_).slice(8,-1);if(k==="Object"&&_.constructor&&(k=_.constructor.name),k==="Map"||k==="Set")return Array.from(_);if(k==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(k))return c(_,M)}}function c(_,M){(M==null||M>_.length)&&(M=_.length);for(var k=0,p=new Array(M);k<M;k++)p[k]=_[k];return p}function f(_,M){var k=typeof Symbol<"u"&&_[Symbol.iterator]||_["@@iterator"];if(!k){if(Array.isArray(_)||(k=u(_))||M){k&&(_=k);var p=0,R=function(){};return{s:R,n:function(){return p>=_.length?{done:!0}:{done:!1,value:_[p++]}},e:function(B){throw B},f:R}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var z=!0,D=!1,W;return{s:function(){k=k.call(_)},n:function(){var B=k.next();return z=B.done,B},e:function(B){D=!0,W=B},f:function(){try{!z&&k.return!=null&&k.return()}finally{if(D)throw W}}}}function h(_,M){var k=[],p=[];function R(z,D){if(z.length===1)k.push(z[0]),p.push(z[0]);else{for(var W=Array(z.length-1),B=0;B<W.length;B++)B===0&&k.push(z[0]),B===W.length-1&&p.push(z[B+1]),W[B]=[(1-D)*z[B][0]+D*z[B+1][0],(1-D)*z[B][1]+D*z[B+1][1]];R(W,D)}}return _.length&&R(_,M),{left:k,right:p.reverse()}}function d(_){var M={};return _.length===4&&(M.x2=_[2][0],M.y2=_[2][1]),_.length>=3&&(M.x1=_[1][0],M.y1=_[1][1]),M.x=_[_.length-1][0],M.y=_[_.length-1][1],_.length===4?M.type="C":_.length===3?M.type="Q":M.type="L",M}function g(_,M){M=M||2;for(var k=[],p=_,R=1/M,z=0;z<M-1;z++){var D=R/(1-R*z),W=h(p,D);k.push(W.left),p=W.right}return k.push(p),k}function m(_,M,k){var p=[[_.x,_.y]];return M.x1!=null&&p.push([M.x1,M.y1]),M.x2!=null&&p.push([M.x2,M.y2]),p.push([M.x,M.y]),g(p,k).map(d)}var v=/[MLCSTQAHVZmlcstqahv]|-?[\d.e+-]+/g,w={M:["x","y"],L:["x","y"],H:["x"],V:["y"],C:["x1","y1","x2","y2","x","y"],S:["x2","y2","x","y"],Q:["x1","y1","x","y"],T:["x","y"],A:["rx","ry","xAxisRotation","largeArcFlag","sweepFlag","x","y"],Z:[]};Object.keys(w).forEach(function(_){w[_.toLowerCase()]=w[_]});function b(_,M){for(var k=Array(_),p=0;p<_;p++)k[p]=M;return k}function S(_){return"".concat(_.type).concat(w[_.type].map(function(M){return _[M]}).join(","))}function A(_,M){var k={x1:"x",y1:"y",x2:"x",y2:"y"},p=["xAxisRotation","largeArcFlag","sweepFlag"];if(_.type!==M.type&&M.type.toUpperCase()!=="M"){var R={};Object.keys(M).forEach(function(z){var D=M[z],W=_[z];W===void 0&&(p.includes(z)?W=D:(W===void 0&&k[z]&&(W=_[k[z]]),W===void 0&&(W=0))),R[z]=W}),R.type=M.type,_=R}return _}function N(_,M,k){var p=[];if(M.type==="L"||M.type==="Q"||M.type==="C")p=p.concat(m(_,M,k));else{var R=l({},_);R.type==="M"&&(R.type="L"),p=p.concat(b(k-1).map(function(){return R})),p.push(M)}return p}function I(_,M,k){var p=_.length-1,R=M.length-1,z=p/R,D=b(R).reduce(function(B,H,$){var oe=Math.floor(z*$);if(k&&oe<_.length-1&&k(_[oe],_[oe+1])){var ye=z*$%1<.5;B[oe]&&(ye?oe>0?oe-=1:oe<_.length-1&&(oe+=1):oe<_.length-1?oe+=1:oe>0&&(oe-=1))}return B[oe]=(B[oe]||0)+1,B},[]),W=D.reduce(function(B,H,$){if($===_.length-1){var oe=b(H,l({},_[_.length-1]));return oe[0].type==="M"&&oe.forEach(function(ye){ye.type="L"}),B.concat(oe)}return B.concat(N(_[$],_[$+1],H))},[]);return W.unshift(_[0]),W}function T(_){for(var M=(_||"").match(v)||[],k=[],p,R,z=0;z<M.length;++z)if(p=w[M[z]],p){R={type:M[z]};for(var D=0;D<p.length;++D)R[p[D]]=+M[z+D+1];z+=p.length,k.push(R)}return k}function L(_,M,k){var p=_==null?[]:_.slice(),R=M==null?[]:M.slice(),z=a(k)==="object"?k:{excludeSegment:k,snapEndsToInput:!0},D=z.excludeSegment,W=z.snapEndsToInput;if(!p.length&&!R.length)return function(){return[]};var B=(p.length===0||p[p.length-1].type==="Z")&&(R.length===0||R[R.length-1].type==="Z");p.length>0&&p[p.length-1].type==="Z"&&p.pop(),R.length>0&&R[R.length-1].type==="Z"&&R.pop(),p.length?R.length||R.push(p[0]):p.push(R[0]);var H=Math.abs(R.length-p.length);H!==0&&(R.length>p.length?p=I(p,R,D):R.length<p.length&&(R=I(R,p,D))),p=p.map(function(oe,ye){return A(oe,R[ye])});var $=p.map(function(oe){return i({},oe)});return B&&($.push({type:"Z"}),p.push({type:"Z"})),function(ye){if(ye===1&&W)return M??[];if(ye===0)return p;for(var ge=0;ge<$.length;++ge){var Ee=p[ge],xe=R[ge],te=$[ge],ke=f(w[te.type]),Se;try{for(ke.s();!(Se=ke.n()).done;){var we=Se.value;te[we]=(1-ye)*Ee[we]+ye*xe[we],(we==="largeArcFlag"||we==="sweepFlag")&&(te[we]=Math.round(te[we]))}}catch(se){ke.e(se)}finally{ke.f()}}return $}}function C(_,M,k){var p=T(_),R=T(M),z=a(k)==="object"?k:{excludeSegment:k,snapEndsToInput:!0},D=z.excludeSegment,W=z.snapEndsToInput;if(!p.length&&!R.length)return function(){return""};var B=L(p,R,{excludeSegment:D,snapEndsToInput:W});return function($){if($===1&&W)return M??"";var oe=B($),ye="",ge=f(oe),Ee;try{for(ge.s();!(Ee=ge.n()).done;){var xe=Ee.value;ye+=S(xe)}}catch(te){ge.e(te)}finally{ge.f()}return ye}}n.interpolatePath=C,n.interpolatePathCommands=L,n.pathCommandsFromString=T,Object.defineProperty(n,"__esModule",{value:!0})})})(Oc,Oc.exports);var R1=Oc.exports;function $o(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t);break}return this}const hd=Symbol("implicit");function eu(){var t=new mh,e=[],n=[],r=hd;function i(a){let s=t.get(a);if(s===void 0){if(r!==hd)return r;t.set(a,s=e.push(a)-1)}return n[s%n.length]}return i.domain=function(a){if(!arguments.length)return e.slice();e=[],t=new mh;for(const s of a)t.has(s)||t.set(s,e.push(s)-1);return i},i.range=function(a){return arguments.length?(n=Array.from(a),i):n.slice()},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return eu(e,n).unknown(r)},$o.apply(i,arguments),i}function wa(){var t=eu().unknown(void 0),e=t.domain,n=t.range,r=0,i=1,a,s,l=!1,u=0,c=0,f=.5;delete t.unknown;function h(){var d=e().length,g=i<r,m=g?i:r,v=g?r:i;a=(v-m)/Math.max(1,d-u+c*2),l&&(a=Math.floor(a)),m+=(v-m-a*(d-u))*f,s=a*(1-u),l&&(m=Math.round(m),s=Math.round(s));var w=Co(d).map(function(b){return m+a*b});return n(g?w.reverse():w)}return t.domain=function(d){return arguments.length?(e(d),h()):e()},t.range=function(d){return arguments.length?([r,i]=d,r=+r,i=+i,h()):[r,i]},t.rangeRound=function(d){return[r,i]=d,r=+r,i=+i,l=!0,h()},t.bandwidth=function(){return s},t.step=function(){return a},t.round=function(d){return arguments.length?(l=!!d,h()):l},t.padding=function(d){return arguments.length?(u=Math.min(1,c=+d),h()):u},t.paddingInner=function(d){return arguments.length?(u=Math.min(1,d),h()):u},t.paddingOuter=function(d){return arguments.length?(c=+d,h()):c},t.align=function(d){return arguments.length?(f=Math.max(0,Math.min(1,d)),h()):f},t.copy=function(){return wa(e(),[r,i]).round(l).paddingInner(u).paddingOuter(c).align(f)},$o.apply(h(),arguments)}function Rf(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function E1(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function es(){}var Oo=.7,nl=1/Oo,Ca="\\s*([+-]?\\d+)\\s*",No="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Lr="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Hk=/^#([0-9a-f]{3,8})$/,qk=new RegExp(`^rgb\\(${Ca},${Ca},${Ca}\\)$`),Uk=new RegExp(`^rgb\\(${Lr},${Lr},${Lr}\\)$`),Gk=new RegExp(`^rgba\\(${Ca},${Ca},${Ca},${No}\\)$`),Xk=new RegExp(`^rgba\\(${Lr},${Lr},${Lr},${No}\\)$`),Vk=new RegExp(`^hsl\\(${No},${Lr},${Lr}\\)$`),Zk=new RegExp(`^hsla\\(${No},${Lr},${Lr},${No}\\)$`),dd={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Rf(es,Lo,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:gd,formatHex:gd,formatHex8:Kk,formatHsl:Qk,formatRgb:md,toString:md});function gd(){return this.rgb().formatHex()}function Kk(){return this.rgb().formatHex8()}function Qk(){return O1(this).formatHsl()}function md(){return this.rgb().formatRgb()}function Lo(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=Hk.exec(t))?(n=e[1].length,e=parseInt(e[1],16),n===6?yd(e):n===3?new Un(e>>8&15|e>>4&240,e>>4&15|e&240,(e&15)<<4|e&15,1):n===8?ms(e>>24&255,e>>16&255,e>>8&255,(e&255)/255):n===4?ms(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|e&240,((e&15)<<4|e&15)/255):null):(e=qk.exec(t))?new Un(e[1],e[2],e[3],1):(e=Uk.exec(t))?new Un(e[1]*255/100,e[2]*255/100,e[3]*255/100,1):(e=Gk.exec(t))?ms(e[1],e[2],e[3],e[4]):(e=Xk.exec(t))?ms(e[1]*255/100,e[2]*255/100,e[3]*255/100,e[4]):(e=Vk.exec(t))?xd(e[1],e[2]/100,e[3]/100,1):(e=Zk.exec(t))?xd(e[1],e[2]/100,e[3]/100,e[4]):dd.hasOwnProperty(t)?yd(dd[t]):t==="transparent"?new Un(NaN,NaN,NaN,0):null}function yd(t){return new Un(t>>16&255,t>>8&255,t&255,1)}function ms(t,e,n,r){return r<=0&&(t=e=n=NaN),new Un(t,e,n,r)}function Jk(t){return t instanceof es||(t=Lo(t)),t?(t=t.rgb(),new Un(t.r,t.g,t.b,t.opacity)):new Un}function rl(t,e,n,r){return arguments.length===1?Jk(t):new Un(t,e,n,r??1)}function Un(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}Rf(Un,rl,E1(es,{brighter(t){return t=t==null?nl:Math.pow(nl,t),new Un(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=t==null?Oo:Math.pow(Oo,t),new Un(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Un(ta(this.r),ta(this.g),ta(this.b),il(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:vd,formatHex:vd,formatHex8:$k,formatRgb:_d,toString:_d}));function vd(){return`#${Gi(this.r)}${Gi(this.g)}${Gi(this.b)}`}function $k(){return`#${Gi(this.r)}${Gi(this.g)}${Gi(this.b)}${Gi((isNaN(this.opacity)?1:this.opacity)*255)}`}function _d(){const t=il(this.opacity);return`${t===1?"rgb(":"rgba("}${ta(this.r)}, ${ta(this.g)}, ${ta(this.b)}${t===1?")":`, ${t})`}`}function il(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function ta(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Gi(t){return t=ta(t),(t<16?"0":"")+t.toString(16)}function xd(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new vr(t,e,n,r)}function O1(t){if(t instanceof vr)return new vr(t.h,t.s,t.l,t.opacity);if(t instanceof es||(t=Lo(t)),!t)return new vr;if(t instanceof vr)return t;t=t.rgb();var e=t.r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),a=Math.max(e,n,r),s=NaN,l=a-i,u=(a+i)/2;return l?(e===a?s=(n-r)/l+(n<r)*6:n===a?s=(r-e)/l+2:s=(e-n)/l+4,l/=u<.5?a+i:2-a-i,s*=60):l=u>0&&u<1?0:s,new vr(s,l,u,t.opacity)}function ew(t,e,n,r){return arguments.length===1?O1(t):new vr(t,e,n,r??1)}function vr(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}Rf(vr,ew,E1(es,{brighter(t){return t=t==null?nl:Math.pow(nl,t),new vr(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=t==null?Oo:Math.pow(Oo,t),new vr(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new Un(Xu(t>=240?t-240:t+120,i,r),Xu(t,i,r),Xu(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new vr(bd(this.h),ys(this.s),ys(this.l),il(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=il(this.opacity);return`${t===1?"hsl(":"hsla("}${bd(this.h)}, ${ys(this.s)*100}%, ${ys(this.l)*100}%${t===1?")":`, ${t})`}`}}));function bd(t){return t=(t||0)%360,t<0?t+360:t}function ys(t){return Math.max(0,Math.min(1,t||0))}function Xu(t,e,n){return(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)*255}const Ef=t=>()=>t;function tw(t,e){return function(n){return t+n*e}}function nw(t,e,n){return t=Math.pow(t,n),e=Math.pow(e,n)-t,n=1/n,function(r){return Math.pow(t+r*e,n)}}function rw(t){return(t=+t)==1?N1:function(e,n){return n-e?nw(e,n,t):Ef(isNaN(e)?n:e)}}function N1(t,e){var n=e-t;return n?tw(t,n):Ef(isNaN(t)?e:t)}const kd=function t(e){var n=rw(e);function r(i,a){var s=n((i=rl(i)).r,(a=rl(a)).r),l=n(i.g,a.g),u=n(i.b,a.b),c=N1(i.opacity,a.opacity);return function(f){return i.r=s(f),i.g=l(f),i.b=u(f),i.opacity=c(f),i+""}}return r.gamma=t,r}(1);function iw(t,e){e||(e=[]);var n=t?Math.min(e.length,t.length):0,r=e.slice(),i;return function(a){for(i=0;i<n;++i)r[i]=t[i]*(1-a)+e[i]*a;return r}}function aw(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function ow(t,e){var n=e?e.length:0,r=t?Math.min(n,t.length):0,i=new Array(r),a=new Array(n),s;for(s=0;s<r;++s)i[s]=Wo(t[s],e[s]);for(;s<n;++s)a[s]=e[s];return function(l){for(s=0;s<r;++s)a[s]=i[s](l);return a}}function sw(t,e){var n=new Date;return t=+t,e=+e,function(r){return n.setTime(t*(1-r)+e*r),n}}function al(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}}function lw(t,e){var n={},r={},i;(t===null||typeof t!="object")&&(t={}),(e===null||typeof e!="object")&&(e={});for(i in e)i in t?n[i]=Wo(t[i],e[i]):r[i]=e[i];return function(a){for(i in n)r[i]=n[i](a);return r}}var Nc=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Vu=new RegExp(Nc.source,"g");function uw(t){return function(){return t}}function cw(t){return function(e){return t(e)+""}}function fw(t,e){var n=Nc.lastIndex=Vu.lastIndex=0,r,i,a,s=-1,l=[],u=[];for(t=t+"",e=e+"";(r=Nc.exec(t))&&(i=Vu.exec(e));)(a=i.index)>n&&(a=e.slice(n,a),l[s]?l[s]+=a:l[++s]=a),(r=r[0])===(i=i[0])?l[s]?l[s]+=i:l[++s]=i:(l[++s]=null,u.push({i:s,x:al(r,i)})),n=Vu.lastIndex;return n<e.length&&(a=e.slice(n),l[s]?l[s]+=a:l[++s]=a),l.length<2?u[0]?cw(u[0].x):uw(e):(e=u.length,function(c){for(var f=0,h;f<e;++f)l[(h=u[f]).i]=h.x(c);return l.join("")})}function Wo(t,e){var n=typeof e,r;return e==null||n==="boolean"?Ef(e):(n==="number"?al:n==="string"?(r=Lo(e))?(e=r,kd):fw:e instanceof Lo?kd:e instanceof Date?sw:aw(e)?iw:Array.isArray(e)?ow:typeof e.valueOf!="function"&&typeof e.toString!="function"||isNaN(e)?lw:al)(t,e)}function L1(t,e){return t=+t,e=+e,function(n){return Math.round(t*(1-n)+e*n)}}function wd(t,e){for(var n=new Array(e),r=0;r<e;++r)n[r]=t(r/(e-1));return n}function hw(t){return function(){return t}}function dw(t){return+t}var Md=[0,1];function Nr(t){return t}function Lc(t,e){return(e-=t=+t)?function(n){return(n-t)/e}:hw(isNaN(e)?NaN:.5)}function gw(t,e){var n;return t>e&&(n=t,t=e,e=n),function(r){return Math.max(t,Math.min(e,r))}}function mw(t,e,n){var r=t[0],i=t[1],a=e[0],s=e[1];return i<r?(r=Lc(i,r),a=n(s,a)):(r=Lc(r,i),a=n(a,s)),function(l){return a(r(l))}}function yw(t,e,n){var r=Math.min(t.length,e.length)-1,i=new Array(r),a=new Array(r),s=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++s<r;)i[s]=Lc(t[s],t[s+1]),a[s]=n(e[s],e[s+1]);return function(l){var u=nv(t,l,1,r)-1;return a[u](i[u](l))}}function Of(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function W1(){var t=Md,e=Md,n=Wo,r,i,a,s=Nr,l,u,c;function f(){var d=Math.min(t.length,e.length);return s!==Nr&&(s=gw(t[0],t[d-1])),l=d>2?yw:mw,u=c=null,h}function h(d){return d==null||isNaN(d=+d)?a:(u||(u=l(t.map(r),e,n)))(r(s(d)))}return h.invert=function(d){return s(i((c||(c=l(e,t.map(r),al)))(d)))},h.domain=function(d){return arguments.length?(t=Array.from(d,dw),f()):t.slice()},h.range=function(d){return arguments.length?(e=Array.from(d),f()):e.slice()},h.rangeRound=function(d){return e=Array.from(d),n=L1,f()},h.clamp=function(d){return arguments.length?(s=d?!0:Nr,f()):s!==Nr},h.interpolate=function(d){return arguments.length?(n=d,f()):n},h.unknown=function(d){return arguments.length?(a=d,h):a},function(d,g){return r=d,i=g,f()}}function I1(){return W1()(Nr,Nr)}function vw(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)}function ol(t,e){if((n=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var n,r=t.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+t.slice(n+1)]}function Ya(t){return t=ol(Math.abs(t)),t?t[1]:NaN}function _w(t,e){return function(n,r){for(var i=n.length,a=[],s=0,l=t[0],u=0;i>0&&l>0&&(u+l+1>r&&(l=Math.max(1,r-u)),a.push(n.substring(i-=l,i+l)),!((u+=l+1)>r));)l=t[s=(s+1)%t.length];return a.reverse().join(e)}}function xw(t){return function(e){return e.replace(/[0-9]/g,function(n){return t[+n]})}}var bw=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function sl(t){if(!(e=bw.exec(t)))throw new Error("invalid format: "+t);var e;return new Nf({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}sl.prototype=Nf.prototype;function Nf(t){this.fill=t.fill===void 0?" ":t.fill+"",this.align=t.align===void 0?">":t.align+"",this.sign=t.sign===void 0?"-":t.sign+"",this.symbol=t.symbol===void 0?"":t.symbol+"",this.zero=!!t.zero,this.width=t.width===void 0?void 0:+t.width,this.comma=!!t.comma,this.precision=t.precision===void 0?void 0:+t.precision,this.trim=!!t.trim,this.type=t.type===void 0?"":t.type+""}Nf.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function kw(t){e:for(var e=t.length,n=1,r=-1,i;n<e;++n)switch(t[n]){case".":r=i=n;break;case"0":r===0&&(r=n),i=n;break;default:if(!+t[n])break e;r>0&&(r=0);break}return r>0?t.slice(0,r)+t.slice(i+1):t}var z1;function ww(t,e){var n=ol(t,e);if(!n)return t+"";var r=n[0],i=n[1],a=i-(z1=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,s=r.length;return a===s?r:a>s?r+new Array(a-s+1).join("0"):a>0?r.slice(0,a)+"."+r.slice(a):"0."+new Array(1-a).join("0")+ol(t,Math.max(0,e+a-1))[0]}function pd(t,e){var n=ol(t,e);if(!n)return t+"";var r=n[0],i=n[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}const Sd={"%":(t,e)=>(t*100).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:vw,e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>pd(t*100,e),r:pd,s:ww,X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Ad(t){return t}var Td=Array.prototype.map,Cd=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Mw(t){var e=t.grouping===void 0||t.thousands===void 0?Ad:_w(Td.call(t.grouping,Number),t.thousands+""),n=t.currency===void 0?"":t.currency[0]+"",r=t.currency===void 0?"":t.currency[1]+"",i=t.decimal===void 0?".":t.decimal+"",a=t.numerals===void 0?Ad:xw(Td.call(t.numerals,String)),s=t.percent===void 0?"%":t.percent+"",l=t.minus===void 0?"−":t.minus+"",u=t.nan===void 0?"NaN":t.nan+"";function c(h){h=sl(h);var d=h.fill,g=h.align,m=h.sign,v=h.symbol,w=h.zero,b=h.width,S=h.comma,A=h.precision,N=h.trim,I=h.type;I==="n"?(S=!0,I="g"):Sd[I]||(A===void 0&&(A=12),N=!0,I="g"),(w||d==="0"&&g==="=")&&(w=!0,d="0",g="=");var T=v==="$"?n:v==="#"&&/[boxX]/.test(I)?"0"+I.toLowerCase():"",L=v==="$"?r:/[%p]/.test(I)?s:"",C=Sd[I],_=/[defgprs%]/.test(I);A=A===void 0?6:/[gprs]/.test(I)?Math.max(1,Math.min(21,A)):Math.max(0,Math.min(20,A));function M(k){var p=T,R=L,z,D,W;if(I==="c")R=C(k)+R,k="";else{k=+k;var B=k<0||1/k<0;if(k=isNaN(k)?u:C(Math.abs(k),A),N&&(k=kw(k)),B&&+k==0&&m!=="+"&&(B=!1),p=(B?m==="("?m:l:m==="-"||m==="("?"":m)+p,R=(I==="s"?Cd[8+z1/3]:"")+R+(B&&m==="("?")":""),_){for(z=-1,D=k.length;++z<D;)if(W=k.charCodeAt(z),48>W||W>57){R=(W===46?i+k.slice(z+1):k.slice(z))+R,k=k.slice(0,z);break}}}S&&!w&&(k=e(k,1/0));var H=p.length+k.length+R.length,$=H<b?new Array(b-H+1).join(d):"";switch(S&&w&&(k=e($+k,$.length?b-R.length:1/0),$=""),g){case"<":k=p+k+R+$;break;case"=":k=p+$+k+R;break;case"^":k=$.slice(0,H=$.length>>1)+p+k+R+$.slice(H);break;default:k=$+p+k+R;break}return a(k)}return M.toString=function(){return h+""},M}function f(h,d){var g=c((h=sl(h),h.type="f",h)),m=Math.max(-8,Math.min(8,Math.floor(Ya(d)/3)))*3,v=Math.pow(10,-m),w=Cd[8+m/3];return function(b){return g(v*b)+w}}return{format:c,formatPrefix:f}}var vs,j1,F1;pw({thousands:",",grouping:[3],currency:["$",""]});function pw(t){return vs=Mw(t),j1=vs.format,F1=vs.formatPrefix,vs}function Sw(t){return Math.max(0,-Ya(Math.abs(t)))}function Aw(t,e){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(Ya(e)/3)))*3-Ya(Math.abs(t)))}function Tw(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,Ya(e)-Ya(t))+1}function Cw(t,e,n,r){var i=mc(t,e,n),a;switch(r=sl(r??",f"),r.type){case"s":{var s=Math.max(Math.abs(t),Math.abs(e));return r.precision==null&&!isNaN(a=Aw(i,s))&&(r.precision=a),F1(r,s)}case"":case"e":case"g":case"p":case"r":{r.precision==null&&!isNaN(a=Tw(i,Math.max(Math.abs(t),Math.abs(e))))&&(r.precision=a-(r.type==="e"));break}case"f":case"%":{r.precision==null&&!isNaN(a=Sw(i))&&(r.precision=a-(r.type==="%")*2);break}}return j1(r)}function B1(t){var e=t.domain;return t.ticks=function(n){var r=e();return lv(r[0],r[r.length-1],n??10)},t.tickFormat=function(n,r){var i=e();return Cw(i[0],i[i.length-1],n??10,r)},t.nice=function(n){n==null&&(n=10);var r=e(),i=0,a=r.length-1,s=r[i],l=r[a],u,c,f=10;for(l<s&&(c=s,s=l,l=c,c=i,i=a,a=c);f-- >0;){if(c=gc(s,l,n),c===u)return r[i]=s,r[a]=l,e(r);if(c>0)s=Math.floor(s/c)*c,l=Math.ceil(l/c)*c;else if(c<0)s=Math.ceil(s*c)/c,l=Math.floor(l*c)/c;else break;u=c}return t},t}function Ni(){var t=I1();return t.copy=function(){return Of(t,Ni())},$o.apply(t,arguments),B1(t)}function Dw(t,e){t=t.slice();var n=0,r=t.length-1,i=t[n],a=t[r],s;return a<i&&(s=n,n=r,r=s,s=i,i=a,a=s),t[n]=e.floor(i),t[r]=e.ceil(a),t}function Dd(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function Pw(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function Rw(t){return t<0?-t*t:t*t}function Ew(t){var e=t(Nr,Nr),n=1;function r(){return n===1?t(Nr,Nr):n===.5?t(Pw,Rw):t(Dd(n),Dd(1/n))}return e.exponent=function(i){return arguments.length?(n=+i,r()):n},B1(e)}function Y1(){var t=Ew(W1());return t.copy=function(){return Of(t,Y1()).exponent(t.exponent())},$o.apply(t,arguments),t}function Ow(){return Y1.apply(null,arguments).exponent(.5)}const Zu=new Date,Ku=new Date;function dn(t,e,n,r){function i(a){return t(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(t(a=new Date(+a)),a),i.ceil=a=>(t(a=new Date(a-1)),e(a,1),t(a),a),i.round=a=>{const s=i(a),l=i.ceil(a);return a-s<l-a?s:l},i.offset=(a,s)=>(e(a=new Date(+a),s==null?1:Math.floor(s)),a),i.range=(a,s,l)=>{const u=[];if(a=i.ceil(a),l=l==null?1:Math.floor(l),!(a<s)||!(l>0))return u;let c;do u.push(c=new Date(+a)),e(a,l),t(a);while(c<a&&a<s);return u},i.filter=a=>dn(s=>{if(s>=s)for(;t(s),!a(s);)s.setTime(s-1)},(s,l)=>{if(s>=s)if(l<0)for(;++l<=0;)for(;e(s,-1),!a(s););else for(;--l>=0;)for(;e(s,1),!a(s););}),n&&(i.count=(a,s)=>(Zu.setTime(+a),Ku.setTime(+s),t(Zu),t(Ku),Math.floor(n(Zu,Ku))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(r?s=>r(s)%a===0:s=>i.count(0,s)%a===0):i)),i}const ll=dn(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);ll.every=t=>(t=Math.floor(t),!isFinite(t)||!(t>0)?null:t>1?dn(e=>{e.setTime(Math.floor(e/t)*t)},(e,n)=>{e.setTime(+e+n*t)},(e,n)=>(n-e)/t):ll);ll.range;const ti=1e3,sr=ti*60,ni=sr*60,ui=ni*24,Lf=ui*7,Pd=ui*30,Qu=ui*365,Ma=dn(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*ti)},(t,e)=>(e-t)/ti,t=>t.getUTCSeconds());Ma.range;const Wf=dn(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*ti)},(t,e)=>{t.setTime(+t+e*sr)},(t,e)=>(e-t)/sr,t=>t.getMinutes());Wf.range;const Nw=dn(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*sr)},(t,e)=>(e-t)/sr,t=>t.getUTCMinutes());Nw.range;const If=dn(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*ti-t.getMinutes()*sr)},(t,e)=>{t.setTime(+t+e*ni)},(t,e)=>(e-t)/ni,t=>t.getHours());If.range;const Lw=dn(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*ni)},(t,e)=>(e-t)/ni,t=>t.getUTCHours());Lw.range;const ri=dn(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*sr)/ui,t=>t.getDate()-1);ri.range;const zf=dn(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/ui,t=>t.getUTCDate()-1);zf.range;const Ww=dn(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/ui,t=>Math.floor(t/ui));Ww.range;function la(t){return dn(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(e,n)=>{e.setDate(e.getDate()+n*7)},(e,n)=>(n-e-(n.getTimezoneOffset()-e.getTimezoneOffset())*sr)/Lf)}const tu=la(0),ul=la(1),Iw=la(2),zw=la(3),Ha=la(4),jw=la(5),Fw=la(6);tu.range;ul.range;Iw.range;zw.range;Ha.range;jw.range;Fw.range;function ua(t){return dn(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCDate(e.getUTCDate()+n*7)},(e,n)=>(n-e)/Lf)}const H1=ua(0),cl=ua(1),Bw=ua(2),Yw=ua(3),qa=ua(4),Hw=ua(5),qw=ua(6);H1.range;cl.range;Bw.range;Yw.range;qa.range;Hw.range;qw.range;const jf=dn(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());jf.range;const Uw=dn(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());Uw.range;const zr=dn(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());zr.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:dn(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,n)=>{e.setFullYear(e.getFullYear()+n*t)});zr.range;const ra=dn(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());ra.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:dn(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCFullYear(e.getUTCFullYear()+n*t)});ra.range;function Gw(t,e,n,r,i,a){const s=[[Ma,1,ti],[Ma,5,5*ti],[Ma,15,15*ti],[Ma,30,30*ti],[a,1,sr],[a,5,5*sr],[a,15,15*sr],[a,30,30*sr],[i,1,ni],[i,3,3*ni],[i,6,6*ni],[i,12,12*ni],[r,1,ui],[r,2,2*ui],[n,1,Lf],[e,1,Pd],[e,3,3*Pd],[t,1,Qu]];function l(c,f,h){const d=f<c;d&&([c,f]=[f,c]);const g=h&&typeof h.range=="function"?h:u(c,f,h),m=g?g.range(c,+f+1):[];return d?m.reverse():m}function u(c,f,h){const d=Math.abs(f-c)/h,g=To(([,,w])=>w).right(s,d);if(g===s.length)return t.every(mc(c/Qu,f/Qu,h));if(g===0)return ll.every(Math.max(mc(c,f,h),1));const[m,v]=s[d/s[g-1][2]<s[g][2]/d?g-1:g];return m.every(v)}return[l,u]}const[q1,Xw]=Gw(zr,jf,tu,ri,If,Wf);function Ju(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function $u(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function fo(t,e,n){return{y:t,m:e,d:n,H:0,M:0,S:0,L:0}}function Vw(t){var e=t.dateTime,n=t.date,r=t.time,i=t.periods,a=t.days,s=t.shortDays,l=t.months,u=t.shortMonths,c=ho(i),f=go(i),h=ho(a),d=go(a),g=ho(s),m=go(s),v=ho(l),w=go(l),b=ho(u),S=go(u),A={a:B,A:H,b:$,B:oe,c:null,d:Wd,e:Wd,f:v3,g:T3,G:D3,H:g3,I:m3,j:y3,L:U1,m:_3,M:x3,p:ye,q:ge,Q:jd,s:Fd,S:b3,u:k3,U:w3,V:M3,w:p3,W:S3,x:null,X:null,y:A3,Y:C3,Z:P3,"%":zd},N={a:Ee,A:xe,b:te,B:ke,c:null,d:Id,e:Id,f:N3,g:q3,G:G3,H:R3,I:E3,j:O3,L:X1,m:L3,M:W3,p:Se,q:we,Q:jd,s:Fd,S:I3,u:z3,U:j3,V:F3,w:B3,W:Y3,x:null,X:null,y:H3,Y:U3,Z:X3,"%":zd},I={a:M,A:k,b:p,B:R,c:z,d:Nd,e:Nd,f:c3,g:Od,G:Ed,H:Ld,I:Ld,j:o3,L:u3,m:a3,M:s3,p:_,q:i3,Q:h3,s:d3,S:l3,u:$w,U:e3,V:t3,w:Jw,W:n3,x:D,X:W,y:Od,Y:Ed,Z:r3,"%":f3};A.x=T(n,A),A.X=T(r,A),A.c=T(e,A),N.x=T(n,N),N.X=T(r,N),N.c=T(e,N);function T(se,V){return function(K){var G=[],be=-1,_e=0,ae=se.length,J,de,De;for(K instanceof Date||(K=new Date(+K));++be<ae;)se.charCodeAt(be)===37&&(G.push(se.slice(_e,be)),(de=Rd[J=se.charAt(++be)])!=null?J=se.charAt(++be):de=J==="e"?" ":"0",(De=V[J])&&(J=De(K,de)),G.push(J),_e=be+1);return G.push(se.slice(_e,be)),G.join("")}}function L(se,V){return function(K){var G=fo(1900,void 0,1),be=C(G,se,K+="",0),_e,ae;if(be!=K.length)return null;if("Q"in G)return new Date(G.Q);if("s"in G)return new Date(G.s*1e3+("L"in G?G.L:0));if(V&&!("Z"in G)&&(G.Z=0),"p"in G&&(G.H=G.H%12+G.p*12),G.m===void 0&&(G.m="q"in G?G.q:0),"V"in G){if(G.V<1||G.V>53)return null;"w"in G||(G.w=1),"Z"in G?(_e=$u(fo(G.y,0,1)),ae=_e.getUTCDay(),_e=ae>4||ae===0?cl.ceil(_e):cl(_e),_e=zf.offset(_e,(G.V-1)*7),G.y=_e.getUTCFullYear(),G.m=_e.getUTCMonth(),G.d=_e.getUTCDate()+(G.w+6)%7):(_e=Ju(fo(G.y,0,1)),ae=_e.getDay(),_e=ae>4||ae===0?ul.ceil(_e):ul(_e),_e=ri.offset(_e,(G.V-1)*7),G.y=_e.getFullYear(),G.m=_e.getMonth(),G.d=_e.getDate()+(G.w+6)%7)}else("W"in G||"U"in G)&&("w"in G||(G.w="u"in G?G.u%7:"W"in G?1:0),ae="Z"in G?$u(fo(G.y,0,1)).getUTCDay():Ju(fo(G.y,0,1)).getDay(),G.m=0,G.d="W"in G?(G.w+6)%7+G.W*7-(ae+5)%7:G.w+G.U*7-(ae+6)%7);return"Z"in G?(G.H+=G.Z/100|0,G.M+=G.Z%100,$u(G)):Ju(G)}}function C(se,V,K,G){for(var be=0,_e=V.length,ae=K.length,J,de;be<_e;){if(G>=ae)return-1;if(J=V.charCodeAt(be++),J===37){if(J=V.charAt(be++),de=I[J in Rd?V.charAt(be++):J],!de||(G=de(se,K,G))<0)return-1}else if(J!=K.charCodeAt(G++))return-1}return G}function _(se,V,K){var G=c.exec(V.slice(K));return G?(se.p=f.get(G[0].toLowerCase()),K+G[0].length):-1}function M(se,V,K){var G=g.exec(V.slice(K));return G?(se.w=m.get(G[0].toLowerCase()),K+G[0].length):-1}function k(se,V,K){var G=h.exec(V.slice(K));return G?(se.w=d.get(G[0].toLowerCase()),K+G[0].length):-1}function p(se,V,K){var G=b.exec(V.slice(K));return G?(se.m=S.get(G[0].toLowerCase()),K+G[0].length):-1}function R(se,V,K){var G=v.exec(V.slice(K));return G?(se.m=w.get(G[0].toLowerCase()),K+G[0].length):-1}function z(se,V,K){return C(se,e,V,K)}function D(se,V,K){return C(se,n,V,K)}function W(se,V,K){return C(se,r,V,K)}function B(se){return s[se.getDay()]}function H(se){return a[se.getDay()]}function $(se){return u[se.getMonth()]}function oe(se){return l[se.getMonth()]}function ye(se){return i[+(se.getHours()>=12)]}function ge(se){return 1+~~(se.getMonth()/3)}function Ee(se){return s[se.getUTCDay()]}function xe(se){return a[se.getUTCDay()]}function te(se){return u[se.getUTCMonth()]}function ke(se){return l[se.getUTCMonth()]}function Se(se){return i[+(se.getUTCHours()>=12)]}function we(se){return 1+~~(se.getUTCMonth()/3)}return{format:function(se){var V=T(se+="",A);return V.toString=function(){return se},V},parse:function(se){var V=L(se+="",!1);return V.toString=function(){return se},V},utcFormat:function(se){var V=T(se+="",N);return V.toString=function(){return se},V},utcParse:function(se){var V=L(se+="",!0);return V.toString=function(){return se},V}}}var Rd={"-":"",_:" ",0:"0"},kn=/^\s*\d+/,Zw=/^%/,Kw=/[\\^$*+?|[\]().{}]/g;function It(t,e,n){var r=t<0?"-":"",i=(r?-t:t)+"",a=i.length;return r+(a<n?new Array(n-a+1).join(e)+i:i)}function Qw(t){return t.replace(Kw,"\\$&")}function ho(t){return new RegExp("^(?:"+t.map(Qw).join("|")+")","i")}function go(t){return new Map(t.map((e,n)=>[e.toLowerCase(),n]))}function Jw(t,e,n){var r=kn.exec(e.slice(n,n+1));return r?(t.w=+r[0],n+r[0].length):-1}function $w(t,e,n){var r=kn.exec(e.slice(n,n+1));return r?(t.u=+r[0],n+r[0].length):-1}function e3(t,e,n){var r=kn.exec(e.slice(n,n+2));return r?(t.U=+r[0],n+r[0].length):-1}function t3(t,e,n){var r=kn.exec(e.slice(n,n+2));return r?(t.V=+r[0],n+r[0].length):-1}function n3(t,e,n){var r=kn.exec(e.slice(n,n+2));return r?(t.W=+r[0],n+r[0].length):-1}function Ed(t,e,n){var r=kn.exec(e.slice(n,n+4));return r?(t.y=+r[0],n+r[0].length):-1}function Od(t,e,n){var r=kn.exec(e.slice(n,n+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function r3(t,e,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(n,n+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function i3(t,e,n){var r=kn.exec(e.slice(n,n+1));return r?(t.q=r[0]*3-3,n+r[0].length):-1}function a3(t,e,n){var r=kn.exec(e.slice(n,n+2));return r?(t.m=r[0]-1,n+r[0].length):-1}function Nd(t,e,n){var r=kn.exec(e.slice(n,n+2));return r?(t.d=+r[0],n+r[0].length):-1}function o3(t,e,n){var r=kn.exec(e.slice(n,n+3));return r?(t.m=0,t.d=+r[0],n+r[0].length):-1}function Ld(t,e,n){var r=kn.exec(e.slice(n,n+2));return r?(t.H=+r[0],n+r[0].length):-1}function s3(t,e,n){var r=kn.exec(e.slice(n,n+2));return r?(t.M=+r[0],n+r[0].length):-1}function l3(t,e,n){var r=kn.exec(e.slice(n,n+2));return r?(t.S=+r[0],n+r[0].length):-1}function u3(t,e,n){var r=kn.exec(e.slice(n,n+3));return r?(t.L=+r[0],n+r[0].length):-1}function c3(t,e,n){var r=kn.exec(e.slice(n,n+6));return r?(t.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function f3(t,e,n){var r=Zw.exec(e.slice(n,n+1));return r?n+r[0].length:-1}function h3(t,e,n){var r=kn.exec(e.slice(n));return r?(t.Q=+r[0],n+r[0].length):-1}function d3(t,e,n){var r=kn.exec(e.slice(n));return r?(t.s=+r[0],n+r[0].length):-1}function Wd(t,e){return It(t.getDate(),e,2)}function g3(t,e){return It(t.getHours(),e,2)}function m3(t,e){return It(t.getHours()%12||12,e,2)}function y3(t,e){return It(1+ri.count(zr(t),t),e,3)}function U1(t,e){return It(t.getMilliseconds(),e,3)}function v3(t,e){return U1(t,e)+"000"}function _3(t,e){return It(t.getMonth()+1,e,2)}function x3(t,e){return It(t.getMinutes(),e,2)}function b3(t,e){return It(t.getSeconds(),e,2)}function k3(t){var e=t.getDay();return e===0?7:e}function w3(t,e){return It(tu.count(zr(t)-1,t),e,2)}function G1(t){var e=t.getDay();return e>=4||e===0?Ha(t):Ha.ceil(t)}function M3(t,e){return t=G1(t),It(Ha.count(zr(t),t)+(zr(t).getDay()===4),e,2)}function p3(t){return t.getDay()}function S3(t,e){return It(ul.count(zr(t)-1,t),e,2)}function A3(t,e){return It(t.getFullYear()%100,e,2)}function T3(t,e){return t=G1(t),It(t.getFullYear()%100,e,2)}function C3(t,e){return It(t.getFullYear()%1e4,e,4)}function D3(t,e){var n=t.getDay();return t=n>=4||n===0?Ha(t):Ha.ceil(t),It(t.getFullYear()%1e4,e,4)}function P3(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+It(e/60|0,"0",2)+It(e%60,"0",2)}function Id(t,e){return It(t.getUTCDate(),e,2)}function R3(t,e){return It(t.getUTCHours(),e,2)}function E3(t,e){return It(t.getUTCHours()%12||12,e,2)}function O3(t,e){return It(1+zf.count(ra(t),t),e,3)}function X1(t,e){return It(t.getUTCMilliseconds(),e,3)}function N3(t,e){return X1(t,e)+"000"}function L3(t,e){return It(t.getUTCMonth()+1,e,2)}function W3(t,e){return It(t.getUTCMinutes(),e,2)}function I3(t,e){return It(t.getUTCSeconds(),e,2)}function z3(t){var e=t.getUTCDay();return e===0?7:e}function j3(t,e){return It(H1.count(ra(t)-1,t),e,2)}function V1(t){var e=t.getUTCDay();return e>=4||e===0?qa(t):qa.ceil(t)}function F3(t,e){return t=V1(t),It(qa.count(ra(t),t)+(ra(t).getUTCDay()===4),e,2)}function B3(t){return t.getUTCDay()}function Y3(t,e){return It(cl.count(ra(t)-1,t),e,2)}function H3(t,e){return It(t.getUTCFullYear()%100,e,2)}function q3(t,e){return t=V1(t),It(t.getUTCFullYear()%100,e,2)}function U3(t,e){return It(t.getUTCFullYear()%1e4,e,4)}function G3(t,e){var n=t.getUTCDay();return t=n>=4||n===0?qa(t):qa.ceil(t),It(t.getUTCFullYear()%1e4,e,4)}function X3(){return"+0000"}function zd(){return"%"}function jd(t){return+t}function Fd(t){return Math.floor(+t/1e3)}var da,Z1;V3({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function V3(t){return da=Vw(t),Z1=da.format,da.parse,da.utcFormat,da.utcParse,da}function Z3(t){return new Date(t)}function K3(t){return t instanceof Date?+t:+new Date(+t)}function K1(t,e,n,r,i,a,s,l,u,c){var f=I1(),h=f.invert,d=f.domain,g=c(".%L"),m=c(":%S"),v=c("%I:%M"),w=c("%I %p"),b=c("%a %d"),S=c("%b %d"),A=c("%B"),N=c("%Y");function I(T){return(u(T)<T?g:l(T)<T?m:s(T)<T?v:a(T)<T?w:r(T)<T?i(T)<T?b:S:n(T)<T?A:N)(T)}return f.invert=function(T){return new Date(h(T))},f.domain=function(T){return arguments.length?d(Array.from(T,K3)):d().map(Z3)},f.ticks=function(T){var L=d();return t(L[0],L[L.length-1],T??10)},f.tickFormat=function(T,L){return L==null?I:c(L)},f.nice=function(T){var L=d();return(!T||typeof T.range!="function")&&(T=e(L[0],L[L.length-1],T??10)),T?d(Dw(L,T)):f},f.copy=function(){return Of(f,K1(t,e,n,r,i,a,s,l,u,c))},f}function Bd(){return $o.apply(K1(q1,Xw,zr,jf,tu,ri,If,Wf,Ma,Z1).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}const Yd="__text_measurement_id";function Q3(t,e){try{let n=document.getElementById(Yd);if(!n){const r=document.createElementNS("http://www.w3.org/2000/svg","svg");r.style.width="0",r.style.height="0",r.style.position="absolute",r.style.top="-100%",r.style.left="-100%",n=document.createElementNS("http://www.w3.org/2000/svg","text"),n.setAttribute("id",Yd),r.appendChild(n),document.body.appendChild(r)}return Object.assign(n.style,e),n.textContent=t,n.getComputedTextLength()}catch{return null}}const mr=$a(Q3,(t,e)=>`${t}_${JSON.stringify(e)}`);function fl(t){return t.replace(/^\w/,e=>e.toUpperCase())}const J3="…";function $3(t,{position:e="end",ellipsis:n=J3,maxWidth:r,style:i,maxChars:a}){if(!t)return"";if(r===void 0&&a===void 0)return t;let s=t;if(a!==void 0&&t.length>a)if(e==="start")s=n+t.slice(-a);else if(e==="middle"){const l=Math.floor(a/2);s=t.slice(0,l)+n+t.slice(-l)}else s=t.slice(0,a)+n;if(r!==void 0){const l=mr(s,i);if(l===null||l<=r)return s;const u=mr(n,i)??0;let c=r-u;if(e==="start"){let f=s.slice(n.length),h=mr(f,i);for(;h!==null&&h>c&&f.length>0;)f=f.slice(1),h=mr(f,i);return n+f}else if(e==="middle"){const f=c/2;let h="",d="",g="",m="";for(let v=0,w=s.length-1;v<s.length&&w>=0;v++,w--){const b=s.slice(0,v+1),S=s.slice(w),A=mr(b,i),N=mr(S,i);A!==null&&A<=f&&(h=b),N!==null&&N<=f&&(d=S);const I=mr(h+n+d,i);if(I!==null&&I<=r)g=h,m=d;else break}return g+n+m}else{let f=s.slice(0,-n.length),h=mr(f+n,i);for(;h!==null&&h>r&&f.length>0;)f=f.slice(0,-1),h=mr(f+n,i);return f+n}}return s}function yo(t,e){return t.length!==e.length?!1:t.every(n=>e.includes(n))}function _s(t,e,n){return e?eM(e[t],n):n}function eM(t=[],e){return Array.isArray(e)===!0?e.map((n,r)=>n===null?t[r]:n):t}function xs(t,{domain:e,scale:n,padding:r,nice:i,reverse:a,width:s,height:l,range:u,percentRange:c}){const f=oM(t,s,l,a,u,c),h=n.copy();return h.domain(e),(!h.interpolator||typeof h.interpolator=="function"&&h.interpolator().name.startsWith("identity"))&&h.range(f),r&&h.domain(nM(h,r)),(i===!0||typeof i=="number")&&(typeof h.nice=="function"?h.nice(typeof i=="number"?i:void 0):console.error(`[Layer Chart] You set \`${t}Nice: true\` but the ${t}Scale does not have a \`.nice\` method. Ignoring...`)),h}const tM=["scaleThreshold","scaleQuantile","scaleQuantize","scaleSequentialQuantile"];function nM(t,e){if(typeof t.range!="function")throw new Error("Scale method `range` must be a function");if(typeof t.domain!="function")throw new Error("Scale method `domain` must be a function");if(!Array.isArray(e)||tM.includes(Q1(t))||J1(t)===!0)return t.domain();const{lift:n,ground:r}=hM(t),i=t.domain()[0],a=Object.prototype.toString.call(i)==="[object Date]",[s,l]=t.domain().map(g=>n(a?g.getTime():g)),[u,c]=t.range(),f=e[0]||0,h=e[1]||0,d=(l-s)/(Math.abs(c-u)-f-h);return[s-f*d,h*d+l].map(g=>r(a?new Date(g).getTime():g))}function Nn(t,e=""){return`scale${fl(e)}${fl(t)}`}function Q1(t){if(typeof t.bandwidth=="function")return typeof t.paddingInner=="function"?Nn("band"):Nn("point");if(yo(Object.keys(t),["domain","range","unknown","copy"]))return Nn("ordinal");let e="";if(t.interpolator&&(t.domain().length===3?e="diverging":e="sequential"),t.quantiles)return Nn("quantile",e);if(t.thresholds)return Nn("quantize",e);if(t.constant)return Nn("symlog",e);if(t.base)return Nn("log",e);if(t.exponent)return t.exponent()===.5?Nn("sqrt",e):Nn("pow",e);if(yo(Object.keys(t),["domain","range","invertExtent","unknown","copy"]))return Nn("threshold");if(yo(Object.keys(t),["invert","range","domain","unknown","copy","ticks","tickFormat","nice"]))return Nn("identity");if(yo(Object.keys(t),["invert","domain","range","rangeRound","round","clamp","unknown","copy","ticks","tickFormat","nice"]))return Nn("radial");if(e)return Nn(e);if(t.domain()[0]instanceof Date){const n=new Date;let r="";return n.getDay=()=>r="time",n.getUTCDay=()=>r="utc",t.tickFormat(0,"%a")(n),Nn(r)}return Nn("linear")}function J1(t){return!!(typeof t.bandwidth=="function"||yo(Object.keys(t),["domain","range","unknown","copy"]))}function rM(t,e,n){const r=Object.entries(n).reduce((a,[s,l])=>{const u=J1(l.scale)===!0?"ordinal":"other";return a[u]||(a[u]={}),a[u][s]=e[s],a},{ordinal:!1,other:!1});let i={};if(r.ordinal){const a=Object.fromEntries(Object.entries(n).map(([s,l])=>[s,l.sort]));i=iM(t,r.ordinal,a)}if(r.other){const a=dM(t,r.other);i={...i,...a}}return i}function iM(t,e,n={}){if(!Array.isArray(t))throw new TypeError(`The first argument of calcUniques() must be an array. You passed in a ${typeof t}. If you got this error using the <Chart> component, consider passing a flat array to the \`flatData\` prop`);if(Array.isArray(e)||e===void 0||e===null)throw new TypeError("The second argument of calcUniques() must be an object with field names as keys and accessor functions as values.");const r={},i=Object.keys(e);for(const a of i){const s=new rv,l=e[a];if(!l)continue;for(const c of t){const f=l(c);if(Array.isArray(f))for(const h of f)s.add(h);else s.add(f)}const u=Array.from(s);(n.sort===!0||n[a]===!0)&&u.sort((c,f)=>typeof c=="number"&&typeof f=="number"?c-f:String(c).localeCompare(String(f))),r[a]=u}return r}function aM(t,e,n,r,i){let a,s;return i===!0?(a=0,s=100):(a=t==="r"?1:0,s=t==="y"?n:t==="r"?25:e),r===!0?[s,a]:[a,s]}function oM(t,e,n,r,i,a=!1){return i?typeof i=="function"?i({width:e,height:n}):i:aM(t,e,n,r,a)}function Hd(t){return t}function sM(t){return t.constant?"symlog":t.base?"log":typeof t.exponent=="function"?t.exponent()===.5?"sqrt":"pow":"other"}function lM(t){return e=>Math.log(t*e)}function uM(t){return e=>t*Math.exp(e)}function cM(t){return e=>Math.sign(e)*Math.log1p(Math.abs(e/t))}function fM(t){return e=>Math.sign(e)*Math.expm1(Math.abs(e))*t}function bs(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function hM(t){const e=sM(t);switch(e){case"log":{const n=t.domain(),r=Math.sign(n[0]);return{lift:lM(r),ground:uM(r),scaleType:e}}case"pow":return{lift:bs(1),ground:bs(1/1),scaleType:e};case"sqrt":return{lift:bs(.5),ground:bs(1/.5),scaleType:e};case"symlog":return{lift:cM(1),ground:fM(1),scaleType:e};default:return{lift:Hd,ground:Hd,scaleType:e}}}function ga(t,e){return n=>{const r=t(n);if(e)return Array.isArray(r)?r.map(i=>e(i)):e(r)}}function dM(t,e){if(!Array.isArray(t))throw new TypeError(`The first argument of calcExtents() must be an array. You passed in a ${typeof t}. If you got this error using the <Chart> component, consider passing a flat array to the \`flatData\` prop.`);if(Array.isArray(e)||e===void 0||e===null)throw new TypeError("The second argument of calcExtents() must be an object with field names as keys as accessor functions as values.");const n={},r=Object.keys(e),i=r.length;let a,s,l,u,c,f,h,d;const g=t.length;for(a=0;a<i;a+=1)if(u=r[a],h=e[u],c=null,f=null,!!h){for(s=0;s<g;s+=1)if(d=h(t[s]),Array.isArray(d)){const m=d.length;for(l=0;l<m;l+=1)d[l]!==void 0&&d[l]!==null&&(typeof d[l]=="string"||Number.isNaN(d[l])===!1)&&((c===null||d[l]<c)&&(c=d[l]),(f===null||d[l]>f)&&(f=d[l]))}else d!=null&&(typeof d=="string"||Number.isNaN(d)===!1)&&((c===null||d<c)&&(c=d),(f===null||d>f)&&(f=d));n[u]=[c,f]}return n}function gM(t){var e;t.nextSibling&&((e=t.parentNode)==null||e.appendChild(t))}const ar="    ";function mM(t){Object.entries(t).forEach(([e,n])=>{console.log(`${ar}${e}:`,n)})}function yM(t){const{r:e,g:n,b:r,opacity:i}=rl(t);return[e,n,r].every(a=>a>=0&&a<=255)?{r:e,g:n,b:r,o:i}:!1}function qd(t,e,n=""){const r=t[e](),i=_M(r);i?vM(i,e,r):console.log(`${ar}${ar}${fl(e)}:${n}`,r)}function vM(t,e,n){console.log(`${ar}${ar}${fl(e)}:    %cArray%c(${n.length}) `+t[0]+"%c ]","color: #1377e4","color: #737373","color: #1478e4",...t[1],"color: #1478e4")}function _M(t){const e=[],n=t.map((r,i)=>{const a=yM(r);if(a!==!1){e.push(a);const s=i===t.length-1?" ":"";return`%c ${r}${s}`}return r});return e.length?[`%c[ ${n.join(", ")}`,e.map(r=>`background-color: rgba(${r.r}, ${r.g}, ${r.b}, ${r.o}); color:${bM(r)};`)]:null}function xM(t,e,n){const r=Q1(e);console.log(`${ar}${t}:`),console.log(`${ar}${ar}Accessor: "${n.toString()}"`),console.log(`${ar}${ar}Type: ${r}`),qd(e,"domain"),qd(e,"range"," ")}function bM({r:t,g:e,b:n}){return(.2126*t+.7152*e+.0722*n)/255>.6?"black":"white"}function kM(t){console.log("/********* LayerChart Debug ************/"),console.log("Bounding box:"),mM(t.boundingBox),console.log("Data:"),console.log(ar,t.data),t.flatData&&(console.log("flatData:"),console.log(ar,t.flatData)),console.log("Scales:"),Object.keys(t.activeGetters).forEach(e=>{xM(e,t[`${e}Scale`],t[e])}),console.log(`/************ End LayerChart Debug ***************/
`)}function Ud(t,e={}){return Object.fromEntries(Object.entries(t).filter(([n,r])=>r!==void 0&&e[n]===void 0))}function wM(t){return typeof t=="function"&&typeof t.range=="function"}function ht(t){return typeof t.bandwidth=="function"}function Wc(t){const e=t.domain();return e[0]instanceof Date||e[1]instanceof Date}function ks(t){return wM(t)?t.range():(console.error("[LayerChart] Your scale doesn't have a `.range` method?"),[])}function MM(t){var i;const e=t.domain(),n=t.step(),r=n*(((i=t.paddingOuter)==null?void 0:i.call(t))??t.padding());return function(a){const s=Math.floor((a-r/2)/n);return e[Math.max(0,Math.min(s,e.length-1))]}}function Qr(t,e){var n;return ht(t)?MM(t)(e):(n=t.invert)==null?void 0:n.call(t,e)}function ec(t,e,n,r){const i=t.copy();return e&&i.domain(e),typeof n=="function"?i.range(n(r)):i.range(n),i}function pM(t){return t===0?!0:t}function ws(t){return pM(t)?Array.isArray(t)?e=>t.map(n=>typeof n!="function"?e[n]:n(e)):typeof t!="function"?e=>e[t]:t:null}const $1=typeof window<"u"?window:void 0;function SM(t){let e=t.activeElement;for(;e!=null&&e.shadowRoot;){const n=e.shadowRoot.activeElement;if(n===e)break;e=n}return e}var Ia,qo;class AM{constructor(e={}){gt(this,Ia);gt(this,qo);const{window:n=$1,document:r=n==null?void 0:n.document}=e;n!==void 0&&(_t(this,Ia,r),_t(this,qo,wy(i=>{const a=fh(n,"focusin",i),s=fh(n,"focusout",i);return()=>{a(),s()}})))}get current(){var e;return(e=ie(this,qo))==null||e.call(this),ie(this,Ia)?SM(ie(this,Ia)):null}}Ia=new WeakMap,qo=new WeakMap;new AM;function TM(t){return typeof t=="function"}function em(t,e){if(TM(t)){const r=t();return r===void 0?e:r}return t===void 0?e:t}var Uo,Kr;class ji{constructor(e){gt(this,Uo);gt(this,Kr);_t(this,Uo,e),_t(this,Kr,Symbol(e))}get key(){return ie(this,Kr)}exists(){return uy(ie(this,Kr))}get(){const e=lc(ie(this,Kr));if(e===void 0)throw new Error(`Context "${ie(this,Uo)}" not found`);return e}getOr(e){const n=lc(ie(this,Kr));return n===void 0?e:n}set(e){return eg(ie(this,Kr),e)}}Uo=new WeakMap,Kr=new WeakMap;function CM(t,e){let n=Ve(null);const r=x(()=>em(e,250));function i(...a){if(o(n))o(n).timeout&&clearTimeout(o(n).timeout);else{let s,l;const u=new Promise((c,f)=>{s=c,l=f});Re(n,{timeout:null,runner:null,promise:u,resolve:s,reject:l},!0)}return o(n).runner=async()=>{if(!o(n))return;const s=o(n);Re(n,null);try{s.resolve(await t.apply(this,a))}catch(l){s.reject(l)}},o(n).timeout=setTimeout(o(n).runner,o(r)),o(n).promise}return i.cancel=async()=>{(!o(n)||o(n).timeout===null)&&(await new Promise(a=>setTimeout(a,0)),!o(n)||o(n).timeout===null)||(clearTimeout(o(n).timeout),o(n).reject("Cancelled"),Re(n,null))},i.runScheduledNow=async()=>{var a,s;(!o(n)||!o(n).timeout)&&(await new Promise(l=>setTimeout(l,0)),!o(n)||!o(n).timeout)||(clearTimeout(o(n).timeout),o(n).timeout=null,await((s=(a=o(n)).runner)==null?void 0:s.call(a)))},Object.defineProperty(i,"pending",{enumerable:!0,get(){var a;return!!((a=o(n))!=null&&a.timeout)}}),i}function DM(t,e){switch(t){case"post":Ir(e);break;case"pre":vt(e);break}}function tm(t,e,n,r={}){const{lazy:i=!1}=r;let a=!i,s=Array.isArray(t)?[]:void 0;DM(e,()=>{const l=Array.isArray(t)?t.map(c=>c()):t();if(!a){a=!0,s=l;return}const u=Ao(()=>n(l,s));return s=l,u})}function Ff(t,e,n){tm(t,"post",e,n)}function PM(t,e,n){tm(t,"pre",e,n)}Ff.pre=PM;function RM(t,e,n={}){const{window:r=$1}=n;let i;const a=x(()=>{const l=em(t);return new Set(l?Array.isArray(l)?l:[l]:[])}),s=ng(()=>{Ir(()=>{if(!(!o(a).size||!r)){i=new r.MutationObserver(e);for(const l of o(a))i.observe(l,n);return()=>{i==null||i.disconnect(),i=void 0}}})});return Ir(()=>s),{stop:s,takeRecords(){return i==null?void 0:i.takeRecords()}}}function Ne(t){return`lc-${t}`}function EM(t){return typeof t=="object"&&t!==null&&typeof t!="function"}function Jt(t,e,n){const r=Ne(e);return EM(t)?{...t,class:Le(r,t.class??"",n)}:{class:Le(r,n)}}const Ic={x:0,y:0},zc=1,nm=new ji("TransformContext");function OM(){let t=Ve(Xo(Ic)),e=Ve(zc);return{mode:"none",get scale(){return o(e)},setScale:r=>{Re(e,r,!0)},get translate(){return o(t)},setTranslate:r=>{Re(t,r,!0)},moving:!1,dragging:!1,scrollMode:"none",setScrollMode:()=>{},reset:()=>{},zoomIn:()=>{},zoomOut:()=>{},translateCenter:()=>{},zoomTo:()=>{}}}function nu(){return nm.getOr(OM())}function NM(t){return nm.set(t)}var LM=rt("<div><!></div>");function WM(t,e){it(e,!0);let n=P(e,"mode",3,"none"),r=P(e,"processTranslate",3,(ae,J,de,De)=>({x:ae+de,y:J+De})),i=P(e,"disablePointer",3,!1),a=P(e,"initialScrollMode",3,"none"),s=P(e,"clickDistance",3,10),l=P(e,"ondragend",3,()=>{}),u=P(e,"ondragstart",3,()=>{}),c=P(e,"onTransform",3,()=>{}),f=P(e,"onwheel",3,()=>{}),h=P(e,"onpointerdown",3,()=>{}),d=P(e,"onpointermove",3,()=>{}),g=P(e,"ontouchmove",3,()=>{}),m=P(e,"onpointerup",3,()=>{}),v=P(e,"ondblclick",3,()=>{}),w=P(e,"onclickcapture",3,()=>{}),b=P(e,"ref",15),S=P(e,"transformContext",15),A=bt(e,["$$slots","$$events","$$legacy","mode","motion","processTranslate","disablePointer","initialScrollMode","clickDistance","ondragend","ondragstart","onTransform","initialTranslate","initialScale","onwheel","onpointerdown","onpointermove","ontouchmove","onpointerup","ondblclick","onclickcapture","ref","children","class","transformContext"]),N=Ve(void 0);vt(()=>{b(o(N))}),S({get mode(){return n()},get scale(){return k.current},setScale:K,get translate(){return M.current},setTranslate:V,get dragging(){return o(L)},get moving(){return o(se)},reset:D,zoomIn:W,zoomOut:B,translateCenter:H,zoomTo:$,get scrollMode(){return o(C)},setScrollMode:z});const I=Gt();let T=!1,L=Ve(!1),C=Ve(Xo(a()));const _=Pi(e.motion),M=Pc(e.initialTranslate??Ic,_),k=Pc(e.initialScale??zc,_);let p={x:0,y:0},R={x:0,y:0};function z(ae){Re(C,ae,!0)}function D(){M.target=e.initialTranslate??Ic,k.target=e.initialScale??zc}function W(){ke(1.25,{x:(I.width+I.padding.left)/2,y:(I.height+I.padding.top)/2})}function B(){ke(.8,{x:(I.width+I.padding.left)/2,y:(I.height+I.padding.top)/2})}function H(){M.target={x:0,y:0}}function $(ae,J){const de=J?I.width<I.height?I.width/J.width:I.height/J.height:1;M.target={x:I.width/2-ae.x*de,y:I.height/2-ae.y*de},J&&(k.target=de)}function oe(ae){var J,de;(J=h())==null||J(ae),!(n()==="none"||i())&&(ae.preventDefault(),T=!0,Re(L,!1),p=ei(ae),R=M.current,(de=u())==null||de())}function ye(ae){var Z,ne;if((Z=d())==null||Z(ae),!T)return;ae.preventDefault();const J=ei(ae),de=J.x-p.x,De=J.y-p.y;o(L)||Re(L,de*de+De*De>s()),o(L)&&(ae.stopPropagation(),(ne=ae.currentTarget)==null||ne.setPointerCapture(ae.pointerId),V(r()(R.x,R.y,de,De),M.type==="spring"?{instant:!0}:M.type==="tween"?{duration:0}:void 0))}function ge(ae){var J,de;(J=m())==null||J(ae),T=!1,Re(L,!1),(de=l())==null||de()}function Ee(ae){var J;(J=w())==null||J(ae),o(L)&&ae.stopPropagation()}function xe(ae){var de;if((de=v())==null||de(ae),n()==="none"||i())return;const J=ei(ae);ke(ae.shiftKey?.5:2,J)}function te(ae){var De;if((De=f())==null||De(ae),n()==="none"||i()||o(C)==="none")return;ae.preventDefault();const J=p=ei(ae),de=ae.ctrlKey;if(o(C)==="scale"||de){const Z=-ae.deltaY*(ae.deltaMode===1?.05:ae.deltaMode?1:.002)*(ae.ctrlKey?10:1);ke(Math.pow(2,Z),J,k.type==="spring"?{instant:!0}:k.type==="tween"?{duration:0}:void 0)}else if(o(C)==="translate"){const Z=M.current;M.set(r()(Z.x,Z.y,-ae.deltaX,-ae.deltaY),M.type==="spring"?{instant:!0}:M.type==="tween"?{duration:0}:void 0).then(()=>{}).catch(()=>{})}}function ke(ae,J,de=void 0){const De=k.current,Z=k.current*ae;K(Z,de);const ne={x:(J.x-I.padding.left-M.current.x)/De,y:(J.y-I.padding.top-M.current.y)/De},he={x:J.x-I.padding.left-ne.x*Z,y:J.y-I.padding.top-ne.y*Z};V(he,de)}const Se=od(),we=od(),se=x(()=>o(L)||Se.current||we.current);function V(ae,J){Se.handle(M.set(ae,J))}function K(ae,J){we.handle(k.set(ae,J))}Ff([()=>k.current,()=>M.current],()=>{c()({scale:k.current,translate:M.current})}),NM(S());var G=LM(),be=ae=>{var J;(J=g())==null||J(ae),n()!=="none"&&!i()&&ae.preventDefault()};wt(G,ae=>({onwheel:te,onpointerdown:oe,onpointermove:ye,ontouchmove:be,onpointerup:ge,ondblclick:xe,onclickcapture:Ee,class:ae,...A}),[()=>Le(Ne("transform-context"),"h-full",e.class)]);var _e=nt(G);return Xe(_e,()=>e.children??Rt,()=>({transformContext:S()})),$e(G),Wt(G,ae=>Re(N,ae),()=>o(N)),Q(t,G),at({setScrollMode:z,reset:D,zoomIn:W,zoomOut:B,translateCenter:H,zoomTo:$,setTranslate:V,setScale:K})}const rm=new ji("GeoContext");function Bf(){return rm.getOr({projection:void 0})}function IM(t){return rm.set(t)}function zM(t,e){it(e,!0);let n=P(e,"applyTransform",19,()=>[]),r=P(e,"geoContext",15);const i=Gt(),a=nu();let s=Ve(void 0);const l={get projection(){return o(s)},set projection(h){Re(s,h,!0)}};r(l),IM(l);const u=x(()=>e.fixedAspectRatio?[100,100/e.fixedAspectRatio]:[i.width,i.height]);vt(()=>{if(!e.projection)return;const h=e.projection();e.fitGeojson&&"fitSize"in h&&h.fitSize(o(u),e.fitGeojson),"scale"in h&&(e.scale&&h.scale(e.scale),n().includes("scale")&&h.scale(a.scale)),"rotate"in h&&(e.rotate&&h.rotate([e.rotate.yaw,e.rotate.pitch,e.rotate.roll]),n().includes("rotate")&&h.rotate([a.translate.x,a.translate.y])),"translate"in h&&(e.translate&&h.translate(e.translate),n().includes("translate")&&h.translate([a.translate.x,a.translate.y])),e.center&&"center"in h&&h.center(e.center),e.reflectX&&h.reflectX(e.reflectX),e.reflectY&&h.reflectY(e.reflectY),e.clipAngle&&"clipAngle"in h&&h.clipAngle(e.clipAngle),e.clipExtent&&"clipExtent"in h&&h.clipExtent(e.clipExtent),l.projection=h});var c=Ce(),f=ce(c);Xe(f,()=>e.children,()=>({geoContext:l})),Q(t,c),at()}function jM(t){const e=+this._x.call(null,t),n=+this._y.call(null,t);return im(this.cover(e,n),e,n,t)}function im(t,e,n,r){if(isNaN(e)||isNaN(n))return t;var i,a=t._root,s={data:r},l=t._x0,u=t._y0,c=t._x1,f=t._y1,h,d,g,m,v,w,b,S;if(!a)return t._root=s,t;for(;a.length;)if((v=e>=(h=(l+c)/2))?l=h:c=h,(w=n>=(d=(u+f)/2))?u=d:f=d,i=a,!(a=a[b=w<<1|v]))return i[b]=s,t;if(g=+t._x.call(null,a.data),m=+t._y.call(null,a.data),e===g&&n===m)return s.next=a,i?i[b]=s:t._root=s,t;do i=i?i[b]=new Array(4):t._root=new Array(4),(v=e>=(h=(l+c)/2))?l=h:c=h,(w=n>=(d=(u+f)/2))?u=d:f=d;while((b=w<<1|v)===(S=(m>=d)<<1|g>=h));return i[S]=a,i[b]=s,t}function FM(t){var e,n,r=t.length,i,a,s=new Array(r),l=new Array(r),u=1/0,c=1/0,f=-1/0,h=-1/0;for(n=0;n<r;++n)isNaN(i=+this._x.call(null,e=t[n]))||isNaN(a=+this._y.call(null,e))||(s[n]=i,l[n]=a,i<u&&(u=i),i>f&&(f=i),a<c&&(c=a),a>h&&(h=a));if(u>f||c>h)return this;for(this.cover(u,c).cover(f,h),n=0;n<r;++n)im(this,s[n],l[n],t[n]);return this}function BM(t,e){if(isNaN(t=+t)||isNaN(e=+e))return this;var n=this._x0,r=this._y0,i=this._x1,a=this._y1;if(isNaN(n))i=(n=Math.floor(t))+1,a=(r=Math.floor(e))+1;else{for(var s=i-n||1,l=this._root,u,c;n>t||t>=i||r>e||e>=a;)switch(c=(e<r)<<1|t<n,u=new Array(4),u[c]=l,l=u,s*=2,c){case 0:i=n+s,a=r+s;break;case 1:n=i-s,a=r+s;break;case 2:i=n+s,r=a-s;break;case 3:n=i-s,r=a-s;break}this._root&&this._root.length&&(this._root=l)}return this._x0=n,this._y0=r,this._x1=i,this._y1=a,this}function YM(){var t=[];return this.visit(function(e){if(!e.length)do t.push(e.data);while(e=e.next)}),t}function HM(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]}function Fn(t,e,n,r,i){this.node=t,this.x0=e,this.y0=n,this.x1=r,this.y1=i}function qM(t,e,n){var r,i=this._x0,a=this._y0,s,l,u,c,f=this._x1,h=this._y1,d=[],g=this._root,m,v;for(g&&d.push(new Fn(g,i,a,f,h)),n==null?n=1/0:(i=t-n,a=e-n,f=t+n,h=e+n,n*=n);m=d.pop();)if(!(!(g=m.node)||(s=m.x0)>f||(l=m.y0)>h||(u=m.x1)<i||(c=m.y1)<a))if(g.length){var w=(s+u)/2,b=(l+c)/2;d.push(new Fn(g[3],w,b,u,c),new Fn(g[2],s,b,w,c),new Fn(g[1],w,l,u,b),new Fn(g[0],s,l,w,b)),(v=(e>=b)<<1|t>=w)&&(m=d[d.length-1],d[d.length-1]=d[d.length-1-v],d[d.length-1-v]=m)}else{var S=t-+this._x.call(null,g.data),A=e-+this._y.call(null,g.data),N=S*S+A*A;if(N<n){var I=Math.sqrt(n=N);i=t-I,a=e-I,f=t+I,h=e+I,r=g.data}}return r}function UM(t){if(isNaN(f=+this._x.call(null,t))||isNaN(h=+this._y.call(null,t)))return this;var e,n=this._root,r,i,a,s=this._x0,l=this._y0,u=this._x1,c=this._y1,f,h,d,g,m,v,w,b;if(!n)return this;if(n.length)for(;;){if((m=f>=(d=(s+u)/2))?s=d:u=d,(v=h>=(g=(l+c)/2))?l=g:c=g,e=n,!(n=n[w=v<<1|m]))return this;if(!n.length)break;(e[w+1&3]||e[w+2&3]||e[w+3&3])&&(r=e,b=w)}for(;n.data!==t;)if(i=n,!(n=n.next))return this;return(a=n.next)&&delete n.next,i?(a?i.next=a:delete i.next,this):e?(a?e[w]=a:delete e[w],(n=e[0]||e[1]||e[2]||e[3])&&n===(e[3]||e[2]||e[1]||e[0])&&!n.length&&(r?r[b]=n:this._root=n),this):(this._root=a,this)}function GM(t){for(var e=0,n=t.length;e<n;++e)this.remove(t[e]);return this}function XM(){return this._root}function VM(){var t=0;return this.visit(function(e){if(!e.length)do++t;while(e=e.next)}),t}function ZM(t){var e=[],n,r=this._root,i,a,s,l,u;for(r&&e.push(new Fn(r,this._x0,this._y0,this._x1,this._y1));n=e.pop();)if(!t(r=n.node,a=n.x0,s=n.y0,l=n.x1,u=n.y1)&&r.length){var c=(a+l)/2,f=(s+u)/2;(i=r[3])&&e.push(new Fn(i,c,f,l,u)),(i=r[2])&&e.push(new Fn(i,a,f,c,u)),(i=r[1])&&e.push(new Fn(i,c,s,l,f)),(i=r[0])&&e.push(new Fn(i,a,s,c,f))}return this}function KM(t){var e=[],n=[],r;for(this._root&&e.push(new Fn(this._root,this._x0,this._y0,this._x1,this._y1));r=e.pop();){var i=r.node;if(i.length){var a,s=r.x0,l=r.y0,u=r.x1,c=r.y1,f=(s+u)/2,h=(l+c)/2;(a=i[0])&&e.push(new Fn(a,s,l,f,h)),(a=i[1])&&e.push(new Fn(a,f,l,u,h)),(a=i[2])&&e.push(new Fn(a,s,h,f,c)),(a=i[3])&&e.push(new Fn(a,f,h,u,c))}n.push(r)}for(;r=n.pop();)t(r.node,r.x0,r.y0,r.x1,r.y1);return this}function QM(t){return t[0]}function JM(t){return arguments.length?(this._x=t,this):this._x}function $M(t){return t[1]}function ep(t){return arguments.length?(this._y=t,this):this._y}function am(t,e,n){var r=new Yf(e??QM,n??$M,NaN,NaN,NaN,NaN);return t==null?r:r.addAll(t)}function Yf(t,e,n,r,i,a){this._x=t,this._y=e,this._x0=n,this._y0=r,this._x1=i,this._y1=a,this._root=void 0}function Gd(t){for(var e={data:t.data},n=e;t=t.next;)n=n.next={data:t.data};return e}var Bn=am.prototype=Yf.prototype;Bn.copy=function(){var t=new Yf(this._x,this._y,this._x0,this._y0,this._x1,this._y1),e=this._root,n,r;if(!e)return t;if(!e.length)return t._root=Gd(e),t;for(n=[{source:e,target:t._root=new Array(4)}];e=n.pop();)for(var i=0;i<4;++i)(r=e.source[i])&&(r.length?n.push({source:r,target:e.target[i]=new Array(4)}):e.target[i]=Gd(r));return t};Bn.add=jM;Bn.addAll=FM;Bn.cover=BM;Bn.data=YM;Bn.extent=HM;Bn.find=qM;Bn.remove=UM;Bn.removeAll=GM;Bn.root=XM;Bn.size=VM;Bn.visit=ZM;Bn.visitAfter=KM;Bn.x=JM;Bn.y=ep;var tp=xt("<title> </title>"),np=xt("<g><!></g>"),rp=xt("<svg><!><defs><!></defs><g><!></g></svg>");function Ls(t,e){it(e,!0);let n=P(e,"ref",15),r=P(e,"innerRef",15),i=P(e,"zIndex",3,0),a=P(e,"ignoreTransform",3,!1),s=P(e,"center",3,!1),l=bt(e,["$$slots","$$events","$$legacy","ref","innerRef","zIndex","pointerEvents","viewBox","ignoreTransform","center","class","title","defs","children"]),u=Ve(void 0),c=Ve(void 0);vt(()=>{n(o(u))}),vt(()=>{r(o(c))});const f=Gt(),h=nu(),d=x(()=>{if(h.mode==="canvas"&&!a())return`translate(${h.translate.x},${h.translate.y}) scale(${h.scale})`;if(s())return`translate(${s()==="x"||s()===!0?f.width/2:0}, ${s()==="y"||s()===!0?f.height/2:0})`});Zf("svg");var g=rp();wt(g,L=>({viewBox:e.viewBox,width:f.containerWidth,height:f.containerHeight,class:L,role:"figure",...l,[ir]:{"z-index":i()}}),[()=>Le(Ne("layout-svg"),"absolute top-0 left-0 overflow-visible",e.pointerEvents===!1&&"pointer-events-none",e.class)]);var m=nt(g);{var v=L=>{var C=Ce(),_=ce(C);Xe(_,()=>e.title),Q(L,C)},w=(L,C)=>{{var _=M=>{var k=tp(),p=nt(k,!0);$e(k),mt(R=>{St(k,0,R),Jn(p,e.title)},[()=>pt(Ne("layout-svg-title"))]),Q(M,k)};ue(L,M=>{e.title&&M(_)},C)}};ue(m,L=>{typeof e.title=="function"?L(v):L(w,!1)})}var b=Fe(m),S=nt(b);Xe(S,()=>e.defs??Rt),$e(b);var A=Fe(b),N=nt(A);{var I=L=>{var C=np(),_=nt(C);Xe(_,()=>e.children??Rt,()=>({ref:o(u)})),$e(C),mt(M=>{ft(C,"transform",o(d)),St(C,0,M)},[()=>pt(Ne("layout-svg-g-transform"))]),Q(L,C)},T=L=>{var C=Ce(),_=ce(C);Xe(_,()=>e.children??Rt,()=>({ref:o(u)})),Q(L,C)};ue(N,L=>{o(d)?L(I):L(T,!1)})}$e(A),Wt(A,L=>Re(c,L),()=>o(c)),$e(g),Wt(g,L=>Re(u,L),()=>o(u)),mt(L=>{St(A,0,L),ft(A,"transform",`translate(${f.padding.left??""}, ${f.padding.top??""})`)},[()=>pt(Ne("layout-svg-g"))]),Q(t,g),at()}const ip="rgb(0, 0, 0)",Xd="__layerchart_canvas_styles_id";function hl(t,{styles:e,classes:n}={}){try{let r=document.getElementById(Xd);return r||(r=document.createElementNS("http://www.w3.org/2000/svg","svg"),r.setAttribute("id",Xd),r.style.display="none",t.after(r)),r=r,r.removeAttribute("style"),r.removeAttribute("class"),e&&Object.assign(r.style,e),n&&r.setAttribute("class",Le(n).split(" ").filter(a=>!a.startsWith("transition-")).join(" ")),window.getComputedStyle(r)}catch(r){return console.error("Unable to get computed styles",r),{}}}function ru(t,e,n={}){var a,s,l,u,c,f,h,d,g,m,v;const r=hl(t.canvas,n),i=(r==null?void 0:r.paintOrder)==="stroke"?["stroke","fill"]:["fill","stroke"];if(r!=null&&r.opacity&&(t.globalAlpha=Number(r==null?void 0:r.opacity)),t.font=`${r.fontWeight} ${r.fontSize} ${r.fontFamily}`,r.textAnchor==="middle"?t.textAlign="center":r.textAnchor==="end"?t.textAlign="right":t.textAlign=r.textAlign,r.strokeDasharray.includes(",")){const w=r.strokeDasharray.split(",").map(b=>Number(b.replace("px","")));t.setLineDash(w)}for(const w of i)if(w==="fill"){const b=(a=n.styles)!=null&&a.fill&&(((s=n.styles)==null?void 0:s.fill)instanceof CanvasGradient||((l=n.styles)==null?void 0:l.fill)instanceof CanvasPattern||!((c=(u=n.styles)==null?void 0:u.fill)!=null&&c.includes("var")))?n.styles.fill:r==null?void 0:r.fill;if(b&&!["none",ip].includes(b)){const S=t.globalAlpha,A=Number(r==null?void 0:r.fillOpacity),N=Number(r==null?void 0:r.opacity);t.globalAlpha=A*N,t.fillStyle=b,e.fill(t),t.globalAlpha=S}}else if(w==="stroke"){const b=(f=n.styles)!=null&&f.stroke&&(((h=n.styles)==null?void 0:h.stroke)instanceof CanvasGradient||!((g=(d=n.styles)==null?void 0:d.stroke)!=null&&g.includes("var")))?(m=n.styles)==null?void 0:m.stroke:r==null?void 0:r.stroke;b&&!["none"].includes(b)&&(t.lineWidth=typeof(r==null?void 0:r.strokeWidth)=="string"?Number((v=r==null?void 0:r.strokeWidth)==null?void 0:v.replace("px","")):(r==null?void 0:r.strokeWidth)??1,t.strokeStyle=b,e.stroke(t))}}function iu(t,e,n={}){const r=new Path2D(e??"");ru(t,{fill:i=>i.fill(r),stroke:i=>i.stroke(r)},n)}function ap(t,e,n,r={}){e&&ru(t,{fill:i=>i.fillText(e.toString(),n.x,n.y),stroke:i=>i.strokeText(e.toString(),n.x,n.y)},r)}function op(t,e,n={}){ru(t,{fill:r=>r.fillRect(e.x,e.y,e.width,e.height),stroke:r=>r.strokeRect(e.x,e.y,e.width,e.height)},n)}function om(t,e,n={}){t.beginPath(),t.arc(e.cx,e.cy,e.r,0,2*Math.PI),ru(t,{fill:r=>{r.fill()},stroke:r=>{r.stroke()}},n),t.closePath()}function Vd(t,e,n){const r=window.devicePixelRatio||1;return t.canvas.width=e*r,t.canvas.height=n*r,t.canvas.style.width=`${e}px`,t.canvas.style.height=`${n}px`,t.scale(r,r),{width:t.canvas.width,height:t.canvas.height}}function sp(t,e,n){const r=window.devicePixelRatio??1,i=t.getImageData(e*r,n*r,1,1),[a,s,l,u]=i.data;return{r:a,g:s,b:l,a:u}}function lp(t,e,n,r,i,a){const s=t.createLinearGradient(e,n,r,i);for(const{offset:l,color:u}of a)s.addColorStop(l,u);return s}const up=$a(lp,(t,e,n,r,i,a)=>JSON.stringify({x0:e,y0:n,x1:r,y1:i,stops:a}));function cp(t,e,n,r,i){var u;const a=document.createElement("canvas"),s=a.getContext("2d");t.canvas.after(a),a.width=e,a.height=n,i&&(s.fillStyle=i,s.fillRect(0,0,e,n));for(const c of r)s.save(),c.type==="circle"?om(s,{cx:c.cx,cy:c.cy,r:c.r},{styles:{fill:c.fill,opacity:c.opacity}}):c.type==="line"&&iu(s,c.path,{styles:{stroke:c.stroke,strokeWidth:c.strokeWidth,opacity:c.opacity}}),s.restore();const l=t.createPattern(a,"repeat");return(u=t.canvas.parentElement)==null||u.removeChild(a),l}const fp=$a(cp,(t,e,n,r,i)=>JSON.stringify({width:e,height:n,shapes:r,background:i}));function*Zd(t=500){let e=0;for(;e<16777216;){const n=e&255,r=(e&65280)>>8,i=(e&16711680)>>16;e+=t,yield{r:n,g:r,b:i,a:255}}return{r:0,g:0,b:0,a:255}}function Kd(t){return t.a!==void 0?`rgba(${t.r},${t.g},${t.b},${t.a})`:`rgb(${t.r},${t.g},${t.b})`}const sm=new ji("CanvasContext"),hp={register:t=>()=>{},invalidate:()=>{}};function dp(){return sm.getOr(hp)}function gp(t){return sm.set(t)}function mi(t){const e=dp();vt(()=>Ao(()=>e.register(t)))}var mp=rt("<canvas><!></canvas> <canvas></canvas> <!>",1);function yp(t,e){it(e,!0);let n=P(e,"ref",15),r=P(e,"canvasContext",15),i=P(e,"willReadFrequently",3,!1),a=P(e,"debug",3,!1),s=P(e,"zIndex",3,0),l=P(e,"pointerEvents",3,!0),u=P(e,"center",3,!1),c=P(e,"ignoreTransform",3,!1),f=P(e,"disableHitCanvas",3,!1),h=bt(e,["$$slots","$$events","$$legacy","ref","canvasContext","willReadFrequently","debug","zIndex","pointerEvents","fallback","center","ignoreTransform","disableHitCanvas","class","children","onclick","ondblclick","onpointerenter","onpointermove","onpointerleave","onpointerdown","ontouchmove"]),d=Ve(void 0),g=Ve(void 0);vt(()=>{n(o(d))}),vt(()=>{r(o(g))});const m=Gt(),v=nu(),w=new qg("Canvas");let b=new Map,S=!1,A,N=Ve(void 0),I=Ve(void 0),T=Zd(),L=Ve(!1),C=null;const _=new Map;function M(V){const{x:K,y:G}=ei(V),be=sp(o(I),K,G),_e=Kd(be),ae=_.get(_e);return w.debug({colorKey:_e,component:ae,componentByColor:_}),ae}const k=V=>{var G,be,_e,ae,J,de,De,Z,ne,he;Re(L,!0);const K=M(V);K!=C&&(C&&((be=(G=C.events)==null?void 0:G.pointerleave)==null||be.call(G,V),(ae=(_e=C.events)==null?void 0:_e.pointerout)==null||ae.call(_e,V)),(de=(J=K==null?void 0:K.events)==null?void 0:J.pointerenter)==null||de.call(J,V),(Z=(De=K==null?void 0:K.events)==null?void 0:De.pointerover)==null||Z.call(De,V)),(he=(ne=K==null?void 0:K.events)==null?void 0:ne.pointermove)==null||he.call(ne,V),C=K},p=V=>{var K,G,be,_e;(G=(K=C==null?void 0:C.events)==null?void 0:K.pointerleave)==null||G.call(K,V),(_e=(be=C==null?void 0:C.events)==null?void 0:be.pointerout)==null||_e.call(be,V),C=null,Re(L,!1)},{dark:R}=new fk;Ff(()=>R.current,()=>{W.invalidate()}),RM(()=>document.documentElement,()=>W.invalidate(),{attributes:!0,attributeFilter:["class","data-theme"]}),df(()=>{var V,K;return Re(g,(V=o(d))==null?void 0:V.getContext("2d",{willReadFrequently:i()}),!0),Re(I,(K=o(N))==null?void 0:K.getContext("2d",{willReadFrequently:!1}),!0),()=>{A&&cancelAnimationFrame(A)}});function z(){if(!o(g))return;Vd(o(g),m.containerWidth,m.containerHeight),o(g).clearRect(0,0,m.containerWidth,m.containerHeight),o(g).translate(m.padding.left??0,m.padding.top??0);let V;u()?(V={x:u()==="x"||u()===!0?m.width/2:0,y:u()==="y"||u()===!0?m.height/2:0},o(g).translate(V.x,V.y)):v.mode==="canvas"&&!c()&&(o(g).translate(v.translate.x,v.translate.y),o(g).scale(v.scale,v.scale));const K=[],G=[];for(const[_e,ae]of b)ae.retainState?K.push(ae):G.push(ae);for(const _e of K)_e.render(o(g));const be=o(g).getTransform();for(const _e of G)o(g).save(),_e.render(o(g)),o(g).restore();if(o(I)){Vd(o(I),m.containerWidth,m.containerHeight),o(I).clearRect(0,0,m.containerWidth,m.containerHeight),o(I).resetTransform(),o(I).setTransform(be),T=Zd();const _e=!o(L)&&v.moving;for(const ae of K)ae.events&&Object.values(ae.events).filter(de=>de).length>0&&!_e&&v.dragging;for(const ae of G)if(ae.events&&Object.values(ae.events).filter(de=>de).length>0&&!_e&&!v.dragging&&!f()){const de=Kd(T.next().value),De={styles:{fill:de,stroke:de,_fillOpacity:.1}};o(I).save(),ae.render(o(I),De),o(I).restore(),_.set(de,ae)}}S=!1}function D(){function V(G){const be=Symbol();b.set(be,G),K();const _e=ng(()=>{G.deps&&vt(()=>{var ae;(ae=G.deps)==null||ae.call(G),K()})});return vt(()=>_e),()=>{b.delete(be),_e(),K()}}function K(){S||(S=!0,A=requestAnimationFrame(z))}return{register:V,invalidate:K}}const W=D();vt(()=>{m.height,m.width,m.containerHeight,m.containerWidth,v.dragging,W.invalidate()}),gp(W),Zf("canvas");var B=mp(),H=ce(B),$=V=>{var G,be,_e;const K=M(V);(be=(G=K==null?void 0:K.events)==null?void 0:G.click)==null||be.call(G,V),(_e=e.onclick)==null||_e.call(e,V)},oe=V=>{var G,be,_e;const K=M(V);(be=(G=K==null?void 0:K.events)==null?void 0:G.dblclick)==null||be.call(G,V),(_e=e.ondblclick)==null||_e.call(e,V)},ye=V=>{var G,be,_e;const K=M(V);(be=(G=K==null?void 0:K.events)==null?void 0:G.pointerdown)==null||be.call(G,V),(_e=e.onpointerdown)==null||_e.call(e,V)},ge=V=>{var K;(K=e.onpointerenter)==null||K.call(e,V),k(V)},Ee=V=>{var K;(K=e.onpointermove)==null||K.call(e,V),k(V)},xe=V=>{var K;(K=e.onpointerleave)==null||K.call(e,V),p(V)},te=V=>{var G,be;C&&V.preventDefault();const K=M(V);(be=(G=K==null?void 0:K.events)==null?void 0:G.touchmove)==null||be.call(G,V)};wt(H,V=>({class:V,onclick:$,ondblclick:oe,onpointerdown:ye,onpointerenter:ge,onpointermove:Ee,onpointerleave:xe,ontouchmove:te,...h,[ir]:{"z-index":s()}}),[()=>Le(Ne("layout-canvas"),"absolute top-0 left-0 w-full h-full",l()===!1&&"pointer-events-none",e.class)]);var ke=nt(H);{var Se=V=>{var K=Ce(),G=ce(K);{var be=ae=>{var J=Ce(),de=ce(J);Xe(de,()=>e.fallback),Q(ae,J)},_e=ae=>{var J=So();mt(()=>Jn(J,e.fallback)),Q(ae,J)};ue(G,ae=>{typeof e.fallback=="function"?ae(be):ae(_e,!1)})}Q(V,K)};ue(ke,V=>{e.fallback&&V(Se)})}$e(H),Wt(H,V=>Re(d,V),()=>o(d));var we=Fe(H,2);Wt(we,V=>Re(N,V),()=>o(N));var se=Fe(we,2);Xe(se,()=>e.children??Rt,()=>({ref:o(d),canvasContext:o(g)})),mt(V=>St(we,1,V),[()=>pt(Le(Ne("hit-canvas"),"layerchart-hitcanvas","absolute top-0 left-0 w-full h-full","pointer-events-none","border border-danger",!a()&&"opacity-0"))]),Q(t,B),at()}function cr(t){const e=x(t),n=x(()=>o(e)&&typeof o(e)=="object"?n2(o(e)):o(e));return{get current(){return o(n)}}}var vp=xt("<rect></rect>");function Da(t,e){it(e,!0);let n=P(e,"x",3,0),r=P(e,"y",3,0),i=P(e,"initialX",19,n),a=P(e,"initialY",19,r),s=P(e,"initialHeight",19,()=>e.height),l=P(e,"initialWidth",19,()=>e.width),u=P(e,"ref",15),c=bt(e,["$$slots","$$events","$$legacy","height","width","x","y","initialX","initialY","fill","fillOpacity","stroke","initialHeight","initialWidth","strokeWidth","opacity","ref","motion","class","onclick","ondblclick","onpointerenter","onpointermove","onpointerleave","onpointerover","onpointerout"]),f=Ve(void 0);vt(()=>{u(o(f))});const h=on(i(),()=>n(),Pi(e.motion,"x")),d=on(a(),()=>r(),Pi(e.motion,"y")),g=on(l(),()=>e.width,Pi(e.motion,"width")),m=on(s(),()=>e.height,Pi(e.motion,"height")),v=Fr();function w(T,L){op(T,{x:h.current,y:d.current,width:g.current,height:m.current},L?to({styles:{strokeWidth:e.strokeWidth}},L):{styles:{fill:e.fill,fillOpacity:e.fillOpacity,stroke:e.stroke,strokeWidth:e.strokeWidth,opacity:e.opacity},classes:e.class})}const b=cr(()=>e.fill),S=cr(()=>e.stroke);v==="canvas"&&mi({name:"Rect",render:w,events:{click:e.onclick,dblclick:e.ondblclick,pointerenter:e.onpointerenter,pointermove:e.onpointermove,pointerleave:e.onpointerleave,pointerover:e.onpointerover,pointerout:e.onpointerout},deps:()=>[h.current,d.current,g.current,m.current,b.current,S.current,e.strokeWidth,e.opacity,e.class]});var A=Ce(),N=ce(A);{var I=T=>{var L=vp();wt(L,C=>({x:h.current,y:d.current,width:g.current,height:m.current,fill:e.fill,"fill-opacity":e.fillOpacity,stroke:e.stroke,"stroke-width":e.strokeWidth,opacity:e.opacity,class:C,...c,onclick:e.onclick,ondblclick:e.ondblclick,onpointerenter:e.onpointerenter,onpointermove:e.onpointermove,onpointerleave:e.onpointerleave,onpointerover:e.onpointerover,onpointerout:e.onpointerout}),[()=>Le(Ne("rect"),e.fill==null&&"fill-surface-content",e.class)]),Wt(L,C=>Re(f,C),()=>o(f)),Q(T,L)};ue(N,T=>{v==="svg"&&T(I)})}Q(t,A),at()}function Rn(t,e){return`${t}-${e}`}var _p=xt("<use></use>"),xp=xt("<defs><clipPath><!><!></clipPath></defs>"),bp=xt("<g><!></g>"),kp=xt("<!><!>",1);function lm(t,e){const n=Mr();it(e,!0);let r=P(e,"id",19,()=>Rn("clipPath-",n)),i=P(e,"disabled",3,!1),a=bt(e,["$$slots","$$events","$$legacy","id","useId","disabled","children","clip"]);const s=x(()=>`url(#${r()})`),l=Fr();var u=kp(),c=ce(u);{var f=g=>{var m=xp(),v=nt(m);wt(v,()=>({id:r(),...a}));var w=nt(v);Xe(w,()=>e.clip??Rt,()=>({id:r()}));var b=Fe(w);{var S=A=>{var N=_p();mt(()=>ft(N,"href",`#${e.useId??""}`)),Q(A,N)};ue(b,A=>{e.useId&&A(S)})}$e(v),$e(m),Q(g,m)};ue(c,g=>{l==="svg"&&g(f)})}var h=Fe(c);{var d=g=>{var m=Ce(),v=ce(m);{var w=S=>{var A=Ce(),N=ce(A);Xe(N,()=>e.children,()=>({id:r(),url:o(s),useId:e.useId})),Q(S,A)},b=S=>{var A=bp();let N;var I=nt(A);Xe(I,()=>e.children,()=>({id:r(),url:o(s),useId:e.useId})),$e(A),mt(T=>{St(A,0,T),N=wr(A,"",N,{"clip-path":o(s)})},[()=>pt(Ne("clip-path-g"))]),Q(S,A)};ue(v,S=>{i()||l!=="svg"?S(w):S(b,!1)})}Q(g,m)};ue(h,g=>{e.children&&g(d)})}Q(t,u),at()}function wp(t,e){const n=Mr();it(e,!0);let r=P(e,"id",19,()=>Rn("clipPath-",n)),i=P(e,"x",3,0),a=P(e,"y",3,0),s=P(e,"disabled",3,!1),l=bt(e,["$$slots","$$events","$$legacy","id","x","y","disabled","children"]);lm(t,{get id(){return r()},get disabled(){return s()},clip:f=>{var h=x(()=>Jt(l,"clip-path-rect"));Da(f,qe({get x(){return i()},get y(){return a()}},()=>o(h)))},children:(f,h)=>{let d=()=>h==null?void 0:h().url;var g=Ce(),m=ce(g);Xe(m,()=>e.children??Rt,()=>({id:r(),url:d()})),Q(f,g)},$$slots:{clip:!0,default:!0}}),at()}function jc(t,e){it(e,!0);let n=P(e,"full",3,!1),r=P(e,"disabled",3,!1),i=bt(e,["$$slots","$$events","$$legacy","full","disabled"]);const a=Gt(),s=x(()=>n()&&a.padding.left?-a.padding.left:0),l=x(()=>n()&&a.padding.top?-a.padding.top:0),u=x(()=>{var h,d;return a.height+(n()?(((h=a.padding)==null?void 0:h.top)??0)+(((d=a.padding)==null?void 0:d.bottom)??0):0)}),c=x(()=>{var h,d;return a.width+(n()?(((h=a.padding)==null?void 0:h.left)??0)+(((d=a.padding)==null?void 0:d.right)??0):0)});var f=x(()=>Jt(i,"chart-clip-path"));wp(t,qe({get x(){return o(s)},get y(){return o(l)},get disabled(){return r()},get height(){return o(u)},get width(){return o(c)}},()=>o(f))),at()}const ai=11102230246251565e-32,Dn=134217729,Mp=(3+8*ai)*ai;function tc(t,e,n,r,i){let a,s,l,u,c=e[0],f=r[0],h=0,d=0;f>c==f>-c?(a=c,c=e[++h]):(a=f,f=r[++d]);let g=0;if(h<t&&d<n)for(f>c==f>-c?(s=c+a,l=a-(s-c),c=e[++h]):(s=f+a,l=a-(s-f),f=r[++d]),a=s,l!==0&&(i[g++]=l);h<t&&d<n;)f>c==f>-c?(s=a+c,u=s-a,l=a-(s-u)+(c-u),c=e[++h]):(s=a+f,u=s-a,l=a-(s-u)+(f-u),f=r[++d]),a=s,l!==0&&(i[g++]=l);for(;h<t;)s=a+c,u=s-a,l=a-(s-u)+(c-u),c=e[++h],a=s,l!==0&&(i[g++]=l);for(;d<n;)s=a+f,u=s-a,l=a-(s-u)+(f-u),f=r[++d],a=s,l!==0&&(i[g++]=l);return(a!==0||g===0)&&(i[g++]=a),g}function pp(t,e){let n=e[0];for(let r=1;r<t;r++)n+=e[r];return n}function ts(t){return new Float64Array(t)}const Sp=(3+16*ai)*ai,Ap=(2+12*ai)*ai,Tp=(9+64*ai)*ai*ai,ma=ts(4),Qd=ts(8),Jd=ts(12),$d=ts(16),Ln=ts(4);function Cp(t,e,n,r,i,a,s){let l,u,c,f,h,d,g,m,v,w,b,S,A,N,I,T,L,C;const _=t-i,M=n-i,k=e-a,p=r-a;N=_*p,d=Dn*_,g=d-(d-_),m=_-g,d=Dn*p,v=d-(d-p),w=p-v,I=m*w-(N-g*v-m*v-g*w),T=k*M,d=Dn*k,g=d-(d-k),m=k-g,d=Dn*M,v=d-(d-M),w=M-v,L=m*w-(T-g*v-m*v-g*w),b=I-L,h=I-b,ma[0]=I-(b+h)+(h-L),S=N+b,h=S-N,A=N-(S-h)+(b-h),b=A-T,h=A-b,ma[1]=A-(b+h)+(h-T),C=S+b,h=C-S,ma[2]=S-(C-h)+(b-h),ma[3]=C;let R=pp(4,ma),z=Ap*s;if(R>=z||-R>=z||(h=t-_,l=t-(_+h)+(h-i),h=n-M,c=n-(M+h)+(h-i),h=e-k,u=e-(k+h)+(h-a),h=r-p,f=r-(p+h)+(h-a),l===0&&u===0&&c===0&&f===0)||(z=Tp*s+Mp*Math.abs(R),R+=_*f+p*l-(k*c+M*u),R>=z||-R>=z))return R;N=l*p,d=Dn*l,g=d-(d-l),m=l-g,d=Dn*p,v=d-(d-p),w=p-v,I=m*w-(N-g*v-m*v-g*w),T=u*M,d=Dn*u,g=d-(d-u),m=u-g,d=Dn*M,v=d-(d-M),w=M-v,L=m*w-(T-g*v-m*v-g*w),b=I-L,h=I-b,Ln[0]=I-(b+h)+(h-L),S=N+b,h=S-N,A=N-(S-h)+(b-h),b=A-T,h=A-b,Ln[1]=A-(b+h)+(h-T),C=S+b,h=C-S,Ln[2]=S-(C-h)+(b-h),Ln[3]=C;const D=tc(4,ma,4,Ln,Qd);N=_*f,d=Dn*_,g=d-(d-_),m=_-g,d=Dn*f,v=d-(d-f),w=f-v,I=m*w-(N-g*v-m*v-g*w),T=k*c,d=Dn*k,g=d-(d-k),m=k-g,d=Dn*c,v=d-(d-c),w=c-v,L=m*w-(T-g*v-m*v-g*w),b=I-L,h=I-b,Ln[0]=I-(b+h)+(h-L),S=N+b,h=S-N,A=N-(S-h)+(b-h),b=A-T,h=A-b,Ln[1]=A-(b+h)+(h-T),C=S+b,h=C-S,Ln[2]=S-(C-h)+(b-h),Ln[3]=C;const W=tc(D,Qd,4,Ln,Jd);N=l*f,d=Dn*l,g=d-(d-l),m=l-g,d=Dn*f,v=d-(d-f),w=f-v,I=m*w-(N-g*v-m*v-g*w),T=u*c,d=Dn*u,g=d-(d-u),m=u-g,d=Dn*c,v=d-(d-c),w=c-v,L=m*w-(T-g*v-m*v-g*w),b=I-L,h=I-b,Ln[0]=I-(b+h)+(h-L),S=N+b,h=S-N,A=N-(S-h)+(b-h),b=A-T,h=A-b,Ln[1]=A-(b+h)+(h-T),C=S+b,h=C-S,Ln[2]=S-(C-h)+(b-h),Ln[3]=C;const B=tc(W,Jd,4,Ln,$d);return $d[B-1]}function Ms(t,e,n,r,i,a){const s=(e-a)*(n-i),l=(t-i)*(r-a),u=s-l,c=Math.abs(s+l);return Math.abs(u)>=Sp*c?u:-Cp(t,e,n,r,i,a,c)}const e0=Math.pow(2,-52),ps=new Uint32Array(512);class dl{static from(e,n=Op,r=Np){const i=e.length,a=new Float64Array(i*2);for(let s=0;s<i;s++){const l=e[s];a[2*s]=n(l),a[2*s+1]=r(l)}return new dl(a)}constructor(e){const n=e.length>>1;if(n>0&&typeof e[0]!="number")throw new Error("Expected coords to contain numbers.");this.coords=e;const r=Math.max(2*n-5,0);this._triangles=new Uint32Array(r*3),this._halfedges=new Int32Array(r*3),this._hashSize=Math.ceil(Math.sqrt(n)),this._hullPrev=new Uint32Array(n),this._hullNext=new Uint32Array(n),this._hullTri=new Uint32Array(n),this._hullHash=new Int32Array(this._hashSize),this._ids=new Uint32Array(n),this._dists=new Float64Array(n),this.update()}update(){const{coords:e,_hullPrev:n,_hullNext:r,_hullTri:i,_hullHash:a}=this,s=e.length>>1;let l=1/0,u=1/0,c=-1/0,f=-1/0;for(let _=0;_<s;_++){const M=e[2*_],k=e[2*_+1];M<l&&(l=M),k<u&&(u=k),M>c&&(c=M),k>f&&(f=k),this._ids[_]=_}const h=(l+c)/2,d=(u+f)/2;let g,m,v;for(let _=0,M=1/0;_<s;_++){const k=nc(h,d,e[2*_],e[2*_+1]);k<M&&(g=_,M=k)}const w=e[2*g],b=e[2*g+1];for(let _=0,M=1/0;_<s;_++){if(_===g)continue;const k=nc(w,b,e[2*_],e[2*_+1]);k<M&&k>0&&(m=_,M=k)}let S=e[2*m],A=e[2*m+1],N=1/0;for(let _=0;_<s;_++){if(_===g||_===m)continue;const M=Rp(w,b,S,A,e[2*_],e[2*_+1]);M<N&&(v=_,N=M)}let I=e[2*v],T=e[2*v+1];if(N===1/0){for(let k=0;k<s;k++)this._dists[k]=e[2*k]-e[0]||e[2*k+1]-e[1];pa(this._ids,this._dists,0,s-1);const _=new Uint32Array(s);let M=0;for(let k=0,p=-1/0;k<s;k++){const R=this._ids[k],z=this._dists[R];z>p&&(_[M++]=R,p=z)}this.hull=_.subarray(0,M),this.triangles=new Uint32Array(0),this.halfedges=new Uint32Array(0);return}if(Ms(w,b,S,A,I,T)<0){const _=m,M=S,k=A;m=v,S=I,A=T,v=_,I=M,T=k}const L=Ep(w,b,S,A,I,T);this._cx=L.x,this._cy=L.y;for(let _=0;_<s;_++)this._dists[_]=nc(e[2*_],e[2*_+1],L.x,L.y);pa(this._ids,this._dists,0,s-1),this._hullStart=g;let C=3;r[g]=n[v]=m,r[m]=n[g]=v,r[v]=n[m]=g,i[g]=0,i[m]=1,i[v]=2,a.fill(-1),a[this._hashKey(w,b)]=g,a[this._hashKey(S,A)]=m,a[this._hashKey(I,T)]=v,this.trianglesLen=0,this._addTriangle(g,m,v,-1,-1,-1);for(let _=0,M,k;_<this._ids.length;_++){const p=this._ids[_],R=e[2*p],z=e[2*p+1];if(_>0&&Math.abs(R-M)<=e0&&Math.abs(z-k)<=e0||(M=R,k=z,p===g||p===m||p===v))continue;let D=0;for(let oe=0,ye=this._hashKey(R,z);oe<this._hashSize&&(D=a[(ye+oe)%this._hashSize],!(D!==-1&&D!==r[D]));oe++);D=n[D];let W=D,B;for(;B=r[W],Ms(R,z,e[2*W],e[2*W+1],e[2*B],e[2*B+1])>=0;)if(W=B,W===D){W=-1;break}if(W===-1)continue;let H=this._addTriangle(W,p,r[W],-1,-1,i[W]);i[p]=this._legalize(H+2),i[W]=H,C++;let $=r[W];for(;B=r[$],Ms(R,z,e[2*$],e[2*$+1],e[2*B],e[2*B+1])<0;)H=this._addTriangle($,p,B,i[p],-1,i[$]),i[p]=this._legalize(H+2),r[$]=$,C--,$=B;if(W===D)for(;B=n[W],Ms(R,z,e[2*B],e[2*B+1],e[2*W],e[2*W+1])<0;)H=this._addTriangle(B,p,W,-1,i[W],i[B]),this._legalize(H+2),i[B]=H,r[W]=W,C--,W=B;this._hullStart=n[p]=W,r[W]=n[$]=p,r[p]=$,a[this._hashKey(R,z)]=p,a[this._hashKey(e[2*W],e[2*W+1])]=W}this.hull=new Uint32Array(C);for(let _=0,M=this._hullStart;_<C;_++)this.hull[_]=M,M=r[M];this.triangles=this._triangles.subarray(0,this.trianglesLen),this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}_hashKey(e,n){return Math.floor(Dp(e-this._cx,n-this._cy)*this._hashSize)%this._hashSize}_legalize(e){const{_triangles:n,_halfedges:r,coords:i}=this;let a=0,s=0;for(;;){const l=r[e],u=e-e%3;if(s=u+(e+2)%3,l===-1){if(a===0)break;e=ps[--a];continue}const c=l-l%3,f=u+(e+1)%3,h=c+(l+2)%3,d=n[s],g=n[e],m=n[f],v=n[h];if(Pp(i[2*d],i[2*d+1],i[2*g],i[2*g+1],i[2*m],i[2*m+1],i[2*v],i[2*v+1])){n[e]=v,n[l]=d;const b=r[h];if(b===-1){let A=this._hullStart;do{if(this._hullTri[A]===h){this._hullTri[A]=e;break}A=this._hullPrev[A]}while(A!==this._hullStart)}this._link(e,b),this._link(l,r[s]),this._link(s,h);const S=c+(l+1)%3;a<ps.length&&(ps[a++]=S)}else{if(a===0)break;e=ps[--a]}}return s}_link(e,n){this._halfedges[e]=n,n!==-1&&(this._halfedges[n]=e)}_addTriangle(e,n,r,i,a,s){const l=this.trianglesLen;return this._triangles[l]=e,this._triangles[l+1]=n,this._triangles[l+2]=r,this._link(l,i),this._link(l+1,a),this._link(l+2,s),this.trianglesLen+=3,l}}function Dp(t,e){const n=t/(Math.abs(t)+Math.abs(e));return(e>0?3-n:1+n)/4}function nc(t,e,n,r){const i=t-n,a=e-r;return i*i+a*a}function Pp(t,e,n,r,i,a,s,l){const u=t-s,c=e-l,f=n-s,h=r-l,d=i-s,g=a-l,m=u*u+c*c,v=f*f+h*h,w=d*d+g*g;return u*(h*w-v*g)-c*(f*w-v*d)+m*(f*g-h*d)<0}function Rp(t,e,n,r,i,a){const s=n-t,l=r-e,u=i-t,c=a-e,f=s*s+l*l,h=u*u+c*c,d=.5/(s*c-l*u),g=(c*f-l*h)*d,m=(s*h-u*f)*d;return g*g+m*m}function Ep(t,e,n,r,i,a){const s=n-t,l=r-e,u=i-t,c=a-e,f=s*s+l*l,h=u*u+c*c,d=.5/(s*c-l*u),g=t+(c*f-l*h)*d,m=e+(s*h-u*f)*d;return{x:g,y:m}}function pa(t,e,n,r){if(r-n<=20)for(let i=n+1;i<=r;i++){const a=t[i],s=e[a];let l=i-1;for(;l>=n&&e[t[l]]>s;)t[l+1]=t[l--];t[l+1]=a}else{const i=n+r>>1;let a=n+1,s=r;mo(t,i,a),e[t[n]]>e[t[r]]&&mo(t,n,r),e[t[a]]>e[t[r]]&&mo(t,a,r),e[t[n]]>e[t[a]]&&mo(t,n,a);const l=t[a],u=e[l];for(;;){do a++;while(e[t[a]]<u);do s--;while(e[t[s]]>u);if(s<a)break;mo(t,a,s)}t[n+1]=t[s],t[s]=l,r-a+1>=s-n?(pa(t,e,a,r),pa(t,e,n,s-1)):(pa(t,e,n,s-1),pa(t,e,a,r))}}function mo(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}function Op(t){return t[0]}function Np(t){return t[1]}const t0=1e-6;class Xi{constructor(){this._x0=this._y0=this._x1=this._y1=null,this._=""}moveTo(e,n){this._+=`M${this._x0=this._x1=+e},${this._y0=this._y1=+n}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")}lineTo(e,n){this._+=`L${this._x1=+e},${this._y1=+n}`}arc(e,n,r){e=+e,n=+n,r=+r;const i=e+r,a=n;if(r<0)throw new Error("negative radius");this._x1===null?this._+=`M${i},${a}`:(Math.abs(this._x1-i)>t0||Math.abs(this._y1-a)>t0)&&(this._+="L"+i+","+a),r&&(this._+=`A${r},${r},0,1,1,${e-r},${n}A${r},${r},0,1,1,${this._x1=i},${this._y1=a}`)}rect(e,n,r,i){this._+=`M${this._x0=this._x1=+e},${this._y0=this._y1=+n}h${+r}v${+i}h${-r}Z`}value(){return this._||null}}class Fc{constructor(){this._=[]}moveTo(e,n){this._.push([e,n])}closePath(){this._.push(this._[0].slice())}lineTo(e,n){this._.push([e,n])}value(){return this._.length?this._:null}}let Lp=class{constructor(e,[n,r,i,a]=[0,0,960,500]){if(!((i=+i)>=(n=+n))||!((a=+a)>=(r=+r)))throw new Error("invalid bounds");this.delaunay=e,this._circumcenters=new Float64Array(e.points.length*2),this.vectors=new Float64Array(e.points.length*2),this.xmax=i,this.xmin=n,this.ymax=a,this.ymin=r,this._init()}update(){return this.delaunay.update(),this._init(),this}_init(){const{delaunay:{points:e,hull:n,triangles:r},vectors:i}=this;let a,s;const l=this.circumcenters=this._circumcenters.subarray(0,r.length/3*2);for(let v=0,w=0,b=r.length,S,A;v<b;v+=3,w+=2){const N=r[v]*2,I=r[v+1]*2,T=r[v+2]*2,L=e[N],C=e[N+1],_=e[I],M=e[I+1],k=e[T],p=e[T+1],R=_-L,z=M-C,D=k-L,W=p-C,B=(R*W-z*D)*2;if(Math.abs(B)<1e-9){if(a===void 0){a=s=0;for(const $ of n)a+=e[$*2],s+=e[$*2+1];a/=n.length,s/=n.length}const H=1e9*Math.sign((a-L)*W-(s-C)*D);S=(L+k)/2-H*W,A=(C+p)/2+H*D}else{const H=1/B,$=R*R+z*z,oe=D*D+W*W;S=L+(W*$-z*oe)*H,A=C+(R*oe-D*$)*H}l[w]=S,l[w+1]=A}let u=n[n.length-1],c,f=u*4,h,d=e[2*u],g,m=e[2*u+1];i.fill(0);for(let v=0;v<n.length;++v)u=n[v],c=f,h=d,g=m,f=u*4,d=e[2*u],m=e[2*u+1],i[c+2]=i[f]=g-m,i[c+3]=i[f+1]=d-h}render(e){const n=e==null?e=new Xi:void 0,{delaunay:{halfedges:r,inedges:i,hull:a},circumcenters:s,vectors:l}=this;if(a.length<=1)return null;for(let f=0,h=r.length;f<h;++f){const d=r[f];if(d<f)continue;const g=Math.floor(f/3)*2,m=Math.floor(d/3)*2,v=s[g],w=s[g+1],b=s[m],S=s[m+1];this._renderSegment(v,w,b,S,e)}let u,c=a[a.length-1];for(let f=0;f<a.length;++f){u=c,c=a[f];const h=Math.floor(i[c]/3)*2,d=s[h],g=s[h+1],m=u*4,v=this._project(d,g,l[m+2],l[m+3]);v&&this._renderSegment(d,g,v[0],v[1],e)}return n&&n.value()}renderBounds(e){const n=e==null?e=new Xi:void 0;return e.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin),n&&n.value()}renderCell(e,n){const r=n==null?n=new Xi:void 0,i=this._clip(e);if(i===null||!i.length)return;n.moveTo(i[0],i[1]);let a=i.length;for(;i[0]===i[a-2]&&i[1]===i[a-1]&&a>1;)a-=2;for(let s=2;s<a;s+=2)(i[s]!==i[s-2]||i[s+1]!==i[s-1])&&n.lineTo(i[s],i[s+1]);return n.closePath(),r&&r.value()}*cellPolygons(){const{delaunay:{points:e}}=this;for(let n=0,r=e.length/2;n<r;++n){const i=this.cellPolygon(n);i&&(i.index=n,yield i)}}cellPolygon(e){const n=new Fc;return this.renderCell(e,n),n.value()}_renderSegment(e,n,r,i,a){let s;const l=this._regioncode(e,n),u=this._regioncode(r,i);l===0&&u===0?(a.moveTo(e,n),a.lineTo(r,i)):(s=this._clipSegment(e,n,r,i,l,u))&&(a.moveTo(s[0],s[1]),a.lineTo(s[2],s[3]))}contains(e,n,r){return n=+n,n!==n||(r=+r,r!==r)?!1:this.delaunay._step(e,n,r)===e}*neighbors(e){const n=this._clip(e);if(n)for(const r of this.delaunay.neighbors(e)){const i=this._clip(r);if(i){e:for(let a=0,s=n.length;a<s;a+=2)for(let l=0,u=i.length;l<u;l+=2)if(n[a]===i[l]&&n[a+1]===i[l+1]&&n[(a+2)%s]===i[(l+u-2)%u]&&n[(a+3)%s]===i[(l+u-1)%u]){yield r;break e}}}}_cell(e){const{circumcenters:n,delaunay:{inedges:r,halfedges:i,triangles:a}}=this,s=r[e];if(s===-1)return null;const l=[];let u=s;do{const c=Math.floor(u/3);if(l.push(n[c*2],n[c*2+1]),u=u%3===2?u-2:u+1,a[u]!==e)break;u=i[u]}while(u!==s&&u!==-1);return l}_clip(e){if(e===0&&this.delaunay.hull.length===1)return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];const n=this._cell(e);if(n===null)return null;const{vectors:r}=this,i=e*4;return this._simplify(r[i]||r[i+1]?this._clipInfinite(e,n,r[i],r[i+1],r[i+2],r[i+3]):this._clipFinite(e,n))}_clipFinite(e,n){const r=n.length;let i=null,a,s,l=n[r-2],u=n[r-1],c,f=this._regioncode(l,u),h,d=0;for(let g=0;g<r;g+=2)if(a=l,s=u,l=n[g],u=n[g+1],c=f,f=this._regioncode(l,u),c===0&&f===0)h=d,d=0,i?i.push(l,u):i=[l,u];else{let m,v,w,b,S;if(c===0){if((m=this._clipSegment(a,s,l,u,c,f))===null)continue;[v,w,b,S]=m}else{if((m=this._clipSegment(l,u,a,s,f,c))===null)continue;[b,S,v,w]=m,h=d,d=this._edgecode(v,w),h&&d&&this._edge(e,h,d,i,i.length),i?i.push(v,w):i=[v,w]}h=d,d=this._edgecode(b,S),h&&d&&this._edge(e,h,d,i,i.length),i?i.push(b,S):i=[b,S]}if(i)h=d,d=this._edgecode(i[0],i[1]),h&&d&&this._edge(e,h,d,i,i.length);else if(this.contains(e,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2))return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];return i}_clipSegment(e,n,r,i,a,s){const l=a<s;for(l&&([e,n,r,i,a,s]=[r,i,e,n,s,a]);;){if(a===0&&s===0)return l?[r,i,e,n]:[e,n,r,i];if(a&s)return null;let u,c,f=a||s;f&8?(u=e+(r-e)*(this.ymax-n)/(i-n),c=this.ymax):f&4?(u=e+(r-e)*(this.ymin-n)/(i-n),c=this.ymin):f&2?(c=n+(i-n)*(this.xmax-e)/(r-e),u=this.xmax):(c=n+(i-n)*(this.xmin-e)/(r-e),u=this.xmin),a?(e=u,n=c,a=this._regioncode(e,n)):(r=u,i=c,s=this._regioncode(r,i))}}_clipInfinite(e,n,r,i,a,s){let l=Array.from(n),u;if((u=this._project(l[0],l[1],r,i))&&l.unshift(u[0],u[1]),(u=this._project(l[l.length-2],l[l.length-1],a,s))&&l.push(u[0],u[1]),l=this._clipFinite(e,l))for(let c=0,f=l.length,h,d=this._edgecode(l[f-2],l[f-1]);c<f;c+=2)h=d,d=this._edgecode(l[c],l[c+1]),h&&d&&(c=this._edge(e,h,d,l,c),f=l.length);else this.contains(e,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)&&(l=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]);return l}_edge(e,n,r,i,a){for(;n!==r;){let s,l;switch(n){case 5:n=4;continue;case 4:n=6,s=this.xmax,l=this.ymin;break;case 6:n=2;continue;case 2:n=10,s=this.xmax,l=this.ymax;break;case 10:n=8;continue;case 8:n=9,s=this.xmin,l=this.ymax;break;case 9:n=1;continue;case 1:n=5,s=this.xmin,l=this.ymin;break}(i[a]!==s||i[a+1]!==l)&&this.contains(e,s,l)&&(i.splice(a,0,s,l),a+=2)}return a}_project(e,n,r,i){let a=1/0,s,l,u;if(i<0){if(n<=this.ymin)return null;(s=(this.ymin-n)/i)<a&&(u=this.ymin,l=e+(a=s)*r)}else if(i>0){if(n>=this.ymax)return null;(s=(this.ymax-n)/i)<a&&(u=this.ymax,l=e+(a=s)*r)}if(r>0){if(e>=this.xmax)return null;(s=(this.xmax-e)/r)<a&&(l=this.xmax,u=n+(a=s)*i)}else if(r<0){if(e<=this.xmin)return null;(s=(this.xmin-e)/r)<a&&(l=this.xmin,u=n+(a=s)*i)}return[l,u]}_edgecode(e,n){return(e===this.xmin?1:e===this.xmax?2:0)|(n===this.ymin?4:n===this.ymax?8:0)}_regioncode(e,n){return(e<this.xmin?1:e>this.xmax?2:0)|(n<this.ymin?4:n>this.ymax?8:0)}_simplify(e){if(e&&e.length>4){for(let n=0;n<e.length;n+=2){const r=(n+2)%e.length,i=(n+4)%e.length;(e[n]===e[r]&&e[r]===e[i]||e[n+1]===e[r+1]&&e[r+1]===e[i+1])&&(e.splice(r,2),n-=2)}e.length||(e=null)}return e}};const Wp=2*Math.PI,ya=Math.pow;function Ip(t){return t[0]}function zp(t){return t[1]}function jp(t){const{triangles:e,coords:n}=t;for(let r=0;r<e.length;r+=3){const i=2*e[r],a=2*e[r+1],s=2*e[r+2];if((n[s]-n[i])*(n[a+1]-n[i+1])-(n[a]-n[i])*(n[s+1]-n[i+1])>1e-10)return!1}return!0}function Fp(t,e,n){return[t+Math.sin(t+e)*n,e+Math.cos(t-e)*n]}class au{static from(e,n=Ip,r=zp,i){return new au("length"in e?Bp(e,n,r,i):Float64Array.from(Yp(e,n,r,i)))}constructor(e){this._delaunator=new dl(e),this.inedges=new Int32Array(e.length/2),this._hullIndex=new Int32Array(e.length/2),this.points=this._delaunator.coords,this._init()}update(){return this._delaunator.update(),this._init(),this}_init(){const e=this._delaunator,n=this.points;if(e.hull&&e.hull.length>2&&jp(e)){this.collinear=Int32Array.from({length:n.length/2},(d,g)=>g).sort((d,g)=>n[2*d]-n[2*g]||n[2*d+1]-n[2*g+1]);const u=this.collinear[0],c=this.collinear[this.collinear.length-1],f=[n[2*u],n[2*u+1],n[2*c],n[2*c+1]],h=1e-8*Math.hypot(f[3]-f[1],f[2]-f[0]);for(let d=0,g=n.length/2;d<g;++d){const m=Fp(n[2*d],n[2*d+1],h);n[2*d]=m[0],n[2*d+1]=m[1]}this._delaunator=new dl(n)}else delete this.collinear;const r=this.halfedges=this._delaunator.halfedges,i=this.hull=this._delaunator.hull,a=this.triangles=this._delaunator.triangles,s=this.inedges.fill(-1),l=this._hullIndex.fill(-1);for(let u=0,c=r.length;u<c;++u){const f=a[u%3===2?u-2:u+1];(r[u]===-1||s[f]===-1)&&(s[f]=u)}for(let u=0,c=i.length;u<c;++u)l[i[u]]=u;i.length<=2&&i.length>0&&(this.triangles=new Int32Array(3).fill(-1),this.halfedges=new Int32Array(3).fill(-1),this.triangles[0]=i[0],s[i[0]]=1,i.length===2&&(s[i[1]]=0,this.triangles[1]=i[1],this.triangles[2]=i[1]))}voronoi(e){return new Lp(this,e)}*neighbors(e){const{inedges:n,hull:r,_hullIndex:i,halfedges:a,triangles:s,collinear:l}=this;if(l){const h=l.indexOf(e);h>0&&(yield l[h-1]),h<l.length-1&&(yield l[h+1]);return}const u=n[e];if(u===-1)return;let c=u,f=-1;do{if(yield f=s[c],c=c%3===2?c-2:c+1,s[c]!==e)return;if(c=a[c],c===-1){const h=r[(i[e]+1)%r.length];h!==f&&(yield h);return}}while(c!==u)}find(e,n,r=0){if(e=+e,e!==e||(n=+n,n!==n))return-1;const i=r;let a;for(;(a=this._step(r,e,n))>=0&&a!==r&&a!==i;)r=a;return a}_step(e,n,r){const{inedges:i,hull:a,_hullIndex:s,halfedges:l,triangles:u,points:c}=this;if(i[e]===-1||!c.length)return(e+1)%(c.length>>1);let f=e,h=ya(n-c[e*2],2)+ya(r-c[e*2+1],2);const d=i[e];let g=d;do{let m=u[g];const v=ya(n-c[m*2],2)+ya(r-c[m*2+1],2);if(v<h&&(h=v,f=m),g=g%3===2?g-2:g+1,u[g]!==e)break;if(g=l[g],g===-1){if(g=a[(s[e]+1)%a.length],g!==m&&ya(n-c[g*2],2)+ya(r-c[g*2+1],2)<h)return g;break}}while(g!==d);return f}render(e){const n=e==null?e=new Xi:void 0,{points:r,halfedges:i,triangles:a}=this;for(let s=0,l=i.length;s<l;++s){const u=i[s];if(u<s)continue;const c=a[s]*2,f=a[u]*2;e.moveTo(r[c],r[c+1]),e.lineTo(r[f],r[f+1])}return this.renderHull(e),n&&n.value()}renderPoints(e,n){n===void 0&&(!e||typeof e.moveTo!="function")&&(n=e,e=null),n=n==null?2:+n;const r=e==null?e=new Xi:void 0,{points:i}=this;for(let a=0,s=i.length;a<s;a+=2){const l=i[a],u=i[a+1];e.moveTo(l+n,u),e.arc(l,u,n,0,Wp)}return r&&r.value()}renderHull(e){const n=e==null?e=new Xi:void 0,{hull:r,points:i}=this,a=r[0]*2,s=r.length;e.moveTo(i[a],i[a+1]);for(let l=1;l<s;++l){const u=2*r[l];e.lineTo(i[u],i[u+1])}return e.closePath(),n&&n.value()}hullPolygon(){const e=new Fc;return this.renderHull(e),e.value()}renderTriangle(e,n){const r=n==null?n=new Xi:void 0,{points:i,triangles:a}=this,s=a[e*=3]*2,l=a[e+1]*2,u=a[e+2]*2;return n.moveTo(i[s],i[s+1]),n.lineTo(i[l],i[l+1]),n.lineTo(i[u],i[u+1]),n.closePath(),r&&r.value()}*trianglePolygons(){const{triangles:e}=this;for(let n=0,r=e.length/3;n<r;++n)yield this.trianglePolygon(n)}trianglePolygon(e){const n=new Fc;return this.renderTriangle(e,n),n.value()}}function Bp(t,e,n,r){const i=t.length,a=new Float64Array(i*2);for(let s=0;s<i;++s){const l=t[s];a[s*2]=e.call(r,l,s,t),a[s*2+1]=n.call(r,l,s,t)}return a}function*Yp(t,e,n,r){let i=0;for(const a of t)yield e.call(r,a,i,t),yield n.call(r,a,i,t),++i}var Vt=1e-6,n0=1e-12,Nt=Math.PI,lr=Nt/2,r0=Nt/4,fr=Nt*2,Wn=180/Nt,Pt=Nt/180,nn=Math.abs,um=Math.atan,ci=Math.atan2,At=Math.cos,Bc=Math.hypot,Tt=Math.sin,Hp=Math.sign||function(t){return t>0?1:t<0?-1:0},jr=Math.sqrt;function qp(t){return t>1?0:t<-1?Nt:Math.acos(t)}function Ii(t){return t>1?lr:t<-1?-lr:Math.asin(t)}function fn(){}function gl(t,e){t&&a0.hasOwnProperty(t.type)&&a0[t.type](t,e)}var i0={Feature:function(t,e){gl(t.geometry,e)},FeatureCollection:function(t,e){for(var n=t.features,r=-1,i=n.length;++r<i;)gl(n[r].geometry,e)}},a0={Sphere:function(t,e){e.sphere()},Point:function(t,e){t=t.coordinates,e.point(t[0],t[1],t[2])},MultiPoint:function(t,e){for(var n=t.coordinates,r=-1,i=n.length;++r<i;)t=n[r],e.point(t[0],t[1],t[2])},LineString:function(t,e){Yc(t.coordinates,e,0)},MultiLineString:function(t,e){for(var n=t.coordinates,r=-1,i=n.length;++r<i;)Yc(n[r],e,0)},Polygon:function(t,e){o0(t.coordinates,e)},MultiPolygon:function(t,e){for(var n=t.coordinates,r=-1,i=n.length;++r<i;)o0(n[r],e)},GeometryCollection:function(t,e){for(var n=t.geometries,r=-1,i=n.length;++r<i;)gl(n[r],e)}};function Yc(t,e,n){var r=-1,i=t.length-n,a;for(e.lineStart();++r<i;)a=t[r],e.point(a[0],a[1],a[2]);e.lineEnd()}function o0(t,e){var n=-1,r=t.length;for(e.polygonStart();++n<r;)Yc(t[n],e,1);e.polygonEnd()}function Si(t,e){t&&i0.hasOwnProperty(t.type)?i0[t.type](t,e):gl(t,e)}function Hc(t){return[ci(t[1],t[0]),Ii(t[2])]}function Ua(t){var e=t[0],n=t[1],r=At(n);return[r*At(e),r*Tt(e),Tt(n)]}function Ss(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function ml(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function rc(t,e){t[0]+=e[0],t[1]+=e[1],t[2]+=e[2]}function As(t,e){return[t[0]*e,t[1]*e,t[2]*e]}function qc(t){var e=jr(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=e,t[1]/=e,t[2]/=e}var vo,yl,vl,_l,xl,bl,kl,wl,Uc,Gc,Xc,cm,fm,In,zn,jn,_r={sphere:fn,point:Hf,lineStart:s0,lineEnd:l0,polygonStart:function(){_r.lineStart=Xp,_r.lineEnd=Vp},polygonEnd:function(){_r.lineStart=s0,_r.lineEnd=l0}};function Hf(t,e){t*=Pt,e*=Pt;var n=At(e);ns(n*At(t),n*Tt(t),Tt(e))}function ns(t,e,n){++vo,vl+=(t-vl)/vo,_l+=(e-_l)/vo,xl+=(n-xl)/vo}function s0(){_r.point=Up}function Up(t,e){t*=Pt,e*=Pt;var n=At(e);In=n*At(t),zn=n*Tt(t),jn=Tt(e),_r.point=Gp,ns(In,zn,jn)}function Gp(t,e){t*=Pt,e*=Pt;var n=At(e),r=n*At(t),i=n*Tt(t),a=Tt(e),s=ci(jr((s=zn*a-jn*i)*s+(s=jn*r-In*a)*s+(s=In*i-zn*r)*s),In*r+zn*i+jn*a);yl+=s,bl+=s*(In+(In=r)),kl+=s*(zn+(zn=i)),wl+=s*(jn+(jn=a)),ns(In,zn,jn)}function l0(){_r.point=Hf}function Xp(){_r.point=Zp}function Vp(){hm(cm,fm),_r.point=Hf}function Zp(t,e){cm=t,fm=e,t*=Pt,e*=Pt,_r.point=hm;var n=At(e);In=n*At(t),zn=n*Tt(t),jn=Tt(e),ns(In,zn,jn)}function hm(t,e){t*=Pt,e*=Pt;var n=At(e),r=n*At(t),i=n*Tt(t),a=Tt(e),s=zn*a-jn*i,l=jn*r-In*a,u=In*i-zn*r,c=Bc(s,l,u),f=Ii(c),h=c&&-f/c;Uc.add(h*s),Gc.add(h*l),Xc.add(h*u),yl+=f,bl+=f*(In+(In=r)),kl+=f*(zn+(zn=i)),wl+=f*(jn+(jn=a)),ns(In,zn,jn)}function u0(t){vo=yl=vl=_l=xl=bl=kl=wl=0,Uc=new xr,Gc=new xr,Xc=new xr,Si(t,_r);var e=+Uc,n=+Gc,r=+Xc,i=Bc(e,n,r);return i<n0&&(e=bl,n=kl,r=wl,yl<Vt&&(e=vl,n=_l,r=xl),i=Bc(e,n,r),i<n0)?[NaN,NaN]:[ci(n,e)*Wn,Ii(r/i)*Wn]}function Vc(t,e){function n(r,i){return r=t(r,i),e(r[0],r[1])}return t.invert&&e.invert&&(n.invert=function(r,i){return r=e.invert(r,i),r&&t.invert(r[0],r[1])}),n}function Zc(t,e){return nn(t)>Nt&&(t-=Math.round(t/fr)*fr),[t,e]}Zc.invert=Zc;function dm(t,e,n){return(t%=fr)?e||n?Vc(f0(t),h0(e,n)):f0(t):e||n?h0(e,n):Zc}function c0(t){return function(e,n){return e+=t,nn(e)>Nt&&(e-=Math.round(e/fr)*fr),[e,n]}}function f0(t){var e=c0(t);return e.invert=c0(-t),e}function h0(t,e){var n=At(t),r=Tt(t),i=At(e),a=Tt(e);function s(l,u){var c=At(u),f=At(l)*c,h=Tt(l)*c,d=Tt(u),g=d*n+f*r;return[ci(h*i-g*a,f*n-d*r),Ii(g*i+h*a)]}return s.invert=function(l,u){var c=At(u),f=At(l)*c,h=Tt(l)*c,d=Tt(u),g=d*i-h*a;return[ci(h*i+d*a,f*n+g*r),Ii(g*n-f*r)]},s}function Kp(t){t=dm(t[0]*Pt,t[1]*Pt,t.length>2?t[2]*Pt:0);function e(n){return n=t(n[0]*Pt,n[1]*Pt),n[0]*=Wn,n[1]*=Wn,n}return e.invert=function(n){return n=t.invert(n[0]*Pt,n[1]*Pt),n[0]*=Wn,n[1]*=Wn,n},e}function Qp(t,e,n,r,i,a){if(n){var s=At(e),l=Tt(e),u=r*n;i==null?(i=e+r*fr,a=e-u/2):(i=d0(s,i),a=d0(s,a),(r>0?i<a:i>a)&&(i+=r*fr));for(var c,f=i;r>0?f>a:f<a;f-=u)c=Hc([s,-l*At(f),-l*Tt(f)]),t.point(c[0],c[1])}}function d0(t,e){e=Ua(e),e[0]-=t,qc(e);var n=qp(-e[1]);return((-e[2]<0?-n:n)+fr-Vt)%fr}function gm(){var t=[],e;return{point:function(n,r,i){e.push([n,r,i])},lineStart:function(){t.push(e=[])},lineEnd:fn,rejoin:function(){t.length>1&&t.push(t.pop().concat(t.shift()))},result:function(){var n=t;return t=[],e=null,n}}}function Ws(t,e){return nn(t[0]-e[0])<Vt&&nn(t[1]-e[1])<Vt}function Ts(t,e,n,r){this.x=t,this.z=e,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function mm(t,e,n,r,i){var a=[],s=[],l,u;if(t.forEach(function(m){if(!((v=m.length-1)<=0)){var v,w=m[0],b=m[v],S;if(Ws(w,b)){if(!w[2]&&!b[2]){for(i.lineStart(),l=0;l<v;++l)i.point((w=m[l])[0],w[1]);i.lineEnd();return}b[0]+=2*Vt}a.push(S=new Ts(w,m,null,!0)),s.push(S.o=new Ts(w,null,S,!1)),a.push(S=new Ts(b,m,null,!1)),s.push(S.o=new Ts(b,null,S,!0))}}),!!a.length){for(s.sort(e),g0(a),g0(s),l=0,u=s.length;l<u;++l)s[l].e=n=!n;for(var c=a[0],f,h;;){for(var d=c,g=!0;d.v;)if((d=d.n)===c)return;f=d.z,i.lineStart();do{if(d.v=d.o.v=!0,d.e){if(g)for(l=0,u=f.length;l<u;++l)i.point((h=f[l])[0],h[1]);else r(d.x,d.n.x,1,i);d=d.n}else{if(g)for(f=d.p.z,l=f.length-1;l>=0;--l)i.point((h=f[l])[0],h[1]);else r(d.x,d.p.x,-1,i);d=d.p}d=d.o,f=d.z,g=!g}while(!d.v);i.lineEnd()}}}function g0(t){if(e=t.length){for(var e,n=0,r=t[0],i;++n<e;)r.n=i=t[n],i.p=r,r=i;r.n=i=t[0],i.p=r}}function ic(t){return nn(t[0])<=Nt?t[0]:Hp(t[0])*((nn(t[0])+Nt)%fr-Nt)}function Jp(t,e){var n=ic(e),r=e[1],i=Tt(r),a=[Tt(n),-At(n),0],s=0,l=0,u=new xr;i===1?r=lr+Vt:i===-1&&(r=-lr-Vt);for(var c=0,f=t.length;c<f;++c)if(d=(h=t[c]).length)for(var h,d,g=h[d-1],m=ic(g),v=g[1]/2+r0,w=Tt(v),b=At(v),S=0;S<d;++S,m=N,w=T,b=L,g=A){var A=h[S],N=ic(A),I=A[1]/2+r0,T=Tt(I),L=At(I),C=N-m,_=C>=0?1:-1,M=_*C,k=M>Nt,p=w*T;if(u.add(ci(p*_*Tt(M),b*L+p*At(M))),s+=k?C+_*fr:C,k^m>=n^N>=n){var R=ml(Ua(g),Ua(A));qc(R);var z=ml(a,R);qc(z);var D=(k^C>=0?-1:1)*Ii(z[2]);(r>D||r===D&&(R[0]||R[1]))&&(l+=k^C>=0?1:-1)}}return(s<-1e-6||s<Vt&&u<-1e-12)^l&1}function ym(t,e,n,r){return function(i){var a=e(i),s=gm(),l=e(s),u=!1,c,f,h,d={point:g,lineStart:v,lineEnd:w,polygonStart:function(){d.point=b,d.lineStart=S,d.lineEnd=A,f=[],c=[]},polygonEnd:function(){d.point=g,d.lineStart=v,d.lineEnd=w,f=vg(f);var N=Jp(c,r);f.length?(u||(i.polygonStart(),u=!0),mm(f,e5,N,n,i)):N&&(u||(i.polygonStart(),u=!0),i.lineStart(),n(null,null,1,i),i.lineEnd()),u&&(i.polygonEnd(),u=!1),f=c=null},sphere:function(){i.polygonStart(),i.lineStart(),n(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function g(N,I){t(N,I)&&i.point(N,I)}function m(N,I){a.point(N,I)}function v(){d.point=m,a.lineStart()}function w(){d.point=g,a.lineEnd()}function b(N,I){h.push([N,I]),l.point(N,I)}function S(){l.lineStart(),h=[]}function A(){b(h[0][0],h[0][1]),l.lineEnd();var N=l.clean(),I=s.result(),T,L=I.length,C,_,M;if(h.pop(),c.push(h),h=null,!!L){if(N&1){if(_=I[0],(C=_.length-1)>0){for(u||(i.polygonStart(),u=!0),i.lineStart(),T=0;T<C;++T)i.point((M=_[T])[0],M[1]);i.lineEnd()}return}L>1&&N&2&&I.push(I.pop().concat(I.shift())),f.push(I.filter($p))}}return d}}function $p(t){return t.length>1}function e5(t,e){return((t=t.x)[0]<0?t[1]-lr-Vt:lr-t[1])-((e=e.x)[0]<0?e[1]-lr-Vt:lr-e[1])}const m0=ym(function(){return!0},t5,r5,[-Nt,-lr]);function t5(t){var e=NaN,n=NaN,r=NaN,i;return{lineStart:function(){t.lineStart(),i=1},point:function(a,s){var l=a>0?Nt:-Nt,u=nn(a-e);nn(u-Nt)<Vt?(t.point(e,n=(n+s)/2>0?lr:-lr),t.point(r,n),t.lineEnd(),t.lineStart(),t.point(l,n),t.point(a,n),i=0):r!==l&&u>=Nt&&(nn(e-r)<Vt&&(e-=r*Vt),nn(a-l)<Vt&&(a-=l*Vt),n=n5(e,n,a,s),t.point(r,n),t.lineEnd(),t.lineStart(),t.point(l,n),i=0),t.point(e=a,n=s),r=l},lineEnd:function(){t.lineEnd(),e=n=NaN},clean:function(){return 2-i}}}function n5(t,e,n,r){var i,a,s=Tt(t-n);return nn(s)>Vt?um((Tt(e)*(a=At(r))*Tt(n)-Tt(r)*(i=At(e))*Tt(t))/(i*a*s)):(e+r)/2}function r5(t,e,n,r){var i;if(t==null)i=n*lr,r.point(-Nt,i),r.point(0,i),r.point(Nt,i),r.point(Nt,0),r.point(Nt,-i),r.point(0,-i),r.point(-Nt,-i),r.point(-Nt,0),r.point(-Nt,i);else if(nn(t[0]-e[0])>Vt){var a=t[0]<e[0]?Nt:-Nt;i=n*a/2,r.point(-a,i),r.point(0,i),r.point(a,i)}else r.point(e[0],e[1])}function i5(t){var e=At(t),n=2*Pt,r=e>0,i=nn(e)>Vt;function a(f,h,d,g){Qp(g,t,n,d,f,h)}function s(f,h){return At(f)*At(h)>e}function l(f){var h,d,g,m,v;return{lineStart:function(){m=g=!1,v=1},point:function(w,b){var S=[w,b],A,N=s(w,b),I=r?N?0:c(w,b):N?c(w+(w<0?Nt:-Nt),b):0;if(!h&&(m=g=N)&&f.lineStart(),N!==g&&(A=u(h,S),(!A||Ws(h,A)||Ws(S,A))&&(S[2]=1)),N!==g)v=0,N?(f.lineStart(),A=u(S,h),f.point(A[0],A[1])):(A=u(h,S),f.point(A[0],A[1],2),f.lineEnd()),h=A;else if(i&&h&&r^N){var T;!(I&d)&&(T=u(S,h,!0))&&(v=0,r?(f.lineStart(),f.point(T[0][0],T[0][1]),f.point(T[1][0],T[1][1]),f.lineEnd()):(f.point(T[1][0],T[1][1]),f.lineEnd(),f.lineStart(),f.point(T[0][0],T[0][1],3)))}N&&(!h||!Ws(h,S))&&f.point(S[0],S[1]),h=S,g=N,d=I},lineEnd:function(){g&&f.lineEnd(),h=null},clean:function(){return v|(m&&g)<<1}}}function u(f,h,d){var g=Ua(f),m=Ua(h),v=[1,0,0],w=ml(g,m),b=Ss(w,w),S=w[0],A=b-S*S;if(!A)return!d&&f;var N=e*b/A,I=-e*S/A,T=ml(v,w),L=As(v,N),C=As(w,I);rc(L,C);var _=T,M=Ss(L,_),k=Ss(_,_),p=M*M-k*(Ss(L,L)-1);if(!(p<0)){var R=jr(p),z=As(_,(-M-R)/k);if(rc(z,L),z=Hc(z),!d)return z;var D=f[0],W=h[0],B=f[1],H=h[1],$;W<D&&($=D,D=W,W=$);var oe=W-D,ye=nn(oe-Nt)<Vt,ge=ye||oe<Vt;if(!ye&&H<B&&($=B,B=H,H=$),ge?ye?B+H>0^z[1]<(nn(z[0]-D)<Vt?B:H):B<=z[1]&&z[1]<=H:oe>Nt^(D<=z[0]&&z[0]<=W)){var Ee=As(_,(-M+R)/k);return rc(Ee,L),[z,Hc(Ee)]}}}function c(f,h){var d=r?t:Nt-t,g=0;return f<-d?g|=1:f>d&&(g|=2),h<-d?g|=4:h>d&&(g|=8),g}return ym(s,l,a,r?[0,-t]:[-Nt,t-Nt])}function a5(t,e,n,r,i,a){var s=t[0],l=t[1],u=e[0],c=e[1],f=0,h=1,d=u-s,g=c-l,m;if(m=n-s,!(!d&&m>0)){if(m/=d,d<0){if(m<f)return;m<h&&(h=m)}else if(d>0){if(m>h)return;m>f&&(f=m)}if(m=i-s,!(!d&&m<0)){if(m/=d,d<0){if(m>h)return;m>f&&(f=m)}else if(d>0){if(m<f)return;m<h&&(h=m)}if(m=r-l,!(!g&&m>0)){if(m/=g,g<0){if(m<f)return;m<h&&(h=m)}else if(g>0){if(m>h)return;m>f&&(f=m)}if(m=a-l,!(!g&&m<0)){if(m/=g,g<0){if(m>h)return;m>f&&(f=m)}else if(g>0){if(m<f)return;m<h&&(h=m)}return f>0&&(t[0]=s+f*d,t[1]=l+f*g),h<1&&(e[0]=s+h*d,e[1]=l+h*g),!0}}}}}var Cs=1e9,Ds=-1e9;function o5(t,e,n,r){function i(c,f){return t<=c&&c<=n&&e<=f&&f<=r}function a(c,f,h,d){var g=0,m=0;if(c==null||(g=s(c,h))!==(m=s(f,h))||u(c,f)<0^h>0)do d.point(g===0||g===3?t:n,g>1?r:e);while((g=(g+h+4)%4)!==m);else d.point(f[0],f[1])}function s(c,f){return nn(c[0]-t)<Vt?f>0?0:3:nn(c[0]-n)<Vt?f>0?2:1:nn(c[1]-e)<Vt?f>0?1:0:f>0?3:2}function l(c,f){return u(c.x,f.x)}function u(c,f){var h=s(c,1),d=s(f,1);return h!==d?h-d:h===0?f[1]-c[1]:h===1?c[0]-f[0]:h===2?c[1]-f[1]:f[0]-c[0]}return function(c){var f=c,h=gm(),d,g,m,v,w,b,S,A,N,I,T,L={point:C,lineStart:p,lineEnd:R,polygonStart:M,polygonEnd:k};function C(D,W){i(D,W)&&f.point(D,W)}function _(){for(var D=0,W=0,B=g.length;W<B;++W)for(var H=g[W],$=1,oe=H.length,ye=H[0],ge,Ee,xe=ye[0],te=ye[1];$<oe;++$)ge=xe,Ee=te,ye=H[$],xe=ye[0],te=ye[1],Ee<=r?te>r&&(xe-ge)*(r-Ee)>(te-Ee)*(t-ge)&&++D:te<=r&&(xe-ge)*(r-Ee)<(te-Ee)*(t-ge)&&--D;return D}function M(){f=h,d=[],g=[],T=!0}function k(){var D=_(),W=T&&D,B=(d=vg(d)).length;(W||B)&&(c.polygonStart(),W&&(c.lineStart(),a(null,null,1,c),c.lineEnd()),B&&mm(d,l,D,a,c),c.polygonEnd()),f=c,d=g=m=null}function p(){L.point=z,g&&g.push(m=[]),I=!0,N=!1,S=A=NaN}function R(){d&&(z(v,w),b&&N&&h.rejoin(),d.push(h.result())),L.point=C,N&&f.lineEnd()}function z(D,W){var B=i(D,W);if(g&&m.push([D,W]),I)v=D,w=W,b=B,I=!1,B&&(f.lineStart(),f.point(D,W));else if(B&&N)f.point(D,W);else{var H=[S=Math.max(Ds,Math.min(Cs,S)),A=Math.max(Ds,Math.min(Cs,A))],$=[D=Math.max(Ds,Math.min(Cs,D)),W=Math.max(Ds,Math.min(Cs,W))];a5(H,$,t,e,n,r)?(N||(f.lineStart(),f.point(H[0],H[1])),f.point($[0],$[1]),B||f.lineEnd(),T=!1):B&&(f.lineStart(),f.point(D,W),T=!1)}S=D,A=W,N=B}return L}}var Kc,Qc,Is,zs,Ga={sphere:fn,point:fn,lineStart:s5,lineEnd:fn,polygonStart:fn,polygonEnd:fn};function s5(){Ga.point=u5,Ga.lineEnd=l5}function l5(){Ga.point=Ga.lineEnd=fn}function u5(t,e){t*=Pt,e*=Pt,Qc=t,Is=Tt(e),zs=At(e),Ga.point=c5}function c5(t,e){t*=Pt,e*=Pt;var n=Tt(e),r=At(e),i=nn(t-Qc),a=At(i),s=Tt(i),l=r*s,u=zs*n-Is*r*a,c=Is*n+zs*r*a;Kc.add(ci(jr(l*l+u*u),c)),Qc=t,Is=n,zs=r}function f5(t){return Kc=new xr,Si(t,Ga),+Kc}var Jc=[null,null],h5={type:"LineString",coordinates:Jc};function y0(t,e){return Jc[0]=t,Jc[1]=e,f5(h5)}const $c=t=>t;var ac=new xr,ef=new xr,vm,_m,tf,nf,Jr={point:fn,lineStart:fn,lineEnd:fn,polygonStart:function(){Jr.lineStart=d5,Jr.lineEnd=m5},polygonEnd:function(){Jr.lineStart=Jr.lineEnd=Jr.point=fn,ac.add(nn(ef)),ef=new xr},result:function(){var t=ac/2;return ac=new xr,t}};function d5(){Jr.point=g5}function g5(t,e){Jr.point=xm,vm=tf=t,_m=nf=e}function xm(t,e){ef.add(nf*t-tf*e),tf=t,nf=e}function m5(){xm(vm,_m)}var Xa=1/0,Ml=Xa,Io=-Xa,pl=Io,Sl={point:y5,lineStart:fn,lineEnd:fn,polygonStart:fn,polygonEnd:fn,result:function(){var t=[[Xa,Ml],[Io,pl]];return Io=pl=-(Ml=Xa=1/0),t}};function y5(t,e){t<Xa&&(Xa=t),t>Io&&(Io=t),e<Ml&&(Ml=e),e>pl&&(pl=e)}var rf=0,af=0,_o=0,Al=0,Tl=0,Sa=0,of=0,sf=0,xo=0,bm,km,Er,Or,or={point:ia,lineStart:v0,lineEnd:_0,polygonStart:function(){or.lineStart=x5,or.lineEnd=b5},polygonEnd:function(){or.point=ia,or.lineStart=v0,or.lineEnd=_0},result:function(){var t=xo?[of/xo,sf/xo]:Sa?[Al/Sa,Tl/Sa]:_o?[rf/_o,af/_o]:[NaN,NaN];return rf=af=_o=Al=Tl=Sa=of=sf=xo=0,t}};function ia(t,e){rf+=t,af+=e,++_o}function v0(){or.point=v5}function v5(t,e){or.point=_5,ia(Er=t,Or=e)}function _5(t,e){var n=t-Er,r=e-Or,i=jr(n*n+r*r);Al+=i*(Er+t)/2,Tl+=i*(Or+e)/2,Sa+=i,ia(Er=t,Or=e)}function _0(){or.point=ia}function x5(){or.point=k5}function b5(){wm(bm,km)}function k5(t,e){or.point=wm,ia(bm=Er=t,km=Or=e)}function wm(t,e){var n=t-Er,r=e-Or,i=jr(n*n+r*r);Al+=i*(Er+t)/2,Tl+=i*(Or+e)/2,Sa+=i,i=Or*t-Er*e,of+=i*(Er+t),sf+=i*(Or+e),xo+=i*3,ia(Er=t,Or=e)}function Mm(t){this._context=t}Mm.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(t,e){switch(this._point){case 0:{this._context.moveTo(t,e),this._point=1;break}case 1:{this._context.lineTo(t,e);break}default:{this._context.moveTo(t+this._radius,e),this._context.arc(t,e,this._radius,0,fr);break}}},result:fn};var lf=new xr,oc,pm,Sm,bo,ko,zo={point:fn,lineStart:function(){zo.point=w5},lineEnd:function(){oc&&Am(pm,Sm),zo.point=fn},polygonStart:function(){oc=!0},polygonEnd:function(){oc=null},result:function(){var t=+lf;return lf=new xr,t}};function w5(t,e){zo.point=Am,pm=bo=t,Sm=ko=e}function Am(t,e){bo-=t,ko-=e,lf.add(jr(bo*bo+ko*ko)),bo=t,ko=e}let x0,Cl,b0,k0;class w0{constructor(e){this._append=e==null?Tm:M5(e),this._radius=4.5,this._=""}pointRadius(e){return this._radius=+e,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(e,n){switch(this._point){case 0:{this._append`M${e},${n}`,this._point=1;break}case 1:{this._append`L${e},${n}`;break}default:{if(this._append`M${e},${n}`,this._radius!==b0||this._append!==Cl){const r=this._radius,i=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,b0=r,Cl=this._append,k0=this._,this._=i}this._+=k0;break}}}result(){const e=this._;return this._="",e.length?e:null}}function Tm(t){let e=1;this._+=t[0];for(const n=t.length;e<n;++e)this._+=arguments[e]+t[e]}function M5(t){const e=Math.floor(t);if(!(e>=0))throw new RangeError(`invalid digits: ${t}`);if(e>15)return Tm;if(e!==x0){const n=10**e;x0=e,Cl=function(i){let a=1;this._+=i[0];for(const s=i.length;a<s;++a)this._+=Math.round(arguments[a]*n)/n+i[a]}}return Cl}function p5(t,e){let n=3,r=4.5,i,a;function s(l){return l&&(typeof r=="function"&&a.pointRadius(+r.apply(this,arguments)),Si(l,i(a))),a.result()}return s.area=function(l){return Si(l,i(Jr)),Jr.result()},s.measure=function(l){return Si(l,i(zo)),zo.result()},s.bounds=function(l){return Si(l,i(Sl)),Sl.result()},s.centroid=function(l){return Si(l,i(or)),or.result()},s.projection=function(l){return arguments.length?(i=l==null?(t=null,$c):(t=l).stream,s):t},s.context=function(l){return arguments.length?(a=l==null?(e=null,new w0(n)):new Mm(e=l),typeof r!="function"&&a.pointRadius(r),s):e},s.pointRadius=function(l){return arguments.length?(r=typeof l=="function"?l:(a.pointRadius(+l),+l),s):r},s.digits=function(l){if(!arguments.length)return n;if(l==null)n=null;else{const u=Math.floor(l);if(!(u>=0))throw new RangeError(`invalid digits: ${l}`);n=u}return e===null&&(a=new w0(n)),s},s.projection(t).digits(n).context(e)}function S5(t){return{stream:ou(t)}}function ou(t){return function(e){var n=new uf;for(var r in t)n[r]=t[r];return n.stream=e,n}}function uf(){}uf.prototype={constructor:uf,point:function(t,e){this.stream.point(t,e)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function qf(t,e,n){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),r!=null&&t.clipExtent(null),Si(n,t.stream(Sl)),e(Sl.result()),r!=null&&t.clipExtent(r),t}function Cm(t,e,n){return qf(t,function(r){var i=e[1][0]-e[0][0],a=e[1][1]-e[0][1],s=Math.min(i/(r[1][0]-r[0][0]),a/(r[1][1]-r[0][1])),l=+e[0][0]+(i-s*(r[1][0]+r[0][0]))/2,u=+e[0][1]+(a-s*(r[1][1]+r[0][1]))/2;t.scale(150*s).translate([l,u])},n)}function A5(t,e,n){return Cm(t,[[0,0],e],n)}function T5(t,e,n){return qf(t,function(r){var i=+e,a=i/(r[1][0]-r[0][0]),s=(i-a*(r[1][0]+r[0][0]))/2,l=-a*r[0][1];t.scale(150*a).translate([s,l])},n)}function C5(t,e,n){return qf(t,function(r){var i=+e,a=i/(r[1][1]-r[0][1]),s=-a*r[0][0],l=(i-a*(r[1][1]+r[0][1]))/2;t.scale(150*a).translate([s,l])},n)}var M0=16,D5=At(30*Pt);function p0(t,e){return+e?R5(t,e):P5(t)}function P5(t){return ou({point:function(e,n){e=t(e,n),this.stream.point(e[0],e[1])}})}function R5(t,e){function n(r,i,a,s,l,u,c,f,h,d,g,m,v,w){var b=c-r,S=f-i,A=b*b+S*S;if(A>4*e&&v--){var N=s+d,I=l+g,T=u+m,L=jr(N*N+I*I+T*T),C=Ii(T/=L),_=nn(nn(T)-1)<Vt||nn(a-h)<Vt?(a+h)/2:ci(I,N),M=t(_,C),k=M[0],p=M[1],R=k-r,z=p-i,D=S*R-b*z;(D*D/A>e||nn((b*R+S*z)/A-.5)>.3||s*d+l*g+u*m<D5)&&(n(r,i,a,s,l,u,k,p,_,N/=L,I/=L,T,v,w),w.point(k,p),n(k,p,_,N,I,T,c,f,h,d,g,m,v,w))}}return function(r){var i,a,s,l,u,c,f,h,d,g,m,v,w={point:b,lineStart:S,lineEnd:N,polygonStart:function(){r.polygonStart(),w.lineStart=I},polygonEnd:function(){r.polygonEnd(),w.lineStart=S}};function b(C,_){C=t(C,_),r.point(C[0],C[1])}function S(){h=NaN,w.point=A,r.lineStart()}function A(C,_){var M=Ua([C,_]),k=t(C,_);n(h,d,f,g,m,v,h=k[0],d=k[1],f=C,g=M[0],m=M[1],v=M[2],M0,r),r.point(h,d)}function N(){w.point=b,r.lineEnd()}function I(){S(),w.point=T,w.lineEnd=L}function T(C,_){A(i=C,_),a=h,s=d,l=g,u=m,c=v,w.point=A}function L(){n(h,d,f,g,m,v,a,s,i,l,u,c,M0,r),w.lineEnd=N,N()}return w}}var E5=ou({point:function(t,e){this.stream.point(t*Pt,e*Pt)}});function O5(t){return ou({point:function(e,n){var r=t(e,n);return this.stream.point(r[0],r[1])}})}function N5(t,e,n,r,i){function a(s,l){return s*=r,l*=i,[e+t*s,n-t*l]}return a.invert=function(s,l){return[(s-e)/t*r,(n-l)/t*i]},a}function S0(t,e,n,r,i,a){if(!a)return N5(t,e,n,r,i);var s=At(a),l=Tt(a),u=s*t,c=l*t,f=s/t,h=l/t,d=(l*n-s*e)/t,g=(l*e+s*n)/t;function m(v,w){return v*=r,w*=i,[u*v-c*w+e,n-c*v-u*w]}return m.invert=function(v,w){return[r*(f*v-h*w+d),i*(g-h*v-f*w)]},m}function L5(t){return W5(function(){return t})()}function W5(t){var e,n=150,r=480,i=250,a=0,s=0,l=0,u=0,c=0,f,h=0,d=1,g=1,m=null,v=m0,w=null,b,S,A,N=$c,I=.5,T,L,C,_,M;function k(D){return C(D[0]*Pt,D[1]*Pt)}function p(D){return D=C.invert(D[0],D[1]),D&&[D[0]*Wn,D[1]*Wn]}k.stream=function(D){return _&&M===D?_:_=E5(O5(f)(v(T(N(M=D)))))},k.preclip=function(D){return arguments.length?(v=D,m=void 0,z()):v},k.postclip=function(D){return arguments.length?(N=D,w=b=S=A=null,z()):N},k.clipAngle=function(D){return arguments.length?(v=+D?i5(m=D*Pt):(m=null,m0),z()):m*Wn},k.clipExtent=function(D){return arguments.length?(N=D==null?(w=b=S=A=null,$c):o5(w=+D[0][0],b=+D[0][1],S=+D[1][0],A=+D[1][1]),z()):w==null?null:[[w,b],[S,A]]},k.scale=function(D){return arguments.length?(n=+D,R()):n},k.translate=function(D){return arguments.length?(r=+D[0],i=+D[1],R()):[r,i]},k.center=function(D){return arguments.length?(a=D[0]%360*Pt,s=D[1]%360*Pt,R()):[a*Wn,s*Wn]},k.rotate=function(D){return arguments.length?(l=D[0]%360*Pt,u=D[1]%360*Pt,c=D.length>2?D[2]%360*Pt:0,R()):[l*Wn,u*Wn,c*Wn]},k.angle=function(D){return arguments.length?(h=D%360*Pt,R()):h*Wn},k.reflectX=function(D){return arguments.length?(d=D?-1:1,R()):d<0},k.reflectY=function(D){return arguments.length?(g=D?-1:1,R()):g<0},k.precision=function(D){return arguments.length?(T=p0(L,I=D*D),z()):jr(I)},k.fitExtent=function(D,W){return Cm(k,D,W)},k.fitSize=function(D,W){return A5(k,D,W)},k.fitWidth=function(D,W){return T5(k,D,W)},k.fitHeight=function(D,W){return C5(k,D,W)};function R(){var D=S0(n,0,0,d,g,h).apply(null,e(a,s)),W=S0(n,r-D[0],i-D[1],d,g,h);return f=dm(l,u,c),L=Vc(e,W),C=Vc(f,L),T=p0(L,I),z()}function z(){return _=M=null,k}return function(){return e=t.apply(this,arguments),k.invert=e.invert&&p,R()}}function I5(t){return function(e,n){var r=jr(e*e+n*n),i=t(r),a=Tt(i),s=At(i);return[ci(e*a,r*s),Ii(r&&n*a/r)]}}function Dm(t,e){var n=At(e),r=1+At(t)*n;return[n*Tt(t)/r,Tt(e)/r]}Dm.invert=I5(function(t){return 2*um(t)});function z5(){return L5(Dm).scale(250).clipAngle(142)}const Uf=Math.PI,A0=Uf/2,T0=180/Uf,C0=Uf/180,j5=Math.atan2,D0=Math.cos,F5=Math.max,B5=Math.min,P0=Math.sin,Y5=Math.sign||function(t){return t>0?1:t<0?-1:0},Pm=Math.sqrt;function H5(t){return t>1?A0:t<-1?-A0:Math.asin(t)}function Rm(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function Qn(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function Dl(t,e){return[t[0]+e[0],t[1]+e[1],t[2]+e[2]]}function Pl(t){var e=Pm(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);return[t[0]/e,t[1]/e,t[2]/e]}function Gf(t){return[j5(t[1],t[0])*T0,H5(F5(-1,B5(1,t[2])))*T0]}function Wr(t){const e=t[0]*C0,n=t[1]*C0,r=D0(n);return[r*D0(e),r*P0(e),P0(n)]}function Xf(t){return t=t.map(e=>Wr(e)),Rm(t[0],Qn(t[2],t[1]))}function q5(t){const e=G5(t),n=V5(e),r=X5(n,t),i=K5(n,t.length),a=U5(i,t),s=Z5(n,t),{polygons:l,centers:u}=Q5(s,n,t),c=J5(l),f=eS(n,t),h=$5(r,n);return{delaunay:e,edges:r,triangles:n,centers:u,neighbors:i,polygons:l,mesh:c,hull:f,urquhart:h,find:a}}function U5(t,e){function n(r,i){let a=r[0]-i[0],s=r[1]-i[1],l=r[2]-i[2];return a*a+s*s+l*l}return function(i,a,s){s===void 0&&(s=0);let l,u,c=s;const f=Wr([i,a]);do l=s,s=null,u=n(f,Wr(e[l])),t[l].forEach(h=>{let d=n(f,Wr(e[h]));if(d<u){u=d,s=h,c=h;return}});while(s!==null);return c}}function G5(t){if(t.length<2)return{};let e=0;for(;isNaN(t[e][0]+t[e][1])&&e++<t.length;);const n=Kp(t[e]),r=z5().translate([0,0]).scale(1).rotate(n.invert([180,0]));t=t.map(r);const i=[];let a=1;for(let h=0,d=t.length;h<d;h++){let g=t[h][0]**2+t[h][1]**2;!isFinite(g)||g>1e32?i.push(h):g>a&&(a=g)}const s=1e6*Pm(a);i.forEach(h=>t[h]=[s,0]),t.push([0,s]),t.push([-s,0]),t.push([0,-s]);const l=au.from(t);l.projection=r;const{triangles:u,halfedges:c,inedges:f}=l;for(let h=0,d=c.length;h<d;h++)if(c[h]<0){const g=h%3==2?h-2:h+1,m=h%3==0?h+2:h-1,v=c[g],w=c[m];c[v]=w,c[w]=v,c[g]=c[m]=-1,u[h]=u[g]=u[m]=e,f[u[v]]=v%3==0?v+2:v-1,f[u[w]]=w%3==0?w+2:w-1,h+=2-h%3}else u[h]>t.length-3-1&&(u[h]=e);return l}function X5(t,e){const n=new Set;return e.length===2?[[0,1]]:(t.forEach(r=>{if(r[0]!==r[1]&&!(Xf(r.map(i=>e[i]))<0))for(let i=0,a;i<3;i++)a=(i+1)%3,n.add(ur([r[i],r[a]]).join("-"))}),Array.from(n,r=>r.split("-").map(Number)))}function V5(t){const{triangles:e}=t;if(!e)return[];const n=[];for(let r=0,i=e.length/3;r<i;r++){const a=e[3*r],s=e[3*r+1],l=e[3*r+2];a!==s&&s!==l&&n.push([a,l,s])}return n}function Z5(t,e){return t.map(n=>{const r=n.map(a=>e[a]).map(Wr),i=Dl(Dl(Qn(r[1],r[0]),Qn(r[2],r[1])),Qn(r[0],r[2]));return Gf(Pl(i))})}function K5(t,e){const n=[];return t.forEach(r=>{for(let i=0;i<3;i++){const a=r[i],s=r[(i+1)%3];n[a]=n[a]||[],n[a].push(s)}}),t.length===0&&(e===2?(n[0]=[1],n[1]=[0]):e===1&&(n[0]=[])),n}function Q5(t,e,n){const r=[],i=t.slice();if(e.length===0){if(n.length<2)return{polygons:r,centers:i};if(n.length===2){const l=Wr(n[0]),u=Wr(n[1]),c=Pl(Dl(l,u)),f=Pl(Qn(l,u)),h=Qn(c,f),d=[c,Qn(c,h),Qn(Qn(c,h),h),Qn(Qn(Qn(c,h),h),h)].map(Gf).map(s);return r.push(d),r.push(d.slice().reverse()),{polygons:r,centers:i}}}e.forEach((l,u)=>{for(let c=0;c<3;c++){const f=l[c],h=l[(c+1)%3],d=l[(c+2)%3];r[f]=r[f]||[],r[f].push([h,d,u,[f,h,d]])}});const a=r.map(l=>{const u=[l[0][2]];let c=l[0][1];for(let f=1;f<l.length;f++)for(let h=0;h<l.length;h++)if(l[h][0]==c){c=l[h][1],u.push(l[h][2]);break}if(u.length>2)return u;if(u.length==2){const f=R0(n[l[0][3][0]],n[l[0][3][1]],i[u[0]]),h=R0(n[l[0][3][2]],n[l[0][3][0]],i[u[0]]),d=s(f),g=s(h);return[u[0],g,u[1],d]}});function s(l){let u=-1;return i.slice(e.length,1/0).forEach((c,f)=>{c[0]===l[0]&&c[1]===l[1]&&(u=f+e.length)}),u<0&&(u=i.length,i.push(l)),u}return{polygons:a,centers:i}}function R0(t,e,n){t=Wr(t),e=Wr(e),n=Wr(n);const r=Y5(Rm(Qn(e,t),n));return Gf(Pl(Dl(t,e)).map(i=>r*i))}function J5(t){const e=[];return t.forEach(n=>{if(!n)return;let r=n[n.length-1];for(let i of n)i>r&&e.push([r,i]),r=i}),e}function $5(t,e){return function(n){const r=new Map,i=new Map;return t.forEach((a,s)=>{const l=a.join("-");r.set(l,n[s]),i.set(l,!0)}),e.forEach(a=>{let s=0,l=-1;for(let u=0;u<3;u++){let c=ur([a[u],a[(u+1)%3]]).join("-");r.get(c)>s&&(s=r.get(c),l=c)}i.set(l,!1)}),t.map(a=>i.get(a.join("-")))}}function eS(t,e){const n=new Set,r=[];t.map(l=>{if(!(Xf(l.map(u=>e[u>e.length?0:u]))>1e-12))for(let u=0;u<3;u++){let c=[l[u],l[(u+1)%3]],f=`${c[0]}-${c[1]}`;n.has(f)?n.delete(f):n.add(`${c[1]}-${c[0]}`)}});const i=new Map;let a;if(n.forEach(l=>{l=l.split("-").map(Number),i.set(l[0],l[1]),a=l[0]}),a===void 0)return r;let s=a;do{r.push(s);let l=i.get(s);i.set(s,-1),s=l}while(s>-1&&s!==a);return r}function tS(t){const e=function(n){if(e.delaunay=null,e._data=n,typeof e._data=="object"&&e._data.type==="FeatureCollection"&&(e._data=e._data.features),typeof e._data=="object"){const r=e._data.map(i=>[e._vx(i),e._vy(i),i]).filter(i=>isFinite(i[0]+i[1]));e.points=r.map(i=>[i[0],i[1]]),e.valid=r.map(i=>i[2]),e.delaunay=q5(e.points)}return e};return e._vx=function(n){if(typeof n=="object"&&"type"in n)return u0(n)[0];if(0 in n)return n[0]},e._vy=function(n){if(typeof n=="object"&&"type"in n)return u0(n)[1];if(1 in n)return n[1]},e.x=function(n){return n?(e._vx=n,e):e._vx},e.y=function(n){return n?(e._vy=n,e):e._vy},e.polygons=function(n){if(n!==void 0&&e(n),!e.delaunay)return!1;const r={type:"FeatureCollection",features:[]};return e.valid.length===0||(e.delaunay.polygons.forEach((i,a)=>r.features.push({type:"Feature",geometry:i?{type:"Polygon",coordinates:[[...i,i[0]].map(s=>e.delaunay.centers[s])]}:null,properties:{site:e.valid[a],sitecoordinates:e.points[a],neighbours:e.delaunay.neighbors[a]}})),e.valid.length===1&&r.features.push({type:"Feature",geometry:{type:"Sphere"},properties:{site:e.valid[0],sitecoordinates:e.points[0],neighbours:[]}})),r},e.triangles=function(n){return n!==void 0&&e(n),e.delaunay?{type:"FeatureCollection",features:e.delaunay.triangles.map((r,i)=>(r=r.map(a=>e.points[a]),r.center=e.delaunay.centers[i],r)).filter(r=>Xf(r)>0).map(r=>({type:"Feature",properties:{circumcenter:r.center},geometry:{type:"Polygon",coordinates:[[...r,r[0]]]}}))}:!1},e.links=function(n){if(n!==void 0&&e(n),!e.delaunay)return!1;const r=e.delaunay.edges.map(a=>y0(e.points[a[0]],e.points[a[1]])),i=e.delaunay.urquhart(r);return{type:"FeatureCollection",features:e.delaunay.edges.map((a,s)=>({type:"Feature",properties:{source:e.valid[a[0]],target:e.valid[a[1]],length:r[s],urquhart:!!i[s]},geometry:{type:"LineString",coordinates:[e.points[a[0]],e.points[a[1]]]}}))}},e.mesh=function(n){return n!==void 0&&e(n),e.delaunay?{type:"MultiLineString",coordinates:e.delaunay.edges.map(r=>[e.points[r[0]],e.points[r[1]]])}:!1},e.cellMesh=function(n){if(n!==void 0&&e(n),!e.delaunay)return!1;const{centers:r,polygons:i}=e.delaunay,a=[];for(const s of i)if(s)for(let l=s.length,u=s[l-1],c=s[0],f=0;f<l;u=c,c=s[++f])c>u&&a.push([r[u],r[c]]);return{type:"MultiLineString",coordinates:a}},e._found=void 0,e.find=function(n,r,i){if(e._found=e.delaunay.find(n,r,e._found),!i||y0([n,r],e.points[e._found])<i)return e._found},e.hull=function(n){n!==void 0&&e(n);const r=e.delaunay.hull,i=e.points;return r.length===0?null:{type:"Polygon",coordinates:[[...r.map(a=>i[a]),i[r[0]]]]}},t?e(t):e}function nS(t,e,n){const r=w1(),i=p5(t,rS(e(r))),a=s=>(i(s),n===void 0?r+"":void 0);return Object.setPrototypeOf(a,i),a}function rS(t){return{beginPath(){},moveTo(e,n){t.lineStart(),t.point(e,n)},arc(e,n,r,i,a,s){},lineTo(e,n){t.point(e,n)},closePath(){t.lineEnd()}}}function iS(t,e,n){const r=t.fitSize(e,n),i=r.translate();return{translate:{x:i[0],y:i[1]},scale:r.scale()}}var aS=xt("<path></path>");function oS(t,e){it(e,!0);let n=P(e,"curve",3,P1),r=P(e,"ref",15),i=bt(e,["$$slots","$$events","$$legacy","fill","stroke","strokeWidth","opacity","geoTransform","geojson","tooltipContext","curve","onclick","class","ref","children"]),a=Ve(void 0);vt(()=>{r(o(a))});const s=Bf(),l=x(()=>e.geoTransform&&s.projection?S5(e.geoTransform(s.projection)):s.projection),u=x(()=>{if(e.geojson,!!o(l))return nS(o(l),n())}),c=Fr();function f(I,T){var C;if(!e.geojson)return;const L=(C=o(u))==null?void 0:C(e.geojson);iu(I,L,T?to({styles:{strokeWidth:e.strokeWidth}},T):{styles:{fill:e.fill,stroke:e.stroke,strokeWidth:e.strokeWidth,opacity:e.opacity},classes:e.class})}const h=cr(()=>e.fill),d=cr(()=>e.stroke);function g(I){var T;(T=e.onclick)==null||T.call(e,I,o(u))}const m=I=>{var T,L;(T=e.onpointerenter)==null||T.call(e,I),(L=e.tooltipContext)==null||L.show(I,e.geojson)},v=I=>{var T,L;(T=e.onpointermove)==null||T.call(e,I),(L=e.tooltipContext)==null||L.show(I,e.geojson)},w=I=>{var T,L;(T=e.onpointerleave)==null||T.call(e,I),(L=e.tooltipContext)==null||L.hide()};c==="canvas"&&mi({name:"GeoPath",render:f,events:{click:g,pointerenter:m,pointermove:v,pointerleave:w,pointerdown:e.onpointerdown,touchmove:e.ontouchmove},deps:()=>[e.geojson,o(l),h.current,d.current,e.strokeWidth,e.opacity,e.class]});var b=Ce(),S=ce(b);{var A=I=>{var T=Ce(),L=ce(T);Xe(L,()=>e.children,()=>({geoPath:o(u)})),Q(I,T)},N=(I,T)=>{{var L=C=>{var _=aS();wt(_,(M,k)=>({...i,d:M,fill:e.fill,stroke:e.stroke,"stroke-width":e.strokeWidth,opacity:e.opacity,onclick:g,onpointerenter:m,onpointermove:v,onpointerleave:w,class:k}),[()=>{var M;return e.geojson?(M=o(u))==null?void 0:M(e.geojson):""},()=>Le(Ne("geo-path"),e.fill==null&&"fill-transparent",e.class)]),Wt(_,M=>Re(a,M),()=>o(a)),Q(C,_)};ue(I,C=>{c==="svg"&&C(L)},T)}};ue(S,I=>{e.children?I(A):I(N,!1)})}Q(t,b),at()}var sS=xt("<circle></circle>");function aa(t,e){it(e,!0);let n=P(e,"cx",3,0),r=P(e,"cy",3,0),i=P(e,"r",3,1),a=P(e,"ref",15),s=bt(e,["$$slots","$$events","$$legacy","cx","initialCx","cy","initialCy","r","initialR","motion","fill","fillOpacity","stroke","strokeWidth","opacity","class","ref"]),l=Ve(void 0);vt(()=>{a(o(l))});const u=e.initialCx??n(),c=e.initialCy??r(),f=e.initialR??i(),h=Fr(),d=on(u,()=>n(),e.motion),g=on(c,()=>r(),e.motion),m=on(f,()=>i(),e.motion);function v(I,T){om(I,{cx:d.current,cy:g.current,r:m.current},T?to({styles:{strokeWidth:e.strokeWidth}},T):{styles:{fill:e.fill,fillOpacity:e.fillOpacity,stroke:e.stroke,strokeWidth:e.strokeWidth,opacity:e.opacity},classes:e.class})}const w=cr(()=>e.fill),b=cr(()=>e.stroke);h==="canvas"&&mi({name:"Circle",render:v,events:{click:e.onclick,pointerdown:e.onpointerdown,pointerenter:e.onpointerenter,pointermove:e.onpointermove,pointerleave:e.onpointerleave},deps:()=>[d.current,g.current,m.current,w.current,e.fillOpacity,b.current,e.strokeWidth,e.opacity,e.class]});var S=Ce(),A=ce(S);{var N=I=>{var T=sS();wt(T,L=>({cx:d.current,cy:g.current,r:m.current,fill:e.fill,"fill-opacity":e.fillOpacity,stroke:e.stroke,"stroke-width":e.strokeWidth,opacity:e.opacity,class:L,...s}),[()=>Le(Ne("circle"),e.fill==null&&"fill-surface-content",e.class)]),Wt(T,L=>Re(l,L),()=>o(l)),Q(I,T)};ue(A,I=>{h==="svg"&&I(N)})}Q(t,S),at()}function E0(t,e){const n=Mr();it(e,!0);let r=P(e,"id",19,()=>Rn("clipPath-",n)),i=P(e,"cx",3,0),a=P(e,"cy",3,0),s=P(e,"disabled",3,!1),l=P(e,"ref",15),u=bt(e,["$$slots","$$events","$$legacy","id","cx","cy","r","motion","disabled","ref","children"]),c=Ve(void 0);vt(()=>{l(o(c))}),lm(t,{get id(){return r()},get disabled(){return s()},get children(){return e.children},clip:h=>{var d=x(()=>Jt(u,"clip-path-circle"));aa(h,qe({get cx(){return i()},get cy(){return a()},get r(){return e.r},get motion(){return e.motion}},()=>o(d),{get ref(){return o(c)},set ref(g){Re(c,g,!0)}}))},$$slots:{clip:!0}}),at()}function lS(t,e){it(e,!0);let n=P(e,"classes",19,()=>({})),r=bt(e,["$$slots","$$events","$$legacy","data","r","classes","onclick","onpointerenter","onpointerdown","onpointermove","class"]);const i=Gt(),a=Bf(),s=x(()=>(e.data??i.flatData).map(h=>{const d=a.projection?i.x(h):i.xGet(h),g=a.projection?i.y(h):i.yGet(h),m=Array.isArray(d)?Bt(d):d,v=Array.isArray(g)?Bt(g):g;let w;if(i.radial){const b=br(m,v);w=[b[0]+i.width/2,b[1]+i.height/2]}else w=[m,v];return w.data=h,w})),l=x(()=>Math.max(i.width,0)),u=x(()=>Math.max(i.height,0)),c=x(()=>e.r===0||e.r==null||e.r===1/0),f=x(()=>Le(Ne("voronoi-g"),n().root,e.class));kr(t,qe(()=>r,{get class(){return o(f)},children:(h,d)=>{var g=Ce(),m=ce(g);{var v=b=>{var S=Ce();const A=x(()=>tS().polygons(o(s)));var N=ce(S);$t(N,17,()=>o(A).features,Pn,(I,T)=>{const L=x(()=>{var p;return e.r?(p=a.projection)==null?void 0:p.call(a,o(T).properties.sitecoordinates):null}),C=x(()=>{var p;return(p=o(L))==null?void 0:p[0]}),_=x(()=>{var p;return(p=o(L))==null?void 0:p[1]}),M=x(()=>e.r??0),k=x(()=>o(L)==null||o(c));E0(I,{get cx(){return o(C)},get cy(){return o(_)},get r(){return o(M)},get disabled(){return o(k)},children:(p,R)=>{const z=x(()=>Le(Ne("voronoi-geo-path"),"fill-transparent stroke-transparent",n().path));oS(p,{get geojson(){return o(T)},get class(){return o(z)},onclick:D=>{var W;return(W=e.onclick)==null?void 0:W.call(e,D,{data:o(T).properties.site.data,feature:o(T)})},onpointerenter:D=>{var W;return(W=e.onpointerenter)==null?void 0:W.call(e,D,{data:o(T).properties.site.data,feature:o(T)})},onpointermove:D=>{var W;return(W=e.onpointermove)==null?void 0:W.call(e,D,{data:o(T).properties.site.data,feature:o(T)})},onpointerdown:D=>{var W;return(W=e.onpointerdown)==null?void 0:W.call(e,D,{data:o(T).properties.site.data,feature:o(T)})},onpointerleave,ontouchmove:D=>{D.preventDefault()}})},$$slots:{default:!0}})}),Q(b,S)},w=b=>{var S=Ce();const A=x(()=>au.from(o(s)).voronoi([0,0,o(l),o(u)]));var N=ce(S);$t(N,17,()=>o(s),Pn,(I,T,L)=>{var C=Ce();const _=x(()=>o(A).renderCell(L));var M=ce(C);{var k=p=>{const R=x(()=>e.r??0);E0(p,{get cx(){return o(T)[0]},get cy(){return o(T)[1]},get r(){return o(R)},get disabled(){return o(c)},children:(z,D)=>{const W=x(()=>Le(Ne("voronoi-path"),"fill-transparent stroke-transparent",n().path));Va(z,{get pathData(){return o(_)},get class(){return o(W)},onclick:B=>{var H;return(H=e.onclick)==null?void 0:H.call(e,B,{data:o(T).data,point:o(T)})},onpointerenter:B=>{var H;return(H=e.onpointerenter)==null?void 0:H.call(e,B,{data:o(T).data,point:o(T)})},onpointermove:B=>{var H;return(H=e.onpointermove)==null?void 0:H.call(e,B,{data:o(T).data,point:o(T)})},onpointerleave,onpointerdown:B=>{var H;return(H=e.onpointerdown)==null?void 0:H.call(e,B,{data:o(T).data,point:o(T)})},ontouchmove:B=>{B.preventDefault()}})},$$slots:{default:!0}})};ue(M,p=>{o(_)&&p(k)})}Q(I,C)}),Q(b,S)};ue(m,b=>{a.projection?b(v):b(w,!1)})}Q(h,g)},$$slots:{default:!0}})),at()}function xa(t){return t*Math.PI/180}function Ps(t){return t*(180/Math.PI)}function uS(t,e){let n=Math.atan2(e,t);return n+=Math.PI/2,n<0&&(n+=2*Math.PI),{radius:Math.sqrt(t**2+e**2),radians:n}}function cS(t){return typeof t=="number"?t:Number(t.replace("%",""))/100}function Rs(t,e){return t instanceof Date?new Date(t.getTime()+e):t+e}function fS(t,e=!0){const n=[];return t.visit((r,i,a,s,l)=>{(e||Array.isArray(r))&&n.push({x:i,y:a,width:s-i,height:l-a})}),n}function hS({ctx:t,data:e,metaCtx:n}){return(n.stackSeries?[...n.visibleSeries].reverse():n.visibleSeries).map(a=>{var d;const s=a.data?$l(a.data,e,t.x):e,l=Yt(a.value??(a.data?t.y:a.key)),u=n.orientation==="vertical"?t.x(e):t.y(e),c=a.label??(a.key!=="default"?a.key:"value"),f=s?l(s):void 0,h=a.color??((d=t.cScale)==null?void 0:d.call(t,t.c(e)));return{...a.data,chartType:"bar",color:h,label:u,name:c,value:f,valueAccessor:l,key:a.key,payload:e,rawSeriesData:a,formatter:jt}})}function dS({ctx:t,data:e,metaCtx:n}){return(n.stackSeries?[...n.visibleSeries].reverse():n.visibleSeries).map(a=>{var d;const s=a.data?$l(a.data,e,t.x):e,l=Yt(a.value??(a.data?t.y:a.key)),u=t.x(e),c=a.label??(a.key!=="default"?a.key:"value"),f=s?l(s):void 0,h=a.color??((d=t.cScale)==null?void 0:d.call(t,t.c(e)));return{...a.data,chartType:"area",color:h,label:u,name:c,value:f,valueAccessor:l,key:a.key,payload:e,rawSeriesData:a,formatter:jt}})}function gS({ctx:t,data:e,metaCtx:n}){return n.visibleSeries.map(r=>{var f;const i=r.data?$l(r.data,e,t.x):e,a=t.x(e),s=Yt(r.value??(r.data?t.y:r.key)),l=r.label??(r.key!=="default"?r.key:"value"),u=i?s(i):void 0,c=r.color??((f=t.cScale)==null?void 0:f.call(t,t.c(e)));return{...r.data,chartType:"line",color:c,label:a,name:l,value:u,valueAccessor:s,key:r.key,payload:e,rawSeriesData:r,formatter:jt}})}function mS({ctx:t,data:e,metaCtx:n}){var l;const r=Yt(n.key),i=Yt(n.label),a=Yt(n.value),s=Yt(n.color);return[{key:r(e),label:i(e)||r(e),value:a(e),color:s(e)??((l=t.cScale)==null?void 0:l.call(t,t.c(e))),payload:e,chartType:"pie",labelAccessor:i,keyAccessor:r,valueAccessor:a,colorAccessor:s}]}function yS({ctx:t,data:e,metaCtx:n}){return[{payload:e,key:""}]}const Em=new ji("TooltipMetaContext");function vS(){return Em.getOr(null)}function _S(t){return Em.set(t)}function xS({ctx:t,tooltipData:e,metaCtx:n}){if(!n)return[{payload:e,key:""}];switch(n.type){case"bar":return hS({ctx:t,data:e,metaCtx:n});case"area":return dS({ctx:t,data:e,metaCtx:n});case"line":return gS({ctx:t,data:e,metaCtx:n});case"pie":case"arc":return mS({ctx:t,data:e,metaCtx:n});case"scatter":return yS({ctx:t,data:e,metaCtx:n})}}const Om=new ji("TooltipContext");function Vf(){return Om.get()}function bS(t){return Om.set(t)}var kS=(t,e,n)=>{o(e)&&n(t)},wS=(t,e,n,r)=>{o(e)&&n.data!=null&&r()(t,{data:n.data})},MS=()=>{},pS=(t,e,n)=>{var r;return e(t,(r=o(n))==null?void 0:r.data)},SS=t=>{const e=t.target;e!=null&&e.hasPointerCapture(t.pointerId)&&e.releasePointerCapture(t.pointerId)},AS=(t,e,n)=>{var r;e()(t,{data:(r=o(n))==null?void 0:r.data})},TS=xt("<rect></rect>"),CS=xt("<g></g>"),DS=xt("<rect></rect>"),PS=xt("<g><!></g>"),RS=rt("<div><div><!> <!></div></div>");function ES(t,e){it(e,!0);const n=Gt(),r=Bf();let i=P(e,"ref",15),a=P(e,"debug",3,!1),s=P(e,"findTooltipData",3,"closest"),l=P(e,"hideDelay",3,0),u=P(e,"locked",3,!1),c=P(e,"mode",3,"manual"),f=P(e,"onclick",3,()=>{}),h=P(e,"radius",3,1/0),d=P(e,"raiseTarget",3,!1),g=P(e,"tooltipContext",15),m=Ve(void 0);vt(()=>{i(o(m))});let v=Ve(0),w=Ve(0),b=Ve(null),S=Ve(Xo([])),A=Ve(!1),N=Ve(!1);const I=vS(),T={get x(){return o(v)},get y(){return o(w)},get data(){return o(b)},get payload(){return o(S)},show:k,hide:p,get mode(){return c()},get isHoveringTooltipArea(){return o(A)},get isHoveringTooltipContent(){return o(N)},set isHoveringTooltipContent(xe){Re(N,xe,!0)}};g(T),bS(T);let L;const C=To(xe=>{const te=n.x(xe);return Array.isArray(te)?te[0]:te}).left,_=To(xe=>{const te=n.y(xe);return Array.isArray(te)?te[0]:te}).left;function M(xe,te,ke,Se){switch(s()){case"closest":return te===void 0?xe:xe===void 0||Number(ke)-Number(Se(xe))>Number(Se(te))-Number(ke)?te:xe;case"left":return xe;case"right":default:return te}}function k(xe,te){var we;if(L&&clearTimeout(L),u())return;const ke=xe.target.closest(".lc-root-container"),Se=ei(xe,ke);if(o(m)!==void 0&&te==null&&(Se.x<o(m).offsetLeft||Se.x>o(m).offsetLeft+o(m).offsetWidth||Se.y<o(m).offsetTop||Se.y>o(m).offsetTop+o(m).offsetHeight)){p();return}if(te==null)switch(c()){case"bisect-x":{let se;if(n.radial){const{radians:be}=uS(Se.x-n.width/2,Se.y-n.height/2);se=Qr(n.xScale,be)}else se=Qr(n.xScale,Se.x-n.padding.left);const V=C(n.flatData,se,1),K=n.flatData[V-1],G=n.flatData[V];te=M(K,G,se,n.x);break}case"bisect-y":{const se=Qr(n.yScale,Se.y-n.padding.top),V=_(n.flatData,se,1),K=n.flatData[V-1],G=n.flatData[V];te=M(K,G,se,n.y);break}case"bisect-band":{const se=Qr(n.xScale,Se.x),V=Qr(n.yScale,Se.y);if(ht(n.xScale)){const K=n.flatData.filter(ae=>n.x(ae)===se).sort(Mu(n.y)),G=_(K,V,1),be=K[G-1],_e=K[G];te=M(be,_e,V,n.y)}else if(ht(n.yScale)){const K=n.flatData.filter(ae=>n.y(ae)===V).sort(Mu(n.x)),G=C(K,se,1),be=K[G-1],_e=K[G];te=M(be,_e,se,n.x)}break}case"quadtree":{te=(we=o(R))==null?void 0:we.find(Se.x-n.padding.left,Se.y-n.padding.top,h());break}}if(te){d()&&gM(xe.target);const se=xS({ctx:n,tooltipData:te,metaCtx:I});Re(v,Se.x,!0),Re(w,Se.y,!0),Re(b,te,!0),Re(S,se,!0)}else p()}function p(){u()||(Re(A,!1),L=setTimeout(()=>{!o(A)&&!o(N)&&(Re(b,null),Re(S,[],!0))},l()))}const R=x(()=>{if(c()==="quadtree")return am().x(xe=>{if(r.projection){const ke=n.x(xe),Se=n.y(xe);return(r.projection([ke,Se])??[0,0])[0]}const te=n.xGet(xe);return Array.isArray(te)?Bt(te):te}).y(xe=>{if(r.projection){const ke=n.x(xe),Se=n.y(xe);return(r.projection([ke,Se])??[0,0])[1]}const te=n.yGet(xe);return Array.isArray(te)?Bt(te):te}).addAll(n.flatData)}),z=x(()=>c()==="bounds"||c()==="band"?n.flatData.map(xe=>{const te=n.xGet(xe),ke=n.yGet(xe),Se=Array.isArray(te)?te[0]:te,we=Array.isArray(ke)?ke[0]:ke,se=ht(n.xScale)?n.xScale.padding()*n.xScale.step()/2:0,V=ht(n.yScale)?n.yScale.padding()*n.yScale.step()/2:0,K=Xt(n.xRange)-Bt(n.xRange),G=Xt(n.yRange)-Bt(n.yRange);if(c()==="band")return{x:ht(n.xScale)?Se-se:Bt(n.xRange),y:ht(n.yScale)?we-V:Bt(n.yRange),width:ht(n.xScale)?n.xScale.step():K,height:ht(n.yScale)?n.yScale.step():G,data:xe};if(c()==="bounds")return{x:ht(n.xScale)||Array.isArray(te)?Se-se:Bt(n.xRange),y:we-V,width:Array.isArray(te)?te[1]-te[0]:ht(n.xScale)?n.xScale.step():Bt(n.xRange)+Se,height:Array.isArray(ke)?ke[1]-ke[0]:ht(n.yScale)?n.yScale.step():Xt(n.yRange)-we,data:xe}}).filter(xe=>xe!==void 0).sort(Mu("x")):[]),D=x(()=>["bisect-x","bisect-y","bisect-band","quadtree"].includes(c()));var W=RS();W.__pointermove=[kS,D,k],W.__click=[wS,D,T,f],W.__keydown=[MS];let B;var H=nt(W);let $;var oe=nt(H);Xe(oe,()=>e.children??Rt,()=>({tooltipContext:T}));var ye=Fe(oe,2);{var ge=xe=>{Ls(xe,{children:(te,ke)=>{const Se=x(()=>({path:Le(a()&&"fill-danger/10 stroke-danger")}));lS(te,{get r(){return h()},onpointerenter:(we,{data:se})=>{k(we,se)},onpointermove:(we,{data:se})=>{k(we,se)},onpointerleave:()=>p(),onpointerdown:we=>{var se;(se=we.target)!=null&&se.hasPointerCapture(we.pointerId)&&we.target.releasePointerCapture(we.pointerId)},onclick:(we,{data:se})=>{f()(we,{data:se})},get classes(){return o(Se)}})},$$slots:{default:!0}})},Ee=(xe,te)=>{{var ke=we=>{Ls(we,{get center(){return n.radial},children:(se,V)=>{var K=CS();$t(K,21,()=>o(z),Pn,(G,be)=>{var _e=Ce(),ae=ce(_e);{var J=De=>{const Z=x(()=>o(be).y+o(be).height),ne=x(()=>o(be).x+o(be).width),he=x(()=>Le(Ne("tooltip-rect"),a()?"fill-danger/10 stroke-danger":"fill-transparent"));Qf(De,{get innerRadius(){return o(be).y},get outerRadius(){return o(Z)},get startAngle(){return o(be).x},get endAngle(){return o(ne)},get class(){return o(he)},onpointerenter:Te=>{var ve;return k(Te,(ve=o(be))==null?void 0:ve.data)},onpointermove:Te=>{var ve;return k(Te,(ve=o(be))==null?void 0:ve.data)},onpointerleave:()=>p(),onpointerdown:Te=>{const ve=Te.target;ve!=null&&ve.hasPointerCapture(Te.pointerId)&&ve.releasePointerCapture(Te.pointerId)},onclick:Te=>{var ve;f()(Te,{data:(ve=o(be))==null?void 0:ve.data})}})},de=De=>{var Z=TS();Z.__pointermove=[pS,k,be],Z.__pointerdown=[SS],Z.__click=[AS,f,be],mt(ne=>{var he,Te,ve,Ie;ft(Z,"x",(he=o(be))==null?void 0:he.x),ft(Z,"y",(Te=o(be))==null?void 0:Te.y),ft(Z,"width",(ve=o(be))==null?void 0:ve.width),ft(Z,"height",(Ie=o(be))==null?void 0:Ie.height),St(Z,0,ne)},[()=>pt(Le(Ne("tooltip-rect"),a()?"fill-danger/10 stroke-danger":"fill-transparent"))]),ba("pointerenter",Z,ne=>{var he;return k(ne,(he=o(be))==null?void 0:he.data)}),ba("pointerleave",Z,()=>p()),Q(De,Z)};ue(ae,De=>{n.radial?De(J):De(de,!1)})}Q(G,_e)}),$e(K),mt(G=>St(K,0,G),[()=>pt(Ne("tooltip-rects-g"))]),Q(se,K)},$$slots:{default:!0}})},Se=(we,se)=>{{var V=K=>{Ls(K,{pointerEvents:!1,children:(G,be)=>{jc(G,{children:(_e,ae)=>{var J=PS(),de=nt(J);{var De=Z=>{var ne=Ce(),he=ce(ne);$t(he,17,()=>fS(o(R),!1),Pn,(Te,ve)=>{var Ie=DS();mt(pe=>{ft(Ie,"x",o(ve).x),ft(Ie,"y",o(ve).y),ft(Ie,"width",o(ve).width),ft(Ie,"height",o(ve).height),St(Ie,0,pe)},[()=>pt(Le(Ne("tooltip-quadtree-rect"),a()?"fill-danger/10 stroke-danger":"fill-transparent"))]),Q(Te,Ie)}),Q(Z,ne)};ue(de,Z=>{o(R)&&Z(De)})}$e(J),mt(Z=>St(J,0,Z),[()=>pt(Ne("tooltip-quadtree-g"))]),Q(_e,J)},$$slots:{default:!0}})},$$slots:{default:!0}})};ue(we,K=>{c()==="quadtree"&&a()&&K(V)},se)}};ue(xe,we=>{c()==="bounds"||c()==="band"?we(ke):we(Se,!1)},te)}};ue(ye,xe=>{c()==="voronoi"?xe(ge):xe(Ee,!1)})}$e(H),$e(W),Wt(W,xe=>Re(m,xe),()=>o(m)),mt((xe,te)=>{St(W,1,xe),B=wr(W,"",B,{top:`${n.padding.top??""}px`,left:`${n.padding.left??""}px`,width:`${n.width??""}px`,height:`${n.height??""}px`}),St(H,1,te),$=wr(H,"",$,{top:`-${n.padding.top??0??""}px`,left:`-${n.padding.left??0??""}px`,width:`${n.containerWidth??""}px`,height:`${n.containerHeight??""}px`})},[()=>pt(Le(Ne("tooltip-context"),"absolute touch-none",a()&&o(D)&&"bg-danger/10 outline outline-danger")),()=>pt(Le(Ne("tooltip-context-container"),"absolute"))]),ba("pointerenter",W,xe=>{Re(A,!0),o(D)&&k(xe)}),ba("pointerleave",W,xe=>{Re(A,!1),p()}),Q(t,W),at()}mf(["pointermove","click","keydown","pointerdown"]);const OS=new ji("BrushContext");function NS(t){return OS.set(t)}var LS=(t,e)=>e(),WS=rt("<div></div> <div></div>",1),IS=rt("<div></div> <div></div>",1),zS=rt("<div></div> <!> <!>",1),jS=rt("<div><div><!></div> <!></div>");function FS(t,e){it(e,!0);const n=Gt();let r=P(e,"brushContext",15),i=P(e,"axis",3,"x"),a=P(e,"handleSize",3,5),s=P(e,"resetOnEnd",3,!1),l=P(e,"ignoreResetClick",3,!1),u=P(e,"xDomain",7),c=P(e,"yDomain",7),f=P(e,"mode",3,"integrated"),h=P(e,"disabled",3,!1),d=P(e,"range",19,()=>({})),g=P(e,"handle",19,()=>({})),m=P(e,"classes",19,()=>({})),v=P(e,"onBrushEnd",3,()=>{}),w=P(e,"onBrushStart",3,()=>{}),b=P(e,"onChange",3,()=>{}),S=P(e,"onReset",3,()=>{}),A=Ve(void 0);u()===void 0&&u(n.xScale.domain()),c()===void 0&&c(n.yScale.domain()),vt(()=>{u()===void 0&&u(n.xScale.domain())}),vt(()=>{c()===void 0&&c(n.yScale.domain())});const N=u(),I=c(),T=n.config.xDomain,L=n.config.yDomain,C=x(()=>ur(n.xScale.domain())),_=x(()=>o(C)[0]),M=x(()=>o(C)[1]),k=x(()=>ur(n.yScale.domain())),p=x(()=>o(k)[0]),R=x(()=>o(k)[1]),z=x(()=>{var J;return n.yScale((J=c())==null?void 0:J[1])}),D=x(()=>{var J;return n.yScale((J=c())==null?void 0:J[0])}),W=x(()=>{var J;return n.xScale((J=u())==null?void 0:J[0])}),B=x(()=>{var J;return n.xScale((J=u())==null?void 0:J[1])}),H=x(()=>({x:i()==="both"||i()==="x"?o(W):0,y:i()==="both"||i()==="y"?o(z):0,width:i()==="both"||i()==="x"?o(B)-o(W):n.width,height:i()==="both"||i()==="y"?o(D)-o(z):n.height}));let $=Ve(!1);const oe={get xDomain(){return u()},set xDomain(J){u(J)},get yDomain(){return c()},set yDomain(J){c(J)},get isActive(){return o($)},set isActive(J){Re($,J,!0)},get range(){return o(H)},get handleSize(){return a()}};r(oe),NS(oe);const ye=new qg("BrushContext"),ge=1;function Ee(J){return de=>{var Te,ve,Ie,pe;ye.debug("drag start"),de.stopPropagation();const De=ei(de,o(A));if(De&&(De.x<0||De.x>n.width||De.y<0||De.y>n.height)){ye.debug("ignoring click as outside of chart bounds",{startPoint:De,width:n.width,height:n.height});return}const Z={xDomain:[((Te=u())==null?void 0:Te[0])??o(_),((ve=u())==null?void 0:ve[1])??o(M)],yDomain:[((Ie=c())==null?void 0:Ie[0])??o(p),((pe=c())==null?void 0:pe[1])??o(R)],value:{x:Qr(n.xScale,(De==null?void 0:De.x)??0),y:Qr(n.yScale,(De==null?void 0:De.y)??0)}};w()({xDomain:u(),yDomain:c()});const ne=Ke=>{const Ge=ei(Ke,o(A));J(Z,{x:Qr(n.xScale,(Ge==null?void 0:Ge.x)??0),y:Qr(n.yScale,(Ge==null?void 0:Ge.y)??0)}),b()({xDomain:u(),yDomain:c()})},he=Ke=>{const Ge=ei(Ke,o(A)),ut=Math.abs(((De==null?void 0:De.x)??0)-((Ge==null?void 0:Ge.x)??0)),ot=Math.abs(((De==null?void 0:De.y)??0)-((Ge==null?void 0:Ge.y)??0));!Array.from(Ke.target.classList).some(Et=>["range","handle"].includes(Et))&&ut<ge&&ot<ge||o(H).width<ge||o(H).height<ge?l()?ye.debug("ignoring frame click reset"):(ye.debug("resetting due to frame click"),V(),b()({xDomain:u(),yDomain:c()})):ye.debug("drag end",{target:Ke.target,xPointDelta:ut,yPointDelta:ot,rangeWidth:o(H).width,rangeHeight:o(H).height}),v()({xDomain:u(),yDomain:c()}),s()&&(l()?oe.isActive=!1:V()),window.removeEventListener("pointermove",ne),window.removeEventListener("pointerup",he)};window.addEventListener("pointermove",ne),window.addEventListener("pointerup",he)}}const xe=Ee((J,de)=>{ye.debug("createRange"),oe.isActive=!0,u([Hn(Bt([J.value.x,de.x]),o(_),o(M)),Hn(Xt([J.value.x,de.x]),o(_),o(M))]),c([Hn(Bt([J.value.y,de.y]),o(p),o(R)),Hn(Xt([J.value.y,de.y]),o(p),o(R))])}),te=Ee((J,de)=>{ye.debug("adjustRange");const De=Hn(de.x-J.value.x,o(_)-J.xDomain[0],o(M)-J.xDomain[1]);u([Rs(J.xDomain[0],De),Rs(J.xDomain[1],De)]);const Z=Hn(de.y-J.value.y,o(p)-J.yDomain[0],o(R)-J.yDomain[1]);c([Rs(J.yDomain[0],Z),Rs(J.yDomain[1],Z)])}),ke=Ee((J,de)=>{ye.debug("adjustTop"),c([Hn(de.y<J.yDomain[0]?de.y:J.yDomain[0],o(p),o(R)),Hn(de.y<J.yDomain[0]?J.yDomain[0]:de.y,o(p),o(R))])}),Se=Ee((J,de)=>{ye.debug("adjustBottom"),c([Hn(de.y>J.yDomain[1]?J.yDomain[1]:de.y,o(p),o(R)),Hn(de.y>J.yDomain[1]?de.y:J.yDomain[1],o(p),o(R))])}),we=Ee((J,de)=>{ye.debug("adjustLeft"),u([Hn(de.x>J.xDomain[1]?J.xDomain[1]:de.x,o(_),o(M)),Hn(de.x>J.xDomain[1]?de.x:J.xDomain[1],o(_),o(M))])}),se=Ee((J,de)=>{ye.debug("adjustRight"),u([Hn(de.x<J.xDomain[0]?de.x:J.xDomain[0],o(_),o(M)),Hn(de.x<J.xDomain[0]?J.xDomain[0]:de.x,o(_),o(M))])});function V(){ye.debug("reset"),oe.isActive=!1,S()({xDomain:u(),yDomain:c()}),u(N),c(I)}function K(){ye.debug("selectedAll"),u([o(_),o(M)]),c([o(p),o(R)])}vt(()=>{var J,de,De,Z,ne,he,Te,ve,Ie,pe,Ke,Ge;if(f()==="separated"){const ut=((de=(J=u())==null?void 0:J[0])==null?void 0:de.valueOf())!==((De=T==null?void 0:T[0])==null?void 0:De.valueOf())||((ne=(Z=u())==null?void 0:Z[1])==null?void 0:ne.valueOf())!==((he=T==null?void 0:T[1])==null?void 0:he.valueOf()),ot=((ve=(Te=c())==null?void 0:Te[0])==null?void 0:ve.valueOf())!==((Ie=L==null?void 0:L[0])==null?void 0:Ie.valueOf())||((Ke=(pe=c())==null?void 0:pe[1])==null?void 0:Ke.valueOf())!==((Ge=L==null?void 0:L[1])==null?void 0:Ge.valueOf()),Ze=i()==="x"?ut:i()=="y"?ot:ut||ot;oe.isActive=Ze}});var G=Ce(),be=ce(G);{var _e=J=>{var de=Ce(),De=ce(de);Xe(De,()=>e.children??Rt,()=>({brushContext:oe})),Q(J,de)},ae=J=>{var de=jS();const De=x(()=>Ne("brush-handle"));de.__pointerdown=xe,de.__dblclick=[LS,K];let Z;var ne=nt(de);let he;var Te=nt(ne);Xe(Te,()=>e.children??Rt,()=>({brushContext:oe})),$e(ne);var ve=Fe(ne,2);{var Ie=pe=>{var Ke=zS(),Ge=ce(Ke),ut=()=>V();wt(Ge,qt=>({...d(),class:qt,onpointerdown:te,ondblclick:ut,[ir]:{left:`${o(H).x??""}px`,top:`${o(H).y??""}px`,width:`${o(H).width??""}px`,height:`${o(H).height??""}px`}}),[()=>{var qt;return Le(Ne("brush-range"),"absolute bg-surface-content/10 cursor-move select-none","z-10",m().range,(qt=d())==null?void 0:qt.class)}]);var ot=Fe(Ge,2);{var Ze=qt=>{var Ot=WS(),un=ce(Ot),Zt=Ct=>{Ct.stopPropagation(),c()&&(c()[0]=o(p),b()({xDomain:u(),yDomain:c()}))};wt(un,Ct=>({...g(),"data-position":"top",class:Ct,onpointerdown:ke,ondblclick:Zt,[ir]:{left:`${o(H).x??""}px`,top:`${o(H).y??""}px`,width:`${o(H).width??""}px`,height:`${a()??""}px`}}),[()=>{var Ct;return Le(o(De),"cursor-ns-resize select-none","range absolute","z-10",m().handle,(Ct=g())==null?void 0:Ct.class)}]);var En=Fe(un,2),Sn=Ct=>{Ct.stopPropagation(),c()&&(c()[1]=o(R),b()({xDomain:u(),yDomain:c()}))};wt(En,Ct=>({...g(),"data-position":"bottom",class:Ct,onpointerdown:Se,ondblclick:Sn,[ir]:{left:`${o(H).x??""}px`,top:`${o(D)-a()}px`,width:`${o(H).width??""}px`,height:`${a()??""}px`}}),[()=>{var Ct;return Le(o(De),"handle bottom","cursor-ns-resize select-none","range absolute","z-10",m().handle,(Ct=g())==null?void 0:Ct.class)}]),Q(qt,Ot)};ue(ot,qt=>{(i()==="both"||i()==="y")&&qt(Ze)})}var Et=Fe(ot,2);{var rn=qt=>{var Ot=IS(),un=ce(Ot),Zt=Ct=>{Ct.stopPropagation(),u()&&(u()[0]=o(_),b()({xDomain:u(),yDomain:c()}))};wt(un,Ct=>({...g(),"data-position":"left",class:Ct,onpointerdown:we,ondblclick:Zt,[ir]:{left:`${o(H).x??""}px`,top:`${o(H).y??""}px`,width:`${a()??""}px`,height:`${o(H).height??""}px`}}),[()=>{var Ct;return Le(o(De),"cursor-ew-resize select-none","range absolute","z-10",m().handle,(Ct=g())==null?void 0:Ct.class)}]);var En=Fe(un,2),Sn=Ct=>{Ct.stopPropagation(),u()&&(u()[1]=o(M),b()({xDomain:u(),yDomain:c()}))};wt(En,Ct=>({...g(),"data-position":"right",class:Ct,onpointerdown:se,ondblclick:Sn,[ir]:{left:`${o(B)-a()+1}px`,top:`${o(H).y??""}px`,width:`${a()??""}px`,height:`${o(H).height??""}px`}}),[()=>{var Ct;return Le(o(De),"cursor-ew-resize select-none","range absolute","z-10",m().handle,(Ct=g())==null?void 0:Ct.class)}]),Q(qt,Ot)};ue(Et,qt=>{(i()==="both"||i()==="x")&&qt(rn)})}Q(pe,Ke)};ue(ve,pe=>{oe.isActive&&pe(Ie)})}$e(de),Wt(de,pe=>Re(A,pe),()=>o(A)),mt((pe,Ke)=>{St(de,1,pe),Z=wr(de,"",Z,{top:`${n.padding.top??""}px`,left:`${n.padding.left??""}px`,width:`${n.width??""}px`,height:`${n.height??""}px`}),St(ne,1,Ke),he=wr(ne,"",he,{top:`-${n.padding.top??0??""}px`,left:`-${n.padding.left??0??""}px`,width:`${n.containerWidth??""}px`,height:`${n.containerHeight??""}px`})},[()=>pt(Le(Ne("brush-context"),"absolute touch-none")),()=>pt(Le(Ne("brush-container"),"absolute"))]),Q(J,de)};ue(be,J=>{h()?J(_e):J(ae,!1)})}Q(t,G),at()}mf(["pointerdown","dblclick"]);const O0={top:0,right:0,bottom:0,left:0},Nm=new ji("ChartContext");function Gt(){return Nm.getOr({})}function BS(t){return Nm.set(t)}const Lm=new ji("RenderContext");function Fr(){return Lm.get()}function Zf(t){return Lm.set(t)}var YS=rt("<div><!></div>");function HS(t,e){it(e,!0);let n=P(e,"ssr",3,!1),r=P(e,"pointerEvents",3,!0),i=P(e,"position",3,"relative"),a=P(e,"percentRange",3,!1),s=P(e,"ref",15),l=P(e,"data",19,()=>[]),u=P(e,"xNice",3,!1),c=P(e,"yNice",3,!1),f=P(e,"zNice",3,!1),h=P(e,"rNice",3,!1),d=P(e,"xScale",19,Ni),g=P(e,"yScale",19,Ni),m=P(e,"zScale",19,Ni),v=P(e,"rScale",19,Ow),w=P(e,"padding",19,()=>({})),b=P(e,"verbose",3,!0),S=P(e,"debug",3,!1),A=P(e,"extents",19,()=>({})),N=P(e,"xDomainSort",3,!1),I=P(e,"yDomainSort",3,!1),T=P(e,"zDomainSort",3,!1),L=P(e,"rDomainSort",3,!1),C=P(e,"xReverse",3,!1),_=P(e,"zReverse",3,!1),M=P(e,"rReverse",3,!1),k=P(e,"xBaseline",3,null),p=P(e,"yBaseline",3,null),R=P(e,"meta",23,()=>({})),z=P(e,"radial",3,!1),D=P(e,"context",15),W=Ve(void 0);vt(()=>{s(o(W))});const B=x(()=>e.xRange?e.xRange:z()?[0,2*Math.PI]:void 0);let H=Ve(100),$=Ve(100);const oe=CM(kM,200),ye=x(()=>{if(e.xDomain!==void 0)return e.xDomain;if(k()!=null&&Array.isArray(l())){const X=l().flatMap(Yt(e.x));return[Bt([k(),...X]),Xt([k(),...X])]}}),ge=x(()=>{if(e.yDomain!==void 0)return e.yDomain;if(p()!=null&&Array.isArray(l())){const X=l().flatMap(Yt(e.y));return[Bt([p(),...X]),Xt([p(),...X])]}}),Ee=x(()=>e.yRange??(z()?({height:X})=>[0,X/2]:void 0)),xe=x(()=>g()?!ht(g()):!0),te=x(()=>ws(e.x)),ke=x(()=>ws(e.y)),Se=x(()=>ws(e.z)),we=x(()=>ws(e.r)),se=x(()=>Yt(e.c)),V=x(()=>Yt(e.x1)),K=x(()=>Yt(e.y1)),G=x(()=>e.flatData??l()),be=x(()=>Ud(_u(A()))),_e=x(()=>({x:o(te),y:o(ke),z:o(Se),r:o(we)})),ae=x(()=>typeof w()=="number"?{...O0,top:w(),right:w(),bottom:w(),left:w()}:{...O0,...w()});let J=Ve(!1);const de=x(()=>{const X=o(ae).top,fe=o(H)-o(ae).right,Pe=o($)-o(ae).bottom,Be=o(ae).left,Ue=fe-Be,Je=Pe-X;return b()===!0&&(Ue<=0&&o(J)===!0&&console.warn(`[LayerChart] Target div has zero or negative width (${Ue}). Did you forget to set an explicit width in CSS on the container?`),Je<=0&&o(J)===!0&&console.warn(`[LayerChart] Target div has zero or negative height (${Je}). Did you forget to set an explicit height in CSS on the container?`)),{top:X,left:Be,bottom:Pe,right:fe,width:Ue,height:Je}}),De=x(()=>o(de).width),Z=x(()=>o(de).height),ne=x(()=>{const X={x:{scale:d(),sort:N()},y:{scale:g(),sort:I()},z:{scale:m(),sort:T()},r:{scale:v(),sort:L()}},fe=Ud(o(_e),o(be)),Pe=Object.fromEntries(Object.keys(fe).map(Be=>[Be,X[Be]]));return Object.keys(fe).length>0?{...rM(o(G),fe,Pe),...o(be)}:{}}),he=x(()=>_s("x",o(ne),o(ye))),Te=x(()=>_s("y",o(ne),o(ge))),ve=x(()=>_s("z",o(ne),e.zDomain)),Ie=x(()=>_s("r",o(ne),e.rDomain)),pe=x(()=>e.x1Domain??ur(Ta(l()),o(V))),Ke=x(()=>e.y1Domain??ur(Ta(l()),o(K))),Ge=x(()=>e.cDomain??i2(Ta(l()).map(o(se)))),ut=x(()=>_u(e.xPadding)),ot=x(()=>_u(o(ne))),Ze=x(()=>xs("x",{scale:d(),domain:o(he),padding:o(ut),nice:u(),reverse:C(),percentRange:a(),range:o(B),height:o(Z),width:o(De),extents:o(ot)})),Et=x(()=>ga(o(te),o(Ze))),rn=x(()=>xs("y",{scale:g(),domain:o(Te),padding:e.yPadding,nice:c(),reverse:o(xe),percentRange:a(),range:o(Ee),height:o(Z),width:o(De),extents:o(be)})),qt=x(()=>ga(o(ke),o(rn))),Ot=x(()=>xs("z",{scale:m(),domain:o(ve),padding:e.zPadding,nice:f(),reverse:_(),percentRange:a(),range:e.zRange,height:o(Z),width:o(De),extents:o(be)})),un=x(()=>ga(o(Se),o(Ot))),Zt=x(()=>xs("r",{scale:v(),domain:o(Ie),padding:e.rPadding,nice:h(),reverse:M(),percentRange:a(),range:e.rRange,height:o(Z),width:o(De),extents:o(be)})),En=x(()=>ga(o(we),o(Zt))),Sn=x(()=>e.x1Scale&&e.x1Range?ec(e.x1Scale,o(pe),e.x1Range,{xScale:o(Ze),width:o(De),height:o(Z)}):null),Ct=x(()=>ga(o(V),o(Sn))),pr=x(()=>e.y1Scale&&e.y1Range?ec(e.y1Scale,o(Ke),e.y1Range,{yScale:o(rn),width:o(De),height:o(Z)}):null),Fi=x(()=>ga(o(K),o(pr))),Yr=x(()=>e.cRange?ec(e.cScale??eu(),o(Ge),e.cRange,{width:o(De),height:o(Z)}):null),yi=x(()=>X=>{var fe;return(fe=o(Yr))==null?void 0:fe(o(se)(X))}),vi=x(()=>o(Ze).domain()),_i=x(()=>o(rn).domain()),en=x(()=>o(Ot).domain()),gn=x(()=>o(Zt).domain()),An=x(()=>ks(o(Ze))),mn=x(()=>ks(o(rn))),hr=x(()=>ks(o(Ot))),Hr=x(()=>ks(o(Zt))),xi=x(()=>o(De)/o(Z)),qr=x(()=>({x:e.x,y:e.y,z:e.z,r:e.r,c:e.c,x1:e.x1,y1:e.y1,xDomain:o(ye),yDomain:o(ge),zDomain:e.zDomain,rDomain:e.rDomain,x1Domain:e.x1Domain,y1Domain:e.y1Domain,cDomain:e.cDomain,xRange:e.xRange,yRange:e.yRange,zRange:e.zRange,rRange:e.rRange,cRange:e.cRange,x1Range:e.x1Range,y1Range:e.y1Range}));let Gn=Ve(null),Sr=Ve(null),Ar=Ve(null),Ur=Ve(null);const On={get activeGetters(){return o(_e)},get config(){return o(qr)},get width(){return o(De)},get height(){return o(Z)},get percentRange(){return a()},get aspectRatio(){return o(xi)},get containerWidth(){return o(H)},get containerHeight(){return o($)},get x(){return o(te)},get y(){return o(ke)},get z(){return o(Se)},get r(){return o(we)},get c(){return o(se)},get x1(){return o(V)},get y1(){return o(K)},get data(){return l()},get xNice(){return u()},get yNice(){return c()},get zNice(){return f()},get rNice(){return h()},get xDomainSort(){return N()},get yDomainSort(){return I()},get zDomainSort(){return T()},get rDomainSort(){return L()},get xReverse(){return C()},get yReverse(){return o(xe)},get zReverse(){return _()},get rReverse(){return M()},get xPadding(){return e.xPadding},get yPadding(){return e.yPadding},get zPadding(){return e.zPadding},get rPadding(){return e.rPadding},get padding(){return o(ae)},get flatData(){return o(G)},get extents(){return o(ne)},get xDomain(){return o(vi)},get yDomain(){return o(_i)},get zDomain(){return o(en)},get rDomain(){return o(gn)},get cDomain(){return o(Ge)},get x1Domain(){return o(pe)},get y1Domain(){return o(Ke)},get xRange(){return o(An)},get yRange(){return o(mn)},get zRange(){return o(hr)},get rRange(){return o(Hr)},get cRange(){return e.cRange},get x1Range(){return e.x1Range},get y1Range(){return e.y1Range},get meta(){return R()},set meta(X){R(X)},get xScale(){return o(Ze)},get yScale(){return o(rn)},get zScale(){return o(Ot)},get rScale(){return o(Zt)},get yGet(){return o(qt)},get xGet(){return o(Et)},get zGet(){return o(un)},get rGet(){return o(En)},get cGet(){return o(yi)},get x1Get(){return o(Ct)},get y1Get(){return o(Fi)},get cScale(){return o(Yr)},get x1Scale(){return o(Sn)},get y1Scale(){return o(pr)},get radial(){return z()},get containerRef(){return o(W)},get geo(){return o(Gn)},get transform(){return o(Sr)},get tooltip(){return o(Ar)},get brush(){return o(Ur)}};D(On),BS(On),Ir(()=>{Re(J,!0)}),df(()=>{o(de)&&S()===!0&&(n()===!0||typeof window<"u")&&oe({data:l(),flatData:typeof o(G)<"u"?o(G):null,boundingBox:o(de),activeGetters:o(_e),x:e.x,y:e.y,z:e.z,r:e.r,xScale:o(Ze),yScale:o(rn),zScale:o(Ot),rScale:o(Zt)})}),Ir(()=>{var X;o(J)&&((X=e.onResize)==null||X.call(e,{width:On.width,height:On.height,containerWidth:On.containerWidth,containerHeight:On.containerHeight}))});const bi=x(()=>{var X,fe,Pe,Be;return(fe=(X=e.geo)==null?void 0:X.applyTransform)!=null&&fe.includes("translate")&&((Pe=e.geo)!=null&&Pe.fitGeojson)&&((Be=e.geo)!=null&&Be.projection)?iS(e.geo.projection(),[o(De),o(Z)],e.geo.fitGeojson):void 0}),tn=x(()=>{if(e.geo)return(X,fe,Pe,Be)=>{var Ue,Je;if((Ue=e.geo.applyTransform)!=null&&Ue.includes("rotate")&&((Je=o(Gn))!=null&&Je.projection)){const et=o(Gn).projection.scale()??0,ct=75;return{x:X+Pe*(ct/et),y:fe+Be*(ct/et)*-1}}else return{x:X+Pe,y:fe+Be}}}),O=x(()=>typeof e.brush=="object"?e.brush:{disabled:!e.brush}),j=x(()=>typeof e.tooltip=="object"?e.tooltip:{});var F=Ce(),U=ce(F);{var q=X=>{var fe=YS();let Pe;var Be=nt(fe);Il(Be,()=>o(J),Ue=>{const Je=x(()=>{var kt,wn,yn;return((kt=e.transform)==null?void 0:kt.mode)??((yn=(wn=e.geo)==null?void 0:wn.applyTransform)==null?void 0:yn.length)?"manual":"none"}),et=x(()=>{var kt;return(kt=o(bi))==null?void 0:kt.translate}),ct=x(()=>{var kt;return(kt=o(bi))==null?void 0:kt.scale});WM(Ue,qe({get mode(){return o(Je)},get initialTranslate(){return o(et)},get initialScale(){return o(ct)},get processTranslate(){return o(tn)}},()=>e.transform,{get ondragstart(){return e.ondragstart},get onTransform(){return e.onTransform},get ondragend(){return e.ondragend},get transformContext(){return o(Sr)},set transformContext(kt){Re(Sr,kt,!0)},children:(kt,wn)=>{zM(kt,qe(()=>e.geo,{get geoContext(){return o(Gn)},set geoContext(yn){Re(Gn,yn,!0)},children:(yn,ca)=>{FS(yn,qe(()=>o(O),{get brushContext(){return o(Ur)},set brushContext(Tn){Re(Ur,Tn,!0)},children:(Tn,Mn)=>{ES(Tn,qe(()=>o(j),{get tooltipContext(){return o(Ar)},set tooltipContext(oo){Re(Ar,oo,!0)},children:(oo,ih)=>{var rs=Ce(),uu=ce(rs);Xe(uu,()=>e.children??Rt,()=>({context:On})),Q(oo,rs)},$$slots:{default:!0}}))},$$slots:{default:!0}}))},$$slots:{default:!0}}))},$$slots:{default:!0}}))}),$e(fe),Wt(fe,Ue=>Re(W,Ue),()=>o(W)),mt(Ue=>{St(fe,1,Ue,"svelte-1j8sovf"),Pe=wr(fe,"",Pe,{position:i(),top:i()==="absolute"?"0":null,right:i()==="absolute"?"0":null,bottom:i()==="absolute"?"0":null,left:i()==="absolute"?"0":null,"pointer-events":r()===!1?"none":null})},[()=>pt(Ne("root-container"))]),Fs(fe,"clientWidth",Ue=>Re(H,Ue)),Fs(fe,"clientHeight",Ue=>Re($,Ue)),Q(X,fe)};ue(U,X=>{(n()===!0||typeof window<"u")&&X(q)})}Q(t,F),at()}var qS=xt("<g><!></g>"),US=rt("<div><!></div>");function kr(t,e){it(e,!0);const n=Gt();let r=P(e,"center",3,!1),i=P(e,"preventTouchMove",3,!1),a=P(e,"opacity",3,void 0),s=P(e,"ref",15),l=bt(e,["$$slots","$$events","$$legacy","x","initialX","y","initialY","center","preventTouchMove","opacity","motion","transitionIn","transitionInParams","class","children","ref"]),u=Ve(void 0);vt(()=>{s(o(u))});const c=e.initialX??e.x,f=e.initialY??e.y,h=x(()=>e.x??(r()==="x"||r()===!0?n.width/2:0)),d=x(()=>e.y??(r()==="y"||r()===!0?n.height/2:0)),g=on(c,()=>o(h),e.motion),m=on(f,()=>o(d),e.motion),v=x(()=>{var C;return e.transitionIn?e.transitionIn:(C=Jo(e.motion))!=null&&C.options?gf:()=>{}}),w=x(()=>e.transitionInParams?e.transitionInParams:{easing:ag}),b=x(()=>{if(r()||e.x!=null||e.y!=null)return`translate(${g.current}px, ${m.current}px)`}),S=Fr();S==="canvas"&&mi({name:"Group",render:C=>{const _=C.globalAlpha;C.globalAlpha=a()??1,C.translate(g.current??0,m.current??0),C.globalAlpha=_},retainState:!0,events:{click:e.onclick,dblclick:e.ondblclick,pointerenter:e.onpointerenter,pointermove:e.onpointermove,pointerleave:e.onpointerleave,pointerdown:e.onpointerdown},deps:()=>[g.current,m.current,a()]});const A=C=>{var _;i()&&C.preventDefault(),(_=e.ontouchmove)==null||_.call(e,C)};var N=Ce(),I=ce(N);{var T=C=>{var _=Ce(),M=ce(_);Xe(M,()=>e.children??Rt),Q(C,_)},L=(C,_)=>{{var M=p=>{var R=qS();wt(R,D=>({class:D,opacity:a(),...l,ontouchmove:A,[ir]:{transform:o(b)}}),[()=>Le(Ne("group-g"),e.class)]);var z=nt(R);Xe(z,()=>e.children??Rt),$e(R),Wt(R,D=>Re(u,D),()=>o(u)),js(1,R,()=>o(v),()=>o(w)),Q(p,R)},k=p=>{var R=US();wt(R,D=>({...l,class:D,ontouchmove:A,[ir]:{transform:o(b),opacity:a()}}),[()=>Le(Ne("group-div"),"absolute",e.class)]);var z=nt(R);Xe(z,()=>e.children??Rt),$e(R),Wt(R,D=>Re(u,D),()=>o(u)),js(1,R,()=>o(v),()=>o(w)),Q(p,R)};ue(C,p=>{S==="svg"?p(M):p(k,!1)},_)}};ue(I,C=>{S==="canvas"?C(T):C(L,!1)})}Q(t,N),at()}function GS(t,e=0){let n=t;return n=n.replace(/([MLTQCSAZ])(-?\d*\.?\d+),(-?\d*\.?\d+)/g,(r,i,a,s)=>`${i}${a},${e}`),n=n.replace(/([v])(-?\d*\.?\d+)/g,(r,i,a)=>`${i}0`),n}var XS=xt('<path d="M 0 0 L 10 5 L 0 10 z"></path>'),VS=xt('<polyline points="0 0, 10 5, 0 10"></polyline>'),ZS=xt("<circle></circle>"),KS=xt('<polyline points="5 0, 5 10"></polyline>'),QS=xt("<defs><marker><!></marker></defs>");function JS(t,e){const n=Mr();it(e,!0);let r=P(e,"id",19,()=>Rn("marker-",n)),i=P(e,"size",3,10),a=P(e,"markerWidth",19,i),s=P(e,"markerHeight",19,i),l=P(e,"markerUnits",3,"userSpaceOnUse"),u=P(e,"orient",3,"auto-start-reverse"),c=P(e,"refX",19,()=>["arrow","triangle"].includes(e.type??"")?9:5),f=P(e,"refY",3,5),h=P(e,"viewBox",3,"0 0 10 10"),d=bt(e,["$$slots","$$events","$$legacy","type","id","size","markerWidth","markerHeight","markerUnits","orient","refX","refY","viewBox","class","children"]);var g=QS(),m=nt(g);wt(m,S=>({id:r(),markerWidth:a(),markerHeight:s(),markerUnits:l(),orient:u(),refX:c(),refY:f(),viewBox:h(),...d,class:S}),[()=>Le(Ne("marker"),"overflow-visible",e.stroke==null&&(["arrow","circle-stroke","line"].includes(e.type??"")?"stroke-[context-stroke]":e.type==="circle"?"stroke-surface-100":"stroke-none"),"[stroke-linecap:round] [stroke-linejoin:round]",e.fill==null&&(["triangle","dot","circle"].includes(e.type??"")?"fill-[context-stroke]":e.type==="circle-stroke"?"fill-surface-100":"fill-none"),e.class)]);var v=nt(m);{var w=S=>{var A=Ce(),N=ce(A);Xe(N,()=>e.children),Q(S,A)},b=(S,A)=>{{var N=T=>{var L=XS();mt(C=>St(L,0,C),[()=>pt(Ne("marker-triangle"))]),Q(T,L)},I=(T,L)=>{{var C=M=>{var k=VS();mt(p=>St(k,0,p),[()=>pt(Ne("marker-arrow"))]),Q(M,k)},_=(M,k)=>{{var p=z=>{var D=ZS();ft(D,"cx",5),ft(D,"cy",5),ft(D,"r",5),mt(W=>St(D,0,W),[()=>pt(Ne("marker-circle"))]),Q(z,D)},R=(z,D)=>{{var W=B=>{var H=KS();mt($=>St(H,0,$),[()=>pt(Ne("marker-line"))]),Q(B,H)};ue(z,B=>{e.type==="line"&&B(W)},D)}};ue(M,z=>{e.type==="circle"||e.type==="circle-stroke"||e.type==="dot"?z(p):z(R,!1)},k)}};ue(T,M=>{e.type==="arrow"?M(C):M(_,!1)},L)}};ue(S,T=>{e.type==="triangle"?T(N):T(I,!1)},A)}};ue(v,S=>{e.children?S(w):S(b,!1)})}$e(m),$e(g),Q(t,g),at()}function oi(t,e){var n=Ce(),r=ce(n);{var i=s=>{var l=Ce(),u=ce(l);Xe(u,()=>e.marker,()=>({id:e.id})),Q(s,l)},a=(s,l)=>{{var u=c=>{const f=x(()=>typeof e.marker=="string"?e.marker:void 0);JS(c,qe({get id(){return e.id},get type(){return o(f)}},()=>typeof e.marker=="object"?e.marker:null))};ue(s,c=>{e.marker&&c(u)},l)}};ue(r,s=>{typeof e.marker=="function"?s(i):s(a,!1)})}Q(t,n)}var $S=xt("<path></path><!><!><!><!><!>",1);function Va(t,e){const n=Mr();it(e,!0);const r=Gt();let i=P(e,"splineRef",15),a=bt(e,["$$slots","$$events","$$legacy","data","pathData","x","y","motion","draw","curve","defined","fill","stroke","strokeWidth","fillOpacity","class","marker","markerStart","markerMid","markerEnd","startContent","endContent","opacity","splineRef"]),s=Ve(void 0);vt(()=>{i(o(s))});const l=x(()=>e.markerStart??e.marker),u=x(()=>e.markerMid??e.marker),c=x(()=>e.markerEnd??e.marker),f=x(()=>o(l)?Rn("marker-start",n):""),h=x(()=>o(u)?Rn("marker-mid",n):""),d=x(()=>o(c)?Rn("marker-end",n):"");function g($,oe,ye){let ge=ye($);return Array.isArray(ge)&&(ge=Xt(ge)),oe.domain().length?oe(ge):ge}const m=x(()=>e.x?Yt(e.x):r.x),v=x(()=>e.y?Yt(e.y):r.y),w=x(()=>ht(r.xScale)?r.xScale.bandwidth()/2:0),b=x(()=>ht(r.yScale)?r.yScale.bandwidth()/2:0),S=Jo(e.motion),A=S?{type:S.type,options:{interpolate:R1.interpolatePath,...S.options}}:void 0;function N(){if(A){if(e.pathData)return GS(e.pathData,Math.min(r.yScale(0)??r.yRange[0],r.yRange[0]));if(r.config.x){const $=r.radial?ud().angle(oe=>r.xScale(o(m)(oe))+0).radius(oe=>Math.min(r.yScale(0),r.yRange[0])):el().x(oe=>r.xScale(o(m)(oe))+o(w)).y(oe=>Math.min(r.yScale(0),r.yRange[0]));return $.defined(e.defined??(oe=>o(m)(oe)!=null&&o(v)(oe)!=null)),e.curve&&$.curve(e.curve),$(e.data??r.data)}}else return""}const I=x(()=>{const $=r.radial?ud().angle(oe=>g(oe,r.xScale,o(m))+0).radius(oe=>g(oe,r.yScale,o(v))+o(b)):el().x(oe=>g(oe,r.xScale,o(m))+o(w)).y(oe=>g(oe,r.yScale,o(v))+o(b));return $.defined(e.defined??(oe=>o(m)(oe)!=null&&o(v)(oe)!=null)),e.curve&&$.curve(e.curve),e.pathData??$(e.data??r.data)??""}),T=on(N(),()=>o(I),A),L=x(()=>e.draw?xy:()=>({}));let C=Ve(Xo(Symbol()));const _=Fr();function M($,oe){iu($,T.current,oe?to({styles:{strokeWidth:e.strokeWidth}},oe):{styles:{fill:e.fill,fillOpacity:e.fillOpacity,stroke:e.stroke,strokeWidth:e.strokeWidth,opacity:e.opacity},classes:e.class})}const k=cr(()=>e.fill),p=cr(()=>e.stroke);_==="canvas"&&mi({name:"Spline",render:M,events:{click:e.onclick,pointerenter:e.onpointerenter,pointermove:e.onpointermove,pointerleave:e.onpointerleave,pointerdown:e.onpointerdown,pointerover:e.onpointerover,pointerout:e.onpointerout,touchmove:e.ontouchmove},deps:()=>[k.current,e.fillOpacity,p.current,e.strokeWidth,e.opacity,e.class,T.current]});let R=Ve(void 0);const z=x(()=>typeof e.draw=="object"&&e.draw.duration!==void 0&&typeof e.draw.duration!="function"?e.draw.duration:800),D=Pc(void 0,e.draw?{type:"tween",duration:()=>o(z),easing:typeof e.draw=="object"&&e.draw.easing?e.draw.easing:ky,interpolate(){return $=>{var ge,Ee;const oe=((ge=o(s))==null?void 0:ge.getTotalLength())??0;return(Ee=o(s))==null?void 0:Ee.getPointAtLength(oe*$)}}}:{type:"none"});Ir(()=>{if(!e.startContent&&!e.endContent||(o(I),!o(s)||!o(s).getTotalLength()))return;Re(R,o(s).getPointAtLength(0),!0);const $=o(s).getTotalLength();D.target=o(s).getPointAtLength($)}),Ir(()=>{e.draw&&(e.pathData,e.data,r.data,Re(C,Symbol(),!0))});var W=Ce(),B=ce(W);{var H=$=>{var oe=Ce(),ye=ce(oe);Il(ye,()=>o(C),ge=>{var Ee=$S(),xe=ce(Ee);wt(xe,G=>({d:T.current,...a,class:G,fill:e.fill,"fill-opacity":e.fillOpacity,stroke:e.stroke,"stroke-width":e.strokeWidth,opacity:e.opacity,"marker-start":o(f)?`url(#${o(f)})`:void 0,"marker-mid":o(h)?`url(#${o(h)})`:void 0,"marker-end":o(d)?`url(#${o(d)})`:void 0}),[()=>Le(Ne("spline-path"),!e.fill&&"fill-none",!e.stroke&&"stroke-surface-content",e.class)]),Wt(xe,G=>Re(s,G),()=>o(s));var te=Fe(xe);oi(te,{get id(){return o(f)},get marker(){return o(l)}});var ke=Fe(te);oi(ke,{get id(){return o(h)},get marker(){return o(u)}});var Se=Fe(ke);oi(Se,{get id(){return o(d)},get marker(){return o(c)}});var we=Fe(Se);{var se=G=>{const be=x(()=>Ne("spline-g-start"));kr(G,{get x(){return o(R).x},get y(){return o(R).y},get class(){return o(be)},children:(_e,ae)=>{var J=Ce(),de=ce(J);Xe(de,()=>e.startContent,()=>({point:o(R)})),Q(_e,J)},$$slots:{default:!0}})};ue(we,G=>{e.startContent&&o(R)&&G(se)})}var V=Fe(we);{var K=G=>{const be=x(()=>Ne("spline-g-end"));kr(G,{get x(){return D.current.x},get y(){return D.current.y},get class(){return o(be)},children:(_e,ae)=>{var J=Ce(),de=ce(J);Xe(de,()=>e.endContent,()=>({point:D.current})),Q(_e,J)},$$slots:{default:!0}})};ue(V,G=>{e.endContent&&D.current&&G(K)})}js(5,xe,()=>o(L),()=>typeof e.draw=="object"?e.draw:void 0),Q(ge,Ee)}),Q($,oe)};ue(B,$=>{_==="svg"&&$(H)})}Q(t,W),at()}function Kf(t){const e=t.match(/(^.+?)(L|Z)/);return!e||!e[1]?t:e[1]}function N0(t){return(t%360+360)%360}function e6(t){const e=x(()=>(t.innerRadius()+t.outerRadius())/2),n=x(()=>t.cornerRadius()<=0||o(e)<=0?0:Math.min(t.cornerRadius(),o(e))*.5/o(e)),r=x(()=>t.invertCorner()?t.startAngle()-o(n):t.startAngle()+o(n)),i=x(()=>t.invertCorner()?t.endAngle()+o(n):t.endAngle()-o(n)),a=x(()=>Kf(Eo().outerRadius(o(e)).innerRadius(o(e)-.5).startAngle(o(r)).endAngle(o(i))()??""));return{get current(){return o(a)}}}function t6(t){const e=x(()=>t.cornerRadius()<=0||t.innerRadius()<=0?0:t.cornerRadius()>=t.innerRadius()?Math.PI/4:t.cornerRadius()*.5/t.innerRadius()),n=x(()=>t.invertCorner()?t.startAngle()-o(e):t.startAngle()+o(e)),r=x(()=>t.invertCorner()?t.endAngle()+o(e):t.endAngle()-o(e)),i=x(()=>Kf(Eo().innerRadius(t.innerRadius()).outerRadius(t.innerRadius()+.5).startAngle(o(n)).endAngle(o(r))()??""));return{get current(){return o(i)}}}function n6(t){const e=x(()=>t.cornerRadius()<=0||t.outerRadius()<=0?0:t.cornerRadius()*.5/t.outerRadius()),n=x(()=>t.invertCorner()?t.startAngle()-o(e):t.startAngle()+o(e)),r=x(()=>t.invertCorner()?t.endAngle()+o(e):t.endAngle()-o(e)),i=x(()=>Kf(Eo().innerRadius(t.outerRadius()-.5).outerRadius(t.outerRadius()).startAngle(o(n)).endAngle(o(r))()??""));return{get current(){return o(i)}}}function r6(t,e){const n=e-Math.PI/2;return[t*Math.cos(n),t*Math.sin(n)]}function L0(t,e={},n){const r=x(()=>{const L=t.startAngle(),C=t.endAngle(),_=e.startOffset;if(_)try{const M=parseFloat(_.slice(0,-1))/100;if(!isNaN(M)&&M>=0&&M<=1){const k=C-L;return L+k*M}else console.warn("Invalid percentage for startOffset:",_)}catch(M){console.warn("Could not parse startOffset percentage:",_,M)}return L}),i=x(()=>Ps(o(r))),a=x(()=>N0(o(i))),s=x(()=>Ps(t.startAngle())),l=x(()=>Ps(t.endAngle())),u=x(()=>o(s)<o(l)),c=x(()=>o(u)&&(o(a)>=270||o(a)<=90)),f=x(()=>!o(u)&&(o(a)>270||o(a)<=90)),h=x(()=>o(u)&&o(a)<270&&o(a)>=90),d=x(()=>!o(u)&&o(a)<=270&&o(a)>90),g=x(()=>o(f)||o(h)),m={...t,startAngle:()=>o(g)?t.endAngle():t.startAngle(),endAngle:()=>o(g)?t.startAngle():t.endAngle(),invertCorner:()=>o(h)||o(d)},v=t6(m),w=e6(m),b=n6(m),S=x(()=>o(h)||o(d)?"auto":o(c)||o(f)?"hanging":"auto"),A=x(()=>{if(o(h)||o(d))return"hanging"}),N=x(()=>o(g)?{startOffset:e.startOffset??"100%",textAnchor:"end"}:{startOffset:e.startOffset??void 0}),I=x(()=>{if(n!=="outer-radial")return{};const L=(t.startAngle()+t.endAngle())/2,C=e.radialOffset??e.outerPadding??23,_=N0(Ps(L));let M="middle",k=C;const p=_>45&&_<135,R=_>225&&_<315,z=_<=45||_>=315,D=_>=135&&_<=225,W=t.outerRadius()+k,[B,H]=r6(W,L);return z?(M="start",(_>350||_<10)&&(M="start")):D?(M="end",_>170&&_<190&&(M="end")):(p||R)&&(M="middle"),{x:B,y:H,textAnchor:M,dominantBaseline:"middle"}}),T=x(()=>{if(n==="inner")return{path:v.current,...o(N),dominantBaseline:o(S)};if(n==="outer")return{path:b.current,...o(N),dominantBaseline:o(A)};if(n==="middle")return{path:w.current,...o(N),dominantBaseline:"middle"};if(n==="centroid"){const L=t.centroid();return{x:L[0],y:L[1],textAnchor:"middle",verticalAnchor:"middle"}}else return o(I)});return{get current(){return o(T)}}}var i6=rt("<!> <!> <!>",1);function Qf(t,e){it(e,!0);let n=P(e,"ref",15),r=P(e,"trackRef",15),i=P(e,"value",3,0),a=P(e,"initialValue",3,0),s=P(e,"domain",19,()=>[0,100]),l=P(e,"range",19,()=>[0,360]),u=P(e,"cornerRadius",3,0),c=P(e,"padAngle",3,0),f=P(e,"stroke",3,"none"),h=P(e,"offset",3,0),d=P(e,"onpointerenter",3,()=>{}),g=P(e,"onpointermove",3,()=>{}),m=P(e,"onpointerleave",3,()=>{}),v=P(e,"ontouchmove",3,()=>{}),w=P(e,"track",3,!1),b=bt(e,["$$slots","$$events","$$legacy","ref","trackRef","motion","value","initialValue","domain","range","startAngle","endAngle","innerRadius","outerRadius","cornerRadius","padAngle","trackStartAngle","trackEndAngle","trackInnerRadius","trackOuterRadius","trackCornerRadius","trackPadAngle","fill","fillOpacity","stroke","strokeWidth","opacity","data","offset","onpointerenter","onpointermove","onpointerleave","ontouchmove","tooltipContext","track","children","class"]),S=Ve(void 0),A=Ve(void 0);vt(()=>{n(o(S))}),vt(()=>{r(o(A))});const N=Gt(),I=x(()=>e.endAngle??xa(N.config.xRange?Xt(N.xRange):Xt(l()))),T=on(a(),()=>i(),e.motion),L=x(()=>Ni().domain(s()).range(l()));function C(Z,ne){return Z?Z>1?Z:Z>0?ne*Z:Z<0?ne+Z:Z:ne}const _=x(()=>C(e.outerRadius,(Math.min(N.xRange[1],N.yRange[0])??0)/2)),M=x(()=>e.trackOuterRadius?C(e.trackOuterRadius,(Math.min(N.xRange[1],N.yRange[0])??0)/2):o(_));function k(Z,ne){return Z==null?Math.min(...N.yRange):Z>1?Z:Z>0?ne*Z:Z<0?ne+Z:Z}const p=x(()=>k(e.innerRadius,o(_))),R=x(()=>e.trackInnerRadius?k(e.trackInnerRadius,o(M)):o(p)),z=x(()=>e.startAngle??xa(l()[0])),D=x(()=>e.trackStartAngle??e.startAngle??xa(l()[0])),W=x(()=>e.trackEndAngle??e.endAngle??xa(l()[1])),B=x(()=>e.trackCornerRadius??u()),H=x(()=>e.trackPadAngle??c()),$=x(()=>e.endAngle??xa(o(L)(T.current))),oe=x(()=>Eo().innerRadius(o(p)).outerRadius(o(_)).startAngle(o(z)).endAngle(o($)).cornerRadius(u()).padAngle(c())),ye=x(()=>Eo().innerRadius(o(R)).outerRadius(o(M)).startAngle(o(D)).endAngle(o(W)).cornerRadius(o(B)).padAngle(o(H))),ge=x(()=>((o(z)??0)+(o(I)??0))/2),Ee=x(()=>Math.sin(o(ge))*h()),xe=x(()=>-Math.cos(o(ge))*h()),te=x(()=>{const Z=o(ye).centroid();return[Z[0]+o(Ee),Z[1]+o(xe)]}),ke=x(()=>o(A)?o(A).getBBox():{}),Se=Z=>{var ne,he;(ne=d())==null||ne(Z),(he=e.tooltipContext)==null||he.show(Z,e.data)},we=Z=>{var ne,he;(ne=g())==null||ne(Z),(he=e.tooltipContext)==null||he.show(Z,e.data)},se=Z=>{var ne,he;(ne=m())==null||ne(Z),(he=e.tooltipContext)==null||he.hide()};function V(Z,ne={}){return L0({startAngle:()=>o(D),endAngle:()=>o(W),outerRadius:()=>o(M)+(ne.outerPadding?ne.outerPadding:0),innerRadius:()=>o(R),cornerRadius:()=>o(B),centroid:()=>o(te)},ne,Z).current}function K(Z,ne={}){return L0({startAngle:()=>o(z),endAngle:()=>o($),outerRadius:()=>o(_)+(ne.outerPadding?ne.outerPadding:0),innerRadius:()=>o(p),cornerRadius:()=>u(),centroid:()=>o(te)},ne,Z).current}var G=i6(),be=ce(G);{var _e=Z=>{const ne=x(()=>o(ye)());var he=x(()=>Jt(w(),"arc-track"));Va(Z,qe({get pathData(){return o(ne)},stroke:"none"},()=>o(he),{get splineRef(){return o(A)},set splineRef(Te){Re(A,Te,!0)}}))};ue(be,Z=>{w()&&Z(_e)})}var ae=Fe(be,2);const J=x(()=>o(oe)()),de=x(()=>Le(Ne("arc-line"),e.class));Va(ae,qe({get pathData(){return o(J)},get transform(){return`translate(${o(Ee)??""}, ${o(xe)??""})`},get fill(){return e.fill},get fillOpacity(){return e.fillOpacity},get stroke(){return f()},get"stroke-width"(){return e.strokeWidth},get opacity(){return e.opacity}},()=>b,{get class(){return o(de)},onpointerenter:Se,onpointermove:we,onpointerleave:se,ontouchmove:Z=>{var ne;(ne=v())==null||ne(Z),e.tooltipContext&&Z.preventDefault()},get splineRef(){return o(S)},set splineRef(Z){Re(S,Z,!0)}}));var De=Fe(ae,2);Xe(De,()=>e.children??Rt,()=>({centroid:o(te),boundingBox:o(ke),value:T.current,getTrackTextProps:V,getArcTextProps:K})),Q(t,G),at()}var a6=rt("<div><!></div>");function o6(t,e){it(e,!0);let n=P(e,"ref",15),r=P(e,"zIndex",3,0),i=P(e,"pointerEvents",3,!0),a=P(e,"center",3,!1),s=P(e,"ignoreTransform",3,!1),l=bt(e,["$$slots","$$events","$$legacy","ref","zIndex","pointerEvents","role","aria-label","aria-labelledby","aria-describedby","center","ignoreTransform","class","children"]),u=Ve(void 0);vt(()=>{n(o(u))});const c=x(()=>e.role||(e["aria-label"]||e["aria-labelledby"]||e["aria-describedby"]?"figure":void 0)),f=Gt(),h=nu(),d=x(()=>{if(h.mode==="canvas"&&!s())return`translate(${h.translate.x}px,${h.translate.y}px) scale(${h.scale})`;if(a())return`translate(${a()==="x"||a()===!0?f.width/2:0}px, ${a()==="y"||a()===!0?f.height/2:0}px)`});Zf("html");var g=a6();wt(g,v=>({class:v,role:o(c),"aria-label":e["aria-label"],"aria-labelledby":e["aria-labelledby"],"aria-describedby":e["aria-describedby"],...l,[ir]:{transform:o(d),"transform-origin":"top left","z-index":r(),"pointer-events":i()===!1?"none":null,top:`${f.padding.top??""}px`,bottom:`${f.padding.bottom??""}px`,left:`${f.padding.left??""}px`,right:`${f.padding.right??""}px`}}),[()=>Le(Ne("layout-html"),"absolute top-0 left-0",i()===!1&&"pointer-events-none",e.class)]);var m=nt(g);Xe(m,()=>e.children??Rt,()=>({ref:o(u)})),$e(g),Wt(g,v=>Re(u,v),()=>o(u)),Q(t,g),at()}function s6(t,e){let n=bt(e,["$$slots","$$events","$$legacy","type","children"]);var r=Ce(),i=ce(r);{var a=l=>{yp(l,qe(()=>n,{children:(u,c)=>{var f=Ce(),h=ce(f);Xe(h,()=>e.children??Rt),Q(u,f)},$$slots:{default:!0}}))},s=(l,u)=>{{var c=h=>{Ls(h,qe(()=>n,{children:(d,g)=>{var m=Ce(),v=ce(m);Xe(v,()=>e.children??Rt),Q(d,m)},$$slots:{default:!0}}))},f=(h,d)=>{{var g=m=>{o6(m,qe(()=>n,{children:(v,w)=>{var b=Ce(),S=ce(b);Xe(S,()=>e.children??Rt),Q(v,b)},$$slots:{default:!0}}))};ue(h,m=>{e.type==="html"&&m(g)},d)}};ue(l,h=>{e.type==="svg"?h(c):h(f,!1)},u)}};ue(i,l=>{e.type==="canvas"?l(a):l(s,!1)})}Q(t,r)}var l6=xt("<image></image>");function u6(t,e){it(e,!0);let n=P(e,"steps",3,10),r=P(e,"height",3,"20px"),i=P(e,"width",3,"100%"),a=P(e,"ref",15),s=bt(e,["$$slots","$$events","$$legacy","interpolator","steps","height","width","ref"]),l=Ve(void 0);vt(()=>{a(o(l))});let u=Ve("");Ir(()=>{const f=document.createElement("canvas");f.width=n(),f.height=1;const h=f.getContext("2d");for(let d=0;d<n();++d)e.interpolator&&(h.fillStyle=e.interpolator(d/(n()-1))),h.fillRect(d,0,1,1);Re(u,f.toDataURL(),!0)});var c=l6();wt(c,f=>({href:o(u),preserveAspectRatio:"none",height:r(),width:i(),...f}),[()=>Jt(s,"color-ramp")]),Wt(c,f=>Re(l,f),()=>o(l)),Q(t,c),at()}var c6=xt("<rect></rect>"),f6=xt("<line></line>"),h6=xt('<text text-anchor="middle"> </text><!>',1),d6=xt("<svg><g><!></g><g></g></svg>"),g6=(t,e,n)=>{var r;return(r=e.onclick)==null?void 0:r.call(e,t,o(n))},m6=rt("<button><div></div> <div> </div></button>"),y6=rt("<div></div>"),v6=rt("<div><div> </div> <!></div>");function _6(t,e){it(e,!0);let n=P(e,"title",3,""),r=P(e,"width",3,320),i=P(e,"height",3,10),a=P(e,"ticks",19,()=>r()/64),s=P(e,"tickFontSize",3,10),l=P(e,"tickLength",3,4),u=P(e,"orientation",3,"horizontal"),c=P(e,"variant",3,"ramp"),f=P(e,"classes",19,()=>({})),h=P(e,"ref",15),d=bt(e,["$$slots","$$events","$$legacy","scale","title","width","height","ticks","tickFormat","tickValues","tickFontSize","tickLength","placement","orientation","onclick","onpointerenter","onpointerleave","variant","classes","ref","class","children"]),g=Ve(void 0);vt(()=>{h(o(g))});const m=Gt(),v=x(()=>e.scale??m.cScale),w=x(()=>{var L,C,_,M;if(o(v))if(o(v).interpolate){const k=Math.min(o(v).domain().length,o(v).range().length),p=(C=(L=o(v).copy()).rangeRound)==null?void 0:C.call(L,wd(Wo(0,r()),k)),R=o(v).copy().domain(wd(Wo(0,1),k)),z=e.tickFormat??((_=p==null?void 0:p.tickFormat)==null?void 0:_.call(p));return{xScale:p,interpolator:R,tickFormat:z,tickLabelOffset:0,tickLine:!0,tickValues:e.tickValues,tickLength:l(),swatches:void 0}}else if(o(v).interpolator){const k=Object.assign(o(v).copy().interpolator(L1(0,r())),{range(){return[0,r()]}}),p=o(v).interpolator();let R=e.tickValues;if(!k.ticks&&R===void 0){const D=Math.round(a()+1);R=Co(D).map(W=>cv(o(v).domain(),W/(D-1)))}const z=e.tickFormat??((M=k.tickFormat)==null?void 0:M.call(k));return{interpolator:p,tickValues:R,tickFormat:z,swatches:void 0,tickLabelOffset:0,tickLine:!0,tickLength:l(),xScale:k}}else if(o(v).invertExtent){const k=o(v).thresholds?o(v).thresholds():o(v).quantiles?o(v).quantiles():o(v).domain(),p=Ni().domain([-1,o(v).range().length-1]).rangeRound([0,r()]),R=o(v).range().map((W,B)=>({x:p(B-1),y:0,width:p(B)-p(B-1),height:i(),fill:W})),z=Co(k.length);return{xScale:p,swatches:R,tickValues:z,tickFormat:W=>{const B=k[W];return e.tickFormat?jt(B,e.tickFormat):B},tickLabelOffset:0,tickLine:!0,tickLength:l(),interpolator:void 0}}else{const k=wa().domain(o(v).domain()).rangeRound([0,r()]),p=o(v).domain().map(B=>({x:k(B),y:0,width:Math.max(0,k.bandwidth()-1),height:i(),fill:o(v)(B)})),R=o(v).domain(),z=k.bandwidth()/2;return{xScale:k,tickFormat:e.tickFormat,tickLabelOffset:z,tickLine:!1,tickLength:0,tickValues:R,swatches:p,interpolator:void 0}}else return{xScale:void 0,interpolator:void 0,swatches:void 0,tickLabelOffset:0,tickLine:!0,tickLength:l(),tickFormat:e.tickFormat,tickValues:e.tickValues}});var b=v6();wt(b,L=>({...d,"data-placement":e.placement,class:L}),[()=>Le(Ne("legend-container"),"inline-block","z-1",e.placement&&["absolute",{"top-left":"top-0 left-0",top:"top-0 left-1/2 -translate-x-1/2","top-right":"top-0 right-0",left:"top-1/2 left-0 -translate-y-1/2",center:"top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",right:"top-1/2 right-0 -translate-y-1/2","bottom-left":"bottom-0 left-0",bottom:"bottom-0 left-1/2 -translate-x-1/2","bottom-right":"bottom-0 right-0"}[e.placement]],e.class,f().root)]);var S=nt(b),A=nt(S,!0);$e(S);var N=Fe(S,2);{var I=L=>{var C=Ce(),_=ce(C),M=hf(()=>{var k,p;return{values:o(w).tickValues??((p=(k=o(w).xScale)==null?void 0:k.ticks)==null?void 0:p.call(k,a()))??[],scale:o(v)}});Xe(_,()=>e.children,()=>o(M)),Q(L,C)},T=(L,C)=>{{var _=k=>{var p=d6(),R=nt(p),z=nt(R);{var D=H=>{const $=x(()=>Ne("legend-color-ramp"));u6(H,{get width(){return r()},get height(){return i()},get interpolator(){return o(w).interpolator},get class(){return o($)}})},W=(H,$)=>{{var oe=ye=>{var ge=Ce(),Ee=ce(ge);$t(Ee,17,()=>o(w).swatches,Pn,(xe,te)=>{var ke=c6();wt(ke,Se=>({...Se}),[()=>Jt(o(te),"legend-swatch")]),Q(xe,ke)}),Q(ye,ge)};ue(H,ye=>{o(w).swatches&&ye(oe)},$)}};ue(z,H=>{o(w).interpolator?H(D):H(W,!1)})}$e(R);var B=Fe(R);$t(B,21,()=>{var H,$;return e.tickValues??(($=(H=o(w).xScale)==null?void 0:H.ticks)==null?void 0:$.call(H,a()))??[]},Pn,(H,$)=>{var oe=h6(),ye=ce(oe);let ge;var Ee=nt(ye,!0);$e(ye);var xe=Fe(ye);{var te=ke=>{var Se=f6();ft(Se,"y1",0),mt((we,se,V)=>{ft(Se,"x1",we),ft(Se,"x2",se),ft(Se,"y2",i()+l()),St(Se,0,V)},[()=>{var we,se;return(se=(we=o(w)).xScale)==null?void 0:se.call(we,o($))},()=>{var we,se;return(se=(we=o(w)).xScale)==null?void 0:se.call(we,o($))},()=>pt(Le(Ne("legend-tick-line"),"stroke-surface-content",f().tick))]),Q(ke,Se)};ue(xe,ke=>{o(w).tickLine&&ke(te)})}mt((ke,Se,we)=>{ft(ye,"x",ke),ft(ye,"y",i()+l()+s()),St(ye,0,Se),ge=wr(ye,"",ge,{"font-size":s()}),Jn(Ee,we)},[()=>{var ke,Se;return((Se=(ke=o(w)).xScale)==null?void 0:Se.call(ke,o($)))+o(w).tickLabelOffset},()=>pt(Le(Ne("legend-tick-text"),"text-[10px] fill-surface-content",f().label)),()=>e.tickFormat?jt(o($),e.tickFormat):o($)]),Q(H,oe)}),$e(B),$e(p),mt((H,$,oe)=>{ft(p,"width",r()),ft(p,"height",i()+l()+s()),ft(p,"viewBox",`0 0 ${r()??""} ${i()+l()+s()}`),St(p,0,H),St(R,0,$),St(B,0,oe)},[()=>pt(Le(Ne("legend-ramp-svg"),"overflow-visible")),()=>pt(Ne("legend-ramp-g")),()=>pt(Ne("legend-tick-group"))]),Q(k,p)},M=(k,p)=>{{var R=z=>{var D=y6();$t(D,21,()=>{var W,B;return o(w).tickValues??((B=(W=o(w).xScale)==null?void 0:W.ticks)==null?void 0:B.call(W,a()))??[]},Pn,(W,B)=>{var H=m6();const $=x(()=>{var te;return((te=o(v))==null?void 0:te(o(B)))??""}),oe=x(()=>({value:o(B),color:o($)}));H.__click=[g6,e,oe];var ye=nt(H);let ge;var Ee=Fe(ye,2),xe=nt(Ee,!0);$e(Ee),$e(H),mt((te,ke,Se,we)=>{St(H,1,te),St(ye,1,ke),ge=wr(ye,"",ge,{"background-color":o($)}),St(Ee,1,Se),Jn(xe,we)},[()=>{var te,ke;return pt(Le(Ne("legend-swatch-button"),"flex gap-1",!e.onclick&&"cursor-auto",(ke=(te=f()).item)==null?void 0:ke.call(te,o(oe))))},()=>pt(Le(Ne("legend-swatch"),"h-4 w-4 rounded-full",f().swatch)),()=>pt(Le(Ne("legend-swatch-label"),"text-xs text-surface-content whitespace-nowrap",f().label)),()=>e.tickFormat?jt(o(B),e.tickFormat):o(B)]),ba("pointerenter",H,te=>{var ke;return(ke=e.onpointerenter)==null?void 0:ke.call(e,te,o(oe))}),ba("pointerleave",H,te=>{var ke;return(ke=e.onpointerleave)==null?void 0:ke.call(e,te,o(oe))}),Q(W,H)}),$e(D),mt(W=>St(D,1,W),[()=>pt(Le(Ne("legend-swatch-group"),"flex gap-x-4 gap-y-1",u()==="vertical"&&"flex-col",f().swatches))]),Q(z,D)};ue(k,z=>{c()==="swatches"&&z(R)},p)}};ue(L,k=>{c()==="ramp"?k(_):k(M,!1)},C)}};ue(N,L=>{e.children?L(I):L(T,!1)})}$e(b),Wt(b,L=>Re(g,L),()=>o(g)),mt(L=>{St(S,1,L),Jn(A,n())},[()=>pt(Le(Ne("legend-title"),"text-[10px] font-semibold",f().title))]),Q(t,b),at()}mf(["click"]);var x6=rt("<div></div>"),b6=rt("<div><!> <!></div>");function k6(t,e){it(e,!0);let n=P(e,"ref",15),r=P(e,"colorRef",15),i=P(e,"classes",19,()=>({root:"",color:""})),a=P(e,"props",19,()=>({root:{},color:{}})),s=bt(e,["$$slots","$$events","$$legacy","ref","colorRef","value","format","color","classes","props","class","children"]),l=Ve(void 0),u=Ve(void 0);vt(()=>{n(o(l))}),vt(()=>{r(o(u))});var c=b6();wt(c,v=>({class:v,...s}),[()=>{var v;return Le(Ne("tooltip-header"),"font-semibold whitespace-nowrap border-b mb-1 pb-1 flex items-center gap-2",i().root,(v=a().root)==null?void 0:v.class,e.class)}]);var f=nt(c);{var h=v=>{var w=x6();let b;Wt(w,S=>Re(u,S),()=>o(u)),mt(S=>{St(w,1,S),b=wr(w,"",b,{"--color":e.color})},[()=>pt(Le(Ne("tooltip-header-color"),"color","inline-block size-2 rounded-full bg-[var(--color)]",i().color))]),Q(v,w)};ue(f,v=>{e.color&&v(h)})}var d=Fe(f,2);{var g=v=>{var w=Ce(),b=ce(w);Xe(b,()=>e.children??Rt),Q(v,w)},m=v=>{var w=So();mt(b=>Jn(w,b),[()=>e.format?jt(e.value,e.format):e.value]),Q(v,w)};ue(d,v=>{e.children?v(g):v(m,!1)})}$e(c),Wt(c,v=>Re(l,v),()=>o(l)),Q(t,c),at()}var w6=rt("<div></div>"),M6=rt("<div><div><!> <!></div> <div><!></div></div>");function W0(t,e){it(e,!0);let n=P(e,"ref",15),r=P(e,"labelRef",15),i=P(e,"valueRef",15),a=P(e,"colorRef",15),s=P(e,"valueAlign",3,"left"),l=P(e,"classes",19,()=>({root:"",label:"",value:"",color:""})),u=P(e,"props",19,()=>({root:{},label:{},value:{},color:{}})),c=bt(e,["$$slots","$$events","$$legacy","ref","labelRef","valueRef","colorRef","label","value","format","valueAlign","color","classes","props","class","children"]),f=Ve(void 0),h=Ve(void 0),d=Ve(void 0),g=Ve(void 0);vt(()=>{n(o(f))}),vt(()=>{r(o(h))}),vt(()=>{i(o(d))}),vt(()=>{a(o(g))});var m=M6();wt(m,_=>({...u().root,class:_,...c}),[()=>{var _;return Le(Ne("tooltip-item-root"),"contents",l().root,e.class,(_=u().root)==null?void 0:_.class)}]);var v=nt(m);wt(v,_=>({...u().label,class:_}),[()=>{var _;return Le(Ne("tooltip-item-label"),"label","flex items-center gap-2 whitespace-nowrap",l().label,(_=u().label)==null?void 0:_.class)}]);var w=nt(v);{var b=_=>{var M=w6();wt(M,k=>({...u().color,class:k,[ir]:{"--color":e.color}}),[()=>{var k;return Le(Ne("tooltip-item-color"),"color","inline-block size-2 rounded-full bg-[var(--color)]",l().color,(k=u().color)==null?void 0:k.class)}]),Wt(M,k=>Re(g,k),()=>o(g)),Q(_,M)};ue(w,_=>{e.color&&_(b)})}var S=Fe(w,2);{var A=_=>{var M=Ce(),k=ce(M);Xe(k,()=>e.label),Q(_,M)},N=_=>{var M=So();mt(()=>Jn(M,e.label)),Q(_,M)};ue(S,_=>{typeof e.label=="function"?_(A):_(N,!1)})}$e(v),Wt(v,_=>Re(h,_),()=>o(h));var I=Fe(v,2);wt(I,_=>({...u().value,class:_}),[()=>{var _;return Le(Ne("tooltip-item-value"),"value","tabular-nums",{"text-right":s()==="right","text-center":s()==="center"},l().value,(_=u().value)==null?void 0:_.class)}]);var T=nt(I);{var L=_=>{var M=Ce(),k=ce(M);Xe(k,()=>e.children),Q(_,M)},C=_=>{var M=So();mt(k=>Jn(M,k),[()=>e.format?jt(e.value,e.format):e.value]),Q(_,M)};ue(T,_=>{e.children?_(L):_(C,!1)})}$e(I),Wt(I,_=>Re(d,_),()=>o(d)),$e(m),Wt(m,_=>Re(f,_),()=>o(f)),Q(t,m),at()}var p6=rt("<div><!></div>");function S6(t,e){it(e,!0);let n=P(e,"ref",15),r=bt(e,["$$slots","$$events","$$legacy","ref","class","children"]),i=Ve(void 0);vt(()=>{n(o(i))});var a=p6();wt(a,l=>({class:l,...r}),[()=>Le(Ne("tooltip-list"),"grid grid-cols-[1fr_auto] gap-x-2 gap-y-1 items-center",e.class)]);var s=nt(a);Xe(s,()=>e.children??Rt),$e(a),Wt(a,l=>Re(i,l),()=>o(i)),Q(t,a),at()}var A6=rt("<div><!></div>");function T6(t,e){it(e,!0);let n=P(e,"ref",15),r=bt(e,["$$slots","$$events","$$legacy","ref","class","children"]),i=Ve(void 0);vt(()=>{n(o(i))});var a=A6();wt(a,l=>({class:l,...r}),[()=>Le(Ne("tooltip-separator"),"rounded-sm bg-surface-content/20 my-1 col-span-full h-px",e.class)]);var s=nt(a);Xe(s,()=>e.children??Rt),$e(a),Wt(a,l=>Re(i,l),()=>o(i)),Q(t,a),at()}var C6=rt("<div><!></div>"),D6=rt("<div><div><!></div></div>");function Wm(t,e){it(e,!0);let n=P(e,"anchor",3,"top-left"),r=P(e,"classes",19,()=>({})),i=P(e,"contained",3,"container"),a=P(e,"motion",3,"spring"),s=P(e,"pointerEvents",3,!1),l=P(e,"variant",3,"default"),u=P(e,"x",3,"pointer"),c=P(e,"xOffset",19,()=>u()==="pointer"?10:0),f=P(e,"y",3,"pointer"),h=P(e,"yOffset",19,()=>f()==="pointer"?10:0),d=P(e,"rootRef",15),g=P(e,"props",19,()=>({root:{},container:{},content:{}})),m=Ve(void 0);vt(()=>{d(o(m))});const v=Gt(),w=Vf();let b=Ve(0),S=Ve(0);function A(M,k,p,R){const z=k==="center"?R/2:k==="end"?R:0;return M+(k==="end"?-p:p)-z}const N=x(()=>{var B;if(!w.data){const H=Ao(()=>w.x),$=Ao(()=>w.y);return{x:H,y:$}}const M=ht(v.xScale)?v.xScale.step()/2-v.xScale.padding()*v.xScale.step()/2:0,k=typeof u()=="number"?u():u()==="data"?v.xGet(w.data)+v.padding.left+M:w.x;let p="start";switch(n()){case"top-left":case"left":case"bottom-left":p="start";break;case"top":case"center":case"bottom":p="center";break;case"top-right":case"right":case"bottom-right":p="end";break}const R=ht(v.yScale)?v.yScale.step()/2-v.yScale.padding()*v.yScale.step()/2:0,z=typeof f()=="number"?f():f()==="data"?v.yGet(w.data)+v.padding.top+R:w.y;let D="start";switch(n()){case"top-left":case"top":case"top-right":D="start";break;case"left":case"center":case"right":D="center";break;case"bottom-left":case"bottom":case"bottom-right":D="end";break}const W={top:A(z,D,h(),o(S)),left:A(k,p,c(),o(b)),bottom:0,right:0};if(W.bottom=W.top+o(S),W.right=W.left+o(b),i()==="container")typeof u()!="number"&&((p==="start"||p==="center")&&W.right>v.containerWidth&&(W.left=A(k,"end",c(),o(b))),(p==="end"||p==="center")&&W.left<v.padding.left&&(W.left=A(k,"start",c(),o(b)))),W.right=W.left+o(b),typeof f()!="number"&&((D==="start"||D==="center")&&W.bottom>v.containerHeight&&(W.top=A(z,"end",h(),o(S))),(D==="end"||D==="center")&&W.top<v.padding.top&&(W.top=A(z,"start",h(),o(S)))),W.bottom=W.top+o(S);else if(i()==="window"&&(B=o(m))!=null&&B.parentElement){const H=o(m).parentElement.getBoundingClientRect();typeof u()!="number"&&((p==="start"||p==="center")&&H.left+W.right>window.innerWidth&&(W.left=A(k,"end",c(),o(b))),(p==="end"||p==="center")&&H.left+W.left<0&&(W.left=A(k,"start",c(),o(b)))),W.right=W.left+o(b),typeof f()!="number"&&((D==="start"||D==="center")&&H.top+W.bottom>window.innerHeight&&(W.top=A(z,"end",h(),o(S))),(D==="end"||D==="center")&&H.top+W.top<0&&(W.top=A(z,"start",h(),o(S)))),W.bottom=W.top+o(S)}return{x:W.left,y:W.top}}),I=on(w.x,()=>o(N).x,a()),T=on(w.y,()=>o(N).y,a());Ir(()=>{w.data||(w.isHoveringTooltipContent=!1)});var L=Ce(),C=ce(L);{var _=M=>{var k=D6(),p=()=>{w.isHoveringTooltipContent=!0},R=()=>{w.isHoveringTooltipContent=!1};wt(k,(B,H)=>({...g().root,class:B,onpointerenter:p,onpointerleave:R,[vy]:H,[ir]:{top:`${T.current??""}px`,left:`${I.current??""}px`}}),[()=>{var B;return Le("root",Ne("tooltip-root"),r().root,(B=g().root)==null?void 0:B.class)},()=>({"pointer-events-none":!s()})],"svelte-1cwff6a");var z=nt(k);wt(z,B=>({...g().container,class:B}),[()=>{var B;return Le(Ne("tooltip-container"),l()!=="none"&&["text-sm py-1 px-2 h-full rounded-sm elevation-1"],{default:["bg-surface-100/90 dark:bg-surface-300/90 backdrop-filter backdrop-blur-[2px] text-surface-content","[&_.label]:text-surface-content/75"],invert:["bg-surface-content/90 backdrop-filter backdrop-blur-[2px] text-surface-100 border border-surface-content","[&_.label]:text-surface-100/50"],none:""}[l()],r().container,(B=g().container)==null?void 0:B.class,e.class)}],"svelte-1cwff6a");var D=nt(z);{var W=B=>{var H=C6();wt(H,oe=>({...g().content,class:oe}),[()=>Le(Ne("tooltip-content"),r().content)],"svelte-1cwff6a");var $=nt(H);Xe($,()=>e.children,()=>({data:w.data,payload:w.payload})),$e(H),Q(B,H)};ue(D,B=>{e.children&&B(W)})}$e(z),$e(k),Wt(k,B=>Re(m,B),()=>o(m)),js(3,k,()=>gf,()=>({duration:100})),Fs(k,"clientWidth",B=>Re(b,B)),Fs(k,"clientHeight",B=>Re(S,B)),Q(M,k)};ue(C,M=>{w.data&&M(_)})}Q(t,L),at()}var Go;class P6{constructor(){gt(this,Go,Ve(null));dt(this,"set",e=>{this.current=e})}get current(){return o(ie(this,Go))}set current(e){Re(ie(this,Go),e,!0)}}Go=new WeakMap;var Dr;class R6{constructor(e){gt(this,Dr,Ve([]));dt(this,"selectedSeries",new ad);dt(this,"selectedKeys",new ad);dt(this,"highlightKey",new P6);Re(ie(this,Dr),e()),vt(()=>{Re(ie(this,Dr),e())})}get series(){return o(ie(this,Dr))}get isDefaultSeries(){return o(ie(this,Dr)).length===1&&o(ie(this,Dr))[0].key==="default"}get allSeriesData(){return o(ie(this,Dr)).flatMap(e=>{var n;return(n=e.data)==null?void 0:n.map(r=>({seriesKey:e.key,...r}))}).filter(e=>e)}get visibleSeries(){return o(ie(this,Dr)).filter(e=>this.selectedSeries.isEmpty()||this.selectedSeries.isSelected(e.key))}}Dr=new WeakMap;function E6(t){var e;return{scale:t.seriesState.isDefaultSeries?void 0:eu(t.seriesState.series.map(n=>n.key),t.seriesState.series.map(n=>n.color)),tickFormat:n=>{var r;return((r=t.seriesState.series.find(i=>i.key===n))==null?void 0:r.label)??n},placement:"bottom",variant:"swatches",onclick:(n,r)=>t.seriesState.selectedSeries.toggle(r.value),onpointerenter:(n,r)=>t.seriesState.highlightKey.current=r.value,onpointerleave:()=>t.seriesState.highlightKey.current=null,...t.props,classes:{item:n=>t.seriesState.visibleSeries.length&&!t.seriesState.visibleSeries.some(r=>r.key===n.value)?"opacity-50":"",...(e=t.props)==null?void 0:e.classes}}}var O6=xt("<line></line><!><!><!>",1);function si(t,e){const n=Mr();it(e,!0);let r=P(e,"initialX1",19,()=>e.x1),i=P(e,"initialY1",19,()=>e.y1),a=P(e,"initialX2",19,()=>e.x2),s=P(e,"initialY2",19,()=>e.y2),l=bt(e,["$$slots","$$events","$$legacy","x1","initialX1","y1","initialY1","x2","initialX2","y2","initialY2","class","strokeWidth","opacity","fill","stroke","marker","markerEnd","markerStart","markerMid","motion","fillOpacity"]);const u=x(()=>e.markerStart||e.marker?Rn("marker-start",n):""),c=x(()=>e.markerMid||e.marker?Rn("marker-mid",n):""),f=x(()=>e.markerEnd||e.marker?Rn("marker-end",n):""),h=on(r(),()=>e.x1,e.motion),d=on(i(),()=>e.y1,e.motion),g=on(a(),()=>e.x2,e.motion),m=on(s(),()=>e.y2,e.motion),v=Fr();function w(T,L){const C=`M ${h.current},${d.current} L ${g.current},${m.current}`;iu(T,C,L?to({styles:{strokeWidth:e.strokeWidth}},L):{styles:{fill:e.fill,stroke:e.stroke,strokeWidth:e.strokeWidth,opacity:e.opacity},classes:e.class})}const b=cr(()=>e.fill),S=cr(()=>e.stroke);v==="canvas"&&mi({name:"Line",render:w,events:{click:e.onclick,pointerenter:e.onpointerenter,pointermove:e.onpointermove,pointerleave:e.onpointerleave},deps:()=>[h.current,d.current,g.current,m.current,b.current,S.current,e.strokeWidth,e.opacity,e.class]});var A=Ce(),N=ce(A);{var I=T=>{var L=O6(),C=ce(L);wt(C,D=>({x1:h.current,y1:d.current,x2:g.current,y2:m.current,fill:e.fill,stroke:e.stroke,"fill-opacity":e.fillOpacity,"stroke-width":e.strokeWidth,opacity:e.opacity,"marker-start":o(u)?`url(#${o(u)})`:void 0,"marker-mid":o(c)?`url(#${o(c)})`:void 0,"marker-end":o(f)?`url(#${o(f)})`:void 0,class:D,...l}),[()=>Le(Ne("line"),e.stroke===void 0&&"stroke-surface-content",e.class)]);var _=Fe(C);const M=x(()=>e.markerStart??e.marker);oi(_,{get id(){return o(u)},get marker(){return o(M)}});var k=Fe(_);const p=x(()=>e.markerMid??e.marker);oi(k,{get id(){return o(c)},get marker(){return o(p)}});var R=Fe(k);const z=x(()=>e.markerEnd??e.marker);oi(R,{get id(){return o(f)},get marker(){return o(z)}}),Q(T,L)};ue(N,T=>{v==="svg"&&T(I)})}Q(t,A),at()}var N6=rt("<!> <!>",1);function Ri(t,e){it(e,!0);let n=P(e,"x",3,!1),r=P(e,"xOffset",3,0),i=P(e,"y",3,!1),a=P(e,"yOffset",3,0),s=bt(e,["$$slots","$$events","$$legacy","x","xOffset","y","yOffset","class","children"]);const l=Gt(),u=x(()=>ur(l.xRange)),c=x(()=>ur(l.yRange));function f(d,g){switch(typeof d){case"boolean":return d;case"string":return!0;default:return g==="x"?l.xScale(d)>=o(u)[0]&&l.xScale(d)<=o(u)[1]:l.yScale(d)>=o(c)[0]&&l.yScale(d)<=o(c)[1]}}const h=x(()=>Ne("rule-g"));kr(t,{get class(){return o(h)},children:(d,g)=>{var m=N6(),v=ce(m);{var w=A=>{var N=Ce();const I=x(()=>n()===!0||n()==="left"?o(u)[0]:n()==="right"?o(u)[1]:l.xScale(n())+r());var T=ce(N);{var L=_=>{const M=x(()=>{const[R,z]=br(o(I),Number(o(c)[0]));return{x1:R,y1:z}}),k=x(()=>{const[R,z]=br(o(I),Number(o(c)[1]));return{x2:R,y2:z}}),p=x(()=>Le(Ne("rule-x-radial-line"),"stroke-surface-content/10",e.class));si(_,qe(()=>s,{get x1(){return o(M).x1},get y1(){return o(M).y1},get x2(){return o(k).x2},get y2(){return o(k).y2},get class(){return o(p)}}))},C=_=>{const M=x(()=>l.yRange[0]||0),k=x(()=>l.yRange[1]||0),p=x(()=>Le(Ne("rule-x-line"),"stroke-surface-content/50",e.class));si(_,qe(()=>s,{get x1(){return o(I)},get x2(){return o(I)},get y1(){return o(M)},get y2(){return o(k)},get class(){return o(p)}}))};ue(T,_=>{l.radial?_(L):_(C,!1)})}Q(A,N)};ue(v,A=>{f(n(),"x")&&A(w)})}var b=Fe(v,2);{var S=A=>{var N=Ce(),I=ce(N);{var T=C=>{const _=x(()=>i()===!0||i()==="bottom"?o(c)[1]:i()==="top"?o(c)[0]:l.yScale(i())+a()),M=x(()=>Le(Ne("rule-y-radial-circle"),"fill-none stroke-surface-content/50",e.class));aa(C,{get r(){return o(_)},get class(){return o(M)}})},L=C=>{const _=x(()=>l.xRange[0]||0),M=x(()=>l.xRange[1]||0),k=x(()=>i()===!0||i()==="bottom"?o(c)[1]:i()==="top"?o(c)[0]:l.yScale(i())+a()),p=x(()=>i()===!0||i()==="bottom"?o(c)[1]:i()==="top"?o(c)[0]:l.yScale(i())+a()),R=x(()=>Le(Ne("rule-y-line"),"stroke-surface-content/50",e.class));si(C,qe(()=>s,{get x1(){return o(_)},get x2(){return o(M)},get y1(){return o(k)},get y2(){return o(p)},get class(){return o(R)}}))};ue(I,C=>{l.radial?C(T):C(L,!1)})}Q(A,N)};ue(b,A=>{f(i(),"y")&&A(S)})}Q(d,m)},$$slots:{default:!0}}),at()}function L6(t){if(t&&typeof t.getTotalLength=="function")try{return t.getTotalLength()}catch(e){return console.error("Error getting path length:",e),0}return 0}var W6=xt("<path></path>"),I6=xt("<defs><!></defs><text><textPath> </textPath></text>",1),z6=xt("<tspan> </tspan>"),j6=xt("<text></text>"),F6=xt("<svg><!></svg>");function Za(t,e){const n=Mr();it(e,!0);let r=P(e,"x",3,0),i=P(e,"initialX",19,r),a=P(e,"y",3,0),s=P(e,"initialY",19,a),l=P(e,"dx",3,0),u=P(e,"dy",3,0),c=P(e,"lineHeight",3,"1em"),f=P(e,"capHeight",3,"0.71em"),h=P(e,"scaleToFit",3,!1),d=P(e,"textAnchor",3,"start"),g=P(e,"verticalAnchor",3,"end"),m=P(e,"dominantBaseline",3,"auto"),v=P(e,"opacity",3,1),w=P(e,"strokeWidth",3,0),b=P(e,"svgRef",15),S=P(e,"ref",15),A=P(e,"svgProps",19,()=>({})),N=P(e,"truncate",3,!1),I=P(e,"pathId",19,()=>Rn("text-path",n)),T=P(e,"startOffset",3,"0%"),L=bt(e,["$$slots","$$events","$$legacy","value","x","initialX","y","initialY","dx","dy","lineHeight","capHeight","width","scaleToFit","textAnchor","verticalAnchor","dominantBaseline","rotate","opacity","strokeWidth","stroke","fill","fillOpacity","motion","svgRef","ref","class","svgProps","truncate","path","pathId","startOffset","transform"]);const C=Fr();let _=Ve(void 0),M=Ve(void 0),k=Ve(void 0);vt(()=>{S(o(_))}),vt(()=>{b(o(M))});let p;const R=x(()=>e.path?L6(o(k)):e.width),z=x(()=>({maxChars:void 0,position:"end",maxWidth:o(R)})),D=x(()=>typeof N()=="boolean"?N()?o(z):!1:{...o(z),...N()}),W=x(()=>e.value!=null?e.value.toString().replace(/\\n/g,`
`):""),B=x(()=>o(D)?$3(o(W),o(D)):o(W)),H=x(()=>mr(" ",p)||0),$=x(()=>o(B).split(`
`).flatMap(J=>J.split(/(?:(?!\u00A0+)\s+)/).reduce((De,Z)=>{const ne=De[De.length-1],he=mr(Z,p)||0;if(ne&&(e.width==null||h()||(ne.width||0)+he+o(H)<e.width))ne.words.push(Z),ne.width=ne.width||0,ne.width+=he+o(H);else{const Te={words:[Z],width:he};De.push(Te)}return De},[]))),oe=x(()=>o($).length);function ye(ae){if(typeof ae=="number")return ae;const J=ae.match(/([\d.]+)(\D+)/),de=Number(J==null?void 0:J[1]);switch(J==null?void 0:J[2]){case"px":return de;case"em":case"rem":return de*16;default:return 0}}const ge=x(()=>g()==="start"?ye(f()):g()==="middle"?(o(oe)-1)/2*-ye(c())+ye(f())/2:(o(oe)-1)*-ye(c())),Ee=x(()=>{if(h()&&o(oe)>0&&typeof r()=="number"&&typeof a()=="number"&&typeof e.width=="number"){const ae=o($)[0].width||1,J=e.width/ae,de=J,De=r()-J*r(),Z=a()-de*a();return`matrix(${J}, 0, 0, ${de}, ${De}, ${Z})`}else return""}),xe=x(()=>e.rotate?`rotate(${e.rotate}, ${r()}, ${a()})`:""),te=x(()=>e.transform??`${o(Ee)} ${o(xe)}`);function ke(ae){return typeof ae=="number"&&Number.isFinite(ae)||typeof ae=="string"}const Se=on(i(),()=>r(),e.motion),we=on(s(),()=>a(),e.motion);function se(ae,J){const de=ye(c()),De=ye(we.current)+ye(u())+ye(o(ge)),Z=ye(Se.current)+ye(l());if(ae.save(),e.rotate!==void 0){const ve=ye(r()),Ie=ye(a()),pe=xa(e.rotate);ae.translate(ve,Ie),ae.rotate(pe),ae.translate(-ve,-Ie)}const ne=J?to({styles:{strokeWidth:w()}},J):{styles:{fill:e.fill,fillOpacity:e.fillOpacity,stroke:e.stroke,strokeWidth:w(),opacity:v(),paintOrder:"stroke",textAnchor:d()},classes:Le(e.fill===void 0&&"fill-surface-content",e.class)},he=hl(ae.canvas,ne);ae.font=`${he.fontSize} ${he.fontFamily}`;const Te=d()==="middle"?"center":d()==="end"?"end":"start";ae.textAlign=Te;for(let ve=0;ve<o($).length;ve++){const pe=o($)[ve].words.join(" "),Ke=Z,Ge=De+ve*de;ap(ae,pe,{x:Ke,y:Ge},ne)}ae.restore()}const V=cr(()=>e.fill),K=cr(()=>e.stroke);C==="canvas"&&mi({name:"Text",render:se,deps:()=>[e.value,Se.current,we.current,V.current,K.current,w(),v(),e.class,o(D),e.rotate,c(),d(),g()]});var G=Ce(),be=ce(G);{var _e=ae=>{var J=F6();wt(J,ne=>({x:l(),y:u(),...A(),class:ne}),[()=>{var ne;return Le(Ne("text-svg"),"overflow-visible [paint-order:stroke]",(ne=A())==null?void 0:ne.class)}]);var de=nt(J);{var De=ne=>{var he=I6(),Te=ce(he),ve=nt(Te);Il(ve,()=>e.path,Ge=>{var ut=W6();Wt(ut,ot=>Re(k,ot),()=>o(k)),mt(()=>{ft(ut,"id",I()),ft(ut,"d",e.path)}),Q(Ge,ut)}),$e(Te);var Ie=Fe(Te);wt(Ie,Ge=>({dy:u(),...L,fill:e.fill,"fill-opacity":e.fillOpacity,stroke:e.stroke,"stroke-width":w(),opacity:v(),transform:e.transform,class:Ge}),[()=>Le(Ne("text"),e.fill===void 0&&"fill-surface-content",e.class)]);var pe=nt(Ie),Ke=nt(pe,!0);$e(pe),$e(Ie),Wt(Ie,Ge=>Re(_,Ge),()=>o(_)),mt((Ge,ut)=>{wr(pe,`text-anchor: ${d()??""};`),ft(pe,"dominant-baseline",m()),ft(pe,"href",`#${I()??""}`),ft(pe,"startOffset",T()),St(pe,0,Ge),Jn(Ke,ut)},[()=>pt(Le(Ne("text-path"))),()=>o($).map(Ge=>Ge.words.join(" ")).join()]),Q(ne,he)},Z=(ne,he)=>{{var Te=ve=>{var Ie=j6();wt(Ie,pe=>({x:Se.current,y:we.current,transform:o(te),"text-anchor":d(),"dominant-baseline":m(),...L,fill:e.fill,"fill-opacity":e.fillOpacity,stroke:e.stroke,"stroke-width":w(),opacity:v(),class:pe}),[()=>Le(Ne("text"),e.fill===void 0&&"fill-surface-content",e.class)]),$t(Ie,21,()=>o($),Pn,(pe,Ke,Ge)=>{var ut=z6(),ot=nt(ut,!0);$e(ut),mt((Ze,Et)=>{ft(ut,"x",Se.current),ft(ut,"dy",Ge===0?o(ge):c()),St(ut,0,Ze),Jn(ot,Et)},[()=>pt(Ne("text-tspan")),()=>o(Ke).words.join(" ")]),Q(pe,ut)}),$e(Ie),Wt(Ie,pe=>Re(_,pe),()=>o(_)),Q(ve,Ie)};ue(ne,ve=>{ke(r())&&ke(a())&&ve(Te)},he)}};ue(de,ne=>{e.path?ne(De):ne(Z,!1)})}$e(J),Wt(J,ne=>Re(M,ne),()=>o(M)),Q(ae,J)};ue(be,ae=>{C==="svg"&&ae(_e)})}Q(t,G),at()}function I0(t,e=!1){return function(n,r){if(+t>=+new Xr({duration:{years:1}}))return jt(n,"year");if(+t>=+new Xr({duration:{days:28}})){const i=r===0||+zr.floor(n)==+n;return e?jt(n,"month",{variant:"short"})+(i?`
${jt(n,"year")}`:""):jt(n,"month",{variant:"short"})+(i?` '${jt(n,"year",{variant:"short"})}`:"")}else if(+t>=+new Xr({duration:{days:1}})){const i=r===0||n.getDate()<=t.days;return e?jt(n,"custom",{custom:ze.DayOfMonth_numeric})+(i?`
${jt(n,"month",{variant:"short"})}`:""):jt(n,"day",{variant:"short"})}else if(+t>=+new Xr({duration:{hours:1}})){const i=r===0||+ri.floor(n)==+n;return e?jt(n,"custom",{custom:ze.Hour_numeric})+(i?`
${jt(n,"day",{variant:"short"})}`:""):i?jt(n,"day",{variant:"short"}):jt(n,"custom",{custom:ze.Hour_numeric})}else if(+t>=+new Xr({duration:{minutes:1}})){const i=r===0||+ri.floor(n)==+n;return e?jt(n,"time",{variant:"short"})+(i?`
${jt(n,"day",{variant:"short"})}`:""):jt(n,"time",{variant:"short"})}else if(+t>=+new Xr({duration:{seconds:1}})){const i=r===0||+ri.floor(n)==+n;return jt(n,"time")+(e&&i?`
${jt(n,"day",{variant:"short"})}`:"")}else if(+t>=+new Xr({duration:{milliseconds:1}})){const i=r===0||+ri.floor(n)==+n;return jt(n,"custom",{custom:[ze.Hour_2Digit,ze.Minute_2Digit,ze.Second_2Digit,ze.MiliSecond_3,ze.Hour_woAMPM]})+(e&&i?`
${jt(n,"day",{variant:"short"})}`:"")}else return n.toString()}}function cf(t,e,n){return Array.isArray(e)?e:typeof e=="function"?e(t)??[]:Bg(e)&&"interval"in e?e.interval===null||!("ticks"in t)||typeof t.ticks!="function"?[]:t.ticks(e.interval):ht(t)?e&&typeof e=="number"?t.domain().filter((r,i)=>i%e===0):t.domain():t.ticks&&typeof t.ticks=="function"?t.ticks(n??(typeof e=="number"?e:void 0)):[]}function B6(t,e,n,r,i=!1){if(r)return a=>jt(a,r);if(Wc(t)&&n)if(Bg(e)&&"interval"in e&&e.interval!=null){const a=e.interval.floor(new Date),s=e.interval.ceil(new Date);return I0(new Xr({start:a,end:s}),i)}else{const[a,s]=q1(t.domain()[0],t.domain()[1],n);return I0(new Xr({start:a,end:s}),i)}return t.tickFormat?t.tickFormat(n):a=>`${a}`}var Y6=rt("<!> <!> <!>",1),H6=rt("<!> <!> <!>",1);function z0(t,e){it(e,!0);let n=P(e,"label",3,""),r=P(e,"labelPlacement",3,"middle"),i=P(e,"rule",3,!1),a=P(e,"grid",3,!1),s=P(e,"tickSpacing",19,()=>["top","bottom","angle"].includes(e.placement)?80:["left","right","radius"].includes(e.placement)?50:void 0),l=P(e,"tickMultiline",3,!1),u=P(e,"tickLength",3,4),c=P(e,"tickMarks",3,!0),f=P(e,"classes",19,()=>({})),h=bt(e,["$$slots","$$events","$$legacy","placement","label","labelPlacement","labelProps","rule","grid","ticks","tickSpacing","tickMultiline","tickLength","tickMarks","format","tickLabelProps","motion","transitionIn","transitionInParams","scale","classes","class","tickLabel"]);const d=Gt(),g=x(()=>e.placement==="angle"?"angle":e.placement==="radius"?"radius":["top","bottom"].includes(e.placement)?"horizontal":"vertical"),m=x(()=>e.scale??(["horizontal","angle"].includes(o(g))?d.xScale:d.yScale)),v=x(()=>ur(d.xRange)),w=x(()=>ur(d.yRange)),b=x(()=>o(g)==="vertical"?d.height:o(g)==="horizontal"?d.width:o(g)==="radius"?d.height/2:o(g)==="angle"?d.width:null),S=x(()=>typeof e.ticks=="number"?e.ticks:s()&&o(b)?Math.round(o(b)/s()):void 0),A=x(()=>cf(o(m),e.ticks,o(S))),N=x(()=>B6(o(m),e.ticks,o(S),e.format,l()));function I(R){switch(e.placement){case"top":return{x:o(m)(R)+(ht(o(m))?o(m).bandwidth()/2:0),y:o(w)[0]};case"bottom":return{x:o(m)(R)+(ht(o(m))?o(m).bandwidth()/2:0),y:o(w)[1]};case"left":return{x:o(v)[0],y:o(m)(R)+(ht(o(m))?o(m).bandwidth()/2:0)};case"right":return{x:o(v)[1],y:o(m)(R)+(ht(o(m))?o(m).bandwidth()/2:0)};case"angle":return{x:o(m)(R),y:o(w)[1]};case"radius":return{x:o(v)[0],y:o(m)(R)+(ht(o(m))?o(m).bandwidth()/2:0)}}}function T(R){switch(e.placement){case"top":return{textAnchor:"middle",verticalAnchor:"end",dy:-u()-2};case"bottom":return{textAnchor:"middle",verticalAnchor:"start",dy:u()};case"left":return{textAnchor:"end",verticalAnchor:"middle",dx:-u(),dy:-2};case"right":return{textAnchor:"start",verticalAnchor:"middle",dx:u(),dy:-2};case"angle":const z=o(m)(R);return{textAnchor:z===0||Math.abs(z-Math.PI)<.01||Math.abs(z-Math.PI*2)<.01?"middle":z>Math.PI?"end":"start",verticalAnchor:"middle",dx:Math.sin(z)*(u()+2),dy:-Math.cos(z)*(u()+4)};case"radius":return{textAnchor:"middle",verticalAnchor:"middle",dx:2,dy:-2}}}const L=x(()=>e.placement==="left"||o(g)==="horizontal"&&r()==="start"?-d.padding.left:e.placement==="right"||o(g)==="horizontal"&&r()==="end"?d.width+d.padding.right:d.width/2),C=x(()=>e.placement==="top"||o(g)==="vertical"&&r()==="start"?-d.padding.top:o(g)==="vertical"&&r()==="middle"?d.height/2:e.placement==="bottom"||r()==="end"?d.height+d.padding.bottom:"0"),_=x(()=>r()==="middle"?"middle":e.placement==="right"||o(g)==="horizontal"&&r()==="end"?"end":"start"),M=x(()=>e.placement==="top"||o(g)==="vertical"&&r()==="start"||e.placement==="left"&&r()==="middle"?"start":"end"),k=x(()=>{var R;return{value:typeof n()=="function"?"":void 0,x:o(L),y:o(C),textAnchor:o(_),verticalAnchor:o(M),rotate:o(g)==="vertical"&&r()==="middle"?-90:0,capHeight:".5rem",...e.labelProps,class:Le(Ne("axis-label"),"text-[10px] stroke-surface-100 [stroke-width:2px] font-light",f().label,(R=e.labelProps)==null?void 0:R.class)}}),p=x(()=>Le(Ne("axis"),`placement-${e.placement}`,f().root,e.class));kr(t,qe(()=>h,{get"data-placement"(){return e.placement},get class(){return o(p)},children:(R,z)=>{var D=H6(),W=ce(D);{var B=ge=>{const Ee=x(()=>Jt(i(),"axis-rule")),xe=x(()=>e.placement==="left"||e.placement==="right"?e.placement:e.placement==="angle"),te=x(()=>e.placement==="top"||e.placement==="bottom"?e.placement:e.placement==="radius"),ke=x(()=>{var Se;return Le("stroke-surface-content/50",f().rule,(Se=o(Ee))==null?void 0:Se.class)});Ri(ge,qe({get x(){return o(xe)},get y(){return o(te)},get motion(){return e.motion}},()=>o(Ee),{get class(){return o(ke)}}))};ue(W,ge=>{i()!==!1&&ge(B)})}var H=Fe(W,2);{var $=ge=>{var Ee=Ce(),xe=ce(Ee);Xe(xe,n,()=>({props:o(k)})),Q(ge,Ee)},oe=(ge,Ee)=>{{var xe=te=>{Za(te,qe(()=>o(k)))};ue(ge,te=>{n()&&te(xe)},Ee)}};ue(H,ge=>{typeof n()=="function"?ge($):ge(oe,!1)})}var ye=Fe(H,2);$t(ye,18,()=>o(A),ge=>ge,(ge,Ee,xe)=>{const te=x(()=>I(Ee)),ke=x(()=>{const[V,K]=br(o(te).x,o(te).y);return{radialTickCoordsX:V,radialTickCoordsY:K}}),Se=x(()=>{const[V,K]=br(o(te).x,o(te).y+u());return{radialTickMarkCoordsX:V,radialTickMarkCoordsY:K}}),we=x(()=>{var V;return{x:o(g)==="angle"?o(ke).radialTickCoordsX:o(te).x,y:o(g)==="angle"?o(ke).radialTickCoordsY:o(te).y,value:o(N)(Ee,o(xe)),...T(Ee),motion:e.motion,...e.tickLabelProps,class:Le(Ne("axis-tick-label"),"text-[10px] stroke-surface-100 [stroke-width:2px] font-light",f().tickLabel,(V=e.tickLabelProps)==null?void 0:V.class)}}),se=x(()=>Ne("axis-tick-group"));kr(ge,{get transitionIn(){return e.transitionIn},get transitionInParams(){return e.transitionInParams},get class(){return o(se)},children:(V,K)=>{var G=Y6(),be=ce(G);{var _e=ne=>{const he=x(()=>Jt(a(),"axis-grid")),Te=x(()=>o(g)==="horizontal"||o(g)==="angle"?Ee:!1),ve=x(()=>o(g)==="vertical"||o(g)==="radius"?Ee:!1),Ie=x(()=>{var pe;return Le("stroke-surface-content/10",f().rule,(pe=o(he))==null?void 0:pe.class)});Ri(ne,qe({get x(){return o(Te)},get y(){return o(ve)},get motion(){return e.motion}},()=>o(he),{get class(){return o(Ie)}}))};ue(be,ne=>{a()!==!1&&ne(_e)})}var ae=Fe(be,2);{var J=ne=>{var he=Ce();const Te=x(()=>Le(Ne("axis-tick"),"stroke-surface-content/50",f().tick));var ve=ce(he);{var Ie=Ke=>{const Ge=x(()=>o(te).y+(e.placement==="top"?-u():u()));si(Ke,{get x1(){return o(te).x},get y1(){return o(te).y},get x2(){return o(te).x},get y2(){return o(Ge)},get motion(){return e.motion},get class(){return o(Te)}})},pe=(Ke,Ge)=>{{var ut=Ze=>{const Et=x(()=>o(te).x+(e.placement==="left"?-u():u()));si(Ze,{get x1(){return o(te).x},get y1(){return o(te).y},get x2(){return o(Et)},get y2(){return o(te).y},get motion(){return e.motion},get class(){return o(Te)}})},ot=(Ze,Et)=>{{var rn=qt=>{si(qt,{get x1(){return o(ke).radialTickCoordsX},get y1(){return o(ke).radialTickCoordsY},get x2(){return o(Se).radialTickMarkCoordsX},get y2(){return o(Se).radialTickMarkCoordsY},get motion(){return e.motion},get class(){return o(Te)}})};ue(Ze,qt=>{o(g)==="angle"&&qt(rn)},Et)}};ue(Ke,Ze=>{o(g)==="vertical"?Ze(ut):Ze(ot,!1)},Ge)}};ue(ve,Ke=>{o(g)==="horizontal"?Ke(Ie):Ke(pe,!1)})}Q(ne,he)};ue(ae,ne=>{c()&&ne(J)})}var de=Fe(ae,2);{var De=ne=>{var he=Ce(),Te=ce(he);Xe(Te,()=>e.tickLabel,()=>({props:o(we),index:o(xe)})),Q(ne,he)},Z=ne=>{Za(ne,qe(()=>o(we)))};ue(de,ne=>{e.tickLabel?ne(De):ne(Z,!1)})}Q(V,G)},$$slots:{default:!0}})}),Q(R,D)},$$slots:{default:!0}})),at()}var q6=rt("<!> <!>",1),U6=rt("<!> <!>",1),G6=rt("<!> <!>",1);function X6(t,e){it(e,!0);const n=Gt();let r=P(e,"x",3,!1),i=P(e,"y",3,!1),a=P(e,"bandAlign",3,"center"),s=P(e,"radialY",3,"circle"),l=P(e,"transitionInParams",19,()=>({easing:ag})),u=P(e,"classes",19,()=>({})),c=P(e,"ref",15),f=bt(e,["$$slots","$$events","$$legacy","x","y","xTicks","yTicks","bandAlign","radialY","motion","transitionIn","transitionInParams","classes","class","ref"]),h=Ve(void 0);vt(()=>{c(o(h))});const d=x(()=>e.yTicks??(ht(n.yScale)?void 0:4)),g=x(()=>Jo(e.motion)),m=x(()=>{var N;return e.transitionIn??((N=o(g))==null?void 0:N.options)?gf:()=>({})}),v=x(()=>cf(n.xScale,e.xTicks)),w=x(()=>cf(n.yScale,o(d))),b=x(()=>ht(n.xScale)?a()==="between"?-(n.xScale.padding()*n.xScale.step())/2:n.xScale.step()/2-n.xScale.padding()*n.xScale.step()/2:0),S=x(()=>ht(n.yScale)?a()==="between"?-(n.yScale.padding()*n.yScale.step())/2:n.yScale.step()/2-n.yScale.padding()*n.yScale.step()/2:0),A=x(()=>Le(Ne("grid"),u().root,e.class));kr(t,qe({get class(){return o(A)}},()=>f,{get ref(){return o(h)},set ref(N){Re(h,N,!0)},children:(N,I)=>{var T=G6(),L=ce(T);{var C=k=>{const p=x(()=>Jt(r(),"grid-x-line")),R=x(()=>Ne("grid-x"));kr(k,{get transitionIn(){return o(m)},get transitionInParams(){return l()},get class(){return o(R)},children:(z,D)=>{var W=q6(),B=ce(W);$t(B,16,()=>o(v),oe=>oe,(oe,ye,ge,Ee)=>{var xe=Ce(),te=ce(xe);{var ke=we=>{const se=x(()=>{const[G,be]=br(n.xScale(ye),n.yRange[0]);return{x1:G,y1:be}}),V=x(()=>{const[G,be]=br(n.xScale(ye),n.yRange[1]);return{x2:G,y2:be}}),K=x(()=>{var G;return Le(Ne("grid-x-radial-line"),"stroke-surface-content/10",u().line,(G=o(p))==null?void 0:G.class)});si(we,qe({get x1(){return o(se).x1},get y1(){return o(se).y1},get x2(){return o(V).x2},get y2(){return o(V).y2},get motion(){return o(g)}},()=>o(p),{get class(){return o(K)}}))},Se=we=>{const se=x(()=>{var V;return Le(Ne("grid-x-rule"),"stroke-surface-content/10",u().line,(V=o(p))==null?void 0:V.class)});Ri(we,qe({get x(){return ye},get xOffset(){return o(b)},get motion(){return e.motion}},()=>o(p),{get class(){return o(se)}}))};ue(te,we=>{n.radial?we(ke):we(Se,!1)})}Q(oe,xe)});var H=Fe(B,2);{var $=oe=>{const ye=x(()=>n.xScale.step()+o(b)),ge=x(()=>{var Ee;return Le(Ne("grid-x-end-rule"),"stroke-surface-content/10",u().line,(Ee=o(p))==null?void 0:Ee.class)});Ri(oe,qe({get x(){return o(v)[o(v).length-1]},get xOffset(){return o(ye)},get motion(){return e.motion}},()=>o(p),{get class(){return o(ge)}}))};ue(H,oe=>{ht(n.xScale)&&a()==="between"&&!n.radial&&o(v).length&&oe($)})}Q(z,W)},$$slots:{default:!0}})};ue(L,k=>{r()&&k(C)})}var _=Fe(L,2);{var M=k=>{const p=x(()=>Jt(i(),"grid-y-line")),R=x(()=>Ne("grid-y"));kr(k,{get transitionIn(){return o(m)},get transitionInParams(){return l()},get class(){return o(R)},children:(z,D)=>{var W=U6(),B=ce(W);$t(B,16,()=>o(w),oe=>oe,(oe,ye,ge,Ee)=>{var xe=Ce(),te=ce(xe);{var ke=we=>{var se=Ce(),V=ce(se);{var K=be=>{const _e=x(()=>n.yScale(ye)+o(S)),ae=x(()=>{var J;return Le(Ne("grid-y-radial-circle"),"fill-none stroke-surface-content/10",u().line,(J=o(p))==null?void 0:J.class)});aa(be,qe({get r(){return o(_e)},get motion(){return e.motion}},()=>o(p),{get class(){return o(ae)}}))},G=be=>{const _e=x(()=>o(v).map(J=>({x:J,y:ye}))),ae=x(()=>{var J;return Le(Ne("grid-y-radial-line"),"stroke-surface-content/10",u().line,(J=o(p))==null?void 0:J.class)});Va(be,qe({get data(){return o(_e)},x:"x",y:"y",get motion(){return o(g)},get curve(){return P1}},()=>o(p),{get class(){return o(ae)}}))};ue(V,be=>{s()==="circle"?be(K):be(G,!1)})}Q(we,se)},Se=we=>{const se=x(()=>{var V;return Le(Ne("grid-y-rule"),"stroke-surface-content/10",u().line,(V=o(p))==null?void 0:V.class)});Ri(we,qe({get y(){return ye},get yOffset(){return o(S)},get motion(){return e.motion}},()=>o(p),{get class(){return o(se)}}))};ue(te,we=>{n.radial?we(ke):we(Se,!1)})}Q(oe,xe)});var H=Fe(B,2);{var $=oe=>{var ye=Ce(),ge=ce(ye);{var Ee=te=>{const ke=x(()=>n.yScale(o(w)[o(w).length-1])+n.yScale.step()+o(S)),Se=x(()=>{var we;return Le(Ne("grid-y-radial-circle"),"fill-none stroke-surface-content/10",u().line,(we=o(p))==null?void 0:we.class)});aa(te,qe({get r(){return o(ke)},get motion(){return e.motion}},()=>o(p),{get class(){return o(Se)}}))},xe=te=>{const ke=x(()=>n.yScale.step()+o(S)),Se=x(()=>{var we;return Le(Ne("grid-y-end-rule"),"stroke-surface-content/10",u().line,(we=o(p))==null?void 0:we.class)});Ri(te,qe({get y(){return o(w)[o(w).length-1]},get yOffset(){return o(ke)},get motion(){return e.motion}},()=>o(p),{get class(){return o(Se)}}))};ue(ge,te=>{n.radial?te(Ee):te(xe,!1)})}Q(oe,ye)};ue(H,oe=>{ht(n.yScale)&&a()==="between"&&o(w).length&&oe($)})}Q(z,W)},$$slots:{default:!0}})};ue(_,k=>{i()&&k(M)})}Q(N,T)},$$slots:{default:!0}})),at()}function V6(t){const e=(t==null?void 0:t.all)??0,n=(t==null?void 0:t.x)??e,r=(t==null?void 0:t.y)??e,i=(t==null?void 0:t.left)??n,a=(t==null?void 0:t.right)??n,s=(t==null?void 0:t.top)??r,l=(t==null?void 0:t.bottom)??r;return{left:i,right:a,bottom:l,top:s}}function Z6(t,e){const n=x(()=>e==null?void 0:e());return r=>{var h,d,g,m,v,w,b,S,A;const i=V6((h=o(n))==null?void 0:h.insets),a=t.xScale.domain(),s=t.yScale.domain(),l=Yt(((d=o(n))==null?void 0:d.x)??t.x),u=Yt(((g=o(n))==null?void 0:g.y)??t.y),c=Yt(((m=o(n))==null?void 0:m.x1)??t.x1),f=Yt(((v=o(n))==null?void 0:v.y1)??t.y1);if(ht(t.yScale)){const N=j0(t.yScale(u(r))??0)+(t.y1Scale?t.y1Scale(f(r)):0)+i.top,I=Math.max(0,t.yScale.bandwidth?(t.y1Scale?((b=(w=t.y1Scale).bandwidth)==null?void 0:b.call(w))??0:t.yScale.bandwidth())-i.bottom-i.top:0),T=l(r);let L=0,C=0;Array.isArray(T)?(L=Bt(T),C=Xt(T)):T==null?(L=0,C=0):T>0?(L=Xt([0,a[0]]),C=T):(L=T,C=Bt([0,a[1]]));const _=t.xScale(L)+i.left,M=Math.max(0,t.xScale(C)-t.xScale(L)-i.left-i.right);return{x:_,y:N,width:M,height:I}}else{const N=j0(t.xScale(l(r)))+(t.x1Scale?t.x1Scale(c(r)):0)+i.left,I=Math.max(0,t.xScale.bandwidth?(t.x1Scale?((A=(S=t.x1Scale).bandwidth)==null?void 0:A.call(S))??0:t.xScale.bandwidth())-i.left-i.right:0),T=u(r);let L=0,C=0;Array.isArray(T)?(L=Xt(T),C=Bt(T)):T==null?(L=0,C=0):T>0?(L=T,C=Xt([0,s[0]])):(L=Bt([0,s[1]]),C=T);const _=t.yScale(L)+i.top,M=t.yScale(C)-t.yScale(L)-i.bottom-i.top;return{x:N,y:_,width:I,height:M}}}}function j0(t){return Array.isArray(t)?t[0]:t}function Im(t,e){it(e,!0);const n=Gt();let r=P(e,"x",19,()=>n.x),i=P(e,"y",19,()=>n.y),a=P(e,"stroke",3,"black"),s=P(e,"strokeWidth",3,0),l=P(e,"radius",3,0),u=P(e,"rounded",3,"all"),c=bt(e,["$$slots","$$events","$$legacy","data","x","y","x1","y1","fill","fillOpacity","stroke","strokeWidth","opacity","radius","rounded","motion","insets","initialX","initialY","initialHeight","initialWidth"]);const f=x(()=>a()===null||a()===void 0?"black":a()),h=x(()=>Z6(n,()=>({x:r(),y:i(),x1:e.x1,y1:e.y1,insets:e.insets}))),d=x(()=>o(h)(e.data)??{x:0,y:0,width:0,height:0}),g=x(()=>ht(n.xScale)),m=x(()=>Yt(o(g)?i():r())),v=x(()=>o(m)(e.data)),w=x(()=>Array.isArray(o(v))?a2(o(v)):o(v)),b=x(()=>u()==="edge"?o(g)?o(w)>=0?"top":"bottom":o(w)>=0?"right":"left":u()),S=x(()=>["all","top","left","top-left"].includes(o(b))),A=x(()=>["all","top","right","top-right"].includes(o(b))),N=x(()=>["all","bottom","left","bottom-left"].includes(o(b))),I=x(()=>["all","bottom","right","bottom-right"].includes(o(b))),T=x(()=>o(d).width),L=x(()=>o(d).height),C=x(()=>2*l()),_=x(()=>`M${o(d).x+l()},${o(d).y} h${o(T)-o(C)}
      ${o(A)?`a${l()},${l()} 0 0 1 ${l()},${l()}`:`h${l()}v${l()}`}
      v${o(L)-o(C)}
      ${o(I)?`a${l()},${l()} 0 0 1 ${-l()},${l()}`:`v${l()}h${-l()}`}
      h${o(C)-o(T)}
      ${o(N)?`a${l()},${l()} 0 0 1 ${-l()},${-l()}`:`h${-l()}v${-l()}`}
      v${o(C)-o(L)}
      ${o(S)?`a${l()},${l()} 0 0 1 ${l()},${-l()}`:`v${-l()}h${l()}`}
      z`.split(`
`).join(""));var M=Ce(),k=ce(M);{var p=z=>{const D=x(()=>o(d).y+o(d).height),W=x(()=>o(d).x+o(d).width);var B=x(()=>Jt(c,"bar"));Qf(z,qe({get innerRadius(){return o(d).y},get outerRadius(){return o(D)},get startAngle(){return o(d).x},get endAngle(){return o(W)},get fill(){return e.fill},get fillOpacity(){return e.fillOpacity},get stroke(){return o(f)},get strokeWidth(){return s()},get opacity(){return e.opacity},get cornerRadius(){return l()}},()=>o(B)))},R=(z,D)=>{{var W=H=>{const $=x(()=>o(b)==="none"?0:l());var oe=x(()=>Jt(c,"bar"));Da(H,qe({get fill(){return e.fill},get fillOpacity(){return e.fillOpacity},get stroke(){return o(f)},get strokeWidth(){return s()},get opacity(){return e.opacity},get rx(){return o($)},get motion(){return e.motion},get initialX(){return e.initialX},get initialY(){return e.initialY},get initialHeight(){return e.initialHeight},get initialWidth(){return e.initialWidth}},()=>o(d),()=>o(oe)))},B=H=>{const $=x(()=>Jo(e.motion));var oe=x(()=>Jt(c,"bar"));Va(H,qe({get pathData(){return o(_)},get fill(){return e.fill},get fillOpacity(){return e.fillOpacity},get stroke(){return o(f)},get strokeWidth(){return s()},get opacity(){return e.opacity},get motion(){return o($)}},()=>o(oe)))};ue(z,H=>{o(b)==="all"||o(b)==="none"||l()===0?H(W):H(B,!1)},D)}};ue(k,z=>{n.radial?z(p):z(R,!1)})}Q(t,M),at()}var K6=rt("<!> <!> <!> <!>",1);function Q6(t,e){it(e,!0);const n=Gt(),r=Vf();let i=P(e,"x",19,()=>n.x),a=P(e,"y",19,()=>n.y),s=P(e,"points",3,!1),l=P(e,"lines",3,!1),u=P(e,"area",3,!1),c=P(e,"bar",3,!1),f=P(e,"motion",3,"spring");const h=x(()=>Yt(i())),d=x(()=>Yt(a())),g=x(()=>e.data??r.data),m=x(()=>o(h)(o(g))),v=x(()=>Array.isArray(o(m))?o(m).map(D=>n.xScale(D)):n.xScale(o(m))),w=x(()=>ht(n.xScale)&&!n.radial?n.xScale.bandwidth()/2:0),b=x(()=>o(d)(o(g))),S=x(()=>Array.isArray(o(b))?o(b).map(D=>n.yScale(D)):n.yScale(o(b))),A=x(()=>ht(n.yScale)&&!n.radial?n.yScale.bandwidth()/2:0),N=x(()=>e.axis==null?ht(n.yScale)?"y":"x":e.axis),I=x(()=>{let D=[];return o(g)&&((o(N)==="x"||o(N)==="both")&&(Array.isArray(o(v))?D=[...D,...o(v).filter(cs).map((W,B)=>({x1:W+o(w),y1:Bt(n.yRange),x2:W+o(w),y2:Xt(n.yRange)}))]:o(v)&&(D=[...D,{x1:o(v)+o(w),y1:Bt(n.yRange),x2:o(v)+o(w),y2:Xt(n.yRange)}])),(o(N)==="y"||o(N)==="both")&&(Array.isArray(o(S))?D=[...D,...o(S).filter(cs).map((W,B)=>({x1:Bt(n.xRange),y1:W+o(A),x2:Xt(n.xRange),y2:W+o(A)}))]:o(S)&&(D=[...D,{x1:Bt(n.xRange),y1:o(S)+o(A),x2:Xt(n.xRange),y2:o(S)+o(A)}])),n.radial&&(D=D.map(W=>{const[B,H]=br(W.x1,W.y1),[$,oe]=br(W.x2,W.y2);return{...W,x1:B,y1:H,x2:$,y2:oe}}))),D}),T=x(()=>{const D={x:0,y:0,width:0,height:0};if(!o(g))return D;if(o(N)==="x"||o(N)==="both"){if(Array.isArray(o(v)))D.width=Xt(o(v))-Bt(o(v));else if(ht(n.xScale))D.width=n.xScale.step();else{const W=n.flatData.findIndex($=>Number(o(h)($))===Number(o(h)(o(g)))),H=W+1===n.flatData.length?Xt(n.xDomain):o(h)(n.flatData[W+1]);D.width=(n.xScale(H)??0)-(o(v)??0)}D.x=(Array.isArray(o(v))?Bt(o(v)):o(v))-(ht(n.xScale)?n.xScale.padding()*n.xScale.step()/2:0),o(N)==="x"&&(D.y=Bt(n.yRange),D.height=Xt(n.yRange)-Bt(n.yRange))}if(o(N)==="y"||o(N)==="both"){if(Array.isArray(o(S)))D.height=Xt(o(S))-Bt(o(S));else if(ht(n.yScale))D.height=n.yScale.step();else{const W=n.flatData.findIndex($=>Number(o(h)($))===Number(o(h)(o(g)))),H=W+1===n.flatData.length?Xt(n.yDomain):o(h)(n.flatData[W+1]);D.height=(n.yScale(H)??0)-(o(S)??0)}D.y=(Array.isArray(o(S))?Bt(o(S)):o(S))-(ht(n.yScale)?n.yScale.padding()*n.yScale.step()/2:0),o(N)==="y"&&(D.width=Xt(n.xRange))}return D}),L=x(()=>{let D=[];if(!o(g))return D;if(Array.isArray(o(v)))if(Array.isArray(o(g))){const W=o(g);Array.isArray(n.data)&&(D=n.data.map(H=>({series:H,point:H.find($=>o(d)($)===o(d)(W))})).filter(H=>H.point).map((H,$)=>({x:n.xScale(H.point[1])+o(w),y:o(S)+o(A),fill:n.config.c?n.cGet(H.series):null,data:{x:H.point[1],y:o(b)}})))}else D=o(v).filter(cs).map((W,B)=>{var $;const H=($=n.config.x)==null?void 0:$[B];return{x:W+o(w),y:o(S)+o(A),fill:n.config.c?n.cGet({...o(g),$key:H}):null,data:{x:o(m),y:o(b)}}});else if(Array.isArray(o(S)))if(Array.isArray(o(g))){const W=o(g);Array.isArray(n.data)&&(D=n.data.map(H=>({series:H,point:H.find($=>o(h)($)===o(h)(W))})).filter(H=>H.point).map((H,$)=>({x:o(v)+o(w),y:n.yScale(H.point[1])+o(A),fill:n.config.c?n.cGet(H.series):null,data:{x:o(m),y:H.point[1]}})))}else D=o(S).filter(cs).map((W,B)=>{const H=n.config.y[B];return{x:o(v)+o(w),y:W+o(A),fill:n.config.c?n.cGet({...o(g),$key:H}):null,data:{x:o(m),y:o(b)}}});else o(v)!=null&&o(S)!=null?D=[{x:o(v)+o(w),y:o(S)+o(A),fill:n.config.c?n.cGet(o(g)):null,data:{x:o(m),y:o(b)}}]:D=[];return n.radial&&(D=D.map(W=>{const[B,H]=br(W.x,W.y);return{...W,x:B,y:H}})),D}),C=x(()=>Jt(u(),"highlight-area")),_=x(()=>Jt(c(),"highlight-bar")),M=x(()=>Jt(l(),"highlight-line")),k=x(()=>Jt(s(),"highlight-point"));var p=Ce(),R=ce(p);{var z=D=>{var W=K6(),B=ce(W);{var H=te=>{var ke=Ce(),Se=ce(ke);{var we=V=>{var K=Ce(),G=ce(K);Xe(G,u,()=>({area:o(T)})),Q(V,K)},se=(V,K)=>{{var G=_e=>{const ae=x(()=>f()==="spring"?"spring":void 0),J=x(()=>o(T).x+o(T).width),de=x(()=>o(T).y+o(T).height),De=x(()=>Le(!o(C).fill&&"fill-surface-content/5",o(C).class)),Z=x(()=>e.onAreaClick&&(ne=>e.onAreaClick(ne,{data:o(g)})));Qf(_e,{get motion(){return o(ae)},get startAngle(){return o(T).x},get endAngle(){return o(J)},get innerRadius(){return o(T).y},get outerRadius(){return o(de)},get class(){return o(De)},get onclick(){return o(Z)}})},be=_e=>{const ae=x(()=>f()==="spring"?"spring":void 0),J=x(()=>Le(!o(C).fill&&"fill-surface-content/5",o(C).class)),de=x(()=>e.onAreaClick&&(De=>e.onAreaClick(De,{data:o(g)})));Da(_e,qe({get motion(){return o(ae)}},()=>o(T),()=>o(C),{get class(){return o(J)},get onclick(){return o(de)}}))};ue(V,_e=>{n.radial?_e(G):_e(be,!1)},K)}};ue(Se,V=>{typeof u()=="function"?V(we):V(se,!1)})}Q(te,ke)};ue(B,te=>{u()&&te(H)})}var $=Fe(B,2);{var oe=te=>{var ke=Ce(),Se=ce(ke);{var we=V=>{var K=Ce(),G=ce(K);Xe(G,c),Q(V,K)},se=V=>{const K=x(()=>f()==="spring"?"spring":void 0),G=x(()=>Le(!o(_).fill&&"fill-primary",o(_).class)),be=x(()=>e.onBarClick&&(_e=>e.onBarClick(_e,{data:o(g)})));Im(V,qe({get motion(){return o(K)},get data(){return o(g)}},()=>o(_),{get class(){return o(G)},get onclick(){return o(be)}}))};ue(Se,V=>{typeof c()=="function"?V(we):V(se,!1)})}Q(te,ke)};ue($,te=>{c()&&te(oe)})}var ye=Fe($,2);{var ge=te=>{var ke=Ce(),Se=ce(ke);{var we=V=>{var K=Ce(),G=ce(K);Xe(G,l,()=>({lines:o(I)})),Q(V,K)},se=V=>{var K=Ce(),G=ce(K);$t(G,17,()=>o(I),Pn,(be,_e)=>{const ae=x(()=>f()==="spring"?"spring":void 0),J=x(()=>Le("stroke-surface-content/20 stroke-2 [stroke-dasharray:2,2] pointer-events-none",o(M).class));si(be,qe({get motion(){return o(ae)},get x1(){return o(_e).x1},get y1(){return o(_e).y1},get x2(){return o(_e).x2},get y2(){return o(_e).y2}},()=>o(M),{get class(){return o(J)}}))}),Q(V,K)};ue(Se,V=>{typeof l()=="function"?V(we):V(se,!1)})}Q(te,ke)};ue(ye,te=>{l()&&te(ge)})}var Ee=Fe(ye,2);{var xe=te=>{var ke=Ce(),Se=ce(ke);{var we=V=>{var K=Ce(),G=ce(K);Xe(G,s,()=>({points:o(L)})),Q(V,K)},se=V=>{var K=Ce(),G=ce(K);$t(G,17,()=>o(L),Pn,(be,_e)=>{const ae=x(()=>f()==="spring"?"spring":void 0),J=x(()=>Le("stroke-white [paint-order:stroke] drop-shadow-sm",!o(_e).fill&&(typeof s()=="boolean"||!s().fill)&&"fill-primary",o(k).class)),de=x(()=>e.onPointClick&&(he=>{he.stopPropagation()})),De=x(()=>e.onPointClick&&(he=>e.onPointClick(he,{point:o(_e),data:o(g)}))),Z=x(()=>e.onPointEnter&&(he=>{e.onPointClick&&(he.target.style.cursor="pointer"),e.onPointEnter(he,{point:o(_e),data:o(g)})})),ne=x(()=>e.onPointLeave&&(he=>{e.onPointClick&&(he.target.style.cursor="default"),e.onPointLeave(he,{point:o(_e),data:o(g)})}));aa(be,qe({get motion(){return o(ae)},get cx(){return o(_e).x},get cy(){return o(_e).y},get fill(){return o(_e).fill},r:4,strokeWidth:6},()=>o(k),{get class(){return o(J)},get onpointerdown(){return o(de)},get onclick(){return o(De)},get onpointerenter(){return o(Z)},get onpointerleave(){return o(ne)}}))}),Q(V,K)};ue(Se,V=>{typeof s()=="function"?V(we):V(se,!1)})}Q(te,ke)};ue(Ee,te=>{s()&&te(xe)})}Q(D,W)};ue(R,D=>{o(g)&&D(z)})}Q(t,p),at()}function zm(t,e){return Math.abs(t.x-e.x)<1e-6&&Math.abs(t.y-e.y)<1e-6}function J6(t,e){return zm(t,e)?"":`M ${t.x} ${t.y} L ${e.x} ${e.y}`}function Ei(t){return Math.abs(t)<1e-6}function Jf({source:t,target:e,sweep:n}){return n==="horizontal-vertical"?`M ${t.x} ${t.y} L ${e.x} ${t.y} L ${e.x} ${e.y}`:`M ${t.x} ${t.y} L ${t.x} ${e.y} L ${e.x} ${e.y}`}function $6(t){const{radius:e,dx:n,dy:r,source:i,target:a,sweep:s}=t,l=Math.max(0,Math.min(e,Math.abs(n),Math.abs(r)));if(Ei(l))return Jf(t);const u=Math.sign(n),c=Math.sign(r);if(s==="horizontal-vertical"){const f={x:a.x-l*u,y:i.y},h={x:a.x,y:i.y+l*c};return`M ${i.x} ${i.y} L ${f.x} ${f.y} L ${h.x} ${h.y} L ${a.x} ${a.y}`}else{const f={x:i.x,y:a.y-l*c},h={x:i.x+l*u,y:a.y};return`M ${i.x} ${i.y} L ${f.x} ${f.y} L ${h.x} ${h.y} L ${a.x} ${a.y}`}}function e4(t){const{radius:e,dx:n,dy:r,source:i,target:a,sweep:s}=t,l=Math.max(0,Math.min(e,Math.abs(n),Math.abs(r)));if(Ei(l))return Jf(t);const u=Math.sign(n),c=Math.sign(r);if(s==="horizontal-vertical"){const f={x:a.x-l*u,y:i.y},h={x:a.x,y:i.y+l*c},d=u*c>0?1:0;return`M ${i.x} ${i.y} L ${f.x} ${f.y} A ${l} ${l} 0 0 ${d} ${h.x} ${h.y} L ${a.x} ${a.y}`}else{const f={x:i.x,y:a.y-l*c},h={x:i.x+l*u,y:a.y},d=u*c>0?0:1;return`M ${i.x} ${i.y} L ${f.x} ${f.y} A ${l} ${l} 0 0 ${d} ${h.x} ${h.y} L ${a.x} ${a.y}`}}const F0={square:Jf,beveled:$6,rounded:e4};function t4(t){const{source:e,target:n,type:r}=t;if(zm(e,n))return"";const i=n.x-e.x,a=n.y-e.y;return r==="straight"||Ei(i)||Ei(a)?J6(e,n):(F0[r]||F0.square)({...t,dx:i,dy:a})}const B0="M0,0L0,0";function n4({source:t,target:e,sweep:n,curve:r}){const i=e.x-t.x,a=e.y-t.y,s=el().curve(r);let l=[];const u=Ei(i)||Ei(a);if(n==="none"||u?l=[[t.x,t.y],[e.x,e.y]]:n==="horizontal-vertical"?l=[[t.x,t.y],[e.x,t.y],[e.x,e.y]]:n==="vertical-horizontal"&&(l=[[t.x,t.y],[t.x,e.y],[e.x,e.y]]),l.length===2&&Ei(i)&&Ei(i))return B0;const c=s(l);return!c||c.includes("NaN")?B0:c}var r4=rt("<!> <!> <!> <!>",1);function i4(t,e){const n=Mr();it(e,!0);let r=P(e,"source",19,()=>({x:0,y:0})),i=P(e,"target",19,()=>({x:100,y:100})),a=P(e,"type",3,"rounded"),s=P(e,"radius",3,20),l=P(e,"curve",3,Pf),u=P(e,"splineRef",15),c=bt(e,["$$slots","$$events","$$legacy","source","target","sweep","type","radius","curve","splineRef","pathData","marker","markerStart","markerMid","markerEnd","motion"]);const f=x(()=>e.sweep?e.sweep:a()==="d3"?"none":"horizontal-vertical"),h=x(()=>e.markerStart||e.marker?Rn("marker-start",n):""),d=x(()=>e.markerMid||e.marker?Rn("marker-mid",n):""),g=x(()=>e.markerEnd||e.marker?Rn("marker-end",n):""),m=Jo(e.motion),v=m?{type:m.type,options:{interpolate:R1.interpolatePath,...m.options}}:void 0,w=x(()=>e.pathData?e.pathData:a()==="d3"?n4({source:r(),target:i(),sweep:o(f),curve:l()}):t4({source:r(),target:i(),sweep:o(f),type:a(),radius:s()})),b=on("",()=>o(w),v||{type:"none"});var S=r4(),A=ce(S);const N=x(()=>o(h)?`url(#${o(h)})`:void 0),I=x(()=>o(d)?`url(#${o(d)})`:void 0),T=x(()=>o(g)?`url(#${o(g)})`:void 0);var L=x(()=>Jt(c,"connector"));Va(A,qe({get pathData(){return b.current},get"marker-start"(){return o(N)},get"marker-mid"(){return o(I)},get"marker-end"(){return o(T)}},()=>o(L),()=>c,{get splineRef(){return u()},set splineRef(k){u(k)}}));var C=Fe(A,2);oi(C,{get id(){return o(h)},get marker(){return e.markerStart}});var _=Fe(C,2);oi(_,{get id(){return o(d)},get marker(){return e.markerMid}});var M=Fe(_,2);oi(M,{get id(){return o(g)},get marker(){return e.markerEnd}}),Q(t,S),at()}const va={x:0,y:0};function a4(t,e){it(e,!0);let n=P(e,"sankey",3,!1),r=P(e,"type",3,"d3"),i=P(e,"sweep",3,"none"),a=bt(e,["$$slots","$$events","$$legacy","data","sankey","source","target","orientation","x","y","curve","explicitCoords","type","sweep"]);const s=x(()=>e.source?e.source:n()?v=>({node:v.source,y:v.y0,isSource:!0}):v=>v.source),l=x(()=>e.target?e.target:n()?v=>({node:v.target,y:v.y1,isSource:!1}):v=>v.target),u=x(()=>e.orientation?e.orientation:n()?"horizontal":"vertical"),c=x(()=>e.curve?e.curve:o(u)==="horizontal"?Wk:Ik),f=x(()=>e.x?e.x:n()?v=>v.isSource?v.node.x1:v.node.x0:v=>o(u)==="horizontal"?v.y:v.x),h=x(()=>e.y?e.y:n()?v=>v.y:v=>o(u)==="horizontal"?v.x:v.y),d=x(()=>{if(e.explicitCoords)return{x:e.explicitCoords.x1,y:e.explicitCoords.y1};if(!e.data)return va;try{const v=o(s)(e.data);if(v==null)return va;const w=o(f)(v),b=o(h)(v);return{x:Number.isFinite(w)?w:0,y:Number.isFinite(b)?b:0}}catch(v){return console.error("Error accessing source coordinates:",v,"Data:",e.data),va}}),g=x(()=>{if(e.explicitCoords)return{x:e.explicitCoords.x2,y:e.explicitCoords.y2};if(!e.data)return va;try{const v=o(l)(e.data);if(v==null)return va;const w=o(f)(v),b=o(h)(v);return{x:Number.isFinite(w)?w:0,y:Number.isFinite(b)?b:0}}catch(v){return console.error("Error accessing target coordinates:",v,"Data:",e.data),va}});var m=x(()=>Jt(a,"link"));i4(t,qe({get source(){return o(d)},get target(){return o(g)},get type(){return r()},get curve(){return o(c)},get sweep(){return i()}},()=>o(m))),at()}var o4=rt("<!> <!>",1);function s4(t,e){it(e,!0);const n=Gt();let r=P(e,"r",3,5),i=P(e,"links",3,!1),a=bt(e,["$$slots","$$events","$$legacy","data","x","y","r","offsetX","offsetY","links","fill","fillOpacity","stroke","strokeWidth","opacity","children"]);function s(b,S,A){return typeof S=="function"?S(b,n):S??(ht(A)&&!n.radial?A.bandwidth()/2:0)}const l=x(()=>e.x?Yt(e.x):n.x),u=x(()=>e.y?Yt(e.y):n.y),c=x(()=>e.data??n.data),f=(b,S,A)=>{const N=n.xScale(b),I=n.yScale(S);return{x:N+s(N,e.offsetX,n.xScale),y:I+s(I,e.offsetY,n.yScale),r:n.config.r?n.rGet(A):r(),xValue:b,yValue:S,data:A}},h=x(()=>o(c).flatMap(b=>{const S=o(l)(b),A=o(u)(b);return Array.isArray(S)?S.filter(Boolean).map(N=>f(N,A,b)):Array.isArray(A)?A.filter(Boolean).map(N=>f(S,N,b)):S!=null&&A!=null?f(S,A,b):[]})),d=x(()=>o(c).flatMap(b=>{const S=o(l)(b),A=o(u)(b);if(Array.isArray(S)){const[N,I]=ur(n.xGet(b)),T=n.yGet(b)+s(n.yGet(b),e.offsetY,n.yScale);return{source:{x:N+s(N,e.offsetX,n.xScale)+(n.config.r?n.rGet(b):r()),y:T},target:{x:I+s(I,e.offsetX,n.xScale)-(n.config.r?n.rGet(b):r()),y:T},data:b}}else if(Array.isArray(A)){const N=n.xGet(b)+s(n.xGet(b),e.offsetX,n.xScale),[I,T]=ur(n.yGet(b));return{source:{x:N,y:I+s(I,e.offsetY,n.yScale)},target:{x:N,y:T+s(T,e.offsetY,n.yScale)},data:b}}}));var g=Ce(),m=ce(g);{var v=b=>{var S=Ce(),A=ce(S);Xe(A,()=>e.children,()=>({points:o(h)})),Q(b,S)},w=b=>{var S=o4(),A=ce(S);{var N=T=>{var L=Ce(),C=ce(L);$t(C,17,()=>o(d),Pn,(_,M)=>{const k=x(()=>e.fill??(n.config.c?n.cGet(o(M).data):null));var p=x(()=>Jt(i(),"points-link"));a4(_,qe({get data(){return o(M)},get stroke(){return o(k)}},()=>o(p)))}),Q(T,L)};ue(A,T=>{i()&&T(N)})}var I=Fe(A,2);$t(I,17,()=>o(h),Pn,(T,L)=>{const C=x(()=>br(o(L).x,o(L).y)),_=x(()=>n.radial?o(C)[0]:o(L).x),M=x(()=>n.radial?o(C)[1]:o(L).y),k=x(()=>e.fill??(n.config.c?n.cGet(o(L).data):null));var p=x(()=>Jt(a,"point"));aa(T,qe({get cx(){return o(_)},get cy(){return o(M)},get r(){return o(L).r},get fill(){return o(k)},get fillOpacity(){return e.fillOpacity},get stroke(){return e.stroke},get strokeWidth(){return e.strokeWidth},get opacity(){return e.opacity}},()=>o(p)))}),Q(b,S)};ue(m,b=>{e.children?b(v):b(w,!1)})}Q(t,g),at()}function l4(t,e){it(e,!0);const n=Gt();let r=P(e,"placement",3,"outside"),i=P(e,"offset",19,()=>r()==="center"?0:4),a=P(e,"key",3,(c,f)=>f),s=bt(e,["$$slots","$$events","$$legacy","data","value","x","y","placement","offset","format","key","children","class","fill"]);function l(c){var m,v,w,b;const f=ht(n.yScale)?c.xValue:c.yValue,h=typeof e.fill=="function"?Yt(e.fill)(c.data):e.fill,d=e.value?Yt(e.value)(c.data):ht(n.yScale)?c.xValue:c.yValue,g=jt(d,e.format??(e.value?void 0:ht(n.yScale)?(v=(m=n.xScale).tickFormat)==null?void 0:v.call(m):(b=(w=n.yScale).tickFormat)==null?void 0:b.call(w)));return ht(n.yScale)?f<0?{value:g,fill:h,x:c.x+(r()==="outside"?-i():i()),y:c.y,textAnchor:r()==="outside"?"end":"start",verticalAnchor:"middle",capHeight:".6rem"}:{value:g,fill:h,x:c.x+(r()==="outside"?i():-i()),y:c.y,textAnchor:r()==="outside"?"start":"end",verticalAnchor:"middle",capHeight:".6rem"}:f<0?{value:g,fill:h,x:c.x,y:c.y+(r()==="outside"?i():-i()),capHeight:".6rem",textAnchor:"middle",verticalAnchor:r()==="center"?"middle":r()==="outside"?"start":"end"}:{value:g,fill:h,x:c.x,y:c.y+(r()==="outside"?-i():i()),capHeight:".6rem",textAnchor:"middle",verticalAnchor:r()==="center"?"middle":r()==="outside"?"end":"start"}}const u=x(()=>Ne("labels-g"));kr(t,{get class(){return o(u)},children:(c,f)=>{s4(c,{get data(){return e.data},get x(){return e.x},get y(){return e.y},children:(d,g)=>{let m=()=>g==null?void 0:g().points;var v=Ce(),w=ce(v);$t(w,19,m,(b,S)=>a()(b.data,S),(b,S)=>{var A=Ce();const N=x(()=>Jt(l(o(S)),"labels-text"));var I=ce(A);{var T=C=>{var _=Ce(),M=ce(_);Xe(M,()=>e.children,()=>({data:o(S),textProps:o(N)})),Q(C,_)},L=C=>{const _=x(()=>Le("text-xs",r()==="inside"?"fill-surface-300 stroke-surface-content":"fill-surface-content stroke-surface-100",o(N).class,e.class));Za(C,qe(()=>o(N),()=>s,{get class(){return o(_)}}))};ue(I,C=>{e.children?C(T):C(L,!1)})}Q(b,A)}),Q(d,v)},$$slots:{default:!0}})},$$slots:{default:!0}}),at()}var u4=rt("<!> <!>",1),c4=rt("<!> <!>",1),f4=rt("<!> <!>",1);function h4(t,e){it(e,!0);let n=P(e,"seriesState",7),r=P(e,"canHaveTotal",3,!1);const i=Gt();var a=Ce(),s=ce(a);pi(s,()=>Wm,(l,u)=>{u(l,qe({get context(){return i}},()=>{var f;return(f=e.tooltipProps)==null?void 0:f.root},{children:(f,h)=>{let d=()=>h==null?void 0:h().data,g=()=>h==null?void 0:h().payload;var m=f4(),v=ce(m);pi(v,()=>k6,(b,S)=>{S(b,qe({get value(){return g()[0].label},get format(){return jt}},()=>{var A;return(A=e.tooltipProps)==null?void 0:A.header}))});var w=Fe(v,2);pi(w,()=>S6,(b,S)=>{S(b,qe(()=>{var A;return(A=e.tooltipProps)==null?void 0:A.list},{children:(A,N)=>{var I=c4(),T=ce(I);$t(T,19,g,(_,M)=>_.key??M,(_,M)=>{var k=Ce(),p=ce(k);pi(p,()=>W0,(R,z)=>{z(R,qe({get label(){return o(M).name},get value(){return o(M).value},get color(){return o(M).color},get format(){return jt},valueAlign:"right",onpointerenter:()=>n().highlightKey.current=o(M).key,onpointerleave:()=>n().highlightKey.current=null},()=>{var D;return(D=e.tooltipProps)==null?void 0:D.item}))}),Q(_,k)});var L=Fe(T,2);{var C=_=>{var M=u4(),k=ce(M);pi(k,()=>T6,(z,D)=>{D(z,qe(()=>{var W;return(W=e.tooltipProps)==null?void 0:W.separator},{children:void 0}))});var p=Fe(k,2);const R=x(()=>hv(n().visibleSeries,z=>{const D=z.data?$l(z.data,d(),i.x):d();return Yt(z.value??(z.data?i.y:z.key))(D)}));pi(p,()=>W0,(z,D)=>{D(z,qe({label:"total",get value(){return o(R)},format:"integer",valueAlign:"right"},()=>{var W;return(W=e.tooltipProps)==null?void 0:W.item}))}),Q(_,M)};ue(L,_=>{var M;r()&&g().length>1&&!((M=e.tooltipProps)!=null&&M.hideTotal)&&_(C)})}Q(A,I)},$$slots:{default:!0}}))}),Q(f,m)},$$slots:{default:!0}}))}),Q(t,a),at()}var d4=rt("<!> <!>",1);function g4(t,e){it(e,!0);const n=P(e,"labelPlacement",3,"top-right"),r=P(e,"labelXOffset",3,0),i=P(e,"labelYOffset",3,0),a=Gt(),s=x(()=>e.x!=null),l=x(()=>({x1:e.x?a.xScale(e.x):a.xRange[0],y1:e.y&&!e.x?a.yScale(e.y):a.yRange[0],x2:e.x?a.xScale(e.x):a.xRange[1],y2:e.y?a.yScale(e.y):a.yRange[1]})),u=x(()=>o(s)?{x:o(l).x1+(n().includes("left")?-r():r()),y:(n().includes("top")?o(l).y2:n().includes("bottom")?o(l).y1:(o(l).y1-o(l).y2)/2)+(["top","bottom-left","bottom-right"].includes(n())?-i():i()),dy:-2,textAnchor:n().includes("left")?"end":n().includes("right")?"start":"middle",verticalAnchor:n()==="top"?"end":n()==="bottom"||n().includes("top")?"start":n().includes("bottom")?"end":"middle"}:{x:(n().includes("left")?o(l).x1:n().includes("right")?o(l).x2:(o(l).x2-o(l).x1)/2)+(["left","top-right","bottom-right"].includes(n())?-r():r()),y:o(l).y1+(n().includes("top")?-i():i()),dy:-2,textAnchor:n()==="left"?"end":n()==="right"||n().includes("left")?"start":n().includes("right")?"end":"middle",verticalAnchor:n().includes("top")?"end":n().includes("bottom")?"start":"middle"});var c=d4(),f=ce(c);const h=x(()=>{var m,v;return Le("stroke-surface-content",(v=(m=e.props)==null?void 0:m.line)==null?void 0:v.class)});si(f,qe({get x1(){return o(l).x1},get y1(){return o(l).y1},get x2(){return o(l).x2},get y2(){return o(l).y2}},()=>{var m;return(m=e.props)==null?void 0:m.line},{get class(){return o(h)}}));var d=Fe(f,2);{var g=m=>{const v=x(()=>{var w,b;return Le("text-xs pointer-events-none",(b=(w=e.props)==null?void 0:w.label)==null?void 0:b.class)});Za(m,qe({get value(){return e.label}},()=>o(u),()=>{var w;return(w=e.props)==null?void 0:w.label},{get class(){return o(v)}}))};ue(d,m=>{e.label&&m(g)})}Q(t,c),at()}var m4=rt("<!> <!>",1);function y4(t,e){it(e,!0);const n=P(e,"r",3,4),r=P(e,"labelPlacement",3,"center"),i=P(e,"labelXOffset",3,0),a=P(e,"labelYOffset",3,0),s=Gt(),l=x(()=>({x:e.x?s.xScale(e.x)+(ht(s.xScale)?s.xScale.bandwidth()/2:0):0,y:e.y?s.yScale(e.y)+(ht(s.yScale)?s.yScale.bandwidth()/2:0):s.height})),u=x(()=>({x:o(l).x+((["top","center","bottom"].includes(r())?0:n())+i())*(r().includes("left")?-1:1),y:o(l).y+((["left","center","right"].includes(r())?0:n())+a())*(r().includes("top")?-1:1),dy:-2,textAnchor:r().includes("left")?"end":r().includes("right")?"start":"middle",verticalAnchor:r().includes("top")?"end":r().includes("bottom")?"start":"middle"}));var c=m4(),f=ce(c);const h=x(()=>{var m,v;return Le("stroke-surface-100",(v=(m=e.props)==null?void 0:m.circle)==null?void 0:v.class)});aa(f,qe({get cx(){return o(l).x},get cy(){return o(l).y},get r(){return n()},onpointermove:m=>{e.details&&(m.stopPropagation(),s.tooltip.show(m,{annotation:{label:e.label,details:e.details}}))},onpointerleave:()=>{e.details&&s.tooltip.hide()}},()=>{var m;return(m=e.props)==null?void 0:m.circle},{get class(){return o(h)}}));var d=Fe(f,2);{var g=m=>{const v=x(()=>{var w,b;return Le("text-xs pointer-events-none",(b=(w=e.props)==null?void 0:w.label)==null?void 0:b.class)});Za(m,qe({get value(){return e.label}},()=>o(u),()=>{var w;return(w=e.props)==null?void 0:w.label},{get class(){return o(v)}}))};ue(d,m=>{e.label&&m(g)})}Q(t,c),at()}var v4=xt("<stop></stop>"),_4=xt("<stop></stop>"),x4=xt("<defs><linearGradient><!></linearGradient></defs><!>",1);function b4(t,e){const n=Mr();it(e,!0);let r=P(e,"id",19,()=>Rn("linearGradient-",n)),i=P(e,"stops",19,()=>["var(--tw-gradient-from)","var(--tw-gradient-to)"]),a=P(e,"vertical",3,!1),s=P(e,"x1",3,"0%"),l=P(e,"y1",3,"0%"),u=P(e,"x2",19,()=>a()?"0%":"100%"),c=P(e,"y2",19,()=>a()?"100%":"0%"),f=P(e,"units",3,"objectBoundingBox"),h=P(e,"ref",15),d=bt(e,["$$slots","$$events","$$legacy","id","stops","vertical","x1","y1","x2","y2","rotate","units","ref","class","stopsContent","children"]),g=Ve(void 0);vt(()=>{h(o(g))});const m=Gt(),v=Fr();let w=Ve(void 0);function b(T){const L=i().map((_,M)=>{if(Array.isArray(_)){const{fill:k}=hl(T.canvas,{styles:{fill:_[1]},classes:e.class});return{offset:cS(_[0]),color:k}}else{const{fill:k}=hl(T.canvas,{styles:{fill:_},classes:e.class});return{offset:M/(i().length-1),color:k}}}),C=up(T,m.padding.left,m.padding.top,a()?m.padding.left:m.width-m.padding.right,a()?m.height+m.padding.bottom:m.padding.top,L);Re(w,C,!0)}v==="canvas"&&mi({name:"Gradient",render:b,deps:()=>[s(),l(),u(),c(),i(),e.class]});var S=Ce(),A=ce(S);{var N=T=>{var L=Ce(),C=ce(L),_=hf(()=>({id:r(),gradient:o(w)}));Xe(C,()=>e.children??Rt,()=>o(_)),Q(T,L)},I=(T,L)=>{{var C=_=>{var M=x4(),k=ce(M),p=nt(k);wt(p,B=>({id:r(),x1:s(),y1:l(),x2:u(),y2:c(),gradientTransform:e.rotate?`rotate(${e.rotate})`:"",gradientUnits:f(),...B}),[()=>Jt(d,"linear-gradient")]);var R=nt(p);{var z=B=>{var H=Ce(),$=ce(H);Xe($,()=>e.stopsContent??Rt),Q(B,H)},D=(B,H)=>{{var $=oe=>{var ye=Ce(),ge=ce(ye);$t(ge,17,i,Pn,(Ee,xe,te)=>{var ke=Ce(),Se=ce(ke);{var we=V=>{var K=v4();mt(G=>{ft(K,"offset",o(xe)[0]),ft(K,"stop-color",o(xe)[1]),St(K,0,G)},[()=>pt(Le(Ne("linear-gradient-stop"),e.class))]),Q(V,K)},se=V=>{var K=_4();mt(G=>{ft(K,"offset",`${te*(100/(i().length-1))}%`),ft(K,"stop-color",o(xe)),St(K,0,G)},[()=>pt(Le(Ne("linear-gradient-stop"),e.class))]),Q(V,K)};ue(Se,V=>{Array.isArray(o(xe))?V(we):V(se,!1)})}Q(Ee,ke)}),Q(oe,ye)};ue(B,oe=>{i()&&oe($)},H)}};ue(R,B=>{e.stopsContent?B(z):B(D,!1)})}$e(p),Wt(p,B=>Re(g,B),()=>o(g)),$e(k);var W=Fe(k);Xe(W,()=>e.children??Rt,()=>({id:r(),gradient:`url(#${r()})`})),Q(_,M)};ue(T,_=>{v==="svg"&&_(C)},L)}};ue(A,T=>{v==="canvas"?T(N):T(I,!1)})}Q(t,S),at()}var k4=xt("<rect></rect>"),w4=xt('<path fill="none"></path>'),M4=xt("<circle></circle>"),p4=xt("<!><!><!>",1),S4=xt("<defs><pattern><!></pattern></defs><!>",1);function A4(t,e){const n=Mr();it(e,!0);let r=P(e,"id",19,()=>Rn("pattern-",n)),i=P(e,"size",3,4),a=P(e,"width",19,i),s=P(e,"height",19,i),l=bt(e,["$$slots","$$events","$$legacy","id","size","width","height","lines","circles","background","patternContent","children"]);const u=Fr();let c=Ve(null),f=Xo([]);if(e.lines){const w=Array.isArray(e.lines)?e.lines:e.lines===!0?[{}]:[e.lines];for(const b of w){const S=b.color??"var(--color-surface-content)",A=b.width??1,N=b.opacity??1;let I=Math.round(b.rotate??0)%360;I>180?I=I-360:I>90?I=I-180:I<-180?I=I+360:I<-90&&(I=I+180);let T="";I===0?T=`
        M 0 0 L ${a()} 0
        M 0 ${s()} L ${a()} ${s()}
    `:I===90?T=`
        M 0 0 L 0 ${s()}
        M ${a()} 0 L ${a()} ${s()}
    `:I>0?T=`
          M 0 ${-s()} L ${a()*2} ${s()}
          M ${-a()} ${-s()} L ${a()} ${s()}
          M ${-a()} 0 L ${a()} ${s()*2}
      `:T=`
          M ${-a()} ${s()} L ${a()} ${-s()}
          M ${-a()} ${s()*2} L ${a()*2} ${-s()}
          M 0 ${s()*2} L ${a()*2} 0
      `,f.push({type:"line",path:T,stroke:S,strokeWidth:A,opacity:N})}}if(e.circles){const w=Array.isArray(e.circles)?e.circles:e.circles===!0?[{}]:[e.circles];for(const b of w)b.stagger?f.push({type:"circle",cx:i()/4,cy:i()/4,r:b.radius??1,fill:b.color??"var(--color-surface-content)",opacity:b.opacity??1},{type:"circle",cx:i()*3/4,cy:i()*3/4,r:b.radius??1,fill:b.color??"var(--color-surface-content)",opacity:b.opacity??1}):f.push({type:"circle",cx:i()/2,cy:i()/2,r:b.radius??1,fill:b.color??"var(--color-surface-content)",opacity:b.opacity??1})}function h(w){const b=fp(w,a(),s(),f,e.background);Re(c,b,!0)}u==="canvas"&&mi({name:"Pattern",render:h,deps:()=>[a(),s(),f,e.background]});var d=Ce(),g=ce(d);{var m=w=>{var b=Ce(),S=ce(b),A=hf(()=>({id:r(),pattern:o(c)}));Xe(S,()=>e.children??Rt,()=>o(A)),Q(w,b)},v=(w,b)=>{{var S=A=>{var N=S4(),I=ce(N),T=nt(I);wt(T,k=>({id:r(),width:a(),height:s(),patternUnits:"userSpaceOnUse",...k}),[()=>Jt(l,"pattern")]);var L=nt(T);{var C=k=>{var p=Ce(),R=ce(p);Xe(R,()=>e.patternContent??Rt),Q(k,p)},_=k=>{var p=p4(),R=ce(p);{var z=B=>{var H=k4();mt(()=>{ft(H,"width",a()),ft(H,"height",s()),ft(H,"fill",e.background)}),Q(B,H)};ue(R,B=>{e.background&&B(z)})}var D=Fe(R);$t(D,17,()=>f.filter(B=>B.type==="line"),Pn,(B,H)=>{var $=w4();mt(()=>{ft($,"d",o(H).path),ft($,"stroke",o(H).stroke),ft($,"stroke-width",o(H).strokeWidth),ft($,"opacity",o(H).opacity)}),Q(B,$)});var W=Fe(D);$t(W,17,()=>f.filter(B=>B.type==="circle"),Pn,(B,H)=>{var $=M4();mt(()=>{ft($,"cx",o(H).cx),ft($,"cy",o(H).cy),ft($,"r",o(H).r),ft($,"fill",o(H).fill),ft($,"opacity",o(H).opacity)}),Q(B,$)}),Q(k,p)};ue(L,k=>{e.patternContent?k(C):k(_,!1)})}$e(T),$e(I);var M=Fe(I);Xe(M,()=>e.children??Rt,()=>({id:r(),pattern:`url(#${r()})`})),Q(A,N)};ue(w,A=>{u==="svg"&&A(S)},b)}};ue(g,w=>{u==="canvas"?w(m):w(v,!1)})}Q(t,d),at()}var T4=rt("<!> <!> <!> <!>",1);function C4(t,e){it(e,!0);const n=P(e,"labelPlacement",3,"center"),r=P(e,"labelXOffset",3,0),i=P(e,"labelYOffset",3,0),a=Gt(),s=x(()=>({x:e.x?a.xScale(e.x[0]??a.xDomain[0])-(ht(a.xScale)?a.xScale.padding()*a.xScale.step()/2:0):a.xRange[0],y:e.y?a.yScale(e.y[1]??a.yDomain[1]):a.yRange[1],width:e.x?a.xScale(e.x[1]??a.xDomain[1])-a.xScale(e.x[0]??a.xDomain[0])+(ht(a.xScale)?a.xScale.step():0):a.width,height:e.y?a.yScale(e.y[0]??a.yDomain[0])-a.yScale(e.y[1]??a.yDomain[1]):a.height})),l=x(()=>({x:((n().includes("left")?o(s).x:n().includes("right")?(o(s).x??0)+o(s).width:(o(s).x??0)+o(s).width/2)??0)+(n().includes("right")?-r():r()),y:((n().includes("top")?o(s).y:n().includes("bottom")?(o(s).y??0)+o(s).height:(o(s).y??0)+o(s).height/2)??0)+(n().includes("bottom")?-i():i()),dy:-2,textAnchor:n().includes("left")?"start":n().includes("right")?"end":"middle",verticalAnchor:n().includes("top")?"start":n().includes("bottom")?"end":"middle"}));var u=T4(),c=ce(u);{var f=b=>{const S=x(()=>{var A,N;return Le((N=(A=e.props)==null?void 0:A.rect)==null?void 0:N.class,e.class)});Da(b,qe(()=>o(s),()=>{var A;return(A=e.props)==null?void 0:A.rect},{get fill(){return e.fill},get class(){return o(S)}}))};ue(c,b=>{(e.fill||e.class)&&b(f)})}var h=Fe(c,2);{var d=b=>{b4(b,qe(()=>e.gradient,{children:(A,N)=>{let I=()=>N==null?void 0:N().gradient;Da(A,qe(()=>o(s),()=>{var T;return(T=e.props)==null?void 0:T.rect},{get fill(){return I()}}))},$$slots:{default:!0}}))};ue(h,b=>{e.gradient&&b(d)})}var g=Fe(h,2);{var m=b=>{A4(b,qe(()=>e.pattern,{children:(A,N)=>{let I=()=>N==null?void 0:N().pattern;Da(A,qe(()=>o(s),()=>{var T;return(T=e.props)==null?void 0:T.rect},{get fill(){return I()}}))},$$slots:{default:!0}}))};ue(g,b=>{e.pattern&&b(m)})}var v=Fe(g,2);{var w=b=>{const S=x(()=>{var A,N;return Le("text-xs pointer-events-none",(N=(A=e.props)==null?void 0:A.label)==null?void 0:N.class)});Za(b,qe({get value(){return e.label}},()=>o(l),()=>{var A;return(A=e.props)==null?void 0:A.label},{get class(){return o(S)}}))};ue(v,b=>{e.label&&b(w)})}Q(t,u),at()}function Y0(t,e){it(e,!0);let n=x(()=>e.annotations.filter(a=>(a.layer===e.layer||a.layer==null&&e.layer==="above")&&(e.highlightKey==null||a.seriesKey==null||a.seriesKey===e.highlightKey)&&e.visibleSeries.some(s=>a.seriesKey==null||a.seriesKey===s.key)));var r=Ce(),i=ce(r);$t(i,17,()=>o(n),Pn,(a,s)=>{var l=Ce(),u=ce(l);{var c=h=>{y4(h,qe(()=>o(s)))},f=(h,d)=>{{var g=v=>{g4(v,qe(()=>o(s)))},m=(v,w)=>{{var b=S=>{C4(S,qe(()=>o(s)))};ue(v,S=>{o(s).type==="range"&&S(b)},w)}};ue(h,v=>{o(s).type==="line"?v(g):v(m,!1)},d)}};ue(u,h=>{o(s).type==="point"?h(c):h(f,!1)})}Q(a,l)}),Q(t,r),at()}function D4(t,e){it(e,!0);let n=P(e,"key",3,(h,d)=>d),r=P(e,"onBarClick",3,()=>{}),i=P(e,"radius",3,0),a=P(e,"strokeWidth",3,0),s=P(e,"stroke",3,"black"),l=bt(e,["$$slots","$$events","$$legacy","fill","key","data","onBarClick","children","radius","strokeWidth","stroke"]);const u=Gt(),c=x(()=>Ta(e.data??u.data)),f=x(()=>Ne("bars"));kr(t,{get class(){return o(f)},children:(h,d)=>{var g=Ce(),m=ce(g);{var v=b=>{var S=Ce(),A=ce(S);Xe(A,()=>e.children),Q(b,S)},w=b=>{var S=Ce(),A=ce(S);$t(A,19,()=>o(c),(N,I)=>n()(N,I),(N,I)=>{const T=x(()=>e.fill??(u.config.c?u.cGet(o(I)):null));var L=x(()=>Jt(l,"bars-bar"));Im(N,qe({get data(){return o(I)},get radius(){return i()},get strokeWidth(){return a()},get stroke(){return s()},get fill(){return o(T)},onclick:C=>r()(C,{data:o(I)})},()=>o(L)))}),Q(b,S)};ue(m,b=>{e.children?b(v):b(w,!1)})}Q(h,g)},$$slots:{default:!0}}),at()}var P4=rt("<!> <!> <!>",1),R4=rt("<!> <!>",1),E4=rt("<!> <!> <!>",1),O4=rt("<!> <!> <!>",1),N4=rt("<!> <!> <!> <!> <!>",1),L4=rt("<!> <!> <!> <!> <!>",1);function D8(t,e){it(e,!0);let n=P(e,"data",19,()=>[]),r=P(e,"xDomain",7),i=P(e,"radial",3,!1),a=P(e,"orientation",3,"vertical"),s=P(e,"seriesLayout",3,"overlap"),l=P(e,"axis",3,!0),u=P(e,"brush",3,!1),c=P(e,"grid",3,!0),f=P(e,"labels",3,!1),h=P(e,"legend",3,!1);P(e,"points",3,!1);let d=P(e,"rule",3,!0),g=P(e,"onTooltipClick",3,()=>{}),m=P(e,"onBarClick",3,()=>{}),v=P(e,"props",19,()=>({})),w=P(e,"renderContext",3,"svg"),b=P(e,"profile",3,!1),S=P(e,"debug",3,!1),A=P(e,"bandPadding",19,()=>i()?0:.4),N=P(e,"groupPadding",3,0),I=P(e,"stackPadding",3,0),T=P(e,"tooltip",3,!0),L=P(e,"highlight",3,!0),C=P(e,"annotations",19,()=>[]),_=P(e,"context",15),M=bt(e,["$$slots","$$events","$$legacy","data","x","y","xDomain","radial","orientation","series","seriesLayout","axis","brush","grid","labels","legend","points","rule","onTooltipClick","onBarClick","props","renderContext","profile","debug","xScale","yScale","bandPadding","groupPadding","stackPadding","tooltip","children","aboveContext","belowContext","belowMarks","aboveMarks","marks","highlight","annotations","context"]);const k=x(()=>e.series===void 0?[{key:"default",label:a()==="vertical"?typeof e.y=="string"?e.y:"value":typeof e.x=="string"?e.x:"value",value:a()==="vertical"?e.y:e.x}]:e.series),p=new R6(()=>o(k)),R=x(()=>a()==="vertical"),z=x(()=>s().startsWith("stack")),D=x(()=>s()==="group"),W=x(()=>{let pe=p.allSeriesData.length?p.allSeriesData:Ta(n());if(o(z)){const Ke=p.visibleSeries.map(ot=>ot.key),Ge=s()==="stackExpand"?Bk:s()==="stackDiverging"?Yk:tl,ut=Fk().keys(Ke).value((ot,Ze)=>{const Et=o(k).find(rn=>rn.key===Ze);return Yt(Et.value??Et.key)(ot)}).offset(Ge)(Ta(n()));pe=pe.map((ot,Ze)=>({...ot,stackData:ut.map(Et=>Et[Ze])}))}return pe}),B=x(()=>e.xScale??(o(R)?wa().padding(A()):Yt(e.x)(o(W)[0])instanceof Date?Bd():Ni())),H=x(()=>o(R)||Wc(o(B))?void 0:0),$=x(()=>e.yScale??(o(R)?Yt(e.y)(o(W)[0])instanceof Date?Bd():Ni():wa().padding(A()))),oe=x(()=>o(R)||Wc(o($))?0:void 0),ye=x(()=>o(D)&&o(R)?wa().padding(N()):void 0),ge=x(()=>o(D)&&o(R)?p.visibleSeries.map(pe=>pe.key):void 0),Ee=x(()=>o(D)&&o(R)?({xScale:pe})=>[0,pe.bandwidth()]:void 0),xe=x(()=>o(D)&&!o(R)?wa().padding(N()):void 0),te=x(()=>o(D)&&!o(R)?p.visibleSeries.map(pe=>pe.key):void 0),ke=x(()=>o(D)&&!o(R)?({yScale:pe})=>[0,pe.bandwidth()]:void 0);function Se(pe){return pe&&typeof pe=="object"&&"stackData"in pe}function we(pe,Ke){var rn,qt;const Ge=Ke==0,ut=Ke==p.visibleSeries.length-1,ot=s().startsWith("stack");let Ze;if(ot){const Ot=I()/2;o(R)?Ze={bottom:Ge?void 0:Ot,top:ut?void 0:Ot}:Ze={left:Ge?void 0:Ot,right:ut?void 0:Ot}}const Et=o(z)?Ot=>Ot.stackData[Ke]:pe.value??(pe.data?void 0:pe.key);return{data:pe.data,x:o(R)?void 0:Et,y:o(R)?Et:void 0,x1:o(R)&&o(D)?Ot=>pe.value??pe.key:void 0,y1:!o(R)&&o(D)?Ot=>pe.value??pe.key:void 0,rounded:ot&&Ke!==p.visibleSeries.length-1?"none":Array.isArray(e.x)||Array.isArray(e.y)?"all":"edge",radius:4,strokeWidth:1,insets:Ze,fill:pe.color,onBarClick:(Ot,un)=>m()(Ot,{...un,series:pe}),...v().bars,...pe.props,class:Le("transition-opacity",p.highlightKey.current&&p.highlightKey.current!==pe.key&&"opacity-10",(rn=v().bars)==null?void 0:rn.class,(qt=pe.props)==null?void 0:qt.class)}}function se(pe,Ke){var Ge;return{...v().labels,...typeof f()=="object"?f():null,class:Le("stroke-surface-200 transition-opacity",p.highlightKey.current&&p.highlightKey.current!==pe.key&&"opacity-10",(Ge=v().labels)==null?void 0:Ge.class,typeof f()=="object"&&f().class)}}const V=x(()=>({...typeof u()=="object"?u():null,...v().brush}));function K(){return E6({seriesState:p,props:{...v().legend,...typeof h()=="object"?h():null}})}function G(){return{x:!o(R)||i(),y:o(R)||i(),...typeof c()=="object"?c():null,...v().grid}}function be(){return{area:!0,...v().highlight}}function _e(pe){return pe==="y"?{placement:i()?"radius":"left",format:o(R)&&s()==="stackExpand"?"percentRound":void 0,...typeof l()=="object"?l():null,...v().yAxis}:{placement:i()?"angle":"bottom",format:!o(R)&&s()==="stackExpand"?"percentRound":void 0,...typeof l()=="object"?l():null,...v().xAxis}}function ae(){return{x:o(R)?!1:0,y:o(R)?0:!1,...typeof d()=="object"?d():null,...v().rule}}b()&&(console.time("BarChart render"),df(()=>{console.timeEnd("BarChart render")})),_S({type:"bar",get orientation(){return a()},get stackSeries(){return o(z)},get visibleSeries(){return p.visibleSeries}});function J(pe){return pe||(o(z)?Ke=>Se(Ke)?p.visibleSeries.flatMap((Ge,ut)=>Ke.stackData[ut]):void 0:p.visibleSeries.map(Ke=>Ke.value??Ke.key))}const de=x(()=>J(e.x)),De=x(()=>a()==="horizontal"),Z=x(()=>J(e.y)),ne=x(()=>a()==="vertical"),he=x(()=>o(R)?e.y:e.x),Te=x(()=>i()?void 0:bk(l(),h())),ve=x(()=>{var pe;return T()===!1?!1:{mode:"band",onclick:g(),debug:S(),...(pe=v().tooltip)==null?void 0:pe.context}}),Ie=x(()=>u()&&(u()===!0||u().mode==null||u().mode==="integrated")?{axis:"x",resetOnEnd:!0,xDomain:r(),...o(V),onBrushEnd:pe=>{var Ke,Ge;r(pe.xDomain),(Ge=(Ke=o(V)).onBrushEnd)==null||Ge.call(Ke,pe)}}:!1);HS(t,qe({get data(){return o(W)},get x(){return o(de)},get xDomain(){return r()},get xScale(){return o(B)},get xBaseline(){return o(H)},get xNice(){return o(De)},get x1Scale(){return o(ye)},get x1Domain(){return o(ge)},get x1Range(){return o(Ee)},get y(){return o(Z)},get yScale(){return o($)},get yBaseline(){return o(oe)},get yNice(){return o(ne)},get y1Scale(){return o(xe)},get y1Domain(){return o(te)},get y1Range(){return o(ke)},get c(){return o(he)},cRange:["var(--color-primary)"],get radial(){return i()},get padding(){return o(Te)}},()=>M,{get tooltip(){return o(ve)},get brush(){return o(Ie)},get context(){return _()},set context(Ke){_(Ke)},children:(Ke,Ge)=>{let ut=()=>Ge==null?void 0:Ge().context;var ot=Ce();const Ze=x(()=>({context:ut(),series:o(k),visibleSeries:p.visibleSeries,getBarsProps:we,getLabelsProps:se,getLegendProps:K,getGridProps:G,getHighlightProps:be,getAxisProps:_e,getRuleProps:ae,highlightKey:p.highlightKey.current,setHighlightKey:p.highlightKey.set}));var Et=ce(ot);{var rn=Ot=>{var un=Ce(),Zt=ce(un);Xe(Zt,()=>e.children,()=>o(Ze)),Q(Ot,un)},qt=Ot=>{var un=L4(),Zt=ce(un);Xe(Zt,()=>e.belowContext??Rt,()=>o(Ze));var En=Fe(Zt,2),Sn=x(()=>w()==="canvas"?v().canvas:v().svg);s6(En,qe({get type(){return w()}},()=>o(Sn),{get center(){return i()},get debug(){return S()},children:(en,gn)=>{var An=N4(),mn=ce(An);{var hr=tn=>{var O=Ce(),j=ce(O);Xe(j,c,()=>o(Ze)),Q(tn,O)},Hr=(tn,O)=>{{var j=F=>{var U=x(G);X6(F,qe(()=>o(U)))};ue(tn,F=>{c()&&F(j)},O)}};ue(mn,tn=>{typeof c()=="function"?tn(hr):tn(Hr,!1)})}var xi=Fe(mn,2);const qr=x(()=>!u());jc(xi,{get disabled(){return o(qr)},children:(tn,O)=>{var j=P4(),F=ce(j);Y0(F,{get annotations(){return C()},layer:"below",get highlightKey(){return p.highlightKey.current},get visibleSeries(){return p.visibleSeries}});var U=Fe(F,2);Xe(U,()=>e.belowMarks??Rt,()=>o(Ze));var q=Fe(U,2);{var X=Pe=>{var Be=Ce(),Ue=ce(Be);Xe(Ue,()=>e.marks,()=>o(Ze)),Q(Pe,Be)},fe=Pe=>{var Be=Ce(),Ue=ce(Be);$t(Ue,19,()=>p.visibleSeries,Je=>Je.key,(Je,et,ct)=>{var kt=x(()=>we(o(et),o(ct)));D4(Je,qe(()=>o(kt)))}),Q(Pe,Be)};ue(q,Pe=>{typeof e.marks=="function"?Pe(X):Pe(fe,!1)})}Q(tn,j)},$$slots:{default:!0}});var Gn=Fe(xi,2);Xe(Gn,()=>e.aboveMarks??Rt,()=>o(Ze));var Sr=Fe(Gn,2);{var Ar=tn=>{var O=R4(),j=ce(O);Xe(j,l,()=>o(Ze));var F=Fe(j,2);{var U=X=>{var fe=Ce(),Pe=ce(fe);Xe(Pe,d,()=>o(Ze)),Q(X,fe)},q=(X,fe)=>{{var Pe=Be=>{var Ue=x(ae);Ri(Be,qe(()=>o(Ue)))};ue(X,Be=>{d()&&Be(Pe)},fe)}};ue(F,X=>{typeof d()=="function"?X(U):X(q,!1)})}Q(tn,O)},Ur=(tn,O)=>{{var j=F=>{var U=E4(),q=ce(U);{var X=et=>{var ct=x(()=>_e("y"));z0(et,qe(()=>o(ct)))};ue(q,et=>{l()!=="x"&&et(X)})}var fe=Fe(q,2);{var Pe=et=>{var ct=x(()=>_e("x"));z0(et,qe(()=>o(ct)))};ue(fe,et=>{l()!=="y"&&et(Pe)})}var Be=Fe(fe,2);{var Ue=et=>{var ct=Ce(),kt=ce(ct);Xe(kt,d,()=>o(Ze)),Q(et,ct)},Je=(et,ct)=>{{var kt=wn=>{var yn=x(ae);Ri(wn,qe(()=>o(yn)))};ue(et,wn=>{d()&&wn(kt)},ct)}};ue(Be,et=>{typeof d()=="function"?et(Ue):et(Je,!1)})}Q(F,U)};ue(tn,F=>{l()&&F(j)},O)}};ue(Sr,tn=>{typeof l()=="function"?tn(Ar):tn(Ur,!1)})}var On=Fe(Sr,2);const bi=x(()=>!u());jc(On,{get disabled(){return o(bi)},full:!0,children:(tn,O)=>{var j=O4(),F=ce(j);{var U=Ue=>{var Je=Ce(),et=ce(Je);Xe(et,L,()=>o(Ze)),Q(Ue,Je)},q=(Ue,Je)=>{{var et=ct=>{var kt=x(be);Q6(ct,qe(()=>o(kt)))};ue(Ue,ct=>{L()&&ct(et)},Je)}};ue(F,Ue=>{typeof L()=="function"?Ue(U):Ue(q,!1)})}var X=Fe(F,2);{var fe=Ue=>{var Je=Ce(),et=ce(Je);Xe(et,f,()=>o(Ze)),Q(Ue,Je)},Pe=(Ue,Je)=>{{var et=ct=>{var kt=Ce(),wn=ce(kt);$t(wn,19,()=>p.visibleSeries,yn=>yn.key,(yn,ca,Tn)=>{var Mn=x(()=>se(o(ca),o(Tn)));l4(yn,qe(()=>o(Mn)))}),Q(ct,kt)};ue(Ue,ct=>{f()&&ct(et)},Je)}};ue(X,Ue=>{typeof f()=="function"?Ue(fe):Ue(Pe,!1)})}var Be=Fe(X,2);Y0(Be,{get annotations(){return C()},layer:"above",get highlightKey(){return p.highlightKey.current},get visibleSeries(){return p.visibleSeries}}),Q(tn,j)},$$slots:{default:!0}}),Q(en,An)},$$slots:{default:!0}}));var Ct=Fe(En,2);Xe(Ct,()=>e.aboveContext??Rt,()=>o(Ze));var pr=Fe(Ct,2);{var Fi=en=>{var gn=Ce(),An=ce(gn);Xe(An,h,()=>o(Ze)),Q(en,gn)},Yr=(en,gn)=>{{var An=mn=>{var hr=x(K);_6(mn,qe(()=>o(hr)))};ue(en,mn=>{h()&&mn(An)},gn)}};ue(pr,en=>{typeof h()=="function"?en(Fi):en(Yr,!1)})}var yi=Fe(pr,2);{var vi=en=>{var gn=Ce(),An=ce(gn);Xe(An,T,()=>o(Ze)),Q(en,gn)},_i=(en,gn)=>{{var An=mn=>{const hr=x(()=>o(z)||o(D));h4(mn,{get tooltipProps(){return v().tooltip},get canHaveTotal(){return o(hr)},get seriesState(){return p}})};ue(en,mn=>{T()&&mn(An)},gn)}};ue(yi,en=>{typeof T()=="function"?en(vi):en(_i,!1)})}Q(Ot,un)};ue(Et,Ot=>{e.children?Ot(rn):Ot(qt,!1)})}Q(Ke,ot)},$$slots:{default:!0}})),at()}var W4="\0",qi="\0",H0="";let I4=class{constructor(e){dt(this,"_isDirected",!0);dt(this,"_isMultigraph",!1);dt(this,"_isCompound",!1);dt(this,"_label");dt(this,"_defaultNodeLabelFn",()=>{});dt(this,"_defaultEdgeLabelFn",()=>{});dt(this,"_nodes",{});dt(this,"_in",{});dt(this,"_preds",{});dt(this,"_out",{});dt(this,"_sucs",{});dt(this,"_edgeObjs",{});dt(this,"_edgeLabels",{});dt(this,"_nodeCount",0);dt(this,"_edgeCount",0);dt(this,"_parent");dt(this,"_children");e&&(this._isDirected=Object.hasOwn(e,"directed")?e.directed:!0,this._isMultigraph=Object.hasOwn(e,"multigraph")?e.multigraph:!1,this._isCompound=Object.hasOwn(e,"compound")?e.compound:!1),this._isCompound&&(this._parent={},this._children={},this._children[qi]={})}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(e){return this._label=e,this}graph(){return this._label}setDefaultNodeLabel(e){return this._defaultNodeLabelFn=e,typeof e!="function"&&(this._defaultNodeLabelFn=()=>e),this}nodeCount(){return this._nodeCount}nodes(){return Object.keys(this._nodes)}sources(){var e=this;return this.nodes().filter(n=>Object.keys(e._in[n]).length===0)}sinks(){var e=this;return this.nodes().filter(n=>Object.keys(e._out[n]).length===0)}setNodes(e,n){var r=arguments,i=this;return e.forEach(function(a){r.length>1?i.setNode(a,n):i.setNode(a)}),this}setNode(e,n){return Object.hasOwn(this._nodes,e)?(arguments.length>1&&(this._nodes[e]=n),this):(this._nodes[e]=arguments.length>1?n:this._defaultNodeLabelFn(e),this._isCompound&&(this._parent[e]=qi,this._children[e]={},this._children[qi][e]=!0),this._in[e]={},this._preds[e]={},this._out[e]={},this._sucs[e]={},++this._nodeCount,this)}node(e){return this._nodes[e]}hasNode(e){return Object.hasOwn(this._nodes,e)}removeNode(e){var n=this;if(Object.hasOwn(this._nodes,e)){var r=i=>n.removeEdge(n._edgeObjs[i]);delete this._nodes[e],this._isCompound&&(this._removeFromParentsChildList(e),delete this._parent[e],this.children(e).forEach(function(i){n.setParent(i)}),delete this._children[e]),Object.keys(this._in[e]).forEach(r),delete this._in[e],delete this._preds[e],Object.keys(this._out[e]).forEach(r),delete this._out[e],delete this._sucs[e],--this._nodeCount}return this}setParent(e,n){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(n===void 0)n=qi;else{n+="";for(var r=n;r!==void 0;r=this.parent(r))if(r===e)throw new Error("Setting "+n+" as parent of "+e+" would create a cycle");this.setNode(n)}return this.setNode(e),this._removeFromParentsChildList(e),this._parent[e]=n,this._children[n][e]=!0,this}_removeFromParentsChildList(e){delete this._children[this._parent[e]][e]}parent(e){if(this._isCompound){var n=this._parent[e];if(n!==qi)return n}}children(e=qi){if(this._isCompound){var n=this._children[e];if(n)return Object.keys(n)}else{if(e===qi)return this.nodes();if(this.hasNode(e))return[]}}predecessors(e){var n=this._preds[e];if(n)return Object.keys(n)}successors(e){var n=this._sucs[e];if(n)return Object.keys(n)}neighbors(e){var n=this.predecessors(e);if(n){const i=new Set(n);for(var r of this.successors(e))i.add(r);return Array.from(i.values())}}isLeaf(e){var n;return this.isDirected()?n=this.successors(e):n=this.neighbors(e),n.length===0}filterNodes(e){var n=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});n.setGraph(this.graph());var r=this;Object.entries(this._nodes).forEach(function([s,l]){e(s)&&n.setNode(s,l)}),Object.values(this._edgeObjs).forEach(function(s){n.hasNode(s.v)&&n.hasNode(s.w)&&n.setEdge(s,r.edge(s))});var i={};function a(s){var l=r.parent(s);return l===void 0||n.hasNode(l)?(i[s]=l,l):l in i?i[l]:a(l)}return this._isCompound&&n.nodes().forEach(s=>n.setParent(s,a(s))),n}setDefaultEdgeLabel(e){return this._defaultEdgeLabelFn=e,typeof e!="function"&&(this._defaultEdgeLabelFn=()=>e),this}edgeCount(){return this._edgeCount}edges(){return Object.values(this._edgeObjs)}setPath(e,n){var r=this,i=arguments;return e.reduce(function(a,s){return i.length>1?r.setEdge(a,s,n):r.setEdge(a,s),s}),this}setEdge(){var e,n,r,i,a=!1,s=arguments[0];typeof s=="object"&&s!==null&&"v"in s?(e=s.v,n=s.w,r=s.name,arguments.length===2&&(i=arguments[1],a=!0)):(e=s,n=arguments[1],r=arguments[3],arguments.length>2&&(i=arguments[2],a=!0)),e=""+e,n=""+n,r!==void 0&&(r=""+r);var l=wo(this._isDirected,e,n,r);if(Object.hasOwn(this._edgeLabels,l))return a&&(this._edgeLabels[l]=i),this;if(r!==void 0&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(e),this.setNode(n),this._edgeLabels[l]=a?i:this._defaultEdgeLabelFn(e,n,r);var u=z4(this._isDirected,e,n,r);return e=u.v,n=u.w,Object.freeze(u),this._edgeObjs[l]=u,q0(this._preds[n],e),q0(this._sucs[e],n),this._in[n][l]=u,this._out[e][l]=u,this._edgeCount++,this}edge(e,n,r){var i=arguments.length===1?sc(this._isDirected,arguments[0]):wo(this._isDirected,e,n,r);return this._edgeLabels[i]}edgeAsObj(){const e=this.edge(...arguments);return typeof e!="object"?{label:e}:e}hasEdge(e,n,r){var i=arguments.length===1?sc(this._isDirected,arguments[0]):wo(this._isDirected,e,n,r);return Object.hasOwn(this._edgeLabels,i)}removeEdge(e,n,r){var i=arguments.length===1?sc(this._isDirected,arguments[0]):wo(this._isDirected,e,n,r),a=this._edgeObjs[i];return a&&(e=a.v,n=a.w,delete this._edgeLabels[i],delete this._edgeObjs[i],U0(this._preds[n],e),U0(this._sucs[e],n),delete this._in[n][i],delete this._out[e][i],this._edgeCount--),this}inEdges(e,n){var r=this._in[e];if(r){var i=Object.values(r);return n?i.filter(a=>a.v===n):i}}outEdges(e,n){var r=this._out[e];if(r){var i=Object.values(r);return n?i.filter(a=>a.w===n):i}}nodeEdges(e,n){var r=this.inEdges(e,n);if(r)return r.concat(this.outEdges(e,n))}};function q0(t,e){t[e]?t[e]++:t[e]=1}function U0(t,e){--t[e]||delete t[e]}function wo(t,e,n,r){var i=""+e,a=""+n;if(!t&&i>a){var s=i;i=a,a=s}return i+H0+a+H0+(r===void 0?W4:r)}function z4(t,e,n,r){var i=""+e,a=""+n;if(!t&&i>a){var s=i;i=a,a=s}var l={v:i,w:a};return r&&(l.name=r),l}function sc(t,e){return wo(t,e.v,e.w,e.name)}var j4=I4,F4="2.2.4",B4={Graph:j4,version:F4};function Y4(t){var e={},n={},r=[];function i(a){if(Object.hasOwn(n,a))throw new ff;Object.hasOwn(e,a)||(n[a]=!0,e[a]=!0,t.predecessors(a).forEach(i),delete n[a],r.push(a))}if(t.sinks().forEach(i),Object.keys(e).length!==t.nodeCount())throw new ff;return r}class ff extends Error{constructor(){super(...arguments)}}Y4.CycleException=ff;var jm=H4;function H4(t,e,n){Array.isArray(e)||(e=[e]);var r=t.isDirected()?l=>t.successors(l):l=>t.neighbors(l),i=n==="post"?q4:U4,a=[],s={};return e.forEach(l=>{if(!t.hasNode(l))throw new Error("Graph does not have node: "+l);i(l,r,s,a)}),a}function q4(t,e,n,r){for(var i=[[t,!1]];i.length>0;){var a=i.pop();a[1]?r.push(a[0]):Object.hasOwn(n,a[0])||(n[a[0]]=!0,i.push([a[0],!0]),Fm(e(a[0]),s=>i.push([s,!1])))}}function U4(t,e,n,r){for(var i=[t];i.length>0;){var a=i.pop();Object.hasOwn(n,a)||(n[a]=!0,r.push(a),Fm(e(a),s=>i.push(s)))}}function Fm(t,e){for(var n=t.length;n--;)e(t[n],n,t);return t}var G4=jm,X4=V4;function V4(t,e){return G4(t,e,"post")}var Z4=jm,K4=Q4;function Q4(t,e){return Z4(t,e,"pre")}var J4={postorder:X4,preorder:K4},G0=B4,Br={Graph:G0.Graph,alg:J4,version:G0.version};Br.Graph;let Bm=Br.Graph;var fi={addBorderNode:s9,addDummyNode:Ym,applyWithChunking:su,asNonCompoundGraph:e9,buildLayerMatrix:i9,intersectRect:r9,mapValues:g9,maxRank:qm,normalizeRanks:a9,notime:f9,partition:u9,pick:d9,predecessorWeights:n9,range:Gm,removeEmptyRanks:o9,simplify:$4,successorWeights:t9,time:c9,uniqueId:Um,zipObject:$f};function Ym(t,e,n,r){let i;do i=Um(r);while(t.hasNode(i));return n.dummy=e,t.setNode(i,n),i}function $4(t){let e=new Bm().setGraph(t.graph());return t.nodes().forEach(n=>e.setNode(n,t.node(n))),t.edges().forEach(n=>{let r=e.edge(n.v,n.w)||{weight:0,minlen:1},i=t.edge(n);e.setEdge(n.v,n.w,{weight:r.weight+i.weight,minlen:Math.max(r.minlen,i.minlen)})}),e}function e9(t){let e=new Bm({multigraph:t.isMultigraph()}).setGraph(t.graph());return t.nodes().forEach(n=>{t.children(n).length||e.setNode(n,t.node(n))}),t.edges().forEach(n=>{e.setEdge(n,t.edge(n))}),e}function t9(t){let e=t.nodes().map(n=>{let r={};return t.outEdges(n).forEach(i=>{r[i.w]=(r[i.w]||0)+t.edge(i).weight}),r});return $f(t.nodes(),e)}function n9(t){let e=t.nodes().map(n=>{let r={};return t.inEdges(n).forEach(i=>{r[i.v]=(r[i.v]||0)+t.edge(i).weight}),r});return $f(t.nodes(),e)}function r9(t,e){let n=t.x,r=t.y,i=e.x-n,a=e.y-r,s=t.width/2,l=t.height/2;if(!i&&!a)throw new Error("Not possible to find intersection inside of the rectangle");let u,c;return Math.abs(a)*s>Math.abs(i)*l?(a<0&&(l=-l),u=l*i/a,c=l):(i<0&&(s=-s),u=s,c=s*a/i),{x:n+u,y:r+c}}function i9(t){let e=Gm(qm(t)+1).map(()=>[]);return t.nodes().forEach(n=>{let r=t.node(n),i=r.rank;i!==void 0&&(e[i][r.order]=n)}),e}function a9(t){let e=t.nodes().map(r=>{let i=t.node(r).rank;return i===void 0?Number.MAX_VALUE:i}),n=su(Math.min,e);t.nodes().forEach(r=>{let i=t.node(r);Object.hasOwn(i,"rank")&&(i.rank-=n)})}function o9(t){let e=t.nodes().map(s=>t.node(s).rank),n=su(Math.min,e),r=[];t.nodes().forEach(s=>{let l=t.node(s).rank-n;r[l]||(r[l]=[]),r[l].push(s)});let i=0,a=t.graph().nodeRankFactor;Array.from(r).forEach((s,l)=>{s===void 0&&l%a!==0?--i:s!==void 0&&i&&s.forEach(u=>t.node(u).rank+=i)})}function s9(t,e,n,r){let i={width:0,height:0};return arguments.length>=4&&(i.rank=n,i.order=r),Ym(t,"border",i,e)}function l9(t,e=Hm){const n=[];for(let r=0;r<t.length;r+=e){const i=t.slice(r,r+e);n.push(i)}return n}const Hm=65535;function su(t,e){if(e.length>Hm){const n=l9(e);return t.apply(null,n.map(r=>t.apply(null,r)))}else return t.apply(null,e)}function qm(t){const n=t.nodes().map(r=>{let i=t.node(r).rank;return i===void 0?Number.MIN_VALUE:i});return su(Math.max,n)}function u9(t,e){let n={lhs:[],rhs:[]};return t.forEach(r=>{e(r)?n.lhs.push(r):n.rhs.push(r)}),n}function c9(t,e){let n=Date.now();try{return e()}finally{console.log(t+" time: "+(Date.now()-n)+"ms")}}function f9(t,e){return e()}let h9=0;function Um(t){var e=++h9;return toString(t)+e}function Gm(t,e,n=1){e==null&&(e=t,t=0);let r=a=>a<e;n<0&&(r=a=>e<a);const i=[];for(let a=t;r(a);a+=n)i.push(a);return i}function d9(t,e){const n={};for(const r of e)t[r]!==void 0&&(n[r]=t[r]);return n}function g9(t,e){let n=e;return typeof e=="string"&&(n=r=>r[e]),Object.entries(t).reduce((r,[i,a])=>(r[i]=n(a,i),r),{})}function $f(t,e){return t.reduce((n,r,i)=>(n[r]=e[i],n),{})}fi.uniqueId;const{applyWithChunking:m9}=fi;var lu={longestPath:y9,slack:v9};function y9(t){var e={};function n(r){var i=t.node(r);if(Object.hasOwn(e,r))return i.rank;e[r]=!0;let a=t.outEdges(r).map(l=>l==null?Number.POSITIVE_INFINITY:n(l.w)-t.edge(l).minlen);var s=m9(Math.min,a);return s===Number.POSITIVE_INFINITY&&(s=0),i.rank=s}t.sources().forEach(n)}function v9(t,e){return t.node(e.w).rank-t.node(e.v).rank-t.edge(e).minlen}var _9=Br.Graph,Rl=lu.slack,x9=b9;function b9(t){var e=new _9({directed:!1}),n=t.nodes()[0],r=t.nodeCount();e.setNode(n,{});for(var i,a;k9(e,t)<r;)i=w9(e,t),a=e.hasNode(i.v)?Rl(t,i):-Rl(t,i),M9(e,t,a);return e}function k9(t,e){function n(r){e.nodeEdges(r).forEach(i=>{var a=i.v,s=r===a?i.w:a;!t.hasNode(s)&&!Rl(e,i)&&(t.setNode(s,{}),t.setEdge(r,s,{}),n(s))})}return t.nodes().forEach(n),t.nodeCount()}function w9(t,e){return e.edges().reduce((r,i)=>{let a=Number.POSITIVE_INFINITY;return t.hasNode(i.v)!==t.hasNode(i.w)&&(a=Rl(e,i)),a<r[0]?[a,i]:r},[Number.POSITIVE_INFINITY,null])[1]}function M9(t,e,n){t.nodes().forEach(r=>e.node(r).rank+=n)}var p9=x9,X0=lu.slack,S9=lu.longestPath,A9=Br.alg.preorder,T9=Br.alg.postorder,C9=fi.simplify;ao.initLowLimValues=th;ao.initCutValues=eh;ao.calcCutValue=Xm;ao.leaveEdge=Zm;ao.enterEdge=Km;ao.exchangeEdges=Qm;function ao(t){t=C9(t),S9(t);var e=p9(t);th(e),eh(e,t);for(var n,r;n=Zm(e);)r=Km(e,t,n),Qm(e,t,n,r)}function eh(t,e){var n=T9(t,t.nodes());n=n.slice(0,n.length-1),n.forEach(r=>D9(t,e,r))}function D9(t,e,n){var r=t.node(n),i=r.parent;t.edge(n,i).cutvalue=Xm(t,e,n)}function Xm(t,e,n){var r=t.node(n),i=r.parent,a=!0,s=e.edge(n,i),l=0;return s||(a=!1,s=e.edge(i,n)),l=s.weight,e.nodeEdges(n).forEach(u=>{var c=u.v===n,f=c?u.w:u.v;if(f!==i){var h=c===a,d=e.edge(u).weight;if(l+=h?d:-d,R9(t,n,f)){var g=t.edge(n,f).cutvalue;l+=h?-g:g}}}),l}function th(t,e){arguments.length<2&&(e=t.nodes()[0]),Vm(t,{},1,e)}function Vm(t,e,n,r,i){var a=n,s=t.node(r);return e[r]=!0,t.neighbors(r).forEach(l=>{Object.hasOwn(e,l)||(n=Vm(t,e,n,l,r))}),s.low=a,s.lim=n++,i?s.parent=i:delete s.parent,n}function Zm(t){return t.edges().find(e=>t.edge(e).cutvalue<0)}function Km(t,e,n){var r=n.v,i=n.w;e.hasEdge(r,i)||(r=n.w,i=n.v);var a=t.node(r),s=t.node(i),l=a,u=!1;a.lim>s.lim&&(l=s,u=!0);var c=e.edges().filter(f=>u===V0(t,t.node(f.v),l)&&u!==V0(t,t.node(f.w),l));return c.reduce((f,h)=>X0(e,h)<X0(e,f)?h:f)}function Qm(t,e,n,r){var i=n.v,a=n.w;t.removeEdge(i,a),t.setEdge(r.v,r.w,{}),th(t),eh(t,e),P9(t,e)}function P9(t,e){var n=t.nodes().find(i=>!e.node(i).parent),r=A9(t,n);r=r.slice(1),r.forEach(i=>{var a=t.node(i).parent,s=e.edge(i,a),l=!1;s||(s=e.edge(a,i),l=!0),e.node(i).rank=e.node(a).rank+(l?s.minlen:-s.minlen)})}function R9(t,e,n){return t.hasEdge(e,n)}function V0(t,e,n){return n.low<=e.lim&&e.lim<=n.lim}var E9=lu;E9.longestPath;fi.zipObject;Br.Graph;Br.Graph;let O9=Br.Graph,$r=fi;var N9={positionX:U9};function L9(t,e){let n={};function r(i,a){let s=0,l=0,u=i.length,c=a[a.length-1];return a.forEach((f,h)=>{let d=I9(t,f),g=d?t.node(d).order:u;(d||f===c)&&(a.slice(l,h+1).forEach(m=>{t.predecessors(m).forEach(v=>{let w=t.node(v),b=w.order;(b<s||g<b)&&!(w.dummy&&t.node(m).dummy)&&Jm(n,v,m)})}),l=h+1,s=g)}),a}return e.length&&e.reduce(r),n}function W9(t,e){let n={};function r(a,s,l,u,c){let f;$r.range(s,l).forEach(h=>{f=a[h],t.node(f).dummy&&t.predecessors(f).forEach(d=>{let g=t.node(d);g.dummy&&(g.order<u||g.order>c)&&Jm(n,d,f)})})}function i(a,s){let l=-1,u,c=0;return s.forEach((f,h)=>{if(t.node(f).dummy==="border"){let d=t.predecessors(f);d.length&&(u=t.node(d[0]).order,r(s,c,h,l,u),c=h,l=u)}r(s,c,s.length,u,a.length)}),s}return e.length&&e.reduce(i),n}function I9(t,e){if(t.node(e).dummy)return t.predecessors(e).find(n=>t.node(n).dummy)}function Jm(t,e,n){if(e>n){let i=e;e=n,n=i}let r=t[e];r||(t[e]=r={}),r[n]=!0}function z9(t,e,n){if(e>n){let r=e;e=n,n=r}return!!t[e]&&Object.hasOwn(t[e],n)}function j9(t,e,n,r){let i={},a={},s={};return e.forEach(l=>{l.forEach((u,c)=>{i[u]=u,a[u]=u,s[u]=c})}),e.forEach(l=>{let u=-1;l.forEach(c=>{let f=r(c);if(f.length){f=f.sort((d,g)=>s[d]-s[g]);let h=(f.length-1)/2;for(let d=Math.floor(h),g=Math.ceil(h);d<=g;++d){let m=f[d];a[c]===c&&u<s[m]&&!z9(n,c,m)&&(a[m]=c,a[c]=i[c]=i[m],u=s[m])}}})}),{root:i,align:a}}function F9(t,e,n,r,i){let a={},s=B9(t,e,n,i),l=i?"borderLeft":"borderRight";function u(h,d){let g=s.nodes(),m=g.pop(),v={};for(;m;)v[m]?h(m):(v[m]=!0,g.push(m),g=g.concat(d(m))),m=g.pop()}function c(h){a[h]=s.inEdges(h).reduce((d,g)=>Math.max(d,a[g.v]+s.edge(g)),0)}function f(h){let d=s.outEdges(h).reduce((m,v)=>Math.min(m,a[v.w]-s.edge(v)),Number.POSITIVE_INFINITY),g=t.node(h);d!==Number.POSITIVE_INFINITY&&g.borderType!==l&&(a[h]=Math.max(a[h],d))}return u(c,s.predecessors.bind(s)),u(f,s.successors.bind(s)),Object.keys(r).forEach(h=>a[h]=a[n[h]]),a}function B9(t,e,n,r){let i=new O9,a=t.graph(),s=G9(a.nodesep,a.edgesep,r);return e.forEach(l=>{let u;l.forEach(c=>{let f=n[c];if(i.setNode(f),u){var h=n[u],d=i.edge(h,f);i.setEdge(h,f,Math.max(s(t,c,u),d||0))}u=c})}),i}function Y9(t,e){return Object.values(e).reduce((n,r)=>{let i=Number.NEGATIVE_INFINITY,a=Number.POSITIVE_INFINITY;Object.entries(r).forEach(([l,u])=>{let c=X9(t,l)/2;i=Math.max(u+c,i),a=Math.min(u-c,a)});const s=i-a;return s<n[0]&&(n=[s,r]),n},[Number.POSITIVE_INFINITY,null])[1]}function H9(t,e){let n=Object.values(e),r=$r.applyWithChunking(Math.min,n),i=$r.applyWithChunking(Math.max,n);["u","d"].forEach(a=>{["l","r"].forEach(s=>{let l=a+s,u=t[l];if(u===e)return;let c=Object.values(u),f=r-$r.applyWithChunking(Math.min,c);s!=="l"&&(f=i-$r.applyWithChunking(Math.max,c)),f&&(t[l]=$r.mapValues(u,h=>h+f))})})}function q9(t,e){return $r.mapValues(t.ul,(n,r)=>{if(e)return t[e.toLowerCase()][r];{let i=Object.values(t).map(a=>a[r]).sort((a,s)=>a-s);return(i[1]+i[2])/2}})}function U9(t){let e=$r.buildLayerMatrix(t),n=Object.assign(L9(t,e),W9(t,e)),r={},i;["u","d"].forEach(s=>{i=s==="u"?e:Object.values(e).reverse(),["l","r"].forEach(l=>{l==="r"&&(i=i.map(h=>Object.values(h).reverse()));let u=(s==="u"?t.predecessors:t.successors).bind(t),c=j9(t,i,n,u),f=F9(t,i,c.root,c.align,l==="r");l==="r"&&(f=$r.mapValues(f,h=>-h)),r[s+l]=f})});let a=Y9(t,r);return H9(r,a),q9(r,t.graph().align)}function G9(t,e,n){return(r,i,a)=>{let s=r.node(i),l=r.node(a),u=0,c;if(u+=s.width/2,Object.hasOwn(s,"labelpos"))switch(s.labelpos.toLowerCase()){case"l":c=-s.width/2;break;case"r":c=s.width/2;break}if(c&&(u+=n?c:-c),c=0,u+=(s.dummy?e:t)/2,u+=(l.dummy?e:t)/2,u+=l.width/2,Object.hasOwn(l,"labelpos"))switch(l.labelpos.toLowerCase()){case"l":c=l.width/2;break;case"r":c=-l.width/2;break}return c&&(u+=n?c:-c),c=0,u}}function X9(t,e){return t.node(e).width}N9.positionX;fi.normalizeRanks;fi.removeEmptyRanks;Br.Graph;Br.Graph;fi.time,fi.notime;var Z0={exports:{}};(function(t,e){(function(n,r){r(e)})(Ka,function(n){function r(u){return u.k}function i(u){return[u.x,u.y]}function a(u){return function(){return u}}function s(){let u=0,c=0,f=960,h=500,d=!0,g=!0,m=256,v=r,w=i,b=0;function S(){const A=+v.apply(this,arguments),N=w.apply(this,arguments),I=Math.log2(A/m),T=Math.round(Math.max(I+b,0)),L=Math.pow(2,I-T)*m,C=+N[0]-A/2,_=+N[1]-A/2,M=Math.max(d?0:-1/0,Math.floor((u-C)/L)),k=Math.min(d?1<<T:1/0,Math.ceil((f-C)/L)),p=Math.max(g?0:-1/0,Math.floor((c-_)/L)),R=Math.min(g?1<<T:1/0,Math.ceil((h-_)/L)),z=[];for(let D=p;D<R;++D)for(let W=M;W<k;++W)z.push([W,D,T]);return z.translate=[C/L,_/L],z.scale=L,z}return S.size=function(A){return arguments.length?(u=c=0,f=+A[0],h=+A[1],S):[f-u,h-c]},S.extent=function(A){return arguments.length?(u=+A[0][0],c=+A[0][1],f=+A[1][0],h=+A[1][1],S):[[u,c],[f,h]]},S.scale=function(A){return arguments.length?(v=typeof A=="function"?A:a(+A),S):v},S.translate=function(A){return arguments.length?(w=typeof A=="function"?A:a([+A[0],+A[1]]),S):w},S.zoomDelta=function(A){return arguments.length?(b=+A,S):b},S.tileSize=function(A){return arguments.length?(m=+A,S):m},S.clamp=function(A){return arguments.length?(d=g=!!A,S):d&&g},S.clampX=function(A){return arguments.length?(d=!!A,S):d},S.clampY=function(A){return arguments.length?(g=!!A,S):g},S}function l([u,c,f]){const h=1<<f;return[u-Math.floor(u/h)*h,c-Math.floor(c/h)*h,f]}n.tile=s,n.tileWrap=l,Object.defineProperty(n,"__esModule",{value:!0})})})(Z0,Z0.exports);var K0={exports:{}},Es={exports:{}},Q0;function V9(){return Q0||(Q0=1,function(t,e){(function(n,r){r(e)})(Ka,function(n){function r(O,j){return O<j?-1:O>j?1:O>=j?0:NaN}function i(O){let j=O,F=O;O.length===1&&(j=(fe,Pe)=>O(fe)-Pe,F=a(O));function U(fe,Pe,Be,Ue){for(Be==null&&(Be=0),Ue==null&&(Ue=fe.length);Be<Ue;){const Je=Be+Ue>>>1;F(fe[Je],Pe)<0?Be=Je+1:Ue=Je}return Be}function q(fe,Pe,Be,Ue){for(Be==null&&(Be=0),Ue==null&&(Ue=fe.length);Be<Ue;){const Je=Be+Ue>>>1;F(fe[Je],Pe)>0?Ue=Je:Be=Je+1}return Be}function X(fe,Pe,Be,Ue){Be==null&&(Be=0),Ue==null&&(Ue=fe.length);const Je=U(fe,Pe,Be,Ue-1);return Je>Be&&j(fe[Je-1],Pe)>-j(fe[Je],Pe)?Je-1:Je}return{left:U,center:X,right:q}}function a(O){return(j,F)=>r(O(j),F)}function s(O){return O===null?NaN:+O}function*l(O,j){if(j===void 0)for(let F of O)F!=null&&(F=+F)>=F&&(yield F);else{let F=-1;for(let U of O)(U=j(U,++F,O))!=null&&(U=+U)>=U&&(yield U)}}const u=i(r),c=u.right,f=u.left,h=i(s).center;function d(O,j){let F=0;if(j===void 0)for(let U of O)U!=null&&(U=+U)>=U&&++F;else{let U=-1;for(let q of O)(q=j(q,++U,O))!=null&&(q=+q)>=q&&++F}return F}function g(O){return O.length|0}function m(O){return!(O>0)}function v(O){return typeof O!="object"||"length"in O?O:Array.from(O)}function w(O){return j=>O(...j)}function b(...O){const j=typeof O[O.length-1]=="function"&&w(O.pop());O=O.map(v);const F=O.map(g),U=O.length-1,q=new Array(U+1).fill(0),X=[];if(U<0||F.some(m))return X;for(;;){X.push(q.map((Pe,Be)=>O[Be][Pe]));let fe=U;for(;++q[fe]===F[fe];){if(fe===0)return j?X.map(j):X;q[fe--]=0}}}function S(O,j){var F=0,U=0;return Float64Array.from(O,j===void 0?q=>F+=+q||0:q=>F+=+j(q,U++,O)||0)}function A(O,j){return j<O?-1:j>O?1:j>=O?0:NaN}function N(O,j){let F=0,U,q=0,X=0;if(j===void 0)for(let fe of O)fe!=null&&(fe=+fe)>=fe&&(U=fe-q,q+=U/++F,X+=U*(fe-q));else{let fe=-1;for(let Pe of O)(Pe=j(Pe,++fe,O))!=null&&(Pe=+Pe)>=Pe&&(U=Pe-q,q+=U/++F,X+=U*(Pe-q))}if(F>1)return X/(F-1)}function I(O,j){const F=N(O,j);return F&&Math.sqrt(F)}function T(O,j){let F,U;if(j===void 0)for(const q of O)q!=null&&(F===void 0?q>=q&&(F=U=q):(F>q&&(F=q),U<q&&(U=q)));else{let q=-1;for(let X of O)(X=j(X,++q,O))!=null&&(F===void 0?X>=X&&(F=U=X):(F>X&&(F=X),U<X&&(U=X)))}return[F,U]}class L{constructor(){this._partials=new Float64Array(32),this._n=0}add(j){const F=this._partials;let U=0;for(let q=0;q<this._n&&q<32;q++){const X=F[q],fe=j+X,Pe=Math.abs(j)<Math.abs(X)?j-(fe-X):X-(fe-j);Pe&&(F[U++]=Pe),j=fe}return F[U]=j,this._n=U+1,this}valueOf(){const j=this._partials;let F=this._n,U,q,X,fe=0;if(F>0){for(fe=j[--F];F>0&&(U=fe,q=j[--F],fe=U+q,X=q-(fe-U),!X););F>0&&(X<0&&j[F-1]<0||X>0&&j[F-1]>0)&&(q=X*2,U=fe+q,q==U-fe&&(fe=U))}return fe}}function C(O,j){const F=new L;if(j===void 0)for(let U of O)(U=+U)&&F.add(U);else{let U=-1;for(let q of O)(q=+j(q,++U,O))&&F.add(q)}return+F}function _(O,j){const F=new L;let U=-1;return Float64Array.from(O,j===void 0?q=>F.add(+q||0):q=>F.add(+j(q,++U,O)||0))}class M extends Map{constructor(j,F=D){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:F}}),j!=null)for(const[U,q]of j)this.set(U,q)}get(j){return super.get(p(this,j))}has(j){return super.has(p(this,j))}set(j,F){return super.set(R(this,j),F)}delete(j){return super.delete(z(this,j))}}class k extends Set{constructor(j,F=D){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:F}}),j!=null)for(const U of j)this.add(U)}has(j){return super.has(p(this,j))}add(j){return super.add(R(this,j))}delete(j){return super.delete(z(this,j))}}function p({_intern:O,_key:j},F){const U=j(F);return O.has(U)?O.get(U):F}function R({_intern:O,_key:j},F){const U=j(F);return O.has(U)?O.get(U):(O.set(U,F),F)}function z({_intern:O,_key:j},F){const U=j(F);return O.has(U)&&(F=O.get(F),O.delete(U)),F}function D(O){return O!==null&&typeof O=="object"?O.valueOf():O}function W(O){return O}function B(O,...j){return xe(O,W,W,j)}function H(O,...j){return xe(O,Array.from,W,j)}function $(O,j,...F){return xe(O,W,j,F)}function oe(O,j,...F){return xe(O,Array.from,j,F)}function ye(O,...j){return xe(O,W,Ee,j)}function ge(O,...j){return xe(O,Array.from,Ee,j)}function Ee(O){if(O.length!==1)throw new Error("duplicate key");return O[0]}function xe(O,j,F,U){return function q(X,fe){if(fe>=U.length)return F(X);const Pe=new M,Be=U[fe++];let Ue=-1;for(const Je of X){const et=Be(Je,++Ue,X),ct=Pe.get(et);ct?ct.push(Je):Pe.set(et,[Je])}for(const[Je,et]of Pe)Pe.set(Je,q(et,fe));return j(Pe)}(O,0)}function te(O,j){return Array.from(j,F=>O[F])}function ke(O,...j){if(typeof O[Symbol.iterator]!="function")throw new TypeError("values is not iterable");O=Array.from(O);let[F=r]=j;if(F.length===1||j.length>1){const U=Uint32Array.from(O,(q,X)=>X);return j.length>1?(j=j.map(q=>O.map(q)),U.sort((q,X)=>{for(const fe of j){const Pe=r(fe[q],fe[X]);if(Pe)return Pe}})):(F=O.map(F),U.sort((q,X)=>r(F[q],F[X]))),te(O,U)}return O.sort(F)}function Se(O,j,F){return(j.length===1?ke($(O,j,F),([U,q],[X,fe])=>r(q,fe)||r(U,X)):ke(B(O,F),([U,q],[X,fe])=>j(q,fe)||r(U,X))).map(([U])=>U)}var we=Array.prototype,se=we.slice;function V(O){return function(){return O}}var K=Math.sqrt(50),G=Math.sqrt(10),be=Math.sqrt(2);function _e(O,j,F){var U,q=-1,X,fe,Pe;if(j=+j,O=+O,F=+F,O===j&&F>0)return[O];if((U=j<O)&&(X=O,O=j,j=X),(Pe=ae(O,j,F))===0||!isFinite(Pe))return[];if(Pe>0){let Be=Math.round(O/Pe),Ue=Math.round(j/Pe);for(Be*Pe<O&&++Be,Ue*Pe>j&&--Ue,fe=new Array(X=Ue-Be+1);++q<X;)fe[q]=(Be+q)*Pe}else{Pe=-Pe;let Be=Math.round(O*Pe),Ue=Math.round(j*Pe);for(Be/Pe<O&&++Be,Ue/Pe>j&&--Ue,fe=new Array(X=Ue-Be+1);++q<X;)fe[q]=(Be+q)/Pe}return U&&fe.reverse(),fe}function ae(O,j,F){var U=(j-O)/Math.max(0,F),q=Math.floor(Math.log(U)/Math.LN10),X=U/Math.pow(10,q);return q>=0?(X>=K?10:X>=G?5:X>=be?2:1)*Math.pow(10,q):-Math.pow(10,-q)/(X>=K?10:X>=G?5:X>=be?2:1)}function J(O,j,F){var U=Math.abs(j-O)/Math.max(0,F),q=Math.pow(10,Math.floor(Math.log(U)/Math.LN10)),X=U/q;return X>=K?q*=10:X>=G?q*=5:X>=be&&(q*=2),j<O?-q:q}function de(O,j,F){let U;for(;;){const q=ae(O,j,F);if(q===U||q===0||!isFinite(q))return[O,j];q>0?(O=Math.floor(O/q)*q,j=Math.ceil(j/q)*q):q<0&&(O=Math.ceil(O*q)/q,j=Math.floor(j*q)/q),U=q}}function De(O){return Math.ceil(Math.log(d(O))/Math.LN2)+1}function Z(){var O=W,j=T,F=De;function U(q){Array.isArray(q)||(q=Array.from(q));var X,fe=q.length,Pe,Be=new Array(fe);for(X=0;X<fe;++X)Be[X]=O(q[X],X,q);var Ue=j(Be),Je=Ue[0],et=Ue[1],ct=F(Be,Je,et);if(!Array.isArray(ct)){const ca=et,Tn=+ct;if(j===T&&([Je,et]=de(Je,et,Tn)),ct=_e(Je,et,Tn),ct[ct.length-1]>=et)if(ca>=et&&j===T){const Mn=ae(Je,et,Tn);isFinite(Mn)&&(Mn>0?et=(Math.floor(et/Mn)+1)*Mn:Mn<0&&(et=(Math.ceil(et*-Mn)+1)/-Mn))}else ct.pop()}for(var kt=ct.length;ct[0]<=Je;)ct.shift(),--kt;for(;ct[kt-1]>et;)ct.pop(),--kt;var wn=new Array(kt+1),yn;for(X=0;X<=kt;++X)yn=wn[X]=[],yn.x0=X>0?ct[X-1]:Je,yn.x1=X<kt?ct[X]:et;for(X=0;X<fe;++X)Pe=Be[X],Je<=Pe&&Pe<=et&&wn[c(ct,Pe,0,kt)].push(q[X]);return wn}return U.value=function(q){return arguments.length?(O=typeof q=="function"?q:V(q),U):O},U.domain=function(q){return arguments.length?(j=typeof q=="function"?q:V([q[0],q[1]]),U):j},U.thresholds=function(q){return arguments.length?(F=typeof q=="function"?q:Array.isArray(q)?V(se.call(q)):V(q),U):F},U}function ne(O,j){let F;if(j===void 0)for(const U of O)U!=null&&(F<U||F===void 0&&U>=U)&&(F=U);else{let U=-1;for(let q of O)(q=j(q,++U,O))!=null&&(F<q||F===void 0&&q>=q)&&(F=q)}return F}function he(O,j){let F;if(j===void 0)for(const U of O)U!=null&&(F>U||F===void 0&&U>=U)&&(F=U);else{let U=-1;for(let q of O)(q=j(q,++U,O))!=null&&(F>q||F===void 0&&q>=q)&&(F=q)}return F}function Te(O,j,F=0,U=O.length-1,q=r){for(;U>F;){if(U-F>600){const Be=U-F+1,Ue=j-F+1,Je=Math.log(Be),et=.5*Math.exp(2*Je/3),ct=.5*Math.sqrt(Je*et*(Be-et)/Be)*(Ue-Be/2<0?-1:1),kt=Math.max(F,Math.floor(j-Ue*et/Be+ct)),wn=Math.min(U,Math.floor(j+(Be-Ue)*et/Be+ct));Te(O,j,kt,wn,q)}const X=O[j];let fe=F,Pe=U;for(ve(O,F,j),q(O[U],X)>0&&ve(O,F,U);fe<Pe;){for(ve(O,fe,Pe),++fe,--Pe;q(O[fe],X)<0;)++fe;for(;q(O[Pe],X)>0;)--Pe}q(O[F],X)===0?ve(O,F,Pe):(++Pe,ve(O,Pe,U)),Pe<=j&&(F=Pe+1),j<=Pe&&(U=Pe-1)}return O}function ve(O,j,F){const U=O[j];O[j]=O[F],O[F]=U}function Ie(O,j,F){if(O=Float64Array.from(l(O,F)),!!(U=O.length)){if((j=+j)<=0||U<2)return he(O);if(j>=1)return ne(O);var U,q=(U-1)*j,X=Math.floor(q),fe=ne(Te(O,X).subarray(0,X+1)),Pe=he(O.subarray(X+1));return fe+(Pe-fe)*(q-X)}}function pe(O,j,F=s){if(U=O.length){if((j=+j)<=0||U<2)return+F(O[0],0,O);if(j>=1)return+F(O[U-1],U-1,O);var U,q=(U-1)*j,X=Math.floor(q),fe=+F(O[X],X,O),Pe=+F(O[X+1],X+1,O);return fe+(Pe-fe)*(q-X)}}function Ke(O,j,F){return Math.ceil((F-j)/(2*(Ie(O,.75)-Ie(O,.25))*Math.pow(d(O),-1/3)))}function Ge(O,j,F){return Math.ceil((F-j)/(3.5*I(O)*Math.pow(d(O),-1/3)))}function ut(O,j){let F,U=-1,q=-1;if(j===void 0)for(const X of O)++q,X!=null&&(F<X||F===void 0&&X>=X)&&(F=X,U=q);else for(let X of O)(X=j(X,++q,O))!=null&&(F<X||F===void 0&&X>=X)&&(F=X,U=q);return U}function ot(O,j){let F=0,U=0;if(j===void 0)for(let q of O)q!=null&&(q=+q)>=q&&(++F,U+=q);else{let q=-1;for(let X of O)(X=j(X,++q,O))!=null&&(X=+X)>=X&&(++F,U+=X)}if(F)return U/F}function Ze(O,j){return Ie(O,.5,j)}function*Et(O){for(const j of O)yield*j}function rn(O){return Array.from(Et(O))}function qt(O,j){let F,U=-1,q=-1;if(j===void 0)for(const X of O)++q,X!=null&&(F>X||F===void 0&&X>=X)&&(F=X,U=q);else for(let X of O)(X=j(X,++q,O))!=null&&(F>X||F===void 0&&X>=X)&&(F=X,U=q);return U}function Ot(O,j=un){const F=[];let U,q=!1;for(const X of O)q&&F.push(j(U,X)),U=X,q=!0;return F}function un(O,j){return[O,j]}function Zt(O,j,F){O=+O,j=+j,F=(q=arguments.length)<2?(j=O,O=0,1):q<3?1:+F;for(var U=-1,q=Math.max(0,Math.ceil((j-O)/F))|0,X=new Array(q);++U<q;)X[U]=O+U*F;return X}function En(O,j=r){let F,U=!1;if(j.length===1){let q;for(const X of O){const fe=j(X);(U?r(fe,q)<0:r(fe,fe)===0)&&(F=X,q=fe,U=!0)}}else for(const q of O)(U?j(q,F)<0:j(q,q)===0)&&(F=q,U=!0);return F}function Sn(O,j=r){if(j.length===1)return qt(O,j);let F,U=-1,q=-1;for(const X of O)++q,(U<0?j(X,X)===0:j(X,F)<0)&&(F=X,U=q);return U}function Ct(O,j=r){let F,U=!1;if(j.length===1){let q;for(const X of O){const fe=j(X);(U?r(fe,q)>0:r(fe,fe)===0)&&(F=X,q=fe,U=!0)}}else for(const q of O)(U?j(q,F)>0:j(q,q)===0)&&(F=q,U=!0);return F}function pr(O,j=r){if(j.length===1)return ut(O,j);let F,U=-1,q=-1;for(const X of O)++q,(U<0?j(X,X)===0:j(X,F)>0)&&(F=X,U=q);return U}function Fi(O,j){const F=Sn(O,j);return F<0?void 0:F}var Yr=yi(Math.random);function yi(O){return function(F,U=0,q=F.length){let X=q-(U=+U);for(;X;){const fe=O()*X--|0,Pe=F[X+U];F[X+U]=F[fe+U],F[fe+U]=Pe}return F}}function vi(O,j){let F=0;if(j===void 0)for(let U of O)(U=+U)&&(F+=U);else{let U=-1;for(let q of O)(q=+j(q,++U,O))&&(F+=q)}return F}function _i(O){if(!(X=O.length))return[];for(var j=-1,F=he(O,en),U=new Array(F);++j<F;)for(var q=-1,X,fe=U[j]=new Array(X);++q<X;)fe[q]=O[q][j];return U}function en(O){return O.length}function gn(){return _i(arguments)}function An(O,j){if(typeof j!="function")throw new TypeError("test is not a function");let F=-1;for(const U of O)if(!j(U,++F,O))return!1;return!0}function mn(O,j){if(typeof j!="function")throw new TypeError("test is not a function");let F=-1;for(const U of O)if(j(U,++F,O))return!0;return!1}function hr(O,j){if(typeof j!="function")throw new TypeError("test is not a function");const F=[];let U=-1;for(const q of O)j(q,++U,O)&&F.push(q);return F}function Hr(O,j){if(typeof O[Symbol.iterator]!="function")throw new TypeError("values is not iterable");if(typeof j!="function")throw new TypeError("mapper is not a function");return Array.from(O,(F,U)=>j(F,U,O))}function xi(O,j,F){if(typeof j!="function")throw new TypeError("reducer is not a function");const U=O[Symbol.iterator]();let q,X,fe=-1;if(arguments.length<3){if({done:q,value:F}=U.next(),q)return;++fe}for(;{done:q,value:X}=U.next(),!q;)F=j(F,X,++fe,O);return F}function qr(O){if(typeof O[Symbol.iterator]!="function")throw new TypeError("values is not iterable");return Array.from(O).reverse()}function Gn(O,...j){O=new Set(O);for(const F of j)for(const U of F)O.delete(U);return O}function Sr(O,j){const F=j[Symbol.iterator](),U=new Set;for(const q of O){if(U.has(q))return!1;let X,fe;for(;({value:X,done:fe}=F.next())&&!fe;){if(Object.is(q,X))return!1;U.add(X)}}return!0}function Ar(O){return O instanceof Set?O:new Set(O)}function Ur(O,...j){O=new Set(O),j=j.map(Ar);e:for(const F of O)for(const U of j)if(!U.has(F)){O.delete(F);continue e}return O}function On(O,j){const F=O[Symbol.iterator](),U=new Set;for(const q of j){if(U.has(q))continue;let X,fe;for(;{value:X,done:fe}=F.next();){if(fe)return!1;if(U.add(X),Object.is(q,X))break}}return!0}function bi(O,j){return On(j,O)}function tn(...O){const j=new Set;for(const F of O)for(const U of F)j.add(U);return j}n.Adder=L,n.InternMap=M,n.InternSet=k,n.ascending=r,n.bin=Z,n.bisect=c,n.bisectCenter=h,n.bisectLeft=f,n.bisectRight=c,n.bisector=i,n.count=d,n.cross=b,n.cumsum=S,n.descending=A,n.deviation=I,n.difference=Gn,n.disjoint=Sr,n.every=An,n.extent=T,n.fcumsum=_,n.filter=hr,n.fsum=C,n.greatest=Ct,n.greatestIndex=pr,n.group=B,n.groupSort=Se,n.groups=H,n.histogram=Z,n.index=ye,n.indexes=ge,n.intersection=Ur,n.least=En,n.leastIndex=Sn,n.map=Hr,n.max=ne,n.maxIndex=ut,n.mean=ot,n.median=Ze,n.merge=rn,n.min=he,n.minIndex=qt,n.nice=de,n.pairs=Ot,n.permute=te,n.quantile=Ie,n.quantileSorted=pe,n.quickselect=Te,n.range=Zt,n.reduce=xi,n.reverse=qr,n.rollup=$,n.rollups=oe,n.scan=Fi,n.shuffle=Yr,n.shuffler=yi,n.some=mn,n.sort=ke,n.subset=bi,n.sum=vi,n.superset=On,n.thresholdFreedmanDiaconis=Ke,n.thresholdScott=Ge,n.thresholdSturges=De,n.tickIncrement=ae,n.tickStep=J,n.ticks=_e,n.transpose=_i,n.union=tn,n.variance=N,n.zip=gn,Object.defineProperty(n,"__esModule",{value:!0})})}(Es,Es.exports)),Es.exports}var Os={exports:{}},Ns={exports:{}},J0;function Z9(){return J0||(J0=1,function(t,e){(function(n,r){r(e)})(Ka,function(n){var r=Math.PI,i=2*r,a=1e-6,s=i-a;function l(){this._x0=this._y0=this._x1=this._y1=null,this._=""}function u(){return new l}l.prototype=u.prototype={constructor:l,moveTo:function(c,f){this._+="M"+(this._x0=this._x1=+c)+","+(this._y0=this._y1=+f)},closePath:function(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")},lineTo:function(c,f){this._+="L"+(this._x1=+c)+","+(this._y1=+f)},quadraticCurveTo:function(c,f,h,d){this._+="Q"+ +c+","+ +f+","+(this._x1=+h)+","+(this._y1=+d)},bezierCurveTo:function(c,f,h,d,g,m){this._+="C"+ +c+","+ +f+","+ +h+","+ +d+","+(this._x1=+g)+","+(this._y1=+m)},arcTo:function(c,f,h,d,g){c=+c,f=+f,h=+h,d=+d,g=+g;var m=this._x1,v=this._y1,w=h-c,b=d-f,S=m-c,A=v-f,N=S*S+A*A;if(g<0)throw new Error("negative radius: "+g);if(this._x1===null)this._+="M"+(this._x1=c)+","+(this._y1=f);else if(N>a)if(!(Math.abs(A*w-b*S)>a)||!g)this._+="L"+(this._x1=c)+","+(this._y1=f);else{var I=h-m,T=d-v,L=w*w+b*b,C=I*I+T*T,_=Math.sqrt(L),M=Math.sqrt(N),k=g*Math.tan((r-Math.acos((L+N-C)/(2*_*M)))/2),p=k/M,R=k/_;Math.abs(p-1)>a&&(this._+="L"+(c+p*S)+","+(f+p*A)),this._+="A"+g+","+g+",0,0,"+ +(A*I>S*T)+","+(this._x1=c+R*w)+","+(this._y1=f+R*b)}},arc:function(c,f,h,d,g,m){c=+c,f=+f,h=+h,m=!!m;var v=h*Math.cos(d),w=h*Math.sin(d),b=c+v,S=f+w,A=1^m,N=m?d-g:g-d;if(h<0)throw new Error("negative radius: "+h);this._x1===null?this._+="M"+b+","+S:(Math.abs(this._x1-b)>a||Math.abs(this._y1-S)>a)&&(this._+="L"+b+","+S),h&&(N<0&&(N=N%i+i),N>s?this._+="A"+h+","+h+",0,1,"+A+","+(c-v)+","+(f-w)+"A"+h+","+h+",0,1,"+A+","+(this._x1=b)+","+(this._y1=S):N>a&&(this._+="A"+h+","+h+",0,"+ +(N>=r)+","+A+","+(this._x1=c+h*Math.cos(g))+","+(this._y1=f+h*Math.sin(g))))},rect:function(c,f,h,d){this._+="M"+(this._x0=this._x1=+c)+","+(this._y0=this._y1=+f)+"h"+ +h+"v"+ +d+"h"+-h+"Z"},toString:function(){return this._}},n.path=u,Object.defineProperty(n,"__esModule",{value:!0})})}(Ns,Ns.exports)),Ns.exports}var $0;function K9(){return $0||($0=1,function(t,e){(function(n,r){r(e,Z9())})(Ka,function(n,r){function i(y){return function(){return y}}var a=Math.abs,s=Math.atan2,l=Math.cos,u=Math.max,c=Math.min,f=Math.sin,h=Math.sqrt,d=1e-12,g=Math.PI,m=g/2,v=2*g;function w(y){return y>1?0:y<-1?g:Math.acos(y)}function b(y){return y>=1?m:y<=-1?-m:Math.asin(y)}function S(y){return y.innerRadius}function A(y){return y.outerRadius}function N(y){return y.startAngle}function I(y){return y.endAngle}function T(y){return y&&y.padAngle}function L(y,E,Y,ee,le,me,Me,re){var Ae=Y-y,He=ee-E,je=Me-le,Qe=re-me,lt=Qe*Ae-je*He;if(!(lt*lt<d))return lt=(je*(E-me)-Qe*(y-le))/lt,[y+lt*Ae,E+lt*He]}function C(y,E,Y,ee,le,me,Me){var re=y-Y,Ae=E-ee,He=(Me?me:-me)/h(re*re+Ae*Ae),je=He*Ae,Qe=-He*re,lt=y+je,zt=E+Qe,Mt=Y+je,Kt=ee+Qe,Lt=(lt+Mt)/2,vn=(zt+Kt)/2,cn=Mt-lt,sn=Kt-zt,_n=cn*cn+sn*sn,$n=le-me,Yn=lt*Kt-Mt*zt,ki=(sn<0?-1:1)*h(u(0,$n*$n*_n-Yn*Yn)),wi=(Yn*sn-cn*ki)/_n,er=(-Yn*cn-sn*ki)/_n,dr=(Yn*sn+cn*ki)/_n,gr=(-Yn*cn+sn*ki)/_n,yt=wi-Lt,Ut=er-vn,Bi=dr-Lt,Yi=gr-vn;return yt*yt+Ut*Ut>Bi*Bi+Yi*Yi&&(wi=dr,er=gr),{cx:wi,cy:er,x01:-je,y01:-Qe,x11:wi*(le/$n-1),y11:er*(le/$n-1)}}function _(){var y=S,E=A,Y=i(0),ee=null,le=N,me=I,Me=T,re=null;function Ae(){var He,je,Qe=+y.apply(this,arguments),lt=+E.apply(this,arguments),zt=le.apply(this,arguments)-m,Mt=me.apply(this,arguments)-m,Kt=a(Mt-zt),Lt=Mt>zt;if(re||(re=He=r.path()),lt<Qe&&(je=lt,lt=Qe,Qe=je),!(lt>d))re.moveTo(0,0);else if(Kt>v-d)re.moveTo(lt*l(zt),lt*f(zt)),re.arc(0,0,lt,zt,Mt,!Lt),Qe>d&&(re.moveTo(Qe*l(Mt),Qe*f(Mt)),re.arc(0,0,Qe,Mt,zt,Lt));else{var vn=zt,cn=Mt,sn=zt,_n=Mt,$n=Kt,Yn=Kt,ki=Me.apply(this,arguments)/2,wi=ki>d&&(ee?+ee.apply(this,arguments):h(Qe*Qe+lt*lt)),er=c(a(lt-Qe)/2,+Y.apply(this,arguments)),dr=er,gr=er,yt,Ut;if(wi>d){var Bi=b(wi/Qe*f(ki)),Yi=b(wi/lt*f(ki));($n-=Bi*2)>d?(Bi*=Lt?1:-1,sn+=Bi,_n-=Bi):($n=0,sn=_n=(zt+Mt)/2),(Yn-=Yi*2)>d?(Yi*=Lt?1:-1,vn+=Yi,cn-=Yi):(Yn=0,vn=cn=(zt+Mt)/2)}var fa=lt*l(vn),ha=lt*f(vn),is=Qe*l(_n),as=Qe*f(_n);if(er>d){var os=lt*l(cn),ss=lt*f(cn),cu=Qe*l(sn),fu=Qe*f(sn),Gr;if(Kt<g&&(Gr=L(fa,ha,cu,fu,os,ss,is,as))){var hu=fa-Gr[0],du=ha-Gr[1],gu=os-Gr[0],mu=ss-Gr[1],lh=1/f(w((hu*gu+du*mu)/(h(hu*hu+du*du)*h(gu*gu+mu*mu)))/2),uh=h(Gr[0]*Gr[0]+Gr[1]*Gr[1]);dr=c(er,(Qe-uh)/(lh-1)),gr=c(er,(lt-uh)/(lh+1))}}Yn>d?gr>d?(yt=C(cu,fu,fa,ha,lt,gr,Lt),Ut=C(os,ss,is,as,lt,gr,Lt),re.moveTo(yt.cx+yt.x01,yt.cy+yt.y01),gr<er?re.arc(yt.cx,yt.cy,gr,s(yt.y01,yt.x01),s(Ut.y01,Ut.x01),!Lt):(re.arc(yt.cx,yt.cy,gr,s(yt.y01,yt.x01),s(yt.y11,yt.x11),!Lt),re.arc(0,0,lt,s(yt.cy+yt.y11,yt.cx+yt.x11),s(Ut.cy+Ut.y11,Ut.cx+Ut.x11),!Lt),re.arc(Ut.cx,Ut.cy,gr,s(Ut.y11,Ut.x11),s(Ut.y01,Ut.x01),!Lt))):(re.moveTo(fa,ha),re.arc(0,0,lt,vn,cn,!Lt)):re.moveTo(fa,ha),!(Qe>d)||!($n>d)?re.lineTo(is,as):dr>d?(yt=C(is,as,os,ss,Qe,-dr,Lt),Ut=C(fa,ha,cu,fu,Qe,-dr,Lt),re.lineTo(yt.cx+yt.x01,yt.cy+yt.y01),dr<er?re.arc(yt.cx,yt.cy,dr,s(yt.y01,yt.x01),s(Ut.y01,Ut.x01),!Lt):(re.arc(yt.cx,yt.cy,dr,s(yt.y01,yt.x01),s(yt.y11,yt.x11),!Lt),re.arc(0,0,Qe,s(yt.cy+yt.y11,yt.cx+yt.x11),s(Ut.cy+Ut.y11,Ut.cx+Ut.x11),Lt),re.arc(Ut.cx,Ut.cy,dr,s(Ut.y11,Ut.x11),s(Ut.y01,Ut.x01),!Lt))):re.arc(0,0,Qe,_n,sn,Lt)}if(re.closePath(),He)return re=null,He+""||null}return Ae.centroid=function(){var He=(+y.apply(this,arguments)+ +E.apply(this,arguments))/2,je=(+le.apply(this,arguments)+ +me.apply(this,arguments))/2-g/2;return[l(je)*He,f(je)*He]},Ae.innerRadius=function(He){return arguments.length?(y=typeof He=="function"?He:i(+He),Ae):y},Ae.outerRadius=function(He){return arguments.length?(E=typeof He=="function"?He:i(+He),Ae):E},Ae.cornerRadius=function(He){return arguments.length?(Y=typeof He=="function"?He:i(+He),Ae):Y},Ae.padRadius=function(He){return arguments.length?(ee=He==null?null:typeof He=="function"?He:i(+He),Ae):ee},Ae.startAngle=function(He){return arguments.length?(le=typeof He=="function"?He:i(+He),Ae):le},Ae.endAngle=function(He){return arguments.length?(me=typeof He=="function"?He:i(+He),Ae):me},Ae.padAngle=function(He){return arguments.length?(Me=typeof He=="function"?He:i(+He),Ae):Me},Ae.context=function(He){return arguments.length?(re=He??null,Ae):re},Ae}function M(y){this._context=y}M.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(y,E){switch(y=+y,E=+E,this._point){case 0:this._point=1,this._line?this._context.lineTo(y,E):this._context.moveTo(y,E);break;case 1:this._point=2;default:this._context.lineTo(y,E);break}}};function k(y){return new M(y)}function p(y){return y[0]}function R(y){return y[1]}function z(){var y=p,E=R,Y=i(!0),ee=null,le=k,me=null;function Me(re){var Ae,He=re.length,je,Qe=!1,lt;for(ee==null&&(me=le(lt=r.path())),Ae=0;Ae<=He;++Ae)!(Ae<He&&Y(je=re[Ae],Ae,re))===Qe&&((Qe=!Qe)?me.lineStart():me.lineEnd()),Qe&&me.point(+y(je,Ae,re),+E(je,Ae,re));if(lt)return me=null,lt+""||null}return Me.x=function(re){return arguments.length?(y=typeof re=="function"?re:i(+re),Me):y},Me.y=function(re){return arguments.length?(E=typeof re=="function"?re:i(+re),Me):E},Me.defined=function(re){return arguments.length?(Y=typeof re=="function"?re:i(!!re),Me):Y},Me.curve=function(re){return arguments.length?(le=re,ee!=null&&(me=le(ee)),Me):le},Me.context=function(re){return arguments.length?(re==null?ee=me=null:me=le(ee=re),Me):ee},Me}function D(){var y=p,E=null,Y=i(0),ee=R,le=i(!0),me=null,Me=k,re=null;function Ae(je){var Qe,lt,zt,Mt=je.length,Kt,Lt=!1,vn,cn=new Array(Mt),sn=new Array(Mt);for(me==null&&(re=Me(vn=r.path())),Qe=0;Qe<=Mt;++Qe){if(!(Qe<Mt&&le(Kt=je[Qe],Qe,je))===Lt)if(Lt=!Lt)lt=Qe,re.areaStart(),re.lineStart();else{for(re.lineEnd(),re.lineStart(),zt=Qe-1;zt>=lt;--zt)re.point(cn[zt],sn[zt]);re.lineEnd(),re.areaEnd()}Lt&&(cn[Qe]=+y(Kt,Qe,je),sn[Qe]=+Y(Kt,Qe,je),re.point(E?+E(Kt,Qe,je):cn[Qe],ee?+ee(Kt,Qe,je):sn[Qe]))}if(vn)return re=null,vn+""||null}function He(){return z().defined(le).curve(Me).context(me)}return Ae.x=function(je){return arguments.length?(y=typeof je=="function"?je:i(+je),E=null,Ae):y},Ae.x0=function(je){return arguments.length?(y=typeof je=="function"?je:i(+je),Ae):y},Ae.x1=function(je){return arguments.length?(E=je==null?null:typeof je=="function"?je:i(+je),Ae):E},Ae.y=function(je){return arguments.length?(Y=typeof je=="function"?je:i(+je),ee=null,Ae):Y},Ae.y0=function(je){return arguments.length?(Y=typeof je=="function"?je:i(+je),Ae):Y},Ae.y1=function(je){return arguments.length?(ee=je==null?null:typeof je=="function"?je:i(+je),Ae):ee},Ae.lineX0=Ae.lineY0=function(){return He().x(y).y(Y)},Ae.lineY1=function(){return He().x(y).y(ee)},Ae.lineX1=function(){return He().x(E).y(Y)},Ae.defined=function(je){return arguments.length?(le=typeof je=="function"?je:i(!!je),Ae):le},Ae.curve=function(je){return arguments.length?(Me=je,me!=null&&(re=Me(me)),Ae):Me},Ae.context=function(je){return arguments.length?(je==null?me=re=null:re=Me(me=je),Ae):me},Ae}function W(y,E){return E<y?-1:E>y?1:E>=y?0:NaN}function B(y){return y}function H(){var y=B,E=W,Y=null,ee=i(0),le=i(v),me=i(0);function Me(re){var Ae,He=re.length,je,Qe,lt=0,zt=new Array(He),Mt=new Array(He),Kt=+ee.apply(this,arguments),Lt=Math.min(v,Math.max(-v,le.apply(this,arguments)-Kt)),vn,cn=Math.min(Math.abs(Lt)/He,me.apply(this,arguments)),sn=cn*(Lt<0?-1:1),_n;for(Ae=0;Ae<He;++Ae)(_n=Mt[zt[Ae]=Ae]=+y(re[Ae],Ae,re))>0&&(lt+=_n);for(E!=null?zt.sort(function($n,Yn){return E(Mt[$n],Mt[Yn])}):Y!=null&&zt.sort(function($n,Yn){return Y(re[$n],re[Yn])}),Ae=0,Qe=lt?(Lt-He*sn)/lt:0;Ae<He;++Ae,Kt=vn)je=zt[Ae],_n=Mt[je],vn=Kt+(_n>0?_n*Qe:0)+sn,Mt[je]={data:re[je],index:Ae,value:_n,startAngle:Kt,endAngle:vn,padAngle:cn};return Mt}return Me.value=function(re){return arguments.length?(y=typeof re=="function"?re:i(+re),Me):y},Me.sortValues=function(re){return arguments.length?(E=re,Y=null,Me):E},Me.sort=function(re){return arguments.length?(Y=re,E=null,Me):Y},Me.startAngle=function(re){return arguments.length?(ee=typeof re=="function"?re:i(+re),Me):ee},Me.endAngle=function(re){return arguments.length?(le=typeof re=="function"?re:i(+re),Me):le},Me.padAngle=function(re){return arguments.length?(me=typeof re=="function"?re:i(+re),Me):me},Me}var $=ye(k);function oe(y){this._curve=y}oe.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(y,E){this._curve.point(E*Math.sin(y),E*-Math.cos(y))}};function ye(y){function E(Y){return new oe(y(Y))}return E._curve=y,E}function ge(y){var E=y.curve;return y.angle=y.x,delete y.x,y.radius=y.y,delete y.y,y.curve=function(Y){return arguments.length?E(ye(Y)):E()._curve},y}function Ee(){return ge(z().curve($))}function xe(){var y=D().curve($),E=y.curve,Y=y.lineX0,ee=y.lineX1,le=y.lineY0,me=y.lineY1;return y.angle=y.x,delete y.x,y.startAngle=y.x0,delete y.x0,y.endAngle=y.x1,delete y.x1,y.radius=y.y,delete y.y,y.innerRadius=y.y0,delete y.y0,y.outerRadius=y.y1,delete y.y1,y.lineStartAngle=function(){return ge(Y())},delete y.lineX0,y.lineEndAngle=function(){return ge(ee())},delete y.lineX1,y.lineInnerRadius=function(){return ge(le())},delete y.lineY0,y.lineOuterRadius=function(){return ge(me())},delete y.lineY1,y.curve=function(Me){return arguments.length?E(ye(Me)):E()._curve},y}function te(y,E){return[(E=+E)*Math.cos(y-=Math.PI/2),E*Math.sin(y)]}var ke=Array.prototype.slice;function Se(y){return y.source}function we(y){return y.target}function se(y){var E=Se,Y=we,ee=p,le=R,me=null;function Me(){var re,Ae=ke.call(arguments),He=E.apply(this,Ae),je=Y.apply(this,Ae);if(me||(me=re=r.path()),y(me,+ee.apply(this,(Ae[0]=He,Ae)),+le.apply(this,Ae),+ee.apply(this,(Ae[0]=je,Ae)),+le.apply(this,Ae)),re)return me=null,re+""||null}return Me.source=function(re){return arguments.length?(E=re,Me):E},Me.target=function(re){return arguments.length?(Y=re,Me):Y},Me.x=function(re){return arguments.length?(ee=typeof re=="function"?re:i(+re),Me):ee},Me.y=function(re){return arguments.length?(le=typeof re=="function"?re:i(+re),Me):le},Me.context=function(re){return arguments.length?(me=re??null,Me):me},Me}function V(y,E,Y,ee,le){y.moveTo(E,Y),y.bezierCurveTo(E=(E+ee)/2,Y,E,le,ee,le)}function K(y,E,Y,ee,le){y.moveTo(E,Y),y.bezierCurveTo(E,Y=(Y+le)/2,ee,Y,ee,le)}function G(y,E,Y,ee,le){var me=te(E,Y),Me=te(E,Y=(Y+le)/2),re=te(ee,Y),Ae=te(ee,le);y.moveTo(me[0],me[1]),y.bezierCurveTo(Me[0],Me[1],re[0],re[1],Ae[0],Ae[1])}function be(){return se(V)}function _e(){return se(K)}function ae(){var y=se(G);return y.angle=y.x,delete y.x,y.radius=y.y,delete y.y,y}var J={draw:function(y,E){var Y=Math.sqrt(E/g);y.moveTo(Y,0),y.arc(0,0,Y,0,v)}},de={draw:function(y,E){var Y=Math.sqrt(E/5)/2;y.moveTo(-3*Y,-Y),y.lineTo(-Y,-Y),y.lineTo(-Y,-3*Y),y.lineTo(Y,-3*Y),y.lineTo(Y,-Y),y.lineTo(3*Y,-Y),y.lineTo(3*Y,Y),y.lineTo(Y,Y),y.lineTo(Y,3*Y),y.lineTo(-Y,3*Y),y.lineTo(-Y,Y),y.lineTo(-3*Y,Y),y.closePath()}},De=Math.sqrt(1/3),Z=De*2,ne={draw:function(y,E){var Y=Math.sqrt(E/Z),ee=Y*De;y.moveTo(0,-Y),y.lineTo(ee,0),y.lineTo(0,Y),y.lineTo(-ee,0),y.closePath()}},he=.8908130915292852,Te=Math.sin(g/10)/Math.sin(7*g/10),ve=Math.sin(v/10)*Te,Ie=-Math.cos(v/10)*Te,pe={draw:function(y,E){var Y=Math.sqrt(E*he),ee=ve*Y,le=Ie*Y;y.moveTo(0,-Y),y.lineTo(ee,le);for(var me=1;me<5;++me){var Me=v*me/5,re=Math.cos(Me),Ae=Math.sin(Me);y.lineTo(Ae*Y,-re*Y),y.lineTo(re*ee-Ae*le,Ae*ee+re*le)}y.closePath()}},Ke={draw:function(y,E){var Y=Math.sqrt(E),ee=-Y/2;y.rect(ee,ee,Y,Y)}},Ge=Math.sqrt(3),ut={draw:function(y,E){var Y=-Math.sqrt(E/(Ge*3));y.moveTo(0,Y*2),y.lineTo(-Ge*Y,-Y),y.lineTo(Ge*Y,-Y),y.closePath()}},ot=-.5,Ze=Math.sqrt(3)/2,Et=1/Math.sqrt(12),rn=(Et/2+1)*3,qt={draw:function(y,E){var Y=Math.sqrt(E/rn),ee=Y/2,le=Y*Et,me=ee,Me=Y*Et+Y,re=-me,Ae=Me;y.moveTo(ee,le),y.lineTo(me,Me),y.lineTo(re,Ae),y.lineTo(ot*ee-Ze*le,Ze*ee+ot*le),y.lineTo(ot*me-Ze*Me,Ze*me+ot*Me),y.lineTo(ot*re-Ze*Ae,Ze*re+ot*Ae),y.lineTo(ot*ee+Ze*le,ot*le-Ze*ee),y.lineTo(ot*me+Ze*Me,ot*Me-Ze*me),y.lineTo(ot*re+Ze*Ae,ot*Ae-Ze*re),y.closePath()}},Ot=[J,de,ne,Ke,pe,ut,qt];function un(){var y=i(J),E=i(64),Y=null;function ee(){var le;if(Y||(Y=le=r.path()),y.apply(this,arguments).draw(Y,+E.apply(this,arguments)),le)return Y=null,le+""||null}return ee.type=function(le){return arguments.length?(y=typeof le=="function"?le:i(le),ee):y},ee.size=function(le){return arguments.length?(E=typeof le=="function"?le:i(+le),ee):E},ee.context=function(le){return arguments.length?(Y=le??null,ee):Y},ee}function Zt(){}function En(y,E,Y){y._context.bezierCurveTo((2*y._x0+y._x1)/3,(2*y._y0+y._y1)/3,(y._x0+2*y._x1)/3,(y._y0+2*y._y1)/3,(y._x0+4*y._x1+E)/6,(y._y0+4*y._y1+Y)/6)}function Sn(y){this._context=y}Sn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:En(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(y,E){switch(y=+y,E=+E,this._point){case 0:this._point=1,this._line?this._context.lineTo(y,E):this._context.moveTo(y,E);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:En(this,y,E);break}this._x0=this._x1,this._x1=y,this._y0=this._y1,this._y1=E}};function Ct(y){return new Sn(y)}function pr(y){this._context=y}pr.prototype={areaStart:Zt,areaEnd:Zt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(y,E){switch(y=+y,E=+E,this._point){case 0:this._point=1,this._x2=y,this._y2=E;break;case 1:this._point=2,this._x3=y,this._y3=E;break;case 2:this._point=3,this._x4=y,this._y4=E,this._context.moveTo((this._x0+4*this._x1+y)/6,(this._y0+4*this._y1+E)/6);break;default:En(this,y,E);break}this._x0=this._x1,this._x1=y,this._y0=this._y1,this._y1=E}};function Fi(y){return new pr(y)}function Yr(y){this._context=y}Yr.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(y,E){switch(y=+y,E=+E,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var Y=(this._x0+4*this._x1+y)/6,ee=(this._y0+4*this._y1+E)/6;this._line?this._context.lineTo(Y,ee):this._context.moveTo(Y,ee);break;case 3:this._point=4;default:En(this,y,E);break}this._x0=this._x1,this._x1=y,this._y0=this._y1,this._y1=E}};function yi(y){return new Yr(y)}function vi(y,E){this._basis=new Sn(y),this._beta=E}vi.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var y=this._x,E=this._y,Y=y.length-1;if(Y>0)for(var ee=y[0],le=E[0],me=y[Y]-ee,Me=E[Y]-le,re=-1,Ae;++re<=Y;)Ae=re/Y,this._basis.point(this._beta*y[re]+(1-this._beta)*(ee+Ae*me),this._beta*E[re]+(1-this._beta)*(le+Ae*Me));this._x=this._y=null,this._basis.lineEnd()},point:function(y,E){this._x.push(+y),this._y.push(+E)}};var _i=function y(E){function Y(ee){return E===1?new Sn(ee):new vi(ee,E)}return Y.beta=function(ee){return y(+ee)},Y}(.85);function en(y,E,Y){y._context.bezierCurveTo(y._x1+y._k*(y._x2-y._x0),y._y1+y._k*(y._y2-y._y0),y._x2+y._k*(y._x1-E),y._y2+y._k*(y._y1-Y),y._x2,y._y2)}function gn(y,E){this._context=y,this._k=(1-E)/6}gn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:en(this,this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(y,E){switch(y=+y,E=+E,this._point){case 0:this._point=1,this._line?this._context.lineTo(y,E):this._context.moveTo(y,E);break;case 1:this._point=2,this._x1=y,this._y1=E;break;case 2:this._point=3;default:en(this,y,E);break}this._x0=this._x1,this._x1=this._x2,this._x2=y,this._y0=this._y1,this._y1=this._y2,this._y2=E}};var An=function y(E){function Y(ee){return new gn(ee,E)}return Y.tension=function(ee){return y(+ee)},Y}(0);function mn(y,E){this._context=y,this._k=(1-E)/6}mn.prototype={areaStart:Zt,areaEnd:Zt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(y,E){switch(y=+y,E=+E,this._point){case 0:this._point=1,this._x3=y,this._y3=E;break;case 1:this._point=2,this._context.moveTo(this._x4=y,this._y4=E);break;case 2:this._point=3,this._x5=y,this._y5=E;break;default:en(this,y,E);break}this._x0=this._x1,this._x1=this._x2,this._x2=y,this._y0=this._y1,this._y1=this._y2,this._y2=E}};var hr=function y(E){function Y(ee){return new mn(ee,E)}return Y.tension=function(ee){return y(+ee)},Y}(0);function Hr(y,E){this._context=y,this._k=(1-E)/6}Hr.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(y,E){switch(y=+y,E=+E,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:en(this,y,E);break}this._x0=this._x1,this._x1=this._x2,this._x2=y,this._y0=this._y1,this._y1=this._y2,this._y2=E}};var xi=function y(E){function Y(ee){return new Hr(ee,E)}return Y.tension=function(ee){return y(+ee)},Y}(0);function qr(y,E,Y){var ee=y._x1,le=y._y1,me=y._x2,Me=y._y2;if(y._l01_a>d){var re=2*y._l01_2a+3*y._l01_a*y._l12_a+y._l12_2a,Ae=3*y._l01_a*(y._l01_a+y._l12_a);ee=(ee*re-y._x0*y._l12_2a+y._x2*y._l01_2a)/Ae,le=(le*re-y._y0*y._l12_2a+y._y2*y._l01_2a)/Ae}if(y._l23_a>d){var He=2*y._l23_2a+3*y._l23_a*y._l12_a+y._l12_2a,je=3*y._l23_a*(y._l23_a+y._l12_a);me=(me*He+y._x1*y._l23_2a-E*y._l12_2a)/je,Me=(Me*He+y._y1*y._l23_2a-Y*y._l12_2a)/je}y._context.bezierCurveTo(ee,le,me,Me,y._x2,y._y2)}function Gn(y,E){this._context=y,this._alpha=E}Gn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(y,E){if(y=+y,E=+E,this._point){var Y=this._x2-y,ee=this._y2-E;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(Y*Y+ee*ee,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(y,E):this._context.moveTo(y,E);break;case 1:this._point=2;break;case 2:this._point=3;default:qr(this,y,E);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=y,this._y0=this._y1,this._y1=this._y2,this._y2=E}};var Sr=function y(E){function Y(ee){return E?new Gn(ee,E):new gn(ee,0)}return Y.alpha=function(ee){return y(+ee)},Y}(.5);function Ar(y,E){this._context=y,this._alpha=E}Ar.prototype={areaStart:Zt,areaEnd:Zt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(y,E){if(y=+y,E=+E,this._point){var Y=this._x2-y,ee=this._y2-E;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(Y*Y+ee*ee,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=y,this._y3=E;break;case 1:this._point=2,this._context.moveTo(this._x4=y,this._y4=E);break;case 2:this._point=3,this._x5=y,this._y5=E;break;default:qr(this,y,E);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=y,this._y0=this._y1,this._y1=this._y2,this._y2=E}};var Ur=function y(E){function Y(ee){return E?new Ar(ee,E):new mn(ee,0)}return Y.alpha=function(ee){return y(+ee)},Y}(.5);function On(y,E){this._context=y,this._alpha=E}On.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(y,E){if(y=+y,E=+E,this._point){var Y=this._x2-y,ee=this._y2-E;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(Y*Y+ee*ee,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:qr(this,y,E);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=y,this._y0=this._y1,this._y1=this._y2,this._y2=E}};var bi=function y(E){function Y(ee){return E?new On(ee,E):new Hr(ee,0)}return Y.alpha=function(ee){return y(+ee)},Y}(.5);function tn(y){this._context=y}tn.prototype={areaStart:Zt,areaEnd:Zt,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(y,E){y=+y,E=+E,this._point?this._context.lineTo(y,E):(this._point=1,this._context.moveTo(y,E))}};function O(y){return new tn(y)}function j(y){return y<0?-1:1}function F(y,E,Y){var ee=y._x1-y._x0,le=E-y._x1,me=(y._y1-y._y0)/(ee||le<0&&-0),Me=(Y-y._y1)/(le||ee<0&&-0),re=(me*le+Me*ee)/(ee+le);return(j(me)+j(Me))*Math.min(Math.abs(me),Math.abs(Me),.5*Math.abs(re))||0}function U(y,E){var Y=y._x1-y._x0;return Y?(3*(y._y1-y._y0)/Y-E)/2:E}function q(y,E,Y){var ee=y._x0,le=y._y0,me=y._x1,Me=y._y1,re=(me-ee)/3;y._context.bezierCurveTo(ee+re,le+re*E,me-re,Me-re*Y,me,Me)}function X(y){this._context=y}X.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:q(this,this._t0,U(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(y,E){var Y=NaN;if(y=+y,E=+E,!(y===this._x1&&E===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(y,E):this._context.moveTo(y,E);break;case 1:this._point=2;break;case 2:this._point=3,q(this,U(this,Y=F(this,y,E)),Y);break;default:q(this,this._t0,Y=F(this,y,E));break}this._x0=this._x1,this._x1=y,this._y0=this._y1,this._y1=E,this._t0=Y}}};function fe(y){this._context=new Pe(y)}(fe.prototype=Object.create(X.prototype)).point=function(y,E){X.prototype.point.call(this,E,y)};function Pe(y){this._context=y}Pe.prototype={moveTo:function(y,E){this._context.moveTo(E,y)},closePath:function(){this._context.closePath()},lineTo:function(y,E){this._context.lineTo(E,y)},bezierCurveTo:function(y,E,Y,ee,le,me){this._context.bezierCurveTo(E,y,ee,Y,me,le)}};function Be(y){return new X(y)}function Ue(y){return new fe(y)}function Je(y){this._context=y}Je.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var y=this._x,E=this._y,Y=y.length;if(Y)if(this._line?this._context.lineTo(y[0],E[0]):this._context.moveTo(y[0],E[0]),Y===2)this._context.lineTo(y[1],E[1]);else for(var ee=et(y),le=et(E),me=0,Me=1;Me<Y;++me,++Me)this._context.bezierCurveTo(ee[0][me],le[0][me],ee[1][me],le[1][me],y[Me],E[Me]);(this._line||this._line!==0&&Y===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(y,E){this._x.push(+y),this._y.push(+E)}};function et(y){var E,Y=y.length-1,ee,le=new Array(Y),me=new Array(Y),Me=new Array(Y);for(le[0]=0,me[0]=2,Me[0]=y[0]+2*y[1],E=1;E<Y-1;++E)le[E]=1,me[E]=4,Me[E]=4*y[E]+2*y[E+1];for(le[Y-1]=2,me[Y-1]=7,Me[Y-1]=8*y[Y-1]+y[Y],E=1;E<Y;++E)ee=le[E]/me[E-1],me[E]-=ee,Me[E]-=ee*Me[E-1];for(le[Y-1]=Me[Y-1]/me[Y-1],E=Y-2;E>=0;--E)le[E]=(Me[E]-le[E+1])/me[E];for(me[Y-1]=(y[Y]+le[Y-1])/2,E=0;E<Y-1;++E)me[E]=2*y[E+1]-le[E+1];return[le,me]}function ct(y){return new Je(y)}function kt(y,E){this._context=y,this._t=E}kt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(y,E){switch(y=+y,E=+E,this._point){case 0:this._point=1,this._line?this._context.lineTo(y,E):this._context.moveTo(y,E);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,E),this._context.lineTo(y,E);else{var Y=this._x*(1-this._t)+y*this._t;this._context.lineTo(Y,this._y),this._context.lineTo(Y,E)}break}}this._x=y,this._y=E}};function wn(y){return new kt(y,.5)}function yn(y){return new kt(y,0)}function ca(y){return new kt(y,1)}function Tn(y,E){if((Me=y.length)>1)for(var Y=1,ee,le,me=y[E[0]],Me,re=me.length;Y<Me;++Y)for(le=me,me=y[E[Y]],ee=0;ee<re;++ee)me[ee][1]+=me[ee][0]=isNaN(le[ee][1])?le[ee][0]:le[ee][1]}function Mn(y){for(var E=y.length,Y=new Array(E);--E>=0;)Y[E]=E;return Y}function oo(y,E){return y[E]}function ih(){var y=i([]),E=Mn,Y=Tn,ee=oo;function le(me){var Me=y.apply(this,arguments),re,Ae=me.length,He=Me.length,je=new Array(He),Qe;for(re=0;re<He;++re){for(var lt=Me[re],zt=je[re]=new Array(Ae),Mt=0,Kt;Mt<Ae;++Mt)zt[Mt]=Kt=[0,+ee(me[Mt],lt,Mt,me)],Kt.data=me[Mt];zt.key=lt}for(re=0,Qe=E(je);re<He;++re)je[Qe[re]].index=re;return Y(je,Qe),je}return le.keys=function(me){return arguments.length?(y=typeof me=="function"?me:i(ke.call(me)),le):y},le.value=function(me){return arguments.length?(ee=typeof me=="function"?me:i(+me),le):ee},le.order=function(me){return arguments.length?(E=me==null?Mn:typeof me=="function"?me:i(ke.call(me)),le):E},le.offset=function(me){return arguments.length?(Y=me??Tn,le):Y},le}function rs(y,E){if((ee=y.length)>0){for(var Y,ee,le=0,me=y[0].length,Me;le<me;++le){for(Me=Y=0;Y<ee;++Y)Me+=y[Y][le][1]||0;if(Me)for(Y=0;Y<ee;++Y)y[Y][le][1]/=Me}Tn(y,E)}}function uu(y,E){if((Ae=y.length)>0)for(var Y,ee=0,le,me,Me,re,Ae,He=y[E[0]].length;ee<He;++ee)for(Me=re=0,Y=0;Y<Ae;++Y)(me=(le=y[E[Y]][ee])[1]-le[0])>0?(le[0]=Me,le[1]=Me+=me):me<0?(le[1]=re,le[0]=re+=me):(le[0]=0,le[1]=me)}function $m(y,E){if((le=y.length)>0){for(var Y=0,ee=y[E[0]],le,me=ee.length;Y<me;++Y){for(var Me=0,re=0;Me<le;++Me)re+=y[Me][Y][1]||0;ee[Y][1]+=ee[Y][0]=-re/2}Tn(y,E)}}function ey(y,E){if(!(!((Me=y.length)>0)||!((me=(le=y[E[0]]).length)>0))){for(var Y=0,ee=1,le,me,Me;ee<me;++ee){for(var re=0,Ae=0,He=0;re<Me;++re){for(var je=y[E[re]],Qe=je[ee][1]||0,lt=je[ee-1][1]||0,zt=(Qe-lt)/2,Mt=0;Mt<re;++Mt){var Kt=y[E[Mt]],Lt=Kt[ee][1]||0,vn=Kt[ee-1][1]||0;zt+=Lt-vn}Ae+=Qe,He+=zt*Qe}le[ee-1][1]+=le[ee-1][0]=Y,Ae&&(Y-=He/Ae)}le[ee-1][1]+=le[ee-1][0]=Y,Tn(y,E)}}function ah(y){var E=y.map(ty);return Mn(y).sort(function(Y,ee){return E[Y]-E[ee]})}function ty(y){for(var E=-1,Y=0,ee=y.length,le,me=-1/0;++E<ee;)(le=+y[E][1])>me&&(me=le,Y=E);return Y}function oh(y){var E=y.map(sh);return Mn(y).sort(function(Y,ee){return E[Y]-E[ee]})}function sh(y){for(var E=0,Y=-1,ee=y.length,le;++Y<ee;)(le=+y[Y][1])&&(E+=le);return E}function ny(y){return oh(y).reverse()}function ry(y){var E=y.length,Y,ee,le=y.map(sh),me=ah(y),Me=0,re=0,Ae=[],He=[];for(Y=0;Y<E;++Y)ee=me[Y],Me<re?(Me+=le[ee],Ae.push(ee)):(re+=le[ee],He.push(ee));return He.reverse().concat(Ae)}function iy(y){return Mn(y).reverse()}n.arc=_,n.area=D,n.areaRadial=xe,n.curveBasis=Ct,n.curveBasisClosed=Fi,n.curveBasisOpen=yi,n.curveBundle=_i,n.curveCardinal=An,n.curveCardinalClosed=hr,n.curveCardinalOpen=xi,n.curveCatmullRom=Sr,n.curveCatmullRomClosed=Ur,n.curveCatmullRomOpen=bi,n.curveLinear=k,n.curveLinearClosed=O,n.curveMonotoneX=Be,n.curveMonotoneY=Ue,n.curveNatural=ct,n.curveStep=wn,n.curveStepAfter=ca,n.curveStepBefore=yn,n.line=z,n.lineRadial=Ee,n.linkHorizontal=be,n.linkRadial=ae,n.linkVertical=_e,n.pie=H,n.pointRadial=te,n.radialArea=xe,n.radialLine=Ee,n.stack=ih,n.stackOffsetDiverging=uu,n.stackOffsetExpand=rs,n.stackOffsetNone=Tn,n.stackOffsetSilhouette=$m,n.stackOffsetWiggle=ey,n.stackOrderAppearance=ah,n.stackOrderAscending=oh,n.stackOrderDescending=ny,n.stackOrderInsideOut=ry,n.stackOrderNone=Mn,n.stackOrderReverse=iy,n.symbol=un,n.symbolCircle=J,n.symbolCross=de,n.symbolDiamond=ne,n.symbolSquare=Ke,n.symbolStar=pe,n.symbolTriangle=ut,n.symbolWye=qt,n.symbols=Ot,Object.defineProperty(n,"__esModule",{value:!0})})}(Os,Os.exports)),Os.exports}(function(t,e){(function(n,r){r(e,V9(),K9())})(Ka,function(n,r,i){function a(C){return C.target.depth}function s(C){return C.depth}function l(C,_){return _-1-C.height}function u(C,_){return C.sourceLinks.length?C.depth:_-1}function c(C){return C.targetLinks.length?C.depth:C.sourceLinks.length?r.min(C.sourceLinks,a)-1:0}function f(C){return function(){return C}}function h(C,_){return g(C.source,_.source)||C.index-_.index}function d(C,_){return g(C.target,_.target)||C.index-_.index}function g(C,_){return C.y0-_.y0}function m(C){return C.value}function v(C){return C.index}function w(C){return C.nodes}function b(C){return C.links}function S(C,_){const M=C.get(_);if(!M)throw new Error("missing: "+_);return M}function A({nodes:C}){for(const _ of C){let M=_.y0,k=M;for(const p of _.sourceLinks)p.y0=M+p.width/2,M+=p.width;for(const p of _.targetLinks)p.y1=k+p.width/2,k+=p.width}}function N(){let C=0,_=0,M=1,k=1,p=24,R=8,z,D=v,W=u,B,H,$=w,oe=b,ye=6;function ge(){const Z={nodes:$.apply(null,arguments),links:oe.apply(null,arguments)};return Ee(Z),xe(Z),te(Z),ke(Z),se(Z),A(Z),Z}ge.update=function(Z){return A(Z),Z},ge.nodeId=function(Z){return arguments.length?(D=typeof Z=="function"?Z:f(Z),ge):D},ge.nodeAlign=function(Z){return arguments.length?(W=typeof Z=="function"?Z:f(Z),ge):W},ge.nodeSort=function(Z){return arguments.length?(B=Z,ge):B},ge.nodeWidth=function(Z){return arguments.length?(p=+Z,ge):p},ge.nodePadding=function(Z){return arguments.length?(R=z=+Z,ge):R},ge.nodes=function(Z){return arguments.length?($=typeof Z=="function"?Z:f(Z),ge):$},ge.links=function(Z){return arguments.length?(oe=typeof Z=="function"?Z:f(Z),ge):oe},ge.linkSort=function(Z){return arguments.length?(H=Z,ge):H},ge.size=function(Z){return arguments.length?(C=_=0,M=+Z[0],k=+Z[1],ge):[M-C,k-_]},ge.extent=function(Z){return arguments.length?(C=+Z[0][0],M=+Z[1][0],_=+Z[0][1],k=+Z[1][1],ge):[[C,_],[M,k]]},ge.iterations=function(Z){return arguments.length?(ye=+Z,ge):ye};function Ee({nodes:Z,links:ne}){for(const[Te,ve]of Z.entries())ve.index=Te,ve.sourceLinks=[],ve.targetLinks=[];const he=new Map(Z.map((Te,ve)=>[D(Te,ve,Z),Te]));for(const[Te,ve]of ne.entries()){ve.index=Te;let{source:Ie,target:pe}=ve;typeof Ie!="object"&&(Ie=ve.source=S(he,Ie)),typeof pe!="object"&&(pe=ve.target=S(he,pe)),Ie.sourceLinks.push(ve),pe.targetLinks.push(ve)}if(H!=null)for(const{sourceLinks:Te,targetLinks:ve}of Z)Te.sort(H),ve.sort(H)}function xe({nodes:Z}){for(const ne of Z)ne.value=ne.fixedValue===void 0?Math.max(r.sum(ne.sourceLinks,m),r.sum(ne.targetLinks,m)):ne.fixedValue}function te({nodes:Z}){const ne=Z.length;let he=new Set(Z),Te=new Set,ve=0;for(;he.size;){for(const Ie of he){Ie.depth=ve;for(const{target:pe}of Ie.sourceLinks)Te.add(pe)}if(++ve>ne)throw new Error("circular link");he=Te,Te=new Set}}function ke({nodes:Z}){const ne=Z.length;let he=new Set(Z),Te=new Set,ve=0;for(;he.size;){for(const Ie of he){Ie.height=ve;for(const{source:pe}of Ie.targetLinks)Te.add(pe)}if(++ve>ne)throw new Error("circular link");he=Te,Te=new Set}}function Se({nodes:Z}){const ne=r.max(Z,ve=>ve.depth)+1,he=(M-C-p)/(ne-1),Te=new Array(ne);for(const ve of Z){const Ie=Math.max(0,Math.min(ne-1,Math.floor(W.call(null,ve,ne))));ve.layer=Ie,ve.x0=C+Ie*he,ve.x1=ve.x0+p,Te[Ie]?Te[Ie].push(ve):Te[Ie]=[ve]}if(B)for(const ve of Te)ve.sort(B);return Te}function we(Z){const ne=r.min(Z,he=>(k-_-(he.length-1)*z)/r.sum(he,m));for(const he of Z){let Te=_;for(const ve of he){ve.y0=Te,ve.y1=Te+ve.value*ne,Te=ve.y1+z;for(const Ie of ve.sourceLinks)Ie.width=Ie.value*ne}Te=(k-Te+z)/(he.length+1);for(let ve=0;ve<he.length;++ve){const Ie=he[ve];Ie.y0+=Te*(ve+1),Ie.y1+=Te*(ve+1)}J(he)}}function se(Z){const ne=Se(Z);z=Math.min(R,(k-_)/(r.max(ne,he=>he.length)-1)),we(ne);for(let he=0;he<ye;++he){const Te=Math.pow(.99,he),ve=Math.max(1-Te,(he+1)/ye);K(ne,Te,ve),V(ne,Te,ve)}}function V(Z,ne,he){for(let Te=1,ve=Z.length;Te<ve;++Te){const Ie=Z[Te];for(const pe of Ie){let Ke=0,Ge=0;for(const{source:ot,value:Ze}of pe.targetLinks){let Et=Ze*(pe.layer-ot.layer);Ke+=de(ot,pe)*Et,Ge+=Et}if(!(Ge>0))continue;let ut=(Ke/Ge-pe.y0)*ne;pe.y0+=ut,pe.y1+=ut,ae(pe)}B===void 0&&Ie.sort(g),G(Ie,he)}}function K(Z,ne,he){for(let Te=Z.length,ve=Te-2;ve>=0;--ve){const Ie=Z[ve];for(const pe of Ie){let Ke=0,Ge=0;for(const{target:ot,value:Ze}of pe.sourceLinks){let Et=Ze*(ot.layer-pe.layer);Ke+=De(pe,ot)*Et,Ge+=Et}if(!(Ge>0))continue;let ut=(Ke/Ge-pe.y0)*ne;pe.y0+=ut,pe.y1+=ut,ae(pe)}B===void 0&&Ie.sort(g),G(Ie,he)}}function G(Z,ne){const he=Z.length>>1,Te=Z[he];_e(Z,Te.y0-z,he-1,ne),be(Z,Te.y1+z,he+1,ne),_e(Z,k,Z.length-1,ne),be(Z,_,0,ne)}function be(Z,ne,he,Te){for(;he<Z.length;++he){const ve=Z[he],Ie=(ne-ve.y0)*Te;Ie>1e-6&&(ve.y0+=Ie,ve.y1+=Ie),ne=ve.y1+z}}function _e(Z,ne,he,Te){for(;he>=0;--he){const ve=Z[he],Ie=(ve.y1-ne)*Te;Ie>1e-6&&(ve.y0-=Ie,ve.y1-=Ie),ne=ve.y0-z}}function ae({sourceLinks:Z,targetLinks:ne}){if(H===void 0){for(const{source:{sourceLinks:he}}of ne)he.sort(d);for(const{target:{targetLinks:he}}of Z)he.sort(h)}}function J(Z){if(H===void 0)for(const{sourceLinks:ne,targetLinks:he}of Z)ne.sort(d),he.sort(h)}function de(Z,ne){let he=Z.y0-(Z.sourceLinks.length-1)*z/2;for(const{target:Te,width:ve}of Z.sourceLinks){if(Te===ne)break;he+=ve+z}for(const{source:Te,width:ve}of ne.targetLinks){if(Te===Z)break;he-=ve}return he}function De(Z,ne){let he=ne.y0-(ne.targetLinks.length-1)*z/2;for(const{source:Te,width:ve}of ne.targetLinks){if(Te===Z)break;he+=ve+z}for(const{target:Te,width:ve}of Z.sourceLinks){if(Te===ne)break;he-=ve}return he}return ge}function I(C){return[C.source.x1,C.y0]}function T(C){return[C.target.x0,C.y1]}function L(){return i.linkHorizontal().source(I).target(T)}n.sankey=N,n.sankeyCenter=c,n.sankeyJustify=u,n.sankeyLeft=s,n.sankeyLinkHorizontal=L,n.sankeyRight=l,Object.defineProperty(n,"__esModule",{value:!0})})})(K0,K0.exports);var Q9=rt("<div><!></div>"),J9=rt("<div></div>"),$9=rt('<span class="text-foreground font-mono font-medium tabular-nums"> </span>'),e8=rt('<!> <div><div class="grid gap-1.5"><!> <span class="text-muted-foreground"> </span></div> <!></div>',1),t8=rt("<div><!></div>"),n8=rt('<div><!> <div class="grid gap-1.5"></div></div>');function R8(t,e){it(e,!0);const n=v=>{var w=Ce(),b=ce(w);{var S=A=>{var N=Q9(),I=nt(N);{var T=C=>{var _=Ce(),M=ce(_);Xe(M,()=>o(h)),Q(C,_)},L=C=>{var _=So();mt(()=>Jn(_,o(h))),Q(C,_)};ue(I,C=>{typeof o(h)=="function"?C(T):C(L,!1)})}$e(N),mt(C=>St(N,1,C),[()=>pt(_a("font-medium",e.labelClassName))]),Q(A,N)};ue(b,A=>{o(h)&&A(S)})}Q(v,w)};function r(v,w){return`${v}`}P(e,"ref",11,null);let i=P(e,"hideLabel",3,!1),a=P(e,"indicator",3,"dot"),s=P(e,"hideIndicator",3,!1),l=P(e,"labelFormatter",3,r),u=bt(e,["$$slots","$$events","$$legacy","ref","class","hideLabel","indicator","hideIndicator","labelKey","label","labelFormatter","labelClassName","formatter","nameKey","color"]);const c=Vy(),f=Vf(),h=x(()=>{var A,N;if(i()||!((A=f.payload)!=null&&A.length))return null;const[v]=f.payload,w=e.labelKey||(v==null?void 0:v.label)||(v==null?void 0:v.name)||"value",b=gh(c.config,v,w),S=!e.labelKey&&typeof e.label=="string"?((N=c.config[e.label])==null?void 0:N.label)||e.label:(b==null?void 0:b.label)??v.label;return S?l()?l()(S,f.payload):S:null}),d=x(()=>f.payload.length===1&&a()!=="dot");var g=Ce(),m=ce(g);pi(m,()=>Wm,(v,w)=>{w(v,{variant:"none",children:(b,S)=>{var A=n8();wt(A,L=>({class:L,...u}),[()=>_a("border-border/50 bg-background grid min-w-[9rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",e.class)]);var N=nt(A);{var I=L=>{n(L)};ue(N,L=>{o(d)||L(I)})}var T=Fe(N,2);$t(T,23,()=>f.payload,(L,C)=>L.key+C,(L,C,_)=>{var M=t8();const k=x(()=>`${e.nameKey||o(C).key||o(C).name||"value"}`),p=x(()=>gh(c.config,o(C),o(k))),R=x(()=>{var B;return e.color||((B=o(C).payload)==null?void 0:B.color)||o(C).color});var z=nt(M);{var D=B=>{var H=Ce(),$=ce(H);Xe($,()=>e.formatter,()=>({value:o(C).value,name:o(C).name,item:o(C),index:o(_),payload:f.payload})),Q(B,H)},W=B=>{var H=e8(),$=ce(H);{var oe=V=>{var K=Ce(),G=ce(K);pi(G,()=>o(p).icon,(be,_e)=>{_e(be,{})}),Q(V,K)},ye=(V,K)=>{{var G=be=>{var _e=J9();mt(ae=>{wr(_e,`--color-bg: ${o(R)??""}; --color-border: ${o(R)??""};`),St(_e,1,ae)},[()=>pt(_a("border-(--color-border) bg-(--color-bg) shrink-0 rounded-[2px]",{"size-2.5":a()==="dot","h-full w-1":a()==="line","w-0 border-[1.5px] border-dashed bg-transparent":a()==="dashed","my-0.5":o(d)&&a()==="dashed"}))]),Q(be,_e)};ue(V,be=>{s()||be(G)},K)}};ue($,V=>{var K;(K=o(p))!=null&&K.icon?V(oe):V(ye,!1)})}var ge=Fe($,2),Ee=nt(ge),xe=nt(Ee);{var te=V=>{n(V)};ue(xe,V=>{o(d)&&V(te)})}var ke=Fe(xe,2),Se=nt(ke,!0);$e(ke),$e(Ee);var we=Fe(Ee,2);{var se=V=>{var K=$9(),G=nt(K,!0);$e(K),mt(be=>Jn(G,be),[()=>o(C).value.toLocaleString()]),Q(V,K)};ue(we,V=>{o(C).value&&V(se)})}$e(ge),mt(V=>{var K;St(ge,1,V),Jn(Se,((K=o(p))==null?void 0:K.label)||o(C).name)},[()=>pt(_a("flex flex-1 shrink-0 justify-between leading-none",o(d)?"items-end":"items-center"))]),Q(B,H)};ue(z,B=>{e.formatter&&o(C).value!==void 0&&o(C).name?B(D):B(W,!1)})}$e(M),mt(B=>St(M,1,B),[()=>pt(_a("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:size-2.5",a()==="dot"&&"items-center"))]),Q(L,M)}),$e(T),$e(A),Q(b,A)},$$slots:{default:!0}})}),Q(t,g),at()}export{D8 as B,A8 as C,R8 as a};
