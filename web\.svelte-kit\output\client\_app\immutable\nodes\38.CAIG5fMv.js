import{f,t as g,a as o,c as tt}from"../chunks/BasJTneF.js";import{p as Sr,c as h,s as l,f as $,n as v,d as $e,m as aa,g as u,r as c,a as kr,t as B,e as Dt,w as ir,bc as vr,aJ as na,o as wt,k as Et,v as fs,x as Te}from"../chunks/CGmarHxI.js";import{s as C}from"../chunks/CIt1g2O9.js";import{i as fe}from"../chunks/u21ee2wt.js";import{e as zt,i as Ut}from"../chunks/C3w0v0gR.js";import{c as _}from"../chunks/BvdI7LR8.js";import{p as Vt}from"../chunks/Btcx8l8F.js";import{S as oa}from"../chunks/C6g8ubaU.js";import"../chunks/CgXBgsce.js";import{i as Ur}from"../chunks/BIEMS98f.js";import{C as Br}from"../chunks/DuGukytH.js";import{C as Zr}from"../chunks/Cdn-N1RY.js";import{C as Yr}from"../chunks/BkJY4La4.js";import{C as Hr}from"../chunks/DETxXRrJ.js";import{C as Gr}from"../chunks/GwmmX_iF.js";import{C as Kr}from"../chunks/D50jIuLr.js";import{B as Se}from"../chunks/B1K98fMG.js";import{B as Ot}from"../chunks/DaBofrVv.js";import{R as ia,D as la,a as ua}from"../chunks/WD4kvFhR.js";import{R as da,A as ca,a as fa,b as pa,c as ha,d as va,e as ma,f as $a}from"../chunks/BnikQ10_.js";import{t as jt}from"../chunks/DjPYYl4Z.js";import{B as yr}from"../chunks/hA0h0kTo.js";import{P as js}from"../chunks/DR5zc253.js";import{E as _a}from"../chunks/DdoUfFy4.js";import{D as Fr}from"../chunks/Z6UAQTuv.js";import{B as ga,L as ba}from"../chunks/Ce4BqqU6.js";import{S as ya}from"../chunks/DumgozFE.js";import{D as xa}from"../chunks/Dz4exfp3.js";import{T as Es}from"../chunks/C33xR25f.js";import{b as Ht,a as Ar,e as wa,s as Sa}from"../chunks/CmxjS0TN.js";import{b as ka}from"../chunks/VYoCKyli.js";import{p as Pa}from"../chunks/CWmzcjye.js";import{R as Ta,D as ja,a as Ea}from"../chunks/tdzGgazS.js";import{I as $r}from"../chunks/DMTMHyMa.js";import{L as Ct}from"../chunks/BvvicRXk.js";import{R as Gt,S as Kt,a as Xt,b as Ze}from"../chunks/CGK0g3x_.js";import{S as ps}from"../chunks/D9yI7a4E.js";import{D as Fa,a as Aa,b as Oa,c as Ca}from"../chunks/CKh8VGVX.js";import{I as Fs}from"../chunks/BuYRPDDz.js";import{S as Wt}from"../chunks/B2lQHLf_.js";import{c as Da}from"../chunks/nZgk9enP.js";import{g as It}from"../chunks/BiJhC7W5.js";import{P as dr,R as cr,T as fr,a as pr}from"../chunks/ChRM_Un0.js";import{B as As}from"../chunks/CDnvByek.js";import{M as Ma}from"../chunks/CwgkX8t9.js";import{D as Na}from"../chunks/6BxQgNmX.js";import{C as Ia}from"../chunks/DZCYCPd3.js";import{E as Ra}from"../chunks/zNKWipEG.js";import{T as za,R as Va}from"../chunks/I7hvcB12.js";import{a as La,b as qa,c as Ja,d as Ua,R as Ba}from"../chunks/CTn0v-X8.js";import{S as lr}from"../chunks/BPvdPoic.js";import{T as Or}from"../chunks/C88uNE8B.js";import{C as Za}from"../chunks/DW7T7T22.js";import{B as hs}from"../chunks/CIPPbbaT.js";import{T as Cr}from"../chunks/DmZyh-PW.js";import{S as Ya}from"../chunks/BoNCRmBc.js";import{R as vs}from"../chunks/qwsZpUIl.js";import{C as Ha}from"../chunks/DkmCSZhC.js";import{S as Ga}from"../chunks/KVutzy_p.js";import{X as Ka}from"../chunks/CnpHcmx3.js";import{C as Xa}from"../chunks/CKg8MWp_.js";var Wa=f("<!> Create Your First Alert",1),Qa=f(`<div class="rounded-lg border p-6 text-center"><div class="bg-foreground mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full"><!></div> <h3 class="mb-2 text-lg font-medium">No job alerts yet</h3> <p class="text-muted-foreground mx-auto mb-4 max-w-md">Create job alerts to get notified when new jobs matching your criteria are available. You'll
        receive email notifications based on your selected frequency.</p> <!></div>`),en=f(" <!>",1),tn=f('<!> <span class="sr-only">Open menu</span>',1),rn=f("<!> <span>Disable Alert</span>",1),sn=f("<!> <span>Enable Alert</span>",1),an=f("<!> <span>Edit Alert</span>",1),nn=f("<!> <span>Delete Alert</span>",1),on=f("<!> <!> <!> <!>",1),ln=f("<!> <!>",1),un=f('<div class="flex items-start justify-between"><div><!> <!> <div class="mt-2"><!></div></div> <!></div>'),dn=f('<div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 2L11 13"></path><path d="M22 2l-7 20-4-9-9-4 20-7z"></path></svg> <span class="font-medium">Last sent:</span> </div>'),cn=f('<div class="flex flex-wrap gap-x-6 gap-y-2 text-sm text-gray-500"><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="M12 6v6l4 2"></path></svg> <span class="font-medium">Frequency:</span> </div> <div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg> <span class="font-medium">Created:</span> </div> <!></div>'),fn=f('<div class="flex justify-end"><!></div>'),pn=f("<!> <!> <!>",1),hn=f('<div class="space-y-4"></div>'),vn=f("<!> <!>",1),mn=f("<!> <!>",1),$n=f("<!> <!>",1),_n=f("<div><!> <!></div>");function gn(s,e){Sr(e,!1);let t=Vt(e,"alerts",28,()=>[]),r=Vt(e,"onCreateAlert",8,()=>{}),a=aa(!1),n=null;const i=w=>new Date(w).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),d=w=>{switch(w){case"daily":return"Daily";case"weekly":return"Weekly";case"monthly":return"Monthly";default:return"As needed"}},p=w=>{n=w,jt.info("Edit functionality will be available soon")},m=w=>{n=w,$e(a,!0)},y=async w=>{try{const E=await fetch("/api/job-alerts",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:w.id,enabled:!w.enabled})}),te=await E.json();if(!E.ok)throw new Error(te.error||"Failed to update job alert");t(t().map(Y=>Y.id===w.id?{...Y,enabled:!Y.enabled}:Y)),jt.success(`Alert ${w.enabled?"disabled":"enabled"} successfully`)}catch(E){console.error("Error updating job alert:",E),jt.error("Failed to update job alert")}},b=async()=>{if(n)try{const w=await fetch("/api/job-alerts",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:n.id})}),E=await w.json();if(!w.ok)throw new Error(E.error||"Failed to delete job alert");t(t().filter(te=>te.id!==n.id)),$e(a,!1),n=null,jt.success("Alert deleted successfully")}catch(w){console.error("Error deleting job alert:",w),jt.error("Failed to delete job alert")}},M=w=>{if(!w)return"All jobs";const E=[];if(w.keywords&&E.push(`Keywords: ${w.keywords}`),w.location&&E.push(`Location: ${w.location}`),w.jobType){const te={full_time:"Full-time",part_time:"Part-time",contract:"Contract",temporary:"Temporary",internship:"Internship"};E.push(`Job Type: ${te[w.jobType]||w.jobType}`)}return w.remote&&E.push("Remote Only"),E.length>0?E.join(", "):"All jobs"},ue=w=>w?"default":"secondary",ne=w=>Math.floor(Math.random()*20);Ur();var K=_n(),de=h(K);{var be=w=>{var E=Qa(),te=h(E),Y=h(te);yr(Y,{class:"text-background h-6 w-6"}),c(te);var F=l(te,6);Se(F,{class:"mt-6",variant:"default",onclick:r(),children:(V,re)=>{var L=Wa(),ke=$(L);js(ke,{class:"mr-2 h-4 w-4"}),v(),o(V,L)},$$slots:{default:!0}}),c(E),o(w,E)},Q=w=>{var E=hn();zt(E,5,t,te=>te.id,(te,Y)=>{Br(te,{children:(F,V)=>{var re=pn(),L=$(re);Gr(L,{class:"p-4",children:(z,H)=>{var X=un(),je=h(X),Ee=h(je);Kr(Ee,{class:"flex items-center gap-2",children:(Ke,P)=>{v();var T=en(),N=$(T),Pt=l(N);const Xe=Dt(()=>ue(u(Y).enabled));Ot(Pt,{get variant(){return u(Xe)},children:(_t,gt)=>{v();var We=g();B(()=>C(We,u(Y).enabled?"Active":"Disabled")),o(_t,We)},$$slots:{default:!0}}),B(()=>C(N,`${u(Y).name??""} `)),o(Ke,T)},$$slots:{default:!0}});var kt=l(Ee,2);Yr(kt,{class:"mt-1",children:(Ke,P)=>{v();var T=g();B(N=>C(T,N),[()=>M(u(Y).searchParams)],Dt),o(Ke,T)},$$slots:{default:!0}});var $t=l(kt,2),Bt=h($t);Ot(Bt,{variant:"outline",class:"text-xs",children:(Ke,P)=>{v();var T=g();B(N=>C(T,`${N??""} matching jobs`),[()=>ne(u(Y))],Dt),o(Ke,T)},$$slots:{default:!0}}),c($t),c(je);var qt=l(je,2);ia(qt,{children:(Ke,P)=>{var T=ln(),N=$(T);la(N,{children:(Xe,_t)=>{Se(Xe,{variant:"ghost",size:"icon",class:"h-8 w-8",children:(gt,We)=>{var ot=tn(),Nt=$(ot);_a(Nt,{class:"h-4 w-4"}),v(2),o(gt,ot)},$$slots:{default:!0}})},$$slots:{default:!0}});var Pt=l(N,2);ua(Pt,{align:"end",children:(Xe,_t)=>{var gt=on(),We=$(gt);Fr(We,{onclick:()=>y(u(Y)),class:"flex items-center gap-2",children:(Je,rt)=>{var Ye=tt(),Ce=$(Ye);{var Ue=De=>{var Me=rn(),Ne=$(Me);ga(Ne,{class:"h-4 w-4"}),v(2),o(De,Me)},Ie=De=>{var Me=sn(),Ne=$(Me);yr(Ne,{class:"h-4 w-4"}),v(2),o(De,Me)};fe(Ce,De=>{u(Y).enabled?De(Ue):De(Ie,!1)})}o(Je,Ye)},$$slots:{default:!0}});var ot=l(We,2);Fr(ot,{onclick:()=>p(u(Y)),class:"flex items-center gap-2",children:(Je,rt)=>{var Ye=an(),Ce=$(Ye);ya(Ce,{class:"h-4 w-4"}),v(2),o(Je,Ye)},$$slots:{default:!0}});var Nt=l(ot,2);xa(Nt,{});var bt=l(Nt,2);Fr(bt,{onclick:()=>m(u(Y)),class:"flex items-center gap-2 text-red-600",children:(Je,rt)=>{var Ye=nn(),Ce=$(Ye);Es(Ce,{class:"h-4 w-4"}),v(2),o(Je,Ye)},$$slots:{default:!0}}),o(Xe,gt)},$$slots:{default:!0}}),o(Ke,T)},$$slots:{default:!0}}),c(X),o(z,X)},$$slots:{default:!0}});var ke=l(L,2);Zr(ke,{class:"p-4 pt-0",children:(z,H)=>{var X=cn(),je=h(X),Ee=l(h(je),3);c(je);var kt=l(je,2),$t=l(h(kt),3);c(kt);var Bt=l(kt,2);{var qt=Ke=>{var P=dn(),T=l(h(P),3);c(P),B(N=>C(T,` ${N??""}`),[()=>i(u(Y).lastSentAt)],Dt),o(Ke,P)};fe(Bt,Ke=>{u(Y).lastSentAt&&Ke(qt)})}c(X),B((Ke,P)=>{C(Ee,` ${Ke??""}`),C($t,` ${P??""}`)},[()=>d(u(Y).frequency),()=>i(u(Y).createdAt)],Dt),o(z,X)},$$slots:{default:!0}});var Ge=l(ke,2);Hr(Ge,{class:"p-4 pt-0",children:(z,H)=>{var X=fn(),je=h(X);Se(je,{variant:"outline",size:"sm",class:"text-xs",onclick:()=>window.location.href="/dashboard/jobs",children:(Ee,kt)=>{v();var $t=g("View Matching Jobs");o(Ee,$t)},$$slots:{default:!0}}),c(X),o(z,X)},$$slots:{default:!0}}),o(F,re)},$$slots:{default:!0}})}),c(E),o(w,E)};fe(de,w=>{t().length===0?w(be):w(Q,!1)})}var he=l(de,2);da(he,{get open(){return u(a)},children:(w,E)=>{ca(w,{children:(te,Y)=>{var F=$n(),V=$(F);fa(V,{children:(L,ke)=>{var Ge=vn(),z=$(Ge);pa(z,{children:(X,je)=>{v();var Ee=g("Delete Job Alert");o(X,Ee)},$$slots:{default:!0}});var H=l(z,2);ha(H,{children:(X,je)=>{v();var Ee=g("Are you sure you want to delete this job alert? This action cannot be undone.");o(X,Ee)},$$slots:{default:!0}}),o(L,Ge)},$$slots:{default:!0}});var re=l(V,2);va(re,{children:(L,ke)=>{var Ge=mn(),z=$(Ge);ma(z,{onclick:()=>$e(a,!1),children:(X,je)=>{v();var Ee=g("Cancel");o(X,Ee)},$$slots:{default:!0}});var H=l(z,2);$a(H,{onclick:b,children:(X,je)=>{v();var Ee=g("Delete");o(X,Ee)},$$slots:{default:!0}}),o(L,Ge)},$$slots:{default:!0}}),o(te,F)},$$slots:{default:!0}})},$$slots:{default:!0}}),c(K),o(s,K),kr()}var ms=Object.prototype.hasOwnProperty;function Ir(s,e){var t,r;if(s===e)return!0;if(s&&e&&(t=s.constructor)===e.constructor){if(t===Date)return s.getTime()===e.getTime();if(t===RegExp)return s.toString()===e.toString();if(t===Array){if((r=s.length)===e.length)for(;r--&&Ir(s[r],e[r]););return r===-1}if(!t||typeof s=="object"){r=0;for(t in s)if(ms.call(s,t)&&++r&&!ms.call(e,t)||!(t in e)||!Ir(s[t],e[t]))return!1;return Object.keys(e).length===r}}return s!==s&&e!==e}function bn(s){return new Promise(e=>{s.subscribe(e)()})}function yn(s,e,t){s.update(r=>(Ds(r,e,t),r))}function xn(s){return JSON.parse(JSON.stringify(s))}function Xr(s){return s==null}function Os(s){return Xr(s)||Object.keys(s).length<=0}function Cs(s){let e=[];for(const[,t]of Object.entries(s)){const r=typeof t=="object"?Cs(t):[t];e=[...e,...r]}return e}function Rr(s,e,t={}){for(const r in e)switch(!0){case(e[r].type==="object"&&!Os(e[r].fields)):{t[r]=Rr(s[r],e[r].fields,{...t[r]});break}case e[r].type==="array":{const a=s&&s[r]?s[r]:[];t[r]=a.map(n=>{const i=Rr(n,e[r].innerType.fields,{...t[r]});return Object.keys(i).length>0?i:""});break}default:t[r]=""}return t}const wn=Ir;function zr(s,e){if(Array.isArray(s))return s.map(r=>zr(r,e));const t={};for(const r in s)t[r]=typeof s[r]=="object"&&!Xr(s[r])?zr(s[r],e):e;return t}function Ds(s,e,t){if(new Object(s)!==s)return s;Array.isArray(e)||(e=e.toString().match(/[^.[\]]+/g)||[]);const r=e.slice(0,-1).reduce((a,n,i)=>new Object(a[n])===a[n]?a[n]:a[n]=Math.trunc(Math.abs(e[i+1]))===+e[i+1]?[]:{},s);return r[e[e.length-1]]=t,s}const He={assignDeep:zr,cloneDeep:xn,deepEqual:wn,getErrorsFromSchema:Rr,getValues:Cs,isEmpty:Os,isNullish:Xr,set:Ds,subscribeOnce:bn,update:yn},$s="";function Sn(s){return s.getAttribute&&s.getAttribute("type")==="checkbox"}function kn(s){return s.getAttribute&&s.getAttribute("type")==="file"}function Pn(s){return kn(s)?s.files:Sn(s)?s.checked:s.value}const Tn=s=>{let e=s.initialValues||{};const t=s.validationSchema,r=s.validate,a=s.onSubmit,n={values:()=>He.cloneDeep(e),errors:()=>t?He.getErrorsFromSchema(e,t.fields):He.assignDeep(e,$s),touched:()=>He.assignDeep(e,!1)},i=ir(n.values()),d=ir(n.errors()),p=ir(n.touched()),m=ir(!1),y=ir(!1),b=vr(d,F=>He.getValues(F).every(re=>re===$s)),M=vr(i,F=>{const V=He.assignDeep(F,!1);for(let re in F)V[re]=!He.deepEqual(F[re],e[re]);return V}),ue=vr(M,F=>He.getValues(F).includes(!0));function ne(F){return He.subscribeOnce(i).then(V=>K(F,V[F]))}function K(F,V){return te(F,!0),t?(y.set(!0),t.validateAt(F,na(i)).then(()=>He.update(d,F,"")).catch(re=>He.update(d,F,re.message)).finally(()=>{y.set(!1)})):r?(y.set(!0),Promise.resolve().then(()=>r({[F]:V})).then(re=>He.update(d,F,He.isNullish(re)?"":re[F])).finally(()=>{y.set(!1)})):Promise.resolve()}function de(F,V){return E(F,V),K(F,V)}function be(F){const V=F.target,re=V.name||V.id,L=Pn(V);return de(re,L)}function Q(F){return F&&F.preventDefault&&F.preventDefault(),m.set(!0),He.subscribeOnce(i).then(V=>typeof r=="function"?(y.set(!0),Promise.resolve().then(()=>r(V)).then(re=>{if(He.isNullish(re)||He.getValues(re).length===0)return w(V);d.set(re),m.set(!1)}).finally(()=>y.set(!1))):t?(y.set(!0),t.validate(V,{abortEarly:!1}).then(()=>w(V)).catch(re=>{if(re&&re.inner){const L=n.errors();re.inner.map(ke=>He.set(L,ke.path,ke.message)),d.set(L)}m.set(!1)}).finally(()=>y.set(!1))):w(V))}function he(){i.set(n.values()),d.set(n.errors()),p.set(n.touched())}function w(F){return Promise.resolve().then(()=>d.set(n.errors())).then(()=>a(F,i,d)).finally(()=>m.set(!1))}function E(F,V){He.update(i,F,V)}function te(F,V){He.update(p,F,V)}function Y(F){e=F,he()}return{form:i,errors:d,touched:p,modified:M,isValid:b,isSubmitting:m,isValidating:y,isModified:ue,handleChange:be,handleSubmit:Q,handleReset:he,updateField:E,updateValidateField:de,updateTouched:te,validateField:ne,updateInitialValues:Y,state:vr([i,d,p,M,b,y,m,ue],([F,V,re,L,ke,Ge,z,H])=>({form:F,errors:V,touched:re,modified:L,isValid:ke,isSubmitting:z,isValidating:Ge,isModified:H}))}};var xe={};function er(s){this._maxSize=s,this.clear()}er.prototype.clear=function(){this._size=0,this._values=Object.create(null)};er.prototype.get=function(s){return this._values[s]};er.prototype.set=function(s,e){return this._size>=this._maxSize&&this.clear(),s in this._values||this._size++,this._values[s]=e};var jn=/[^.^\]^[]+|(?=\[\]|\.\.)/g,Ms=/^\d+$/,En=/^\d/,Fn=/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/g,An=/^\s*(['"]?)(.*?)(\1)\s*$/,Wr=512,_s=new er(Wr),gs=new er(Wr),bs=new er(Wr),On={Cache:er,split:Vr,normalizePath:Dr,setter:function(s){var e=Dr(s);return gs.get(s)||gs.set(s,function(r,a){for(var n=0,i=e.length,d=r;n<i-1;){var p=e[n];if(p==="__proto__"||p==="constructor"||p==="prototype")return r;d=d[e[n++]]}d[e[n]]=a})},getter:function(s,e){var t=Dr(s);return bs.get(s)||bs.set(s,function(a){for(var n=0,i=t.length;n<i;)if(a!=null||!e)a=a[t[n++]];else return;return a})},join:function(s){return s.reduce(function(e,t){return e+(Qr(t)||Ms.test(t)?"["+t+"]":(e?".":"")+t)},"")},forEach:function(s,e,t){Cn(Array.isArray(s)?s:Vr(s),e,t)}};function Dr(s){return _s.get(s)||_s.set(s,Vr(s).map(function(e){return e.replace(An,"$2")}))}function Vr(s){return s.match(jn)||[""]}function Cn(s,e,t){var r=s.length,a,n,i,d;for(n=0;n<r;n++)a=s[n],a&&(Nn(a)&&(a='"'+a+'"'),d=Qr(a),i=!d&&/^\d+$/.test(a),e.call(t,a,d,i,n,s))}function Qr(s){return typeof s=="string"&&s&&["'",'"'].indexOf(s.charAt(0))!==-1}function Dn(s){return s.match(En)&&!s.match(Ms)}function Mn(s){return Fn.test(s)}function Nn(s){return!Qr(s)&&(Dn(s)||Mn(s))}const In=/[A-Z\xc0-\xd6\xd8-\xde]?[a-z\xdf-\xf6\xf8-\xff]+(?:['’](?:d|ll|m|re|s|t|ve))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde]|$)|(?:[A-Z\xc0-\xd6\xd8-\xde]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:D|LL|M|RE|S|T|VE))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde](?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])|$)|[A-Z\xc0-\xd6\xd8-\xde]?(?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:d|ll|m|re|s|t|ve))?|[A-Z\xc0-\xd6\xd8-\xde]+(?:['’](?:D|LL|M|RE|S|T|VE))?|\d*(?:1ST|2ND|3RD|(?![123])\dTH)(?=\b|[a-z_])|\d*(?:1st|2nd|3rd|(?![123])\dth)(?=\b|[A-Z_])|\d+|(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?)*/g,Pr=s=>s.match(In)||[],Tr=s=>s[0].toUpperCase()+s.slice(1),es=(s,e)=>Pr(s).join(e).toLowerCase(),Ns=s=>Pr(s).reduce((e,t)=>`${e}${e?t[0].toUpperCase()+t.slice(1).toLowerCase():t.toLowerCase()}`,""),Rn=s=>Tr(Ns(s)),zn=s=>es(s,"_"),Vn=s=>es(s,"-"),Ln=s=>Tr(es(s," ")),qn=s=>Pr(s).map(Tr).join(" ");var Jn={words:Pr,upperFirst:Tr,camelCase:Ns,pascalCase:Rn,snakeCase:zn,kebabCase:Vn,sentenceCase:Ln,titleCase:qn},ts={exports:{}};ts.exports=function(s){return Is(Un(s),s)};ts.exports.array=Is;function Is(s,e){var t=s.length,r=new Array(t),a={},n=t,i=Bn(e),d=Zn(s);for(e.forEach(function(m){if(!d.has(m[0])||!d.has(m[1]))throw new Error("Unknown node. There is an unknown node in the supplied edges.")});n--;)a[n]||p(s[n],n,new Set);return r;function p(m,y,b){if(b.has(m)){var M;try{M=", node was:"+JSON.stringify(m)}catch{M=""}throw new Error("Cyclic dependency"+M)}if(!d.has(m))throw new Error("Found unknown node. Make sure to provided all involved nodes. Unknown node: "+JSON.stringify(m));if(!a[y]){a[y]=!0;var ue=i.get(m)||new Set;if(ue=Array.from(ue),y=ue.length){b.add(m);do{var ne=ue[--y];p(ne,d.get(ne),b)}while(y);b.delete(m)}r[--t]=m}}}function Un(s){for(var e=new Set,t=0,r=s.length;t<r;t++){var a=s[t];e.add(a[0]),e.add(a[1])}return Array.from(e)}function Bn(s){for(var e=new Map,t=0,r=s.length;t<r;t++){var a=s[t];e.has(a[0])||e.set(a[0],new Set),e.has(a[1])||e.set(a[1],new Set),e.get(a[0]).add(a[1])}return e}function Zn(s){for(var e=new Map,t=0,r=s.length;t<r;t++)e.set(s[t],t);return e}var Yn=ts.exports;Object.defineProperty(xe,"__esModule",{value:!0});var Qt=On,Mr=Jn,Hn=Yn;function Gn(s){return s&&typeof s=="object"&&"default"in s?s:{default:s}}var Kn=Gn(Hn);const Xn=Object.prototype.toString,Wn=Error.prototype.toString,Qn=RegExp.prototype.toString,eo=typeof Symbol<"u"?Symbol.prototype.toString:()=>"",to=/^Symbol\((.*)\)(.*)$/;function ro(s){return s!=+s?"NaN":s===0&&1/s<0?"-0":""+s}function ys(s,e=!1){if(s==null||s===!0||s===!1)return""+s;const t=typeof s;if(t==="number")return ro(s);if(t==="string")return e?`"${s}"`:s;if(t==="function")return"[Function "+(s.name||"anonymous")+"]";if(t==="symbol")return eo.call(s).replace(to,"Symbol($1)");const r=Xn.call(s).slice(8,-1);return r==="Date"?isNaN(s.getTime())?""+s:s.toISOString(s):r==="Error"||s instanceof Error?"["+Wn.call(s)+"]":r==="RegExp"?Qn.call(s):null}function Lt(s,e){let t=ys(s,e);return t!==null?t:JSON.stringify(s,function(r,a){let n=ys(this[r],e);return n!==null?n:a},2)}function Rs(s){return s==null?[]:[].concat(s)}let zs,Vs,Ls,so=/\$\{\s*(\w+)\s*\}/g;zs=Symbol.toStringTag;class xs{constructor(e,t,r,a){this.name=void 0,this.message=void 0,this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=void 0,this.inner=void 0,this[zs]="Error",this.name="ValidationError",this.value=t,this.path=r,this.type=a,this.errors=[],this.inner=[],Rs(e).forEach(n=>{if(nt.isError(n)){this.errors.push(...n.errors);const i=n.inner.length?n.inner:[n];this.inner.push(...i)}else this.errors.push(n)}),this.message=this.errors.length>1?`${this.errors.length} errors occurred`:this.errors[0]}}Vs=Symbol.hasInstance;Ls=Symbol.toStringTag;class nt extends Error{static formatError(e,t){const r=t.label||t.path||"this";return t=Object.assign({},t,{path:r,originalPath:t.path}),typeof e=="string"?e.replace(so,(a,n)=>Lt(t[n])):typeof e=="function"?e(t):e}static isError(e){return e&&e.name==="ValidationError"}constructor(e,t,r,a,n){const i=new xs(e,t,r,a);if(n)return i;super(),this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=[],this.inner=[],this[Ls]="Error",this.name=i.name,this.message=i.message,this.type=i.type,this.value=i.value,this.path=i.path,this.errors=i.errors,this.inner=i.inner,Error.captureStackTrace&&Error.captureStackTrace(this,nt)}static[Vs](e){return xs[Symbol.hasInstance](e)||super[Symbol.hasInstance](e)}}let Rt={default:"${path} is invalid",required:"${path} is a required field",defined:"${path} must be defined",notNull:"${path} cannot be null",oneOf:"${path} must be one of the following values: ${values}",notOneOf:"${path} must not be one of the following values: ${values}",notType:({path:s,type:e,value:t,originalValue:r})=>{const a=r!=null&&r!==t?` (cast from the value \`${Lt(r,!0)}\`).`:".";return e!=="mixed"?`${s} must be a \`${e}\` type, but the final value was: \`${Lt(t,!0)}\``+a:`${s} must match the configured type. The validated value was: \`${Lt(t,!0)}\``+a}},St={length:"${path} must be exactly ${length} characters",min:"${path} must be at least ${min} characters",max:"${path} must be at most ${max} characters",matches:'${path} must match the following: "${regex}"',email:"${path} must be a valid email",url:"${path} must be a valid URL",uuid:"${path} must be a valid UUID",datetime:"${path} must be a valid ISO date-time",datetime_precision:"${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits",datetime_offset:'${path} must be a valid ISO date-time with UTC "Z" timezone',trim:"${path} must be a trimmed string",lowercase:"${path} must be a lowercase string",uppercase:"${path} must be a upper case string"},Zt={min:"${path} must be greater than or equal to ${min}",max:"${path} must be less than or equal to ${max}",lessThan:"${path} must be less than ${less}",moreThan:"${path} must be greater than ${more}",positive:"${path} must be a positive number",negative:"${path} must be a negative number",integer:"${path} must be an integer"},Lr={min:"${path} field must be later than ${min}",max:"${path} field must be at earlier than ${max}"},qr={isValue:"${path} field must be ${value}"},_r={noUnknown:"${path} field has unspecified keys: ${unknown}",exact:"${path} object contains unknown properties: ${properties}"},gr={min:"${path} field must have at least ${min} items",max:"${path} field must have less than or equal to ${max} items",length:"${path} must have ${length} items"},qs={notType:s=>{const{path:e,value:t,spec:r}=s,a=r.types.length;if(Array.isArray(t)){if(t.length<a)return`${e} tuple value has too few items, expected a length of ${a} but got ${t.length} for value: \`${Lt(t,!0)}\``;if(t.length>a)return`${e} tuple value has too many items, expected a length of ${a} but got ${t.length} for value: \`${Lt(t,!0)}\``}return nt.formatError(Rt.notType,s)}};var Js=Object.assign(Object.create(null),{mixed:Rt,string:St,number:Zt,date:Lr,object:_r,array:gr,boolean:qr,tuple:qs});const tr=s=>s&&s.__isYupSchema__;class xr{static fromOptions(e,t){if(!t.then&&!t.otherwise)throw new TypeError("either `then:` or `otherwise:` is required for `when()` conditions");let{is:r,then:a,otherwise:n}=t,i=typeof r=="function"?r:(...d)=>d.every(p=>p===r);return new xr(e,(d,p)=>{var m;let y=i(...d)?a:n;return(m=y==null?void 0:y(p))!=null?m:p})}constructor(e,t){this.fn=void 0,this.refs=e,this.refs=e,this.fn=t}resolve(e,t){let r=this.refs.map(n=>n.getValue(t==null?void 0:t.value,t==null?void 0:t.parent,t==null?void 0:t.context)),a=this.fn(r,e,t);if(a===void 0||a===e)return e;if(!tr(a))throw new TypeError("conditions must return a schema object");return a.resolve(t)}}const mr={context:"$",value:"."};function ao(s,e){return new Yt(s,e)}class Yt{constructor(e,t={}){if(this.key=void 0,this.isContext=void 0,this.isValue=void 0,this.isSibling=void 0,this.path=void 0,this.getter=void 0,this.map=void 0,typeof e!="string")throw new TypeError("ref must be a string, got: "+e);if(this.key=e.trim(),e==="")throw new TypeError("ref must be a non-empty string");this.isContext=this.key[0]===mr.context,this.isValue=this.key[0]===mr.value,this.isSibling=!this.isContext&&!this.isValue;let r=this.isContext?mr.context:this.isValue?mr.value:"";this.path=this.key.slice(r.length),this.getter=this.path&&Qt.getter(this.path,!0),this.map=t.map}getValue(e,t,r){let a=this.isContext?r:this.isValue?e:t;return this.getter&&(a=this.getter(a||{})),this.map&&(a=this.map(a)),a}cast(e,t){return this.getValue(e,t==null?void 0:t.parent,t==null?void 0:t.context)}resolve(){return this}describe(){return{type:"ref",key:this.key}}toString(){return`Ref(${this.key})`}static isRef(e){return e&&e.__isYupRef}}Yt.prototype.__isYupRef=!0;const Mt=s=>s==null;function sr(s){function e({value:t,path:r="",options:a,originalValue:n,schema:i},d,p){const{name:m,test:y,params:b,message:M,skipAbsent:ue}=s;let{parent:ne,context:K,abortEarly:de=i.spec.abortEarly,disableStackTrace:be=i.spec.disableStackTrace}=a;function Q(L){return Yt.isRef(L)?L.getValue(t,ne,K):L}function he(L={}){const ke=Object.assign({value:t,originalValue:n,label:i.spec.label,path:L.path||r,spec:i.spec,disableStackTrace:L.disableStackTrace||be},b,L.params);for(const z of Object.keys(ke))ke[z]=Q(ke[z]);const Ge=new nt(nt.formatError(L.message||M,ke),t,ke.path,L.type||m,ke.disableStackTrace);return Ge.params=ke,Ge}const w=de?d:p;let E={path:r,parent:ne,type:m,from:a.from,createError:he,resolve:Q,options:a,originalValue:n,schema:i};const te=L=>{nt.isError(L)?w(L):L?p(null):w(he())},Y=L=>{nt.isError(L)?w(L):d(L)};if(ue&&Mt(t))return te(!0);let V;try{var re;if(V=y.call(E,t,E),typeof((re=V)==null?void 0:re.then)=="function"){if(a.sync)throw new Error(`Validation test of type: "${E.type}" returned a Promise during a synchronous validate. This test will finish after the validate call has returned`);return Promise.resolve(V).then(te,Y)}}catch(L){Y(L);return}te(V)}return e.OPTIONS=s,e}function rs(s,e,t,r=t){let a,n,i;return e?(Qt.forEach(e,(d,p,m)=>{let y=p?d.slice(1,d.length-1):d;s=s.resolve({context:r,parent:a,value:t});let b=s.type==="tuple",M=m?parseInt(y,10):0;if(s.innerType||b){if(b&&!m)throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part "${i}" must contain an index to the tuple element, e.g. "${i}[0]"`);if(t&&M>=t.length)throw new Error(`Yup.reach cannot resolve an array item at index: ${d}, in the path: ${e}. because there is no value at that index. `);a=t,t=t&&t[M],s=b?s.spec.types[M]:s.innerType}if(!m){if(!s.fields||!s.fields[y])throw new Error(`The schema does not contain the path: ${e}. (failed at: ${i} which is a type: "${s.type}")`);a=t,t=t&&t[y],s=s.fields[y]}n=y,i=p?"["+d+"]":"."+d}),{schema:s,parent:a,parentPath:n}):{parent:a,parentPath:e,schema:s}}function no(s,e,t,r){return rs(s,e,t,r).schema}class wr extends Set{describe(){const e=[];for(const t of this.values())e.push(Yt.isRef(t)?t.describe():t);return e}resolveAll(e){let t=[];for(const r of this.values())t.push(e(r));return t}clone(){return new wr(this.values())}merge(e,t){const r=this.clone();return e.forEach(a=>r.add(a)),t.forEach(a=>r.delete(a)),r}}function ar(s,e=new Map){if(tr(s)||!s||typeof s!="object")return s;if(e.has(s))return e.get(s);let t;if(s instanceof Date)t=new Date(s.getTime()),e.set(s,t);else if(s instanceof RegExp)t=new RegExp(s),e.set(s,t);else if(Array.isArray(s)){t=new Array(s.length),e.set(s,t);for(let r=0;r<s.length;r++)t[r]=ar(s[r],e)}else if(s instanceof Map){t=new Map,e.set(s,t);for(const[r,a]of s.entries())t.set(r,ar(a,e))}else if(s instanceof Set){t=new Set,e.set(s,t);for(const r of s)t.add(ar(r,e))}else if(s instanceof Object){t={},e.set(s,t);for(const[r,a]of Object.entries(s))t[r]=ar(a,e)}else throw Error(`Unable to clone ${s}`);return t}class dt{constructor(e){this.type=void 0,this.deps=[],this.tests=void 0,this.transforms=void 0,this.conditions=[],this._mutate=void 0,this.internalTests={},this._whitelist=new wr,this._blacklist=new wr,this.exclusiveTests=Object.create(null),this._typeCheck=void 0,this.spec=void 0,this.tests=[],this.transforms=[],this.withMutation(()=>{this.typeError(Rt.notType)}),this.type=e.type,this._typeCheck=e.check,this.spec=Object.assign({strip:!1,strict:!1,abortEarly:!0,recursive:!0,disableStackTrace:!1,nullable:!1,optional:!0,coerce:!0},e==null?void 0:e.spec),this.withMutation(t=>{t.nonNullable()})}get _type(){return this.type}clone(e){if(this._mutate)return e&&Object.assign(this.spec,e),this;const t=Object.create(Object.getPrototypeOf(this));return t.type=this.type,t._typeCheck=this._typeCheck,t._whitelist=this._whitelist.clone(),t._blacklist=this._blacklist.clone(),t.internalTests=Object.assign({},this.internalTests),t.exclusiveTests=Object.assign({},this.exclusiveTests),t.deps=[...this.deps],t.conditions=[...this.conditions],t.tests=[...this.tests],t.transforms=[...this.transforms],t.spec=ar(Object.assign({},this.spec,e)),t}label(e){let t=this.clone();return t.spec.label=e,t}meta(...e){if(e.length===0)return this.spec.meta;let t=this.clone();return t.spec.meta=Object.assign(t.spec.meta||{},e[0]),t}withMutation(e){let t=this._mutate;this._mutate=!0;let r=e(this);return this._mutate=t,r}concat(e){if(!e||e===this)return this;if(e.type!==this.type&&this.type!=="mixed")throw new TypeError(`You cannot \`concat()\` schema's of different types: ${this.type} and ${e.type}`);let t=this,r=e.clone();const a=Object.assign({},t.spec,r.spec);return r.spec=a,r.internalTests=Object.assign({},t.internalTests,r.internalTests),r._whitelist=t._whitelist.merge(e._whitelist,e._blacklist),r._blacklist=t._blacklist.merge(e._blacklist,e._whitelist),r.tests=t.tests,r.exclusiveTests=t.exclusiveTests,r.withMutation(n=>{e.tests.forEach(i=>{n.test(i.OPTIONS)})}),r.transforms=[...t.transforms,...r.transforms],r}isType(e){return e==null?!!(this.spec.nullable&&e===null||this.spec.optional&&e===void 0):this._typeCheck(e)}resolve(e){let t=this;if(t.conditions.length){let r=t.conditions;t=t.clone(),t.conditions=[],t=r.reduce((a,n)=>n.resolve(a,e),t),t=t.resolve(e)}return t}resolveOptions(e){var t,r,a,n;return Object.assign({},e,{from:e.from||[],strict:(t=e.strict)!=null?t:this.spec.strict,abortEarly:(r=e.abortEarly)!=null?r:this.spec.abortEarly,recursive:(a=e.recursive)!=null?a:this.spec.recursive,disableStackTrace:(n=e.disableStackTrace)!=null?n:this.spec.disableStackTrace})}cast(e,t={}){let r=this.resolve(Object.assign({value:e},t)),a=t.assert==="ignore-optionality",n=r._cast(e,t);if(t.assert!==!1&&!r.isType(n)){if(a&&Mt(n))return n;let i=Lt(e),d=Lt(n);throw new TypeError(`The value of ${t.path||"field"} could not be cast to a value that satisfies the schema type: "${r.type}". 

attempted value: ${i} 
`+(d!==i?`result of cast: ${d}`:""))}return n}_cast(e,t){let r=e===void 0?e:this.transforms.reduce((a,n)=>n.call(this,a,e,this),e);return r===void 0&&(r=this.getDefault(t)),r}_validate(e,t={},r,a){let{path:n,originalValue:i=e,strict:d=this.spec.strict}=t,p=e;d||(p=this._cast(p,Object.assign({assert:!1},t)));let m=[];for(let y of Object.values(this.internalTests))y&&m.push(y);this.runTests({path:n,value:p,originalValue:i,options:t,tests:m},r,y=>{if(y.length)return a(y,p);this.runTests({path:n,value:p,originalValue:i,options:t,tests:this.tests},r,a)})}runTests(e,t,r){let a=!1,{tests:n,value:i,originalValue:d,path:p,options:m}=e,y=K=>{a||(a=!0,t(K,i))},b=K=>{a||(a=!0,r(K,i))},M=n.length,ue=[];if(!M)return b([]);let ne={value:i,originalValue:d,path:p,options:m,schema:this};for(let K=0;K<n.length;K++){const de=n[K];de(ne,y,function(Q){Q&&(Array.isArray(Q)?ue.push(...Q):ue.push(Q)),--M<=0&&b(ue)})}}asNestedTest({key:e,index:t,parent:r,parentPath:a,originalParent:n,options:i}){const d=e??t;if(d==null)throw TypeError("Must include `key` or `index` for nested validations");const p=typeof d=="number";let m=r[d];const y=Object.assign({},i,{strict:!0,parent:r,value:m,originalValue:n[d],key:void 0,[p?"index":"key"]:d,path:p||d.includes(".")?`${a||""}[${p?d:`"${d}"`}]`:(a?`${a}.`:"")+e});return(b,M,ue)=>this.resolve(y)._validate(m,y,M,ue)}validate(e,t){var r;let a=this.resolve(Object.assign({},t,{value:e})),n=(r=t==null?void 0:t.disableStackTrace)!=null?r:a.spec.disableStackTrace;return new Promise((i,d)=>a._validate(e,t,(p,m)=>{nt.isError(p)&&(p.value=m),d(p)},(p,m)=>{p.length?d(new nt(p,m,void 0,void 0,n)):i(m)}))}validateSync(e,t){var r;let a=this.resolve(Object.assign({},t,{value:e})),n,i=(r=t==null?void 0:t.disableStackTrace)!=null?r:a.spec.disableStackTrace;return a._validate(e,Object.assign({},t,{sync:!0}),(d,p)=>{throw nt.isError(d)&&(d.value=p),d},(d,p)=>{if(d.length)throw new nt(d,e,void 0,void 0,i);n=p}),n}isValid(e,t){return this.validate(e,t).then(()=>!0,r=>{if(nt.isError(r))return!1;throw r})}isValidSync(e,t){try{return this.validateSync(e,t),!0}catch(r){if(nt.isError(r))return!1;throw r}}_getDefault(e){let t=this.spec.default;return t==null?t:typeof t=="function"?t.call(this,e):ar(t)}getDefault(e){return this.resolve(e||{})._getDefault(e)}default(e){return arguments.length===0?this._getDefault():this.clone({default:e})}strict(e=!0){return this.clone({strict:e})}nullability(e,t){const r=this.clone({nullable:e});return r.internalTests.nullable=sr({message:t,name:"nullable",test(a){return a===null?this.schema.spec.nullable:!0}}),r}optionality(e,t){const r=this.clone({optional:e});return r.internalTests.optionality=sr({message:t,name:"optionality",test(a){return a===void 0?this.schema.spec.optional:!0}}),r}optional(){return this.optionality(!0)}defined(e=Rt.defined){return this.optionality(!1,e)}nullable(){return this.nullability(!0)}nonNullable(e=Rt.notNull){return this.nullability(!1,e)}required(e=Rt.required){return this.clone().withMutation(t=>t.nonNullable(e).defined(e))}notRequired(){return this.clone().withMutation(e=>e.nullable().optional())}transform(e){let t=this.clone();return t.transforms.push(e),t}test(...e){let t;if(e.length===1?typeof e[0]=="function"?t={test:e[0]}:t=e[0]:e.length===2?t={name:e[0],test:e[1]}:t={name:e[0],message:e[1],test:e[2]},t.message===void 0&&(t.message=Rt.default),typeof t.test!="function")throw new TypeError("`test` is a required parameters");let r=this.clone(),a=sr(t),n=t.exclusive||t.name&&r.exclusiveTests[t.name]===!0;if(t.exclusive&&!t.name)throw new TypeError("Exclusive tests must provide a unique `name` identifying the test");return t.name&&(r.exclusiveTests[t.name]=!!t.exclusive),r.tests=r.tests.filter(i=>!(i.OPTIONS.name===t.name&&(n||i.OPTIONS.test===a.OPTIONS.test))),r.tests.push(a),r}when(e,t){!Array.isArray(e)&&typeof e!="string"&&(t=e,e=".");let r=this.clone(),a=Rs(e).map(n=>new Yt(n));return a.forEach(n=>{n.isSibling&&r.deps.push(n.key)}),r.conditions.push(typeof t=="function"?new xr(a,t):xr.fromOptions(a,t)),r}typeError(e){let t=this.clone();return t.internalTests.typeError=sr({message:e,name:"typeError",skipAbsent:!0,test(r){return this.schema._typeCheck(r)?!0:this.createError({params:{type:this.schema.type}})}}),t}oneOf(e,t=Rt.oneOf){let r=this.clone();return e.forEach(a=>{r._whitelist.add(a),r._blacklist.delete(a)}),r.internalTests.whiteList=sr({message:t,name:"oneOf",skipAbsent:!0,test(a){let n=this.schema._whitelist,i=n.resolveAll(this.resolve);return i.includes(a)?!0:this.createError({params:{values:Array.from(n).join(", "),resolved:i}})}}),r}notOneOf(e,t=Rt.notOneOf){let r=this.clone();return e.forEach(a=>{r._blacklist.add(a),r._whitelist.delete(a)}),r.internalTests.blacklist=sr({message:t,name:"notOneOf",test(a){let n=this.schema._blacklist,i=n.resolveAll(this.resolve);return i.includes(a)?this.createError({params:{values:Array.from(n).join(", "),resolved:i}}):!0}}),r}strip(e=!0){let t=this.clone();return t.spec.strip=e,t}describe(e){const t=(e?this.resolve(e):this).clone(),{label:r,meta:a,optional:n,nullable:i}=t.spec;return{meta:a,label:r,optional:n,nullable:i,default:t.getDefault(e),type:t.type,oneOf:t._whitelist.describe(),notOneOf:t._blacklist.describe(),tests:t.tests.map(p=>({name:p.OPTIONS.name,params:p.OPTIONS.params})).filter((p,m,y)=>y.findIndex(b=>b.name===p.name)===m)}}}dt.prototype.__isYupSchema__=!0;for(const s of["validate","validateSync"])dt.prototype[`${s}At`]=function(e,t,r={}){const{parent:a,parentPath:n,schema:i}=rs(this,e,t,r.context);return i[s](a&&a[n],Object.assign({},r,{parent:a,path:e}))};for(const s of["equals","is"])dt.prototype[s]=dt.prototype.oneOf;for(const s of["not","nope"])dt.prototype[s]=dt.prototype.notOneOf;const oo=()=>!0;function Us(s){return new ss(s)}class ss extends dt{constructor(e){super(typeof e=="function"?{type:"mixed",check:e}:Object.assign({type:"mixed",check:oo},e))}}Us.prototype=ss.prototype;function as(){return new ns}class ns extends dt{constructor(){super({type:"boolean",check(e){return e instanceof Boolean&&(e=e.valueOf()),typeof e=="boolean"}}),this.withMutation(()=>{this.transform((e,t,r)=>{if(r.spec.coerce&&!r.isType(e)){if(/^(true|1)$/i.test(String(e)))return!0;if(/^(false|0)$/i.test(String(e)))return!1}return e})})}isTrue(e=qr.isValue){return this.test({message:e,name:"is-value",exclusive:!0,params:{value:"true"},test(t){return Mt(t)||t===!0}})}isFalse(e=qr.isValue){return this.test({message:e,name:"is-value",exclusive:!0,params:{value:"false"},test(t){return Mt(t)||t===!1}})}default(e){return super.default(e)}defined(e){return super.defined(e)}optional(){return super.optional()}required(e){return super.required(e)}notRequired(){return super.notRequired()}nullable(){return super.nullable()}nonNullable(e){return super.nonNullable(e)}strip(e){return super.strip(e)}}as.prototype=ns.prototype;const io=/^(\d{4}|[+-]\d{6})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:[ T]?(\d{2}):?(\d{2})(?::?(\d{2})(?:[,.](\d{1,}))?)?(?:(Z)|([+-])(\d{2})(?::?(\d{2}))?)?)?$/;function lo(s){const e=Jr(s);if(!e)return Date.parse?Date.parse(s):Number.NaN;if(e.z===void 0&&e.plusMinus===void 0)return new Date(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond).valueOf();let t=0;return e.z!=="Z"&&e.plusMinus!==void 0&&(t=e.hourOffset*60+e.minuteOffset,e.plusMinus==="+"&&(t=0-t)),Date.UTC(e.year,e.month,e.day,e.hour,e.minute+t,e.second,e.millisecond)}function Jr(s){var e,t;const r=io.exec(s);return r?{year:Jt(r[1]),month:Jt(r[2],1)-1,day:Jt(r[3],1),hour:Jt(r[4]),minute:Jt(r[5]),second:Jt(r[6]),millisecond:r[7]?Jt(r[7].substring(0,3)):0,precision:(e=(t=r[7])==null?void 0:t.length)!=null?e:void 0,z:r[8]||void 0,plusMinus:r[9]||void 0,hourOffset:Jt(r[10]),minuteOffset:Jt(r[11])}:null}function Jt(s,e=0){return Number(s)||e}let uo=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,co=/^((https?|ftp):)?\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,fo=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,po="^\\d{4}-\\d{2}-\\d{2}",ho="\\d{2}:\\d{2}:\\d{2}",vo="(([+-]\\d{2}(:?\\d{2})?)|Z)",mo=new RegExp(`${po}T${ho}(\\.\\d+)?${vo}$`),$o=s=>Mt(s)||s===s.trim(),_o={}.toString();function Bs(){return new os}class os extends dt{constructor(){super({type:"string",check(e){return e instanceof String&&(e=e.valueOf()),typeof e=="string"}}),this.withMutation(()=>{this.transform((e,t,r)=>{if(!r.spec.coerce||r.isType(e)||Array.isArray(e))return e;const a=e!=null&&e.toString?e.toString():e;return a===_o?e:a})})}required(e){return super.required(e).withMutation(t=>t.test({message:e||Rt.required,name:"required",skipAbsent:!0,test:r=>!!r.length}))}notRequired(){return super.notRequired().withMutation(e=>(e.tests=e.tests.filter(t=>t.OPTIONS.name!=="required"),e))}length(e,t=St.length){return this.test({message:t,name:"length",exclusive:!0,params:{length:e},skipAbsent:!0,test(r){return r.length===this.resolve(e)}})}min(e,t=St.min){return this.test({message:t,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(r){return r.length>=this.resolve(e)}})}max(e,t=St.max){return this.test({name:"max",exclusive:!0,message:t,params:{max:e},skipAbsent:!0,test(r){return r.length<=this.resolve(e)}})}matches(e,t){let r=!1,a,n;return t&&(typeof t=="object"?{excludeEmptyString:r=!1,message:a,name:n}=t:a=t),this.test({name:n||"matches",message:a||St.matches,params:{regex:e},skipAbsent:!0,test:i=>i===""&&r||i.search(e)!==-1})}email(e=St.email){return this.matches(uo,{name:"email",message:e,excludeEmptyString:!0})}url(e=St.url){return this.matches(co,{name:"url",message:e,excludeEmptyString:!0})}uuid(e=St.uuid){return this.matches(fo,{name:"uuid",message:e,excludeEmptyString:!1})}datetime(e){let t="",r,a;return e&&(typeof e=="object"?{message:t="",allowOffset:r=!1,precision:a=void 0}=e:t=e),this.matches(mo,{name:"datetime",message:t||St.datetime,excludeEmptyString:!0}).test({name:"datetime_offset",message:t||St.datetime_offset,params:{allowOffset:r},skipAbsent:!0,test:n=>{if(!n||r)return!0;const i=Jr(n);return i?!!i.z:!1}}).test({name:"datetime_precision",message:t||St.datetime_precision,params:{precision:a},skipAbsent:!0,test:n=>{if(!n||a==null)return!0;const i=Jr(n);return i?i.precision===a:!1}})}ensure(){return this.default("").transform(e=>e===null?"":e)}trim(e=St.trim){return this.transform(t=>t!=null?t.trim():t).test({message:e,name:"trim",test:$o})}lowercase(e=St.lowercase){return this.transform(t=>Mt(t)?t:t.toLowerCase()).test({message:e,name:"string_case",exclusive:!0,skipAbsent:!0,test:t=>Mt(t)||t===t.toLowerCase()})}uppercase(e=St.uppercase){return this.transform(t=>Mt(t)?t:t.toUpperCase()).test({message:e,name:"string_case",exclusive:!0,skipAbsent:!0,test:t=>Mt(t)||t===t.toUpperCase()})}}Bs.prototype=os.prototype;let go=s=>s!=+s;function Zs(){return new is}class is extends dt{constructor(){super({type:"number",check(e){return e instanceof Number&&(e=e.valueOf()),typeof e=="number"&&!go(e)}}),this.withMutation(()=>{this.transform((e,t,r)=>{if(!r.spec.coerce)return e;let a=e;if(typeof a=="string"){if(a=a.replace(/\s/g,""),a==="")return NaN;a=+a}return r.isType(a)||a===null?a:parseFloat(a)})})}min(e,t=Zt.min){return this.test({message:t,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(r){return r>=this.resolve(e)}})}max(e,t=Zt.max){return this.test({message:t,name:"max",exclusive:!0,params:{max:e},skipAbsent:!0,test(r){return r<=this.resolve(e)}})}lessThan(e,t=Zt.lessThan){return this.test({message:t,name:"max",exclusive:!0,params:{less:e},skipAbsent:!0,test(r){return r<this.resolve(e)}})}moreThan(e,t=Zt.moreThan){return this.test({message:t,name:"min",exclusive:!0,params:{more:e},skipAbsent:!0,test(r){return r>this.resolve(e)}})}positive(e=Zt.positive){return this.moreThan(0,e)}negative(e=Zt.negative){return this.lessThan(0,e)}integer(e=Zt.integer){return this.test({name:"integer",message:e,skipAbsent:!0,test:t=>Number.isInteger(t)})}truncate(){return this.transform(e=>Mt(e)?e:e|0)}round(e){var t;let r=["ceil","floor","round","trunc"];if(e=((t=e)==null?void 0:t.toLowerCase())||"round",e==="trunc")return this.truncate();if(r.indexOf(e.toLowerCase())===-1)throw new TypeError("Only valid options for round() are: "+r.join(", "));return this.transform(a=>Mt(a)?a:Math[e](a))}}Zs.prototype=is.prototype;let Ys=new Date(""),bo=s=>Object.prototype.toString.call(s)==="[object Date]";function ls(){return new nr}class nr extends dt{constructor(){super({type:"date",check(e){return bo(e)&&!isNaN(e.getTime())}}),this.withMutation(()=>{this.transform((e,t,r)=>!r.spec.coerce||r.isType(e)||e===null?e:(e=lo(e),isNaN(e)?nr.INVALID_DATE:new Date(e)))})}prepareParam(e,t){let r;if(Yt.isRef(e))r=e;else{let a=this.cast(e);if(!this._typeCheck(a))throw new TypeError(`\`${t}\` must be a Date or a value that can be \`cast()\` to a Date`);r=a}return r}min(e,t=Lr.min){let r=this.prepareParam(e,"min");return this.test({message:t,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(a){return a>=this.resolve(r)}})}max(e,t=Lr.max){let r=this.prepareParam(e,"max");return this.test({message:t,name:"max",exclusive:!0,params:{max:e},skipAbsent:!0,test(a){return a<=this.resolve(r)}})}}nr.INVALID_DATE=Ys;ls.prototype=nr.prototype;ls.INVALID_DATE=Ys;function yo(s,e=[]){let t=[],r=new Set,a=new Set(e.map(([i,d])=>`${i}-${d}`));function n(i,d){let p=Qt.split(i)[0];r.add(p),a.has(`${d}-${p}`)||t.push([d,p])}for(const i of Object.keys(s)){let d=s[i];r.add(i),Yt.isRef(d)&&d.isSibling?n(d.path,i):tr(d)&&"deps"in d&&d.deps.forEach(p=>n(p,i))}return Kn.default.array(Array.from(r),t).reverse()}function ws(s,e){let t=1/0;return s.some((r,a)=>{var n;if((n=e.path)!=null&&n.includes(r))return t=a,!0}),t}function Hs(s){return(e,t)=>ws(s,e)-ws(s,t)}const Gs=(s,e,t)=>{if(typeof s!="string")return s;let r=s;try{r=JSON.parse(s)}catch{}return t.isType(r)?r:s};function br(s){if("fields"in s){const e={};for(const[t,r]of Object.entries(s.fields))e[t]=br(r);return s.setFields(e)}if(s.type==="array"){const e=s.optional();return e.innerType&&(e.innerType=br(e.innerType)),e}return s.type==="tuple"?s.optional().clone({types:s.spec.types.map(br)}):"optional"in s?s.optional():s}const xo=(s,e)=>{const t=[...Qt.normalizePath(e)];if(t.length===1)return t[0]in s;let r=t.pop(),a=Qt.getter(Qt.join(t),!0)(s);return!!(a&&r in a)};let Ss=s=>Object.prototype.toString.call(s)==="[object Object]";function ks(s,e){let t=Object.keys(s.fields);return Object.keys(e).filter(r=>t.indexOf(r)===-1)}const wo=Hs([]);function Ks(s){return new us(s)}class us extends dt{constructor(e){super({type:"object",check(t){return Ss(t)||typeof t=="function"}}),this.fields=Object.create(null),this._sortErrors=wo,this._nodes=[],this._excludedEdges=[],this.withMutation(()=>{e&&this.shape(e)})}_cast(e,t={}){var r;let a=super._cast(e,t);if(a===void 0)return this.getDefault(t);if(!this._typeCheck(a))return a;let n=this.fields,i=(r=t.stripUnknown)!=null?r:this.spec.noUnknown,d=[].concat(this._nodes,Object.keys(a).filter(b=>!this._nodes.includes(b))),p={},m=Object.assign({},t,{parent:p,__validating:t.__validating||!1}),y=!1;for(const b of d){let M=n[b],ue=b in a;if(M){let ne,K=a[b];m.path=(t.path?`${t.path}.`:"")+b,M=M.resolve({value:K,context:t.context,parent:p});let de=M instanceof dt?M.spec:void 0,be=de==null?void 0:de.strict;if(de!=null&&de.strip){y=y||b in a;continue}ne=!t.__validating||!be?M.cast(a[b],m):a[b],ne!==void 0&&(p[b]=ne)}else ue&&!i&&(p[b]=a[b]);(ue!==b in p||p[b]!==a[b])&&(y=!0)}return y?p:a}_validate(e,t={},r,a){let{from:n=[],originalValue:i=e,recursive:d=this.spec.recursive}=t;t.from=[{schema:this,value:i},...n],t.__validating=!0,t.originalValue=i,super._validate(e,t,r,(p,m)=>{if(!d||!Ss(m)){a(p,m);return}i=i||m;let y=[];for(let b of this._nodes){let M=this.fields[b];!M||Yt.isRef(M)||y.push(M.asNestedTest({options:t,key:b,parent:m,parentPath:t.path,originalParent:i}))}this.runTests({tests:y,value:m,originalValue:i,options:t},r,b=>{a(b.sort(this._sortErrors).concat(p),m)})})}clone(e){const t=super.clone(e);return t.fields=Object.assign({},this.fields),t._nodes=this._nodes,t._excludedEdges=this._excludedEdges,t._sortErrors=this._sortErrors,t}concat(e){let t=super.concat(e),r=t.fields;for(let[a,n]of Object.entries(this.fields)){const i=r[a];r[a]=i===void 0?n:i}return t.withMutation(a=>a.setFields(r,[...this._excludedEdges,...e._excludedEdges]))}_getDefault(e){if("default"in this.spec)return super._getDefault(e);if(!this._nodes.length)return;let t={};return this._nodes.forEach(r=>{var a;const n=this.fields[r];let i=e;(a=i)!=null&&a.value&&(i=Object.assign({},i,{parent:i.value,value:i.value[r]})),t[r]=n&&"getDefault"in n?n.getDefault(i):void 0}),t}setFields(e,t){let r=this.clone();return r.fields=e,r._nodes=yo(e,t),r._sortErrors=Hs(Object.keys(e)),t&&(r._excludedEdges=t),r}shape(e,t=[]){return this.clone().withMutation(r=>{let a=r._excludedEdges;return t.length&&(Array.isArray(t[0])||(t=[t]),a=[...r._excludedEdges,...t]),r.setFields(Object.assign(r.fields,e),a)})}partial(){const e={};for(const[t,r]of Object.entries(this.fields))e[t]="optional"in r&&r.optional instanceof Function?r.optional():r;return this.setFields(e)}deepPartial(){return br(this)}pick(e){const t={};for(const r of e)this.fields[r]&&(t[r]=this.fields[r]);return this.setFields(t,this._excludedEdges.filter(([r,a])=>e.includes(r)&&e.includes(a)))}omit(e){const t=[];for(const r of Object.keys(this.fields))e.includes(r)||t.push(r);return this.pick(t)}from(e,t,r){let a=Qt.getter(e,!0);return this.transform(n=>{if(!n)return n;let i=n;return xo(n,e)&&(i=Object.assign({},n),r||delete i[e],i[t]=a(n)),i})}json(){return this.transform(Gs)}exact(e){return this.test({name:"exact",exclusive:!0,message:e||_r.exact,test(t){if(t==null)return!0;const r=ks(this.schema,t);return r.length===0||this.createError({params:{properties:r.join(", ")}})}})}stripUnknown(){return this.clone({noUnknown:!0})}noUnknown(e=!0,t=_r.noUnknown){typeof e!="boolean"&&(t=e,e=!0);let r=this.test({name:"noUnknown",exclusive:!0,message:t,test(a){if(a==null)return!0;const n=ks(this.schema,a);return!e||n.length===0||this.createError({params:{unknown:n.join(", ")}})}});return r.spec.noUnknown=e,r}unknown(e=!0,t=_r.noUnknown){return this.noUnknown(!e,t)}transformKeys(e){return this.transform(t=>{if(!t)return t;const r={};for(const a of Object.keys(t))r[e(a)]=t[a];return r})}camelCase(){return this.transformKeys(Mr.camelCase)}snakeCase(){return this.transformKeys(Mr.snakeCase)}constantCase(){return this.transformKeys(e=>Mr.snakeCase(e).toUpperCase())}describe(e){const t=(e?this.resolve(e):this).clone(),r=super.describe(e);r.fields={};for(const[n,i]of Object.entries(t.fields)){var a;let d=e;(a=d)!=null&&a.value&&(d=Object.assign({},d,{parent:d.value,value:d.value[n]})),r.fields[n]=i.describe(d)}return r}}Ks.prototype=us.prototype;function Xs(s){return new ds(s)}class ds extends dt{constructor(e){super({type:"array",spec:{types:e},check(t){return Array.isArray(t)}}),this.innerType=void 0,this.innerType=e}_cast(e,t){const r=super._cast(e,t);if(!this._typeCheck(r)||!this.innerType)return r;let a=!1;const n=r.map((i,d)=>{const p=this.innerType.cast(i,Object.assign({},t,{path:`${t.path||""}[${d}]`}));return p!==i&&(a=!0),p});return a?n:r}_validate(e,t={},r,a){var n;let i=this.innerType,d=(n=t.recursive)!=null?n:this.spec.recursive;t.originalValue!=null&&t.originalValue,super._validate(e,t,r,(p,m)=>{var y;if(!d||!i||!this._typeCheck(m)){a(p,m);return}let b=new Array(m.length);for(let ue=0;ue<m.length;ue++){var M;b[ue]=i.asNestedTest({options:t,index:ue,parent:m,parentPath:t.path,originalParent:(M=t.originalValue)!=null?M:e})}this.runTests({value:m,tests:b,originalValue:(y=t.originalValue)!=null?y:e,options:t},r,ue=>a(ue.concat(p),m))})}clone(e){const t=super.clone(e);return t.innerType=this.innerType,t}json(){return this.transform(Gs)}concat(e){let t=super.concat(e);return t.innerType=this.innerType,e.innerType&&(t.innerType=t.innerType?t.innerType.concat(e.innerType):e.innerType),t}of(e){let t=this.clone();if(!tr(e))throw new TypeError("`array.of()` sub-schema must be a valid yup schema not: "+Lt(e));return t.innerType=e,t.spec=Object.assign({},t.spec,{types:e}),t}length(e,t=gr.length){return this.test({message:t,name:"length",exclusive:!0,params:{length:e},skipAbsent:!0,test(r){return r.length===this.resolve(e)}})}min(e,t){return t=t||gr.min,this.test({message:t,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(r){return r.length>=this.resolve(e)}})}max(e,t){return t=t||gr.max,this.test({message:t,name:"max",exclusive:!0,params:{max:e},skipAbsent:!0,test(r){return r.length<=this.resolve(e)}})}ensure(){return this.default(()=>[]).transform((e,t)=>this._typeCheck(e)?e:t==null?[]:[].concat(t))}compact(e){let t=e?(r,a,n)=>!e(r,a,n):r=>!!r;return this.transform(r=>r!=null?r.filter(t):r)}describe(e){const t=(e?this.resolve(e):this).clone(),r=super.describe(e);if(t.innerType){var a;let n=e;(a=n)!=null&&a.value&&(n=Object.assign({},n,{parent:n.value,value:n.value[0]})),r.innerType=t.innerType.describe(n)}return r}}Xs.prototype=ds.prototype;function Ws(s){return new cs(s)}class cs extends dt{constructor(e){super({type:"tuple",spec:{types:e},check(t){const r=this.spec.types;return Array.isArray(t)&&t.length===r.length}}),this.withMutation(()=>{this.typeError(qs.notType)})}_cast(e,t){const{types:r}=this.spec,a=super._cast(e,t);if(!this._typeCheck(a))return a;let n=!1;const i=r.map((d,p)=>{const m=d.cast(a[p],Object.assign({},t,{path:`${t.path||""}[${p}]`}));return m!==a[p]&&(n=!0),m});return n?i:a}_validate(e,t={},r,a){let n=this.spec.types;super._validate(e,t,r,(i,d)=>{var p;if(!this._typeCheck(d)){a(i,d);return}let m=[];for(let[b,M]of n.entries()){var y;m[b]=M.asNestedTest({options:t,index:b,parent:d,parentPath:t.path,originalParent:(y=t.originalValue)!=null?y:e})}this.runTests({value:d,tests:m,originalValue:(p=t.originalValue)!=null?p:e,options:t},r,b=>a(b.concat(i),d))})}describe(e){const t=(e?this.resolve(e):this).clone(),r=super.describe(e);return r.innerType=t.spec.types.map((a,n)=>{var i;let d=e;return(i=d)!=null&&i.value&&(d=Object.assign({},d,{parent:d.value,value:d.value[n]})),a.describe(d)}),r}}Ws.prototype=cs.prototype;function So(s){return new jr(s)}function Ps(s){try{return s()}catch(e){if(nt.isError(e))return Promise.reject(e);throw e}}class jr{constructor(e){this.type="lazy",this.__isYupSchema__=!0,this.spec=void 0,this._resolve=(t,r={})=>{let a=this.builder(t,r);if(!tr(a))throw new TypeError("lazy() functions must return a valid schema");return this.spec.optional&&(a=a.optional()),a.resolve(r)},this.builder=e,this.spec={meta:void 0,optional:!1}}clone(e){const t=new jr(this.builder);return t.spec=Object.assign({},this.spec,e),t}optionality(e){return this.clone({optional:e})}optional(){return this.optionality(!0)}resolve(e){return this._resolve(e.value,e)}cast(e,t){return this._resolve(e,t).cast(e,t)}asNestedTest(e){let{key:t,index:r,parent:a,options:n}=e,i=a[r??t];return this._resolve(i,Object.assign({},n,{value:i,parent:a})).asNestedTest(e)}validate(e,t){return Ps(()=>this._resolve(e,t).validate(e,t))}validateSync(e,t){return this._resolve(e,t).validateSync(e,t)}validateAt(e,t,r){return Ps(()=>this._resolve(t,r).validateAt(e,t,r))}validateSyncAt(e,t,r){return this._resolve(t,r).validateSyncAt(e,t,r)}isValid(e,t){try{return this._resolve(e,t).isValid(e,t)}catch(r){if(nt.isError(r))return Promise.resolve(!1);throw r}}isValidSync(e,t){return this._resolve(e,t).isValidSync(e,t)}describe(e){return e?this.resolve(e).describe(e):{type:"lazy",meta:this.spec.meta,label:void 0}}meta(...e){if(e.length===0)return this.spec.meta;let t=this.clone();return t.spec.meta=Object.assign(t.spec.meta||{},e[0]),t}}function ko(s){Object.keys(s).forEach(e=>{Object.keys(s[e]).forEach(t=>{Js[e][t]=s[e][t]})})}function Po(s,e,t){if(!s||!tr(s.prototype))throw new TypeError("You must provide a yup schema constructor function");if(typeof e!="string")throw new TypeError("A Method name must be provided");if(typeof t!="function")throw new TypeError("Method function must be provided");s.prototype[e]=t}xe.ArraySchema=ds;xe.BooleanSchema=ns;xe.DateSchema=nr;xe.LazySchema=jr;xe.MixedSchema=ss;xe.NumberSchema=is;xe.ObjectSchema=us;xe.Schema=dt;xe.StringSchema=os;xe.TupleSchema=cs;xe.ValidationError=nt;xe.addMethod=Po;xe.array=Xs;xe.bool=as;var Ts=xe.boolean=as;xe.date=ls;xe.defaultLocale=Js;xe.getIn=rs;xe.isSchema=tr;xe.lazy=So;xe.mixed=Us;xe.number=Zs;var To=xe.object=Ks;xe.printValue=Lt;xe.reach=no;xe.ref=ao;xe.setLocale=ko;var ur=xe.string=Bs;xe.tuple=Ws;var jo=f("<!> <!>",1),Eo=f('<p class="mt-1 text-xs text-red-500"> </p>'),Fo=f("<!> <!>",1),Ao=f("<!> <!>",1),Oo=f('<p class="mt-1 text-xs text-red-500"> </p>'),Co=f("<!> <span>Creating...</span>",1),Do=f("<!> <span>Create Alert</span>",1),Mo=f("<!> <!>",1),No=f('<!> <div class="bg-primary/80 text-background/80 mb-2 rounded-lg p-3 text-sm"><div class="flex items-start"><!> <div><p class="font-medium">Tips for effective job alerts:</p> <ul class="ml-5 mt-1 list-disc"><li>Use specific keywords related to your skills</li> <li>Include location preferences or select "Remote"</li> <li>Choose a frequency that works best for your job search</li></ul></div></div></div> <form><div class="grid gap-4 py-4"><div class="grid grid-cols-3 items-center gap-4"><!> <!> <!></div> <div class="grid grid-cols-3 items-center gap-4"><!> <!></div> <div class="grid grid-cols-3 items-center gap-4"><!> <!></div> <div class="grid grid-cols-3 items-center gap-4"><!> <!></div> <div class="grid grid-cols-3 items-center gap-4"><!> <div class="items-right align-right col-span-2 flex items-center space-x-2"><!> <span class="text-muted-foreground text-sm">Only show remote jobs</span></div></div> <div class="grid grid-cols-3 items-center gap-4"><!> <div class="col-span-2"><!> <!></div></div> <div class="grid grid-cols-3 items-center gap-4"><!> <div class="col-span-2 flex items-center space-x-2"><!> <span class="text-muted-foreground text-sm">Receive emails with new job matches</span></div></div></div> <!></form>',1),Io=f("<!> <!>",1);function Ro(s,e){Sr(e,!1);const[t,r]=Sa(),a=()=>Ar(b,"$form",t),n=()=>Ar(M,"$errors",t),i=()=>Ar(ne,"$isSubmitting",t);let d=Vt(e,"onClose",8),p=Vt(e,"onCreated",8);const m="",y=To().shape({name:ur().required("Alert name is required"),keywords:ur(),location:ur(),jobType:ur(),remote:Ts(),frequency:ur().required("Frequency is required"),enabled:Ts()}),{form:b,errors:M,handleSubmit:ue,isSubmitting:ne}=Tn({initialValues:{name:"",keywords:"",location:"",jobType:"",remote:!1,frequency:"daily",enabled:!0},validationSchema:y,onSubmit:async Q=>{try{const he={keywords:Q.keywords||void 0,location:Q.location||void 0,jobType:Q.jobType||void 0,remote:Q.remote||void 0};Object.keys(he).forEach(Y=>{he[Y]===void 0&&delete he[Y]});const w={name:Q.name,searchParams:he,frequency:Q.frequency,enabled:Q.enabled},E=await fetch("/api/job-alerts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(w)}),te=await E.json();if(!E.ok)throw new Error(te.error||"Failed to create job alert");jt.success("Job alert created successfully"),p()()}catch(he){console.error("Error creating job alert:",he),jt.error("Failed to create job alert")}}}),K=[{value:"",label:"Any"},{value:"full_time",label:"Full-time"},{value:"part_time",label:"Part-time"},{value:"contract",label:"Contract"},{value:"temporary",label:"Temporary"},{value:"internship",label:"Internship"}],de=[{value:"daily",label:"Daily"},{value:"weekly",label:"Weekly"},{value:"monthly",label:"Monthly"}];Ur(),Ta(s,{open:!0,children:(Q,he)=>{var w=Io(),E=$(w);ja(E,{});var te=l(E,2);Ea(te,{class:"sm:max-w-[500px]",children:(Y,F)=>{var V=No(),re=$(V);Fa(re,{children:(S,pe)=>{var q=jo(),Ae=$(q);Aa(Ae,{class:"mb-3",children:(se,A)=>{v();var G=g("Create Job Alert");o(se,G)},$$slots:{default:!0}});var ce=l(Ae,2);Oa(ce,{children:(se,A)=>{v();var G=g(`Set up a new job alert to get notified when new jobs matching your criteria are available.
        You'll receive email notifications based on your selected frequency.`);o(se,G)},$$slots:{default:!0}}),o(S,q)},$$slots:{default:!0}});var L=l(re,2),ke=h(L),Ge=h(ke);Fs(Ge,{class:"mr-2 mt-0.5 h-4 w-4"}),v(2),c(ke),c(L);var z=l(L,2),H=h(z),X=h(H),je=h(X);Ct(je,{for:"name",class:"text-left",children:(S,pe)=>{v();var q=g("Alert Name");o(S,q)},$$slots:{default:!0}});var Ee=l(je,2);const kt=Dt(()=>n().name?"border-red-500":"");$r(Ee,{id:"name",placeholder:"e.g., Software Developer Jobs",get class(){return`col-span-2 ${u(kt)??""}`},get value(){return a().name},set value(S){Ht(b,wt(a).name=S,wt(a))},$$legacy:!0});var $t=l(Ee,2);{var Bt=S=>{var pe=Eo(),q=h(pe,!0);c(pe),B(()=>C(q,n().name)),o(S,pe)};fe($t,S=>{n().name&&S(Bt)})}c(X);var qt=l(X,2),Ke=h(qt);Ct(Ke,{for:"keywords",class:"text-left",children:(S,pe)=>{v();var q=g("Keywords");o(S,q)},$$slots:{default:!0}});var P=l(Ke,2);$r(P,{class:"col-span-2",id:"keywords",placeholder:"e.g., javascript, react",get value(){return a().keywords},set value(S){Ht(b,wt(a).keywords=S,wt(a))},$$legacy:!0}),c(qt);var T=l(qt,2),N=h(T);Ct(N,{for:"location",class:"text-left",children:(S,pe)=>{v();var q=g("Location");o(S,q)},$$slots:{default:!0}});var Pt=l(N,2);$r(Pt,{class:"col-span-2",id:"location",placeholder:"e.g., New York, Remote",get value(){return a().location},set value(S){Ht(b,wt(a).location=S,wt(a))},$$legacy:!0}),c(T);var Xe=l(T,2),_t=h(Xe);Ct(_t,{for:"jobType",class:"text-left",children:(S,pe)=>{v();var q=g("Job Type");o(S,q)},$$slots:{default:!0}});var gt=l(_t,2);Gt(gt,{type:"single",name:"jobType",get value(){return a().jobType},onValueChange:S=>{Ht(b,wt(a).jobType=S,wt(a))},children:(S,pe)=>{var q=Fo(),Ae=$(q);Kt(Ae,{class:"col-span-2 w-full px-3 py-2",children:(se,A)=>{const G=Dt(()=>{var D;return((D=K.find(ae=>ae.value===a().jobType))==null?void 0:D.label)||"Select job type"});Wt(se,{get placeholder(){return u(G)}})},$$slots:{default:!0}});var ce=l(Ae,2);Xt(ce,{class:"max-h-60",children:(se,A)=>{var G=tt(),D=$(G);zt(D,1,()=>K,Ut,(ae,x)=>{Ze(ae,{get value(){return u(x).value},children:(I,k)=>{v();var J=g();B(()=>C(J,u(x).label)),o(I,J)},$$slots:{default:!0}})}),o(se,G)},$$slots:{default:!0}}),o(S,q)},$$slots:{default:!0}}),c(Xe);var We=l(Xe,2),ot=h(We);Ct(ot,{for:"remote",class:"text-left",children:(S,pe)=>{v();var q=g("Remote Only");o(S,q)},$$slots:{default:!0}});var Nt=l(ot,2),bt=h(Nt);ps(bt,{id:"remote",name:"remote",get checked(){return a().remote},onCheckedChange:S=>{Ht(b,wt(a).remote=S,wt(a))}}),v(2),c(Nt),c(We);var Je=l(We,2),rt=h(Je);Ct(rt,{for:"frequency",class:"text-left",children:(S,pe)=>{v();var q=g("Frequency");o(S,q)},$$slots:{default:!0}});var Ye=l(rt,2),Ce=h(Ye);Gt(Ce,{type:"single",name:"frequency",get value(){return a().frequency},onValueChange:S=>{Ht(b,wt(a).frequency=S,wt(a))},children:(S,pe)=>{var q=Ao(),Ae=$(q);Kt(Ae,{class:"w-full px-3 py-2",children:(se,A)=>{const G=Dt(()=>{var D;return((D=de.find(ae=>ae.value===a().frequency))==null?void 0:D.label)||"Select frequency"});Wt(se,{get placeholder(){return u(G)}})},$$slots:{default:!0}});var ce=l(Ae,2);Xt(ce,{class:"max-h-60",children:(se,A)=>{var G=tt(),D=$(G);zt(D,1,()=>de,Ut,(ae,x)=>{Ze(ae,{get value(){return u(x).value},children:(I,k)=>{v();var J=g();B(()=>C(J,u(x).label)),o(I,J)},$$slots:{default:!0}})}),o(se,G)},$$slots:{default:!0}}),o(S,q)},$$slots:{default:!0}});var Ue=l(Ce,2);{var Ie=S=>{var pe=Oo(),q=h(pe,!0);c(pe),B(()=>C(q,n().frequency)),o(S,pe)};fe(Ue,S=>{n().frequency&&S(Ie)})}c(Ye),c(Je);var De=l(Je,2),Me=h(De);Ct(Me,{for:"enabled",class:"text-left",children:(S,pe)=>{v();var q=g("Enabled");o(S,q)},$$slots:{default:!0}});var Ne=l(Me,2),st=h(Ne);ps(st,{id:"enabled",name:"enabled",get checked(){return a().enabled},onCheckedChange:S=>{Ht(b,wt(a).enabled=S,wt(a))}}),v(2),c(Ne),c(De),c(H);var yt=l(H,2);Ca(yt,{class:"mt-2 sm:justify-between",children:(S,pe)=>{var q=Mo(),Ae=$(q);Se(Ae,{type:"button",variant:"outline",get onclick(){return d()},children:(A,G)=>{v();var D=g("Cancel");o(A,D)},$$slots:{default:!0}});var ce=l(Ae,2);const se=Dt(()=>i()||!a().name.trim()||!a().frequency.trim()||!a().keywords.trim()||!a().location.trim()||!a().jobType.trim());Se(ce,{type:"submit",get disabled(){return u(se)},class:"flex items-center gap-2",children:(A,G)=>{var D=tt(),ae=$(D);{var x=k=>{var J=Co(),O=$(J);ba(O,{class:"h-4 w-4 animate-spin"}),v(2),o(k,J)},I=k=>{var J=Do(),O=$(J);yr(O,{class:"h-4 w-4"}),v(2),o(k,J)};fe(ae,k=>{i()?k(x):k(I,!1)})}o(A,D)},$$slots:{default:!0}}),o(S,q)},$$slots:{default:!0}}),c(z),wa("submit",z,Pa(ue)),o(Y,V)},$$slots:{default:!0}}),o(Q,w)},$$slots:{default:!0}}),ka(e,"userId",m);var be=kr({userId:m});return r(),be}var zo=f("<p>Remove from saved jobs</p>"),Vo=f("<!> <!>",1),Lo=f("<!> ",1),qo=f("<!> <!>",1),Jo=f('<p class="flex items-center gap-1"><!> </p>'),Uo=f('<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> </p>'),Bo=f('<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> </p>'),Zo=f('<p class="flex items-center gap-1"><!> </p>'),Yo=f('<p class="flex items-center gap-1"><!> </p>'),Ho=f('<div class="flex flex-wrap gap-1"></div>'),Go=f('<div class="text-muted-foreground space-y-1 text-xs"><!> <!> <!> <!> <!></div> <!>',1),Ko=f("<!> View Job",1),Xo=f('<div class="flex w-full gap-2"><!> <!></div>'),Wo=f('<div class="absolute right-2 top-2 z-10"><!></div> <!> <!> <!>',1);function Qo(s,e){Sr(e,!1);let t=Vt(e,"job",8);const r=Da();function a(){r("remove",{jobId:t().id})}function n(i){return i.salaryMin&&i.salaryMax?`$${i.salaryMin.toLocaleString()} - $${i.salaryMax.toLocaleString()}`:i.salary?i.salary:null}Ur(),Br(s,{class:"hover:border-primary/50 group relative transition-all hover:shadow-sm",children:(i,d)=>{var p=Wo(),m=$(p),y=h(m);dr(y,{children:(ne,K)=>{cr(ne,{children:(de,be)=>{var Q=Vo(),he=$(Q);fr(he,{children:(E,te)=>{Se(E,{size:"sm",variant:"ghost",class:"hover:bg-background/80 h-6 w-6 p-0 hover:text-red-500",onclick:a,children:(Y,F)=>{Es(Y,{class:"h-3 w-3"})},$$slots:{default:!0}})},$$slots:{default:!0}});var w=l(he,2);pr(w,{children:(E,te)=>{var Y=zo();o(E,Y)},$$slots:{default:!0}}),o(de,Q)},$$slots:{default:!0}})},$$slots:{default:!0}}),c(m);var b=l(m,2);Gr(b,{class:"border-border gap-1 border-b !p-4",children:(ne,K)=>{var de=qo(),be=$(de);Kr(be,{class:"flex items-center gap-2",children:(he,w)=>{var E=Lo(),te=$(E);As(te,{class:"h-5 w-5"});var Y=l(te);B(()=>C(Y,` ${t().title??""}`)),o(he,E)},$$slots:{default:!0}});var Q=l(be,2);Yr(Q,{children:(he,w)=>{v();var E=g();B(()=>C(E,t().company)),o(he,E)},$$slots:{default:!0}}),o(ne,de)},$$slots:{default:!0}});var M=l(b,2);Zr(M,{class:"flex flex-col gap-2 p-4",children:(ne,K)=>{var de=Go(),be=$(de),Q=h(be);{var he=z=>{var H=Jo(),X=h(H);Ma(X,{class:"h-3 w-3"});var je=l(X);c(H),B(()=>C(je,` ${t().location??""}`)),o(z,H)};fe(Q,z=>{t().location&&z(he)})}var w=l(Q,2);{var E=z=>{var H=Uo(),X=l(h(H));c(H),B(()=>C(X,` ${t().employmentType??""}`)),o(z,H)};fe(w,z=>{t().employmentType&&z(E)})}var te=l(w,2);{var Y=z=>{var H=Bo(),X=l(h(H));c(H),B(()=>C(X,` ${t().remoteType??""}`)),o(z,H)};fe(te,z=>{t().remoteType&&z(Y)})}var F=l(te,2);{var V=z=>{var H=Zo(),X=h(H);Na(X,{class:"h-3 w-3"});var je=l(X);c(H),B(Ee=>C(je,` ${Ee??""}`),[()=>n(t())],Dt),o(z,H)};fe(F,z=>{n(t())&&z(V)})}var re=l(F,2);{var L=z=>{var H=Yo(),X=h(H);Ia(X,{class:"h-3 w-3"});var je=l(X);c(H),B(Ee=>C(je,` Posted ${Ee??""}`),[()=>new Date(t().postedDate).toLocaleDateString()],Dt),o(z,H)};fe(re,z=>{t().postedDate&&z(L)})}c(be);var ke=l(be,2);{var Ge=z=>{var H=Ho();zt(H,5,()=>t().benefits.slice(0,3),Ut,(X,je)=>{Ot(X,{variant:"outline",class:"px-1.5 py-0.5 text-xs",children:(Ee,kt)=>{v();var $t=g();B(()=>C($t,u(je))),o(Ee,$t)},$$slots:{default:!0}})}),c(H),o(z,H)};fe(ke,z=>{t().benefits&&t().benefits.length>0&&z(Ge)})}o(ne,de)},$$slots:{default:!0}});var ue=l(M,2);Hr(ue,{class:"border-t !p-2",children:(ne,K)=>{var de=Xo(),be=h(de);Se(be,{size:"sm",variant:"outline",class:"h-8 flex-1 text-xs",onclick:()=>window.open(t().url,"_blank"),children:(he,w)=>{var E=Ko(),te=$(E);Ra(te,{class:"mr-1 h-3 w-3"}),v(),o(he,E)},$$slots:{default:!0}});var Q=l(be,2);Se(Q,{size:"sm",class:"h-8 flex-1 text-xs",onclick:()=>It(`/dashboard/jobs/${t().id}`),children:(he,w)=>{v();var E=g("Apply Now");o(he,E)},$$slots:{default:!0}}),c(de),o(ne,de)},$$slots:{default:!0}}),o(i,p)},$$slots:{default:!0}}),kr()}var ei=f('<div class="flex flex-col items-center justify-center py-12 text-center"><div class="mx-auto max-w-md"><h3 class="mb-2 text-xl font-semibold"> </h3> <p class="mb-6 text-gray-500"> </p> <!></div></div>');function Nr(s,e){let t=Vt(e,"title",8),r=Vt(e,"description",8),a=Vt(e,"actionText",8,void 0),n=Vt(e,"actionHref",8,void 0);var i=ei(),d=h(i),p=h(d),m=h(p,!0);c(p);var y=l(p,2),b=h(y,!0);c(y);var M=l(y,2);{var ue=ne=>{Se(ne,{get href(){return n()},children:(K,de)=>{v();var be=g();B(()=>C(be,a())),o(K,be)},$$slots:{default:!0}})};fe(M,ne=>{a()&&n()&&ne(ue)})}c(d),c(i),B(()=>{C(m,t()),C(b,r())}),o(s,i)}var ti=f("<!> <span>Job Matches</span>",1),ri=f("<!> <span>Saved Jobs</span>",1),si=f("<!> <span>Job Alerts</span>",1),ai=f("<!> <!> <!>",1),ni=f("<!> Filters <!>",1),oi=f("<p> </p>"),ii=f("<!> <!>",1),li=f('<div class="flex items-center gap-2"><!> <!></div>'),ui=f("<!> <!>",1),di=f('<div class="rounded-lg border p-6"><!></div>'),ci=f('<div class="rounded-lg border p-6"><!></div>'),fi=f("<p>Save job</p>"),pi=f("<!> <!>",1),hi=f("<p>Dismiss job</p>"),vi=f("<!> <!>",1),mi=f("<li> </li>"),$i=f('<div class="space-y-1"><p class="font-medium">Why this matches:</p> <ul class="space-y-0.5 text-xs"><li> </li> <li> </li> <li> </li> <!></ul></div>'),_i=f("<!> <!>",1),gi=f("<!> ",1),bi=f("<!> <!>",1),yi=f('<div class="flex flex-wrap gap-1"></div>'),xi=f('<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> </p>'),wi=f('<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> </p>'),Si=f('<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> </p>'),ki=f('<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> </p>'),Pi=f('<!> <div class="text-muted-foreground space-y-1 text-xs"><p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> </p> <!> <!> <!></div>',1),Ti=f('<div class="absolute right-2 top-2 z-10 flex gap-1" role="group" aria-label="Job actions"><!> <!></div> <div class="absolute -left-2 -top-2 z-10"><!></div> <!> <!> <!>',1),ji=f("<!> <!> <!> <!>",1),Ei=f("<!> <!>",1),Fi=f('<div class="mt-8 flex items-center justify-between"><div class="flex items-center gap-4"><div class="text-muted-foreground text-sm"> </div> <div class="flex items-center gap-2"><span class="text-muted-foreground text-sm">Show:</span> <!></div></div> <div class="flex items-center gap-2"><!> <!> <div class="flex items-center gap-1"></div> <!> <!></div></div>'),Ai=f('<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"></div> <!>',1),Oi=f('<div class="border-border flex items-center justify-between border-b p-2"><div class="flex items-center gap-4"><!> <div class="flex items-center gap-2"><!> <!></div></div> <!></div> <div class="p-4"><!></div>',1),Ci=f("<!> <span>Refresh</span>",1),Di=f("<!> <span>Browse Jobs</span>",1),Mi=f('<details class="mt-2"><summary class="cursor-pointer font-medium">Sample job data</summary> <pre class="mt-2 overflow-auto text-xs"> </pre></details>'),Ni=f('<p class="text-red-600">No saved jobs found in data</p> <!>',1),Ii=f('<div class="mb-4"><!></div> <div class="mb-4 rounded-lg bg-gray-50 p-3 text-xs"><p><strong>Debug Info:</strong></p> <p> </p> <p> </p> <p> </p> <!></div>',1),Ri=f('<div class="rounded-lg border p-6"><div class="space-y-3"><!> <!> <div class="space-y-2"><!> <!></div> <!></div></div>'),zi=f('<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"></div>'),Vi=f('<div class="rounded-lg border border-red-200 bg-red-50 p-6"><div class="flex flex-col items-center space-y-2 text-center"><!> <h3 class="text-lg font-medium text-red-800">Error loading saved jobs</h3> <p class="text-sm text-red-600"> </p> <!></div></div>'),Li=f('<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"></div>'),qi=f('<div class="rounded-lg border p-6 text-center"><div class="text-muted-foreground"><p class="text-lg font-medium">No jobs match your search</p> <p class="mt-1 text-sm">Try adjusting your search terms or clear the filter.</p> <!></div></div>'),Ji=f('<div class="mb-6 flex items-center justify-between"><div class="flex items-center gap-4"><h2 class="text-lg font-semibold">Your Saved Jobs</h2> <!> <!></div> <div class="flex gap-2"><!> <!></div></div> <!> <!>',1),Ui=f("<!> <span>Create Alert</span>",1),Bi=f('<div class="mb-6 flex items-center justify-between"><div class="flex items-center gap-4"><h2 class="text-lg font-semibold">Your Job Alerts</h2> <!> <!></div> <!></div> <div class="rounded-lg border p-6"><!></div>',1),Zi=f('<div class="p-0"><!></div> <!> <!> <!>',1),Yi=f("<!> <!>",1),Hi=f("<!> <!> <!> <!> <!>",1),Gi=f("<!> <!>",1),Ki=f("<!> <!> <!> <!>",1),Xi=f("<!> <!>",1),Wi=f("<!> <!> <!> <!>",1),Qi=f("<!> <!>",1),el=f('<div class="flex gap-2"><!></div>'),tl=f('<!> <div class="space-y-6 p-4"><div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <!></div> <div class="space-y-2"><!> <div class="flex flex-wrap gap-2"><!> <!> <!> <!></div></div></div> <!>',1),rl=f("<!> <!> <!> <!>",1);function du(s,e){Sr(e,!0);let t=Vt(e,"data",7),r=Et(""),a=Et(""),n=Et("match"),i=Et(!1),d=Et(fs(t().pagination.limit)),p=Et(!1),m=Te(()=>()=>20),y=Te(()=>()=>{let P=0;return u(r)&&P++,u(a)&&P++,u(n)!=="match"&&P++,P}),b=Te(()=>()=>{let P=[...t().matches];if(u(r)){const T=parseInt(u(r))/100;P=P.filter(N=>N.matchScore>=T)}return u(a)&&(P=P.filter(T=>{const N=T.job_listing;return u(a)==="remote"?N.remoteType==="Remote":u(a)==="hybrid"?N.remoteType==="Hybrid":u(a)==="onsite"?N.remoteType==="On-site":!0})),P.sort((T,N)=>{switch(u(n)){case"newest":return new Date(N.job_listing.postedDate).getTime()-new Date(T.job_listing.postedDate).getTime();case"salary":const Pt=T.job_listing.salaryMax||T.job_listing.salaryMin||0;return(N.job_listing.salaryMax||N.job_listing.salaryMin||0)-Pt;case"company":return T.job_listing.company.localeCompare(N.job_listing.company);case"match":default:return N.matchScore-T.matchScore}}),P}),M=Te(()=>()=>{if(!u(Q))return u(K);const P=u(Q).toLowerCase();return u(K).filter(T=>{var gt,We,ot;const N=T.job_listing;if(!N)return console.log("No job_listing found for savedJob:",T),!1;const Pt=(gt=N.title)==null?void 0:gt.toLowerCase().includes(P),Xe=(We=N.company)==null?void 0:We.toLowerCase().includes(P),_t=(ot=N.location)==null?void 0:ot.toLowerCase().includes(P);return Pt||Xe||_t})});const ue=["matches","saved","alerts"];let ne=Et("matches");if(typeof window<"u"){const T=new URL(window.location.href).searchParams.get("tab");T&&ue.includes(T)&&$e(ne,T,!0)}let K=Et(fs(t().savedJobs||[])),de=Et(null),be=Et(!1),Q=Et(""),he=Et(!1);function w(P){switch(P){case"saved":return"Saved Jobs";case"alerts":return"Job Alerts";default:return"Job Matches"}}function E(P){switch(P){case"saved":return"View and manage your saved job opportunities";case"alerts":return"Manage your job alerts and notifications";default:return"Jobs matched to your profile based on your resume"}}let te=Te(()=>w(u(ne))),Y=Te(()=>E(u(ne))),F=!1;typeof window<"u"&&$e(p,!1);async function V(P){try{const T=await fetch(`/api/jobs/${P}/save`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({notes:""})}),N=await T.json();if(!T.ok)throw new Error(N.error||"Failed to save job");jt.success("Job saved!",{description:"Added to your saved jobs"}),u(ne)==="saved"&&H()}catch(T){console.error("Error saving job:",T),jt.error("Failed to save job")}}async function re(P){try{const T=await fetch(`/api/jobs/${P}/dismiss`,{method:"POST",headers:{"Content-Type":"application/json"}}),N=await T.json();if(!T.ok)throw new Error(N.error||"Failed to dismiss job");t().matches=t().matches.filter(Pt=>Pt.job_listing.id!==P),jt.success("Job dismissed",{description:"This job will no longer appear in your matches"})}catch(T){console.error("Error dismissing job:",T),jt.error("Failed to dismiss job")}}function L(P){$e(ne,P,!0),P==="saved"&&!F&&H(),je(P)}function ke(){$e(he,!0)}function Ge(){$e(he,!1),typeof window<"u"&&window.location.reload()}function z(){$e(he,!1)}async function H(){$e(de,null),$e(be,!0);try{const P=await fetch("/api/saved-jobs");if(!P.ok)throw new Error("Failed to fetch saved jobs");const T=await P.json();$e(K,T.savedJobs||[],!0),F=!0}catch(P){console.error("Error fetching saved jobs:",P),$e(de,P.message,!0),F=!1}finally{$e(be,!1)}}async function X(P){try{const T=await fetch(`/api/jobs/${P}/save`,{method:"DELETE"});if(T.ok)$e(K,u(K).filter(N=>N.jobId!==P),!0);else{const N=await T.json();throw new Error(N.error||"Failed to remove job")}}catch(T){console.error("Error removing saved job:",T)}}function je(P){if(typeof window<"u"){const T=new URL(window.location.href);T.searchParams.set("tab",P),window.history.replaceState({},"",T.toString())}}var Ee=rl(),kt=$(Ee);oa(kt,{get title(){return`${u(te)??""} | Hirli`},get description(){return u(Y)},keywords:"job matches, saved jobs, job alerts, AI recommendations, career matches, job opportunities, skill matching, resume matching"});var $t=l(kt,2);_($t,()=>Va,(P,T)=>{T(P,{get value(){return u(ne)},onValueChange:L,children:(N,Pt)=>{var Xe=Zi(),_t=$(Xe),gt=h(_t);_(gt,()=>za,(bt,Je)=>{Je(bt,{class:"border-t-0",children:(rt,Ye)=>{var Ce=ai(),Ue=$(Ce);_(Ue,()=>Or,(Me,Ne)=>{Ne(Me,{value:"matches",class:"flex-1 gap-2",children:(st,yt)=>{var S=ti(),pe=$(S);Za(pe,{class:"h-4 w-4"}),v(2),o(st,S)},$$slots:{default:!0}})});var Ie=l(Ue,2);_(Ie,()=>Or,(Me,Ne)=>{Ne(Me,{value:"saved",class:"flex-1 gap-2",children:(st,yt)=>{var S=ri(),pe=$(S);hs(pe,{class:"h-4 w-4"}),v(2),o(st,S)},$$slots:{default:!0}})});var De=l(Ie,2);_(De,()=>Or,(Me,Ne)=>{Ne(Me,{value:"alerts",class:"flex-1 gap-2",children:(st,yt)=>{var S=si(),pe=$(S);yr(pe,{class:"h-4 w-4"}),v(2),o(st,S)},$$slots:{default:!0}})}),o(rt,Ce)},$$slots:{default:!0}})}),c(_t);var We=l(_t,2);_(We,()=>Cr,(bt,Je)=>{Je(bt,{value:"matches",children:(rt,Ye)=>{var Ce=Oi(),Ue=$(Ce),Ie=h(Ue),De=h(Ie);Se(De,{variant:"outline",onclick:()=>$e(i,!0),class:"relative flex items-center gap-2",children:(ce,se)=>{var A=ni(),G=$(A);Ya(G,{class:"h-4 w-4"});var D=l(G,2);{var ae=x=>{Ot(x,{variant:"default",class:"ml-1 h-5 w-5 rounded-full p-0 text-xs",children:(I,k)=>{v();var J=g();B(O=>C(J,O),[()=>u(y)()]),o(I,J)},$$slots:{default:!0}})};fe(D,x=>{u(y)()>0&&x(ae)})}o(ce,A)},$$slots:{default:!0}});var Me=l(De,2),Ne=h(Me);Ot(Ne,{variant:"secondary",children:(ce,se)=>{v();var A=g();B(G=>C(A,`${G??""} matches found`),[()=>u(b)().length]),o(ce,A)},$$slots:{default:!0}});var st=l(Ne,2);_(st,()=>dr,(ce,se)=>{se(ce,{children:(A,G)=>{var D=tt(),ae=$(D);_(ae,()=>cr,(x,I)=>{I(x,{children:(k,J)=>{var O=ii(),U=$(O);_(U,()=>fr,(W,oe)=>{oe(W,{children:(ee,Z)=>{Fs(ee,{class:"text-muted-foreground h-4 w-4 cursor-help"})},$$slots:{default:!0}})});var j=l(U,2);_(j,()=>pr,(W,oe)=>{oe(W,{children:(ee,Z)=>{var R=oi(),me=h(R);c(R),B(ye=>C(me,`Free plan: ${ye??""} matches per day`),[()=>u(m)()]),o(ee,R)},$$slots:{default:!0}})}),o(k,O)},$$slots:{default:!0}})}),o(A,D)},$$slots:{default:!0}})}),c(Me),c(Ie);var yt=l(Ie,2);_(yt,()=>Gt,(ce,se)=>{se(ce,{type:"single",get value(){return t().selectedProfileId},get disabled(){return u(p)},onValueChange:A=>{A&&A!==t().selectedProfileId&&($e(p,!0),It(`/dashboard/matches?profileId=${A}&page=1&limit=${u(d)}`))},children:(A,G)=>{var D=ui(),ae=$(D);_(ae,()=>Kt,(I,k)=>{k(I,{class:" px-3 py-2",children:(J,O)=>{var U=li(),j=h(U);{var W=Z=>{vs(Z,{class:"h-4 w-4 animate-spin"})};fe(j,Z=>{u(p)&&Z(W)})}var oe=l(j,2);const ee=Te(()=>{var Z;return((Z=t().profiles.find(R=>R.id===t().selectedProfileId))==null?void 0:Z.name)||"Select Profile"});_(oe,()=>Wt,(Z,R)=>{R(Z,{get placeholder(){return u(ee)}})}),c(U),o(J,U)},$$slots:{default:!0}})});var x=l(ae,2);_(x,()=>Xt,(I,k)=>{k(I,{class:"max-h-60",children:(J,O)=>{var U=tt(),j=$(U);zt(j,17,()=>t().profiles,Ut,(W,oe)=>{var ee=tt(),Z=$(ee);_(Z,()=>Ze,(R,me)=>{me(R,{get value(){return u(oe).id},children:(ye,_e)=>{v();var Re=g();B(()=>C(Re,u(oe).name)),o(ye,Re)},$$slots:{default:!0}})}),o(W,ee)}),o(J,U)},$$slots:{default:!0}})}),o(A,D)},$$slots:{default:!0}})}),c(Ue);var S=l(Ue,2),pe=h(S);{var q=ce=>{var se=di(),A=h(se);Nr(A,{title:"No profiles found",description:"Create a profile to get job matches based on your resume.",actionText:"Create Profile",actionHref:"/dashboard/settings/profile"}),c(se),o(ce,se)},Ae=(ce,se)=>{{var A=D=>{var ae=ci(),x=h(ae);const I=Te(()=>t().matches.length===0?"No job matches available":"No matches with current filters"),k=Te(()=>t().matches.length===0?"We are continuously adding new job opportunities. Check back soon or browse all available jobs.":"Try adjusting your filters to see more job matches. You have "+t().matches.length+" total matches available."),J=Te(()=>t().matches.length===0?"Browse All Jobs":"Clear Filters"),O=Te(()=>t().matches.length===0?"/dashboard/jobs":"#");Nr(x,{get title(){return u(I)},get description(){return u(k)},get actionText(){return u(J)},get actionHref(){return u(O)},$$events:{click(...U){var j;(j=t().matches.length===0?void 0:()=>{$e(r,""),$e(a,""),$e(n,"match")})==null||j.apply(this,U)}}}),c(ae),o(D,ae)},G=D=>{var ae=Ai(),x=$(ae);zt(x,21,()=>u(b)(),Ut,(J,O)=>{var U=tt();const j=Te(()=>u(O).job_listing),W=Te(()=>Math.round(u(O).matchScore*100)),oe=Te(()=>u(W)>=90?"bg-green-100 text-green-800 border-green-200":u(W)>=80?"bg-blue-100 text-blue-800 border-blue-200":u(W)>=70?"bg-yellow-100 text-yellow-800 border-yellow-200":"bg-gray-100 text-gray-800 border-gray-200");var ee=$(U);_(ee,()=>Br,(Z,R)=>{R(Z,{class:"hover:border-primary/50 group relative gap-0 p-0 transition-all hover:shadow-sm",children:(me,ye)=>{var _e=Ti(),Re=$(_e),Pe=h(Re);_(Pe,()=>dr,(Fe,at)=>{at(Fe,{children:(ct,Ft)=>{var Qe=tt(),it=$(Qe);_(it,()=>cr,(ft,lt)=>{lt(ft,{children:(pt,xt)=>{var et=pi(),Ve=$(et);_(Ve,()=>fr,(Be,Le)=>{Le(Be,{children:(qe,vt)=>{Se(qe,{size:"sm",variant:"ghost",class:"hover:bg-background/80 h-6 w-6 p-0",onclick:()=>V(u(j).id),children:(le,ge)=>{hs(le,{class:"h-3 w-3"})},$$slots:{default:!0}})},$$slots:{default:!0}})});var ht=l(Ve,2);_(ht,()=>pr,(Be,Le)=>{Le(Be,{children:(qe,vt)=>{var le=fi();o(qe,le)},$$slots:{default:!0}})}),o(pt,et)},$$slots:{default:!0}})}),o(ct,Qe)},$$slots:{default:!0}})});var ie=l(Pe,2);_(ie,()=>dr,(Fe,at)=>{at(Fe,{children:(ct,Ft)=>{var Qe=tt(),it=$(Qe);_(it,()=>cr,(ft,lt)=>{lt(ft,{children:(pt,xt)=>{var et=vi(),Ve=$(et);_(Ve,()=>fr,(Be,Le)=>{Le(Be,{children:(qe,vt)=>{Se(qe,{size:"sm",variant:"ghost",class:"hover:bg-background/80 h-6 w-6 p-0",onclick:()=>re(u(j).id),children:(le,ge)=>{Ka(le,{class:"h-3 w-3"})},$$slots:{default:!0}})},$$slots:{default:!0}})});var ht=l(Ve,2);_(ht,()=>pr,(Be,Le)=>{Le(Be,{children:(qe,vt)=>{var le=hi();o(qe,le)},$$slots:{default:!0}})}),o(pt,et)},$$slots:{default:!0}})}),o(ct,Qe)},$$slots:{default:!0}})}),c(Re);var ve=l(Re,2),ze=h(ve);_(ze,()=>dr,(Fe,at)=>{at(Fe,{children:(ct,Ft)=>{var Qe=tt(),it=$(Qe);_(it,()=>cr,(ft,lt)=>{lt(ft,{children:(pt,xt)=>{var et=_i(),Ve=$(et);_(Ve,()=>fr,(Be,Le)=>{Le(Be,{children:(qe,vt)=>{Ot(qe,{get class(){return`border-background rounded-full border-2 px-2 py-1 text-xs font-bold ${u(oe)??""}`},children:(le,ge)=>{v();var ut=g();B(()=>C(ut,`${u(W)??""}%`)),o(le,ut)},$$slots:{default:!0}})},$$slots:{default:!0}})});var ht=l(Ve,2);_(ht,()=>pr,(Be,Le)=>{Le(Be,{class:"max-w-xs",children:(qe,vt)=>{var le=$i(),ge=l(h(le),2),ut=h(ge),mt=h(ut);c(ut);var At=l(ut,2),hr=h(At);c(At);var rr=l(At,2),Qs=h(rr);c(rr);var ea=l(rr,2);{var ta=or=>{var Er=mi(),ra=h(Er);c(Er),B(sa=>C(ra,`• Tech stack: ${sa??""}`),[()=>u(j).techStack.slice(0,2).join(", ")]),o(or,Er)};fe(ea,or=>{u(j).techStack&&u(j).techStack.length>0&&or(ta)})}c(ge),c(le),B(or=>{C(mt,`• Skills match: ${or??""}%`),C(hr,`• Experience level: ${u(j).experienceLevel||"Mid-level"}`),C(Qs,`• Location preference: ${u(j).remoteType||"On-site"}`)},[()=>Math.round(u(W)*.8)]),o(qe,le)},$$slots:{default:!0}})}),o(pt,et)},$$slots:{default:!0}})}),o(ct,Qe)},$$slots:{default:!0}})}),c(ve);var Oe=l(ve,2);_(Oe,()=>Gr,(Fe,at)=>{at(Fe,{class:"border-border gap-1 border-b !p-4",children:(ct,Ft)=>{var Qe=bi(),it=$(Qe);_(it,()=>Kr,(lt,pt)=>{pt(lt,{class:"flex items-center gap-2",children:(xt,et)=>{var Ve=gi(),ht=$(Ve);As(ht,{class:"h-5 w-5"});var Be=l(ht);B(()=>C(Be,` ${u(j).title??""}`)),o(xt,Ve)},$$slots:{default:!0}})});var ft=l(it,2);_(ft,()=>Yr,(lt,pt)=>{pt(lt,{children:(xt,et)=>{v();var Ve=g();B(()=>C(Ve,u(j).company)),o(xt,Ve)},$$slots:{default:!0}})}),o(ct,Qe)},$$slots:{default:!0}})});var we=l(Oe,2);_(we,()=>Zr,(Fe,at)=>{at(Fe,{class:"flex flex-col gap-2 p-4",children:(ct,Ft)=>{var Qe=Pi(),it=$(Qe);{var ft=le=>{var ge=yi();zt(ge,21,()=>u(j).benefits.slice(0,3),Ut,(ut,mt)=>{Ot(ut,{variant:"outline",class:"px-1.5 py-0.5 text-xs",children:(At,hr)=>{v();var rr=g();B(()=>C(rr,u(mt))),o(At,rr)},$$slots:{default:!0}})}),c(ge),o(le,ge)};fe(it,le=>{u(j).benefits&&u(j).benefits.length>0&&le(ft)})}var lt=l(it,2),pt=h(lt),xt=l(h(pt));c(pt);var et=l(pt,2);{var Ve=le=>{var ge=xi(),ut=l(h(ge));c(ge),B((mt,At)=>C(ut,` $${mt??""} - $${At??""}`),[()=>{var mt;return(mt=u(j).salaryMin)==null?void 0:mt.toLocaleString()},()=>{var mt;return(mt=u(j).salaryMax)==null?void 0:mt.toLocaleString()}]),o(le,ge)},ht=(le,ge)=>{{var ut=mt=>{var At=wi(),hr=l(h(At));c(At),B(()=>C(hr,` ${u(j).salary??""}`)),o(mt,At)};fe(le,mt=>{u(j).salary&&mt(ut)},ge)}};fe(et,le=>{u(j).salaryMin&&u(j).salaryMax?le(Ve):le(ht,!1)})}var Be=l(et,2);{var Le=le=>{var ge=Si(),ut=l(h(ge));c(ge),B(()=>C(ut,` ${u(j).employmentType??""}`)),o(le,ge)};fe(Be,le=>{u(j).employmentType&&le(Le)})}var qe=l(Be,2);{var vt=le=>{var ge=ki(),ut=l(h(ge));c(ge),B(()=>C(ut,` ${u(j).remoteType??""}`)),o(le,ge)};fe(qe,le=>{u(j).remoteType&&le(vt)})}c(lt),B(()=>C(xt,` ${u(j).location??""}`)),o(ct,Qe)},$$slots:{default:!0}})});var Tt=l(we,2);_(Tt,()=>Hr,(Fe,at)=>{at(Fe,{class:"border-t !p-2",children:(ct,Ft)=>{Se(ct,{size:"sm",class:"h-8 w-full text-xs",onclick:()=>It(`/dashboard/jobs/${u(j).id}`),children:(Qe,it)=>{v();var ft=g("Apply Now");o(Qe,ft)},$$slots:{default:!0}})},$$slots:{default:!0}})}),o(me,_e)},$$slots:{default:!0}})}),o(J,U)}),c(x);var I=l(x,2);{var k=J=>{var O=Fi(),U=h(O),j=h(U),W=h(j);c(j);var oe=l(j,2),ee=l(h(oe),2);const Z=Te(()=>String(u(d)));_(ee,()=>Gt,(we,Tt)=>{Tt(we,{type:"single",get value(){return u(Z)},onValueChange:Fe=>{const at=parseInt(Fe||"20");$e(d,at,!0),It(`/dashboard/matches?profileId=${t().selectedProfileId}&page=1&limit=${at}`)},children:(Fe,at)=>{var ct=Ei(),Ft=$(ct);_(Ft,()=>Kt,(it,ft)=>{ft(it,{class:"h-8 w-16",children:(lt,pt)=>{var xt=tt(),et=$(xt);const Ve=Te(()=>String(u(d)));_(et,()=>Wt,(ht,Be)=>{Be(ht,{get placeholder(){return u(Ve)}})}),o(lt,xt)},$$slots:{default:!0}})});var Qe=l(Ft,2);_(Qe,()=>Xt,(it,ft)=>{ft(it,{children:(lt,pt)=>{var xt=ji(),et=$(xt);_(et,()=>Ze,(Le,qe)=>{qe(Le,{value:"10",children:(vt,le)=>{v();var ge=g("10");o(vt,ge)},$$slots:{default:!0}})});var Ve=l(et,2);_(Ve,()=>Ze,(Le,qe)=>{qe(Le,{value:"20",children:(vt,le)=>{v();var ge=g("20");o(vt,ge)},$$slots:{default:!0}})});var ht=l(Ve,2);_(ht,()=>Ze,(Le,qe)=>{qe(Le,{value:"50",children:(vt,le)=>{v();var ge=g("50");o(vt,ge)},$$slots:{default:!0}})});var Be=l(ht,2);_(Be,()=>Ze,(Le,qe)=>{qe(Le,{value:"100",children:(vt,le)=>{v();var ge=g("100");o(vt,ge)},$$slots:{default:!0}})}),o(lt,xt)},$$slots:{default:!0}})}),o(Fe,ct)},$$slots:{default:!0}})}),c(oe),c(U);var R=l(U,2),me=h(R);const ye=Te(()=>t().pagination.page===1);Se(me,{variant:"outline",size:"sm",get disabled(){return u(ye)},onclick:()=>It(`/dashboard/matches?profileId=${t().selectedProfileId}&page=1&limit=${u(d)}`),children:(we,Tt)=>{v();var Fe=g("First");o(we,Fe)},$$slots:{default:!0}});var _e=l(me,2);const Re=Te(()=>t().pagination.page===1);Se(_e,{variant:"outline",size:"sm",get disabled(){return u(Re)},onclick:()=>It(`/dashboard/matches?profileId=${t().selectedProfileId}&page=${t().pagination.page-1}&limit=${u(d)}`),children:(we,Tt)=>{v();var Fe=g("Previous");o(we,Fe)},$$slots:{default:!0}});var Pe=l(_e,2);zt(Pe,21,()=>Array.from({length:Math.min(5,t().pagination.totalPages)},(we,Tt)=>Math.max(1,t().pagination.page-2)+Tt).filter(we=>we<=t().pagination.totalPages),Ut,(we,Tt)=>{const Fe=Te(()=>u(Tt)===t().pagination.page?"default":"outline");Se(we,{get variant(){return u(Fe)},size:"sm",class:"h-8 w-8 p-0",onclick:()=>It(`/dashboard/matches?profileId=${t().selectedProfileId}&page=${u(Tt)}&limit=${u(d)}`),children:(at,ct)=>{v();var Ft=g();B(()=>C(Ft,u(Tt))),o(at,Ft)},$$slots:{default:!0}})}),c(Pe);var ie=l(Pe,2);const ve=Te(()=>!t().pagination.hasMore);Se(ie,{variant:"outline",size:"sm",get disabled(){return u(ve)},onclick:()=>It(`/dashboard/matches?profileId=${t().selectedProfileId}&page=${t().pagination.page+1}&limit=${u(d)}`),children:(we,Tt)=>{v();var Fe=g("Next");o(we,Fe)},$$slots:{default:!0}});var ze=l(ie,2);const Oe=Te(()=>!t().pagination.hasMore);Se(ze,{variant:"outline",size:"sm",get disabled(){return u(Oe)},onclick:()=>It(`/dashboard/matches?profileId=${t().selectedProfileId}&page=${t().pagination.totalPages}&limit=${u(d)}`),children:(we,Tt)=>{v();var Fe=g("Last");o(we,Fe)},$$slots:{default:!0}}),c(R),c(O),B(we=>C(W,`Showing ${(t().pagination.page-1)*t().pagination.limit+1} to ${we??""} of ${t().pagination.totalCount??""} results`),[()=>Math.min(t().pagination.page*t().pagination.limit,t().pagination.totalCount)]),o(J,O)};fe(I,J=>{t().pagination.totalPages>1&&J(k)})}o(D,ae)};fe(ce,D=>{u(b)().length===0?D(A):D(G,!1)},se)}};fe(pe,ce=>{t().profiles.length===0?ce(q):ce(Ae,!1)})}c(S),o(rt,Ce)},$$slots:{default:!0}})});var ot=l(We,2);_(ot,()=>Cr,(bt,Je)=>{Je(bt,{value:"saved",class:"p-4",children:(rt,Ye)=>{var Ce=Ji(),Ue=$(Ce),Ie=h(Ue),De=l(h(Ie),2);Ot(De,{variant:"secondary",class:"text-sm",children:(A,G)=>{v();var D=g();B(()=>C(D,`${u(K).length??""} saved`)),o(A,D)},$$slots:{default:!0}});var Me=l(De,2);{var Ne=A=>{Ot(A,{variant:"outline",class:"text-sm",children:(G,D)=>{v();var ae=g();B(x=>C(ae,`${x??""} filtered`),[()=>u(M)().length]),o(G,ae)},$$slots:{default:!0}})};fe(Me,A=>{u(Q)&&A(Ne)})}c(Ie);var st=l(Ie,2),yt=h(st);Se(yt,{variant:"outline",onclick:H,get disabled(){return u(be)},class:"flex items-center gap-2",children:(A,G)=>{var D=Ci(),ae=$(D);const x=Te(()=>u(be)?"animate-spin":"");vs(ae,{get class(){return`h-4 w-4 ${u(x)??""}`}}),v(2),o(A,D)},$$slots:{default:!0}});var S=l(yt,2);Se(S,{variant:"outline",onclick:()=>It("/dashboard/jobs"),class:"flex items-center gap-2",children:(A,G)=>{var D=Di(),ae=$(D);Ha(ae,{class:"h-4 w-4"}),v(2),o(A,D)},$$slots:{default:!0}}),c(st),c(Ue);var pe=l(Ue,2);{var q=A=>{var G=Ii(),D=$(G),ae=h(D);$r(ae,{type:"text",placeholder:"Search saved jobs by title, company, or location...",class:"max-w-md",get value(){return u(Q)},set value(Z){$e(Q,Z,!0)}}),c(D);var x=l(D,2),I=l(h(x),2),k=h(I);c(I);var J=l(I,2),O=h(J);c(J);var U=l(J,2),j=h(U);c(U);var W=l(U,2);{var oe=Z=>{var R=Mi(),me=l(h(R),2),ye=h(me,!0);c(me),c(R),B(_e=>C(ye,_e),[()=>JSON.stringify(u(K)[0],null,2)]),o(Z,R)},ee=Z=>{var R=Ni(),me=l($(R),2);{var ye=_e=>{Se(_e,{size:"sm",class:"mt-2",onclick:()=>V(t().matches[0].job_listing.id),children:(Re,Pe)=>{v();var ie=g("Test: Save First Match");o(Re,ie)},$$slots:{default:!0}})};fe(me,_e=>{t().matches.length>0&&_e(ye)})}o(Z,R)};fe(W,Z=>{u(K).length>0?Z(oe):Z(ee,!1)})}c(x),B(Z=>{C(k,`Total saved jobs: ${u(K).length??""}`),C(O,`Filtered results: ${Z??""}`),C(j,`Search term: "${u(Q)??""}"`)},[()=>u(M)().length]),o(A,G)};fe(pe,A=>{u(K).length>0&&A(q)})}var Ae=l(pe,2);{var ce=A=>{var G=zi();zt(G,20,()=>Array(8),Ut,(D,ae)=>{var x=Ri(),I=h(x),k=h(I);lr(k,{class:"h-4 w-3/4"});var J=l(k,2);lr(J,{class:"h-4 w-1/2"});var O=l(J,2),U=h(O);lr(U,{class:"h-3 w-full"});var j=l(U,2);lr(j,{class:"h-3 w-2/3"}),c(O);var W=l(O,2);lr(W,{class:"h-8 w-full rounded"}),c(I),c(x),o(D,x)}),c(G),o(A,G)},se=(A,G)=>{{var D=x=>{var I=Vi(),k=h(I),J=h(k);Xa(J,{class:"h-6 w-6 text-red-500"});var O=l(J,4),U=h(O,!0);c(O);var j=l(O,2);Se(j,{variant:"outline",class:"mt-4",onclick:H,children:(W,oe)=>{v();var ee=g("Try Again");o(W,ee)},$$slots:{default:!0}}),c(k),c(I),B(()=>C(U,u(de))),o(x,I)},ae=(x,I)=>{{var k=O=>{var U=tt(),j=$(U);{var W=ee=>{var Z=Li();zt(Z,21,()=>u(M)(),Ut,(R,me)=>{var ye=tt();const _e=Te(()=>u(me).job_listing);var Re=$(ye);{var Pe=ie=>{Qo(ie,{get job(){return u(_e)},$$events:{remove:ve=>X(ve.detail.jobId)}})};fe(Re,ie=>{u(_e)&&ie(Pe)})}o(R,ye)}),c(Z),o(ee,Z)},oe=ee=>{var Z=qi(),R=h(Z),me=l(h(R),4);Se(me,{variant:"outline",class:"mt-4",onclick:()=>$e(Q,""),children:(ye,_e)=>{v();var Re=g("Clear Search");o(ye,Re)},$$slots:{default:!0}}),c(R),c(Z),o(ee,Z)};fe(j,ee=>{u(M)().length>0?ee(W):ee(oe,!1)})}o(O,U)},J=O=>{Nr(O,{title:"No saved jobs",description:"Save jobs that interest you to keep track of opportunities.",actionText:"Browse Jobs",actionHref:"/dashboard/jobs"})};fe(x,O=>{u(K).length>0?O(k):O(J,!1)},I)}};fe(A,x=>{u(de)?x(D):x(ae,!1)},G)}};fe(Ae,A=>{u(be)?A(ce):A(se,!1)})}o(rt,Ce)},$$slots:{default:!0}})});var Nt=l(ot,2);_(Nt,()=>Cr,(bt,Je)=>{Je(bt,{value:"alerts",class:"p-4",children:(rt,Ye)=>{var Ce=Bi(),Ue=$(Ce),Ie=h(Ue),De=l(h(Ie),2);Ot(De,{variant:"secondary",class:"text-sm",children:(q,Ae)=>{v();var ce=g();B(()=>{var se;return C(ce,`${(((se=t().jobAlerts)==null?void 0:se.length)||0)??""} alerts`)}),o(q,ce)},$$slots:{default:!0}});var Me=l(De,2);{var Ne=q=>{Ot(q,{variant:"default",class:"text-sm",children:(Ae,ce)=>{v();var se=g();B(A=>C(se,`${A??""} active`),[()=>t().jobAlerts.filter(A=>A.enabled).length]),o(Ae,se)},$$slots:{default:!0}})};fe(Me,q=>{var Ae;(Ae=t().jobAlerts)!=null&&Ae.filter(ce=>ce.enabled).length&&q(Ne)})}c(Ie);var st=l(Ie,2);Se(st,{variant:"default",onclick:ke,class:"flex items-center gap-2",children:(q,Ae)=>{var ce=Ui(),se=$(ce);js(se,{class:"h-4 w-4"}),v(2),o(q,ce)},$$slots:{default:!0}}),c(Ue);var yt=l(Ue,2),S=h(yt);const pe=Te(()=>t().jobAlerts||[]);gn(S,{get alerts(){return u(pe)},onCreateAlert:ke}),c(yt),o(rt,Ce)},$$slots:{default:!0}})}),o(N,Xe)},$$slots:{default:!0}})});var Bt=l($t,2);_(Bt,()=>Ba,(P,T)=>{T(P,{get open(){return u(i)},set open(N){$e(i,N,!0)},children:(N,Pt)=>{var Xe=tt(),_t=$(Xe);_(_t,()=>La,(gt,We)=>{We(gt,{side:"left",class:"w-[400px] sm:w-[540px]",children:(ot,Nt)=>{var bt=tl(),Je=$(bt);_(Je,()=>qa,(x,I)=>{I(x,{class:"border-b p-4",children:(k,J)=>{var O=Yi(),U=$(O);_(U,()=>Ja,(W,oe)=>{oe(W,{children:(ee,Z)=>{v();var R=g("Filter Jobs");o(ee,R)},$$slots:{default:!0}})});var j=l(U,2);_(j,()=>Ua,(W,oe)=>{oe(W,{children:(ee,Z)=>{v();var R=g("Refine your job matches with filters and sorting options.");o(ee,R)},$$slots:{default:!0}})}),o(k,O)},$$slots:{default:!0}})});var rt=l(Je,2),Ye=h(rt),Ce=h(Ye);Ct(Ce,{class:"text-sm font-medium",children:(x,I)=>{v();var k=g("Match Score");o(x,k)},$$slots:{default:!0}});var Ue=l(Ce,2);_(Ue,()=>Gt,(x,I)=>{I(x,{type:"single",get value(){return u(r)},onValueChange:k=>{$e(r,k||"",!0)},children:(k,J)=>{var O=Gi(),U=$(O);_(U,()=>Kt,(W,oe)=>{oe(W,{children:(ee,Z)=>{var R=tt(),me=$(R);_(me,()=>Wt,(ye,_e)=>{_e(ye,{placeholder:"All Matches"})}),o(ee,R)},$$slots:{default:!0}})});var j=l(U,2);_(j,()=>Xt,(W,oe)=>{oe(W,{children:(ee,Z)=>{var R=Hi(),me=$(R);_(me,()=>Ze,(ie,ve)=>{ve(ie,{value:"",children:(ze,Oe)=>{v();var we=g("All Matches");o(ze,we)},$$slots:{default:!0}})});var ye=l(me,2);_(ye,()=>Ze,(ie,ve)=>{ve(ie,{value:"90",children:(ze,Oe)=>{v();var we=g("90%+ (Excellent)");o(ze,we)},$$slots:{default:!0}})});var _e=l(ye,2);_(_e,()=>Ze,(ie,ve)=>{ve(ie,{value:"80",children:(ze,Oe)=>{v();var we=g("80-89% (Great)");o(ze,we)},$$slots:{default:!0}})});var Re=l(_e,2);_(Re,()=>Ze,(ie,ve)=>{ve(ie,{value:"70",children:(ze,Oe)=>{v();var we=g("70-79% (Good)");o(ze,we)},$$slots:{default:!0}})});var Pe=l(Re,2);_(Pe,()=>Ze,(ie,ve)=>{ve(ie,{value:"60",children:(ze,Oe)=>{v();var we=g("60-69% (Fair)");o(ze,we)},$$slots:{default:!0}})}),o(ee,R)},$$slots:{default:!0}})}),o(k,O)},$$slots:{default:!0}})}),c(Ye);var Ie=l(Ye,2),De=h(Ie);Ct(De,{class:"text-sm font-medium",children:(x,I)=>{v();var k=g("Location Type");o(x,k)},$$slots:{default:!0}});var Me=l(De,2);_(Me,()=>Gt,(x,I)=>{I(x,{type:"single",get value(){return u(a)},onValueChange:k=>{$e(a,k||"",!0)},children:(k,J)=>{var O=Xi(),U=$(O);_(U,()=>Kt,(W,oe)=>{oe(W,{children:(ee,Z)=>{var R=tt(),me=$(R);_(me,()=>Wt,(ye,_e)=>{_e(ye,{placeholder:"All Locations"})}),o(ee,R)},$$slots:{default:!0}})});var j=l(U,2);_(j,()=>Xt,(W,oe)=>{oe(W,{children:(ee,Z)=>{var R=Ki(),me=$(R);_(me,()=>Ze,(Pe,ie)=>{ie(Pe,{value:"",children:(ve,ze)=>{v();var Oe=g("All Locations");o(ve,Oe)},$$slots:{default:!0}})});var ye=l(me,2);_(ye,()=>Ze,(Pe,ie)=>{ie(Pe,{value:"remote",children:(ve,ze)=>{v();var Oe=g("Remote");o(ve,Oe)},$$slots:{default:!0}})});var _e=l(ye,2);_(_e,()=>Ze,(Pe,ie)=>{ie(Pe,{value:"hybrid",children:(ve,ze)=>{v();var Oe=g("Hybrid");o(ve,Oe)},$$slots:{default:!0}})});var Re=l(_e,2);_(Re,()=>Ze,(Pe,ie)=>{ie(Pe,{value:"onsite",children:(ve,ze)=>{v();var Oe=g("On-site");o(ve,Oe)},$$slots:{default:!0}})}),o(ee,R)},$$slots:{default:!0}})}),o(k,O)},$$slots:{default:!0}})}),c(Ie);var Ne=l(Ie,2),st=h(Ne);Ct(st,{class:"text-sm font-medium",children:(x,I)=>{v();var k=g("Sort By");o(x,k)},$$slots:{default:!0}});var yt=l(st,2);_(yt,()=>Gt,(x,I)=>{I(x,{type:"single",get value(){return u(n)},onValueChange:k=>{$e(n,k||"match",!0)},children:(k,J)=>{var O=Qi(),U=$(O);_(U,()=>Kt,(W,oe)=>{oe(W,{children:(ee,Z)=>{var R=tt(),me=$(R);_(me,()=>Wt,(ye,_e)=>{_e(ye,{placeholder:"Best Match"})}),o(ee,R)},$$slots:{default:!0}})});var j=l(U,2);_(j,()=>Xt,(W,oe)=>{oe(W,{children:(ee,Z)=>{var R=Wi(),me=$(R);_(me,()=>Ze,(Pe,ie)=>{ie(Pe,{value:"match",children:(ve,ze)=>{v();var Oe=g("Best Match");o(ve,Oe)},$$slots:{default:!0}})});var ye=l(me,2);_(ye,()=>Ze,(Pe,ie)=>{ie(Pe,{value:"newest",children:(ve,ze)=>{v();var Oe=g("Newest First");o(ve,Oe)},$$slots:{default:!0}})});var _e=l(ye,2);_(_e,()=>Ze,(Pe,ie)=>{ie(Pe,{value:"salary",children:(ve,ze)=>{v();var Oe=g("Highest Salary");o(ve,Oe)},$$slots:{default:!0}})});var Re=l(_e,2);_(Re,()=>Ze,(Pe,ie)=>{ie(Pe,{value:"company",children:(ve,ze)=>{v();var Oe=g("Company A-Z");o(ve,Oe)},$$slots:{default:!0}})}),o(ee,R)},$$slots:{default:!0}})}),o(k,O)},$$slots:{default:!0}})}),c(Ne);var S=l(Ne,2),pe=h(S);Ct(pe,{class:"text-sm font-medium",children:(x,I)=>{v();var k=g("Quick Filters");o(x,k)},$$slots:{default:!0}});var q=l(pe,2),Ae=h(q);const ce=Te(()=>u(r)==="90"?"default":"outline");Se(Ae,{get variant(){return u(ce)},size:"sm",class:"h-8",onclick:()=>{$e(r,u(r)==="90"?"":"90",!0)},children:(x,I)=>{v();var k=g("High Match (90%+)");o(x,k)},$$slots:{default:!0}});var se=l(Ae,2);const A=Te(()=>u(a)==="remote"?"default":"outline");Se(se,{get variant(){return u(A)},size:"sm",class:"h-8",onclick:()=>{$e(a,u(a)==="remote"?"":"remote",!0)},children:(x,I)=>{v();var k=g("Remote Only");o(x,k)},$$slots:{default:!0}});var G=l(se,2);Se(G,{variant:"outline",size:"sm",class:"h-8",onclick:()=>{jt.info("Tech company filter coming soon!")},children:(x,I)=>{v();var k=g("Tech Companies");o(x,k)},$$slots:{default:!0}});var D=l(G,2);Se(D,{variant:"outline",size:"sm",class:"h-8",onclick:()=>{jt.info("Salary filter coming soon!")},children:(x,I)=>{v();var k=g("$100k+ Salary");o(x,k)},$$slots:{default:!0}}),c(q),c(S),c(rt);var ae=l(rt,2);_(ae,()=>Ga,(x,I)=>{I(x,{class:"border-t p-2",children:(k,J)=>{var O=el(),U=h(O);Se(U,{variant:"outline",onclick:()=>{$e(r,""),$e(a,""),$e(n,"match")},children:(j,W)=>{v();var oe=g("Clear All");o(j,oe)},$$slots:{default:!0}}),c(O),o(k,O)},$$slots:{default:!0}})}),o(ot,bt)},$$slots:{default:!0}})}),o(N,Xe)},$$slots:{default:!0}})});var qt=l(Bt,2);{var Ke=P=>{const T=Te(()=>{var N;return(N=t().user)==null?void 0:N.id});Ro(P,{onClose:z,onCreated:Ge,get userId(){return u(T)}})};fe(qt,P=>{u(he)&&P(Ke)})}o(s,Ee),kr()}export{du as component};
