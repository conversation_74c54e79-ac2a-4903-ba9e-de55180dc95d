import{c,a as s,f as n}from"./BasJTneF.js";import{p as g,f as b,g as o,x as h,a as y}from"./CGmarHxI.js";import{i as x}from"./u21ee2wt.js";import{r as m,e as f}from"./B-Xjo-Yt.js";import{b as P}from"./CzsE_FAw.js";import{p as k,r as H}from"./Btcx8l8F.js";import{m as S,s as O}from"./BfX7a-t9.js";var j=n("<input/>"),q=n("<input/>");function F(u,e){g(e,!0);let i=k(e,"value",15),l=H(e,["$$slots","$$events","$$legacy","value"]);const t=h(()=>S(l,{"aria-hidden":"true",tabindex:-1,style:O}));var p=c(),v=b(p);{var _=a=>{var r=j();m(r),f(r,()=>({...o(t),value:i()})),s(a,r)},d=a=>{var r=q();m(r),f(r,()=>({...o(t)})),P(r,i),s(a,r)};x(v,a=>{o(t).type==="checkbox"?a(_):a(d,!1)})}s(u,p),y()}export{F as H};
