import"./BasJTneF.js";import"./CgXBgsce.js";import{t as n}from"./DjPYYl4Z.js";async function u(r,t,e){try{const s=await fetch("/api/ai/resume/suggestions",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({resumeId:r,section:t,content:e})});if(!s.ok)throw new Error(`Error getting resume suggestions: ${s.statusText}`);return(await s.json()).suggestions}catch(s){return console.error("Error getting resume suggestions:",s),n.error("Failed to get AI suggestions. Please try again."),[]}}async function g(r){try{const t=await fetch(`/api/ai/resume/suggestions/${r}/apply`,{method:"POST",headers:{"Content-Type":"application/json"}});if(!t.ok)throw new Error(`Error applying resume suggestion: ${t.statusText}`);const e=await t.json();return n.success("AI suggestion applied successfully"),e.success}catch(t){return console.error("Error applying resume suggestion:",t),n.error("Failed to apply AI suggestion. Please try again."),!1}}async function l(r){try{const t=await fetch(`/api/ai/ats?resumeId=${r}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(t.ok)return(await t.json()).analysis;const e=await fetch("/api/ai/ats",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({resumeId:r})});if(!e.ok)throw new Error(`Error getting ATS analysis: ${e.statusText}`);return(await e.json()).analysis}catch(t){return console.error("Error getting ATS analysis:",t),n.error("Failed to get ATS analysis. Please try again."),null}}async function y(r,t){try{const e=await fetch(`/api/ai/ats?resumeId=${r}&jobId=${t}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok)return(await e.json()).analysis;const s=await fetch("/api/ai/ats/job",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({resumeId:r,jobId:t})});if(!s.ok)throw new Error(`Error getting job-specific ATS analysis: ${s.statusText}`);return(await s.json()).analysis}catch(e){return console.error("Error getting job-specific ATS analysis:",e),n.error("Failed to get job-specific ATS analysis. Please try again."),null}}async function d(r,t,e,s){try{const a=await fetch("/api/ai/interview/sessions",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({applicationId:t,jobTitle:r,company:e,jobDescription:s})});if(!a.ok)throw new Error(`Error creating interview coaching session: ${a.statusText}`);return(await a.json()).session}catch(a){return console.error("Error creating interview coaching session:",a),n.error("Failed to create interview coaching session. Please try again."),null}}async function w(r,t,e){try{const s=await fetch(`/api/ai/interview/${r}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({questionIndex:t,response:e})});if(!s.ok)throw new Error(`Error submitting interview response: ${s.statusText}`);const a=await s.json();return{feedback:a.feedback,session:a.session}}catch(s){return console.error("Error submitting interview response:",s),n.error("Failed to submit interview response. Please try again."),null}}async function h(r,t){try{const e=await fetch(`/api/ai/interview/${r}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t})});if(!e.ok)throw new Error(`Error updating interview session status: ${e.statusText}`);return(await e.json()).session}catch(e){return console.error("Error updating interview session status:",e),n.error("Failed to update interview session status. Please try again."),null}}export{l as a,u as b,d as c,g as d,y as g,w as s,h as u};
