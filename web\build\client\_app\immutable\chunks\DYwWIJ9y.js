import{t as u,P as n,E as l,R as g,ay as y,F as h,A as p,I as R,B as b,D as w,z as f}from"./CGmarHxI.js";import{b as m,d as A}from"./BasJTneF.js";function O(c,v,i=!1,o=!1,D=!1){var _=c,t="";u(()=>{var r=g;if(t===(t=v()??"")){n&&l();return}if(r.nodes_start!==null&&(y(r.nodes_start,r.nodes_end),r.nodes_start=r.nodes_end=null),t!==""){if(n){h.data;for(var e=l(),d=e;e!==null&&(e.nodeType!==8||e.data!=="");)d=e,e=p(e);if(e===null)throw R(),b;m(h,d),_=w(e);return}var s=t+"";i?s=`<svg>${s}</svg>`:o&&(s=`<math>${s}</math>`);var a=A(s);if((i||o)&&(a=f(a)),m(f(a),a.lastChild),i||o)for(;f(a);)_.before(f(a));else _.before(a)}})}export{O as h};
