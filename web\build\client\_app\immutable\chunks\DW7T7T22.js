import{c as i,a as n}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as l}from"./BBa424ah.js";import{l as m,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function k(t,o){const e=m(o,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335"}],["path",{d:"m9 11 3 3L22 4"}]];f(t,d({name:"circle-check-big"},()=>e,{get iconNode(){return s},children:(a,$)=>{var r=i(),c=p(r);l(c,o,"default",{},null),n(a,r)},$$slots:{default:!0}}))}export{k as C};
