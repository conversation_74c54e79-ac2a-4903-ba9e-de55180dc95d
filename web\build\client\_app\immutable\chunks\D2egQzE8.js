import{b as I,o as L}from"./Ntteq2n_.js";import"./BasJTneF.js";import{p as W,a as S}from"./CGmarHxI.js";import{p as C}from"./Btcx8l8F.js";import"./BfX7a-t9.js";import{n as b}from"./DX6rZLP_.js";function z(t,e){const n=[];for(let o=0;o<t.length;o+=e)n.push(t.slice(o,o+e));return n}function D(t,e){return t>=0&&t<e.length}function N(t,e,n=!0){if(!(t.length===0||e<0||e>=t.length))return t.length===1&&e===0?t[0]:e===t.length-1?n?t[0]:void 0:t[e+1]}function _(t,e,n=!0){if(!(t.length===0||e<0||e>=t.length))return t.length===1&&e===0?t[0]:e===0?n?t[t.length-1]:void 0:t[e-1]}function j(t,e,n,o=!0){if(t.length===0||e<0||e>=t.length)return;let f=e+n;return o?f=(f%t.length+t.length)%t.length:f=Math.max(0,Math.min(f,t.length-1)),t[f]}function q(t,e,n,o=!0){if(t.length===0||e<0||e>=t.length)return;let f=e-n;return o?f=(f%t.length+t.length)%t.length:f=Math.max(0,Math.min(f,t.length-1)),t[f]}function a(t,e,n){const o=e.toLowerCase();if(o.endsWith(" ")){const u=o.slice(0,-1);if(t.filter(i=>i.toLowerCase().startsWith(u)).length<=1)return a(t,u,n);const s=n==null?void 0:n.toLowerCase();if(s&&s.startsWith(u)&&s.charAt(u.length)===" "&&e.trim()===u)return n;const g=t.filter(i=>i.toLowerCase().startsWith(o));if(g.length>0){const i=n?t.indexOf(n):-1;return M(g,Math.max(i,0)).find(x=>x!==n)||n}}const m=e.length>1&&Array.from(e).every(u=>u===e[0])?e[0]:e,c=m.toLowerCase(),h=n?t.indexOf(n):-1;let l=M(t,Math.max(h,0));m.length===1&&(l=l.filter(u=>u!==n));const d=l.find(u=>u==null?void 0:u.toLowerCase().startsWith(c));return d!==n?d:void 0}function M(t,e){return t.map((n,o)=>t[(e+o)%t.length])}function B(t){const e=I("",1e3),n=(t==null?void 0:t.onMatch)??(c=>c.focus()),o=(t==null?void 0:t.getCurrentItem)??(()=>document.activeElement);function f(c,h){var s,g;if(!h.length)return;e.current=e.current+c;const l=o(),w=((g=(s=h.find(i=>i===l))==null?void 0:s.textContent)==null?void 0:g.trim())??"",d=h.map(i=>{var r;return((r=i.textContent)==null?void 0:r.trim())??""}),u=a(d,e.current,w),p=h.find(i=>{var r;return((r=i.textContent)==null?void 0:r.trim())===u});return p&&n(p),p}function m(){e.current=""}return{search:e,handleTypeaheadSearch:f,resetTypeahead:m}}function F(t,e){W(e,!0);let n=C(e,"mounted",15,!1),o=C(e,"onMountedChange",3,b);L(()=>(n(!0),o()(!0),()=>{n(!1),o()(!1)})),S()}export{F as M,q as b,z as c,j as f,a as g,D as i,N as n,_ as p,B as u};
