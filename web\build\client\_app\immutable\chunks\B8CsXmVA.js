import{c as k,a as p,f as m}from"./BasJTneF.js";import{p as L,i as b,d as s,k as h,f as x,g as d,a as j,c as w,r as C,t as N}from"./CGmarHxI.js";import{s as O}from"./CIt1g2O9.js";import{i as R}from"./u21ee2wt.js";import{p as S}from"./Btcx8l8F.js";var T=m('<span class="text-gray-400">Loading...</span>'),B=m("<span> </span>");function z(v,o){L(o,!0);let l=S(o,"fallback",3,"None specified"),n=h(""),i=h(!0);const c=new Map;async function g(){if(!o.locationIds){s(n,l()),s(i,!1);return}if(c.has(o.locationIds)){s(n,c.get(o.locationIds),!0),s(i,!1);return}try{const e=o.locationIds.split(", ").filter(Boolean);if(e.length===0){s(n,l()),s(i,!1);return}const t=[];for(const a of e)if(a.includes("|")){const r=a.split("|");r.length>=3?t.push(`${r[1]}, ${r[2]}`):t.push(a)}else try{const r=await fetch("/api/locations/resolve",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({ids:[a]})});if(r.ok){const f=await r.json();f.length>0?t.push(`${f[0].name}, ${f[0].state.code}`):t.push(a)}else t.push(a)}catch{t.push(a)}s(n,t.join(", ")||l(),!0),c.set(o.locationIds,d(n))}catch(e){console.error("Error resolving locations:",e),s(n,o.locationIds,!0)}finally{s(i,!1)}}b(()=>{s(i,!0),g()});var u=k(),_=x(u);{var y=e=>{var t=T();p(e,t)},I=e=>{var t=B(),a=w(t,!0);C(t),N(()=>O(a,d(n))),p(e,t)};R(_,e=>{d(i)?e(y):e(I,!1)})}p(v,u),j()}export{z as R};
