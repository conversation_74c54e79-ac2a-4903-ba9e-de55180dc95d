import{c as _,a as l,f as m}from"./BasJTneF.js";import{p as I,i as b,d as s,k as y,f as x,g as f,a as j,c as K,r as C,t as L}from"./CGmarHxI.js";import{s as N}from"./CIt1g2O9.js";import{i as O}from"./u21ee2wt.js";import{p as R}from"./Btcx8l8F.js";var S=m('<span class="text-gray-400">Loading...</span>'),T=m("<span> </span>");function q(p,a){I(a,!0);let n=R(a,"fallback",3,"None specified"),t=y(""),o=y(!0);const i=new Map;async function u(){if(!a.keywordIds){s(t,n()),s(o,!1);return}if(i.has(a.keywordIds)){s(t,i.get(a.keywordIds),!0),s(o,!1);return}try{const e=a.keywordIds.split(", ").filter(Boolean);if(e.length===0){s(t,n()),s(o,!1);return}const r=await fetch("/api/occupations/resolve",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({ids:e})});if(r.ok){const h=(await r.json()).map(g=>g.title).join(", ");s(t,h||n(),!0),i.set(a.keywordIds,f(t))}else s(t,a.keywordIds,!0)}catch(e){console.error("Error resolving keywords:",e),s(t,a.keywordIds,!0)}finally{s(o,!1)}}b(()=>{s(o,!0),u()});var d=_(),v=x(d);{var w=e=>{var r=S();l(e,r)},k=e=>{var r=T(),c=K(r,!0);C(r),L(()=>N(c,f(t))),l(e,r)};O(v,e=>{f(o)?e(w):e(k,!1)})}l(p,d),j()}export{q as R};
