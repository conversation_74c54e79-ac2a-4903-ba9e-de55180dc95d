import{f as v,a as e,t as u,c as oe}from"../chunks/BasJTneF.js";import"../chunks/CgXBgsce.js";import{o as le}from"../chunks/nZgk9enP.js";import{p as ie,f as c,t as B,a as de,s as t,d as D,c as h,m as J,e as me,g as q,r as x,n}from"../chunks/CGmarHxI.js";import{s as C}from"../chunks/CIt1g2O9.js";import{i as ue}from"../chunks/u21ee2wt.js";import{a as ne}from"../chunks/B-Xjo-Yt.js";import{i as ve}from"../chunks/BIEMS98f.js";import{p as fe}from"../chunks/Btcx8l8F.js";import{B as y}from"../chunks/B1K98fMG.js";import{C as K}from"../chunks/DuGukytH.js";import{C as Q}from"../chunks/Cdn-N1RY.js";import{C as W}from"../chunks/GwmmX_iF.js";import{C as X}from"../chunks/D50jIuLr.js";import{g as $e}from"../chunks/BiJhC7W5.js";import{R as pe,T as _e}from"../chunks/I7hvcB12.js";import{T as A}from"../chunks/C88uNE8B.js";import{T as Y}from"../chunks/DmZyh-PW.js";var ce=v("<!> <!> <!>",1),he=v('<p class="text-muted-foreground mb-1 text-sm"> </p> <p class="mb-1 text-sm"> </p> <p class="mb-4 text-sm"> </p> <div class="flex gap-2"><!> <!> <!></div>',1),xe=v("<!> <!>",1),ge=v('<div class="h-[800px] w-full overflow-hidden rounded border border-gray-200"><iframe title="Resume Document" class="h-full w-full"></iframe></div>'),Pe=v('<p class="text-muted-foreground">Document not available for viewing</p>'),be=v("<!> <!>",1),we=v("<!> <!> <!>",1),De=v('<h1 class="mb-4 text-2xl font-semibold"> </h1> <div class="text-muted-foreground mb-6 text-sm"> </div> <!>',1);function Ge(Z,F){ie(F,!1);let r=fe(F,"data",8),R=J("details"),T=J("");le(()=>{r().resume.fileUrl?D(T,`/uploads${r().resume.fileUrl}`):r().resume.fileName&&D(T,`/uploads/resumes/${r().resume.fileName}`)}),ve();var L=De(),k=c(L),ee=h(k,!0);x(k);var U=t(k,2),te=h(U);x(U);var re=t(U,2);pe(re,{get value(){return q(R)},onValueChange:N=>D(R,N),class:"w-full",children:(N,Ce)=>{var M=we(),O=c(M);_e(O,{children:(P,j)=>{var g=ce(),V=c(g);A(V,{value:"details",children:(s,a)=>{n();var o=u("Resume Details");e(s,o)},$$slots:{default:!0}});var d=t(V,2);A(d,{value:"parsed",children:(s,a)=>{n();var o=u("Parsed Data");e(s,o)},$$slots:{default:!0}});var f=t(d,2);A(f,{value:"view",children:(s,a)=>{n();var o=u("View Document");e(s,o)},$$slots:{default:!0}}),e(P,g)},$$slots:{default:!0}});var S=t(O,2);Y(S,{value:"details",class:"mt-4",children:(P,j)=>{K(P,{class:"mb-6",children:(g,V)=>{var d=xe(),f=c(d);W(f,{children:(a,o)=>{X(a,{children:(l,$)=>{n();var p=u("Resume Details");e(l,p)},$$slots:{default:!0}})},$$slots:{default:!0}});var s=t(f,2);Q(s,{children:(a,o)=>{var l=he(),$=c(l),p=h($);x($);var b=t($,2),m=h(b);x(b);var i=t(b,2),z=h(i);x(i);var E=t(i,2),G=h(E);y(G,{onclick:()=>$e(`/dashboard/resume/${r().resume.id}/optimize`),children:(_,I)=>{n();var w=u("Optimize Resume");e(_,w)},$$slots:{default:!0}});var H=t(G,2);y(H,{variant:"outline",onclick:()=>D(R,"parsed"),children:(_,I)=>{n();var w=u("View Parsed Data");e(_,w)},$$slots:{default:!0}});var se=t(H,2);y(se,{variant:"outline",onclick:()=>D(R,"view"),children:(_,I)=>{n();var w=u("View Document");e(_,w)},$$slots:{default:!0}}),x(E),B(_=>{C(p,`Uploaded: ${_??""}`),C(m,`Profile: ${r().resume.profile.name??""}`),C(z,`Filename: ${r().resume.fileName??""}`)},[()=>new Date(r().resume.createdAt).toLocaleDateString()],me),e(a,l)},$$slots:{default:!0}}),e(g,d)},$$slots:{default:!0}})},$$slots:{default:!0}});var ae=t(S,2);Y(ae,{value:"view",class:"mt-4",children:(P,j)=>{K(P,{children:(g,V)=>{var d=be(),f=c(d);W(f,{children:(a,o)=>{X(a,{children:(l,$)=>{n();var p=u("Document Viewer");e(l,p)},$$slots:{default:!0}})},$$slots:{default:!0}});var s=t(f,2);Q(s,{children:(a,o)=>{var l=oe(),$=c(l);{var p=m=>{var i=ge(),z=h(i);x(i),B(()=>ne(z,"src",q(T))),e(m,i)},b=m=>{var i=Pe();e(m,i)};ue($,m=>{q(T)?m(p):m(b,!1)})}e(a,l)},$$slots:{default:!0}}),e(g,d)},$$slots:{default:!0}})},$$slots:{default:!0}}),e(N,M)},$$slots:{default:!0}}),B(()=>{C(ee,r().resume.name),C(te,`Dashboard > Resume > ${r().resume.name??""}`)}),e(Z,L),de()}export{Ge as component};
