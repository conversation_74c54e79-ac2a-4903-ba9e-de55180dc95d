var ct=Object.defineProperty;var Je=t=>{throw TypeError(t)};var ut=(t,e,n)=>e in t?ct(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var w=(t,e,n)=>ut(t,typeof e!="symbol"?e+"":e,n),dt=(t,e,n)=>e.has(t)||Je("Cannot "+n);var d=(t,e,n)=>(dt(t,e,"read from private field"),n?n.call(t):e.get(t)),z=(t,e,n)=>e.has(t)?Je("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n);import{c as k,a as L,f as De}from"./BasJTneF.js";import{k as T,g as v,d as A,v as pt,i as Ne,o as ht,x as E,p as W,f as R,a as X,c as Ee,au as Me,r as ke}from"./CGmarHxI.js";import{s as K,c as Ze}from"./ncUU1dSD.js";import{i as Re}from"./u21ee2wt.js";import{e as Ge}from"./B-Xjo-Yt.js";import{p as P,r as Y,s as Ke}from"./Btcx8l8F.js";import{u as Fe,w as me,e as ft,b as x,m as Oe}from"./BfX7a-t9.js";import{c as $e}from"./BvdI7LR8.js";import{I as gt}from"./DxW95yuQ.js";import{b as yt}from"./5V1tIHTN.js";import{o as mt}from"./nZgk9enP.js";import{o as F}from"./CmxjS0TN.js";import{s as zt}from"./BniYvUIG.js";import{a as vt}from"./OOsIR5sE.js";import{C as St}from"./DuoUhxYL.js";function xt(t,e,n,i){const r=Array.isArray(e)?e:[e];return r.forEach(s=>t.addEventListener(s,n,i)),()=>{r.forEach(s=>t.removeEventListener(s,n,i))}}function bt({layout:t,panesArray:e,pivotIndices:n}){let i=0,r=100,s=0,a=0;const o=n[0];for(let c=0;c<e.length;c++){const g=e[c].constraints,{maxSize:h=100,minSize:f=0}=g;c===o?(i=f,r=h):(s+=f,a+=h)}const l=Math.min(r,100-s),p=Math.max(i,100-a),u=t[o];return{valueMax:l,valueMin:p,valueNow:u}}function S(t,e="Assertion failed!"){if(!t)throw console.error(e),new Error(e)}const wt=100,je=10;function D(t,e,n=je){return te(t,e,n)===0}function te(t,e,n=je){const i=Ve(t,n),r=Ve(e,n);return Math.sign(i-r)}function Q(t,e){if(t.length!==e.length)return!1;for(let n=0;n<t.length;n++)if(t[n]!==e[n])return!1;return!0}function Ve(t,e){return Number.parseFloat(t.toFixed(e))}const _e=typeof document<"u";function Pt(t){return t instanceof HTMLElement}function et(t){return t.type==="keydown"}function tt(t){return t.type.startsWith("mouse")}function nt(t){return t.type.startsWith("touch")}function q({paneConstraints:t,paneIndex:e,initialSize:n}){const i=t[e];S(i!=null,"Pane constraints should not be null.");const{collapsedSize:r=0,collapsible:s,maxSize:a=100,minSize:o=0}=i;let l=n;return te(l,o)<0&&(l=It(l,s,r,o)),l=Math.min(a,l),Number.parseFloat(l.toFixed(je))}function It(t,e,n,i){if(!e)return i;const r=(n+i)/2;return te(t,r)<0?n:i}function G(){}function At({groupId:t,layout:e,panesArray:n}){const i=ye(t);for(let r=0;r<n.length-1;r++){const{valueMax:s,valueMin:a,valueNow:o}=bt({layout:e,panesArray:n,pivotIndices:[r,r+1]}),l=i[r];if(Pt(l)){const p=n[r];l.setAttribute("aria-controls",p.opts.id.current),l.setAttribute("aria-valuemax",`${Math.round(s)}`),l.setAttribute("aria-valuemin",`${Math.round(a)}`),l.setAttribute("aria-valuenow",o!=null?`${Math.round(o)}`:"")}}return()=>{i.forEach(r=>{r.removeAttribute("aria-controls"),r.removeAttribute("aria-valuemax"),r.removeAttribute("aria-valuemin"),r.removeAttribute("aria-valuenow")})}}function ye(t){return _e?Array.from(document.querySelectorAll(`[data-pane-resizer-id][data-pane-group-id="${t}"]`)):[]}function rt(t,e){return _e?ye(t).findIndex(r=>r.getAttribute("data-pane-resizer-id")===e)??null:null}function We(t,e){const n=rt(t,e);return n!=null?[n,n+1]:[-1,-1]}function O(t,e,n){const i=t.map(p=>p.constraints),r=j(t,e),s=i[r],o=r===t.length-1?[r-1,r]:[r,r+1],l=n[r];return{...s,paneSize:l,pivotIndices:o}}function j(t,e){return t.findIndex(n=>n.opts.id.current===e.opts.id.current)}function Z(t,e,n){for(let i=0;i<e.length;i++){const r=e[i],s=t[i];S(s);const{collapsedSize:a=0,collapsible:o}=s.constraints,l=n[s.opts.id.current];if(!(l==null||r!==l))continue;n[s.opts.id.current]=r;const{onCollapse:p,onExpand:u,onResize:c}=s.callbacks;c==null||c(r,l),o&&(p||u)&&(u&&(l==null||l===a)&&r!==a&&u(),p&&(l==null||l!==a)&&r===a&&p())}}function Ct({panesArray:t}){const e=Array(t.length),n=t.map(s=>s.constraints);let i=0,r=100;for(let s=0;s<t.length;s++){const a=n[s];S(a);const{defaultSize:o}=a;o!=null&&(i++,e[s]=o,r-=o)}for(let s=0;s<t.length;s++){const a=n[s];S(a);const{defaultSize:o}=a;if(o!=null)continue;const l=t.length-i,p=r/l;i++,e[s]=p,r-=p}return e}function Lt({layout:t,paneConstraints:e}){const n=[...t],i=n.reduce((s,a)=>s+a,0);if(n.length!==e.length)throw new Error(`Invalid ${e.length} pane layout: ${n.map(s=>`${s}%`).join(", ")}`);if(!D(i,100))for(let s=0;s<e.length;s++){const a=n[s];S(a!=null);const o=100/i*a;n[s]=o}let r=0;for(let s=0;s<e.length;s++){const a=n[s];S(a!=null);const o=q({paneConstraints:e,paneIndex:s,initialSize:a});a!==o&&(r+=a-o,n[s]=o)}if(!D(r,0))for(let s=0;s<e.length;s++){const a=n[s];S(a!=null);const o=a+r,l=q({paneConstraints:e,paneIndex:s,initialSize:o});if(a!==l&&(r-=l-a,n[s]=l,D(r,0)))break}return n}function Dt(t){if(!_e)return null;const e=document.querySelector(`[data-pane-group][data-pane-group-id="${t}"]`);return e||null}function qe(t){if(!_e)return null;const e=document.querySelector(`[data-pane-resizer-id="${t}"]`);return e||null}function Et(t,e,n,i){const r=n==="horizontal",s=qe(e);S(s);const a=s.getAttribute("data-pane-group-id");S(a);const{initialCursorPosition:o}=i,l=st(n,t),p=Dt(a);S(p);const u=p.getBoundingClientRect(),c=r?u.width:u.height;return(l-o)/c*100}function Mt(t,e,n,i,r){if(et(t)){const s=n==="horizontal";let a=0;t.shiftKey?a=100:r!=null?a=r:a=10;let o=0;switch(t.key){case"ArrowDown":o=s?0:a;break;case"ArrowLeft":o=s?-a:0;break;case"ArrowRight":o=s?a:0;break;case"ArrowUp":o=s?0:-a;break;case"End":o=100;break;case"Home":o=-100;break}return o}else return i==null?0:Et(t,e,n,i)}function st(t,e){const n=t==="horizontal";if(tt(e))return n?e.clientX:e.clientY;if(nt(e)){const i=e.touches[0];return S(i),n?i.screenX:i.screenY}else throw new Error(`Unsupported event type "${e.type}"`)}function kt(t,e,n){var l,p;const i=qe(e),r=ye(t),s=i?r.indexOf(i):-1,a=((l=n[s])==null?void 0:l.opts.id.current)??null,o=((p=n[s+1])==null?void 0:p.opts.id.current)??null;return[a,o]}let Xe=0;function ze(t="paneforge"){return Xe++,`${t}-${Xe}`}function $({delta:t,layout:e,paneConstraints:n,pivotIndices:i,trigger:r}){if(D(t,0))return e;const s=[...e],[a,o]=i;let l=0;if(r==="keyboard"){{const u=t<0?o:a,c=n[u];if(S(c),c.collapsible){const g=e[u];S(g!=null);const h=n[u];S(h);const{collapsedSize:f=0,minSize:m=0}=h;if(D(g,f)){const y=m-g;te(y,Math.abs(t))>0&&(t=t<0?0-y:y)}}}{const u=t<0?a:o,c=n[u];S(c);const{collapsible:g}=c;if(g){const h=e[u];S(h!=null);const f=n[u];S(f);const{collapsedSize:m=0,minSize:y=0}=f;if(D(h,y)){const b=h-m;te(b,Math.abs(t))>0&&(t=t<0?0-b:b)}}}}{const u=t<0?1:-1;let c=t<0?o:a,g=0;for(;;){const f=e[c];S(f!=null);const y=q({paneConstraints:n,paneIndex:c,initialSize:100})-f;if(g+=y,c+=u,c<0||c>=n.length)break}const h=Math.min(Math.abs(t),Math.abs(g));t=t<0?0-h:h}{let c=t<0?a:o;for(;c>=0&&c<n.length;){const g=Math.abs(t)-Math.abs(l),h=e[c];S(h!=null);const f=h-g,m=q({paneConstraints:n,paneIndex:c,initialSize:f});if(!D(h,m)&&(l+=h-m,s[c]=m,l.toPrecision(3).localeCompare(Math.abs(t).toPrecision(3),void 0,{numeric:!0})>=0))break;t<0?c--:c++}}if(D(l,0))return e;{const u=t<0?o:a,c=e[u];S(c!=null);const g=c+l,h=q({paneConstraints:n,paneIndex:u,initialSize:g});if(s[u]=h,!D(h,g)){let f=g-h,y=t<0?o:a;for(;y>=0&&y<n.length;){const b=s[y];S(b!=null);const I=b+f,C=q({paneConstraints:n,paneIndex:y,initialSize:I});if(D(b,C)||(f-=C-b,s[y]=C),D(f,0))break;t>0?y--:y++}}}const p=s.reduce((u,c)=>c+u,0);return D(p,100)?s:e}let Be=null,B=null;function it(t){switch(t){case"horizontal":return"ew-resize";case"horizontal-max":return"w-resize";case"horizontal-min":return"e-resize";case"vertical":return"ns-resize";case"vertical-max":return"n-resize";case"vertical-min":return"s-resize"}}function Rt(){B!==null&&(document.head.removeChild(B),Be=null,B=null)}function Te(t){if(Be===t)return;Be=t;const e=it(t);B===null&&(B=document.createElement("style"),document.head.appendChild(B)),B.innerHTML=`*{cursor: ${e}!important;}`}function _t({defaultSize:t,dragState:e,layout:n,panesArray:i,paneIndex:r,precision:s=3}){const a=n[r];let o;return a==null?o=t??"1":i.length===1?o="1":o=a.toPrecision(s),{flexBasis:0,flexGrow:o,flexShrink:1,overflow:"hidden",pointerEvents:e!==null?"none":void 0}}function Ye(t){try{if(typeof localStorage>"u")throw new TypeError("localStorage is not supported in this environment");t.getItem=e=>localStorage.getItem(e),t.setItem=(e,n)=>localStorage.setItem(e,n)}catch(e){console.error(e),t.getItem=()=>null,t.setItem=()=>{}}}function at(t){return`paneforge:${t}`}function ot(t){return t.map(n=>n.opts.order.current?`${n.opts.order.current}:${JSON.stringify(n.constraints)}`:JSON.stringify(n.constraints)).sort().join(",")}function lt(t,e){try{const n=at(t),i=e.getItem(n),r=JSON.parse(i||"");if(typeof r=="object"&&r!==null)return r}catch{}return null}function Ht(t,e,n){const i=lt(t,n)||{},r=ot(e);return i[r]||null}function Tt(t,e,n,i,r){const s=at(t),a=ot(e),o=lt(t,r)||{};o[a]={expandToSizes:Object.fromEntries(n.entries()),layout:i};try{r.setItem(s,JSON.stringify(o))}catch(l){console.error(l)}}const Qe={};function Nt(t,e=10){let n=null;return(...r)=>{n!==null&&clearTimeout(n),n=setTimeout(()=>{t(...r)},e)}}function Bt({autoSaveId:t,layout:e,storage:n,panesArray:i,paneSizeBeforeCollapse:r}){if(e.length===0||e.length!==i.length)return;let s=Qe[t];s==null&&(s=Nt(Tt,wt),Qe[t]=s);const a=[...i],o=new Map(r);s(t,a,o,e,n)}const ee={getItem:t=>(Ye(ee),ee.getItem(t)),setItem:(t,e)=>{Ye(ee),ee.setItem(t,e)}};var ne,re,se,ie,ae,oe,ve,le;class Gt{constructor(e){w(this,"opts");z(this,ne,T(null));z(this,re,T([]));z(this,se,T([]));z(this,ie,T(!1));z(this,ae,T(pt({})));w(this,"paneSizeBeforeCollapseMap",new Map);z(this,oe,T(0));w(this,"setLayout",e=>{this.layout=e});w(this,"registerResizeHandle",e=>n=>{var y,b;n.preventDefault();const i=this.opts.direction.current,r=this.dragState,s=this.opts.id.current,a=this.opts.keyboardResizeBy.current,o=this.layout,l=this.panesArray,{initialLayout:p}=r??{},u=We(s,e);let c=Mt(n,e,i,r,a);if(c===0)return;const g=i==="horizontal";document.dir==="rtl"&&g&&(c=-c);const h=l.map(I=>I.constraints),f=$({delta:c,layout:p??o,paneConstraints:h,pivotIndices:u,trigger:et(n)?"keyboard":"mouse-or-touch"}),m=!Q(o,f);(tt(n)||nt(n))&&this.prevDelta!==c&&(this.prevDelta=c,Te(m?g?"horizontal":"vertical":g?c<0?"horizontal-min":"horizontal-max":c<0?"vertical-min":"vertical-max")),m&&(this.setLayout(f),(b=(y=this.opts.onLayout).current)==null||b.call(y,f),Z(l,f,this.paneIdToLastNotifiedSizeMap))});w(this,"resizePane",(e,n)=>{var c,g;const i=this.layout,r=this.panesArray,s=r.map(h=>h.constraints),{paneSize:a,pivotIndices:o}=O(r,e,i);S(a!=null);const p=j(r,e)===r.length-1?a-n:n-a,u=$({delta:p,layout:i,paneConstraints:s,pivotIndices:o,trigger:"imperative-api"});Q(i,u)||(this.setLayout(u),(g=(c=this.opts.onLayout).current)==null||g.call(c,u),Z(r,u,this.paneIdToLastNotifiedSizeMap))});w(this,"startDragging",(e,n)=>{const i=this.opts.direction.current,r=this.layout,s=qe(e);S(s);const a=st(i,n);this.dragState={dragHandleId:e,dragHandleRect:s.getBoundingClientRect(),initialCursorPosition:a,initialLayout:r}});w(this,"stopDragging",()=>{Rt(),this.dragState=null});w(this,"isPaneCollapsed",e=>{const n=this.panesArray,i=this.layout,{collapsedSize:r=0,collapsible:s,paneSize:a}=O(n,e,i);return s===!0&&a===r});w(this,"expandPane",e=>{var f,m;const n=this.layout,i=this.panesArray;if(!e.constraints.collapsible)return;const r=i.map(y=>y.constraints),{collapsedSize:s=0,paneSize:a,minSize:o=0,pivotIndices:l}=O(i,e,n);if(a!==s)return;const p=this.paneSizeBeforeCollapseMap.get(e.opts.id.current),u=p!=null&&p>=o?p:o,g=j(i,e)===i.length-1?a-u:u-a,h=$({delta:g,layout:n,paneConstraints:r,pivotIndices:l,trigger:"imperative-api"});Q(n,h)||(this.setLayout(h),(m=(f=this.opts.onLayout).current)==null||m.call(f,h),Z(i,h,this.paneIdToLastNotifiedSizeMap))});w(this,"collapsePane",e=>{var c,g;const n=this.layout,i=this.panesArray;if(!e.constraints.collapsible)return;const r=i.map(h=>h.constraints),{collapsedSize:s=0,paneSize:a,pivotIndices:o}=O(i,e,n);if(S(a!=null),a===s)return;this.paneSizeBeforeCollapseMap.set(e.opts.id.current,a);const p=j(i,e)===i.length-1?a-s:s-a,u=$({delta:p,layout:n,paneConstraints:r,pivotIndices:o,trigger:"imperative-api"});Q(n,u)||(this.layout=u,(g=(c=this.opts.onLayout).current)==null||g.call(c,u),Z(i,u,this.paneIdToLastNotifiedSizeMap))});w(this,"getPaneSize",e=>O(this.panesArray,e,this.layout).paneSize);w(this,"getPaneStyle",(e,n)=>{const i=this.panesArray,r=this.layout,s=this.dragState,a=j(i,e);return _t({defaultSize:n,dragState:s,layout:r,panesArray:i,paneIndex:a})});w(this,"isPaneExpanded",e=>{const{collapsedSize:n=0,collapsible:i,paneSize:r}=O(this.panesArray,e,this.layout);return!i||r>n});w(this,"registerPane",e=>{const n=[...this.panesArray,e];return n.sort((i,r)=>{const s=i.opts.order.current,a=r.opts.order.current;return s==null&&a==null?0:s==null?-1:a==null?1:s-a}),this.panesArray=n,this.panesArrayChanged=!0,()=>{const i=[...this.panesArray],r=j(this.panesArray,e);r<0||(i.splice(r,1),this.panesArray=i,delete this.paneIdToLastNotifiedSizeMap[e.opts.id.current],this.panesArrayChanged=!0)}});z(this,ve,()=>{const e=this.opts.id.current,n=ye(e),i=this.panesArray,r=n.map(s=>{const a=s.getAttribute("data-pane-resizer-id");if(!a)return G;const[o,l]=kt(e,a,i);if(o==null||l==null)return G;const u=xt(s,"keydown",c=>{if(c.defaultPrevented||c.key!=="Enter")return;c.preventDefault();const g=this.panesArray,h=g.findIndex(M=>M.opts.id.current===o);if(h<0)return;const f=g[h];S(f);const m=this.layout,y=m[h],{collapsedSize:b=0,collapsible:I,minSize:C=0}=f.constraints;if(!(y!=null&&I))return;const H=$({delta:D(y,b)?C-y:b-y,layout:m,paneConstraints:g.map(M=>M.constraints),pivotIndices:We(e,a),trigger:"keyboard"});m!==H&&(this.layout=H)});return()=>{u()}});return()=>{for(const s of r)s()}});z(this,le,E(()=>({id:this.opts.id.current,"data-pane-group":"","data-direction":this.opts.direction.current,"data-pane-group-id":this.opts.id.current,style:{display:"flex",flexDirection:this.opts.direction.current==="horizontal"?"row":"column",height:"100%",overflow:"hidden",width:"100%"}})));this.opts=e,Fe(e),me([()=>this.opts.id.current,()=>this.layout,()=>this.panesArray],()=>At({groupId:this.opts.id.current,layout:this.layout,panesArray:this.panesArray})),Ne(()=>ht(()=>d(this,ve).call(this))),me([()=>this.opts.autoSaveId.current,()=>this.layout,()=>this.opts.storage.current],()=>{this.opts.autoSaveId.current&&Bt({autoSaveId:this.opts.autoSaveId.current,layout:this.layout,storage:this.opts.storage.current,panesArray:this.panesArray,paneSizeBeforeCollapse:this.paneSizeBeforeCollapseMap})}),me(()=>this.panesArrayChanged,()=>{var s,a;if(!this.panesArrayChanged)return;this.panesArrayChanged=!1;const n=this.layout;let i=null;if(this.opts.autoSaveId.current){const o=Ht(this.opts.autoSaveId.current,this.panesArray,this.opts.storage.current);o&&(this.paneSizeBeforeCollapseMap=new Map(Object.entries(o.expandToSizes)),i=o.layout)}i==null&&(i=Ct({panesArray:this.panesArray}));const r=Lt({layout:i,paneConstraints:this.panesArray.map(o=>o.constraints)});Q(n,r)||(this.layout=r,(a=(s=this.opts.onLayout).current)==null||a.call(s,r),Z(this.panesArray,r,this.paneIdToLastNotifiedSizeMap))})}get dragState(){return v(d(this,ne))}set dragState(e){A(d(this,ne),e)}get layout(){return v(d(this,re))}set layout(e){A(d(this,re),e)}get panesArray(){return v(d(this,se))}set panesArray(e){A(d(this,se),e)}get panesArrayChanged(){return v(d(this,ie))}set panesArrayChanged(e){A(d(this,ie),e,!0)}get paneIdToLastNotifiedSizeMap(){return v(d(this,ae))}set paneIdToLastNotifiedSizeMap(e){A(d(this,ae),e,!0)}get prevDelta(){return v(d(this,oe))}set prevDelta(e){A(d(this,oe),e,!0)}get props(){return v(d(this,le))}set props(e){A(d(this,le),e)}}ne=new WeakMap,re=new WeakMap,se=new WeakMap,ie=new WeakMap,ae=new WeakMap,oe=new WeakMap,ve=new WeakMap,le=new WeakMap;const Kt=["ArrowDown","ArrowLeft","ArrowRight","ArrowUp","End","Home"];var ce,U,ue,J,Se,xe,be,we,Pe,Ie,Ae,Ce,de;class Ft{constructor(e,n){w(this,"opts");w(this,"group");z(this,ce,E(()=>{var e;return((e=this.group.dragState)==null?void 0:e.dragHandleId)===this.opts.id.current}));z(this,U,T(!1));w(this,"resizeHandler",null);z(this,ue,e=>{e.preventDefault(),!this.opts.disabled.current&&(this.group.startDragging(this.opts.id.current,e),this.opts.onDraggingChange.current(!0))});z(this,J,()=>{const e=this.opts.ref.current;e&&(e.blur(),this.group.stopDragging(),this.opts.onDraggingChange.current(!1))});z(this,Se,e=>{if(this.opts.disabled.current||!this.resizeHandler||e.defaultPrevented)return;if(Kt.includes(e.key)){e.preventDefault(),this.resizeHandler(e);return}if(e.key!=="F6")return;e.preventDefault();const n=ye(this.group.opts.id.current),i=rt(this.group.opts.id.current,this.opts.id.current);if(i===null)return;let r=0;e.shiftKey?i>0?r=i-1:r=n.length-1:i+1<n.length?r=i+1:r=0,n[r].focus()});z(this,xe,()=>{A(d(this,U),!1)});z(this,be,()=>{A(d(this,U),!0)});z(this,we,e=>{d(this,ue).call(this,e)});z(this,Pe,()=>{d(this,J).call(this)});z(this,Ie,()=>{d(this,J).call(this)});z(this,Ae,()=>{d(this,J).call(this)});z(this,Ce,e=>{d(this,ue).call(this,e)});z(this,de,E(()=>({id:this.opts.id.current,role:"separator","data-direction":this.group.opts.direction.current,"data-pane-group-id":this.group.opts.id.current,"data-active":v(d(this,ce))?"pointer":v(d(this,U))?"keyboard":void 0,"data-enabled":!this.opts.disabled.current,"data-pane-resizer-id":this.opts.id.current,"data-pane-resizer":"",tabIndex:this.opts.tabIndex.current,style:{cursor:it(this.group.opts.direction.current),touchAction:"none",userSelect:"none","-webkit-user-select":"none","-webkit-touch-callout":"none"},onkeydown:d(this,Se),onblur:d(this,xe),onfocus:d(this,be),onmousedown:d(this,we),onmouseup:d(this,Pe),ontouchcancel:d(this,Ie),ontouchend:d(this,Ae),ontouchstart:d(this,Ce)})));this.opts=e,this.group=n,Fe(e),Ne(()=>{this.opts.disabled.current?this.resizeHandler=null:this.resizeHandler=this.group.registerResizeHandle(this.opts.id.current)}),Ne(()=>{const i=this.opts.ref.current;if(!i)return;const r=this.opts.disabled.current,s=this.resizeHandler,a=v(d(this,ce));if(r||s===null||!a)return;const o=u=>{s(u)},l=u=>{s(u)},p=()=>{i.blur(),this.group.stopDragging(),this.opts.onDraggingChange.current(!1)};return ft(F(document.body,"contextmenu",p),F(document.body,"mousemove",o),F(document.body,"touchmove",o,{passive:!1}),F(document.body,"mouseleave",l),F(window,"mouseup",p),F(window,"touchend",p))})}get props(){return v(d(this,de))}set props(e){A(d(this,de),e)}}ce=new WeakMap,U=new WeakMap,ue=new WeakMap,J=new WeakMap,Se=new WeakMap,xe=new WeakMap,be=new WeakMap,we=new WeakMap,Pe=new WeakMap,Ie=new WeakMap,Ae=new WeakMap,Ce=new WeakMap,de=new WeakMap;var _,pe,he,fe,V,Le,ge;class Ot{constructor(e,n){w(this,"opts");w(this,"group");z(this,_,T(""));z(this,pe,E(()=>({onCollapse:this.opts.onCollapse.current,onExpand:this.opts.onExpand.current,onResize:this.opts.onResize.current})));z(this,he,E(()=>({collapsedSize:this.opts.collapsedSize.current,collapsible:this.opts.collapsible.current,defaultSize:this.opts.defaultSize.current,maxSize:this.opts.maxSize.current,minSize:this.opts.minSize.current})));z(this,fe,e=>{A(d(this,_),e,!0),vt(()=>{if(this.opts.ref.current){const n=this.opts.ref.current;if(!(getComputedStyle(n).transitionDuration!=="0s")){A(d(this,_),"");return}const s=a=>{a.propertyName==="flex-grow"&&(A(d(this,_),""),n.removeEventListener("transitionend",s))};n.addEventListener("transitionend",s)}else A(d(this,_),"")})});w(this,"pane",{collapse:()=>{d(this,fe).call(this,"collapsing"),this.group.collapsePane(this)},expand:()=>{d(this,fe).call(this,"expanding"),this.group.expandPane(this)},getSize:()=>this.group.getPaneSize(this),isCollapsed:()=>this.group.isPaneCollapsed(this),isExpanded:()=>this.group.isPaneExpanded(this),resize:e=>this.group.resizePane(this,e),getId:()=>this.opts.id.current});z(this,V,E(()=>this.group.isPaneCollapsed(this)));z(this,Le,E(()=>v(d(this,_))!==""?v(d(this,_)):v(d(this,V))?"collapsed":"expanded"));z(this,ge,E(()=>({id:this.opts.id.current,style:this.group.getPaneStyle(this,this.opts.defaultSize.current),"data-pane":"","data-pane-id":this.opts.id.current,"data-pane-group-id":this.group.opts.id.current,"data-collapsed":v(d(this,V))?"":void 0,"data-expanded":v(d(this,V))?void 0:"","data-pane-state":v(d(this,Le))})));this.opts=e,this.group=n,Fe(e),mt(()=>this.group.registerPane(this)),me(()=>zt(this.constraints),()=>{this.group.panesArrayChanged=!0})}get callbacks(){return v(d(this,pe))}set callbacks(e){A(d(this,pe),e)}get constraints(){return v(d(this,he))}set constraints(e){A(d(this,he),e)}get props(){return v(d(this,ge))}set props(e){A(d(this,ge),e)}}_=new WeakMap,pe=new WeakMap,he=new WeakMap,fe=new WeakMap,V=new WeakMap,Le=new WeakMap,ge=new WeakMap;const Ue=new St("PaneGroup");function jt(t){return Ue.set(new Gt(t))}function qt(t){return new Ft(t,Ue.get())}function Ut(t){return new Ot(t,Ue.get())}var Jt=De("<div><!></div>");function Vt(t,e){W(e,!0);let n=P(e,"autoSaveId",3,null),i=P(e,"id",19,ze),r=P(e,"keyboardResizeBy",3,null),s=P(e,"onLayoutChange",3,G),a=P(e,"storage",3,ee),o=P(e,"ref",15,null),l=Y(e,["$$slots","$$events","$$legacy","autoSaveId","direction","id","keyboardResizeBy","onLayoutChange","storage","ref","child","children"]);const p=jt({id:x.with(()=>i()??ze()),ref:x.with(()=>o(),I=>o(I)),autoSaveId:x.with(()=>n()),direction:x.with(()=>e.direction),keyboardResizeBy:x.with(()=>r()),onLayout:x.with(()=>s()),storage:x.with(()=>a())}),u=()=>p.layout,c=p.setLayout,g=()=>p.opts.id.current,h=E(()=>Oe(l,p.props));var f=k(),m=R(f);{var y=I=>{var C=k(),H=R(C);K(H,()=>e.child,()=>({props:v(h)})),L(I,C)},b=I=>{var C=Jt();Ge(C,()=>({...v(h)}));var H=Ee(C);K(H,()=>e.children??Me),ke(C),L(I,C)};Re(m,I=>{e.child?I(y):I(b,!1)})}return L(t,f),X({getLayout:u,setLayout:c,getId:g})}var Wt=De("<div><!></div>");function yn(t,e){W(e,!0);let n=P(e,"id",19,ze),i=P(e,"ref",15,null),r=P(e,"onCollapse",3,G),s=P(e,"onExpand",3,G),a=P(e,"onResize",3,G),o=Y(e,["$$slots","$$events","$$legacy","id","ref","collapsedSize","collapsible","defaultSize","maxSize","minSize","onCollapse","onExpand","onResize","order","child","children"]);const l=Ut({id:x.with(()=>n()),ref:x.with(()=>i(),M=>i(M)),collapsedSize:x.with(()=>e.collapsedSize),collapsible:x.with(()=>e.collapsible),defaultSize:x.with(()=>e.defaultSize),maxSize:x.with(()=>e.maxSize),minSize:x.with(()=>e.minSize),onCollapse:x.with(()=>r()),onExpand:x.with(()=>s()),onResize:x.with(()=>a()),order:x.with(()=>e.order)}),p=l.pane.collapse,u=l.pane.expand,c=l.pane.getSize,g=l.pane.isCollapsed,h=l.pane.isExpanded,f=l.pane.resize,m=l.pane.getId,y=E(()=>Oe(o,l.props));var b=k(),I=R(b);{var C=M=>{var N=k(),He=R(N);K(He,()=>e.child,()=>({props:v(y)})),L(M,N)},H=M=>{var N=Wt();Ge(N,()=>({...v(y)}));var He=Ee(N);K(He,()=>e.children??Me),ke(N),L(M,N)};Re(I,M=>{e.child?M(C):M(H,!1)})}return L(t,b),X({collapse:p,expand:u,getSize:c,isCollapsed:g,isExpanded:h,resize:f,getId:m})}var Xt=De("<div><!></div>");function Yt(t,e){W(e,!0);let n=P(e,"id",19,ze),i=P(e,"ref",15,null),r=P(e,"disabled",3,!1),s=P(e,"onDraggingChange",3,G),a=P(e,"tabindex",3,0),o=Y(e,["$$slots","$$events","$$legacy","id","ref","disabled","onDraggingChange","tabindex","child","children"]);const l=qt({id:x.with(()=>n()),ref:x.with(()=>i(),f=>i(f)),disabled:x.with(()=>r()),onDraggingChange:x.with(()=>s()),tabIndex:x.with(()=>a())}),p=E(()=>Oe(o,l.props));var u=k(),c=R(u);{var g=f=>{var m=k(),y=R(m);K(y,()=>e.child,()=>({props:v(p)})),L(f,m)},h=f=>{var m=Xt();Ge(m,()=>({...v(p)}));var y=Ee(m);K(y,()=>e.children??Me),ke(m),L(f,m)};Re(c,f=>{e.child?f(g):f(h,!1)})}L(t,u),X()}function Qt(t,e){W(e,!0);let n=Y(e,["$$slots","$$events","$$legacy"]);const i=[["circle",{cx:"9",cy:"12",r:"1"}],["circle",{cx:"9",cy:"5",r:"1"}],["circle",{cx:"9",cy:"19",r:"1"}],["circle",{cx:"15",cy:"12",r:"1"}],["circle",{cx:"15",cy:"5",r:"1"}],["circle",{cx:"15",cy:"19",r:"1"}]];gt(t,Ke({name:"grip-vertical"},()=>n,{get iconNode(){return i},children:(r,s)=>{var a=k(),o=R(a);K(o,()=>e.children??Me),L(r,a)},$$slots:{default:!0}})),X()}var Zt=De('<div class="bg-border rounded-xs z-10 flex h-4 w-3 items-center justify-center border"><!></div>');function mn(t,e){W(e,!0);let n=P(e,"ref",15,null),i=P(e,"withHandle",3,!1),r=Y(e,["$$slots","$$events","$$legacy","ref","class","withHandle"]);var s=k(),a=R(s);const o=E(()=>Ze("bg-border focus-visible:ring-ring focus-visible:outline-hidden relative flex w-px items-center justify-center after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:ring-1 focus-visible:ring-offset-1 data-[direction=vertical]:h-px data-[direction=vertical]:w-full data-[direction=vertical]:after:left-0 data-[direction=vertical]:after:h-1 data-[direction=vertical]:after:w-full data-[direction=vertical]:after:-translate-y-1/2 data-[direction=vertical]:after:translate-x-0 [&[data-direction=vertical]>div]:rotate-90",e.class));$e(a,()=>Yt,(l,p)=>{p(l,Ke({"data-slot":"resizable-handle",get class(){return v(o)}},()=>r,{get ref(){return n()},set ref(u){n(u)},children:(u,c)=>{var g=k(),h=R(g);{var f=m=>{var y=Zt(),b=Ee(y);Qt(b,{class:"size-2.5"}),ke(y),L(m,y)};Re(h,m=>{i()&&m(f)})}L(u,g)},$$slots:{default:!0}}))}),L(t,s),X()}function zn(t,e){W(e,!0),P(e,"ref",11,null);let n=P(e,"this",15),i=Y(e,["$$slots","$$events","$$legacy","ref","this","class"]);var r=k(),s=R(r);const a=E(()=>Ze("flex h-full w-full data-[direction=vertical]:flex-col",e.class));$e(s,()=>Vt,(o,l)=>{yt(l(o,Ke({"data-slot":"resizable-pane-group",get class(){return v(a)}},()=>i)),p=>n(p),()=>n())}),L(t,r),X()}export{yn as P,zn as R,mn as a};
