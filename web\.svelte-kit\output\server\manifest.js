export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["assets/favicon/favicon-128x128.png","assets/favicon/favicon-16x16.png","assets/favicon/favicon-192x192.png","assets/favicon/favicon-256x256.png","assets/favicon/favicon-32x32.png","assets/favicon/favicon-48x48.png","assets/favicon/favicon-512x512.png","assets/favicon/favicon-64x64.png","assets/favicon/favicon.ico","assets/favicon/manifest.json","assets/fonts/Inter-Italic-VariableFont_opsz,wght.ttf","assets/fonts/Inter-VariableFont_opsz,wght.ttf","assets/fonts/Roboto-Italic-VariableFont_wdth,wght.ttf","assets/fonts/Roboto-VariableFont_wdth,wght.ttf","assets/svg/google.svg","assets/svg/linkedin.svg","assets/webp/accent.webp","assets/webp/classic.webp","assets/webp/minimalist.webp","assets/webp/modern.webp","debug-tools.html","site.webmanifest","studio/favicon.ico","studio/index.html","studio/static/.gitkeep","studio/static/apple-touch-icon.png","studio/static/browser-brmmQ1t9.js","studio/static/favicon-192.png","studio/static/favicon-512.png","studio/static/favicon-96.png","studio/static/favicon.ico","studio/static/favicon.svg","studio/static/index-D8KJVLq7.js","studio/static/index2-BMjMOPK3.js","studio/static/index3-BNY2sVNS.js","studio/static/manifest.webmanifest","studio/static/refractor-fojG8wt3.js","studio/static/resources-Ck3zQgZu.js","studio/static/resources-DSajARg_.js","studio/static/resources2-D6hvydvy.js","studio/static/resources3-CZHseQDq.js","studio/static/resources4-1DGMzDYL.js","studio/static/resources5-DsYbJdPK.js","studio/static/resources6-D4aEngfB.js","studio/static/sanity-CyfLjDop.js","studio/static/sanity-DV0NwVOn.js","studio/static/sanity-X1s7OzRS.js","studio/static/SanityVision-B_653MrW.js","studio/static/stegaEncodeSourceMap-B9cQufyk.js","studio/static/ViteDevServerStopped-B_NO0zRS.js","studio/vendor/react/index-ClGR8UKT.mjs","studio/vendor/react/jsx-dev-runtime-CmvbC6Mp.mjs","studio/vendor/react/jsx-runtime-MPXMuslR.mjs","studio/vendor/react/package.json-_hTv_IDL.mjs","studio/vendor/react-dom/client-BjvhQLWp.mjs","studio/vendor/react-dom/index-BtnwqIhl.mjs","studio/vendor/react-dom/package.json-BEz7F3rk.mjs","studio/vendor/react-dom/server-HH7ZgBma.mjs","studio/vendor/react-dom/server.browser-gWQ0XzMy.mjs","studio/vendor/styled-components/index-BP3W3NaG.mjs","studio/vendor/styled-components/package.json-D1iMoum4.mjs","uploads/cover-letters/9cac1c6a-bfdd-46b0-bfbc-5eeaa21799eb.docx","uploads/documents/test-document.txt","uploads/references/3ab3e0e5-b0f2-4ec2-8a9c-3dfea5a70349.doc","uploads/resumes/f25f4797-2777-4dbc-8810-870271104d97.pdf","uploads/resumes/Resume.docx","uploads/sample-resumes/deedy-resume-sample.png","uploads/sample-resumes/sample-resume-1.docx","uploads/sample-resumes/sample-resume-2.docx","uploads/sample-resumes/sample-resume-3.docx","uploads/sample-resumes/software-engineer-github-2.pdf","service-worker.js"]),
	mimeTypes: {".png":"image/png",".json":"application/json",".ttf":"font/ttf",".svg":"image/svg+xml",".webp":"image/webp",".html":"text/html",".webmanifest":"application/manifest+json",".js":"text/javascript",".mjs":"text/javascript",".txt":"text/plain",".doc":"application/msword",".pdf":"application/pdf"},
	_: {
		client: {start:"_app/immutable/entry/start.LXXwh9lH.js",app:"_app/immutable/entry/app.Br3zMcAh.js",imports:["_app/immutable/entry/start.LXXwh9lH.js","_app/immutable/chunks/BiJhC7W5.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/entry/app.Br3zMcAh.js","_app/immutable/chunks/C1FmrZbK.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Btcx8l8F.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js')),
			__memo(() => import('./nodes/4.js')),
			__memo(() => import('./nodes/5.js')),
			__memo(() => import('./nodes/6.js')),
			__memo(() => import('./nodes/7.js')),
			__memo(() => import('./nodes/8.js')),
			__memo(() => import('./nodes/9.js')),
			__memo(() => import('./nodes/10.js')),
			__memo(() => import('./nodes/11.js')),
			__memo(() => import('./nodes/12.js')),
			__memo(() => import('./nodes/13.js')),
			__memo(() => import('./nodes/14.js')),
			__memo(() => import('./nodes/15.js')),
			__memo(() => import('./nodes/16.js')),
			__memo(() => import('./nodes/17.js')),
			__memo(() => import('./nodes/18.js')),
			__memo(() => import('./nodes/19.js')),
			__memo(() => import('./nodes/20.js')),
			__memo(() => import('./nodes/21.js')),
			__memo(() => import('./nodes/22.js')),
			__memo(() => import('./nodes/23.js')),
			__memo(() => import('./nodes/24.js')),
			__memo(() => import('./nodes/25.js')),
			__memo(() => import('./nodes/26.js')),
			__memo(() => import('./nodes/27.js')),
			__memo(() => import('./nodes/28.js')),
			__memo(() => import('./nodes/29.js')),
			__memo(() => import('./nodes/30.js')),
			__memo(() => import('./nodes/31.js')),
			__memo(() => import('./nodes/32.js')),
			__memo(() => import('./nodes/33.js')),
			__memo(() => import('./nodes/34.js')),
			__memo(() => import('./nodes/35.js')),
			__memo(() => import('./nodes/36.js')),
			__memo(() => import('./nodes/37.js')),
			__memo(() => import('./nodes/38.js')),
			__memo(() => import('./nodes/39.js')),
			__memo(() => import('./nodes/40.js')),
			__memo(() => import('./nodes/41.js')),
			__memo(() => import('./nodes/42.js')),
			__memo(() => import('./nodes/43.js')),
			__memo(() => import('./nodes/44.js')),
			__memo(() => import('./nodes/45.js')),
			__memo(() => import('./nodes/46.js')),
			__memo(() => import('./nodes/47.js')),
			__memo(() => import('./nodes/48.js')),
			__memo(() => import('./nodes/49.js')),
			__memo(() => import('./nodes/50.js')),
			__memo(() => import('./nodes/51.js')),
			__memo(() => import('./nodes/52.js')),
			__memo(() => import('./nodes/53.js')),
			__memo(() => import('./nodes/54.js')),
			__memo(() => import('./nodes/55.js')),
			__memo(() => import('./nodes/56.js')),
			__memo(() => import('./nodes/57.js')),
			__memo(() => import('./nodes/58.js')),
			__memo(() => import('./nodes/59.js')),
			__memo(() => import('./nodes/60.js')),
			__memo(() => import('./nodes/61.js')),
			__memo(() => import('./nodes/62.js')),
			__memo(() => import('./nodes/63.js')),
			__memo(() => import('./nodes/64.js')),
			__memo(() => import('./nodes/65.js')),
			__memo(() => import('./nodes/66.js')),
			__memo(() => import('./nodes/67.js')),
			__memo(() => import('./nodes/68.js')),
			__memo(() => import('./nodes/69.js')),
			__memo(() => import('./nodes/70.js')),
			__memo(() => import('./nodes/71.js')),
			__memo(() => import('./nodes/72.js')),
			__memo(() => import('./nodes/73.js')),
			__memo(() => import('./nodes/74.js')),
			__memo(() => import('./nodes/75.js')),
			__memo(() => import('./nodes/76.js')),
			__memo(() => import('./nodes/77.js')),
			__memo(() => import('./nodes/78.js')),
			__memo(() => import('./nodes/79.js')),
			__memo(() => import('./nodes/80.js')),
			__memo(() => import('./nodes/81.js')),
			__memo(() => import('./nodes/82.js')),
			__memo(() => import('./nodes/83.js')),
			__memo(() => import('./nodes/84.js')),
			__memo(() => import('./nodes/85.js')),
			__memo(() => import('./nodes/86.js')),
			__memo(() => import('./nodes/87.js')),
			__memo(() => import('./nodes/88.js')),
			__memo(() => import('./nodes/89.js')),
			__memo(() => import('./nodes/90.js')),
			__memo(() => import('./nodes/91.js')),
			__memo(() => import('./nodes/92.js')),
			__memo(() => import('./nodes/93.js')),
			__memo(() => import('./nodes/94.js')),
			__memo(() => import('./nodes/95.js')),
			__memo(() => import('./nodes/96.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 9 },
				endpoint: __memo(() => import('./entries/endpoints/_server.ts.js'))
			},
			{
				id: "/about",
				pattern: /^\/about\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 10 },
				endpoint: null
			},
			{
				id: "/admin/features",
				pattern: /^\/admin\/features\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 11 },
				endpoint: null
			},
			{
				id: "/api",
				pattern: /^\/api\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/_server.ts.js'))
			},
			{
				id: "/api/admin/check-admin",
				pattern: /^\/api\/admin\/check-admin\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/check-admin/_server.ts.js'))
			},
			{
				id: "/api/admin/feature-usage",
				pattern: /^\/api\/admin\/feature-usage\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/feature-usage/_server.ts.js'))
			},
			{
				id: "/api/admin/feature-usage/check",
				pattern: /^\/api\/admin\/feature-usage\/check\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/feature-usage/check/_server.ts.js'))
			},
			{
				id: "/api/admin/feature-usage/export",
				pattern: /^\/api\/admin\/feature-usage\/export\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/feature-usage/export/_server.ts.js'))
			},
			{
				id: "/api/admin/feature-usage/summary",
				pattern: /^\/api\/admin\/feature-usage\/summary\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/feature-usage/summary/_server.ts.js'))
			},
			{
				id: "/api/admin/features",
				pattern: /^\/api\/admin\/features\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/features/_server.ts.js'))
			},
			{
				id: "/api/admin/features/seed-all",
				pattern: /^\/api\/admin\/features\/seed-all\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/features/seed-all/_server.ts.js'))
			},
			{
				id: "/api/admin/features/seed-analysis",
				pattern: /^\/api\/admin\/features\/seed-analysis\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/features/seed-analysis/_server.ts.js'))
			},
			{
				id: "/api/admin/features/seed-service",
				pattern: /^\/api\/admin\/features\/seed-service\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/features/seed-service/_server.ts.js'))
			},
			{
				id: "/api/admin/features/sync-all",
				pattern: /^\/api\/admin\/features\/sync-all\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/features/sync-all/_server.ts.js'))
			},
			{
				id: "/api/admin/features/sync",
				pattern: /^\/api\/admin\/features\/sync\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/features/sync/_server.ts.js'))
			},
			{
				id: "/api/admin/initialize-features",
				pattern: /^\/api\/admin\/initialize-features\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/initialize-features/_server.ts.js'))
			},
			{
				id: "/api/admin/make-admin",
				pattern: /^\/api\/admin\/make-admin\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/make-admin/_server.ts.js'))
			},
			{
				id: "/api/admin/mock-users",
				pattern: /^\/api\/admin\/mock-users\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/mock-users/_server.ts.js'))
			},
			{
				id: "/api/admin/plans",
				pattern: /^\/api\/admin\/plans\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/plans/_server.ts.js'))
			},
			{
				id: "/api/admin/plans/initialize",
				pattern: /^\/api\/admin\/plans\/initialize\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/plans/initialize/_server.ts.js'))
			},
			{
				id: "/api/admin/plans/load-from-stripe",
				pattern: /^\/api\/admin\/plans\/load-from-stripe\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/plans/load-from-stripe/_server.ts.js'))
			},
			{
				id: "/api/admin/plans/sync-stripe",
				pattern: /^\/api\/admin\/plans\/sync-stripe\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/plans/sync-stripe/_server.ts.js'))
			},
			{
				id: "/api/admin/seed-features",
				pattern: /^\/api\/admin\/seed-features\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/seed-features/_server.ts.js'))
			},
			{
				id: "/api/admin/users",
				pattern: /^\/api\/admin\/users\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/users/_server.ts.js'))
			},
			{
				id: "/api/admin/users/[userId]",
				pattern: /^\/api\/admin\/users\/([^/]+?)\/?$/,
				params: [{"name":"userId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/users/_userId_/_server.ts.js'))
			},
			{
				id: "/api/admin/users/[userId]/plan",
				pattern: /^\/api\/admin\/users\/([^/]+?)\/plan\/?$/,
				params: [{"name":"userId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/admin/users/_userId_/plan/_server.ts.js'))
			},
			{
				id: "/api/ai/ats",
				pattern: /^\/api\/ai\/ats\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/ats/_server.ts.js'))
			},
			{
				id: "/api/ai/ats/analyze/[resumeId]",
				pattern: /^\/api\/ai\/ats\/analyze\/([^/]+?)\/?$/,
				params: [{"name":"resumeId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/ats/analyze/_resumeId_/_server.ts.js'))
			},
			{
				id: "/api/ai/ats/job-match",
				pattern: /^\/api\/ai\/ats\/job-match\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/ats/job-match/_server.ts.js'))
			},
			{
				id: "/api/ai/ats/job",
				pattern: /^\/api\/ai\/ats\/job\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/ats/job/_server.ts.js'))
			},
			{
				id: "/api/ai/ats/text",
				pattern: /^\/api\/ai\/ats\/text\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/ats/text/_server.ts.js'))
			},
			{
				id: "/api/ai/interview",
				pattern: /^\/api\/ai\/interview\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/interview/_server.ts.js'))
			},
			{
				id: "/api/ai/interview/sessions",
				pattern: /^\/api\/ai\/interview\/sessions\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/interview/sessions/_server.ts.js'))
			},
			{
				id: "/api/ai/interview/[id]",
				pattern: /^\/api\/ai\/interview\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/interview/_id_/_server.ts.js'))
			},
			{
				id: "/api/ai/job-match",
				pattern: /^\/api\/ai\/job-match\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/job-match/_server.ts.js'))
			},
			{
				id: "/api/ai/jobs/match-details",
				pattern: /^\/api\/ai\/jobs\/match-details\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/jobs/match-details/_server.ts.js'))
			},
			{
				id: "/api/ai/resume/suggestions",
				pattern: /^\/api\/ai\/resume\/suggestions\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/resume/suggestions/_server.ts.js'))
			},
			{
				id: "/api/ai/resume/suggestions/[id]/apply",
				pattern: /^\/api\/ai\/resume\/suggestions\/([^/]+?)\/apply\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ai/resume/suggestions/_id_/apply/_server.ts.js'))
			},
			{
				id: "/api/applications",
				pattern: /^\/api\/applications\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/applications/_server.ts.js'))
			},
			{
				id: "/api/applications/add",
				pattern: /^\/api\/applications\/add\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/applications/add/_server.ts.js'))
			},
			{
				id: "/api/applications/check",
				pattern: /^\/api\/applications\/check\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/applications/check/_server.ts.js'))
			},
			{
				id: "/api/applications/status",
				pattern: /^\/api\/applications\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/applications/status/_server.ts.js'))
			},
			{
				id: "/api/applications/[applicationId]",
				pattern: /^\/api\/applications\/([^/]+?)\/?$/,
				params: [{"name":"applicationId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/applications/_applicationId_/_server.ts.js'))
			},
			{
				id: "/api/applications/[applicationId]/interviews",
				pattern: /^\/api\/applications\/([^/]+?)\/interviews\/?$/,
				params: [{"name":"applicationId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/applications/_applicationId_/interviews/_server.ts.js'))
			},
			{
				id: "/api/applications/[applicationId]/interviews/[interviewId]",
				pattern: /^\/api\/applications\/([^/]+?)\/interviews\/([^/]+?)\/?$/,
				params: [{"name":"applicationId","optional":false,"rest":false,"chained":false},{"name":"interviewId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/applications/_applicationId_/interviews/_interviewId_/_server.ts.js'))
			},
			{
				id: "/api/applications/[applicationId]/interviews/[interviewId]/questions",
				pattern: /^\/api\/applications\/([^/]+?)\/interviews\/([^/]+?)\/questions\/?$/,
				params: [{"name":"applicationId","optional":false,"rest":false,"chained":false},{"name":"interviewId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/applications/_applicationId_/interviews/_interviewId_/questions/_server.ts.js'))
			},
			{
				id: "/api/auth/check-session",
				pattern: /^\/api\/auth\/check-session\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/check-session/_server.ts.js'))
			},
			{
				id: "/api/auth/forgot-password",
				pattern: /^\/api\/auth\/forgot-password\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/forgot-password/_server.ts.js'))
			},
			{
				id: "/api/auth/google",
				pattern: /^\/api\/auth\/google\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/google/_server.ts.js'))
			},
			{
				id: "/api/auth/linkedin",
				pattern: /^\/api\/auth\/linkedin\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/linkedin/_server.ts.js'))
			},
			{
				id: "/api/auth/login",
				pattern: /^\/api\/auth\/login\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/login/_server.ts.js'))
			},
			{
				id: "/api/auth/logout",
				pattern: /^\/api\/auth\/logout\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/logout/_server.ts.js'))
			},
			{
				id: "/api/auth/refresh-session",
				pattern: /^\/api\/auth\/refresh-session\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/refresh-session/_server.ts.js'))
			},
			{
				id: "/api/auth/resend-verification",
				pattern: /^\/api\/auth\/resend-verification\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/resend-verification/_server.ts.js'))
			},
			{
				id: "/api/auth/reset-password",
				pattern: /^\/api\/auth\/reset-password\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/reset-password/_server.ts.js'))
			},
			{
				id: "/api/auth/signup",
				pattern: /^\/api\/auth\/signup\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/signup/_server.ts.js'))
			},
			{
				id: "/api/auth/verify-token",
				pattern: /^\/api\/auth\/verify-token\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/verify-token/_server.ts.js'))
			},
			{
				id: "/api/auth/verify",
				pattern: /^\/api\/auth\/verify\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/auth/verify/_server.ts.js'))
			},
			{
				id: "/api/automation/runs",
				pattern: /^\/api\/automation\/runs\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/automation/runs/_server.ts.js'))
			},
			{
				id: "/api/automation/runs/[id]",
				pattern: /^\/api\/automation\/runs\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/automation/runs/_id_/_server.ts.js'))
			},
			{
				id: "/api/automation/runs/[id]/jobs",
				pattern: /^\/api\/automation\/runs\/([^/]+?)\/jobs\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/automation/runs/_id_/jobs/_server.ts.js'))
			},
			{
				id: "/api/automation/runs/[id]/settings",
				pattern: /^\/api\/automation\/runs\/([^/]+?)\/settings\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/automation/runs/_id_/settings/_server.ts.js'))
			},
			{
				id: "/api/automation/runs/[id]/stop",
				pattern: /^\/api\/automation\/runs\/([^/]+?)\/stop\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/automation/runs/_id_/stop/_server.ts.js'))
			},
			{
				id: "/api/automation/ws",
				pattern: /^\/api\/automation\/ws\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/automation/ws/_server.ts.js'))
			},
			{
				id: "/api/billing/cancel-subscription",
				pattern: /^\/api\/billing\/cancel-subscription\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/billing/cancel-subscription/_server.ts.js'))
			},
			{
				id: "/api/billing/create-checkout-session",
				pattern: /^\/api\/billing\/create-checkout-session\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/billing/create-checkout-session/_server.ts.js'))
			},
			{
				id: "/api/billing/create-portal-session",
				pattern: /^\/api\/billing\/create-portal-session\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/billing/create-portal-session/_server.ts.js'))
			},
			{
				id: "/api/billing/create-setup-intent",
				pattern: /^\/api\/billing\/create-setup-intent\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/billing/create-setup-intent/_server.ts.js'))
			},
			{
				id: "/api/billing/data",
				pattern: /^\/api\/billing\/data\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/billing/data/_server.ts.js'))
			},
			{
				id: "/api/billing/delete-payment-method",
				pattern: /^\/api\/billing\/delete-payment-method\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/billing/delete-payment-method/_server.ts.js'))
			},
			{
				id: "/api/billing/get-payment-methods",
				pattern: /^\/api\/billing\/get-payment-methods\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/billing/get-payment-methods/_server.ts.js'))
			},
			{
				id: "/api/billing/payment-methods",
				pattern: /^\/api\/billing\/payment-methods\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/billing/payment-methods/_server.ts.js'))
			},
			{
				id: "/api/billing/resume-subscription",
				pattern: /^\/api\/billing\/resume-subscription\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/billing/resume-subscription/_server.ts.js'))
			},
			{
				id: "/api/billing/set-default-payment-method",
				pattern: /^\/api\/billing\/set-default-payment-method\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/billing/set-default-payment-method/_server.ts.js'))
			},
			{
				id: "/api/checkout",
				pattern: /^\/api\/checkout\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/checkout/_server.ts.js'))
			},
			{
				id: "/api/companies",
				pattern: /^\/api\/companies\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/companies/_server.ts.js'))
			},
			{
				id: "/api/companies/featured",
				pattern: /^\/api\/companies\/featured\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/companies/featured/_server.ts.js'))
			},
			{
				id: "/api/documents",
				pattern: /^\/api\/documents\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/documents/_server.ts.js'))
			},
			{
				id: "/api/documents/[id]",
				pattern: /^\/api\/documents\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/documents/_id_/_server.ts.js'))
			},
			{
				id: "/api/documents/[id]/parse",
				pattern: /^\/api\/documents\/([^/]+?)\/parse\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/documents/_id_/parse/_server.ts.js'))
			},
			{
				id: "/api/documents/[id]/view",
				pattern: /^\/api\/documents\/([^/]+?)\/view\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/documents/_id_/view/_server.ts.js'))
			},
			{
				id: "/api/document/upload",
				pattern: /^\/api\/document\/upload\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/document/upload/_server.ts.js'))
			},
			{
				id: "/api/document/[id]",
				pattern: /^\/api\/document\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/document/_id_/_server.ts.js'))
			},
			{
				id: "/api/email/analytics",
				pattern: /^\/api\/email\/analytics\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/analytics/_server.ts.js'))
			},
			{
				id: "/api/email/analytics/check",
				pattern: /^\/api\/email\/analytics\/check\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/analytics/check/_server.ts.js'))
			},
			{
				id: "/api/email/analytics/events",
				pattern: /^\/api\/email\/analytics\/events\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/analytics/events/_server.ts.js'))
			},
			{
				id: "/api/email/analytics/export",
				pattern: /^\/api\/email\/analytics\/export\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/analytics/export/_server.ts.js'))
			},
			{
				id: "/api/email/analytics/stats",
				pattern: /^\/api\/email\/analytics\/stats\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/analytics/stats/_server.ts.js'))
			},
			{
				id: "/api/email/audiences",
				pattern: /^\/api\/email\/audiences\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/audiences/_server.ts.js'))
			},
			{
				id: "/api/email/audiences/contacts",
				pattern: /^\/api\/email\/audiences\/contacts\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/audiences/contacts/_server.ts.js'))
			},
			{
				id: "/api/email/audiences/contacts/import",
				pattern: /^\/api\/email\/audiences\/contacts\/import\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/audiences/contacts/import/_server.ts.js'))
			},
			{
				id: "/api/email/audiences/[id]",
				pattern: /^\/api\/email\/audiences\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/audiences/_id_/_server.ts.js'))
			},
			{
				id: "/api/email/audiences/[id]/contacts",
				pattern: /^\/api\/email\/audiences\/([^/]+?)\/contacts\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/audiences/_id_/contacts/_server.ts.js'))
			},
			{
				id: "/api/email/audiences/[id]/import",
				pattern: /^\/api\/email\/audiences\/([^/]+?)\/import\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/audiences/_id_/import/_server.ts.js'))
			},
			{
				id: "/api/email/broadcasts",
				pattern: /^\/api\/email\/broadcasts\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/broadcasts/_server.ts.js'))
			},
			{
				id: "/api/email/broadcasts/cancel",
				pattern: /^\/api\/email\/broadcasts\/cancel\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/broadcasts/cancel/_server.ts.js'))
			},
			{
				id: "/api/email/clear-failed",
				pattern: /^\/api\/email\/clear-failed\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/clear-failed/_server.ts.js'))
			},
			{
				id: "/api/email/config",
				pattern: /^\/api\/email\/config\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/config/_server.ts.js'))
			},
			{
				id: "/api/email/process-queue",
				pattern: /^\/api\/email\/process-queue\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/process-queue/_server.ts.js'))
			},
			{
				id: "/api/email/queue-status",
				pattern: /^\/api\/email\/queue-status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/queue-status/_server.ts.js'))
			},
			{
				id: "/api/email/queue",
				pattern: /^\/api\/email\/queue\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/queue/_server.ts.js'))
			},
			{
				id: "/api/email/retry-failed",
				pattern: /^\/api\/email\/retry-failed\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/retry-failed/_server.ts.js'))
			},
			{
				id: "/api/email/status",
				pattern: /^\/api\/email\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/status/_server.ts.js'))
			},
			{
				id: "/api/email/templates/list",
				pattern: /^\/api\/email\/templates\/list\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/templates/list/_server.ts.js'))
			},
			{
				id: "/api/email/webhook",
				pattern: /^\/api\/email\/webhook\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/webhook/_server.ts.js'))
			},
			{
				id: "/api/email/webhook/setup",
				pattern: /^\/api\/email\/webhook\/setup\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 12 },
				endpoint: null
			},
			{
				id: "/api/email/worker",
				pattern: /^\/api\/email\/worker\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/worker/_server.ts.js'))
			},
			{
				id: "/api/email/worker/metrics",
				pattern: /^\/api\/email\/worker\/metrics\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/email/worker/metrics/_server.ts.js'))
			},
			{
				id: "/api/feature-access",
				pattern: /^\/api\/feature-access\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/feature-access/_server.ts.js'))
			},
			{
				id: "/api/feature-check",
				pattern: /^\/api\/feature-check\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/feature-check/_server.ts.js'))
			},
			{
				id: "/api/feature-usage",
				pattern: /^\/api\/feature-usage\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/feature-usage/_server.ts.js'))
			},
			{
				id: "/api/feature-usage/reset",
				pattern: /^\/api\/feature-usage\/reset\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/feature-usage/reset/_server.ts.js'))
			},
			{
				id: "/api/feature-usage/with-plan-limits",
				pattern: /^\/api\/feature-usage\/with-plan-limits\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/feature-usage/with-plan-limits/_server.ts.js'))
			},
			{
				id: "/api/graphql",
				pattern: /^\/api\/graphql\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/graphql/_server.ts.js'))
			},
			{
				id: "/api/health",
				pattern: /^\/api\/health\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/health/_server.ts.js'))
			},
			{
				id: "/api/health/collector",
				pattern: /^\/api\/health\/collector\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/health/collector/_server.ts.js'))
			},
			{
				id: "/api/health/report",
				pattern: /^\/api\/health\/report\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/health/report/_server.ts.js'))
			},
			{
				id: "/api/health/services",
				pattern: /^\/api\/health\/services\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/health/services/_server.ts.js'))
			},
			{
				id: "/api/help",
				pattern: /^\/api\/help\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/help/_server.ts.js'))
			},
			{
				id: "/api/help/categories",
				pattern: /^\/api\/help\/categories\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/help/categories/_server.ts.js'))
			},
			{
				id: "/api/help/search",
				pattern: /^\/api\/help\/search\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/help/search/_server.ts.js'))
			},
			{
				id: "/api/help/tags",
				pattern: /^\/api\/help\/tags\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/help/tags/_server.ts.js'))
			},
			{
				id: "/api/help/[slug]",
				pattern: /^\/api\/help\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/help/_slug_/_server.ts.js'))
			},
			{
				id: "/api/job-alerts",
				pattern: /^\/api\/job-alerts\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/job-alerts/_server.ts.js'))
			},
			{
				id: "/api/jobs",
				pattern: /^\/api\/jobs\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/jobs/_server.ts.js'))
			},
			{
				id: "/api/jobs/saved",
				pattern: /^\/api\/jobs\/saved\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/jobs/saved/_server.ts.js'))
			},
			{
				id: "/api/jobs/search",
				pattern: /^\/api\/jobs\/search\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/jobs/search/_server.ts.js'))
			},
			{
				id: "/api/jobs/search/status",
				pattern: /^\/api\/jobs\/search\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/jobs/search/status/_server.ts.js'))
			},
			{
				id: "/api/jobs/[id]",
				pattern: /^\/api\/jobs\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/jobs/_id_/_server.ts.js'))
			},
			{
				id: "/api/jobs/[id]/apply",
				pattern: /^\/api\/jobs\/([^/]+?)\/apply\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/jobs/_id_/apply/_server.ts.js'))
			},
			{
				id: "/api/jobs/[id]/dismiss",
				pattern: /^\/api\/jobs\/([^/]+?)\/dismiss\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/jobs/_id_/dismiss/_server.ts.js'))
			},
			{
				id: "/api/jobs/[id]/is-saved",
				pattern: /^\/api\/jobs\/([^/]+?)\/is-saved\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/jobs/_id_/is-saved/_server.ts.js'))
			},
			{
				id: "/api/jobs/[id]/report",
				pattern: /^\/api\/jobs\/([^/]+?)\/report\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/jobs/_id_/report/_server.ts.js'))
			},
			{
				id: "/api/jobs/[id]/save",
				pattern: /^\/api\/jobs\/([^/]+?)\/save\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/jobs/_id_/save/_server.ts.js'))
			},
			{
				id: "/api/languages",
				pattern: /^\/api\/languages\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/languages/_server.ts.js'))
			},
			{
				id: "/api/locations",
				pattern: /^\/api\/locations\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/locations/_server.ts.js'))
			},
			{
				id: "/api/locations/resolve",
				pattern: /^\/api\/locations\/resolve\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/locations/resolve/_server.ts.js'))
			},
			{
				id: "/api/maintenance",
				pattern: /^\/api\/maintenance\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/maintenance/_server.ts.js'))
			},
			{
				id: "/api/maintenance/[id]/history",
				pattern: /^\/api\/maintenance\/([^/]+?)\/history\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/maintenance/_id_/history/_server.ts.js'))
			},
			{
				id: "/api/metrics/[service]/history",
				pattern: /^\/api\/metrics\/([^/]+?)\/history\/?$/,
				params: [{"name":"service","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/metrics/_service_/history/_server.ts.js'))
			},
			{
				id: "/api/notifications",
				pattern: /^\/api\/notifications\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/notifications/_server.ts.js'))
			},
			{
				id: "/api/notifications/history",
				pattern: /^\/api\/notifications\/history\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/notifications/history/_server.ts.js'))
			},
			{
				id: "/api/notifications/mark-all-read",
				pattern: /^\/api\/notifications\/mark-all-read\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/notifications/mark-all-read/_server.ts.js'))
			},
			{
				id: "/api/notifications/send",
				pattern: /^\/api\/notifications\/send\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/notifications/send/_server.ts.js'))
			},
			{
				id: "/api/notifications/settings",
				pattern: /^\/api\/notifications\/settings\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/notifications/settings/_server.ts.js'))
			},
			{
				id: "/api/occupations",
				pattern: /^\/api\/occupations\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/occupations/_server.ts.js'))
			},
			{
				id: "/api/occupations/resolve",
				pattern: /^\/api\/occupations\/resolve\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/occupations/resolve/_server.ts.js'))
			},
			{
				id: "/api/passkeys",
				pattern: /^\/api\/passkeys\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/passkeys/_server.ts.js'))
			},
			{
				id: "/api/profile-picture",
				pattern: /^\/api\/profile-picture\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/profile-picture/_server.ts.js'))
			},
			{
				id: "/api/profiles",
				pattern: /^\/api\/profiles\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/profiles/_server.ts.js'))
			},
			{
				id: "/api/profiles/[id]/publish",
				pattern: /^\/api\/profiles\/([^/]+?)\/publish\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/profiles/_id_/publish/_server.ts.js'))
			},
			{
				id: "/api/profiles/[id]/unpublish",
				pattern: /^\/api\/profiles\/([^/]+?)\/unpublish\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/profiles/_id_/unpublish/_server.ts.js'))
			},
			{
				id: "/api/profile",
				pattern: /^\/api\/profile\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/profile/_server.ts.js'))
			},
			{
				id: "/api/profile/[id]",
				pattern: /^\/api\/profile\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/profile/_id_/_server.ts.js'))
			},
			{
				id: "/api/profile/[id]/data",
				pattern: /^\/api\/profile\/([^/]+?)\/data\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/profile/_id_/data/_server.ts.js'))
			},
			{
				id: "/api/profile/[id]/parsing-status",
				pattern: /^\/api\/profile\/([^/]+?)\/parsing-status\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/profile/_id_/parsing-status/_server.ts.js'))
			},
			{
				id: "/api/push/vapid-key",
				pattern: /^\/api\/push\/vapid-key\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/push/vapid-key/_server.ts.js'))
			},
			{
				id: "/api/push/[action]",
				pattern: /^\/api\/push\/([^/]+?)\/?$/,
				params: [{"name":"action","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/push/_action_/_server.ts.js'))
			},
			{
				id: "/api/referrals",
				pattern: /^\/api\/referrals\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/referrals/_server.ts.js'))
			},
			{
				id: "/api/referrals/analytics",
				pattern: /^\/api\/referrals\/analytics\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/referrals/analytics/_server.ts.js'))
			},
			{
				id: "/api/referrals/validate",
				pattern: /^\/api\/referrals\/validate\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/referrals/validate/_server.ts.js'))
			},
			{
				id: "/api/resumes",
				pattern: /^\/api\/resumes\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resumes/_server.ts.js'))
			},
			{
				id: "/api/resume/create",
				pattern: /^\/api\/resume\/create\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/create/_server.ts.js'))
			},
			{
				id: "/api/resume/default",
				pattern: /^\/api\/resume\/default\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/default/_server.ts.js'))
			},
			{
				id: "/api/resume/duplicate",
				pattern: /^\/api\/resume\/duplicate\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/duplicate/_server.ts.js'))
			},
			{
				id: "/api/resume/fix-parsing",
				pattern: /^\/api\/resume\/fix-parsing\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/fix-parsing/_server.ts.js'))
			},
			{
				id: "/api/resume/generate/status",
				pattern: /^\/api\/resume\/generate\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/generate/status/_server.ts.js'))
			},
			{
				id: "/api/resume/manual-parse",
				pattern: /^\/api\/resume\/manual-parse\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/manual-parse/_server.ts.js'))
			},
			{
				id: "/api/resume/optimize",
				pattern: /^\/api\/resume\/optimize\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/optimize/_server.ts.js'))
			},
			{
				id: "/api/resume/profile/[profileId]",
				pattern: /^\/api\/resume\/profile\/([^/]+?)\/?$/,
				params: [{"name":"profileId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/profile/_profileId_/_server.ts.js'))
			},
			{
				id: "/api/resume/rename",
				pattern: /^\/api\/resume\/rename\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/rename/_server.ts.js'))
			},
			{
				id: "/api/resume/scanner/status",
				pattern: /^\/api\/resume\/scanner\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/scanner/status/_server.ts.js'))
			},
			{
				id: "/api/resume/templates",
				pattern: /^\/api\/resume\/templates\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/templates/_server.ts.js'))
			},
			{
				id: "/api/resume/upload",
				pattern: /^\/api\/resume\/upload\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/upload/_server.ts.js'))
			},
			{
				id: "/api/resume/[id]",
				pattern: /^\/api\/resume\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/_id_/_server.ts.js'))
			},
			{
				id: "/api/resume/[id]/data",
				pattern: /^\/api\/resume\/([^/]+?)\/data\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/_id_/data/_server.ts.js'))
			},
			{
				id: "/api/resume/[id]/download",
				pattern: /^\/api\/resume\/([^/]+?)\/download\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/_id_/download/_server.ts.js'))
			},
			{
				id: "/api/resume/[id]/optimize",
				pattern: /^\/api\/resume\/([^/]+?)\/optimize\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/_id_/optimize/_server.ts.js'))
			},
			{
				id: "/api/resume/[id]/parse",
				pattern: /^\/api\/resume\/([^/]+?)\/parse\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/_id_/parse/_server.ts.js'))
			},
			{
				id: "/api/resume/[id]/parsing-status",
				pattern: /^\/api\/resume\/([^/]+?)\/parsing-status\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/_id_/parsing-status/_server.ts.js'))
			},
			{
				id: "/api/resume/[id]/status",
				pattern: /^\/api\/resume\/([^/]+?)\/status\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/_id_/status/_server.ts.js'))
			},
			{
				id: "/api/resume/[id]/update-status",
				pattern: /^\/api\/resume\/([^/]+?)\/update-status\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/resume/_id_/update-status/_server.ts.js'))
			},
			{
				id: "/api/saved-jobs",
				pattern: /^\/api\/saved-jobs\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/saved-jobs/_server.ts.js'))
			},
			{
				id: "/api/schools",
				pattern: /^\/api\/schools\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/schools/_server.ts.js'))
			},
			{
				id: "/api/search",
				pattern: /^\/api\/search\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/search/_server.ts.js'))
			},
			{
				id: "/api/search/global",
				pattern: /^\/api\/search\/global\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/search/global/_server.ts.js'))
			},
			{
				id: "/api/search/users",
				pattern: /^\/api\/search\/users\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/search/users/_server.ts.js'))
			},
			{
				id: "/api/skills",
				pattern: /^\/api\/skills\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/skills/_server.ts.js'))
			},
			{
				id: "/api/submit",
				pattern: /^\/api\/submit\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/submit/_server.ts.js'))
			},
			{
				id: "/api/system/memory",
				pattern: /^\/api\/system\/memory\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/system/memory/_server.ts.js'))
			},
			{
				id: "/api/test-companies",
				pattern: /^\/api\/test-companies\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/test-companies/_server.ts.js'))
			},
			{
				id: "/api/upgrade",
				pattern: /^\/api\/upgrade\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/upgrade/_server.ts.js'))
			},
			{
				id: "/api/usage",
				pattern: /^\/api\/usage\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/usage/_server.ts.js'))
			},
			{
				id: "/api/usage/analytics",
				pattern: /^\/api\/usage\/analytics\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/usage/analytics/_server.ts.js'))
			},
			{
				id: "/api/user",
				pattern: /^\/api\/user\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/user/_server.ts.js'))
			},
			{
				id: "/api/user/me",
				pattern: /^\/api\/user\/me\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/user/me/_server.ts.js'))
			},
			{
				id: "/api/user/set-admin",
				pattern: /^\/api\/user\/set-admin\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/user/set-admin/_server.ts.js'))
			},
			{
				id: "/api/user/status",
				pattern: /^\/api\/user\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/user/status/_server.ts.js'))
			},
			{
				id: "/api/webhooks/stripe",
				pattern: /^\/api\/webhooks\/stripe\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/webhooks/stripe/_server.ts.js'))
			},
			{
				id: "/api/websocket",
				pattern: /^\/api\/websocket\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/websocket/_server.ts.js'))
			},
			{
				id: "/api/worker-process",
				pattern: /^\/api\/worker-process\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/worker-process/_server.ts.js'))
			},
			{
				id: "/api/worker-process/[id]",
				pattern: /^\/api\/worker-process\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/worker-process/_id_/_server.ts.js'))
			},
			{
				id: "/api/worker/health",
				pattern: /^\/api\/worker\/health\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/worker/health/_server.ts.js'))
			},
			{
				id: "/api/ws",
				pattern: /^\/api\/ws\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/api/ws/_server.ts.js'))
			},
			{
				id: "/auth/forgot-password",
				pattern: /^\/auth\/forgot-password\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 13 },
				endpoint: null
			},
			{
				id: "/auth/google-callback",
				pattern: /^\/auth\/google-callback\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 14 },
				endpoint: null
			},
			{
				id: "/auth/linkedin-callback",
				pattern: /^\/auth\/linkedin-callback\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 15 },
				endpoint: null
			},
			{
				id: "/auth/passkey",
				pattern: /^\/auth\/passkey\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/auth/passkey/_server.ts.js'))
			},
			{
				id: "/auth/reset-password",
				pattern: /^\/auth\/reset-password\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 16 },
				endpoint: null
			},
			{
				id: "/auth/sign-in",
				pattern: /^\/auth\/sign-in\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 17 },
				endpoint: null
			},
			{
				id: "/auth/sign-up",
				pattern: /^\/auth\/sign-up\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 18 },
				endpoint: null
			},
			{
				id: "/auth/verified",
				pattern: /^\/auth\/verified\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 19 },
				endpoint: null
			},
			{
				id: "/auth/verify",
				pattern: /^\/auth\/verify\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 20 },
				endpoint: null
			},
			{
				id: "/auto-apply",
				pattern: /^\/auto-apply\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 21 },
				endpoint: null
			},
			{
				id: "/blog",
				pattern: /^\/blog\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 22 },
				endpoint: null
			},
			{
				id: "/blog/[slug]",
				pattern: /^\/blog\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 23 },
				endpoint: null
			},
			{
				id: "/co-pilot",
				pattern: /^\/co-pilot\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 24 },
				endpoint: null
			},
			{
				id: "/contact",
				pattern: /^\/contact\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 25 },
				endpoint: null
			},
			{
				id: "/dashboard",
				pattern: /^\/dashboard\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 26 },
				endpoint: null
			},
			{
				id: "/dashboard/automation",
				pattern: /^\/dashboard\/automation\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 27 },
				endpoint: null
			},
			{
				id: "/dashboard/automation/[id]",
				pattern: /^\/dashboard\/automation\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 28 },
				endpoint: null
			},
			{
				id: "/dashboard/builder",
				pattern: /^\/dashboard\/builder\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 29 },
				endpoint: null
			},
			{
				id: "/dashboard/builder/superform/[id]",
				pattern: /^\/dashboard\/builder\/superform\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 30 },
				endpoint: null
			},
			{
				id: "/dashboard/builder/[id]",
				pattern: /^\/dashboard\/builder\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 31 },
				endpoint: null
			},
			{
				id: "/dashboard/documents",
				pattern: /^\/dashboard\/documents\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 32 },
				endpoint: null
			},
			{
				id: "/dashboard/documents/[id]",
				pattern: /^\/dashboard\/documents\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 33 },
				endpoint: null
			},
			{
				id: "/dashboard/documents/[id]/ats",
				pattern: /^\/dashboard\/documents\/([^/]+?)\/ats\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 34 },
				endpoint: null
			},
			{
				id: "/dashboard/features",
				pattern: /^\/dashboard\/features\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 35 },
				endpoint: null
			},
			{
				id: "/dashboard/jobs",
				pattern: /^\/dashboard\/jobs\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 36 },
				endpoint: null
			},
			{
				id: "/dashboard/jobs/[id]",
				pattern: /^\/dashboard\/jobs\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 37 },
				endpoint: null
			},
			{
				id: "/dashboard/matches",
				pattern: /^\/dashboard\/matches\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 38 },
				endpoint: null
			},
			{
				id: "/dashboard/notifications",
				pattern: /^\/dashboard\/notifications\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 39 },
				endpoint: null
			},
			{
				id: "/dashboard/resumes",
				pattern: /^\/dashboard\/resumes\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 40 },
				endpoint: null
			},
			{
				id: "/dashboard/resumes/[id]",
				pattern: /^\/dashboard\/resumes\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 41 },
				endpoint: null
			},
			{
				id: "/dashboard/resumes/[id]/optimize",
				pattern: /^\/dashboard\/resumes\/([^/]+?)\/optimize\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 42 },
				endpoint: null
			},
			{
				id: "/dashboard/settings",
				pattern: /^\/dashboard\/settings\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 43 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/account",
				pattern: /^\/dashboard\/settings\/account\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 44 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin",
				pattern: /^\/dashboard\/settings\/admin\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 45 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/email",
				pattern: /^\/dashboard\/settings\/admin\/email\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,6,], errors: [1,,,,,], leaf: 46 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/email/analytics",
				pattern: /^\/dashboard\/settings\/admin\/email\/analytics\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,6,], errors: [1,,,,,], leaf: 47 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/email/audiences",
				pattern: /^\/dashboard\/settings\/admin\/email\/audiences\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,6,], errors: [1,,,,,], leaf: 48 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/email/broadcast",
				pattern: /^\/dashboard\/settings\/admin\/email\/broadcast\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,6,], errors: [1,,,,,], leaf: 49 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/email/queue",
				pattern: /^\/dashboard\/settings\/admin\/email\/queue\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,6,], errors: [1,,,,,], leaf: 50 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/feature-usage",
				pattern: /^\/dashboard\/settings\/admin\/feature-usage\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 51 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/features",
				pattern: /^\/dashboard\/settings\/admin\/features\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 52 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/maintenance",
				pattern: /^\/dashboard\/settings\/admin\/maintenance\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 53 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/notifications",
				pattern: /^\/dashboard\/settings\/admin\/notifications\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 54 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/plans",
				pattern: /^\/dashboard\/settings\/admin\/plans\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 55 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/seed-features",
				pattern: /^\/dashboard\/settings\/admin\/seed-features\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 56 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/subscriptions",
				pattern: /^\/dashboard\/settings\/admin\/subscriptions\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 57 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/analysis",
				pattern: /^\/dashboard\/settings\/analysis\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 58 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/billing",
				pattern: /^\/dashboard\/settings\/billing\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 59 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/email",
				pattern: /^\/dashboard\/settings\/email\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 60 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/general",
				pattern: /^\/dashboard\/settings\/general\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 61 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/interview-coach",
				pattern: /^\/dashboard\/settings\/interview-coach\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 62 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/make-admin",
				pattern: /^\/dashboard\/settings\/make-admin\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 63 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/notifications",
				pattern: /^\/dashboard\/settings\/notifications\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 64 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/profile",
				pattern: /^\/dashboard\/settings\/profile\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 65 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/profile/[id]",
				pattern: /^\/dashboard\/settings\/profile\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 66 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/referrals",
				pattern: /^\/dashboard\/settings\/referrals\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 67 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/security",
				pattern: /^\/dashboard\/settings\/security\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 68 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/team",
				pattern: /^\/dashboard\/settings\/team\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 69 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/usage",
				pattern: /^\/dashboard\/settings\/usage\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 70 },
				endpoint: null
			},
			{
				id: "/dashboard/tracker",
				pattern: /^\/dashboard\/tracker\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 71 },
				endpoint: null
			},
			{
				id: "/dashboard/usage",
				pattern: /^\/dashboard\/usage\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 72 },
				endpoint: __memo(() => import('./entries/endpoints/dashboard/usage/_server.ts.js'))
			},
			{
				id: "/employers",
				pattern: /^\/employers\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 73 },
				endpoint: null
			},
			{
				id: "/health",
				pattern: /^\/health\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/health/_server.ts.js'))
			},
			{
				id: "/help",
				pattern: /^\/help\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 74 },
				endpoint: null
			},
			{
				id: "/help/category/[slug]",
				pattern: /^\/help\/category\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 75 },
				endpoint: null
			},
			{
				id: "/help/quick-start",
				pattern: /^\/help\/quick-start\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 76 },
				endpoint: null
			},
			{
				id: "/help/search",
				pattern: /^\/help\/search\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 77 },
				endpoint: null
			},
			{
				id: "/help/[slug]",
				pattern: /^\/help\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 78 },
				endpoint: null
			},
			{
				id: "/job-tracker",
				pattern: /^\/job-tracker\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 79 },
				endpoint: null
			},
			{
				id: "/jobs",
				pattern: /^\/jobs\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 80 },
				endpoint: null
			},
			{
				id: "/legal",
				pattern: /^\/legal\/?$/,
				params: [],
				page: { layouts: [0,7,], errors: [1,,], leaf: 81 },
				endpoint: null
			},
			{
				id: "/legal/[slug]",
				pattern: /^\/legal\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,7,], errors: [1,,], leaf: 82 },
				endpoint: null
			},
			{
				id: "/press",
				pattern: /^\/press\/?$/,
				params: [],
				page: { layouts: [0,8,], errors: [1,,], leaf: 83 },
				endpoint: null
			},
			{
				id: "/press/coverage",
				pattern: /^\/press\/coverage\/?$/,
				params: [],
				page: { layouts: [0,8,], errors: [1,,], leaf: 84 },
				endpoint: null
			},
			{
				id: "/press/images",
				pattern: /^\/press\/images\/?$/,
				params: [],
				page: { layouts: [0,8,], errors: [1,,], leaf: 85 },
				endpoint: null
			},
			{
				id: "/press/releases",
				pattern: /^\/press\/releases\/?$/,
				params: [],
				page: { layouts: [0,8,], errors: [1,,], leaf: 86 },
				endpoint: null
			},
			{
				id: "/press/releases/[slug]",
				pattern: /^\/press\/releases\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,8,], errors: [1,,], leaf: 87 },
				endpoint: null
			},
			{
				id: "/pricing",
				pattern: /^\/pricing\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 88 },
				endpoint: null
			},
			{
				id: "/profile/[id]",
				pattern: /^\/profile\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 89 },
				endpoint: null
			},
			{
				id: "/recruiters",
				pattern: /^\/recruiters\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 90 },
				endpoint: null
			},
			{
				id: "/resources",
				pattern: /^\/resources\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 91 },
				endpoint: null
			},
			{
				id: "/resources/[slug]",
				pattern: /^\/resources\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 92 },
				endpoint: null
			},
			{
				id: "/resume-builder",
				pattern: /^\/resume-builder\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 93 },
				endpoint: null
			},
			{
				id: "/robots.txt",
				pattern: /^\/robots\.txt\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/robots.txt/_server.ts.js'))
			},
			{
				id: "/sitemap.xml",
				pattern: /^\/sitemap\.xml\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/sitemap.xml/_server.ts.js'))
			},
			{
				id: "/studio",
				pattern: /^\/studio\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 94 },
				endpoint: __memo(() => import('./entries/endpoints/studio/_server.ts.js'))
			},
			{
				id: "/studio/[path]",
				pattern: /^\/studio\/([^/]+?)\/?$/,
				params: [{"name":"path","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./entries/endpoints/studio/_path_/_server.ts.js'))
			},
			{
				id: "/system-status",
				pattern: /^\/system-status\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 95 },
				endpoint: null
			},
			{
				id: "/system-status/history",
				pattern: /^\/system-status\/history\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 96 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
