import{c as l,a as p}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as d,s as c}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function M(a,t){const r=d(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M15 3h6v6"}],["path",{d:"M10 14 21 3"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}]];f(a,c({name:"external-link"},()=>r,{get iconNode(){return s},children:(e,$)=>{var o=l(),n=i(o);m(n,t,"default",{},null),p(e,o)},$$slots:{default:!0}}))}export{M as E};
