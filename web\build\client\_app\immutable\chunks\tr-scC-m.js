import{c as l,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as p}from"./CGmarHxI.js";import{s as d}from"./BBa424ah.js";import{l as m,s as c}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(t,o){const r=m(o,["children","$$slots","$$events","$$legacy"]),a=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["polyline",{points:"7 10 12 15 17 10"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3"}]];f(t,c({name:"download"},()=>r,{get iconNode(){return a},children:(e,$)=>{var s=l(),n=p(s);d(n,o,"default",{},null),i(e,s)},$$slots:{default:!0}}))}export{x as D};
