import{c as r,a as d}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as c}from"./CGmarHxI.js";import{s as $}from"./BBa424ah.js";import{l as p,s as i}from"./Btcx8l8F.js";import{I as h}from"./D4f2twK-.js";function N(e,a){const o=p(a,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"M2.97 12.92A2 2 0 0 0 2 14.63v3.24a2 2 0 0 0 .97 1.71l3 1.8a2 2 0 0 0 2.06 0L12 19v-5.5l-5-3-4.03 2.42Z"}],["path",{d:"m7 16.5-4.74-2.85"}],["path",{d:"m7 16.5 5-3"}],["path",{d:"M7 16.5v5.17"}],["path",{d:"M12 13.5V19l3.97 2.38a2 2 0 0 0 2.06 0l3-1.8a2 2 0 0 0 .97-1.71v-3.24a2 2 0 0 0-.97-1.71L17 10.5l-5 3Z"}],["path",{d:"m17 16.5-5-3"}],["path",{d:"m17 16.5 4.74-2.85"}],["path",{d:"M17 16.5v5.17"}],["path",{d:"M7.97 4.42A2 2 0 0 0 7 6.13v4.37l5 3 5-3V6.13a2 2 0 0 0-.97-1.71l-3-1.8a2 2 0 0 0-2.06 0l-3 1.8Z"}],["path",{d:"M12 8 7.26 5.15"}],["path",{d:"m12 8 4.74-2.85"}],["path",{d:"M12 13.5V8"}]];h(e,i({name:"boxes"},()=>o,{get iconNode(){return n},children:(s,u)=>{var t=r(),l=c(t);$(l,a,"default",{},null),d(s,t)},$$slots:{default:!0}}))}function z(e,a){const o=p(a,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16"}],["path",{d:"M7 11.207a.5.5 0 0 1 .146-.353l2-2a.5.5 0 0 1 .708 0l3.292 3.292a.5.5 0 0 0 .708 0l4.292-4.292a.5.5 0 0 1 .854.353V16a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1z"}]];h(e,i({name:"chart-area"},()=>o,{get iconNode(){return n},children:(s,u)=>{var t=r(),l=c(t);$(l,a,"default",{},null),d(s,t)},$$slots:{default:!0}}))}function L(e,a){const o=p(a,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"}],["path",{d:"M3.22 12H9.5l.5-1 2 4.5 2-7 1.5 3.5h5.27"}]];h(e,i({name:"heart-pulse"},()=>o,{get iconNode(){return n},children:(s,u)=>{var t=r(),l=c(t);$(l,a,"default",{},null),d(s,t)},$$slots:{default:!0}}))}function P(e,a){const o=p(a,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"}],["path",{d:"m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"}],["path",{d:"M12 3v6"}]];h(e,i({name:"package-2"},()=>o,{get iconNode(){return n},children:(s,u)=>{var t=r(),l=c(t);$(l,a,"default",{},null),d(s,t)},$$slots:{default:!0}}))}function y(e,a){const o=p(a,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"M12 22v-9"}],["path",{d:"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z"}],["path",{d:"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13"}],["path",{d:"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z"}]];h(e,i({name:"package-open"},()=>o,{get iconNode(){return n},children:(s,u)=>{var t=r(),l=c(t);$(l,a,"default",{},null),d(s,t)},$$slots:{default:!0}}))}export{N as B,z as C,L as H,P,y as a};
