import{c as n,a as d}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as l}from"./Btcx8l8F.js";import{I as h}from"./D4f2twK-.js";function b(a,t){const o=c(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16"}],["path",{d:"M7 16h8"}],["path",{d:"M7 11h12"}],["path",{d:"M7 6h3"}]];h(a,l({name:"chart-bar"},()=>o,{get iconNode(){return s},children:(e,f)=>{var r=n(),p=i(r);m(p,t,"default",{},null),d(e,r)},$$slots:{default:!0}}))}export{b as C};
