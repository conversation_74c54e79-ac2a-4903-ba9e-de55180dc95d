const n=self;n.addEventListener("fetch",()=>{});n.addEventListener("install",o=>{console.log("Service Worker installing..."),n.skipWaiting()});n.addEventListener("activate",o=>{console.log("Service Worker activating..."),n.clients.claim()});n.addEventListener("push",o=>{console.log("Push event received:",o);const a=n.registration;let t={title:"Auto Apply",body:"You have a new notification",icon:"assets/favicon/favicon.ico",badge:"assets/favicon/favicon.ico",tag:"hirli-notification",data:{url:"/dashboard/notifications",timestamp:Date.now()}};if(o.data)try{const i=JSON.parse(o.data.text());t={title:i.title||t.title,body:i.message||i.body||t.body,icon:i.icon||t.icon,badge:i.badge||t.badge,tag:i.tag||`notification-${i.id||Date.now()}`,data:{url:i.url||t.data.url,notificationId:i.id,timestamp:Date.now(),...i.data}}}catch(i){console.error("Error parsing push data:",i)}const e=a.showNotification(t.title,{body:t.body,icon:t.icon,badge:t.badge,tag:t.tag,data:t.data,requireInteraction:!1,silent:!1});o.waitUntil(e)});n.addEventListener("notificationclick",o=>{var t;if(console.log("Notification clicked:",o),o.notification.close(),o.action==="dismiss")return;const a=((t=o.notification.data)==null?void 0:t.url)||"/dashboard/notifications";o.waitUntil(n.clients.matchAll({type:"window",includeUncontrolled:!0}).then(e=>{for(const i of e){const c=new URL(i.url),r=new URL(a,n.location.origin);if(c.origin===r.origin&&"focus"in i)return i.focus(),i.url!==a?i.navigate(a):i}if(n.clients.openWindow)return n.clients.openWindow(a)}))});
