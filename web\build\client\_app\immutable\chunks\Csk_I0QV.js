import{c as p,a}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as l}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function I(s,o){const t=c(o,["children","$$slots","$$events","$$legacy"]),e=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17"}],["polyline",{points:"16 7 22 7 22 13"}]];f(s,d({name:"trending-up"},()=>t,{get iconNode(){return e},children:(n,$)=>{var r=p(),i=l(r);m(i,o,"default",{},null),a(n,r)},$$slots:{default:!0}}))}export{I as T};
