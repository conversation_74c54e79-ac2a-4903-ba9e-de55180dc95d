import{c as n,a as i}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as m}from"./CGmarHxI.js";import{s as p}from"./BBa424ah.js";import{l,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function x(s,r){const t=l(r,["children","$$slots","$$events","$$legacy"]),e=[["circle",{cx:"11",cy:"11",r:"8"}],["path",{d:"m21 21-4.3-4.3"}]];f(s,d({name:"search"},()=>t,{get iconNode(){return e},children:(a,$)=>{var o=n(),c=m(o);p(c,r,"default",{},null),i(a,o)},$$slots:{default:!0}}))}export{x as S};
