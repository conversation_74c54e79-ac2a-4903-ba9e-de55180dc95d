import{c as p,a as l}from"./BasJTneF.js";import"./CgXBgsce.js";import{f as i}from"./CGmarHxI.js";import{s as m}from"./BBa424ah.js";import{l as c,s as d}from"./Btcx8l8F.js";import{I as f}from"./D4f2twK-.js";function y(r,a){const s=c(a,["children","$$slots","$$events","$$legacy"]),t=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"}]];f(r,d({name:"square-pen"},()=>s,{get iconNode(){return t},children:(e,$)=>{var o=p(),n=i(o);m(n,a,"default",{},null),l(e,o)},$$slots:{default:!0}}))}export{y as S};
